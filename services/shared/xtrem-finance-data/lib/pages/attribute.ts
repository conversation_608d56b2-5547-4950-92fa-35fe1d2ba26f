import { extractEdges, withoutEdges } from '@sage/xtrem-client';
import type { Attribute as AttributeNode, AttributeType, GraphApi } from '@sage/xtrem-finance-data-api';
import type { Item } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { featuresFinancialIntegration } from '../menu-items/features-financial-integration';

@ui.decorators.page<Attribute, AttributeNode>({
    module: 'finance',
    title: 'Attribute',
    subtitle: 'Attribute',
    objectTypeSingular: 'Attribute',
    objectTypePlural: 'Attributes',
    idField() {
        return this.name;
    },
    menuItem: featuresFinancialIntegration,
    priority: 700,
    node: '@sage/xtrem-finance-data/Attribute',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'id' }),
            titleRight: ui.nestedFields.text({ bind: 'name' }),
            line3: ui.nestedFields.reference({
                bind: 'attributeType',
                node: '@sage/xtrem-finance-data/AttributeType',
                valueField: 'name',
                title: 'Attribute type',
                tunnelPage: undefined,
            }),
            line3Right: ui.nestedFields.reference({
                bind: 'attributeRestrictedTo',
                valueField: 'name',
                node: '@sage/xtrem-finance-data/Attribute',
                title: 'Restricted to name',
                tunnelPage: undefined,
            }),
            restrictedTo: ui.nestedFields.reference({
                bind: 'attributeRestrictedTo',
                valueField: 'id',
                node: '@sage/xtrem-finance-data/Attribute',
                title: 'Restricted to ID',
                tunnelPage: undefined,
            }),
            site: ui.nestedFields.reference({
                bind: 'site',
                valueField: 'id',
                node: '@sage/xtrem-system/Site',
                title: 'Site',
                tunnelPage: undefined,
            }),
            item: ui.nestedFields.reference({
                bind: 'item',
                valueField: 'id',
                node: '@sage/xtrem-master-data/Item',
                title: 'Item',
                tunnelPage: undefined,
            }),
        },
        async optionsMenu(graph, storage) {
            const attributeTypeQuery = withoutEdges(
                await graph
                    .node('@sage/xtrem-finance-data/AttributeType')
                    .query(
                        ui.queryUtils.edgesSelector(
                            { id: true, name: true, isActive: true, nodeLink: true },
                            { filter: { isActive: true, nodeLink: 'attribute' } },
                        ),
                    )
                    .execute(),
            );

            const attributeTypes = attributeTypeQuery
                .map(edge => ({
                    title: `${edge.name}`,
                    graphQLFilter: { attributeType: { name: { _eq: edge.name } } },
                }))
                .sort((e1, e2) => e1.title.localeCompare(e2.title));

            if (storage.get('attributeType')) {
                for (let i = 0; i < attributeTypes.length; i += 1) {
                    if (attributeTypes[i].title === (storage.get('attributeType') as string)) {
                        attributeTypes.splice(0, 0, attributeTypes[i]);
                        attributeTypes.splice(i + 1, 1);
                        break;
                    }
                }
            }
            storage.set('attributeType', attributeTypes[0].title);
            return attributeTypes;
        },

        onOptionsMenuValueChange(_mainFilterValue, selectedFilter) {
            if (this.$.storage.get('attributeType') !== selectedFilter) {
                this.$.router.emptyPage().catch(error => {
                    throw error;
                });
            }
            this.$.storage.set('onOptionsMenuValueChange', true);
            this.$.storage.set('attributeType', selectedFilter || '');
            this.$.router.refresh().catch(error => {
                throw error;
            });
        },
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
        if (!this.$.recordId) {
            if (
                (this.$.storage.get('attributeType') && this.$.storage.get('onOptionsMenuValueChange')) ||
                !this.attributeType.value
            ) {
                this.attributeType.value =
                    (await this.getAttributeTypeByName(this.$.storage.get('attributeType') as string)) ?? null;
            }
        }
        this.$.storage.set('onOptionsMenuValueChange', false);
        this.id.isReadOnly = !!this.$.recordId;
        this.$.setPageClean();
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class Attribute extends ui.Page<GraphApi, AttributeNode> {
    @ui.decorators.section<Attribute>({ isTitleHidden: true, title: 'General' }) mainSection: ui.containers.Section;

    @ui.decorators.block<Attribute>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.switchField<Attribute>({
        parent() {
            return this.mainBlock;
        },
        fetchesDefaults: true,
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<Attribute>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.textField<Attribute>({
        parent() {
            return this.mainBlock;
        },
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    id: ui.fields.Text;

    @ui.decorators.textField<Attribute>({
        parent() {
            return this.mainBlock;
        },
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.referenceField<Attribute, AttributeNode>({
        parent() {
            return this.mainBlock;
        },
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select attribute restricted to',
        isHidden() {
            return !this.attributeType.value?.attributeTypeRestrictedTo;
        },
        filter() {
            return { attributeType: { _id: this.attributeType.value?.attributeTypeRestrictedTo?._id } };
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference({
                bind: 'attributeRestrictedTo',
                valueField: 'name',
                node: '@sage/xtrem-finance-data/Attribute',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
            ui.nestedFields.reference({
                bind: 'site',
                valueField: 'name',
                node: '@sage/xtrem-system/Site',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
            ui.nestedFields.reference({
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
        ],
    })
    attributeRestrictedTo: ui.fields.Reference<AttributeType>;

    @ui.decorators.separatorField<Attribute>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
    })
    separator: ui.fields.Separator;

    @ui.decorators.referenceField<Attribute, AttributeType>({
        parent() {
            return this.mainBlock;
        },
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select attribute type',
        filter: { nodeLink: { _eq: 'attribute' } },
        columns: [
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'nodeLink' }),
            ui.nestedFields.reference({
                bind: 'attributeTypeRestrictedTo',
                node: '@sage/xtrem-finance-data/AttributeType',
                valueField: 'name',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
            ui.nestedFields.technical({ bind: 'isLinkedToSite' }),
            ui.nestedFields.technical({ bind: 'isLinkedToItem' }),
        ],
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    attributeType: ui.fields.Reference<AttributeType>;

    @ui.decorators.separatorField<Attribute>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    typeSeparator: ui.fields.Separator;

    @ui.decorators.referenceField<Attribute, Site>({
        parent() {
            return this.mainBlock;
        },
        node: '@sage/xtrem-system/Site',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select site',
        helperTextField: 'id',
        isHidden() {
            return !this.attributeType.value?.isLinkedToSite;
        },
        filter: { isActive: true },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference({
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                valueField: 'name',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
        ],
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<Attribute, Item>({
        parent() {
            return this.mainBlock;
        },
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select item',
        isHidden() {
            return !this.attributeType.value?.isLinkedToItem;
        },
        filter: { isActive: true },
    })
    item: ui.fields.Reference<Item>;

    async getAttributeTypeByName(name: string) {
        const result = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-finance-data/AttributeType')
                .query(ui.queryUtils.edgesSelector({ _id: true, id: true, name: true }, { filter: { name } }))
                .execute(),
        );
        return result.length > 0 ? result.at(0) : null;
    }
}
