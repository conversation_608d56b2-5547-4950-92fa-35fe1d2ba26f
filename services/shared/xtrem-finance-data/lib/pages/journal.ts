import { extractEdges } from '@sage/xtrem-client';
import type { GraphApi } from '@sage/xtrem-finance-data-api';
import type { Legislation } from '@sage/xtrem-structure-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { getIsSubjectToGlTaxExcludedAmount } from '../client-functions/common';
import { featuresFinancialIntegration } from '../menu-items/features-financial-integration';

@ui.decorators.page<Journal>({
    module: 'finance',
    title: 'Journal',
    objectTypeSingular: 'Journal',
    objectTypePlural: 'Journals',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-finance-data/Journal',
    menuItem: featuresFinancialIntegration,
    priority: 200,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'id' }),
            line2: ui.nestedFields.text({ bind: 'name' }),
            line3: ui.nestedFields.reference({
                bind: 'legislation',
                tunnelPage: undefined,
            }),
            line6: ui.nestedFields.reference({
                bind: 'sequence',
                node: '@sage/xtrem-master-data/SequenceNumber',
                valueField: 'name',
                title: 'Sequence number',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
        },
        optionsMenu: [
            { title: 'All', graphQLFilter: {} },
            { title: 'Active', graphQLFilter: { isActive: { _eq: 'true' } } },
        ],
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
        if (this.$.recordId && this.sequence.value) {
            await this.controlSequenceNumber();
        }
        this.$.setPageClean();
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class Journal extends ui.Page<GraphApi> {
    @ui.decorators.section<Journal>({ title: 'General', isTitleHidden: true }) generalSection: ui.containers.Section;

    @ui.decorators.block<Journal>({
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.switchField<Journal>({
        parent() {
            return this.generalBlock;
        },
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<Journal>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.textField<Journal>({
        parent() {
            return this.generalBlock;
        },
        title: 'ID',
        width: 'small',
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<Journal>({
        parent() {
            return this.generalBlock;
        },
        title: 'Name',
        width: 'large',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.referenceField<Journal, Legislation>({
        parent() {
            return this.generalBlock;
        },
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select legislation',
        placeholder: 'Select legislation',
        isMandatory: true,
        width: 'large',
        async onChange() {
            this.isSubjectToGlTaxExcludedAmount.value = this.legislation.value?.id
                ? await getIsSubjectToGlTaxExcludedAmount(this, this.legislation.value?.id || '')
                : false;
        },
    })
    legislation: ui.fields.Reference;

    @ui.decorators.separatorField<Journal>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    documentTypeSeparator: ui.fields.Separator;

    @ui.decorators.checkboxField<Journal>({
        parent() {
            return this.generalBlock;
        },
        title: 'Assigned to GL excluding tax',
        isHidden: true,
    })
    isSubjectToGlTaxExcludedAmount: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Journal>({
        parent() {
            return this.generalBlock;
        },
        title: 'Tax impact',
        isHidden() {
            return !this.isSubjectToGlTaxExcludedAmount.value;
        },
    })
    taxImpact: ui.fields.Checkbox;

    @ui.decorators.block<Journal>({
        parent() {
            return this.generalSection;
        },
        title: 'Numbering',
    })
    numberingBlock: ui.containers.Block;

    @ui.decorators.referenceField<Journal>({
        parent() {
            return this.numberingBlock;
        },
        title: 'Sequence number',
        width: 'large',
        node: '@sage/xtrem-master-data/SequenceNumber',
        valueField: 'name',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select sequence number',
        helperTextField: 'id',
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
    })
    sequence: ui.fields.Reference;

    @ui.decorators.textField<Journal>({ isHidden: true, title: '_id' }) _id: ui.fields.Text;

    private async controlSequenceNumber() {
        const journalEntryTypes = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-finance-data/JournalEntryType')
                .query(ui.queryUtils.edgesSelector({ _id: true }, { filter: { headerJournal: this._id.value } }))
                .execute(),
        );
        this.sequence.isDisabled = !!journalEntryTypes?.length;
    }
}
