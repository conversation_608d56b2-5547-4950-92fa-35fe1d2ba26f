import { extractEdges, withoutEdges } from '@sage/xtrem-client';
import type { Dimension as DimensionNode, DimensionType, GraphApi } from '@sage/xtrem-finance-data-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { featuresFinancialIntegration } from '../menu-items/features-financial-integration';

@ui.decorators.page<Dimension, DimensionNode>({
    module: 'finance',
    title: 'Dimension',
    objectTypeSingular: 'Dimension',
    objectTypePlural: 'Dimensions',
    idField() {
        return this.name;
    },
    menuItem: featuresFinancialIntegration,
    priority: 900,
    node: '@sage/xtrem-finance-data/Dimension',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'id' }),
            titleRight: ui.nestedFields.text({ bind: 'name' }),
            line3: ui.nestedFields.reference({
                bind: 'dimensionType',
                node: '@sage/xtrem-finance-data/DimensionType',
                valueField: 'name',
                title: 'Dimension type',
                tunnelPage: undefined,
            }),
        },
        async optionsMenu(graph, storage) {
            const result = withoutEdges(
                await graph
                    .node('@sage/xtrem-finance-data/DimensionType')
                    .query(
                        { edges: { node: { _id: true, name: true, isActive: true } } },
                        { filter: { isActive: true } },
                    )
                    .execute(),
            );

            const dimensionTypes = result.map(edge => ({
                title: `${edge.name}`,
                graphQLFilter: { dimensionType: { name: { _eq: edge.name } } },
            }));

            if (storage.get('dimensionType')) {
                for (let i = 0; i < dimensionTypes.length; i += 1) {
                    if (dimensionTypes[i].title === (storage.get('dimensionType') as string)) {
                        dimensionTypes.splice(0, 0, dimensionTypes[i]);
                        dimensionTypes.splice(i + 1, 1);
                        break;
                    }
                }
            }
            storage.set('dimensionType', dimensionTypes[0].title);
            return [...dimensionTypes];
        },
        onOptionsMenuValueChange(_mainFilterValue, selectedFilter) {
            if (this.$.storage.get('dimensionType') !== selectedFilter) {
                this.$.router.emptyPage().catch(error => {
                    throw error;
                });
            }
            this.$.storage.set('onOptionsMenuValueChange', true);
            this.$.storage.set('dimensionType', selectedFilter ?? '');
            this.$.router.refresh().catch(error => {
                throw error;
            });
        },
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
        if (!this.$.recordId) {
            if (
                (this.$.storage.get('dimensionType') && this.$.storage.get('onOptionsMenuValueChange')) ||
                !this.dimensionType.value
            ) {
                this.dimensionType.value =
                    (await this.getDimensionTypeByName(this.$.storage.get('dimensionType') as string)) ?? null;
            }
        }
        this.$.storage.set('onOptionsMenuValueChange', false);
        this.id.isReadOnly = !!this.$.recordId;
        this.$.setPageClean();
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class Dimension extends ui.Page<GraphApi, DimensionNode> {
    @ui.decorators.section<Dimension>({ isTitleHidden: true, title: 'General' })
    mainSection: ui.containers.Section;

    @ui.decorators.block<Dimension>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.switchField<Dimension>({
        parent() {
            return this.mainBlock;
        },
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<Dimension>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.textField<Dimension>({
        parent() {
            return this.mainBlock;
        },
        title: 'ID',
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    id: ui.fields.Text;

    @ui.decorators.textField<Dimension>({
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.separatorField<Dimension>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
    })
    separator: ui.fields.Separator;

    @ui.decorators.referenceField<Dimension, DimensionType>({
        parent() {
            return this.mainBlock;
        },
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension type',
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    dimensionType: ui.fields.Reference<DimensionType>;

    async getDimensionTypeByName(name: string) {
        const result = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-finance-data/DimensionType')
                .query(ui.queryUtils.edgesSelector({ _id: true, docProperty: true, name: true }, { filter: { name } }))
                .execute(),
        );
        return result.length > 0 ? result.at(0) : null;
    }
}
