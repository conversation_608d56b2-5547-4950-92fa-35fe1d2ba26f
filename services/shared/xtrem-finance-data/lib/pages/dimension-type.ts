import { extractEdges } from '@sage/xtrem-client';
import type { Dimension, GraphApi } from '@sage/xtrem-finance-data-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { featuresFinancialIntegration } from '../menu-items/features-financial-integration';

@ui.decorators.page<DimensionType>({
    module: 'finance',
    title: 'Dimension type',
    objectTypeSingular: 'Dimension type',
    objectTypePlural: 'Dimension types',
    idField() {
        return this.name;
    },
    priority: 800,
    menuItem: featuresFinancialIntegration,
    node: '@sage/xtrem-finance-data/DimensionType',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'name' }),
            titleRight: ui.nestedFields.label({
                bind: 'docProperty',
                optionType: '@sage/xtrem-finance-data/DocProperty',
                title: 'Document property',
            }),
        },
        optionsMenu: [
            { title: 'All', graphQLFilter: {} },
            { title: 'Active', graphQLFilter: { isActive: { _eq: true } } },
            { title: 'Inactive', graphQLFilter: { isActive: { _eq: false } } },
        ],
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
        if (!this.$.recordId) {
            this.isActive.value = true;
        }
        this.$.setPageClean();
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class DimensionType extends ui.Page<GraphApi> {
    @ui.decorators.section<DimensionType>({ isTitleHidden: true, title: 'General' })
    mainSection: ui.containers.Section;

    @ui.decorators.block<DimensionType>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.numericField<DimensionType>({ isHidden: true, title: '_id' }) _id: ui.fields.Numeric;

    @ui.decorators.switchField<DimensionType>({ isHidden: true, title: 'Used' }) isUsed: ui.fields.Switch;

    @ui.decorators.switchField<DimensionType>({
        parent() {
            return this.mainBlock;
        },
        title: 'Active',

        async onChange() {
            if (
                this.$.recordId &&
                (await this.checkDimensionFromDimensionType(this.$.recordId)) &&
                this.isActive.value === false
            ) {
                // initial value is true, new value is false
                const options: ui.dialogs.DialogOptions = {
                    acceptButton: { text: 'Deactivate' },
                    cancelButton: { text: 'Cancel' },
                };
                if (
                    !(await this.$.dialog
                        .confirmation(
                            'warn',
                            ui.localize(
                                '@sage/xtrem-finance-data/dimension-deactivation-dialog-title',
                                'Confirm deactivation',
                            ),
                            ui.localize(
                                '@sage/xtrem-finance-data/dimension-deactivation-dialog-content',
                                'You are about to deactivate this dimension type here and in all the documents using it.',
                            ),
                            options,
                        )
                        .then(() => {
                            this.$.showToast(
                                ui.localize(
                                    '@sage/xtrem-finance-data/dimension-type-deactivation-effective-dialog-title',
                                    'Click Save to make the change effective',
                                ),
                                { type: 'info' },
                            );
                            return true;
                        })
                        .catch(() => false))
                ) {
                    this.isActive.value = true;
                }
            }
        },
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<DimensionType>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.textField<DimensionType>({
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
        isMandatory: true,

        async onChange() {
            if (this.$.recordId) {
                const options: ui.dialogs.DialogOptions = {
                    acceptButton: { text: 'Rename' },
                    cancelButton: { text: 'Cancel' },
                };
                if (
                    !(await this.$.dialog
                        .confirmation(
                            'warn',
                            ui.localize(
                                '@sage/xtrem-finance-data/dimension-type-name-change-dialog-title',
                                'Confirm renaming',
                            ),
                            ui.localize(
                                '@sage/xtrem-finance-data/dimension-type-name-change-dialog-content',
                                'You are about to rename this dimension type here and in all the documents using it.',
                            ),
                            options,
                        )
                        .then(() => {
                            this.$.loader.isHidden = false;
                            // TODO: [RM] check if we need this update
                            // await updatePage(this, ui);
                            this.$.loader.isHidden = true;
                            return true;
                        })
                        .catch(() => false))
                ) {
                    await this.$.router.refresh();
                }
            }
        },
    })
    name: ui.fields.Text;

    @ui.decorators.selectField<DimensionType>({
        parent() {
            return this.mainBlock;
        },
        title: 'Document property',
        optionType: '@sage/xtrem-finance-data/DocProperty',
        isMandatory: true,
        isReadOnly() {
            return this.isUsed.value || false;
        },
    })
    docProperty: ui.fields.Select;

    async checkDimensionFromDimensionType(id: string) {
        const filter = { filter: { dimensionType: { _id: id } }, first: 1 };
        const result = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-finance-data/Dimension')
                .query(ui.queryUtils.edgesSelector<Dimension>({ _id: true }, filter))
                .execute(),
        );

        return result.length === 1;
    }
}
