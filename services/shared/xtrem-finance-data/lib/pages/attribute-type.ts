import type { AttributeType as AttributeTypeNode, GraphApi } from '@sage/xtrem-finance-data-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { featuresFinancialIntegration } from '../menu-items/features-financial-integration';

@ui.decorators.page<AttributeType, AttributeTypeNode>({
    module: 'finance',
    title: 'Attribute type',
    objectTypeSingular: 'Attribute type',
    objectTypePlural: 'Attribute types',
    idField() {
        return this.name;
    },
    menuItem: featuresFinancialIntegration,
    priority: 600,
    node: '@sage/xtrem-finance-data/AttributeType',
    navigationPanel: {
        listItem: {
            line2: ui.nestedFields.text({ bind: 'id' }),
            title: ui.nestedFields.text({ bind: 'name' }),
            line3: ui.nestedFields.dropdownList({
                bind: 'nodeLink',
                title: 'Node link',
                optionType: '@sage/xtrem-finance-data/NodeLink',
            }),
            line_4: ui.nestedFields.text({ bind: 'queryFilter', title: 'Filter', isHiddenOnMainField: true }),
        },
        optionsMenu: [
            { title: 'All', graphQLFilter: {} },
            { title: 'Active', graphQLFilter: { isActive: { _eq: true } } },
            { title: 'Inactive', graphQLFilter: { isActive: { _eq: false } } },
        ],
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
        this.manageFields();
        this.$.setPageClean();
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
    },
})
export class AttributeType extends ui.Page<GraphApi, AttributeTypeNode> {
    @ui.decorators.section<AttributeType>({ isTitleHidden: true, title: 'General' }) mainSection: ui.containers.Section;

    @ui.decorators.block<AttributeType>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.switchField<AttributeType>({
        parent() {
            return this.mainBlock;
        },
        title: 'Active',
        async onChange() {
            if (this.nodeLink.value === 'attribute' && this.isActive.value === false) {
                // initial value is true, new value is false
                // TODO: scan Attribute node to see if there is any attribute with the current Attribute Type, if yes, do
                // the following,otherwise, do nothing
                const options: ui.dialogs.DialogOptions = {
                    acceptButton: { text: 'Deactivate' },
                    cancelButton: { text: 'Cancel' },
                };
                if (
                    !(await this.$.dialog
                        .confirmation(
                            'warn',
                            ui.localize('@sage/xtrem-finance-data/deactivation-dialog-title', 'Confirm deactivation'),
                            ui.localize(
                                '@sage/xtrem-finance-data/deactivation-dialog-content',
                                'You are about to deactivate this attribute type here and in all the documents using it.',
                            ),
                            options,
                        )
                        .then(() => {
                            this.$.showToast(
                                ui.localize(
                                    '@sage/xtrem-finance-data/attribute-type-deactivation-effective-dialog-title',
                                    'Click Save to make the change effective',
                                ),
                                { type: 'info' },
                            );
                            return true;
                        })
                        .catch(() => false))
                ) {
                    this.isActive.value = true;
                }
            }
            this.manageFields();
        },
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<AttributeType>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.textField<AttributeType>({
        parent() {
            return this.mainBlock;
        },
        title: 'ID',
        isMandatory: true,
        isReadOnly: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<AttributeType>({
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.dropdownListField<AttributeType>({
        parent() {
            return this.mainBlock;
        },
        title: 'Node link',
        optionType: '@sage/xtrem-finance-data/NodeLink',
        isMandatory: true,
        isReadOnly: true,
    })
    nodeLink: ui.fields.DropdownList;

    @ui.decorators.textField<AttributeType>({
        parent() {
            return this.mainBlock;
        },
        title: 'Filter',
        isReadOnly: true,
    })
    queryFilter: ui.fields.Text;

    @ui.decorators.separatorField<AttributeType>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
    })
    separator: ui.fields.Separator;

    @ui.decorators.referenceField<AttributeType>({
        parent() {
            return this.mainBlock;
        },
        title: 'Restricted to',
        node: '@sage/xtrem-finance-data/AttributeType',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select attribute type restricted to',
        helperTextField: 'id',
        filter: { nodeLink: { _eq: 'attribute' } },
        columns: [
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'nodeLink' }),
        ],
        isReadOnly: true,
    })
    attributeTypeRestrictedTo: ui.fields.Reference<AttributeTypeNode>;

    @ui.decorators.multiDropdownField<AttributeType>({
        parent() {
            return this.mainBlock;
        },
        options() {
            return ['item', 'site'];
        },
        map(value: string) {
            switch (value) {
                case 'item':
                    return ui.localizeEnumMember('@sage/xtrem-finance-data/nodeLinkDataType', 'item');
                case 'site':
                    return ui.localizeEnumMember('@sage/xtrem-finance-data/FinanceItemType', 'site');
                default:
                    throw new Error(
                        ui.localize(
                            '@sage/xtrem-finance-data/pages__attribute_type__linked_to_not_valid',
                            '{{linkedTo}} linked to is not valid.',
                            { linkedTo: value },
                        ),
                    );
            }
        },
        title: 'Linked to',
        isReadOnly: true,
    })
    linkedTo: ui.fields.MultiDropdown;

    @ui.decorators.switchField<AttributeType>({
        parent() {
            return this.mainBlock;
        },
        isHidden: true,
    })
    isLinkedToSite: ui.fields.Switch;

    @ui.decorators.switchField<AttributeType>({
        parent() {
            return this.mainBlock;
        },
        isHidden: true,
    })
    isLinkedToItem: ui.fields.Switch;

    manageFields() {
        const nodeLink = this.nodeLink.value;
        this.isActive.isReadOnly = nodeLink === 'site';
        this.name.isReadOnly = this.isActive.isReadOnly;
    }
}
