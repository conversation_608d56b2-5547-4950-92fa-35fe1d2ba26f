import * as xtremCommunication from '@sage/xtrem-communication';
import { ActivityExtension } from '@sage/xtrem-core';
import { dimensionsAndAttributesOperations } from '../functions/dimensions';
import { FinanceTransaction } from '../nodes/finance-transaction';

export const sysNotificationHistoryExtension = new ActivityExtension({
    extends: xtremCommunication.activities.sysNotificationHistory,
    __filename,
    permissions: [],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [() => FinanceTransaction],
            },
            ...dimensionsAndAttributesOperations,
        ],
        create: [
            {
                operations: ['lookup'],
                on: [() => FinanceTransaction],
            },
            ...dimensionsAndAttributesOperations,
        ],
        update: [
            {
                operations: ['lookup'],
                on: [() => FinanceTransaction],
            },
            ...dimensionsAndAttributesOperations,
        ],
        delete: [
            {
                operations: ['lookup'],
                on: [() => FinanceTransaction],
            },
            ...dimensionsAndAttributesOperations,
        ],
    },
});
