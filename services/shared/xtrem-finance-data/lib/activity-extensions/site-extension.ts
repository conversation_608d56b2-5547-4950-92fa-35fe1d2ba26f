import { ActivityExtension } from '@sage/xtrem-core';
import { activities as xtremSystemActivities } from '@sage/xtrem-system';
import { dimensionsAndAttributesOperations } from '../functions/dimensions';

const { site } = xtremSystemActivities;

export const siteExtension = new ActivityExtension({
    extends: site,
    __filename,
    permissions: [],
    operationGrants: {
        read: [...dimensionsAndAttributesOperations],
        create: [...dimensionsAndAttributesOperations],
        update: [...dimensionsAndAttributesOperations],
        delete: [...dimensionsAndAttributesOperations],
    },
});
