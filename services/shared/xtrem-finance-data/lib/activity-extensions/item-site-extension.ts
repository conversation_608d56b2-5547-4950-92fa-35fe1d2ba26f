import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { dimensionsAndAttributesOperations } from '../functions/dimensions';

export const itemSiteExtension = new ActivityExtension({
    extends: xtremMasterData.activities.itemSite,
    __filename,
    permissions: [],
    operationGrants: {
        read: [...dimensionsAndAttributesOperations],
        create: [...dimensionsAndAttributesOperations],
        update: [...dimensionsAndAttributesOperations],
        delete: [...dimensionsAndAttributesOperations],
    },
});
