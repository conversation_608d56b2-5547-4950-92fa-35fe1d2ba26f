import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { dimensionsAndAttributesOperations } from '../functions/dimensions';

export const itemExtension = new ActivityExtension({
    extends: xtremMasterData.activities.item,
    __filename,
    permissions: [],
    operationGrants: {
        read: [...dimensionsAndAttributesOperations],
        create: [...dimensionsAndAttributesOperations],
        update: [...dimensionsAndAttributesOperations],
    },
});
