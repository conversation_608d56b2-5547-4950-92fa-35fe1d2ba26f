import { ActivityExtension } from '@sage/xtrem-core';
import { activities as xtremMasterActivities } from '@sage/xtrem-master-data';
import { dimensionsAndAttributesOperations } from '../functions/dimensions';

export const supplierExtension = new ActivityExtension({
    extends: xtremMasterActivities.supplier,
    __filename,
    permissions: [],
    operationGrants: {
        read: [...dimensionsAndAttributesOperations],
        create: [...dimensionsAndAttributesOperations],
        update: [...dimensionsAndAttributesOperations],
        delete: [...dimensionsAndAttributesOperations],
    },
});
