import { ActivityExtension } from '@sage/xtrem-core';
import { activities as xtremSystemActivities } from '@sage/xtrem-system';
import { dimensionsAndAttributesOperations } from '../functions/dimensions';

const { company } = xtremSystemActivities;

export const companyExtension = new ActivityExtension({
    extends: company,
    __filename,
    permissions: [],
    operationGrants: {
        read: [...dimensionsAndAttributesOperations],
        create: [...dimensionsAndAttributesOperations],
        update: [...dimensionsAndAttributesOperations],
        delete: [...dimensionsAndAttributesOperations],
    },
});
