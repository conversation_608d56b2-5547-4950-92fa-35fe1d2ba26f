import { Activity } from '@sage/xtrem-core';
import { CompanyDefaultDimension } from '../nodes/company-default-dimension';

export const companyDefaultDimension = new Activity({
    description: 'Company default dimension',
    node: () => CompanyDefaultDimension,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [{ operations: ['create', 'update', 'delete'], on: [() => CompanyDefaultDimension] }],
    },
});
