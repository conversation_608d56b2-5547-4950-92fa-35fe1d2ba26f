import { Activity } from '@sage/xtrem-core';
import { DatevConfiguration } from '../nodes/datev-configuration';

export const datevConfiguration = new Activity({
    description: 'DATEV configuration',
    node: () => DatevConfiguration,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            { operations: ['update'], on: [() => DatevConfiguration] },
            { operations: ['datevConfigurationControlsOnSave'], on: [() => DatevConfiguration] },
        ],
    },
});
