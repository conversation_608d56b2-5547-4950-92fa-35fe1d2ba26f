import { Activity } from '@sage/xtrem-core';
import { PostingClassDefinition } from '../nodes/posting-class-definition';

export const postingClassDefinition = new Activity({
    description: 'Posting class definition',
    node: () => PostingClassDefinition,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [{ operations: ['update'], on: [() => PostingClassDefinition] }],
    },
});
