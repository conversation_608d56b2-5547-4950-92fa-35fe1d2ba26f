import { Activity } from '@sage/xtrem-core';
import { nodes as xtremStructureNodes } from '@sage/xtrem-structure';
import { nodes as xtremTaxNodes } from '@sage/xtrem-tax';
import * as xtremFinanceData from '../index';

const { ChartOfAccount, Country } = xtremStructureNodes;
const { TaxSolution, TaxCategory } = xtremTaxNodes;

const readPermissions = [
    { operations: ['getSingleChartOfAccount'], on: [() => ChartOfAccount] },
    { operations: ['lookup'], on: [() => TaxSolution, () => Country, () => TaxCategory] },
];

export const postingClass = new Activity({
    description: 'Posting class',
    node: () => xtremFinanceData.nodes.PostingClass,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [...readPermissions],
        manage: [
            ...readPermissions,
            { operations: ['create', 'update', 'delete'], on: [() => xtremFinanceData.nodes.PostingClass] },
            { operations: ['getTaxCategoryIds'], on: [() => xtremFinanceData.nodes.PostingClassLineDetail] },
        ],
    },
});
