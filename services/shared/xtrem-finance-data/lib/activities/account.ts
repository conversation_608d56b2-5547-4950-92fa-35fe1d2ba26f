import { Activity } from '@sage/xtrem-core';
import { nodes as xtremStructureNodes } from '@sage/xtrem-structure';
import { Account } from '../nodes/account';

const { ChartOfAccount, Legislation } = xtremStructureNodes;

export const account = new Activity({
    description: 'Account',
    node: () => Account,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [{ operations: ['lookup'], on: [() => Legislation, () => ChartOfAccount] }],
        manage: [
            { operations: ['create', 'update', 'delete'], on: [() => Account] },
            { operations: ['lookup'], on: [() => Legislation, () => ChartOfAccount] },
        ],
    },
});
