import { Activity } from '@sage/xtrem-core';
import { nodes as xtremStructureNodes } from '@sage/xtrem-structure';
import { nodes as xtremTaxNodes } from '@sage/xtrem-tax';
import * as xtremFinanceData from '../index';

const { Country } = xtremStructureNodes;
const { TaxSolution, TaxCategory } = xtremTaxNodes;

export const journal = new Activity({
    description: 'Journal',
    node: () => xtremFinanceData.nodes.Journal,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [
                    () => TaxSolution,
                    () => Country,
                    () => TaxCategory,
                    () => xtremFinanceData.nodes.JournalEntryType,
                ],
            },
        ],
        manage: [
            { operations: ['create', 'update', 'delete'], on: [() => xtremFinanceData.nodes.Journal] },
            {
                operations: ['lookup'],
                on: [
                    () => TaxSolution,
                    () => Country,
                    () => TaxCategory,
                    () => xtremFinanceData.nodes.JournalEntryType,
                ],
            },
        ],
    },
});
