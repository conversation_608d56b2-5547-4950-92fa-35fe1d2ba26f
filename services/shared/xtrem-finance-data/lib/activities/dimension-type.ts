import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '../index';

export const dimensionType = new Activity({
    description: 'Dimension type',
    node: () => xtremFinanceData.nodes.DimensionType,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            { operations: ['create', 'update', 'delete'], on: [() => xtremFinanceData.nodes.DimensionType] },
            { operations: ['lookup'], on: [() => xtremFinanceData.nodes.Dimension] },
        ],
    },
});
