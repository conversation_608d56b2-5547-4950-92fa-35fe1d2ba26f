import { Activity } from '@sage/xtrem-core';
import { DimensionDefinitionLevelAndDefault } from '../nodes/dimension-definition-level-and-default';

export const dimensionDefinitionLevelAndDefault = new Activity({
    description: 'Dimension definition level and default',
    node: () => DimensionDefinitionLevelAndDefault,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [{ operations: ['update'], on: [() => DimensionDefinitionLevelAndDefault] }],
    },
});
