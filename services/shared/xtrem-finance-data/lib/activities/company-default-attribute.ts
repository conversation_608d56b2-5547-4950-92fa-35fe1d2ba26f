import { Activity } from '@sage/xtrem-core';
import { CompanyDefaultAttribute } from '../nodes/company-default-attribute';

export const companyDefaultAttribute = new Activity({
    description: 'Company default attribute',
    node: () => CompanyDefaultAttribute,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [{ operations: ['create', 'update', 'delete'], on: [() => CompanyDefaultAttribute] }],
    },
});
