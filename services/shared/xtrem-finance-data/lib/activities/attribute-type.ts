import { Activity } from '@sage/xtrem-core';
import { AttributeType } from '../nodes/attribute-type';

export const attributeType = new Activity({
    description: 'Attribute type',
    node: () => AttributeType,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [{ operations: ['read'], on: [() => AttributeType] }],
        manage: [{ operations: ['update'], on: [() => AttributeType] }],
    },
});
