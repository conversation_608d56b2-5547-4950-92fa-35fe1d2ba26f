{"@sage/xtrem-finance-data/activity__account__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/activity__attribute__name": "Attribut", "@sage/xtrem-finance-data/activity__attribute_type__name": "Type d'attribut", "@sage/xtrem-finance-data/activity__bank_account__name": "Compte bancaire", "@sage/xtrem-finance-data/activity__company_default_attribute__name": "Attribut par défaut société", "@sage/xtrem-finance-data/activity__company_default_dimension__name": "Section par défaut société", "@sage/xtrem-finance-data/activity__datev_configuration__name": "Configuration DATEV", "@sage/xtrem-finance-data/activity__dimension__name": "Section", "@sage/xtrem-finance-data/activity__dimension_definition_level_and_default__name": "Niveau de définition section et défaut", "@sage/xtrem-finance-data/activity__dimension_type__name": "Axe", "@sage/xtrem-finance-data/activity__journal__name": "Journal", "@sage/xtrem-finance-data/activity__journal_entry_type__name": "Type d'écriture", "@sage/xtrem-finance-data/activity__posting_class__name": "Classe de comptabilisation", "@sage/xtrem-finance-data/activity__posting_class_definition__name": "Définition de classe de comptabilisation", "@sage/xtrem-finance-data/attribute-type-deactivation-effective-dialog-title": "Sélectionnez 'Enregistrer' pour appliquer vos modifications.", "@sage/xtrem-finance-data/base-document-item-line/set-dimension-missing-line": "Ligne absente pour actualiser la section", "@sage/xtrem-finance-data/base-document-item-line/update-dimension-posted": "Vous ne pouvez pas mettre à jour une section pour un document clos.", "@sage/xtrem-finance-data/cancel": "Annuler", "@sage/xtrem-finance-data/cannot_set_tax_with_account_not_subjected_to_taxes": "Vous ne pouvez pas définir un détail de taxe pour un compte qui n'est pas soumis aux taxes.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_account": "Le compte {{account}} est introuvable.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_address": "L'adresse {{address}} est introuvable.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_base_document_line": "La ligne de document de base {{baseDocumentLine}} est introuvable.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_base_tax": "La ligne de taxe {{baseTax}} est introuvable.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_customer": "Le client {{customer}} est introuvable.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_financial_site": "Le site {{financialSite}} est introuvable.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_item": "L'article {{item}} est introuvable.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_payment_term": "La condition de paiement {{paymentTerm}}est introuvable.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_posting_class_ap_ar_invoice": "La classe de comptabilisation pour le document {{documentType}} {{documentNumber}} et l'article {{itemId}} est introuvable. Le type de classe de comptabilisation est: {{postingClassType}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_posting_class_journal_entry": "La classe de comptabilisation pour le document {{documentType}} {{documentNumber}}, est introuvable. Le type de classe de comptabilisation est : {{postingClassType}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_resource": "La ressource {{resource}} est introuvable.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_stock_journal": "Le journal de stock {{stockJournal}} est introuvable.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_supplier": "Le fournisseur {{supplier}} est introuvable.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_tax": "La taxe {{tax}} est introuvable.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_transaction_currency": "La devise {{transactionCurrency}} est introuvable.", "@sage/xtrem-finance-data/classes__localized-messages__item_posting_class_missing_on_item": "Renseignez une classe de comptabilisation pour l'article {{item}}.", "@sage/xtrem-finance-data/classes__localized-messages__no_bp_account": "{{documentType}} {{documentNumber}} : le compte est introuvable pour l'entité commerciale {{businessPartner}}.", "@sage/xtrem-finance-data/classes__localized-messages__no_finance_document_lines_generated_for_item": "Le compte ne peut pas être déterminé pour l'article {{item}}, le type d'écriture {{journalEntryType}} et le type de mouvement {{movementType}}.", "@sage/xtrem-finance-data/classes__localized-messages__resource_posting_class_missing_on_resource": "Renseignez une classe de comptabilisation pour la ressource {{resource}}.", "@sage/xtrem-finance-data/classes__localized-messages__tax_posting_class_missing_on_tax": "Renseignez une classe de comptabilisation pour la taxe {{tax}}.", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_attribute_type": "<PERSON><PERSON> <PERSON> sélectionner un {{attribute}} sur la ligne {{line}}.", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type": "<PERSON><PERSON> sélectionner la section {{level}} : {{dimension}}.", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type_item_tax_amount_false": "<PERSON><PERSON> <PERSON><PERSON> sélectionner la section {{level}} [{{dimension}}] pour cet article : {{item}} dans le document : {{sourceDocumentNumber}}.", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type_item_tax_amount_True": "<PERSON><PERSON> de<PERSON> sélectionner la section {{level}} [{{dimension}}] pour cet article : {{item}} dans le document : {{sourceDocumentNumber}} (détails de taxe).", "@sage/xtrem-finance-data/confirm-dialog-content": "Vous êtes sur le point d'appliquer ce paramétrage de comptabilisation à toutes les transactions futures.", "@sage/xtrem-finance-data/confirm-dialog-title": "Confirmer le paramétrage", "@sage/xtrem-finance-data/confirm-update": "Confirmer la mise à jour", "@sage/xtrem-finance-data/data_types__accounts_payable_receivable_invoice_document_line_type_enum__name": "Accounts payable receivable invoice document line type enum", "@sage/xtrem-finance-data/data_types__accounts_payable_receivable_invoice_line_type_enum__name": "Accounts payable receivable invoice line type enum", "@sage/xtrem-finance-data/data_types__accounts_payable_receivable_invoice_origin_enum__name": "Accounts payable receivable invoice origin enum", "@sage/xtrem-finance-data/data_types__amount_type_enum__name": "Enum type montant", "@sage/xtrem-finance-data/data_types__analytical_measure_type_enum__name": "Enum type de mesure analytique", "@sage/xtrem-finance-data/data_types__attribute__name": "Attribut", "@sage/xtrem-finance-data/data_types__attribute_dimension_type_level_enum__name": "Enum niveau type section attribut", "@sage/xtrem-finance-data/data_types__attribute_type__name": "Type d'attribut", "@sage/xtrem-finance-data/data_types__bank_account__name": "Compte bancaire", "@sage/xtrem-finance-data/data_types__bank_account_type_enum__name": "Enum type compte bancaire", "@sage/xtrem-finance-data/data_types__close_reason__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/data_types__common_reference_enum__name": "Enum référence commune", "@sage/xtrem-finance-data/data_types__dimension__name": "Section", "@sage/xtrem-finance-data/data_types__dimension_definition_level_enum__name": "Enum niveau de définition section", "@sage/xtrem-finance-data/data_types__dimension_type__name": "Axe", "@sage/xtrem-finance-data/data_types__doc_property_enum__name": "Enum propriété document", "@sage/xtrem-finance-data/data_types__finance_document_type_enum__name": "Enum type document finance", "@sage/xtrem-finance-data/data_types__finance_integration_app_enum__name": "Enum de l'application d'intégration finance", "@sage/xtrem-finance-data/data_types__finance_integration_status_enum__name": "Enum statut intégration financière", "@sage/xtrem-finance-data/data_types__finance_item_type_enum__name": "Enum type article finance", "@sage/xtrem-finance-data/data_types__header_description_enum__name": "Enum description en-tête", "@sage/xtrem-finance-data/data_types__header_posting_date_enum__name": "Enum de date de comptabilisation en-tête", "@sage/xtrem-finance-data/data_types__item_stock_management_criteria_enum__name": "Enum des critères de gestion du stock d'articles", "@sage/xtrem-finance-data/data_types__journal_origin_enum__name": "Enum origine journal", "@sage/xtrem-finance-data/data_types__journal_status_enum__name": "Enum statut de journal", "@sage/xtrem-finance-data/data_types__master_data_default_enum__name": "Enum données de référence par défaut", "@sage/xtrem-finance-data/data_types__movement_type_enum__name": "Enum type de mouvement", "@sage/xtrem-finance-data/data_types__node_link_enum__name": "Enum lien de node", "@sage/xtrem-finance-data/data_types__open_item_status_enum__name": "Enum statut échéance", "@sage/xtrem-finance-data/data_types__posting_class__name": "Classe de comptabilisation", "@sage/xtrem-finance-data/data_types__posting_class_type_enum__name": "Enum type classe comptabilisation", "@sage/xtrem-finance-data/data_types__posting_status_enum__name": "Enum statut comptabilisation", "@sage/xtrem-finance-data/data_types__sign_enum__name": "Enum signe", "@sage/xtrem-finance-data/data_types__source_document_type_enum__name": "Enum type document origine", "@sage/xtrem-finance-data/data_types__stored_attributes_data_type__name": "Type de données attributs stockés", "@sage/xtrem-finance-data/data_types__stored_dimensions_data_type__name": "Type de données sections stockées", "@sage/xtrem-finance-data/data_types__target_document_type_enum__name": "Enum type document cible", "@sage/xtrem-finance-data/data_types__tax_engine_enum__name": "Enum moteur de taxe", "@sage/xtrem-finance-data/data_types__tax_management_enum__name": "Enum gestion de taxe", "@sage/xtrem-finance-data/data_types__validation_severity_data_type__name": "Type de données degré validation", "@sage/xtrem-finance-data/deactivation-dialog-content": "Vous êtes sur le point de désactiver ce type d'attribut ici et dans tous les documents qui l'utilisent.", "@sage/xtrem-finance-data/deactivation-dialog-title": "Confirmer la désactivation", "@sage/xtrem-finance-data/dimension-deactivation-dialog-content": "Vous êtes sur le point de désactiver cet axe ici et dans tous les documents qui l'utilisent.", "@sage/xtrem-finance-data/dimension-deactivation-dialog-title": "Confirmer la désactivation", "@sage/xtrem-finance-data/dimension-type-deactivation-effective-dialog-title": "Cliquez sur 'Enregistrer' pour appliquer vos modifications.", "@sage/xtrem-finance-data/dimension-type-name-change-dialog-content": "Vous êtes sur le point de renommer cet axe ici et dans tous les documents qui l'utilisent.", "@sage/xtrem-finance-data/dimension-type-name-change-dialog-title": "Confirmer le renommage", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_document_line_type__documentLine": "Ligne de document", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_document_line_type__taxLine": "Ligne de taxe", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_line_type__fixedAssets": "Immobilisations", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_line_type__goods": "Produits", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_line_type__services": "Services", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_origin__creditMemo": "Avoir", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_origin__direct": "Directe", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_origin__invoice": "Facture", "@sage/xtrem-finance-data/enums__amount_type__adjustmentAmount": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__amount_type__adjustmentNonabsorbedAmount": "Montant de régularisation non-absorbé", "@sage/xtrem-finance-data/enums__amount_type__amount": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__amount_type__amountExcludingTax": "Montant HT", "@sage/xtrem-finance-data/enums__amount_type__amountIncludingTax": "Montant TTC", "@sage/xtrem-finance-data/enums__amount_type__deductibleTaxAmount": "Montant de taxe déductible", "@sage/xtrem-finance-data/enums__amount_type__inTransitAmount": "Montant en transit", "@sage/xtrem-finance-data/enums__amount_type__inTransitVarianceAmount": "Montant d'écart en transit", "@sage/xtrem-finance-data/enums__amount_type__landedCostAdjustmentAmount": "Montant de régularisation des frais d'approche", "@sage/xtrem-finance-data/enums__amount_type__landedCostAdjustmentNonabsorbedAmount": "Montant non-absorbé de régularisation de frais d'approche", "@sage/xtrem-finance-data/enums__amount_type__landedCostStockInTransitAdjustmentAmount": "Montant de régularisation de stock en transit pour frais d'approche", "@sage/xtrem-finance-data/enums__amount_type__landedCostStockInTransitAdjustmentNonabsorbedAmount": "Montant non-absorbé de régularisation de stock en transit pour frais d'approche", "@sage/xtrem-finance-data/enums__amount_type__landedCostStockInTransitAmount": "Montant de stock en transit pour frais d'approche", "@sage/xtrem-finance-data/enums__amount_type__nonDeductibleTaxAmount": "Montant de taxe non-déductible", "@sage/xtrem-finance-data/enums__amount_type__reverseChargeDeductibleTaxAmount": "Montant de taxe déductible autoliquidation", "@sage/xtrem-finance-data/enums__amount_type__reverseChargeNonDeductibleTaxAmount": "Montant de taxe non-déductible autoliquidation", "@sage/xtrem-finance-data/enums__amount_type__taxAmount": "Montant de taxe", "@sage/xtrem-finance-data/enums__amount_type__varianceAmount": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__analytical_measure_type__attribute": "Attribut", "@sage/xtrem-finance-data/enums__analytical_measure_type__dimension": "Section", "@sage/xtrem-finance-data/enums__attribute_dimension_type_level__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__attribute_dimension_type_level__company": "Société", "@sage/xtrem-finance-data/enums__bank_account_type__current": "Actuel", "@sage/xtrem-finance-data/enums__common_reference__documentNumber": "Numéro de document", "@sage/xtrem-finance-data/enums__common_reference__sourceDocumentNumber": "Numéro de document d'origine", "@sage/xtrem-finance-data/enums__dimension_definition_level__intersiteTransferOrder": "Ordre de transfert intersite", "@sage/xtrem-finance-data/enums__dimension_definition_level__manufacturingDirect": "Production origine directe", "@sage/xtrem-finance-data/enums__dimension_definition_level__manufacturingOrderToOrder": "Production à la commande", "@sage/xtrem-finance-data/enums__dimension_definition_level__purchasingDirect": "Achats origine directe", "@sage/xtrem-finance-data/enums__dimension_definition_level__purchasingOrderToOrder": "Achats à la commande", "@sage/xtrem-finance-data/enums__dimension_definition_level__salesDirect": "Ventes origine directe", "@sage/xtrem-finance-data/enums__dimension_definition_level__stockDirect": "Stock origine directe", "@sage/xtrem-finance-data/enums__doc_property__dimension01": "Section 01", "@sage/xtrem-finance-data/enums__doc_property__dimension02": "Section 02", "@sage/xtrem-finance-data/enums__doc_property__dimension03": "Section 03", "@sage/xtrem-finance-data/enums__doc_property__dimension04": "Section 04", "@sage/xtrem-finance-data/enums__doc_property__dimension05": "Section 05", "@sage/xtrem-finance-data/enums__doc_property__dimension06": "Section 06", "@sage/xtrem-finance-data/enums__doc_property__dimension07": "Section 07", "@sage/xtrem-finance-data/enums__doc_property__dimension08": "Section 08", "@sage/xtrem-finance-data/enums__doc_property__dimension09": "Section 09", "@sage/xtrem-finance-data/enums__doc_property__dimension10": "Section 10", "@sage/xtrem-finance-data/enums__doc_property__dimension11": "Section 11", "@sage/xtrem-finance-data/enums__doc_property__dimension12": "Section 12", "@sage/xtrem-finance-data/enums__doc_property__dimension13": "Section 13", "@sage/xtrem-finance-data/enums__doc_property__dimension14": "Section 14", "@sage/xtrem-finance-data/enums__doc_property__dimension15": "Section 15", "@sage/xtrem-finance-data/enums__doc_property__dimension16": "Section 16", "@sage/xtrem-finance-data/enums__doc_property__dimension17": "Section 17", "@sage/xtrem-finance-data/enums__doc_property__dimension18": "Section 18", "@sage/xtrem-finance-data/enums__doc_property__dimension19": "Section 19", "@sage/xtrem-finance-data/enums__doc_property__dimension20": "Section 20", "@sage/xtrem-finance-data/enums__doc_property__dimensionType01": "Axe 01", "@sage/xtrem-finance-data/enums__doc_property__dimensionType02": "Axe 02", "@sage/xtrem-finance-data/enums__doc_property__dimensionType03": "Axe 03", "@sage/xtrem-finance-data/enums__doc_property__dimensionType04": "Axe 04", "@sage/xtrem-finance-data/enums__doc_property__dimensionType05": "Axe 05", "@sage/xtrem-finance-data/enums__doc_property__dimensionType06": "Axe 06", "@sage/xtrem-finance-data/enums__doc_property__dimensionType07": "Axe 07", "@sage/xtrem-finance-data/enums__doc_property__dimensionType08": "Axe 08", "@sage/xtrem-finance-data/enums__doc_property__dimensionType09": "Axe 09", "@sage/xtrem-finance-data/enums__doc_property__dimensionType10": "Axe 10", "@sage/xtrem-finance-data/enums__doc_property__dimensionType11": "Axe 11", "@sage/xtrem-finance-data/enums__doc_property__dimensionType12": "Axe 12", "@sage/xtrem-finance-data/enums__doc_property__dimensionType13": "Axe 13", "@sage/xtrem-finance-data/enums__doc_property__dimensionType14": "Axe 14", "@sage/xtrem-finance-data/enums__doc_property__dimensionType15": "Axe 15", "@sage/xtrem-finance-data/enums__doc_property__dimensionType16": "Axe 16", "@sage/xtrem-finance-data/enums__doc_property__dimensionType17": "Axe 17", "@sage/xtrem-finance-data/enums__doc_property__dimensionType18": "Axe 18", "@sage/xtrem-finance-data/enums__doc_property__dimensionType19": "Axe 19", "@sage/xtrem-finance-data/enums__doc_property__dimensionType20": "Axe 20", "@sage/xtrem-finance-data/enums__finance_document_type__apInvoice": "Facture comptable fournisseur", "@sage/xtrem-finance-data/enums__finance_document_type__arInvoice": "Facture comptable client", "@sage/xtrem-finance-data/enums__finance_document_type__bankReconciliationDeposit": "Encaissement de rapprochement bancaire", "@sage/xtrem-finance-data/enums__finance_document_type__bankReconciliationWithdrawal": "Décaissement de rapprochement bancaire", "@sage/xtrem-finance-data/enums__finance_document_type__miscellaneousStockIssue": "Sortie de stock diverse", "@sage/xtrem-finance-data/enums__finance_document_type__miscellaneousStockReceipt": "Entrée de stock diverse", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseCreditMemo": "Avoir d'achat", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseInvoice": "Facture d'achat", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseReceipt": "<PERSON><PERSON><PERSON> d'<PERSON>", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseReturn": "Retour d'achat", "@sage/xtrem-finance-data/enums__finance_document_type__salesCreditMemo": "Avoir de vente", "@sage/xtrem-finance-data/enums__finance_document_type__salesInvoice": "Facture de vente", "@sage/xtrem-finance-data/enums__finance_document_type__salesReturnReceipt": "<PERSON>é<PERSON> de retour de vente", "@sage/xtrem-finance-data/enums__finance_document_type__salesShipment": "Expédition de vente", "@sage/xtrem-finance-data/enums__finance_document_type__stockAdjustment": "Régularisation de stock", "@sage/xtrem-finance-data/enums__finance_document_type__stockCount": "Inventaire", "@sage/xtrem-finance-data/enums__finance_document_type__stockTransferReceipt": "Réception de transfert de stock", "@sage/xtrem-finance-data/enums__finance_document_type__stockTransferShipment": "Expédition de transfert de stock", "@sage/xtrem-finance-data/enums__finance_document_type__stockValueChange": "Changement de la valeur du stock", "@sage/xtrem-finance-data/enums__finance_document_type__workInProgress": "En cours", "@sage/xtrem-finance-data/enums__finance_integration_app__frp1000": "Sage FRP 1000", "@sage/xtrem-finance-data/enums__finance_integration_app__intacct": "Sage Intacct", "@sage/xtrem-finance-data/enums__finance_integration_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__finance_integration_status__failed": "Échec", "@sage/xtrem-finance-data/enums__finance_integration_status__notRecorded": "Non enregistrée", "@sage/xtrem-finance-data/enums__finance_integration_status__pending": "En attente", "@sage/xtrem-finance-data/enums__finance_integration_status__posted": "Comptabilisée", "@sage/xtrem-finance-data/enums__finance_integration_status__recorded": "Enregistré", "@sage/xtrem-finance-data/enums__finance_integration_status__recording": "Enregistrement", "@sage/xtrem-finance-data/enums__finance_integration_status__submitted": "Soumise", "@sage/xtrem-finance-data/enums__finance_integration_status__toBeRecorded": "A enregistrer", "@sage/xtrem-finance-data/enums__finance_item_type__landedCostItem": "Article de type frais d'approche", "@sage/xtrem-finance-data/enums__finance_item_type__nonStockItem": "Article hors stock", "@sage/xtrem-finance-data/enums__finance_item_type__serviceItem": "Article de service", "@sage/xtrem-finance-data/enums__finance_item_type__stockItem": "Article géré en stock", "@sage/xtrem-finance-data/enums__header_description__documentNumber": "Numéro de document", "@sage/xtrem-finance-data/enums__header_description__documentType": "Type de document", "@sage/xtrem-finance-data/enums__header_description__transactionDescription": "Description de transaction", "@sage/xtrem-finance-data/enums__header_posting_date__documentDate": "Date document", "@sage/xtrem-finance-data/enums__header_posting_date__endOfMonth": "<PERSON> de mois", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__both": "Les deux", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__managedInStock": "<PERSON><PERSON><PERSON> en stock", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__notApplicable": "Sans objet", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__notManagedInStock": "Non géré en stock", "@sage/xtrem-finance-data/enums__journal_origin__apInvoice": "Facture comptable fournisseur", "@sage/xtrem-finance-data/enums__journal_origin__arInvoice": "Facture comptable client", "@sage/xtrem-finance-data/enums__journal_origin__directEntry": "Saisie directe", "@sage/xtrem-finance-data/enums__journal_origin__manufacturing": "Production", "@sage/xtrem-finance-data/enums__journal_origin__purchase": "Achats", "@sage/xtrem-finance-data/enums__journal_origin__sales": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__journal_origin__stock": "Stock", "@sage/xtrem-finance-data/enums__journal_status__draft": "Brouillon", "@sage/xtrem-finance-data/enums__journal_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__journal_status__inProgress": "En cours", "@sage/xtrem-finance-data/enums__journal_status__posted": "Comptabilisé", "@sage/xtrem-finance-data/enums__master_data_default__customer": "Client", "@sage/xtrem-finance-data/enums__master_data_default__item": "Article", "@sage/xtrem-finance-data/enums__master_data_default__receivingSite": "Site de réception", "@sage/xtrem-finance-data/enums__master_data_default__shippingSite": "Site d'expédition", "@sage/xtrem-finance-data/enums__master_data_default__site": "Site", "@sage/xtrem-finance-data/enums__master_data_default__sourceDocument": "Document d'origine", "@sage/xtrem-finance-data/enums__master_data_default__supplier": "Fournisseur", "@sage/xtrem-finance-data/enums__movement_type__document": "Document", "@sage/xtrem-finance-data/enums__movement_type__laborRunTimeTracking": "Suivi temps opératoire main d'oeuvre", "@sage/xtrem-finance-data/enums__movement_type__laborSetupTimeTracking": "Suivi temps de réglage main d'oeuvre", "@sage/xtrem-finance-data/enums__movement_type__machineRunTimeTracking": "Suivi temps opératoire machine", "@sage/xtrem-finance-data/enums__movement_type__machineSetupTimeTracking": "Suivi temps de réglage machine", "@sage/xtrem-finance-data/enums__movement_type__materialTracking": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__movement_type__productionTracking": "Suivi de production", "@sage/xtrem-finance-data/enums__movement_type__stockJournal": "Journal de stock", "@sage/xtrem-finance-data/enums__movement_type__toolRunTimeTracking": "Suivi temps opératoire outil", "@sage/xtrem-finance-data/enums__movement_type__toolSetupTimeTracking": "Suivi temps de réglage outil", "@sage/xtrem-finance-data/enums__movement_type__workOrderActualCostAdjustment": "Régularisation de coût réel d'ordre de fabrication", "@sage/xtrem-finance-data/enums__movement_type__workOrderActualCostAdjustmentNonAbsorbed": "Régularisation de coût réel d'ordre de fabrication non absorbée", "@sage/xtrem-finance-data/enums__movement_type__workOrderNegativeActualCostAdjustment": "Régularisation de coût réel négatif d'ordre de fabrication", "@sage/xtrem-finance-data/enums__movement_type__workOrderNegativeActualCostAdjustmentNonAbsorbed": "Régularisation de coût réel négatif d'ordre de fabrication non absorbée", "@sage/xtrem-finance-data/enums__movement_type__workOrderNegativeVariance": "Écart négatif OF", "@sage/xtrem-finance-data/enums__movement_type__workOrderVariance": "Écart OF", "@sage/xtrem-finance-data/enums__node_link__attribute": "Attribut", "@sage/xtrem-finance-data/enums__node_link__customer": "Client", "@sage/xtrem-finance-data/enums__node_link__item": "Article", "@sage/xtrem-finance-data/enums__node_link__site": "Site", "@sage/xtrem-finance-data/enums__node_link__supplier": "Fournisseur", "@sage/xtrem-finance-data/enums__open_item_status__notPaid": "Non réglée", "@sage/xtrem-finance-data/enums__open_item_status__paid": "Totalement réglée", "@sage/xtrem-finance-data/enums__open_item_status__partiallyPaid": "Partiellement rég<PERSON>e", "@sage/xtrem-finance-data/enums__posting_class_type__company": "Société", "@sage/xtrem-finance-data/enums__posting_class_type__customer": "Client", "@sage/xtrem-finance-data/enums__posting_class_type__header": "<PERSON>-tête", "@sage/xtrem-finance-data/enums__posting_class_type__item": "Article", "@sage/xtrem-finance-data/enums__posting_class_type__line": "Ligne", "@sage/xtrem-finance-data/enums__posting_class_type__resource": "Ressource", "@sage/xtrem-finance-data/enums__posting_class_type__supplier": "Fournisseur", "@sage/xtrem-finance-data/enums__posting_class_type__tax": "Taxe", "@sage/xtrem-finance-data/enums__posting_status__generated": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__posting_status__generationError": "Erreur de génération", "@sage/xtrem-finance-data/enums__posting_status__generationInProgress": "Génération en cours", "@sage/xtrem-finance-data/enums__posting_status__notPosted": "Non comptabilisée", "@sage/xtrem-finance-data/enums__posting_status__posted": "Comptabilisé", "@sage/xtrem-finance-data/enums__posting_status__postingError": "Erreur de comptabilisation", "@sage/xtrem-finance-data/enums__posting_status__postingInProgress": "Comptabilisation en cours", "@sage/xtrem-finance-data/enums__posting_status__toBeGenerated": "A générer", "@sage/xtrem-finance-data/enums__sign__C": "Crédit", "@sage/xtrem-finance-data/enums__sign__D": "Débit", "@sage/xtrem-finance-data/enums__source_document_type__materialTracking": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__source_document_type__operationTracking": "Suivi d'opération", "@sage/xtrem-finance-data/enums__source_document_type__productionTracking": "Suivi de production", "@sage/xtrem-finance-data/enums__source_document_type__purchaseCreditMemo": "Avoir d'achat", "@sage/xtrem-finance-data/enums__source_document_type__purchaseInvoice": "Facture d'achat", "@sage/xtrem-finance-data/enums__source_document_type__purchaseOrder": "Commande d'achat", "@sage/xtrem-finance-data/enums__source_document_type__purchaseReceipt": "<PERSON><PERSON><PERSON> d'<PERSON>", "@sage/xtrem-finance-data/enums__source_document_type__purchaseReturn": "Retour d'achat", "@sage/xtrem-finance-data/enums__source_document_type__salesCreditMemo": "Avoir de vente", "@sage/xtrem-finance-data/enums__source_document_type__salesInvoice": "Facture de vente", "@sage/xtrem-finance-data/enums__source_document_type__salesReturnRequest": "<PERSON><PERSON><PERSON> de retour de vente", "@sage/xtrem-finance-data/enums__source_document_type__salesShipment": "Expédition de vente", "@sage/xtrem-finance-data/enums__source_document_type__stockTransferOrder": "Ordre de transfert de stock", "@sage/xtrem-finance-data/enums__source_document_type__stockTransferReceipt": "Réception de transfert de stock", "@sage/xtrem-finance-data/enums__source_document_type__stockTransferShipment": "Expédition de transfert de stock", "@sage/xtrem-finance-data/enums__source_document_type__workOrderClose": "Solde d'ordre de fabrication", "@sage/xtrem-finance-data/enums__target_document_type__accountsPayableInvoice": "Facture comptable fournisseur", "@sage/xtrem-finance-data/enums__target_document_type__accountsReceivableAdvance": "Avance client", "@sage/xtrem-finance-data/enums__target_document_type__accountsReceivableInvoice": "Facture client", "@sage/xtrem-finance-data/enums__target_document_type__accountsReceivablePayment": "Règlement client", "@sage/xtrem-finance-data/enums__target_document_type__journalEntry": "Écriture", "@sage/xtrem-finance-data/enums__tax_engine__avalaraAvaTax": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__tax_engine__genericTaxCalculation": "Calcul de taxe générique", "@sage/xtrem-finance-data/enums__tax_management__excludingTax": "<PERSON>rs <PERSON>e", "@sage/xtrem-finance-data/enums__tax_management__includingTax": "Toutes Taxes Comprises", "@sage/xtrem-finance-data/enums__tax_management__other": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__tax_management__reverseCharge": "Autoliquidation", "@sage/xtrem-finance-data/enums__tax_management__tax": "Taxe", "@sage/xtrem-finance-data/error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/events__control__posting_class_account_control__account_is_mandatory": "V<PERSON> devez renseigner un compte lorsqu'il n'y a pas de détails de ligne.", "@sage/xtrem-finance-data/events__control__posting_class_line_detail__secondary_criteria_is_tax": "<PERSON><PERSON> de<PERSON> renseigner le code taxe lorsque la ligne de classe de comptabilisation secondaire est de type taxe.", "@sage/xtrem-finance-data/events__posting_class_definition__this_posting_class_definition_has_details": "V<PERSON> devez supprimer les détails de la ligne de classe de comptabilisation pour cette définition avant de pouvoir supprimer les critères supplémentaires.", "@sage/xtrem-finance-data/functions__accounting_engine__journals_created": "Écritures créées : {{journalsCreated}}", "@sage/xtrem-finance-data/functions__default_dimension_lib__wrong_source_for_dimensions": "Vous ne pouvez pas affecter la section par défaut {{masterDataDefault}} à ce document {{dimensionDefinitionLevel}}.", "@sage/xtrem-finance-data/functions__dimensions__attribute_type_restricted_to": "L'attribut {{attributeTypeRestrictedToName}} doit être renseigné.", "@sage/xtrem-finance-data/is_active_business_site_attribute_type_or_value_inactive": "Le site {{businessSite}} ou le type d'attribut du site commercial est inactif ou le site n'est pas défini comme un site commercial.", "@sage/xtrem-finance-data/is_active_customer_attribute_type_or_value_inactive": "Le client {{customer}} ou le type d'attribut du client est inactif.", "@sage/xtrem-finance-data/is_active_dimension_inactive": "La section {{dimension}} ou l'axe {{dimensionType}} sont inactifs.", "@sage/xtrem-finance-data/is_active_employee_attribute_inactive": "L'attribut {{employeeAttribute}} ou le type d'attribut du collaborateur est inactif.", "@sage/xtrem-finance-data/is_active_financial_site_attribute_type_or_value_inactive": "Le site {{financialSite}} ou le type d'attribut du site financier est inactif ou le site n'est pas défini comme un site financier.", "@sage/xtrem-finance-data/is_active_invalid_attribute": "Attribut invalide : {{other}}", "@sage/xtrem-finance-data/is_active_item_attribute_type_or_value_inactive": "L'article {{item}} ou le type d'attribut de l'article est inactif.", "@sage/xtrem-finance-data/is_active_manufacturing_site_attribute_type_or_value_inactive": "Le site {{manufacturingSite}} ou le type d'attribut du site de rattachement est inactif ou le site n'est pas défini comme un site de rattachement.", "@sage/xtrem-finance-data/is_active_project_attribute_inactive": "L'attribut {{projectAttribute}} ou le type d'attribut 'Affaire' est inactif.", "@sage/xtrem-finance-data/is_active_stock_site_attribute_type_or_value_inactive": "Le stock {{stockSite}} ou le type d'attribut du site de stock est inactif ou le site n'est pas défini comme un site de stock.", "@sage/xtrem-finance-data/is_active_supplier_attribute_type_or_value_inactive": "Le fournisseur {{supplier}} ou le type d'attribut du fournisseur est inactif.", "@sage/xtrem-finance-data/is_active_task_attribute_inactive": "L'attribut {{taskAttribute}} ou le type d'attribut \"Tâche\" est inactif.", "@sage/xtrem-finance-data/journal_entry_type_line_legislations_mismatch": "La législation {{postingClassLegislation}} du type de compte est différente de la législation {{typeLegislation}} du type d'écriture.", "@sage/xtrem-finance-data/menu_item__features-financial-integration": "Finance", "@sage/xtrem-finance-data/menu_item__payment-tracking": "Suivi des règlements", "@sage/xtrem-finance-data/node-extensions__base_business_relation_extension__property__datevId": "Code DATEV", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__financeIntegrationRecords": "Enregistrements intégration financière", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__financeIntegrationStatus": "Statut intégration financière", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__forceUpdateForFinance": "Forcer la mise à jour pour la finance", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__wasTaxDataChanged": "Changement de données de taxe", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension": "Définir la section", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__failed": "Échec de paramétrage de section.", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__parameter__baseDocumentItemLine": "Ligne d'article de document de base", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__parameter__storedAttributes": "Attributs stockés", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__parameter__storedDimensions": "Sections stockées", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__analyticalData": "Données analytiques", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__computedAttributes": "Attributs calculés", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__dimensionDefinitionLevel": "Niveau de définition section", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__storedAttributes": "Attributs stockés", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__storedDimensions": "Sections stockées", "@sage/xtrem-finance-data/node-extensions__base_document_line_inquiry_extension__property__postingClass": "Classe de comptabilisation", "@sage/xtrem-finance-data/node-extensions__company_extension__property__attributeTypes": "Types d'attributs", "@sage/xtrem-finance-data/node-extensions__company_extension__property__bankAccount": "Compte bancaire", "@sage/xtrem-finance-data/node-extensions__company_extension__property__datevConsultantNumber": "Numéro du consultant DATEV", "@sage/xtrem-finance-data/node-extensions__company_extension__property__datevCustomerNumber": "Numéro client DATEV", "@sage/xtrem-finance-data/node-extensions__company_extension__property__defaultAttributes": "Attributs par défaut", "@sage/xtrem-finance-data/node-extensions__company_extension__property__defaultDimensions": "Sections par défaut", "@sage/xtrem-finance-data/node-extensions__company_extension__property__dimensionTypes": "Axes", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doApPosting": "Comptabilisation factures fournisseurs", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doArPosting": "Comptabilisation factures clients", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doNonAbsorbedPosting": "Comptabilisation non absorbé", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doStockPosting": "Comptabilisation stock", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doWipPosting": "Comptabilisation en-cours", "@sage/xtrem-finance-data/node-extensions__company_extension__property__postingClass": "Classe de comptabilisation", "@sage/xtrem-finance-data/node-extensions__company_extension__property__taxEngine": "Moteur de taxe", "@sage/xtrem-finance-data/node-extensions__company-extension__do_non_absorbed_posting_not_allowed": "Comptabilisation non-absorbée interdite en cas d'absence de comptabilisation de stock.", "@sage/xtrem-finance-data/node-extensions__company-extension__do_non_absorbed_posting_not_allowed_on_legislation": "Vous ne pouvez pas comptabiliser de montants non absorbés pour cette législation.", "@sage/xtrem-finance-data/node-extensions__company-extension__do_wip_posting_not_allowed_on_legislation": "Vous ne pouvez pas comptabiliser d'en-cours pour cette législation.", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__analyticalData": "Données analytiques", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__postingClass": "Classe de comptabilisation", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__storedAttributes": "Attributs stockés", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__storedDimensions": "Sections stockées", "@sage/xtrem-finance-data/node-extensions__detailed_resource_extension__property__postingClass": "Classe de comptabilisation", "@sage/xtrem-finance-data/node-extensions__item_extension__property__analyticalData": "Données analytiques", "@sage/xtrem-finance-data/node-extensions__item_extension__property__postingClass": "Classe de comptabilisation", "@sage/xtrem-finance-data/node-extensions__item_extension__property__storedAttributes": "Attributs stockés", "@sage/xtrem-finance-data/node-extensions__item_extension__property__storedDimensions": "Sections stockées", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doApPosting": "Comptabilisation factures fournisseurs", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doArPosting": "Comptabilisation factures clients", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doLandedCostGoodsInTransitPosting": "Comptabilisation des marchandises en transit pour les frais d'approche", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doNonAbsorbedPosting": "Comptabilisation non absorbé", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doNonStockVariancePosting": "Comptabilisation écart hors stock", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doStockPosting": "Comptabilisation stock", "@sage/xtrem-finance-data/node-extensions__site_extension__property__analyticalData": "Données analytiques", "@sage/xtrem-finance-data/node-extensions__site_extension__property__storedAttributes": "Attributs stockés", "@sage/xtrem-finance-data/node-extensions__site_extension__property__storedDimensions": "Sections stockées", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__analyticalData": "Données analytiques", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__postingClass": "Classe de comptabilisation", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__storedAttributes": "Attributs stockés", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__storedDimensions": "Sections stockées", "@sage/xtrem-finance-data/node-extensions__tax_extension__property__postingClass": "Classe de comptabilisation", "@sage/xtrem-finance-data/node-extensions__tax_extension__property__postingKey": "Clé de comptabilisation", "@sage/xtrem-finance-data/nodes__account__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__account__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__account__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__account__datev_id_invalid": "Le code DATEV doit être un numéro compris entre {{fromValue}} et {{toValue}}.", "@sage/xtrem-finance-data/nodes__account__datev_id_not_unique": "Le code DATEV doit être unique.", "@sage/xtrem-finance-data/nodes__account__datev_tax_invalid": "V<PERSON> devez affecter un code taxe associé à l'un de ces pays : {{countryIds}}.", "@sage/xtrem-finance-data/nodes__account__node_name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__account__property__attributeTypes": "Types d'attributs", "@sage/xtrem-finance-data/nodes__account__property__chartOfAccount": "Plan comptable", "@sage/xtrem-finance-data/nodes__account__property__composedDescription": "Description composée", "@sage/xtrem-finance-data/nodes__account__property__datevId": "Code DATEV", "@sage/xtrem-finance-data/nodes__account__property__dimensionTypes": "Axes", "@sage/xtrem-finance-data/nodes__account__property__id": "Code", "@sage/xtrem-finance-data/nodes__account__property__isActive": "Actif", "@sage/xtrem-finance-data/nodes__account__property__isAutomaticAccount": "Compte automatique", "@sage/xtrem-finance-data/nodes__account__property__isControl": "Collectif", "@sage/xtrem-finance-data/nodes__account__property__isDirectEntryForbidden": "Saisie directe interdite", "@sage/xtrem-finance-data/nodes__account__property__name": "Nom", "@sage/xtrem-finance-data/nodes__account__property__tax": "Taxe", "@sage/xtrem-finance-data/nodes__account__property__taxManagement": "Gestion des taxes", "@sage/xtrem-finance-data/nodes__account__tax_management_control": "Lorsque les informations de taxe sont exclues des écritures, vous devez sélectionnez 'Autre' comme type de gestion des taxes.", "@sage/xtrem-finance-data/nodes__account_attribute_type__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__account_attribute_type__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__account_attribute_type__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__account_attribute_type__attribute-type-not-active": "Le type d'attribut est inactif.", "@sage/xtrem-finance-data/nodes__account_attribute_type__node_name": "Type d'attribut de compte", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__analyticalMeasureType": "Type de mesure analytique", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__attributeType": "Type d'attribut", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__isRequired": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__account_dimension_type__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__account_dimension_type__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__account_dimension_type__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__account_dimension_type__dimension-type-not-active": "L'axe est inactif.", "@sage/xtrem-finance-data/nodes__account_dimension_type__node_name": "Axe de compte", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__analyticalMeasureType": "Type de mesure analytique", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__dimensionType": "Axe", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__isRequired": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounting_staging__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__accounting_staging__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__accounting_staging__node_name": "Préparation comptable", "@sage/xtrem-finance-data/nodes__accounting_staging__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging__property__amounts": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging__property__analyticalData": "Données analytiques", "@sage/xtrem-finance-data/nodes__accounting_staging__property__baseDocumentLine": "Ligne de document de base", "@sage/xtrem-finance-data/nodes__accounting_staging__property__batchId": "Code batch", "@sage/xtrem-finance-data/nodes__accounting_staging__property__batchSize": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging__property__companyFxRate": "Cours de change de la société", "@sage/xtrem-finance-data/nodes__accounting_staging__property__companyFxRateDivisor": "Diviseur du cours de change de la société", "@sage/xtrem-finance-data/nodes__accounting_staging__property__customer": "Client", "@sage/xtrem-finance-data/nodes__accounting_staging__property__customerPostingClass": "Classe de comptabilisation client", "@sage/xtrem-finance-data/nodes__accounting_staging__property__description": "Description", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentDate": "Date de document", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentNumber": "Numéro de document", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentSysId": "Code système du document", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentType": "Type de document", "@sage/xtrem-finance-data/nodes__accounting_staging__property__dueDate": "Date d'échéance", "@sage/xtrem-finance-data/nodes__accounting_staging__property__financialSite": "Site financier", "@sage/xtrem-finance-data/nodes__accounting_staging__property__fxRateDate": "Date du cours de change", "@sage/xtrem-finance-data/nodes__accounting_staging__property__isPrinted": "Impression", "@sage/xtrem-finance-data/nodes__accounting_staging__property__isProcessed": "Est traitée", "@sage/xtrem-finance-data/nodes__accounting_staging__property__item": "Article", "@sage/xtrem-finance-data/nodes__accounting_staging__property__itemPostingClass": "Classe de comptabilisation article", "@sage/xtrem-finance-data/nodes__accounting_staging__property__movementType": "Type de mouvement", "@sage/xtrem-finance-data/nodes__accounting_staging__property__originNotificationId": "Code de notification d'origine", "@sage/xtrem-finance-data/nodes__accounting_staging__property__paymentTerm": "Condition de paiement", "@sage/xtrem-finance-data/nodes__accounting_staging__property__payToSupplier": "Fournisseur payé", "@sage/xtrem-finance-data/nodes__accounting_staging__property__payToSupplierLinkedAddress": "Adresse liée du fournisseur payé", "@sage/xtrem-finance-data/nodes__accounting_staging__property__providerSite": "Site de fourniture", "@sage/xtrem-finance-data/nodes__accounting_staging__property__recipientSite": "Site de consommation", "@sage/xtrem-finance-data/nodes__accounting_staging__property__replyTopic": "Sujet de réponse", "@sage/xtrem-finance-data/nodes__accounting_staging__property__resource": "Ressource", "@sage/xtrem-finance-data/nodes__accounting_staging__property__resourcePostingClass": "Classe de comptabilisation de ressources", "@sage/xtrem-finance-data/nodes__accounting_staging__property__returnLinkedAddress": "<PERSON><PERSON><PERSON> de retour liée", "@sage/xtrem-finance-data/nodes__accounting_staging__property__sourceBaseDocumentLine": "Ligne de document de base d'origine", "@sage/xtrem-finance-data/nodes__accounting_staging__property__sourceDocumentNumber": "Numéro de document d'origine", "@sage/xtrem-finance-data/nodes__accounting_staging__property__sourceDocumentType": "Type de document d'origine", "@sage/xtrem-finance-data/nodes__accounting_staging__property__stockJournal": "Journal de stock", "@sage/xtrem-finance-data/nodes__accounting_staging__property__storedAttributes": "Attributs stockés", "@sage/xtrem-finance-data/nodes__accounting_staging__property__storedComputedAttributes": "Attributs calculés stockés", "@sage/xtrem-finance-data/nodes__accounting_staging__property__storedDimensions": "Sections stockées", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplier": "Fournisseur", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplierDocumentDate": "Date du document fournisseur", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplierDocumentNumber": "Numéro du document fournisseur", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplierPostingClass": "Classe de comptabilisation du fournisseur", "@sage/xtrem-finance-data/nodes__accounting_staging__property__targetDocumentType": "Type de document cible", "@sage/xtrem-finance-data/nodes__accounting_staging__property__taxCalculationStatus": "Statut du calcul de taxe", "@sage/xtrem-finance-data/nodes__accounting_staging__property__taxDate": "Date de taxe", "@sage/xtrem-finance-data/nodes__accounting_staging__property__taxes": "Taxes", "@sage/xtrem-finance-data/nodes__accounting_staging__property__toBeReprocessed": "A retraiter", "@sage/xtrem-finance-data/nodes__accounting_staging__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__node_name": "Montant de préparation comptable", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__accountingStaging": "Préparation comptable", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__amount": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__amountType": "Type montant", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__baseTax": "Taxe de base", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__documentLineType": "Type de ligne de document", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__tax": "Taxe", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__taxDate": "Date de taxe", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__taxPostingClass": "Classe de comptabilisation de taxe", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__taxRate": "Taux de taxe", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__node_name": "Taxe de documents de préparation comptable", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__baseTax": "Taxe de base", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__batchId": "Code batch", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__documentNumber": "Numéro de document", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__documentType": "Type de document", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__targetDocumentType": "Type de document cible", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__node_name": "Taxe de ligne de préparation comptable", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__property__accountingStaging": "Préparation comptable", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__property__baseTax": "Taxe de base", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__node_name": "Préparation de lignes de factures comptables fournisseurs", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__property__accountingStaging": "Préparation comptable", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__property__accountsPayableInvoiceLine": "Ligne de facture comptable fournisseur", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__node_name": "Préparation de lignes de factures clients", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__property__accountingStaging": "Préparation comptable", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__property__accountsReceivableInvoiceLine": "Ligne de facture client", "@sage/xtrem-finance-data/nodes__analytical_data__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__analytical_data__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__analytical_data__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__analytical_data__node_name": "Données analytiques", "@sage/xtrem-finance-data/nodes__analytical_data__property__businessSite": "Site commercial", "@sage/xtrem-finance-data/nodes__analytical_data__property__businessSiteType": "Type de site commercial", "@sage/xtrem-finance-data/nodes__analytical_data__property__customer": "Client", "@sage/xtrem-finance-data/nodes__analytical_data__property__customerType": "Type de client", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension01": "Section 01", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension02": "Section 02", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension03": "Section 03", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension04": "Section 04", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension05": "Section 05", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension06": "Section 06", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension07": "Section 07", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension08": "Section 08", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension09": "Section 09", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension10": "Section 10", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension11": "Section 11", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension12": "Section 12", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension13": "Section 13", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension14": "Section 14", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension15": "Section 15", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension16": "Section 16", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension17": "Section 17", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension18": "Section 18", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension19": "Section 19", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension20": "Section 20", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType01": "Axe 01", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType02": "Axe 02", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType03": "Axe 03", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType04": "Axe 04", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType05": "Axe 05", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType06": "Axe 06", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType07": "Axe 07", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType08": "Axe 08", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType09": "Axe 09", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType10": "Axe 10", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType11": "Axe 11", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType12": "Axe 12", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType13": "Axe 13", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType14": "Axe 14", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType15": "Axe 15", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType16": "Axe 16", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType17": "Axe 17", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType18": "Axe 18", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType19": "Axe 19", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType20": "Axe 20", "@sage/xtrem-finance-data/nodes__analytical_data__property__employee": "Collaborateur", "@sage/xtrem-finance-data/nodes__analytical_data__property__employeeType": "Type de collaborateur", "@sage/xtrem-finance-data/nodes__analytical_data__property__financialSite": "Site financier", "@sage/xtrem-finance-data/nodes__analytical_data__property__financialSiteType": "Type de site financier", "@sage/xtrem-finance-data/nodes__analytical_data__property__item": "Article", "@sage/xtrem-finance-data/nodes__analytical_data__property__itemType": "Type d'article", "@sage/xtrem-finance-data/nodes__analytical_data__property__manufacturingSite": "Site de production", "@sage/xtrem-finance-data/nodes__analytical_data__property__manufacturingSiteType": "Type de site de production", "@sage/xtrem-finance-data/nodes__analytical_data__property__project": "<PERSON>e", "@sage/xtrem-finance-data/nodes__analytical_data__property__projectType": "Type d'affaire", "@sage/xtrem-finance-data/nodes__analytical_data__property__stockSite": "Site de stock", "@sage/xtrem-finance-data/nodes__analytical_data__property__stockSiteType": "Type de site de stock", "@sage/xtrem-finance-data/nodes__analytical_data__property__supplier": "Fournisseur", "@sage/xtrem-finance-data/nodes__analytical_data__property__supplierType": "Type de fournisseur", "@sage/xtrem-finance-data/nodes__analytical_data__property__task": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__analytical_data__property__taskType": "Type de tâche", "@sage/xtrem-finance-data/nodes__attribute__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__attribute__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__attribute__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__attribute__attribute_restricted_to": "Vous devez définir un attribut valide pour \"Limité à\".", "@sage/xtrem-finance-data/nodes__attribute__node_link_value": "V<PERSON> devez définir le lien de node en tant que \"attribut\".", "@sage/xtrem-finance-data/nodes__attribute__node_name": "Attribut", "@sage/xtrem-finance-data/nodes__attribute__property__attributeRestrictedTo": "Attribut limité à", "@sage/xtrem-finance-data/nodes__attribute__property__attributeRestrictedToId": "Attribut limité au code", "@sage/xtrem-finance-data/nodes__attribute__property__attributeType": "Type d'attribut", "@sage/xtrem-finance-data/nodes__attribute__property__composedDescription": "Description composée", "@sage/xtrem-finance-data/nodes__attribute__property__id": "Code", "@sage/xtrem-finance-data/nodes__attribute__property__isActive": "Actif", "@sage/xtrem-finance-data/nodes__attribute__property__item": "Article", "@sage/xtrem-finance-data/nodes__attribute__property__name": "Nom", "@sage/xtrem-finance-data/nodes__attribute__property__site": "Site", "@sage/xtrem-finance-data/nodes__attribute__referential__integrity": "L'attribut ne peut pas être supprimé. Il est déjà utilisé.", "@sage/xtrem-finance-data/nodes__attribute__type__referential__integrity": "Le type d'attribut ne peut pas être supprimé. Il est déjà utilisé.", "@sage/xtrem-finance-data/nodes__attribute__type__referential__integrity_isRestrictedToInUse": "Le type d'attribut est utilisé. Vous ne pouvez pas le désactiver.", "@sage/xtrem-finance-data/nodes__attribute_type__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__attribute_type__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__attribute_type__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__attribute_type__node__isActive": "Vous pouvez uniquement définir le type d'atttribut comme étant actif si le type d'attribut limité à {{id}} est actif.", "@sage/xtrem-finance-data/nodes__attribute_type__node_link_changeable_properties": "Vous pouvez uniquement mettre à jour la case à cocher 'Actif' ou le nom des propriétés.", "@sage/xtrem-finance-data/nodes__attribute_type__node_name": "Type d'attribut", "@sage/xtrem-finance-data/nodes__attribute_type__property__analyticalMeasureType": "Type de mesure analytique", "@sage/xtrem-finance-data/nodes__attribute_type__property__attributeTypeRestrictedTo": "Type d'attribut limité à", "@sage/xtrem-finance-data/nodes__attribute_type__property__id": "Code", "@sage/xtrem-finance-data/nodes__attribute_type__property__isActive": "Actif", "@sage/xtrem-finance-data/nodes__attribute_type__property__isLinkedToItem": "Lié à l'article", "@sage/xtrem-finance-data/nodes__attribute_type__property__isLinkedToSite": "Lié au site", "@sage/xtrem-finance-data/nodes__attribute_type__property__linkedTo": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__attribute_type__property__name": "Nom", "@sage/xtrem-finance-data/nodes__attribute_type__property__nodeLink": "Lien de node", "@sage/xtrem-finance-data/nodes__attribute_type__property__queryFilter": "<PERSON><PERSON><PERSON> de requ<PERSON>", "@sage/xtrem-finance-data/nodes__bank_account__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__bank_account__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__bank_account__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__bank_account__node_name": "Compte bancaire", "@sage/xtrem-finance-data/nodes__bank_account__property__bankAccountType": "Type de compte bancaire", "@sage/xtrem-finance-data/nodes__bank_account__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__bank_account__property__financialSite": "Site financier", "@sage/xtrem-finance-data/nodes__bank_account__property__id": "Code", "@sage/xtrem-finance-data/nodes__bank_account__property__isActive": "Actif", "@sage/xtrem-finance-data/nodes__bank_account__property__name": "Nom", "@sage/xtrem-finance-data/nodes__base_business_relation__datev_id_invalid": "Le code DATEV doit être un numéro compris entre {{fromValue}} et {{toValue}}.", "@sage/xtrem-finance-data/nodes__base_business_relation__datev_id_not_unique": "Le code DATEV doit être unique.", "@sage/xtrem-finance-data/nodes__base_finance_document__node_name": "Document finance de base", "@sage/xtrem-finance-data/nodes__base_finance_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_finance_document__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_finance_line__node_name": "Ligne finance de base", "@sage/xtrem-finance-data/nodes__base_finance_line__property__attributesAndDimensions": "Attributs et sections", "@sage/xtrem-finance-data/nodes__base_finance_line__property__document": "Document", "@sage/xtrem-finance-data/nodes__base_finance_line__property__documentId": "Code de document", "@sage/xtrem-finance-data/nodes__base_finance_line__property__documentNumber": "Numéro de document", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__node_name": "Section ligne finance de base", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__analyticalData": "Données analytiques", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__businessSite": "Site commercial", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__customer": "Client", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension01": "Section 01", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension02": "Section 02", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension03": "Section 03", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension04": "Section 04", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension05": "Section 05", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension06": "Section 06", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension07": "Section 07", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension08": "Section 08", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension09": "Section 09", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension10": "Section 10", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension11": "Section 11", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension12": "Section 12", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension13": "Section 13", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension14": "Section 14", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension15": "Section 15", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension16": "Section 16", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension17": "Section 17", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension18": "Section 18", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension19": "Section 19", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension20": "Section 20", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__employee": "Collaborateur", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__financialSite": "Site financier", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__item": "Article", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__manufacturingSite": "Site de production", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__originLine": "Ligne d'origine", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__project": "<PERSON>e", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__stockSite": "Site de stock", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__storedAttributes": "Attributs stockés", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__storedDimensions": "Sections stockées", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__supplier": "Fournisseur", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__task": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_open_item__document_type_invalid": "Le type de document doit être 'Facture d'achat', 'Avoir d'achat', 'Facture client, 'Facture de vente', 'Avoir de vente' ou 'Facture fournisseur'.", "@sage/xtrem-finance-data/nodes__base_open_item__negative_forced_amount_paid": "Le montant réglé forcé être supérieur ou égal à 0.", "@sage/xtrem-finance-data/nodes__base_open_item__node_name": "Échéance de base", "@sage/xtrem-finance-data/nodes__base_open_item__property__businessEntity": "Entité commerciale", "@sage/xtrem-finance-data/nodes__base_open_item__property__businessEntityPayment": "Règlement de l'entité commerciale", "@sage/xtrem-finance-data/nodes__base_open_item__property__businessRelation": "Relation commerciale", "@sage/xtrem-finance-data/nodes__base_open_item__property__closeReason": "Solder le motif", "@sage/xtrem-finance-data/nodes__base_open_item__property__closeText": "Texte de fermeture", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountDue": "<PERSON><PERSON> soci<PERSON> dû", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountDueSigned": "<PERSON><PERSON> société dû signé", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountPaid": "<PERSON><PERSON> soci<PERSON> ré<PERSON>", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountPaidSigned": "<PERSON>ant société réglé signé", "@sage/xtrem-finance-data/nodes__base_open_item__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountAmount": "Montant d'escompte", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountDate": "Date d'escompte", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountFrom": "Calcul date escompte", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountPaymentBeforeDate": "Règlement d'escompte avant le", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountType": "Type d'escompte", "@sage/xtrem-finance-data/nodes__base_open_item__property__displayDiscountPaymentDate": "Affichage date de règlement d'escompte", "@sage/xtrem-finance-data/nodes__base_open_item__property__documentNumber": "Numéro de document", "@sage/xtrem-finance-data/nodes__base_open_item__property__documentSysId": "Code système du document", "@sage/xtrem-finance-data/nodes__base_open_item__property__documentType": "Type de document", "@sage/xtrem-finance-data/nodes__base_open_item__property__dueDate": "Date d'échéance", "@sage/xtrem-finance-data/nodes__base_open_item__property__financialSiteAmountDue": "Montant du site financier dû", "@sage/xtrem-finance-data/nodes__base_open_item__property__financialSiteAmountPaid": "Montant du site financier réglé", "@sage/xtrem-finance-data/nodes__base_open_item__property__forcedAmountPaid": "<PERSON><PERSON> <PERSON><PERSON> r<PERSON>", "@sage/xtrem-finance-data/nodes__base_open_item__property__forcedAmountPaidSigned": "<PERSON><PERSON> for<PERSON> ré<PERSON> signé", "@sage/xtrem-finance-data/nodes__base_open_item__property__penaltyAmount": "Montant de pénalité", "@sage/xtrem-finance-data/nodes__base_open_item__property__penaltyPaymentType": "Type de règlement de pénalité", "@sage/xtrem-finance-data/nodes__base_open_item__property__remainingCompanyAmountSigned": "Montant société restant signé", "@sage/xtrem-finance-data/nodes__base_open_item__property__remainingTransactionAmountSigned": "Montant transaction restant signé", "@sage/xtrem-finance-data/nodes__base_open_item__property__status": "Statut", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountDue": "Montant de transaction dû", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountDueSigned": "Montant de transaction dû et signé", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountPaid": "Montant de transaction réglé", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountPaidSigned": "Montant de transaction réglé signé", "@sage/xtrem-finance-data/nodes__base_open_item__property__type": "Type", "@sage/xtrem-finance-data/nodes__base_payment_document__id_already_exists": "Le code existe déjà. Aucun compteur ne sera affecté au document courant.", "@sage/xtrem-finance-data/nodes__base_payment_document__node_name": "Document de règlement de base", "@sage/xtrem-finance-data/nodes__base_payment_document__payment_amount_discrepancy": "Le montant de règlement du document {{amount}} doit être le même que le montant total des règlements de toutes les lignes {{total}}.", "@sage/xtrem-finance-data/nodes__base_payment_document__payment_amount_invalid": "Le montant de règlement du document {{amount}} doit être supérieur à 0.", "@sage/xtrem-finance-data/nodes__base_payment_document__property__amount": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_payment_document__property__amountBankCurrency": "Montant en devise banque", "@sage/xtrem-finance-data/nodes__base_payment_document__property__bankAccount": "Compte bancaire", "@sage/xtrem-finance-data/nodes__base_payment_document__property__businessRelation": "Relation commerciale", "@sage/xtrem-finance-data/nodes__base_payment_document__property__businessRelationName": "Nom relation commerciale", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyAmount": "<PERSON><PERSON> de la société", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyCurrency": "<PERSON><PERSON> socié<PERSON>", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyExchangeRate": "Cours de change de la société", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyExchangeRateDivisor": "Diviseur du cours de change de la société", "@sage/xtrem-finance-data/nodes__base_payment_document__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_payment_document__property__customer": "Client", "@sage/xtrem-finance-data/nodes__base_payment_document__property__exchangeRateDate": "Date du cours de change", "@sage/xtrem-finance-data/nodes__base_payment_document__property__financialSite": "Site financier", "@sage/xtrem-finance-data/nodes__base_payment_document__property__financialSiteName": "Nom du site financier", "@sage/xtrem-finance-data/nodes__base_payment_document__property__isVoided": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_payment_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_payment_document__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_payment_document__property__paymentDate": "Date de règlement", "@sage/xtrem-finance-data/nodes__base_payment_document__property__paymentMethod": "Mode de règlement", "@sage/xtrem-finance-data/nodes__base_payment_document__property__postingDate": "Date de comptabilisation", "@sage/xtrem-finance-data/nodes__base_payment_document__property__postingStatus": "Statut de comptabilisation", "@sage/xtrem-finance-data/nodes__base_payment_document__property__reference": "Référence", "@sage/xtrem-finance-data/nodes__base_payment_document__property__supplier": "Fournisseur", "@sage/xtrem-finance-data/nodes__base_payment_document__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-finance-data/nodes__base_payment_document__property__type": "Type", "@sage/xtrem-finance-data/nodes__base_payment_document__property__voidDate": "Date d'annulation", "@sage/xtrem-finance-data/nodes__base_payment_document__property__voidText": "Texte d'annulation", "@sage/xtrem-finance-data/nodes__base-open_item__close_reason_mandatory": "Le motif de solde est obligatoire.", "@sage/xtrem-finance-data/nodes__base-open_item__paid_company_amount_discrepancy": "Le montant société réglé doit être inférieur ou égal au montant société dû.", "@sage/xtrem-finance-data/nodes__base-open_item__paid_financial_site_amount_discrepancy": "Le montant du site financier réglé doit être inférieur ou égal au montant du site financier dû.", "@sage/xtrem-finance-data/nodes__base-open_item__paid_transaction_amount_discrepancy": "Le montant de la transaction réglé doit être inférieur ou égal au montant de la transaction dû.", "@sage/xtrem-finance-data/nodes__base-open_item__wrong_forced_amount_paid": "Le montant forcé réglé doit être entre 0 et {{maxForcedAmount}}.", "@sage/xtrem-finance-data/nodes__base-payment_document__bank_amount_invalid": "Le montant en devise de banque {{amount}} doit être supérieur à 0.", "@sage/xtrem-finance-data/nodes__base-payment_document__financial_site_discrepancy": "Le site financier du document doit être le même que celui du compte bancaire.", "@sage/xtrem-finance-data/nodes__base-payment-document__void_date_mandatory": "La date d'annulation est obligatoire.", "@sage/xtrem-finance-data/nodes__base-payment-document__void_date_should_be_after_payment_date": "La date d'annulation doit être ultérieure à la date de commande.", "@sage/xtrem-finance-data/nodes__close_reason__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__close_reason__asyncMutation__asyncExport__parameter__filter": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__close_reason__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__close_reason__node_name": "Solder le motif", "@sage/xtrem-finance-data/nodes__close_reason__property__id": "Code", "@sage/xtrem-finance-data/nodes__close_reason__property__isActive": "Active", "@sage/xtrem-finance-data/nodes__close_reason__property__name": "Nom", "@sage/xtrem-finance-data/nodes__company__datev_number_invalid": "Renseignez un nombre entre {{first}} et {{last}}.", "@sage/xtrem-finance-data/nodes__company__datev_number_mandatory": "Si la législation est celle de l'Allemagne, le numéro est demandé.", "@sage/xtrem-finance-data/nodes__company__datev_number_only_for_germany": "Ce numéro s'applique uniquement à la législation allemande.", "@sage/xtrem-finance-data/nodes__company__project_task_discrepancy": "Les attributs d'affaire et de tâche doivent provenir de la même origine par défaut.", "@sage/xtrem-finance-data/nodes__company_attribute_type__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__company_attribute_type__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__company_attribute_type__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__company_attribute_type__attribute-type-not-active": "Le type d'attribut est inactif.", "@sage/xtrem-finance-data/nodes__company_attribute_type__node_name": "Type d'attribut de société", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__analyticalMeasureType": "Type de mesure analytique", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__attributeType": "Type d'attribut", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__company": "Société", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__isRequired": "Est requis", "@sage/xtrem-finance-data/nodes__company_default_attribute__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__company_default_attribute__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__company_default_attribute__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__company_default_attribute__node_link_master_data_default__not_allowed": "La valeur par défaut {{masterDataDefault}} n'est pas autorisée dans le document {{dimensionDefinitionLevel}}.", "@sage/xtrem-finance-data/nodes__company_default_attribute__node_name": "Attribut par défaut société", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__attributeType": "Type d'attribut", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__company": "Société", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__dimensionDefinitionLevel": "Niveau de définition section", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__masterDataDefault": "<PERSON><PERSON><PERSON> de référence par défaut", "@sage/xtrem-finance-data/nodes__company_default_dimension__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__company_default_dimension__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__company_default_dimension__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__company_default_dimension__node_name": "Section par défaut société", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__company": "Société", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__dimensionDefinitionLevel": "Niveau de définition section", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__dimensionType": "Axe", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__masterDataDefault": "<PERSON><PERSON><PERSON> de référence par défaut", "@sage/xtrem-finance-data/nodes__company_dimension_type__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__company_dimension_type__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__company_dimension_type__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__company_dimension_type__dimension-type-not-active": "L'axe est inactif.", "@sage/xtrem-finance-data/nodes__company_dimension_type__node_name": "Axe de compte", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__analyticalMeasureType": "Type de mesure analytique", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__company": "Société", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__dimensionType": "Axe", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__isRequired": "Est requis", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_account_datev_id_length": "La longueur du code DATEV pour certains comptes ne correspond pas à la longueur du compte. Les longueurs doivent être identiques.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_customer_datev_id_length": "La longueur du code DATEV pour certains clients ne correspond pas à la longueur du code client et fournisseur. Les longueurs doivent être identiques.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_customer_datev_id_range": "Les codes DATEV de certains clients sont en dehors de la borne des codes clients. Les codes doivent être compris dans la borne.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_supplier_datev_id_length": "La longueur du code DATEV de certains fournisseurs ne correspond pas à la longueur du code client et fournisseur. Les longueurs doivent être identiques.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_supplier_datev_id_range": "Les codes DATEV de certains fournisseurs sont en dehors de la borne des codes fournisseurs. Les codes doivent se trouver à l'intérieur de la borne.", "@sage/xtrem-finance-data/nodes__datev_configuration__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__datev_configuration__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__datev_configuration__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__datev_configuration__invalid_length": "Renseignez un nombre entre {{fromValue}} et {{toValue}}.", "@sage/xtrem-finance-data/nodes__datev_configuration__mutation__datevConfigurationControlsOnSave": "Contrôles de configuration DATEV à l'enregistrement", "@sage/xtrem-finance-data/nodes__datev_configuration__mutation__datevConfigurationControlsOnSave__failed": "Échec des contrôles de configuration DATEV à l'enregistrement.", "@sage/xtrem-finance-data/nodes__datev_configuration__mutation__datevConfigurationControlsOnSave__parameter__datevConfiguration": "Configuration DATEV", "@sage/xtrem-finance-data/nodes__datev_configuration__node_name": "Configuration DATEV", "@sage/xtrem-finance-data/nodes__datev_configuration__property__accountLength": "<PERSON><PERSON>ur du compte", "@sage/xtrem-finance-data/nodes__datev_configuration__property__customerRangeEnd": "Fin borne client", "@sage/xtrem-finance-data/nodes__datev_configuration__property__customerRangeStart": "Début borne client", "@sage/xtrem-finance-data/nodes__datev_configuration__property__customerSupplierLength": "<PERSON><PERSON><PERSON> fournisseur client", "@sage/xtrem-finance-data/nodes__datev_configuration__property__id": "Code", "@sage/xtrem-finance-data/nodes__datev_configuration__property__isActive": "Active", "@sage/xtrem-finance-data/nodes__datev_configuration__property__name": "Nom", "@sage/xtrem-finance-data/nodes__datev_configuration__property__skrCoa": "SKR COA", "@sage/xtrem-finance-data/nodes__datev_configuration__property__supplierRangeEnd": "Fin borne fournisseur", "@sage/xtrem-finance-data/nodes__datev_configuration__property__supplierRangeStart": "Début borne fournisseur", "@sage/xtrem-finance-data/nodes__datev_configuration__wrong_length": "La longueur du compte ne correspond pas à la longueur du code client et fournisseur.", "@sage/xtrem-finance-data/nodes__dimension__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__dimension__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__dimension__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__dimension__node_name": "Section", "@sage/xtrem-finance-data/nodes__dimension__property__composedDescription": "Description composée", "@sage/xtrem-finance-data/nodes__dimension__property__dimensionType": "Axe", "@sage/xtrem-finance-data/nodes__dimension__property__id": "Code", "@sage/xtrem-finance-data/nodes__dimension__property__isActive": "Active", "@sage/xtrem-finance-data/nodes__dimension__property__name": "Nom", "@sage/xtrem-finance-data/nodes__dimension__referential__integrity": "La dimension ne peut pas être supprimée. Elle est déjà utilisée.", "@sage/xtrem-finance-data/nodes__dimension__type__inactive": "L'axe est inactif.", "@sage/xtrem-finance-data/nodes__dimension__type__referential__integrity": "L'axe ne peut pas être supprimé. Il est déjà utilisé.", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__node_name": "Niveau de définition section et défaut", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__property__dimensionDefinitionLevel": "Niveau de définition section", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__property__masterDataDefault": "<PERSON><PERSON><PERSON> de référence par défaut", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getAttributesAndDimensionsFromItem": "Obtenir attributs et sections à partir de l'article", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getAttributesAndDimensionsFromItem__failed": "Échec d'obtention des attributs et sections à partir de l'article.", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getAttributesAndDimensionsFromItem__parameter__data": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensions": "Obtenir les attributs et les sections par défaut", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensions__failed": "Échec d'obtention des attributs et des sections par défaut.", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensions__parameter__data": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensionsOrderToOrder": "Obtenir attributs et section par défaut ordre à le commande", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensionsOrderToOrder__failed": "Échec d'obtention des attributs et des sections par défaut d'ordre à le commande.", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensionsOrderToOrder__parameter__data": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__dimension_type__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__dimension_type__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__dimension_type__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__dimension_type__max_value": "Tous les axes disponibles sont imputés.", "@sage/xtrem-finance-data/nodes__dimension_type__node_name": "Axe", "@sage/xtrem-finance-data/nodes__dimension_type__property__analyticalMeasureType": "Type de mesure analytique", "@sage/xtrem-finance-data/nodes__dimension_type__property__dimensions": "Sections", "@sage/xtrem-finance-data/nodes__dimension_type__property__docProperty": "Propriété du document", "@sage/xtrem-finance-data/nodes__dimension_type__property__isActive": "Actif", "@sage/xtrem-finance-data/nodes__dimension_type__property__isUsed": "En utilisation", "@sage/xtrem-finance-data/nodes__dimension_type__property__name": "Nom", "@sage/xtrem-finance-data/nodes__dimension_type__property__setupId": "Code paramétrage", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_accounting_staging": "Il existe des écritures en attente pour ce document. Aucune notification ne sera renvoyée.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_finance_transaction_records": "Il n'existe aucun enregistrement de transaction de finance pour ce document. Aucune notification ne sera renvoyée.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_finance_transaction_status": "Le document est en cours de traitement. Aucune notification ne sera renvoyée.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_package_active": "Le package finance n'est pas actif.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_pi_allocated_to_po": "Cette facture d'achat comporte des valeurs allouées à une commande d'achat. Une notification ne sera pas renvoyée.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_po_allocated_to_pr": "Cette réception d'achat comporte des valeurs allouées à partir d'une commande d'achat. Une notification ne sera pas renvoyée.", "@sage/xtrem-finance-data/nodes__finance_transaction__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__finance_transaction__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__finance_transaction__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__finance_transaction__node_name": "Transaction finance", "@sage/xtrem-finance-data/nodes__finance_transaction__property__batchId": "Code batch", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentNumber": "Numéro de document", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentNumberLink": "Lien de numéro de document", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentSysId": "Code système document", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentType": "Type de document", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financeIntegrationApp": "Application intégration financière", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financeIntegrationAppRecordId": "Code de l'enregistrement d'application d'intégration financière", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financeIntegrationAppUrl": "URL d'application d'intégration finance", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financialSite": "Site financier", "@sage/xtrem-finance-data/nodes__finance_transaction__property__hasSourceForDimensionLines": "Existence d'une source pour les lignes de sections", "@sage/xtrem-finance-data/nodes__finance_transaction__property__lastStatusUpdate": "Dernière mise à jour statut", "@sage/xtrem-finance-data/nodes__finance_transaction__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__finance_transaction__property__message": "Message", "@sage/xtrem-finance-data/nodes__finance_transaction__property__paymentTracking": "Suivi des règlements", "@sage/xtrem-finance-data/nodes__finance_transaction__property__postingStatus": "Statut de comptabilisation", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentLink": "Lien de document d'origine", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentNumber": "Numéro de document d'origine", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentSysId": "Code système du document d'origine", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentType": "Type de document d'origine", "@sage/xtrem-finance-data/nodes__finance_transaction__property__status": "Statut", "@sage/xtrem-finance-data/nodes__finance_transaction__property__targetDocumentNumber": "Numéro du document cible", "@sage/xtrem-finance-data/nodes__finance_transaction__property__targetDocumentSysId": "Code système du document cible", "@sage/xtrem-finance-data/nodes__finance_transaction__property__targetDocumentType": "Type de document cible", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusData": "Obtenir les données statuts de comptabilisation", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusData__failed": "Échec d'obtention des données de statuts de comptabilisation.", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusData__parameter__documentNumber": "Numéro de document", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId": "Obtenir les données de statut de comptabilisation par code document", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId__failed": "Échec d'obtention des données de statut de comptabilisation par code document.", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId__parameter__documentSysId": "Code système du document", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId__parameter__documentType": "Type de document", "@sage/xtrem-finance-data/nodes__finance_transaction__status_update_not_allowed": "Le statut de transaction finance ne peut pas être mis à jour de {{previousStatus}} à {{newStatus}}.", "@sage/xtrem-finance-data/nodes__finance_transaction_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__finance_transaction_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__finance_transaction_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__finance_transaction_line__node_name": "Ligne de transaction financière", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__financeTransaction": "Transaction finance", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__isSourceForDimension": "Source pour section", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__sourceDocumentNumber": "Numéro de document d'origine", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__sourceDocumentSysId": "Code système du document d'origine", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__sourceDocumentType": "Type de document d'origine", "@sage/xtrem-finance-data/nodes__journal__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__journal__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__journal__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__journal__node_name": "Journal", "@sage/xtrem-finance-data/nodes__journal__primaryDocumentType_not_allowed": "Le type de document principal est uniquement disponible pour la législation française.", "@sage/xtrem-finance-data/nodes__journal__property__id": "Code", "@sage/xtrem-finance-data/nodes__journal__property__isActive": "Actif", "@sage/xtrem-finance-data/nodes__journal__property__isSubjectToGlTaxExcludedAmount": "Soumis au montant Hors Taxe du grand-livre", "@sage/xtrem-finance-data/nodes__journal__property__legislation": "Législation", "@sage/xtrem-finance-data/nodes__journal__property__name": "Nom", "@sage/xtrem-finance-data/nodes__journal__property__primaryDocumentType": "Type de document principal", "@sage/xtrem-finance-data/nodes__journal__property__secondaryDocumentType": "Type de document secondaire", "@sage/xtrem-finance-data/nodes__journal__property__sequence": "Compteur", "@sage/xtrem-finance-data/nodes__journal__property__taxImpact": "Impact de taxe", "@sage/xtrem-finance-data/nodes__journal__secondaryDocumentType_not_allowed": "Le type de document secondaire est uniquement disponible pour la législation française.", "@sage/xtrem-finance-data/nodes__journal__taxImpact_cannot_be_set": "Au moins une ligne de la solution de taxe doit être 'Affectée au montant du GL HT' pour définir l'option \"Impact de taxe\" pour le journal.", "@sage/xtrem-finance-data/nodes__journal_cannot_modify_account_entry_exists": "Vous ne pouvez pas modifier le compteur. Il existe une écriture pour ce journal.", "@sage/xtrem-finance-data/nodes__journal_entry_type__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__journal_entry_type__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__journal_entry_type__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__journal_entry_type__contra_journal_entry_type_line_invalid": "Le type d'écriture de contrepartie sur la ligne doit être identique au type d'écriture.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_account_type_empty": "Laisser le type de compte d'en-tête vide.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_account_type_mandatory": "Renseignez le type de compte dans l'en-tête.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_amount_type_empty": "Laisser le type de montant d'en-tête vide.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_amount_type_mandatory": "Renseignez le type de montant dans l'en-tête.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_journal_empty": "Laisser le journal d'en-tête vide.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_journal_mandatory": "Renseignez le journal dans l'en-tête.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_posting_date_empty": "Laisser la date de comptabilisation d'en-tête vide.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_posting_date_mandatory": "Renseignez la date de comptabilisation dans l'en-tête.", "@sage/xtrem-finance-data/nodes__journal_entry_type__node_name": "Type d'écriture", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__documentType": "Type de document", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerAccountType": "Type de compte d'en-tête", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerAmountType": "Type de montant d'en-tête", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerDescription": "Description", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerJournal": "Journal", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerPostingDate": "Date de comptabilisation", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__immediatePosting": "Imputation immédiate", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__isActive": "Active", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__legislation": "Législation", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__name": "Nom", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__setupId": "Code paramétrage", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__targetDocumentType": "Type de document cible", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__forced_false": "Les articles de type frais d'approche peuvent avoir uniquement le type de mouvement Document.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__not_allowed": "Les articles de type frais d'approche ne sont pas autorisés si le type de classe de comptabilisation n'est pas Article ou si la classe de comptabilisation n'est pas détaillée.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__posting_class_definition_control": "Les articles de type frais d'approche ne sont pas autorisés par le type de compte.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__forced_false": "Les articles hors stock peuvent avoir uniquement le type de mouvement Document.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__not_allowed": "Les articles hors stock ne sont pas autorisés si le type de classe de comptabilisation n'est pas Article ou si la classe de comptabilisation n'est pas détaillée.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__posting_class_definition_control": "Les articles hors stock ne sont pas autorisés par le type de compte.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__forced_false": "Les articles de type service peuvent avoir uniquement le type de mouvement Document.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__not_allowed": "Les articles de type service ne sont pas autorisés si le type de classe de comptabilisation n'est pas Article ou si la classe de comptabilisation n'est pas détaillée.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__posting_class_definition_control": "Les articles de type service ne sont pas autorisés par le type de compte.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__forced_true": "<PERSON><PERSON> de<PERSON> sélectionner des articles gérés en stock pour ce type de mouvement : {{movementType}}.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__not_allowed": "Les articles de stock ne sont pas autorisés si le type de classe de comptabilisation n'est pas Article ou si la classe de comptabilisation n'est pas détaillée.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__posting_class_definition_control": "Les articles de stock ne sont pas autorisés par le type de compte.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__node_name": "Ligne de type d'écriture", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__accountType": "Type de compte", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__amountType": "Type montant", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__commonReference": "Référence commune", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__contraJournalEntryTypeLine": "Ligne de type d'écriture de compte de contrepartie", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isLandedCostItemAllowed": "Article de type frais d'approche autorisé", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isNonStockItemAllowed": "Article hors stock autorisé", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isServiceItemAllowed": "Article de type service autorisé", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isStockItemAllowed": "Article de stock autorisé", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__itemStockManagementCriteria": "Critères de gestion du stock d'articles", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__journalEntryType": "Type d'écriture", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__movementType": "Type de mouvement", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__sign": "<PERSON>s", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__sign__empty": "Laisser le sens vide.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__sign_mandatory": "Renseigner le signe.", "@sage/xtrem-finance-data/nodes__journal-entry-type-line__at_least_one_item_type_mandatory": "Au moins un des articles de stock, hors stock, de type service ou de type frais d'approche doit être défini pour le type Compte et le type de classe de comptabilisation Article.", "@sage/xtrem-finance-data/nodes__journal-entry-type-line__contra_journal_entry_type_line_mandatory": "Si le type de document cible est une écriture, le compte de contrepartie est nécessaire.", "@sage/xtrem-finance-data/nodes__open_items__type_invalid": "Le type d'échéance doit être 'Client' ou 'Fournisseur'.", "@sage/xtrem-finance-data/nodes__payment_document_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__payment_document_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__payment_document_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__payment_document_line__negative_discount_amount": "Le montant d'escompte doit être supérieur ou égal à 0.", "@sage/xtrem-finance-data/nodes__payment_document_line__negative_penalty_amount": "Le montant de pénalité doit être supérieur ou égal à 0.", "@sage/xtrem-finance-data/nodes__payment_document_line__node_name": "Ligne de document de règlement", "@sage/xtrem-finance-data/nodes__payment_document_line__property__adjustmentAmount": "Montant de régularisation", "@sage/xtrem-finance-data/nodes__payment_document_line__property__amount": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__payment_document_line__property__amountBankCurrency": "Montant en devise banque", "@sage/xtrem-finance-data/nodes__payment_document_line__property__companyAmount": "<PERSON><PERSON> de la société", "@sage/xtrem-finance-data/nodes__payment_document_line__property__companyCurrency": "<PERSON><PERSON> socié<PERSON>", "@sage/xtrem-finance-data/nodes__payment_document_line__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__payment_document_line__property__discountAmount": "Montant d'escompte", "@sage/xtrem-finance-data/nodes__payment_document_line__property__document": "Document", "@sage/xtrem-finance-data/nodes__payment_document_line__property__documentId": "Code du document", "@sage/xtrem-finance-data/nodes__payment_document_line__property__documentNumber": "Numéro de document", "@sage/xtrem-finance-data/nodes__payment_document_line__property__financialSite": "Site financier", "@sage/xtrem-finance-data/nodes__payment_document_line__property__origin": "Origine", "@sage/xtrem-finance-data/nodes__payment_document_line__property__originalNodeFactory": "Node livré d'origine", "@sage/xtrem-finance-data/nodes__payment_document_line__property__originalOpenItem": "Échéance d'origine", "@sage/xtrem-finance-data/nodes__payment_document_line__property__paymentTracking": "Suivi des règlements", "@sage/xtrem-finance-data/nodes__payment_document_line__property__penaltyAmount": "Montant de pénalité", "@sage/xtrem-finance-data/nodes__payment_document_line__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-finance-data/nodes__payment_tracking__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__payment_tracking__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__payment_tracking__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__payment_tracking__node_name": "Suivi des règlements", "@sage/xtrem-finance-data/nodes__payment_tracking__property__amountPaid": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__payment_tracking__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__payment_tracking__property__discountPaymentAmount": "Valeur d'escompte", "@sage/xtrem-finance-data/nodes__payment_tracking__property__discountPaymentBeforeDate": "Règlement d'escompte avant le", "@sage/xtrem-finance-data/nodes__payment_tracking__property__discountPaymentType": "Type d'escompte", "@sage/xtrem-finance-data/nodes__payment_tracking__property__document": "Document", "@sage/xtrem-finance-data/nodes__payment_tracking__property__forcedAmountPaid": "<PERSON><PERSON> <PERSON><PERSON> r<PERSON>", "@sage/xtrem-finance-data/nodes__payment_tracking__property__openItems": "Échéances", "@sage/xtrem-finance-data/nodes__payment_tracking__property__paymentLines": "Lignes de règlement", "@sage/xtrem-finance-data/nodes__payment_tracking__property__paymentTerm": "Condition de paiement", "@sage/xtrem-finance-data/nodes__payment_tracking__property__penaltyPaymentAmount": "Montant de règlement de pénalité", "@sage/xtrem-finance-data/nodes__payment_tracking__property__penaltyPaymentType": "Type de règlement de pénalité", "@sage/xtrem-finance-data/nodes__payment_tracking__property__status": "Statut", "@sage/xtrem-finance-data/nodes__posting_class__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__posting_class__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__posting_class__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_landed_cost_items": "Au moins une ligne autorise les articles de type frais d'approche.", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_non_stock_items": "Au moins une ligne autorise les articles hors stock.", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_service_items": "Au moins une ligne autorise les articles de type service.", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_stock_items": "Au moins une ligne autorise les articles de stock.", "@sage/xtrem-finance-data/nodes__posting_class__lines_mandatory": "La classe de comptabilisation doit contenir au moins une ligne.", "@sage/xtrem-finance-data/nodes__posting_class__node_name": "Classe de comptabilisation", "@sage/xtrem-finance-data/nodes__posting_class__one_non_detailed_allowed": "Seule une classe de comptabilisation non détaillées de type {{type}} est autorisée.", "@sage/xtrem-finance-data/nodes__posting_class__property__financeItemType": "Type article finance", "@sage/xtrem-finance-data/nodes__posting_class__property__id": "Code", "@sage/xtrem-finance-data/nodes__posting_class__property__isDetailed": "Détaillée", "@sage/xtrem-finance-data/nodes__posting_class__property__isLandedCostItemAllowed": "Article de type frais d'approche autorisé", "@sage/xtrem-finance-data/nodes__posting_class__property__isNonStockItemAllowed": "Article hors stock autorisé", "@sage/xtrem-finance-data/nodes__posting_class__property__isServiceItemAllowed": "Article de type service autorisé", "@sage/xtrem-finance-data/nodes__posting_class__property__isStockItemAllowed": "Article de stock autorisé", "@sage/xtrem-finance-data/nodes__posting_class__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__posting_class__property__name": "Nom", "@sage/xtrem-finance-data/nodes__posting_class__property__setupId": "Code paramétrage", "@sage/xtrem-finance-data/nodes__posting_class__property__type": "Type", "@sage/xtrem-finance-data/nodes__posting_class__update_is_detailed_not_allowed": "Vous ne pouvez pas désactiver l'option 'Détaillée' parce que la classe de comptabilisation {{postingClassName}} est liée à {{recordNameUsingThisPostingClass}}.", "@sage/xtrem-finance-data/nodes__posting_class_definition__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__posting_class_definition__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__posting_class_definition__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__posting_class_definition__can_have_secondary_criteria_not_allowed": "Vous ne pouvez pas renseigner de critère secondaire pour ce type de classe de comptabilisation : {{ postingClassType }}.", "@sage/xtrem-finance-data/nodes__posting_class_definition__criteria_and_secondary_criteria_are_the_same": "<PERSON><PERSON> de<PERSON> sélectionner des critères différents.", "@sage/xtrem-finance-data/nodes__posting_class_definition__node_name": "Définition de classe de comptabilisation", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__accountTypeName": "Nom du type de compte", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__additionalCriteria": "Critères supplémentaires", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__canHaveAdditionalCriteria": "Possibilité d'avoir des critères supplémentaires", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__financeItemType": "Type article finance", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__id": "Code", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isDetailed": "Détaillée", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isLandedCostItemAllowed": "Article de type frais d'approche autorisé", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isNonStockItemAllowed": "Article hors stock autorisé", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isServiceItemAllowed": "Article de type service autorisé", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isStockItemAllowed": "Article de stock autorisé", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__legislation": "Législation", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__postingClassType": "Type de classe de comptabilisation", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__setupId": "Code paramétrage", "@sage/xtrem-finance-data/nodes__posting_class_definition__secondary_criteria_incorrect_value": "Le second critère doit être de type taxe.", "@sage/xtrem-finance-data/nodes__posting_class_definition__secondary_criteria_not_allowed": "Vous ne pouvez pas renseigner un critère secondaire.", "@sage/xtrem-finance-data/nodes__posting_class_definition__tax_solution_control": "Les solutions de taxe pour cette législation doivent comporter au moins une catégorie de taxe obligatoire.", "@sage/xtrem-finance-data/nodes__posting_class_definition__update_is_detailed_not_allowed": "Vous ne pouvez pas modifier l'option 'Détaillée' parce que la classe de comptabilisation {{ postingClassName }} est liée à cette définition.", "@sage/xtrem-finance-data/nodes__posting_class_line__already_used_posting_class_definition": "Cette définition de classe de comptabilisation est déjà utilisée par une autre ligne.", "@sage/xtrem-finance-data/nodes__posting_class_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__posting_class_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__posting_class_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__posting_class_line__node_name": "Ligne de classe de comptabilisation", "@sage/xtrem-finance-data/nodes__posting_class_line__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__posting_class_line__property__accountId": "Code de compte", "@sage/xtrem-finance-data/nodes__posting_class_line__property__chartOfAccount": "Plan comptable", "@sage/xtrem-finance-data/nodes__posting_class_line__property__definition": "Définition", "@sage/xtrem-finance-data/nodes__posting_class_line__property__details": "Détails", "@sage/xtrem-finance-data/nodes__posting_class_line__property__hasDetails": "Détails", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isLandedCostItemAllowed": "Article de type frais d'approche autorisé", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isNonStockItemAllowed": "Article hors stock autorisé", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isServiceItemAllowed": "Article de type service autorisé", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isStockItemAllowed": "Article de stock autorisé", "@sage/xtrem-finance-data/nodes__posting_class_line__property__postingClass": "Classe de comptabilisation", "@sage/xtrem-finance-data/nodes__posting_class_line__property__updateAccountTaxManagement": "Mise à jour gestion des taxe de comptes", "@sage/xtrem-finance-data/nodes__posting_class_line__query__getPostingClassAccounts": "Obtenir le comptes de classes de comptabilisation", "@sage/xtrem-finance-data/nodes__posting_class_line__query__getPostingClassAccounts__failed": "Échec d'obtention des comptes de classes de comptabilisation.", "@sage/xtrem-finance-data/nodes__posting_class_line__query__getPostingClassAccounts__parameter__postingClassDefinition": "Définition de classe de comptabilisation", "@sage/xtrem-finance-data/nodes__posting_class_line__wrong_account_tax_management": "Sélectionner une gestion de comptes de type 'Taxe'.", "@sage/xtrem-finance-data/nodes__posting_class_line__wrong_posting_class_definition_is_detailed": "Lorsque la classe de comptabilisation a un nom, seules les définitions de classes de comptabilisation ayant la case à cocher 'Détaillée' activée sont autorisées. Lorsque la classe de comptabilisation n'a pas de nom, seules les définitions de classes de comptabilisation ayant la case à cocher 'Détaillée' inactive sont autorisées.", "@sage/xtrem-finance-data/nodes__posting_class_line__wrong_posting_class_definition_type": "La définition de la classe de comptabilisation doit être de type {{ postingClassType }}.", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__mutation__getTaxCategoryIds": "Obtenir codes de catégorie de taxe", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__mutation__getTaxCategoryIds__failed": "Échec d'obtention des codes de catégorie de taxe.", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__mutation__getTaxCategoryIds__parameter__legislation": "Législation", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__no_secondary_criteria_defined": "Cette classe de comptabilisation n'a pas l'option de critère secondaire sélectionnée.", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__node_name": "Détail de ligne de classe de comptabilisation", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__property__postingClassLine": "Ligne de classe de comptabilisation", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__property__tax": "Taxe", "@sage/xtrem-finance-data/nodes__tax__posting_key_invalid": "La clé de comptabilisation doit être un numéro compris entre 1 et 9999.", "@sage/xtrem-finance-data/nodes__tax__posting_key_wrong_country": "Vous pouvez uniquement renseigner une valeur si le pays est Allemagne.", "@sage/xtrem-finance-data/package__name": "Paramétrage finance", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension____navigationPanel__listItem__datevId__title": "Code DATEV", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension____navigationPanel__listItem__line11__title": "Classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__datevId____title": "Code DATEV", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____lookupDialogTitle": "Sélectionner la section 01", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____title": "Section 01", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____lookupDialogTitle": "Sélectionner la section 02", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____title": "Section 02", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____lookupDialogTitle": "Sélectionner la section 03", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____title": "Section 03", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____lookupDialogTitle": "Sélectionner la section 04", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____title": "Section 04", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____lookupDialogTitle": "Sélectionner la section 05", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____title": "Section 05", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____lookupDialogTitle": "Sélectionner la section 06", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____title": "Section 06", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____lookupDialogTitle": "Sélectionner la section 07", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____title": "Section 07", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____lookupDialogTitle": "Sélectionner la section 08", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____title": "Section 08", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____lookupDialogTitle": "Sélectionner la section 09", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____title": "Section 09", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____lookupDialogTitle": "Sélectionner la section 10", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____title": "Section 10", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____lookupDialogTitle": "Sélectionner la section 11", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____title": "Section 11", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____lookupDialogTitle": "Sélectionner la section 12", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____title": "Section 12", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____lookupDialogTitle": "Sélectionner la section 13", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____title": "Section 13", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____lookupDialogTitle": "Sélectionner la section 14", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____title": "Section 14", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____lookupDialogTitle": "Sélectionner la section 15", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____title": "Section 15", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____lookupDialogTitle": "Sélectionner la section 16", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____title": "Section 16", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____lookupDialogTitle": "Sélectionner la section 17", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____title": "Section 17", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____lookupDialogTitle": "Sélectionner la section 18", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____title": "Section 18", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____lookupDialogTitle": "Sélectionner la section 19", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____title": "Section 19", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____lookupDialogTitle": "Sélectionner la section 20", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____title": "Section 20", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimensionBlock____title": "Sections", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____columns__title__attributeType__id": "Type d'attribut", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____lookupDialogTitle": "Sé<PERSON><PERSON><PERSON> le collaborateur", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____title": "Collaborateur", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__postingClass____lookupDialogTitle": "Sélectionner la classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__postingClass____title": "Classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____columns__title__attributeType__id": "Type d'attribut", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____lookupDialogTitle": "Sélectionner l'affaire", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____title": "<PERSON>e", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____columns__title__attributeType__id": "Type attribut", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____lookupDialogTitle": "Sélectionner la tâche", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__company_extension____navigationPanel__listItem__datevConsultantNumber__title": "Numéro du consultant DATEV", "@sage/xtrem-finance-data/page-extensions__company_extension____navigationPanel__listItem__datevCustomerNumber__title": "Numéro client DATEV", "@sage/xtrem-finance-data/page-extensions__company_extension__addDefaultDimension____title": "<PERSON><PERSON><PERSON> ligne", "@sage/xtrem-finance-data/page-extensions__company_extension__addDimensionAttributeLine____title": "Sections et attributs obligatoires", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____columns__title__id": "Nom", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____columns__title__type": "Type analytique", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____title": "Sections demandées", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeTypes____columns__title__analyticalMeasureType": "Type analytique", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeTypes____title": "Attributs", "@sage/xtrem-finance-data/page-extensions__company_extension__bankAccount____lookupDialogTitle": "Sélectionnez le compte bancaire", "@sage/xtrem-finance-data/page-extensions__company_extension__bankAccount____title": "Compte bancaire par défaut", "@sage/xtrem-finance-data/page-extensions__company_extension__country____columns__title__legislation__name": "Législation", "@sage/xtrem-finance-data/page-extensions__company_extension__datevConsultantNumber____title": "Numéro du consultant DATEV", "@sage/xtrem-finance-data/page-extensions__company_extension__datevCustomerNumber____title": "Numéro client DATEV", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____columns__columns__company__name__title": "Nom", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____columns__columns__company__name__title__2": "Code", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____columns__title__company__name": "Société", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____title": "Attributs", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributesAndDimensions____columns__title__document": "Document et origine", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributesAndDimensions____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributesAndDimensions____title": "Règles de sections par défaut", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____columns__columns__company__name__title": "Nom", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____columns__columns__company__name__title__2": "Code", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____columns__title__company__name": "Société", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____title": "Sections", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionsBlock____title": "Sections", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionsSection____title": "Sections", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionTypes____columns__title__analyticalMeasureType": "Type analytique", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionTypes____title": "Sections", "@sage/xtrem-finance-data/page-extensions__company_extension__doApPosting____title": "Comptabilisation factures fournisseurs", "@sage/xtrem-finance-data/page-extensions__company_extension__doArPosting____title": "Comptabilisation factures clients", "@sage/xtrem-finance-data/page-extensions__company_extension__doNonAbsorbedPosting____title": "Comptabilisation de montant non-absorbée", "@sage/xtrem-finance-data/page-extensions__company_extension__doStockPosting____title": "Comptabilisation stock", "@sage/xtrem-finance-data/page-extensions__company_extension__doWipPosting____title": "Comptabilisation en-cours", "@sage/xtrem-finance-data/page-extensions__company_extension__financePostingBlock____title": "Finance", "@sage/xtrem-finance-data/page-extensions__company_extension__manufacturingPostingBlock____title": "Production", "@sage/xtrem-finance-data/page-extensions__company_extension__postingClass____columns__title__type": "Type", "@sage/xtrem-finance-data/page-extensions__company_extension__postingClass____lookupDialogTitle": "Sélectionner la classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__company_extension__postingClass____title": "Classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__company_extension__postingSection____title": "Comptabilisation", "@sage/xtrem-finance-data/page-extensions__company_extension__selectAttributeAndDimensionBlock____title": "Gestion", "@sage/xtrem-finance-data/page-extensions__company_extension__selectAttributeAndDimensionSection____title": "Sections demandées", "@sage/xtrem-finance-data/page-extensions__company_extension__selectDimensionAndAttributeTypes____columns__title__id": "Nom", "@sage/xtrem-finance-data/page-extensions__company_extension__selectDimensionAndAttributeTypes____columns__title__type": "Type analytique", "@sage/xtrem-finance-data/page-extensions__company_extension__selectDimensionAndAttributeTypes____title": "Sections demandées", "@sage/xtrem-finance-data/page-extensions__company_extension__stockPostingBlock____title": "Stock", "@sage/xtrem-finance-data/page-extensions__company_extension__taxEngine____title": "Package de calcul de taxe", "@sage/xtrem-finance-data/page-extensions__company_extension__taxManagementBlock____title": "Gestion taxes", "@sage/xtrem-finance-data/page-extensions__customer_extension____navigationPanel__listItem__datevId__title": "Code DATEV", "@sage/xtrem-finance-data/page-extensions__customer_extension____navigationPanel__listItem__line11__title": "Classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__customer_extension__datevId____title": "Code DATEV", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____lookupDialogTitle": "Sélectionner la section 01", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____title": "Section 01", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____lookupDialogTitle": "Sélectionner la section 02", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____title": "Section 02", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____lookupDialogTitle": "Sélectionner la section 03", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____title": "Section 03", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____lookupDialogTitle": "Sélectionner la section 04", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____title": "Section 04", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____lookupDialogTitle": "Sélectionner la section 05", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____title": "Section 05", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____lookupDialogTitle": "Sélectionner la section 06", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____title": "Section 06", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____lookupDialogTitle": "Sélectionner la section 07", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____title": "Section 07", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____lookupDialogTitle": "Sélectionner la section 08", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____title": "Section 08", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____lookupDialogTitle": "Sélectionner la section 09", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____title": "Section 09", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____lookupDialogTitle": "Sélectionner la section 10", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____title": "Section 10", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____lookupDialogTitle": "Sélectionner la section 11", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____title": "Section 11", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____lookupDialogTitle": "Sélectionner la section 12", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____title": "Section 12", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____lookupDialogTitle": "Sélectionner la section 13", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____title": "Section 13", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____lookupDialogTitle": "Sélectionner la section 14", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____title": "Section 14", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____lookupDialogTitle": "Sélectionner la section 15", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____title": "Section 15", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____lookupDialogTitle": "Sélectionner la section 16", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____title": "Section 16", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____lookupDialogTitle": "Sélectionner la section 17", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____title": "Section 17", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____lookupDialogTitle": "Sélectionner la section 18", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____title": "Section 18", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____lookupDialogTitle": "Sélectionner la section 19", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____title": "Section 19", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____lookupDialogTitle": "Sélectionner la section 20", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____title": "Section 20", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimensionBlock____title": "Sections", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____columns__title__attributeType__id": "Type attribut", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____lookupDialogTitle": "Sé<PERSON><PERSON><PERSON> le collaborateur", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____title": "Collaborateur", "@sage/xtrem-finance-data/page-extensions__customer_extension__postingClass____columns__title__type": "Type", "@sage/xtrem-finance-data/page-extensions__customer_extension__postingClass____lookupDialogTitle": "Sélectionner la classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__customer_extension__postingClass____title": "Classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____columns__title__attributeType__id": "Type attribut", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____lookupDialogTitle": "Sélectionner l'affaire", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____title": "<PERSON>e", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____columns__title__attributeType__id": "Type attribut", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____lookupDialogTitle": "Sélectionner la tâche", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension____navigationPanel__listItem__postingClass__title": "Classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____lookupDialogTitle": "Sélectionner section 01", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____title": "Section 01", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____lookupDialogTitle": "Sélectionner section 02", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____title": "Section 02", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____lookupDialogTitle": "Sélectionner section 03", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____title": "Section 03", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____lookupDialogTitle": "Sélectionner section 04", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____title": "Section 04", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____lookupDialogTitle": "Sélectionner section 05", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____title": "Section 05", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____lookupDialogTitle": "Sélectionner section 06", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____title": "Section 06", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____lookupDialogTitle": "Sélectionner section 07", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____title": "Section 07", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____lookupDialogTitle": "Sélectionner section 08", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____title": "Section 08", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____lookupDialogTitle": "Sélectionner section 09", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____title": "Section 09", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____lookupDialogTitle": "Sélectionner section 10", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____title": "Section 10", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____lookupDialogTitle": "Sélectionner section 11", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____title": "Section 11", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____lookupDialogTitle": "Sélectionner section 12", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____title": "Section 12", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____lookupDialogTitle": "Sélectionner section 13", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____title": "Section 13", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____lookupDialogTitle": "Sélectionner section 14", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____title": "Section 14", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____lookupDialogTitle": "Sélectionner section 15", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____title": "Section 15", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____lookupDialogTitle": "Sélectionner section 16", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____title": "Section 16", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____lookupDialogTitle": "Sélectionner section 17", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____title": "Section 17", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____lookupDialogTitle": "Sélectionner section 18", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____title": "Section 18", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____lookupDialogTitle": "Sélectionner section 19", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____title": "Section 19", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____lookupDialogTitle": "Sélectionner section 20", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____title": "Section 20", "@sage/xtrem-finance-data/page-extensions__item_extension__dimensionBlock____title": "Sections", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____columns__title__attributeType__id": "Type attribut", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____lookupDialogTitle": "Sélectionner collaborateur", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____title": "Collaborateur", "@sage/xtrem-finance-data/page-extensions__item_extension__postingClass____columns__title__type": "Type", "@sage/xtrem-finance-data/page-extensions__item_extension__postingClass____lookupDialogTitle": "Sélectionner la classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__item_extension__postingClass____title": "Classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__item_extension__project____columns__title__attributeType__id": "Type attribut", "@sage/xtrem-finance-data/page-extensions__item_extension__project____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__project____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__project____lookupDialogTitle": "Sélectionner affaire", "@sage/xtrem-finance-data/page-extensions__item_extension__project____title": "<PERSON>e", "@sage/xtrem-finance-data/page-extensions__item_extension__task____columns__title__attributeType__id": "Type attribut", "@sage/xtrem-finance-data/page-extensions__item_extension__task____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__item_extension__task____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__item_extension__task____lookupDialogTitle": "Sélectionner tâche", "@sage/xtrem-finance-data/page-extensions__item_extension__task____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__labor_resource_extension__postingClass____lookupDialogTitle": "Sélectionner la classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__labor_resource_extension__postingClass____title": "Classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__machine_resource_extension__postingClass____lookupDialogTitle": "Sélectionner la classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__machine_resource_extension__postingClass____title": "Classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____lookupDialogTitle": "Sélectionner la section 01", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____title": "Section 01", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____lookupDialogTitle": "Sélectionner la section 02", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____title": "Section 02", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____lookupDialogTitle": "Sélectionner la section 03", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____title": "Section 03", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____lookupDialogTitle": "Sélectionner la section 04", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____title": "Section 04", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____lookupDialogTitle": "Sélectionner la section 05", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____title": "Section 05", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____lookupDialogTitle": "Sélectionner la section 06", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____title": "Section 06", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____lookupDialogTitle": "Sélectionner la section 07", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____title": "Section 07", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____lookupDialogTitle": "Sélectionner la section 08", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____title": "Section 08", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____lookupDialogTitle": "Sélectionner la section 09", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____title": "Section 09", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____lookupDialogTitle": "Sélectionner la section 10", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____title": "Section 10", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____lookupDialogTitle": "Sélectionner la section 11", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____title": "Section 11", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____lookupDialogTitle": "Sélectionner la section 12", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____title": "Section 12", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____lookupDialogTitle": "Sélectionner la section 13", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____title": "Section 13", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____lookupDialogTitle": "Sélectionner la section 14", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____title": "Section 14", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____lookupDialogTitle": "Sélectionner la section 15", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____title": "Section 15", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____lookupDialogTitle": "Sélectionner la section 16", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____title": "Section 16", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____lookupDialogTitle": "Sélectionner la section 17", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____title": "Section 17", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____lookupDialogTitle": "Sélectionner la section 18", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____title": "Section 18", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____lookupDialogTitle": "Sélectionner la section 19", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____title": "Section 19", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____lookupDialogTitle": "Sélectionner la section 20", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____title": "Section 20", "@sage/xtrem-finance-data/page-extensions__site_extension__dimensionBlock____title": "Sections", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____columns__title__attributeType__id": "Type d'attribut", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____lookupDialogTitle": "Sé<PERSON><PERSON><PERSON> le collaborateur", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____title": "Collaborateur", "@sage/xtrem-finance-data/page-extensions__site_extension__financialSection____title": "Financières", "@sage/xtrem-finance-data/page-extensions__site_extension__project____columns__title__attributeType__id": "Type attribut", "@sage/xtrem-finance-data/page-extensions__site_extension__project____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__project____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__project____lookupDialogTitle": "Sélectionner l'affaire", "@sage/xtrem-finance-data/page-extensions__site_extension__project____title": "<PERSON>e", "@sage/xtrem-finance-data/page-extensions__site_extension__task____columns__title__attributeType__id": "Type attribut", "@sage/xtrem-finance-data/page-extensions__site_extension__task____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__site_extension__task____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__site_extension__task____lookupDialogTitle": "Sélectionner la tâche", "@sage/xtrem-finance-data/page-extensions__site_extension__task____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension____navigationPanel__listItem__datevId__title": "Code DATEV", "@sage/xtrem-finance-data/page-extensions__supplier_extension____navigationPanel__listItem__line11__title": "Classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__supplier_extension__datevId____title": "Code DATEV", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____lookupDialogTitle": "Sélectionner la section 01", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____title": "Section 01", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____lookupDialogTitle": "Sélectionner la section 02", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____title": "Section 02", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____lookupDialogTitle": "Sélectionner la section 03", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____title": "Section 03", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____lookupDialogTitle": "Sélectionner la section 04", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____title": "Section 04", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____lookupDialogTitle": "Sélectionner la section 05", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____title": "Section 05", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____lookupDialogTitle": "Sélectionner la section 06", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____title": "Section 06", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____lookupDialogTitle": "Sélectionner la section 07", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____title": "Section 07", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____lookupDialogTitle": "Sélectionner la section 08", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____title": "Section 08", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____lookupDialogTitle": "Sélectionner la section 09", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____title": "Section 09", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____lookupDialogTitle": "Sélectionner la section 10", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____title": "Section 10", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____lookupDialogTitle": "Sélectionner la section 11", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____title": "Section 11", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____lookupDialogTitle": "Sélectionner la section 12", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____title": "Section 12", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____lookupDialogTitle": "Sélectionner la section 13", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____title": "Section 13", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____lookupDialogTitle": "Sélectionner la section 14", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____title": "Section 14", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____lookupDialogTitle": "Sélectionner la section 15", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____title": "Section 15", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____lookupDialogTitle": "Sélectionner la section 16", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____title": "Section 16", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____lookupDialogTitle": "Sélectionner la section 17", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____title": "Section 17", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____lookupDialogTitle": "Sélectionner la section 18", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____title": "Section 18", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____lookupDialogTitle": "Sélectionner la section 19", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____title": "Section 19", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____lookupDialogTitle": "Sélectionner la section 20", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____title": "Section 20", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimensionBlock____title": "Sections", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____columns__title__attributeType__id": "Type attribut", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____lookupDialogTitle": "Sé<PERSON><PERSON><PERSON> le collaborateur", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____title": "Collaborateur", "@sage/xtrem-finance-data/page-extensions__supplier_extension__postingClass____columns__title__type": "Type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__postingClass____lookupDialogTitle": "Sélectionner la classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__supplier_extension__postingClass____title": "Classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____columns__title__attributeType__id": "Type attribut", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____lookupDialogTitle": "Sélectionner l'affaire", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____title": "<PERSON>e", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____columns__title__attributeType__id": "Type attribut", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____columns__title__id": "Code", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____columns__title__name": "Nom", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____lookupDialogTitle": "Sélectionner la tâche", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionBlock____title": "Finance", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionCriteriaBlock____title": "Critères", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionDocumentType____title": "Type de document", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___createStamp": "Envoyée", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___id": "Code", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__batchId": "Code batch", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__documentNumber": "N° document", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__documentType": "Type document", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__message": "Message", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__postingStatus": "Statut", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__status": "Statut de notification", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__targetDocumentType": "Type document cible", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____title": "Résultats", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionSection____title": "Finance", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionStatus____title": "Statut", "@sage/xtrem-finance-data/page-extensions__tax_extension____navigationPanel__listItem__line7__title": "Classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__tax_extension____navigationPanel__listItem__postingKey__title": "Clé de comptabilisation", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingClass____columns__title__type": "Type", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingClass____lookupDialogTitle": "Sélectionner la classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingClass____title": "Classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingKey____title": "Clé de comptabilisation", "@sage/xtrem-finance-data/page-extensions__tool_resource_extension__postingClass____lookupDialogTitle": "Sélectionner la classe de comptabilisation", "@sage/xtrem-finance-data/page-extensions__tool_resource_extension__postingClass____title": "Classe de comptabilisation", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__datevId__title": "Code DATEV", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line_4__title": "Saisie directe interdite", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line_5__title": "Plan comptable", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line3__title": "Collectif", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line4__title": "Saisie directe interdite", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line5__title": "Plan comptable", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line6__title": "Gestion des taxes", "@sage/xtrem-finance-data/pages__account____navigationPanel__optionsMenu__title": "Plan comptable FR", "@sage/xtrem-finance-data/pages__account____navigationPanel__optionsMenu__title__2": "Plan comptable US", "@sage/xtrem-finance-data/pages__account____objectTypePlural": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__account____objectTypeSingular": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__account____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__account___id____title": "Code", "@sage/xtrem-finance-data/pages__account__attributeDimensionTypes____columns__title__type": "Type analytique", "@sage/xtrem-finance-data/pages__account__attributeDimensionTypes____title": "Sections demandées", "@sage/xtrem-finance-data/pages__account__attributeDimensionTypesBlock____title": "Sections demandées", "@sage/xtrem-finance-data/pages__account__chartOfAccount____lookupDialogTitle": "Sélectionner le plan comptable", "@sage/xtrem-finance-data/pages__account__chartOfAccount____title": "Plan comptable", "@sage/xtrem-finance-data/pages__account__datevIdString____title": "Code DATEV", "@sage/xtrem-finance-data/pages__account__datevSection____title": "DATEV", "@sage/xtrem-finance-data/pages__account__empty_tax_warning": "<PERSON><PERSON> de<PERSON> renseigner le code taxe lorsque le compte automatique est activé.", "@sage/xtrem-finance-data/pages__account__headerSection____title": "Section d'en-tête", "@sage/xtrem-finance-data/pages__account__id____title": "Code", "@sage/xtrem-finance-data/pages__account__isActive____title": "Actif", "@sage/xtrem-finance-data/pages__account__isAutomaticAccount____title": "Compte automatique", "@sage/xtrem-finance-data/pages__account__isControl____title": "Collectif", "@sage/xtrem-finance-data/pages__account__isDirectEntryForbidden____title": "Saisie directe interdite", "@sage/xtrem-finance-data/pages__account__isDirectPostingForbidden____title": "Imputation directe interdite", "@sage/xtrem-finance-data/pages__account__mainSection____title": "Général", "@sage/xtrem-finance-data/pages__account__name____title": "Nom", "@sage/xtrem-finance-data/pages__account__option_menu____title__all": "Toutes", "@sage/xtrem-finance-data/pages__account__save____title": "Enregistrer", "@sage/xtrem-finance-data/pages__account__tax____columns__lookupDialogTitle__country": "Sélectionner le pays", "@sage/xtrem-finance-data/pages__account__tax____title": "Taxe", "@sage/xtrem-finance-data/pages__account__taxManagement____title": "Gestion de la taxe", "@sage/xtrem-finance-data/pages__account__TaxManagementBlock____title": "Gestion de la taxe", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__item__title": "Article", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__line3__title": "Type d'attribut", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__line3Right__title": "Limité au nom", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__restrictedTo__title": "Limité au code", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__site__title": "Site", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__optionsMenu__title": "<PERSON>e", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__optionsMenu__title__2": "Collaborateur", "@sage/xtrem-finance-data/pages__attribute____objectTypePlural": "Attributs", "@sage/xtrem-finance-data/pages__attribute____objectTypeSingular": "Attribut", "@sage/xtrem-finance-data/pages__attribute____subtitle": "Attribut", "@sage/xtrem-finance-data/pages__attribute____title": "Attribut", "@sage/xtrem-finance-data/pages__attribute__attributeRestrictedTo____lookupDialogTitle": "Sélectionner un attribut limité à", "@sage/xtrem-finance-data/pages__attribute__attributeType____lookupDialogTitle": "Sélectionner le type d'attribut", "@sage/xtrem-finance-data/pages__attribute__item____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-finance-data/pages__attribute__mainSection____title": "Général", "@sage/xtrem-finance-data/pages__attribute__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__listItem__line_4__title": "Filtre", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__listItem__line3__title": "Lien de node", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__listItem__line4__title": "Filtre", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__optionsMenu__title__3": "Inactif", "@sage/xtrem-finance-data/pages__attribute_type____objectTypePlural": "Types d'attributs", "@sage/xtrem-finance-data/pages__attribute_type____objectTypeSingular": "Type d'attribut", "@sage/xtrem-finance-data/pages__attribute_type____title": "Type d'attribut", "@sage/xtrem-finance-data/pages__attribute_type__attributeTypeRestrictedTo____lookupDialogTitle": "Sélectionner un type d'attribut limité à", "@sage/xtrem-finance-data/pages__attribute_type__attributeTypeRestrictedTo____title": "Limité à", "@sage/xtrem-finance-data/pages__attribute_type__id____title": "Code", "@sage/xtrem-finance-data/pages__attribute_type__isActive____title": "Actif", "@sage/xtrem-finance-data/pages__attribute_type__linked_to_not_valid": "L'attribut {{linkedTo}} n'est pas valide.", "@sage/xtrem-finance-data/pages__attribute_type__linkedTo____title": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__attribute_type__mainSection____title": "Général", "@sage/xtrem-finance-data/pages__attribute_type__name____title": "Nom", "@sage/xtrem-finance-data/pages__attribute_type__nodeLink____title": "Lien de node", "@sage/xtrem-finance-data/pages__attribute_type__queryFilter____title": "Filtre", "@sage/xtrem-finance-data/pages__bank_account____navigationPanel__listItem__line_4__title": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__bank_account____navigationPanel__listItem__line3__title": "Site financier", "@sage/xtrem-finance-data/pages__bank_account____navigationPanel__listItem__line4__title": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__bank_account____objectTypePlural": "Comptes bancaires", "@sage/xtrem-finance-data/pages__bank_account____objectTypeSingular": "Compte bancaire", "@sage/xtrem-finance-data/pages__bank_account____title": "Compte bancaire", "@sage/xtrem-finance-data/pages__bank_account__currency____columns__title__id": "Code ISO 4217", "@sage/xtrem-finance-data/pages__bank_account__currency____columns__title__symbol": "Symbole", "@sage/xtrem-finance-data/pages__bank_account__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-finance-data/pages__bank_account__currency____placeholder": "Sélectionner la devise", "@sage/xtrem-finance-data/pages__bank_account__currency____title": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__bank_account__financialSite____columns__title__currency__name": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__bank_account__financialSite____columns__title__id": "Code ", "@sage/xtrem-finance-data/pages__bank_account__financialSite____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__bank_account__financialSite____lookupDialogTitle": "Sélectionner le site financier", "@sage/xtrem-finance-data/pages__bank_account__financialSite____title": "Site financier", "@sage/xtrem-finance-data/pages__bank_account__headerSection____title": "Section d'en-tête", "@sage/xtrem-finance-data/pages__bank_account__id____title": "Code", "@sage/xtrem-finance-data/pages__bank_account__isActive____title": "Actif", "@sage/xtrem-finance-data/pages__bank_account__mainSection____title": "Informations", "@sage/xtrem-finance-data/pages__bank_account__name____title": "Nom", "@sage/xtrem-finance-data/pages__bank-account__option_menu____title__all": "Tous", "@sage/xtrem-finance-data/pages__business_entity_customer__datev_id": "Code DATEV", "@sage/xtrem-finance-data/pages__business_entity_customer__wrong_datev_id": "Renseignez un nombre entre {{first}} et {{last}}.", "@sage/xtrem-finance-data/pages__business_entity_customer_extension__datev_id_warning": "<PERSON><PERSON> de<PERSON> renseigner le code DATEV avant d'extraire les données lorsque l'intégration DATEV est activée.", "@sage/xtrem-finance-data/pages__company__dimensions_setup_update_confirmation": "Confirmer le paramétrage", "@sage/xtrem-finance-data/pages__company__dimensions_setup_update_confirmation_apply": "Vous êtes sur le point d'appliquer ce paramétrage à tous les documents futurs et en attente.", "@sage/xtrem-finance-data/pages__company_extension__duplicate_error": "Il existe des documents en double. Assurez-vous que chaque ligne est associée à un document unique.", "@sage/xtrem-finance-data/pages__company_extension__project_task_warning": "Le {{attributeName}} est demandé lorsqu vous renseignez un {{restrictedToName}}.", "@sage/xtrem-finance-data/pages__datev_configuration____title": "Configuration DATEV", "@sage/xtrem-finance-data/pages__datev_configuration__customer_id_range": "De {{start}} à {{end}}", "@sage/xtrem-finance-data/pages__datev_configuration__customerIdRange____title": "Borne code client", "@sage/xtrem-finance-data/pages__datev_configuration__customerSupplierLength____title": "Longueur code client et fournisseur", "@sage/xtrem-finance-data/pages__datev_configuration__isActive____title": "Active", "@sage/xtrem-finance-data/pages__datev_configuration__mainSection____title": "Général", "@sage/xtrem-finance-data/pages__datev_configuration__save____title": "Enregistrer", "@sage/xtrem-finance-data/pages__datev_configuration__skrCoaString____title": "SKR", "@sage/xtrem-finance-data/pages__datev_configuration__supplier_id_range": "De {{start}} à {{end}}", "@sage/xtrem-finance-data/pages__datev_configuration__supplierIdRange____title": "Borne code fournisseur", "@sage/xtrem-finance-data/pages__datev_configuration_save_warnings": "Avertissements lors de l'enregistrement :", "@sage/xtrem-finance-data/pages__dimension____navigationPanel__listItem__line3__title": "Axe", "@sage/xtrem-finance-data/pages__dimension____objectTypePlural": "Sections", "@sage/xtrem-finance-data/pages__dimension____objectTypeSingular": "Section", "@sage/xtrem-finance-data/pages__dimension____title": "Section", "@sage/xtrem-finance-data/pages__dimension__dimensionType____lookupDialogTitle": "Sélectionner l'axe", "@sage/xtrem-finance-data/pages__dimension__id____title": "Code", "@sage/xtrem-finance-data/pages__dimension__isActive____title": "Active", "@sage/xtrem-finance-data/pages__dimension__mainSection____title": "Général", "@sage/xtrem-finance-data/pages__dimension__name____title": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel____title": "Sections", "@sage/xtrem-finance-data/pages__dimension_panel__apply____title": "Appliquer à toutes les lignes", "@sage/xtrem-finance-data/pages__dimension_panel__applyAll____title": "Appliquer à tout", "@sage/xtrem-finance-data/pages__dimension_panel__applyOnNew____title": "Appliquer aux nouvelles lignes uniquement", "@sage/xtrem-finance-data/pages__dimension_panel__applyReleasedItem____title": "Appliquer à l'article lancé uniquement", "@sage/xtrem-finance-data/pages__dimension_panel__cancel____title": "Annuler", "@sage/xtrem-finance-data/pages__dimension_panel__defaulted_from_item_plural": "Les valeurs {{itemList}} proviennent par défaut de l'article et ne s'affichent pas à ce niveau. <PERSON><PERSON> de<PERSON> renseigner ces valeurs sur la ligne pour chaque article.", "@sage/xtrem-finance-data/pages__dimension_panel__defaulted_from_item_singular": "La valeur {{itemList}} provient par défaut de l'article et ne s'affiche pas à ce niveau. <PERSON><PERSON> de<PERSON> renseigner cette valeur sur la ligne pour chaque article.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____lookupDialogTitle": "Sélectionner la section 01", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____title": "Section 01", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____lookupDialogTitle": "Sélectionner la section 02", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____title": "Section 02", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____lookupDialogTitle": "Sélectionner la section 03", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____title": "Section 03", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____lookupDialogTitle": "Sélectionner la section 04", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____title": "Section 04", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____lookupDialogTitle": "Sélectionner la section 05", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____title": "Section 05", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____lookupDialogTitle": "Sélectionner la section 06", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____title": "Section 06", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____lookupDialogTitle": "Sélectionner la section 07", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____title": "Section 07", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____lookupDialogTitle": "Sélectionner la section 08", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____title": "Section 08", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____lookupDialogTitle": "Sélectionner la section 09", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____title": "Section 09", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____lookupDialogTitle": "Sélectionner la section 10", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____title": "Section 10", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____lookupDialogTitle": "Sélectionner la section 11", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____title": "Section 11", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____lookupDialogTitle": "Sélectionner la section 12", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____title": "Section 12", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____lookupDialogTitle": "Sélectionner la section 13", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____title": "Section 13", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____lookupDialogTitle": "Sélectionner la section 14", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____title": "Section 14", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____lookupDialogTitle": "Sélectionner la section 15", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____title": "Section 15", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____lookupDialogTitle": "Sélectionner la section 16", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____title": "Section 16", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____lookupDialogTitle": "Sélectionner la section 17", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____title": "Section 17", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____lookupDialogTitle": "Sélectionner la section 18", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____title": "Section 18", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____lookupDialogTitle": "Sélectionner la section 19", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____title": "Section 19", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__columns__dimensionType__docProperty__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__title__dimensionType__docProperty": "Axe", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____lookupDialogTitle": "Sélectionner la section 20", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____title": "Section 20", "@sage/xtrem-finance-data/pages__dimension_panel__employee____columns__title__attributeType__id": "Type d'attribut", "@sage/xtrem-finance-data/pages__dimension_panel__employee____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__employee____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__employee____lookupDialogTitle": "Sé<PERSON><PERSON><PERSON> le collaborateur", "@sage/xtrem-finance-data/pages__dimension_panel__employee____title": "Collaborateur", "@sage/xtrem-finance-data/pages__dimension_panel__ok____title": "OK", "@sage/xtrem-finance-data/pages__dimension_panel__page_without_dimensions": "Cette page doit avoir des sections et des attributs.", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__attributeType__id": "Type d'attribut", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__site__id": "Site", "@sage/xtrem-finance-data/pages__dimension_panel__project____lookupDialogTitle": "Sélectionner l'affaire", "@sage/xtrem-finance-data/pages__dimension_panel__project____title": "<PERSON>e", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__attributeType__id": "Type attribut", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__id": "Code", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__item__id": "Article", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__name": "Nom", "@sage/xtrem-finance-data/pages__dimension_panel__task____lookupDialogTitle": "Sélectionner la tâche", "@sage/xtrem-finance-data/pages__dimension_panel__task____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__listItem__titleRight__title": "Propriété du document", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__optionsMenu__title__3": "Inactif", "@sage/xtrem-finance-data/pages__dimension_type____objectTypePlural": "Axes", "@sage/xtrem-finance-data/pages__dimension_type____objectTypeSingular": "Axe", "@sage/xtrem-finance-data/pages__dimension_type____title": "Axe", "@sage/xtrem-finance-data/pages__dimension_type___id____title": "Code", "@sage/xtrem-finance-data/pages__dimension_type__docProperty____title": "Propriété document", "@sage/xtrem-finance-data/pages__dimension_type__isActive____title": "Actif", "@sage/xtrem-finance-data/pages__dimension_type__isUsed____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_type__mainSection____title": "Général", "@sage/xtrem-finance-data/pages__dimension_type__name____title": "Nom", "@sage/xtrem-finance-data/pages__journal____navigationPanel__listItem__line6__title": "Compteur", "@sage/xtrem-finance-data/pages__journal____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-finance-data/pages__journal____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-finance-data/pages__journal____objectTypePlural": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal____objectTypeSingular": "Journal", "@sage/xtrem-finance-data/pages__journal____title": "Journal", "@sage/xtrem-finance-data/pages__journal___id____title": "Code", "@sage/xtrem-finance-data/pages__journal__generalSection____title": "Général", "@sage/xtrem-finance-data/pages__journal__id____title": "Code", "@sage/xtrem-finance-data/pages__journal__isActive____title": "Actif", "@sage/xtrem-finance-data/pages__journal__isSubjectToGlTaxExcludedAmount____title": "Affectée au montant du GL HT", "@sage/xtrem-finance-data/pages__journal__legislation____lookupDialogTitle": "Sélectionner la législation", "@sage/xtrem-finance-data/pages__journal__legislation____placeholder": "Sélectionner...", "@sage/xtrem-finance-data/pages__journal__name____title": "Nom", "@sage/xtrem-finance-data/pages__journal__numberingBlock____title": "Numérotation", "@sage/xtrem-finance-data/pages__journal__sequence____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> le compteur", "@sage/xtrem-finance-data/pages__journal__sequence____title": "Compteur", "@sage/xtrem-finance-data/pages__journal__taxImpact____title": "Impact de taxe", "@sage/xtrem-finance-data/pages__journal_entry_type____objectTypePlural": "Types d'écritures", "@sage/xtrem-finance-data/pages__journal_entry_type____objectTypeSingular": "Type d'écriture", "@sage/xtrem-finance-data/pages__journal_entry_type____title": "Type d'écriture", "@sage/xtrem-finance-data/pages__journal_entry_type___id____title": "Code", "@sage/xtrem-finance-data/pages__journal_entry_type__documentType____title": "Type de document", "@sage/xtrem-finance-data/pages__journal_entry_type__generalSection____title": "Général", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____columns__title__accountTypeName": "Nom", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____columns__title__isDetailed": "Dé<PERSON>lé", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____columns__title__postingClassType": "Type classe comptabilisation", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____lookupDialogTitle": "Sélectionner le type de compte", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____title": "Type de compte", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAmountType____title": "Type montant", "@sage/xtrem-finance-data/pages__journal_entry_type__headerDescription____title": "Description", "@sage/xtrem-finance-data/pages__journal_entry_type__headerJournal____lookupDialogTitle": "Sélectionner le journal", "@sage/xtrem-finance-data/pages__journal_entry_type__headerJournal____title": "Journal", "@sage/xtrem-finance-data/pages__journal_entry_type__headerPostingDate____title": "Date de comptabilisation", "@sage/xtrem-finance-data/pages__journal_entry_type__headerSetupBlock____title": "Paramétrage en-tête", "@sage/xtrem-finance-data/pages__journal_entry_type__immediatePosting____title": "Imputation immédiate", "@sage/xtrem-finance-data/pages__journal_entry_type__isActive____title": "Actif", "@sage/xtrem-finance-data/pages__journal_entry_type__legislation____lookupDialogTitle": "Sélectionner la législation", "@sage/xtrem-finance-data/pages__journal_entry_type__legislation____placeholder": "Sélectionner la législation", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__columns__accountType__accountTypeName__title": "Nom", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__columns__accountType__accountTypeName__title__2": "Type classe comptabilisation", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__columns__accountType__accountTypeName__title__3": "Dé<PERSON>lé", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__accountType__accountTypeName": "Type compte", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__amountType": "Type montant", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__commonReference": "Référence commune", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__contraJournalEntryTypeLine__accountType__accountTypeName": "Type cpte contrepartie", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__contraJournalEntryTypeLine__amountType": "Type mnt cpte contrepartie", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isLandedCostItemAllowed": "Articles de type frais d'approche", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isNonStockItemAllowed": "Articles hors stock", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isServiceItemAllowed": "Articles de service", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isStockItemAllowed": "Articles de stock", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__movementType": "Type mouvement", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__sign": "<PERSON>s", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____dropdownActions__title": "Classe comptabilisation liée", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____title": "Paramétrage des lignes", "@sage/xtrem-finance-data/pages__journal_entry_type__name____title": "Nom", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____columns__title__account__composedDescription": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____columns__title__postingClass__name": "Nom classe comptabilisation", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__account__composedDescription": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__account__composedDescription__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__postingClass__name": "Nom classe comptabilisation", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__tax__name": "Taxe", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____title": "Comptes lignes", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____columns__title__account__composedDescription": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____columns__title__postingClass__name": "Nom classe comptabilisation", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__account__composedDescription": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__account__composedDescription__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__postingClass__name": "Nom classe comptabilisation", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__tax__name": "Taxe", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____title": "Comptes en<PERSON>tê<PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__side_panel_linked_posting_class_title": "Classe de comptabilisation liée", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelAccountTypeName____title": "Nom du type de compte", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelGeneralSection____title": "", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelHeaderAccountTypeName____title": "Nom du type de compte", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelHeaderBlock____title": "<PERSON>-tête", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelHeaderIsDetailed____title": "Dé<PERSON>lé", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelIsDetailed____title": "Dé<PERSON>lé", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelLinesBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__targetDocumentType____title": "Type document cible", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__listItem__line3__title": "Détaillée", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__2": "Société", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__3": "Client", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__4": "Article", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__5": "Ressource", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__6": "Fournisseur", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__7": "Taxe", "@sage/xtrem-finance-data/pages__posting_class____objectTypePlural": "Classes de comptabilisation", "@sage/xtrem-finance-data/pages__posting_class____objectTypeSingular": "Classe de comptabilisation", "@sage/xtrem-finance-data/pages__posting_class____title": "Classe de comptabilisation", "@sage/xtrem-finance-data/pages__posting_class___id____title": "Code", "@sage/xtrem-finance-data/pages__posting_class__accountTypes____columns__title__chartOfAccount__legislation__name": "Législation", "@sage/xtrem-finance-data/pages__posting_class__accountTypes____columns__title__definition__accountTypeName": "Nom type compte", "@sage/xtrem-finance-data/pages__posting_class__accountTypes____title": "Types de comptes", "@sage/xtrem-finance-data/pages__posting_class__addLine____title": "<PERSON><PERSON><PERSON> ligne", "@sage/xtrem-finance-data/pages__posting_class__copyAccounts____title": "<PERSON>pier comptes", "@sage/xtrem-finance-data/pages__posting_class__finance_item_type_not_valid": "Le type d'article finance {{financeItemType}} n'est pas valide.", "@sage/xtrem-finance-data/pages__posting_class__financeItemType____title": "Type article", "@sage/xtrem-finance-data/pages__posting_class__generalBlock____title": "Classe de comptabilisation", "@sage/xtrem-finance-data/pages__posting_class__generalSection____title": "Général", "@sage/xtrem-finance-data/pages__posting_class__id____title": "Code", "@sage/xtrem-finance-data/pages__posting_class__isDetailed____title": "Détaillée", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__account__name__title": "Nom", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__account__name__title__2": "Code", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__definition__accountTypeName__title": "Nom", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__definition__accountTypeName__title__2": "Code", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__definition__accountTypeName__title__3": "Dé<PERSON>lé", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__account__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__accountId": "Code compte", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__chartOfAccount__name": "Plan comptable", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__definition__accountTypeName": "Nom type compte", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isLandedCostItemAllowed": "Articles de type frais d'approche", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isNonStockItemAllowed": "Articles hors stock", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isServiceItemAllowed": "Articles de service", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isStockItemAllowed": "Articles de stock", "@sage/xtrem-finance-data/pages__posting_class__lines____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title": "Nom", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__2": "Code", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__3": "Nom", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__4": "Code", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__5": "Plan comptable", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__definition__accountTypeName__title": "Nom", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__definition__accountTypeName__title__2": "Code", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__tax__name__title": "Nom", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__tax__name__title__2": "Code", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__tax__name__title__3": "Pays", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__id": "Code compte", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__id__2": "Code compte", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__name__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__chartOfAccount__name": "Plan comptable", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__definition__accountTypeName": "Nom type compte", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__definition__additionalCriteria": "Critères supplémentaires", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isLandedCostItemAllowed": "Articles de type frais d'approche", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isNonStockItemAllowed": "Articles hors stock", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isServiceItemAllowed": "Articles de service", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isStockItemAllowed": "Articles de stock", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__tax__name": "Taxe", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__tax__taxCategory__name": "Catégorie de taxe", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__dropdownActions__title": "A<PERSON>ter détail", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__loadLinesBlock____title": "Définitions classes de comptabilisation", "@sage/xtrem-finance-data/pages__posting_class__loadLinesSection____title": "Charger lignes", "@sage/xtrem-finance-data/pages__posting_class__name____title": "Nom", "@sage/xtrem-finance-data/pages__posting_class__postingClass____columns__title__type": "Type", "@sage/xtrem-finance-data/pages__posting_class__postingClass____lookupDialogTitle": "Sélectionner la classe de comptabilisation", "@sage/xtrem-finance-data/pages__posting_class__postingClass____placeholder": "Sélectionner la classe de comptabilisation", "@sage/xtrem-finance-data/pages__posting_class__postingClass____title": "Modèle de classe de comptabilisation", "@sage/xtrem-finance-data/pages__posting_class__selectAll____title": "Sé<PERSON><PERSON><PERSON> toutes les lignes", "@sage/xtrem-finance-data/pages__posting_class__selectLines____title": "Sélectionner les lignes", "@sage/xtrem-finance-data/pages__posting_class__type____title": "Type", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__listItem__line_4__title": "Détaillée", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__listItem__line4__title": "Détaillée", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__listItem__titleRight__title": "Législation", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__2": "Client", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__3": "Article", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__4": "Ressource", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__5": "Fournisseur", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__6": "Taxe", "@sage/xtrem-finance-data/pages__posting_class_definition____objectTypePlural": "Définitions classes de comptabilisation", "@sage/xtrem-finance-data/pages__posting_class_definition____objectTypeSingular": "Définition classe de comptabilisation", "@sage/xtrem-finance-data/pages__posting_class_definition____title": "Définition classe de comptabilisation", "@sage/xtrem-finance-data/pages__posting_class_definition___id____title": "Code", "@sage/xtrem-finance-data/pages__posting_class_definition__accountTypeName____title": "Nom du type de compte", "@sage/xtrem-finance-data/pages__posting_class_definition__additionalCriteria____title": "Critères supplémentaires", "@sage/xtrem-finance-data/pages__posting_class_definition__financeItemType____title": "Type article", "@sage/xtrem-finance-data/pages__posting_class_definition__generalBlock____title": "Définition classe de comptabilisation", "@sage/xtrem-finance-data/pages__posting_class_definition__generalSection____title": "Général", "@sage/xtrem-finance-data/pages__posting_class_definition__id____title": "Code", "@sage/xtrem-finance-data/pages__posting_class_definition__isDetailed____title": "Détaillée", "@sage/xtrem-finance-data/pages__posting_class_definition__legislation____lookupDialogTitle": "Sélectionner la législation", "@sage/xtrem-finance-data/pages__posting_class_definition__legislation____placeholder": "Sélectionner...", "@sage/xtrem-finance-data/pages__posting_class_definition__postingClassType____title": "Type de classe", "@sage/xtrem-finance-data/pages__posting_class_duplicate__not_allowed": "L'opération a échoué à cause d'une contrainte d'intégrité de la base de données.", "@sage/xtrem-finance-data/pages__posting-class__load_lines": "Charger lignes", "@sage/xtrem-finance-data/pages__posting-class__no_new_lines": "Il n'y a aucune ligne à ajouter.", "@sage/xtrem-finance-data/pages__posting-class_line_deletion": "", "@sage/xtrem-finance-data/pages__posting-class_line_deletion_title": "Confirmer la <PERSON> de ligne", "@sage/xtrem-finance-data/pages__posting-class-template__info": "Seules les lignes qui s'appliquent aux mêmes types d'articles sont sélectionnées.", "@sage/xtrem-finance-data/pages__posting-class-template__info_title": "Classe de comptabilisation", "@sage/xtrem-finance-data/pages_company_confirmation": "Appliquer", "@sage/xtrem-finance-data/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/permission__delete__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/permission__manage__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-finance-data/permission__update__name": "Mettre à jour", "@sage/xtrem-finance-data/service_options__payment_tracking_option__intacct_is_active": "Le suivi des règlements est impossible si l'intégration Intacct est inactive.", "@sage/xtrem-finance-data/service_options__payment_tracking_option__name": "Option de suivi des règlements", "@sage/xtrem-finance-data/sys__notification_history__search": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/update-account-tax-management-context": "Vous êtes sur le point de définir une gestion de compte de type 'Taxe'.", "@sage/xtrem-finance-data/update-account-tax-management-title": "Confirmer la mise à jour de la gestion des taxes de comptes"}