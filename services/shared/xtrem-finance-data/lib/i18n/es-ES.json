{"@sage/xtrem-finance-data/activity__account__name": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/activity__attribute__name": "Atributo", "@sage/xtrem-finance-data/activity__attribute_type__name": "Tipo de atributo", "@sage/xtrem-finance-data/activity__bank_account__name": "Cuenta bancaria", "@sage/xtrem-finance-data/activity__company_default_attribute__name": "Atributo por defecto de sociedad", "@sage/xtrem-finance-data/activity__company_default_dimension__name": "Sección por defecto de sociedad", "@sage/xtrem-finance-data/activity__datev_configuration__name": "Configuración de DATEV", "@sage/xtrem-finance-data/activity__dimension__name": "Sección", "@sage/xtrem-finance-data/activity__dimension_definition_level_and_default__name": "Nivel de definición y valor predeterminado de sección", "@sage/xtrem-finance-data/activity__dimension_type__name": "<PERSON><PERSON>", "@sage/xtrem-finance-data/activity__journal__name": "Diario", "@sage/xtrem-finance-data/activity__journal_entry_type__name": "Tipo de asiento", "@sage/xtrem-finance-data/activity__posting_class__name": "Clase contable", "@sage/xtrem-finance-data/activity__posting_class_definition__name": "Definición de clase contable", "@sage/xtrem-finance-data/attribute-type-deactivation-effective-dialog-title": "Selecciona \"Guardar\" para aplicar los cambios.", "@sage/xtrem-finance-data/base-document-item-line/set-dimension-missing-line": "Falta una línea. No puedes actualizar la sección.", "@sage/xtrem-finance-data/base-document-item-line/update-dimension-posted": "No puedes actualizar secciones en un documento cerrado.", "@sage/xtrem-finance-data/cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/cannot_set_tax_with_account_not_subjected_to_taxes": "No puedes definir impuestos en una cuenta no sujeta a impuestos.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_account": "No se ha encontrado la cuenta {{account}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_address": "No se ha encontrado la dirección {{address}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_base_document_line": "No se ha encontrado la línea de documento de origen {{baseDocumentLine}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_base_tax": "No se ha encontrado la línea del impuesto {{baseTax}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_customer": "No se ha encontrado el cliente {{customer}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_financial_site": "No se ha encontrado la planta {{financialSite}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_item": "No se ha encontrado el artículo {{item}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_payment_term": "No se han encontrado las condiciones de pago {{paymentTerm}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_posting_class_ap_ar_invoice": "No se ha encontrado la clase contable para el documento {{documentType}}, número {{documentNumber}} y artículo {{itemId}}. El tipo de clase contable es {{postingClassType}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_posting_class_journal_entry": "No se ha encontrado la clase contable para el documento {{documentType}}, número {{documentNumber}}. El tipo de clase contable es {{postingClassType}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_resource": "No se ha encontrado el recurso {{resource}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_stock_journal": "No se ha encontrado el diario de stock {{stockJournal}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_supplier": "No se ha encontrado el proveedor {{supplier}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_tax": "No se ha encontrado el impuesto {{tax}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_transaction_currency": "No se ha encontrado la divisa {{transactionCurrency}}.", "@sage/xtrem-finance-data/classes__localized-messages__item_posting_class_missing_on_item": "Introduce una clase contable para el artículo {{item}}.", "@sage/xtrem-finance-data/classes__localized-messages__no_bp_account": "{{documentType}} {{documentNumber}}. No se ha encontrado la cuenta para la entidad empresarial {{businessPartner}}.", "@sage/xtrem-finance-data/classes__localized-messages__no_finance_document_lines_generated_for_item": "La cuenta no se ha podido determinar para el artículo {{item}}, tipo de asiento {{journalEntryType}} y tipo de movimiento {{movementType}}.", "@sage/xtrem-finance-data/classes__localized-messages__resource_posting_class_missing_on_resource": "Introduce una clase contable para el recurso {{resource}}.", "@sage/xtrem-finance-data/classes__localized-messages__tax_posting_class_missing_on_tax": "Introduce una clase contable para el impuesto {{tax}}.", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_attribute_type": "Selecciona un atributo de tipo \"{{attribute}}\" en la línea {{line}} .", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type": "Selecciona el eje \"{{dimension}}\" de nivel \"{{level}}\".", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type_item_tax_amount_false": "Selecciona el eje \"{{dimension}}\" de nivel \"{{level}}\" para el artículo \"{{item}}\" en el documento {{sourceDocumentNumber}}.", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type_item_tax_amount_True": "Selecciona el eje {{dimension}} de nivel {{level}} para el artículo {{item}} en el documento {{sourceDocumentNumber}} (Detalles de impuestos).", "@sage/xtrem-finance-data/confirm-dialog-content": "¿Quieres aplicar esta configuración de contabilización a las transacciones futuras?", "@sage/xtrem-finance-data/confirm-dialog-title": "Confirmar configuración", "@sage/xtrem-finance-data/confirm-update": "Confirmar actualiza<PERSON>", "@sage/xtrem-finance-data/data_types__accounts_payable_receivable_invoice_document_line_type_enum__name": "Accounts payable receivable invoice document line type enum", "@sage/xtrem-finance-data/data_types__accounts_payable_receivable_invoice_line_type_enum__name": "Accounts payable receivable invoice line type enum", "@sage/xtrem-finance-data/data_types__accounts_payable_receivable_invoice_origin_enum__name": "Accounts payable receivable invoice origin enum", "@sage/xtrem-finance-data/data_types__amount_type_enum__name": "Amount type enum", "@sage/xtrem-finance-data/data_types__analytical_measure_type_enum__name": "Tipo de medida analítica", "@sage/xtrem-finance-data/data_types__attribute__name": "Atributo", "@sage/xtrem-finance-data/data_types__attribute_dimension_type_level_enum__name": "Attribute dimension type level enum", "@sage/xtrem-finance-data/data_types__attribute_type__name": "Tipo de atributo", "@sage/xtrem-finance-data/data_types__bank_account__name": "Cuenta bancaria", "@sage/xtrem-finance-data/data_types__bank_account_type_enum__name": "Bank account type enum", "@sage/xtrem-finance-data/data_types__close_reason__name": "Motivo de cierre", "@sage/xtrem-finance-data/data_types__common_reference_enum__name": "Referencia común", "@sage/xtrem-finance-data/data_types__dimension__name": "Sección", "@sage/xtrem-finance-data/data_types__dimension_definition_level_enum__name": "Dimension definition level enum", "@sage/xtrem-finance-data/data_types__dimension_type__name": "<PERSON><PERSON>", "@sage/xtrem-finance-data/data_types__doc_property_enum__name": "Propiedad de documento", "@sage/xtrem-finance-data/data_types__finance_document_type_enum__name": "Tipo de documento contable", "@sage/xtrem-finance-data/data_types__finance_integration_app_enum__name": "Aplicación de integración contable", "@sage/xtrem-finance-data/data_types__finance_integration_status_enum__name": "Estado de integración contable", "@sage/xtrem-finance-data/data_types__finance_item_type_enum__name": "Tipo de artículo", "@sage/xtrem-finance-data/data_types__header_description_enum__name": "Descripción", "@sage/xtrem-finance-data/data_types__header_posting_date_enum__name": "<PERSON><PERSON> con<PERSON>", "@sage/xtrem-finance-data/data_types__item_stock_management_criteria_enum__name": "Criterios de gestión de stock de artículo", "@sage/xtrem-finance-data/data_types__journal_origin_enum__name": "Journal origin enum", "@sage/xtrem-finance-data/data_types__journal_status_enum__name": "Journal status enum", "@sage/xtrem-finance-data/data_types__master_data_default_enum__name": "Master data default enum", "@sage/xtrem-finance-data/data_types__movement_type_enum__name": "Tipo de movimiento", "@sage/xtrem-finance-data/data_types__node_link_enum__name": "Node link enum", "@sage/xtrem-finance-data/data_types__open_item_status_enum__name": "Open item status enum", "@sage/xtrem-finance-data/data_types__posting_class__name": "Clase contable", "@sage/xtrem-finance-data/data_types__posting_class_type_enum__name": "Tipo de clase contable", "@sage/xtrem-finance-data/data_types__posting_status_enum__name": "Shipping status enum", "@sage/xtrem-finance-data/data_types__sign_enum__name": "Sign enum", "@sage/xtrem-finance-data/data_types__source_document_type_enum__name": "Tipo de documento de origen", "@sage/xtrem-finance-data/data_types__stored_attributes_data_type__name": "Stored attributes data type", "@sage/xtrem-finance-data/data_types__stored_dimensions_data_type__name": "Stored dimensions data type", "@sage/xtrem-finance-data/data_types__target_document_type_enum__name": "Tipo de documento de destino", "@sage/xtrem-finance-data/data_types__tax_engine_enum__name": "Tax engine enum", "@sage/xtrem-finance-data/data_types__tax_management_enum__name": "Lot management enum", "@sage/xtrem-finance-data/data_types__validation_severity_data_type__name": "Validation severity data type", "@sage/xtrem-finance-data/deactivation-dialog-content": "¿Quieres desactivar este tipo de atributo en todos los documentos en que se esté utilizando?", "@sage/xtrem-finance-data/deactivation-dialog-title": "Confirmar desactivación", "@sage/xtrem-finance-data/dimension-deactivation-dialog-content": "¿Quieres desactivar este eje en todos los documentos en que se esté utilizando?", "@sage/xtrem-finance-data/dimension-deactivation-dialog-title": "Confirmar desactivación", "@sage/xtrem-finance-data/dimension-type-deactivation-effective-dialog-title": "Selecciona \"Guardar\" para aplicar los cambios.", "@sage/xtrem-finance-data/dimension-type-name-change-dialog-content": "¿Quieres cambiar el nombre de este eje en todos los documentos en que se esté utilizando?", "@sage/xtrem-finance-data/dimension-type-name-change-dialog-title": "Confirmar cambio de nombre", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_document_line_type__documentLine": "Línea de documento", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_document_line_type__taxLine": "Línea de impuesto", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_line_type__fixedAssets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_line_type__goods": "Bienes", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_line_type__services": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_origin__creditMemo": "Factura rectificativa", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_origin__direct": "Directa", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_origin__invoice": "Factura", "@sage/xtrem-finance-data/enums__amount_type__adjustmentAmount": "Importe de regularización", "@sage/xtrem-finance-data/enums__amount_type__adjustmentNonabsorbedAmount": "Importe de regularización sin absorber", "@sage/xtrem-finance-data/enums__amount_type__amount": "Importe", "@sage/xtrem-finance-data/enums__amount_type__amountExcludingTax": "Importe sin impuestos", "@sage/xtrem-finance-data/enums__amount_type__amountIncludingTax": "Importe con impuestos", "@sage/xtrem-finance-data/enums__amount_type__deductibleTaxAmount": "Importe de impuesto deducible", "@sage/xtrem-finance-data/enums__amount_type__inTransitAmount": "Importe en tránsito", "@sage/xtrem-finance-data/enums__amount_type__inTransitVarianceAmount": "Importe de desviación en tránsito", "@sage/xtrem-finance-data/enums__amount_type__landedCostAdjustmentAmount": "Importe de regularización de gastos de entrega", "@sage/xtrem-finance-data/enums__amount_type__landedCostAdjustmentNonabsorbedAmount": "Importe de regularización sin absorber de gastos de entrega", "@sage/xtrem-finance-data/enums__amount_type__landedCostStockInTransitAdjustmentAmount": "Importe de regularización de gastos de entrega de stock en tránsito", "@sage/xtrem-finance-data/enums__amount_type__landedCostStockInTransitAdjustmentNonabsorbedAmount": "Importe de regularización sin absorber de gastos de entrega de stock en tránsito", "@sage/xtrem-finance-data/enums__amount_type__landedCostStockInTransitAmount": "Importe de gastos de entrega en tránsito", "@sage/xtrem-finance-data/enums__amount_type__nonDeductibleTaxAmount": "Importe de impuesto no deducible", "@sage/xtrem-finance-data/enums__amount_type__reverseChargeDeductibleTaxAmount": "Importe de impuesto deducible con inversión de sujeto pasivo", "@sage/xtrem-finance-data/enums__amount_type__reverseChargeNonDeductibleTaxAmount": "Importe de impuesto no deducible con inversión de sujeto pasivo", "@sage/xtrem-finance-data/enums__amount_type__taxAmount": "Importe de impuesto", "@sage/xtrem-finance-data/enums__amount_type__varianceAmount": "Importe de desviación", "@sage/xtrem-finance-data/enums__analytical_measure_type__attribute": "Atributo", "@sage/xtrem-finance-data/enums__analytical_measure_type__dimension": "Sección", "@sage/xtrem-finance-data/enums__attribute_dimension_type_level__account": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__attribute_dimension_type_level__company": "Sociedad", "@sage/xtrem-finance-data/enums__bank_account_type__current": "Actual", "@sage/xtrem-finance-data/enums__common_reference__documentNumber": "Número de documento", "@sage/xtrem-finance-data/enums__common_reference__sourceDocumentNumber": "Número de documento de origen", "@sage/xtrem-finance-data/enums__dimension_definition_level__intersiteTransferOrder": "Orden de transferencia de stock", "@sage/xtrem-finance-data/enums__dimension_definition_level__manufacturingDirect": "Fabricación directa", "@sage/xtrem-finance-data/enums__dimension_definition_level__manufacturingOrderToOrder": "Fabricación por pedido", "@sage/xtrem-finance-data/enums__dimension_definition_level__purchasingDirect": "Compra directa", "@sage/xtrem-finance-data/enums__dimension_definition_level__purchasingOrderToOrder": "Compra por pedido", "@sage/xtrem-finance-data/enums__dimension_definition_level__salesDirect": "Venta directa", "@sage/xtrem-finance-data/enums__dimension_definition_level__stockDirect": "Stock directo", "@sage/xtrem-finance-data/enums__doc_property__dimension01": "Sección 01", "@sage/xtrem-finance-data/enums__doc_property__dimension02": "Sección 02", "@sage/xtrem-finance-data/enums__doc_property__dimension03": "Sección 03", "@sage/xtrem-finance-data/enums__doc_property__dimension04": "Sección 04", "@sage/xtrem-finance-data/enums__doc_property__dimension05": "Sección 05", "@sage/xtrem-finance-data/enums__doc_property__dimension06": "Sección 06", "@sage/xtrem-finance-data/enums__doc_property__dimension07": "Sección 07", "@sage/xtrem-finance-data/enums__doc_property__dimension08": "Sección 08", "@sage/xtrem-finance-data/enums__doc_property__dimension09": "Sección 09", "@sage/xtrem-finance-data/enums__doc_property__dimension10": "Sección 10", "@sage/xtrem-finance-data/enums__doc_property__dimension11": "Sección 11", "@sage/xtrem-finance-data/enums__doc_property__dimension12": "Sección 12", "@sage/xtrem-finance-data/enums__doc_property__dimension13": "Sección 13", "@sage/xtrem-finance-data/enums__doc_property__dimension14": "Sección 14", "@sage/xtrem-finance-data/enums__doc_property__dimension15": "Sección 15", "@sage/xtrem-finance-data/enums__doc_property__dimension16": "Sección 16", "@sage/xtrem-finance-data/enums__doc_property__dimension17": "Sección 17", "@sage/xtrem-finance-data/enums__doc_property__dimension18": "Sección 18", "@sage/xtrem-finance-data/enums__doc_property__dimension19": "Sección 19", "@sage/xtrem-finance-data/enums__doc_property__dimension20": "Sección 20", "@sage/xtrem-finance-data/enums__doc_property__dimensionType01": "Eje 01", "@sage/xtrem-finance-data/enums__doc_property__dimensionType02": "Eje 02", "@sage/xtrem-finance-data/enums__doc_property__dimensionType03": "Eje 03", "@sage/xtrem-finance-data/enums__doc_property__dimensionType04": "Eje 04", "@sage/xtrem-finance-data/enums__doc_property__dimensionType05": "Eje 05", "@sage/xtrem-finance-data/enums__doc_property__dimensionType06": "Eje 06", "@sage/xtrem-finance-data/enums__doc_property__dimensionType07": "Eje 07", "@sage/xtrem-finance-data/enums__doc_property__dimensionType08": "Eje 08", "@sage/xtrem-finance-data/enums__doc_property__dimensionType09": "Eje 09", "@sage/xtrem-finance-data/enums__doc_property__dimensionType10": "Eje 10", "@sage/xtrem-finance-data/enums__doc_property__dimensionType11": "Eje 11", "@sage/xtrem-finance-data/enums__doc_property__dimensionType12": "Eje 12", "@sage/xtrem-finance-data/enums__doc_property__dimensionType13": "Eje 13", "@sage/xtrem-finance-data/enums__doc_property__dimensionType14": "Eje 14", "@sage/xtrem-finance-data/enums__doc_property__dimensionType15": "Eje 15", "@sage/xtrem-finance-data/enums__doc_property__dimensionType16": "Eje 16", "@sage/xtrem-finance-data/enums__doc_property__dimensionType17": "Eje 17", "@sage/xtrem-finance-data/enums__doc_property__dimensionType18": "Eje 18", "@sage/xtrem-finance-data/enums__doc_property__dimensionType19": "Eje 19", "@sage/xtrem-finance-data/enums__doc_property__dimensionType20": "Eje 20", "@sage/xtrem-finance-data/enums__finance_document_type__apInvoice": "Factura contable de proveedor", "@sage/xtrem-finance-data/enums__finance_document_type__arInvoice": "Factura contable de cliente", "@sage/xtrem-finance-data/enums__finance_document_type__bankReconciliationDeposit": "Conciliación bancaria de depósito", "@sage/xtrem-finance-data/enums__finance_document_type__bankReconciliationWithdrawal": "Conciliación bancaria de retirada", "@sage/xtrem-finance-data/enums__finance_document_type__miscellaneousStockIssue": "Salida de stock varia", "@sage/xtrem-finance-data/enums__finance_document_type__miscellaneousStockReceipt": "Entrada de stock varia", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseCreditMemo": "Factura rectificativa de compra", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseInvoice": "Factura de compra", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseReceipt": "Recepción de compra", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseReturn": "Devolución de compra", "@sage/xtrem-finance-data/enums__finance_document_type__salesCreditMemo": "Factura rectificativa de venta", "@sage/xtrem-finance-data/enums__finance_document_type__salesInvoice": "Factura de venta", "@sage/xtrem-finance-data/enums__finance_document_type__salesReturnReceipt": "Recepción de devolución de venta", "@sage/xtrem-finance-data/enums__finance_document_type__salesShipment": "Expedición de venta", "@sage/xtrem-finance-data/enums__finance_document_type__stockAdjustment": "Regularización de stock", "@sage/xtrem-finance-data/enums__finance_document_type__stockCount": "Inventario", "@sage/xtrem-finance-data/enums__finance_document_type__stockTransferReceipt": "Recepción de transferencia de stock", "@sage/xtrem-finance-data/enums__finance_document_type__stockTransferShipment": "Expedición de transferencia de stock", "@sage/xtrem-finance-data/enums__finance_document_type__stockValueChange": "Cambio de valor de stock", "@sage/xtrem-finance-data/enums__finance_document_type__workInProgress": "Trabajo en curso", "@sage/xtrem-finance-data/enums__finance_integration_app__frp1000": "Sage FRP 1000", "@sage/xtrem-finance-data/enums__finance_integration_app__intacct": "Sage Intacct", "@sage/xtrem-finance-data/enums__finance_integration_status__error": "Error", "@sage/xtrem-finance-data/enums__finance_integration_status__failed": "Fallida", "@sage/xtrem-finance-data/enums__finance_integration_status__notRecorded": "Sin registrar", "@sage/xtrem-finance-data/enums__finance_integration_status__pending": "Pendiente", "@sage/xtrem-finance-data/enums__finance_integration_status__posted": "Contabilizada", "@sage/xtrem-finance-data/enums__finance_integration_status__recorded": "Registrada", "@sage/xtrem-finance-data/enums__finance_integration_status__recording": "En proceso de registro", "@sage/xtrem-finance-data/enums__finance_integration_status__submitted": "Enviada", "@sage/xtrem-finance-data/enums__finance_integration_status__toBeRecorded": "Por registrar", "@sage/xtrem-finance-data/enums__finance_item_type__landedCostItem": "Artículo de gastos de entrega", "@sage/xtrem-finance-data/enums__finance_item_type__nonStockItem": "Artículo sin stock", "@sage/xtrem-finance-data/enums__finance_item_type__serviceItem": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__finance_item_type__stockItem": "Artículo de stock", "@sage/xtrem-finance-data/enums__header_description__documentNumber": "Número de documento", "@sage/xtrem-finance-data/enums__header_description__documentType": "Tipo de documento", "@sage/xtrem-finance-data/enums__header_description__transactionDescription": "Descripción de transacción", "@sage/xtrem-finance-data/enums__header_posting_date__documentDate": "Fecha de documento", "@sage/xtrem-finance-data/enums__header_posting_date__endOfMonth": "Fin de mes", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__both": "Ambos", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__managedInStock": "Con gestión de stock", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__notApplicable": "No aplicable", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__notManagedInStock": "Sin gestión de stock", "@sage/xtrem-finance-data/enums__journal_origin__apInvoice": "Factura contable de proveedor", "@sage/xtrem-finance-data/enums__journal_origin__arInvoice": "Factura contable de cliente", "@sage/xtrem-finance-data/enums__journal_origin__directEntry": "Asiento directo", "@sage/xtrem-finance-data/enums__journal_origin__manufacturing": "Producción", "@sage/xtrem-finance-data/enums__journal_origin__purchase": "Compras", "@sage/xtrem-finance-data/enums__journal_origin__sales": "Ventas", "@sage/xtrem-finance-data/enums__journal_origin__stock": "Stock", "@sage/xtrem-finance-data/enums__journal_status__draft": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__journal_status__error": "Error", "@sage/xtrem-finance-data/enums__journal_status__inProgress": "En curso", "@sage/xtrem-finance-data/enums__journal_status__posted": "Contabilizado", "@sage/xtrem-finance-data/enums__master_data_default__customer": "Cliente", "@sage/xtrem-finance-data/enums__master_data_default__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__master_data_default__receivingSite": "Planta de recepción", "@sage/xtrem-finance-data/enums__master_data_default__shippingSite": "Planta de expedición", "@sage/xtrem-finance-data/enums__master_data_default__site": "Planta", "@sage/xtrem-finance-data/enums__master_data_default__sourceDocument": "Documento de origen", "@sage/xtrem-finance-data/enums__master_data_default__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__movement_type__document": "Documento", "@sage/xtrem-finance-data/enums__movement_type__laborRunTimeTracking": "Seguimiento de tiempos operacionales de mano de obra", "@sage/xtrem-finance-data/enums__movement_type__laborSetupTimeTracking": "Seguimiento de tiempos de ajuste de mano de obra", "@sage/xtrem-finance-data/enums__movement_type__machineRunTimeTracking": "Seguimiento de tiempos operacionales de máquina", "@sage/xtrem-finance-data/enums__movement_type__machineSetupTimeTracking": "Seguimiento de tiempos de ajuste de máquina", "@sage/xtrem-finance-data/enums__movement_type__materialTracking": "Seguimiento de material", "@sage/xtrem-finance-data/enums__movement_type__productionTracking": "Seguimiento de producción", "@sage/xtrem-finance-data/enums__movement_type__stockJournal": "Diario de stock", "@sage/xtrem-finance-data/enums__movement_type__toolRunTimeTracking": "Seguimiento de tiempos operacionales de herramienta", "@sage/xtrem-finance-data/enums__movement_type__toolSetupTimeTracking": "Seguimiento de tiempos de ajuste de herramienta", "@sage/xtrem-finance-data/enums__movement_type__workOrderActualCostAdjustment": "Regularización de coste de orden de fabricación", "@sage/xtrem-finance-data/enums__movement_type__workOrderActualCostAdjustmentNonAbsorbed": "Regularización de coste de orden de fabricación sin absorber", "@sage/xtrem-finance-data/enums__movement_type__workOrderNegativeActualCostAdjustment": "Deducción de coste de orden de fabricación", "@sage/xtrem-finance-data/enums__movement_type__workOrderNegativeActualCostAdjustmentNonAbsorbed": "Deducción de coste de orden de fabricación sin absorber", "@sage/xtrem-finance-data/enums__movement_type__workOrderNegativeVariance": "Desviación negativa de orden de fabricación", "@sage/xtrem-finance-data/enums__movement_type__workOrderVariance": "Desviación de orden de fabricación", "@sage/xtrem-finance-data/enums__node_link__attribute": "Atributo", "@sage/xtrem-finance-data/enums__node_link__customer": "Cliente", "@sage/xtrem-finance-data/enums__node_link__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__node_link__site": "Planta", "@sage/xtrem-finance-data/enums__node_link__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__open_item_status__notPaid": "<PERSON> pagar", "@sage/xtrem-finance-data/enums__open_item_status__paid": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__open_item_status__partiallyPaid": "<PERSON><PERSON><PERSON><PERSON> pagado", "@sage/xtrem-finance-data/enums__posting_class_type__company": "Sociedad", "@sage/xtrem-finance-data/enums__posting_class_type__customer": "Cliente", "@sage/xtrem-finance-data/enums__posting_class_type__header": "Cabecera", "@sage/xtrem-finance-data/enums__posting_class_type__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__posting_class_type__line": "Lín<PERSON>", "@sage/xtrem-finance-data/enums__posting_class_type__resource": "Recurso", "@sage/xtrem-finance-data/enums__posting_class_type__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__posting_class_type__tax": "Impuesto", "@sage/xtrem-finance-data/enums__posting_status__generated": "Generado", "@sage/xtrem-finance-data/enums__posting_status__generationError": "Error de generación", "@sage/xtrem-finance-data/enums__posting_status__generationInProgress": "Generación en curso", "@sage/xtrem-finance-data/enums__posting_status__notPosted": "Sin contabilizar", "@sage/xtrem-finance-data/enums__posting_status__posted": "Contabilizada", "@sage/xtrem-finance-data/enums__posting_status__postingError": "Error de contabilización", "@sage/xtrem-finance-data/enums__posting_status__postingInProgress": "Contabilización en curso", "@sage/xtrem-finance-data/enums__posting_status__toBeGenerated": "Por generar", "@sage/xtrem-finance-data/enums__sign__C": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__sign__D": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__source_document_type__materialTracking": "Seguimiento de material", "@sage/xtrem-finance-data/enums__source_document_type__operationTracking": "Seguimiento de operaciones", "@sage/xtrem-finance-data/enums__source_document_type__productionTracking": "Seguimiento de producción", "@sage/xtrem-finance-data/enums__source_document_type__purchaseCreditMemo": "Factura rectificativa de compra", "@sage/xtrem-finance-data/enums__source_document_type__purchaseInvoice": "Factura de compra", "@sage/xtrem-finance-data/enums__source_document_type__purchaseOrder": "Pedido de compra", "@sage/xtrem-finance-data/enums__source_document_type__purchaseReceipt": "Recepción de compra", "@sage/xtrem-finance-data/enums__source_document_type__purchaseReturn": "Devolución de compra", "@sage/xtrem-finance-data/enums__source_document_type__salesCreditMemo": "Factura rectificativa de venta", "@sage/xtrem-finance-data/enums__source_document_type__salesInvoice": "Factura de venta", "@sage/xtrem-finance-data/enums__source_document_type__salesReturnRequest": "Solicitud de devolución de venta", "@sage/xtrem-finance-data/enums__source_document_type__salesShipment": "Expedición de venta", "@sage/xtrem-finance-data/enums__source_document_type__stockTransferOrder": "Orden de transferencia de stock", "@sage/xtrem-finance-data/enums__source_document_type__stockTransferReceipt": "Recepción de transferencia de stock", "@sage/xtrem-finance-data/enums__source_document_type__stockTransferShipment": "Expedición de transferencia de stock", "@sage/xtrem-finance-data/enums__source_document_type__workOrderClose": "Cierre de orden de fabricación", "@sage/xtrem-finance-data/enums__target_document_type__accountsPayableInvoice": "Factura contable de proveedor", "@sage/xtrem-finance-data/enums__target_document_type__accountsReceivableAdvance": "Anticipo de cliente", "@sage/xtrem-finance-data/enums__target_document_type__accountsReceivableInvoice": "Factura contable de cliente", "@sage/xtrem-finance-data/enums__target_document_type__accountsReceivablePayment": "Pago de cliente", "@sage/xtrem-finance-data/enums__target_document_type__journalEntry": "Asiento", "@sage/xtrem-finance-data/enums__tax_engine__avalaraAvaTax": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__tax_engine__genericTaxCalculation": "Cálculo de impuestos genérico", "@sage/xtrem-finance-data/enums__tax_management__excludingTax": "Sin impuestos", "@sage/xtrem-finance-data/enums__tax_management__includingTax": "Con impuestos", "@sage/xtrem-finance-data/enums__tax_management__other": "Otra", "@sage/xtrem-finance-data/enums__tax_management__reverseCharge": "Inversión de sujeto pasivo", "@sage/xtrem-finance-data/enums__tax_management__tax": "Impuestos", "@sage/xtrem-finance-data/error": "Error", "@sage/xtrem-finance-data/events__control__posting_class_account_control__account_is_mandatory": "Si no se han definido detalles en la línea, tienes que introducir una cuenta.", "@sage/xtrem-finance-data/events__control__posting_class_line_detail__secondary_criteria_is_tax": "Si la línea de clase contable secundaria es de tipo \"Impuesto\", tienes que introducir el código de impuesto.", "@sage/xtrem-finance-data/events__posting_class_definition__this_posting_class_definition_has_details": "Elimina los detalles de la línea de clase contable para poder quitar los criterios adicionales.", "@sage/xtrem-finance-data/functions__accounting_engine__journals_created": "Asientos creados: {{journalsCreated}}", "@sage/xtrem-finance-data/functions__default_dimension_lib__wrong_source_for_dimensions": "No puedes asignar la sección {{masterDataDefault}} a este documento de tipo {{dimensionDefinitionLevel}}.", "@sage/xtrem-finance-data/functions__dimensions__attribute_type_restricted_to": "The {{attributeTypeRestrictedToName}} attribute needs to be filled in.", "@sage/xtrem-finance-data/is_active_business_site_attribute_type_or_value_inactive": "La planta {{businessSite}} no está activa o no está definida como planta empresarial o el tipo de atributo de la planta empresarial no está activo.", "@sage/xtrem-finance-data/is_active_customer_attribute_type_or_value_inactive": "El cliente {{customer}} o el tipo de atributo del cliente no está activo.", "@sage/xtrem-finance-data/is_active_dimension_inactive": "La sección {{dimension}} o el eje {{dimensionType}} no está activo.", "@sage/xtrem-finance-data/is_active_employee_attribute_inactive": "El atributo {{employeeAttribute}} o el tipo de atributo del trabajador no está activo.", "@sage/xtrem-finance-data/is_active_financial_site_attribute_type_or_value_inactive": "La planta {{financialSite}} no está activa o no está definida como planta financiera o el tipo de atributo de la planta financiera no está activo.", "@sage/xtrem-finance-data/is_active_invalid_attribute": "Atributo no válido: {{{other}}", "@sage/xtrem-finance-data/is_active_item_attribute_type_or_value_inactive": "El artículo {{item}} o el tipo de atributo del artículo no está activo.", "@sage/xtrem-finance-data/is_active_manufacturing_site_attribute_type_or_value_inactive": "La planta {{manufacturingSite}} no está activa o no está definida como planta de producción o el tipo de atributo de la planta de producción no está activo.", "@sage/xtrem-finance-data/is_active_project_attribute_inactive": "El atributo {{projectAttribute}} o el tipo de atributo \"Proyecto\" no está activo.", "@sage/xtrem-finance-data/is_active_stock_site_attribute_type_or_value_inactive": "La planta {{stockSite}} no está activa o no está definida como planta de stock o el tipo de atributo de la planta de stock no está activo.", "@sage/xtrem-finance-data/is_active_supplier_attribute_type_or_value_inactive": "El proveedor {{supplier}} o el tipo de atributo del proveedor no está activo.", "@sage/xtrem-finance-data/is_active_task_attribute_inactive": "The {{taskAttribute}} attribute or \"Task\" attribute type is inactive.", "@sage/xtrem-finance-data/journal_entry_type_line_legislations_mismatch": "La legislación {{postingClassLegislation}} del tipo de cuenta es diferente de la del tipo de asiento: {{typeLegislation}}.", "@sage/xtrem-finance-data/menu_item__features-financial-integration": "Contabilidad", "@sage/xtrem-finance-data/menu_item__payment-tracking": "Seguimiento de pagos", "@sage/xtrem-finance-data/node-extensions__base_business_relation_extension__property__datevId": "Id. de DATEV", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__financeIntegrationRecords": "Registros de integración contable", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__financeIntegrationStatus": "Estado de integración contable", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__forceUpdateForFinance": "Forzar actualización de Contabilidad", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__wasTaxDataChanged": "Datos de impuestos modificados", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension": "Definir secci<PERSON>", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__failed": "Error al definir la sección", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__parameter__baseDocumentItemLine": "Línea de artículo de documento base", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__parameter__storedAttributes": "Atributos almacenados", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__parameter__storedDimensions": "Secciones almacenadas", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__computedAttributes": "Atributos calculados", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__dimensionDefinitionLevel": "Nivel de definición de sección", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-finance-data/node-extensions__base_document_line_inquiry_extension__property__postingClass": "Clase contable", "@sage/xtrem-finance-data/node-extensions__company_extension__property__attributeTypes": "Tipos de atributo", "@sage/xtrem-finance-data/node-extensions__company_extension__property__bankAccount": "Cuenta bancaria", "@sage/xtrem-finance-data/node-extensions__company_extension__property__datevConsultantNumber": "Número de asesor de DATEV", "@sage/xtrem-finance-data/node-extensions__company_extension__property__datevCustomerNumber": "Número de cliente de DATEV", "@sage/xtrem-finance-data/node-extensions__company_extension__property__defaultAttributes": "Atributos por defecto", "@sage/xtrem-finance-data/node-extensions__company_extension__property__defaultDimensions": "Secciones por defecto", "@sage/xtrem-finance-data/node-extensions__company_extension__property__dimensionTypes": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doApPosting": "Contabilización de documento de proveedor", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doArPosting": "Contabilización de documento de cliente", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doNonAbsorbedPosting": "Contabilización de importe sin absorber", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doStockPosting": "Contabilización de stock", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doWipPosting": "Contabilización de trabajo en curso", "@sage/xtrem-finance-data/node-extensions__company_extension__property__postingClass": "Clase contable", "@sage/xtrem-finance-data/node-extensions__company_extension__property__taxEngine": "Motor de impuestos", "@sage/xtrem-finance-data/node-extensions__company-extension__do_non_absorbed_posting_not_allowed": "Sin una contabilización de stock, no se puede contabilizar el importe sin absorber.", "@sage/xtrem-finance-data/node-extensions__company-extension__do_non_absorbed_posting_not_allowed_on_legislation": "No puedes contabilizar importes sin absorber en esta legislación.", "@sage/xtrem-finance-data/node-extensions__company-extension__do_wip_posting_not_allowed_on_legislation": "No puedes contabilizar trabajos en curso en esta legislación.", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__postingClass": "Clase contable", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-finance-data/node-extensions__detailed_resource_extension__property__postingClass": "Clase contable", "@sage/xtrem-finance-data/node-extensions__item_extension__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-finance-data/node-extensions__item_extension__property__postingClass": "Clase contable", "@sage/xtrem-finance-data/node-extensions__item_extension__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-finance-data/node-extensions__item_extension__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doApPosting": "Contabilización de documento de proveedor", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doArPosting": "Contabilización de documento de cliente", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doLandedCostGoodsInTransitPosting": "Contabilización de gastos de entrega de bienes en tránsito", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doNonAbsorbedPosting": "Contabilización de importe sin absorber", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doNonStockVariancePosting": "", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doStockPosting": "Contabilización de stock", "@sage/xtrem-finance-data/node-extensions__site_extension__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-finance-data/node-extensions__site_extension__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-finance-data/node-extensions__site_extension__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__postingClass": "Clase contable", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-finance-data/node-extensions__tax_extension__property__postingClass": "Clase contable", "@sage/xtrem-finance-data/node-extensions__tax_extension__property__postingKey": "<PERSON><PERSON><PERSON> contable", "@sage/xtrem-finance-data/nodes__account__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__account__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__account__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__account__datev_id_invalid": "El id. de DATEV debe ser un número entre {{fromValue}} y {{toValue}}.", "@sage/xtrem-finance-data/nodes__account__datev_id_not_unique": "El id. de DATEV debe ser único.", "@sage/xtrem-finance-data/nodes__account__datev_tax_invalid": "Asigna un código de impuesto vinculado a uno de los siguientes países: {{countryIds}}.", "@sage/xtrem-finance-data/nodes__account__node_name": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__account__property__attributeTypes": "Tipos de atributo", "@sage/xtrem-finance-data/nodes__account__property__chartOfAccount": "Plan de cuentas", "@sage/xtrem-finance-data/nodes__account__property__composedDescription": "Descripción compuesta", "@sage/xtrem-finance-data/nodes__account__property__datevId": "Id. de DATEV", "@sage/xtrem-finance-data/nodes__account__property__dimensionTypes": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__account__property__id": "Id.", "@sage/xtrem-finance-data/nodes__account__property__isActive": "Activa", "@sage/xtrem-finance-data/nodes__account__property__isAutomaticAccount": "Cuenta automática", "@sage/xtrem-finance-data/nodes__account__property__isControl": "Control", "@sage/xtrem-finance-data/nodes__account__property__isDirectEntryForbidden": "Asiento directo no permitido", "@sage/xtrem-finance-data/nodes__account__property__name": "Nombre", "@sage/xtrem-finance-data/nodes__account__property__tax": "Impuesto", "@sage/xtrem-finance-data/nodes__account__property__taxManagement": "Gestión de impuestos", "@sage/xtrem-finance-data/nodes__account__tax_management_control": "La gestión de impuestos de los asientos sin información fiscal debe ser \"Otra\".", "@sage/xtrem-finance-data/nodes__account_attribute_type__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__account_attribute_type__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__account_attribute_type__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__account_attribute_type__attribute-type-not-active": "El tipo de atributo no está activo.", "@sage/xtrem-finance-data/nodes__account_attribute_type__node_name": "Tipo de atributo de cuenta", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__account": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__analyticalMeasureType": "Tipo de medida analítica", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__attributeType": "Tipo de atributo", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__isRequired": "Obligatorio", "@sage/xtrem-finance-data/nodes__account_dimension_type__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__account_dimension_type__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__account_dimension_type__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__account_dimension_type__dimension-type-not-active": "El eje no está activo.", "@sage/xtrem-finance-data/nodes__account_dimension_type__node_name": "<PERSON><PERSON> de cuenta", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__account": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__analyticalMeasureType": "Tipo de medida analítica", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__dimensionType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__isRequired": "Obligatorio", "@sage/xtrem-finance-data/nodes__accounting_staging__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__accounting_staging__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__accounting_staging__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__accounting_staging__node_name": "Contabilidad provisional", "@sage/xtrem-finance-data/nodes__accounting_staging__property__account": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging__property__amounts": "Importes", "@sage/xtrem-finance-data/nodes__accounting_staging__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging__property__baseDocumentLine": "Línea de documento base", "@sage/xtrem-finance-data/nodes__accounting_staging__property__batchId": "<PERSON><PERSON><PERSON> de tanda", "@sage/xtrem-finance-data/nodes__accounting_staging__property__batchSize": "<PERSON><PERSON><PERSON> tanda", "@sage/xtrem-finance-data/nodes__accounting_staging__property__companyFxRate": "Tipo de cambio de sociedad", "@sage/xtrem-finance-data/nodes__accounting_staging__property__companyFxRateDivisor": "Divisor de tipo de cambio de sociedad", "@sage/xtrem-finance-data/nodes__accounting_staging__property__customer": "Cliente", "@sage/xtrem-finance-data/nodes__accounting_staging__property__customerPostingClass": "Clase contable de cliente", "@sage/xtrem-finance-data/nodes__accounting_staging__property__description": "Descripción", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentDate": "Fecha de documento", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentNumber": "Número de documento", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentSysId": "Id. de sistema de documento", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentType": "Tipo de documento", "@sage/xtrem-finance-data/nodes__accounting_staging__property__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance-data/nodes__accounting_staging__property__financialSite": "Planta financiera", "@sage/xtrem-finance-data/nodes__accounting_staging__property__fxRateDate": "Fecha de tipo de cambio", "@sage/xtrem-finance-data/nodes__accounting_staging__property__isPrinted": "Impreso", "@sage/xtrem-finance-data/nodes__accounting_staging__property__isProcessed": "Procesado", "@sage/xtrem-finance-data/nodes__accounting_staging__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging__property__itemPostingClass": "Clase contable de artículo", "@sage/xtrem-finance-data/nodes__accounting_staging__property__movementType": "Tipo de movimiento", "@sage/xtrem-finance-data/nodes__accounting_staging__property__originNotificationId": "Id. de notificación de origen", "@sage/xtrem-finance-data/nodes__accounting_staging__property__paymentTerm": "Condiciones de pago", "@sage/xtrem-finance-data/nodes__accounting_staging__property__payToSupplier": "<PERSON><PERSON><PERSON><PERSON> pagado", "@sage/xtrem-finance-data/nodes__accounting_staging__property__payToSupplierLinkedAddress": "Dirección de proveedor pagado", "@sage/xtrem-finance-data/nodes__accounting_staging__property__providerSite": "Planta de origen", "@sage/xtrem-finance-data/nodes__accounting_staging__property__recipientSite": "Planta de destino", "@sage/xtrem-finance-data/nodes__accounting_staging__property__replyTopic": "Tema de respuesta", "@sage/xtrem-finance-data/nodes__accounting_staging__property__resource": "Recurso", "@sage/xtrem-finance-data/nodes__accounting_staging__property__resourcePostingClass": "Clase contable de recurso", "@sage/xtrem-finance-data/nodes__accounting_staging__property__returnLinkedAddress": "Dirección de devolución", "@sage/xtrem-finance-data/nodes__accounting_staging__property__sourceBaseDocumentLine": "Línea de documento base de origen", "@sage/xtrem-finance-data/nodes__accounting_staging__property__sourceDocumentNumber": "Número de documento de origen", "@sage/xtrem-finance-data/nodes__accounting_staging__property__sourceDocumentType": "Tipo de documento de origen", "@sage/xtrem-finance-data/nodes__accounting_staging__property__stockJournal": "Diario de stock", "@sage/xtrem-finance-data/nodes__accounting_staging__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-finance-data/nodes__accounting_staging__property__storedComputedAttributes": "Atributos calculados almacenados", "@sage/xtrem-finance-data/nodes__accounting_staging__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplierDocumentDate": "<PERSON><PERSON> de documento de proveedor", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplierDocumentNumber": "Número de documento de proveedor", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplierPostingClass": "Clase contable de proveedor", "@sage/xtrem-finance-data/nodes__accounting_staging__property__targetDocumentType": "Tipo de documento de destino", "@sage/xtrem-finance-data/nodes__accounting_staging__property__taxCalculationStatus": "Estado de cálculo de impuestos", "@sage/xtrem-finance-data/nodes__accounting_staging__property__taxDate": "Fecha de impuesto", "@sage/xtrem-finance-data/nodes__accounting_staging__property__taxes": "Impuestos", "@sage/xtrem-finance-data/nodes__accounting_staging__property__toBeReprocessed": "Por reprocesar", "@sage/xtrem-finance-data/nodes__accounting_staging__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__node_name": "Importe contable provisional", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__accountingStaging": "Contabilidad provisional", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__amount": "Importe", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__amountType": "Tipo de importe", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__baseTax": "Impuesto base", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__documentLineType": "Tipo de línea de documento", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__tax": "Impuesto", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__taxDate": "Fecha de impuesto", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__taxPostingClass": "Clase contable de impuesto", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__taxRate": "Tipo impositivo", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__node_name": "Impuesto de documento contable provisional", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__baseTax": "Impuesto base", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__batchId": "<PERSON><PERSON><PERSON> de tanda", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__documentNumber": "Número de documento", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__documentType": "Tipo de documento", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__targetDocumentType": "Tipo de documento de destino", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__node_name": "Impuesto de línea contable provisional", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__property__accountingStaging": "Contabilidad provisional", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__property__baseTax": "Impuesto base", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__node_name": "Línea de factura de proveedor provisional", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__property__accountingStaging": "Contabilidad provisional", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__property__accountsPayableInvoiceLine": "Línea de factura contable de proveedor", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__node_name": "Línea de factura de cliente provisional", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__property__accountingStaging": "Contabilidad provisional", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__property__accountsReceivableInvoiceLine": "Línea de factura contable de cliente", "@sage/xtrem-finance-data/nodes__analytical_data__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__analytical_data__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__analytical_data__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__analytical_data__node_name": "Datos analític<PERSON>", "@sage/xtrem-finance-data/nodes__analytical_data__property__businessSite": "Planta empresarial", "@sage/xtrem-finance-data/nodes__analytical_data__property__businessSiteType": "Tipo de planta empresarial", "@sage/xtrem-finance-data/nodes__analytical_data__property__customer": "Cliente", "@sage/xtrem-finance-data/nodes__analytical_data__property__customerType": "Tipo de cliente", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension01": "Sección 01", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension02": "Sección 02", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension03": "Sección 03", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension04": "Sección 04", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension05": "Sección 05", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension06": "Sección 06", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension07": "Sección 07", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension08": "Sección 08", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension09": "Sección 09", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension10": "Sección 10", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension11": "Sección 11", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension12": "Sección 12", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension13": "Sección 13", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension14": "Sección 14", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension15": "Sección 15", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension16": "Sección 16", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension17": "Sección 17", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension18": "Sección 18", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension19": "Sección 19", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension20": "Sección 20", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType01": "Eje 01", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType02": "Eje 02", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType03": "Eje 03", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType04": "Eje 04", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType05": "Eje 05", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType06": "Eje 06", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType07": "Eje 07", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType08": "Eje 08", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType09": "Eje 09", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType10": "Eje 10", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType11": "Eje 11", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType12": "Eje 12", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType13": "Eje 13", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType14": "Eje 14", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType15": "Eje 15", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType16": "Eje 16", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType17": "Eje 17", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType18": "Eje 18", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType19": "Eje 19", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType20": "Eje 20", "@sage/xtrem-finance-data/nodes__analytical_data__property__employee": "Trabajador", "@sage/xtrem-finance-data/nodes__analytical_data__property__employeeType": "Tipo de trabajador", "@sage/xtrem-finance-data/nodes__analytical_data__property__financialSite": "Planta financiera", "@sage/xtrem-finance-data/nodes__analytical_data__property__financialSiteType": "Tipo de planta financiera", "@sage/xtrem-finance-data/nodes__analytical_data__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__analytical_data__property__itemType": "Tipo de artículo", "@sage/xtrem-finance-data/nodes__analytical_data__property__manufacturingSite": "Planta de producción", "@sage/xtrem-finance-data/nodes__analytical_data__property__manufacturingSiteType": "Tipo de planta de producción", "@sage/xtrem-finance-data/nodes__analytical_data__property__project": "Proyecto", "@sage/xtrem-finance-data/nodes__analytical_data__property__projectType": "Tipo de proyecto", "@sage/xtrem-finance-data/nodes__analytical_data__property__stockSite": "Planta de stock", "@sage/xtrem-finance-data/nodes__analytical_data__property__stockSiteType": "Tipo de planta de stock", "@sage/xtrem-finance-data/nodes__analytical_data__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__analytical_data__property__supplierType": "<PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-finance-data/nodes__analytical_data__property__task": "Tarea", "@sage/xtrem-finance-data/nodes__analytical_data__property__taskType": "Tipo de tarea", "@sage/xtrem-finance-data/nodes__attribute__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__attribute__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__attribute__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__attribute__attribute_restricted_to": "Define un atributo válido en \"Restringido a\".", "@sage/xtrem-finance-data/nodes__attribute__node_link_value": "Define el vínculo al nodo como \"Atributo\".", "@sage/xtrem-finance-data/nodes__attribute__node_name": "Atributo", "@sage/xtrem-finance-data/nodes__attribute__property__attributeRestrictedTo": "Atributo restringido", "@sage/xtrem-finance-data/nodes__attribute__property__attributeRestrictedToId": "Atributo restringido a id.", "@sage/xtrem-finance-data/nodes__attribute__property__attributeType": "Tipo de atributo", "@sage/xtrem-finance-data/nodes__attribute__property__composedDescription": "Descripción compuesta", "@sage/xtrem-finance-data/nodes__attribute__property__id": "Id.", "@sage/xtrem-finance-data/nodes__attribute__property__isActive": "Activo", "@sage/xtrem-finance-data/nodes__attribute__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__attribute__property__name": "Nombre", "@sage/xtrem-finance-data/nodes__attribute__property__site": "Planta", "@sage/xtrem-finance-data/nodes__attribute__referential__integrity": "El atributo no se puede eliminar porque ya se está utilizando.", "@sage/xtrem-finance-data/nodes__attribute__type__referential__integrity": "El tipo de atributo no se puede eliminar porque ya se está utilizando.", "@sage/xtrem-finance-data/nodes__attribute__type__referential__integrity_isRestrictedToInUse": "El tipo de atributo no se puede desactivar porque ya se está utilizando.", "@sage/xtrem-finance-data/nodes__attribute_type__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__attribute_type__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__attribute_type__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__attribute_type__node__isActive": "Solo puedes activar el tipo de atributo si el tipo de atributo restringido {{id}} está activo.", "@sage/xtrem-finance-data/nodes__attribute_type__node_link_changeable_properties": "Solo puedes actualizar la casilla \"Activo\" del nombre de las propiedades.", "@sage/xtrem-finance-data/nodes__attribute_type__node_name": "Tipo de atributo", "@sage/xtrem-finance-data/nodes__attribute_type__property__analyticalMeasureType": "Tipo de medida analítica", "@sage/xtrem-finance-data/nodes__attribute_type__property__attributeTypeRestrictedTo": "Tipo de atributo restringido", "@sage/xtrem-finance-data/nodes__attribute_type__property__id": "Id.", "@sage/xtrem-finance-data/nodes__attribute_type__property__isActive": "Activo", "@sage/xtrem-finance-data/nodes__attribute_type__property__isLinkedToItem": "Vinculado al artículo", "@sage/xtrem-finance-data/nodes__attribute_type__property__isLinkedToSite": "Vinculado a la planta", "@sage/xtrem-finance-data/nodes__attribute_type__property__linkedTo": "Vinculado a", "@sage/xtrem-finance-data/nodes__attribute_type__property__name": "Nombre", "@sage/xtrem-finance-data/nodes__attribute_type__property__nodeLink": "Vín<PERSON><PERSON> a nodo", "@sage/xtrem-finance-data/nodes__attribute_type__property__queryFilter": "Filtro de consultas", "@sage/xtrem-finance-data/nodes__bank_account__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__bank_account__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__bank_account__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__bank_account__node_name": "Cuenta bancaria", "@sage/xtrem-finance-data/nodes__bank_account__property__bankAccountType": "Tipo de cuenta bancaria", "@sage/xtrem-finance-data/nodes__bank_account__property__currency": "Divisa", "@sage/xtrem-finance-data/nodes__bank_account__property__financialSite": "Planta financiera", "@sage/xtrem-finance-data/nodes__bank_account__property__id": "Id.", "@sage/xtrem-finance-data/nodes__bank_account__property__isActive": "Activa", "@sage/xtrem-finance-data/nodes__bank_account__property__name": "Nombre", "@sage/xtrem-finance-data/nodes__base_business_relation__datev_id_invalid": "El id. de DATEV debe ser un número entre {{fromValue}} y {{toValue}}.", "@sage/xtrem-finance-data/nodes__base_business_relation__datev_id_not_unique": "El id. de DATEV debe ser único.", "@sage/xtrem-finance-data/nodes__base_finance_document__node_name": "Documento contable base", "@sage/xtrem-finance-data/nodes__base_finance_document__property__lines": "Líneas", "@sage/xtrem-finance-data/nodes__base_finance_document__property__number": "Número", "@sage/xtrem-finance-data/nodes__base_finance_line__node_name": "Línea de contabilidad base", "@sage/xtrem-finance-data/nodes__base_finance_line__property__attributesAndDimensions": "Atributos y secciones", "@sage/xtrem-finance-data/nodes__base_finance_line__property__document": "Documento", "@sage/xtrem-finance-data/nodes__base_finance_line__property__documentId": "Id. de documento", "@sage/xtrem-finance-data/nodes__base_finance_line__property__documentNumber": "Número de documento", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__node_name": "Sección de línea de contabilidad base", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__businessSite": "Planta empresarial", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__customer": "Cliente", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension01": "Sección 01", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension02": "Sección 02", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension03": "Sección 03", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension04": "Sección 04", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension05": "Sección 05", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension06": "Sección 06", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension07": "Sección 07", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension08": "Sección 08", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension09": "Sección 09", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension10": "Sección 10", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension11": "Sección 11", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension12": "Sección 12", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension13": "Sección 13", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension14": "Sección 14", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension15": "Sección 15", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension16": "Sección 16", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension17": "Sección 17", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension18": "Sección 18", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension19": "Sección 19", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension20": "Sección 20", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__employee": "Trabajador", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__financialSite": "Planta financiera", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__manufacturingSite": "Planta de producción", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__originLine": "<PERSON><PERSON>ea de origen", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__project": "Proyecto", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__stockSite": "Planta de stock", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__task": "Tarea", "@sage/xtrem-finance-data/nodes__base_open_item__document_type_invalid": "El documento debe ser una factura de compra, una factura rectificativa de compra, una factura contable de proveedor, una factura de venta, una factura rectificativa de venta o una factura contable de cliente.", "@sage/xtrem-finance-data/nodes__base_open_item__negative_forced_amount_paid": "El importe pagado forzado debe ser superior o igual a 0.", "@sage/xtrem-finance-data/nodes__base_open_item__node_name": "Vencimiento base", "@sage/xtrem-finance-data/nodes__base_open_item__property__businessEntity": "Entidad empresarial", "@sage/xtrem-finance-data/nodes__base_open_item__property__businessEntityPayment": "Pago de entidad empresarial", "@sage/xtrem-finance-data/nodes__base_open_item__property__businessRelation": "Relación de negocio", "@sage/xtrem-finance-data/nodes__base_open_item__property__closeReason": "Motivo de cierre", "@sage/xtrem-finance-data/nodes__base_open_item__property__closeText": "Nota de c<PERSON>re", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountDue": "Importe pendiente de sociedad", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountDueSigned": "Importe pendiente de sociedad", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountPaid": "Importe pagado de sociedad", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountPaidSigned": "Importe pagado de sociedad con signo", "@sage/xtrem-finance-data/nodes__base_open_item__property__currency": "Divisa", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountAmount": "Importe de descuento", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountDate": "Fecha de descuento", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountFrom": "Descuento desde", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountPaymentBeforeDate": "Fecha límite de pago de descuento", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountType": "Tipo de descuento", "@sage/xtrem-finance-data/nodes__base_open_item__property__displayDiscountPaymentDate": "Fecha de pago de descuento", "@sage/xtrem-finance-data/nodes__base_open_item__property__documentNumber": "Número de documento", "@sage/xtrem-finance-data/nodes__base_open_item__property__documentSysId": "Id. de sistema de documento", "@sage/xtrem-finance-data/nodes__base_open_item__property__documentType": "Tipo de documento", "@sage/xtrem-finance-data/nodes__base_open_item__property__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance-data/nodes__base_open_item__property__financialSiteAmountDue": "Importe pendiente de planta financiera", "@sage/xtrem-finance-data/nodes__base_open_item__property__financialSiteAmountPaid": "Importe pagado de planta financiera", "@sage/xtrem-finance-data/nodes__base_open_item__property__forcedAmountPaid": "Importe pagado forzado", "@sage/xtrem-finance-data/nodes__base_open_item__property__forcedAmountPaidSigned": "Importe pagado forzado con signo", "@sage/xtrem-finance-data/nodes__base_open_item__property__penaltyAmount": "Importe de recargo", "@sage/xtrem-finance-data/nodes__base_open_item__property__penaltyPaymentType": "Tipo de pago de recargo", "@sage/xtrem-finance-data/nodes__base_open_item__property__remainingCompanyAmountSigned": "Importe restante de sociedad con signo", "@sage/xtrem-finance-data/nodes__base_open_item__property__remainingTransactionAmountSigned": "Importe restante de transacción con signo", "@sage/xtrem-finance-data/nodes__base_open_item__property__status": "Estado", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountDue": "Importe pendiente de transacción", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountDueSigned": "Importe pendiente de transacción firmado", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountPaid": "Importe pagado de transacción", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountPaidSigned": "Importe pagado de transacción con signo", "@sage/xtrem-finance-data/nodes__base_open_item__property__type": "Tipo", "@sage/xtrem-finance-data/nodes__base_payment_document__id_already_exists": "El id. ya existe. No se asignará ningún número de secuencia a este documento.", "@sage/xtrem-finance-data/nodes__base_payment_document__node_name": "Documento de pago base", "@sage/xtrem-finance-data/nodes__base_payment_document__payment_amount_discrepancy": "El importe de pago del documento ({{amount}}) debe coincidir con el importe de pago total de todas las líneas ({{total}}).", "@sage/xtrem-finance-data/nodes__base_payment_document__payment_amount_invalid": "El importe de pago del documento ({{amount}}) debe ser positivo.", "@sage/xtrem-finance-data/nodes__base_payment_document__property__amount": "Importe", "@sage/xtrem-finance-data/nodes__base_payment_document__property__amountBankCurrency": "Importe en divisa bancaria", "@sage/xtrem-finance-data/nodes__base_payment_document__property__bankAccount": "Cuenta bancaria", "@sage/xtrem-finance-data/nodes__base_payment_document__property__businessRelation": "Relación de negocio", "@sage/xtrem-finance-data/nodes__base_payment_document__property__businessRelationName": "Nombre de relación de negocio", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyAmount": "Importe de sociedad", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyCurrency": "Divisa de sociedad", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyExchangeRate": "Tipo de cambio de sociedad", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyExchangeRateDivisor": "Divisor de tipo de cambio de sociedad", "@sage/xtrem-finance-data/nodes__base_payment_document__property__currency": "Divisa", "@sage/xtrem-finance-data/nodes__base_payment_document__property__customer": "Cliente", "@sage/xtrem-finance-data/nodes__base_payment_document__property__exchangeRateDate": "Fecha de tipo de cambio", "@sage/xtrem-finance-data/nodes__base_payment_document__property__financialSite": "Planta financiera", "@sage/xtrem-finance-data/nodes__base_payment_document__property__financialSiteName": "Nombre de planta financiera", "@sage/xtrem-finance-data/nodes__base_payment_document__property__isVoided": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_payment_document__property__lines": "Líneas", "@sage/xtrem-finance-data/nodes__base_payment_document__property__number": "Número", "@sage/xtrem-finance-data/nodes__base_payment_document__property__paymentDate": "Fecha de pago", "@sage/xtrem-finance-data/nodes__base_payment_document__property__paymentMethod": "Forma de pago", "@sage/xtrem-finance-data/nodes__base_payment_document__property__postingDate": "Fecha de contabilización", "@sage/xtrem-finance-data/nodes__base_payment_document__property__postingStatus": "Estado de contabilización", "@sage/xtrem-finance-data/nodes__base_payment_document__property__reference": "Referencia", "@sage/xtrem-finance-data/nodes__base_payment_document__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_payment_document__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-finance-data/nodes__base_payment_document__property__type": "Tipo", "@sage/xtrem-finance-data/nodes__base_payment_document__property__voidDate": "Fecha de anulación", "@sage/xtrem-finance-data/nodes__base_payment_document__property__voidText": "Nota de anulación", "@sage/xtrem-finance-data/nodes__base-open_item__close_reason_mandatory": "Introduce el motivo de cierre.", "@sage/xtrem-finance-data/nodes__base-open_item__paid_company_amount_discrepancy": "El importe pagado de la sociedad debe ser inferior o igual a su importe pendiente.", "@sage/xtrem-finance-data/nodes__base-open_item__paid_financial_site_amount_discrepancy": "El importe pagado de la planta financiera debe ser inferior o igual a su importe pendiente.", "@sage/xtrem-finance-data/nodes__base-open_item__paid_transaction_amount_discrepancy": "El importe pagado de la transacción debe ser inferior o igual a su importe pendiente.", "@sage/xtrem-finance-data/nodes__base-open_item__wrong_forced_amount_paid": "El importe pagado forzado debe estar entre 0 y {{maxForcedAmount}}.", "@sage/xtrem-finance-data/nodes__base-payment_document__bank_amount_invalid": "El importe en divisa bancaria del documento ({{amount}}) debe ser positivo.", "@sage/xtrem-finance-data/nodes__base-payment_document__financial_site_discrepancy": "La planta financiera del documento debe coincidir con la de la cuenta bancaria.", "@sage/xtrem-finance-data/nodes__base-payment-document__void_date_mandatory": "Introduce la fecha de anulación.", "@sage/xtrem-finance-data/nodes__base-payment-document__void_date_should_be_after_payment_date": "La fecha de anulación debe ser posterior a la de pago.", "@sage/xtrem-finance-data/nodes__close_reason__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__close_reason__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__close_reason__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__close_reason__node_name": "Motivo de cierre", "@sage/xtrem-finance-data/nodes__close_reason__property__id": "Id.", "@sage/xtrem-finance-data/nodes__close_reason__property__isActive": "Activo", "@sage/xtrem-finance-data/nodes__close_reason__property__name": "Nombre", "@sage/xtrem-finance-data/nodes__company__datev_number_invalid": "Introduce un número entre {{first}} y {{last}}.", "@sage/xtrem-finance-data/nodes__company__datev_number_mandatory": "En el caso de la legislación alemana, este número es obligatorio.", "@sage/xtrem-finance-data/nodes__company__datev_number_only_for_germany": "Este número solo es necesario para la legislación alemana.", "@sage/xtrem-finance-data/nodes__company__project_task_discrepancy": "Los atributos de proyecto y de tarea deben tener el mismo origen.", "@sage/xtrem-finance-data/nodes__company_attribute_type__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__company_attribute_type__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__company_attribute_type__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__company_attribute_type__attribute-type-not-active": "El tipo de atributo no está activo.", "@sage/xtrem-finance-data/nodes__company_attribute_type__node_name": "Tipo de atributo de sociedad", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__analyticalMeasureType": "Tipo de medida analítica", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__attributeType": "Tipo de atributo", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__company": "Sociedad", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__isRequired": "Obligatorio", "@sage/xtrem-finance-data/nodes__company_default_attribute__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__company_default_attribute__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__company_default_attribute__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__company_default_attribute__node_link_master_data_default__not_allowed": "El valor {{masterDataDefault}} no está permitido en el documento de tipo {{dimensionDefinitionLevel}}.", "@sage/xtrem-finance-data/nodes__company_default_attribute__node_name": "Atributo por defecto de sociedad", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__attributeType": "Tipo de atributo", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__company": "Sociedad", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__dimensionDefinitionLevel": "Nivel de definición de sección", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__masterDataDefault": "Valor predeterminado de datos maestros", "@sage/xtrem-finance-data/nodes__company_default_dimension__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__company_default_dimension__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__company_default_dimension__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__company_default_dimension__node_name": "Sección por defecto de sociedad", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__company": "Sociedad", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__dimensionDefinitionLevel": "Nivel de definición de sección", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__dimensionType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__masterDataDefault": "Valor predeterminado de datos maestros", "@sage/xtrem-finance-data/nodes__company_dimension_type__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__company_dimension_type__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__company_dimension_type__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__company_dimension_type__dimension-type-not-active": "El eje no está activo.", "@sage/xtrem-finance-data/nodes__company_dimension_type__node_name": "Eje de sociedad", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__analyticalMeasureType": "Tipo de medida analítica", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__company": "Sociedad", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__dimensionType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__isRequired": "Obligatorio", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_account_datev_id_length": "La longitud del id. de DATEV debe ser la misma que la de la cuenta en todas las cuentas.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_customer_datev_id_length": "La longitud del id. de DATEV debe ser la misma que la del id. de cliente y proveedor en todos los clientes.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_customer_datev_id_range": "Los ids. de DATEV deben situarse dentro del rango de ids. de cliente.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_supplier_datev_id_length": "La longitud del id. de DATEV debe ser la misma que la del id. de cliente y proveedor en todos los proveedores.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_supplier_datev_id_range": "Los ids. de DATEV deben situarse dentro del rango de ids. de proveedor.", "@sage/xtrem-finance-data/nodes__datev_configuration__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__datev_configuration__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__datev_configuration__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__datev_configuration__invalid_length": "Introduce un número entre {{fromValue}} y {{toValue}}.", "@sage/xtrem-finance-data/nodes__datev_configuration__mutation__datevConfigurationControlsOnSave": "Controles de configuración de DATEV al guardar", "@sage/xtrem-finance-data/nodes__datev_configuration__mutation__datevConfigurationControlsOnSave__failed": "Error en los controles de configuración de DATEV al guardar", "@sage/xtrem-finance-data/nodes__datev_configuration__mutation__datevConfigurationControlsOnSave__parameter__datevConfiguration": "Configuración de DATEV", "@sage/xtrem-finance-data/nodes__datev_configuration__node_name": "Configuración de DATEV", "@sage/xtrem-finance-data/nodes__datev_configuration__property__accountLength": "<PERSON><PERSON><PERSON> de cuenta", "@sage/xtrem-finance-data/nodes__datev_configuration__property__customerRangeEnd": "<PERSON><PERSON>o de cliente", "@sage/xtrem-finance-data/nodes__datev_configuration__property__customerRangeStart": "<PERSON><PERSON> rango de cliente", "@sage/xtrem-finance-data/nodes__datev_configuration__property__customerSupplierLength": "Longitud de id. de cliente y proveedor", "@sage/xtrem-finance-data/nodes__datev_configuration__property__id": "Id.", "@sage/xtrem-finance-data/nodes__datev_configuration__property__isActive": "Activa", "@sage/xtrem-finance-data/nodes__datev_configuration__property__name": "Nombre", "@sage/xtrem-finance-data/nodes__datev_configuration__property__skrCoa": "SKR COA", "@sage/xtrem-finance-data/nodes__datev_configuration__property__supplierRangeEnd": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-finance-data/nodes__datev_configuration__property__supplierRangeStart": "<PERSON><PERSON> rango de <PERSON>", "@sage/xtrem-finance-data/nodes__datev_configuration__wrong_length": "La longitud de la cuenta no coincide con la del id. de cliente y proveedor.", "@sage/xtrem-finance-data/nodes__dimension__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__dimension__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__dimension__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__dimension__node_name": "Sección", "@sage/xtrem-finance-data/nodes__dimension__property__composedDescription": "Descripción compuesta", "@sage/xtrem-finance-data/nodes__dimension__property__dimensionType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__dimension__property__id": "Id.", "@sage/xtrem-finance-data/nodes__dimension__property__isActive": "Activa", "@sage/xtrem-finance-data/nodes__dimension__property__name": "Nombre", "@sage/xtrem-finance-data/nodes__dimension__referential__integrity": "La sección no se puede eliminar porque ya se está utilizando.", "@sage/xtrem-finance-data/nodes__dimension__type__inactive": "El eje no está activo.", "@sage/xtrem-finance-data/nodes__dimension__type__referential__integrity": "El eje no se puede eliminar porque ya se está utilizando.", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__node_name": "Nivel de definición y valor predeterminado de sección", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__property__dimensionDefinitionLevel": "Nivel de definición de sección", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__property__masterDataDefault": "Valor predeterminado de datos maestros", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getAttributesAndDimensionsFromItem": "Obtener atributos y secciones de artículo", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getAttributesAndDimensionsFromItem__failed": "Error al obtener los atributos y las secciones del artículo", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getAttributesAndDimensionsFromItem__parameter__data": "Datos", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensions": "Obtener atributos y secciones por defecto", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensions__failed": "Error al obtener los atributos y las secciones por defecto", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensions__parameter__data": "Datos", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensionsOrderToOrder": "Obtener atributos y secciones por defecto de pedido por pedido", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensionsOrderToOrder__failed": "Error al obtener los atributos y las secciones por defecto del pedido por pedido", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensionsOrderToOrder__parameter__data": "Datos", "@sage/xtrem-finance-data/nodes__dimension_type__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__dimension_type__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__dimension_type__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__dimension_type__max_value": "Todos los ejes disponibles se han asignado.", "@sage/xtrem-finance-data/nodes__dimension_type__node_name": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__dimension_type__property__analyticalMeasureType": "Tipo de medida analítica", "@sage/xtrem-finance-data/nodes__dimension_type__property__dimensions": "Secciones", "@sage/xtrem-finance-data/nodes__dimension_type__property__docProperty": "Propiedad de documento", "@sage/xtrem-finance-data/nodes__dimension_type__property__isActive": "Activo", "@sage/xtrem-finance-data/nodes__dimension_type__property__isUsed": "En uso", "@sage/xtrem-finance-data/nodes__dimension_type__property__name": "Nombre", "@sage/xtrem-finance-data/nodes__dimension_type__property__setupId": "Id. de parametrización", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_accounting_staging": "Hay asientos pendientes para este documento. No se reenviará ninguna notificación.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_finance_transaction_records": "No hay registros de transacciones contables para este documento. No se reenviará ninguna notificación.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_finance_transaction_status": "El documento se está procesando. No se reenviará ninguna notificación.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_package_active": "El paquete de contabilidad no está activo.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_pi_allocated_to_po": "Esta factura tiene valores asignados a un pedido de compra. No se reenviará ninguna notificación.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_po_allocated_to_pr": "Esta recepción tiene valores asignados desde un pedido de compra. No se reenviará ninguna notificación.", "@sage/xtrem-finance-data/nodes__finance_transaction__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__finance_transaction__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__finance_transaction__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__finance_transaction__node_name": "Transacción contable", "@sage/xtrem-finance-data/nodes__finance_transaction__property__batchId": "<PERSON><PERSON><PERSON> de tanda", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentNumber": "Número de documento", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentNumberLink": "Vínculo a documento", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentSysId": "Id. de sistema de documento", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentType": "Tipo de documento", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financeIntegrationApp": "Aplicación de integración contable", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financeIntegrationAppRecordId": "Id. de registro de aplicación de integración contable", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financeIntegrationAppUrl": "URL de aplicación de integración contable", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financialSite": "Planta financiera", "@sage/xtrem-finance-data/nodes__finance_transaction__property__hasSourceForDimensionLines": "Origen de líneas de sección", "@sage/xtrem-finance-data/nodes__finance_transaction__property__lastStatusUpdate": "Last status update", "@sage/xtrem-finance-data/nodes__finance_transaction__property__lines": "Líneas", "@sage/xtrem-finance-data/nodes__finance_transaction__property__message": "Men<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__finance_transaction__property__paymentTracking": "Seguimiento de pagos", "@sage/xtrem-finance-data/nodes__finance_transaction__property__postingStatus": "Estado de contabilización", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentLink": "Vínculo a documento de origen", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentNumber": "Número de documento de origen", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentSysId": "Id. de sistema de documento de origen", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentType": "Tipo de documento de origen", "@sage/xtrem-finance-data/nodes__finance_transaction__property__status": "Estado", "@sage/xtrem-finance-data/nodes__finance_transaction__property__targetDocumentNumber": "Número de documento de destino", "@sage/xtrem-finance-data/nodes__finance_transaction__property__targetDocumentSysId": "Id. de sistema de documento de destino", "@sage/xtrem-finance-data/nodes__finance_transaction__property__targetDocumentType": "Tipo de documento de destino", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusData": "Obtener datos de estado de contabilización", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusData__failed": "Error al obtener los datos del estado de contabilización", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusData__parameter__documentNumber": "Número de documento", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId": "Obtener datos de estado de contabilización por id. de documento", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId__failed": "Error al obtener los datos del estado de contabilización por id. de documento", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId__parameter__documentSysId": "Id. de sistema de documento", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId__parameter__documentType": "Tipo de documento", "@sage/xtrem-finance-data/nodes__finance_transaction__status_update_not_allowed": "El estado de transacción contable no puede pasar de \"{{previousStatus}}\" a \"{{newStatus}}\".", "@sage/xtrem-finance-data/nodes__finance_transaction_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__finance_transaction_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__finance_transaction_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__finance_transaction_line__node_name": "Línea de transacción contable", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__financeTransaction": "Transacción contable", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__isSourceForDimension": "Origen de sección", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__sourceDocumentNumber": "Número de documento de origen", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__sourceDocumentSysId": "Id. de sistema de documento de origen", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__sourceDocumentType": "Tipo de documento de origen", "@sage/xtrem-finance-data/nodes__journal__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__journal__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__journal__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__journal__node_name": "Diario", "@sage/xtrem-finance-data/nodes__journal__primaryDocumentType_not_allowed": "El tipo de documento principal solo se puede definir para la legislación francesa.", "@sage/xtrem-finance-data/nodes__journal__property__id": "Id.", "@sage/xtrem-finance-data/nodes__journal__property__isActive": "Activo", "@sage/xtrem-finance-data/nodes__journal__property__isSubjectToGlTaxExcludedAmount": "Sujeto a importe sin impuestos de LM", "@sage/xtrem-finance-data/nodes__journal__property__legislation": "Legislación", "@sage/xtrem-finance-data/nodes__journal__property__name": "Nombre", "@sage/xtrem-finance-data/nodes__journal__property__primaryDocumentType": "Tipo de documento principal", "@sage/xtrem-finance-data/nodes__journal__property__secondaryDocumentType": "Tipo de documento secundario", "@sage/xtrem-finance-data/nodes__journal__property__sequence": "Secuencia", "@sage/xtrem-finance-data/nodes__journal__property__taxImpact": "Impacto fiscal", "@sage/xtrem-finance-data/nodes__journal__secondaryDocumentType_not_allowed": "El tipo de documento secundario solo se puede definir para la legislación francesa.", "@sage/xtrem-finance-data/nodes__journal__taxImpact_cannot_be_set": "Al menos una línea de la solución de impuestos debe tener marcada la opción \"Asignada a importe sin impuestos de LM\" para definir la opción \"Impacto fiscal\" en el diario.", "@sage/xtrem-finance-data/nodes__journal_cannot_modify_account_entry_exists": "No puedes cambiar el número de secuencia. Este diario contiene un asiento.", "@sage/xtrem-finance-data/nodes__journal_entry_type__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__journal_entry_type__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__journal_entry_type__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__journal_entry_type__contra_journal_entry_type_line_invalid": "El tipo de asiento de contrapartida de la línea debe coincidir con el tipo de asiento.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_account_type_empty": "Deja el tipo de cuenta de la cabecerar en blanco.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_account_type_mandatory": "Introduce el tipo de cuenta en la cabecera.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_amount_type_empty": "Deja el tipo de importe de la cabecera en blanco.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_amount_type_mandatory": "Introduce el tipo de importe en la cabecera.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_journal_empty": "Deja el diario de la cabecera en blanco.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_journal_mandatory": "Introduce el diario en la cabecera.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_posting_date_empty": "Deja la fecha contable de la cabecera en blanco.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_posting_date_mandatory": "Introduce la fecha de contabilización en la cabecera.", "@sage/xtrem-finance-data/nodes__journal_entry_type__node_name": "Tipo de asiento", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__documentType": "Tipo de documento", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerAccountType": "Tipo de cuenta", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerAmountType": "Tipo de importe", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerDescription": "Descripción", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerJournal": "Diario", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerPostingDate": "<PERSON><PERSON> con<PERSON>", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__immediatePosting": "Contabilización inmediata", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__isActive": "Activo", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__legislation": "Legislación", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__lines": "Líneas", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__name": "Nombre", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__setupId": "Id. de parametrización", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__targetDocumentType": "Tipo de documento de destino", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__forced_false": "The Landed cost items may only be set for movement type Document.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__not_allowed": "The Landed cost items are not allowed if the posting class type is not item or the posting class is not detailed.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__posting_class_definition_control": "The Landed cost items are not allowed by the account type.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__forced_false": "The Non-stock items may only be set for movement type Document.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__not_allowed": "The Non-stock items are not allowed if the posting class type is not item or the posting class is not detailed.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__posting_class_definition_control": "The Non-stock items are not allowed by the account type.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__forced_false": "The Service items may only be set for movement type Document.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__not_allowed": "The Service items are not allowed if the posting class type is not item or the posting class is not detailed.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__posting_class_definition_control": "The Service items are not allowed by the account type.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__forced_true": "Selecciona \"Artículos de stock\" en el tipo de movimiento {{movementType}}.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__not_allowed": "The Stock items are not allowed if the posting class type is not item or the posting class is not detailed.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__posting_class_definition_control": "The Stock items are not allowed by the account type.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__node_name": "Línea de tipo de asiento", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__accountType": "Tipo de cuenta", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__amountType": "Tipo de importe", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__commonReference": "Referencia común", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__contraJournalEntryTypeLine": "Línea de tipo de asiento de contrapartida", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isLandedCostItemAllowed": "Artículo de gastos de entrega permitido", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isNonStockItemAllowed": "Artículo sin stock permitido", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isServiceItemAllowed": "Artículo de tipo servicio permitido", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isStockItemAllowed": "Artículo de stock permitido", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__itemStockManagementCriteria": "Criterios de gestión de stock de artículo", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__journalEntryType": "Tipo de asiento", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__movementType": "Tipo de movimiento", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__sign": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__sign__empty": "Deja el signo en blanco.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__sign_mandatory": "Introduce el signo.", "@sage/xtrem-finance-data/nodes__journal-entry-type-line__at_least_one_item_type_mandatory": "At least one of Stock items, Non-stock items, Service items or Landed cost items must be set for Account type with Posting class type Item.", "@sage/xtrem-finance-data/nodes__journal-entry-type-line__contra_journal_entry_type_line_mandatory": "Si el documento de destino es un asiento, la cuenta de contrapartida es obligatoria.", "@sage/xtrem-finance-data/nodes__open_items__type_invalid": "El vencimiento debe ser de tipo \"Cliente\" o \"Proveedor\".", "@sage/xtrem-finance-data/nodes__payment_document_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__payment_document_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__payment_document_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__payment_document_line__negative_discount_amount": "El importe de descuento debe ser superior o igual a 0.", "@sage/xtrem-finance-data/nodes__payment_document_line__negative_penalty_amount": "El importe de recargo debe ser superior o igual a 0.", "@sage/xtrem-finance-data/nodes__payment_document_line__node_name": "Línea de documento de pago", "@sage/xtrem-finance-data/nodes__payment_document_line__property__adjustmentAmount": "Importe de regularización", "@sage/xtrem-finance-data/nodes__payment_document_line__property__amount": "Importe", "@sage/xtrem-finance-data/nodes__payment_document_line__property__amountBankCurrency": "Importe en divisa bancaria", "@sage/xtrem-finance-data/nodes__payment_document_line__property__companyAmount": "Importe de sociedad", "@sage/xtrem-finance-data/nodes__payment_document_line__property__companyCurrency": "Divisa de sociedad", "@sage/xtrem-finance-data/nodes__payment_document_line__property__currency": "Divisa", "@sage/xtrem-finance-data/nodes__payment_document_line__property__discountAmount": "Importe de descuento", "@sage/xtrem-finance-data/nodes__payment_document_line__property__document": "Documento", "@sage/xtrem-finance-data/nodes__payment_document_line__property__documentId": "Id. de documento", "@sage/xtrem-finance-data/nodes__payment_document_line__property__documentNumber": "Número de documento", "@sage/xtrem-finance-data/nodes__payment_document_line__property__financialSite": "Planta financiera", "@sage/xtrem-finance-data/nodes__payment_document_line__property__origin": "Origen", "@sage/xtrem-finance-data/nodes__payment_document_line__property__originalNodeFactory": "<PERSON><PERSON> original", "@sage/xtrem-finance-data/nodes__payment_document_line__property__originalOpenItem": "Vencimiento original", "@sage/xtrem-finance-data/nodes__payment_document_line__property__paymentTracking": "Seguimiento de pagos", "@sage/xtrem-finance-data/nodes__payment_document_line__property__penaltyAmount": "Importe de recargo", "@sage/xtrem-finance-data/nodes__payment_document_line__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-finance-data/nodes__payment_tracking__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__payment_tracking__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__payment_tracking__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__payment_tracking__node_name": "Seguimiento de pagos", "@sage/xtrem-finance-data/nodes__payment_tracking__property__amountPaid": "Importe pagado", "@sage/xtrem-finance-data/nodes__payment_tracking__property__currency": "Divisa", "@sage/xtrem-finance-data/nodes__payment_tracking__property__discountPaymentAmount": "Importe de pago de descuento", "@sage/xtrem-finance-data/nodes__payment_tracking__property__discountPaymentBeforeDate": "Fecha límite de pago de descuento", "@sage/xtrem-finance-data/nodes__payment_tracking__property__discountPaymentType": "Tipo de pago de descuento", "@sage/xtrem-finance-data/nodes__payment_tracking__property__document": "Documento", "@sage/xtrem-finance-data/nodes__payment_tracking__property__forcedAmountPaid": "Importe pagado forzado", "@sage/xtrem-finance-data/nodes__payment_tracking__property__openItems": "Vencimientos", "@sage/xtrem-finance-data/nodes__payment_tracking__property__paymentLines": "Líneas de pago", "@sage/xtrem-finance-data/nodes__payment_tracking__property__paymentTerm": "Condiciones de pago", "@sage/xtrem-finance-data/nodes__payment_tracking__property__penaltyPaymentAmount": "Importe de pago de recargo", "@sage/xtrem-finance-data/nodes__payment_tracking__property__penaltyPaymentType": "Tipo de pago de recargo", "@sage/xtrem-finance-data/nodes__payment_tracking__property__status": "Estado", "@sage/xtrem-finance-data/nodes__posting_class__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__posting_class__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__posting_class__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_landed_cost_items": "Al menos una línea permite artículos de gastos de entrega.", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_non_stock_items": "Al menos una línea permite artículos sin stock.", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_service_items": "Al menos una línea permite artículos de tipo servicio.", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_stock_items": "Al menos una línea permite artículos de stock.", "@sage/xtrem-finance-data/nodes__posting_class__lines_mandatory": "Introduce al menos una línea en la clase contable.", "@sage/xtrem-finance-data/nodes__posting_class__node_name": "Clase contable", "@sage/xtrem-finance-data/nodes__posting_class__one_non_detailed_allowed": "Solo se permite una clase contable no detallada de tipo {{type}}.", "@sage/xtrem-finance-data/nodes__posting_class__property__financeItemType": "Tipo de artículo", "@sage/xtrem-finance-data/nodes__posting_class__property__id": "Id.", "@sage/xtrem-finance-data/nodes__posting_class__property__isDetailed": "Detallada", "@sage/xtrem-finance-data/nodes__posting_class__property__isLandedCostItemAllowed": "Artículo de gastos de entrega permitido", "@sage/xtrem-finance-data/nodes__posting_class__property__isNonStockItemAllowed": "Artículo sin stock permitido", "@sage/xtrem-finance-data/nodes__posting_class__property__isServiceItemAllowed": "Artículo de tipo servicio permitido", "@sage/xtrem-finance-data/nodes__posting_class__property__isStockItemAllowed": "Artículo de stock permitido", "@sage/xtrem-finance-data/nodes__posting_class__property__lines": "Líneas", "@sage/xtrem-finance-data/nodes__posting_class__property__name": "Nombre", "@sage/xtrem-finance-data/nodes__posting_class__property__setupId": "Id. de parametrización", "@sage/xtrem-finance-data/nodes__posting_class__property__type": "Tipo", "@sage/xtrem-finance-data/nodes__posting_class__update_is_detailed_not_allowed": "No puedes cambiar la opción \"Detallada\" porque la clase contable \"{{ postingClassName }}\" está vinculada a \"{{recordNameUsingThisPostingClass}}\".", "@sage/xtrem-finance-data/nodes__posting_class_definition__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__posting_class_definition__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__posting_class_definition__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__posting_class_definition__can_have_secondary_criteria_not_allowed": "No puedes introducir un criterio adicional para la clase contable de tipo {{ postingClassType }}.", "@sage/xtrem-finance-data/nodes__posting_class_definition__criteria_and_secondary_criteria_are_the_same": "Cambia los criterios.", "@sage/xtrem-finance-data/nodes__posting_class_definition__node_name": "Definición de clase contable", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__accountTypeName": "Nombre de tipo de cuenta", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__additionalCriteria": "Criterios adicionales", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__canHaveAdditionalCriteria": "Puede tener criterios adicionales", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__financeItemType": "Tipo de artículo", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__id": "Id.", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isDetailed": "Detallada", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isLandedCostItemAllowed": "Artículo de gastos de entrega permitido", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isNonStockItemAllowed": "Artículo sin stock permitido", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isServiceItemAllowed": "Artículo de tipo servicio permitido", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isStockItemAllowed": "Artículo de stock permitido", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__legislation": "Legislación", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__postingClassType": "Tipo de clase contable", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__setupId": "Id. de parametrización", "@sage/xtrem-finance-data/nodes__posting_class_definition__secondary_criteria_incorrect_value": "El criterio secundario debe ser el impuesto.", "@sage/xtrem-finance-data/nodes__posting_class_definition__secondary_criteria_not_allowed": "No puedes introducir un criterio secundario.", "@sage/xtrem-finance-data/nodes__posting_class_definition__tax_solution_control": "Las soluciones de impuestos de esta legislación deben tener al menos una categoría de impuesto obligatoria.", "@sage/xtrem-finance-data/nodes__posting_class_definition__update_is_detailed_not_allowed": "No puedes cambiar la opción \"Detallada\" porque la clase contable \"{{ postingClassName }}\" está vinculada a esta definición.", "@sage/xtrem-finance-data/nodes__posting_class_line__already_used_posting_class_definition": "La definición de esta clase contable ya se utiliza en otra línea.", "@sage/xtrem-finance-data/nodes__posting_class_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__posting_class_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__posting_class_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__posting_class_line__node_name": "Línea de clase contable", "@sage/xtrem-finance-data/nodes__posting_class_line__property__account": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__posting_class_line__property__accountId": "<PERSON><PERSON><PERSON> de cuenta", "@sage/xtrem-finance-data/nodes__posting_class_line__property__chartOfAccount": "Plan de cuentas", "@sage/xtrem-finance-data/nodes__posting_class_line__property__definition": "Definición", "@sage/xtrem-finance-data/nodes__posting_class_line__property__details": "Detalles", "@sage/xtrem-finance-data/nodes__posting_class_line__property__hasDetails": "Tiene de<PERSON>les", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isLandedCostItemAllowed": "Artículo de gastos de entrega permitido", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isNonStockItemAllowed": "Artículo sin stock permitido", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isServiceItemAllowed": "Artículo de tipo servicio permitido", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isStockItemAllowed": "Artículo de stock permitido", "@sage/xtrem-finance-data/nodes__posting_class_line__property__postingClass": "Clase contable", "@sage/xtrem-finance-data/nodes__posting_class_line__property__updateAccountTaxManagement": "Actualizar gestión de impuestos de cuenta", "@sage/xtrem-finance-data/nodes__posting_class_line__query__getPostingClassAccounts": "Obtener cuentas de clase contable", "@sage/xtrem-finance-data/nodes__posting_class_line__query__getPostingClassAccounts__failed": "Error al obtener las cuentas de la clase contable", "@sage/xtrem-finance-data/nodes__posting_class_line__query__getPostingClassAccounts__parameter__postingClassDefinition": "Definición de clase contable", "@sage/xtrem-finance-data/nodes__posting_class_line__wrong_account_tax_management": "Selecciona una gestión de impuestos de cuentas de tipo \"Impuesto\".", "@sage/xtrem-finance-data/nodes__posting_class_line__wrong_posting_class_definition_is_detailed": "Cuando la clase contable tiene un nombre, solo se permiten las definiciones con la casilla \"Detalles\" marcada. Cuando no lo tiene, solo se permiten las definiciones con la casilla \"Detalles\" desmarcada.", "@sage/xtrem-finance-data/nodes__posting_class_line__wrong_posting_class_definition_type": "La definición de la clase contable debe ser de {{ postingClassType }}.", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__mutation__getTaxCategoryIds": "Obtener ids. de categorías de impuesto", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__mutation__getTaxCategoryIds__failed": "Error al obtener los ids. de las categorías de impuesto", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__mutation__getTaxCategoryIds__parameter__legislation": "Legislación", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__no_secondary_criteria_defined": "Esta clase contable no tiene la opción de criterio secundario seleccionada.", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__node_name": "Detalles de línea de clase contable", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__property__account": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__property__postingClassLine": "Línea de clase contable", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__property__tax": "Impuesto", "@sage/xtrem-finance-data/nodes__tax__posting_key_invalid": "La clave contable debe ser un número entre 1 y 9999.", "@sage/xtrem-finance-data/nodes__tax__posting_key_wrong_country": "Solo puedes completar este campo si el país es Alemania.", "@sage/xtrem-finance-data/package__name": "Configuración de contabilidad", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension____navigationPanel__listItem__datevId__title": "Id. de DATEV", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension____navigationPanel__listItem__line11__title": "Clase contable", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__datevId____title": "Id. de DATEV", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____lookupDialogTitle": "Seleccionar sección 01", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____title": "Sección 01", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____lookupDialogTitle": "Seleccionar sección 02", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____title": "Sección 02", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____lookupDialogTitle": "Seleccionar sección 03", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____title": "Sección 03", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____lookupDialogTitle": "Seleccionar sección 04", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____title": "Sección 04", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____lookupDialogTitle": "Seleccionar sección 05", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____title": "Sección 05", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____lookupDialogTitle": "Seleccionar sección 06", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____title": "Sección 06", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____lookupDialogTitle": "Seleccionar sección 07", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____title": "Sección 07", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____lookupDialogTitle": "Seleccionar sección 08", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____title": "Sección 08", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____lookupDialogTitle": "Seleccionar sección 09", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____title": "Sección 09", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____lookupDialogTitle": "Seleccionar sección 10", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____title": "Sección 10", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____lookupDialogTitle": "Seleccionar sección 11", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____title": "Sección 11", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____lookupDialogTitle": "Seleccionar sección 12", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____title": "Sección 12", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____lookupDialogTitle": "Seleccionar sección 13", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____title": "Sección 13", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____lookupDialogTitle": "Seleccionar sección 14", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____title": "Sección 14", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____lookupDialogTitle": "Seleccionar sección 15", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____title": "Sección 15", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____lookupDialogTitle": "Seleccionar sección 16", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____title": "Sección 16", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____lookupDialogTitle": "Seleccionar sección 17", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____title": "Sección 17", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____lookupDialogTitle": "Seleccionar sección 18", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____title": "Sección 18", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____lookupDialogTitle": "Seleccionar sección 19", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____title": "Sección 19", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____lookupDialogTitle": "Seleccionar sección 20", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____title": "Sección 20", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimensionBlock____title": "Secciones", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>aja<PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____title": "Trabajador", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__postingClass____lookupDialogTitle": "Seleccionar clase contable", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__postingClass____title": "Clase contable", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____lookupDialogTitle": "Seleccionar proyecto", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____title": "Proyecto", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarea", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____title": "Tarea", "@sage/xtrem-finance-data/page-extensions__company_extension____navigationPanel__listItem__datevConsultantNumber__title": "Número de asesor de DATEV", "@sage/xtrem-finance-data/page-extensions__company_extension____navigationPanel__listItem__datevCustomerNumber__title": "Número de cliente de DATEV", "@sage/xtrem-finance-data/page-extensions__company_extension__addDefaultDimension____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__company_extension__addDimensionAttributeLine____title": "Sección y atributos obligatorios", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____columns__title__id": "Nombre", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____columns__title__type": "Tipo analítico", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____dropdownActions__title": "Eliminar", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____title": "Secciones obligatorias", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeTypes____columns__title__analyticalMeasureType": "Tipo analítico", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeTypes____title": "Atributos", "@sage/xtrem-finance-data/page-extensions__company_extension__bankAccount____lookupDialogTitle": "Seleccionar cuenta bancaria", "@sage/xtrem-finance-data/page-extensions__company_extension__bankAccount____title": "Cuenta bancaria por defecto", "@sage/xtrem-finance-data/page-extensions__company_extension__country____columns__title__legislation__name": "Legislación", "@sage/xtrem-finance-data/page-extensions__company_extension__datevConsultantNumber____title": "Número de asesor de DATEV", "@sage/xtrem-finance-data/page-extensions__company_extension__datevCustomerNumber____title": "Número de cliente de DATEV", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____columns__columns__company__name__title": "Nombre", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____columns__columns__company__name__title__2": "Id.", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____columns__title__company__name": "Sociedad", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____title": "Atributos", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributesAndDimensions____columns__title__document": "Documento", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributesAndDimensions____dropdownActions__title": "Eliminar", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributesAndDimensions____title": "Reglas de secciones por defecto", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____columns__columns__company__name__title": "Nombre", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____columns__columns__company__name__title__2": "Id.", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____columns__title__company__name": "Sociedad", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____title": "Secciones", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionsBlock____title": "Secciones", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionsSection____title": "Secciones", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionTypes____columns__title__analyticalMeasureType": "Tipo analítico", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionTypes____title": "Secciones", "@sage/xtrem-finance-data/page-extensions__company_extension__doApPosting____title": "Contabilización de documento de proveedor", "@sage/xtrem-finance-data/page-extensions__company_extension__doArPosting____title": "Contabilización de documento de cliente", "@sage/xtrem-finance-data/page-extensions__company_extension__doNonAbsorbedPosting____title": "Contabilización de importe sin absorber", "@sage/xtrem-finance-data/page-extensions__company_extension__doStockPosting____title": "Contabilización de stock", "@sage/xtrem-finance-data/page-extensions__company_extension__doWipPosting____title": "Contabilización de trabajo en curso", "@sage/xtrem-finance-data/page-extensions__company_extension__financePostingBlock____title": "Contabilidad", "@sage/xtrem-finance-data/page-extensions__company_extension__manufacturingPostingBlock____title": "Producción", "@sage/xtrem-finance-data/page-extensions__company_extension__postingClass____columns__title__type": "Tipo", "@sage/xtrem-finance-data/page-extensions__company_extension__postingClass____lookupDialogTitle": "Seleccionar clase contable", "@sage/xtrem-finance-data/page-extensions__company_extension__postingClass____title": "Clase contable", "@sage/xtrem-finance-data/page-extensions__company_extension__postingSection____title": "Contabilización", "@sage/xtrem-finance-data/page-extensions__company_extension__selectAttributeAndDimensionBlock____title": "Gestión", "@sage/xtrem-finance-data/page-extensions__company_extension__selectAttributeAndDimensionSection____title": "Secciones obligatorias", "@sage/xtrem-finance-data/page-extensions__company_extension__selectDimensionAndAttributeTypes____columns__title__id": "Nombre", "@sage/xtrem-finance-data/page-extensions__company_extension__selectDimensionAndAttributeTypes____columns__title__type": "Tipo analítico", "@sage/xtrem-finance-data/page-extensions__company_extension__selectDimensionAndAttributeTypes____title": "Secciones obligatorias", "@sage/xtrem-finance-data/page-extensions__company_extension__stockPostingBlock____title": "Stock", "@sage/xtrem-finance-data/page-extensions__company_extension__taxEngine____title": "Paquete de cálculo de impuestos", "@sage/xtrem-finance-data/page-extensions__company_extension__taxManagementBlock____title": "Gestión de impuestos", "@sage/xtrem-finance-data/page-extensions__customer_extension____navigationPanel__listItem__datevId__title": "Id. de DATEV", "@sage/xtrem-finance-data/page-extensions__customer_extension____navigationPanel__listItem__line11__title": "Clase contable", "@sage/xtrem-finance-data/page-extensions__customer_extension__datevId____title": "Id. de DATEV", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____lookupDialogTitle": "Seleccionar sección 01", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____title": "Sección 01", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____lookupDialogTitle": "Seleccionar sección 02", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____title": "Sección 02", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____lookupDialogTitle": "Seleccionar sección 03", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____title": "Sección 03", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____lookupDialogTitle": "Seleccionar sección 04", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____title": "Sección 04", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____lookupDialogTitle": "Seleccionar sección 05", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____title": "Sección 05", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____lookupDialogTitle": "Seleccionar sección 06", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____title": "Sección 06", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____lookupDialogTitle": "Seleccionar sección 07", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____title": "Sección 07", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____lookupDialogTitle": "Seleccionar sección 08", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____title": "Sección 08", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____lookupDialogTitle": "Seleccionar sección 09", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____title": "Sección 09", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____lookupDialogTitle": "Seleccionar sección 10", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____title": "Sección 10", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____lookupDialogTitle": "Seleccionar sección 11", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____title": "Sección 11", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____lookupDialogTitle": "Seleccionar sección 12", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____title": "Sección 12", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____lookupDialogTitle": "Seleccionar sección 13", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____title": "Sección 13", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____lookupDialogTitle": "Seleccionar sección 14", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____title": "Sección 14", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____lookupDialogTitle": "Seleccionar sección 15", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____title": "Sección 15", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____lookupDialogTitle": "Seleccionar sección 16", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____title": "Sección 16", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____lookupDialogTitle": "Seleccionar sección 17", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____title": "Sección 17", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____lookupDialogTitle": "Seleccionar sección 18", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____title": "Sección 18", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____lookupDialogTitle": "Seleccionar sección 19", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____title": "Sección 19", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____lookupDialogTitle": "Seleccionar sección 20", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____title": "Sección 20", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimensionBlock____title": "Secciones", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>aja<PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____title": "Trabajador", "@sage/xtrem-finance-data/page-extensions__customer_extension__postingClass____columns__title__type": "Tipo", "@sage/xtrem-finance-data/page-extensions__customer_extension__postingClass____lookupDialogTitle": "Seleccionar clase contable", "@sage/xtrem-finance-data/page-extensions__customer_extension__postingClass____title": "Clase contable", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____lookupDialogTitle": "Seleccionar proyecto", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____title": "Proyecto", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarea", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____title": "Tarea", "@sage/xtrem-finance-data/page-extensions__item_extension____navigationPanel__listItem__postingClass__title": "Clase contable", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____lookupDialogTitle": "Seleccionar sección 01", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____title": "Sección 01", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____lookupDialogTitle": "Seleccionar sección 02", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____title": "Sección 02", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____lookupDialogTitle": "Seleccionar sección 03", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____title": "Sección 03", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____lookupDialogTitle": "Seleccionar sección 04", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____title": "Sección 04", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____lookupDialogTitle": "Seleccionar sección 05", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____title": "Sección 05", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____lookupDialogTitle": "Seleccionar sección 06", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____title": "Sección 06", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____lookupDialogTitle": "Seleccionar sección 07", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____title": "Sección 07", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____lookupDialogTitle": "Seleccionar sección 08", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____title": "Sección 08", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____lookupDialogTitle": "Seleccionar sección 09", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____title": "Sección 09", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____lookupDialogTitle": "Seleccionar sección 10", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____title": "Sección 10", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____lookupDialogTitle": "Seleccionar sección 11", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____title": "Sección 11", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____lookupDialogTitle": "Seleccionar sección 12", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____title": "Sección 12", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____lookupDialogTitle": "Seleccionar sección 13", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____title": "Sección 13", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____lookupDialogTitle": "Seleccionar sección 14", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____title": "Sección 14", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____lookupDialogTitle": "Seleccionar sección 15", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____title": "Sección 15", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____lookupDialogTitle": "Seleccionar sección 16", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____title": "Sección 16", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____lookupDialogTitle": "Seleccionar sección 17", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____title": "Sección 17", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____lookupDialogTitle": "Seleccionar sección 18", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____title": "Sección 18", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____lookupDialogTitle": "Seleccionar sección 19", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____title": "Sección 19", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____lookupDialogTitle": "Seleccionar sección 20", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____title": "Sección 20", "@sage/xtrem-finance-data/page-extensions__item_extension__dimensionBlock____title": "Secciones", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>aja<PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____title": "Trabajador", "@sage/xtrem-finance-data/page-extensions__item_extension__postingClass____columns__title__type": "Tipo", "@sage/xtrem-finance-data/page-extensions__item_extension__postingClass____lookupDialogTitle": "Seleccionar clase contable", "@sage/xtrem-finance-data/page-extensions__item_extension__postingClass____title": "Clase contable", "@sage/xtrem-finance-data/page-extensions__item_extension__project____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__item_extension__project____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__project____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__project____lookupDialogTitle": "Seleccionar proyecto", "@sage/xtrem-finance-data/page-extensions__item_extension__project____title": "Proyecto", "@sage/xtrem-finance-data/page-extensions__item_extension__task____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__item_extension__task____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__item_extension__task____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__item_extension__task____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarea", "@sage/xtrem-finance-data/page-extensions__item_extension__task____title": "Tarea", "@sage/xtrem-finance-data/page-extensions__labor_resource_extension__postingClass____lookupDialogTitle": "Seleccionar clase contable", "@sage/xtrem-finance-data/page-extensions__labor_resource_extension__postingClass____title": "Clase contable", "@sage/xtrem-finance-data/page-extensions__machine_resource_extension__postingClass____lookupDialogTitle": "Seleccionar clase contable", "@sage/xtrem-finance-data/page-extensions__machine_resource_extension__postingClass____title": "Clase contable", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____lookupDialogTitle": "Seleccionar sección 01", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____title": "Sección 01", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____lookupDialogTitle": "Seleccionar sección 02", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____title": "Sección 02", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____lookupDialogTitle": "Seleccionar sección 03", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____title": "Sección 03", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____lookupDialogTitle": "Seleccionar sección 04", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____title": "Sección 04", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____lookupDialogTitle": "Seleccionar sección 05", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____title": "Sección 05", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____lookupDialogTitle": "Seleccionar sección 06", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____title": "Sección 06", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____lookupDialogTitle": "Seleccionar sección 07", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____title": "Sección 07", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____lookupDialogTitle": "Seleccionar sección 08", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____title": "Sección 08", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____lookupDialogTitle": "Seleccionar sección 09", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____title": "Sección 09", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____lookupDialogTitle": "Seleccionar sección 10", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____title": "Sección 10", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____lookupDialogTitle": "Seleccionar sección 11", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____title": "Sección 11", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____lookupDialogTitle": "Seleccionar sección 12", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____title": "Sección 12", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____lookupDialogTitle": "Seleccionar sección 13", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____title": "Sección 13", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____lookupDialogTitle": "Seleccionar sección 14", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____title": "Sección 14", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____lookupDialogTitle": "Seleccionar sección 15", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____title": "Sección 15", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____lookupDialogTitle": "Seleccionar sección 16", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____title": "Sección 16", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____lookupDialogTitle": "Seleccionar sección 17", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____title": "Sección 17", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____lookupDialogTitle": "Seleccionar sección 18", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____title": "Sección 18", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____lookupDialogTitle": "Seleccionar sección 19", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____title": "Sección 19", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____lookupDialogTitle": "Seleccionar sección 20", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____title": "Sección 20", "@sage/xtrem-finance-data/page-extensions__site_extension__dimensionBlock____title": "Secciones", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>aja<PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____title": "Trabajador", "@sage/xtrem-finance-data/page-extensions__site_extension__financialSection____title": "Contabilidad", "@sage/xtrem-finance-data/page-extensions__site_extension__project____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__site_extension__project____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__project____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__project____lookupDialogTitle": "Seleccionar proyecto", "@sage/xtrem-finance-data/page-extensions__site_extension__project____title": "Proyecto", "@sage/xtrem-finance-data/page-extensions__site_extension__task____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__site_extension__task____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__site_extension__task____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__site_extension__task____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarea", "@sage/xtrem-finance-data/page-extensions__site_extension__task____title": "Tarea", "@sage/xtrem-finance-data/page-extensions__supplier_extension____navigationPanel__listItem__datevId__title": "Id. de DATEV", "@sage/xtrem-finance-data/page-extensions__supplier_extension____navigationPanel__listItem__line11__title": "Clase contable", "@sage/xtrem-finance-data/page-extensions__supplier_extension__datevId____title": "Id. de DATEV", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____lookupDialogTitle": "Seleccionar sección 01", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____title": "Sección 01", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____lookupDialogTitle": "Seleccionar sección 02", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____title": "Sección 02", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____lookupDialogTitle": "Seleccionar sección 03", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____title": "Sección 03", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____lookupDialogTitle": "Seleccionar sección 04", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____title": "Sección 04", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____lookupDialogTitle": "Seleccionar sección 05", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____title": "Sección 05", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____lookupDialogTitle": "Seleccionar sección 06", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____title": "Sección 06", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____lookupDialogTitle": "Seleccionar sección 07", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____title": "Sección 07", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____lookupDialogTitle": "Seleccionar sección 08", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____title": "Sección 08", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____lookupDialogTitle": "Seleccionar sección 09", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____title": "Sección 09", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____lookupDialogTitle": "Seleccionar sección 10", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____title": "Sección 10", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____lookupDialogTitle": "Seleccionar sección 11", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____title": "Sección 11", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____lookupDialogTitle": "Seleccionar sección 12", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____title": "Sección 12", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____lookupDialogTitle": "Seleccionar sección 13", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____title": "Sección 13", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____lookupDialogTitle": "Seleccionar sección 14", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____title": "Sección 14", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____lookupDialogTitle": "Seleccionar sección 15", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____title": "Sección 15", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____lookupDialogTitle": "Seleccionar sección 16", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____title": "Sección 16", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____lookupDialogTitle": "Seleccionar sección 17", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____title": "Sección 17", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____lookupDialogTitle": "Seleccionar sección 18", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____title": "Sección 18", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____lookupDialogTitle": "Seleccionar sección 19", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____title": "Sección 19", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____lookupDialogTitle": "Seleccionar sección 20", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____title": "Sección 20", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimensionBlock____title": "Secciones", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>aja<PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____title": "Trabajador", "@sage/xtrem-finance-data/page-extensions__supplier_extension__postingClass____columns__title__type": "Tipo", "@sage/xtrem-finance-data/page-extensions__supplier_extension__postingClass____lookupDialogTitle": "Seleccionar clase contable", "@sage/xtrem-finance-data/page-extensions__supplier_extension__postingClass____title": "Clase contable", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____lookupDialogTitle": "Seleccionar proyecto", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____title": "Proyecto", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____columns__title__id": "Id.", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____columns__title__name": "Nombre", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarea", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____title": "Tarea", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionBlock____title": "Contabilidad", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionCriteriaBlock____title": "Criterios", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionDocumentType____title": "Tipo de documento", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___createStamp": "Envío", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___id": "Id.", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__batchId": "<PERSON><PERSON><PERSON> de tanda", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__documentNumber": "Número de documento", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__documentType": "Tipo de documento", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__message": "Men<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__postingStatus": "Estado", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__status": "Estado de notificación", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__targetDocumentType": "Tipo de documento de destino", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____title": "Resul<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionSection____title": "Contabilidad", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionStatus____title": "Estado", "@sage/xtrem-finance-data/page-extensions__tax_extension____navigationPanel__listItem__line7__title": "Clase contable", "@sage/xtrem-finance-data/page-extensions__tax_extension____navigationPanel__listItem__postingKey__title": "<PERSON><PERSON><PERSON> contable", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingClass____columns__title__type": "Tipo", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingClass____lookupDialogTitle": "Seleccionar clase contable", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingClass____title": "Clase contable", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingKey____title": "<PERSON><PERSON><PERSON> contable", "@sage/xtrem-finance-data/page-extensions__tool_resource_extension__postingClass____lookupDialogTitle": "Seleccionar clase contable", "@sage/xtrem-finance-data/page-extensions__tool_resource_extension__postingClass____title": "Clase contable", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__datevId__title": "Id. de DATEV", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line_4__title": "Asiento directo no permitido", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line_5__title": "Plan de cuentas", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line3__title": "Control", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line4__title": "Asiento directo no permitido", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line5__title": "Plan de cuentas", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line6__title": "Gestión de impuestos", "@sage/xtrem-finance-data/pages__account____navigationPanel__optionsMenu__title": "Plan de cuentas de Francia", "@sage/xtrem-finance-data/pages__account____navigationPanel__optionsMenu__title__2": "Plan de cuentas de EE. UU.", "@sage/xtrem-finance-data/pages__account____objectTypePlural": "Cuentas", "@sage/xtrem-finance-data/pages__account____objectTypeSingular": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__account____title": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__account___id____title": "Id.", "@sage/xtrem-finance-data/pages__account__attributeDimensionTypes____columns__title__type": "Tipo analítico", "@sage/xtrem-finance-data/pages__account__attributeDimensionTypes____title": "Secciones obligatorias", "@sage/xtrem-finance-data/pages__account__attributeDimensionTypesBlock____title": "Secciones obligatorias", "@sage/xtrem-finance-data/pages__account__chartOfAccount____lookupDialogTitle": "Seleccionar plan de cuentas", "@sage/xtrem-finance-data/pages__account__chartOfAccount____title": "Plan de cuentas", "@sage/xtrem-finance-data/pages__account__datevIdString____title": "Id. de DATEV", "@sage/xtrem-finance-data/pages__account__datevSection____title": "DATEV", "@sage/xtrem-finance-data/pages__account__empty_tax_warning": "Si la cuenta está definida como automática, el código de impuesto es obligatorio.", "@sage/xtrem-finance-data/pages__account__headerSection____title": "Sección de cabecera", "@sage/xtrem-finance-data/pages__account__id____title": "Id.", "@sage/xtrem-finance-data/pages__account__isActive____title": "Activa", "@sage/xtrem-finance-data/pages__account__isAutomaticAccount____title": "Cuenta automática", "@sage/xtrem-finance-data/pages__account__isControl____title": "Control", "@sage/xtrem-finance-data/pages__account__isDirectEntryForbidden____title": "Asiento directo no permitido", "@sage/xtrem-finance-data/pages__account__isDirectPostingForbidden____title": "Asiento directo no permitido", "@sage/xtrem-finance-data/pages__account__mainSection____title": "General", "@sage/xtrem-finance-data/pages__account__name____title": "Nombre", "@sage/xtrem-finance-data/pages__account__option_menu____title__all": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__account__save____title": "Guardar", "@sage/xtrem-finance-data/pages__account__tax____columns__lookupDialogTitle__country": "Seleccionar país", "@sage/xtrem-finance-data/pages__account__tax____title": "Impuesto", "@sage/xtrem-finance-data/pages__account__taxManagement____title": "Gestión de impuestos", "@sage/xtrem-finance-data/pages__account__TaxManagementBlock____title": "Gestión de impuestos", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__item__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__line3__title": "Tipo de atributo", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__line3Right__title": "Restringido a nombre", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__restrictedTo__title": "Restringido a id.", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__site__title": "Planta", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__optionsMenu__title": "Proyecto", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__optionsMenu__title__2": "Trabajador", "@sage/xtrem-finance-data/pages__attribute____objectTypePlural": "Atributos", "@sage/xtrem-finance-data/pages__attribute____objectTypeSingular": "Atributo", "@sage/xtrem-finance-data/pages__attribute____subtitle": "Atributo", "@sage/xtrem-finance-data/pages__attribute____title": "Atributo", "@sage/xtrem-finance-data/pages__attribute__attributeRestrictedTo____lookupDialogTitle": "Seleccionar atributo restringido", "@sage/xtrem-finance-data/pages__attribute__attributeType____lookupDialogTitle": "Seleccionar tipo de atributo", "@sage/xtrem-finance-data/pages__attribute__item____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__attribute__mainSection____title": "General", "@sage/xtrem-finance-data/pages__attribute__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__listItem__line_4__title": "Filtro", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__listItem__line3__title": "Vín<PERSON><PERSON> a nodo", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__listItem__line4__title": "Filtro", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__optionsMenu__title__3": "Inactivos", "@sage/xtrem-finance-data/pages__attribute_type____objectTypePlural": "Tipos de atributo", "@sage/xtrem-finance-data/pages__attribute_type____objectTypeSingular": "Tipo de atributo", "@sage/xtrem-finance-data/pages__attribute_type____title": "Tipo de atributo", "@sage/xtrem-finance-data/pages__attribute_type__attributeTypeRestrictedTo____lookupDialogTitle": "Seleccionar tipo de atributo restringido", "@sage/xtrem-finance-data/pages__attribute_type__attributeTypeRestrictedTo____title": "Restringido a", "@sage/xtrem-finance-data/pages__attribute_type__id____title": "Id.", "@sage/xtrem-finance-data/pages__attribute_type__isActive____title": "Activo", "@sage/xtrem-finance-data/pages__attribute_type__linked_to_not_valid": "El tipo de atributo no puede estar vinculado a \"{{linkedTo}}\".", "@sage/xtrem-finance-data/pages__attribute_type__linkedTo____title": "Vinculado a", "@sage/xtrem-finance-data/pages__attribute_type__mainSection____title": "General", "@sage/xtrem-finance-data/pages__attribute_type__name____title": "Nombre", "@sage/xtrem-finance-data/pages__attribute_type__nodeLink____title": "Vín<PERSON><PERSON> a nodo", "@sage/xtrem-finance-data/pages__attribute_type__queryFilter____title": "Filtro", "@sage/xtrem-finance-data/pages__bank_account____navigationPanel__listItem__line_4__title": "Divisa", "@sage/xtrem-finance-data/pages__bank_account____navigationPanel__listItem__line3__title": "Planta financiera", "@sage/xtrem-finance-data/pages__bank_account____navigationPanel__listItem__line4__title": "Divisa", "@sage/xtrem-finance-data/pages__bank_account____objectTypePlural": "Cuentas bancarias", "@sage/xtrem-finance-data/pages__bank_account____objectTypeSingular": "Cuenta bancaria", "@sage/xtrem-finance-data/pages__bank_account____title": "Cuenta bancaria", "@sage/xtrem-finance-data/pages__bank_account__currency____columns__title__id": "Código ISO 4217", "@sage/xtrem-finance-data/pages__bank_account__currency____columns__title__symbol": "Símbolo", "@sage/xtrem-finance-data/pages__bank_account__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-finance-data/pages__bank_account__currency____placeholder": "Seleccionar divisa", "@sage/xtrem-finance-data/pages__bank_account__currency____title": "Divisa", "@sage/xtrem-finance-data/pages__bank_account__financialSite____columns__title__currency__name": "Divisa", "@sage/xtrem-finance-data/pages__bank_account__financialSite____columns__title__id": "Id. ", "@sage/xtrem-finance-data/pages__bank_account__financialSite____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__bank_account__financialSite____lookupDialogTitle": "Seleccionar planta financiera", "@sage/xtrem-finance-data/pages__bank_account__financialSite____title": "Planta financiera", "@sage/xtrem-finance-data/pages__bank_account__headerSection____title": "Sección de cabecera", "@sage/xtrem-finance-data/pages__bank_account__id____title": "Id.", "@sage/xtrem-finance-data/pages__bank_account__isActive____title": "Activa", "@sage/xtrem-finance-data/pages__bank_account__mainSection____title": "Información", "@sage/xtrem-finance-data/pages__bank_account__name____title": "Nombre", "@sage/xtrem-finance-data/pages__bank-account__option_menu____title__all": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__business_entity_customer__datev_id": "Id. de DATEV", "@sage/xtrem-finance-data/pages__business_entity_customer__wrong_datev_id": "Introduce un número entre {{first}} y {{last}}.", "@sage/xtrem-finance-data/pages__business_entity_customer_extension__datev_id_warning": "Si la integración con DATEV está activa, tienes que introducir el id. de DATEV antes de extraer los datos.", "@sage/xtrem-finance-data/pages__company__dimensions_setup_update_confirmation": "Confirmar configuración", "@sage/xtrem-finance-data/pages__company__dimensions_setup_update_confirmation_apply": "Esta configuración se aplicará a todos los documentos pendientes y futuros.", "@sage/xtrem-finance-data/pages__company_extension__duplicate_error": "Hay documentos duplicados. Asigna un documento diferente a cada línea.", "@sage/xtrem-finance-data/pages__company_extension__project_task_warning": "The {{attributeName}} is required when you enter a {{restrictedToName}}.", "@sage/xtrem-finance-data/pages__datev_configuration____title": "Configuración de DATEV", "@sage/xtrem-finance-data/pages__datev_configuration__customer_id_range": "Desde {{start}} hasta {{end}}", "@sage/xtrem-finance-data/pages__datev_configuration__customerIdRange____title": "Rango de ids. de cliente", "@sage/xtrem-finance-data/pages__datev_configuration__customerSupplierLength____title": "Longitud de id. de cliente y proveedor", "@sage/xtrem-finance-data/pages__datev_configuration__isActive____title": "Activa", "@sage/xtrem-finance-data/pages__datev_configuration__mainSection____title": "General", "@sage/xtrem-finance-data/pages__datev_configuration__save____title": "Guardar", "@sage/xtrem-finance-data/pages__datev_configuration__skrCoaString____title": "SKR", "@sage/xtrem-finance-data/pages__datev_configuration__supplier_id_range": "Desde {{start}} hasta {{end}}", "@sage/xtrem-finance-data/pages__datev_configuration__supplierIdRange____title": "<PERSON>ngo de ids. de proveedor", "@sage/xtrem-finance-data/pages__datev_configuration_save_warnings": "Avisos al guardar:", "@sage/xtrem-finance-data/pages__dimension____navigationPanel__listItem__line3__title": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension____objectTypePlural": "Secciones", "@sage/xtrem-finance-data/pages__dimension____objectTypeSingular": "Sección", "@sage/xtrem-finance-data/pages__dimension____title": "Sección", "@sage/xtrem-finance-data/pages__dimension__dimensionType____lookupDialogTitle": "Se<PERSON><PERSON><PERSON>r eje", "@sage/xtrem-finance-data/pages__dimension__id____title": "Id.", "@sage/xtrem-finance-data/pages__dimension__isActive____title": "Activa", "@sage/xtrem-finance-data/pages__dimension__mainSection____title": "General", "@sage/xtrem-finance-data/pages__dimension__name____title": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel____title": "Secciones", "@sage/xtrem-finance-data/pages__dimension_panel__apply____title": "Aplicar a todas las líneas", "@sage/xtrem-finance-data/pages__dimension_panel__applyAll____title": "Aplicar a todo", "@sage/xtrem-finance-data/pages__dimension_panel__applyOnNew____title": "Aplicar solo a líneas nuevas", "@sage/xtrem-finance-data/pages__dimension_panel__applyReleasedItem____title": "Aplicar solo a art<PERSON><PERSON><PERSON>do", "@sage/xtrem-finance-data/pages__dimension_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__defaulted_from_item_plural": "Los valores \"{{itemList}}\" se extraen por defecto del artículo y no se muestran a este nivel. Introdúcelos en la línea de cada artículo.", "@sage/xtrem-finance-data/pages__dimension_panel__defaulted_from_item_singular": "El valor \"{{itemList}}\" se extrae por defecto del artículo y no se muestra a este nivel. Introdúcelo en la línea de cada artículo.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____lookupDialogTitle": "Seleccionar sección 01", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____title": "Sección 01", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____lookupDialogTitle": "Seleccionar sección 02", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____title": "Sección 02", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____lookupDialogTitle": "Seleccionar sección 03", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____title": "Sección 03", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____lookupDialogTitle": "Seleccionar sección 04", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____title": "Sección 04", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____lookupDialogTitle": "Seleccionar sección 05", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____title": "Sección 05", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____lookupDialogTitle": "Seleccionar sección 06", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____title": "Sección 06", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____lookupDialogTitle": "Seleccionar sección 07", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____title": "Sección 07", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____lookupDialogTitle": "Seleccionar sección 08", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____title": "Sección 08", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____lookupDialogTitle": "Seleccionar sección 09", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____title": "Sección 09", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____lookupDialogTitle": "Seleccionar sección 10", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____title": "Sección 10", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____lookupDialogTitle": "Seleccionar sección 11", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____title": "Sección 11", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____lookupDialogTitle": "Seleccionar sección 12", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____title": "Sección 12", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____lookupDialogTitle": "Seleccionar sección 13", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____title": "Sección 13", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____lookupDialogTitle": "Seleccionar sección 14", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____title": "Sección 14", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____lookupDialogTitle": "Seleccionar sección 15", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____title": "Sección 15", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____lookupDialogTitle": "Seleccionar sección 16", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____title": "Sección 16", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____lookupDialogTitle": "Seleccionar sección 17", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____title": "Sección 17", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____lookupDialogTitle": "Seleccionar sección 18", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____title": "Sección 18", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____lookupDialogTitle": "Seleccionar sección 19", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____title": "Sección 19", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__columns__dimensionType__docProperty__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__title__dimensionType__docProperty": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____lookupDialogTitle": "Seleccionar sección 20", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____title": "Sección 20", "@sage/xtrem-finance-data/pages__dimension_panel__employee____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/pages__dimension_panel__employee____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__employee____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__employee____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>aja<PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__employee____title": "Trabajador", "@sage/xtrem-finance-data/pages__dimension_panel__ok____title": "Aceptar", "@sage/xtrem-finance-data/pages__dimension_panel__page_without_dimensions": "La página debe tener atributos y secciones.", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__site__id": "Planta", "@sage/xtrem-finance-data/pages__dimension_panel__project____lookupDialogTitle": "Seleccionar proyecto", "@sage/xtrem-finance-data/pages__dimension_panel__project____title": "Proyecto", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__attributeType__id": "Tipo de atributo", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__id": "Id.", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__item__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__name": "Nombre", "@sage/xtrem-finance-data/pages__dimension_panel__task____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarea", "@sage/xtrem-finance-data/pages__dimension_panel__task____title": "Tarea", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__listItem__titleRight__title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__optionsMenu__title__3": "Inactivos", "@sage/xtrem-finance-data/pages__dimension_type____objectTypePlural": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_type____objectTypeSingular": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_type____title": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_type___id____title": "Id.", "@sage/xtrem-finance-data/pages__dimension_type__docProperty____title": "Propiedad de documento", "@sage/xtrem-finance-data/pages__dimension_type__isActive____title": "Activo", "@sage/xtrem-finance-data/pages__dimension_type__isUsed____title": "En uso", "@sage/xtrem-finance-data/pages__dimension_type__mainSection____title": "General", "@sage/xtrem-finance-data/pages__dimension_type__name____title": "Nombre", "@sage/xtrem-finance-data/pages__journal____navigationPanel__listItem__line6__title": "Número de secuencia", "@sage/xtrem-finance-data/pages__journal____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-finance-data/pages__journal____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-finance-data/pages__journal____objectTypePlural": "Diarios", "@sage/xtrem-finance-data/pages__journal____objectTypeSingular": "Diario", "@sage/xtrem-finance-data/pages__journal____title": "Diario", "@sage/xtrem-finance-data/pages__journal___id____title": "Id.", "@sage/xtrem-finance-data/pages__journal__generalSection____title": "General", "@sage/xtrem-finance-data/pages__journal__id____title": "Id.", "@sage/xtrem-finance-data/pages__journal__isActive____title": "Activo", "@sage/xtrem-finance-data/pages__journal__isSubjectToGlTaxExcludedAmount____title": "Asignada a importe sin impuestos de LM", "@sage/xtrem-finance-data/pages__journal__legislation____lookupDialogTitle": "Seleccionar legislación", "@sage/xtrem-finance-data/pages__journal__legislation____placeholder": "Seleccionar...", "@sage/xtrem-finance-data/pages__journal__name____title": "Nombre", "@sage/xtrem-finance-data/pages__journal__numberingBlock____title": "Numeración", "@sage/xtrem-finance-data/pages__journal__sequence____lookupDialogTitle": "Seleccionar número de secuencia", "@sage/xtrem-finance-data/pages__journal__sequence____title": "Número de secuencia", "@sage/xtrem-finance-data/pages__journal__taxImpact____title": "Impacto fiscal", "@sage/xtrem-finance-data/pages__journal_entry_type____objectTypePlural": "Tipos de asiento", "@sage/xtrem-finance-data/pages__journal_entry_type____objectTypeSingular": "Tipo de asiento", "@sage/xtrem-finance-data/pages__journal_entry_type____title": "Tipo de asiento", "@sage/xtrem-finance-data/pages__journal_entry_type___id____title": "Id.", "@sage/xtrem-finance-data/pages__journal_entry_type__documentType____title": "Tipo de documento", "@sage/xtrem-finance-data/pages__journal_entry_type__generalSection____title": "General", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____columns__title__accountTypeName": "Nombre", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____columns__title__isDetailed": "Detallada", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____columns__title__postingClassType": "Tipo de clase contable", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____lookupDialogTitle": "Seleccionar tipo de cuenta", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____title": "Tipo de cuenta", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAmountType____title": "Tipo de importe", "@sage/xtrem-finance-data/pages__journal_entry_type__headerDescription____title": "Descripción", "@sage/xtrem-finance-data/pages__journal_entry_type__headerJournal____lookupDialogTitle": "Seleccionar diario", "@sage/xtrem-finance-data/pages__journal_entry_type__headerJournal____title": "Diario", "@sage/xtrem-finance-data/pages__journal_entry_type__headerPostingDate____title": "Fecha de contabilización", "@sage/xtrem-finance-data/pages__journal_entry_type__headerSetupBlock____title": "Configuración de cabecera", "@sage/xtrem-finance-data/pages__journal_entry_type__immediatePosting____title": "Contabilización inmediata", "@sage/xtrem-finance-data/pages__journal_entry_type__isActive____title": "Activo", "@sage/xtrem-finance-data/pages__journal_entry_type__legislation____lookupDialogTitle": "Seleccionar legislación", "@sage/xtrem-finance-data/pages__journal_entry_type__legislation____placeholder": "Seleccionar legislación", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__columns__accountType__accountTypeName__title": "Nombre", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__columns__accountType__accountTypeName__title__2": "Tipo de clase contable", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__columns__accountType__accountTypeName__title__3": "Detallada", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__accountType__accountTypeName": "Tipo de cuenta", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__amountType": "Tipo de importe", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__commonReference": "Referencia común", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__contraJournalEntryTypeLine__accountType__accountTypeName": "Tipo de cuenta de contrapartida", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__contraJournalEntryTypeLine__amountType": "Tipo de importe de cuenta de contrapartida", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isLandedCostItemAllowed": "Artículos de gastos de entrega", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isNonStockItemAllowed": "Artículos sin stock", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isServiceItemAllowed": "Artículos de tipo servicio", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isStockItemAllowed": "Artículos de stock", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__movementType": "Tipo de movimiento", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__sign": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____dropdownActions__title": "Clase contable vinculada", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____title": "Configuración de líneas", "@sage/xtrem-finance-data/pages__journal_entry_type__name____title": "Nombre", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____columns__title__account__composedDescription": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____columns__title__postingClass__name": "Nombre de clase contable", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__account__composedDescription": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__account__composedDescription__2": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__postingClass__name": "Nombre de clase contable", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__tax__name": "Impuesto", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____title": "Cuentas de líneas", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____columns__title__account__composedDescription": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____columns__title__postingClass__name": "Nombre de clase contable", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__account__composedDescription": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__account__composedDescription__2": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__postingClass__name": "Nombre de clase contable", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__tax__name": "Impuesto", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____title": "Cuentas de cabecera", "@sage/xtrem-finance-data/pages__journal_entry_type__side_panel_linked_posting_class_title": "Clase contable vinculada", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelAccountTypeName____title": "Nombre de tipo de cuenta", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelGeneralSection____title": "", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelHeaderAccountTypeName____title": "Nombre de tipo de cuenta", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelHeaderBlock____title": "Cabecera", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelHeaderIsDetailed____title": "Detallada", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelIsDetailed____title": "Detallada", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelLinesBlock____title": "Líneas", "@sage/xtrem-finance-data/pages__journal_entry_type__targetDocumentType____title": "Tipo de documento de destino", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__listItem__line3__title": "Detallada", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__2": "Sociedad", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__3": "Cliente", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__4": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__5": "Recurso", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__6": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__7": "Impuesto", "@sage/xtrem-finance-data/pages__posting_class____objectTypePlural": "Clases contables", "@sage/xtrem-finance-data/pages__posting_class____objectTypeSingular": "Clase contable", "@sage/xtrem-finance-data/pages__posting_class____title": "Clase contable", "@sage/xtrem-finance-data/pages__posting_class___id____title": "Id.", "@sage/xtrem-finance-data/pages__posting_class__accountTypes____columns__title__chartOfAccount__legislation__name": "Legislación", "@sage/xtrem-finance-data/pages__posting_class__accountTypes____columns__title__definition__accountTypeName": "Nombre de tipo de cuenta", "@sage/xtrem-finance-data/pages__posting_class__accountTypes____title": "Tipos de cuentas", "@sage/xtrem-finance-data/pages__posting_class__addLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__copyAccounts____title": "<PERSON><PERSON><PERSON> cuentas", "@sage/xtrem-finance-data/pages__posting_class__finance_item_type_not_valid": "El tipo de artículo {{financeItemType}} no es válido.", "@sage/xtrem-finance-data/pages__posting_class__financeItemType____title": "Tipo de artículo", "@sage/xtrem-finance-data/pages__posting_class__generalBlock____title": "Clase contable", "@sage/xtrem-finance-data/pages__posting_class__generalSection____title": "General", "@sage/xtrem-finance-data/pages__posting_class__id____title": "Id.", "@sage/xtrem-finance-data/pages__posting_class__isDetailed____title": "Detallada", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__account__name__title": "Nombre", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__account__name__title__2": "Id.", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__definition__accountTypeName__title": "Nombre", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__definition__accountTypeName__title__2": "Id.", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__definition__accountTypeName__title__3": "Detallada", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__account__name": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__accountId": "<PERSON><PERSON><PERSON> de cuenta", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__chartOfAccount__name": "Plan de cuentas", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__definition__accountTypeName": "Nombre de tipo de cuenta", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isLandedCostItemAllowed": "Artículos de gastos de entrega", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isNonStockItemAllowed": "Artículos sin stock", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isServiceItemAllowed": "Artículos de tipo servicio", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isStockItemAllowed": "Artículos de stock", "@sage/xtrem-finance-data/pages__posting_class__lines____dropdownActions__title": "Eliminar", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title": "Nombre", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__2": "Id.", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__3": "Nombre", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__4": "Id.", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__5": "Plan de cuentas", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__definition__accountTypeName__title": "Nombre", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__definition__accountTypeName__title__2": "Id.", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__tax__name__title": "Nombre", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__tax__name__title__2": "Id.", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__tax__name__title__3": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__id": "<PERSON><PERSON><PERSON> de cuenta", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__id__2": "<PERSON><PERSON><PERSON> de cuenta", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__name": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__name__2": "C<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__chartOfAccount__name": "Plan de cuentas", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__definition__accountTypeName": "Nombre de tipo de cuenta", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__definition__additionalCriteria": "Criterios adicionales", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isLandedCostItemAllowed": "Artículos de gastos de entrega", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isNonStockItemAllowed": "Artículos sin stock", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isServiceItemAllowed": "Artículos de servicio", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isStockItemAllowed": "Artículos de stock", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__tax__name": "Impuesto", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__tax__taxCategory__name": "Categoría de impuesto", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__dropdownActions__title__2": "Eliminar", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__dropdownActions__title__3": "Eliminar", "@sage/xtrem-finance-data/pages__posting_class__lines____title": "Líneas", "@sage/xtrem-finance-data/pages__posting_class__loadLinesBlock____title": "Definiciones de clases contables", "@sage/xtrem-finance-data/pages__posting_class__loadLinesSection____title": "Cargar lín<PERSON>", "@sage/xtrem-finance-data/pages__posting_class__name____title": "Nombre", "@sage/xtrem-finance-data/pages__posting_class__postingClass____columns__title__type": "Tipo", "@sage/xtrem-finance-data/pages__posting_class__postingClass____lookupDialogTitle": "Seleccionar clase contable", "@sage/xtrem-finance-data/pages__posting_class__postingClass____placeholder": "Seleccionar clase contable", "@sage/xtrem-finance-data/pages__posting_class__postingClass____title": "Plantilla de clase contable", "@sage/xtrem-finance-data/pages__posting_class__selectAll____title": "Seleccionar todas las líneas", "@sage/xtrem-finance-data/pages__posting_class__selectLines____title": "Seleccionar l<PERSON>", "@sage/xtrem-finance-data/pages__posting_class__type____title": "Tipo", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__listItem__line_4__title": "Detallada", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__listItem__line4__title": "Detallada", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__listItem__titleRight__title": "Legislación", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__2": "Cliente", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__4": "Recurso", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__5": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__6": "Impuesto", "@sage/xtrem-finance-data/pages__posting_class_definition____objectTypePlural": "Definiciones de clases contables", "@sage/xtrem-finance-data/pages__posting_class_definition____objectTypeSingular": "Definición de clase contable", "@sage/xtrem-finance-data/pages__posting_class_definition____title": "Definición de clase contable", "@sage/xtrem-finance-data/pages__posting_class_definition__accountTypeName____title": "Nombre de tipo de cuenta", "@sage/xtrem-finance-data/pages__posting_class_definition__additionalCriteria____title": "Criterios adicionales", "@sage/xtrem-finance-data/pages__posting_class_definition__financeItemType____title": "Tipo de artículo", "@sage/xtrem-finance-data/pages__posting_class_definition__generalBlock____title": "Definición de clase contable", "@sage/xtrem-finance-data/pages__posting_class_definition__generalSection____title": "General", "@sage/xtrem-finance-data/pages__posting_class_definition__id____title": "Id.", "@sage/xtrem-finance-data/pages__posting_class_definition__isDetailed____title": "Detallada", "@sage/xtrem-finance-data/pages__posting_class_definition__legislation____lookupDialogTitle": "Seleccionar legislación", "@sage/xtrem-finance-data/pages__posting_class_definition__legislation____placeholder": "Seleccionar...", "@sage/xtrem-finance-data/pages__posting_class_definition__postingClassType____title": "Tipo de clase contable", "@sage/xtrem-finance-data/pages__posting_class_duplicate__not_allowed": "Ha habido un error en la operación por una restricción de integridad de la base de datos.", "@sage/xtrem-finance-data/pages__posting-class__load_lines": "Cargar lín<PERSON>", "@sage/xtrem-finance-data/pages__posting-class__no_new_lines": "No hay líneas que añadir.", "@sage/xtrem-finance-data/pages__posting-class_line_deletion": "", "@sage/xtrem-finance-data/pages__posting-class_line_deletion_title": "Confirmar elimina<PERSON> de línea", "@sage/xtrem-finance-data/pages__posting-class-template__info": "Solo se seleccionan las líneas con los mismos tipos de artículo.", "@sage/xtrem-finance-data/pages__posting-class-template__info_title": "Clase contable", "@sage/xtrem-finance-data/pages_company_confirmation": "Aplicar", "@sage/xtrem-finance-data/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/permission__delete__name": "Eliminar", "@sage/xtrem-finance-data/permission__manage__name": "Gestionar", "@sage/xtrem-finance-data/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-finance-data/permission__update__name": "Actualizar", "@sage/xtrem-finance-data/service_options__payment_tracking_option__intacct_is_active": "Si la integración con Sage Intacct está activa, el seguimiento de pagos no se puede activar.", "@sage/xtrem-finance-data/service_options__payment_tracking_option__name": "Opción de seguimiento de pagos", "@sage/xtrem-finance-data/sys__notification_history__search": "Buscar", "@sage/xtrem-finance-data/update-account-tax-management-context": "¿Quieres definir la gestión de impuestos de la cuenta en \"Impuestos\"?", "@sage/xtrem-finance-data/update-account-tax-management-title": "Confirmar actualiza<PERSON>"}