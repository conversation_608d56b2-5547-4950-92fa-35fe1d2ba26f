{"@sage/xtrem-finance-data/activity__account__name": "Ko<PERSON>", "@sage/xtrem-finance-data/activity__attribute__name": "Attribut", "@sage/xtrem-finance-data/activity__attribute_type__name": "Attributtyp", "@sage/xtrem-finance-data/activity__bank_account__name": "Bankkonto", "@sage/xtrem-finance-data/activity__company_default_attribute__name": "Standardattribut Unternehmen", "@sage/xtrem-finance-data/activity__company_default_dimension__name": "Standardsektor Unternehmen", "@sage/xtrem-finance-data/activity__datev_configuration__name": "DATEV-Konfiguration", "@sage/xtrem-finance-data/activity__dimension__name": "Se<PERSON><PERSON>", "@sage/xtrem-finance-data/activity__dimension_definition_level_and_default__name": "Definitionsebene und Standard Sektor", "@sage/xtrem-finance-data/activity__dimension_type__name": "Dimension", "@sage/xtrem-finance-data/activity__journal__name": "Journal", "@sage/xtrem-finance-data/activity__journal_entry_type__name": "Buchungstyp", "@sage/xtrem-finance-data/activity__posting_class__name": "Buchungsklasse", "@sage/xtrem-finance-data/activity__posting_class_definition__name": "Definition Buchungsklasse", "@sage/xtrem-finance-data/attribute-type-deactivation-effective-dialog-title": "<PERSON><PERSON>hlen Sie 'Speichern', um Ihre Änderung zu übernehmen.", "@sage/xtrem-finance-data/base-document-item-line/set-dimension-missing-line": "Zeile für Sektor-Aktualisierung fehlt.", "@sage/xtrem-finance-data/base-document-item-line/update-dimension-posted": "Sie können einen Sektor in einem abgeschlossenen Dokument nicht aktualisieren.", "@sage/xtrem-finance-data/cancel": "Abbrechen", "@sage/xtrem-finance-data/cannot_set_tax_with_account_not_subjected_to_taxes": "Sie können ein Steuerdetail nicht mit einem Konto setzen, das nicht Steuern unterliegt.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_account": "Das Konto {{account}} wurde nicht gefunden.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_address": "Die Adresse {{address}} wurde nicht gefunden.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_base_document_line": "Die Basisdokumentzeile {{baseDocumentLine}} wurde nicht gefunden.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_base_tax": "Die Steuerzeile {{baseTax}} wurde nicht gefunden.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_customer": "Der Kunde {{customer}} wurde nicht gefunden.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_financial_site": "Der Standort {{financialSite}} wurde nicht gefunden.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_item": "Der Artikel {{item}} wurde nicht gefunden.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_payment_term": "Die Zahlungsbedingung {{paymentTerm}} wurde nicht gefunden.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_posting_class_ap_ar_invoice": "Die Buchungsklasse für das Dokument {{documentType}} {{documentNumber}} und den Artikel {{itemId}} wurde nicht gefunden. Der Buchungsklassentyp ist: {{postingClassType}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_posting_class_journal_entry": "Die Buchungsklasse für das Dokument {{documentType}} {{documentNumber}} wurde nicht gefunden. Der Buchungsklassentyp ist: {{postingClassType}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_resource": "Die Ressource {{resource}} wurde nicht gefunden.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_stock_journal": "Das Bestandsjournal {{stockJournal}} wurde nicht gefunden.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_supplier": "Der Lieferant {{supplier}} wurde nicht gefunden.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_tax": "Die Steuer {{tax}} wurde nicht gefunden.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_transaction_currency": "Die Währung {{transactionCurrency}} wurde nicht gefunden.", "@sage/xtrem-finance-data/classes__localized-messages__item_posting_class_missing_on_item": "Erfassen Sie eine Buchungsklasse für den Artikel {{item}}.", "@sage/xtrem-finance-data/classes__localized-messages__no_bp_account": "{{documentType}} {{documentNumber}}: Das Konto für die Geschäftsentität {{businessPartner}} wurde nicht gefunden.", "@sage/xtrem-finance-data/classes__localized-messages__no_finance_document_lines_generated_for_item": "Das Konto konnte nicht für den Artikel {{item}}, den Buchungstyp {{journalEntryType}} und den Bewegungstyp {{movementType}} ermittelt werden.", "@sage/xtrem-finance-data/classes__localized-messages__resource_posting_class_missing_on_resource": "Erfassen Sie eine Buchungsklasse für die Ressource {{resource}}.", "@sage/xtrem-finance-data/classes__localized-messages__tax_posting_class_missing_on_tax": "Erfassen Sie eine Buchungsklasse für die Steuer {{tax}}.", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_attribute_type": "<PERSON><PERSON> mü<PERSON> {{attribute}} in Zeile {{line}} auswählen.", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type": "<PERSON><PERSON> müssen den Sektor der Ebene {{level}} [{{dimension}}] auswählen.", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type_item_tax_amount_false": "<PERSON><PERSON> müssen den Sektor der Ebene {{level}} [{{dimension}}] für den Artikel {{item}} im Dokument {{sourceDocumentNumber}} auswählen.", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type_item_tax_amount_True": "<PERSON>e müssen den Sektor der Ebene {{level}} [{{dimension}}] für den Artikel {{item}} im Dokument {{sourceDocumentNumber}} (Steuerdetails) auswählen.", "@sage/xtrem-finance-data/confirm-dialog-content": "<PERSON>e sind dabei, diese Buchungseinstellungen auf alle zukünftigen Transaktionen anzuwenden.", "@sage/xtrem-finance-data/confirm-dialog-title": "Einstellungen bestätigen", "@sage/xtrem-finance-data/confirm-update": "Aktualisierung bestätigen", "@sage/xtrem-finance-data/data_types__accounts_payable_receivable_invoice_document_line_type_enum__name": "Enum Typ Dokumentzeile Eingangs-/Ausgangsrechung", "@sage/xtrem-finance-data/data_types__accounts_payable_receivable_invoice_line_type_enum__name": "Enum Typ Zeile Eingangs-/Ausgangsrechnung", "@sage/xtrem-finance-data/data_types__accounts_payable_receivable_invoice_origin_enum__name": "Enum Ursprung Eingangs-/Ausgangsrechnung", "@sage/xtrem-finance-data/data_types__amount_type_enum__name": "Enum <PERSON>", "@sage/xtrem-finance-data/data_types__analytical_measure_type_enum__name": "Enum Typ Maßnahme Kostenrechnung", "@sage/xtrem-finance-data/data_types__attribute__name": "Attribut", "@sage/xtrem-finance-data/data_types__attribute_dimension_type_level_enum__name": "Enum Ebene Dimension Attribut", "@sage/xtrem-finance-data/data_types__attribute_type__name": "Attributtyp", "@sage/xtrem-finance-data/data_types__bank_account__name": "Bankkonto", "@sage/xtrem-finance-data/data_types__bank_account_type_enum__name": "<PERSON><PERSON>", "@sage/xtrem-finance-data/data_types__close_reason__name": "Grund für Abschluss", "@sage/xtrem-finance-data/data_types__common_reference_enum__name": "Enum Allgemeine Referenz", "@sage/xtrem-finance-data/data_types__dimension__name": "Se<PERSON><PERSON>", "@sage/xtrem-finance-data/data_types__dimension_definition_level_enum__name": "Enum Definitionsebene Sektor", "@sage/xtrem-finance-data/data_types__dimension_type__name": "Dimension", "@sage/xtrem-finance-data/data_types__doc_property_enum__name": "Enum Dokumenteigenschaft", "@sage/xtrem-finance-data/data_types__finance_document_type_enum__name": "Enum Typ Finanzdokument", "@sage/xtrem-finance-data/data_types__finance_integration_app_enum__name": "Enum Anwendung Integration Finanzen", "@sage/xtrem-finance-data/data_types__finance_integration_status_enum__name": "Enum Status Integration Finanzen", "@sage/xtrem-finance-data/data_types__finance_item_type_enum__name": "Enum Typ Finanzartikel", "@sage/xtrem-finance-data/data_types__header_description_enum__name": "Enum Bezeichnung Kopfzeile", "@sage/xtrem-finance-data/data_types__header_posting_date_enum__name": "Enum Buchungsdatum Kopfzeile", "@sage/xtrem-finance-data/data_types__item_stock_management_criteria_enum__name": "Item stock management criteria enum", "@sage/xtrem-finance-data/data_types__journal_origin_enum__name": "Enum Ursprung Journal", "@sage/xtrem-finance-data/data_types__journal_status_enum__name": "Enum Status Journal", "@sage/xtrem-finance-data/data_types__master_data_default_enum__name": "Enum Standard Stammdaten", "@sage/xtrem-finance-data/data_types__movement_type_enum__name": "Enum Bewewgungstyp", "@sage/xtrem-finance-data/data_types__node_link_enum__name": "Enum Node-Verknüpfung", "@sage/xtrem-finance-data/data_types__open_item_status_enum__name": "Enum Status offener Posten", "@sage/xtrem-finance-data/data_types__posting_class__name": "Buchungsklasse", "@sage/xtrem-finance-data/data_types__posting_class_type_enum__name": "Enum Typ Buchungsklasse", "@sage/xtrem-finance-data/data_types__posting_status_enum__name": "Enum Buchungsstatus", "@sage/xtrem-finance-data/data_types__sign_enum__name": "Enum Vorzeichen", "@sage/xtrem-finance-data/data_types__source_document_type_enum__name": "Enum Typ Ursprungsdokument", "@sage/xtrem-finance-data/data_types__stored_attributes_data_type__name": "Datentyp gespeicherte Attribute", "@sage/xtrem-finance-data/data_types__stored_dimensions_data_type__name": "Datentyp gespeicherte Sektoren", "@sage/xtrem-finance-data/data_types__target_document_type_enum__name": "Enum <PERSON>", "@sage/xtrem-finance-data/data_types__tax_engine_enum__name": "<PERSON><PERSON>", "@sage/xtrem-finance-data/data_types__tax_management_enum__name": "En<PERSON> Steuerverwaltung", "@sage/xtrem-finance-data/data_types__validation_severity_data_type__name": "Datentyp Dringlichkeit Validierung", "@sage/xtrem-finance-data/deactivation-dialog-content": "<PERSON><PERSON> sind dabei, dieses Attribut hier und in allen Dokumenten, in denen es verwendet wird, zu deaktivieren.", "@sage/xtrem-finance-data/deactivation-dialog-title": "Deaktivierung bestätigen", "@sage/xtrem-finance-data/dimension-deactivation-dialog-content": "Sie sind dabei, diese Dimension hier und in allen Dokumenten, in denen sie verwendet wird, zu deaktivieren.", "@sage/xtrem-finance-data/dimension-deactivation-dialog-title": "Deaktivierung bestätigen", "@sage/xtrem-finance-data/dimension-type-deactivation-effective-dialog-title": "<PERSON><PERSON>hlen Sie 'Speichern', um Ihre Änderung zu übernehmen.", "@sage/xtrem-finance-data/dimension-type-name-change-dialog-content": "Sie sind dabei, diese Dimension hier und in allen Dokumenten, in denen sie verwendet wird, umzubenennen.", "@sage/xtrem-finance-data/dimension-type-name-change-dialog-title": "Umbenennen bestätigen", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_document_line_type__documentLine": "Dokumentzeile", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_document_line_type__taxLine": "Steuerzeile", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_line_type__fixedAssets": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_line_type__goods": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_line_type__services": "Services", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_origin__creditMemo": "Gutschrift", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_origin__direct": "Direkt", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_origin__invoice": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__amount_type__adjustmentAmount": "Korrekturbetrag", "@sage/xtrem-finance-data/enums__amount_type__adjustmentNonabsorbedAmount": "Korrektur nicht absorbierter Betrag", "@sage/xtrem-finance-data/enums__amount_type__amount": "Betrag", "@sage/xtrem-finance-data/enums__amount_type__amountExcludingTax": "Betrag exkl. Steuern", "@sage/xtrem-finance-data/enums__amount_type__amountIncludingTax": "Betrag inkl. Steuern", "@sage/xtrem-finance-data/enums__amount_type__deductibleTaxAmount": "Abzugsfähiger Steuerbetrag", "@sage/xtrem-finance-data/enums__amount_type__inTransitAmount": "Betrag in Transit", "@sage/xtrem-finance-data/enums__amount_type__inTransitVarianceAmount": "Abweichungsbetrag in Transit", "@sage/xtrem-finance-data/enums__amount_type__landedCostAdjustmentAmount": "Betrag Korrektur Einstandskosten", "@sage/xtrem-finance-data/enums__amount_type__landedCostAdjustmentNonabsorbedAmount": "Nicht absorbierter Betrag Korrektur Einstandskosten", "@sage/xtrem-finance-data/enums__amount_type__landedCostStockInTransitAdjustmentAmount": "Betrag Korrektur Einstandskosten Bestand in Transit", "@sage/xtrem-finance-data/enums__amount_type__landedCostStockInTransitAdjustmentNonabsorbedAmount": "Nicht absorbierter Betrag Korrektur Einstandskosten Bestand in Transit", "@sage/xtrem-finance-data/enums__amount_type__landedCostStockInTransitAmount": "Betrag Einstandskosten Bestand in Transit", "@sage/xtrem-finance-data/enums__amount_type__nonDeductibleTaxAmount": "Nicht abzugsfähiger Steuerbetrag", "@sage/xtrem-finance-data/enums__amount_type__reverseChargeDeductibleTaxAmount": "Abzugsfähiger Steuerbetrag Reverse Charge", "@sage/xtrem-finance-data/enums__amount_type__reverseChargeNonDeductibleTaxAmount": "Nicht abzugsfähiger Steuerbetrag Reverse Charge", "@sage/xtrem-finance-data/enums__amount_type__taxAmount": "Steuerbetrag", "@sage/xtrem-finance-data/enums__amount_type__varianceAmount": "Abweichungsbetrag", "@sage/xtrem-finance-data/enums__analytical_measure_type__attribute": "Attribut", "@sage/xtrem-finance-data/enums__analytical_measure_type__dimension": "Se<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__attribute_dimension_type_level__account": "Ko<PERSON>", "@sage/xtrem-finance-data/enums__attribute_dimension_type_level__company": "Unternehmen", "@sage/xtrem-finance-data/enums__bank_account_type__current": "Aktuell", "@sage/xtrem-finance-data/enums__common_reference__documentNumber": "Dokumentnummer", "@sage/xtrem-finance-data/enums__common_reference__sourceDocumentNumber": "<PERSON><PERSON>mer Ursprungsdokument", "@sage/xtrem-finance-data/enums__dimension_definition_level__intersiteTransferOrder": "Intersite-Transferauftrag", "@sage/xtrem-finance-data/enums__dimension_definition_level__manufacturingDirect": "Fertigung direkt", "@sage/xtrem-finance-data/enums__dimension_definition_level__manufacturingOrderToOrder": "Fertigung Auftrag-zu-Auftrag", "@sage/xtrem-finance-data/enums__dimension_definition_level__purchasingDirect": "Einkauf direkt", "@sage/xtrem-finance-data/enums__dimension_definition_level__purchasingOrderToOrder": "Einkauf Auftrag-zu-Auftrag", "@sage/xtrem-finance-data/enums__dimension_definition_level__salesDirect": "Verkauf direkt", "@sage/xtrem-finance-data/enums__dimension_definition_level__stockDirect": "Bestand direkt", "@sage/xtrem-finance-data/enums__doc_property__dimension01": "Sektor 01", "@sage/xtrem-finance-data/enums__doc_property__dimension02": "Sektor 02", "@sage/xtrem-finance-data/enums__doc_property__dimension03": "Sektor 03", "@sage/xtrem-finance-data/enums__doc_property__dimension04": "Sektor 04", "@sage/xtrem-finance-data/enums__doc_property__dimension05": "Sektor 05", "@sage/xtrem-finance-data/enums__doc_property__dimension06": "Sektor 06", "@sage/xtrem-finance-data/enums__doc_property__dimension07": "Sektor 07", "@sage/xtrem-finance-data/enums__doc_property__dimension08": "Sektor 08", "@sage/xtrem-finance-data/enums__doc_property__dimension09": "Sektor 09", "@sage/xtrem-finance-data/enums__doc_property__dimension10": "Sektor 10", "@sage/xtrem-finance-data/enums__doc_property__dimension11": "Sektor 11", "@sage/xtrem-finance-data/enums__doc_property__dimension12": "Sektor 12", "@sage/xtrem-finance-data/enums__doc_property__dimension13": "Sektor 13", "@sage/xtrem-finance-data/enums__doc_property__dimension14": "Sektor 14", "@sage/xtrem-finance-data/enums__doc_property__dimension15": "Sektor 15", "@sage/xtrem-finance-data/enums__doc_property__dimension16": "Sektor 16", "@sage/xtrem-finance-data/enums__doc_property__dimension17": "Sektor 17", "@sage/xtrem-finance-data/enums__doc_property__dimension18": "Sektor 18", "@sage/xtrem-finance-data/enums__doc_property__dimension19": "Sektor 19", "@sage/xtrem-finance-data/enums__doc_property__dimension20": "Sektor 20", "@sage/xtrem-finance-data/enums__doc_property__dimensionType01": "Dimension 01", "@sage/xtrem-finance-data/enums__doc_property__dimensionType02": "Dimension 02", "@sage/xtrem-finance-data/enums__doc_property__dimensionType03": "Dimension 03", "@sage/xtrem-finance-data/enums__doc_property__dimensionType04": "Dimension 04", "@sage/xtrem-finance-data/enums__doc_property__dimensionType05": "Dimension 05", "@sage/xtrem-finance-data/enums__doc_property__dimensionType06": "Dimension 06", "@sage/xtrem-finance-data/enums__doc_property__dimensionType07": "Dimension 07", "@sage/xtrem-finance-data/enums__doc_property__dimensionType08": "Dimension 08", "@sage/xtrem-finance-data/enums__doc_property__dimensionType09": "Dimension 09", "@sage/xtrem-finance-data/enums__doc_property__dimensionType10": "Dimension 10", "@sage/xtrem-finance-data/enums__doc_property__dimensionType11": "Dimension 11", "@sage/xtrem-finance-data/enums__doc_property__dimensionType12": "Dimension 12", "@sage/xtrem-finance-data/enums__doc_property__dimensionType13": "Dimension 13", "@sage/xtrem-finance-data/enums__doc_property__dimensionType14": "Dimension 14", "@sage/xtrem-finance-data/enums__doc_property__dimensionType15": "Dimension 15", "@sage/xtrem-finance-data/enums__doc_property__dimensionType16": "Dimension 16", "@sage/xtrem-finance-data/enums__doc_property__dimensionType17": "Dimension 17", "@sage/xtrem-finance-data/enums__doc_property__dimensionType18": "Dimension 18", "@sage/xtrem-finance-data/enums__doc_property__dimensionType19": "Dimension 19", "@sage/xtrem-finance-data/enums__doc_property__dimensionType20": "Dimension 20", "@sage/xtrem-finance-data/enums__finance_document_type__apInvoice": "Eingangsrechnung", "@sage/xtrem-finance-data/enums__finance_document_type__arInvoice": "Ausgangsrechnung", "@sage/xtrem-finance-data/enums__finance_document_type__bankReconciliationDeposit": "Bankabstimmung Einzahlungen", "@sage/xtrem-finance-data/enums__finance_document_type__bankReconciliationWithdrawal": "Bankabstimmung Auszahlungen", "@sage/xtrem-finance-data/enums__finance_document_type__miscellaneousStockIssue": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__finance_document_type__miscellaneousStockReceipt": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseCreditMemo": "Einkaufsgutschrift", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseInvoice": "Einkaufsrechnung", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseReceipt": "Wareneingang", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseReturn": "Einkaufsretoure", "@sage/xtrem-finance-data/enums__finance_document_type__salesCreditMemo": "Verkaufsgutschrift", "@sage/xtrem-finance-data/enums__finance_document_type__salesInvoice": "Verkaufsrechnung", "@sage/xtrem-finance-data/enums__finance_document_type__salesReturnReceipt": "Verkaufsretoureneingang", "@sage/xtrem-finance-data/enums__finance_document_type__salesShipment": "Warenausgang", "@sage/xtrem-finance-data/enums__finance_document_type__stockAdjustment": "Bestandskorrektur", "@sage/xtrem-finance-data/enums__finance_document_type__stockCount": "Inventur", "@sage/xtrem-finance-data/enums__finance_document_type__stockTransferReceipt": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__finance_document_type__stockTransferShipment": "<PERSON><PERSON><PERSON>fer", "@sage/xtrem-finance-data/enums__finance_document_type__stockValueChange": "Änderung Bestandswert", "@sage/xtrem-finance-data/enums__finance_document_type__workInProgress": "Work-In-Progress", "@sage/xtrem-finance-data/enums__finance_integration_app__frp1000": "Sage FRP 1000", "@sage/xtrem-finance-data/enums__finance_integration_app__intacct": "Sage Intacct", "@sage/xtrem-finance-data/enums__finance_integration_status__error": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__finance_integration_status__failed": "Fehlgeschlagen", "@sage/xtrem-finance-data/enums__finance_integration_status__notRecorded": "Nicht erfasst", "@sage/xtrem-finance-data/enums__finance_integration_status__pending": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__finance_integration_status__posted": "Gebucht", "@sage/xtrem-finance-data/enums__finance_integration_status__recorded": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__finance_integration_status__recording": "Wird erfasst", "@sage/xtrem-finance-data/enums__finance_integration_status__submitted": "Übermittelt", "@sage/xtrem-finance-data/enums__finance_integration_status__toBeRecorded": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__finance_item_type__landedCostItem": "Einstandskostenartikel", "@sage/xtrem-finance-data/enums__finance_item_type__nonStockItem": "Nicht bestandsgeführter Artikel", "@sage/xtrem-finance-data/enums__finance_item_type__serviceItem": "Dienstleistungsartikel", "@sage/xtrem-finance-data/enums__finance_item_type__stockItem": "Bestandsgeführter Artikel", "@sage/xtrem-finance-data/enums__header_description__documentNumber": "Dokumentnummer", "@sage/xtrem-finance-data/enums__header_description__documentType": "Dokumenttyp", "@sage/xtrem-finance-data/enums__header_description__transactionDescription": "Transaktionsbezeichnung", "@sage/xtrem-finance-data/enums__header_posting_date__documentDate": "Dokumentdatum", "@sage/xtrem-finance-data/enums__header_posting_date__endOfMonth": "<PERSON><PERSON>end<PERSON>", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__both": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__managedInStock": "Bestandsgeführt", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__notApplicable": "<PERSON>cht zu<PERSON>nd", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__notManagedInStock": "Nicht bestandsgeführt", "@sage/xtrem-finance-data/enums__journal_origin__apInvoice": "Eingangsrechnung", "@sage/xtrem-finance-data/enums__journal_origin__arInvoice": "Ausgangsrechnung", "@sage/xtrem-finance-data/enums__journal_origin__directEntry": "Direkte Buchung", "@sage/xtrem-finance-data/enums__journal_origin__manufacturing": "Fertigung", "@sage/xtrem-finance-data/enums__journal_origin__purchase": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__journal_origin__sales": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__journal_origin__stock": "Bestand", "@sage/xtrem-finance-data/enums__journal_status__draft": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__journal_status__error": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__journal_status__inProgress": "In Bearbeitung", "@sage/xtrem-finance-data/enums__journal_status__posted": "Gebucht", "@sage/xtrem-finance-data/enums__master_data_default__customer": "Kunde", "@sage/xtrem-finance-data/enums__master_data_default__item": "Artikel", "@sage/xtrem-finance-data/enums__master_data_default__receivingSite": "Eingangsstandort", "@sage/xtrem-finance-data/enums__master_data_default__shippingSite": "Versandstandort", "@sage/xtrem-finance-data/enums__master_data_default__site": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__master_data_default__sourceDocument": "Ursprungsdokument", "@sage/xtrem-finance-data/enums__master_data_default__supplier": "Lieferant", "@sage/xtrem-finance-data/enums__movement_type__document": "Dokument", "@sage/xtrem-finance-data/enums__movement_type__laborRunTimeTracking": "Rückmeldung Bearbeitungszeit Arbeitskraft", "@sage/xtrem-finance-data/enums__movement_type__laborSetupTimeTracking": "Rückmeldung Rüstzeit Arbeitskraft", "@sage/xtrem-finance-data/enums__movement_type__machineRunTimeTracking": "Rückmeldung Bearbeitungszeit Maschine", "@sage/xtrem-finance-data/enums__movement_type__machineSetupTimeTracking": "Rückmeldung Rüstzeit Maschine", "@sage/xtrem-finance-data/enums__movement_type__materialTracking": "Materialrückmeldung", "@sage/xtrem-finance-data/enums__movement_type__productionTracking": "Rückmeldung Fertigung", "@sage/xtrem-finance-data/enums__movement_type__stockJournal": "Bestandsjournal", "@sage/xtrem-finance-data/enums__movement_type__toolRunTimeTracking": "Rückmeldung Bearbeitungszeit Werkzeug", "@sage/xtrem-finance-data/enums__movement_type__toolSetupTimeTracking": "Rückmeldung Rüstzeit Werkzeug", "@sage/xtrem-finance-data/enums__movement_type__workOrderActualCostAdjustment": "Korrektur Ist-Kosten Fertigungsauftrag", "@sage/xtrem-finance-data/enums__movement_type__workOrderActualCostAdjustmentNonAbsorbed": "Korrektur Ist-Kosten Fertigungsauftrag nicht absorbiert", "@sage/xtrem-finance-data/enums__movement_type__workOrderNegativeActualCostAdjustment": "Korrektur negative Ist-Kosten Fertigungsauftrag", "@sage/xtrem-finance-data/enums__movement_type__workOrderNegativeActualCostAdjustmentNonAbsorbed": "Korrektur negative Ist-Kosten Fertigungsauftrag nicht absorbiert", "@sage/xtrem-finance-data/enums__movement_type__workOrderNegativeVariance": "Negative Abweichung Fertigungsauftrag", "@sage/xtrem-finance-data/enums__movement_type__workOrderVariance": "Abweichung Fertigungsauftrag", "@sage/xtrem-finance-data/enums__node_link__attribute": "Attribut", "@sage/xtrem-finance-data/enums__node_link__customer": "Kunde", "@sage/xtrem-finance-data/enums__node_link__item": "Artikel", "@sage/xtrem-finance-data/enums__node_link__site": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__node_link__supplier": "Lieferant", "@sage/xtrem-finance-data/enums__open_item_status__notPaid": "<PERSON><PERSON> bezahlt", "@sage/xtrem-finance-data/enums__open_item_status__paid": "Be<PERSON>hlt", "@sage/xtrem-finance-data/enums__open_item_status__partiallyPaid": "Teilweise bezahlt", "@sage/xtrem-finance-data/enums__posting_class_type__company": "Unternehmen", "@sage/xtrem-finance-data/enums__posting_class_type__customer": "Kunde", "@sage/xtrem-finance-data/enums__posting_class_type__header": "Kopfzeile", "@sage/xtrem-finance-data/enums__posting_class_type__item": "Artikel", "@sage/xtrem-finance-data/enums__posting_class_type__line": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__posting_class_type__resource": "Ressource", "@sage/xtrem-finance-data/enums__posting_class_type__supplier": "Lieferant", "@sage/xtrem-finance-data/enums__posting_class_type__tax": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__posting_status__generated": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__posting_status__generationError": "Generierungsfehler", "@sage/xtrem-finance-data/enums__posting_status__generationInProgress": "Generierung in Bearbeitung", "@sage/xtrem-finance-data/enums__posting_status__notPosted": "<PERSON>cht geb<PERSON>t", "@sage/xtrem-finance-data/enums__posting_status__posted": "Gebucht", "@sage/xtrem-finance-data/enums__posting_status__postingError": "Buchungsfehler", "@sage/xtrem-finance-data/enums__posting_status__postingInProgress": "Buchung in Bearbeitung", "@sage/xtrem-finance-data/enums__posting_status__toBeGenerated": "<PERSON><PERSON> gene<PERSON>", "@sage/xtrem-finance-data/enums__sign__C": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__sign__D": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__source_document_type__materialTracking": "Materialrückmeldung", "@sage/xtrem-finance-data/enums__source_document_type__operationTracking": "Arbeitsgangrückmeldung", "@sage/xtrem-finance-data/enums__source_document_type__productionTracking": "Rückmeldung Fertigung", "@sage/xtrem-finance-data/enums__source_document_type__purchaseCreditMemo": "Einkaufsgutschrift", "@sage/xtrem-finance-data/enums__source_document_type__purchaseInvoice": "Einkaufsrechnung", "@sage/xtrem-finance-data/enums__source_document_type__purchaseOrder": "Bestellung", "@sage/xtrem-finance-data/enums__source_document_type__purchaseReceipt": "Wareneingang", "@sage/xtrem-finance-data/enums__source_document_type__purchaseReturn": "Einkaufsretoure", "@sage/xtrem-finance-data/enums__source_document_type__salesCreditMemo": "Verkaufsgutschrift", "@sage/xtrem-finance-data/enums__source_document_type__salesInvoice": "Verkaufsrechnung", "@sage/xtrem-finance-data/enums__source_document_type__salesReturnRequest": "Verkaufsretourenanforderung", "@sage/xtrem-finance-data/enums__source_document_type__salesShipment": "Warenausgang", "@sage/xtrem-finance-data/enums__source_document_type__stockTransferOrder": "Auftrag Bestandstransfer", "@sage/xtrem-finance-data/enums__source_document_type__stockTransferReceipt": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__source_document_type__stockTransferShipment": "<PERSON><PERSON><PERSON>fer", "@sage/xtrem-finance-data/enums__source_document_type__workOrderClose": "Abschluss Fertigungsauftrag", "@sage/xtrem-finance-data/enums__target_document_type__accountsPayableInvoice": "Eingangsrechnung", "@sage/xtrem-finance-data/enums__target_document_type__accountsReceivableAdvance": "Anzahlung Ausgangsrechnung", "@sage/xtrem-finance-data/enums__target_document_type__accountsReceivableInvoice": "Ausgangsrechnung", "@sage/xtrem-finance-data/enums__target_document_type__accountsReceivablePayment": "Zahlung Ausgangsrechnung", "@sage/xtrem-finance-data/enums__target_document_type__journalEntry": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__tax_engine__avalaraAvaTax": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__tax_engine__genericTaxCalculation": "Generische Steuerberechnung", "@sage/xtrem-finance-data/enums__tax_management__excludingTax": "Exkl. Steuern", "@sage/xtrem-finance-data/enums__tax_management__includingTax": "Inkl. Steuern", "@sage/xtrem-finance-data/enums__tax_management__other": "Sonstige", "@sage/xtrem-finance-data/enums__tax_management__reverseCharge": "Reverse Charge", "@sage/xtrem-finance-data/enums__tax_management__tax": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/error": "<PERSON><PERSON>", "@sage/xtrem-finance-data/events__control__posting_class_account_control__account_is_mandatory": "<PERSON>e müssen ein Ko<PERSON> er<PERSON>, wenn keine Zeilendetails vorhanden sind.", "@sage/xtrem-finance-data/events__control__posting_class_line_detail__secondary_criteria_is_tax": "Sie müssen den Steuercode erfassen, wenn die zweite Buchungsklassenzeile vom Typ Steuer ist.", "@sage/xtrem-finance-data/events__posting_class_definition__this_posting_class_definition_has_details": "Sie müssen die Buchungsklassenzeilendetails für diese Definition löschen, bevor Sie die zusätzlichen Kriterien entfernen.", "@sage/xtrem-finance-data/functions__accounting_engine__journals_created": "Journale erstellt: {{journalsCreated}}", "@sage/xtrem-finance-data/functions__default_dimension_lib__wrong_source_for_dimensions": "Sie können den Standard aus {{masterDataDefault}} diesem Dokument {{dimensionDefinitionLevel}} nicht zuweisen.", "@sage/xtrem-finance-data/functions__dimensions__attribute_type_restricted_to": "Das Attribut {{attributeTypeRestrictedToName}} muss ausgefüllt werden.", "@sage/xtrem-finance-data/is_active_business_site_attribute_type_or_value_inactive": "Der Standort {{businessSite}} oder der Geschäftsstandortattributtyp ist inaktiv, oder der Standort ist nicht als Geschäftsstandort definiert.", "@sage/xtrem-finance-data/is_active_customer_attribute_type_or_value_inactive": "Der Kunde {{customer}} oder der Kundenattributtyp ist inaktiv.", "@sage/xtrem-finance-data/is_active_dimension_inactive": "Der Sektor {{dimension}} oder die Dimension {{dimensionType}} ist inaktiv.", "@sage/xtrem-finance-data/is_active_employee_attribute_inactive": "Das Attribut {{employeeAttribute}} oder der Mitarbeiterattributtyp ist inaktiv.", "@sage/xtrem-finance-data/is_active_financial_site_attribute_type_or_value_inactive": "Der Standort {{financialSite}} oder der Buchhaltungsstandortattributtyp ist inaktiv, oder der Standort ist nicht als Buchhaltungsstandort definiert.", "@sage/xtrem-finance-data/is_active_invalid_attribute": "Attribut {{{other}}} ung<PERSON><PERSON>ig", "@sage/xtrem-finance-data/is_active_item_attribute_type_or_value_inactive": "Der Artikel {{customer}} oder der Artikelattributtyp ist inaktiv.", "@sage/xtrem-finance-data/is_active_manufacturing_site_attribute_type_or_value_inactive": "Der Standort {{manufacturingSite}} oder der Fertigungsstandortattributtyp ist inaktiv, oder der Standort ist nicht als Fertigungsstandort definiert.", "@sage/xtrem-finance-data/is_active_project_attribute_inactive": "Das Attribut {{projectAttribute}} oder der Attributtyp 'Projekt' ist inaktiv.", "@sage/xtrem-finance-data/is_active_stock_site_attribute_type_or_value_inactive": "Der Standort {{stockSite}} oder der Lagerstandortattributtyp ist inaktiv, oder der Standort ist nicht als Lagerstandort definiert.", "@sage/xtrem-finance-data/is_active_supplier_attribute_type_or_value_inactive": "Der Lieferant {{supplier}} oder der Lieferantenattributtyp ist inaktiv.", "@sage/xtrem-finance-data/is_active_task_attribute_inactive": "Das Attribut {{taskAttribute}} oder der Attributtyp \"Aufgabe\" ist nicht aktiv.", "@sage/xtrem-finance-data/journal_entry_type_line_legislations_mismatch": "Die Rechtsordnung {{postingClassLegislation}} der Kontoart unterscheidet sich von der Rechtsordnung {{typeLegislation}} des Buchungstyps.", "@sage/xtrem-finance-data/menu_item__features-financial-integration": "Fin<PERSON>zen", "@sage/xtrem-finance-data/menu_item__payment-tracking": "Zahlungsverfolgung", "@sage/xtrem-finance-data/node-extensions__base_business_relation_extension__property__datevId": "DATEV-ID", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__financeIntegrationRecords": "Datensätze Integration Finanzen", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__financeIntegrationStatus": "Status Integration Finanzen", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__forceUpdateForFinance": "Aktualisierung für Finanzen erzwingen", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__wasTaxDataChanged": "Wurden Steuerdaten geändert", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__failed": "<PERSON><PERSON><PERSON> setzen fehlgeschlagen.", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__parameter__baseDocumentItemLine": "Basisdokumentartikelzeile", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__parameter__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__parameter__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__analyticalData": "Analytische Daten", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__computedAttributes": "Berechnete Attribute", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__dimensionDefinitionLevel": "Definitionsebene Sektor", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-finance-data/node-extensions__base_document_line_inquiry_extension__property__postingClass": "Buchungsklasse", "@sage/xtrem-finance-data/node-extensions__company_extension__property__attributeTypes": "Attributtypen", "@sage/xtrem-finance-data/node-extensions__company_extension__property__bankAccount": "Bankkonto", "@sage/xtrem-finance-data/node-extensions__company_extension__property__datevConsultantNumber": "DATEV-Beraternummer", "@sage/xtrem-finance-data/node-extensions__company_extension__property__datevCustomerNumber": "DATEV-Kundennummer", "@sage/xtrem-finance-data/node-extensions__company_extension__property__defaultAttributes": "Standardattribute", "@sage/xtrem-finance-data/node-extensions__company_extension__property__defaultDimensions": "Standardsektoren", "@sage/xtrem-finance-data/node-extensions__company_extension__property__dimensionTypes": "Dimensionen", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doApPosting": "Buchung Eingangsrechnung", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doArPosting": "Buchung Ausgangsrechnung", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doNonAbsorbedPosting": "Nicht absorbierte Buchung", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doStockPosting": "Buchung Bestand", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doWipPosting": "Buchung WIP", "@sage/xtrem-finance-data/node-extensions__company_extension__property__postingClass": "Buchungsklasse", "@sage/xtrem-finance-data/node-extensions__company_extension__property__taxEngine": "Steuermodul", "@sage/xtrem-finance-data/node-extensions__company-extension__do_non_absorbed_posting_not_allowed": "Nicht absorbierte Buchung nicht zul<PERSON>ssig, wenn keine Bestandsbuchung vorhanden.", "@sage/xtrem-finance-data/node-extensions__company-extension__do_non_absorbed_posting_not_allowed_on_legislation": "Sie können keine nicht absorbierten Beträge für diese Rechtsordnung buchen.", "@sage/xtrem-finance-data/node-extensions__company-extension__do_wip_posting_not_allowed_on_legislation": "Sie können keine WIP für diese Rechtsordnung buchen.", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__analyticalData": "Analytische Daten", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__postingClass": "Buchungsklasse", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-finance-data/node-extensions__detailed_resource_extension__property__postingClass": "Buchungsklasse", "@sage/xtrem-finance-data/node-extensions__item_extension__property__analyticalData": "Analytische Daten", "@sage/xtrem-finance-data/node-extensions__item_extension__property__postingClass": "Buchungsklasse", "@sage/xtrem-finance-data/node-extensions__item_extension__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-finance-data/node-extensions__item_extension__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doApPosting": "Buchung Eingangsrechnung", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doArPosting": "Buchung Ausgangsrechnung", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doLandedCostGoodsInTransitPosting": "Buchung Einstandskosten Waren in Zustellung", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doNonAbsorbedPosting": "Nicht absorbierte Buchung", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doNonStockVariancePosting": "Nicht-Bestandsabweichungsbuchung", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doStockPosting": "Buchung Bestand", "@sage/xtrem-finance-data/node-extensions__site_extension__property__analyticalData": "Analytische Daten", "@sage/xtrem-finance-data/node-extensions__site_extension__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-finance-data/node-extensions__site_extension__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__analyticalData": "Analytische Daten", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__postingClass": "Buchungsklasse", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-finance-data/node-extensions__tax_extension__property__postingClass": "Buchungsklasse", "@sage/xtrem-finance-data/node-extensions__tax_extension__property__postingKey": "Buchungsschlüssel", "@sage/xtrem-finance-data/nodes__account__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__account__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__account__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__account__datev_id_invalid": "Die DATEV-ID muss eine Zahl zwischen {{fromValue}} und {{toValue}} sein.", "@sage/xtrem-finance-data/nodes__account__datev_id_not_unique": "Die DATEV-ID muss eindeutig sein.", "@sage/xtrem-finance-data/nodes__account__datev_tax_invalid": "Sie müssen einen Steuercode zuweisen, der mit einem dieser Länder verknüpft ist: {{countryIds}}.", "@sage/xtrem-finance-data/nodes__account__node_name": "Ko<PERSON>", "@sage/xtrem-finance-data/nodes__account__property__attributeTypes": "Attributtypen", "@sage/xtrem-finance-data/nodes__account__property__chartOfAccount": "Kontenrahmen", "@sage/xtrem-finance-data/nodes__account__property__composedDescription": "Zusammengesetzte Bezeichnung", "@sage/xtrem-finance-data/nodes__account__property__datevId": "DATEV-ID", "@sage/xtrem-finance-data/nodes__account__property__dimensionTypes": "Dimensionen", "@sage/xtrem-finance-data/nodes__account__property__id": "ID", "@sage/xtrem-finance-data/nodes__account__property__isActive": "Aktiv", "@sage/xtrem-finance-data/nodes__account__property__isAutomaticAccount": "Ist Automatikkonto", "@sage/xtrem-finance-data/nodes__account__property__isControl": "Prüfung", "@sage/xtrem-finance-data/nodes__account__property__isDirectEntryForbidden": "Direkte Buchung nicht zulässig", "@sage/xtrem-finance-data/nodes__account__property__name": "Name", "@sage/xtrem-finance-data/nodes__account__property__tax": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__account__property__taxManagement": "Steuerverwaltung", "@sage/xtrem-finance-data/nodes__account__tax_management_control": "<PERSON>n Steuerinformationen von Buchungen ausgeschlossen sind, müssen Sie die Steuerverwaltung auf 'Sonstige' setzen.", "@sage/xtrem-finance-data/nodes__account_attribute_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__account_attribute_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__account_attribute_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__account_attribute_type__attribute-type-not-active": "Der Attributtyp ist inaktiv.", "@sage/xtrem-finance-data/nodes__account_attribute_type__node_name": "Attributtyp Konto", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__account": "Ko<PERSON>", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__analyticalMeasureType": "Typ Maßnahme Ko<PERSON>re<PERSON>nung", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__attributeType": "Attributtyp", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__isRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__account_dimension_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__account_dimension_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__account_dimension_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__account_dimension_type__dimension-type-not-active": "Die Dimension ist inaktiv.", "@sage/xtrem-finance-data/nodes__account_dimension_type__node_name": "Dimension Konto", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__account": "Ko<PERSON>", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__analyticalMeasureType": "Typ Maßnahme Ko<PERSON>re<PERSON>nung", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__dimensionType": "Dimension", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__isRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounting_staging__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__accounting_staging__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__accounting_staging__node_name": "Staging Buchhaltung", "@sage/xtrem-finance-data/nodes__accounting_staging__property__account": "Ko<PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging__property__amounts": "Beträge", "@sage/xtrem-finance-data/nodes__accounting_staging__property__analyticalData": "Analytische Daten", "@sage/xtrem-finance-data/nodes__accounting_staging__property__baseDocumentLine": "Basisdokumentzeilen", "@sage/xtrem-finance-data/nodes__accounting_staging__property__batchId": "Batch-ID", "@sage/xtrem-finance-data/nodes__accounting_staging__property__batchSize": "Batchgröße", "@sage/xtrem-finance-data/nodes__accounting_staging__property__companyFxRate": "Wechselkurs Unternehmen", "@sage/xtrem-finance-data/nodes__accounting_staging__property__companyFxRateDivisor": "Wechselkursdivisor Unternehmen", "@sage/xtrem-finance-data/nodes__accounting_staging__property__customer": "Kunde", "@sage/xtrem-finance-data/nodes__accounting_staging__property__customerPostingClass": "Buchungsklasse Kunde", "@sage/xtrem-finance-data/nodes__accounting_staging__property__description": "Bezeichnung", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentDate": "Dokumentdatum", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentNumber": "Dokumentnummer", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentSysId": "System-ID Dokument", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentType": "Dokumenttyp", "@sage/xtrem-finance-data/nodes__accounting_staging__property__dueDate": "Fälligkeitsdatum", "@sage/xtrem-finance-data/nodes__accounting_staging__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance-data/nodes__accounting_staging__property__fxRateDate": "Wechselkursdatum", "@sage/xtrem-finance-data/nodes__accounting_staging__property__isPrinted": "<PERSON><PERSON> g<PERSON><PERSON><PERSON>t", "@sage/xtrem-finance-data/nodes__accounting_staging__property__isProcessed": "<PERSON>t verarbei<PERSON>t", "@sage/xtrem-finance-data/nodes__accounting_staging__property__item": "Artikel", "@sage/xtrem-finance-data/nodes__accounting_staging__property__itemPostingClass": "Buchungsklasse Artikel", "@sage/xtrem-finance-data/nodes__accounting_staging__property__movementType": "Bewegungstyp", "@sage/xtrem-finance-data/nodes__accounting_staging__property__originNotificationId": "Ursprüngliche Benachrichtigungs-ID", "@sage/xtrem-finance-data/nodes__accounting_staging__property__paymentTerm": "Zahlungsbedingung", "@sage/xtrem-finance-data/nodes__accounting_staging__property__payToSupplier": "Zahlungsempfänger", "@sage/xtrem-finance-data/nodes__accounting_staging__property__payToSupplierLinkedAddress": "Verknüpfte Zahlungsempfängeradresse", "@sage/xtrem-finance-data/nodes__accounting_staging__property__providerSite": "Anbieterstandort", "@sage/xtrem-finance-data/nodes__accounting_staging__property__recipientSite": "Empfängerstandort", "@sage/xtrem-finance-data/nodes__accounting_staging__property__replyTopic": "Antwort-Thema", "@sage/xtrem-finance-data/nodes__accounting_staging__property__resource": "Ressource", "@sage/xtrem-finance-data/nodes__accounting_staging__property__resourcePostingClass": "Buchungsklasse Ressource", "@sage/xtrem-finance-data/nodes__accounting_staging__property__returnLinkedAddress": "Verknüpfte Retouradresse", "@sage/xtrem-finance-data/nodes__accounting_staging__property__sourceBaseDocumentLine": "Zeile Ursprungsdokument Basis", "@sage/xtrem-finance-data/nodes__accounting_staging__property__sourceDocumentNumber": "<PERSON><PERSON>mer Ursprungsdokument", "@sage/xtrem-finance-data/nodes__accounting_staging__property__sourceDocumentType": "Typ Ursprungsdokument", "@sage/xtrem-finance-data/nodes__accounting_staging__property__stockJournal": "Bestandsjournal", "@sage/xtrem-finance-data/nodes__accounting_staging__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-finance-data/nodes__accounting_staging__property__storedComputedAttributes": "Gespeicherte berechnete Attribute", "@sage/xtrem-finance-data/nodes__accounting_staging__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplier": "Lieferant", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplierDocumentDate": "Datum Lieferantendokument", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplierDocumentNumber": "Lieferantendokumentnummer", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplierPostingClass": "Lieferantenbuchungsklasse", "@sage/xtrem-finance-data/nodes__accounting_staging__property__targetDocumentType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging__property__taxCalculationStatus": "Status Steuerberechnung", "@sage/xtrem-finance-data/nodes__accounting_staging__property__taxDate": "Steuerdatum", "@sage/xtrem-finance-data/nodes__accounting_staging__property__taxes": "Steuern", "@sage/xtrem-finance-data/nodes__accounting_staging__property__toBeReprocessed": "<PERSON><PERSON><PERSON> zu verarbeiten", "@sage/xtrem-finance-data/nodes__accounting_staging__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__node_name": "Betrag Staging Buchhaltung", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__accountingStaging": "Staging Buchhaltung", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__amount": "Betrag", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__amountType": "Betragstyp", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__baseTax": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__documentLineType": "Dokumentzeilentyp", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__tax": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__taxDate": "Steuerdatum", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__taxPostingClass": "Steuerbuchungsklasse", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__taxRate": "Steuersatz", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__node_name": "Dokumentsteuer Staging Buchhaltung", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__baseTax": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__batchId": "Batch-ID", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__documentNumber": "Dokumentnummer", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__documentType": "Dokumenttyp", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__targetDocumentType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__node_name": "Zeilensteuer Staging Buchhaltung", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__property__accountingStaging": "Staging Buchhaltung", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__property__baseTax": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__node_name": "Bereitstellung Eingangsrechnungszeile", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__property__accountingStaging": "Staging Buchhaltung", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__property__accountsPayableInvoiceLine": "Eingangsrechnungszeile", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__node_name": "Bereitstellung Ausgangsrechnungszeile", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__property__accountingStaging": "Staging Buchhaltung", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__property__accountsReceivableInvoiceLine": "Ausgangsrechnungszeile", "@sage/xtrem-finance-data/nodes__analytical_data__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__analytical_data__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__analytical_data__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__analytical_data__node_name": "Analytische Daten", "@sage/xtrem-finance-data/nodes__analytical_data__property__businessSite": "Geschäftsstandort", "@sage/xtrem-finance-data/nodes__analytical_data__property__businessSiteType": "Typ Geschäftsstandort", "@sage/xtrem-finance-data/nodes__analytical_data__property__customer": "Kunde", "@sage/xtrem-finance-data/nodes__analytical_data__property__customerType": "Kundentyp", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension01": "Sektor 01", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension02": "Sektor 02", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension03": "Sektor 03", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension04": "Sektor 04", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension05": "Sektor 05", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension06": "Sektor 06", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension07": "Sektor 07", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension08": "Sektor 08", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension09": "Sektor 09", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension10": "Sektor 10", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension11": "Sektor 11", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension12": "Sektor 12", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension13": "Sektor 13", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension14": "Sektor 14", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension15": "Sektor 15", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension16": "Sektor 16", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension17": "Sektor 17", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension18": "Sektor 18", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension19": "Sektor 19", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension20": "Sektor 20", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType01": "Dimension 01", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType02": "Dimension 02", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType03": "Dimension 03", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType04": "Dimension 04", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType05": "Dimension 05", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType06": "Dimension 06", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType07": "Dimension 07", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType08": "Dimension 08", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType09": "Dimension 09", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType10": "Dimension 10", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType11": "Dimension 11", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType12": "Dimension 12", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType13": "Dimension 13", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType14": "Dimension 14", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType15": "Dimension 15", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType16": "Dimension 16", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType17": "Dimension 17", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType18": "Dimension 18", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType19": "Dimension 19", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType20": "Dimension 20", "@sage/xtrem-finance-data/nodes__analytical_data__property__employee": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__analytical_data__property__employeeType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__analytical_data__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance-data/nodes__analytical_data__property__financialSiteType": "Typ Buchhaltungsstandort", "@sage/xtrem-finance-data/nodes__analytical_data__property__item": "Artikel", "@sage/xtrem-finance-data/nodes__analytical_data__property__itemType": "Artikeltyp", "@sage/xtrem-finance-data/nodes__analytical_data__property__manufacturingSite": "Fertigungsstandort", "@sage/xtrem-finance-data/nodes__analytical_data__property__manufacturingSiteType": "Typ Fertigungsstandort", "@sage/xtrem-finance-data/nodes__analytical_data__property__project": "Projekt", "@sage/xtrem-finance-data/nodes__analytical_data__property__projectType": "Projekttyp", "@sage/xtrem-finance-data/nodes__analytical_data__property__stockSite": "Lagerstandort", "@sage/xtrem-finance-data/nodes__analytical_data__property__stockSiteType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__analytical_data__property__supplier": "Lieferant", "@sage/xtrem-finance-data/nodes__analytical_data__property__supplierType": "Lieferantentyp", "@sage/xtrem-finance-data/nodes__analytical_data__property__task": "Aufgabe", "@sage/xtrem-finance-data/nodes__analytical_data__property__taskType": "Aufgabentyp", "@sage/xtrem-finance-data/nodes__attribute__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__attribute__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__attribute__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__attribute__attribute_restricted_to": "Sie müssen das Feld \"Eingeschränkt auf\" auf ein gültiges Attribut setzen.", "@sage/xtrem-finance-data/nodes__attribute__node_link_value": "Sie müssen die Node-Verknüpfung auf 'Attribut' setzen.", "@sage/xtrem-finance-data/nodes__attribute__node_name": "Attribut", "@sage/xtrem-finance-data/nodes__attribute__property__attributeRestrictedTo": "Attribut eingeschränkt auf", "@sage/xtrem-finance-data/nodes__attribute__property__attributeRestrictedToId": "Attribut eingeschränkt auf ID", "@sage/xtrem-finance-data/nodes__attribute__property__attributeType": "Attributtyp", "@sage/xtrem-finance-data/nodes__attribute__property__composedDescription": "Zusammengesetzte Bezeichnung", "@sage/xtrem-finance-data/nodes__attribute__property__id": "ID", "@sage/xtrem-finance-data/nodes__attribute__property__isActive": "Aktiv", "@sage/xtrem-finance-data/nodes__attribute__property__item": "Artikel", "@sage/xtrem-finance-data/nodes__attribute__property__name": "Name", "@sage/xtrem-finance-data/nodes__attribute__property__site": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__attribute__referential__integrity": "Das Attribut kann nicht gelöscht werden. Es wird bereits verwendet.", "@sage/xtrem-finance-data/nodes__attribute__type__referential__integrity": "Der Attributtyp kann nicht gelöscht werden. Er wird bereits verwendet.", "@sage/xtrem-finance-data/nodes__attribute__type__referential__integrity_isRestrictedToInUse": "Der Attributtyp wird verwendet. Sie können ihn nicht deaktivieren.", "@sage/xtrem-finance-data/nodes__attribute_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__attribute_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__attribute_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__attribute_type__node__isActive": "<PERSON>e können den Attributtyp nur aktivieren, wenn der Attributtyp 'Eingeschränkt auf {{id}}' aktiv ist.", "@sage/xtrem-finance-data/nodes__attribute_type__node_link_changeable_properties": "Sie können nur das Kontrollkästchen 'Aktiv' oder den Eigenschaftsnamen aktualisieren.", "@sage/xtrem-finance-data/nodes__attribute_type__node_name": "Attributtyp", "@sage/xtrem-finance-data/nodes__attribute_type__property__analyticalMeasureType": "Typ Maßnahme Ko<PERSON>re<PERSON>nung", "@sage/xtrem-finance-data/nodes__attribute_type__property__attributeTypeRestrictedTo": "Attributtyp eingeschränkt auf", "@sage/xtrem-finance-data/nodes__attribute_type__property__id": "ID", "@sage/xtrem-finance-data/nodes__attribute_type__property__isActive": "Aktiv", "@sage/xtrem-finance-data/nodes__attribute_type__property__isLinkedToItem": "Ist verknüpft mit Artikel", "@sage/xtrem-finance-data/nodes__attribute_type__property__isLinkedToSite": "Ist verknüpft mit Standort", "@sage/xtrem-finance-data/nodes__attribute_type__property__linkedTo": "Verknüpft mit", "@sage/xtrem-finance-data/nodes__attribute_type__property__name": "Name", "@sage/xtrem-finance-data/nodes__attribute_type__property__nodeLink": "Node-Verknüpfung", "@sage/xtrem-finance-data/nodes__attribute_type__property__queryFilter": "Abfragefilter", "@sage/xtrem-finance-data/nodes__bank_account__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__bank_account__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__bank_account__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__bank_account__node_name": "Bankkonto", "@sage/xtrem-finance-data/nodes__bank_account__property__bankAccountType": "Bankkontoart", "@sage/xtrem-finance-data/nodes__bank_account__property__currency": "Währung", "@sage/xtrem-finance-data/nodes__bank_account__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance-data/nodes__bank_account__property__id": "ID", "@sage/xtrem-finance-data/nodes__bank_account__property__isActive": "Aktiv", "@sage/xtrem-finance-data/nodes__bank_account__property__name": "Name", "@sage/xtrem-finance-data/nodes__base_business_relation__datev_id_invalid": "Die DATEV-ID muss eine Zahl zwischen {{fromValue}} und {{toValue}} sein.", "@sage/xtrem-finance-data/nodes__base_business_relation__datev_id_not_unique": "Die DATEV-ID muss eindeutig sein.", "@sage/xtrem-finance-data/nodes__base_finance_document__node_name": "Basisfinanzdokument", "@sage/xtrem-finance-data/nodes__base_finance_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_finance_document__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_finance_line__node_name": "Basisfinanzzeile", "@sage/xtrem-finance-data/nodes__base_finance_line__property__attributesAndDimensions": "Attribute und Sektoren", "@sage/xtrem-finance-data/nodes__base_finance_line__property__document": "Dokument", "@sage/xtrem-finance-data/nodes__base_finance_line__property__documentId": "Dokument-ID", "@sage/xtrem-finance-data/nodes__base_finance_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__node_name": "Basisfinanzzeilensektor", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__analyticalData": "Analytische Daten", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__businessSite": "Geschäftsstandort", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__customer": "Kunde", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension01": "Sektor 01", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension02": "Sektor 02", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension03": "Sektor 03", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension04": "Sektor 04", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension05": "Sektor 05", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension06": "Sektor 06", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension07": "Sektor 07", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension08": "Sektor 08", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension09": "Sektor 09", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension10": "Sektor 10", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension11": "Sektor 11", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension12": "Sektor 12", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension13": "Sektor 13", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension14": "Sektor 14", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension15": "Sektor 15", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension16": "Sektor 16", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension17": "Sektor 17", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension18": "Sektor 18", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension19": "Sektor 19", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension20": "Sektor 20", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__employee": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__item": "Artikel", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__manufacturingSite": "Fertigungsstandort", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__originLine": "Ursprüngliche Zeile", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__project": "Projekt", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__stockSite": "Lagerstandort", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__supplier": "Lieferant", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__task": "Aufgabe", "@sage/xtrem-finance-data/nodes__base_open_item__document_type_invalid": "Der Dokumenttyp muss 'Einkaufsrechnung', 'Einkaufsgutschrift', 'Eingangsrechnung', 'Verkaufsrechnung', 'Verkaufsgutschrift' oder 'Ausgangsrechnung' sein.", "@sage/xtrem-finance-data/nodes__base_open_item__negative_forced_amount_paid": "Der erzwungene bezahlte Betrag muss größer als oder gleich 0 sein.", "@sage/xtrem-finance-data/nodes__base_open_item__node_name": "Basis offener Posten", "@sage/xtrem-finance-data/nodes__base_open_item__property__businessEntity": "Geschäftsentität", "@sage/xtrem-finance-data/nodes__base_open_item__property__businessEntityPayment": "Zahlung Geschäftsentität", "@sage/xtrem-finance-data/nodes__base_open_item__property__businessRelation": "Geschäftsbeziehung", "@sage/xtrem-finance-data/nodes__base_open_item__property__closeReason": "Grund für Abschluss", "@sage/xtrem-finance-data/nodes__base_open_item__property__closeText": "Text Abschluss", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountDue": "Unternehmensbetrag fällig", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountDueSigned": "Vorzeichen Unternehmensbetrag fällig", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountPaid": "Unternehmensbetrag bezahlt", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountPaidSigned": "Vorzeichen Unternehmensbetrag bezahlt", "@sage/xtrem-finance-data/nodes__base_open_item__property__currency": "Währung", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountAmount": "Skontobetrag", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountDate": "Skontodatum", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountFrom": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountPaymentBeforeDate": "Skonto Zahlung vor Datum", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountType": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_open_item__property__displayDiscountPaymentDate": "Anzeige Datum Zahlung Skonto", "@sage/xtrem-finance-data/nodes__base_open_item__property__documentNumber": "Dokumentnummer", "@sage/xtrem-finance-data/nodes__base_open_item__property__documentSysId": "System-ID Dokument", "@sage/xtrem-finance-data/nodes__base_open_item__property__documentType": "Dokumenttyp", "@sage/xtrem-finance-data/nodes__base_open_item__property__dueDate": "Fälligkeitsdatum", "@sage/xtrem-finance-data/nodes__base_open_item__property__financialSiteAmountDue": "Fälliger Betrag Buchhaltungsstandort", "@sage/xtrem-finance-data/nodes__base_open_item__property__financialSiteAmountPaid": "Bezahlter Betrag Buchhaltungsstandort", "@sage/xtrem-finance-data/nodes__base_open_item__property__forcedAmountPaid": "Bezahlter Betrag erzwungen", "@sage/xtrem-finance-data/nodes__base_open_item__property__forcedAmountPaidSigned": "Vorzeichen bezahlter Betrag erzwungen", "@sage/xtrem-finance-data/nodes__base_open_item__property__penaltyAmount": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_open_item__property__penaltyPaymentType": "Zahlungsart Mahngebühr", "@sage/xtrem-finance-data/nodes__base_open_item__property__remainingCompanyAmountSigned": "Vorzeichen Unternehmensbetrag verbleibend", "@sage/xtrem-finance-data/nodes__base_open_item__property__remainingTransactionAmountSigned": "Vorzeichen Transaktionsbetrag verbleibend", "@sage/xtrem-finance-data/nodes__base_open_item__property__status": "Status", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountDue": "Transaktionsbetrag fällig", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountDueSigned": "Vorzeichen Transaktionsbetrag fällig", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountPaid": "Transaktionsbetrag bezahlt", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountPaidSigned": "Vorzeichen Transaktionsbetrag bezahlt", "@sage/xtrem-finance-data/nodes__base_open_item__property__type": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_payment_document__id_already_exists": "Die ID existiert bereits. Dem aktuellen Dokument wird kein Nummernkreis zugewiesen.", "@sage/xtrem-finance-data/nodes__base_payment_document__node_name": "Basiszahlungsdokument", "@sage/xtrem-finance-data/nodes__base_payment_document__payment_amount_discrepancy": "Der Zahlungsbetrag des Dokuments {{amount}} muss der Summe der Zahlungsbeträge aller Zeilen {{total}} entsprechen.", "@sage/xtrem-finance-data/nodes__base_payment_document__payment_amount_invalid": "Der Zahlungsbetrag des Dokuments {{amount}} muss positiv sein.", "@sage/xtrem-finance-data/nodes__base_payment_document__property__amount": "Betrag", "@sage/xtrem-finance-data/nodes__base_payment_document__property__amountBankCurrency": "Betrag Bankwährung", "@sage/xtrem-finance-data/nodes__base_payment_document__property__bankAccount": "Bankkonto", "@sage/xtrem-finance-data/nodes__base_payment_document__property__businessRelation": "Geschäftsbeziehung", "@sage/xtrem-finance-data/nodes__base_payment_document__property__businessRelationName": "Name Geschäftsbeziehung", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyAmount": "Unternehmensbetrag", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyCurrency": "Unternehmenswährung", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyExchangeRate": "Wechselkurs Unternehmen", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyExchangeRateDivisor": "Wechselkursdivisor Unternehmen", "@sage/xtrem-finance-data/nodes__base_payment_document__property__currency": "Währung", "@sage/xtrem-finance-data/nodes__base_payment_document__property__customer": "Kunde", "@sage/xtrem-finance-data/nodes__base_payment_document__property__exchangeRateDate": "Wechselkursdatum", "@sage/xtrem-finance-data/nodes__base_payment_document__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance-data/nodes__base_payment_document__property__financialSiteName": "Name Buchhaltungsstandort", "@sage/xtrem-finance-data/nodes__base_payment_document__property__isVoided": "<PERSON><PERSON> stor<PERSON>t", "@sage/xtrem-finance-data/nodes__base_payment_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_payment_document__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_payment_document__property__paymentDate": "Zahlungsdatum", "@sage/xtrem-finance-data/nodes__base_payment_document__property__paymentMethod": "Zahlungsart", "@sage/xtrem-finance-data/nodes__base_payment_document__property__postingDate": "Buchungsdatum", "@sage/xtrem-finance-data/nodes__base_payment_document__property__postingStatus": "Buchungsstatus", "@sage/xtrem-finance-data/nodes__base_payment_document__property__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_payment_document__property__supplier": "Lieferant", "@sage/xtrem-finance-data/nodes__base_payment_document__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-finance-data/nodes__base_payment_document__property__type": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_payment_document__property__voidDate": "Stornierungsdatum", "@sage/xtrem-finance-data/nodes__base_payment_document__property__voidText": "Stornierungstext", "@sage/xtrem-finance-data/nodes__base-open_item__close_reason_mandatory": "Der Grund für den Abschluss ist erforderlich.", "@sage/xtrem-finance-data/nodes__base-open_item__paid_company_amount_discrepancy": "Der bezahlte Unternehmensbetrag muss kleiner als oder gleich dem fälligen Unternehmensbetrag sein.", "@sage/xtrem-finance-data/nodes__base-open_item__paid_financial_site_amount_discrepancy": "Der bezahlte Buchhaltungsstandortbetrag muss kleiner als oder gleich dem fälligen Buchhaltungsstandortbetrag sein.", "@sage/xtrem-finance-data/nodes__base-open_item__paid_transaction_amount_discrepancy": "Der bezahlte Transaktionsbetrag muss kleiner als oder gleich dem fälligen Transaktionsbetrag sein.", "@sage/xtrem-finance-data/nodes__base-open_item__wrong_forced_amount_paid": "Der erzwungene bezahlte Betrag muss zwischen 0 und {{maxForcedAmount}} liegen.", "@sage/xtrem-finance-data/nodes__base-payment_document__bank_amount_invalid": "Der Betrag in Bankwährung des Dokuments {{amount}} muss positiv sein.", "@sage/xtrem-finance-data/nodes__base-payment_document__financial_site_discrepancy": "Der Buchhaltungsstandort des Dokuments muss der gleiche sein wie der Buchhaltungsstandort des Bankkontos.", "@sage/xtrem-finance-data/nodes__base-payment-document__void_date_mandatory": "Das Stornierungsdatum ist erforderlich.", "@sage/xtrem-finance-data/nodes__base-payment-document__void_date_should_be_after_payment_date": "Das Stornierungsdatum muss nach dem Zahlungsdatum liegen.", "@sage/xtrem-finance-data/nodes__close_reason__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__close_reason__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__close_reason__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__close_reason__node_name": "Grund für Abschluss", "@sage/xtrem-finance-data/nodes__close_reason__property__id": "ID", "@sage/xtrem-finance-data/nodes__close_reason__property__isActive": "Ist aktiv", "@sage/xtrem-finance-data/nodes__close_reason__property__name": "Name", "@sage/xtrem-finance-data/nodes__company__datev_number_invalid": "Erfassen Sie eine Nummer zwischen {{first}} und {{last}}.", "@sage/xtrem-finance-data/nodes__company__datev_number_mandatory": "Bei Rechtsordnung Deutschland ist diese Nummer eine Pflichtangabe.", "@sage/xtrem-finance-data/nodes__company__datev_number_only_for_germany": "Diese Nummer betrifft nur die Rechtsordnung Deutschland.", "@sage/xtrem-finance-data/nodes__company__project_task_discrepancy": "Die Attribute vom Typ Projekt und Aufgabe müssen aus dem gleichen Ursprung kommen.", "@sage/xtrem-finance-data/nodes__company_attribute_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__company_attribute_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__company_attribute_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__company_attribute_type__attribute-type-not-active": "Der Attributtyp ist inaktiv", "@sage/xtrem-finance-data/nodes__company_attribute_type__node_name": "Attributtyp Unternehmen", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__analyticalMeasureType": "Typ Maßnahme Ko<PERSON>re<PERSON>nung", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__attributeType": "Attributtyp", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__company": "Unternehmen", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__isRequired": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__company_default_attribute__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__company_default_attribute__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__company_default_attribute__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__company_default_attribute__node_link_master_data_default__not_allowed": "Der Standard aus {{masterDataDefault}} ist im Dokument {{dimensionDefinitionLevel}} nicht zulässig.", "@sage/xtrem-finance-data/nodes__company_default_attribute__node_name": "Standardattribut Unternehmen", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__attributeType": "Attributtyp", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__company": "Unternehmen", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__dimensionDefinitionLevel": "Definitionsebene Sektor", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__masterDataDefault": "Standard Stammdaten", "@sage/xtrem-finance-data/nodes__company_default_dimension__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__company_default_dimension__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__company_default_dimension__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__company_default_dimension__node_name": "Standardsektor Unternehmen", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__company": "Unternehmen", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__dimensionDefinitionLevel": "Definitionsebene Sektor", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__dimensionType": "Dimension", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__masterDataDefault": "Standard Stammdaten", "@sage/xtrem-finance-data/nodes__company_dimension_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__company_dimension_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__company_dimension_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__company_dimension_type__dimension-type-not-active": "Die Dimension ist nicht aktiv", "@sage/xtrem-finance-data/nodes__company_dimension_type__node_name": "Dimension Unternehmen", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__analyticalMeasureType": "Typ Maßnahme Ko<PERSON>re<PERSON>nung", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__company": "Unternehmen", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__dimensionType": "Dimension", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__isRequired": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_account_datev_id_length": "Die Länge der DATEV-ID für einige Konten stimmt nicht mit der Kontolänge überein. Diese müssen übereinstimmen.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_customer_datev_id_length": "Die Länge der DATEV-ID für einige Kunden stimmt nicht mit der Länge der Kunden- und Lieferanten-ID überein. Diese müssen übereinstimmen.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_customer_datev_id_range": "Die DATEV-IDs für einige Kunden liegen außerhalb des Kunden-ID-Bereichs. Diese müssen innerhalb des Bereichs liegen.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_supplier_datev_id_length": "Die Länge der DATEV-ID für einige Kunden stimmt nicht mit der Länge der Kunden- und Lieferanten-ID überein. Diese müssen übereinstimmen.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_supplier_datev_id_range": "Die DATEV-IDs für einige Lieferanten liegen außerhalb des Lieferanten-ID-Bereichs. Diese müssen innerhalb des Bereichs liegen.", "@sage/xtrem-finance-data/nodes__datev_configuration__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__datev_configuration__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__datev_configuration__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__datev_configuration__invalid_length": "Erfassen Sie eine Nummer zwischen {{fromValue}} und {{toValue}}.", "@sage/xtrem-finance-data/nodes__datev_configuration__mutation__datevConfigurationControlsOnSave": "Prüfungen DATEV-Konfiguration beim <PERSON>", "@sage/xtrem-finance-data/nodes__datev_configuration__mutation__datevConfigurationControlsOnSave__failed": "Prüfungen DATEV-Konfiguration beim Speichern fehlgeschlagen.", "@sage/xtrem-finance-data/nodes__datev_configuration__mutation__datevConfigurationControlsOnSave__parameter__datevConfiguration": "DATEV-Konfiguration", "@sage/xtrem-finance-data/nodes__datev_configuration__node_name": "DATEV-Konfiguration", "@sage/xtrem-finance-data/nodes__datev_configuration__property__accountLength": "Ko<PERSON>länge", "@sage/xtrem-finance-data/nodes__datev_configuration__property__customerRangeEnd": "Kundenbereich bis", "@sage/xtrem-finance-data/nodes__datev_configuration__property__customerRangeStart": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-finance-data/nodes__datev_configuration__property__customerSupplierLength": "Länge Kunde Lieferant", "@sage/xtrem-finance-data/nodes__datev_configuration__property__id": "ID", "@sage/xtrem-finance-data/nodes__datev_configuration__property__isActive": "Ist aktiv", "@sage/xtrem-finance-data/nodes__datev_configuration__property__name": "Name", "@sage/xtrem-finance-data/nodes__datev_configuration__property__skrCoa": "SKR COA", "@sage/xtrem-finance-data/nodes__datev_configuration__property__supplierRangeEnd": "Lieferantenbereich bis", "@sage/xtrem-finance-data/nodes__datev_configuration__property__supplierRangeStart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-finance-data/nodes__datev_configuration__wrong_length": "Die Kontolänge stimmt nicht mit der Länge der Kunden- und Lieferanten-ID überein.", "@sage/xtrem-finance-data/nodes__dimension__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__dimension__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__dimension__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__dimension__node_name": "Se<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__dimension__property__composedDescription": "Zusammengesetzte Bezeichnung", "@sage/xtrem-finance-data/nodes__dimension__property__dimensionType": "Dimension", "@sage/xtrem-finance-data/nodes__dimension__property__id": "ID", "@sage/xtrem-finance-data/nodes__dimension__property__isActive": "Aktiv", "@sage/xtrem-finance-data/nodes__dimension__property__name": "Name", "@sage/xtrem-finance-data/nodes__dimension__referential__integrity": "Der Sektor kann nicht gelöscht werden. Er wird bereits verwendet.", "@sage/xtrem-finance-data/nodes__dimension__type__inactive": "Die Dimension ist inaktiv.", "@sage/xtrem-finance-data/nodes__dimension__type__referential__integrity": "Die Dimension kann nicht gelöscht werden. Sie wird bereits verwendet.", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__node_name": "Definitionsebene und Standard Sektor", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__property__dimensionDefinitionLevel": "Definitionsebene Sektor", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__property__masterDataDefault": "Standard Stammdaten", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getAttributesAndDimensionsFromItem": "Attribute und Sektoren von Artikel abrufen", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getAttributesAndDimensionsFromItem__failed": "Attribute und Sektoren von Artikel abrufen fehlgeschlagen.", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getAttributesAndDimensionsFromItem__parameter__data": "Daten", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensions": "Standardattribute und -sektoren abrufen", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensions__failed": "Standardattribute und -sektoren abrufen fehlgeschlagen.", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensions__parameter__data": "Daten", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensionsOrderToOrder": "Standardattribute und -sektoren Auftrag-zu-Auftrag abrufen", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensionsOrderToOrder__failed": "Standardattribute und -sektoren Auftrag-zu-Auftrag abrufen fehlgeschlagen.", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensionsOrderToOrder__parameter__data": "Daten", "@sage/xtrem-finance-data/nodes__dimension_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__dimension_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__dimension_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__dimension_type__max_value": "Alle verfügbaren Dimensionen sind reserviert.", "@sage/xtrem-finance-data/nodes__dimension_type__node_name": "Dimension", "@sage/xtrem-finance-data/nodes__dimension_type__property__analyticalMeasureType": "Typ Maßnahme Ko<PERSON>re<PERSON>nung", "@sage/xtrem-finance-data/nodes__dimension_type__property__dimensions": "Sektoren", "@sage/xtrem-finance-data/nodes__dimension_type__property__docProperty": "Dokumenteigenschaft", "@sage/xtrem-finance-data/nodes__dimension_type__property__isActive": "Aktiv", "@sage/xtrem-finance-data/nodes__dimension_type__property__isUsed": "Wird verwendet", "@sage/xtrem-finance-data/nodes__dimension_type__property__name": "Name", "@sage/xtrem-finance-data/nodes__dimension_type__property__setupId": "ID Einstellungen", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_accounting_staging": "Es gibt ausstehende Buchungen für dieses Dokument. Eine Benachrichtigung wird nicht erneut gesendet.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_finance_transaction_records": "Es gibt keine Finanztransaktionsdatensätze für dieses Dokument. Eine Benachrichtigung wird nicht erneut gesendet.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_finance_transaction_status": "Das Dokument wird verarbeitet. Eine Benachrichtigung wird nicht erneut gesendet.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_package_active": "Das Finanzpaket ist nicht aktiv.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_pi_allocated_to_po": "Diese Einkaufsrechnung hat Werte, die einer Bestellung zugeordnet sind. Eine Benachrichtigung wird nicht erneut gesendet.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_po_allocated_to_pr": "Diesem Wareneingang sind Werte aus einer Bestellung zugeordnet. Eine Benachrichtigung wird nicht erneut gesendet.", "@sage/xtrem-finance-data/nodes__finance_transaction__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__finance_transaction__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__finance_transaction__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__finance_transaction__node_name": "Finanztransaktion", "@sage/xtrem-finance-data/nodes__finance_transaction__property__batchId": "Batch-ID", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentNumber": "Dokumentnummer", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentNumberLink": "Verknüpfung Dokumentnummer", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentSysId": "System-ID Dokument", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentType": "Dokumenttyp", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financeIntegrationApp": "Anwendung Integration Finanzen", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financeIntegrationAppRecordId": "Datensatz-ID Anwendung Integration Finanzen", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financeIntegrationAppUrl": "URL Anwendung Integration Finanzen", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance-data/nodes__finance_transaction__property__hasSourceForDimensionLines": "Hat Ursprung für Sektorzeilen", "@sage/xtrem-finance-data/nodes__finance_transaction__property__lastStatusUpdate": "Letzte Statusaktualisierung", "@sage/xtrem-finance-data/nodes__finance_transaction__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__finance_transaction__property__message": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__finance_transaction__property__paymentTracking": "Zahlungsverfolgung", "@sage/xtrem-finance-data/nodes__finance_transaction__property__postingStatus": "Buchungsstatus", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentLink": "Verknüpfung Ursprungsdokument", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentNumber": "<PERSON><PERSON>mer Ursprungsdokument", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentSysId": "System-ID Ursprungsdokument", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentType": "Typ Ursprungsdokument", "@sage/xtrem-finance-data/nodes__finance_transaction__property__status": "Status", "@sage/xtrem-finance-data/nodes__finance_transaction__property__targetDocumentNumber": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__finance_transaction__property__targetDocumentSysId": "System-ID Zieldokument", "@sage/xtrem-finance-data/nodes__finance_transaction__property__targetDocumentType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusData": "Buchungsstatusdaten abrufen", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusData__failed": "Buchungsstatusdaten abrufen fehlgeschlagen.", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusData__parameter__documentNumber": "Dokumentnummer", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId": "Buchungsstatusdaten nach Dokument-ID abrufen", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId__failed": "Buchungsstatusdaten nach Dokument-ID abrufen fehlgeschlagen.", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId__parameter__documentSysId": "System-ID Dokument", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId__parameter__documentType": "Dokumenttyp", "@sage/xtrem-finance-data/nodes__finance_transaction__status_update_not_allowed": "Der Finanztransaktionsstatus kann nicht von {{previousStatus}} auf {{newStatus}} aktualisiert werden.", "@sage/xtrem-finance-data/nodes__finance_transaction_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__finance_transaction_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__finance_transaction_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__finance_transaction_line__node_name": "Finanztransaktionszeile", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__financeTransaction": "Finanztransaktion", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__isSourceForDimension": "Ist Ursprung für Sektor", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__sourceDocumentNumber": "<PERSON><PERSON>mer Ursprungsdokument", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__sourceDocumentSysId": "System-ID Ursprungsdokument", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__sourceDocumentType": "Typ Ursprungsdokument", "@sage/xtrem-finance-data/nodes__journal__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__journal__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__journal__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__journal__node_name": "Journal", "@sage/xtrem-finance-data/nodes__journal__primaryDocumentType_not_allowed": "Der primäre Dokumenttyp ist nur für die französische Rechtsordnung zulässig.", "@sage/xtrem-finance-data/nodes__journal__property__id": "ID", "@sage/xtrem-finance-data/nodes__journal__property__isActive": "Aktiv", "@sage/xtrem-finance-data/nodes__journal__property__isSubjectToGlTaxExcludedAmount": "Unterliegt Betrag exkl. Steuern im Hauptbuch", "@sage/xtrem-finance-data/nodes__journal__property__legislation": "Rechtsordnung", "@sage/xtrem-finance-data/nodes__journal__property__name": "Name", "@sage/xtrem-finance-data/nodes__journal__property__primaryDocumentType": "Primärer Dokumenttyp", "@sage/xtrem-finance-data/nodes__journal__property__secondaryDocumentType": "Sekundärer Dokumenttyp", "@sage/xtrem-finance-data/nodes__journal__property__sequence": "Nummernkreis", "@sage/xtrem-finance-data/nodes__journal__property__taxImpact": "Auswirkung Steuer", "@sage/xtrem-finance-data/nodes__journal__secondaryDocumentType_not_allowed": "Der sekundäre Dokumenttyp ist nur für die französische Rechtsordnung zulässig.", "@sage/xtrem-finance-data/nodes__journal__taxImpact_cannot_be_set": "Mindestens eine Zeile der Steuerlösung muss 'Dem Hauptbuch exkl. Steuern zugewiesen' sein, um die Option 'Auswirkung Steuer' für das Journal zu setzen.", "@sage/xtrem-finance-data/nodes__journal_cannot_modify_account_entry_exists": "Sie können den Nummernkreis nicht ändern. Für dieses Journal ist eine Buchung vorhanden.", "@sage/xtrem-finance-data/nodes__journal_entry_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__journal_entry_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__journal_entry_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__journal_entry_type__contra_journal_entry_type_line_invalid": "Der Gegenbuchungstyp in der Zeile muss mit dem Buchungstyp übereinstimmen.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_account_type_empty": "<PERSON>sen Sie die Kontoart in der Kopfzeile leer.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_account_type_mandatory": "Erfassen Sie die Kontoart in der Kopfzeile.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_amount_type_empty": "<PERSON>sen Sie den Betragstyp in der Kopfzeile leer.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_amount_type_mandatory": "Erfassen Sie den Betragstyp in der Kopfzeile.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_journal_empty": "Lassen Sie das Journal in der Kopfzeile leer.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_journal_mandatory": "Erfassen Sie das Journal in der Kopfzeile.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_posting_date_empty": "Lassen Sie das Buchungsdatum in der Kopfzeile leer.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_posting_date_mandatory": "Erfassen Sie das Buchungsdatum in der Kopfzeile.", "@sage/xtrem-finance-data/nodes__journal_entry_type__node_name": "Buchungstyp", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__documentType": "Dokumenttyp", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerAccountType": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerAmountType": "Betragstyp Kopfzeile", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerDescription": "Bezeichnung Kopfzeile", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerJournal": "Journal Kopfzeile", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerPostingDate": "Buchungsdatum Kopfzeile", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__immediatePosting": "Sofortbuchung", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__isActive": "Aktiv", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__legislation": "Rechtsordnung", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__name": "Name", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__setupId": "ID Einstellungen", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__targetDocumentType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__forced_false": "Die Einstandskostenartikel können nur für den Bewegungstyp Dokument gesetzt werden.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__not_allowed": "Die Einstandskostenartikel sind nicht zul<PERSON>ssig, wenn der Buchungsklassentyp nicht Artikel ist oder die Buchungsklasse nicht angegeben ist.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__posting_class_definition_control": "Die Einstandskostenartikel sind für die Kontoart nicht zulässig.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__forced_false": "Die nicht bestandsgeführten Artikel können nur für den Bewegungstyp Dokument gesetzt werden.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__not_allowed": "Die nicht bestandsgeführten Artikel sind nicht zul<PERSON>ssig, wenn der Buchungsklassentyp nicht Artikel ist oder die Buchungsklasse nicht angegeben ist.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__posting_class_definition_control": "Die nicht bestandsgeführten Artikel sind für die Kontoart nicht zulässig.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__forced_false": "Die Dienstleistungsartikel können nur für den Bewegungstyp Dokument gesetzt werden.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__not_allowed": "Die Dienstleistungsartikel sind nicht zul<PERSON>ssig, wenn der Buchungsklassentyp nicht Artikel ist oder die Buchungsklasse nicht angegeben ist.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__posting_class_definition_control": "Die Dienstleistungsartikel sind für die Kontoart nicht zulässig.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__forced_true": "<PERSON><PERSON><PERSON> diesen Bewegungstyp müssen Sie Bestandsgeführte Artikel auswählen: {{movementType}}", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__not_allowed": "Die bestandsgeführten Artikel sind nicht zul<PERSON>ssig, wenn der Buchungsklassentyp nicht Artikel ist oder die Buchungsklasse nicht angegeben ist.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__posting_class_definition_control": "Die bestandsgeführten Artikel sind für die Kontoart nicht zulässig.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__node_name": "Buchungstypzeile", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__accountType": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__amountType": "Betragstyp", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__commonReference": "Allgemeine Referenz", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__contraJournalEntryTypeLine": "Gegenbuchungstypzeile", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isLandedCostItemAllowed": "Ist Einstandskostenartikel zulässig", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isNonStockItemAllowed": "Ist nicht bestandsgeführter Artikel zulässig", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isServiceItemAllowed": "Ist Dienstleistungsartikel zulässig", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isStockItemAllowed": "Ist bestandsgeführter Artikel zulässig", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__itemStockManagementCriteria": "Kriterien Artikelbestandsverwaltung", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__journalEntryType": "Buchungstyp", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__movementType": "Bewegungstyp", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__sign": "Vorzeichen", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__sign__empty": "<PERSON>sen Sie das Vorzeichen leer.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__sign_mandatory": "Erfassen Sie das Vorzeichen.", "@sage/xtrem-finance-data/nodes__journal-entry-type-line__at_least_one_item_type_mandatory": "Mindestens einer der bestandsgeführten Artikel, nicht bestandsgeführten Artikel, Dienstleistungsartikel oder Einstandskostenartikel muss für die Kontoart mit dem Buchungsklassentyp Artikel gesetzt sein.", "@sage/xtrem-finance-data/nodes__journal-entry-type-line__contra_journal_entry_type_line_mandatory": "<PERSON><PERSON> der Zieldokumenttyp eine Buchung ist, ist das Gegenkonto erforderlich.", "@sage/xtrem-finance-data/nodes__open_items__type_invalid": "Der Offene-Posten-<PERSON><PERSON> muss '<PERSON><PERSON>' oder '<PERSON><PERSON><PERSON>' sein.", "@sage/xtrem-finance-data/nodes__payment_document_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__payment_document_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__payment_document_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__payment_document_line__negative_discount_amount": "Der Skontobetrag muss größer als oder gleich 0 sein.", "@sage/xtrem-finance-data/nodes__payment_document_line__negative_penalty_amount": "Die Mahngebühr muss größer als oder gleich 0 sein.", "@sage/xtrem-finance-data/nodes__payment_document_line__node_name": "Zahlungsdokumentzeile", "@sage/xtrem-finance-data/nodes__payment_document_line__property__adjustmentAmount": "Korrekturbetrag", "@sage/xtrem-finance-data/nodes__payment_document_line__property__amount": "Betrag", "@sage/xtrem-finance-data/nodes__payment_document_line__property__amountBankCurrency": "Betrag Bankwährung", "@sage/xtrem-finance-data/nodes__payment_document_line__property__companyAmount": "Unternehmensbetrag", "@sage/xtrem-finance-data/nodes__payment_document_line__property__companyCurrency": "Unternehmenswährung", "@sage/xtrem-finance-data/nodes__payment_document_line__property__currency": "Währung", "@sage/xtrem-finance-data/nodes__payment_document_line__property__discountAmount": "Skontobetrag", "@sage/xtrem-finance-data/nodes__payment_document_line__property__document": "Dokument", "@sage/xtrem-finance-data/nodes__payment_document_line__property__documentId": "Dokument-ID", "@sage/xtrem-finance-data/nodes__payment_document_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-finance-data/nodes__payment_document_line__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance-data/nodes__payment_document_line__property__origin": "Ursprung", "@sage/xtrem-finance-data/nodes__payment_document_line__property__originalNodeFactory": "Ursprünglicher Node-Standard", "@sage/xtrem-finance-data/nodes__payment_document_line__property__originalOpenItem": "Ursprünglicher offener Posten", "@sage/xtrem-finance-data/nodes__payment_document_line__property__paymentTracking": "Zahlungsverfolgung", "@sage/xtrem-finance-data/nodes__payment_document_line__property__penaltyAmount": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__payment_document_line__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-finance-data/nodes__payment_tracking__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__payment_tracking__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__payment_tracking__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__payment_tracking__node_name": "Zahlungsverfolgung", "@sage/xtrem-finance-data/nodes__payment_tracking__property__amountPaid": "Bezahlter Betrag", "@sage/xtrem-finance-data/nodes__payment_tracking__property__currency": "Währung", "@sage/xtrem-finance-data/nodes__payment_tracking__property__discountPaymentAmount": "Skontobetrag Zahlung", "@sage/xtrem-finance-data/nodes__payment_tracking__property__discountPaymentBeforeDate": "Skonto Zahlung vor Datum", "@sage/xtrem-finance-data/nodes__payment_tracking__property__discountPaymentType": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__payment_tracking__property__document": "Dokument", "@sage/xtrem-finance-data/nodes__payment_tracking__property__forcedAmountPaid": "Bezahlter Betrag erzwungen", "@sage/xtrem-finance-data/nodes__payment_tracking__property__openItems": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__payment_tracking__property__paymentLines": "Zahlungszeilen", "@sage/xtrem-finance-data/nodes__payment_tracking__property__paymentTerm": "Zahlungsbedingung", "@sage/xtrem-finance-data/nodes__payment_tracking__property__penaltyPaymentAmount": "Zahlungsbetrag Mahngebühr", "@sage/xtrem-finance-data/nodes__payment_tracking__property__penaltyPaymentType": "Zahlungsart Mahngebühr", "@sage/xtrem-finance-data/nodes__payment_tracking__property__status": "Status", "@sage/xtrem-finance-data/nodes__posting_class__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__posting_class__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__posting_class__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_landed_cost_items": "Mindestens eine Zeile lässt Einstandskostenartikel zu.", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_non_stock_items": "Mindestens eine Zeile lässt nicht bestandsgeführte Artikel zu.", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_service_items": "Mindestens eine Zeile lässt Dienstleistungsartikel zu.", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_stock_items": "Mindestens eine Zeile lässt bestandsgeführte Artikel zu.", "@sage/xtrem-finance-data/nodes__posting_class__lines_mandatory": "<PERSON>ür die Buchungsklasse ist mindestens eine Zeile erforderlich.", "@sage/xtrem-finance-data/nodes__posting_class__node_name": "Buchungsklasse", "@sage/xtrem-finance-data/nodes__posting_class__one_non_detailed_allowed": "Es ist nur eine nicht detaillierte Buchungsklasse vom Typ {{type}} zulässig.", "@sage/xtrem-finance-data/nodes__posting_class__property__financeItemType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__posting_class__property__id": "ID", "@sage/xtrem-finance-data/nodes__posting_class__property__isDetailed": "Detailliert", "@sage/xtrem-finance-data/nodes__posting_class__property__isLandedCostItemAllowed": "Ist Einstandskostenartikel zulässig", "@sage/xtrem-finance-data/nodes__posting_class__property__isNonStockItemAllowed": "Ist nicht bestandsgeführter Artikel zulässig", "@sage/xtrem-finance-data/nodes__posting_class__property__isServiceItemAllowed": "Ist Dienstleistungsartikel zulässig", "@sage/xtrem-finance-data/nodes__posting_class__property__isStockItemAllowed": "Ist bestandsgeführter Artikel zulässig", "@sage/xtrem-finance-data/nodes__posting_class__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__posting_class__property__name": "Name", "@sage/xtrem-finance-data/nodes__posting_class__property__setupId": "ID Einstellungen", "@sage/xtrem-finance-data/nodes__posting_class__property__type": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__posting_class__update_is_detailed_not_allowed": "<PERSON>e können den Schalter 'Detailliert' nicht auf AUS setzen, da die Buchungsklasse {{postingClassName}} mit {{recordNameUsingThisPostingClass}} verknüpft ist.", "@sage/xtrem-finance-data/nodes__posting_class_definition__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__posting_class_definition__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__posting_class_definition__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__posting_class_definition__can_have_secondary_criteria_not_allowed": "Sie können keine sekundären Kriterien für diesen Buchungsklassentyp erfassen: {{ postingClassType }}.", "@sage/xtrem-finance-data/nodes__posting_class_definition__criteria_and_secondary_criteria_are_the_same": "<PERSON><PERSON> müssen andere Kriterien auswählen.", "@sage/xtrem-finance-data/nodes__posting_class_definition__node_name": "Definition Buchungsklasse", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__accountTypeName": "Name <PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__additionalCriteria": "Zusätzliche Kriterien", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__canHaveAdditionalCriteria": "Kann zusätzliche Kriterien haben", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__financeItemType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__id": "ID", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isDetailed": "Detailliert", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isLandedCostItemAllowed": "Ist Einstandskostenartikel zulässig", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isNonStockItemAllowed": "Ist nicht bestandsgeführter Artikel zulässig", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isServiceItemAllowed": "Ist Dienstleistungsartikel zulässig", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isStockItemAllowed": "Ist bestandsgeführter Artikel zulässig", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__legislation": "Rechtsordnung", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__postingClassType": "Typ Buchungsklasse", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__setupId": "ID Einstellungen", "@sage/xtrem-finance-data/nodes__posting_class_definition__secondary_criteria_incorrect_value": "Die sekundären Kriterien müssen vom Typ Steuer sein.", "@sage/xtrem-finance-data/nodes__posting_class_definition__secondary_criteria_not_allowed": "Sie können keine sekundären Kriterien erfassen.", "@sage/xtrem-finance-data/nodes__posting_class_definition__tax_solution_control": "Die Steuerlösungen für diese Rechtsordnung müssen mindestens eine erforderliche Steuerkategorie haben.", "@sage/xtrem-finance-data/nodes__posting_class_definition__update_is_detailed_not_allowed": "<PERSON>e können den Schalter 'Detailliert' nicht auf AUS setzen, da die Buchungsklasse {{postingClassName}} mit dieser Definition verknüpft ist.", "@sage/xtrem-finance-data/nodes__posting_class_line__already_used_posting_class_definition": "Die Buchungsklassendefinition wird bereits in einer anderen Zeile verwendet.", "@sage/xtrem-finance-data/nodes__posting_class_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__posting_class_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__posting_class_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__posting_class_line__node_name": "Zeile Buchungsklasse", "@sage/xtrem-finance-data/nodes__posting_class_line__property__account": "Ko<PERSON>", "@sage/xtrem-finance-data/nodes__posting_class_line__property__accountId": "Konto-ID", "@sage/xtrem-finance-data/nodes__posting_class_line__property__chartOfAccount": "Kontenrahmen", "@sage/xtrem-finance-data/nodes__posting_class_line__property__definition": "Definition", "@sage/xtrem-finance-data/nodes__posting_class_line__property__details": "Details", "@sage/xtrem-finance-data/nodes__posting_class_line__property__hasDetails": "Hat Details", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isLandedCostItemAllowed": "Ist Einstandskostenartikel zulässig", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isNonStockItemAllowed": "Ist nicht bestandsgeführter Artikel zulässig", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isServiceItemAllowed": "Ist Dienstleistungsartikel zulässig", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isStockItemAllowed": "Ist bestandsgeführter Artikel zulässig", "@sage/xtrem-finance-data/nodes__posting_class_line__property__postingClass": "Buchungsklasse", "@sage/xtrem-finance-data/nodes__posting_class_line__property__updateAccountTaxManagement": "Kontosteuerverwaltung aktualisieren", "@sage/xtrem-finance-data/nodes__posting_class_line__query__getPostingClassAccounts": "Buchungsklassenkonten abrufen", "@sage/xtrem-finance-data/nodes__posting_class_line__query__getPostingClassAccounts__failed": "Buchungsklassenkonten abrufen fehlgeschlagen.", "@sage/xtrem-finance-data/nodes__posting_class_line__query__getPostingClassAccounts__parameter__postingClassDefinition": "Definition Buchungsklasse", "@sage/xtrem-finance-data/nodes__posting_class_line__wrong_account_tax_management": "Wählen Sie eine Kontoverwaltung vom Typ 'Steuer'.", "@sage/xtrem-finance-data/nodes__posting_class_line__wrong_posting_class_definition_is_detailed": "Wenn die Buchungsklasse einen Namen hat, sind nur Buchungsklassendefinitionen mit dem aktivierten Kontrollkästchen 'Detailliert' zulässig. Wenn die Buchungsklasse keinen Namen hat, sind nur Buchungsklassendefinitionen ohne das aktivierte Kontrollkästchen 'Detailliert' zulässig.", "@sage/xtrem-finance-data/nodes__posting_class_line__wrong_posting_class_definition_type": "Die Buchungsklassendefinition muss den Typ {{ postingClassType }} haben.", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__mutation__getTaxCategoryIds": "Steuerkategorie-IDs abrufen", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__mutation__getTaxCategoryIds__failed": "Steuerkategorie-IDs abrufen fehlgeschlagen.", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__mutation__getTaxCategoryIds__parameter__legislation": "Rechtsordnung", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__no_secondary_criteria_defined": "<PERSON><PERSON><PERSON> diese Buchungsklasse ist die Option für sekundäre Kriterien nicht ausgewählt.", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__node_name": "Detail Zeile Buchungsklasse", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__property__account": "Ko<PERSON>", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__property__postingClassLine": "Zeile Buchungsklasse", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__property__tax": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__tax__posting_key_invalid": "Der Buchungsschlüssel muss zwischen 1 und 9999 liegen.", "@sage/xtrem-finance-data/nodes__tax__posting_key_wrong_country": "<PERSON>e können nur einen Wert er<PERSON>ssen, wenn das Land Deutschland ist.", "@sage/xtrem-finance-data/package__name": "Einstellungen Finanzen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension____navigationPanel__listItem__datevId__title": "DATEV-ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension____navigationPanel__listItem__line11__title": "Buchungsklasse", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__datevId____title": "DATEV-ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____lookupDialogTitle": "Sektor 01 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____title": "Sektor 01", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____lookupDialogTitle": "Sektor 02 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____title": "Sektor 02", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____lookupDialogTitle": "Sektor 03 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____title": "Sektor 03", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____lookupDialogTitle": "Sektor 04 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____title": "Sektor 04", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____lookupDialogTitle": "Sektor 05 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____title": "Sektor 05", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____lookupDialogTitle": "Sektor 06 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____title": "Sektor 06", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____lookupDialogTitle": "Sektor 07 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____title": "Sektor 07", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____lookupDialogTitle": "Sektor 08 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____title": "Sektor 08", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____lookupDialogTitle": "Sektor 09 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____title": "Sektor 09", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____lookupDialogTitle": "Sektor 10 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____title": "Sektor 10", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____lookupDialogTitle": "Sektor 11 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____title": "Sektor 11", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____lookupDialogTitle": "Sektor 12 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____title": "Sektor 12", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____lookupDialogTitle": "Sektor 13 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____title": "Sektor 13", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____lookupDialogTitle": "Sektor 14 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____title": "Sektor 14", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____lookupDialogTitle": "Sektor 15 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____title": "Sektor 15", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____lookupDialogTitle": "Sektor 16 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____title": "Sektor 16", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____lookupDialogTitle": "Sektor 17 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____title": "Sektor 17", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____lookupDialogTitle": "Sektor 18 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____title": "Sektor 18", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____lookupDialogTitle": "Sektor 19 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____title": "Sektor 19", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____lookupDialogTitle": "Sektor 20 auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____title": "Sektor 20", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimensionBlock____title": "Sektoren", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____lookupDialogTitle": "Mitarbeiter auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__postingClass____lookupDialogTitle": "Buchungsklasse auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__postingClass____title": "Buchungsklasse", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____lookupDialogTitle": "Projekt auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____title": "Projekt", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____lookupDialogTitle": "Aufgabe auswählen", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____title": "Aufgabe", "@sage/xtrem-finance-data/page-extensions__company_extension____navigationPanel__listItem__datevConsultantNumber__title": "DATEV-Beraternummer", "@sage/xtrem-finance-data/page-extensions__company_extension____navigationPanel__listItem__datevCustomerNumber__title": "DATEV-Kundennummer", "@sage/xtrem-finance-data/page-extensions__company_extension__addDefaultDimension____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__company_extension__addDimensionAttributeLine____title": "Erforderliche Sektoren und Attribute", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____columns__title__id": "Name", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____dropdownActions__title": "Löschen", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____title": "Erforderliche Sektoren", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeTypes____columns__title__analyticalMeasureType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeTypes____title": "Attribute", "@sage/xtrem-finance-data/page-extensions__company_extension__bankAccount____lookupDialogTitle": "Bankkonto auswählen", "@sage/xtrem-finance-data/page-extensions__company_extension__bankAccount____title": "Standardbankkonto", "@sage/xtrem-finance-data/page-extensions__company_extension__country____columns__title__legislation__name": "Rechtsordnung", "@sage/xtrem-finance-data/page-extensions__company_extension__datevConsultantNumber____title": "DATEV-Beraternummer", "@sage/xtrem-finance-data/page-extensions__company_extension__datevCustomerNumber____title": "DATEV-Kundennummer", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____columns__columns__company__name__title": "Name", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____columns__columns__company__name__title__2": "ID", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____columns__title__company__name": "Unternehmen", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____title": "Attribute", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributesAndDimensions____columns__title__document": "Dokument und Ursprung", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributesAndDimensions____dropdownActions__title": "Löschen", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributesAndDimensions____title": "Standardsektorregeln", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____columns__columns__company__name__title": "Name", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____columns__columns__company__name__title__2": "ID", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____columns__title__company__name": "Unternehmen", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____title": "Sektoren", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionsBlock____title": "Sektoren", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionsSection____title": "Sektoren", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionTypes____columns__title__analyticalMeasureType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionTypes____title": "Sektoren", "@sage/xtrem-finance-data/page-extensions__company_extension__doApPosting____title": "Buchung Eingangsrechnung", "@sage/xtrem-finance-data/page-extensions__company_extension__doArPosting____title": "Buchung Ausgangsrechnung", "@sage/xtrem-finance-data/page-extensions__company_extension__doNonAbsorbedPosting____title": "Buchung nicht absorbierter Betrag", "@sage/xtrem-finance-data/page-extensions__company_extension__doStockPosting____title": "Buchung Bestand", "@sage/xtrem-finance-data/page-extensions__company_extension__doWipPosting____title": "Buchung WIP", "@sage/xtrem-finance-data/page-extensions__company_extension__financePostingBlock____title": "Fin<PERSON>zen", "@sage/xtrem-finance-data/page-extensions__company_extension__manufacturingPostingBlock____title": "Fertigung", "@sage/xtrem-finance-data/page-extensions__company_extension__postingClass____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__company_extension__postingClass____lookupDialogTitle": "Buchungsklasse auswählen", "@sage/xtrem-finance-data/page-extensions__company_extension__postingClass____title": "Buchungsklasse", "@sage/xtrem-finance-data/page-extensions__company_extension__postingSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__company_extension__selectAttributeAndDimensionBlock____title": "Verwaltung", "@sage/xtrem-finance-data/page-extensions__company_extension__selectAttributeAndDimensionSection____title": "Erforderliche Sektoren", "@sage/xtrem-finance-data/page-extensions__company_extension__selectDimensionAndAttributeTypes____columns__title__id": "Name", "@sage/xtrem-finance-data/page-extensions__company_extension__selectDimensionAndAttributeTypes____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__company_extension__selectDimensionAndAttributeTypes____title": "Erforderliche Sektoren", "@sage/xtrem-finance-data/page-extensions__company_extension__stockPostingBlock____title": "Bestand", "@sage/xtrem-finance-data/page-extensions__company_extension__taxEngine____title": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__company_extension__taxManagementBlock____title": "Steuerverwaltung", "@sage/xtrem-finance-data/page-extensions__customer_extension____navigationPanel__listItem__datevId__title": "DATEV-ID", "@sage/xtrem-finance-data/page-extensions__customer_extension____navigationPanel__listItem__line11__title": "Buchungsklasse", "@sage/xtrem-finance-data/page-extensions__customer_extension__datevId____title": "DATEV-ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____lookupDialogTitle": "Sektor 01 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____title": "Sektor 01", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____lookupDialogTitle": "Sektor 02 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____title": "Sektor 02", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____lookupDialogTitle": "Sektor 03 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____title": "Sektor 03", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____lookupDialogTitle": "Sektor 04 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____title": "Sektor 04", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____lookupDialogTitle": "Sektor 05 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____title": "Sektor 05", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____lookupDialogTitle": "Sektor 06 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____title": "Sektor 06", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____lookupDialogTitle": "Sektor 07 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____title": "Sektor 07", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____lookupDialogTitle": "Sektor 08 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____title": "Sektor 08", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____lookupDialogTitle": "Sektor 09 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____title": "Sektor 09", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____lookupDialogTitle": "Sektor 10 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____title": "Sektor 10", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____lookupDialogTitle": "Sektor 11 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____title": "Sektor 11", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____lookupDialogTitle": "Sektor 12 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____title": "Sektor 12", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____lookupDialogTitle": "Sektor 13 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____title": "Sektor 13", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____lookupDialogTitle": "Sektor 14 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____title": "Sektor 14", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____lookupDialogTitle": "Sektor 15 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____title": "Sektor 15", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____lookupDialogTitle": "Sektor 16 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____title": "Sektor 16", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____lookupDialogTitle": "Sektor 17 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____title": "Sektor 17", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____lookupDialogTitle": "Sektor 18 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____title": "Sektor 18", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____lookupDialogTitle": "Sektor 19 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____title": "Sektor 19", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____lookupDialogTitle": "Sektor 20 auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____title": "Sektor 20", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimensionBlock____title": "Sektoren", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____lookupDialogTitle": "Mitarbeiter auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__postingClass____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__customer_extension__postingClass____lookupDialogTitle": "Buchungsklasse auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__postingClass____title": "Buchungsklasse", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____lookupDialogTitle": "Projekt auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____title": "Projekt", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____lookupDialogTitle": "Aufgabe auswählen", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____title": "Aufgabe", "@sage/xtrem-finance-data/page-extensions__item_extension____navigationPanel__listItem__postingClass__title": "Buchungsklasse", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____lookupDialogTitle": "Sektor 01 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____title": "Sektor 01", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____lookupDialogTitle": "Sektor 02 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____title": "Sektor 02", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____lookupDialogTitle": "Sektor 03 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____title": "Sektor 03", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____lookupDialogTitle": "Sektor 04 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____title": "Sektor 04", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____lookupDialogTitle": "Sektor 05 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____title": "Sektor 05", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____lookupDialogTitle": "Sektor 06 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____title": "Sektor 06", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____lookupDialogTitle": "Sektor 07 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____title": "Sektor 07", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____lookupDialogTitle": "Sektor 08 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____title": "Sektor 08", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____lookupDialogTitle": "Sektor 09 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____title": "Sektor 09", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____lookupDialogTitle": "Sektor 10 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____title": "Sektor 10", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____lookupDialogTitle": "Sektor 11 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____title": "Sektor 11", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____lookupDialogTitle": "Sektor 12 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____title": "Sektor 12", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____lookupDialogTitle": "Sektor 13 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____title": "Sektor 13", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____lookupDialogTitle": "Sektor 14 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____title": "Sektor 14", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____lookupDialogTitle": "Sektor 15 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____title": "Sektor 15", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____lookupDialogTitle": "Sektor 16 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____title": "Sektor 16", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____lookupDialogTitle": "Sektor 17 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____title": "Sektor 17", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____lookupDialogTitle": "Sektor 18 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____title": "Sektor 18", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____lookupDialogTitle": "Sektor 19 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____title": "Sektor 19", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____lookupDialogTitle": "Sektor 20 auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____title": "Sektor 20", "@sage/xtrem-finance-data/page-extensions__item_extension__dimensionBlock____title": "Sektoren", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____lookupDialogTitle": "Mitarbeiter auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__postingClass____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__item_extension__postingClass____lookupDialogTitle": "Buchungsklasse auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__postingClass____title": "Buchungsklasse", "@sage/xtrem-finance-data/page-extensions__item_extension__project____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__item_extension__project____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__project____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__project____lookupDialogTitle": "Projekt auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__project____title": "Projekt", "@sage/xtrem-finance-data/page-extensions__item_extension__task____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__item_extension__task____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__task____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__task____lookupDialogTitle": "Aufgabe auswählen", "@sage/xtrem-finance-data/page-extensions__item_extension__task____title": "Aufgabe", "@sage/xtrem-finance-data/page-extensions__labor_resource_extension__postingClass____lookupDialogTitle": "Buchungsklasse auswählen", "@sage/xtrem-finance-data/page-extensions__labor_resource_extension__postingClass____title": "Buchungsklasse", "@sage/xtrem-finance-data/page-extensions__machine_resource_extension__postingClass____lookupDialogTitle": "Buchungsklasse auswählen", "@sage/xtrem-finance-data/page-extensions__machine_resource_extension__postingClass____title": "Buchungsklasse", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____lookupDialogTitle": "Sektor 01 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____title": "Sektor 01", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____lookupDialogTitle": "Sektor 02 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____title": "Sektor 02", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____lookupDialogTitle": "Sektor 03 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____title": "Sektor 03", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____lookupDialogTitle": "Sektor 04 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____title": "Sektor 04", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____lookupDialogTitle": "Sektor 05 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____title": "Sektor 05", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____lookupDialogTitle": "Sektor 06 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____title": "Sektor 06", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____lookupDialogTitle": "Sektor 07 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____title": "Sektor 07", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____lookupDialogTitle": "Sektor 08 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____title": "Sektor 08", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____lookupDialogTitle": "Sektor 09 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____title": "Sektor 09", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____lookupDialogTitle": "Sektor 10 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____title": "Sektor 10", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____lookupDialogTitle": "Sektor 11 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____title": "Sektor 11", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____lookupDialogTitle": "Sektor 12 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____title": "Sektor 12", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____lookupDialogTitle": "Sektor 13 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____title": "Sektor 13", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____lookupDialogTitle": "Sektor 14 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____title": "Sektor 14", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____lookupDialogTitle": "Sektor 15 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____title": "Sektor 15", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____lookupDialogTitle": "Sektor 16 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____title": "Sektor 16", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____lookupDialogTitle": "Sektor 17 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____title": "Sektor 17", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____lookupDialogTitle": "Sektor 18 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____title": "Sektor 18", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____lookupDialogTitle": "Sektor 19 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____title": "Sektor 19", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____lookupDialogTitle": "Sektor 20 auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____title": "Sektor 20", "@sage/xtrem-finance-data/page-extensions__site_extension__dimensionBlock____title": "Sektoren", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____lookupDialogTitle": "Mitarbeiter auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__site_extension__financialSection____title": "Fin<PERSON>zen", "@sage/xtrem-finance-data/page-extensions__site_extension__project____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__site_extension__project____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__project____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__project____lookupDialogTitle": "Projekt auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__project____title": "Projekt", "@sage/xtrem-finance-data/page-extensions__site_extension__task____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__site_extension__task____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__task____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__task____lookupDialogTitle": "Aufgabe auswählen", "@sage/xtrem-finance-data/page-extensions__site_extension__task____title": "Aufgabe", "@sage/xtrem-finance-data/page-extensions__supplier_extension____navigationPanel__listItem__datevId__title": "DATEV-ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension____navigationPanel__listItem__line11__title": "Buchungsklasse", "@sage/xtrem-finance-data/page-extensions__supplier_extension__datevId____title": "DATEV-ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____lookupDialogTitle": "Sektor 01 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____title": "Sektor 01", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____lookupDialogTitle": "Sektor 02 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____title": "Sektor 02", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____lookupDialogTitle": "Sektor 03 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____title": "Sektor 03", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____lookupDialogTitle": "Sektor 04 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____title": "Sektor 04", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____lookupDialogTitle": "Sektor 05 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____title": "Sektor 05", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____lookupDialogTitle": "Sektor 06 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____title": "Sektor 06", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____lookupDialogTitle": "Sektor 07 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____title": "Sektor 07", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____lookupDialogTitle": "Sektor 08 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____title": "Sektor 08", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____lookupDialogTitle": "Sektor 09 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____title": "Sektor 09", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____lookupDialogTitle": "Sektor 10 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____title": "Sektor 10", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____lookupDialogTitle": "Sektor 11 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____title": "Sektor 11", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____lookupDialogTitle": "Sektor 12 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____title": "Sektor 12", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____lookupDialogTitle": "Sektor 13 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____title": "Sektor 13", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____lookupDialogTitle": "Sektor 14 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____title": "Sektor 14", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____lookupDialogTitle": "Sektor 15 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____title": "Sektor 15", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____lookupDialogTitle": "Sektor 16 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____title": "Sektor 16", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____lookupDialogTitle": "Sektor 17 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____title": "Sektor 17", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____lookupDialogTitle": "Sektor 18 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____title": "Sektor 18", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____lookupDialogTitle": "Sektor 19 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____title": "Sektor 19", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____lookupDialogTitle": "Sektor 20 auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____title": "Sektor 20", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimensionBlock____title": "Sektoren", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____lookupDialogTitle": "Mitarbeiter auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__postingClass____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__supplier_extension__postingClass____lookupDialogTitle": "Buchungsklasse auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__postingClass____title": "Buchungsklasse", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____lookupDialogTitle": "Projekt auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____title": "Projekt", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____lookupDialogTitle": "Aufgabe auswählen", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____title": "Aufgabe", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionBlock____title": "Fin<PERSON>zen", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionCriteriaBlock____title": "Kriterien", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionDocumentType____title": "Dokumenttyp", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___createStamp": "Gesendet", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___id": "ID", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__batchId": "Batch-ID", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__documentNumber": "Dokumentnummer", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__documentType": "Dokumenttyp", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__message": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__postingStatus": "Status", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__status": "Benachrichtigungssta<PERSON>", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__targetDocumentType": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____title": "Ergebnisse", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionSection____title": "Fin<PERSON>zen", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionStatus____title": "Status", "@sage/xtrem-finance-data/page-extensions__tax_extension____navigationPanel__listItem__line7__title": "Buchungsklasse", "@sage/xtrem-finance-data/page-extensions__tax_extension____navigationPanel__listItem__postingKey__title": "Buchungsschlüssel", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingClass____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingClass____lookupDialogTitle": "Buchungsklasse auswählen", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingClass____title": "Buchungsklasse", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingKey____title": "Buchungsschlüssel", "@sage/xtrem-finance-data/page-extensions__tool_resource_extension__postingClass____lookupDialogTitle": "Buchungsklasse auswählen", "@sage/xtrem-finance-data/page-extensions__tool_resource_extension__postingClass____title": "Buchungsklasse", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__datevId__title": "DATEV-ID", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line_4__title": "Direkte Buchung nicht zulässig", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line_5__title": "Kontenrahmen", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line3__title": "Prüfung", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line4__title": "Direkte Buchung nicht zulässig", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line5__title": "Kontenrahmen", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line6__title": "Steuerverwaltung", "@sage/xtrem-finance-data/pages__account____navigationPanel__optionsMenu__title": "Kontenrahmen FR", "@sage/xtrem-finance-data/pages__account____navigationPanel__optionsMenu__title__2": "Kontenrahmen US", "@sage/xtrem-finance-data/pages__account____objectTypePlural": "Konten", "@sage/xtrem-finance-data/pages__account____objectTypeSingular": "Ko<PERSON>", "@sage/xtrem-finance-data/pages__account____title": "Ko<PERSON>", "@sage/xtrem-finance-data/pages__account___id____title": "ID", "@sage/xtrem-finance-data/pages__account__attributeDimensionTypes____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__account__attributeDimensionTypes____title": "Erforderliche Sektoren", "@sage/xtrem-finance-data/pages__account__attributeDimensionTypesBlock____title": "Erforderliche Sektoren", "@sage/xtrem-finance-data/pages__account__chartOfAccount____lookupDialogTitle": "Kontenrahmen auswählen", "@sage/xtrem-finance-data/pages__account__chartOfAccount____title": "Kontenrahmen", "@sage/xtrem-finance-data/pages__account__datevIdString____title": "DATEV-ID", "@sage/xtrem-finance-data/pages__account__datevSection____title": "DATEV", "@sage/xtrem-finance-data/pages__account__empty_tax_warning": "Sie müssen den Steuercode eingeben, wenn das Automatikkonto aktiviert ist.", "@sage/xtrem-finance-data/pages__account__headerSection____title": "Kopfberei<PERSON>", "@sage/xtrem-finance-data/pages__account__id____title": "ID", "@sage/xtrem-finance-data/pages__account__isActive____title": "Aktiv", "@sage/xtrem-finance-data/pages__account__isAutomaticAccount____title": "Automatikkonto", "@sage/xtrem-finance-data/pages__account__isControl____title": "Prüfung", "@sage/xtrem-finance-data/pages__account__isDirectEntryForbidden____title": "Direkte Buchung nicht zulässig", "@sage/xtrem-finance-data/pages__account__isDirectPostingForbidden____title": "Direkte Buchung nicht zulässig", "@sage/xtrem-finance-data/pages__account__mainSection____title": "Allgemein", "@sage/xtrem-finance-data/pages__account__name____title": "Name", "@sage/xtrem-finance-data/pages__account__option_menu____title__all": "Alle", "@sage/xtrem-finance-data/pages__account__save____title": "Speichern", "@sage/xtrem-finance-data/pages__account__tax____columns__lookupDialogTitle__country": "Land auswählen", "@sage/xtrem-finance-data/pages__account__tax____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__account__taxManagement____title": "Steuerverwaltung", "@sage/xtrem-finance-data/pages__account__TaxManagementBlock____title": "Steuerverwaltung", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__item__title": "Artikel", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__line3__title": "Attributtyp", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__line3Right__title": "Eingeschränkt auf Name", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__restrictedTo__title": "Eingeschränkt auf ID", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__site__title": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__optionsMenu__title": "Projekt", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__attribute____objectTypePlural": "Attribute", "@sage/xtrem-finance-data/pages__attribute____objectTypeSingular": "Attribut", "@sage/xtrem-finance-data/pages__attribute____subtitle": "Attribut", "@sage/xtrem-finance-data/pages__attribute____title": "Attribut", "@sage/xtrem-finance-data/pages__attribute__attributeRestrictedTo____lookupDialogTitle": "Auswahl Attribut eingeschränkt auf", "@sage/xtrem-finance-data/pages__attribute__attributeType____lookupDialogTitle": "Attributtyp auswählen", "@sage/xtrem-finance-data/pages__attribute__item____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-finance-data/pages__attribute__mainSection____title": "Allgemein", "@sage/xtrem-finance-data/pages__attribute__site____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__listItem__line_4__title": "Filter", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__listItem__line3__title": "Node-Verknüpfung", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__listItem__line4__title": "Filter", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-finance-data/pages__attribute_type____objectTypePlural": "Attributtypen", "@sage/xtrem-finance-data/pages__attribute_type____objectTypeSingular": "Attributtyp", "@sage/xtrem-finance-data/pages__attribute_type____title": "Attributtyp", "@sage/xtrem-finance-data/pages__attribute_type__attributeTypeRestrictedTo____lookupDialogTitle": "Auswahl Attributtyp eingeschränkt auf", "@sage/xtrem-finance-data/pages__attribute_type__attributeTypeRestrictedTo____title": "Eingeschränkt auf", "@sage/xtrem-finance-data/pages__attribute_type__id____title": "ID", "@sage/xtrem-finance-data/pages__attribute_type__isActive____title": "Aktiv", "@sage/xtrem-finance-data/pages__attribute_type__linked_to_not_valid": "Der Wert für Verknüpft mit {{linkedTo}} ist nicht gültig.", "@sage/xtrem-finance-data/pages__attribute_type__linkedTo____title": "Verknüpft mit", "@sage/xtrem-finance-data/pages__attribute_type__mainSection____title": "Allgemein", "@sage/xtrem-finance-data/pages__attribute_type__name____title": "Name", "@sage/xtrem-finance-data/pages__attribute_type__nodeLink____title": "Node-Verknüpfung", "@sage/xtrem-finance-data/pages__attribute_type__queryFilter____title": "Filter", "@sage/xtrem-finance-data/pages__bank_account____navigationPanel__listItem__line_4__title": "Währung", "@sage/xtrem-finance-data/pages__bank_account____navigationPanel__listItem__line3__title": "Buchhaltungsstandort", "@sage/xtrem-finance-data/pages__bank_account____navigationPanel__listItem__line4__title": "Währung", "@sage/xtrem-finance-data/pages__bank_account____objectTypePlural": "Bankkonten", "@sage/xtrem-finance-data/pages__bank_account____objectTypeSingular": "Bankkonto", "@sage/xtrem-finance-data/pages__bank_account____title": "Bankkonto", "@sage/xtrem-finance-data/pages__bank_account__currency____columns__title__id": "ISO 4217-Code", "@sage/xtrem-finance-data/pages__bank_account__currency____columns__title__symbol": "Symbol", "@sage/xtrem-finance-data/pages__bank_account__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-finance-data/pages__bank_account__currency____placeholder": "Währung auswählen", "@sage/xtrem-finance-data/pages__bank_account__currency____title": "Währung", "@sage/xtrem-finance-data/pages__bank_account__financialSite____columns__title__currency__name": "Währung", "@sage/xtrem-finance-data/pages__bank_account__financialSite____columns__title__id": "ID ", "@sage/xtrem-finance-data/pages__bank_account__financialSite____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__bank_account__financialSite____lookupDialogTitle": "Buchhaltungsstandort auswählen", "@sage/xtrem-finance-data/pages__bank_account__financialSite____title": "Buchhaltungsstandort", "@sage/xtrem-finance-data/pages__bank_account__headerSection____title": "Kopfberei<PERSON>", "@sage/xtrem-finance-data/pages__bank_account__id____title": "ID", "@sage/xtrem-finance-data/pages__bank_account__isActive____title": "Aktiv", "@sage/xtrem-finance-data/pages__bank_account__mainSection____title": "Informationen", "@sage/xtrem-finance-data/pages__bank_account__name____title": "Name", "@sage/xtrem-finance-data/pages__bank-account__option_menu____title__all": "Alle", "@sage/xtrem-finance-data/pages__business_entity_customer__datev_id": "DATEV-ID", "@sage/xtrem-finance-data/pages__business_entity_customer__wrong_datev_id": "Erfassen Sie eine Nummer zwischen {{first}} und {{last}}.", "@sage/xtrem-finance-data/pages__business_entity_customer_extension__datev_id_warning": "Wenn die DATEV-Integration aktiv ist, müssen Sie vor der Extraktion der Daten die DATEV-ID erfassen.", "@sage/xtrem-finance-data/pages__company__dimensions_setup_update_confirmation": "Einstellungen bestätigen", "@sage/xtrem-finance-data/pages__company__dimensions_setup_update_confirmation_apply": "<PERSON>e sind dabei, diese Einstellungen auf alle ausstehenden und zukünftigen Dokumente anzuwenden.", "@sage/xtrem-finance-data/pages__company_extension__duplicate_error": "<PERSON>s gibt doppelte Dokumente. <PERSON><PERSON><PERSON>, dass jede Zeile ein eindeutiges Dokument hat.", "@sage/xtrem-finance-data/pages__company_extension__project_task_warning": "Das Attribut {{attributeName}} ist er<PERSON><PERSON><PERSON>, wenn Sie einen Wert für {{restrictedToName}} erfassen.", "@sage/xtrem-finance-data/pages__datev_configuration____title": "DATEV-Konfiguration", "@sage/xtrem-finance-data/pages__datev_configuration__customer_id_range": "Von {{start}} bis {{end}}", "@sage/xtrem-finance-data/pages__datev_configuration__customerIdRange____title": "<PERSON><PERSON><PERSON>-<PERSON>", "@sage/xtrem-finance-data/pages__datev_configuration__customerSupplierLength____title": "Länge Kunden- und Lieferanten-ID", "@sage/xtrem-finance-data/pages__datev_configuration__isActive____title": "Aktiv", "@sage/xtrem-finance-data/pages__datev_configuration__mainSection____title": "Allgemein", "@sage/xtrem-finance-data/pages__datev_configuration__save____title": "Speichern", "@sage/xtrem-finance-data/pages__datev_configuration__skrCoaString____title": "SKR", "@sage/xtrem-finance-data/pages__datev_configuration__supplier_id_range": "Von {{start}} bis {{end}}", "@sage/xtrem-finance-data/pages__datev_configuration__supplierIdRange____title": "Bereich Lieferanten-ID", "@sage/xtrem-finance-data/pages__datev_configuration_save_warnings": "Warnungen beim S<PERSON>ichern:", "@sage/xtrem-finance-data/pages__dimension____navigationPanel__listItem__line3__title": "Dimension", "@sage/xtrem-finance-data/pages__dimension____objectTypePlural": "Sektoren", "@sage/xtrem-finance-data/pages__dimension____objectTypeSingular": "Se<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension____title": "Se<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension__dimensionType____lookupDialogTitle": "Dimension auswählen", "@sage/xtrem-finance-data/pages__dimension__id____title": "ID", "@sage/xtrem-finance-data/pages__dimension__isActive____title": "Aktiv", "@sage/xtrem-finance-data/pages__dimension__mainSection____title": "Allgemein", "@sage/xtrem-finance-data/pages__dimension__name____title": "Name", "@sage/xtrem-finance-data/pages__dimension_panel____title": "Sektoren", "@sage/xtrem-finance-data/pages__dimension_panel__apply____title": "Auf alle Zeilen anwenden", "@sage/xtrem-finance-data/pages__dimension_panel__applyAll____title": "Auf alle anwenden", "@sage/xtrem-finance-data/pages__dimension_panel__applyOnNew____title": "<PERSON>ur auf neue Zeilen anwenden", "@sage/xtrem-finance-data/pages__dimension_panel__applyReleasedItem____title": "Nur auf freigegebenen Artikel anwenden", "@sage/xtrem-finance-data/pages__dimension_panel__cancel____title": "Abbrechen", "@sage/xtrem-finance-data/pages__dimension_panel__defaulted_from_item_plural": "Die Werte von {{itemList}} sind Standardwerte des Artikels und werden auf dieser Ebene nicht angezeigt. Sie müssen diese Werte in der Zeile für jeden Artikel erfassen.", "@sage/xtrem-finance-data/pages__dimension_panel__defaulted_from_item_singular": "Der Wert von {{itemList}} ist ein Standardwert des Artikels und wird auf dieser Ebene nicht angezeigt. <PERSON>e müssen diesen Wert in der Zeile für jeden Artikel erfassen.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____lookupDialogTitle": "Sektor 01 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____title": "Sektor 01", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____lookupDialogTitle": "Sektor 02 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____title": "Sektor 02", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____lookupDialogTitle": "Sektor 03 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____title": "Sektor 03", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____lookupDialogTitle": "Sektor 04 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____title": "Sektor 04", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____lookupDialogTitle": "Sektor 05 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____title": "Sektor 05", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____lookupDialogTitle": "Sektor 06 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____title": "Sektor 06", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____lookupDialogTitle": "Sektor 07 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____title": "Sektor 07", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____lookupDialogTitle": "Sektor 08 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____title": "Sektor 08", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____lookupDialogTitle": "Sektor 09 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____title": "Sektor 09", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____lookupDialogTitle": "Sektor 10 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____title": "Sektor 10", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____lookupDialogTitle": "Sektor 11 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____title": "Sektor 11", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____lookupDialogTitle": "Sektor 12 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____title": "Sektor 12", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____lookupDialogTitle": "Sektor 13 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____title": "Sektor 13", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____lookupDialogTitle": "Sektor 14 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____title": "Sektor 14", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____lookupDialogTitle": "Sektor 15 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____title": "Sektor 15", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____lookupDialogTitle": "Sektor 16 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____title": "Sektor 16", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____lookupDialogTitle": "Sektor 17 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____title": "Sektor 17", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____lookupDialogTitle": "Sektor 18 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____title": "Sektor 18", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____lookupDialogTitle": "Sektor 19 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____title": "Sektor 19", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__columns__dimensionType__docProperty__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__title__dimensionType__docProperty": "Dimension", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____lookupDialogTitle": "Sektor 20 auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____title": "Sektor 20", "@sage/xtrem-finance-data/pages__dimension_panel__employee____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/pages__dimension_panel__employee____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__employee____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__employee____lookupDialogTitle": "Mitarbeiter auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__employee____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__ok____title": "OK", "@sage/xtrem-finance-data/pages__dimension_panel__page_without_dimensions": "Die Seite muss Attribute und Sektoren haben.", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__site__id": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__project____lookupDialogTitle": "Projekt auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__project____title": "Projekt", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__attributeType__id": "Attributtyp", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__item__id": "Artikel", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__task____lookupDialogTitle": "Aufgabe auswählen", "@sage/xtrem-finance-data/pages__dimension_panel__task____title": "Aufgabe", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__listItem__titleRight__title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-finance-data/pages__dimension_type____objectTypePlural": "Dimensionen", "@sage/xtrem-finance-data/pages__dimension_type____objectTypeSingular": "Dimension", "@sage/xtrem-finance-data/pages__dimension_type____title": "Dimension", "@sage/xtrem-finance-data/pages__dimension_type___id____title": "ID", "@sage/xtrem-finance-data/pages__dimension_type__docProperty____title": "Dokumenteigenschaft", "@sage/xtrem-finance-data/pages__dimension_type__isActive____title": "Aktiv", "@sage/xtrem-finance-data/pages__dimension_type__isUsed____title": "Verwendet", "@sage/xtrem-finance-data/pages__dimension_type__mainSection____title": "Allgemein", "@sage/xtrem-finance-data/pages__dimension_type__name____title": "Name", "@sage/xtrem-finance-data/pages__journal____navigationPanel__listItem__line6__title": "Nummernkreis", "@sage/xtrem-finance-data/pages__journal____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-finance-data/pages__journal____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-finance-data/pages__journal____objectTypePlural": "Journale", "@sage/xtrem-finance-data/pages__journal____objectTypeSingular": "Journal", "@sage/xtrem-finance-data/pages__journal____title": "Journal", "@sage/xtrem-finance-data/pages__journal___id____title": "ID", "@sage/xtrem-finance-data/pages__journal__generalSection____title": "Allgemein", "@sage/xtrem-finance-data/pages__journal__id____title": "ID", "@sage/xtrem-finance-data/pages__journal__isActive____title": "Aktiv", "@sage/xtrem-finance-data/pages__journal__isSubjectToGlTaxExcludedAmount____title": "Dem Hauptbuch exkl. Steuern zugewiesen", "@sage/xtrem-finance-data/pages__journal__legislation____lookupDialogTitle": "Rechtsordnung auswählen", "@sage/xtrem-finance-data/pages__journal__legislation____placeholder": "Auswählen ...", "@sage/xtrem-finance-data/pages__journal__name____title": "Name", "@sage/xtrem-finance-data/pages__journal__numberingBlock____title": "Nummerierung", "@sage/xtrem-finance-data/pages__journal__sequence____lookupDialogTitle": "Nummernkreis auswählen", "@sage/xtrem-finance-data/pages__journal__sequence____title": "Nummernkreis", "@sage/xtrem-finance-data/pages__journal__taxImpact____title": "Auswirkung Steuer", "@sage/xtrem-finance-data/pages__journal_entry_type____objectTypePlural": "Buchungstypen", "@sage/xtrem-finance-data/pages__journal_entry_type____objectTypeSingular": "Buchungstyp", "@sage/xtrem-finance-data/pages__journal_entry_type____title": "Buchungstyp", "@sage/xtrem-finance-data/pages__journal_entry_type___id____title": "ID", "@sage/xtrem-finance-data/pages__journal_entry_type__documentType____title": "Dokumenttyp", "@sage/xtrem-finance-data/pages__journal_entry_type__generalSection____title": "Allgemein", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____columns__title__accountTypeName": "Name", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____columns__title__isDetailed": "Detailliert", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____columns__title__postingClassType": "Typ Buchungsklasse", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____lookupDialogTitle": "<PERSON><PERSON><PERSON> au<PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAmountType____title": "Betragstyp", "@sage/xtrem-finance-data/pages__journal_entry_type__headerDescription____title": "Bezeichnung", "@sage/xtrem-finance-data/pages__journal_entry_type__headerJournal____lookupDialogTitle": "Journal auswählen", "@sage/xtrem-finance-data/pages__journal_entry_type__headerJournal____title": "Journal", "@sage/xtrem-finance-data/pages__journal_entry_type__headerPostingDate____title": "Buchungsdatum", "@sage/xtrem-finance-data/pages__journal_entry_type__headerSetupBlock____title": "Einstellungen Kopfzeile", "@sage/xtrem-finance-data/pages__journal_entry_type__immediatePosting____title": "Sofortbuchung", "@sage/xtrem-finance-data/pages__journal_entry_type__isActive____title": "Aktiv", "@sage/xtrem-finance-data/pages__journal_entry_type__legislation____lookupDialogTitle": "Rechtsordnung auswählen", "@sage/xtrem-finance-data/pages__journal_entry_type__legislation____placeholder": "Rechtsordnung auswählen", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__columns__accountType__accountTypeName__title": "Name", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__columns__accountType__accountTypeName__title__2": "Typ Buchungsklasse", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__columns__accountType__accountTypeName__title__3": "Detailliert", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__accountType__accountTypeName": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__amountType": "Betragstyp", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__commonReference": "Allgemeine Referenz", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__contraJournalEntryTypeLine__accountType__accountTypeName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__contraJournalEntryTypeLine__amountType": "Gegenkontobetragstyp", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isLandedCostItemAllowed": "Einstandskostenartikel", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isNonStockItemAllowed": "Nicht bestandsgeführte Artikel", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isServiceItemAllowed": "Dienstleistungsartikel", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isStockItemAllowed": "Bestandsgeführte Artikel", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__movementType": "Bewegungstyp", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__sign": "Vorzeichen", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____dropdownActions__title": "Verknüpfte Buchungsklasse", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____title": "Einstellungen Zeile", "@sage/xtrem-finance-data/pages__journal_entry_type__name____title": "Name", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____columns__title__account__composedDescription": "Ko<PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____columns__title__postingClass__name": "Name Buchungsklasse", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__account__composedDescription": "Ko<PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__account__composedDescription__2": "Ko<PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__postingClass__name": "Name Buchungsklasse", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__tax__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____columns__title__account__composedDescription": "Ko<PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____columns__title__postingClass__name": "Name Buchungsklasse", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__account__composedDescription": "Ko<PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__account__composedDescription__2": "Ko<PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__postingClass__name": "Name Buchungsklasse", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__tax__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____title": "Konten Kopfzeile", "@sage/xtrem-finance-data/pages__journal_entry_type__side_panel_linked_posting_class_title": "Verknüpfte Buchungsklasse", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelAccountTypeName____title": "Name <PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelGeneralSection____title": "", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelHeaderAccountTypeName____title": "Name <PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelHeaderBlock____title": "Kopfzeile", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelHeaderIsDetailed____title": "Detailliert", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelIsDetailed____title": "Detailliert", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelLinesBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__journal_entry_type__targetDocumentType____title": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__listItem__line3__title": "Detailliert", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__2": "Unternehmen", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__3": "Kunde", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__4": "Artikel", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__5": "Ressource", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__6": "Lieferant", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__7": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class____objectTypePlural": "Buchungsklassen", "@sage/xtrem-finance-data/pages__posting_class____objectTypeSingular": "Buchungsklasse", "@sage/xtrem-finance-data/pages__posting_class____title": "Buchungsklasse", "@sage/xtrem-finance-data/pages__posting_class___id____title": "ID", "@sage/xtrem-finance-data/pages__posting_class__accountTypes____columns__title__chartOfAccount__legislation__name": "Rechtsordnung", "@sage/xtrem-finance-data/pages__posting_class__accountTypes____columns__title__definition__accountTypeName": "Name <PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__accountTypes____title": "Kontoarten", "@sage/xtrem-finance-data/pages__posting_class__addLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__copyAccounts____title": "Konten kopieren", "@sage/xtrem-finance-data/pages__posting_class__finance_item_type_not_valid": "Der Finanzartikeltyp {{financeItemType}} ist ungültig.", "@sage/xtrem-finance-data/pages__posting_class__financeItemType____title": "Artikeltyp", "@sage/xtrem-finance-data/pages__posting_class__generalBlock____title": "Buchungsklasse", "@sage/xtrem-finance-data/pages__posting_class__generalSection____title": "Allgemein", "@sage/xtrem-finance-data/pages__posting_class__id____title": "ID", "@sage/xtrem-finance-data/pages__posting_class__isDetailed____title": "Detailliert", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__account__name__title": "Name", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__account__name__title__2": "ID", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__definition__accountTypeName__title": "Name", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__definition__accountTypeName__title__2": "ID", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__definition__accountTypeName__title__3": "Detailliert", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__account__name": "Ko<PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__accountId": "Konto-ID", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__chartOfAccount__name": "Kontenrahmen", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__definition__accountTypeName": "Name <PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isLandedCostItemAllowed": "Einstandskostenartikel", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isNonStockItemAllowed": "Nicht bestandsgeführte Artikel", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isServiceItemAllowed": "Dienstleistungsartikel", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isStockItemAllowed": "Bestandsgeführte Artikel", "@sage/xtrem-finance-data/pages__posting_class__lines____dropdownActions__title": "Löschen", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title": "Name", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__2": "ID", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__3": "Name", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__4": "ID", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__5": "Kontenrahmen", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__definition__accountTypeName__title": "Name", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__definition__accountTypeName__title__2": "ID", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__tax__name__title": "Name", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__tax__name__title__2": "ID", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__tax__name__title__3": "Land", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__id": "Konto-ID", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__id__2": "Konto-ID", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__name": "Ko<PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__name__2": "Ko<PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__chartOfAccount__name": "Kontenrahmen", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__definition__accountTypeName": "Name <PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__definition__additionalCriteria": "Zusätzliche Kriterien", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isLandedCostItemAllowed": "Einstandskostenartikel", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isNonStockItemAllowed": "Nicht bestandsgeführte Artikel", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isServiceItemAllowed": "Dienstleistungsartikel", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isStockItemAllowed": "Bestandsgeführte Artikel", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__tax__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__tax__taxCategory__name": "Steuerkategorie", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__dropdownActions__title": "Details hinzufügen", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__dropdownActions__title__2": "Löschen", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__dropdownActions__title__3": "Löschen", "@sage/xtrem-finance-data/pages__posting_class__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__loadLinesBlock____title": "Definitionen Buchungsklasse", "@sage/xtrem-finance-data/pages__posting_class__loadLinesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__name____title": "Name", "@sage/xtrem-finance-data/pages__posting_class__postingClass____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class__postingClass____lookupDialogTitle": "Buchungsklasse auswählen", "@sage/xtrem-finance-data/pages__posting_class__postingClass____placeholder": "Buchungsklasse auswählen", "@sage/xtrem-finance-data/pages__posting_class__postingClass____title": "Vorlage Buchungsklasse", "@sage/xtrem-finance-data/pages__posting_class__selectAll____title": "Alle Zeilen auswählen", "@sage/xtrem-finance-data/pages__posting_class__selectLines____title": "Zeilen auswählen", "@sage/xtrem-finance-data/pages__posting_class__type____title": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__listItem__line_4__title": "Detailliert", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__listItem__line4__title": "Detailliert", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__listItem__titleRight__title": "Rechtsordnung", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__2": "Kunde", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__3": "Artikel", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__4": "Ressource", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__5": "Lieferant", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__6": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class_definition____objectTypePlural": "Definitionen Buchungsklasse", "@sage/xtrem-finance-data/pages__posting_class_definition____objectTypeSingular": "Definition Buchungsklasse", "@sage/xtrem-finance-data/pages__posting_class_definition____title": "Definition Buchungsklasse", "@sage/xtrem-finance-data/pages__posting_class_definition___id____title": "ID", "@sage/xtrem-finance-data/pages__posting_class_definition__accountTypeName____title": "Name <PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class_definition__additionalCriteria____title": "Zusätzliche Kriterien", "@sage/xtrem-finance-data/pages__posting_class_definition__financeItemType____title": "Artikeltyp", "@sage/xtrem-finance-data/pages__posting_class_definition__generalBlock____title": "Definition Buchungsklasse", "@sage/xtrem-finance-data/pages__posting_class_definition__generalSection____title": "Allgemein", "@sage/xtrem-finance-data/pages__posting_class_definition__id____title": "ID", "@sage/xtrem-finance-data/pages__posting_class_definition__isDetailed____title": "Detailliert", "@sage/xtrem-finance-data/pages__posting_class_definition__legislation____lookupDialogTitle": "Rechtsordnung auswählen", "@sage/xtrem-finance-data/pages__posting_class_definition__legislation____placeholder": "Auswählen ...", "@sage/xtrem-finance-data/pages__posting_class_definition__postingClassType____title": "Typ Buchungsklasse", "@sage/xtrem-finance-data/pages__posting_class_duplicate__not_allowed": "Der Vorgang ist aufgrund einer Integritätsbedingung fehlgeschlagen.", "@sage/xtrem-finance-data/pages__posting-class__load_lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting-class__no_new_lines": "<PERSON><PERSON> neuen Zeilen hinzuzufügen.", "@sage/xtrem-finance-data/pages__posting-class_line_deletion": "", "@sage/xtrem-finance-data/pages__posting-class_line_deletion_title": "Löschen der Zeile bestätigen", "@sage/xtrem-finance-data/pages__posting-class-template__info": "Es wurden nur die Zeilen, die die gleichen Artikeltypen betreffen, ausgewählt.", "@sage/xtrem-finance-data/pages__posting-class-template__info_title": "Buchungsklasse", "@sage/xtrem-finance-data/pages_company_confirmation": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/permission__create__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/permission__delete__name": "Löschen", "@sage/xtrem-finance-data/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-finance-data/permission__update__name": "Aktualisieren", "@sage/xtrem-finance-data/service_options__discount_payment_tracking_option__name": "Option Zahlungsverfolgung Rabatt", "@sage/xtrem-finance-data/service_options__payment_tracking_option__intacct_is_active": "Die Zahlungsverfolgung ist nicht möglich, wenn die Intacct-Integration aktiv ist.", "@sage/xtrem-finance-data/service_options__payment_tracking_option__name": "Option Zahlungsverfolgung", "@sage/xtrem-finance-data/sys__notification_history__search": "<PERSON><PERSON>", "@sage/xtrem-finance-data/update-account-tax-management-context": "<PERSON><PERSON> sind dabei, die Kontoverwaltung auf 'Steuer' zu setzen.", "@sage/xtrem-finance-data/update-account-tax-management-title": "Bestätigen Sie die Aktualisierung der Kontosteuerverwaltung."}