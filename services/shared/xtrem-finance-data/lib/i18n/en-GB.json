{"@sage/xtrem-finance-data/activity__account__name": "Account", "@sage/xtrem-finance-data/activity__attribute__name": "Attribute", "@sage/xtrem-finance-data/activity__attribute_type__name": "Attribute type", "@sage/xtrem-finance-data/activity__bank_account__name": "Bank account", "@sage/xtrem-finance-data/activity__company_default_attribute__name": "Company default attribute", "@sage/xtrem-finance-data/activity__company_default_dimension__name": "Company default dimension", "@sage/xtrem-finance-data/activity__datev_configuration__name": "DATEV configuration", "@sage/xtrem-finance-data/activity__dimension__name": "Dimension", "@sage/xtrem-finance-data/activity__dimension_definition_level_and_default__name": "Dimension definition level and default", "@sage/xtrem-finance-data/activity__dimension_type__name": "Dimension type", "@sage/xtrem-finance-data/activity__journal__name": "Journal", "@sage/xtrem-finance-data/activity__journal_entry_type__name": "Journal entry type", "@sage/xtrem-finance-data/activity__posting_class__name": "Posting class", "@sage/xtrem-finance-data/activity__posting_class_definition__name": "Posting class definition", "@sage/xtrem-finance-data/attribute-type-deactivation-effective-dialog-title": "Select 'Save' to apply your change.", "@sage/xtrem-finance-data/base-document-item-line/set-dimension-missing-line": "Missing line to update dimension.", "@sage/xtrem-finance-data/base-document-item-line/update-dimension-posted": "You cannot update dimensions on a closed document.", "@sage/xtrem-finance-data/cancel": "Cancel", "@sage/xtrem-finance-data/cannot_set_tax_with_account_not_subjected_to_taxes": "You cannot set a tax detail with an account that is not subjected to taxes.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_account": "The {{account}} account could not be found.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_address": "The {{address}} address could not be found.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_base_document_line": "The base document line {{baseDocumentLine}} could not be found.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_base_tax": "The tax line {{baseTax}} could not be found.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_customer": "The {{customer}} customer could not be found.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_financial_site": "The {{financialSite}} site could not be found.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_item": "The {{item}} item could not be found.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_payment_term": "The {{paymentTerm}} payment term could not be found.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_posting_class_ap_ar_invoice": "The posting class for the document {{documentType}} {{documentNumber}} and item {{itemId}} could not be found. The posting class type is: {{postingClassType}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_posting_class_journal_entry": "The posting class for the document {{documentType}} {{documentNumber}} could not be found. The posting class type is: {{postingClassType}}.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_resource": "The {{resource}} resource could not be found.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_stock_journal": "The stock journal {{stockJournal}} could not be found.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_supplier": "The {{supplier}} supplier could not be found.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_tax": "The {{tax}} tax could not be found.", "@sage/xtrem-finance-data/classes__localized-messages__cant_read_transaction_currency": "The {{transactionCurrency}} currency could not be found.", "@sage/xtrem-finance-data/classes__localized-messages__item_posting_class_missing_on_item": "Enter a posting class for the {{item}} item.", "@sage/xtrem-finance-data/classes__localized-messages__no_bp_account": "{{documentType}} {{documentNumber}}: The account could not be found for the {{businessPartner}} business entity.", "@sage/xtrem-finance-data/classes__localized-messages__no_finance_document_lines_generated_for_item": "The account cannot be determined for the {{item}} item, {{journalEntryType}} journal entry type and {{movementType}} movement type.", "@sage/xtrem-finance-data/classes__localized-messages__resource_posting_class_missing_on_resource": "Enter a posting class for the {{resource}} resource.", "@sage/xtrem-finance-data/classes__localized-messages__tax_posting_class_missing_on_tax": "Enter a posting class for the {{tax}} tax.", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_attribute_type": "You need to select a {{attribute}} on line {{line}} .", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type": "You need to select a {{dimension}} on line {{line}}.", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type_item_tax_amount_false": "You need to select the {{level}} dimension [{{dimension}}] for this item: {{item}} on document: {{sourceDocumentNumber}}.", "@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type_item_tax_amount_True": "You need to select the {{level}} dimension [{{dimension}}] for this item: {{item}} on document: {{sourceDocumentNumber}} (Tax details).", "@sage/xtrem-finance-data/confirm-dialog-content": "You are about to apply this posting setup to all future transactions.", "@sage/xtrem-finance-data/confirm-dialog-title": "Confirm setup", "@sage/xtrem-finance-data/confirm-update": "Confirm update", "@sage/xtrem-finance-data/data_types__accounts_payable_receivable_invoice_document_line_type_enum__name": "Accounts payable receivable invoice document line type enum", "@sage/xtrem-finance-data/data_types__accounts_payable_receivable_invoice_line_type_enum__name": "Accounts payable receivable invoice line type enum", "@sage/xtrem-finance-data/data_types__accounts_payable_receivable_invoice_origin_enum__name": "Accounts payable receivable invoice origin enum", "@sage/xtrem-finance-data/data_types__amount_type_enum__name": "Amount type enum", "@sage/xtrem-finance-data/data_types__analytical_measure_type_enum__name": "Analytical measure type enum", "@sage/xtrem-finance-data/data_types__attribute__name": "Attribute", "@sage/xtrem-finance-data/data_types__attribute_dimension_type_level_enum__name": "Attribute dimension type level enum", "@sage/xtrem-finance-data/data_types__attribute_type__name": "Attribute type", "@sage/xtrem-finance-data/data_types__bank_account__name": "Bank account", "@sage/xtrem-finance-data/data_types__bank_account_type_enum__name": "Bank account type enum", "@sage/xtrem-finance-data/data_types__close_reason__name": "Close reason", "@sage/xtrem-finance-data/data_types__common_reference_enum__name": "Common reference enum", "@sage/xtrem-finance-data/data_types__dimension__name": "Dimension", "@sage/xtrem-finance-data/data_types__dimension_definition_level_enum__name": "Dimension definition level enum", "@sage/xtrem-finance-data/data_types__dimension_type__name": "Dimension type", "@sage/xtrem-finance-data/data_types__doc_property_enum__name": "Document property enum", "@sage/xtrem-finance-data/data_types__finance_document_type_enum__name": "Finance document type enum", "@sage/xtrem-finance-data/data_types__finance_integration_app_enum__name": "Finance integration application enum", "@sage/xtrem-finance-data/data_types__finance_integration_status_enum__name": "Finance integration status enum", "@sage/xtrem-finance-data/data_types__finance_item_type_enum__name": "Finance item type enum", "@sage/xtrem-finance-data/data_types__header_description_enum__name": "Header description enum", "@sage/xtrem-finance-data/data_types__header_posting_date_enum__name": "Header posting date enum", "@sage/xtrem-finance-data/data_types__item_stock_management_criteria_enum__name": "Item stock management criteria enum", "@sage/xtrem-finance-data/data_types__journal_origin_enum__name": "Journal origin enum", "@sage/xtrem-finance-data/data_types__journal_status_enum__name": "Journal status enum", "@sage/xtrem-finance-data/data_types__master_data_default_enum__name": "Master data default enum", "@sage/xtrem-finance-data/data_types__movement_type_enum__name": "Movement type enum", "@sage/xtrem-finance-data/data_types__node_link_enum__name": "Node link enum", "@sage/xtrem-finance-data/data_types__open_item_status_enum__name": "Open item status enum", "@sage/xtrem-finance-data/data_types__posting_class__name": "Posting class", "@sage/xtrem-finance-data/data_types__posting_class_type_enum__name": "Posting class type enum", "@sage/xtrem-finance-data/data_types__posting_status_enum__name": "Posting status enum", "@sage/xtrem-finance-data/data_types__sign_enum__name": "Sign enum", "@sage/xtrem-finance-data/data_types__source_document_type_enum__name": "Source document type enum", "@sage/xtrem-finance-data/data_types__stored_attributes_data_type__name": "Stored attributes data type", "@sage/xtrem-finance-data/data_types__stored_dimensions_data_type__name": "Stored dimensions data type", "@sage/xtrem-finance-data/data_types__target_document_type_enum__name": "Target document type enum", "@sage/xtrem-finance-data/data_types__tax_engine_enum__name": "Tax engine enum", "@sage/xtrem-finance-data/data_types__tax_management_enum__name": "Tax management enum", "@sage/xtrem-finance-data/data_types__validation_severity_data_type__name": "Validation severity data type", "@sage/xtrem-finance-data/deactivation-dialog-content": "You are about to deactivate this attribute type here and in all the documents using it.", "@sage/xtrem-finance-data/deactivation-dialog-title": "Confirm deactivation", "@sage/xtrem-finance-data/dimension-deactivation-dialog-content": "You are about to deactivate this dimension type here and in all the documents using it.", "@sage/xtrem-finance-data/dimension-deactivation-dialog-title": "Confirm deactivation", "@sage/xtrem-finance-data/dimension-type-deactivation-effective-dialog-title": "Click 'Save' to apply your change.", "@sage/xtrem-finance-data/dimension-type-name-change-dialog-content": "You are about to rename this dimension type here and in all the documents using it.", "@sage/xtrem-finance-data/dimension-type-name-change-dialog-title": "Confirm renaming", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_document_line_type__documentLine": "Document line", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_document_line_type__taxLine": "Tax line", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_line_type__fixedAssets": "Fixed assets", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_line_type__goods": "Goods", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_line_type__services": "Services", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_origin__creditMemo": "Credit note", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_origin__direct": "Direct", "@sage/xtrem-finance-data/enums__accounts_payable_receivable_invoice_origin__invoice": "Invoice", "@sage/xtrem-finance-data/enums__amount_type__adjustmentAmount": "Adjustment amount", "@sage/xtrem-finance-data/enums__amount_type__adjustmentNonabsorbedAmount": "Adjustment non-absorbed amount", "@sage/xtrem-finance-data/enums__amount_type__amount": "Amount", "@sage/xtrem-finance-data/enums__amount_type__amountExcludingTax": "Amount excluding VAT", "@sage/xtrem-finance-data/enums__amount_type__amountIncludingTax": "Amount including VAT", "@sage/xtrem-finance-data/enums__amount_type__deductibleTaxAmount": "Deductible tax amount", "@sage/xtrem-finance-data/enums__amount_type__inTransitAmount": "In transit amount", "@sage/xtrem-finance-data/enums__amount_type__inTransitVarianceAmount": "In transit variance amount", "@sage/xtrem-finance-data/enums__amount_type__landedCostAdjustmentAmount": "Landed cost adjustment amount", "@sage/xtrem-finance-data/enums__amount_type__landedCostAdjustmentNonabsorbedAmount": "Landed cost adjustment nonabsorbed amount", "@sage/xtrem-finance-data/enums__amount_type__landedCostStockInTransitAdjustmentAmount": "Landed cost stock in transit adjustment amount", "@sage/xtrem-finance-data/enums__amount_type__landedCostStockInTransitAdjustmentNonabsorbedAmount": "Landed cost stock in transit adjustment nonabsorbed amount", "@sage/xtrem-finance-data/enums__amount_type__landedCostStockInTransitAmount": "Landed cost stock in transit amount", "@sage/xtrem-finance-data/enums__amount_type__nonDeductibleTaxAmount": "Non deductible tax amount", "@sage/xtrem-finance-data/enums__amount_type__reverseChargeDeductibleTaxAmount": "Reverse charge deductible tax amount", "@sage/xtrem-finance-data/enums__amount_type__reverseChargeNonDeductibleTaxAmount": "Reverse charge non deductible tax amount", "@sage/xtrem-finance-data/enums__amount_type__taxAmount": "Tax amount", "@sage/xtrem-finance-data/enums__amount_type__varianceAmount": "Variance amount", "@sage/xtrem-finance-data/enums__analytical_measure_type__attribute": "Attribute", "@sage/xtrem-finance-data/enums__analytical_measure_type__dimension": "Dimension", "@sage/xtrem-finance-data/enums__attribute_dimension_type_level__account": "Account", "@sage/xtrem-finance-data/enums__attribute_dimension_type_level__company": "Company", "@sage/xtrem-finance-data/enums__bank_account_type__current": "Current", "@sage/xtrem-finance-data/enums__common_reference__documentNumber": "Document number", "@sage/xtrem-finance-data/enums__common_reference__sourceDocumentNumber": "Source document number", "@sage/xtrem-finance-data/enums__dimension_definition_level__intersiteTransferOrder": "Intersite transfer order", "@sage/xtrem-finance-data/enums__dimension_definition_level__manufacturingDirect": "Manufacturing direct", "@sage/xtrem-finance-data/enums__dimension_definition_level__manufacturingOrderToOrder": "Manufacturing order-to-order", "@sage/xtrem-finance-data/enums__dimension_definition_level__purchasingDirect": "Purchasing direct", "@sage/xtrem-finance-data/enums__dimension_definition_level__purchasingOrderToOrder": "Purchasing order-to-order", "@sage/xtrem-finance-data/enums__dimension_definition_level__salesDirect": "Sales direct", "@sage/xtrem-finance-data/enums__dimension_definition_level__stockDirect": "Stock direct", "@sage/xtrem-finance-data/enums__doc_property__dimensionType01": "Dimension type 01", "@sage/xtrem-finance-data/enums__doc_property__dimensionType02": "Dimension type 02", "@sage/xtrem-finance-data/enums__doc_property__dimensionType03": "Dimension type 03", "@sage/xtrem-finance-data/enums__doc_property__dimensionType04": "Dimension type 04", "@sage/xtrem-finance-data/enums__doc_property__dimensionType05": "Dimension type 05", "@sage/xtrem-finance-data/enums__doc_property__dimensionType06": "Dimension type 06", "@sage/xtrem-finance-data/enums__doc_property__dimensionType07": "Dimension type 07", "@sage/xtrem-finance-data/enums__doc_property__dimensionType08": "Dimension type 08", "@sage/xtrem-finance-data/enums__doc_property__dimensionType09": "Dimension type 09", "@sage/xtrem-finance-data/enums__doc_property__dimensionType10": "Dimension type 10", "@sage/xtrem-finance-data/enums__doc_property__dimensionType11": "Dimension type 11", "@sage/xtrem-finance-data/enums__doc_property__dimensionType12": "Dimension type 12", "@sage/xtrem-finance-data/enums__doc_property__dimensionType13": "Dimension type 13", "@sage/xtrem-finance-data/enums__doc_property__dimensionType14": "Dimension type 14", "@sage/xtrem-finance-data/enums__doc_property__dimensionType15": "Dimension type 15", "@sage/xtrem-finance-data/enums__doc_property__dimensionType16": "Dimension type 16", "@sage/xtrem-finance-data/enums__doc_property__dimensionType17": "Dimension type 17", "@sage/xtrem-finance-data/enums__doc_property__dimensionType18": "Dimension type 18", "@sage/xtrem-finance-data/enums__doc_property__dimensionType19": "Dimension type 19", "@sage/xtrem-finance-data/enums__doc_property__dimensionType20": "Dimension type 20", "@sage/xtrem-finance-data/enums__finance_document_type__apInvoice": "AP invoice", "@sage/xtrem-finance-data/enums__finance_document_type__arInvoice": "AR invoice", "@sage/xtrem-finance-data/enums__finance_document_type__bankReconciliationDeposit": "Bank reconciliation deposit", "@sage/xtrem-finance-data/enums__finance_document_type__bankReconciliationWithdrawal": "Bank reconciliation withdrawal", "@sage/xtrem-finance-data/enums__finance_document_type__miscellaneousStockIssue": "Miscellaneous stock issue", "@sage/xtrem-finance-data/enums__finance_document_type__miscellaneousStockReceipt": "Miscellaneous stock receipt", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseCreditMemo": "Purchase credit note", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseInvoice": "Purchase invoice", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseReceipt": "Purchase receipt", "@sage/xtrem-finance-data/enums__finance_document_type__purchaseReturn": "Purchase return", "@sage/xtrem-finance-data/enums__finance_document_type__salesCreditMemo": "Sales credit note", "@sage/xtrem-finance-data/enums__finance_document_type__salesInvoice": "Sales invoice", "@sage/xtrem-finance-data/enums__finance_document_type__salesReturnReceipt": "Sales return receipt", "@sage/xtrem-finance-data/enums__finance_document_type__salesShipment": "Sales shipment", "@sage/xtrem-finance-data/enums__finance_document_type__stockAdjustment": "Stock adjustment", "@sage/xtrem-finance-data/enums__finance_document_type__stockCount": "Stock count", "@sage/xtrem-finance-data/enums__finance_document_type__stockTransferReceipt": "Stock transfer receipt", "@sage/xtrem-finance-data/enums__finance_document_type__stockTransferShipment": "Stock transfer shipment", "@sage/xtrem-finance-data/enums__finance_document_type__stockValueChange": "Stock value change", "@sage/xtrem-finance-data/enums__finance_document_type__workInProgress": "Work in progress", "@sage/xtrem-finance-data/enums__finance_integration_app__frp1000": "Sage FRP 1000", "@sage/xtrem-finance-data/enums__finance_integration_app__intacct": "Sage Intacct", "@sage/xtrem-finance-data/enums__finance_integration_status__error": "Error", "@sage/xtrem-finance-data/enums__finance_integration_status__failed": "Failed", "@sage/xtrem-finance-data/enums__finance_integration_status__notRecorded": "Not recorded", "@sage/xtrem-finance-data/enums__finance_integration_status__pending": "Pending", "@sage/xtrem-finance-data/enums__finance_integration_status__posted": "Posted", "@sage/xtrem-finance-data/enums__finance_integration_status__recorded": "Recorded", "@sage/xtrem-finance-data/enums__finance_integration_status__recording": "Recording", "@sage/xtrem-finance-data/enums__finance_integration_status__submitted": "Submitted", "@sage/xtrem-finance-data/enums__finance_integration_status__toBeRecorded": "To be recorded", "@sage/xtrem-finance-data/enums__finance_item_type__landedCostItem": "Landed cost item", "@sage/xtrem-finance-data/enums__finance_item_type__nonStockItem": "Non-stock item", "@sage/xtrem-finance-data/enums__finance_item_type__serviceItem": "Service item", "@sage/xtrem-finance-data/enums__finance_item_type__stockItem": "Stock item", "@sage/xtrem-finance-data/enums__header_description__documentNumber": "Document number", "@sage/xtrem-finance-data/enums__header_description__documentType": "Document type", "@sage/xtrem-finance-data/enums__header_description__transactionDescription": "Transaction description", "@sage/xtrem-finance-data/enums__header_posting_date__documentDate": "Document date", "@sage/xtrem-finance-data/enums__header_posting_date__endOfMonth": "End of month", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__both": "Both", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__managedInStock": "Managed in stock", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__notApplicable": "Not applicable", "@sage/xtrem-finance-data/enums__item_stock_management_criteria__notManagedInStock": "Not managed in stock", "@sage/xtrem-finance-data/enums__journal_origin__apInvoice": "AP invoice", "@sage/xtrem-finance-data/enums__journal_origin__arInvoice": "AR invoice", "@sage/xtrem-finance-data/enums__journal_origin__directEntry": "Direct entry", "@sage/xtrem-finance-data/enums__journal_origin__manufacturing": "Manufacturing", "@sage/xtrem-finance-data/enums__journal_origin__purchase": "Purchasing", "@sage/xtrem-finance-data/enums__journal_origin__sales": "Sales", "@sage/xtrem-finance-data/enums__journal_origin__stock": "Stock", "@sage/xtrem-finance-data/enums__journal_status__draft": "Draft", "@sage/xtrem-finance-data/enums__journal_status__error": "Error", "@sage/xtrem-finance-data/enums__journal_status__inProgress": "In progress", "@sage/xtrem-finance-data/enums__journal_status__posted": "Posted", "@sage/xtrem-finance-data/enums__master_data_default__customer": "Customer", "@sage/xtrem-finance-data/enums__master_data_default__item": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__master_data_default__receivingSite": "Receiving site", "@sage/xtrem-finance-data/enums__master_data_default__shippingSite": "Shipping site", "@sage/xtrem-finance-data/enums__master_data_default__site": "Site", "@sage/xtrem-finance-data/enums__master_data_default__sourceDocument": "Source document", "@sage/xtrem-finance-data/enums__master_data_default__supplier": "Supplier", "@sage/xtrem-finance-data/enums__movement_type__document": "Document", "@sage/xtrem-finance-data/enums__movement_type__laborRunTimeTracking": "Labour run time tracking", "@sage/xtrem-finance-data/enums__movement_type__laborSetupTimeTracking": "Labour setup time tracking", "@sage/xtrem-finance-data/enums__movement_type__machineRunTimeTracking": "Machine run time tracking", "@sage/xtrem-finance-data/enums__movement_type__machineSetupTimeTracking": "Machine setup time tracking", "@sage/xtrem-finance-data/enums__movement_type__materialTracking": "Material tracking", "@sage/xtrem-finance-data/enums__movement_type__productionTracking": "Production tracking", "@sage/xtrem-finance-data/enums__movement_type__stockJournal": "Stock journal", "@sage/xtrem-finance-data/enums__movement_type__toolRunTimeTracking": "Tool run time tracking", "@sage/xtrem-finance-data/enums__movement_type__toolSetupTimeTracking": "Tool setup time tracking", "@sage/xtrem-finance-data/enums__movement_type__workOrderActualCostAdjustment": "Work order actual cost adjustment", "@sage/xtrem-finance-data/enums__movement_type__workOrderActualCostAdjustmentNonAbsorbed": "Work order actual cost adjustment non-absorbed", "@sage/xtrem-finance-data/enums__movement_type__workOrderNegativeActualCostAdjustment": "Work order negative actual cost adjustment", "@sage/xtrem-finance-data/enums__movement_type__workOrderNegativeActualCostAdjustmentNonAbsorbed": "Work order negative actual cost adjustment non-absorbed", "@sage/xtrem-finance-data/enums__movement_type__workOrderNegativeVariance": "Work order negative variance", "@sage/xtrem-finance-data/enums__movement_type__workOrderVariance": "Work order variance", "@sage/xtrem-finance-data/enums__node_link__attribute": "Attribute", "@sage/xtrem-finance-data/enums__node_link__customer": "Customer", "@sage/xtrem-finance-data/enums__node_link__item": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__node_link__site": "Site", "@sage/xtrem-finance-data/enums__node_link__supplier": "Supplier", "@sage/xtrem-finance-data/enums__open_item_status__notPaid": "Not paid", "@sage/xtrem-finance-data/enums__open_item_status__paid": "Paid", "@sage/xtrem-finance-data/enums__open_item_status__partiallyPaid": "Partially paid", "@sage/xtrem-finance-data/enums__posting_class_type__company": "Company", "@sage/xtrem-finance-data/enums__posting_class_type__customer": "Customer", "@sage/xtrem-finance-data/enums__posting_class_type__header": "Header", "@sage/xtrem-finance-data/enums__posting_class_type__item": "<PERSON><PERSON>", "@sage/xtrem-finance-data/enums__posting_class_type__line": "Line", "@sage/xtrem-finance-data/enums__posting_class_type__resource": "Resource", "@sage/xtrem-finance-data/enums__posting_class_type__supplier": "Supplier", "@sage/xtrem-finance-data/enums__posting_class_type__tax": "Tax", "@sage/xtrem-finance-data/enums__posting_status__generated": "Generated", "@sage/xtrem-finance-data/enums__posting_status__generationError": "Generation error", "@sage/xtrem-finance-data/enums__posting_status__generationInProgress": "Generation in progress", "@sage/xtrem-finance-data/enums__posting_status__notPosted": "Not posted", "@sage/xtrem-finance-data/enums__posting_status__posted": "Posted", "@sage/xtrem-finance-data/enums__posting_status__postingError": "Posting error", "@sage/xtrem-finance-data/enums__posting_status__postingInProgress": "Posting in progress", "@sage/xtrem-finance-data/enums__posting_status__toBeGenerated": "To be generated", "@sage/xtrem-finance-data/enums__sign__C": "Credit", "@sage/xtrem-finance-data/enums__sign__D": "Debit", "@sage/xtrem-finance-data/enums__source_document_type__materialTracking": "Material tracking", "@sage/xtrem-finance-data/enums__source_document_type__operationTracking": "Operation tracking", "@sage/xtrem-finance-data/enums__source_document_type__productionTracking": "Production tracking", "@sage/xtrem-finance-data/enums__source_document_type__purchaseCreditMemo": "Purchase credit note", "@sage/xtrem-finance-data/enums__source_document_type__purchaseInvoice": "Purchase invoice", "@sage/xtrem-finance-data/enums__source_document_type__purchaseOrder": "Purchase order", "@sage/xtrem-finance-data/enums__source_document_type__purchaseReceipt": "Purchase receipt", "@sage/xtrem-finance-data/enums__source_document_type__purchaseReturn": "Purchase return", "@sage/xtrem-finance-data/enums__source_document_type__salesCreditMemo": "Sales credit note", "@sage/xtrem-finance-data/enums__source_document_type__salesInvoice": "Sales invoice", "@sage/xtrem-finance-data/enums__source_document_type__salesReturnRequest": "Sales return request", "@sage/xtrem-finance-data/enums__source_document_type__salesShipment": "Sales shipment", "@sage/xtrem-finance-data/enums__source_document_type__stockTransferOrder": "Stock transfer order", "@sage/xtrem-finance-data/enums__source_document_type__stockTransferReceipt": "Stock transfer receipt", "@sage/xtrem-finance-data/enums__source_document_type__stockTransferShipment": "Stock transfer shipment", "@sage/xtrem-finance-data/enums__source_document_type__workOrderClose": "Work order close", "@sage/xtrem-finance-data/enums__target_document_type__accountsPayableInvoice": "Accounts payable invoice", "@sage/xtrem-finance-data/enums__target_document_type__accountsReceivableAdvance": "Accounts receivable advance", "@sage/xtrem-finance-data/enums__target_document_type__accountsReceivableInvoice": "Accounts receivable invoice", "@sage/xtrem-finance-data/enums__target_document_type__accountsReceivablePayment": "Accounts receivable payment", "@sage/xtrem-finance-data/enums__target_document_type__journalEntry": "Journal entry", "@sage/xtrem-finance-data/enums__tax_engine__avalaraAvaTax": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/enums__tax_engine__genericTaxCalculation": "Generic tax calculation", "@sage/xtrem-finance-data/enums__tax_management__excludingTax": "Excluding VAT", "@sage/xtrem-finance-data/enums__tax_management__includingTax": "Including VAT", "@sage/xtrem-finance-data/enums__tax_management__other": "Other", "@sage/xtrem-finance-data/enums__tax_management__reverseCharge": "Reverse charge", "@sage/xtrem-finance-data/enums__tax_management__tax": "Tax", "@sage/xtrem-finance-data/error": "Error", "@sage/xtrem-finance-data/events__control__posting_class_account_control__account_is_mandatory": "You need to enter an account when there are no line details.", "@sage/xtrem-finance-data/events__control__posting_class_line_detail__secondary_criteria_is_tax": "You need to enter the tax code when the secondary posting class line is tax.", "@sage/xtrem-finance-data/events__posting_class_definition__this_posting_class_definition_has_details": "You need to delete the posting class line details for this definition before removing the Additional criteria.", "@sage/xtrem-finance-data/functions__accounting_engine__journals_created": "Journals created: {{journalsCreated}}", "@sage/xtrem-finance-data/functions__default_dimension_lib__wrong_source_for_dimensions": "You cannot assign the default {{masterDataDefault}} to this document {{dimensionDefinitionLevel}}.", "@sage/xtrem-finance-data/functions__dimensions__attribute_type_restricted_to": "The {{attributeTypeRestrictedToName}} attribute needs to be filled in.", "@sage/xtrem-finance-data/is_active_business_site_attribute_type_or_value_inactive": "The {{businessSite}} site or business site attribute type is inactive or the site is not defined as a business site.", "@sage/xtrem-finance-data/is_active_customer_attribute_type_or_value_inactive": "The {{customer}} customer or customer attribute type is inactive.", "@sage/xtrem-finance-data/is_active_dimension_inactive": "The {{dimension}} dimension or {{dimensionType}} dimension type is inactive.", "@sage/xtrem-finance-data/is_active_employee_attribute_inactive": "The {{employeeAttribute}} attribute or the employee attribute type is inactive.", "@sage/xtrem-finance-data/is_active_financial_site_attribute_type_or_value_inactive": "The {{financialSite}} site or financial site attribute type is inactive or the site is not defined as a financial site.", "@sage/xtrem-finance-data/is_active_invalid_attribute": "Incorrect attribute: {{other}}", "@sage/xtrem-finance-data/is_active_item_attribute_type_or_value_inactive": "The {{item}} item or item attribute type is inactive.", "@sage/xtrem-finance-data/is_active_manufacturing_site_attribute_type_or_value_inactive": "The {{manufacturingSite}} site or manufacturing site attribute type is inactive or the site is not defined as a manufacturing site.", "@sage/xtrem-finance-data/is_active_project_attribute_inactive": "The {{projectAttribute}} attribute or the 'Project' attribute type is inactive.", "@sage/xtrem-finance-data/is_active_stock_site_attribute_type_or_value_inactive": "The {{stockSite}} site or stock site attribute type is inactive or the site is not defined as a stock site.", "@sage/xtrem-finance-data/is_active_supplier_attribute_type_or_value_inactive": "The {{supplier}} supplier or supplier attribute type is inactive.", "@sage/xtrem-finance-data/is_active_task_attribute_inactive": "The {{taskAttribute}} attribute or \"Task\" attribute type is inactive.", "@sage/xtrem-finance-data/journal_entry_type_line_legislations_mismatch": "The account type legislation {{postingClassLegislation}} and journal entry type legislation {{typeLegislation}} must be the same.", "@sage/xtrem-finance-data/menu_item__features-financial-integration": "Finance", "@sage/xtrem-finance-data/menu_item__payment-tracking": "Payment tracking", "@sage/xtrem-finance-data/node-extensions__base_business_relation_extension__property__datevId": "DATEV ID", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__financeIntegrationRecords": "Finance integration records", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__financeIntegrationStatus": "Finance integration status", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__forceUpdateForFinance": "Force update for finance", "@sage/xtrem-finance-data/node-extensions__base_document_extension__property__wasTaxDataChanged": "Was tax data changed", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension": "Set dimension", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__failed": "Set dimension failed.", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__parameter__baseDocumentItemLine": "Base document item line", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__parameter__storedAttributes": "Stored attributes", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__mutation__setDimension__parameter__storedDimensions": "Stored dimensions", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__analyticalData": "Analytical data", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__computedAttributes": "Computed attributes", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__dimensionDefinitionLevel": "Dimension definition level", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__storedAttributes": "Stored attributes", "@sage/xtrem-finance-data/node-extensions__base_document_item_line_extension__property__storedDimensions": "Stored dimensions", "@sage/xtrem-finance-data/node-extensions__base_document_line_inquiry_extension__property__postingClass": "Posting class", "@sage/xtrem-finance-data/node-extensions__company_extension__property__attributeTypes": "Attribute types", "@sage/xtrem-finance-data/node-extensions__company_extension__property__bankAccount": "Bank account", "@sage/xtrem-finance-data/node-extensions__company_extension__property__datevConsultantNumber": "DATEV consultant number", "@sage/xtrem-finance-data/node-extensions__company_extension__property__datevCustomerNumber": "DATEV customer number", "@sage/xtrem-finance-data/node-extensions__company_extension__property__defaultAttributes": "De<PERSON>ult attributes", "@sage/xtrem-finance-data/node-extensions__company_extension__property__defaultDimensions": "Default dimensions", "@sage/xtrem-finance-data/node-extensions__company_extension__property__dimensionTypes": "Dimension types", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doApPosting": "AP posting", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doArPosting": "AR posting", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doNonAbsorbedPosting": "Do non absorbed posting", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doStockPosting": "Stock posting", "@sage/xtrem-finance-data/node-extensions__company_extension__property__doWipPosting": "WIP posting", "@sage/xtrem-finance-data/node-extensions__company_extension__property__postingClass": "Posting class", "@sage/xtrem-finance-data/node-extensions__company_extension__property__taxEngine": "Tax engine", "@sage/xtrem-finance-data/node-extensions__company-extension__do_non_absorbed_posting_not_allowed": "Non absorbed posting not allowed if there is no stock posting.", "@sage/xtrem-finance-data/node-extensions__company-extension__do_non_absorbed_posting_not_allowed_on_legislation": "You cannot post non-absorbed amounts for this legislation.", "@sage/xtrem-finance-data/node-extensions__company-extension__do_wip_posting_not_allowed_on_legislation": "You cannot post WIP for this legislation.", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__analyticalData": "Analytical data", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__postingClass": "Posting class", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__storedAttributes": "Stored attributes", "@sage/xtrem-finance-data/node-extensions__customer_extension__property__storedDimensions": "Stored dimensions", "@sage/xtrem-finance-data/node-extensions__detailed_resource_extension__property__postingClass": "Posting class", "@sage/xtrem-finance-data/node-extensions__item_extension__property__analyticalData": "Analytical data", "@sage/xtrem-finance-data/node-extensions__item_extension__property__postingClass": "Posting class", "@sage/xtrem-finance-data/node-extensions__item_extension__property__storedAttributes": "Stored attributes", "@sage/xtrem-finance-data/node-extensions__item_extension__property__storedDimensions": "Stored dimensions", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doApPosting": "Do <PERSON> posting", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doArPosting": "Do <PERSON> posting", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doLandedCostGoodsInTransitPosting": "Do landed cost goods in transit posting", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doNonAbsorbedPosting": "Do non absorbed posting", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doNonStockVariancePosting": "", "@sage/xtrem-finance-data/node-extensions__legislation_extension__property__doStockPosting": "Do stock posting", "@sage/xtrem-finance-data/node-extensions__site_extension__property__analyticalData": "Analytical data", "@sage/xtrem-finance-data/node-extensions__site_extension__property__storedAttributes": "Stored attributes", "@sage/xtrem-finance-data/node-extensions__site_extension__property__storedDimensions": "Stored dimensions", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__analyticalData": "Analytical data", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__postingClass": "Posting class", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__storedAttributes": "Stored attributes", "@sage/xtrem-finance-data/node-extensions__supplier_extension__property__storedDimensions": "Stored dimensions", "@sage/xtrem-finance-data/node-extensions__tax_extension__property__postingClass": "Posting class", "@sage/xtrem-finance-data/node-extensions__tax_extension__property__postingKey": "Posting key", "@sage/xtrem-finance-data/nodes__account__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__account__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__account__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__account__datev_id_invalid": "The DATEV ID needs to be a number between {{fromValue}} and {{toValue}}.", "@sage/xtrem-finance-data/nodes__account__datev_id_not_unique": "The DATEV ID needs to be unique.", "@sage/xtrem-finance-data/nodes__account__datev_tax_invalid": "You need to assign a tax code linked with one of these countries: {{countryIds}}.", "@sage/xtrem-finance-data/nodes__account__node_name": "Account", "@sage/xtrem-finance-data/nodes__account__property__attributeTypes": "Attribute types", "@sage/xtrem-finance-data/nodes__account__property__chartOfAccount": "Chart of accounts", "@sage/xtrem-finance-data/nodes__account__property__composedDescription": "Composed description", "@sage/xtrem-finance-data/nodes__account__property__datevId": "DATEV ID", "@sage/xtrem-finance-data/nodes__account__property__dimensionTypes": "Dimension types", "@sage/xtrem-finance-data/nodes__account__property__id": "ID", "@sage/xtrem-finance-data/nodes__account__property__isActive": "Active", "@sage/xtrem-finance-data/nodes__account__property__isAutomaticAccount": "Is automatic account", "@sage/xtrem-finance-data/nodes__account__property__isControl": "Control", "@sage/xtrem-finance-data/nodes__account__property__isDirectEntryForbidden": "Direct entry forbidden", "@sage/xtrem-finance-data/nodes__account__property__name": "Name", "@sage/xtrem-finance-data/nodes__account__property__tax": "Tax", "@sage/xtrem-finance-data/nodes__account__property__taxManagement": "Tax management", "@sage/xtrem-finance-data/nodes__account__tax_management_control": "When tax information is excluded from journal entries, you need to set tax management to 'Other.'", "@sage/xtrem-finance-data/nodes__account_attribute_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__account_attribute_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__account_attribute_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__account_attribute_type__attribute-type-not-active": "The attribute type is inactive.", "@sage/xtrem-finance-data/nodes__account_attribute_type__node_name": "Account attribute type", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__account": "Account", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__analyticalMeasureType": "Analytical measure type", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__attributeType": "Attribute type", "@sage/xtrem-finance-data/nodes__account_attribute_type__property__isRequired": "Required", "@sage/xtrem-finance-data/nodes__account_dimension_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__account_dimension_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__account_dimension_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__account_dimension_type__dimension-type-not-active": "The dimension type is inactive.", "@sage/xtrem-finance-data/nodes__account_dimension_type__node_name": "Account dimension type", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__account": "Account", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__analyticalMeasureType": "Analytical measure type", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__dimensionType": "Dimension type", "@sage/xtrem-finance-data/nodes__account_dimension_type__property__isRequired": "Required", "@sage/xtrem-finance-data/nodes__accounting_staging__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounting_staging__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__accounting_staging__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__accounting_staging__node_name": "Accounting staging", "@sage/xtrem-finance-data/nodes__accounting_staging__property__account": "Account", "@sage/xtrem-finance-data/nodes__accounting_staging__property__amounts": "Amounts", "@sage/xtrem-finance-data/nodes__accounting_staging__property__analyticalData": "Analytical data", "@sage/xtrem-finance-data/nodes__accounting_staging__property__baseDocumentLine": "Base document line", "@sage/xtrem-finance-data/nodes__accounting_staging__property__batchId": "Batch ID", "@sage/xtrem-finance-data/nodes__accounting_staging__property__batchSize": "Batch size", "@sage/xtrem-finance-data/nodes__accounting_staging__property__companyFxRate": "Company exchange rate", "@sage/xtrem-finance-data/nodes__accounting_staging__property__companyFxRateDivisor": "Company exchange rate divisor", "@sage/xtrem-finance-data/nodes__accounting_staging__property__customer": "Customer", "@sage/xtrem-finance-data/nodes__accounting_staging__property__customerPostingClass": "Customer posting class", "@sage/xtrem-finance-data/nodes__accounting_staging__property__description": "Description", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentDate": "Document date", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentNumber": "Document number", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentSysId": "Document system ID", "@sage/xtrem-finance-data/nodes__accounting_staging__property__documentType": "Document type", "@sage/xtrem-finance-data/nodes__accounting_staging__property__dueDate": "Due date", "@sage/xtrem-finance-data/nodes__accounting_staging__property__financialSite": "Financial site", "@sage/xtrem-finance-data/nodes__accounting_staging__property__fxRateDate": "Exchange rate date", "@sage/xtrem-finance-data/nodes__accounting_staging__property__isPrinted": "Is printed", "@sage/xtrem-finance-data/nodes__accounting_staging__property__isProcessed": "Is processed", "@sage/xtrem-finance-data/nodes__accounting_staging__property__item": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__accounting_staging__property__itemPostingClass": "Item posting class", "@sage/xtrem-finance-data/nodes__accounting_staging__property__movementType": "Movement type", "@sage/xtrem-finance-data/nodes__accounting_staging__property__originNotificationId": "Origin notification ID", "@sage/xtrem-finance-data/nodes__accounting_staging__property__paymentTerm": "Payment term", "@sage/xtrem-finance-data/nodes__accounting_staging__property__payToSupplier": "Pay-to supplier", "@sage/xtrem-finance-data/nodes__accounting_staging__property__payToSupplierLinkedAddress": "Pay-to supplier linked address", "@sage/xtrem-finance-data/nodes__accounting_staging__property__providerSite": "Provider site", "@sage/xtrem-finance-data/nodes__accounting_staging__property__recipientSite": "Recipient site", "@sage/xtrem-finance-data/nodes__accounting_staging__property__replyTopic": "Reply topic", "@sage/xtrem-finance-data/nodes__accounting_staging__property__resource": "Resource", "@sage/xtrem-finance-data/nodes__accounting_staging__property__resourcePostingClass": "Resource posting class", "@sage/xtrem-finance-data/nodes__accounting_staging__property__returnLinkedAddress": "Return linked address", "@sage/xtrem-finance-data/nodes__accounting_staging__property__sourceBaseDocumentLine": "Source base document line", "@sage/xtrem-finance-data/nodes__accounting_staging__property__sourceDocumentNumber": "Source document number", "@sage/xtrem-finance-data/nodes__accounting_staging__property__sourceDocumentType": "Source document type", "@sage/xtrem-finance-data/nodes__accounting_staging__property__stockJournal": "Stock journal", "@sage/xtrem-finance-data/nodes__accounting_staging__property__storedAttributes": "Stored attributes", "@sage/xtrem-finance-data/nodes__accounting_staging__property__storedComputedAttributes": "Stored computed attributes", "@sage/xtrem-finance-data/nodes__accounting_staging__property__storedDimensions": "Stored dimensions", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplier": "Supplier", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplierDocumentDate": "Supplier document date", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplierDocumentNumber": "Supplier document number", "@sage/xtrem-finance-data/nodes__accounting_staging__property__supplierPostingClass": "Supplier posting class", "@sage/xtrem-finance-data/nodes__accounting_staging__property__targetDocumentType": "Target document type", "@sage/xtrem-finance-data/nodes__accounting_staging__property__taxCalculationStatus": "Tax calculation status", "@sage/xtrem-finance-data/nodes__accounting_staging__property__taxDate": "Tax date", "@sage/xtrem-finance-data/nodes__accounting_staging__property__taxes": "Taxes", "@sage/xtrem-finance-data/nodes__accounting_staging__property__toBeReprocessed": "To be reprocessed", "@sage/xtrem-finance-data/nodes__accounting_staging__property__transactionCurrency": "Transaction currency", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__node_name": "Accounting staging amount", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__accountingStaging": "Accounting staging", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__amount": "Amount", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__amountType": "Amount type", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__baseTax": "Base tax", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__documentLineType": "Document line type", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__tax": "Tax", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__taxDate": "Tax date", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__taxPostingClass": "Tax posting class", "@sage/xtrem-finance-data/nodes__accounting_staging_amount__property__taxRate": "Tax rate", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__node_name": "Accounting staging document tax", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__baseTax": "Base tax", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__batchId": "Batch ID", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__documentNumber": "Document number", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__documentType": "Document type", "@sage/xtrem-finance-data/nodes__accounting_staging_document_tax__property__targetDocumentType": "Target document type", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__node_name": "Accounting staging line tax", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__property__accountingStaging": "Accounting staging", "@sage/xtrem-finance-data/nodes__accounting_staging_line_tax__property__baseTax": "Base tax", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__node_name": "Accounts payable invoice line staging", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__property__accountingStaging": "Accounting staging", "@sage/xtrem-finance-data/nodes__accounts_payable_invoice_line_staging__property__accountsPayableInvoiceLine": "Accounts payable invoice line", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__node_name": "Accounts receivable invoice line staging", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__property__accountingStaging": "Accounting staging", "@sage/xtrem-finance-data/nodes__accounts_receivable_invoice_line_staging__property__accountsReceivableInvoiceLine": "Accounts receivable invoice line", "@sage/xtrem-finance-data/nodes__analytical_data__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__analytical_data__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__analytical_data__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__analytical_data__node_name": "Analytical data", "@sage/xtrem-finance-data/nodes__analytical_data__property__businessSite": "Business site", "@sage/xtrem-finance-data/nodes__analytical_data__property__businessSiteType": "Business site type", "@sage/xtrem-finance-data/nodes__analytical_data__property__customer": "Customer", "@sage/xtrem-finance-data/nodes__analytical_data__property__customerType": "Customer type", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension01": "Dimension 01", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension02": "Dimension 02", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension03": "Dimension 03", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension04": "Dimension 04", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension05": "Dimension 05", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension06": "Dimension 06", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension07": "Dimension 07", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension08": "Dimension 08", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension09": "Dimension 09", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension10": "Dimension 10", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension11": "Dimension 11", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension12": "Dimension 12", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension13": "Dimension 13", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension14": "Dimension 14", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension15": "Dimension 15", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension16": "Dimension 16", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension17": "Dimension 17", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension18": "Dimension 18", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension19": "Dimension 19", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimension20": "Dimension 20", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType01": "Dimension type 01", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType02": "Dimension type 02", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType03": "Dimension type 03", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType04": "Dimension type 04", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType05": "Dimension type 05", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType06": "Dimension type 06", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType07": "Dimension type 07", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType08": "Dimension type 08", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType09": "Dimension type 09", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType10": "Dimension type 10", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType11": "Dimension type 11", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType12": "Dimension type 12", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType13": "Dimension type 13", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType14": "Dimension type 14", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType15": "Dimension type 15", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType16": "Dimension type 16", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType17": "Dimension type 17", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType18": "Dimension type 18", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType19": "Dimension type 19", "@sage/xtrem-finance-data/nodes__analytical_data__property__dimensionType20": "Dimension type 20", "@sage/xtrem-finance-data/nodes__analytical_data__property__employee": "Employee", "@sage/xtrem-finance-data/nodes__analytical_data__property__employeeType": "Employee type", "@sage/xtrem-finance-data/nodes__analytical_data__property__financialSite": "Financial site", "@sage/xtrem-finance-data/nodes__analytical_data__property__financialSiteType": "Financial site type", "@sage/xtrem-finance-data/nodes__analytical_data__property__item": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__analytical_data__property__itemType": "Item type", "@sage/xtrem-finance-data/nodes__analytical_data__property__manufacturingSite": "Manufacturing site", "@sage/xtrem-finance-data/nodes__analytical_data__property__manufacturingSiteType": "Manufacturing site type", "@sage/xtrem-finance-data/nodes__analytical_data__property__project": "Project", "@sage/xtrem-finance-data/nodes__analytical_data__property__projectType": "Project type", "@sage/xtrem-finance-data/nodes__analytical_data__property__stockSite": "Stock site", "@sage/xtrem-finance-data/nodes__analytical_data__property__stockSiteType": "Stock site type", "@sage/xtrem-finance-data/nodes__analytical_data__property__supplier": "Supplier", "@sage/xtrem-finance-data/nodes__analytical_data__property__supplierType": "Supplier type", "@sage/xtrem-finance-data/nodes__analytical_data__property__task": "Task", "@sage/xtrem-finance-data/nodes__analytical_data__property__taskType": "Task type", "@sage/xtrem-finance-data/nodes__attribute__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__attribute__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__attribute__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__attribute__attribute_restricted_to": "You need to set the \"Restricted to\" to a valid attribute.", "@sage/xtrem-finance-data/nodes__attribute__node_link_value": "You need to set the node link to \"attribute\".", "@sage/xtrem-finance-data/nodes__attribute__node_name": "Attribute", "@sage/xtrem-finance-data/nodes__attribute__property__attributeRestrictedTo": "Attribute restricted to", "@sage/xtrem-finance-data/nodes__attribute__property__attributeRestrictedToId": "Attribute restricted to ID", "@sage/xtrem-finance-data/nodes__attribute__property__attributeType": "Attribute type", "@sage/xtrem-finance-data/nodes__attribute__property__composedDescription": "Composed description", "@sage/xtrem-finance-data/nodes__attribute__property__id": "ID", "@sage/xtrem-finance-data/nodes__attribute__property__isActive": "Active", "@sage/xtrem-finance-data/nodes__attribute__property__item": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__attribute__property__name": "Name", "@sage/xtrem-finance-data/nodes__attribute__property__site": "Site", "@sage/xtrem-finance-data/nodes__attribute__referential__integrity": "The attribute cannot be deleted. It is already in use.", "@sage/xtrem-finance-data/nodes__attribute__type__referential__integrity": "The attribute type cannot be deleted. It is already in use.", "@sage/xtrem-finance-data/nodes__attribute__type__referential__integrity_isRestrictedToInUse": "The attribute type is in use. You cannot deactivate it.", "@sage/xtrem-finance-data/nodes__attribute_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__attribute_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__attribute_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__attribute_type__node__isActive": "You can only set the attribute type to active if the restricted to attribute type {{id}} is active.", "@sage/xtrem-finance-data/nodes__attribute_type__node_link_changeable_properties": "You can only update the 'Active' check box of the name of properties.", "@sage/xtrem-finance-data/nodes__attribute_type__node_name": "Attribute type", "@sage/xtrem-finance-data/nodes__attribute_type__property__analyticalMeasureType": "Analytical measure type", "@sage/xtrem-finance-data/nodes__attribute_type__property__attributeTypeRestrictedTo": "Attribute type restricted to", "@sage/xtrem-finance-data/nodes__attribute_type__property__id": "ID", "@sage/xtrem-finance-data/nodes__attribute_type__property__isActive": "Active", "@sage/xtrem-finance-data/nodes__attribute_type__property__isLinkedToItem": "Is linked to item", "@sage/xtrem-finance-data/nodes__attribute_type__property__isLinkedToSite": "Is linked to site", "@sage/xtrem-finance-data/nodes__attribute_type__property__linkedTo": "Linked to", "@sage/xtrem-finance-data/nodes__attribute_type__property__name": "Name", "@sage/xtrem-finance-data/nodes__attribute_type__property__nodeLink": "Node link", "@sage/xtrem-finance-data/nodes__attribute_type__property__queryFilter": "Query filter", "@sage/xtrem-finance-data/nodes__bank_account__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__bank_account__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__bank_account__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__bank_account__node_name": "Bank account", "@sage/xtrem-finance-data/nodes__bank_account__property__bankAccountType": "Bank account type", "@sage/xtrem-finance-data/nodes__bank_account__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__bank_account__property__financialSite": "Financial site", "@sage/xtrem-finance-data/nodes__bank_account__property__id": "ID", "@sage/xtrem-finance-data/nodes__bank_account__property__isActive": "Active", "@sage/xtrem-finance-data/nodes__bank_account__property__name": "Name", "@sage/xtrem-finance-data/nodes__base_business_relation__datev_id_invalid": "The DATEV ID needs to be a number between {{fromValue}} and {{toValue}}.", "@sage/xtrem-finance-data/nodes__base_business_relation__datev_id_not_unique": "The DATEV ID needs to be unique.", "@sage/xtrem-finance-data/nodes__base_finance_document__node_name": "Base finance document", "@sage/xtrem-finance-data/nodes__base_finance_document__property__lines": "Lines", "@sage/xtrem-finance-data/nodes__base_finance_document__property__number": "Number", "@sage/xtrem-finance-data/nodes__base_finance_line__node_name": "Base finance line", "@sage/xtrem-finance-data/nodes__base_finance_line__property__attributesAndDimensions": "Attributes and dimensions", "@sage/xtrem-finance-data/nodes__base_finance_line__property__document": "Document", "@sage/xtrem-finance-data/nodes__base_finance_line__property__documentId": "Document ID", "@sage/xtrem-finance-data/nodes__base_finance_line__property__documentNumber": "Document number", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__node_name": "Base finance line dimension", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__analyticalData": "Analytical data", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__businessSite": "Business site", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__customer": "Customer", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension01": "Dimension 01", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension02": "Dimension 02", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension03": "Dimension 03", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension04": "Dimension 04", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension05": "Dimension 05", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension06": "Dimension 06", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension07": "Dimension 07", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension08": "Dimension 08", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension09": "Dimension 09", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension10": "Dimension 10", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension11": "Dimension 11", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension12": "Dimension 12", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension13": "Dimension 13", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension14": "Dimension 14", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension15": "Dimension 15", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension16": "Dimension 16", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension17": "Dimension 17", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension18": "Dimension 18", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension19": "Dimension 19", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__dimension20": "Dimension 20", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__employee": "Employee", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__financialSite": "Financial site", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__item": "<PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__manufacturingSite": "Manufacturing site", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__originLine": "Origin line", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__project": "Project", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__stockSite": "Stock site", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__storedAttributes": "Stored attributes", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__storedDimensions": "Stored dimensions", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__supplier": "Supplier", "@sage/xtrem-finance-data/nodes__base_finance_line_dimension__property__task": "Task", "@sage/xtrem-finance-data/nodes__base_open_item__document_type_invalid": "The document type needs to be 'Purchase invoice', 'Purchase credit note', 'AP Invoice', 'Sales invoice', 'Sales credit note' or 'AR Invoice'.", "@sage/xtrem-finance-data/nodes__base_open_item__negative_forced_amount_paid": "The forced amount paid needs to be greater than or equal to 0.", "@sage/xtrem-finance-data/nodes__base_open_item__node_name": "Base open item", "@sage/xtrem-finance-data/nodes__base_open_item__property__businessEntity": "Business entity", "@sage/xtrem-finance-data/nodes__base_open_item__property__businessEntityPayment": "Business entity payment", "@sage/xtrem-finance-data/nodes__base_open_item__property__businessRelation": "Business relation", "@sage/xtrem-finance-data/nodes__base_open_item__property__closeReason": "Close reason", "@sage/xtrem-finance-data/nodes__base_open_item__property__closeText": "Close text", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountDue": "Company amount due", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountDueSigned": "Company amount due signed", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountPaid": "Company amount paid", "@sage/xtrem-finance-data/nodes__base_open_item__property__companyAmountPaidSigned": "Company amount paid signed", "@sage/xtrem-finance-data/nodes__base_open_item__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountAmount": "Discount amount", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountDate": "Discount date", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountFrom": "Discount from", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountPaymentBeforeDate": "Discount payment before date", "@sage/xtrem-finance-data/nodes__base_open_item__property__discountType": "Discount type", "@sage/xtrem-finance-data/nodes__base_open_item__property__displayDiscountPaymentDate": "Display discount payment date", "@sage/xtrem-finance-data/nodes__base_open_item__property__documentNumber": "Document number", "@sage/xtrem-finance-data/nodes__base_open_item__property__documentSysId": "Document system ID", "@sage/xtrem-finance-data/nodes__base_open_item__property__documentType": "Document type", "@sage/xtrem-finance-data/nodes__base_open_item__property__dueDate": "Due date", "@sage/xtrem-finance-data/nodes__base_open_item__property__financialSiteAmountDue": "Financial site amount due", "@sage/xtrem-finance-data/nodes__base_open_item__property__financialSiteAmountPaid": "Financial site amount paid", "@sage/xtrem-finance-data/nodes__base_open_item__property__forcedAmountPaid": "Forced amount paid", "@sage/xtrem-finance-data/nodes__base_open_item__property__forcedAmountPaidSigned": "Forced amount paid signed", "@sage/xtrem-finance-data/nodes__base_open_item__property__penaltyAmount": "Penalty amount", "@sage/xtrem-finance-data/nodes__base_open_item__property__penaltyPaymentType": "Penalty payment type", "@sage/xtrem-finance-data/nodes__base_open_item__property__remainingCompanyAmountSigned": "Remaining company amount signed", "@sage/xtrem-finance-data/nodes__base_open_item__property__remainingTransactionAmountSigned": "Remaining transaction amount signed", "@sage/xtrem-finance-data/nodes__base_open_item__property__status": "Status", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountDue": "Transaction amount due", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountDueSigned": "Transaction amount due signed", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountPaid": "Transaction amount paid", "@sage/xtrem-finance-data/nodes__base_open_item__property__transactionAmountPaidSigned": "Transaction amount paid signed", "@sage/xtrem-finance-data/nodes__base_open_item__property__type": "Type", "@sage/xtrem-finance-data/nodes__base_payment_document__id_already_exists": "The ID already exists. No sequence number will be allocated to the current document.", "@sage/xtrem-finance-data/nodes__base_payment_document__node_name": "Base payment document", "@sage/xtrem-finance-data/nodes__base_payment_document__payment_amount_discrepancy": "The payment amount of the document {{amount}} needs to be the same as the total of payment amounts of all lines {{total}}.", "@sage/xtrem-finance-data/nodes__base_payment_document__payment_amount_invalid": "The payment amount of the document {{amount}} needs to be positive.", "@sage/xtrem-finance-data/nodes__base_payment_document__property__amount": "Amount", "@sage/xtrem-finance-data/nodes__base_payment_document__property__amountBankCurrency": "Amount bank currency", "@sage/xtrem-finance-data/nodes__base_payment_document__property__bankAccount": "Bank account", "@sage/xtrem-finance-data/nodes__base_payment_document__property__businessRelation": "Business relation", "@sage/xtrem-finance-data/nodes__base_payment_document__property__businessRelationName": "Business relation name", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyAmount": "Company amount", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyCurrency": "Company currency", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyExchangeRate": "Company exchange rate", "@sage/xtrem-finance-data/nodes__base_payment_document__property__companyExchangeRateDivisor": "Company exchange rate divisor", "@sage/xtrem-finance-data/nodes__base_payment_document__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__base_payment_document__property__customer": "Customer", "@sage/xtrem-finance-data/nodes__base_payment_document__property__exchangeRateDate": "Exchange rate date", "@sage/xtrem-finance-data/nodes__base_payment_document__property__financialSite": "Financial site", "@sage/xtrem-finance-data/nodes__base_payment_document__property__financialSiteName": "Financial site name", "@sage/xtrem-finance-data/nodes__base_payment_document__property__isVoided": "Is voided", "@sage/xtrem-finance-data/nodes__base_payment_document__property__lines": "Lines", "@sage/xtrem-finance-data/nodes__base_payment_document__property__number": "Number", "@sage/xtrem-finance-data/nodes__base_payment_document__property__paymentDate": "Payment date", "@sage/xtrem-finance-data/nodes__base_payment_document__property__paymentMethod": "Payment method", "@sage/xtrem-finance-data/nodes__base_payment_document__property__postingDate": "Posting date", "@sage/xtrem-finance-data/nodes__base_payment_document__property__postingStatus": "Posting status", "@sage/xtrem-finance-data/nodes__base_payment_document__property__reference": "Reference", "@sage/xtrem-finance-data/nodes__base_payment_document__property__supplier": "Supplier", "@sage/xtrem-finance-data/nodes__base_payment_document__property__transactionCurrency": "Transaction currency", "@sage/xtrem-finance-data/nodes__base_payment_document__property__type": "Type", "@sage/xtrem-finance-data/nodes__base_payment_document__property__voidDate": "Void date", "@sage/xtrem-finance-data/nodes__base_payment_document__property__voidText": "Void text", "@sage/xtrem-finance-data/nodes__base-open_item__close_reason_mandatory": "The close reason is mandatory.", "@sage/xtrem-finance-data/nodes__base-open_item__paid_company_amount_discrepancy": "The company amount paid needs to be lower than or equal to the company amount due.", "@sage/xtrem-finance-data/nodes__base-open_item__paid_financial_site_amount_discrepancy": "The financial site amount paid needs to be lower than or equal to the financial site amount due.", "@sage/xtrem-finance-data/nodes__base-open_item__paid_transaction_amount_discrepancy": "The transaction amount paid needs to be lower than or equal to the transaction amount due.", "@sage/xtrem-finance-data/nodes__base-open_item__wrong_forced_amount_paid": "The forced amount paid needs to be between 0 and {{maxForcedAmount}}.", "@sage/xtrem-finance-data/nodes__base-payment_document__bank_amount_invalid": "The amount in bank currency of the document {{amount}} needs to be positive.", "@sage/xtrem-finance-data/nodes__base-payment_document__financial_site_discrepancy": "The financial site of the document needs to be the same as the financial site of the bank account.", "@sage/xtrem-finance-data/nodes__base-payment-document__void_date_mandatory": "The void date is mandatory.", "@sage/xtrem-finance-data/nodes__base-payment-document__void_date_should_be_after_payment_date": "The void date needs to be after the payment date.", "@sage/xtrem-finance-data/nodes__close_reason__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__close_reason__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__close_reason__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__close_reason__node_name": "Close reason", "@sage/xtrem-finance-data/nodes__close_reason__property__id": "ID", "@sage/xtrem-finance-data/nodes__close_reason__property__isActive": "Is active", "@sage/xtrem-finance-data/nodes__close_reason__property__name": "Name", "@sage/xtrem-finance-data/nodes__company__datev_number_invalid": "Enter a number between {{first}} and {{last}}.", "@sage/xtrem-finance-data/nodes__company__datev_number_mandatory": "If the legislation is Germany, this number is required.", "@sage/xtrem-finance-data/nodes__company__datev_number_only_for_germany": "This number is only for German legislation.", "@sage/xtrem-finance-data/nodes__company__project_task_discrepancy": "The project and task attributes need to default from the same origin.", "@sage/xtrem-finance-data/nodes__company_attribute_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__company_attribute_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__company_attribute_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__company_attribute_type__attribute-type-not-active": "The attribute type is inactive", "@sage/xtrem-finance-data/nodes__company_attribute_type__node_name": "Company attribute type", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__analyticalMeasureType": "Analytical measure type", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__attributeType": "Attribute type", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__company": "Company", "@sage/xtrem-finance-data/nodes__company_attribute_type__property__isRequired": "Is required", "@sage/xtrem-finance-data/nodes__company_default_attribute__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__company_default_attribute__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__company_default_attribute__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__company_default_attribute__node_link_master_data_default__not_allowed": "The default {{masterDataDefault}} is not allowed in the document {{dimensionDefinitionLevel}}.", "@sage/xtrem-finance-data/nodes__company_default_attribute__node_name": "Company default attribute", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__attributeType": "Attribute type", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__company": "Company", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__dimensionDefinitionLevel": "Dimension definition level", "@sage/xtrem-finance-data/nodes__company_default_attribute__property__masterDataDefault": "Master data default", "@sage/xtrem-finance-data/nodes__company_default_dimension__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__company_default_dimension__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__company_default_dimension__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__company_default_dimension__node_name": "Company default dimension", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__company": "Company", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__dimensionDefinitionLevel": "Dimension definition level", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__dimensionType": "Dimension type", "@sage/xtrem-finance-data/nodes__company_default_dimension__property__masterDataDefault": "Master data default", "@sage/xtrem-finance-data/nodes__company_dimension_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__company_dimension_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__company_dimension_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__company_dimension_type__dimension-type-not-active": "The dimension type is not active", "@sage/xtrem-finance-data/nodes__company_dimension_type__node_name": "Company dimension type", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__analyticalMeasureType": "Analytical measure type", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__company": "Company", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__dimensionType": "Dimension type", "@sage/xtrem-finance-data/nodes__company_dimension_type__property__isRequired": "Is required", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_account_datev_id_length": "The DATEV ID length for certain accounts does not match the account length. They need to be the same.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_customer_datev_id_length": "The DATEV ID length for certain customers does not match the customer and supplier ID length. They need to be the same.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_customer_datev_id_range": "There are DATEV IDs for certain customers that are outside the customer ID range. They need to be within the range.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_supplier_datev_id_length": "The DATEV ID length for certain suppliers does not match the customer and supplier ID length. They need to be the same.", "@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_supplier_datev_id_range": "There are DATEV IDs for certain suppliers that are outside the supplier ID range. They need to be within the range.", "@sage/xtrem-finance-data/nodes__datev_configuration__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__datev_configuration__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__datev_configuration__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__datev_configuration__invalid_length": "Enter a number between {{fromValue}} and {{toValue}}.", "@sage/xtrem-finance-data/nodes__datev_configuration__mutation__datevConfigurationControlsOnSave": "DATEV configuration controls on save", "@sage/xtrem-finance-data/nodes__datev_configuration__mutation__datevConfigurationControlsOnSave__failed": "Datev configuration controls on save failed.", "@sage/xtrem-finance-data/nodes__datev_configuration__mutation__datevConfigurationControlsOnSave__parameter__datevConfiguration": "DATEV configuration", "@sage/xtrem-finance-data/nodes__datev_configuration__node_name": "DATEV configuration", "@sage/xtrem-finance-data/nodes__datev_configuration__property__accountLength": "Account length", "@sage/xtrem-finance-data/nodes__datev_configuration__property__customerRangeEnd": "Customer range end", "@sage/xtrem-finance-data/nodes__datev_configuration__property__customerRangeStart": "Customer range start", "@sage/xtrem-finance-data/nodes__datev_configuration__property__customerSupplierLength": "Customer supplier length", "@sage/xtrem-finance-data/nodes__datev_configuration__property__id": "ID", "@sage/xtrem-finance-data/nodes__datev_configuration__property__isActive": "Is active", "@sage/xtrem-finance-data/nodes__datev_configuration__property__name": "Name", "@sage/xtrem-finance-data/nodes__datev_configuration__property__skrCoa": "SKR COA", "@sage/xtrem-finance-data/nodes__datev_configuration__property__supplierRangeEnd": "Supplier range end", "@sage/xtrem-finance-data/nodes__datev_configuration__property__supplierRangeStart": "Supplier range start", "@sage/xtrem-finance-data/nodes__datev_configuration__wrong_length": "The account length does not match the customer and supplier ID length.", "@sage/xtrem-finance-data/nodes__dimension__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__dimension__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__dimension__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__dimension__node_name": "Dimension", "@sage/xtrem-finance-data/nodes__dimension__property__composedDescription": "Composed description", "@sage/xtrem-finance-data/nodes__dimension__property__dimensionType": "Dimension type", "@sage/xtrem-finance-data/nodes__dimension__property__id": "ID", "@sage/xtrem-finance-data/nodes__dimension__property__isActive": "Active", "@sage/xtrem-finance-data/nodes__dimension__property__name": "Name", "@sage/xtrem-finance-data/nodes__dimension__referential__integrity": "The dimension cannot be deleted. It is already in use.", "@sage/xtrem-finance-data/nodes__dimension__type__inactive": "The dimension type is inactive.", "@sage/xtrem-finance-data/nodes__dimension__type__referential__integrity": "The dimension type cannot be deleted. It is already in use.", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__node_name": "Dimension definition level and default", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__property__dimensionDefinitionLevel": "Dimension definition level", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__property__masterDataDefault": "Master data default", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getAttributesAndDimensionsFromItem": "Get attributes and dimensions from item", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getAttributesAndDimensionsFromItem__failed": "Get attributes and dimensions from item failed.", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getAttributesAndDimensionsFromItem__parameter__data": "Data", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensions": "Get default attributes and dimensions", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensions__failed": "Get default attributes and dimensions failed.", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensions__parameter__data": "Data", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensionsOrderToOrder": "Get default attributes and dimensions order-to-order", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensionsOrderToOrder__failed": "Get default attributes and dimensions order to order failed.", "@sage/xtrem-finance-data/nodes__dimension_definition_level_and_default__query__getDefaultAttributesAndDimensionsOrderToOrder__parameter__data": "Data", "@sage/xtrem-finance-data/nodes__dimension_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__dimension_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__dimension_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__dimension_type__max_value": "All available dimension types are allocated.", "@sage/xtrem-finance-data/nodes__dimension_type__node_name": "Dimension type", "@sage/xtrem-finance-data/nodes__dimension_type__property__analyticalMeasureType": "Analytical measure type", "@sage/xtrem-finance-data/nodes__dimension_type__property__dimensions": "Dimensions", "@sage/xtrem-finance-data/nodes__dimension_type__property__docProperty": "Document property", "@sage/xtrem-finance-data/nodes__dimension_type__property__isActive": "Active", "@sage/xtrem-finance-data/nodes__dimension_type__property__isUsed": "Is used", "@sage/xtrem-finance-data/nodes__dimension_type__property__name": "Name", "@sage/xtrem-finance-data/nodes__dimension_type__property__setupId": "Setup ID", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_accounting_staging": "There are journal entries pending for this document. A notification will not be resent.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_finance_transaction_records": "There are no finance transaction records for this document. A notification will not be resent.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_finance_transaction_status": "Document is being processed. A notification will not be resent.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_package_active": "The finance package is not active.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_pi_allocated_to_po": "This purchase invoice has values allocated to a purchase order. A notification will not be resent.", "@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_po_allocated_to_pr": "This purchase receipt has values allocated from a purchase order. A notification will not be resent.", "@sage/xtrem-finance-data/nodes__finance_transaction__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__finance_transaction__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__finance_transaction__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__finance_transaction__node_name": "Finance transaction", "@sage/xtrem-finance-data/nodes__finance_transaction__property__batchId": "Batch ID", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentNumber": "Document number", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentNumberLink": "Document number link", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentSysId": "Document system ID", "@sage/xtrem-finance-data/nodes__finance_transaction__property__documentType": "Document type", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financeIntegrationApp": "Finance integration application", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financeIntegrationAppRecordId": "Finance integration application record ID", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financeIntegrationAppUrl": "Finance integration application URL", "@sage/xtrem-finance-data/nodes__finance_transaction__property__financialSite": "Financial site", "@sage/xtrem-finance-data/nodes__finance_transaction__property__hasSourceForDimensionLines": "Has source for dimension lines", "@sage/xtrem-finance-data/nodes__finance_transaction__property__lastStatusUpdate": "Last status update", "@sage/xtrem-finance-data/nodes__finance_transaction__property__lines": "Lines", "@sage/xtrem-finance-data/nodes__finance_transaction__property__message": "Message", "@sage/xtrem-finance-data/nodes__finance_transaction__property__paymentTracking": "Payment tracking", "@sage/xtrem-finance-data/nodes__finance_transaction__property__postingStatus": "Posting status", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentLink": "Source document link", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentNumber": "Source document number", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentSysId": "Source document system ID", "@sage/xtrem-finance-data/nodes__finance_transaction__property__sourceDocumentType": "Source document type", "@sage/xtrem-finance-data/nodes__finance_transaction__property__status": "Status", "@sage/xtrem-finance-data/nodes__finance_transaction__property__targetDocumentNumber": "Target document number", "@sage/xtrem-finance-data/nodes__finance_transaction__property__targetDocumentSysId": "Target document system ID", "@sage/xtrem-finance-data/nodes__finance_transaction__property__targetDocumentType": "Target document type", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusData": "Get posting status data", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusData__failed": "Get posting status data failed.", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusData__parameter__documentNumber": "Document number", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId": "Get posting status data by document ID", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId__failed": "Get posting status data by document ID failed.", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId__parameter__documentSysId": "Document system ID", "@sage/xtrem-finance-data/nodes__finance_transaction__query__getPostingStatusDataByDocumentId__parameter__documentType": "Document type", "@sage/xtrem-finance-data/nodes__finance_transaction__status_update_not_allowed": "The finance transaction status cannot be updated from {{previousStatus}} to {{newStatus}}.", "@sage/xtrem-finance-data/nodes__finance_transaction_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__finance_transaction_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__finance_transaction_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__finance_transaction_line__node_name": "Finance transaction line", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__financeTransaction": "Finance transaction", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__isSourceForDimension": "Is source for dimension", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__sourceDocumentNumber": "Source document number", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__sourceDocumentSysId": "Source document system ID", "@sage/xtrem-finance-data/nodes__finance_transaction_line__property__sourceDocumentType": "Source document type", "@sage/xtrem-finance-data/nodes__journal__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__journal__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__journal__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__journal__node_name": "Journal", "@sage/xtrem-finance-data/nodes__journal__primaryDocumentType_not_allowed": "The primary document type is only allowed for the French legislation.", "@sage/xtrem-finance-data/nodes__journal__property__id": "ID", "@sage/xtrem-finance-data/nodes__journal__property__isActive": "Active", "@sage/xtrem-finance-data/nodes__journal__property__isSubjectToGlTaxExcludedAmount": "Subject to GL VAT excluded amount", "@sage/xtrem-finance-data/nodes__journal__property__legislation": "Legislation", "@sage/xtrem-finance-data/nodes__journal__property__name": "Name", "@sage/xtrem-finance-data/nodes__journal__property__primaryDocumentType": "Primary document type", "@sage/xtrem-finance-data/nodes__journal__property__secondaryDocumentType": "Secondary document type", "@sage/xtrem-finance-data/nodes__journal__property__sequence": "Sequence", "@sage/xtrem-finance-data/nodes__journal__property__taxImpact": "Tax impact", "@sage/xtrem-finance-data/nodes__journal__secondaryDocumentType_not_allowed": "The secondary document type is only allowed for the French legislation.", "@sage/xtrem-finance-data/nodes__journal__taxImpact_cannot_be_set": "At least one line of the tax solution needs to be 'Assigned to GL excluding VAT' to set the 'Tax impact' option for the journal.", "@sage/xtrem-finance-data/nodes__journal_cannot_modify_account_entry_exists": "You cannot change the sequence number. An accounting entry exits for this journal.", "@sage/xtrem-finance-data/nodes__journal_entry_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__journal_entry_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__journal_entry_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__journal_entry_type__contra_journal_entry_type_line_invalid": "The contra journal entry type on the line needs to be the same as the journal entry type.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_account_type_empty": "Leave the header account type clear.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_account_type_mandatory": "Enter the account type in the header.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_amount_type_empty": "Leave the header amount type clear.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_amount_type_mandatory": "Enter the amount type in the header.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_journal_empty": "Leave the header journal clear.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_journal_mandatory": "Enter the journal in the header.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_posting_date_empty": "Leave the header posting date clear.", "@sage/xtrem-finance-data/nodes__journal_entry_type__header_posting_date_mandatory": "Enter the posting date in the header.", "@sage/xtrem-finance-data/nodes__journal_entry_type__node_name": "Journal entry type", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__documentType": "Document type", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerAccountType": "Header account type", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerAmountType": "Header amount type", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerDescription": "Header description", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerJournal": "Header journal", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__headerPostingDate": "Header posting date", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__immediatePosting": "Immediate posting", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__isActive": "Active", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__legislation": "Legislation", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__lines": "Lines", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__name": "Name", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__setupId": "Setup ID", "@sage/xtrem-finance-data/nodes__journal_entry_type__property__targetDocumentType": "Target document type", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__forced_false": "The Landed cost items may only be set for movement type Document.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__not_allowed": "The landed cost items are not allowed if the posting class type is not item or the posting class is not detailed.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__posting_class_definition_control": "The landed cost items are not allowed by the account type.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__forced_false": "The non-stock items can only be set for movement type Document.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__not_allowed": "The non-stock items are not allowed if the posting class type is not item or the posting class is not detailed.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__posting_class_definition_control": "The non-stock items are not allowed by the account type.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__forced_false": "The service items can only be set for movement type Document.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__not_allowed": "The service items are not allowed if the posting class type is not item or the posting class is not detailed.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__posting_class_definition_control": "The service items are not allowed by the account type.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__forced_true": "You need to select Stock items for this movement type: {{movementType}}.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__not_allowed": "The stock items are not allowed if the posting class type is not item or the posting class is not detailed.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__posting_class_definition_control": "The stock items are not allowed by the account type.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__node_name": "Journal entry type line", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__accountType": "Account type", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__amountType": "Amount type", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__commonReference": "Common reference", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__contraJournalEntryTypeLine": "Contra journal entry type line", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isLandedCostItemAllowed": "Is landed cost item allowed", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isNonStockItemAllowed": "Is non-stock item allowed", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isServiceItemAllowed": "Is service item allowed", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__isStockItemAllowed": "Is stock item allowed", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__itemStockManagementCriteria": "Item stock management criteria", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__journalEntryType": "Journal entry type", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__movementType": "Movement type", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__property__sign": "Sign", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__sign__empty": "Leave the sign clear.", "@sage/xtrem-finance-data/nodes__journal_entry_type_line__sign_mandatory": "Enter the sign.", "@sage/xtrem-finance-data/nodes__journal-entry-type-line__at_least_one_item_type_mandatory": "At least one of stock items, non-stock items, service items or landed cost items must be set for Account type with Posting class type Item.", "@sage/xtrem-finance-data/nodes__journal-entry-type-line__contra_journal_entry_type_line_mandatory": "If the target document type is journal entry, the contra account is required.", "@sage/xtrem-finance-data/nodes__open_items__type_invalid": "The open item type needs to be 'Customer' or 'Supplier'.", "@sage/xtrem-finance-data/nodes__payment_document_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__payment_document_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__payment_document_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__payment_document_line__negative_discount_amount": "The discount amount needs to be greater than or equal to 0.", "@sage/xtrem-finance-data/nodes__payment_document_line__negative_penalty_amount": "The penalty amount needs to be greater than or equal to 0.", "@sage/xtrem-finance-data/nodes__payment_document_line__node_name": "Payment document line", "@sage/xtrem-finance-data/nodes__payment_document_line__property__adjustmentAmount": "Adjustment amount", "@sage/xtrem-finance-data/nodes__payment_document_line__property__amount": "Amount", "@sage/xtrem-finance-data/nodes__payment_document_line__property__amountBankCurrency": "Amount bank currency", "@sage/xtrem-finance-data/nodes__payment_document_line__property__companyAmount": "Company amount", "@sage/xtrem-finance-data/nodes__payment_document_line__property__companyCurrency": "Company currency", "@sage/xtrem-finance-data/nodes__payment_document_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__payment_document_line__property__discountAmount": "Discount amount", "@sage/xtrem-finance-data/nodes__payment_document_line__property__document": "Document", "@sage/xtrem-finance-data/nodes__payment_document_line__property__documentId": "Document ID", "@sage/xtrem-finance-data/nodes__payment_document_line__property__documentNumber": "Document number", "@sage/xtrem-finance-data/nodes__payment_document_line__property__financialSite": "Financial site", "@sage/xtrem-finance-data/nodes__payment_document_line__property__origin": "Origin", "@sage/xtrem-finance-data/nodes__payment_document_line__property__originalNodeFactory": "Original node factory", "@sage/xtrem-finance-data/nodes__payment_document_line__property__originalOpenItem": "Original open item", "@sage/xtrem-finance-data/nodes__payment_document_line__property__paymentTracking": "Payment tracking", "@sage/xtrem-finance-data/nodes__payment_document_line__property__penaltyAmount": "Penalty amount", "@sage/xtrem-finance-data/nodes__payment_document_line__property__transactionCurrency": "Transaction currency", "@sage/xtrem-finance-data/nodes__payment_tracking__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__payment_tracking__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__payment_tracking__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__payment_tracking__node_name": "Payment tracking", "@sage/xtrem-finance-data/nodes__payment_tracking__property__amountPaid": "Amount paid", "@sage/xtrem-finance-data/nodes__payment_tracking__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/nodes__payment_tracking__property__discountPaymentAmount": "Discount payment amount", "@sage/xtrem-finance-data/nodes__payment_tracking__property__discountPaymentBeforeDate": "Discount payment before date", "@sage/xtrem-finance-data/nodes__payment_tracking__property__discountPaymentType": "Discount payment type", "@sage/xtrem-finance-data/nodes__payment_tracking__property__document": "Document", "@sage/xtrem-finance-data/nodes__payment_tracking__property__forcedAmountPaid": "Forced amount paid", "@sage/xtrem-finance-data/nodes__payment_tracking__property__openItems": "Open items", "@sage/xtrem-finance-data/nodes__payment_tracking__property__paymentLines": "Payment lines", "@sage/xtrem-finance-data/nodes__payment_tracking__property__paymentTerm": "Payment term", "@sage/xtrem-finance-data/nodes__payment_tracking__property__penaltyPaymentAmount": "Penalty payment amount", "@sage/xtrem-finance-data/nodes__payment_tracking__property__penaltyPaymentType": "Penalty payment type", "@sage/xtrem-finance-data/nodes__payment_tracking__property__status": "Status", "@sage/xtrem-finance-data/nodes__posting_class__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__posting_class__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__posting_class__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_landed_cost_items": "At least one line allows landed cost items.", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_non_stock_items": "At least one line allows non-stock items.", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_service_items": "At least one line allows service items.", "@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_stock_items": "At least one line allows stock items.", "@sage/xtrem-finance-data/nodes__posting_class__lines_mandatory": "The posting class needs at least one line.", "@sage/xtrem-finance-data/nodes__posting_class__node_name": "Posting class", "@sage/xtrem-finance-data/nodes__posting_class__one_non_detailed_allowed": "Only 1 posting class of type {{type}} is allowed.", "@sage/xtrem-finance-data/nodes__posting_class__property__financeItemType": "Finance item type", "@sage/xtrem-finance-data/nodes__posting_class__property__id": "ID", "@sage/xtrem-finance-data/nodes__posting_class__property__isDetailed": "Detailed", "@sage/xtrem-finance-data/nodes__posting_class__property__isLandedCostItemAllowed": "Is landed cost item allowed", "@sage/xtrem-finance-data/nodes__posting_class__property__isNonStockItemAllowed": "Is non-stock item allowed", "@sage/xtrem-finance-data/nodes__posting_class__property__isServiceItemAllowed": "Is service item allowed", "@sage/xtrem-finance-data/nodes__posting_class__property__isStockItemAllowed": "Is stock item allowed", "@sage/xtrem-finance-data/nodes__posting_class__property__lines": "Lines", "@sage/xtrem-finance-data/nodes__posting_class__property__name": "Name", "@sage/xtrem-finance-data/nodes__posting_class__property__setupId": "Setup ID", "@sage/xtrem-finance-data/nodes__posting_class__property__type": "Type", "@sage/xtrem-finance-data/nodes__posting_class__update_is_detailed_not_allowed": "You cannot change the 'Detailed' switch to Off because the {{postingClassName}} posting class is linked to {{recordNameUsingThisPostingClass}}.", "@sage/xtrem-finance-data/nodes__posting_class_definition__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__posting_class_definition__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__posting_class_definition__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__posting_class_definition__can_have_secondary_criteria_not_allowed": "You cannot enter a secondary criteria for this posting class type: {{ postingClassType }}.", "@sage/xtrem-finance-data/nodes__posting_class_definition__criteria_and_secondary_criteria_are_the_same": "You need to select different criteria.", "@sage/xtrem-finance-data/nodes__posting_class_definition__node_name": "Posting class definition", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__accountTypeName": "Account type name", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__additionalCriteria": "Additional criteria", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__canHaveAdditionalCriteria": "Can have additional criteria", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__financeItemType": "Finance item type", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__id": "ID", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isDetailed": "Detailed", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isLandedCostItemAllowed": "Is landed cost item allowed", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isNonStockItemAllowed": "Is non-stock item allowed", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isServiceItemAllowed": "Is service item allowed", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__isStockItemAllowed": "Is stock item allowed", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__legislation": "Legislation", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__postingClassType": "Posting class type", "@sage/xtrem-finance-data/nodes__posting_class_definition__property__setupId": "Setup ID", "@sage/xtrem-finance-data/nodes__posting_class_definition__secondary_criteria_incorrect_value": "The secondary criteria needs to be tax.", "@sage/xtrem-finance-data/nodes__posting_class_definition__secondary_criteria_not_allowed": "You cannot enter a secondary criteria.", "@sage/xtrem-finance-data/nodes__posting_class_definition__tax_solution_control": "The tax solutions for this legislation need to have at least one tax category that is mandatory.", "@sage/xtrem-finance-data/nodes__posting_class_definition__update_is_detailed_not_allowed": "You cannot change the 'Detailed' switch setting because the {{ postingClassName }} posting class is linked to this definition.", "@sage/xtrem-finance-data/nodes__posting_class_line__already_used_posting_class_definition": "This posting class definition is already used on another line.", "@sage/xtrem-finance-data/nodes__posting_class_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__posting_class_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__posting_class_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__posting_class_line__node_name": "Posting class line", "@sage/xtrem-finance-data/nodes__posting_class_line__property__account": "Account", "@sage/xtrem-finance-data/nodes__posting_class_line__property__accountId": "Account ID", "@sage/xtrem-finance-data/nodes__posting_class_line__property__chartOfAccount": "Chart of accounts", "@sage/xtrem-finance-data/nodes__posting_class_line__property__definition": "Definition", "@sage/xtrem-finance-data/nodes__posting_class_line__property__details": "Details", "@sage/xtrem-finance-data/nodes__posting_class_line__property__hasDetails": "Has details", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isLandedCostItemAllowed": "Is landed cost item allowed", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isNonStockItemAllowed": "Is non-stock item allowed", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isServiceItemAllowed": "Is service item allowed", "@sage/xtrem-finance-data/nodes__posting_class_line__property__isStockItemAllowed": "Is stock item allowed", "@sage/xtrem-finance-data/nodes__posting_class_line__property__postingClass": "Posting class", "@sage/xtrem-finance-data/nodes__posting_class_line__property__updateAccountTaxManagement": "Update account tax management", "@sage/xtrem-finance-data/nodes__posting_class_line__query__getPostingClassAccounts": "Get posting class accounts", "@sage/xtrem-finance-data/nodes__posting_class_line__query__getPostingClassAccounts__failed": "Get posting class accounts failed.", "@sage/xtrem-finance-data/nodes__posting_class_line__query__getPostingClassAccounts__parameter__postingClassDefinition": "Posting class definition", "@sage/xtrem-finance-data/nodes__posting_class_line__wrong_account_tax_management": "Select an account management that is 'Tax'.", "@sage/xtrem-finance-data/nodes__posting_class_line__wrong_posting_class_definition_is_detailed": "When the posting class has a name, only posting class definitions with the 'Detailed' check box selected are allowed. When the posting class does not have a name, only posting class definitions without the 'Detailed' check box selected are allowed.", "@sage/xtrem-finance-data/nodes__posting_class_line__wrong_posting_class_definition_type": "The posting class definition must have the {{ postingClassType }} type.", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__mutation__getTaxCategoryIds": "Get tax category IDs", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__mutation__getTaxCategoryIds__failed": "Get tax category IDs failed.", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__mutation__getTaxCategoryIds__parameter__legislation": "Legislation", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__no_secondary_criteria_defined": "This posting class does not have the second criteria option selected.", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__node_name": "Posting class line detail", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__property__account": "Account", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__property__postingClassLine": "Posting class line", "@sage/xtrem-finance-data/nodes__posting_class_line_detail__property__tax": "Tax", "@sage/xtrem-finance-data/nodes__tax__posting_key_invalid": "The Posting key needs to be a number between 1 and 9999.", "@sage/xtrem-finance-data/nodes__tax__posting_key_wrong_country": "You can only enter a value if the country is Germany.", "@sage/xtrem-finance-data/package__name": "Finance setup", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension____navigationPanel__listItem__datevId__title": "DATEV ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension____navigationPanel__listItem__line11__title": "Posting class", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__datevId____title": "DATEV ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____lookupDialogTitle": "Select dimension 01", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension01____title": "Dimension 01", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____lookupDialogTitle": "Select dimension 02", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension02____title": "Dimension 02", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____lookupDialogTitle": "Select dimension 03", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension03____title": "Dimension 03", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____lookupDialogTitle": "Select dimension 04", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension04____title": "Dimension 04", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____lookupDialogTitle": "Select dimension 05", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension05____title": "Dimension 05", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____lookupDialogTitle": "Select dimension 06", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension06____title": "Dimension 06", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____lookupDialogTitle": "Select dimension 07", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension07____title": "Dimension 07", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____lookupDialogTitle": "Select dimension 08", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension08____title": "Dimension 08", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____lookupDialogTitle": "Select dimension 09", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension09____title": "Dimension 09", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____lookupDialogTitle": "Select dimension 10", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension10____title": "Dimension 10", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____lookupDialogTitle": "Select dimension 11", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension11____title": "Dimension 11", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____lookupDialogTitle": "Select dimension 12", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension12____title": "Dimension 12", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____lookupDialogTitle": "Select dimension 13", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension13____title": "Dimension 13", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____lookupDialogTitle": "Select dimension 14", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension14____title": "Dimension 14", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____lookupDialogTitle": "Select dimension 15", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension15____title": "Dimension 15", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____lookupDialogTitle": "Select dimension 16", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension16____title": "Dimension 16", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____lookupDialogTitle": "Select dimension 17", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension17____title": "Dimension 17", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____lookupDialogTitle": "Select dimension 18", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension18____title": "Dimension 18", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____lookupDialogTitle": "Select dimension 19", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension19____title": "Dimension 19", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____lookupDialogTitle": "Select dimension 20", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimension20____title": "Dimension 20", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__dimensionBlock____title": "Dimensions", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____lookupDialogTitle": "Select employee", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__employee____title": "Employee", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__postingClass____lookupDialogTitle": "Select posting class", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__postingClass____title": "Posting class", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____lookupDialogTitle": "Select project", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__project____title": "Project", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____lookupDialogTitle": "Select task", "@sage/xtrem-finance-data/page-extensions__business_entity_customer_extension__task____title": "Task", "@sage/xtrem-finance-data/page-extensions__company_extension____navigationPanel__listItem__datevConsultantNumber__title": "DATEV consultant number", "@sage/xtrem-finance-data/page-extensions__company_extension____navigationPanel__listItem__datevCustomerNumber__title": "DATEV customer number", "@sage/xtrem-finance-data/page-extensions__company_extension__addDefaultDimension____title": "Add line", "@sage/xtrem-finance-data/page-extensions__company_extension__addDimensionAttributeLine____title": "Mandatory dimension and attributes", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____columns__title__id": "Name", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____columns__title__type": "Analytical type", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____dropdownActions__title": "Delete", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeDimensionTypes____title": "Required dimensions", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeTypes____columns__title__analyticalMeasureType": "Analytical type", "@sage/xtrem-finance-data/page-extensions__company_extension__attributeTypes____title": "Attributes", "@sage/xtrem-finance-data/page-extensions__company_extension__bankAccount____lookupDialogTitle": "Select bank account", "@sage/xtrem-finance-data/page-extensions__company_extension__bankAccount____title": "Default bank account", "@sage/xtrem-finance-data/page-extensions__company_extension__country____columns__title__legislation__name": "Legislation", "@sage/xtrem-finance-data/page-extensions__company_extension__datevConsultantNumber____title": "DATEV consultant number", "@sage/xtrem-finance-data/page-extensions__company_extension__datevCustomerNumber____title": "DATEV customer number", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____columns__columns__company__name__title": "Name", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____columns__columns__company__name__title__2": "ID", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____columns__title__company__name": "Company", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributes____title": "Attributes", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributesAndDimensions____columns__title__document": "Document and origin", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributesAndDimensions____dropdownActions__title": "Delete", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultAttributesAndDimensions____title": "Default dimension rules", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____columns__columns__company__name__title": "Name", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____columns__columns__company__name__title__2": "ID", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____columns__title__company__name": "Company", "@sage/xtrem-finance-data/page-extensions__company_extension__defaultDimensions____title": "Dimensions", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionsBlock____title": "Dimensions", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionsSection____title": "Dimensions", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionTypes____columns__title__analyticalMeasureType": "Analytical type", "@sage/xtrem-finance-data/page-extensions__company_extension__dimensionTypes____title": "Dimensions", "@sage/xtrem-finance-data/page-extensions__company_extension__doApPosting____title": "AP posting", "@sage/xtrem-finance-data/page-extensions__company_extension__doArPosting____title": "AR posting", "@sage/xtrem-finance-data/page-extensions__company_extension__doNonAbsorbedPosting____title": "Non absorbed amount posting", "@sage/xtrem-finance-data/page-extensions__company_extension__doStockPosting____title": "Stock posting", "@sage/xtrem-finance-data/page-extensions__company_extension__doWipPosting____title": "WIP posting", "@sage/xtrem-finance-data/page-extensions__company_extension__financePostingBlock____title": "Finance", "@sage/xtrem-finance-data/page-extensions__company_extension__manufacturingPostingBlock____title": "Manufacturing", "@sage/xtrem-finance-data/page-extensions__company_extension__postingClass____lookupDialogTitle": "Select posting class", "@sage/xtrem-finance-data/page-extensions__company_extension__postingClass____title": "Posting class", "@sage/xtrem-finance-data/page-extensions__company_extension__postingSection____title": "Posting", "@sage/xtrem-finance-data/page-extensions__company_extension__selectAttributeAndDimensionBlock____title": "Management", "@sage/xtrem-finance-data/page-extensions__company_extension__selectAttributeAndDimensionSection____title": "Required dimensions", "@sage/xtrem-finance-data/page-extensions__company_extension__selectDimensionAndAttributeTypes____columns__title__id": "Name", "@sage/xtrem-finance-data/page-extensions__company_extension__selectDimensionAndAttributeTypes____columns__title__type": "Analytical type", "@sage/xtrem-finance-data/page-extensions__company_extension__selectDimensionAndAttributeTypes____title": "Required dimensions", "@sage/xtrem-finance-data/page-extensions__company_extension__stockPostingBlock____title": "Stock", "@sage/xtrem-finance-data/page-extensions__company_extension__taxEngine____title": "Tax calculation package", "@sage/xtrem-finance-data/page-extensions__company_extension__taxManagementBlock____title": "Tax management", "@sage/xtrem-finance-data/page-extensions__customer_extension____navigationPanel__listItem__datevId__title": "DATEV ID", "@sage/xtrem-finance-data/page-extensions__customer_extension____navigationPanel__listItem__line11__title": "Posting class", "@sage/xtrem-finance-data/page-extensions__customer_extension__datevId____title": "DATEV ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____lookupDialogTitle": "Select dimension 01", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension01____title": "Dimension 01", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____lookupDialogTitle": "Select dimension 02", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension02____title": "Dimension 02", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____lookupDialogTitle": "Select dimension 03", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension03____title": "Dimension 03", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____lookupDialogTitle": "Select dimension 04", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension04____title": "Dimension 04", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____lookupDialogTitle": "Select dimension 05", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension05____title": "Dimension 05", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____lookupDialogTitle": "Select dimension 06", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension06____title": "Dimension 06", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____lookupDialogTitle": "Select dimension 07", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension07____title": "Dimension 07", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____lookupDialogTitle": "Select dimension 08", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension08____title": "Dimension 08", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____lookupDialogTitle": "Select dimension 09", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension09____title": "Dimension 09", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____lookupDialogTitle": "Select dimension 10", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension10____title": "Dimension 10", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____lookupDialogTitle": "Select dimension 11", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension11____title": "Dimension 11", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____lookupDialogTitle": "Select dimension 12", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension12____title": "Dimension 12", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____lookupDialogTitle": "Select dimension 13", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension13____title": "Dimension 13", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____lookupDialogTitle": "Select dimension 14", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension14____title": "Dimension 14", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____lookupDialogTitle": "Select dimension 15", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension15____title": "Dimension 15", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____lookupDialogTitle": "Select dimension 16", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension16____title": "Dimension 16", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____lookupDialogTitle": "Select dimension 17", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension17____title": "Dimension 17", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____lookupDialogTitle": "Select dimension 18", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension18____title": "Dimension 18", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____lookupDialogTitle": "Select dimension 19", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension19____title": "Dimension 19", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____lookupDialogTitle": "Select dimension 20", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimension20____title": "Dimension 20", "@sage/xtrem-finance-data/page-extensions__customer_extension__dimensionBlock____title": "Dimensions", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____lookupDialogTitle": "Select employee", "@sage/xtrem-finance-data/page-extensions__customer_extension__employee____title": "Employee", "@sage/xtrem-finance-data/page-extensions__customer_extension__postingClass____lookupDialogTitle": "Select posting class", "@sage/xtrem-finance-data/page-extensions__customer_extension__postingClass____title": "Posting class", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____lookupDialogTitle": "Select project", "@sage/xtrem-finance-data/page-extensions__customer_extension__project____title": "Project", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____lookupDialogTitle": "Select task", "@sage/xtrem-finance-data/page-extensions__customer_extension__task____title": "Task", "@sage/xtrem-finance-data/page-extensions__item_extension____navigationPanel__listItem__postingClass__title": "Posting class", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____lookupDialogTitle": "Select dimension 01", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension01____title": "Dimension 01", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____lookupDialogTitle": "Select dimension 02", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension02____title": "Dimension 02", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____lookupDialogTitle": "Select dimension 03", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension03____title": "Dimension 03", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____lookupDialogTitle": "Select dimension 04", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension04____title": "Dimension 04", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____lookupDialogTitle": "Select dimension 05", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension05____title": "Dimension 05", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____lookupDialogTitle": "Select dimension 06", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension06____title": "Dimension 06", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____lookupDialogTitle": "Select dimension 07", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension07____title": "Dimension 07", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____lookupDialogTitle": "Select dimension 08", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension08____title": "Dimension 08", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____lookupDialogTitle": "Select dimension 09", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension09____title": "Dimension 09", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____lookupDialogTitle": "Select dimension 10", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension10____title": "Dimension 10", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____lookupDialogTitle": "Select dimension 11", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension11____title": "Dimension 11", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____lookupDialogTitle": "Select dimension 12", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension12____title": "Dimension 12", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____lookupDialogTitle": "Select dimension 13", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension13____title": "Dimension 13", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____lookupDialogTitle": "Select dimension 14", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension14____title": "Dimension 14", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____lookupDialogTitle": "Select dimension 15", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension15____title": "Dimension 15", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____lookupDialogTitle": "Select dimension 16", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension16____title": "Dimension 16", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____lookupDialogTitle": "Select dimension 17", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension17____title": "Dimension 17", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____lookupDialogTitle": "Select dimension 18", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension18____title": "Dimension 18", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____lookupDialogTitle": "Select dimension 19", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension19____title": "Dimension 19", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____lookupDialogTitle": "Select dimension 20", "@sage/xtrem-finance-data/page-extensions__item_extension__dimension20____title": "Dimension 20", "@sage/xtrem-finance-data/page-extensions__item_extension__dimensionBlock____title": "Dimensions", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____lookupDialogTitle": "Select employee", "@sage/xtrem-finance-data/page-extensions__item_extension__employee____title": "Employee", "@sage/xtrem-finance-data/page-extensions__item_extension__postingClass____lookupDialogTitle": "Select posting class", "@sage/xtrem-finance-data/page-extensions__item_extension__postingClass____title": "Posting class", "@sage/xtrem-finance-data/page-extensions__item_extension__project____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__item_extension__project____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__project____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__project____lookupDialogTitle": "Select project", "@sage/xtrem-finance-data/page-extensions__item_extension__project____title": "Project", "@sage/xtrem-finance-data/page-extensions__item_extension__task____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__item_extension__task____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__item_extension__task____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__item_extension__task____lookupDialogTitle": "Select task", "@sage/xtrem-finance-data/page-extensions__item_extension__task____title": "Task", "@sage/xtrem-finance-data/page-extensions__labor_resource_extension__postingClass____lookupDialogTitle": "Select posting class", "@sage/xtrem-finance-data/page-extensions__labor_resource_extension__postingClass____title": "Posting class", "@sage/xtrem-finance-data/page-extensions__machine_resource_extension__postingClass____lookupDialogTitle": "Select posting class", "@sage/xtrem-finance-data/page-extensions__machine_resource_extension__postingClass____title": "Posting class", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____lookupDialogTitle": "Select dimension 01", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension01____title": "Dimension 01", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____lookupDialogTitle": "Select dimension 02", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension02____title": "Dimension 02", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____lookupDialogTitle": "Select dimension 03", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension03____title": "Dimension 03", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____lookupDialogTitle": "Select dimension 04", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension04____title": "Dimension 04", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____lookupDialogTitle": "Select dimension 05", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension05____title": "Dimension 05", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____lookupDialogTitle": "Select dimension 06", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension06____title": "Dimension 06", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____lookupDialogTitle": "Select dimension 07", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension07____title": "Dimension 07", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____lookupDialogTitle": "Select dimension 08", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension08____title": "Dimension 08", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____lookupDialogTitle": "Select dimension 09", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension09____title": "Dimension 09", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____lookupDialogTitle": "Select dimension 10", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension10____title": "Dimension 10", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____lookupDialogTitle": "Select dimension 11", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension11____title": "Dimension 11", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____lookupDialogTitle": "Select dimension 12", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension12____title": "Dimension 12", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____lookupDialogTitle": "Select dimension 13", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension13____title": "Dimension 13", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____lookupDialogTitle": "Select dimension 14", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension14____title": "Dimension 14", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____lookupDialogTitle": "Select dimension 15", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension15____title": "Dimension 15", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____lookupDialogTitle": "Select dimension 16", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension16____title": "Dimension 16", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____lookupDialogTitle": "Select dimension 17", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension17____title": "Dimension 17", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____lookupDialogTitle": "Select dimension 18", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension18____title": "Dimension 18", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____lookupDialogTitle": "Select dimension 19", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension19____title": "Dimension 19", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____lookupDialogTitle": "Select dimension 20", "@sage/xtrem-finance-data/page-extensions__site_extension__dimension20____title": "Dimension 20", "@sage/xtrem-finance-data/page-extensions__site_extension__dimensionBlock____title": "Dimensions", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____lookupDialogTitle": "Select employee", "@sage/xtrem-finance-data/page-extensions__site_extension__employee____title": "Employee", "@sage/xtrem-finance-data/page-extensions__site_extension__financialSection____title": "Financial", "@sage/xtrem-finance-data/page-extensions__site_extension__project____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__site_extension__project____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__project____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__project____lookupDialogTitle": "Select project", "@sage/xtrem-finance-data/page-extensions__site_extension__project____title": "Project", "@sage/xtrem-finance-data/page-extensions__site_extension__task____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__site_extension__task____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__site_extension__task____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__site_extension__task____lookupDialogTitle": "Select task", "@sage/xtrem-finance-data/page-extensions__site_extension__task____title": "Task", "@sage/xtrem-finance-data/page-extensions__supplier_extension____navigationPanel__listItem__datevId__title": "DATEV ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension____navigationPanel__listItem__line11__title": "Posting class", "@sage/xtrem-finance-data/page-extensions__supplier_extension__datevId____title": "DATEV ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____lookupDialogTitle": "Select dimension 01", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension01____title": "Dimension 01", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____lookupDialogTitle": "Select dimension 02", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension02____title": "Dimension 02", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____lookupDialogTitle": "Select dimension 03", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension03____title": "Dimension 03", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____lookupDialogTitle": "Select dimension 04", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension04____title": "Dimension 04", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____lookupDialogTitle": "Select dimension 05", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension05____title": "Dimension 05", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____lookupDialogTitle": "Select dimension 06", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension06____title": "Dimension 06", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____lookupDialogTitle": "Select dimension 07", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension07____title": "Dimension 07", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____lookupDialogTitle": "Select dimension 08", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension08____title": "Dimension 08", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____lookupDialogTitle": "Select dimension 09", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension09____title": "Dimension 09", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____lookupDialogTitle": "Select dimension 10", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension10____title": "Dimension 10", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____lookupDialogTitle": "Select dimension 11", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension11____title": "Dimension 11", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____lookupDialogTitle": "Select dimension 12", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension12____title": "Dimension 12", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____lookupDialogTitle": "Select dimension 13", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension13____title": "Dimension 13", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____lookupDialogTitle": "Select dimension 14", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension14____title": "Dimension 14", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____lookupDialogTitle": "Select dimension 15", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension15____title": "Dimension 15", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____lookupDialogTitle": "Select dimension 16", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension16____title": "Dimension 16", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____lookupDialogTitle": "Select dimension 17", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension17____title": "Dimension 17", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____lookupDialogTitle": "Select dimension 18", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension18____title": "Dimension 18", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____lookupDialogTitle": "Select dimension 19", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension19____title": "Dimension 19", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____lookupDialogTitle": "Select dimension 20", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimension20____title": "Dimension 20", "@sage/xtrem-finance-data/page-extensions__supplier_extension__dimensionBlock____title": "Dimensions", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____lookupDialogTitle": "Select employee", "@sage/xtrem-finance-data/page-extensions__supplier_extension__employee____title": "Employee", "@sage/xtrem-finance-data/page-extensions__supplier_extension__postingClass____lookupDialogTitle": "Select posting class", "@sage/xtrem-finance-data/page-extensions__supplier_extension__postingClass____title": "Posting class", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____lookupDialogTitle": "Select project", "@sage/xtrem-finance-data/page-extensions__supplier_extension__project____title": "Project", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____columns__title__id": "ID", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____columns__title__name": "Name", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____lookupDialogTitle": "Select task", "@sage/xtrem-finance-data/page-extensions__supplier_extension__task____title": "Task", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionBlock____title": "Finance", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionCriteriaBlock____title": "Criteria", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionDocumentType____title": "Document type", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___createStamp": "<PERSON><PERSON>", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___id": "ID", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__batchId": "Batch ID", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__documentNumber": "Document number", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__documentType": "Document type", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__message": "Message", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__postingStatus": "Status", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__status": "Notification status", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__targetDocumentType": "Target document type", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionHistory____title": "Results", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionSection____title": "Finance", "@sage/xtrem-finance-data/page-extensions__sys_notification_history_extension__financeTransactionStatus____title": "Status", "@sage/xtrem-finance-data/page-extensions__tax_extension____navigationPanel__listItem__line7__title": "Posting class", "@sage/xtrem-finance-data/page-extensions__tax_extension____navigationPanel__listItem__postingKey__title": "Posting key", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingClass____columns__title__type": "Type", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingClass____lookupDialogTitle": "Select posting class", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingClass____title": "Posting class", "@sage/xtrem-finance-data/page-extensions__tax_extension__postingKey____title": "Posting key", "@sage/xtrem-finance-data/page-extensions__tool_resource_extension__postingClass____lookupDialogTitle": "Select posting class", "@sage/xtrem-finance-data/page-extensions__tool_resource_extension__postingClass____title": "Posting class", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__datevId__title": "DATEV ID", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line_4__title": "Direct entry forbidden", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line_5__title": "Chart of accounts", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line3__title": "Control", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line4__title": "Direct entry forbidden", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line5__title": "Chart of accounts", "@sage/xtrem-finance-data/pages__account____navigationPanel__listItem__line6__title": "Tax management", "@sage/xtrem-finance-data/pages__account____objectTypePlural": "Accounts", "@sage/xtrem-finance-data/pages__account____objectTypeSingular": "Account", "@sage/xtrem-finance-data/pages__account____title": "Account", "@sage/xtrem-finance-data/pages__account___id____title": "ID", "@sage/xtrem-finance-data/pages__account__attributeDimensionTypes____columns__title__type": "Analytical type", "@sage/xtrem-finance-data/pages__account__attributeDimensionTypes____title": "Required dimensions", "@sage/xtrem-finance-data/pages__account__attributeDimensionTypesBlock____title": "Required dimensions", "@sage/xtrem-finance-data/pages__account__chartOfAccount____lookupDialogTitle": "Select chart of accounts", "@sage/xtrem-finance-data/pages__account__chartOfAccount____title": "Chart of accounts", "@sage/xtrem-finance-data/pages__account__datevIdString____title": "DATEV ID", "@sage/xtrem-finance-data/pages__account__datevSection____title": "DATEV", "@sage/xtrem-finance-data/pages__account__empty_tax_warning": "You need to enter the tax code when the Automatic account is enabled.", "@sage/xtrem-finance-data/pages__account__headerSection____title": "Header section", "@sage/xtrem-finance-data/pages__account__id____title": "ID", "@sage/xtrem-finance-data/pages__account__isActive____title": "Active", "@sage/xtrem-finance-data/pages__account__isAutomaticAccount____title": "Automatic account", "@sage/xtrem-finance-data/pages__account__isControl____title": "Control", "@sage/xtrem-finance-data/pages__account__isDirectEntryForbidden____title": "Direct entry forbidden", "@sage/xtrem-finance-data/pages__account__mainSection____title": "General", "@sage/xtrem-finance-data/pages__account__name____title": "Name", "@sage/xtrem-finance-data/pages__account__option_menu____title__all": "All", "@sage/xtrem-finance-data/pages__account__save____title": "Save", "@sage/xtrem-finance-data/pages__account__tax____columns__lookupDialogTitle__country": "Select country", "@sage/xtrem-finance-data/pages__account__tax____title": "Tax", "@sage/xtrem-finance-data/pages__account__taxManagement____title": "Tax management", "@sage/xtrem-finance-data/pages__account__TaxManagementBlock____title": "Tax management", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__item__title": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__line3__title": "Attribute type", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__line3Right__title": "Restricted to name", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__restrictedTo__title": "Restricted to ID", "@sage/xtrem-finance-data/pages__attribute____navigationPanel__listItem__site__title": "Site", "@sage/xtrem-finance-data/pages__attribute____objectTypePlural": "Attributes", "@sage/xtrem-finance-data/pages__attribute____objectTypeSingular": "Attribute", "@sage/xtrem-finance-data/pages__attribute____subtitle": "Attribute", "@sage/xtrem-finance-data/pages__attribute____title": "Attribute", "@sage/xtrem-finance-data/pages__attribute__attributeRestrictedTo____lookupDialogTitle": "Select attribute restricted to", "@sage/xtrem-finance-data/pages__attribute__attributeType____lookupDialogTitle": "Select attribute type", "@sage/xtrem-finance-data/pages__attribute__item____lookupDialogTitle": "Select item", "@sage/xtrem-finance-data/pages__attribute__mainSection____title": "General", "@sage/xtrem-finance-data/pages__attribute__site____lookupDialogTitle": "Select site", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__listItem__line_4__title": "Filter", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__listItem__line3__title": "Node link", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__listItem__line4__title": "Filter", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-finance-data/pages__attribute_type____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-finance-data/pages__attribute_type____objectTypePlural": "Attribute types", "@sage/xtrem-finance-data/pages__attribute_type____objectTypeSingular": "Attribute type", "@sage/xtrem-finance-data/pages__attribute_type____title": "Attribute type", "@sage/xtrem-finance-data/pages__attribute_type__attributeTypeRestrictedTo____lookupDialogTitle": "Select attribute type restricted to", "@sage/xtrem-finance-data/pages__attribute_type__attributeTypeRestrictedTo____title": "Restricted to", "@sage/xtrem-finance-data/pages__attribute_type__id____title": "ID", "@sage/xtrem-finance-data/pages__attribute_type__isActive____title": "Active", "@sage/xtrem-finance-data/pages__attribute_type__linked_to_not_valid": "{{linkedTo}} linked to is not valid.", "@sage/xtrem-finance-data/pages__attribute_type__linkedTo____title": "Linked to", "@sage/xtrem-finance-data/pages__attribute_type__mainSection____title": "General", "@sage/xtrem-finance-data/pages__attribute_type__name____title": "Name", "@sage/xtrem-finance-data/pages__attribute_type__nodeLink____title": "Node link", "@sage/xtrem-finance-data/pages__attribute_type__queryFilter____title": "Filter", "@sage/xtrem-finance-data/pages__bank_account____navigationPanel__listItem__line_4__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__bank_account____navigationPanel__listItem__line3__title": "Financial site", "@sage/xtrem-finance-data/pages__bank_account____navigationPanel__listItem__line4__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__bank_account____objectTypePlural": "Bank accounts", "@sage/xtrem-finance-data/pages__bank_account____objectTypeSingular": "Bank account", "@sage/xtrem-finance-data/pages__bank_account____title": "Bank account", "@sage/xtrem-finance-data/pages__bank_account__currency____columns__title__id": "ISO 4217 code", "@sage/xtrem-finance-data/pages__bank_account__currency____columns__title__symbol": "Symbol", "@sage/xtrem-finance-data/pages__bank_account__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-finance-data/pages__bank_account__currency____placeholder": "Select currency", "@sage/xtrem-finance-data/pages__bank_account__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__bank_account__financialSite____columns__title__currency__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance-data/pages__bank_account__financialSite____columns__title__id": "ID ", "@sage/xtrem-finance-data/pages__bank_account__financialSite____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__bank_account__financialSite____lookupDialogTitle": "Select financial site", "@sage/xtrem-finance-data/pages__bank_account__financialSite____title": "Financial site", "@sage/xtrem-finance-data/pages__bank_account__headerSection____title": "Header section", "@sage/xtrem-finance-data/pages__bank_account__id____title": "ID", "@sage/xtrem-finance-data/pages__bank_account__isActive____title": "Active", "@sage/xtrem-finance-data/pages__bank_account__mainSection____title": "Information", "@sage/xtrem-finance-data/pages__bank_account__name____title": "Name", "@sage/xtrem-finance-data/pages__bank-account__option_menu____title__all": "All", "@sage/xtrem-finance-data/pages__business_entity_customer__datev_id": "DATEV ID", "@sage/xtrem-finance-data/pages__business_entity_customer__wrong_datev_id": "Enter a number between {{first}} and {{last}}.", "@sage/xtrem-finance-data/pages__business_entity_customer_extension__datev_id_warning": "You need to enter the DATEV ID before extracting the data when the DATEV integration is active.", "@sage/xtrem-finance-data/pages__company__dimensions_setup_update_confirmation": "Confirm setup", "@sage/xtrem-finance-data/pages__company__dimensions_setup_update_confirmation_apply": "You are about to apply these settings to all pending and future documents.", "@sage/xtrem-finance-data/pages__company_extension__duplicate_error": "There are duplicate documents. Make sure that each line has a unique document.", "@sage/xtrem-finance-data/pages__company_extension__project_task_warning": "The {{attributeName}} is required when you enter a {{restrictedToName}}.", "@sage/xtrem-finance-data/pages__datev_configuration____title": "DATEV configuration", "@sage/xtrem-finance-data/pages__datev_configuration__customer_id_range": "From {{start}} to {{end}}", "@sage/xtrem-finance-data/pages__datev_configuration__customerIdRange____title": "Customer ID range", "@sage/xtrem-finance-data/pages__datev_configuration__customerSupplierLength____title": "Customer and supplier ID length", "@sage/xtrem-finance-data/pages__datev_configuration__isActive____title": "Active", "@sage/xtrem-finance-data/pages__datev_configuration__mainSection____title": "General", "@sage/xtrem-finance-data/pages__datev_configuration__save____title": "Save", "@sage/xtrem-finance-data/pages__datev_configuration__skrCoaString____title": "SKR", "@sage/xtrem-finance-data/pages__datev_configuration__supplier_id_range": "From {{start}} to {{end}}", "@sage/xtrem-finance-data/pages__datev_configuration__supplierIdRange____title": "Supplier ID range", "@sage/xtrem-finance-data/pages__datev_configuration_save_warnings": "Warnings whilst saving:", "@sage/xtrem-finance-data/pages__dimension____navigationPanel__listItem__line3__title": "Dimension type", "@sage/xtrem-finance-data/pages__dimension____objectTypePlural": "Dimensions", "@sage/xtrem-finance-data/pages__dimension____objectTypeSingular": "Dimension", "@sage/xtrem-finance-data/pages__dimension____title": "Dimension", "@sage/xtrem-finance-data/pages__dimension__dimensionType____lookupDialogTitle": "Select dimension type", "@sage/xtrem-finance-data/pages__dimension__id____title": "ID", "@sage/xtrem-finance-data/pages__dimension__isActive____title": "Active", "@sage/xtrem-finance-data/pages__dimension__mainSection____title": "General", "@sage/xtrem-finance-data/pages__dimension__name____title": "Name", "@sage/xtrem-finance-data/pages__dimension_panel____title": "Dimensions", "@sage/xtrem-finance-data/pages__dimension_panel__apply____title": "Apply to all lines", "@sage/xtrem-finance-data/pages__dimension_panel__applyAll____title": "Apply to all", "@sage/xtrem-finance-data/pages__dimension_panel__applyOnNew____title": "Apply to new lines only", "@sage/xtrem-finance-data/pages__dimension_panel__applyReleasedItem____title": "Apply to released item only", "@sage/xtrem-finance-data/pages__dimension_panel__cancel____title": "Cancel", "@sage/xtrem-finance-data/pages__dimension_panel__defaulted_from_item_plural": "{{itemList}} values default from the item and do not display at this level. You need to enter these values on the line for each item.", "@sage/xtrem-finance-data/pages__dimension_panel__defaulted_from_item_singular": "{{itemList}} value defaults from the item and does not display at this level. You need to enter this value on the line for each item.", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____lookupDialogTitle": "Select dimension 01", "@sage/xtrem-finance-data/pages__dimension_panel__dimension01____title": "Dimension 01", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____lookupDialogTitle": "Select dimension 02", "@sage/xtrem-finance-data/pages__dimension_panel__dimension02____title": "Dimension 02", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____lookupDialogTitle": "Select dimension 03", "@sage/xtrem-finance-data/pages__dimension_panel__dimension03____title": "Dimension 03", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____lookupDialogTitle": "Select dimension 04", "@sage/xtrem-finance-data/pages__dimension_panel__dimension04____title": "Dimension 04", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____lookupDialogTitle": "Select dimension 05", "@sage/xtrem-finance-data/pages__dimension_panel__dimension05____title": "Dimension 05", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____lookupDialogTitle": "Select dimension 06", "@sage/xtrem-finance-data/pages__dimension_panel__dimension06____title": "Dimension 06", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____lookupDialogTitle": "Select dimension 07", "@sage/xtrem-finance-data/pages__dimension_panel__dimension07____title": "Dimension 07", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____lookupDialogTitle": "Select dimension 08", "@sage/xtrem-finance-data/pages__dimension_panel__dimension08____title": "Dimension 08", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____lookupDialogTitle": "Select dimension 09", "@sage/xtrem-finance-data/pages__dimension_panel__dimension09____title": "Dimension 09", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____lookupDialogTitle": "Select dimension 10", "@sage/xtrem-finance-data/pages__dimension_panel__dimension10____title": "Dimension 10", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____lookupDialogTitle": "Select dimension 11", "@sage/xtrem-finance-data/pages__dimension_panel__dimension11____title": "Dimension 11", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____lookupDialogTitle": "Select dimension 12", "@sage/xtrem-finance-data/pages__dimension_panel__dimension12____title": "Dimension 12", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____lookupDialogTitle": "Select dimension 13", "@sage/xtrem-finance-data/pages__dimension_panel__dimension13____title": "Dimension 13", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____lookupDialogTitle": "Select dimension 14", "@sage/xtrem-finance-data/pages__dimension_panel__dimension14____title": "Dimension 14", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____lookupDialogTitle": "Select dimension 15", "@sage/xtrem-finance-data/pages__dimension_panel__dimension15____title": "Dimension 15", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____lookupDialogTitle": "Select dimension 16", "@sage/xtrem-finance-data/pages__dimension_panel__dimension16____title": "Dimension 16", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____lookupDialogTitle": "Select dimension 17", "@sage/xtrem-finance-data/pages__dimension_panel__dimension17____title": "Dimension 17", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____lookupDialogTitle": "Select dimension 18", "@sage/xtrem-finance-data/pages__dimension_panel__dimension18____title": "Dimension 18", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____lookupDialogTitle": "Select dimension 19", "@sage/xtrem-finance-data/pages__dimension_panel__dimension19____title": "Dimension 19", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__columns__dimensionType__docProperty__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__title__dimensionType__docProperty": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____lookupDialogTitle": "Select dimension 20", "@sage/xtrem-finance-data/pages__dimension_panel__dimension20____title": "Dimension 20", "@sage/xtrem-finance-data/pages__dimension_panel__employee____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/pages__dimension_panel__employee____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__employee____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__employee____lookupDialogTitle": "Select employee", "@sage/xtrem-finance-data/pages__dimension_panel__employee____title": "Employee", "@sage/xtrem-finance-data/pages__dimension_panel__ok____title": "OK", "@sage/xtrem-finance-data/pages__dimension_panel__page_without_dimensions": "The page needs to have attributes and dimensions.", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__project____columns__title__site__id": "Site", "@sage/xtrem-finance-data/pages__dimension_panel__project____lookupDialogTitle": "Select project", "@sage/xtrem-finance-data/pages__dimension_panel__project____title": "Project", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__attributeType__id": "Attribute type", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__id": "ID", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__item__id": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__dimension_panel__task____columns__title__name": "Name", "@sage/xtrem-finance-data/pages__dimension_panel__task____lookupDialogTitle": "Select task", "@sage/xtrem-finance-data/pages__dimension_panel__task____title": "Task", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__listItem__titleRight__title": "Document property", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-finance-data/pages__dimension_type____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-finance-data/pages__dimension_type____objectTypePlural": "Dimension types", "@sage/xtrem-finance-data/pages__dimension_type____objectTypeSingular": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_type____title": "Dimension type", "@sage/xtrem-finance-data/pages__dimension_type___id____title": "ID", "@sage/xtrem-finance-data/pages__dimension_type__docProperty____title": "Document property", "@sage/xtrem-finance-data/pages__dimension_type__isActive____title": "Active", "@sage/xtrem-finance-data/pages__dimension_type__isUsed____title": "Used", "@sage/xtrem-finance-data/pages__dimension_type__mainSection____title": "General", "@sage/xtrem-finance-data/pages__dimension_type__name____title": "Name", "@sage/xtrem-finance-data/pages__journal____navigationPanel__listItem__line6__title": "Sequence number", "@sage/xtrem-finance-data/pages__journal____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-finance-data/pages__journal____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-finance-data/pages__journal____objectTypePlural": "Journals", "@sage/xtrem-finance-data/pages__journal____objectTypeSingular": "Journal", "@sage/xtrem-finance-data/pages__journal____title": "Journal", "@sage/xtrem-finance-data/pages__journal___id____title": "ID", "@sage/xtrem-finance-data/pages__journal__generalSection____title": "General", "@sage/xtrem-finance-data/pages__journal__id____title": "ID", "@sage/xtrem-finance-data/pages__journal__isActive____title": "Active", "@sage/xtrem-finance-data/pages__journal__isSubjectToGlTaxExcludedAmount____title": "Assigned to GL excluding VAT", "@sage/xtrem-finance-data/pages__journal__legislation____lookupDialogTitle": "Select legislation", "@sage/xtrem-finance-data/pages__journal__legislation____placeholder": "Select ...", "@sage/xtrem-finance-data/pages__journal__name____title": "Name", "@sage/xtrem-finance-data/pages__journal__numberingBlock____title": "Numbering", "@sage/xtrem-finance-data/pages__journal__sequence____lookupDialogTitle": "Select sequence number", "@sage/xtrem-finance-data/pages__journal__sequence____title": "Sequence number", "@sage/xtrem-finance-data/pages__journal__taxImpact____title": "Tax impact", "@sage/xtrem-finance-data/pages__journal_entry_type____objectTypePlural": "Journal entry types", "@sage/xtrem-finance-data/pages__journal_entry_type____objectTypeSingular": "Journal entry type", "@sage/xtrem-finance-data/pages__journal_entry_type____title": "Journal entry type", "@sage/xtrem-finance-data/pages__journal_entry_type___id____title": "ID", "@sage/xtrem-finance-data/pages__journal_entry_type__documentType____title": "Document type", "@sage/xtrem-finance-data/pages__journal_entry_type__generalSection____title": "General", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____columns__title__accountTypeName": "Name", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____columns__title__isDetailed": "Detailed", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____columns__title__postingClassType": "Posting class type", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____lookupDialogTitle": "Select account type", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAccountType____title": "Account type", "@sage/xtrem-finance-data/pages__journal_entry_type__headerAmountType____title": "Amount type", "@sage/xtrem-finance-data/pages__journal_entry_type__headerDescription____title": "Description", "@sage/xtrem-finance-data/pages__journal_entry_type__headerJournal____lookupDialogTitle": "Select journal", "@sage/xtrem-finance-data/pages__journal_entry_type__headerJournal____title": "Journal", "@sage/xtrem-finance-data/pages__journal_entry_type__headerPostingDate____title": "Posting date", "@sage/xtrem-finance-data/pages__journal_entry_type__headerSetupBlock____title": "Header setup", "@sage/xtrem-finance-data/pages__journal_entry_type__immediatePosting____title": "Immediate posting", "@sage/xtrem-finance-data/pages__journal_entry_type__isActive____title": "Active", "@sage/xtrem-finance-data/pages__journal_entry_type__legislation____lookupDialogTitle": "Select legislation", "@sage/xtrem-finance-data/pages__journal_entry_type__legislation____placeholder": "Select legislation", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__columns__accountType__accountTypeName__title": "Name", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__columns__accountType__accountTypeName__title__2": "Posting class type", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__columns__accountType__accountTypeName__title__3": "Detailed", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__accountType__accountTypeName": "Account type", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__amountType": "Amount type", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__commonReference": "Common reference", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__contraJournalEntryTypeLine__accountType__accountTypeName": "Contra account type", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__contraJournalEntryTypeLine__amountType": "Contra account amount type", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isLandedCostItemAllowed": "Landed cost items", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isNonStockItemAllowed": "Non-stock items", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isServiceItemAllowed": "Service items", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__isStockItemAllowed": "Stock items", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__movementType": "Movement type", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____columns__title__sign": "Sign", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____dropdownActions__title": "Linked posting class", "@sage/xtrem-finance-data/pages__journal_entry_type__lines____title": "Line setup", "@sage/xtrem-finance-data/pages__journal_entry_type__name____title": "Name", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____columns__title__account__composedDescription": "Account", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____columns__title__postingClass__name": "Posting class name", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__account__composedDescription": "Account", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__account__composedDescription__2": "Account", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__postingClass__name": "Posting class name", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____levels__columns__title__tax__name": "Tax", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassAccounts____title": "Line accounts", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____columns__title__account__composedDescription": "Account", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____columns__title__postingClass__name": "Posting class name", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__account__composedDescription": "Account", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__account__composedDescription__2": "Account", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__postingClass__name": "Posting class name", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____levels__columns__title__tax__name": "Tax", "@sage/xtrem-finance-data/pages__journal_entry_type__postingClassHeaderAccounts____title": "Header accounts", "@sage/xtrem-finance-data/pages__journal_entry_type__side_panel_linked_posting_class_title": "Linked posting class", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelAccountTypeName____title": "Account type name", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelGeneralSection____title": "", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelHeaderAccountTypeName____title": "Account type name", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelHeaderBlock____title": "Header", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelHeaderIsDetailed____title": "Detailed", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelIsDetailed____title": "Detailed", "@sage/xtrem-finance-data/pages__journal_entry_type__sidePanelLinesBlock____title": "Lines", "@sage/xtrem-finance-data/pages__journal_entry_type__targetDocumentType____title": "Target document type", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__listItem__line3__title": "Detailed", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__2": "Company", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__3": "Customer", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__4": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__5": "Resource", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__6": "Supplier", "@sage/xtrem-finance-data/pages__posting_class____navigationPanel__optionsMenu__title__7": "Tax", "@sage/xtrem-finance-data/pages__posting_class____objectTypePlural": "Posting classes", "@sage/xtrem-finance-data/pages__posting_class____objectTypeSingular": "Posting class", "@sage/xtrem-finance-data/pages__posting_class____title": "Posting class", "@sage/xtrem-finance-data/pages__posting_class___id____title": "ID", "@sage/xtrem-finance-data/pages__posting_class__accountTypes____columns__title__chartOfAccount__legislation__name": "Legislation", "@sage/xtrem-finance-data/pages__posting_class__accountTypes____columns__title__definition__accountTypeName": "Account type name", "@sage/xtrem-finance-data/pages__posting_class__accountTypes____title": "Account types", "@sage/xtrem-finance-data/pages__posting_class__addLine____title": "Add line", "@sage/xtrem-finance-data/pages__posting_class__copyAccounts____title": "Copy accounts", "@sage/xtrem-finance-data/pages__posting_class__finance_item_type_not_valid": "{{financeItemType}} finance item type is not valid.", "@sage/xtrem-finance-data/pages__posting_class__financeItemType____title": "Item type", "@sage/xtrem-finance-data/pages__posting_class__generalBlock____title": "Posting class", "@sage/xtrem-finance-data/pages__posting_class__generalSection____title": "General", "@sage/xtrem-finance-data/pages__posting_class__id____title": "ID", "@sage/xtrem-finance-data/pages__posting_class__isDetailed____title": "Detailed", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__account__name__title": "Name", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__account__name__title__2": "ID", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__definition__accountTypeName__title": "Name", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__definition__accountTypeName__title__2": "ID", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__columns__definition__accountTypeName__title__3": "Detailed", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__account__name": "Account", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__accountId": "Account ID", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__chartOfAccount__name": "Chart of accounts", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__definition__accountTypeName": "Account type name", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isLandedCostItemAllowed": "Landed cost items", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isNonStockItemAllowed": "Non-stock items", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isServiceItemAllowed": "Service items", "@sage/xtrem-finance-data/pages__posting_class__lines____columns__title__isStockItemAllowed": "Stock items", "@sage/xtrem-finance-data/pages__posting_class__lines____dropdownActions__title": "Delete", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title": "Name", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__2": "ID", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__3": "Name", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__4": "ID", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__account__name__title__5": "Chart of accounts", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__definition__accountTypeName__title": "Name", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__definition__accountTypeName__title__2": "ID", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__tax__name__title": "Name", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__tax__name__title__2": "ID", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__columns__tax__name__title__3": "Country", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__id": "Account ID", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__id__2": "Account ID", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__name": "Account", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__account__name__2": "Account", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__chartOfAccount__name": "Chart of accounts", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__definition__accountTypeName": "Account type name", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__definition__additionalCriteria": "Additional criteria", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isLandedCostItemAllowed": "Landed cost items", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isNonStockItemAllowed": "Non-stock items", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isServiceItemAllowed": "Service items", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__isStockItemAllowed": "Stock items", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__tax__name": "Tax", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__columns__title__tax__taxCategory__name": "Tax category", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__dropdownActions__title": "Add detail", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__dropdownActions__title__2": "Delete", "@sage/xtrem-finance-data/pages__posting_class__lines____levels__dropdownActions__title__3": "Delete", "@sage/xtrem-finance-data/pages__posting_class__lines____title": "Lines", "@sage/xtrem-finance-data/pages__posting_class__loadLinesBlock____title": "Posting class definitions", "@sage/xtrem-finance-data/pages__posting_class__loadLinesSection____title": "Load lines", "@sage/xtrem-finance-data/pages__posting_class__name____title": "Name", "@sage/xtrem-finance-data/pages__posting_class__postingClass____columns__title__type": "Type", "@sage/xtrem-finance-data/pages__posting_class__postingClass____lookupDialogTitle": "Select posting class", "@sage/xtrem-finance-data/pages__posting_class__postingClass____placeholder": "Select posting class", "@sage/xtrem-finance-data/pages__posting_class__postingClass____title": "Posting class template", "@sage/xtrem-finance-data/pages__posting_class__selectAll____title": "Select all lines", "@sage/xtrem-finance-data/pages__posting_class__selectLines____title": "Select lines", "@sage/xtrem-finance-data/pages__posting_class__type____title": "Type", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__listItem__line_4__title": "Detailed", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__listItem__line4__title": "Detailed", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__listItem__titleRight__title": "Legislation", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__2": "Customer", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__3": "<PERSON><PERSON>", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__4": "Resource", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__5": "Supplier", "@sage/xtrem-finance-data/pages__posting_class_definition____navigationPanel__optionsMenu__title__6": "Tax", "@sage/xtrem-finance-data/pages__posting_class_definition____objectTypePlural": "Posting class definitions", "@sage/xtrem-finance-data/pages__posting_class_definition____objectTypeSingular": "Posting class definition", "@sage/xtrem-finance-data/pages__posting_class_definition____title": "Posting class definition", "@sage/xtrem-finance-data/pages__posting_class_definition___id____title": "ID", "@sage/xtrem-finance-data/pages__posting_class_definition__accountTypeName____title": "Account type name", "@sage/xtrem-finance-data/pages__posting_class_definition__additionalCriteria____title": "Additional criteria", "@sage/xtrem-finance-data/pages__posting_class_definition__financeItemType____title": "Item type", "@sage/xtrem-finance-data/pages__posting_class_definition__generalBlock____title": "Posting class definition", "@sage/xtrem-finance-data/pages__posting_class_definition__generalSection____title": "General", "@sage/xtrem-finance-data/pages__posting_class_definition__id____title": "ID", "@sage/xtrem-finance-data/pages__posting_class_definition__isDetailed____title": "Detailed", "@sage/xtrem-finance-data/pages__posting_class_definition__legislation____lookupDialogTitle": "Select legislation", "@sage/xtrem-finance-data/pages__posting_class_definition__legislation____placeholder": "Select ...", "@sage/xtrem-finance-data/pages__posting_class_definition__postingClassType____title": "Posting class type", "@sage/xtrem-finance-data/pages__posting_class_duplicate__not_allowed": "The operation failed due to a database integrity constraint.", "@sage/xtrem-finance-data/pages__posting-class__load_lines": "Load lines", "@sage/xtrem-finance-data/pages__posting-class__no_new_lines": "There are no lines to add.", "@sage/xtrem-finance-data/pages__posting-class_line_deletion": "", "@sage/xtrem-finance-data/pages__posting-class_line_deletion_title": "Confirm line deletion", "@sage/xtrem-finance-data/pages__posting-class-template__info": "Only the lines that apply to the same item types are selected.", "@sage/xtrem-finance-data/pages__posting-class-template__info_title": "Posting class", "@sage/xtrem-finance-data/pages_company_confirmation": "Apply", "@sage/xtrem-finance-data/permission__create__name": "Create", "@sage/xtrem-finance-data/permission__delete__name": "Delete", "@sage/xtrem-finance-data/permission__manage__name": "Manage", "@sage/xtrem-finance-data/permission__read__name": "Read", "@sage/xtrem-finance-data/permission__update__name": "Update", "@sage/xtrem-finance-data/service_options__discount_payment_tracking_option__name": "Discount payment tracking option", "@sage/xtrem-finance-data/service_options__payment_tracking_option__intacct_is_active": "Payment tracking is not possible if Sage Intacct integration is active.", "@sage/xtrem-finance-data/service_options__payment_tracking_option__name": "Payment tracking option", "@sage/xtrem-finance-data/sys__notification_history__search": "Search", "@sage/xtrem-finance-data/update-account-tax-management-context": "You are about to set the account management to 'Tax'.", "@sage/xtrem-finance-data/update-account-tax-management-title": "Confirm account tax management update."}