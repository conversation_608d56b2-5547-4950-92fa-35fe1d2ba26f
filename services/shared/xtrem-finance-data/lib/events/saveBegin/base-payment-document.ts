import type * as xtremFinanceData from '../..';

export async function saveBegin(basePaymentDocument: xtremFinanceData.nodes.BasePaymentDocument): Promise<void> {
    const numberOfLines = await basePaymentDocument.lines.length;
    const amountBankCurrency = await basePaymentDocument.amountBankCurrency;
    let totalApplied = 0;
    const decimalDigits = (await (await (await basePaymentDocument.bankAccount)?.currency)?.decimalDigits) ?? 2;
    const tmp = 10 ** decimalDigits;
    await basePaymentDocument.lines.forEach(async (line, indexLine) => {
        const lineAmountBankCurrency =
            indexLine === numberOfLines - 1
                ? Math.abs(amountBankCurrency - totalApplied)
                : Math.round(((amountBankCurrency * (await line.amount)) / (await basePaymentDocument.amount)) * tmp) /
                  tmp;
        totalApplied =
            (await line.origin) === 'invoice'
                ? totalApplied + lineAmountBankCurrency
                : totalApplied - lineAmountBankCurrency;
        await line.$.set({ amountBankCurrency: lineAmountBankCurrency });
    });
}
