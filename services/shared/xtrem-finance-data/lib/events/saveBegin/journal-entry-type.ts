import type * as xtremFinanceData from '../..';

export async function saveBegin(journalEntryType: xtremFinanceData.nodes.JournalEntryType): Promise<void> {
    if (
        ['purchaseInvoice', 'purchaseCreditMemo', 'salesInvoice', 'salesCreditMemo', 'apInvoice', 'arInvoice'].includes(
            await journalEntryType.documentType,
        )
    ) {
        await journalEntryType.$.set({ headerPostingDate: 'documentDate' });
        await journalEntryType.$.set({ immediatePosting: true });
    } else {
        await journalEntryType.$.set({ immediatePosting: false });
    }
}
