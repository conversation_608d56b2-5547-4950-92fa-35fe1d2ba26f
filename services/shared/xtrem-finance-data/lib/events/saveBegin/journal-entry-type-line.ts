import type * as xtremFinanceData from '../..';

export async function saveBegin(line: xtremFinanceData.nodes.JournalEntryTypeLine): Promise<void> {
    // force contraJournalEntryTypeLine to be null for Journal entry type with Target document type other than Journal entry
    if ((await (await line.journalEntryType).targetDocumentType) !== 'journalEntry') {
        await line.$.set({
            contraJournalEntryTypeLine: null,
        });
    }
}
