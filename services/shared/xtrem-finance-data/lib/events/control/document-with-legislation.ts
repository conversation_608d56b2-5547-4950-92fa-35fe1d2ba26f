import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremStructure from '@sage/xtrem-structure';
import type * as xtremFinanceData from '../..';

export async function legislationGenericControl(
    cx: ValidationContext,
    postingClassLegislation: xtremStructure.nodes.Legislation,
    entryType: xtremFinanceData.nodes.JournalEntryType,
): Promise<void> {
    if ((await postingClassLegislation.id) !== (await (await entryType.legislation).id))
        cx.error.addLocalized(
            '@sage/xtrem-finance-data/journal_entry_type_line_legislations_mismatch',
            'The legislation {{postingClassLegislation}} of the account type is different from the legislation {{typeLegislation}} of the journal entry type.',
            {
                postingClassLegislation: await postingClassLegislation.id,
                typeLegislation: await (await entryType.legislation).id,
            },
        );
}
