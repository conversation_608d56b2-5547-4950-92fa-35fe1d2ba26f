import type { integer, ValidationContext } from '@sage/xtrem-core';
import { inRange } from 'lodash';
import type * as xtremFinanceData from '../..';

// controls on the datev configuration for length fields:
export async function datevRangeControl(options: {
    cx: ValidationContext;
    configuration: xtremFinanceData.nodes.DatevConfiguration | null;
    value: integer;
    fromValue: integer;
    toValue: integer;
}): Promise<void> {
    if (!inRange(options.value, options.fromValue, options.toValue + 1)) {
        options.cx.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__datev_configuration__invalid_length',
            'Enter a number between {{fromValue}} and {{toValue}}.',
            { fromValue: options.fromValue, toValue: options.toValue },
        );
    }

    if (
        options.configuration &&
        (await options.configuration.accountLength) !== (await options.configuration.customerSupplierLength) - 1
    ) {
        options.cx.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__datev_configuration__wrong_length',
            'The account length does not match the customer and supplier ID length.',
        );
    }
}
