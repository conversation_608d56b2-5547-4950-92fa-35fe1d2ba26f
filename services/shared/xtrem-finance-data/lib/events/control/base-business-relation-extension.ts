import type { ExtensionMembers, ValidationContext } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { inRange } from 'lodash';
import * as xtremFinanceData from '../..';

// controls on the base business relation extension for uniqueness of DATEV ID field:
export async function datevIdControls(
    businessRelation: ExtensionMembers<
        xtremFinanceData.nodeExtensions.BaseBusinessRelationExtension & xtremMasterData.nodes.BaseBusinessRelation
    >,
    cx: ValidationContext,
): Promise<void> {
    if (
        (await businessRelation.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.datevOption)) &&
        (await businessRelation.datevId)
    ) {
        if (
            (await businessRelation.$.context.queryCount(xtremMasterData.nodes.BaseBusinessRelation, {
                filter: {
                    datevId: await businessRelation.datevId,
                    _id: { _ne: businessRelation._id },
                },
            })) > 0
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__base_business_relation__datev_id_not_unique',
                'The DATEV ID needs to be unique.',
            );
        }

        const datevConfiguration: xtremFinanceData.nodes.DatevConfiguration = await businessRelation.$.context.read(
            xtremFinanceData.nodes.DatevConfiguration,
            { id: 'DATEV' },
        );
        const fromValue =
            businessRelation instanceof xtremMasterData.nodes.Customer
                ? await datevConfiguration.customerRangeStart
                : await datevConfiguration.supplierRangeStart;

        const toValue =
            businessRelation instanceof xtremMasterData.nodes.Customer
                ? await datevConfiguration.customerRangeEnd
                : await datevConfiguration.supplierRangeEnd;
        if (
            (await businessRelation.datevId) &&
            !inRange((await businessRelation.datevId) ?? 0, fromValue, toValue + 1)
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__base_business_relation__datev_id_invalid',
                'The DATEV ID needs to be a number between {{fromValue}} and {{toValue}}.',
                { fromValue, toValue },
            );
        }
    }
}
