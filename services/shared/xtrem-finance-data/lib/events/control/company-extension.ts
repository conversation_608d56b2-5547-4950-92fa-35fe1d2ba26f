import type { ExtensionMembers, ValidationContext } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import { inRange } from 'lodash';
import * as xtremFinanceData from '../..';

// Common function for controls on the company extension for DATEV consultant/customer number fields:
// - if the legislation is Germany, this number is required
// - Enter a number between <first> and <last>
async function datevNumberControls(options: {
    company: ExtensionMembers<xtremFinanceData.nodeExtensions.CompanyExtension & xtremSystem.nodes.Company>;
    cx: ValidationContext;
    first: number;
    last: number;
    numberType: 'consultant' | 'customer';
}): Promise<void> {
    if (!(await options.company.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.datevOption))) return;

    const number =
        options.numberType === 'consultant'
            ? await options.company.datevConsultantNumber
            : await options.company.datevCustomerNumber;

    if ((await (await options.company.legislation).id) === 'DE') {
        if (!number) {
            options.cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__company__datev_number_mandatory',
                'If the legislation is Germany, this number is required.',
            );
        }
        if (number && !inRange(number ?? 0, options.first, options.last + 1)) {
            options.cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__company__datev_number_invalid',
                'Enter a number between {{first}} and {{last}}.',
                { first: options.first, last: options.last },
            );
        }
    } else if (number) {
        options.cx.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__company__datev_number_only_for_germany',
            'This number is only for German legislation.',
        );
    }
}

// controls on the company extension for DATEV consultant number field:
// - if the legislation is Germany, this number is required
// - Enter a number between 1001 and 9999999
export async function datevConsultantNumberControls(options: {
    company: ExtensionMembers<xtremFinanceData.nodeExtensions.CompanyExtension & xtremSystem.nodes.Company>;
    cx: ValidationContext;
}): Promise<void> {
    await datevNumberControls({
        ...options,
        first: 1001,
        last: 9999999,
        numberType: 'consultant',
    });
}

// controls on the company extension for DATEV customer number field:
// - if the legislation is Germany, this number is required
// - Enter a number between 1 and 99999
export async function datevCustomerNumberControls(options: {
    company: ExtensionMembers<xtremFinanceData.nodeExtensions.CompanyExtension & xtremSystem.nodes.Company>;
    cx: ValidationContext;
}): Promise<void> {
    await datevNumberControls({
        ...options,
        first: 1,
        last: 99999,
        numberType: 'customer',
    });
}

// control on the company extension for doWipPosting property:
// if the legislation is not in global array legislationsThatDoWipPosting (e.g. France), it can't be set to true
export async function wipPostingControl(options: {
    company: ExtensionMembers<xtremFinanceData.nodeExtensions.CompanyExtension & xtremSystem.nodes.Company>;
    doWipPosting: boolean;
    cx: ValidationContext;
}): Promise<void> {
    if (
        options.doWipPosting &&
        !xtremFinanceData.sharedFunctions.common.legislationsThatDoWipPosting.includes(
            await (
                await options.company.legislation
            ).id,
        )
    ) {
        options.cx.error.addLocalized(
            '@sage/xtrem-finance-data/node-extensions__company-extension__do_wip_posting_not_allowed_on_legislation',
            'You cannot post WIP for this legislation.',
        );
    }
}
