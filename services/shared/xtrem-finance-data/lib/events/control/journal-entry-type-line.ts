import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremFinanceData from '../..';

export async function contraJournalEntryTypeLineMandatory(
    line: xtremFinanceData.nodes.JournalEntryTypeLine,
    cx: ValidationContext,
): Promise<void> {
    if (
        (await (await line.journalEntryType).targetDocumentType) === 'journalEntry' &&
        !(await line.contraJournalEntryTypeLine)
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__journal-entry-type-line__contra_journal_entry_type_line_mandatory',
            'If the target document type is journal entry, the contra account is required.',
        );
    }
}
