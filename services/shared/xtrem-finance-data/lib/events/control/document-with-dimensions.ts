import type { Context, ValidationContext } from '@sage/xtrem-core';
import { asyncArray } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '../..';

export async function storedDimensionsGenericControl(
    context: Context,
    cx: ValidationContext,
    val: object | null,
): Promise<void> {
    if (val) {
        await asyncArray(Object.entries(val)).forEach(async ([dimensionType, dimension]) => {
            if (
                (await xtremFinanceData.functions.checkDimensionTypeInactive(
                    context,
                    xtremFinanceData.nodes.DimensionType,
                    dimensionType,
                )) ||
                (await xtremFinanceData.functions.checkValueInactive(
                    context,
                    xtremFinanceData.nodes.Dimension,
                    dimension as string,
                    {
                        dimensionType: { docProperty: dimensionType, isActive: true },
                    },
                ))
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/is_active_dimension_inactive',
                    'The {{dimension}} dimension or "{{dimensionType}}" dimension type is inactive.',
                    {
                        dimension,
                        dimensionType,
                    },
                );
            }
        });
    }
}

export async function storedAttributesGenericControl(
    context: Context,
    cx: ValidationContext,
    val: xtremMasterData.interfaces.StoredAttributes | null,
): Promise<void> {
    if (val) {
        const {
            employee,
            project,
            task,
            financialSite,
            businessSite,
            stockSite,
            manufacturingSite,
            customer,
            supplier,
            item,
            ...other
        } = val;
        if (Object.keys(other).length !== 0) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/is_active_invalid_attribute',
                'Attribute {{{other}}} invalid',
                {
                    other: JSON.stringify(other),
                },
            );
        }
        if (
            employee &&
            (await xtremFinanceData.functions.checkValueInactive(context, xtremFinanceData.nodes.Attribute, employee, {
                attributeType: { id: 'employee', isActive: true },
            }))
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/is_active_employee_attribute_inactive',
                'The {{employeeAttribute}} attribute or "employee" attribute type is inactive.',
                {
                    employeeAttribute: employee,
                },
            );
        }

        if (
            project &&
            (await xtremFinanceData.functions.checkValueInactive(context, xtremFinanceData.nodes.Attribute, project, {
                attributeType: { id: 'project', isActive: true },
            }))
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/is_active_project_attribute_inactive',
                'The {{projectAttribute}} attribute or "Project" attribute type is inactive.',
                {
                    projectAttribute: project,
                },
            );
        }

        if (
            task &&
            project &&
            (await xtremFinanceData.functions.checkValueInactive(context, xtremFinanceData.nodes.Attribute, task, {
                attributeType: { id: 'task', isActive: true },
                attributeRestrictedToId: project,
            }))
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/is_active_task_attribute_inactive',
                'The {{taskAttribute}} attribute or "Task" attribute type is inactive.',
                {
                    taskAttribute: task,
                },
            );
        }

        if (
            financialSite &&
            !(await xtremFinanceData.functions.checkAttributeValue(context, 'financialSite', financialSite))
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/is_active_financial_site_attribute_type_or_value_inactive',
                'The site {{financialSite}} or Financial site attribute type is inactive or the site is not flagged as a Financial site.',
                {
                    financialSite,
                },
            );
        }

        if (
            businessSite &&
            !(await xtremFinanceData.functions.checkAttributeValue(context, 'businessSite', businessSite))
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/is_active_business_site_attribute_type_or_value_inactive',
                'The site {{businessSite}} or Business site attribute type is inactive or the site is not flagged as a Business site.',
                {
                    businessSite,
                },
            );
        }

        if (stockSite && !(await xtremFinanceData.functions.checkAttributeValue(context, 'stockSite', stockSite))) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/is_active_stock_site_attribute_type_or_value_inactive',
                'The site {{stockSite}} or Stock site attribute type is inactive or the site is not flagged as a Stock site.',
                {
                    stockSite,
                },
            );
        }

        if (
            manufacturingSite &&
            !(await xtremFinanceData.functions.checkAttributeValue(context, 'manufacturingSite', manufacturingSite))
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/is_active_manufacturing_site_attribute_type_or_value_inactive',
                'The site {{manufacturingSite}} or Manufacturing site attribute type is inactive or the site is not flagged as a Manufacturing site.',
                {
                    manufacturingSite,
                },
            );
        }

        if (customer && !(await xtremFinanceData.functions.checkAttributeValue(context, 'customer', customer))) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/is_active_customer_attribute_type_or_value_inactive',
                'The customer {{customer}} or Customer attribute type is inactive.',
                {
                    customer,
                },
            );
        }

        if (supplier && !(await xtremFinanceData.functions.checkAttributeValue(context, 'supplier', supplier))) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/is_active_supplier_attribute_type_or_value_inactive',
                'The supplier {{supplier}} or Supplier attribute type is inactive.',
                {
                    supplier,
                },
            );
        }

        if (item && !(await xtremFinanceData.functions.checkAttributeValue(context, 'item', item))) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/is_active_item_attribute_type_or_value_inactive',
                'The item {{item}} or Item attribute type is inactive.',
                {
                    item,
                },
            );
        }
    }
}
