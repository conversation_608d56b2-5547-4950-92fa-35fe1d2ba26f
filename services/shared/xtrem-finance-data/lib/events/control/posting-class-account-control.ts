import type { ValidationContext } from '@sage/xtrem-core';
import * as xtremFinanceData from '../..';

/**
 * @param account - Account instance
 * @param cx - Validation context
 * @param line - Posting class line instance
 * @returns void
 */
export async function selectAccountManagement(
    account: xtremFinanceData.nodes.Account | null,
    cx: ValidationContext,
    line: xtremFinanceData.nodes.PostingClassLine,
) {
    if (
        account &&
        !(await line.updateAccountTaxManagement) &&
        (await (await line.postingClass).type) === 'tax' &&
        (await account.taxManagement) !== 'tax' &&
        (await (await (await account.chartOfAccount).legislation)?.id) === 'ZA'
    ) {
        if (
            await xtremFinanceData.functions.Common.isSubjectToGlTaxExcludedAmount({
                context: line.$.context,
                legislationId: (await (await (await account.chartOfAccount).legislation)?.id) || '',
                isVatFilter: true,
            })
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__posting_class_line__wrong_account_tax_management',
                'Select an account management that is Tax.',
            );
        }
    }
}

async function isAdditionalCriteriaDefined(
    definition: xtremFinanceData.nodes.PostingClassDefinition,
): Promise<boolean> {
    return (await definition.canHaveAdditionalCriteria) && (await definition.additionalCriteria) !== null;
}

/**
 * Returns a error message if the posting class line have a secondary criteria, no line details and the account is null
 * @param account - Account instance
 * @param cx - Validation context
 * @param line - Posting class line instance
 * @returns void
 */
export async function lineAccountNullable(
    account: xtremFinanceData.nodes.Account | null,
    cx: ValidationContext,
    line: xtremFinanceData.nodes.PostingClassLine,
) {
    const { details } = line;
    if (
        account === null &&
        !((await isAdditionalCriteriaDefined(await line.definition)) && details && (await details.length) > 0)
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-finance-data/events__control__posting_class_account_control__account_is_mandatory',
            'You need to enter an account when there are no line details.',
        );
    }
}
