import type { Context, ValidationContext } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { inRange } from 'lodash';
import * as xtremFinanceData from '../..';

// controls on the account for valid DATEV tax reference:
export async function taxControls(account: xtremFinanceData.nodes.Account, cx: ValidationContext): Promise<void> {
    const tax = await account.tax;
    if ((await account.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.datevOption)) && tax) {
        const countryIds = await (await (await account.chartOfAccount).legislation)?.countries
            .map(country => country.id)
            .toArray();
        if (countryIds && !countryIds.includes((await (await tax.country)?.id) ?? '')) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__account__datev_tax_invalid',
                'You need to assign a tax code linked with one of these countries: {{countryIds}}.',
                { countryIds: countryIds.join(', ') },
            );
        }
    }
}

function getDatevIdCount(account: { context: Context; datevId: number | null; sysId: number }): Promise<number> {
    return account.context.queryCount(xtremFinanceData.nodes.Account, {
        filter: { datevId: account.datevId, _id: { _ne: account.sysId } },
    });
}

async function getDatevIdRange(context: Context): Promise<{ fromValue: number; toValue: number }> {
    const datevConfiguration = await xtremFinanceData.functions.datev.getDatevConfiguration(context);
    return {
        fromValue: 1,
        toValue: 10 ** (await datevConfiguration.accountLength) - 1,
    };
}

// controls on the account for uniqueness and correct range of DATEV ID field:
export async function datevIdControls(account: xtremFinanceData.nodes.Account, cx: ValidationContext): Promise<void> {
    const datevId = await account.datevId;
    if ((await account.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.datevOption)) && datevId) {
        if (
            (await getDatevIdCount({
                context: account.$.context,
                datevId,
                sysId: account._id,
            })) > 0
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__account__datev_id_not_unique',
                'The DATEV ID needs to be unique.',
            );
        }

        const { fromValue, toValue } = await getDatevIdRange(account.$.context);

        if (!inRange(datevId, fromValue, toValue + 1)) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__account__datev_id_invalid',
                'The DATEV ID needs to be a number between {{fromValue}} and {{toValue}}.',
                { fromValue, toValue },
            );
        }
    }
}
