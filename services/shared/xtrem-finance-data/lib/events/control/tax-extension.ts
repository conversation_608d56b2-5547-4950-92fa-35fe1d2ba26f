import type { ExtensionMembers, ValidationContext } from '@sage/xtrem-core';
import type * as xtremTax from '@sage/xtrem-tax';
import { inRange } from 'lodash';
import type * as xtremFinanceData from '../..';

// controls on the postingKey for valid range and German country:
export async function postingKeyControls(options: {
    tax: ExtensionMembers<xtremFinanceData.nodeExtensions.TaxExtension & xtremTax.nodes.Tax>;
    cx: ValidationContext;
}): Promise<void> {
    if (await options.tax.postingKey) {
        if ((await (await (await options.tax.country)?.legislation)?.id) !== 'DE') {
            options.cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__tax__posting_key_wrong_country',
                'You can only enter a value if the country is Germany.',
            );
        }

        if (!inRange((await options.tax.postingKey) ?? 0, 1, 10000)) {
            options.cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__tax__posting_key_invalid',
                'The Posting key needs to be a number between 1 and 9999.',
            );
        }
    }
}
