import type { Collection, Context, ValidationContext } from '@sage/xtrem-core';
import type * as xtremStructure from '@sage/xtrem-structure';
import * as xtremFinanceData from '../../index';

export async function controlDetailedSwitch(
    context: Context,
    validationContext: ValidationContext,
    postingClassDefinitionSysId: xtremFinanceData.nodes.PostingClassDefinition['_id'],
) {
    const postingClassLine = await context
        .query(xtremFinanceData.nodes.PostingClassLine, {
            filter: { definition: postingClassDefinitionSysId },
            first: 1,
        })
        .at(0);
    if (postingClassLine) {
        validationContext.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__posting_class_definition__update_is_detailed_not_allowed',
            `You cannot change the 'Detailed' switch setting because the {{ postingClassName }} posting class is linked to this definition.`,
            { postingClassName: await (await postingClassLine.postingClass).name },
        );
    }
}

function getCountriesFromLegislation(
    legislation: xtremStructure.nodes.Legislation,
): Collection<xtremStructure.nodes.Country> {
    return legislation.countries;
}

// Checks if there is at least one tax category with mandatory tax in one of the tax solutions linked to a country with
// the legislation of the posting class definition
async function checkIsTaxMandatory(parameters: {
    countries: Collection<xtremStructure.nodes.Country>;
}): Promise<boolean> {
    const taxSolutions = parameters.countries.map(country => country.taxSolution);
    return (
        (
            await taxSolutions.map(taxSolution => taxSolution?.getFirstTaxCategoryIdWithIsTaxMandatory()).toArray()
        ).filter(taxSolutionId => taxSolutionId !== null).length >= 1
    );
}

// Checks if there is at least one tax category with mandatory tax in one of the tax solutions linked to a country with
// the legislation of the posting class definition. If not, an error is added to the validation context.
export async function controlIsTaxMandatory(
    validationContext: ValidationContext,
    postingClassDefinition: xtremFinanceData.nodes.PostingClassDefinition,
) {
    if (
        !(await checkIsTaxMandatory({
            countries: getCountriesFromLegislation(await postingClassDefinition.legislation),
        }))
    ) {
        validationContext.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__posting_class_definition__tax_solution_control',
            `The tax solutions for this legislation need to have at least one tax category that is mandatory.`,
        );
    }
}

async function checkDefinitionHasDetails(
    validationContext: ValidationContext,
    postingClassDefinition: xtremFinanceData.nodes.PostingClassDefinition,
): Promise<void> {
    if (
        (await postingClassDefinition.$.context.queryCount(xtremFinanceData.nodes.PostingClassLineDetail, {
            filter: { postingClassLine: { definition: { _id: postingClassDefinition._id } } },
        })) > 0
    ) {
        validationContext.error.addLocalized(
            '@sage/xtrem-finance-data/events__posting_class_definition__this_posting_class_definition_has_details',
            'You need to delete the posting class line details for this definition before removing the Additional criteria.',
        );
    }
}

export async function additionalCriteria(parameters: {
    validationContext: ValidationContext;
    additionalCriteria: xtremFinanceData.enums.PostingClassType | null;
    postingClassDefinition: xtremFinanceData.nodes.PostingClassDefinition;
}): Promise<void> {
    if (!parameters.additionalCriteria) {
        await checkDefinitionHasDetails(parameters.validationContext, parameters.postingClassDefinition);
        return;
    }
    if (parameters.additionalCriteria && !(await parameters.postingClassDefinition.canHaveAdditionalCriteria)) {
        parameters.validationContext.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__posting_class_definition__secondary_criteria_not_allowed',
            `You cannot enter a secondary criteria.`,
        );
        return;
    }
    if (parameters.additionalCriteria !== 'tax') {
        parameters.validationContext.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__posting_class_definition__secondary_criteria_incorrect_value',
            `The secondary criteria needs to be tax.`,
        );
        return;
    }
    if (parameters.additionalCriteria === (await parameters.postingClassDefinition.postingClassType)) {
        parameters.validationContext.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__posting_class_definition__criteria_and_secondary_criteria_are_the_same',
            `You need to select different criteria.`,
        );
    }
    if (parameters.additionalCriteria === 'tax') {
        await xtremFinanceData.events.control.postingClassDefinitionControls.controlIsTaxMandatory(
            parameters.validationContext,
            parameters.postingClassDefinition,
        );
    }
}
