import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremTax from '@sage/xtrem-tax';
import type * as xtremFinanceData from '../..';

export async function taxMandatoryWhenSecondCriteriaIsTax(
    cx: ValidationContext,
    tax: xtremTax.nodes.Tax | null,
    detail: xtremFinanceData.nodes.PostingClassLineDetail,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-finance-data/events__control__posting_class_line_detail__secondary_criteria_is_tax',
            'You need to enter the tax code when the secondary posting class line is tax.',
        )
        .if(!tax && (await (await (await detail.postingClassLine).definition).additionalCriteria) === 'tax')
        .is.true();
}
