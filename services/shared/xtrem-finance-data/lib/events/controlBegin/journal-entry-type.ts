import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremFinanceData from '../..';

export async function controlBegin(
    journalEntryType: xtremFinanceData.nodes.JournalEntryType,
    cx: ValidationContext,
): Promise<void> {
    if ((await journalEntryType.targetDocumentType) === 'journalEntry') {
        await journalEntryType.lines.forEach(async line => {
            if (
                (await line.contraJournalEntryTypeLine) &&
                (await (await line.contraJournalEntryTypeLine)?.journalEntryType) !== journalEntryType
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type__contra_journal_entry_type_line_invalid',
                    'The contra journal entry type on the line needs to be the same as the journal entry type.',
                );
            }
        });
    }
}
