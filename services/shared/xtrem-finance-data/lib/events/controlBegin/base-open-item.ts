import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import * as xtremFinanceData from '../..';

// Check if the close reason is populated when the open item is paid
export async function checkCloseReason(
    baseOpenItem: xtremFinanceData.nodes.BaseOpenItem,
    cx: ValidationContext,
): Promise<void> {
    if (await baseOpenItem.$.context.isServiceOptionEnabled(xtremFinanceData.serviceOptions.paymentTrackingOption)) {
        let missingCloseReason = false;
        if (baseOpenItem.$.status !== NodeStatus.added) {
            const oldForcedAmountPaid = (await (await baseOpenItem.$.old)?.forcedAmountPaid) ?? 0;
            const newForcedAmountPaid = (await baseOpenItem.forcedAmountPaid) ?? 0;
            const newCloseReason = await baseOpenItem.closeReason;
            if (oldForcedAmountPaid !== newForcedAmountPaid || !newCloseReason) {
                const deltaForcedAmount = newForcedAmountPaid - oldForcedAmountPaid;
                const transactionAmountDue = (await baseOpenItem.transactionAmountDue) ?? 0;
                const transactionAmountPaid = (await baseOpenItem.transactionAmountPaid) ?? 0;
                const newTransactionAmountPaid = transactionAmountPaid + deltaForcedAmount;
                // if the new status will be 'paid', the close reason is mandatory
                if (transactionAmountDue === newTransactionAmountPaid && !newCloseReason) {
                    missingCloseReason = true;
                }
            }
        } else {
            missingCloseReason = (await baseOpenItem.status) === 'paid' && !(await baseOpenItem.closeReason);
        }
        if (missingCloseReason) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__base-open_item__close_reason_mandatory',
                'The close reason is mandatory.',
            );
        }
    }
}

// Check if paid amount is less than or equal to due amount in all currencies
export async function checkAmountConsistency(
    baseOpenItem: xtremFinanceData.nodes.BaseOpenItem,
    cx: ValidationContext,
): Promise<void> {
    // error if the paid amount is greater than the due amount in all currencies
    if ((await baseOpenItem.transactionAmountPaid) > (await baseOpenItem.transactionAmountDue)) {
        cx.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__base-open_item__paid_transaction_amount_discrepancy',
            'The transaction amount paid needs to be lower than or equal to the transaction amount due.',
        );
    }
    if ((await baseOpenItem.companyAmountPaid) > (await baseOpenItem.companyAmountDue)) {
        cx.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__base-open_item__paid_company_amount_discrepancy',
            'The company amount paid needs to be lower than or equal to the company amount due.',
        );
    }
    if ((await baseOpenItem.financialSiteAmountPaid) > (await baseOpenItem.financialSiteAmountDue)) {
        cx.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__base-open_item__paid_financial_site_amount_discrepancy',
            'The financial site amount paid needs to be lower than or equal to the financial site amount due.',
        );
    }

    // error if the balance between old and new forced paid amount is greater than remaining due amount in transaction currency
    if (
        (await baseOpenItem.$.context.isServiceOptionEnabled(xtremFinanceData.serviceOptions.paymentTrackingOption)) &&
        baseOpenItem.$.status !== 'added'
    ) {
        const oldForcedAmountPaid = (await (await baseOpenItem.$.old)?.forcedAmountPaid) ?? 0;
        const forcedBalance = ((await baseOpenItem.forcedAmountPaid) ?? 0) - oldForcedAmountPaid;
        const transactionAmountDue = (await baseOpenItem.transactionAmountDue) ?? 0;
        const transactionAmountPaid = (await baseOpenItem.transactionAmountPaid) ?? 0;
        const remainingAmount = transactionAmountDue - transactionAmountPaid;
        if (forcedBalance > remainingAmount) {
            const maxForcedAmount = transactionAmountDue - (transactionAmountPaid - oldForcedAmountPaid);
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__base-open_item__wrong_forced_amount_paid',
                'The forced amount paid needs to be between 0 and {{maxForcedAmount}}.',
                { maxForcedAmount },
            );
        }
    }
}
