import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremFinanceData from '../..';

// Check if there is a discrepancy between BasePaymentDocument.financialSite and bankAccount.financialSite
export async function checkSiteConsistency(
    basePaymentDocument: xtremFinanceData.nodes.BasePaymentDocument,
    cx: ValidationContext,
): Promise<void> {
    // error if the financial site of the base payment document is different from the financial site of the bank account
    if ((await basePaymentDocument.financialSite) !== (await (await basePaymentDocument.bankAccount)?.financialSite)) {
        cx.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__base-payment_document__financial_site_discrepancy',
            'The financial site of the document needs to be the same as the financial site of the bank account.',
        );
    }
}

// Check if the payment amount of the base payment document in transaction and bank currency is positive
export async function checkAmountConsistency(
    basePaymentDocument: xtremFinanceData.nodes.BasePaymentDocument,
    cx: ValidationContext,
): Promise<void> {
    // error if the payment amount of the base payment document is less than or equal to 0
    if (!(await basePaymentDocument.amount) || (await basePaymentDocument.amount) <= 0) {
        cx.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__base_payment_document__payment_amount_invalid',
            'The payment amount of the document {{amount}} needs to be positive.',
            { amount: await basePaymentDocument.amount },
        );
    }
    // error if the amount in bank currency of the base payment document is less than or equal to 0
    if (!(await basePaymentDocument.amountBankCurrency) || (await basePaymentDocument.amountBankCurrency) <= 0) {
        cx.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__base-payment_document__bank_amount_invalid',
            'The amount in bank currency of the document {{amount}} needs to be positive.',
            { amount: await basePaymentDocument.amountBankCurrency },
        );
    }
}
