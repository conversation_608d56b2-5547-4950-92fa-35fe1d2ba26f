import type { AsyncArrayReader, Collection, ValidationContext } from '@sage/xtrem-core';
import type * as xtremFinanceData from '../..';

// Get the default attribute settings (task and project)
function getDefaultAttribute(
    defaultAttributes: Collection<xtremFinanceData.nodes.CompanyDefaultAttribute>,
    attributeType: string,
): AsyncArrayReader<xtremFinanceData.nodes.CompanyDefaultAttribute> {
    return defaultAttributes.filter(async attribute => (await (await attribute.attributeType).id) === attributeType);
}

function getProjectAttributeWithSameTaskDefinitionLevel(parameters: {
    projectDefaults: AsyncArrayReader<xtremFinanceData.nodes.CompanyDefaultAttribute>;
    taskAttribute: xtremFinanceData.nodes.CompanyDefaultAttribute;
}): Promise<xtremFinanceData.nodes.CompanyDefaultAttribute | undefined> {
    return parameters.projectDefaults.find(
        async attribute =>
            (await attribute.dimensionDefinitionLevel) === (await parameters.taskAttribute.dimensionDefinitionLevel),
    );
}

// Check if there is a discrepancy between the default master data of project and task
export async function isProjectTaskDiscrepancy(
    defaultAttributes: Collection<xtremFinanceData.nodes.CompanyDefaultAttribute>,
    cx: ValidationContext,
): Promise<void> {
    // Get the default attribute settings for task
    const taskDefaults = getDefaultAttribute(defaultAttributes, 'task');

    // if there are default settings for task, check if task and project are defaulted from the same master data
    if ((await taskDefaults.length) > 0) {
        const projectDefaults = getDefaultAttribute(defaultAttributes, 'project');

        await taskDefaults.forEach(async taskAttribute => {
            const projectAttribute = await getProjectAttributeWithSameTaskDefinitionLevel({
                projectDefaults,
                taskAttribute,
            });
            if ((await taskAttribute.masterDataDefault) !== (await projectAttribute?.masterDataDefault)) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__company__project_task_discrepancy',
                    'The project and task attributes need to default from the same origin.',
                );
            }
        });
    }
}
