import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremFinanceData from '../..';
/**
 * Returns a error message if the posting class line can not have a secondary criteria
 * @param cx - validation context
 * @param postingClassLineDetail - posting class line detail instance
 * @returns void
 */
export async function checkIfPostingClassLineCanHaveAdditionalCriteria(
    cx: ValidationContext,
    postingClassLineDetail: xtremFinanceData.nodes.PostingClassLineDetail,
) {
    if (!(await (await (await postingClassLineDetail.postingClassLine).definition).canHaveAdditionalCriteria)) {
        cx.error.addLocalized(
            '@sage/xtrem-finance-data/nodes__posting_class_line_detail__no_secondary_criteria_defined',
            'This posting class does not have the second criteria option selected.',
        );
    }
}
