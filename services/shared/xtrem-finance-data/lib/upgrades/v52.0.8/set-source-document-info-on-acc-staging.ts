import { CustomSqlAction } from '@sage/xtrem-system';

export const setSourceDocumentNumberAndType = new CustomSqlAction({
    description:
        'Set source document number and source document type on accounting staging table for manufacturing documents',
    body: async helper => {
        await helper.executeSql(`
DO $$

BEGIN

	UPDATE ${helper.schemaName}.accounting_staging acc
	SET
		source_document_number = ft.source_document_number,
		source_document_type = ft.source_document_type
	FROM ${helper.schemaName}.finance_transaction ft
	WHERE
		acc.batch_id = ft.batch_id and acc._tenant_id = ft._tenant_id and
		acc.document_type = ft.document_type and acc.document_number = ft.document_number and
		acc.document_type = 'workInProgress' and acc.source_document_number = '';
END
$$;

`);
    },
});
