import { CustomSqlAction } from '@sage/xtrem-system';

export const setBaseOpenItemDataFromSalesInvoice = new CustomSqlAction({
    description: 'Set base open item data from sales invoices',
    body: async helper => {
        await helper.executeSql(`
DO $$

BEGIN

    UPDATE ${helper.schemaName}.base_open_item boi
	SET
		discount_from = invoices.discount_from,
		discount_date = invoices.discount_date,
		discount_type = invoices.discount_payment_type::text::${helper.schemaName}.payment_term_discount_or_penalty_type_enum,
		discount_amount = invoices.discount_payment_amount,
		discount_payment_before_date = invoices.discount_payment_before_date,
		penalty_payment_type = invoices.penalty_payment_type,
		penalty_amount = invoices.penalty_payment_amount
	FROM (
		SELECT
			si._id,
			si._tenant_id,
			si.penalty_payment_amount,
			si.discount_payment_type,
			si.discount_payment_amount,
			si.discount_payment_before_date,
			si.penalty_payment_type,
			pt.discount_from,
			pt.discount_date
		FROM ${helper.schemaName}.sales_invoice si
		INNER JOIN ${helper.schemaName}.payment_term pt
		ON si._tenant_id = pt._tenant_id AND si.payment_term = pt._id
	) invoices
	WHERE
	boi._tenant_id=invoices._tenant_id AND boi.document_sys_id = invoices._id and boi.document_type = 'salesInvoice';

END
$$;

`);
    },
});
