import { CustomSqlAction } from '@sage/xtrem-system';

export const setBaseOpenItemDataFromSalesCreditMemo = new CustomSqlAction({
    description: 'Set base open item data from sales credit memos',
    body: async helper => {
        await helper.executeSql(`
DO $$

BEGIN

	UPDATE ${helper.schemaName}.base_open_item boi
	SET
		discount_from = credit_memos.discount_from,
		discount_date = credit_memos.discount_date,
		discount_type = credit_memos.discount_payment_type::text::${helper.schemaName}.payment_term_discount_or_penalty_type_enum,
		discount_amount = credit_memos.discount_payment_amount,
		discount_payment_before_date = credit_memos.discount_payment_before_date,
		penalty_payment_type = credit_memos.penalty_payment_type,
		penalty_amount = credit_memos.penalty_payment_amount
	FROM (
		SELECT
			cm._id,
			cm._tenant_id,
			cm.penalty_payment_amount,
			cm.discount_payment_type,
			cm.discount_payment_amount,
			cm.discount_payment_before_date,
			cm.penalty_payment_type,
			pt.discount_from,
			pt.discount_date
		FROM ${helper.schemaName}.sales_credit_memo cm
		INNER JOIN ${helper.schemaName}.payment_term pt
		ON cm._tenant_id = pt._tenant_id AND cm.payment_term = pt._id
	) credit_memos
	WHERE
	boi._tenant_id=credit_memos._tenant_id AND boi.document_sys_id = credit_memos._id and boi.document_type = 'salesCreditMemo';

END
$$;

`);
    },
});
