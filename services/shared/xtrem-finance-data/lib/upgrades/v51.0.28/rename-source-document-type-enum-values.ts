import { SchemaEnumUpgradeAction } from '@sage/xtrem-system';
import { sourceDocumentTypeDataType } from '../../enums/source-document-type';

export const renameSourceDocumentTypeEnumValues = new SchemaEnumUpgradeAction({
    description: 'Rename source document type enum value workOrder to workOrderClose',
    dataType: sourceDocumentTypeDataType,
    valuesMapping: {
        materialTracking: 'materialTracking',
        operationTracking: 'operationTracking',
        productionTracking: 'productionTracking',
        workOrder: 'workOrderClose',
        purchaseOrder: 'purchaseOrder',
        purchaseReceipt: 'purchaseReceipt',
        purchaseReturn: 'purchaseReturn',
        purchaseInvoice: 'purchaseInvoice',
        purchaseCreditMemo: 'purchaseCreditMemo',
        salesCreditMemo: 'salesCreditMemo',
        salesInvoice: 'salesInvoice',
        salesReturnRequest: 'salesReturnRequest',
        salesShipment: 'salesShipment',
        stockTransferShipment: 'stockTransferShipment',
    },
});
