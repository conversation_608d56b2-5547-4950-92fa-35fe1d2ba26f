import type { Context } from '@sage/xtrem-core';
import { asyncArray, Logger } from '@sage/xtrem-core';
import { CustomSqlAction } from '@sage/xtrem-system';
import * as _ from 'lodash';
import * as xtremFinanceData from '../index';

const logger = Logger.getLogger(__filename, 'upgrade');

function dimensionNames(prefix = '') {
    return Array.from({ length: 20 }, (__, i) => `${prefix}${String(i + 1).padStart(2, '0')}`);
}

const siteAttributeNames = ['financialSite', 'businessSite', 'stockSite', 'manufacturingSite'];
const businessRelationNames = ['customer', 'supplier'];
const miscAttributeNames = ['project', 'task', 'employee'];

const allAttributeNames = [...siteAttributeNames, ...businessRelationNames, 'item', ...miscAttributeNames];

const allColumnNames = [...allAttributeNames.map(_.snakeCase), ...dimensionNames('dimension_')];

/**
 * Basic rows are the distinct combinations of _tenant_id, stored_attributes and stored_dimensions.
 */
function selectBasicDistinctRows(schemaName: string, tableName: string): string {
    return `SELECT DISTINCT
    _tenant_id,
    COALESCE(stored_attributes, jsonb_build_object()) as stored_attributes,
    COALESCE(stored_dimensions, jsonb_build_object()) as stored_dimensions
    FROM ${schemaName}.${tableName}`;
}

/**
 * Partial rows are the raw values (string keys, not reference ids) that are stored
 * in the stored_attributes and stored_dimensions columns.
 */
function selectPartialRows(schemaName: string, tableName: string): string {
    return `SELECT
            *,
            -- attributes ${allAttributeNames
                .map(
                    atb => `
            (stored_attributes ->> '${atb}')::TEXT as partial_${atb}`,
                )
                .join(',')},
            -- dimensions ${dimensionNames()
                .map(
                    digits => `
            (stored_dimensions ->> 'dimensionType${digits}')::TEXT as partial_dimension_${digits}`,
                )
                .join(',')}
            FROM (${selectBasicDistinctRows(schemaName, tableName)})`;
}

/**
 * Rich rows are rows that combine the reference ids for the columns of the analytical_data table,
 * in addition to the stored_attributes and stored_dimensions JSON columns.
 */
function selectRichRows(schemaName: string, tableName: string): string {
    return `SELECT
        _tenant_id,
        stored_attributes,
        stored_dimensions,

        -- site attributes ${siteAttributeNames
            .map(
                atb => `
        (SELECT site._id FROM ${schemaName}.site site
            WHERE site._tenant_id = partial._tenant_id AND site.id = partial.partial_${atb} LIMIT 1) as ${_.snakeCase(atb)}`,
            )
            .join(',')},

        -- supplier and customer attributes ${businessRelationNames
            .map(
                atb => `
        (SELECT bbr._id FROM ${schemaName}.base_business_relation bbr
            INNER JOIN ${schemaName}.business_entity be
            ON be._tenant_id = bbr._tenant_id AND be._id = bbr.business_entity and bbr._constructor = '${_.capitalize(atb)}'
            WHERE be._tenant_id = partial._tenant_id AND be.id = partial.partial_${atb} LIMIT 1) as ${atb}`,
            )
            .join(',')},

        -- item attribute
        (SELECT item._id FROM ${schemaName}.item item
            WHERE item._tenant_id = partial._tenant_id AND item.id = partial.partial_item LIMIT 1) as item,

        -- project, task and employee attributes${miscAttributeNames
            .map(
                atb => `
        (SELECT misc._id FROM ${schemaName}.attribute misc
            INNER JOIN ${schemaName}.attribute_type at ON at._tenant_id = misc._tenant_id AND at._id = misc.attribute_type AND at.id = '${atb}'
            WHERE misc._tenant_id = partial._tenant_id AND misc.id = partial.partial_${atb} ${
                atb === 'task' ? `AND misc.attribute_restricted_to_id = partial.partial_project` : ''
            } LIMIT 1) as ${atb}`,
            )
            .join(',')},

        -- dimensions ${dimensionNames()
            .map(
                digits => `
        (SELECT dim._id FROM ${schemaName}.dimension dim
            INNER JOIN ${schemaName}.dimension_type dt ON dt._tenant_id = dim._tenant_id AND dt._id = dim.dimension_type AND dt.doc_property = 'dimensionType${digits}'
            WHERE dim._tenant_id = partial._tenant_id AND dim.id = partial.partial_dimension_${digits} LIMIT 1) as dimension_${digits}`,
            )
            .join(',')}
        FROM (${selectPartialRows(schemaName, tableName)}) AS partial`;
}

/**
 * Full rows are rich rows to which we added the _hash_values column.
 */
function selectFullRows(schemaName: string, tableName: string): string {
    return `SELECT
        *,
        encode(digest(
            -- replace twice to eliminate extra spaces in the JSON strings
            -- we have to match extactly the format produced by JSON.stringify
            -- to get the same checksum
            replace(replace(
                json_build_object(
                    -- keys must be sorted alphabetically${allColumnNames
                        .sort((s1, s2) => _.camelCase(s1).localeCompare(_.camelCase(s2)))
                        .map(
                            atb => `
                    '${_.camelCase(atb)}', ${atb}`,
                        )
                        .join(',')})::TEXT,
                '" : ', '":'), ', "', ',"'),
            'sha256'), 'base64') as _values_hash
    FROM (${selectRichRows(schemaName, tableName)})`;
}

export function getAnalyticalDataUpgradeSql(schemaName: string, tableName: string): string {
    const createTempTables = `
CREATE TEMP TABLE full_rows AS SELECT *,
    COALESCE(stored_attributes::TEXT, '{}') as attributes,
    COALESCE(stored_dimensions::TEXT, '{}') as dimensions
FROM (${selectFullRows(schemaName, tableName)});

CREATE UNIQUE INDEX temp_full_idx ON full_rows(_tenant_id, attributes, dimensions);

ANALYZE full_rows;

CREATE TEMP TABLE indexed_node AS SELECT _tenant_id, _id,
    COALESCE(stored_attributes::TEXT, '{}') as attributes,
    COALESCE(stored_dimensions::TEXT, '{}') as dimensions
FROM ${schemaName}.${tableName};

CREATE UNIQUE INDEX temp_node_idx ON indexed_node(_tenant_id, attributes, dimensions, _id);

ANALYZE indexed_node;

`;

    const dropTempTables = `
DROP TABLE full_rows;
DROP TABLE indexed_node;
`;

    const insert = `
-- Insert the analytical data that we found into the analytical_data table, ignoring conflicts
INSERT INTO ${schemaName}.analytical_data (
    _tenant_id,
    ${allAttributeNames.map(_.snakeCase).join(`,
    `)},
    ${dimensionNames('dimension_').join(`,
    `)},
    _values_hash,
    _create_user,
    _update_user,
    _create_stamp,
    _update_stamp,
    _update_tick
) SELECT
    _tenant_id,
    ${allAttributeNames.map(_.snakeCase).join(`,
    `)},
    ${dimensionNames('dimension_').join(`,
    `)},
    _values_hash,
    (SELECT _id as _create_user FROM ${schemaName}.user usr
        WHERE usr._tenant_id = f_row._tenant_id AND usr.email = '<EMAIL>'
        LIMIT 1) as _create_user,
    (SELECT _id as _update_user FROM ${schemaName}.user usr
        WHERE usr._tenant_id = f_row._tenant_id AND usr.email = '<EMAIL>'
        LIMIT 1) as _update_user,
    NOW() as _create_stamp,
    NOW() as _update_stamp,
    1 as _update_tick
FROM full_rows AS f_row ON CONFLICT DO NOTHING;
`;

    const update = `
UPDATE ${schemaName}.${tableName} tbl
SET analytical_data = ana._id
    FROM full_rows AS f_row

    INNER JOIN ${schemaName}.analytical_data AS ana
    ON ana._tenant_id = f_row._tenant_id
        AND ana._values_hash = f_row._values_hash

    INNER JOIN indexed_node AS node
    ON node._tenant_id = f_row._tenant_id
        AND node.attributes = f_row.attributes
        AND node.dimensions = f_row.dimensions

    WHERE tbl._tenant_id = node._tenant_id
        AND tbl._id = node._id
    ;
`;

    return `
-- Transfer the data from stored_attributes and stored_dimensions to analytical_data.
-- This script creates the missing analytical_data entries and updates the analytical_data column of ${tableName}.
--
DO $$
BEGIN

-- First, select the distinct stored_dimensions values from ${tableName}
${createTempTables}

-- Insert the missing ones into the analytical_data table
${insert}

-- Update the analytical_data column of ${tableName}
${update}

${dropTempTables}
END
$$;
`;
}

export function restructureAnalyticalDataAction(tableName: string): CustomSqlAction {
    return new CustomSqlAction({
        description: `Restructure analytical data for ${tableName}`,
        fixes: {
            notNullableColumns: [{ table: tableName, column: 'analytical_data' }],
        },
        body: async helper => {
            const { schemaName } = helper;

            const selectTotalSql = `SELECT COUNT(*) as total FROM ${schemaName}.${tableName}`;
            const { total } = (await helper.executeSql<{ total: number }[]>(selectTotalSql))[0];
            logger.info(`${tableName}: restructuring ${total} rows`);

            const sql = getAnalyticalDataUpgradeSql(schemaName, tableName);
            const t0 = Date.now();
            const updated = (await helper.executeSql(sql))?.length ?? 0;
            logger.info(`${tableName}: restructured ${updated} rows out of ${total} in ${Date.now() - t0} ms`);
        },
    });
}

export async function verifyAnalyticalDataRestructuring(context: Context) {
    const t0 = Date.now();

    const propertyNames = allColumnNames.map(_.camelCase).sort();
    const selector = _.zipObject(
        propertyNames,
        propertyNames.map(() => true),
    );
    selector._valuesHash = true;

    const listOfAnalyticalData = await context.select(xtremFinanceData.nodes.AnalyticalData, selector, {
        filter: {},
    });
    const factory = context.application.getFactoryByConstructor(xtremFinanceData.nodes.AnalyticalData);
    await asyncArray(listOfAnalyticalData).forEach(analyticalData => {
        const valuesHash = factory.getValuesHash(analyticalData);
        if (valuesHash !== analyticalData._valuesHash)
            throw factory.logicError(
                `valuesHash mismatch: expected ${valuesHash}, got ${analyticalData._valuesHash} on ${JSON.stringify(analyticalData)}`,
            );
    });
    logger.info(`Verified ${listOfAnalyticalData.length} upgraded analyticalData in ${Date.now() - t0} ms`);
}
