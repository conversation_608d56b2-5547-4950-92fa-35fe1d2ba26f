import { SchemaEnumUpgradeAction } from '@sage/xtrem-system';
import { openItemStatusDataType } from '../../enums/open-item-status';

export const renameOpenItemStatusEnumValues = new SchemaEnumUpgradeAction({
    description: 'Rename open item status enum value partlyPaid to partiallyPaid',
    dataType: openItemStatusDataType,
    valuesMapping: {
        notPaid: 'notPaid',
        partlyPaid: 'partiallyPaid',
        paid: 'paid',
    },
});
