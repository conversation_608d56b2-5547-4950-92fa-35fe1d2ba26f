import * as xtremStructure from '@sage/xtrem-structure';
import { DataUpdateAction } from '@sage/xtrem-system';

export const setDoNonStockVariancePosting = new DataUpdateAction({
    description: 'Set value on the new property doNonStockVariancePosting on Legislation node',
    node: () => xtremStructure.nodes.Legislation,
    set: {
        async doNonStockVariancePosting() {
            return ['AU', 'GB', 'US', 'ZA'].includes(await this.id);
        },
    },
});
