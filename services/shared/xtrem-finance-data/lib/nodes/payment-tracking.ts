import type { Collection, date, decimal, Reference } from '@sage/xtrem-core';
import { decorators, Node, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { lowerFirst } from 'lodash';
import * as xtremFinanceData from '..';

const packageName = 'xtrem-finance-data';

@decorators.node<PaymentTracking>({
    package: packageName,
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    isVitalReferenceChild: true,
    indexes: [{ orderBy: { document: +1 }, isUnique: true, isNaturalKey: true }],
})
export class PaymentTracking extends Node {
    @decorators.referenceProperty<PaymentTracking, 'document'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.BaseDocument,
        lookupAccess: true,
        isVitalParent: true,
    })
    readonly document: Reference<xtremMasterData.nodes.BaseDocument>;

    @decorators.referenceProperty<PaymentTracking, 'paymentTerm'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.PaymentTerm,
        dataType: () => xtremMasterData.dataTypes.paymentTerm,
        lookupAccess: true,
        isNullable: true,
    })
    readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm | null>;

    @decorators.decimalProperty<PaymentTracking, 'discountPaymentAmount'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.price,
        dependsOn: ['paymentTerm'],
        async defaultValue() {
            return (await this.paymentTerm)?.discountAmount ?? null;
        },
        updatedValue: useDefaultValue,
    })
    readonly discountPaymentAmount: Promise<decimal | null>;

    @decorators.decimalProperty<PaymentTracking, 'penaltyPaymentAmount'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.price,
        dependsOn: ['paymentTerm'],
        async defaultValue() {
            return (await this.paymentTerm)?.penaltyAmount ?? null;
        },
        updatedValue: useDefaultValue,
    })
    readonly penaltyPaymentAmount: Promise<decimal | null>;

    @decorators.enumProperty<PaymentTracking, 'penaltyPaymentType'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.enums.paymentTermDiscountOrPenaltyTypeDataType,
        dependsOn: ['paymentTerm'],
        async defaultValue() {
            return (await this.paymentTerm)?.penaltyType ?? null;
        },
        updatedValue: useDefaultValue,
    })
    readonly penaltyPaymentType: Promise<xtremMasterData.enums.PaymentTermDiscountOrPenaltyType | null>;

    @decorators.enumProperty<PaymentTracking, 'discountPaymentType'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.enums.paymentTermDiscountOrPenaltyTypeDataType,
        dependsOn: ['paymentTerm'],
        async defaultValue() {
            return (await this.paymentTerm)?.discountType ?? null;
        },
        updatedValue: useDefaultValue,
    })
    readonly discountPaymentType: Promise<xtremMasterData.enums.PaymentTermDiscountOrPenaltyType | null>;

    @decorators.dateProperty<PaymentTracking, 'discountPaymentBeforeDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dependsOn: ['paymentTerm', 'document'],
        async defaultValue() {
            const paymentTerm = await this.paymentTerm;
            return xtremMasterData.sharedFunctions.getDiscountPaymentBeforeDate({
                discountType: (await paymentTerm?.discountFrom) ?? 'afterInvoiceDate',
                discountDate: (await paymentTerm?.discountDate) ?? 0,
                baseDate: await (await this.document).date,
            });
        },
        updatedValue: useDefaultValue,
    })
    readonly discountPaymentBeforeDate: Promise<date | null>;

    @decorators.collectionProperty<PaymentTracking, 'openItems'>({
        isPublished: true,
        node: () => xtremFinanceData.nodes.BaseOpenItem,
        join: {
            async documentType() {
                return lowerFirst((await this.document).$.factory.name);
            },
            async documentSysId() {
                return (await this.document)._id;
            },
        },
    })
    readonly openItems: Collection<xtremFinanceData.nodes.BaseOpenItem>;

    @decorators.enumProperty<PaymentTracking, 'status'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.openItemStatusDataType,
        async computeValue() {
            const openItem = await this.openItems.takeOne(
                async actOpenItem =>
                    (await actOpenItem.documentType) === lowerFirst((await this.document).$.factory.name) &&
                    (await actOpenItem.documentNumber) === (await (await this.document).number),
            );
            return (await openItem?.status) ?? 'notPaid';
        },
    })
    readonly status: Promise<xtremFinanceData.enums.OpenItemStatus>;

    @decorators.referenceProperty<PaymentTracking, 'currency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await this.document).currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    // TODO: when refactoring after the base document refactoring: make this a stored property
    // Property to show the total amount paid on the document.
    @decorators.decimalProperty<PaymentTracking, 'amountPaid'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        computeValue() {
            return this.openItems.sum(openItem => openItem.transactionAmountPaid);
        },
    })
    readonly amountPaid: Promise<decimal | null>;

    // TODO: when refactoring after the base document refactoring: make this a stored property
    // Property to show the forced amount paid on the document.
    @decorators.decimalProperty<PaymentTracking, 'forcedAmountPaid'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        computeValue() {
            return this.openItems.sum(openItem => openItem.forcedAmountPaid);
        },
    })
    readonly forcedAmountPaid: Promise<decimal | null>;

    @decorators.collectionProperty<PaymentTracking, 'paymentLines'>({
        isPublished: true,
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
        node: () => xtremFinanceData.nodes.PaymentDocumentLine,
        join: {
            paymentTracking() {
                return this;
            },
        },
    })
    readonly paymentLines: Collection<xtremFinanceData.nodes.PaymentDocumentLine>;
}
