import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '../index';

@decorators.node<AccountsReceivableInvoiceLineStaging>({
    package: 'xtrem-finance-data',
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canDeleteMany: true,
})
export class AccountsReceivableInvoiceLineStaging extends Node {
    @decorators.referenceProperty<AccountsReceivableInvoiceLineStaging, 'accountsReceivableInvoiceLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremMasterData.nodes.BaseDocumentLine,
        isFrozen: true,
    })
    readonly accountsReceivableInvoiceLine: Reference<xtremMasterData.nodes.BaseDocumentLine>;

    @decorators.referenceProperty<AccountsReceivableInvoiceLineStaging, 'accountingStaging'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        node: () => xtremFinanceData.nodes.AccountingStaging,
        isFrozen: true,
    })
    readonly accountingStaging: Reference<xtremFinanceData.nodes.AccountingStaging>;
}
