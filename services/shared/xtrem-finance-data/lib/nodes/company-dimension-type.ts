import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '../index';

@decorators.node<CompanyDimensionType>({
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
    storage: 'sql',
})
export class CompanyDimensionType extends Node {
    @decorators.referenceProperty<CompanyDimensionType, 'company'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: false,
        node: () => xtremSystem.nodes.Company,
        isVitalParent: true,
    })
    readonly company: Reference<xtremSystem.nodes.Company>;

    @decorators.referenceProperty<CompanyDimensionType, 'dimensionType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: false,
        node: () => xtremFinanceData.nodes.DimensionType,
        async control(cx) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-finance-data/nodes__account_dimension_type__dimension-type-not-active',
                    'The dimension type is not active',
                )
                .if(await (await this.dimensionType).isActive)
                .is.false();
        },
    })
    readonly dimensionType: Reference<xtremFinanceData.nodes.DimensionType>;

    @decorators.booleanProperty<CompanyDimensionType, 'isRequired'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        defaultValue: true,
        async control(cx) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-finance-data/nodes__company_dimension_type__dimension-type-not-active',
                    'The dimension type is not active',
                )
                .if(await (await this.dimensionType).isActive)
                .is.false();
        },
    })
    readonly isRequired: Promise<boolean>;

    @decorators.enumProperty<CompanyDimensionType, 'analyticalMeasureType'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.analyticalMeasureTypeDataType,
        getValue() {
            return 'dimension';
        },
    })
    readonly analyticalMeasureType: Promise<xtremFinanceData.enums.AnalyticalMeasureType>;
}
