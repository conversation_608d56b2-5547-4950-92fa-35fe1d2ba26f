import { decorators, Node } from '@sage/xtrem-core';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremFinanceData from '..';

@decorators.node<CloseReason>({
    package: 'xtrem-finance-data',
    storage: 'sql',
    isCached: true,
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    indexes: [
        {
            orderBy: {
                id: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isSetupNode: true,
})
export class CloseReason extends Node {
    @decorators.booleanProperty<CloseReason, 'isActive'>({
        isPublished: true,
        isStored: true,
        defaultValue: true,
        provides: ['isActive'],
        lookupAccess: true,
        isOwnedByCustomer: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<CloseReason, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => dataTypes.id,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<CloseReason, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.localizedName,

        duplicateRequiresPrompt: true,
        lookupAccess: true,
    })
    readonly name: Promise<string>;
}
