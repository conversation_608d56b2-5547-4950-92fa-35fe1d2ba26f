import type { Context, integer } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '..';

@decorators.node<DatevConfiguration>({
    storage: 'sql',
    isPublished: true,
    serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
    canRead: true,
    canUpdate: true,
    canDelete: false,
    canCreate: false,
    isSetupNode: true,
    indexes: [{ orderBy: { id: +1 }, isUnique: true, isNaturalKey: true }],
})
export class DatevConfiguration extends Node {
    @decorators.stringProperty<DatevConfiguration, 'id'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.id,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<DatevConfiguration, 'name'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
        isOwnedByCustomer: true,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.booleanProperty<DatevConfiguration, 'isActive'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
        lookupAccess: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.integerProperty<DatevConfiguration, 'accountLength'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: 4,
        isOwnedByCustomer: true,
        lookupAccess: true,
        async control(cx) {
            await xtremFinanceData.events.control.DatevConfiguration.datevRangeControl({
                cx,
                configuration: this,
                value: await this.accountLength,
                fromValue: 4,
                toValue: 8,
            });
        },
    })
    readonly accountLength: Promise<integer>;

    @decorators.integerProperty<DatevConfiguration, 'skrCoa'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: 3,
        isOwnedByCustomer: true,
        lookupAccess: true,
        async control(cx) {
            await xtremFinanceData.events.control.DatevConfiguration.datevRangeControl({
                cx,
                configuration: null,
                value: await this.skrCoa,
                fromValue: 1,
                toValue: 99,
            });
        },
    })
    readonly skrCoa: Promise<integer>;

    @decorators.integerProperty<DatevConfiguration, 'customerSupplierLength'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['accountLength'],
        async defaultValue() {
            return (await this.accountLength) + 1;
        },
        isOwnedByCustomer: true,
        lookupAccess: true,
        async control(cx) {
            await xtremFinanceData.events.control.DatevConfiguration.datevRangeControl({
                cx,
                configuration: null,
                value: await this.customerSupplierLength,
                fromValue: 5,
                toValue: 9,
            });
        },
    })
    readonly customerSupplierLength: Promise<integer>;

    @decorators.integerProperty<DatevConfiguration, 'customerRangeStart'>({
        isPublished: true,
        dependsOn: ['accountLength'],
        lookupAccess: true,
        async computeValue() {
            return 10 ** (await this.accountLength);
        },
    })
    readonly customerRangeStart: Promise<integer>;

    @decorators.integerProperty<DatevConfiguration, 'customerRangeEnd'>({
        isPublished: true,
        dependsOn: ['accountLength'],
        lookupAccess: true,
        async computeValue() {
            return 10 ** (await this.accountLength) * 7 - 1;
        },
    })
    readonly customerRangeEnd: Promise<integer>;

    @decorators.integerProperty<DatevConfiguration, 'supplierRangeStart'>({
        isPublished: true,
        dependsOn: ['accountLength'],
        lookupAccess: true,
        async computeValue() {
            return 10 ** (await this.accountLength) * 7;
        },
    })
    readonly supplierRangeStart: Promise<integer>;

    @decorators.integerProperty<DatevConfiguration, 'supplierRangeEnd'>({
        isPublished: true,
        dependsOn: ['accountLength'],
        lookupAccess: true,
        async computeValue() {
            return 10 ** ((await this.accountLength) + 1) - 1;
        },
    })
    readonly supplierRangeEnd: Promise<integer>;

    // This mutation is called on save of the DatevConfiguration page. It does the following controls and adds the
    // corresponding warnings to the result array:
    // - Check if there are accounts in the DB with a datevId length other than the one set in the DatevConfiguration
    // - Check if there are customers in the DB with a datevId length other than the one set in the DatevConfiguration
    // - Check if there are suppliers in the DB with a datevId length other than the one set in the DatevConfiguration
    // - Check if there are customers in the DB with a datevId outside the range set in the DatevConfiguration
    // - Check if there are suppliers in the DB with a datevId outside the range set in the DatevConfiguration
    @decorators.mutation<typeof DatevConfiguration, 'datevConfigurationControlsOnSave'>({
        isPublished: true,
        parameters: [
            {
                name: 'datevConfiguration',
                type: 'object',
                properties: {
                    accountLength: 'integer',
                    customerRangeStart: 'integer',
                    customerRangeEnd: 'integer',
                    supplierRangeStart: 'integer',
                    supplierRangeEnd: 'integer',
                },
            },
        ],
        return: {
            type: 'array',
            item: 'string',
        },
    })
    static async datevConfigurationControlsOnSave(
        context: Context,
        datevConfiguration: xtremFinanceData.functions.datev.DatevConfiguration,
    ): Promise<string[]> {
        const warnings: string[] = [];
        let warning = '';

        // check if all accounts have a DATEV ID with the correct length
        warning = await xtremFinanceData.functions.datev.checkCorrectDatevIdLengthOnAccounts(
            context,
            datevConfiguration,
        );
        if (warning) {
            warnings.push(warning);
        }

        // check if all customers have a DATEV ID with the correct length
        warning = await xtremFinanceData.functions.datev.checkCorrectDatevIdLengthOnCustomers(
            context,
            datevConfiguration,
        );
        if (warning) {
            warnings.push(warning);
        } else {
            // check if all customers have a DATEV ID in the correct range
            warning = await xtremFinanceData.functions.datev.checkCorrectDatevIdRangeOnCustomers(
                context,
                datevConfiguration,
            );
            if (warning) {
                warnings.push(warning);
            }
        }

        // check if all suppliers have a DATEV ID with the correct length
        warning = await xtremFinanceData.functions.datev.checkCorrectDatevIdLengthOnSuppliers(
            context,
            datevConfiguration,
        );
        if (warning) {
            warnings.push(warning);
        } else {
            // check if all suppliers have a DATEV ID in the correct range
            warning = await xtremFinanceData.functions.datev.checkCorrectDatevIdRangeOnSuppliers(
                context,
                datevConfiguration,
            );
            if (warning) {
                warnings.push(warning);
            }
        }

        return warnings;
    }
}
