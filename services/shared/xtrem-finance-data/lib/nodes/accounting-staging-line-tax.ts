import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinanceData from '../index';

@decorators.node<AccountingStagingLineTax>({
    package: 'xtrem-finance',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    isVitalCollectionChild: true,
})
export class AccountingStagingLineTax
    extends Node
    implements xtremFinanceData.interfaces.FinanceNodes.AccountingStagingLineTax
{
    @decorators.referenceProperty<AccountingStagingLineTax, 'accountingStaging'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.AccountingStaging,
        isVitalParent: true,
    })
    readonly accountingStaging: Reference<xtremFinanceData.nodes.AccountingStaging>;

    @decorators.referenceProperty<AccountingStagingLineTax, 'baseTax'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        node: () => xtremTax.nodes.BaseTax,
    })
    readonly baseTax: Reference<xtremTax.nodes.BaseTax>;
}
