import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '../index';

@decorators.node<AccountsPayableInvoiceLineStaging>({
    package: 'xtrem-finance',
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canDeleteMany: true,
})
export class AccountsPayableInvoiceLineStaging extends Node {
    @decorators.referenceProperty<AccountsPayableInvoiceLineStaging, 'accountsPayableInvoiceLine'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremMasterData.nodes.BaseDocumentLine,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly accountsPayableInvoiceLine: Reference<xtremMasterData.nodes.BaseDocumentLine>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineStaging, 'accountingStaging'>({
        isPublished: true,
        isStored: true,
        node: () => xtremFinanceData.nodes.AccountingStaging,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly accountingStaging: Reference<xtremFinanceData.nodes.AccountingStaging>;
}
