import type { Collection, integer, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '..';

/** abstract Node for attributes & dimension lines
 * must be adapted to journalEntryLine, arInvoiceLine & apInvoiceLine
 */

@decorators.subNode<BaseFinanceLine>({
    isAbstract: true,
    isPublished: true,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
})
export class BaseFinanceLine extends xtremMasterData.nodes.BaseDocumentLine {
    @decorators.referenceProperty<BaseFinanceLine, 'document'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinanceData.nodes.BaseFinanceDocument,
    })
    override readonly document: Reference<xtremFinanceData.nodes.BaseFinanceDocument>;

    @decorators.stringPropertyOverride<BaseFinanceLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<BaseFinanceLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.collectionProperty<BaseFinanceLine, 'attributesAndDimensions'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinanceData.nodes.BaseFinanceLineDimension,
        reverseReference: 'originLine',
    })
    readonly attributesAndDimensions: Collection<xtremFinanceData.nodes.BaseFinanceLineDimension>;
}
