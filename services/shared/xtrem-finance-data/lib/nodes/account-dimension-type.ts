import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremFinanceData from '../index';

@decorators.node<AccountDimensionType>({
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
    storage: 'sql',
})
export class AccountDimensionType extends Node {
    @decorators.referenceProperty<AccountDimensionType, 'account'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: false,
        node: () => xtremFinanceData.nodes.Account,
        isVitalParent: true,
    })
    readonly account: Reference<xtremFinanceData.nodes.Account>;

    @decorators.referenceProperty<AccountDimensionType, 'dimensionType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: false,
        node: () => xtremFinanceData.nodes.DimensionType,
        ignoreIsActive: true,
    })
    readonly dimensionType: Reference<xtremFinanceData.nodes.DimensionType>;

    @decorators.booleanProperty<AccountDimensionType, 'isRequired'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        defaultValue: false,
        async control(cx) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-finance-data/nodes__account_dimension_type__dimension-type-not-active',
                    'The dimension type is not active',
                )
                .if(await (await this.dimensionType).isActive)
                .is.false();
        },
    })
    readonly isRequired: Promise<boolean>;

    @decorators.enumProperty<AccountDimensionType, 'analyticalMeasureType'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.analyticalMeasureTypeDataType,
        getValue() {
            return 'dimension';
        },
    })
    readonly analyticalMeasureType: Promise<xtremFinanceData.enums.AnalyticalMeasureType>;
}
