import type { AnyRecord, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { storedAttributesDataType } from '../data-types/stored-attributes-data-type';
import { checkTypeOrValue } from '../functions/dimensions';
import * as xtremFinanceData from '../index';

@decorators.node<AttributeType>({
    storage: 'sql',
    isPublished: true,
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDelete: false,
    isCached: true,
    indexes: [
        {
            orderBy: { id: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isSetupNode: true,
    async saveBegin() {
        if ((await this.nodeLink) !== 'attribute') {
            await this.$.set({
                isLinkedToSite: false,
                isLinkedToItem: false,
            });
        }
    },
    async controlEnd(cx) {
        if (
            (await (await this.$.old).id) !== (await this.id) ||
            (await (await this.$.old).queryFilter) !== (await this.queryFilter) ||
            (await (await this.$.old).nodeLink) !== (await this.nodeLink)
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__attribute_type__node_link_changeable_properties',
                'You can only update the isActive or the name properties.',
            );
        }
        if (
            this.attributeTypeRestrictedTo &&
            (await this.attributeTypeRestrictedTo) !== null && // Add null check
            (await (await this.attributeTypeRestrictedTo).isActive) === false &&
            (await this.isActive) === true
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__attribute_type__node__isActive',
                'You can only set the attribute type to active if the restricted to attribute type {{id}} is active.',
                {
                    id: await (await this.attributeTypeRestrictedTo).id,
                },
            );
        }

        const isRestrictedToInUse =
            (await this.$.context.query(xtremFinanceData.nodes.AttributeType, {
                filter: {
                    attributeTypeRestrictedTo: { _id: this._id },
                    isActive: true,
                },
                first: 1,
            }).length) > 0;

        if (isRestrictedToInUse) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__attribute__type__referential__integrity_isRestrictedToInUse',
                'The attribute type is in use. You cannot deactivate it.',
            );
        }
    },
    async controlDelete(cx) {
        if (await checkTypeOrValue(this, storedAttributesDataType)) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__attribute__type__referential__integrity',
                'Cannot delete attribute type, it is already in use.',
            );
        }
    },
})
export class AttributeType extends Node {
    @decorators.booleanProperty<AttributeType, 'isActive'>({
        isStored: true,
        isPublished: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
        lookupAccess: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<AttributeType, 'name'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<AttributeType, 'id'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.id,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.enumProperty<AttributeType, 'nodeLink'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremFinanceData.enums.nodeLinkDataType,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly nodeLink: Promise<xtremFinanceData.enums.NodeLink>;

    @decorators.jsonProperty<AttributeType, 'queryFilter'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly queryFilter: Promise<AnyRecord>;

    @decorators.enumProperty<AttributeType, 'analyticalMeasureType'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.analyticalMeasureTypeDataType,
        getValue() {
            return 'attribute';
        },
        lookupAccess: true,
    })
    readonly analyticalMeasureType: Promise<xtremFinanceData.enums.AnalyticalMeasureType>;

    @decorators.referenceProperty<AttributeType, 'attributeTypeRestrictedTo'>({
        isStored: true,
        isPublished: true,
        node: () => xtremFinanceData.nodes.AttributeType,
        ignoreIsActive: true,
        duplicateRequiresPrompt: true,
        lookupAccess: true,
        isNullable: true,
    })
    readonly attributeTypeRestrictedTo: Reference<xtremFinanceData.nodes.AttributeType> | null;

    @decorators.booleanProperty<AttributeType, 'isLinkedToSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly isLinkedToSite: Promise<boolean>;

    @decorators.booleanProperty<AttributeType, 'isLinkedToItem'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly isLinkedToItem: Promise<boolean>;

    @decorators.enumArrayProperty<AttributeType, 'linkedTo'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.nodeLinkDataType,
        async computeValue() {
            const linkedTo: xtremFinanceData.enums.NodeLink[] = [];
            if ((await this.isLinkedToSite) === true) {
                linkedTo.push('site');
            }
            if ((await this.isLinkedToItem) === true) {
                linkedTo.push('item');
            }
            return linkedTo;
        },
    })
    readonly linkedTo: Promise<xtremFinanceData.enums.NodeLink[]>;
}
