import type { Collection, Reference, date, decimal, integer } from '@sage/xtrem-core';
import { Node, decorators, nanoIdDataType, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '../index';

@decorators.node<AccountingStaging>({
    isClearedByReset: true,
    package: 'xtrem-finance',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    canCreate: true,
    indexes: [
        { orderBy: { batchId: 1, documentNumber: 1, documentType: 1, targetDocumentType: 1, isProcessed: 1 } },
        { orderBy: { documentSysId: 1, documentType: 1 } },
    ],
})
export class AccountingStaging extends Node implements xtremFinanceData.interfaces.FinanceNodes.AccountingStaging {
    @decorators.stringProperty<AccountingStaging, 'batchId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.uuid,
    })
    readonly batchId: Promise<string>;

    @decorators.integerProperty<AccountingStaging, 'batchSize'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
    })
    readonly batchSize: Promise<integer>;

    @decorators.referenceProperty<AccountingStaging, 'baseDocumentLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.BaseDocumentLine,
    })
    readonly baseDocumentLine: Reference<xtremMasterData.nodes.BaseDocumentLine | null>;

    @decorators.referenceProperty<AccountingStaging, 'sourceBaseDocumentLine'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.BaseDocumentLine,
    })
    readonly sourceBaseDocumentLine: Reference<xtremMasterData.nodes.BaseDocumentLine | null>;

    @decorators.referenceProperty<AccountingStaging, 'stockJournal'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremStockData.nodes.StockJournal,
    })
    readonly stockJournal: Reference<xtremStockData.nodes.StockJournal | null>;

    @decorators.integerProperty<AccountingStaging, 'documentSysId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly documentSysId: Promise<integer>;

    @decorators.stringProperty<AccountingStaging, 'documentNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly documentNumber: Promise<string>;

    @decorators.stringProperty<AccountingStaging, 'description'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.mediumString,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<AccountingStaging, 'sourceDocumentNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly sourceDocumentNumber: Promise<string>;

    @decorators.dateProperty<AccountingStaging, 'documentDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
    })
    readonly documentDate: Promise<date>;

    @decorators.dateProperty<AccountingStaging, 'taxDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
    })
    readonly taxDate: Promise<date | null>;

    @decorators.enumProperty<AccountingStaging, 'documentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.financeDocumentTypeDataType,
    })
    readonly documentType: Promise<xtremFinanceData.enums.FinanceDocumentType>;

    @decorators.enumProperty<AccountingStaging, 'sourceDocumentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.sourceDocumentTypeDataType,
    })
    readonly sourceDocumentType: Promise<xtremFinanceData.enums.SourceDocumentType | null>;

    @decorators.enumProperty<AccountingStaging, 'movementType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.movementTypeDataType,
    })
    readonly movementType: Promise<xtremFinanceData.enums.MovementType>;

    @decorators.enumProperty<AccountingStaging, 'targetDocumentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.targetDocumentTypeDataType,
    })
    readonly targetDocumentType: Promise<xtremFinanceData.enums.TargetDocumentType>;

    @decorators.referenceProperty<AccountingStaging, 'financialSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<AccountingStaging, 'recipientSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremSystem.nodes.Site,
    })
    readonly recipientSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<AccountingStaging, 'providerSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremSystem.nodes.Site,
    })
    readonly providerSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<AccountingStaging, 'item'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Item,
    })
    readonly item: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referenceProperty<AccountingStaging, 'account'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremFinanceData.nodes.Account,
    })
    readonly account: Reference<xtremFinanceData.nodes.Account | null>;

    @decorators.referenceProperty<AccountingStaging, 'customer'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Customer,
    })
    readonly customer: Reference<xtremMasterData.nodes.Customer | null>;

    @decorators.referenceProperty<AccountingStaging, 'supplier'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Supplier,
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.referenceProperty<AccountingStaging, 'payToSupplier'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Supplier,
    })
    readonly payToSupplier: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.referenceProperty<AccountingStaging, 'payToSupplierLinkedAddress'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
    })
    readonly payToSupplierLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress | null>;

    @decorators.referenceProperty<AccountingStaging, 'returnLinkedAddress'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
    })
    readonly returnLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress | null>;

    @decorators.referenceProperty<AccountingStaging, 'itemPostingClass'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremFinanceData.nodes.PostingClass,
    })
    readonly itemPostingClass: Reference<xtremFinanceData.nodes.PostingClass | null>;

    @decorators.referenceProperty<AccountingStaging, 'customerPostingClass'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremFinanceData.nodes.PostingClass,
    })
    readonly customerPostingClass: Reference<xtremFinanceData.nodes.PostingClass | null>;

    @decorators.referenceProperty<AccountingStaging, 'supplierPostingClass'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremFinanceData.nodes.PostingClass,
    })
    readonly supplierPostingClass: Reference<xtremFinanceData.nodes.PostingClass | null>;

    @decorators.referenceProperty<AccountingStaging, 'resourcePostingClass'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremFinanceData.nodes.PostingClass,
    })
    readonly resourcePostingClass: Reference<xtremFinanceData.nodes.PostingClass | null>;

    @decorators.referenceProperty<AccountingStaging, 'resource'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.DetailedResource,
    })
    readonly resource: Reference<xtremMasterData.nodes.DetailedResource | null>;

    @decorators.collectionProperty<AccountingStaging, 'amounts'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinanceData.nodes.AccountingStagingAmount,
        reverseReference: 'accountingStaging',
    })
    readonly amounts: Collection<xtremFinanceData.nodes.AccountingStagingAmount>;

    @decorators.referenceProperty<AccountingStaging, 'transactionCurrency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Currency,
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<AccountingStaging, 'companyFxRate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
    })
    readonly companyFxRate: Promise<decimal>;

    @decorators.decimalProperty<AccountingStaging, 'companyFxRateDivisor'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        defaultValue: 1,
    })
    readonly companyFxRateDivisor: Promise<decimal>;

    @decorators.referenceProperty<AccountingStaging, 'paymentTerm'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.PaymentTerm,
    })
    readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm | null>;

    @decorators.dateProperty<AccountingStaging, 'dueDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
    })
    readonly dueDate: Promise<date | null>;

    @decorators.dateProperty<AccountingStaging, 'supplierDocumentDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
    })
    readonly supplierDocumentDate: Promise<date | null>;

    @decorators.stringProperty<AccountingStaging, 'supplierDocumentNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly supplierDocumentNumber: Promise<string>;

    @decorators.booleanProperty<AccountingStaging, 'isPrinted'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        defaultValue: false,
    })
    readonly isPrinted: Promise<boolean>;

    @decorators.enumProperty<AccountingStaging, 'taxCalculationStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremMasterData.enums.taxCalculationStatusDataType,
    })
    readonly taxCalculationStatus: Promise<xtremMasterData.enums.TaxCalculationStatus | null>;

    @decorators.dateProperty<AccountingStaging, 'fxRateDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly fxRateDate: Promise<date>;

    @decorators.jsonProperty<AccountingStaging, 'storedDimensions'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        isNullable: true,
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<AccountingStaging, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<AccountingStaging, 'storedAttributes'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        isNullable: true,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.stringProperty<AccountingStaging, 'originNotificationId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => nanoIdDataType,
    })
    readonly originNotificationId: Promise<string>;

    @decorators.jsonProperty<AccountingStaging, 'storedComputedAttributes'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
    })
    readonly storedComputedAttributes: Promise<object>;

    @decorators.booleanProperty<AccountingStaging, 'isProcessed'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        defaultValue: false,
    })
    readonly isProcessed: Promise<boolean>;

    @decorators.booleanProperty<AccountingStaging, 'toBeReprocessed'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        defaultValue: false,
    })
    readonly toBeReprocessed: Promise<boolean>;

    @decorators.stringProperty<AccountingStaging, 'replyTopic'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly replyTopic: Promise<string>;

    @decorators.collectionProperty<AccountingStaging, 'taxes'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinanceData.nodes.AccountingStagingLineTax,
        reverseReference: 'accountingStaging',
    })
    readonly taxes: Collection<xtremFinanceData.nodes.AccountingStagingLineTax>;
}
