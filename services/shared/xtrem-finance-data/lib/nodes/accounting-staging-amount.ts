import type { Reference, date, decimal } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinanceData from '../index';

@decorators.node<AccountingStagingAmount>({
    package: 'xtrem-finance',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    isVitalCollectionChild: true,
})
export class AccountingStagingAmount
    extends Node
    implements xtremFinanceData.interfaces.FinanceNodes.AccountingStagingAmount
{
    @decorators.referenceProperty<AccountingStagingAmount, 'accountingStaging'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.AccountingStaging,
        isVitalParent: true,
    })
    readonly accountingStaging: Reference<xtremFinanceData.nodes.AccountingStaging>;

    @decorators.enumProperty<AccountingStagingAmount, 'amountType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.amountTypeDataType,
    })
    readonly amountType: Promise<xtremFinanceData.enums.AmountType>;

    @decorators.decimalProperty<AccountingStagingAmount, 'amount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
    })
    readonly amount: Promise<decimal>;

    @decorators.referenceProperty<AccountingStagingAmount, 'tax'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremTax.nodes.Tax,
        isNullable: true,
        ignoreIsActive: true,
    })
    readonly tax: Reference<xtremTax.nodes.Tax | null>;

    @decorators.referenceProperty<AccountingStagingAmount, 'taxPostingClass'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremFinanceData.nodes.PostingClass,
        filters: {
            control: {
                type: 'tax',
            },
        },
    })
    readonly taxPostingClass: Reference<xtremFinanceData.nodes.PostingClass | null>;

    @decorators.referenceProperty<AccountingStagingAmount, 'baseTax'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremTax.nodes.BaseTax,
        isNullable: true,
        ignoreIsActive: true,
    })
    readonly baseTax: Reference<xtremTax.nodes.BaseTax | null>;

    @decorators.enumProperty<AccountingStagingAmount, 'documentLineType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.accountsPayableReceivableInvoiceDocumentLineTypeDataType,
    })
    readonly documentLineType: Promise<xtremFinanceData.enums.AccountsPayableReceivableInvoiceDocumentLineType>;

    @decorators.dateProperty<AccountingStagingAmount, 'taxDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
    })
    readonly taxDate: Promise<date> | null;

    @decorators.decimalProperty<AccountingStagingAmount, 'taxRate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.percentage,
    })
    readonly taxRate: Promise<decimal>;
}
