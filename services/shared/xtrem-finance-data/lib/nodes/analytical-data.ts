import type { Reference } from '@sage/xtrem-core';
import { decorators, JoinLiteralValue, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSytem from '@sage/xtrem-system';
import * as xtremFinanceData from '../index';

@decorators.node<AnalyticalData>({
    storage: 'sql',
    isPublished: true,
    isContentAddressable: true,
    isCached: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
})
export class AnalyticalData extends Node {
    @decorators.referenceProperty<AnalyticalData, 'financialSiteType'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.AttributeType,
        join: {
            id: new JoinLiteralValue('financialSiteType'),
        },
    })
    readonly financialSiteType: Reference<xtremFinanceData.nodes.AttributeType | null>;

    @decorators.referenceProperty<AnalyticalData, 'financialSite'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremSytem.nodes.Site,
    })
    readonly financialSite: Reference<xtremSytem.nodes.Site | null>;

    @decorators.referenceProperty<AnalyticalData, 'businessSiteType'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.AttributeType,
        join: {
            id: new JoinLiteralValue('businessSiteType'),
        },
    })
    readonly businessSiteType: Reference<xtremFinanceData.nodes.AttributeType | null>;

    @decorators.referenceProperty<AnalyticalData, 'businessSite'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremSytem.nodes.Site,
    })
    readonly businessSite: Reference<xtremSytem.nodes.Site | null>;

    @decorators.referenceProperty<AnalyticalData, 'stockSiteType'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.AttributeType,
        join: {
            id: new JoinLiteralValue('stockSiteType'),
        },
    })
    readonly stockSiteType: Reference<xtremFinanceData.nodes.AttributeType | null>;

    @decorators.referenceProperty<AnalyticalData, 'stockSite'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremSytem.nodes.Site,
    })
    readonly stockSite: Reference<xtremSytem.nodes.Site | null>;

    @decorators.referenceProperty<AnalyticalData, 'manufacturingSiteType'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.AttributeType,
        join: {
            id: new JoinLiteralValue('manufacturingSiteType'),
        },
    })
    readonly manufacturingSiteType: Reference<xtremFinanceData.nodes.AttributeType | null>;

    @decorators.referenceProperty<AnalyticalData, 'manufacturingSite'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremSytem.nodes.Site,
    })
    readonly manufacturingSite: Reference<xtremSytem.nodes.Site | null>;

    @decorators.referenceProperty<AnalyticalData, 'supplierType'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.AttributeType,
        join: {
            id: new JoinLiteralValue('supplierType'),
        },
    })
    readonly supplierType: Reference<xtremFinanceData.nodes.AttributeType | null>;

    @decorators.referenceProperty<AnalyticalData, 'supplier'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Supplier,
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.referenceProperty<AnalyticalData, 'customerType'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.AttributeType,
        join: {
            id: new JoinLiteralValue('customerType'),
        },
    })
    readonly customerType: Reference<xtremFinanceData.nodes.AttributeType | null>;

    @decorators.referenceProperty<AnalyticalData, 'customer'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Customer,
    })
    readonly customer: Reference<xtremMasterData.nodes.Customer | null>;

    @decorators.referenceProperty<AnalyticalData, 'projectType'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.AttributeType,
        join: {
            id: new JoinLiteralValue('projectType'),
        },
    })
    readonly projectType: Reference<xtremFinanceData.nodes.AttributeType | null>;

    @decorators.referenceProperty<AnalyticalData, 'project'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
    })
    readonly project: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<AnalyticalData, 'taskType'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.AttributeType,
        join: {
            id: new JoinLiteralValue('taskType'),
        },
    })
    readonly taskType: Reference<xtremFinanceData.nodes.AttributeType | null>;

    @decorators.referenceProperty<AnalyticalData, 'task'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
    })
    readonly task: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<AnalyticalData, 'employeeType'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.AttributeType,
        join: {
            id: new JoinLiteralValue('employeeType'),
        },
    })
    readonly employeeType: Reference<xtremFinanceData.nodes.AttributeType | null>;

    @decorators.referenceProperty<AnalyticalData, 'employee'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
    })
    readonly employee: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<AnalyticalData, 'itemType'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.AttributeType,
        join: {
            id: new JoinLiteralValue('itemType'),
        },
    })
    readonly itemType: Reference<xtremFinanceData.nodes.AttributeType | null>;

    @decorators.referenceProperty<AnalyticalData, 'item'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Item,
        ignoreIsActive: true,
    })
    readonly item: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType01'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType01'),
        },
    })
    readonly dimensionType01: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension01'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension01: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType02'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType02'),
        },
    })
    readonly dimensionType02: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension02'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension02: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType03'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType03'),
        },
    })
    readonly dimensionType03: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension03'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension03: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType04'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType04'),
        },
    })
    readonly dimensionType04: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension04'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension04: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType05'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType05'),
        },
    })
    readonly dimensionType05: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension05'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension05: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType06'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType06'),
        },
    })
    readonly dimensionType06: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension06'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension06: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType07'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType07'),
        },
    })
    readonly dimensionType07: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension07'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension07: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType08'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType08'),
        },
    })
    readonly dimensionType08: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension08'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension08: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType09'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType09'),
        },
    })
    readonly dimensionType09: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension09'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension09: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType10'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType10'),
        },
    })
    readonly dimensionType10: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension10'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension10: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType11'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType11'),
        },
    })
    readonly dimensionType11: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension11'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension11: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType12'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType12'),
        },
    })
    readonly dimensionType12: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension12'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension12: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType13'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType13'),
        },
    })
    readonly dimensionType13: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension13'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension13: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType14'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType14'),
        },
    })
    readonly dimensionType14: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension14'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension14: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType15'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType15'),
        },
    })
    readonly dimensionType15: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension15'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension15: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType16'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType16'),
        },
    })
    readonly dimensionType16: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension16'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension16: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType17'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType17'),
        },
    })
    readonly dimensionType17: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension17'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension17: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType18'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType18'),
        },
    })
    readonly dimensionType18: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension18'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension18: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType19'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType19'),
        },
    })
    readonly dimensionType19: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension19'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension19: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimensionType20'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.DimensionType,
        join: {
            docProperty: new JoinLiteralValue('dimensionType20'),
        },
    })
    readonly dimensionType20: Reference<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AnalyticalData, 'dimension20'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
    })
    readonly dimension20: Reference<xtremFinanceData.nodes.Dimension | null>;
}
