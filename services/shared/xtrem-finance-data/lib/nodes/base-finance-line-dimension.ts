import type { Dict, Reference } from '@sage/xtrem-core';
import { decorators, Node, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '..';

/** abstract Node for attributes & dimension lines
 * To be able to manage attributes & dimension genericly
 */

@decorators.node<BaseFinanceLineDimension>({
    isPublished: true,
    isAbstract: true,
    isVitalCollectionChild: true,
    storage: 'sql',
})
export class BaseFinanceLineDimension extends Node {
    @decorators.referenceProperty<BaseFinanceLineDimension, 'originLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinanceData.nodes.BaseFinanceLine,
    })
    readonly originLine: Reference<xtremFinanceData.nodes.BaseFinanceLine>;

    @decorators.jsonProperty<BaseFinanceLineDimension, 'storedAttributes'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<BaseFinanceLineDimension, 'storedDimensions'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
    })
    readonly storedDimensions: Promise<Dict<string> | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    /** STORED attributes */

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension01'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType01 ?? null;
            },
            dimensionType() {
                return '#dimensionType01';
            },
        },
    })
    readonly dimension01: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension02'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType02 ?? null;
            },
            dimensionType() {
                return '#dimensionType02';
            },
        },
    })
    readonly dimension02: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension03'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType03 ?? null;
            },
            dimensionType() {
                return '#dimensionType03';
            },
        },
    })
    readonly dimension03: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension04'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType04 ?? null;
            },
            dimensionType() {
                return '#dimensionType04';
            },
        },
    })
    readonly dimension04: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension05'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType05 ?? null;
            },
            dimensionType() {
                return '#dimensionType05';
            },
        },
    })
    readonly dimension05: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension06'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType06 ?? null;
            },
            dimensionType() {
                return '#dimensionType06';
            },
        },
    })
    readonly dimension06: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension07'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType07 ?? null;
            },
            dimensionType() {
                return '#dimensionType07';
            },
        },
    })
    readonly dimension07: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension08'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType08 ?? null;
            },
            dimensionType() {
                return '#dimensionType08';
            },
        },
    })
    readonly dimension08: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension09'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType09 ?? null;
            },
            dimensionType() {
                return '#dimensionType09';
            },
        },
    })
    readonly dimension09: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension10'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType10 ?? null;
            },
            dimensionType() {
                return '#dimensionType10';
            },
        },
    })
    readonly dimension10: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension11'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType11 ?? null;
            },
            dimensionType() {
                return '#dimensionType11';
            },
        },
    })
    readonly dimension11: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension12'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType12 ?? null;
            },
            dimensionType() {
                return '#dimensionType12';
            },
        },
    })
    readonly dimension12: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension13'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType13 ?? null;
            },
            dimensionType() {
                return '#dimensionType13';
            },
        },
    })
    readonly dimension13: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension14'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType14 ?? null;
            },
            dimensionType() {
                return '#dimensionType14';
            },
        },
    })
    readonly dimension14: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension15'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType15 ?? null;
            },
            dimensionType() {
                return '#dimensionType15';
            },
        },
    })
    readonly dimension15: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension16'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType16 ?? null;
            },
            dimensionType() {
                return '#dimensionType16';
            },
        },
    })
    readonly dimension16: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension17'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType17 ?? null;
            },
            dimensionType() {
                return '#dimensionType17';
            },
        },
    })
    readonly dimension17: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension18'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType18 ?? null;
            },
            dimensionType() {
                return '#dimensionType18';
            },
        },
    })
    readonly dimension18: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension19'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType19 ?? null;
            },
            dimensionType() {
                return '#dimensionType19';
            },
        },
    })
    readonly dimension19: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'dimension20'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType20 ?? null;
            },
            dimensionType() {
                return '#dimensionType20';
            },
        },
    })
    readonly dimension20: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'financialSite'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.financialSite ?? null;
            },
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'businessSite'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.businessSite ?? null;
            },
        },
    })
    readonly businessSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'stockSite'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.stockSite ?? null;
            },
        },
    })
    readonly stockSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'manufacturingSite'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.manufacturingSite ?? null;
            },
        },
    })
    readonly manufacturingSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'customer'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Customer,
        dependsOn: ['storedAttributes'],
        join: {
            async businessEntity() {
                const id = (await this.storedAttributes)?.customer;
                return id ? `#${id}` : null;
            },
        },
    })
    readonly customer: Reference<xtremMasterData.nodes.Customer | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'supplier'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Supplier,
        dependsOn: ['storedAttributes'],
        join: {
            async businessEntity() {
                const id = (await this.storedAttributes)?.supplier;
                return id ? `#${id}` : null;
            },
        },
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'project'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.project ?? null;
            },
            attributeType() {
                return '#project';
            },
        },
    })
    readonly project: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'task'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.task ?? null;
            },
            attributeType() {
                return '#task';
            },
            async attributeRestrictedToId() {
                return (await this.storedAttributes)?.project ?? null;
            },
        },
    })
    readonly task: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'employee'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.employee ?? null;
            },
            attributeType() {
                return '#employee';
            },
        },
    })
    readonly employee: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<BaseFinanceLineDimension, 'item'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Item,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.item ?? null;
            },
        },
    })
    readonly item: Reference<xtremMasterData.nodes.Item | null>;
}
