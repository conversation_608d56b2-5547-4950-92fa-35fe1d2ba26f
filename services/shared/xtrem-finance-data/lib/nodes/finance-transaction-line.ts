import type { integer, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '../index';

@decorators.node<FinanceTransactionLine>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: {
                sourceDocumentSysId: +1,
                sourceDocumentType: +1,
            },
        },
    ],
})
export class FinanceTransactionLine extends Node {
    @decorators.referenceProperty<FinanceTransactionLine, 'financeTransaction'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        isVitalParent: true,
    })
    readonly financeTransaction: Reference<xtremFinanceData.nodes.FinanceTransaction>;

    @decorators.enumProperty<FinanceTransactionLine, 'sourceDocumentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.sourceDocumentTypeDataType,
    })
    readonly sourceDocumentType: Promise<xtremFinanceData.enums.SourceDocumentType>;

    @decorators.stringProperty<FinanceTransactionLine, 'sourceDocumentNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly sourceDocumentNumber: Promise<string>;

    @decorators.integerProperty<FinanceTransactionLine, 'sourceDocumentSysId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
    })
    readonly sourceDocumentSysId: Promise<integer>;

    @decorators.booleanProperty<FinanceTransactionLine, 'isSourceForDimension'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        defaultValue: false,
    })
    readonly isSourceForDimension: Promise<boolean>;
}
