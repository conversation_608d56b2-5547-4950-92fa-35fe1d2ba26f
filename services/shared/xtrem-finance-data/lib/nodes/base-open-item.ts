import type { Reference, date, decimal, integer } from '@sage/xtrem-core';
import { Node, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '../index';

@decorators.node<BaseOpenItem>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    isAbstract: true,
    canUpdate: true,
    isCustomizable: true,

    async controlBegin(cx) {
        await xtremFinanceData.events.controlBegin.BaseOpenItem.checkCloseReason(this, cx);
        await xtremFinanceData.events.controlBegin.BaseOpenItem.checkAmountConsistency(this, cx);
    },
})
export class BaseOpenItem extends Node {
    @decorators.dateProperty<BaseOpenItem, 'dueDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
    })
    readonly dueDate: Promise<date>;

    @decorators.referenceProperty<BaseOpenItem, 'businessEntity'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        node: () => xtremMasterData.nodes.BusinessEntity,
    })
    readonly businessEntity: Reference<xtremMasterData.nodes.BusinessEntity>;

    @decorators.referenceProperty<BaseOpenItem, 'businessRelation'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.BaseBusinessRelation,
        async getValue() {
            return (await this.businessEntity).supplier;
        },
    })
    readonly businessRelation: Reference<xtremMasterData.nodes.BaseBusinessRelation | null>;

    @decorators.referenceProperty<BaseOpenItem, 'businessEntityPayment'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.BusinessEntity,
    })
    readonly businessEntityPayment: Reference<xtremMasterData.nodes.BusinessEntity>;

    @decorators.enumProperty<BaseOpenItem, 'type'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.enums.businessEntityTypeDataType,
        isFrozen: true,
        async control(cx) {
            if ((await this.type) !== 'customer' && (await this.type) !== 'supplier') {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__open_items__type_invalid',
                    "The open item type needs to be 'Customer' or 'Supplier'.",
                );
            }
        },
    })
    readonly type: Promise<xtremMasterData.enums.BusinessEntityType>;

    @decorators.referenceProperty<BaseOpenItem, 'currency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['businessEntity'],
        async defaultValue() {
            return (await this.businessEntity)?.currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<BaseOpenItem, 'transactionAmountDue'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
    })
    readonly transactionAmountDue: Promise<decimal>;

    @decorators.decimalProperty<BaseOpenItem, 'transactionAmountDueSigned'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return ['purchaseCreditMemo', 'salesCreditMemo'].includes(await this.documentType)
                ? (await this.transactionAmountDue) * -1
                : this.transactionAmountDue;
        },
    })
    readonly transactionAmountDueSigned: Promise<decimal>;

    @decorators.decimalProperty<BaseOpenItem, 'transactionAmountPaid'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
    })
    readonly transactionAmountPaid: Promise<decimal>;

    @decorators.decimalProperty<BaseOpenItem, 'transactionAmountPaidSigned'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return ['purchaseCreditMemo', 'salesCreditMemo'].includes(await this.documentType)
                ? (await this.transactionAmountPaid) * -1
                : this.transactionAmountPaid;
        },
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    })
    readonly transactionAmountPaidSigned: Promise<decimal>;

    @decorators.decimalProperty<BaseOpenItem, 'remainingTransactionAmountSigned'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return ['purchaseCreditMemo', 'salesCreditMemo'].includes(await this.documentType)
                ? ((await this.transactionAmountDue) - (await this.transactionAmountPaid)) * -1
                : (await this.transactionAmountDue) - (await this.transactionAmountPaid);
        },
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    })
    readonly remainingTransactionAmountSigned: Promise<decimal>;

    @decorators.decimalProperty<BaseOpenItem, 'companyAmountDue'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
    })
    readonly companyAmountDue: Promise<decimal>;

    @decorators.decimalProperty<BaseOpenItem, 'companyAmountDueSigned'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return ['purchaseCreditMemo', 'salesCreditMemo'].includes(await this.documentType)
                ? (await this.companyAmountDue) * -1
                : this.companyAmountDue;
        },
    })
    readonly companyAmountDueSigned: Promise<decimal>;

    @decorators.decimalProperty<BaseOpenItem, 'companyAmountPaid'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
    })
    readonly companyAmountPaid: Promise<decimal>;

    @decorators.decimalProperty<BaseOpenItem, 'companyAmountPaidSigned'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return ['purchaseCreditMemo', 'salesCreditMemo'].includes(await this.documentType)
                ? (await this.companyAmountPaid) * -1
                : this.companyAmountPaid;
        },
    })
    readonly companyAmountPaidSigned: Promise<decimal>;

    @decorators.decimalProperty<BaseOpenItem, 'remainingCompanyAmountSigned'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return ['purchaseCreditMemo', 'salesCreditMemo'].includes(await this.documentType)
                ? ((await this.companyAmountDue) - (await this.companyAmountPaid)) * -1
                : (await this.companyAmountDue) - (await this.companyAmountPaid);
        },
    })
    readonly remainingCompanyAmountSigned: Promise<decimal>;

    @decorators.decimalProperty<BaseOpenItem, 'financialSiteAmountDue'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
    })
    readonly financialSiteAmountDue: Promise<decimal>;

    @decorators.decimalProperty<BaseOpenItem, 'financialSiteAmountPaid'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
    })
    readonly financialSiteAmountPaid: Promise<decimal>;

    @decorators.enumProperty<BaseOpenItem, 'status'>({
        isStoredOutput: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.openItemStatusDataType,
        dependsOn: ['transactionAmountDue', 'transactionAmountPaid'],
        async defaultValue() {
            if ((await this.transactionAmountDue) === (await this.transactionAmountPaid)) return 'paid';
            if ((await this.transactionAmountPaid) !== 0) return 'partiallyPaid';
            return 'notPaid';
        },
        updatedValue: useDefaultValue,
    })
    readonly status: Promise<xtremFinanceData.enums.OpenItemStatus>;

    @decorators.enumProperty<BaseOpenItem, 'documentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.financeDocumentTypeDataType,
        isFrozen: true,
        control(cx, val) {
            if (
                ![
                    'purchaseInvoice',
                    'purchaseCreditMemo',
                    'apInvoice',
                    'salesInvoice',
                    'salesCreditMemo',
                    'arInvoice',
                ].includes(val)
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__base_open_item__document_type_invalid',
                    "The document type needs to be 'Purchase invoice,' 'Purchase credit memo,' 'AP Invoice,' 'Sales invoice,' 'Sales credit memo' or 'AR Invoice'.",
                );
            }
        },
    })
    readonly documentType: Promise<xtremFinanceData.enums.FinanceDocumentType>;

    @decorators.stringProperty<BaseOpenItem, 'documentNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        isNotEmpty: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly documentNumber: Promise<string>;

    @decorators.integerProperty<BaseOpenItem, 'documentSysId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        isNotZero: true,
    })
    readonly documentSysId: Promise<integer>;

    @decorators.enumProperty<BaseOpenItem, 'discountFrom'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.enums.dueDateTypeDataType,
    })
    readonly discountFrom: Promise<xtremMasterData.enums.DueDateType | null>;

    @decorators.integerProperty<BaseOpenItem, 'discountDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly discountDate: Promise<integer | null>;

    @decorators.enumProperty<BaseOpenItem, 'discountType'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.enums.paymentTermDiscountOrPenaltyTypeDataType,
    })
    readonly discountType: Promise<xtremMasterData.enums.PaymentTermDiscountOrPenaltyType | null>;

    @decorators.decimalProperty<BaseOpenItem, 'discountAmount'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.price,
    })
    readonly discountAmount: Promise<decimal | null>;

    @decorators.dateProperty<BaseOpenItem, 'discountPaymentBeforeDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly discountPaymentBeforeDate: Promise<date | null>;

    @decorators.dateProperty<BaseOpenItem, 'displayDiscountPaymentDate'>({
        isPublished: true,
        isNullable: true,
        async getValue() {
            return (await this.discountAmount) !== 0 ? this.discountPaymentBeforeDate : null;
        },
    })
    readonly displayDiscountPaymentDate: Promise<date | null>;

    @decorators.enumProperty<BaseOpenItem, 'penaltyPaymentType'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.enums.discountOrPenaltyTypeDataType,
    })
    readonly penaltyPaymentType: Promise<xtremMasterData.enums.DiscountOrPenaltyType | null>;

    @decorators.decimalProperty<BaseOpenItem, 'penaltyAmount'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.price,
    })
    readonly penaltyAmount: Promise<decimal | null>;

    @decorators.referenceProperty<BaseOpenItem, 'closeReason'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.CloseReason,
        dataType: () => xtremFinanceData.dataTypes.closeReason,
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    })
    readonly closeReason: Reference<xtremFinanceData.nodes.CloseReason | null>;

    @decorators.stringProperty<BaseOpenItem, 'closeText'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.mediumString,
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    })
    readonly closeText: Promise<string>;

    @decorators.decimalProperty<BaseOpenItem, 'forcedAmountPaid'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
        async control(cx) {
            if ((await this.forcedAmountPaid) < 0) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__base_open_item__negative_forced_amount_paid',
                    'The forced amount paid needs to be greater than or equal to 0.',
                );
            }
        },
    })
    readonly forcedAmountPaid: Promise<decimal>;

    @decorators.decimalProperty<BaseOpenItem, 'forcedAmountPaidSigned'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
        async getValue() {
            return ['purchaseCreditMemo', 'salesCreditMemo'].includes(await this.documentType)
                ? (await this.forcedAmountPaid) * -1
                : this.forcedAmountPaid;
        },
    })
    readonly forcedAmountPaidSigned: Promise<decimal>;
}
