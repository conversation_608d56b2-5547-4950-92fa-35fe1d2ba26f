import type { Context, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremTax from '@sage/xtrem-tax';
import { getTaxCategoryIdsFromLegislation } from '../functions/posting-class-line-details-lib';
import * as xtremFinanceData from '../index';

@decorators.node<PostingClassLineDetail>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
    async controlBegin(cx) {
        await xtremFinanceData.events.controlBegin.postingClassLineDetail.checkIfPostingClassLineCanHaveAdditionalCriteria(
            cx,
            this,
        );
    },
})
export class PostingClassLineDetail extends Node {
    @decorators.referenceProperty<PostingClassLineDetail, 'postingClassLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinanceData.nodes.PostingClassLine,
    })
    readonly postingClassLine: Reference<xtremFinanceData.nodes.PostingClassLine>;

    @decorators.referenceProperty<PostingClassLineDetail, 'account'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ postingClassLine: ['chartOfAccount'] }],
        node: () => xtremFinanceData.nodes.Account,
        filters: {
            control: {
                async chartOfAccount() {
                    return (await (await this.postingClassLine).chartOfAccount)._id;
                },
            },
        },
    })
    readonly account: Reference<xtremFinanceData.nodes.Account>;

    @decorators.referenceProperty<PostingClassLineDetail, 'tax'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dependsOn: [{ postingClassLine: ['chartOfAccount'] }],
        async control(cx, val) {
            await xtremFinanceData.events.control.postingClassLineDetailControls.taxMandatoryWhenSecondCriteriaIsTax(
                cx,
                val,
                this,
            );
        },
        node: () => xtremTax.nodes.Tax,
        filters: {
            control: {
                country: {
                    async legislation() {
                        return (await (await this.postingClassLine).chartOfAccount).legislation;
                    },
                },
                taxCategory: {
                    async id() {
                        const taxCategoryIds = await getTaxCategoryIdsFromLegislation(
                            await (
                                await (
                                    await this.postingClassLine
                                ).chartOfAccount
                            ).legislation,
                        );
                        return { _in: taxCategoryIds };
                    },
                },
            },
        },
    })
    readonly tax: Reference<xtremTax.nodes.Tax | null>;

    // TODO: Remove this workaround when the filter is fixed in the backend
    // https://jira.sage.com/browse/XT-71936
    @decorators.mutation<typeof PostingClassLineDetail, 'getTaxCategoryIds'>({
        isPublished: true,
        parameters: [
            {
                name: 'legislation',
                type: 'reference',
                node: () => xtremStructure.nodes.Legislation,
            },
        ],
        return: {
            type: 'array',
            item: { type: 'string' },
        },
    })
    static getTaxCategoryIds(
        _context: Context,
        legislation: xtremStructure.nodes.Legislation | null,
    ): Promise<string[]> {
        return getTaxCategoryIdsFromLegislation(legislation) as Promise<string[]>;
    }
}
