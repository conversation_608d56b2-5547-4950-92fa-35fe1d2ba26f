import type { Collection, integer, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinanceData from '../index';

@decorators.node<Account>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    canCreate: true,
    indexes: [{ orderBy: { id: +1, chartOfAccount: +1 }, isUnique: true, isNaturalKey: true }],
    notifies: ['created', 'updated', 'deleted'],
    isSetupNode: true,
    async controlBegin(cx) {
        if (await (await this.chartOfAccount).legislation) {
            const taxSolutionLineSubjectToGlTaxExcludedAmount =
                await xtremFinanceData.functions.Common.isSubjectToGlTaxExcludedAmount({
                    context: this.$.context,
                    legislationId: (await (await (await this.chartOfAccount).legislation)?.id) || '',
                    isVatFilter: false,
                });

            if (!taxSolutionLineSubjectToGlTaxExcludedAmount && (await this.taxManagement) !== 'other') {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__account__tax_management_control',
                    "When tax information is excluded from journal entries, you need to set tax management to 'Other.'",
                );
            }
        }
    },
})
export class Account extends Node {
    @decorators.stringProperty<Account, 'id'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.id,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.referenceProperty<Account, 'chartOfAccount'>({
        isStored: true,
        isPublished: true,
        node: () => xtremStructure.nodes.ChartOfAccount,
        duplicateRequiresPrompt: true,
        lookupAccess: true,
    })
    readonly chartOfAccount: Reference<xtremStructure.nodes.ChartOfAccount>;

    @decorators.booleanProperty<Account, 'isActive'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
        lookupAccess: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<Account, 'name'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Account, 'composedDescription'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            return `${await this.id} -- ${await this.name}`;
        },
    })
    readonly composedDescription: Promise<string>;

    @decorators.booleanProperty<Account, 'isDirectEntryForbidden'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly isDirectEntryForbidden: Promise<boolean>;

    @decorators.booleanProperty<Account, 'isControl'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly isControl: Promise<boolean>;

    @decorators.collectionProperty<Account, 'attributeTypes'>({
        isPublished: true,
        isRequired: false,
        isVital: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.AccountAttributeType,
        reverseReference: 'account',
    })
    readonly attributeTypes: Collection<xtremFinanceData.nodes.AccountAttributeType>;

    @decorators.collectionProperty<Account, 'dimensionTypes'>({
        isPublished: true,
        isRequired: false,
        isVital: true,
        node: () => xtremFinanceData.nodes.AccountDimensionType,
        reverseReference: 'account',
        lookupAccess: true,
    })
    readonly dimensionTypes: Collection<xtremFinanceData.nodes.AccountDimensionType>;

    @decorators.enumProperty<Account, 'taxManagement'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.taxManagementDataType,
        defaultValue: 'other',
        lookupAccess: true,
    })
    readonly taxManagement: Promise<xtremFinanceData.enums.TaxManagement>;

    @decorators.booleanProperty<Account, 'isAutomaticAccount'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        isStored: true,
        isPublished: true,
    })
    readonly isAutomaticAccount: Promise<boolean>;

    @decorators.referenceProperty<Account, 'tax'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremTax.nodes.Tax,
        lookupAccess: true,
        async control(cx) {
            await xtremFinanceData.events.control.Account.taxControls(this, cx);
        },
    })
    readonly tax: Reference<xtremTax.nodes.Tax | null>;

    @decorators.integerProperty<Account, 'datevId'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        isStored: true,
        isPublished: true,
        isNullable: true,
        async control(cx) {
            await xtremFinanceData.events.control.Account.datevIdControls(this, cx);
        },
        duplicateRequiresPrompt: true,
        duplicatedValue: null,
    })
    readonly datevId: Promise<integer | null>;
}
