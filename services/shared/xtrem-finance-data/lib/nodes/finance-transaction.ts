import type { Collection, Context, integer, Reference } from '@sage/xtrem-core';
import { datetime, decorators, Node, NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '..';
import { getDocumentNumberLink, getPostingStatusDocumentData } from '../functions/finance-transaction';
import { getDocumentPageNameFinance } from '../shared-functions/get-page';

@decorators.node<FinanceTransaction>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    indexes: [
        {
            orderBy: {
                // If the index changes, review the sort function on xtremFinanceData.functions.notification-lib._sendToAccountingStagingNotifications
                batchId: +1,
                documentNumber: +1,
                documentType: +1,
                targetDocumentType: +1,
            },
            isUnique: true,
        },
        { orderBy: { documentNumber: +1, documentType: +1 } },
        { orderBy: { documentSysId: +1, documentType: +1 } },
        { orderBy: { sourceDocumentSysId: +1, sourceDocumentType: +1 } },
    ],
    async controlBegin(cx) {
        if (
            this.$.status === NodeStatus.modified &&
            !xtremFinanceData.functions.canUpdateFinanceTransactionStatus(
                await this.status,
                await (
                    await this.$.old
                ).status,
            )
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__finance_transaction__status_update_not_allowed',
                'The finance transaction status cannot be updated from {{previousStatus}} to {{newStatus}}.',
                { previousStatus: await (await this.$.old).status, newStatus: await this.status },
            );
        }
    },
    async saveBegin() {
        if (this.$.status === NodeStatus.modified && (await (await this.$.old).status) !== (await this.status)) {
            await this.$.set({ lastStatusUpdate: datetime.now() });
        }
    },
})
export class FinanceTransaction extends Node {
    @decorators.stringProperty<FinanceTransaction, 'batchId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.uuid,
    })
    readonly batchId: Promise<string>;

    @decorators.stringProperty<FinanceTransaction, 'documentNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly documentNumber: Promise<string>;

    @decorators.integerProperty<FinanceTransaction, 'documentSysId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly documentSysId: Promise<integer>;

    @decorators.enumProperty<FinanceTransaction, 'documentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremFinanceData.enums.financeDocumentTypeDataType,
    })
    readonly documentType: Promise<xtremFinanceData.enums.FinanceDocumentType>;

    @decorators.enumProperty<FinanceTransaction, 'targetDocumentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremFinanceData.enums.targetDocumentTypeDataType,
    })
    readonly targetDocumentType: Promise<xtremFinanceData.enums.TargetDocumentType>;

    @decorators.stringProperty<FinanceTransaction, 'targetDocumentNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly targetDocumentNumber: Promise<string>;

    @decorators.integerProperty<FinanceTransaction, 'targetDocumentSysId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly targetDocumentSysId: Promise<integer>;

    @decorators.stringProperty<FinanceTransaction, 'sourceDocumentLink'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.url,
        async computeValue() {
            return getDocumentPageNameFinance({
                sourceType: await this.sourceDocumentType,
                type: await this.documentType,
            });
        },
    })
    readonly sourceDocumentLink: Promise<string>;

    @decorators.jsonProperty<FinanceTransaction, 'documentNumberLink'>({
        isPublished: true,
        lookupAccess: true,
        computeValue() {
            return getDocumentNumberLink(this);
        },
    })
    readonly documentNumberLink: Promise<xtremSynchronization.sharedFunctions.LinkToPage>;

    @decorators.enumProperty<FinanceTransaction, 'sourceDocumentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        dataType: () => xtremFinanceData.enums.sourceDocumentTypeDataType,
    })
    readonly sourceDocumentType: Promise<xtremFinanceData.enums.SourceDocumentType>;

    @decorators.stringProperty<FinanceTransaction, 'sourceDocumentNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly sourceDocumentNumber: Promise<string>;

    @decorators.integerProperty<FinanceTransaction, 'sourceDocumentSysId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
    })
    readonly sourceDocumentSysId: Promise<integer>;

    @decorators.referenceProperty<FinanceTransaction, 'financialSite'>({
        isStored: true,
        node: () => xtremSystem.nodes.Site,
        isPublished: true,
        isFrozen: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.enumProperty<FinanceTransaction, 'status'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
    })
    readonly status: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.enumProperty<FinanceTransaction, 'postingStatus'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.postingStatusDataType,
        dependsOn: ['status', 'financeIntegrationApp'],
        async computeValue() {
            return xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(this.$.context, {
                financeIntegrationStatus: await this.status,
                externalIntegration: (await this.financeIntegrationApp) !== null,
                financialSite: await this.financialSite,
                documentType: await this.documentType,
                targetDocumentType: await this.targetDocumentType,
            });
        },
    })
    readonly postingStatus: Promise<xtremFinanceData.enums.PostingStatus>;

    @decorators.enumProperty<FinanceTransaction, 'financeIntegrationApp'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationAppDataType,
    })
    readonly financeIntegrationApp: Promise<xtremFinanceData.enums.FinanceIntegrationApp | null>;

    @decorators.stringProperty<FinanceTransaction, 'financeIntegrationAppRecordId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly financeIntegrationAppRecordId: Promise<string>;

    @decorators.stringProperty<FinanceTransaction, 'financeIntegrationAppUrl'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.url,
    })
    readonly financeIntegrationAppUrl: Promise<string>;

    // TODO: [RM] work on the message to be an array
    @decorators.stringProperty<FinanceTransaction, 'message'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly message: Promise<string>;

    @decorators.datetimeProperty<FinanceTransaction, 'lastStatusUpdate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: () => datetime.now(),
    })
    readonly lastStatusUpdate: Promise<datetime>;

    @decorators.collectionProperty<FinanceTransaction, 'lines'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinanceData.nodes.FinanceTransactionLine,
        reverseReference: 'financeTransaction',
    })
    readonly lines: Collection<xtremFinanceData.nodes.FinanceTransactionLine>;

    // Do we have at least one finance transaction line which is source for dimensions
    @decorators.booleanProperty<FinanceTransaction, 'hasSourceForDimensionLines'>({
        isPublished: true,
        lookupAccess: true,
        getValue() {
            return this.lines.some(line => line.isSourceForDimension);
        },
    })
    readonly hasSourceForDimensionLines: Promise<boolean>;

    @decorators.referenceProperty<FinanceTransaction, 'paymentTracking'>({
        isStored: true,
        isPublished: true,
        node: () => xtremFinanceData.nodes.PaymentTracking,
        lookupAccess: true,
        isNullable: true,
    })
    readonly paymentTracking: Reference<xtremFinanceData.nodes.PaymentTracking | null>;

    // Query for getting detail information on the posting status of a document. To be used in various document pages
    // such as Sales invoice or credit memo, Purchase invoice or credit memo, AP or AR invoice
    @decorators.query<typeof FinanceTransaction, 'getPostingStatusData'>({
        isPublished: true,
        parameters: [{ name: 'documentNumber', type: 'string' }],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    _id: 'string',
                    documentType: { type: 'enum', dataType: () => xtremFinanceData.enums.targetDocumentTypeDataType },
                    documentNumber: 'string',
                    documentSysId: 'integer',
                    status: { type: 'enum', dataType: () => xtremFinanceData.enums.postingStatusDataType },
                    message: 'string',
                    hasFinanceIntegrationApp: 'boolean',
                    financeIntegrationApp: {
                        type: 'enum',
                        dataType: () => xtremFinanceData.enums.financeIntegrationAppDataType,
                        isNullable: true,
                    },
                    financeIntegrationAppRecordId: 'string',
                    financeIntegrationAppUrl: 'string',
                    externalLink: 'boolean',
                    hasSourceForDimensionLines: 'boolean',
                },
            },
        },
    })
    static async getPostingStatusData(
        context: Context,
        documentNumber: string,
    ): Promise<Array<xtremFinanceData.interfaces.FinancePostingStatusData>> {
        const financeTransactionRecords = context.query(xtremFinanceData.nodes.FinanceTransaction, {
            filter: { documentNumber },
        });

        return getPostingStatusDocumentData(await financeTransactionRecords.toArray());
    }

    // Query for getting detail information on the posting status of a document.
    // To be used in document pages where the document can also be a source document of a journal entry
    @decorators.query<typeof FinanceTransaction, 'getPostingStatusDataByDocumentId'>({
        isPublished: true,
        parameters: [
            { name: 'documentType', type: 'enum', dataType: () => xtremFinanceData.enums.financeDocumentTypeDataType },
            { name: 'documentSysId', type: 'string' },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    _id: 'string',
                    documentType: { type: 'enum', dataType: () => xtremFinanceData.enums.targetDocumentTypeDataType },
                    documentNumber: 'string',
                    documentSysId: 'integer',
                    status: { type: 'enum', dataType: () => xtremFinanceData.enums.postingStatusDataType },
                    message: 'string',
                    hasFinanceIntegrationApp: 'boolean',
                    financeIntegrationApp: {
                        type: 'enum',
                        dataType: () => xtremFinanceData.enums.financeIntegrationAppDataType,
                        isNullable: true,
                    },
                    financeIntegrationAppRecordId: 'string',
                    financeIntegrationAppUrl: 'string',
                    externalLink: 'boolean',
                    hasSourceForDimensionLines: 'boolean',
                },
            },
        },
    })
    static async getPostingStatusDataByDocumentId(
        context: Context,
        documentType: string,
        documentSysId: string,
    ): Promise<Array<xtremFinanceData.interfaces.FinancePostingStatusData>> {
        const financeTransactionRecords = context.query(xtremFinanceData.nodes.FinanceTransaction, {
            filter: { documentType, documentSysId },
        });
        const financeTransactionRecordsFromLines = context
            .query(xtremFinanceData.nodes.FinanceTransactionLine, {
                filter: { sourceDocumentType: documentType, sourceDocumentSysId: documentSysId },
            })
            .map(financeTransactionRecordLine => financeTransactionRecordLine.financeTransaction);

        return getPostingStatusDocumentData([
            ...(await financeTransactionRecords.toArray()),
            ...(await financeTransactionRecordsFromLines.toArray()),
        ]);
    }
}
