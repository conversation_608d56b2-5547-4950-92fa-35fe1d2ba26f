import type { Reference } from '@sage/xtrem-core';
import { Node, NodeStatus, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { storedDimensionsDataType } from '../data-types/stored-dimensions-data-type';
import { checkTypeOrValue } from '../functions/dimensions';
import * as xtremFinanceData from '../index';

@decorators.node<Dimension>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isCached: true,
    canDuplicate: true,
    indexes: [
        {
            orderBy: { id: +1, dimensionType: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    notifies: ['created', 'updated', 'deleted'],
    async controlBegin(cx) {
        if ((await (await this.dimensionType).isActive) === false && (await this.specificsFields())) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__dimension__type__inactive',
                'The dimension type is inactive',
            );
        }
    },
    async controlDelete(cx) {
        if (await checkTypeOrValue(this, storedDimensionsDataType)) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__dimension__referential__integrity',
                'Cannot delete dimension, it is already in use.',
            );
        }
    },
    // Temporary disabled in v46 - will be restored with full fix in v47
    // async deleteBegin() {
    //     const analyticalDataFactory = this.$.context.application.getFactoryByConstructor(
    //         xtremFinanceData.nodes.AnalyticalData,
    //     );
    //     await garbageCollectContentAddressableTable(this.$.context, analyticalDataFactory);
    // },
})
export class Dimension extends Node {
    @decorators.booleanProperty<Dimension, 'isActive'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: true,
        provides: ['isActive'],
        duplicateRequiresPrompt: true,
        lookupAccess: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<Dimension, 'id'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.id,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
        isFrozen() {
            return this.$.status !== NodeStatus.added;
        },
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<Dimension, 'name'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isNotEmpty: true,
        dataType: () => xtremMasterData.dataTypes.mediumString,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Dimension, 'composedDescription'>({
        isPublished: true,
        async getValue() {
            return `${await this.id} -- ${await this.name}`;
        },
        lookupAccess: true,
    })
    readonly composedDescription: Promise<string>;

    @decorators.referenceProperty<Dimension, 'dimensionType'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isNullable: false,
        node: () => xtremFinanceData.nodes.DimensionType,
        ignoreIsActive: true,
        duplicateRequiresPrompt: true,
        lookupAccess: true,
        isFrozen() {
            return this.$.status !== NodeStatus.added;
        },
    })
    readonly dimensionType: Reference<xtremFinanceData.nodes.DimensionType>;

    /**
     *  Used to desactivate this.dimensionType.isActive control to modify specifics fields
     * @returns
     */
    // eslint-disable-next-line class-methods-use-this, require-await
    async specificsFields(): Promise<boolean> {
        return true;
    }
}
