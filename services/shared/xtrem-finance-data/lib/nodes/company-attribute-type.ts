import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '../index';

@decorators.node<CompanyAttributeType>({
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
    storage: 'sql',
})
export class CompanyAttributeType extends Node {
    @decorators.referenceProperty<CompanyAttributeType, 'company'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.Company,
        isVitalParent: true,
    })
    readonly company: Reference<xtremSystem.nodes.Company>;

    @decorators.referenceProperty<CompanyAttributeType, 'attributeType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.AttributeType,
        filters: {
            control: {
                nodeLink: 'attribute',
            },
        },
        async control(cx) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-finance-data/nodes__company_attribute_type__attribute-type-not-active',
                    'The attribute type is inactive',
                )
                .if(await (await this.attributeType).isActive)
                .is.false();
        },
    })
    readonly attributeType: Reference<xtremFinanceData.nodes.AttributeType>;

    @decorators.booleanProperty<CompanyAttributeType, 'isRequired'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        defaultValue: true,
        async control(cx) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-finance-data/nodes__company_attribute_type__attribute-type-not-active',
                    'The attribute type is inactive',
                )
                .if(await (await this.attributeType).isActive)
                .is.false();
        },
    })
    readonly isRequired: Promise<boolean>;

    @decorators.enumProperty<CompanyAttributeType, 'analyticalMeasureType'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.analyticalMeasureTypeDataType,
        getValue() {
            return 'attribute';
        },
    })
    readonly analyticalMeasureType: Promise<xtremFinanceData.enums.AnalyticalMeasureType>;
}
