import type { Reference, ValidationContext } from '@sage/xtrem-core';
import { decorators, Node, NodeStatus } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '../index';

@decorators.node<PostingClassDefinition>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    indexes: [
        {
            orderBy: { legislation: +1, postingClassType: +1, id: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isSetupNode: true,

    async controlEnd(cx: ValidationContext) {
        if (
            this.$.status === NodeStatus.modified &&
            (await (await this.$.old).isDetailed) !== (await this.isDetailed)
        ) {
            await xtremFinanceData.events.control.postingClassDefinitionControls.controlDetailedSwitch(
                this.$.context,
                cx,
                this._id,
            );
        }
    },
})
export class PostingClassDefinition extends Node {
    @decorators.referenceProperty<PostingClassDefinition, 'legislation'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremStructure.nodes.Legislation,
        lookupAccess: true,
    })
    readonly legislation: Reference<xtremStructure.nodes.Legislation>;

    @decorators.stringProperty<PostingClassDefinition, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.id,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.enumProperty<PostingClassDefinition, 'postingClassType'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremFinanceData.enums.postingClassTypeDataType,
        lookupAccess: true,
    })
    readonly postingClassType: Promise<xtremFinanceData.enums.PostingClassType>;

    @decorators.stringProperty<PostingClassDefinition, 'accountTypeName'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
        isNotEmpty: true,
        lookupAccess: true,
    })
    readonly accountTypeName: Promise<string>;

    @decorators.booleanProperty<PostingClassDefinition, 'isDetailed'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        isOwnedByCustomer: true,
        lookupAccess: true,
    })
    readonly isDetailed: Promise<boolean>;

    @decorators.booleanProperty<PostingClassDefinition, 'isStockItemAllowed'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: false,
        lookupAccess: true,
    })
    readonly isStockItemAllowed: Promise<boolean>;

    @decorators.booleanProperty<PostingClassDefinition, 'isNonStockItemAllowed'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: false,
        lookupAccess: true,
    })
    readonly isNonStockItemAllowed: Promise<boolean>;

    @decorators.booleanProperty<PostingClassDefinition, 'isServiceItemAllowed'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: false,
        lookupAccess: true,
    })
    readonly isServiceItemAllowed: Promise<boolean>;

    @decorators.booleanProperty<PostingClassDefinition, 'isLandedCostItemAllowed'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: false,
        lookupAccess: true,
    })
    readonly isLandedCostItemAllowed: Promise<boolean>;

    @decorators.enumArrayProperty<PostingClassDefinition, 'financeItemType'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.financeItemTypeDataType,
        async computeValue() {
            const financeItemTypeSelection: xtremFinanceData.enums.FinanceItemType[] = [];
            if ((await this.isStockItemAllowed) === true) {
                financeItemTypeSelection.push('stockItem');
            }
            if ((await this.isNonStockItemAllowed) === true) {
                financeItemTypeSelection.push('nonStockItem');
            }
            if ((await this.isServiceItemAllowed) === true) {
                financeItemTypeSelection.push('serviceItem');
            }
            if ((await this.isLandedCostItemAllowed) === true) {
                financeItemTypeSelection.push('landedCostItem');
            }
            return financeItemTypeSelection;
        },
        lookupAccess: true,
    })
    readonly financeItemType: Promise<xtremFinanceData.enums.FinanceItemType[]>;

    @decorators.booleanProperty<PostingClassDefinition, 'canHaveAdditionalCriteria'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: false,
        async control(cx, canHaveAdditionalCriteria) {
            if (canHaveAdditionalCriteria === true && (await this.postingClassType) !== 'item') {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__posting_class_definition__can_have_secondary_criteria_not_allowed',
                    `You cannot enter a secondary criteria for this posting class type: {{ postingClassType }}.`,
                    { postingClassType: await this.postingClassType },
                );
            }
        },
    })
    readonly canHaveAdditionalCriteria: Promise<boolean>;

    @decorators.enumProperty<PostingClassDefinition, 'additionalCriteria'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.postingClassTypeDataType,
        lookupAccess: true,
        isOwnedByCustomer: true,
        async control(cx, value) {
            await xtremFinanceData.events.control.postingClassDefinitionControls.additionalCriteria({
                validationContext: cx,
                additionalCriteria: value,
                postingClassDefinition: this,
            });
        },
    })
    readonly additionalCriteria: Promise<xtremFinanceData.enums.PostingClassType> | null;
}
