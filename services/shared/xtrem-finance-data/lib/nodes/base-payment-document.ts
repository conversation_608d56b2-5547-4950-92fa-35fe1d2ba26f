import type { Collection, Reference, date, decimal } from '@sage/xtrem-core';
import { Logger, Node, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { voidDateControl } from '../functions/base-payment-document';
import * as xtremFinanceData from '../index';

const logger = Logger.getLogger(__filename, 'base-payment-document');

@decorators.node<BasePaymentDocument>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    isAbstract: true,
    isCustomizable: true,
    indexes: [{ orderBy: { number: +1 }, isUnique: true, isNaturalKey: true }],
    prepare() {
        if (!this.$.isValueDeferred('number')) {
            logger.warn(() =>
                this.$.context.localize(
                    '@sage/xtrem-finance-data/nodes__base_payment_document__id_already_exists',
                    'The ID already exists. No sequence number will be allocated to the current document.',
                ),
            );
        }
    },
    async controlBegin(cx) {
        await xtremFinanceData.events.controlBegin.BasePaymentDocument.checkSiteConsistency(this, cx);
        await xtremFinanceData.events.controlBegin.BasePaymentDocument.checkAmountConsistency(this, cx);
    },
    async saveBegin() {
        await xtremMasterData.functions.controlDocumentNumber(this);
        await xtremFinanceData.events.saveBegin.BasePaymentDocument.saveBegin(this);
    },
})
export class BasePaymentDocument extends Node {
    @decorators.stringProperty<BasePaymentDocument, 'number'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        provides: ['sequenceNumber'],
        lookupAccess: true,
        defaultValue() {
            return xtremMasterData.functions.getDocumentNumberPlaceholder();
        },
        deferredDefaultValue() {
            return xtremMasterData.functions.allocateDocumentNumber(this);
        },
    })
    readonly number: Promise<string>;

    @decorators.referenceProperty<BasePaymentDocument, 'bankAccount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.BankAccount,
    })
    readonly bankAccount: Reference<xtremFinanceData.nodes.BankAccount | null>;

    @decorators.referenceProperty<BasePaymentDocument, 'financialSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
        filters: {
            control: {
                isFinance: true,
                async legalCompany() {
                    return {
                        _id: {
                            _ne: (
                                await (
                                    await (
                                        await (
                                            await this.businessRelation
                                        )?.businessEntity
                                    )?.site
                                )?.legalCompany
                            )?._id,
                        },
                    };
                },
            },
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.stringProperty<BasePaymentDocument, 'financialSiteName'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNotEmpty: true,
        isRequired: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => xtremSystem.dataTypes.name,
        dependsOn: ['financialSite'],
        async defaultValue() {
            return (await this.financialSite).name;
        },
    })
    readonly financialSiteName: Promise<string>;

    @decorators.referenceProperty<BasePaymentDocument, 'businessRelation'>({
        isPublished: true,
        isRequired: true,
        isStored: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.BaseBusinessRelation,
        filters: {
            control: {
                businessEntity: {
                    site: {
                        async legalCompany() {
                            return { _id: { _ne: (await (await this.financialSite).legalCompany)._id } };
                        },
                    },
                },
            },
        },
    })
    businessRelation: Reference<xtremMasterData.nodes.BaseBusinessRelation>;

    @decorators.stringProperty<BasePaymentDocument, 'businessRelationName'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.name,
        dependsOn: ['businessRelation'],
        async defaultValue() {
            return (await this.businessRelation).name;
        },
    })
    readonly businessRelationName: Promise<string>;

    @decorators.referenceProperty<BasePaymentDocument, 'customer'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.BaseBusinessRelation,
        dependsOn: ['businessRelation'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await this.businessRelation).$.factory.name === 'Customer' ? this.businessRelation : null;
        },
    })
    customer: Reference<xtremMasterData.nodes.BaseBusinessRelation | null>;

    @decorators.referenceProperty<BasePaymentDocument, 'supplier'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.BaseBusinessRelation,
        dependsOn: ['businessRelation'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await this.businessRelation).$.factory.name === 'Supplier' ? this.businessRelation : null;
        },
    })
    supplier: Reference<xtremMasterData.nodes.BaseBusinessRelation | null>;

    @decorators.enumProperty<BasePaymentDocument, 'type'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.enums.BusinessRelationTypeDataType,
        dependsOn: ['businessRelation'],
        async defaultValue() {
            return (await this.businessRelation).$.factory.name === 'Customer' ? 'customer' : 'supplier';
        },
    })
    readonly type: Promise<xtremMasterData.enums.BusinessRelationType>;

    @decorators.enumProperty<BasePaymentDocument, 'paymentMethod'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.enums.PaymentMethodDataType,
        defaultValue: null,
    })
    readonly paymentMethod: Promise<xtremMasterData.enums.PaymentMethod>;

    @decorators.stringProperty<BasePaymentDocument, 'reference'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.mediumString,
    })
    readonly reference: Promise<string>;

    @decorators.dateProperty<BasePaymentDocument, 'paymentDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        provides: ['documentDate'],
    })
    readonly paymentDate: Promise<date>;

    @decorators.dateProperty<BasePaymentDocument, 'postingDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dependsOn: ['paymentDate'],
        defaultValue() {
            return this.paymentDate;
        },
    })
    readonly postingDate: Promise<date>;

    @decorators.referenceProperty<BasePaymentDocument, 'currency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['businessRelation'],
        async defaultValue() {
            return (await this.businessRelation)?.currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<BasePaymentDocument, 'companyCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<BasePaymentDocument, 'transactionCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        getValue() {
            return this.currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.dateProperty<BasePaymentDocument, 'exchangeRateDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dependsOn: ['paymentDate'],
        defaultValue() {
            return this.paymentDate;
        },
    })
    readonly exchangeRateDate: Promise<date>;

    @decorators.decimalProperty<BasePaymentDocument, 'companyExchangeRate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'financialSite', 'exchangeRateDate'],
        async defaultValue() {
            return (
                await xtremMasterData.functions.getRateOrReverseRate(
                    this.$.context,
                    await this.currency,
                    await (
                        await (
                            await this.financialSite
                        ).legalCompany
                    ).currency,
                    await this.exchangeRateDate,
                )
            ).rate;
        },
    })
    readonly companyExchangeRate: Promise<decimal>;

    @decorators.decimalProperty<BasePaymentDocument, 'companyExchangeRateDivisor'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'financialSite', 'exchangeRateDate'],
        async defaultValue() {
            return (
                await xtremMasterData.functions.getRateOrReverseRate(
                    this.$.context,
                    await this.currency,
                    await (
                        await (
                            await this.financialSite
                        ).legalCompany
                    ).currency,
                    await this.exchangeRateDate,
                )
            ).divisor;
        },
    })
    readonly companyExchangeRateDivisor: Promise<decimal>;

    @decorators.enumProperty<BasePaymentDocument, 'postingStatus'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.journalStatusDataType,
        defaultValue() {
            return 'posted';
        },
        lookupAccess: true,
    })
    readonly postingStatus: Promise<xtremFinanceData.enums.JournalStatus>;

    @decorators.decimalProperty<BasePaymentDocument, 'amount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
    })
    readonly amount: Promise<decimal>;

    @decorators.decimalProperty<BasePaymentDocument, 'companyAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
    })
    readonly companyAmount: Promise<decimal>;

    @decorators.decimalProperty<BasePaymentDocument, 'amountBankCurrency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
    })
    readonly amountBankCurrency: Promise<decimal>;

    @decorators.booleanProperty<BasePaymentDocument, 'isVoided'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: false,
        lookupAccess: true,
    })
    readonly isVoided: Promise<boolean>;

    @decorators.stringProperty<BasePaymentDocument, 'voidText'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.mediumString,
    })
    readonly voidText: Promise<string>;

    @decorators.dateProperty<BasePaymentDocument, 'voidDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        async control(cx, val) {
            await voidDateControl({
                context: cx,
                voidDate: val,
                paymentDate: await this.paymentDate,
                isVoided: await this.isVoided,
            });
        },
    })
    readonly voidDate: Promise<date | null>;

    @decorators.collectionProperty<BasePaymentDocument, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => xtremFinanceData.nodes.PaymentDocumentLine,
        reverseReference: 'document',
        lookupAccess: true,
    })
    readonly lines: Collection<xtremFinanceData.nodes.PaymentDocumentLine>;
}
