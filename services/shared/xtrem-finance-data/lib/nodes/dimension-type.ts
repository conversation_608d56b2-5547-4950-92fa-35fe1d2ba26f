import type { Collection } from '@sage/xtrem-core';
import { decorators, Logger, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { storedDimensionsDataType } from '../data-types/stored-dimensions-data-type';
import { checkTypeOrValue } from '../functions/dimensions';
import * as xtremFinanceData from '../index';

const logger = Logger.getLogger(__filename, 'dimension-type');
@decorators.node<DimensionType>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isCached: true,
    isSetupNode: true,
    indexes: [
        {
            orderBy: { docProperty: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    async controlEnd(cx) {
        const dimTypeCount = await this.$.context.queryCount(xtremFinanceData.nodes.DimensionType);
        logger.debug(() => `dimTypeCount ${dimTypeCount}`);
        if (dimTypeCount === 20) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__dimension_type__max_value',
                'All available dimension types are allocated',
            );
        }
        logger.debug(() => `dimTypeCount ${dimTypeCount}`);
    },
    async controlDelete(cx) {
        if (await checkTypeOrValue(this, storedDimensionsDataType)) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__dimension__type__referential__integrity',
                'Cannot delete dimension type, it is already in use.',
            );
        }
    },
})
export class DimensionType extends Node {
    @decorators.booleanProperty<DimensionType, 'isActive'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
        lookupAccess: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<DimensionType, 'name'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.extraLargeString,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    /**
     * Unique dimension 1 to dimension 20
     */
    @decorators.enumProperty<DimensionType, 'docProperty'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.docPropertyDataType,
        lookupAccess: true,
        isFrozen() {
            return this.isDimensionTypeInUse();
        },
    })
    readonly docProperty: Promise<xtremFinanceData.enums.DocProperty>;

    @decorators.booleanProperty<DimensionType, 'isUsed'>({
        isPublished: true,
        computeValue() {
            return this.isDimensionTypeInUse();
        },
    })
    readonly isUsed: Promise<boolean>;

    @decorators.collectionProperty<DimensionType, 'dimensions'>({
        isPublished: true,
        node: () => xtremFinanceData.nodes.Dimension,
        reverseReference: 'dimensionType',
        lookupAccess: true,
    })
    readonly dimensions: Collection<xtremFinanceData.nodes.Dimension>;

    @decorators.enumProperty<DimensionType, 'analyticalMeasureType'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.analyticalMeasureTypeDataType,
        getValue() {
            return 'dimension';
        },
        lookupAccess: true,
    })
    readonly analyticalMeasureType: Promise<xtremFinanceData.enums.AnalyticalMeasureType>;

    async isDimensionTypeInUse() {
        return (
            (await this.$.context.queryCount(xtremFinanceData.nodes.Dimension, {
                filter: {
                    dimensionType: this._id,
                },
            })) > 0
        );
    }
}
