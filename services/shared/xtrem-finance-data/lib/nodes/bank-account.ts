import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '..';

const packageName = 'xtrem-finance-data';

@decorators.node<BankAccount>({
    package: packageName,
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
    serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
})
export class BankAccount extends Node {
    @decorators.booleanProperty<BankAccount, 'isActive'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: true,
        duplicateRequiresPrompt: true,
    })
    readonly isActive: Promise<boolean>;

    /** BANKNAME */
    @decorators.stringProperty<BankAccount, 'name'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly name: Promise<string>;

    /** BANKACCOUNTID  // intacctID field  */
    @decorators.stringProperty<BankAccount, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.code,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
    })
    readonly id: Promise<string>;

    @decorators.referenceProperty<BankAccount, 'financialSite'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        filters: {
            control: {
                isFinance: true,
                isActive: true,
            },
        },
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<BankAccount, 'currency'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        lookupAccess: true,
        dependsOn: ['financialSite'],
        async defaultValue() {
            return (await this.financialSite).currency;
        },
        filters: {
            control: {
                isActive: true,
            },
        },
        duplicateRequiresPrompt: true,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.enumProperty<BankAccount, 'bankAccountType'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.bankAccountTypeDataType,
        defaultValue: 'current',
        lookupAccess: true,
    })
    readonly bankAccountType: Promise<xtremFinanceData.enums.BankAccountType>;

    // /** BANKACCOUNTTYPE  "checking", "savings" */
    // @decorators.stringProperty<BankAccount, 'type'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremSystem.dataTypes.name,
    //     lookupAccess: true,
    // })
    // readonly type: Promise<string>;

    // /** FINANCIALDATA.APJOURNAL payable  */
    // @decorators.stringProperty<BankAccount, 'payableJournal'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremSystem.dataTypes.name,
    //     lookupAccess: true,
    // })
    // readonly payableJournal: Promise<string>;

    // /** FINANCIALDATA.ARPJOURNAL receivable */
    // @decorators.stringProperty<BankAccount, 'receivableJournal'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremSystem.dataTypes.name,
    //     lookupAccess: true,
    // })
    // readonly receivableJournal: Promise<string>;

    //     /** Intacct BANKACCOUNTNO */
    //     @decorators.stringProperty<BankAccount, 'number'>({
    //         isStored: true,
    //         isPublished: true,
    //         dataType: () => xtremSystem.dataTypes.code,
    //         lookupAccess: true,
    //     })
    //     readonly number: Promise<string>;

    //     @decorators.referenceProperty<BankAccount, 'account'>({
    //         isStored: true,
    //         isPublished: true,
    //         isRequired: true,
    //         node: () => xtremFinanceData.nodes.Account,
    //         lookupAccess: true,
    //     })
    //     readonly account: Reference<xtremFinanceData.nodes.Account>;
}
