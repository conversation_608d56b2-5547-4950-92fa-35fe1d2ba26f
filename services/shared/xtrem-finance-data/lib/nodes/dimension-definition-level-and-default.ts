import type { Context } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '../index';

@decorators.node<DimensionDefinitionLevelAndDefault>({
    storage: 'sql',
    isPublished: true,
    isCached: true,
    canRead: true,
    canUpdate: true,
    canDelete: false,
    indexes: [
        {
            orderBy: { dimensionDefinitionLevel: 1, masterDataDefault: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isSetupNode: true,
})
export class DimensionDefinitionLevelAndDefault extends Node {
    @decorators.enumProperty<DimensionDefinitionLevelAndDefault, 'dimensionDefinitionLevel'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.dimensionDefinitionLevelDataType,
        isNullable: false,
    })
    readonly dimensionDefinitionLevel: Promise<xtremFinanceData.enums.DimensionDefinitionLevel>;

    @decorators.enumProperty<DimensionDefinitionLevelAndDefault, 'masterDataDefault'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.masterDataDefaultDataType,
        isNullable: false,
    })
    readonly masterDataDefault: Promise<xtremFinanceData.enums.MasterDataDefault>;

    /**
     * Gets the default attributes and dimensions for a given company and site using the rules for defaulting from the company
     * @param data.dimensionDefinitionLevel either 'manufacturing', 'sales', ... depending on the document for which dimensions are needed
     * @param data.companyId _id of the company to retrieve the rules for defaulting
     * @param data.site a site from which the defaults are taken if the rule specifies it as source
     * @param data.customerId _id of the customer from which the defaults are taken if the rule specifies it as source
     * @param data.supplierId _id of the supplier from which the defaults are taken if the rule specifies it as source
     * @param data.itemId _id of the item from which the defaults are taken if the rule specifies it as source
     * @returns object with the following structure: { attributes: string; dimensions: string }
     */
    @decorators.query<typeof DimensionDefinitionLevelAndDefault, 'getDefaultAttributesAndDimensions'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                isMandatory: true,
                properties: {
                    dimensionDefinitionLevel: {
                        type: 'enum',
                        dataType: () => xtremFinanceData.enums.dimensionDefinitionLevelDataType,
                    },
                    onlyFromItem: 'boolean',
                    companyId: 'integer',
                    siteId: 'integer',
                    customerId: 'integer',
                    supplierId: 'integer',
                    itemId: 'integer',
                    receivingSiteId: 'integer',
                    shippingSiteId: 'integer',
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                attributes: 'string',
                dimensions: 'string',
            },
        },
    })
    static async getDefaultAttributesAndDimensions(
        context: Context,
        data: xtremFinanceData.interfaces.DefaultQueryOptions,
    ): Promise<{ attributes: string; dimensions: string }> {
        const returnObject = {
            attributes: '{}',
            dimensions: '{}',
        };
        const customer = data.customerId
            ? await context.tryRead(xtremMasterData.nodes.Customer, { _id: data.customerId })
            : undefined;
        const supplier = data.supplierId
            ? await context.tryRead(xtremMasterData.nodes.Supplier, { _id: data.supplierId })
            : undefined;
        const site = await context.tryRead(xtremSystem.nodes.Site, { _id: data.siteId });
        const shippingSite = await context.tryRead(xtremSystem.nodes.Site, { _id: data.shippingSiteId });
        const receivingSite = await context.tryRead(xtremSystem.nodes.Site, { _id: data.receivingSiteId });
        const item = data.itemId ? await context.tryRead(xtremMasterData.nodes.Item, { _id: data.itemId }) : undefined;
        if (site) {
            const attributeDefaults = await xtremFinanceData.functions.getDefaultAttributes(context, {
                dimensionDefinitionLevel: data.dimensionDefinitionLevel,
                onlyFromItem: data.onlyFromItem,
                companyId: data.companyId,
                site,
                customer: customer || undefined,
                supplier: supplier || undefined,
                item: item || undefined,
                receivingSite: receivingSite || undefined,
                shippingSite: shippingSite || undefined,
            });
            if (attributeDefaults) {
                returnObject.attributes = JSON.stringify(attributeDefaults);
            }
            const dimensionDefaults = await xtremFinanceData.functions.getDefaultDimensions(context, {
                dimensionDefinitionLevel: data.dimensionDefinitionLevel,
                onlyFromItem: data.onlyFromItem,
                companyId: data.companyId,
                site,
                customer: customer || undefined,
                supplier: supplier || undefined,
                item: item || undefined,
                receivingSite: receivingSite || undefined,
                shippingSite: shippingSite || undefined,
            });
            if (dimensionDefaults) {
                returnObject.dimensions = JSON.stringify(dimensionDefaults);
            }
        }
        return returnObject;
    }

    /**
     * Gets the default attributes and dimensions for order to order management for a given company and site using
     * the rules for defaulting from the company
     * @param data.dimensionDefinitionLevel  either 'manufacturingOrderToOrder', ... depending on the document for which dimensions are needed
     * @param data.companyId _id of the company to retrieve the rules for defaulting
     * @param data.site a site from which the defaults are taken if the rule specifies it as source
     * @param data.storedAttributes: attributes from the source document
     * @param data.storedDimensions: dimensions from the source document
     * @returns object with the following structure: { attributes: string; dimensions: string }
     */
    @decorators.query<typeof DimensionDefinitionLevelAndDefault, 'getDefaultAttributesAndDimensionsOrderToOrder'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                isMandatory: true,
                properties: {
                    dimensionDefinitionLevel: {
                        type: 'enum',
                        dataType: () => xtremFinanceData.enums.dimensionDefinitionLevelDataType,
                    },
                    companyId: 'integer',
                    siteId: 'integer',
                    supplierId: 'integer',
                    itemId: 'integer',
                    storedAttributes: 'string',
                    storedDimensions: 'string',
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                attributes: 'string',
                dimensions: 'string',
            },
        },
    })
    static async getDefaultAttributesAndDimensionsOrderToOrder(
        context: Context,
        data: xtremFinanceData.interfaces.DefaultQueryOptionsOrderToOrder,
    ): Promise<{ attributes: string; dimensions: string }> {
        const returnObject = {
            attributes: '{}',
            dimensions: '{}',
        };
        const supplier = data.supplierId
            ? await context.tryRead(xtremMasterData.nodes.Supplier, { _id: data.supplierId })
            : undefined;
        const item = data.itemId ? await context.tryRead(xtremMasterData.nodes.Item, { _id: data.itemId }) : undefined;
        const site = await context.tryRead(xtremSystem.nodes.Site, { _id: data.siteId });
        if (site) {
            const attributeDefaultsOrderToOrder = await xtremFinanceData.functions.getDefaultOrderToOrderAttributes(
                context,
                {
                    dimensionDefinitionLevel: data.dimensionDefinitionLevel,
                    onlyFromItem: data.onlyFromItem,
                    companyId: data.companyId,
                    site,
                    supplier: supplier || undefined,
                    item: item || undefined,
                    storedAttributes: JSON.parse(data.storedAttributes),
                },
            );
            if (attributeDefaultsOrderToOrder) {
                returnObject.attributes = JSON.stringify(attributeDefaultsOrderToOrder);
            }
            const dimensionDefaultsOrderToOrder = await xtremFinanceData.functions.getDefaultOrderToOrderDimensions(
                context,
                {
                    dimensionDefinitionLevel: data.dimensionDefinitionLevel,
                    onlyFromItem: data.onlyFromItem,
                    companyId: data.companyId,
                    site,
                    supplier: supplier || undefined,
                    item: item || undefined,
                    storedDimensions: JSON.parse(data.storedDimensions),
                },
            );
            if (dimensionDefaultsOrderToOrder) {
                returnObject.dimensions = JSON.stringify(dimensionDefaultsOrderToOrder);
            }
        }
        return returnObject;
    }

    /**
     * Checks if on company there are default settings using the item as source for defaulting. If yes, it
     * returns an array with the names of the attributes/dimensions that are defaulted from item
     * @param data.dimensionDefinitionLevel  either 'manufacturingOrderToOrder', ... depending on the document for which dimensions are needed
     * @param data.companyId _id of the company to retrieve the rules for defaulting
     * @returns empty array if no attribute/dimension is defaulted from item, array of attributes/dimension names else
     */
    @decorators.query<typeof DimensionDefinitionLevelAndDefault, 'getAttributesAndDimensionsFromItem'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                isMandatory: true,
                properties: {
                    dimensionDefinitionLevel: {
                        type: 'enum',
                        dataType: () => xtremFinanceData.enums.dimensionDefinitionLevelDataType,
                    },
                    companyId: 'integer',
                },
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'string',
            },
        },
    })
    static async getAttributesAndDimensionsFromItem(
        context: Context,
        data: { dimensionDefinitionLevel: xtremFinanceData.enums.DimensionDefinitionLevel; companyId: number },
    ): Promise<string[]> {
        const returnArray: string[] = [];
        // get the rules for the company
        const companyDefaultAttributes = context.query(xtremFinanceData.nodes.CompanyDefaultAttribute, {
            filter: { company: data.companyId, dimensionDefinitionLevel: data.dimensionDefinitionLevel },
        });
        const companyDefaultDimensions = context.query(xtremFinanceData.nodes.CompanyDefaultDimension, {
            filter: { company: data.companyId, dimensionDefinitionLevel: data.dimensionDefinitionLevel },
        });
        await companyDefaultAttributes.forEach(
            async (defaultAttribute: xtremFinanceData.nodes.CompanyDefaultAttribute) => {
                if ((await defaultAttribute.masterDataDefault) === 'item') {
                    returnArray.push(await (await defaultAttribute.attributeType).name);
                }
            },
        );
        await companyDefaultDimensions.forEach(
            async (defaultDimension: xtremFinanceData.nodes.CompanyDefaultDimension) => {
                if ((await defaultDimension.masterDataDefault) === 'item') {
                    returnArray.push(await (await defaultDimension.dimensionType).name);
                }
            },
        );
        return returnArray;
    }
}
