import type { Collection, Context, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import { processPostingClass } from '../functions/posting-class-line';
import * as xtremFinanceData from '../index';
import type { JournalEntryTypePostingClassLine } from '../interfaces/posting-class-line';

@decorators.node<PostingClassLine>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
    async saveBegin() {
        if (await this.updateAccountTaxManagement) {
            const account = await this.$.context.read(
                xtremFinanceData.nodes.Account,
                { _id: (await this.account)?._id },
                { forUpdate: true },
            );
            await account.$.set({ taxManagement: 'tax' });
            await account.$.save();
        }
    },
})
export class PostingClassLine extends Node {
    @decorators.referenceProperty<PostingClassLine, 'postingClass'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.PostingClass,
        isVitalParent: true,
    })
    readonly postingClass: Reference<xtremFinanceData.nodes.PostingClass>;

    @decorators.referenceProperty<PostingClassLine, 'chartOfAccount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremStructure.nodes.ChartOfAccount,
        defaultValue() {
            return xtremStructure.nodes.ChartOfAccount.getSingleChartOfAccount(this.$.context);
        },
        filters: {
            lookup: {
                async name() {
                    // Note: Unfortunately ChartOfAccounts node does not have an id property.
                    //       We have to use the name property instead.

                    // Get all distinct chart of account names used by a company
                    const companies = await this.$.context
                        .queryAggregate(xtremSystem.nodes.Company, {
                            group: { chartOfAccount: { name: { _by: 'value' } } },
                            values: { name: { distinctCount: true } },
                        })
                        .toArray();

                    // Build an array of chart of account names.
                    const usedChartOfAccountNames = companies
                        ? companies.map(chartOfAccount => chartOfAccount.group.chartOfAccount.name)
                        : [];

                    // Set the filter according to this array.
                    return { _in: usedChartOfAccountNames };
                },
            },
        },
    })
    readonly chartOfAccount: Reference<xtremStructure.nodes.ChartOfAccount>;

    @decorators.referenceProperty<PostingClassLine, 'definition'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.PostingClassDefinition,
        dependsOn: ['chartOfAccount'],
        async control(cx) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-finance-data/nodes__posting_class_line__wrong_posting_class_definition_type',
                    'The posting class definition must have the {{ postingClassType }} type.',
                    async () => {
                        return { postingClassType: await (await this.postingClass).type };
                    },
                )
                .if(await (await this.definition).postingClassType)
                .is.not.equal.to(await (await this.postingClass).type);
            await cx.error
                .withMessage(
                    '@sage/xtrem-finance-data/nodes__posting_class_line__wrong_posting_class_definition_is_detailed',
                    'If the posting class is detailed, only posting class definitions with the Detailed checkbox selected are allowed. If the posting class is not detailed, only posting class definitions without the Detailed checkbox selected are allowed.',
                )
                .if(await (await this.definition).isDetailed)
                .is.not.equal.to(await (await this.postingClass).isDetailed);

            await cx.error
                .withMessage(
                    '@sage/xtrem-finance-data/nodes__posting_class_line__already_used_posting_class_definition',
                    'The same posting class definition is already in use by another line.',
                )
                .if((await this.definition)._id)
                .is.in(await this.getDefinitionIdsFromOtherLines());
        },
        filters: {
            control: {
                postingClassType: { _nin: ['header', 'line'] },
                async legislation() {
                    return (await (await this.chartOfAccount)?.legislation) || null;
                },
            },
        },
    })
    readonly definition: Reference<xtremFinanceData.nodes.PostingClassDefinition>;

    @decorators.referenceProperty<PostingClassLine, 'account'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.Account,
        dependsOn: ['chartOfAccount'],
        isNullable: true,
        filters: {
            control: {
                async chartOfAccount() {
                    return (await this.chartOfAccount)._id;
                },
            },
        },
        async control(cx, val) {
            await xtremFinanceData.events.control.postingClassAccountControl.selectAccountManagement(val, cx, this);
            await xtremFinanceData.events.control.postingClassAccountControl.lineAccountNullable(val, cx, this);
        },
    })
    readonly account: Reference<xtremFinanceData.nodes.Account | null>;

    @decorators.booleanProperty<PostingClassLine, 'updateAccountTaxManagement'>({
        isPublished: true,
        isTransientInput: true,
    })
    readonly updateAccountTaxManagement: Promise<boolean>;

    @decorators.booleanProperty<PostingClassLine, 'isStockItemAllowed'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            return (await this.definition).isStockItemAllowed;
        },
    })
    readonly isStockItemAllowed: Promise<boolean>;

    @decorators.booleanProperty<PostingClassLine, 'isNonStockItemAllowed'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            return (await this.definition).isNonStockItemAllowed;
        },
    })
    readonly isNonStockItemAllowed: Promise<boolean>;

    @decorators.booleanProperty<PostingClassLine, 'isServiceItemAllowed'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            return (await this.definition).isServiceItemAllowed;
        },
    })
    readonly isServiceItemAllowed: Promise<boolean>;

    @decorators.booleanProperty<PostingClassLine, 'isLandedCostItemAllowed'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            return (await this.definition).isLandedCostItemAllowed;
        },
    })
    readonly isLandedCostItemAllowed: Promise<boolean>;

    @decorators.collectionProperty<PostingClassLine, 'details'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinanceData.nodes.PostingClassLineDetail,
        reverseReference: 'postingClassLine',
    })
    readonly details: Collection<xtremFinanceData.nodes.PostingClassLineDetail>;

    @decorators.booleanProperty<PostingClassLine, 'hasDetails'>({
        isPublished: true,
        async getValue() {
            return (await this.details.length) > 0;
        },
    })
    readonly hasDetails: Promise<boolean>;

    @decorators.query<typeof PostingClassLine, 'getPostingClassAccounts'>({
        isPublished: true,
        parameters: [
            {
                name: 'postingClassDefinition',
                type: 'reference',
                node: () => xtremFinanceData.nodes.PostingClassDefinition,
                isMandatory: true,
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    postingClass: { type: 'reference', node: () => xtremFinanceData.nodes.PostingClass },
                    account: { type: 'reference', node: () => xtremFinanceData.nodes.Account, isNullable: true },
                    details: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                account: {
                                    type: 'reference',
                                    node: () => xtremFinanceData.nodes.Account,
                                    isNullable: true,
                                },
                                tax: { type: 'reference', node: () => xtremTax.nodes.Tax, isNullable: true },
                            },
                        },
                    },
                },
            },
        },
    })
    static async getPostingClassAccounts(
        context: Context,
        postingClassDefinition: xtremFinanceData.nodes.PostingClassDefinition,
    ): Promise<JournalEntryTypePostingClassLine[]> {
        const postingClasses = context.query(xtremFinanceData.nodes.PostingClass, {
            filter: {
                isDetailed: await postingClassDefinition.isDetailed,
                type: await postingClassDefinition.postingClassType,
            },
        });
        return postingClasses.map(postingClass => processPostingClass(postingClass, postingClassDefinition)).toArray();
    }

    private async getDefinitionIdsFromOtherLines(): Promise<number[]> {
        // Get posting class definitions ID from all lines
        const pcLineDefinitionIds = await (await this.postingClass).lines
            .map(async line => (await line.definition)._id)
            .toArray();

        // ... and remove the current line's posting class line ID.
        const pcLineIds = await (await this.postingClass).lines.map(line => line._id).toArray();
        const index = pcLineIds.indexOf(this._id);
        pcLineDefinitionIds.splice(index, 1);

        return pcLineDefinitionIds;
    }
}
