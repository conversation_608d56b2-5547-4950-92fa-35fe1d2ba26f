import type { Collection, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '../index';

@decorators.node<JournalEntryType>({
    storage: 'sql',
    isPublished: true,
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    indexes: [
        { orderBy: { legislation: 1, documentType: 1, targetDocumentType: 1 }, isUnique: true, isNaturalKey: true },
    ],
    isSetupNode: true,
    async saveBegin() {
        await xtremFinanceData.events.saveBegin.JournalEntryType.saveBegin(this);
    },
    async controlBegin(cx) {
        await xtremFinanceData.events.controlBegin.JournalEntryType.controlBegin(this, cx);
    },
})
export class JournalEntryType extends Node {
    @decorators.booleanProperty<JournalEntryType, 'isActive'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<JournalEntryType, 'name'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<JournalEntryType, 'legislation'>({
        isStored: true,
        node: () => xtremStructure.nodes.Legislation,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
    })
    readonly legislation: Reference<xtremStructure.nodes.Legislation>;

    @decorators.enumProperty<JournalEntryType, 'documentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.financeDocumentTypeDataType,
        isFrozen: true,
    })
    readonly documentType: Promise<xtremFinanceData.enums.FinanceDocumentType>;

    @decorators.enumProperty<JournalEntryType, 'targetDocumentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.targetDocumentTypeDataType,
        isFrozen: true,
    })
    readonly targetDocumentType: Promise<xtremFinanceData.enums.TargetDocumentType>;

    @decorators.booleanProperty<JournalEntryType, 'immediatePosting'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        defaultValue: false,
        isOwnedByCustomer: true,
    })
    readonly immediatePosting: Promise<boolean>;

    @decorators.referenceProperty<JournalEntryType, 'headerJournal'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        lookupAccess: true,
        isOwnedByCustomer: true,
        node: () => xtremFinanceData.nodes.Journal,
        async control(cx, val) {
            if ((await this.targetDocumentType) === 'journalEntry') {
                if (!val) {
                    cx.error.addLocalized(
                        '@sage/xtrem-finance-data/nodes__journal_entry_type__header_journal_mandatory',
                        'The header journal is mandatory.',
                    );
                }
            } else if (val) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type__header_journal_empty',
                    'The header journal must be empty.',
                );
            }
        },
        filters: {
            control: {
                sequence() {
                    return { _ne: null };
                },
            },
        },
    })
    readonly headerJournal: Reference<xtremFinanceData.nodes.Journal | null>;

    @decorators.enumProperty<JournalEntryType, 'headerPostingDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        isOwnedByCustomer: true,
        dataType: () => xtremFinanceData.enums.headerPostingDateType,
        async control(cx, val) {
            if ((await this.targetDocumentType) === 'journalEntry') {
                if (!val) {
                    cx.error.addLocalized(
                        '@sage/xtrem-finance-data/nodes__journal_entry_type__header_posting_date_mandatory',
                        'The header posting date is mandatory.',
                    );
                }
            } else if (val) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type__header_posting_date_empty',
                    'The header posting date must be empty.',
                );
            }
        },
    })
    readonly headerPostingDate: Promise<xtremFinanceData.enums.HeaderPostingDate | null>;

    @decorators.enumProperty<JournalEntryType, 'headerDescription'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isOwnedByCustomer: true,
        dataType: () => xtremFinanceData.enums.headerDescriptionDataType,
    })
    readonly headerDescription: Promise<xtremFinanceData.enums.HeaderDescription>;

    @decorators.referenceProperty<JournalEntryType, 'headerAccountType'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.PostingClassDefinition,
        async control(cx, val) {
            if ((await this.targetDocumentType) === 'journalEntry') {
                if (val) {
                    cx.error.addLocalized(
                        '@sage/xtrem-finance-data/nodes__journal_entry_type__header_account_type_empty',
                        'The header account type must be empty.',
                    );
                }
            } else if (!val) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type__header_account_type_mandatory',
                    'The header account type is mandatory.',
                );
            }
        },
        filters: {
            control: {
                async legislation() {
                    return { _eq: (await this.legislation)._id };
                },
                async postingClassType() {
                    return (await this.targetDocumentType) === 'accountsPayableInvoice'
                        ? { _eq: 'supplier' }
                        : { _eq: 'customer' };
                },
            },
        },
    })
    readonly headerAccountType: Reference<xtremFinanceData.nodes.PostingClassDefinition | null>;

    @decorators.enumProperty<JournalEntryType, 'headerAmountType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.amountTypeDataType,
        async control(cx, val) {
            if ((await this.targetDocumentType) === 'journalEntry') {
                if (val) {
                    cx.error.addLocalized(
                        '@sage/xtrem-finance-data/nodes__journal_entry_type__header_amount_type_empty',
                        'The header amount type must be empty.',
                    );
                }
            } else if (!val) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type__header_amount_type_mandatory',
                    'The header amount type is mandatory.',
                );
            }
        },
    })
    readonly headerAmountType: Promise<xtremFinanceData.enums.AmountType | null>;

    @decorators.collectionProperty<JournalEntryType, 'lines'>({
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isVital: true,
        // isOwnedByCustomer: true,
        reverseReference: 'journalEntryType',
        node: () => xtremFinanceData.nodes.JournalEntryTypeLine,
    })
    readonly lines: Collection<xtremFinanceData.nodes.JournalEntryTypeLine>;
}
