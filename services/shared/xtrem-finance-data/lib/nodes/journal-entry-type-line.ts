import type { Reference } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import { legislationGenericControl } from '../events/control/document-with-legislation';
import * as xtremFinanceData from '../index';

@decorators.node<JournalEntryTypeLine>({
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    hasVendorProperty: true,

    async saveBegin() {
        // force contraJournalEntryTypeLine to be null for Journal entry type with Target document type other than Journal entry
        await xtremFinanceData.events.saveBegin.JournalEntryTypeLine.saveBegin(this);
    },
})
export class JournalEntryTypeLine extends Node {
    @decorators.referenceProperty<JournalEntryTypeLine, 'journalEntryType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinanceData.nodes.JournalEntryType,
    })
    readonly journalEntryType: Reference<xtremFinanceData.nodes.JournalEntryType>;

    @decorators.enumProperty<JournalEntryTypeLine, 'movementType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.movementTypeDataType,
        isFrozen: true,
    })
    readonly movementType: Promise<xtremFinanceData.enums.MovementType>;

    @decorators.referenceProperty<JournalEntryTypeLine, 'accountType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.PostingClassDefinition,
        async control(cx) {
            await legislationGenericControl(
                cx,
                await (
                    await this.accountType
                ).legislation,
                await this.journalEntryType,
            );
            if (
                (await (await this.accountType).postingClassType) === 'item' &&
                !(
                    (await this.isStockItemAllowed) ||
                    (await this.isNonStockItemAllowed) ||
                    (await this.isServiceItemAllowed) ||
                    (await this.isLandedCostItemAllowed)
                )
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal-entry-type-line__at_least_one_item_type_mandatory',
                    'At least one of Stock items, Non stock items, Service items or Landed cost items must be set for Account type with Posting class type Item.',
                );
            }
        },
    })
    readonly accountType: Reference<xtremFinanceData.nodes.PostingClassDefinition>;

    @decorators.enumProperty<JournalEntryTypeLine, 'amountType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.amountTypeDataType,
    })
    readonly amountType: Promise<xtremFinanceData.enums.AmountType>;

    @decorators.enumProperty<JournalEntryTypeLine, 'sign'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.signDataType,
        async control(cx, val) {
            if ((await (await this.journalEntryType).targetDocumentType) === 'journalEntry') {
                if (!val) {
                    cx.error.addLocalized(
                        '@sage/xtrem-finance-data/nodes__journal_entry_type_line__sign_mandatory',
                        'The sign is mandatory.',
                    );
                }
            } else if (val) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type_line__sign__empty',
                    'The sign must be empty.',
                );
            }
        },
    })
    readonly sign: Promise<xtremFinanceData.enums.Sign | null>;

    @decorators.enumProperty<JournalEntryTypeLine, 'commonReference'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.commonReferenceDataType,
        isOwnedByCustomer: true,
    })
    readonly commonReference: Promise<xtremFinanceData.enums.CommonReference>;

    @decorators.booleanProperty<JournalEntryTypeLine, 'isStockItemAllowed'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        async control(cx, val: boolean) {
            if (
                ![
                    'document',
                    'laborSetupTimeTracking',
                    'laborRunTimeTracking',
                    'machineSetupTimeTracking',
                    'machineRunTimeTracking',
                    'toolSetupTimeTracking',
                    'toolRunTimeTracking',
                ].includes(await this.movementType) &&
                !val
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__forced_true',
                    'You need to select Stock items for this movement type: {{movementType}}.',
                    {
                        movementType: this.$.context.localizeEnumMember(
                            '@sage/xtrem-finance-data/MovementType',
                            await this.movementType,
                        ),
                    },
                );
            }

            if (val && !(await (await this.accountType).isStockItemAllowed)) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__posting_class_definition_control',
                    'The Stock items are not allowed by the account type.',
                );
            }
        },
    })
    readonly isStockItemAllowed: Promise<boolean>;

    @decorators.booleanProperty<JournalEntryTypeLine, 'isNonStockItemAllowed'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        async control(cx, val: boolean) {
            if ((await this.movementType) !== 'document' && val) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__forced_false',
                    'The Non stock items may only be set for movement type Document.',
                );
            }
            if (val && !(await (await this.accountType).isNonStockItemAllowed)) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__posting_class_definition_control',
                    'The Non stock items are not allowed by the account type.',
                );
            }
        },
    })
    readonly isNonStockItemAllowed: Promise<boolean>;

    @decorators.booleanProperty<JournalEntryTypeLine, 'isServiceItemAllowed'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        async control(cx, val: boolean) {
            if ((await this.movementType) !== 'document' && val) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__forced_false',
                    'The Service items may only be set for movement type Document.',
                );
            }
            if (val && !(await (await this.accountType).isServiceItemAllowed)) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__posting_class_definition_control',
                    'The Service items are not allowed by the account type.',
                );
            }
        },
    })
    readonly isServiceItemAllowed: Promise<boolean>;

    @decorators.booleanProperty<JournalEntryTypeLine, 'isLandedCostItemAllowed'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        async control(cx, val: boolean) {
            if ((await this.movementType) !== 'document' && val) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__forced_false',
                    'The Landed cost items may only be set for movement type Document.',
                );
            }
            if (val && !(await (await this.accountType).isLandedCostItemAllowed)) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__posting_class_definition_control',
                    'The Landed cost items are not allowed by the account type.',
                );
            }
        },
    })
    readonly isLandedCostItemAllowed: Promise<boolean>;

    @decorators.referenceProperty<JournalEntryTypeLine, 'contraJournalEntryTypeLine'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.JournalEntryTypeLine,
        async control(cx) {
            await xtremFinanceData.events.control.JournalEntryTypeLine.contraJournalEntryTypeLineMandatory(this, cx);
        },
    })
    readonly contraJournalEntryTypeLine: Reference<xtremFinanceData.nodes.JournalEntryTypeLine> | null;
}
