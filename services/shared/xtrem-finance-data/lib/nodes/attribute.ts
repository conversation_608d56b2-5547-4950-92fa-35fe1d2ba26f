import type { Reference } from '@sage/xtrem-core';
import { Node, NodeStatus, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { storedAttributesDataType } from '../data-types/stored-attributes-data-type';
import { checkTypeOrValue } from '../functions/dimensions';
import * as xtremFinanceData from '../index';

@decorators.node<Attribute>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isCached: true,
    canDuplicate: true,
    indexes: [
        {
            orderBy: { id: +1, attributeType: +1, attributeRestrictedToId: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    notifies: ['created', 'updated', 'deleted'],
    async controlBegin(cx) {
        if ((await (await this.attributeType).nodeLink) !== 'attribute') {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__attribute__node_link_value',
                'You need to set the node link to "attribute".',
            );
        }
        // if the attribute type is linked to an attribute, the attributeRestrictedTo must not be empty
        if (!(await this.attributeRestrictedTo) && (await (await this.attributeType).attributeTypeRestrictedTo)) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__attribute__attribute_restricted_to',
                'You need to set the "Restricted to" to a valid attribute.',
            );
        }
    },
    async controlDelete(cx) {
        if (await checkTypeOrValue(this, storedAttributesDataType)) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__attribute__referential__integrity',
                'Cannot delete attribute, it is already in use.',
            );
        }
    },
    async saveBegin() {
        const attributeType = await this.attributeType;
        // if the attribute type is not linked to another attribute type, the attributeRestrictedTo must be null
        if (!(await attributeType.attributeTypeRestrictedTo)) {
            await this.$.set({ attributeRestrictedTo: null, attributeRestrictedToId: '' });
        } else {
            await this.$.set({ attributeRestrictedToId: await (await this.attributeRestrictedTo)?.id });
        }
        // if the attribute type is not linked to a site, the site must be null
        if (!(await attributeType.isLinkedToSite)) {
            await this.$.set({ site: null });
        }
        // if the attribute type is not linked to an item, the item must be null
        if (!(await attributeType.isLinkedToItem)) {
            await this.$.set({ item: null });
        }
    },
    // Temporary disabled in v46 - will be restored with full fix in v47
    // async deleteBegin() {
    //     const analyticalDataFactory = this.$.context.application.getFactoryByConstructor(
    //         xtremFinanceData.nodes.AnalyticalData,
    //     );
    //     await garbageCollectContentAddressableTable(this.$.context, analyticalDataFactory);
    // },
})
export class Attribute extends Node {
    @decorators.booleanProperty<Attribute, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
        duplicateRequiresPrompt: true,
        lookupAccess: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<Attribute, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.id,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
        isFrozen() {
            return this.$.status !== NodeStatus.added;
        },
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<Attribute, 'name'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremMasterData.dataTypes.mediumString,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Attribute, 'composedDescription'>({
        isPublished: true,
        async getValue() {
            return `${await this.id} -- ${await this.name}`;
        },
    })
    readonly composedDescription: Promise<string>;

    @decorators.referenceProperty<Attribute, 'attributeType'>({
        isStored: true,
        isPublished: true,
        node: () => xtremFinanceData.nodes.AttributeType,
        ignoreIsActive: true,
        duplicateRequiresPrompt: true,
        lookupAccess: true,
        isFrozen() {
            return this.$.status !== NodeStatus.added;
        },
    })
    readonly attributeType: Reference<xtremFinanceData.nodes.AttributeType>;

    @decorators.referenceProperty<Attribute, 'attributeRestrictedTo'>({
        isStored: true,
        isPublished: true,
        node: () => xtremFinanceData.nodes.Attribute,
        lookupAccess: true,
        allowedInUniqueIndex: true,
        isNullable: true,
    })
    readonly attributeRestrictedTo: Reference<xtremFinanceData.nodes.Attribute> | null;

    // this property is needed for the unique natural key index. The (circular) reference property attributeRestrictedTo
    // cannot be used in the unique natural key index because in Intacct integration we then get the error
    // "RangeError: Maximum call stack size exceeded" because of the circular reference.
    @decorators.stringProperty<Attribute, 'attributeRestrictedToId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.mediumString,
        lookupAccess: true,
        dependsOn: ['attributeRestrictedTo'],
        async defaultValue() {
            return (await this.attributeRestrictedTo)?.id ?? '';
        },
    })
    readonly attributeRestrictedToId: Promise<string>;

    @decorators.referenceProperty<Attribute, 'site'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        lookupAccess: true,
        isNullable: true,
    })
    readonly site: Reference<xtremSystem.nodes.Site> | null;

    @decorators.referenceProperty<Attribute, 'item'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Item,
        isNullable: true,
    })
    readonly item: Reference<xtremMasterData.nodes.Item> | null;
}
