import type { Reference } from '@sage/xtrem-core';
import { decorators, Node, NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '../index';

@decorators.node<Journal>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    canCreate: true,
    indexes: [{ orderBy: { legislation: +1, id: +1 }, isUnique: true, isNaturalKey: true }],
    notifies: ['created', 'updated', 'deleted'],
    isSetupNode: true,
    async controlEnd(cx) {
        if (
            (await this.sequence) &&
            this.$.status === NodeStatus.modified &&
            (await (await this.$.old).sequence) !== (await this.sequence)
        ) {
            const journalEntryType = await this.$.context
                .query(xtremFinanceData.nodes.JournalEntryType, {
                    filter: {
                        headerJournal: this._id,
                    },
                })
                .toArray();

            if (journalEntryType.length) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_cannot_modify_account_entry_exists',
                    'You cannot modify the sequence number as an account entry exits for this journal.',
                );
            }
        }
    },
})
export class Journal extends Node {
    @decorators.stringProperty<Journal, 'id'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.id,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.referenceProperty<Journal, 'legislation'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremStructure.nodes.Legislation,
        duplicateRequiresPrompt: true,
    })
    readonly legislation: Reference<xtremStructure.nodes.Legislation>;

    @decorators.stringProperty<Journal, 'name'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Journal, 'primaryDocumentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.id,
        async control(cx, value) {
            if ((await (await this.legislation).id) !== 'FR' && value)
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal__primaryDocumentType_not_allowed',
                    'The primary document type is only allowed for the French legislation.',
                );
        },
        duplicateRequiresPrompt: true,
    })
    readonly primaryDocumentType: Promise<string>;

    @decorators.stringProperty<Journal, 'secondaryDocumentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.id,
        async control(cx, value) {
            if ((await (await this.legislation).id) !== 'FR' && value)
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal__secondaryDocumentType_not_allowed',
                    'The secondary document type is only allowed for the French legislation.',
                );
        },
        duplicateRequiresPrompt: true,
    })
    readonly secondaryDocumentType: Promise<string>;

    @decorators.booleanProperty<Journal, 'isActive'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        defaultValue: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.referenceProperty<Journal, 'sequence'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.SequenceNumber,
        duplicatedValue: null,
    })
    readonly sequence: Reference<xtremMasterData.nodes.SequenceNumber | null>;

    @decorators.booleanProperty<Journal, 'isSubjectToGlTaxExcludedAmount'>({
        isPublished: true,
        lookupAccess: true,
        async computeValue() {
            return xtremFinanceData.functions.Common.isSubjectToGlTaxExcludedAmount({
                context: this.$.context,
                legislationId: await (await this.legislation).id,
                isVatFilter: true,
            });
        },
    })
    readonly isSubjectToGlTaxExcludedAmount: Promise<boolean>;

    @decorators.booleanProperty<Journal, 'taxImpact'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        defaultValue: false,
        async control(cx, value) {
            if (!(await this.isSubjectToGlTaxExcludedAmount) && value === true)
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal__taxImpact_cannot_be_set',
                    "At least one line of the tax solution needs to be 'Assigned to GL excluding tax' to set the 'Tax impact' option for the journal.",
                );
        },
        duplicateRequiresPrompt: true,
    })
    readonly taxImpact: Promise<boolean>;
}
