import type { Collection, StaticThis, ValidationContext } from '@sage/xtrem-core';
import { decorators, Node, NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import type * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinanceData from '../index';

@decorators.node<PostingClass>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    canCreate: true,
    canDelete: true,
    canDuplicate: true,
    canSearch: true,
    indexes: [{ orderBy: { id: +1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
    async controlBegin(cx: ValidationContext) {
        if ((await this.type) === 'item' && (await this.isDetailed)) {
            xtremFinanceData.sharedFunctions.common
                .getUsedFinanceItemTypesNotSelected(
                    await this.financeItemType,
                    await this.lines
                        .map(async line => {
                            return {
                                isStockItemAllowed: await line.isStockItemAllowed,
                                isNonStockItemAllowed: await line.isNonStockItemAllowed,
                                isServiceItemAllowed: await line.isServiceItemAllowed,
                                isLandedCostItemAllowed: await line.isLandedCostItemAllowed,
                                definition: {
                                    isStockItemAllowed: await (await line.definition).isStockItemAllowed,
                                    isNonStockItemAllowed: await (await line.definition).isNonStockItemAllowed,
                                    isServiceItemAllowed: await (await line.definition).isServiceItemAllowed,
                                    isLandedCostItemAllowed: await (await line.definition).isLandedCostItemAllowed,
                                },
                            };
                        })
                        .toArray(),
                    await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.landedCostOption),
                )
                .forEach(usedFinanceItemTypesNotSelected => {
                    switch (usedFinanceItemTypesNotSelected) {
                        case 'stockItem':
                            cx.error.addLocalized(
                                '@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_stock_items',
                                'At least one line allows stock items.',
                            );
                            break;
                        case 'nonStockItem':
                            cx.error.addLocalized(
                                '@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_non_stock_items',
                                'At least one line allows non stock items.',
                            );
                            break;
                        case 'serviceItem':
                            cx.error.addLocalized(
                                '@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_service_items',
                                'At least one line allows service items.',
                            );
                            break;
                        case 'landedCostItem':
                            cx.error.addLocalized(
                                '@sage/xtrem-finance-data/nodes__posting_class__at_least_one_line_allow_landed_cost_items',
                                'At least one line allows landed cost items.',
                            );
                            break;
                        default:
                            break;
                    }
                });
        }
    },
    async controlEnd(cx: ValidationContext) {
        await cx.error
            .withMessage(
                '@sage/xtrem-finance-data/pages__posting_class_duplicate__not_allowed',
                'The operation failed because the record already exists.',
            )
            .if(
                await this.$.context.queryCount(xtremFinanceData.nodes.PostingClass, {
                    filter: {
                        _and: [
                            { type: await this.type },
                            { name: await this.name },
                            { isDetailed: await this.isDetailed },
                            { _not: { _id: this._id } },
                        ],
                    },
                }),
            )
            .is.not.equal.to(0);

        await cx.error
            .withMessage(
                '@sage/xtrem-finance-data/nodes__posting_class__lines_mandatory',
                'The posting class needs at least one line.',
            )
            .if(await this.lines.length)
            .is.equal.to(0);

        if (
            this.$.status === NodeStatus.modified &&
            !(await this.isDetailed) &&
            (await this.isDetailed) !== (await (await this.$.old).isDetailed) &&
            !['header', 'line'].includes(await this.type)
        ) {
            type BasicMasterData =
                | xtremMasterData.nodes.Item
                | xtremMasterData.nodes.Customer
                | xtremMasterData.nodes.Supplier;
            type RemainingNodes =
                | xtremMasterData.nodes.DetailedResource
                | xtremSystem.nodes.Company
                | xtremTax.nodes.Tax;
            type NodeWithPostingClass = StaticThis<BasicMasterData | RemainingNodes>;
            const clas = this.$.context.introspection.getNodeFromTableName(
                (await this.type) !== 'resource' ? await this.type : 'detailed_resource',
            ) as unknown as NodeWithPostingClass;
            if (clas) {
                const recordsUsingThisPostingClass = await this.$.context
                    .query(clas, { filter: { postingClass: this._id }, first: 1 })
                    .at(0);
                if (recordsUsingThisPostingClass) {
                    const recordNameUsingThisPostingClass =
                        xtremMasterData.functions.businessEntity.isCustomerSupplierNode(recordsUsingThisPostingClass)
                            ? await (
                                  await recordsUsingThisPostingClass.businessEntity
                              ).name
                            : await recordsUsingThisPostingClass.name;

                    cx.error.addLocalized(
                        '@sage/xtrem-finance-data/nodes__posting_class__update_is_detailed_not_allowed',
                        `You cannot change the 'Detailed' switch to Off because the {{postingClassName}} posting class is linked to {{recordNameUsingThisPostingClass}}.`,
                        { postingClassName: await this.name, recordNameUsingThisPostingClass },
                    );
                }
            }
        }
    },
})
export class PostingClass extends Node {
    @decorators.stringProperty<PostingClass, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.setupIdDataType,
        defaultValue() {
            return xtremSystem.functions.generateNanoId();
        },
        lookupAccess: true,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
    })
    readonly id: Promise<string>;

    @decorators.enumProperty<PostingClass, 'type'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.postingClassTypeDataType,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly type: Promise<xtremFinanceData.enums.PostingClassType>;

    @decorators.stringProperty<PostingClass, 'name'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.booleanProperty<PostingClass, 'isDetailed'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: true,
        async control(cx, val) {
            if (
                !val &&
                (await this.$.context.query(xtremFinanceData.nodes.PostingClass, {
                    filter: {
                        _and: [{ type: await this.type }, { isDetailed: false }, { _not: { _id: this._id } }],
                    },
                }).length) > 0
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__posting_class__one_non_detailed_allowed',
                    'Only one non-detailed posting class of type {{type}} is allowed.',
                    { type: await this.type },
                );
            }
        },
        duplicateRequiresPrompt: true,
        lookupAccess: true,
    })
    readonly isDetailed: Promise<boolean>;

    @decorators.booleanProperty<PostingClass, 'isStockItemAllowed'>({
        isStored: true,
        isPublished: true,
        async control(cx, val) {
            if (val && ((await this.type) !== 'item' || !(await this.isDetailed))) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_stock_item_allowed__not_allowed',
                    'The Stock items are not allowed if the posting class type is not item or the posting class is not detailed.',
                );
            }
        },
        defaultValue: false,
        lookupAccess: true,
    })
    readonly isStockItemAllowed: Promise<boolean>;

    @decorators.booleanProperty<PostingClass, 'isNonStockItemAllowed'>({
        isStored: true,
        isPublished: true,
        async control(cx, val) {
            if (val && ((await this.type) !== 'item' || !(await this.isDetailed))) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_non_stock_item_allowed__not_allowed',
                    'The Non stock items are not allowed if the posting class type is not item or the posting class is not detailed.',
                );
            }
        },
        defaultValue: false,
        lookupAccess: true,
    })
    readonly isNonStockItemAllowed: Promise<boolean>;

    @decorators.booleanProperty<PostingClass, 'isServiceItemAllowed'>({
        isStored: true,
        isPublished: true,
        async control(cx, val) {
            if (val && ((await this.type) !== 'item' || !(await this.isDetailed))) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_service_item_allowed__not_allowed',
                    'The Service items are not allowed if the posting class type is not item or the posting class is not detailed.',
                );
            }
        },
        defaultValue: false,
        lookupAccess: true,
    })
    readonly isServiceItemAllowed: Promise<boolean>;

    @decorators.booleanProperty<PostingClass, 'isLandedCostItemAllowed'>({
        isStored: true,
        isPublished: true,
        async control(cx, val) {
            if (val && ((await this.type) !== 'item' || !(await this.isDetailed))) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__journal_entry_type_line__is_landed_cost_item_allowed__not_allowed',
                    'The Landed cost items are not allowed if the posting class type is not item or the posting class is not detailed.',
                );
            }
        },
        defaultValue: false,
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        lookupAccess: true,
    })
    readonly isLandedCostItemAllowed: Promise<boolean>;

    @decorators.enumArrayProperty<PostingClass, 'financeItemType'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.financeItemTypeDataType,
        async computeValue() {
            return xtremFinanceData.sharedFunctions.common.getFinanceItemTypeSelection({
                isStockItemAllowed: await this.isStockItemAllowed,
                isNonStockItemAllowed: await this.isNonStockItemAllowed,
                isServiceItemAllowed: await this.isServiceItemAllowed,
                isLandedCostItemAllowed: await this.isLandedCostItemAllowed,
            });
        },
        lookupAccess: true,
    })
    readonly financeItemType: Promise<xtremFinanceData.enums.FinanceItemType[]>;

    @decorators.collectionProperty<PostingClass, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => xtremFinanceData.nodes.PostingClassLine,
        reverseReference: 'postingClass',
    })
    readonly lines: Collection<xtremFinanceData.nodes.PostingClassLine>;
}
