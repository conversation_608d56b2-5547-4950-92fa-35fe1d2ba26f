import type { decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '../index';

@decorators.subNode<PaymentDocumentLine>({
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    isPublished: true,
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { originalOpenItem: 1, originalNodeFactory: 1 } }],
})
export class PaymentDocumentLine extends xtremMasterData.nodes.BaseDocumentLine {
    @decorators.referenceProperty<PaymentDocumentLine, 'document'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinanceData.nodes.BasePaymentDocument,
    })
    override readonly document: Reference<xtremFinanceData.nodes.BasePaymentDocument>;

    @decorators.stringPropertyOverride<PaymentDocumentLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<PaymentDocumentLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.referenceProperty<PaymentDocumentLine, 'financialSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ document: ['financialSite'] }],
        node: () => xtremSystem.nodes.Site,
        filters: {
            control: {
                isFinance: true,
                async legalCompany() {
                    return (await (await this.document).financialSite).legalCompany;
                },
            },
        },
        async defaultValue() {
            return (await this.document).financialSite;
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<PaymentDocumentLine, 'currency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ document: ['currency'] }],
        node: () => xtremMasterData.nodes.Currency,
        async defaultValue() {
            return (await this.document).currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<PaymentDocumentLine, 'companyCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<PaymentDocumentLine, 'transactionCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        getValue() {
            return this.currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.enumProperty<PaymentDocumentLine, 'origin'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isFrozen: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.accountsPayableReceivableInvoiceOriginDataType,
    })
    readonly origin: Promise<xtremFinanceData.enums.AccountsPayableReceivableInvoiceOrigin>;

    @decorators.decimalProperty<PaymentDocumentLine, 'amount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
    })
    readonly amount: Promise<decimal>;

    @decorators.decimalProperty<PaymentDocumentLine, 'amountBankCurrency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        dependsOn: ['amount'],
        defaultValue() {
            return this.amount;
        },
    })
    readonly amountBankCurrency: Promise<decimal>;

    @decorators.decimalProperty<PaymentDocumentLine, 'discountAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        defaultValue: 0,
        async control(cx) {
            if (((await this.discountAmount) ?? 0) < 0) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__payment_document_line__negative_discount_amount',
                    'The discount amount needs to be greater than or equal to 0.',
                );
            }
        },
    })
    readonly discountAmount: Promise<decimal>;

    @decorators.decimalProperty<PaymentDocumentLine, 'penaltyAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        defaultValue: 0,
        async control(cx) {
            if (((await this.penaltyAmount) ?? 0) < 0) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/nodes__payment_document_line__negative_penalty_amount',
                    'The penalty amount needs to be greater than or equal to 0.',
                );
            }
        },
    })
    readonly penaltyAmount: Promise<decimal>;

    @decorators.decimalProperty<PaymentDocumentLine, 'adjustmentAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        defaultValue: 0,
    })
    readonly adjustmentAmount: Promise<decimal>;

    @decorators.referenceProperty<PaymentDocumentLine, 'originalOpenItem'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        isNullable: true,
        allowedInUniqueIndex: true,
        node: () => xtremFinanceData.nodes.BaseOpenItem,
    })
    readonly originalOpenItem: Reference<xtremFinanceData.nodes.BaseOpenItem | null>;

    @decorators.referenceProperty<PaymentDocumentLine, 'originalNodeFactory'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        node: () => xtremMetadata.nodes.MetaNodeFactory,
    })
    readonly originalNodeFactory: Reference<xtremMetadata.nodes.MetaNodeFactory>;

    @decorators.referenceProperty<PaymentDocumentLine, 'paymentTracking'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.PaymentTracking,
    })
    readonly paymentTracking: Reference<xtremFinanceData.nodes.PaymentTracking | null>;
}
