import type { Reference } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinanceData from '../index';

@decorators.node<AccountingStagingDocumentTax>({
    isClearedByReset: true,
    package: 'xtrem-finance',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    canCreate: true,
    indexes: [
        {
            orderBy: { batchId: 1, documentNumber: 1, documentType: 1, targetDocumentType: 1 },
        },
    ],
})
export class AccountingStagingDocumentTax extends Node {
    @decorators.stringProperty<AccountingStagingDocumentTax, 'batchId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.uuid,
    })
    readonly batchId: Promise<string>;

    @decorators.enumProperty<AccountingStagingDocumentTax, 'documentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.financeDocumentTypeDataType,
    })
    readonly documentType: Promise<xtremFinanceData.enums.FinanceDocumentType>;

    @decorators.stringProperty<AccountingStagingDocumentTax, 'documentNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly documentNumber: Promise<string>;

    @decorators.enumProperty<AccountingStagingDocumentTax, 'targetDocumentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.targetDocumentTypeDataType,
    })
    readonly targetDocumentType: Promise<xtremFinanceData.enums.TargetDocumentType>;

    @decorators.referenceProperty<AccountingStagingDocumentTax, 'baseTax'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        node: () => xtremTax.nodes.BaseTax,
    })
    readonly baseTax: Reference<xtremTax.nodes.BaseTax>;
}
