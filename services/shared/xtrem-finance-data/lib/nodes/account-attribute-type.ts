import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremFinanceData from '../index';

@decorators.node<AccountAttributeType>({
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
    storage: 'sql',
})
export class AccountAttributeType extends Node {
    @decorators.referenceProperty<AccountAttributeType, 'account'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: false,
        node: () => xtremFinanceData.nodes.Account,
        isVitalParent: true,
    })
    readonly account: Reference<xtremFinanceData.nodes.Account>;

    @decorators.referenceProperty<AccountAttributeType, 'attributeType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: false,
        node: () => xtremFinanceData.nodes.AttributeType,
        ignoreIsActive: true,
    })
    readonly attributeType: Reference<xtremFinanceData.nodes.AttributeType>;

    @decorators.booleanProperty<AccountAttributeType, 'isRequired'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        defaultValue: false,
        async control(cx) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-finance-data/nodes__account_attribute_type__attribute-type-not-active',
                    'The attribute type is inactive',
                )
                .if(await (await this.attributeType).isActive)
                .is.false();
        },
    })
    readonly isRequired: Promise<boolean>;

    @decorators.enumProperty<AccountAttributeType, 'analyticalMeasureType'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.analyticalMeasureTypeDataType,
        getValue() {
            return 'attribute';
        },
    })
    readonly analyticalMeasureType: Promise<xtremFinanceData.enums.AnalyticalMeasureType>;
}
