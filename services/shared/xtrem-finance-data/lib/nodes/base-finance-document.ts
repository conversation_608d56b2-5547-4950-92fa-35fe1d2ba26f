import type { Collection } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '..';

/** abstract Node for document journal entry ap invoice ar invoice
 * must be adapted
 */

@decorators.node<BaseFinanceDocument>({
    isAbstract: true,
    isPublished: true,
    canSearch: true,
    canRead: true,
    storage: 'sql',
    isClearedByReset: true,
    indexes: [{ orderBy: { number: 1 }, isUnique: true }],
})
export class BaseFinanceDocument extends Node implements xtremMasterData.interfaces.Document {
    @decorators.stringProperty<BaseFinanceDocument, 'number'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly number: Promise<string>;

    @decorators.collectionProperty<BaseFinanceDocument, 'lines'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinanceData.nodes.BaseFinanceLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<xtremFinanceData.nodes.BaseFinanceLine>;
}
