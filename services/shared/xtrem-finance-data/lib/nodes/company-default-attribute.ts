import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '../index';

@decorators.node<CompanyDefaultAttribute>({
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    isCached: true,
    isVitalCollectionChild: true,
    storage: 'sql',
    indexes: [
        {
            orderBy: { company: 1, attributeType: 1, dimensionDefinitionLevel: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    async controlEnd(cx) {
        const dimensionDocumentTypeAndDefault = await this.$.context.tryRead(
            xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault,
            {
                dimensionDefinitionLevel: await this.dimensionDefinitionLevel,
                masterDataDefault: await this.masterDataDefault,
            },
        );
        // TODO - Correct message and confirm with Trado<PERSON>
        if (!dimensionDocumentTypeAndDefault) {
            cx.error.addLocalized(
                '@sage/xtrem-finance-data/nodes__company_default_attribute__node_link_master_data_default__not_allowed',
                'You cannot assign the default {{masterDataDefault}} to this document {{dimensionDefinitionLevel}}.',
                {
                    masterDataDefault: this.$.context.localizeEnumMember(
                        '@sage/xtrem-finance-data/MasterDataDefault',
                        await this.masterDataDefault,
                    ),
                    dimensionDefinitionLevel: this.$.context.localizeEnumMember(
                        '@sage/xtrem-finance-data/DimensionDefinitionLevel',
                        await this.dimensionDefinitionLevel,
                    ),
                },
            );
        }
    },
})
export class CompanyDefaultAttribute extends Node {
    @decorators.referenceProperty<CompanyDefaultAttribute, 'company'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.Company,
        isVitalParent: true,
    })
    readonly company: Reference<xtremSystem.nodes.Company>;

    @decorators.referenceProperty<CompanyDefaultAttribute, 'attributeType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.AttributeType,
    })
    readonly attributeType: Reference<xtremFinanceData.nodes.AttributeType>;

    @decorators.enumProperty<CompanyDefaultAttribute, 'dimensionDefinitionLevel'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.dimensionDefinitionLevelDataType,
    })
    readonly dimensionDefinitionLevel: Promise<xtremFinanceData.enums.DimensionDefinitionLevel>;

    @decorators.enumProperty<CompanyDefaultAttribute, 'masterDataDefault'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.masterDataDefaultDataType,
    })
    readonly masterDataDefault: Promise<xtremFinanceData.enums.MasterDataDefault>;
}
