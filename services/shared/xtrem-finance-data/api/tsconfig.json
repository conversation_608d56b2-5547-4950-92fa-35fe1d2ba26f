{"extends": "../../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": ".", "composite": true}, "include": ["api.d.ts"], "references": [{"path": "../../../../platform/system/xtrem-authorization/api"}, {"path": "../../../../platform/front-end/xtrem-client"}, {"path": "../../../../platform/system/xtrem-communication/api"}, {"path": "../../xtrem-landed-cost/api"}, {"path": "../../xtrem-master-data/api"}, {"path": "../../xtrem-stock-data/api"}, {"path": "../../xtrem-structure/api"}, {"path": "../../../../platform/system/xtrem-system/api"}, {"path": "../../xtrem-tax/api"}]}