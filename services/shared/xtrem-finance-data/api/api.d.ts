declare module '@sage/xtrem-finance-data-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type {
        GroupRoleSite,
        Package as SageXtremAuthorization$Package,
        SiteGroup,
    } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type {
        LandedCostItem,
        LandedCostItemBinding,
        LandedCostItemInput,
        Package as SageXtremLandedCost$Package,
    } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        Address,
        BaseBusinessRelation,
        BaseDocument,
        BaseDocumentItemLine,
        BaseDocumentItemLineBinding,
        BaseDocumentItemLineInput,
        BaseDocumentLine,
        BaseDocumentLineInquiryInput,
        BomRevisionSequence,
        BusinessEntity,
        BusinessEntityAddress,
        BusinessEntityInput,
        CompanyAddress,
        CompanyAddressBinding,
        CompanyAddressInput,
        CompanyContact,
        CompanyContactBinding,
        CompanyContactInput,
        Currency,
        Customer,
        CustomerInput,
        CustomerSupplierCategory,
        DeliveryMode,
        DetailedResource,
        DetailedResourceInput,
        GroupResource,
        Incoterm,
        Item,
        ItemAllergen,
        ItemAllergenBinding,
        ItemAllergenInput,
        ItemCategory,
        ItemClassifications,
        ItemClassificationsBinding,
        ItemClassificationsInput,
        ItemCustomer,
        ItemCustomerBinding,
        ItemCustomerInput,
        ItemCustomerPrice,
        ItemCustomerPriceBinding,
        ItemCustomerPriceInput,
        ItemInput,
        ItemSite,
        ItemSiteBinding,
        ItemSiteInput,
        ItemSupplier,
        ItemSupplierBinding,
        ItemSupplierInput,
        ItemSupplierPrice,
        ItemSupplierPriceBinding,
        ItemSupplierPriceInput,
        Location,
        Package as SageXtremMasterData$Package,
        PaymentTerm,
        ResourceCostCategory,
        ResourceCostCategoryBinding,
        ResourceCostCategoryInput,
        SequenceNumber,
        StandardIndustrialClassification,
        Supplier,
        SupplierCertificate,
        SupplierCertificateBinding,
        SupplierCertificateInput,
        SupplierInput,
        UnitOfMeasure,
        WeeklyShift,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type { Package as SageXtremStockData$Package, StockJournal, StockStatus } from '@sage/xtrem-stock-data-api';
    import type {
        ChartOfAccount,
        Country,
        Legislation,
        Package as SageXtremStructure$Package,
    } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type {
        Company,
        CompanyInput,
        Package as SageXtremSystem$Package,
        Site,
        SiteInput,
        SysVendor,
        User,
    } from '@sage/xtrem-system-api';
    import type {
        BaseTax,
        ItemTaxGroup,
        Package as SageXtremTax$Package,
        Tax,
        TaxCategory,
        TaxInput,
        TaxValue,
        TaxValueBinding,
        TaxValueInput,
    } from '@sage/xtrem-tax-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface AccountsPayableReceivableInvoiceDocumentLineType$Enum {
        documentLine: 0;
        taxLine: 1;
    }
    export type AccountsPayableReceivableInvoiceDocumentLineType =
        keyof AccountsPayableReceivableInvoiceDocumentLineType$Enum;
    export interface AccountsPayableReceivableInvoiceLineType$Enum {
        goods: 0;
        services: 1;
        fixedAssets: 2;
    }
    export type AccountsPayableReceivableInvoiceLineType = keyof AccountsPayableReceivableInvoiceLineType$Enum;
    export interface AccountsPayableReceivableInvoiceOrigin$Enum {
        direct: 0;
        invoice: 1;
        creditMemo: 2;
    }
    export type AccountsPayableReceivableInvoiceOrigin = keyof AccountsPayableReceivableInvoiceOrigin$Enum;
    export interface AmountType$Enum {
        amount: 0;
        varianceAmount: 1;
        amountIncludingTax: 2;
        deductibleTaxAmount: 3;
        nonDeductibleTaxAmount: 4;
        reverseChargeDeductibleTaxAmount: 5;
        reverseChargeNonDeductibleTaxAmount: 6;
        amountExcludingTax: 7;
        taxAmount: 8;
        adjustmentAmount: 9;
        adjustmentNonabsorbedAmount: 10;
        landedCostAdjustmentAmount: 11;
        landedCostAdjustmentNonabsorbedAmount: 12;
        landedCostStockInTransitAmount: 13;
        landedCostStockInTransitAdjustmentAmount: 14;
        landedCostStockInTransitAdjustmentNonabsorbedAmount: 15;
        inTransitAmount: 16;
        inTransitVarianceAmount: 17;
    }
    export type AmountType = keyof AmountType$Enum;
    export interface AnalyticalMeasureType$Enum {
        attribute: 0;
        dimension: 1;
    }
    export type AnalyticalMeasureType = keyof AnalyticalMeasureType$Enum;
    export interface AttributeDimensionTypeLevel$Enum {
        company: 0;
        account: 1;
    }
    export type AttributeDimensionTypeLevel = keyof AttributeDimensionTypeLevel$Enum;
    export interface BankAccountType$Enum {
        current: 0;
    }
    export type BankAccountType = keyof BankAccountType$Enum;
    export interface CommonReference$Enum {
        documentNumber: 0;
        sourceDocumentNumber: 1;
    }
    export type CommonReference = keyof CommonReference$Enum;
    export interface DimensionDefinitionLevel$Enum {
        manufacturingDirect: 0;
        salesDirect: 1;
        stockDirect: 2;
        purchasingDirect: 3;
        manufacturingOrderToOrder: 4;
        purchasingOrderToOrder: 5;
        intersiteTransferOrder: 6;
    }
    export type DimensionDefinitionLevel = keyof DimensionDefinitionLevel$Enum;
    export interface DocProperty$Enum {
        dimensionType01: 1;
        dimensionType02: 2;
        dimensionType03: 3;
        dimensionType04: 4;
        dimensionType05: 5;
        dimensionType06: 6;
        dimensionType07: 7;
        dimensionType08: 8;
        dimensionType09: 9;
        dimensionType10: 10;
        dimensionType11: 11;
        dimensionType12: 12;
        dimensionType13: 13;
        dimensionType14: 14;
        dimensionType15: 15;
        dimensionType16: 16;
        dimensionType17: 17;
        dimensionType18: 18;
        dimensionType19: 19;
        dimensionType20: 20;
    }
    export type DocProperty = keyof DocProperty$Enum;
    export interface FinanceDocumentType$Enum {
        miscellaneousStockReceipt: 0;
        miscellaneousStockIssue: 1;
        purchaseReceipt: 2;
        purchaseInvoice: 3;
        purchaseCreditMemo: 4;
        salesInvoice: 5;
        salesCreditMemo: 6;
        stockAdjustment: 7;
        salesShipment: 8;
        workInProgress: 9;
        apInvoice: 10;
        arInvoice: 11;
        purchaseReturn: 12;
        salesReturnReceipt: 13;
        bankReconciliationWithdrawal: 14;
        bankReconciliationDeposit: 15;
        stockCount: 16;
        stockValueChange: 17;
        stockTransferShipment: 18;
        stockTransferReceipt: 19;
    }
    export type FinanceDocumentType = keyof FinanceDocumentType$Enum;
    export interface FinanceIntegrationApp$Enum {
        intacct: 0;
        frp1000: 1;
    }
    export type FinanceIntegrationApp = keyof FinanceIntegrationApp$Enum;
    export interface FinanceIntegrationStatus$Enum {
        toBeRecorded: 0;
        recording: 1;
        pending: 2;
        error: 3;
        recorded: 4;
        notRecorded: 5;
        submitted: 6;
        posted: 7;
        failed: 8;
    }
    export type FinanceIntegrationStatus = keyof FinanceIntegrationStatus$Enum;
    export interface FinanceItemType$Enum {
        stockItem: 0;
        nonStockItem: 1;
        serviceItem: 2;
        landedCostItem: 3;
    }
    export type FinanceItemType = keyof FinanceItemType$Enum;
    export interface HeaderDescription$Enum {
        documentType: 0;
        documentNumber: 1;
        transactionDescription: 2;
    }
    export type HeaderDescription = keyof HeaderDescription$Enum;
    export interface HeaderPostingDate$Enum {
        documentDate: 0;
        endOfMonth: 1;
    }
    export type HeaderPostingDate = keyof HeaderPostingDate$Enum;
    export interface JournalOrigin$Enum {
        directEntry: 0;
        purchase: 1;
        sales: 2;
        stock: 3;
        manufacturing: 4;
        apInvoice: 5;
        arInvoice: 6;
    }
    export type JournalOrigin = keyof JournalOrigin$Enum;
    export interface JournalStatus$Enum {
        draft: 0;
        posted: 1;
        inProgress: 2;
        error: 3;
    }
    export type JournalStatus = keyof JournalStatus$Enum;
    export interface MasterDataDefault$Enum {
        site: 0;
        sourceDocument: 1;
        customer: 2;
        supplier: 3;
        item: 4;
        shippingSite: 5;
        receivingSite: 6;
    }
    export type MasterDataDefault = keyof MasterDataDefault$Enum;
    export interface MovementType$Enum {
        stockJournal: 0;
        document: 1;
        productionTracking: 2;
        materialTracking: 3;
        laborSetupTimeTracking: 4;
        laborRunTimeTracking: 5;
        machineSetupTimeTracking: 6;
        machineRunTimeTracking: 7;
        toolSetupTimeTracking: 8;
        toolRunTimeTracking: 9;
        workOrderVariance: 10;
        workOrderNegativeVariance: 11;
        workOrderActualCostAdjustment: 12;
        workOrderNegativeActualCostAdjustment: 13;
        workOrderActualCostAdjustmentNonAbsorbed: 14;
        workOrderNegativeActualCostAdjustmentNonAbsorbed: 15;
    }
    export type MovementType = keyof MovementType$Enum;
    export interface NodeLink$Enum {
        site: 1;
        customer: 2;
        supplier: 3;
        item: 4;
        attribute: 5;
    }
    export type NodeLink = keyof NodeLink$Enum;
    export interface OpenItemStatus$Enum {
        notPaid: 0;
        partiallyPaid: 1;
        paid: 2;
    }
    export type OpenItemStatus = keyof OpenItemStatus$Enum;
    export interface PostingClassType$Enum {
        item: 0;
        supplier: 1;
        customer: 2;
        tax: 3;
        company: 4;
        header: 5;
        line: 6;
        resource: 7;
    }
    export type PostingClassType = keyof PostingClassType$Enum;
    export interface PostingStatus$Enum {
        notPosted: 0;
        generationInProgress: 1;
        generationError: 2;
        generated: 3;
        postingInProgress: 4;
        postingError: 5;
        posted: 6;
        toBeGenerated: 7;
    }
    export type PostingStatus = keyof PostingStatus$Enum;
    export interface Sign$Enum {
        D: 0;
        C: 1;
    }
    export type Sign = keyof Sign$Enum;
    export interface SourceDocumentType$Enum {
        materialTracking: 0;
        operationTracking: 1;
        productionTracking: 2;
        workOrderClose: 3;
        purchaseOrder: 4;
        purchaseReceipt: 5;
        purchaseReturn: 6;
        purchaseInvoice: 7;
        purchaseCreditMemo: 8;
        salesCreditMemo: 9;
        salesInvoice: 10;
        salesReturnRequest: 11;
        salesShipment: 12;
        stockTransferShipment: 13;
        stockTransferOrder: 14;
        stockTransferReceipt: 15;
    }
    export type SourceDocumentType = keyof SourceDocumentType$Enum;
    export interface TargetDocumentType$Enum {
        journalEntry: 0;
        accountsReceivableInvoice: 1;
        accountsPayableInvoice: 2;
        accountsReceivableAdvance: 3;
        accountsReceivablePayment: 4;
    }
    export type TargetDocumentType = keyof TargetDocumentType$Enum;
    export interface TaxEngine$Enum {
        avalaraAvaTax: 0;
        genericTaxCalculation: 1;
    }
    export type TaxEngine = keyof TaxEngine$Enum;
    export interface TaxManagement$Enum {
        other: 0;
        includingTax: 1;
        excludingTax: 2;
        tax: 3;
        reverseCharge: 4;
    }
    export type TaxManagement = keyof TaxManagement$Enum;
    export interface Account extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        chartOfAccount: ChartOfAccount;
        isActive: boolean;
        name: string;
        composedDescription: string;
        isDirectEntryForbidden: boolean;
        isControl: boolean;
        attributeTypes: ClientCollection<AccountAttributeType>;
        dimensionTypes: ClientCollection<AccountDimensionType>;
        taxManagement: TaxManagement;
        isAutomaticAccount: boolean;
        tax: Tax;
        datevId: integer;
    }
    export interface AccountInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        chartOfAccount?: integer | string;
        isActive?: boolean | string;
        name?: string;
        isDirectEntryForbidden?: boolean | string;
        isControl?: boolean | string;
        attributeTypes?: Partial<AccountAttributeTypeInput>[];
        dimensionTypes?: Partial<AccountDimensionTypeInput>[];
        taxManagement?: TaxManagement;
        isAutomaticAccount?: boolean | string;
        tax?: integer | string;
        datevId?: integer | string;
    }
    export interface AccountBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        chartOfAccount: ChartOfAccount;
        isActive: boolean;
        name: string;
        composedDescription: string;
        isDirectEntryForbidden: boolean;
        isControl: boolean;
        attributeTypes: ClientCollection<AccountAttributeTypeBinding>;
        dimensionTypes: ClientCollection<AccountDimensionTypeBinding>;
        taxManagement: TaxManagement;
        isAutomaticAccount: boolean;
        tax: Tax;
        datevId: integer;
    }
    export interface Account$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Account$Lookups {
        _vendor: QueryOperation<SysVendor>;
        chartOfAccount: QueryOperation<ChartOfAccount>;
        tax: QueryOperation<Tax>;
    }
    export interface Account$Operations {
        query: QueryOperation<Account>;
        read: ReadOperation<Account>;
        aggregate: {
            read: AggregateReadOperation<Account>;
            query: AggregateQueryOperation<Account>;
        };
        create: CreateOperation<AccountInput, Account>;
        getDuplicate: GetDuplicateOperation<Account>;
        duplicate: DuplicateOperation<string, AccountInput, Account>;
        update: UpdateOperation<AccountInput, Account>;
        updateById: UpdateByIdOperation<AccountInput, Account>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Account$AsyncOperations;
        lookups(dataOrId: string | { data: AccountInput }): Account$Lookups;
        getDefaults: GetDefaultsOperation<Account>;
    }
    export interface AccountAttributeType extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        account: Account;
        attributeType: AttributeType;
        isRequired: boolean;
        analyticalMeasureType: AnalyticalMeasureType;
    }
    export interface AccountAttributeTypeInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        attributeType?: integer | string;
        isRequired?: boolean | string;
    }
    export interface AccountAttributeTypeBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        account: Account;
        attributeType: AttributeType;
        isRequired: boolean;
        analyticalMeasureType: AnalyticalMeasureType;
    }
    export interface AccountAttributeType$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountAttributeType$Lookups {
        _vendor: QueryOperation<SysVendor>;
        attributeType: QueryOperation<AttributeType>;
    }
    export interface AccountAttributeType$Operations {
        query: QueryOperation<AccountAttributeType>;
        read: ReadOperation<AccountAttributeType>;
        aggregate: {
            read: AggregateReadOperation<AccountAttributeType>;
            query: AggregateQueryOperation<AccountAttributeType>;
        };
        asyncOperations: AccountAttributeType$AsyncOperations;
        lookups(dataOrId: string | { data: AccountAttributeTypeInput }): AccountAttributeType$Lookups;
        getDefaults: GetDefaultsOperation<AccountAttributeType>;
    }
    export interface AccountDimensionType extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        account: Account;
        dimensionType: DimensionType;
        isRequired: boolean;
        analyticalMeasureType: AnalyticalMeasureType;
    }
    export interface AccountDimensionTypeInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        dimensionType?: integer | string;
        isRequired?: boolean | string;
    }
    export interface AccountDimensionTypeBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        account: Account;
        dimensionType: DimensionType;
        isRequired: boolean;
        analyticalMeasureType: AnalyticalMeasureType;
    }
    export interface AccountDimensionType$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountDimensionType$Lookups {
        _vendor: QueryOperation<SysVendor>;
        dimensionType: QueryOperation<DimensionType>;
    }
    export interface AccountDimensionType$Operations {
        query: QueryOperation<AccountDimensionType>;
        read: ReadOperation<AccountDimensionType>;
        aggregate: {
            read: AggregateReadOperation<AccountDimensionType>;
            query: AggregateQueryOperation<AccountDimensionType>;
        };
        asyncOperations: AccountDimensionType$AsyncOperations;
        lookups(dataOrId: string | { data: AccountDimensionTypeInput }): AccountDimensionType$Lookups;
        getDefaults: GetDefaultsOperation<AccountDimensionType>;
    }
    export interface AccountingStaging extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        batchId: string;
        batchSize: integer;
        baseDocumentLine: BaseDocumentLine;
        sourceBaseDocumentLine: BaseDocumentLine;
        stockJournal: StockJournal;
        documentSysId: integer;
        documentNumber: string;
        description: string;
        sourceDocumentNumber: string;
        documentDate: string;
        taxDate: string;
        documentType: FinanceDocumentType;
        sourceDocumentType: SourceDocumentType;
        movementType: MovementType;
        targetDocumentType: TargetDocumentType;
        financialSite: Site;
        recipientSite: Site;
        providerSite: Site;
        item: Item;
        account: Account;
        customer: Customer;
        supplier: Supplier;
        payToSupplier: Supplier;
        payToSupplierLinkedAddress: BusinessEntityAddress;
        returnLinkedAddress: BusinessEntityAddress;
        itemPostingClass: PostingClass;
        customerPostingClass: PostingClass;
        supplierPostingClass: PostingClass;
        resourcePostingClass: PostingClass;
        resource: DetailedResource;
        amounts: ClientCollection<AccountingStagingAmount>;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        paymentTerm: PaymentTerm;
        dueDate: string;
        supplierDocumentDate: string;
        supplierDocumentNumber: string;
        isPrinted: boolean;
        taxCalculationStatus: TaxCalculationStatus;
        fxRateDate: string;
        storedDimensions: string;
        storedAttributes: string;
        originNotificationId: string;
        storedComputedAttributes: string;
        isProcessed: boolean;
        toBeReprocessed: boolean;
        replyTopic: string;
        taxes: ClientCollection<AccountingStagingLineTax>;
        analyticalData: AnalyticalData;
    }
    export interface AccountingStagingInput extends ClientNodeInput {
        batchId?: string;
        batchSize?: integer | string;
        baseDocumentLine?: integer | string;
        sourceBaseDocumentLine?: integer | string;
        stockJournal?: integer | string;
        documentSysId?: integer | string;
        documentNumber?: string;
        description?: string;
        sourceDocumentNumber?: string;
        documentDate?: string;
        taxDate?: string;
        documentType?: FinanceDocumentType;
        sourceDocumentType?: SourceDocumentType;
        movementType?: MovementType;
        targetDocumentType?: TargetDocumentType;
        financialSite?: integer | string;
        recipientSite?: integer | string;
        providerSite?: integer | string;
        item?: integer | string;
        account?: integer | string;
        customer?: integer | string;
        supplier?: integer | string;
        payToSupplier?: integer | string;
        payToSupplierLinkedAddress?: integer | string;
        returnLinkedAddress?: integer | string;
        itemPostingClass?: integer | string;
        customerPostingClass?: integer | string;
        supplierPostingClass?: integer | string;
        resourcePostingClass?: integer | string;
        resource?: integer | string;
        amounts?: Partial<AccountingStagingAmountInput>[];
        transactionCurrency?: integer | string;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        paymentTerm?: integer | string;
        dueDate?: string;
        supplierDocumentDate?: string;
        supplierDocumentNumber?: string;
        isPrinted?: boolean | string;
        taxCalculationStatus?: TaxCalculationStatus;
        fxRateDate?: string;
        storedDimensions?: string;
        storedAttributes?: string;
        originNotificationId?: string;
        storedComputedAttributes?: string;
        isProcessed?: boolean | string;
        toBeReprocessed?: boolean | string;
        replyTopic?: string;
        taxes?: Partial<AccountingStagingLineTaxInput>[];
        analyticalData?: integer | string;
    }
    export interface AccountingStagingBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        batchId: string;
        batchSize: integer;
        baseDocumentLine: BaseDocumentLine;
        sourceBaseDocumentLine: BaseDocumentLine;
        stockJournal: StockJournal;
        documentSysId: integer;
        documentNumber: string;
        description: string;
        sourceDocumentNumber: string;
        documentDate: string;
        taxDate: string;
        documentType: FinanceDocumentType;
        sourceDocumentType: SourceDocumentType;
        movementType: MovementType;
        targetDocumentType: TargetDocumentType;
        financialSite: Site;
        recipientSite: Site;
        providerSite: Site;
        item: Item;
        account: Account;
        customer: Customer;
        supplier: Supplier;
        payToSupplier: Supplier;
        payToSupplierLinkedAddress: BusinessEntityAddress;
        returnLinkedAddress: BusinessEntityAddress;
        itemPostingClass: PostingClass;
        customerPostingClass: PostingClass;
        supplierPostingClass: PostingClass;
        resourcePostingClass: PostingClass;
        resource: DetailedResource;
        amounts: ClientCollection<AccountingStagingAmountBinding>;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        paymentTerm: PaymentTerm;
        dueDate: string;
        supplierDocumentDate: string;
        supplierDocumentNumber: string;
        isPrinted: boolean;
        taxCalculationStatus: TaxCalculationStatus;
        fxRateDate: string;
        storedDimensions: any;
        storedAttributes: any;
        originNotificationId: string;
        storedComputedAttributes: any;
        isProcessed: boolean;
        toBeReprocessed: boolean;
        replyTopic: string;
        taxes: ClientCollection<AccountingStagingLineTaxBinding>;
        analyticalData: AnalyticalData;
    }
    export interface AccountingStaging$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountingStaging$Lookups {
        baseDocumentLine: QueryOperation<BaseDocumentLine>;
        sourceBaseDocumentLine: QueryOperation<BaseDocumentLine>;
        stockJournal: QueryOperation<StockJournal>;
        financialSite: QueryOperation<Site>;
        recipientSite: QueryOperation<Site>;
        providerSite: QueryOperation<Site>;
        item: QueryOperation<Item>;
        account: QueryOperation<Account>;
        customer: QueryOperation<Customer>;
        supplier: QueryOperation<Supplier>;
        payToSupplier: QueryOperation<Supplier>;
        payToSupplierLinkedAddress: QueryOperation<BusinessEntityAddress>;
        returnLinkedAddress: QueryOperation<BusinessEntityAddress>;
        itemPostingClass: QueryOperation<PostingClass>;
        customerPostingClass: QueryOperation<PostingClass>;
        supplierPostingClass: QueryOperation<PostingClass>;
        resourcePostingClass: QueryOperation<PostingClass>;
        resource: QueryOperation<DetailedResource>;
        transactionCurrency: QueryOperation<Currency>;
        paymentTerm: QueryOperation<PaymentTerm>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface AccountingStaging$Operations {
        query: QueryOperation<AccountingStaging>;
        read: ReadOperation<AccountingStaging>;
        aggregate: {
            read: AggregateReadOperation<AccountingStaging>;
            query: AggregateQueryOperation<AccountingStaging>;
        };
        create: CreateOperation<AccountingStagingInput, AccountingStaging>;
        getDuplicate: GetDuplicateOperation<AccountingStaging>;
        update: UpdateOperation<AccountingStagingInput, AccountingStaging>;
        updateById: UpdateByIdOperation<AccountingStagingInput, AccountingStaging>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: AccountingStaging$AsyncOperations;
        lookups(dataOrId: string | { data: AccountingStagingInput }): AccountingStaging$Lookups;
        getDefaults: GetDefaultsOperation<AccountingStaging>;
    }
    export interface AccountingStagingAmount extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        accountingStaging: AccountingStaging;
        amountType: AmountType;
        amount: string;
        tax: Tax;
        taxPostingClass: PostingClass;
        baseTax: BaseTax;
        documentLineType: AccountsPayableReceivableInvoiceDocumentLineType;
        taxDate: string;
        taxRate: string;
    }
    export interface AccountingStagingAmountInput extends VitalClientNodeInput {
        amountType?: AmountType;
        amount?: decimal | string;
        tax?: integer | string;
        taxPostingClass?: integer | string;
        baseTax?: integer | string;
        documentLineType?: AccountsPayableReceivableInvoiceDocumentLineType;
        taxDate?: string;
        taxRate?: decimal | string;
    }
    export interface AccountingStagingAmountBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        accountingStaging: AccountingStaging;
        amountType: AmountType;
        amount: string;
        tax: Tax;
        taxPostingClass: PostingClass;
        baseTax: BaseTax;
        documentLineType: AccountsPayableReceivableInvoiceDocumentLineType;
        taxDate: string;
        taxRate: string;
    }
    export interface AccountingStagingAmount$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountingStagingAmount$Lookups {
        tax: QueryOperation<Tax>;
        taxPostingClass: QueryOperation<PostingClass>;
        baseTax: QueryOperation<BaseTax>;
    }
    export interface AccountingStagingAmount$Operations {
        query: QueryOperation<AccountingStagingAmount>;
        read: ReadOperation<AccountingStagingAmount>;
        aggregate: {
            read: AggregateReadOperation<AccountingStagingAmount>;
            query: AggregateQueryOperation<AccountingStagingAmount>;
        };
        asyncOperations: AccountingStagingAmount$AsyncOperations;
        lookups(dataOrId: string | { data: AccountingStagingAmountInput }): AccountingStagingAmount$Lookups;
        getDefaults: GetDefaultsOperation<AccountingStagingAmount>;
    }
    export interface AccountingStagingDocumentTax extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        batchId: string;
        documentType: FinanceDocumentType;
        documentNumber: string;
        targetDocumentType: TargetDocumentType;
        baseTax: BaseTax;
    }
    export interface AccountingStagingDocumentTaxInput extends ClientNodeInput {
        batchId?: string;
        documentType?: FinanceDocumentType;
        documentNumber?: string;
        targetDocumentType?: TargetDocumentType;
        baseTax?: integer | string;
    }
    export interface AccountingStagingDocumentTaxBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        batchId: string;
        documentType: FinanceDocumentType;
        documentNumber: string;
        targetDocumentType: TargetDocumentType;
        baseTax: BaseTax;
    }
    export interface AccountingStagingDocumentTax$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountingStagingDocumentTax$Lookups {
        baseTax: QueryOperation<BaseTax>;
    }
    export interface AccountingStagingDocumentTax$Operations {
        query: QueryOperation<AccountingStagingDocumentTax>;
        read: ReadOperation<AccountingStagingDocumentTax>;
        aggregate: {
            read: AggregateReadOperation<AccountingStagingDocumentTax>;
            query: AggregateQueryOperation<AccountingStagingDocumentTax>;
        };
        create: CreateOperation<AccountingStagingDocumentTaxInput, AccountingStagingDocumentTax>;
        getDuplicate: GetDuplicateOperation<AccountingStagingDocumentTax>;
        update: UpdateOperation<AccountingStagingDocumentTaxInput, AccountingStagingDocumentTax>;
        updateById: UpdateByIdOperation<AccountingStagingDocumentTaxInput, AccountingStagingDocumentTax>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: AccountingStagingDocumentTax$AsyncOperations;
        lookups(dataOrId: string | { data: AccountingStagingDocumentTaxInput }): AccountingStagingDocumentTax$Lookups;
        getDefaults: GetDefaultsOperation<AccountingStagingDocumentTax>;
    }
    export interface AccountingStagingLineTax extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        accountingStaging: AccountingStaging;
        baseTax: BaseTax;
    }
    export interface AccountingStagingLineTaxInput extends VitalClientNodeInput {
        baseTax?: integer | string;
    }
    export interface AccountingStagingLineTaxBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        accountingStaging: AccountingStaging;
        baseTax: BaseTax;
    }
    export interface AccountingStagingLineTax$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountingStagingLineTax$Lookups {
        baseTax: QueryOperation<BaseTax>;
    }
    export interface AccountingStagingLineTax$Operations {
        query: QueryOperation<AccountingStagingLineTax>;
        read: ReadOperation<AccountingStagingLineTax>;
        aggregate: {
            read: AggregateReadOperation<AccountingStagingLineTax>;
            query: AggregateQueryOperation<AccountingStagingLineTax>;
        };
        asyncOperations: AccountingStagingLineTax$AsyncOperations;
        lookups(dataOrId: string | { data: AccountingStagingLineTaxInput }): AccountingStagingLineTax$Lookups;
        getDefaults: GetDefaultsOperation<AccountingStagingLineTax>;
    }
    export interface AccountsPayableInvoiceLineStaging extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        accountsPayableInvoiceLine: BaseDocumentLine;
        accountingStaging: AccountingStaging;
    }
    export interface AccountsPayableInvoiceLineStagingInput extends VitalClientNodeInput {
        accountingStaging?: integer | string;
    }
    export interface AccountsPayableInvoiceLineStagingBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        accountsPayableInvoiceLine: BaseDocumentLine;
        accountingStaging: AccountingStaging;
    }
    export interface AccountsPayableInvoiceLineStaging$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsPayableInvoiceLineStaging$Lookups {
        accountingStaging: QueryOperation<AccountingStaging>;
    }
    export interface AccountsPayableInvoiceLineStaging$Operations {
        asyncOperations: AccountsPayableInvoiceLineStaging$AsyncOperations;
        lookups(
            dataOrId: string | { data: AccountsPayableInvoiceLineStagingInput },
        ): AccountsPayableInvoiceLineStaging$Lookups;
    }
    export interface AccountsReceivableInvoiceLineStaging extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        accountsReceivableInvoiceLine: BaseDocumentLine;
        accountingStaging: AccountingStaging;
    }
    export interface AccountsReceivableInvoiceLineStagingInput extends VitalClientNodeInput {
        accountingStaging?: integer | string;
    }
    export interface AccountsReceivableInvoiceLineStagingBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        accountsReceivableInvoiceLine: BaseDocumentLine;
        accountingStaging: AccountingStaging;
    }
    export interface AccountsReceivableInvoiceLineStaging$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsReceivableInvoiceLineStaging$Lookups {
        accountingStaging: QueryOperation<AccountingStaging>;
    }
    export interface AccountsReceivableInvoiceLineStaging$Operations {
        asyncOperations: AccountsReceivableInvoiceLineStaging$AsyncOperations;
        lookups(
            dataOrId: string | { data: AccountsReceivableInvoiceLineStagingInput },
        ): AccountsReceivableInvoiceLineStaging$Lookups;
    }
    export interface AnalyticalData extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        financialSiteType: AttributeType;
        financialSite: Site;
        businessSiteType: AttributeType;
        businessSite: Site;
        stockSiteType: AttributeType;
        stockSite: Site;
        manufacturingSiteType: AttributeType;
        manufacturingSite: Site;
        supplierType: AttributeType;
        supplier: Supplier;
        customerType: AttributeType;
        customer: Customer;
        projectType: AttributeType;
        project: Attribute;
        taskType: AttributeType;
        task: Attribute;
        employeeType: AttributeType;
        employee: Attribute;
        itemType: AttributeType;
        item: Item;
        dimensionType01: DimensionType;
        dimension01: Dimension;
        dimensionType02: DimensionType;
        dimension02: Dimension;
        dimensionType03: DimensionType;
        dimension03: Dimension;
        dimensionType04: DimensionType;
        dimension04: Dimension;
        dimensionType05: DimensionType;
        dimension05: Dimension;
        dimensionType06: DimensionType;
        dimension06: Dimension;
        dimensionType07: DimensionType;
        dimension07: Dimension;
        dimensionType08: DimensionType;
        dimension08: Dimension;
        dimensionType09: DimensionType;
        dimension09: Dimension;
        dimensionType10: DimensionType;
        dimension10: Dimension;
        dimensionType11: DimensionType;
        dimension11: Dimension;
        dimensionType12: DimensionType;
        dimension12: Dimension;
        dimensionType13: DimensionType;
        dimension13: Dimension;
        dimensionType14: DimensionType;
        dimension14: Dimension;
        dimensionType15: DimensionType;
        dimension15: Dimension;
        dimensionType16: DimensionType;
        dimension16: Dimension;
        dimensionType17: DimensionType;
        dimension17: Dimension;
        dimensionType18: DimensionType;
        dimension18: Dimension;
        dimensionType19: DimensionType;
        dimension19: Dimension;
        dimensionType20: DimensionType;
        dimension20: Dimension;
    }
    export interface AnalyticalDataInput extends ClientNodeInput {
        financialSite?: integer | string;
        businessSite?: integer | string;
        stockSite?: integer | string;
        manufacturingSite?: integer | string;
        supplier?: integer | string;
        customer?: integer | string;
        project?: integer | string;
        task?: integer | string;
        employee?: integer | string;
        item?: integer | string;
        dimension01?: integer | string;
        dimension02?: integer | string;
        dimension03?: integer | string;
        dimension04?: integer | string;
        dimension05?: integer | string;
        dimension06?: integer | string;
        dimension07?: integer | string;
        dimension08?: integer | string;
        dimension09?: integer | string;
        dimension10?: integer | string;
        dimension11?: integer | string;
        dimension12?: integer | string;
        dimension13?: integer | string;
        dimension14?: integer | string;
        dimension15?: integer | string;
        dimension16?: integer | string;
        dimension17?: integer | string;
        dimension18?: integer | string;
        dimension19?: integer | string;
        dimension20?: integer | string;
    }
    export interface AnalyticalDataBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        financialSiteType: AttributeType;
        financialSite: Site;
        businessSiteType: AttributeType;
        businessSite: Site;
        stockSiteType: AttributeType;
        stockSite: Site;
        manufacturingSiteType: AttributeType;
        manufacturingSite: Site;
        supplierType: AttributeType;
        supplier: Supplier;
        customerType: AttributeType;
        customer: Customer;
        projectType: AttributeType;
        project: Attribute;
        taskType: AttributeType;
        task: Attribute;
        employeeType: AttributeType;
        employee: Attribute;
        itemType: AttributeType;
        item: Item;
        dimensionType01: DimensionType;
        dimension01: Dimension;
        dimensionType02: DimensionType;
        dimension02: Dimension;
        dimensionType03: DimensionType;
        dimension03: Dimension;
        dimensionType04: DimensionType;
        dimension04: Dimension;
        dimensionType05: DimensionType;
        dimension05: Dimension;
        dimensionType06: DimensionType;
        dimension06: Dimension;
        dimensionType07: DimensionType;
        dimension07: Dimension;
        dimensionType08: DimensionType;
        dimension08: Dimension;
        dimensionType09: DimensionType;
        dimension09: Dimension;
        dimensionType10: DimensionType;
        dimension10: Dimension;
        dimensionType11: DimensionType;
        dimension11: Dimension;
        dimensionType12: DimensionType;
        dimension12: Dimension;
        dimensionType13: DimensionType;
        dimension13: Dimension;
        dimensionType14: DimensionType;
        dimension14: Dimension;
        dimensionType15: DimensionType;
        dimension15: Dimension;
        dimensionType16: DimensionType;
        dimension16: Dimension;
        dimensionType17: DimensionType;
        dimension17: Dimension;
        dimensionType18: DimensionType;
        dimension18: Dimension;
        dimensionType19: DimensionType;
        dimension19: Dimension;
        dimensionType20: DimensionType;
        dimension20: Dimension;
    }
    export interface AnalyticalData$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AnalyticalData$Lookups {
        financialSiteType: QueryOperation<AttributeType>;
        financialSite: QueryOperation<Site>;
        businessSiteType: QueryOperation<AttributeType>;
        businessSite: QueryOperation<Site>;
        stockSiteType: QueryOperation<AttributeType>;
        stockSite: QueryOperation<Site>;
        manufacturingSiteType: QueryOperation<AttributeType>;
        manufacturingSite: QueryOperation<Site>;
        supplierType: QueryOperation<AttributeType>;
        supplier: QueryOperation<Supplier>;
        customerType: QueryOperation<AttributeType>;
        customer: QueryOperation<Customer>;
        projectType: QueryOperation<AttributeType>;
        project: QueryOperation<Attribute>;
        taskType: QueryOperation<AttributeType>;
        task: QueryOperation<Attribute>;
        employeeType: QueryOperation<AttributeType>;
        employee: QueryOperation<Attribute>;
        itemType: QueryOperation<AttributeType>;
        item: QueryOperation<Item>;
        dimensionType01: QueryOperation<DimensionType>;
        dimension01: QueryOperation<Dimension>;
        dimensionType02: QueryOperation<DimensionType>;
        dimension02: QueryOperation<Dimension>;
        dimensionType03: QueryOperation<DimensionType>;
        dimension03: QueryOperation<Dimension>;
        dimensionType04: QueryOperation<DimensionType>;
        dimension04: QueryOperation<Dimension>;
        dimensionType05: QueryOperation<DimensionType>;
        dimension05: QueryOperation<Dimension>;
        dimensionType06: QueryOperation<DimensionType>;
        dimension06: QueryOperation<Dimension>;
        dimensionType07: QueryOperation<DimensionType>;
        dimension07: QueryOperation<Dimension>;
        dimensionType08: QueryOperation<DimensionType>;
        dimension08: QueryOperation<Dimension>;
        dimensionType09: QueryOperation<DimensionType>;
        dimension09: QueryOperation<Dimension>;
        dimensionType10: QueryOperation<DimensionType>;
        dimension10: QueryOperation<Dimension>;
        dimensionType11: QueryOperation<DimensionType>;
        dimension11: QueryOperation<Dimension>;
        dimensionType12: QueryOperation<DimensionType>;
        dimension12: QueryOperation<Dimension>;
        dimensionType13: QueryOperation<DimensionType>;
        dimension13: QueryOperation<Dimension>;
        dimensionType14: QueryOperation<DimensionType>;
        dimension14: QueryOperation<Dimension>;
        dimensionType15: QueryOperation<DimensionType>;
        dimension15: QueryOperation<Dimension>;
        dimensionType16: QueryOperation<DimensionType>;
        dimension16: QueryOperation<Dimension>;
        dimensionType17: QueryOperation<DimensionType>;
        dimension17: QueryOperation<Dimension>;
        dimensionType18: QueryOperation<DimensionType>;
        dimension18: QueryOperation<Dimension>;
        dimensionType19: QueryOperation<DimensionType>;
        dimension19: QueryOperation<Dimension>;
        dimensionType20: QueryOperation<DimensionType>;
        dimension20: QueryOperation<Dimension>;
    }
    export interface AnalyticalData$Operations {
        query: QueryOperation<AnalyticalData>;
        read: ReadOperation<AnalyticalData>;
        aggregate: {
            read: AggregateReadOperation<AnalyticalData>;
            query: AggregateQueryOperation<AnalyticalData>;
        };
        create: CreateOperation<AnalyticalDataInput, AnalyticalData>;
        getDuplicate: GetDuplicateOperation<AnalyticalData>;
        update: UpdateOperation<AnalyticalDataInput, AnalyticalData>;
        updateById: UpdateByIdOperation<AnalyticalDataInput, AnalyticalData>;
        asyncOperations: AnalyticalData$AsyncOperations;
        lookups(dataOrId: string | { data: AnalyticalDataInput }): AnalyticalData$Lookups;
        getDefaults: GetDefaultsOperation<AnalyticalData>;
    }
    export interface Attribute extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        composedDescription: string;
        attributeType: AttributeType;
        attributeRestrictedTo: Attribute;
        attributeRestrictedToId: string;
        site: Site;
        item: Item;
    }
    export interface AttributeInput extends ClientNodeInput {
        isActive?: boolean | string;
        id?: string;
        name?: string;
        attributeType?: integer | string;
        attributeRestrictedTo?: integer | string;
        attributeRestrictedToId?: string;
        site?: integer | string;
        item?: integer | string;
    }
    export interface AttributeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        composedDescription: string;
        attributeType: AttributeType;
        attributeRestrictedTo: Attribute;
        attributeRestrictedToId: string;
        site: Site;
        item: Item;
    }
    export interface Attribute$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Attribute$Lookups {
        attributeType: QueryOperation<AttributeType>;
        attributeRestrictedTo: QueryOperation<Attribute>;
        site: QueryOperation<Site>;
        item: QueryOperation<Item>;
    }
    export interface Attribute$Operations {
        query: QueryOperation<Attribute>;
        read: ReadOperation<Attribute>;
        aggregate: {
            read: AggregateReadOperation<Attribute>;
            query: AggregateQueryOperation<Attribute>;
        };
        create: CreateOperation<AttributeInput, Attribute>;
        getDuplicate: GetDuplicateOperation<Attribute>;
        duplicate: DuplicateOperation<string, AttributeInput, Attribute>;
        update: UpdateOperation<AttributeInput, Attribute>;
        updateById: UpdateByIdOperation<AttributeInput, Attribute>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Attribute$AsyncOperations;
        lookups(dataOrId: string | { data: AttributeInput }): Attribute$Lookups;
        getDefaults: GetDefaultsOperation<Attribute>;
    }
    export interface AttributeType extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        name: string;
        id: string;
        nodeLink: NodeLink;
        queryFilter: string;
        analyticalMeasureType: AnalyticalMeasureType;
        attributeTypeRestrictedTo: AttributeType;
        isLinkedToSite: boolean;
        isLinkedToItem: boolean;
        linkedTo: NodeLink[];
    }
    export interface AttributeTypeInput extends ClientNodeInput {
        _vendor?: integer | string;
        isActive?: boolean | string;
        name?: string;
        id?: string;
        nodeLink?: NodeLink;
        queryFilter?: string;
        attributeTypeRestrictedTo?: integer | string;
        isLinkedToSite?: boolean | string;
        isLinkedToItem?: boolean | string;
    }
    export interface AttributeTypeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        name: string;
        id: string;
        nodeLink: NodeLink;
        queryFilter: any;
        analyticalMeasureType: AnalyticalMeasureType;
        attributeTypeRestrictedTo: AttributeType;
        isLinkedToSite: boolean;
        isLinkedToItem: boolean;
        linkedTo: NodeLink[];
    }
    export interface AttributeType$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AttributeType$Lookups {
        _vendor: QueryOperation<SysVendor>;
        attributeTypeRestrictedTo: QueryOperation<AttributeType>;
    }
    export interface AttributeType$Operations {
        query: QueryOperation<AttributeType>;
        read: ReadOperation<AttributeType>;
        aggregate: {
            read: AggregateReadOperation<AttributeType>;
            query: AggregateQueryOperation<AttributeType>;
        };
        update: UpdateOperation<AttributeTypeInput, AttributeType>;
        updateById: UpdateByIdOperation<AttributeTypeInput, AttributeType>;
        asyncOperations: AttributeType$AsyncOperations;
        lookups(dataOrId: string | { data: AttributeTypeInput }): AttributeType$Lookups;
        getDefaults: GetDefaultsOperation<AttributeType>;
    }
    export interface BankAccount extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        name: string;
        id: string;
        financialSite: Site;
        currency: Currency;
        bankAccountType: BankAccountType;
    }
    export interface BankAccountInput extends ClientNodeInput {
        isActive?: boolean | string;
        name?: string;
        id?: string;
        financialSite?: integer | string;
        currency?: integer | string;
        bankAccountType?: BankAccountType;
    }
    export interface BankAccountBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        name: string;
        id: string;
        financialSite: Site;
        currency: Currency;
        bankAccountType: BankAccountType;
    }
    export interface BankAccount$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface BankAccount$Lookups {
        financialSite: QueryOperation<Site>;
        currency: QueryOperation<Currency>;
    }
    export interface BankAccount$Operations {
        query: QueryOperation<BankAccount>;
        read: ReadOperation<BankAccount>;
        aggregate: {
            read: AggregateReadOperation<BankAccount>;
            query: AggregateQueryOperation<BankAccount>;
        };
        create: CreateOperation<BankAccountInput, BankAccount>;
        getDuplicate: GetDuplicateOperation<BankAccount>;
        duplicate: DuplicateOperation<string, BankAccountInput, BankAccount>;
        update: UpdateOperation<BankAccountInput, BankAccount>;
        updateById: UpdateByIdOperation<BankAccountInput, BankAccount>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: BankAccount$AsyncOperations;
        lookups(dataOrId: string | { data: BankAccountInput }): BankAccount$Lookups;
        getDefaults: GetDefaultsOperation<BankAccount>;
    }
    export interface BaseFinanceDocument extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        anyLines: ClientCollection<BaseFinanceLine>;
    }
    export interface BaseFinanceDocumentInput extends ClientNodeInput {
        _constructor?: string;
        number?: string;
        lines?: Partial<BaseFinanceLineInput>[];
    }
    export interface BaseFinanceDocumentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        lines: ClientCollection<BaseFinanceLineBinding>;
    }
    export interface BaseFinanceDocument$Operations {
        query: QueryOperation<BaseFinanceDocument>;
        read: ReadOperation<BaseFinanceDocument>;
        aggregate: {
            read: AggregateReadOperation<BaseFinanceDocument>;
            query: AggregateQueryOperation<BaseFinanceDocument>;
        };
        getDefaults: GetDefaultsOperation<BaseFinanceDocument>;
    }
    export interface BaseFinanceLineDimension extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        originLine: BaseFinanceLine;
        storedAttributes: string;
        storedDimensions: string;
        analyticalData: AnalyticalData;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        financialSite: Site;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: Customer;
        supplier: Supplier;
        project: Attribute;
        task: Attribute;
        employee: Attribute;
        item: Item;
    }
    export interface BaseFinanceLineDimensionInput extends VitalClientNodeInput {
        _constructor?: string;
        storedAttributes?: string;
        storedDimensions?: string;
        analyticalData?: integer | string;
    }
    export interface BaseFinanceLineDimensionBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        originLine: BaseFinanceLine;
        storedAttributes: any;
        storedDimensions: any;
        analyticalData: AnalyticalData;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        financialSite: Site;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: Customer;
        supplier: Supplier;
        project: Attribute;
        task: Attribute;
        employee: Attribute;
        item: Item;
    }
    export interface BaseFinanceLineDimension$Lookups {
        analyticalData: QueryOperation<AnalyticalData>;
        dimension01: QueryOperation<Dimension>;
        dimension02: QueryOperation<Dimension>;
        dimension03: QueryOperation<Dimension>;
        dimension04: QueryOperation<Dimension>;
        dimension05: QueryOperation<Dimension>;
        dimension06: QueryOperation<Dimension>;
        dimension07: QueryOperation<Dimension>;
        dimension08: QueryOperation<Dimension>;
        dimension09: QueryOperation<Dimension>;
        dimension10: QueryOperation<Dimension>;
        dimension11: QueryOperation<Dimension>;
        dimension12: QueryOperation<Dimension>;
        dimension13: QueryOperation<Dimension>;
        dimension14: QueryOperation<Dimension>;
        dimension15: QueryOperation<Dimension>;
        dimension16: QueryOperation<Dimension>;
        dimension17: QueryOperation<Dimension>;
        dimension18: QueryOperation<Dimension>;
        dimension19: QueryOperation<Dimension>;
        dimension20: QueryOperation<Dimension>;
        financialSite: QueryOperation<Site>;
        businessSite: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        manufacturingSite: QueryOperation<Site>;
        customer: QueryOperation<Customer>;
        supplier: QueryOperation<Supplier>;
        project: QueryOperation<Attribute>;
        task: QueryOperation<Attribute>;
        employee: QueryOperation<Attribute>;
        item: QueryOperation<Item>;
    }
    export interface BaseFinanceLineDimension$Operations {
        query: QueryOperation<BaseFinanceLineDimension>;
        read: ReadOperation<BaseFinanceLineDimension>;
        aggregate: {
            read: AggregateReadOperation<BaseFinanceLineDimension>;
            query: AggregateQueryOperation<BaseFinanceLineDimension>;
        };
        lookups(dataOrId: string | { data: BaseFinanceLineDimensionInput }): BaseFinanceLineDimension$Lookups;
        getDefaults: GetDefaultsOperation<BaseFinanceLineDimension>;
    }
    export interface BaseOpenItem extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        dueDate: string;
        businessEntity: BusinessEntity;
        businessRelation: BaseBusinessRelation;
        businessEntityPayment: BusinessEntity;
        type: BusinessEntityType;
        currency: Currency;
        transactionAmountDue: string;
        transactionAmountDueSigned: string;
        transactionAmountPaid: string;
        transactionAmountPaidSigned: string;
        remainingTransactionAmountSigned: string;
        companyAmountDue: string;
        companyAmountDueSigned: string;
        companyAmountPaid: string;
        companyAmountPaidSigned: string;
        remainingCompanyAmountSigned: string;
        financialSiteAmountDue: string;
        financialSiteAmountPaid: string;
        status: OpenItemStatus;
        documentType: FinanceDocumentType;
        documentNumber: string;
        documentSysId: integer;
        discountFrom: DueDateType;
        discountDate: integer;
        discountType: PaymentTermDiscountOrPenaltyType;
        discountAmount: string;
        discountPaymentBeforeDate: string;
        displayDiscountPaymentDate: string;
        penaltyPaymentType: DiscountOrPenaltyType;
        penaltyAmount: string;
        closeReason: CloseReason;
        closeText: string;
        forcedAmountPaid: string;
        forcedAmountPaidSigned: string;
    }
    export interface BaseOpenItemInput extends ClientNodeInput {
        _constructor?: string;
        dueDate?: string;
        businessEntity?: integer | string;
        businessEntityPayment?: integer | string;
        type?: BusinessEntityType;
        currency?: integer | string;
        transactionAmountDue?: decimal | string;
        transactionAmountPaid?: decimal | string;
        companyAmountDue?: decimal | string;
        companyAmountPaid?: decimal | string;
        financialSiteAmountDue?: decimal | string;
        financialSiteAmountPaid?: decimal | string;
        documentType?: FinanceDocumentType;
        documentNumber?: string;
        documentSysId?: integer | string;
        discountFrom?: DueDateType;
        discountDate?: integer | string;
        discountType?: PaymentTermDiscountOrPenaltyType;
        discountAmount?: decimal | string;
        discountPaymentBeforeDate?: string;
        penaltyPaymentType?: DiscountOrPenaltyType;
        penaltyAmount?: decimal | string;
        closeReason?: integer | string;
        closeText?: string;
        forcedAmountPaid?: decimal | string;
    }
    export interface BaseOpenItemBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        dueDate: string;
        businessEntity: BusinessEntity;
        businessRelation: BaseBusinessRelation;
        businessEntityPayment: BusinessEntity;
        type: BusinessEntityType;
        currency: Currency;
        transactionAmountDue: string;
        transactionAmountDueSigned: string;
        transactionAmountPaid: string;
        transactionAmountPaidSigned: string;
        remainingTransactionAmountSigned: string;
        companyAmountDue: string;
        companyAmountDueSigned: string;
        companyAmountPaid: string;
        companyAmountPaidSigned: string;
        remainingCompanyAmountSigned: string;
        financialSiteAmountDue: string;
        financialSiteAmountPaid: string;
        status: OpenItemStatus;
        documentType: FinanceDocumentType;
        documentNumber: string;
        documentSysId: integer;
        discountFrom: DueDateType;
        discountDate: integer;
        discountType: PaymentTermDiscountOrPenaltyType;
        discountAmount: string;
        discountPaymentBeforeDate: string;
        displayDiscountPaymentDate: string;
        penaltyPaymentType: DiscountOrPenaltyType;
        penaltyAmount: string;
        closeReason: CloseReason;
        closeText: string;
        forcedAmountPaid: string;
        forcedAmountPaidSigned: string;
    }
    export interface BaseOpenItem$Lookups {
        businessEntity: QueryOperation<BusinessEntity>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        businessEntityPayment: QueryOperation<BusinessEntity>;
        currency: QueryOperation<Currency>;
        closeReason: QueryOperation<CloseReason>;
    }
    export interface BaseOpenItem$Operations {
        query: QueryOperation<BaseOpenItem>;
        read: ReadOperation<BaseOpenItem>;
        aggregate: {
            read: AggregateReadOperation<BaseOpenItem>;
            query: AggregateQueryOperation<BaseOpenItem>;
        };
        update: UpdateOperation<BaseOpenItemInput, BaseOpenItem>;
        updateById: UpdateByIdOperation<BaseOpenItemInput, BaseOpenItem>;
        lookups(dataOrId: string | { data: BaseOpenItemInput }): BaseOpenItem$Lookups;
        getDefaults: GetDefaultsOperation<BaseOpenItem>;
    }
    export interface BasePaymentDocument extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        bankAccount: BankAccount;
        financialSite: Site;
        financialSiteName: string;
        businessRelation: BaseBusinessRelation;
        businessRelationName: string;
        customer: BaseBusinessRelation;
        supplier: BaseBusinessRelation;
        type: BusinessRelationType;
        paymentMethod: PaymentMethod;
        reference: string;
        paymentDate: string;
        postingDate: string;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        exchangeRateDate: string;
        companyExchangeRate: string;
        companyExchangeRateDivisor: string;
        postingStatus: JournalStatus;
        amount: string;
        companyAmount: string;
        amountBankCurrency: string;
        isVoided: boolean;
        voidText: string;
        voidDate: string;
        lines: ClientCollection<PaymentDocumentLine>;
    }
    export interface BasePaymentDocumentInput extends ClientNodeInput {
        _constructor?: string;
        number?: string;
        bankAccount?: integer | string;
        financialSite?: integer | string;
        financialSiteName?: string;
        businessRelation?: integer | string;
        businessRelationName?: string;
        type?: BusinessRelationType;
        paymentMethod?: PaymentMethod;
        reference?: string;
        paymentDate?: string;
        postingDate?: string;
        currency?: integer | string;
        exchangeRateDate?: string;
        companyExchangeRate?: decimal | string;
        companyExchangeRateDivisor?: decimal | string;
        postingStatus?: JournalStatus;
        amount?: decimal | string;
        companyAmount?: decimal | string;
        amountBankCurrency?: decimal | string;
        isVoided?: boolean | string;
        voidText?: string;
        voidDate?: string;
        lines?: Partial<PaymentDocumentLineInput>[];
    }
    export interface BasePaymentDocumentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        bankAccount: BankAccount;
        financialSite: Site;
        financialSiteName: string;
        businessRelation: BaseBusinessRelation;
        businessRelationName: string;
        customer: BaseBusinessRelation;
        supplier: BaseBusinessRelation;
        type: BusinessRelationType;
        paymentMethod: PaymentMethod;
        reference: string;
        paymentDate: string;
        postingDate: string;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        exchangeRateDate: string;
        companyExchangeRate: string;
        companyExchangeRateDivisor: string;
        postingStatus: JournalStatus;
        amount: string;
        companyAmount: string;
        amountBankCurrency: string;
        isVoided: boolean;
        voidText: string;
        voidDate: string;
        lines: ClientCollection<PaymentDocumentLineBinding>;
    }
    export interface BasePaymentDocument$Lookups {
        bankAccount: QueryOperation<BankAccount>;
        financialSite: QueryOperation<Site>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        customer: QueryOperation<BaseBusinessRelation>;
        supplier: QueryOperation<BaseBusinessRelation>;
        currency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface BasePaymentDocument$Operations {
        query: QueryOperation<BasePaymentDocument>;
        read: ReadOperation<BasePaymentDocument>;
        aggregate: {
            read: AggregateReadOperation<BasePaymentDocument>;
            query: AggregateQueryOperation<BasePaymentDocument>;
        };
        create: CreateOperation<BasePaymentDocumentInput, BasePaymentDocument>;
        getDuplicate: GetDuplicateOperation<BasePaymentDocument>;
        lookups(dataOrId: string | { data: BasePaymentDocumentInput }): BasePaymentDocument$Lookups;
        getDefaults: GetDefaultsOperation<BasePaymentDocument>;
    }
    export interface CloseReason extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
    }
    export interface CloseReasonInput extends ClientNodeInput {
        _vendor?: integer | string;
        isActive?: boolean | string;
        id?: string;
        name?: string;
    }
    export interface CloseReasonBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
    }
    export interface CloseReason$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CloseReason$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface CloseReason$Operations {
        query: QueryOperation<CloseReason>;
        read: ReadOperation<CloseReason>;
        aggregate: {
            read: AggregateReadOperation<CloseReason>;
            query: AggregateQueryOperation<CloseReason>;
        };
        create: CreateOperation<CloseReasonInput, CloseReason>;
        getDuplicate: GetDuplicateOperation<CloseReason>;
        duplicate: DuplicateOperation<string, CloseReasonInput, CloseReason>;
        update: UpdateOperation<CloseReasonInput, CloseReason>;
        updateById: UpdateByIdOperation<CloseReasonInput, CloseReason>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: CloseReason$AsyncOperations;
        lookups(dataOrId: string | { data: CloseReasonInput }): CloseReason$Lookups;
        getDefaults: GetDefaultsOperation<CloseReason>;
    }
    export interface CompanyAttributeType extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        company: Company;
        attributeType: AttributeType;
        isRequired: boolean;
        analyticalMeasureType: AnalyticalMeasureType;
    }
    export interface CompanyAttributeTypeInput extends VitalClientNodeInput {
        attributeType?: integer | string;
        isRequired?: boolean | string;
    }
    export interface CompanyAttributeTypeBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        company: Company;
        attributeType: AttributeType;
        isRequired: boolean;
        analyticalMeasureType: AnalyticalMeasureType;
    }
    export interface CompanyAttributeType$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CompanyAttributeType$Lookups {
        attributeType: QueryOperation<AttributeType>;
    }
    export interface CompanyAttributeType$Operations {
        query: QueryOperation<CompanyAttributeType>;
        read: ReadOperation<CompanyAttributeType>;
        aggregate: {
            read: AggregateReadOperation<CompanyAttributeType>;
            query: AggregateQueryOperation<CompanyAttributeType>;
        };
        asyncOperations: CompanyAttributeType$AsyncOperations;
        lookups(dataOrId: string | { data: CompanyAttributeTypeInput }): CompanyAttributeType$Lookups;
        getDefaults: GetDefaultsOperation<CompanyAttributeType>;
    }
    export interface CompanyDefaultAttribute extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        company: Company;
        attributeType: AttributeType;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        masterDataDefault: MasterDataDefault;
    }
    export interface CompanyDefaultAttributeInput extends VitalClientNodeInput {
        attributeType?: integer | string;
        dimensionDefinitionLevel?: DimensionDefinitionLevel;
        masterDataDefault?: MasterDataDefault;
    }
    export interface CompanyDefaultAttributeBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        company: Company;
        attributeType: AttributeType;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        masterDataDefault: MasterDataDefault;
    }
    export interface CompanyDefaultAttribute$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CompanyDefaultAttribute$Lookups {
        attributeType: QueryOperation<AttributeType>;
    }
    export interface CompanyDefaultAttribute$Operations {
        query: QueryOperation<CompanyDefaultAttribute>;
        read: ReadOperation<CompanyDefaultAttribute>;
        aggregate: {
            read: AggregateReadOperation<CompanyDefaultAttribute>;
            query: AggregateQueryOperation<CompanyDefaultAttribute>;
        };
        create: CreateOperation<CompanyDefaultAttributeInput, CompanyDefaultAttribute>;
        getDuplicate: GetDuplicateOperation<CompanyDefaultAttribute>;
        update: UpdateOperation<CompanyDefaultAttributeInput, CompanyDefaultAttribute>;
        updateById: UpdateByIdOperation<CompanyDefaultAttributeInput, CompanyDefaultAttribute>;
        asyncOperations: CompanyDefaultAttribute$AsyncOperations;
        lookups(dataOrId: string | { data: CompanyDefaultAttributeInput }): CompanyDefaultAttribute$Lookups;
        getDefaults: GetDefaultsOperation<CompanyDefaultAttribute>;
    }
    export interface CompanyDefaultDimension extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        company: Company;
        dimensionType: DimensionType;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        masterDataDefault: MasterDataDefault;
    }
    export interface CompanyDefaultDimensionInput extends VitalClientNodeInput {
        dimensionType?: integer | string;
        dimensionDefinitionLevel?: DimensionDefinitionLevel;
        masterDataDefault?: MasterDataDefault;
    }
    export interface CompanyDefaultDimensionBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        company: Company;
        dimensionType: DimensionType;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        masterDataDefault: MasterDataDefault;
    }
    export interface CompanyDefaultDimension$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CompanyDefaultDimension$Lookups {
        dimensionType: QueryOperation<DimensionType>;
    }
    export interface CompanyDefaultDimension$Operations {
        query: QueryOperation<CompanyDefaultDimension>;
        read: ReadOperation<CompanyDefaultDimension>;
        aggregate: {
            read: AggregateReadOperation<CompanyDefaultDimension>;
            query: AggregateQueryOperation<CompanyDefaultDimension>;
        };
        create: CreateOperation<CompanyDefaultDimensionInput, CompanyDefaultDimension>;
        getDuplicate: GetDuplicateOperation<CompanyDefaultDimension>;
        update: UpdateOperation<CompanyDefaultDimensionInput, CompanyDefaultDimension>;
        updateById: UpdateByIdOperation<CompanyDefaultDimensionInput, CompanyDefaultDimension>;
        asyncOperations: CompanyDefaultDimension$AsyncOperations;
        lookups(dataOrId: string | { data: CompanyDefaultDimensionInput }): CompanyDefaultDimension$Lookups;
        getDefaults: GetDefaultsOperation<CompanyDefaultDimension>;
    }
    export interface CompanyDimensionType extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        company: Company;
        dimensionType: DimensionType;
        isRequired: boolean;
        analyticalMeasureType: AnalyticalMeasureType;
    }
    export interface CompanyDimensionTypeInput extends VitalClientNodeInput {
        dimensionType?: integer | string;
        isRequired?: boolean | string;
    }
    export interface CompanyDimensionTypeBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        company: Company;
        dimensionType: DimensionType;
        isRequired: boolean;
        analyticalMeasureType: AnalyticalMeasureType;
    }
    export interface CompanyDimensionType$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CompanyDimensionType$Lookups {
        dimensionType: QueryOperation<DimensionType>;
    }
    export interface CompanyDimensionType$Operations {
        query: QueryOperation<CompanyDimensionType>;
        read: ReadOperation<CompanyDimensionType>;
        aggregate: {
            read: AggregateReadOperation<CompanyDimensionType>;
            query: AggregateQueryOperation<CompanyDimensionType>;
        };
        asyncOperations: CompanyDimensionType$AsyncOperations;
        lookups(dataOrId: string | { data: CompanyDimensionTypeInput }): CompanyDimensionType$Lookups;
        getDefaults: GetDefaultsOperation<CompanyDimensionType>;
    }
    export interface DatevConfiguration extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        accountLength: integer;
        skrCoa: integer;
        customerSupplierLength: integer;
        customerRangeStart: integer;
        customerRangeEnd: integer;
        supplierRangeStart: integer;
        supplierRangeEnd: integer;
    }
    export interface DatevConfigurationInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        isActive?: boolean | string;
        accountLength?: integer | string;
        skrCoa?: integer | string;
        customerSupplierLength?: integer | string;
    }
    export interface DatevConfigurationBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        accountLength: integer;
        skrCoa: integer;
        customerSupplierLength: integer;
        customerRangeStart: integer;
        customerRangeEnd: integer;
        supplierRangeStart: integer;
        supplierRangeEnd: integer;
    }
    export interface DatevConfiguration$Mutations {
        datevConfigurationControlsOnSave: Node$Operation<
            {
                datevConfiguration?: {
                    accountLength?: integer | string;
                    customerRangeStart?: integer | string;
                    customerRangeEnd?: integer | string;
                    supplierRangeStart?: integer | string;
                    supplierRangeEnd?: integer | string;
                };
            },
            string[]
        >;
    }
    export interface DatevConfiguration$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface DatevConfiguration$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface DatevConfiguration$Operations {
        query: QueryOperation<DatevConfiguration>;
        read: ReadOperation<DatevConfiguration>;
        aggregate: {
            read: AggregateReadOperation<DatevConfiguration>;
            query: AggregateQueryOperation<DatevConfiguration>;
        };
        update: UpdateOperation<DatevConfigurationInput, DatevConfiguration>;
        updateById: UpdateByIdOperation<DatevConfigurationInput, DatevConfiguration>;
        mutations: DatevConfiguration$Mutations;
        asyncOperations: DatevConfiguration$AsyncOperations;
        lookups(dataOrId: string | { data: DatevConfigurationInput }): DatevConfiguration$Lookups;
        getDefaults: GetDefaultsOperation<DatevConfiguration>;
    }
    export interface Dimension extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        composedDescription: string;
        dimensionType: DimensionType;
    }
    export interface DimensionInput extends ClientNodeInput {
        isActive?: boolean | string;
        id?: string;
        name?: string;
        dimensionType?: integer | string;
    }
    export interface DimensionBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        composedDescription: string;
        dimensionType: DimensionType;
    }
    export interface Dimension$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Dimension$Lookups {
        dimensionType: QueryOperation<DimensionType>;
    }
    export interface Dimension$Operations {
        query: QueryOperation<Dimension>;
        read: ReadOperation<Dimension>;
        aggregate: {
            read: AggregateReadOperation<Dimension>;
            query: AggregateQueryOperation<Dimension>;
        };
        create: CreateOperation<DimensionInput, Dimension>;
        getDuplicate: GetDuplicateOperation<Dimension>;
        duplicate: DuplicateOperation<string, DimensionInput, Dimension>;
        update: UpdateOperation<DimensionInput, Dimension>;
        updateById: UpdateByIdOperation<DimensionInput, Dimension>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Dimension$AsyncOperations;
        lookups(dataOrId: string | { data: DimensionInput }): Dimension$Lookups;
        getDefaults: GetDefaultsOperation<Dimension>;
    }
    export interface DimensionDefinitionLevelAndDefault extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        masterDataDefault: MasterDataDefault;
    }
    export interface DimensionDefinitionLevelAndDefaultInput extends ClientNodeInput {
        _vendor?: integer | string;
        dimensionDefinitionLevel?: DimensionDefinitionLevel;
        masterDataDefault?: MasterDataDefault;
    }
    export interface DimensionDefinitionLevelAndDefaultBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        masterDataDefault: MasterDataDefault;
    }
    export interface DimensionDefinitionLevelAndDefault$Queries {
        getDefaultAttributesAndDimensions: Node$Operation<
            {
                data: {
                    dimensionDefinitionLevel?: DimensionDefinitionLevel;
                    onlyFromItem?: boolean | string;
                    companyId?: integer | string;
                    siteId?: integer | string;
                    customerId?: integer | string;
                    supplierId?: integer | string;
                    itemId?: integer | string;
                    receivingSiteId?: integer | string;
                    shippingSiteId?: integer | string;
                };
            },
            {
                attributes: string;
                dimensions: string;
            }
        >;
        getDefaultAttributesAndDimensionsOrderToOrder: Node$Operation<
            {
                data: {
                    dimensionDefinitionLevel?: DimensionDefinitionLevel;
                    companyId?: integer | string;
                    siteId?: integer | string;
                    supplierId?: integer | string;
                    itemId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                };
            },
            {
                attributes: string;
                dimensions: string;
            }
        >;
        getAttributesAndDimensionsFromItem: Node$Operation<
            {
                data: {
                    dimensionDefinitionLevel?: DimensionDefinitionLevel;
                    companyId?: integer | string;
                };
            },
            string[]
        >;
    }
    export interface DimensionDefinitionLevelAndDefault$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface DimensionDefinitionLevelAndDefault$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface DimensionDefinitionLevelAndDefault$Operations {
        query: QueryOperation<DimensionDefinitionLevelAndDefault>;
        read: ReadOperation<DimensionDefinitionLevelAndDefault>;
        aggregate: {
            read: AggregateReadOperation<DimensionDefinitionLevelAndDefault>;
            query: AggregateQueryOperation<DimensionDefinitionLevelAndDefault>;
        };
        queries: DimensionDefinitionLevelAndDefault$Queries;
        update: UpdateOperation<DimensionDefinitionLevelAndDefaultInput, DimensionDefinitionLevelAndDefault>;
        updateById: UpdateByIdOperation<DimensionDefinitionLevelAndDefaultInput, DimensionDefinitionLevelAndDefault>;
        asyncOperations: DimensionDefinitionLevelAndDefault$AsyncOperations;
        lookups(
            dataOrId: string | { data: DimensionDefinitionLevelAndDefaultInput },
        ): DimensionDefinitionLevelAndDefault$Lookups;
        getDefaults: GetDefaultsOperation<DimensionDefinitionLevelAndDefault>;
    }
    export interface DimensionType extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        name: string;
        docProperty: DocProperty;
        isUsed: boolean;
        dimensions: ClientCollection<Dimension>;
        analyticalMeasureType: AnalyticalMeasureType;
    }
    export interface DimensionTypeInput extends ClientNodeInput {
        _vendor?: integer | string;
        isActive?: boolean | string;
        name?: string;
        docProperty?: DocProperty;
    }
    export interface DimensionTypeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        name: string;
        docProperty: DocProperty;
        isUsed: boolean;
        dimensions: ClientCollection<Dimension>;
        analyticalMeasureType: AnalyticalMeasureType;
    }
    export interface DimensionType$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface DimensionType$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface DimensionType$Operations {
        query: QueryOperation<DimensionType>;
        read: ReadOperation<DimensionType>;
        aggregate: {
            read: AggregateReadOperation<DimensionType>;
            query: AggregateQueryOperation<DimensionType>;
        };
        create: CreateOperation<DimensionTypeInput, DimensionType>;
        getDuplicate: GetDuplicateOperation<DimensionType>;
        update: UpdateOperation<DimensionTypeInput, DimensionType>;
        updateById: UpdateByIdOperation<DimensionTypeInput, DimensionType>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: DimensionType$AsyncOperations;
        lookups(dataOrId: string | { data: DimensionTypeInput }): DimensionType$Lookups;
        getDefaults: GetDefaultsOperation<DimensionType>;
    }
    export interface FinanceTransaction extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        batchId: string;
        documentNumber: string;
        documentSysId: integer;
        documentType: FinanceDocumentType;
        targetDocumentType: TargetDocumentType;
        targetDocumentNumber: string;
        targetDocumentSysId: integer;
        sourceDocumentLink: string;
        documentNumberLink: string;
        sourceDocumentType: SourceDocumentType;
        sourceDocumentNumber: string;
        sourceDocumentSysId: integer;
        financialSite: Site;
        status: FinanceIntegrationStatus;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        message: string;
        lastStatusUpdate: string;
        lines: ClientCollection<FinanceTransactionLine>;
        hasSourceForDimensionLines: boolean;
        paymentTracking: PaymentTracking;
        postingStatus: PostingStatus;
    }
    export interface FinanceTransactionInput extends ClientNodeInput {
        batchId?: string;
        documentNumber?: string;
        documentSysId?: integer | string;
        documentType?: FinanceDocumentType;
        targetDocumentType?: TargetDocumentType;
        targetDocumentNumber?: string;
        targetDocumentSysId?: integer | string;
        sourceDocumentType?: SourceDocumentType;
        sourceDocumentNumber?: string;
        sourceDocumentSysId?: integer | string;
        financialSite?: integer | string;
        status?: FinanceIntegrationStatus;
        financeIntegrationApp?: FinanceIntegrationApp;
        financeIntegrationAppRecordId?: string;
        financeIntegrationAppUrl?: string;
        message?: string;
        lastStatusUpdate?: string;
        lines?: Partial<FinanceTransactionLineInput>[];
        paymentTracking?: integer | string;
    }
    export interface FinanceTransactionBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        batchId: string;
        documentNumber: string;
        documentSysId: integer;
        documentType: FinanceDocumentType;
        targetDocumentType: TargetDocumentType;
        targetDocumentNumber: string;
        targetDocumentSysId: integer;
        sourceDocumentLink: string;
        documentNumberLink: any;
        sourceDocumentType: SourceDocumentType;
        sourceDocumentNumber: string;
        sourceDocumentSysId: integer;
        financialSite: Site;
        status: FinanceIntegrationStatus;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        message: string;
        lastStatusUpdate: string;
        lines: ClientCollection<FinanceTransactionLineBinding>;
        hasSourceForDimensionLines: boolean;
        paymentTracking: PaymentTracking;
        postingStatus: PostingStatus;
    }
    export interface FinanceTransaction$Queries {
        getPostingStatusData: Node$Operation<
            {
                documentNumber?: string;
            },
            {
                _id: string;
                documentType: TargetDocumentType;
                documentNumber: string;
                documentSysId: integer;
                status: PostingStatus;
                message: string;
                hasFinanceIntegrationApp: boolean;
                financeIntegrationApp: FinanceIntegrationApp | null;
                financeIntegrationAppRecordId: string;
                financeIntegrationAppUrl: string;
                externalLink: boolean;
                hasSourceForDimensionLines: boolean;
            }[]
        >;
        getPostingStatusDataByDocumentId: Node$Operation<
            {
                documentType?: FinanceDocumentType;
                documentSysId?: string;
            },
            {
                _id: string;
                documentType: TargetDocumentType;
                documentNumber: string;
                documentSysId: integer;
                status: PostingStatus;
                message: string;
                hasFinanceIntegrationApp: boolean;
                financeIntegrationApp: FinanceIntegrationApp | null;
                financeIntegrationAppRecordId: string;
                financeIntegrationAppUrl: string;
                externalLink: boolean;
                hasSourceForDimensionLines: boolean;
            }[]
        >;
    }
    export interface FinanceTransaction$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface FinanceTransaction$Lookups {
        financialSite: QueryOperation<Site>;
        paymentTracking: QueryOperation<PaymentTracking>;
    }
    export interface FinanceTransaction$Operations {
        query: QueryOperation<FinanceTransaction>;
        read: ReadOperation<FinanceTransaction>;
        aggregate: {
            read: AggregateReadOperation<FinanceTransaction>;
            query: AggregateQueryOperation<FinanceTransaction>;
        };
        queries: FinanceTransaction$Queries;
        asyncOperations: FinanceTransaction$AsyncOperations;
        lookups(dataOrId: string | { data: FinanceTransactionInput }): FinanceTransaction$Lookups;
        getDefaults: GetDefaultsOperation<FinanceTransaction>;
    }
    export interface FinanceTransactionLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        financeTransaction: FinanceTransaction;
        sourceDocumentType: SourceDocumentType;
        sourceDocumentNumber: string;
        sourceDocumentSysId: integer;
        isSourceForDimension: boolean;
    }
    export interface FinanceTransactionLineInput extends VitalClientNodeInput {
        sourceDocumentType?: SourceDocumentType;
        sourceDocumentNumber?: string;
        sourceDocumentSysId?: integer | string;
        isSourceForDimension?: boolean | string;
    }
    export interface FinanceTransactionLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        financeTransaction: FinanceTransaction;
        sourceDocumentType: SourceDocumentType;
        sourceDocumentNumber: string;
        sourceDocumentSysId: integer;
        isSourceForDimension: boolean;
    }
    export interface FinanceTransactionLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface FinanceTransactionLine$Operations {
        query: QueryOperation<FinanceTransactionLine>;
        read: ReadOperation<FinanceTransactionLine>;
        aggregate: {
            read: AggregateReadOperation<FinanceTransactionLine>;
            query: AggregateQueryOperation<FinanceTransactionLine>;
        };
        asyncOperations: FinanceTransactionLine$AsyncOperations;
        getDefaults: GetDefaultsOperation<FinanceTransactionLine>;
    }
    export interface Journal extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        legislation: Legislation;
        name: string;
        primaryDocumentType: string;
        secondaryDocumentType: string;
        isActive: boolean;
        sequence: SequenceNumber;
        isSubjectToGlTaxExcludedAmount: boolean;
        taxImpact: boolean;
    }
    export interface JournalInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        legislation?: integer | string;
        name?: string;
        primaryDocumentType?: string;
        secondaryDocumentType?: string;
        isActive?: boolean | string;
        sequence?: integer | string;
        taxImpact?: boolean | string;
    }
    export interface JournalBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        legislation: Legislation;
        name: string;
        primaryDocumentType: string;
        secondaryDocumentType: string;
        isActive: boolean;
        sequence: SequenceNumber;
        isSubjectToGlTaxExcludedAmount: boolean;
        taxImpact: boolean;
    }
    export interface Journal$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Journal$Lookups {
        _vendor: QueryOperation<SysVendor>;
        legislation: QueryOperation<Legislation>;
        sequence: QueryOperation<SequenceNumber>;
    }
    export interface Journal$Operations {
        query: QueryOperation<Journal>;
        read: ReadOperation<Journal>;
        aggregate: {
            read: AggregateReadOperation<Journal>;
            query: AggregateQueryOperation<Journal>;
        };
        create: CreateOperation<JournalInput, Journal>;
        getDuplicate: GetDuplicateOperation<Journal>;
        duplicate: DuplicateOperation<string, JournalInput, Journal>;
        update: UpdateOperation<JournalInput, Journal>;
        updateById: UpdateByIdOperation<JournalInput, Journal>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Journal$AsyncOperations;
        lookups(dataOrId: string | { data: JournalInput }): Journal$Lookups;
        getDefaults: GetDefaultsOperation<Journal>;
    }
    export interface JournalEntryType extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        name: string;
        legislation: Legislation;
        documentType: FinanceDocumentType;
        targetDocumentType: TargetDocumentType;
        immediatePosting: boolean;
        headerJournal: Journal;
        headerPostingDate: HeaderPostingDate;
        headerDescription: HeaderDescription;
        headerAccountType: PostingClassDefinition;
        headerAmountType: AmountType;
        lines: ClientCollection<JournalEntryTypeLine>;
    }
    export interface JournalEntryTypeInput extends ClientNodeInput {
        _vendor?: integer | string;
        isActive?: boolean | string;
        name?: string;
        legislation?: integer | string;
        documentType?: FinanceDocumentType;
        targetDocumentType?: TargetDocumentType;
        immediatePosting?: boolean | string;
        headerJournal?: integer | string;
        headerPostingDate?: HeaderPostingDate;
        headerDescription?: HeaderDescription;
        headerAccountType?: integer | string;
        headerAmountType?: AmountType;
        lines?: Partial<JournalEntryTypeLineInput>[];
    }
    export interface JournalEntryTypeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        name: string;
        legislation: Legislation;
        documentType: FinanceDocumentType;
        targetDocumentType: TargetDocumentType;
        immediatePosting: boolean;
        headerJournal: Journal;
        headerPostingDate: HeaderPostingDate;
        headerDescription: HeaderDescription;
        headerAccountType: PostingClassDefinition;
        headerAmountType: AmountType;
        lines: ClientCollection<JournalEntryTypeLineBinding>;
    }
    export interface JournalEntryType$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface JournalEntryType$Lookups {
        _vendor: QueryOperation<SysVendor>;
        legislation: QueryOperation<Legislation>;
        headerJournal: QueryOperation<Journal>;
        headerAccountType: QueryOperation<PostingClassDefinition>;
    }
    export interface JournalEntryType$Operations {
        query: QueryOperation<JournalEntryType>;
        read: ReadOperation<JournalEntryType>;
        aggregate: {
            read: AggregateReadOperation<JournalEntryType>;
            query: AggregateQueryOperation<JournalEntryType>;
        };
        update: UpdateOperation<JournalEntryTypeInput, JournalEntryType>;
        updateById: UpdateByIdOperation<JournalEntryTypeInput, JournalEntryType>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: JournalEntryType$AsyncOperations;
        lookups(dataOrId: string | { data: JournalEntryTypeInput }): JournalEntryType$Lookups;
        getDefaults: GetDefaultsOperation<JournalEntryType>;
    }
    export interface JournalEntryTypeLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        journalEntryType: JournalEntryType;
        movementType: MovementType;
        accountType: PostingClassDefinition;
        amountType: AmountType;
        sign: Sign;
        commonReference: CommonReference;
        isStockItemAllowed: boolean;
        isNonStockItemAllowed: boolean;
        isServiceItemAllowed: boolean;
        isLandedCostItemAllowed: boolean;
        contraJournalEntryTypeLine: JournalEntryTypeLine;
    }
    export interface JournalEntryTypeLineInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        movementType?: MovementType;
        accountType?: integer | string;
        amountType?: AmountType;
        sign?: Sign;
        commonReference?: CommonReference;
        isStockItemAllowed?: boolean | string;
        isNonStockItemAllowed?: boolean | string;
        isServiceItemAllowed?: boolean | string;
        isLandedCostItemAllowed?: boolean | string;
        contraJournalEntryTypeLine?: integer | string;
    }
    export interface JournalEntryTypeLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        journalEntryType: JournalEntryType;
        movementType: MovementType;
        accountType: PostingClassDefinition;
        amountType: AmountType;
        sign: Sign;
        commonReference: CommonReference;
        isStockItemAllowed: boolean;
        isNonStockItemAllowed: boolean;
        isServiceItemAllowed: boolean;
        isLandedCostItemAllowed: boolean;
        contraJournalEntryTypeLine: JournalEntryTypeLine;
    }
    export interface JournalEntryTypeLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface JournalEntryTypeLine$Lookups {
        _vendor: QueryOperation<SysVendor>;
        accountType: QueryOperation<PostingClassDefinition>;
        contraJournalEntryTypeLine: QueryOperation<JournalEntryTypeLine>;
    }
    export interface JournalEntryTypeLine$Operations {
        query: QueryOperation<JournalEntryTypeLine>;
        read: ReadOperation<JournalEntryTypeLine>;
        aggregate: {
            read: AggregateReadOperation<JournalEntryTypeLine>;
            query: AggregateQueryOperation<JournalEntryTypeLine>;
        };
        asyncOperations: JournalEntryTypeLine$AsyncOperations;
        lookups(dataOrId: string | { data: JournalEntryTypeLineInput }): JournalEntryTypeLine$Lookups;
        getDefaults: GetDefaultsOperation<JournalEntryTypeLine>;
    }
    export interface PaymentTracking extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        document: BaseDocument;
        paymentTerm: PaymentTerm;
        discountPaymentAmount: string;
        penaltyPaymentAmount: string;
        penaltyPaymentType: PaymentTermDiscountOrPenaltyType;
        discountPaymentType: PaymentTermDiscountOrPenaltyType;
        discountPaymentBeforeDate: string;
        openItems: ClientCollection<BaseOpenItem>;
        status: OpenItemStatus;
        currency: Currency;
        amountPaid: string;
        forcedAmountPaid: string;
        paymentLines: ClientCollection<PaymentDocumentLine>;
    }
    export interface PaymentTrackingInput extends VitalClientNodeInput {
        paymentTerm?: integer | string;
        discountPaymentAmount?: decimal | string;
        penaltyPaymentAmount?: decimal | string;
        penaltyPaymentType?: PaymentTermDiscountOrPenaltyType;
        discountPaymentType?: PaymentTermDiscountOrPenaltyType;
        discountPaymentBeforeDate?: string;
    }
    export interface PaymentTrackingBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        document: BaseDocument;
        paymentTerm: PaymentTerm;
        discountPaymentAmount: string;
        penaltyPaymentAmount: string;
        penaltyPaymentType: PaymentTermDiscountOrPenaltyType;
        discountPaymentType: PaymentTermDiscountOrPenaltyType;
        discountPaymentBeforeDate: string;
        openItems: ClientCollection<BaseOpenItem>;
        status: OpenItemStatus;
        currency: Currency;
        amountPaid: string;
        forcedAmountPaid: string;
        paymentLines: ClientCollection<PaymentDocumentLine>;
    }
    export interface PaymentTracking$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PaymentTracking$Lookups {
        paymentTerm: QueryOperation<PaymentTerm>;
        currency: QueryOperation<Currency>;
    }
    export interface PaymentTracking$Operations {
        query: QueryOperation<PaymentTracking>;
        read: ReadOperation<PaymentTracking>;
        aggregate: {
            read: AggregateReadOperation<PaymentTracking>;
            query: AggregateQueryOperation<PaymentTracking>;
        };
        create: CreateOperation<PaymentTrackingInput, PaymentTracking>;
        getDuplicate: GetDuplicateOperation<PaymentTracking>;
        duplicate: DuplicateOperation<string, PaymentTrackingInput, PaymentTracking>;
        update: UpdateOperation<PaymentTrackingInput, PaymentTracking>;
        updateById: UpdateByIdOperation<PaymentTrackingInput, PaymentTracking>;
        asyncOperations: PaymentTracking$AsyncOperations;
        lookups(dataOrId: string | { data: PaymentTrackingInput }): PaymentTracking$Lookups;
        getDefaults: GetDefaultsOperation<PaymentTracking>;
    }
    export interface PostingClass extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        type: PostingClassType;
        name: string;
        isDetailed: boolean;
        isStockItemAllowed: boolean;
        isNonStockItemAllowed: boolean;
        isServiceItemAllowed: boolean;
        isLandedCostItemAllowed: boolean;
        financeItemType: FinanceItemType[];
        lines: ClientCollection<PostingClassLine>;
    }
    export interface PostingClassInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        type?: PostingClassType;
        name?: string;
        isDetailed?: boolean | string;
        isStockItemAllowed?: boolean | string;
        isNonStockItemAllowed?: boolean | string;
        isServiceItemAllowed?: boolean | string;
        isLandedCostItemAllowed?: boolean | string;
        lines?: Partial<PostingClassLineInput>[];
    }
    export interface PostingClassBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        type: PostingClassType;
        name: string;
        isDetailed: boolean;
        isStockItemAllowed: boolean;
        isNonStockItemAllowed: boolean;
        isServiceItemAllowed: boolean;
        isLandedCostItemAllowed: boolean;
        financeItemType: FinanceItemType[];
        lines: ClientCollection<PostingClassLineBinding>;
    }
    export interface PostingClass$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PostingClass$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface PostingClass$Operations {
        query: QueryOperation<PostingClass>;
        read: ReadOperation<PostingClass>;
        aggregate: {
            read: AggregateReadOperation<PostingClass>;
            query: AggregateQueryOperation<PostingClass>;
        };
        create: CreateOperation<PostingClassInput, PostingClass>;
        getDuplicate: GetDuplicateOperation<PostingClass>;
        duplicate: DuplicateOperation<string, PostingClassInput, PostingClass>;
        update: UpdateOperation<PostingClassInput, PostingClass>;
        updateById: UpdateByIdOperation<PostingClassInput, PostingClass>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: PostingClass$AsyncOperations;
        lookups(dataOrId: string | { data: PostingClassInput }): PostingClass$Lookups;
        getDefaults: GetDefaultsOperation<PostingClass>;
    }
    export interface PostingClassDefinition extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        legislation: Legislation;
        id: string;
        postingClassType: PostingClassType;
        accountTypeName: string;
        isDetailed: boolean;
        isStockItemAllowed: boolean;
        isNonStockItemAllowed: boolean;
        isServiceItemAllowed: boolean;
        isLandedCostItemAllowed: boolean;
        financeItemType: FinanceItemType[];
        canHaveAdditionalCriteria: boolean;
        additionalCriteria: PostingClassType;
    }
    export interface PostingClassDefinitionInput extends ClientNodeInput {
        _vendor?: integer | string;
        legislation?: integer | string;
        id?: string;
        postingClassType?: PostingClassType;
        accountTypeName?: string;
        isDetailed?: boolean | string;
        isStockItemAllowed?: boolean | string;
        isNonStockItemAllowed?: boolean | string;
        isServiceItemAllowed?: boolean | string;
        isLandedCostItemAllowed?: boolean | string;
        canHaveAdditionalCriteria?: boolean | string;
        additionalCriteria?: PostingClassType;
    }
    export interface PostingClassDefinitionBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        legislation: Legislation;
        id: string;
        postingClassType: PostingClassType;
        accountTypeName: string;
        isDetailed: boolean;
        isStockItemAllowed: boolean;
        isNonStockItemAllowed: boolean;
        isServiceItemAllowed: boolean;
        isLandedCostItemAllowed: boolean;
        financeItemType: FinanceItemType[];
        canHaveAdditionalCriteria: boolean;
        additionalCriteria: PostingClassType;
    }
    export interface PostingClassDefinition$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PostingClassDefinition$Lookups {
        _vendor: QueryOperation<SysVendor>;
        legislation: QueryOperation<Legislation>;
    }
    export interface PostingClassDefinition$Operations {
        query: QueryOperation<PostingClassDefinition>;
        read: ReadOperation<PostingClassDefinition>;
        aggregate: {
            read: AggregateReadOperation<PostingClassDefinition>;
            query: AggregateQueryOperation<PostingClassDefinition>;
        };
        update: UpdateOperation<PostingClassDefinitionInput, PostingClassDefinition>;
        updateById: UpdateByIdOperation<PostingClassDefinitionInput, PostingClassDefinition>;
        asyncOperations: PostingClassDefinition$AsyncOperations;
        lookups(dataOrId: string | { data: PostingClassDefinitionInput }): PostingClassDefinition$Lookups;
        getDefaults: GetDefaultsOperation<PostingClassDefinition>;
    }
    export interface PostingClassLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        postingClass: PostingClass;
        chartOfAccount: ChartOfAccount;
        definition: PostingClassDefinition;
        account: Account;
        isStockItemAllowed: boolean;
        isNonStockItemAllowed: boolean;
        isServiceItemAllowed: boolean;
        isLandedCostItemAllowed: boolean;
        details: ClientCollection<PostingClassLineDetail>;
        hasDetails: boolean;
    }
    export interface PostingClassLineInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        chartOfAccount?: integer | string;
        definition?: integer | string;
        account?: integer | string;
        updateAccountTaxManagement?: boolean | string;
        details?: Partial<PostingClassLineDetailInput>[];
    }
    export interface PostingClassLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        postingClass: PostingClass;
        chartOfAccount: ChartOfAccount;
        definition: PostingClassDefinition;
        account: Account;
        updateAccountTaxManagement: boolean;
        isStockItemAllowed: boolean;
        isNonStockItemAllowed: boolean;
        isServiceItemAllowed: boolean;
        isLandedCostItemAllowed: boolean;
        details: ClientCollection<PostingClassLineDetailBinding>;
        hasDetails: boolean;
    }
    export interface PostingClassLine$Queries {
        getPostingClassAccounts: Node$Operation<
            {
                postingClassDefinition: string;
            },
            {
                postingClass: PostingClass;
                account: Account | null;
                details: {
                    account: Account | null;
                    tax: Tax | null;
                }[];
            }[]
        >;
    }
    export interface PostingClassLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PostingClassLine$Lookups {
        _vendor: QueryOperation<SysVendor>;
        chartOfAccount: QueryOperation<ChartOfAccount>;
        definition: QueryOperation<PostingClassDefinition>;
        account: QueryOperation<Account>;
    }
    export interface PostingClassLine$Operations {
        query: QueryOperation<PostingClassLine>;
        read: ReadOperation<PostingClassLine>;
        aggregate: {
            read: AggregateReadOperation<PostingClassLine>;
            query: AggregateQueryOperation<PostingClassLine>;
        };
        queries: PostingClassLine$Queries;
        asyncOperations: PostingClassLine$AsyncOperations;
        lookups(dataOrId: string | { data: PostingClassLineInput }): PostingClassLine$Lookups;
        getDefaults: GetDefaultsOperation<PostingClassLine>;
    }
    export interface PostingClassLineDetail extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        postingClassLine: PostingClassLine;
        account: Account;
        tax: Tax;
    }
    export interface PostingClassLineDetailInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        account?: integer | string;
        tax?: integer | string;
    }
    export interface PostingClassLineDetailBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        postingClassLine: PostingClassLine;
        account: Account;
        tax: Tax;
    }
    export interface PostingClassLineDetail$Mutations {
        getTaxCategoryIds: Node$Operation<
            {
                legislation?: string;
            },
            string[]
        >;
    }
    export interface PostingClassLineDetail$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PostingClassLineDetail$Lookups {
        _vendor: QueryOperation<SysVendor>;
        account: QueryOperation<Account>;
        tax: QueryOperation<Tax>;
    }
    export interface PostingClassLineDetail$Operations {
        query: QueryOperation<PostingClassLineDetail>;
        read: ReadOperation<PostingClassLineDetail>;
        aggregate: {
            read: AggregateReadOperation<PostingClassLineDetail>;
            query: AggregateQueryOperation<PostingClassLineDetail>;
        };
        mutations: PostingClassLineDetail$Mutations;
        asyncOperations: PostingClassLineDetail$AsyncOperations;
        lookups(dataOrId: string | { data: PostingClassLineDetailInput }): PostingClassLineDetail$Lookups;
        getDefaults: GetDefaultsOperation<PostingClassLineDetail>;
    }
    export interface BaseFinanceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        documentNumber: string;
        documentId: integer;
        document: BaseFinanceDocument;
        anyAttributesAndDimensions: ClientCollection<BaseFinanceLineDimension>;
    }
    export interface BaseFinanceLineInput extends VitalClientNodeInput {
        _constructor?: string;
        attributesAndDimensions?: Partial<BaseFinanceLineDimensionInput>[];
    }
    export interface BaseFinanceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        documentNumber: string;
        documentId: integer;
        document: BaseFinanceDocument;
        attributesAndDimensions: ClientCollection<BaseFinanceLineDimensionBinding>;
    }
    export interface BaseFinanceLine$Operations {
        query: QueryOperation<BaseFinanceLine>;
        read: ReadOperation<BaseFinanceLine>;
        aggregate: {
            read: AggregateReadOperation<BaseFinanceLine>;
            query: AggregateQueryOperation<BaseFinanceLine>;
        };
        getDefaults: GetDefaultsOperation<BaseFinanceLine>;
    }
    export interface PaymentDocumentLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: BasePaymentDocument;
        financialSite: Site;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        origin: AccountsPayableReceivableInvoiceOrigin;
        amount: string;
        amountBankCurrency: string;
        discountAmount: string;
        penaltyAmount: string;
        adjustmentAmount: string;
        originalOpenItem: BaseOpenItem;
        originalNodeFactory: MetaNodeFactory;
        paymentTracking: PaymentTracking;
    }
    export interface PaymentDocumentLineInput extends VitalClientNodeInput {
        financialSite?: integer | string;
        currency?: integer | string;
        origin?: AccountsPayableReceivableInvoiceOrigin;
        amount?: decimal | string;
        amountBankCurrency?: decimal | string;
        discountAmount?: decimal | string;
        penaltyAmount?: decimal | string;
        adjustmentAmount?: decimal | string;
        originalOpenItem?: integer | string;
        originalNodeFactory?: integer | string;
        paymentTracking?: integer | string;
    }
    export interface PaymentDocumentLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: BasePaymentDocument;
        financialSite: Site;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        origin: AccountsPayableReceivableInvoiceOrigin;
        amount: string;
        amountBankCurrency: string;
        discountAmount: string;
        penaltyAmount: string;
        adjustmentAmount: string;
        originalOpenItem: BaseOpenItem;
        originalNodeFactory: MetaNodeFactory;
        paymentTracking: PaymentTracking;
    }
    export interface PaymentDocumentLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface PaymentDocumentLine$Lookups {
        financialSite: QueryOperation<Site>;
        currency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
        originalOpenItem: QueryOperation<BaseOpenItem>;
        originalNodeFactory: QueryOperation<MetaNodeFactory>;
        paymentTracking: QueryOperation<PaymentTracking>;
    }
    export interface PaymentDocumentLine$Operations {
        query: QueryOperation<PaymentDocumentLine>;
        read: ReadOperation<PaymentDocumentLine>;
        aggregate: {
            read: AggregateReadOperation<PaymentDocumentLine>;
            query: AggregateQueryOperation<PaymentDocumentLine>;
        };
        asyncOperations: PaymentDocumentLine$AsyncOperations;
        lookups(dataOrId: string | { data: PaymentDocumentLineInput }): PaymentDocumentLine$Lookups;
        getDefaults: GetDefaultsOperation<PaymentDocumentLine>;
    }
    export interface BaseBusinessRelationExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        datevId: integer;
    }
    export interface BaseBusinessRelationInputExtension {
        _constructor?: string;
        isActive?: boolean | string;
        businessEntity?: BusinessEntityInput;
        primaryAddress?: integer | string;
        internalNote?: TextStream;
        paymentTerm?: integer | string;
        minimumOrderAmount?: decimal | string;
        category?: integer | string;
        datevId?: integer | string;
    }
    export interface BaseBusinessRelationBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        datevId: integer;
    }
    export interface BaseDocumentExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        anyLines: ClientCollection<BaseDocumentItemLine>;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        financeIntegrationStatus: FinanceIntegrationStatus;
    }
    export interface BaseDocumentInputExtension {
        _constructor?: string;
        number?: string;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        currency?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        lines?: Partial<BaseDocumentItemLineInput>[];
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
    }
    export interface BaseDocumentBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        lines: ClientCollection<BaseDocumentItemLineBinding>;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        financeIntegrationStatus: FinanceIntegrationStatus;
    }
    export interface BaseDocumentItemLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        documentNumber: string;
        documentId: integer;
        document: BaseDocument;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        quantityInStockUnit: string;
        unitToStockUnitConversionFactor: string;
    }
    export interface BaseDocumentItemLineInputExtension {
        _constructor?: string;
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        quantityInStockUnit?: decimal | string;
        unitToStockUnitConversionFactor?: decimal | string;
    }
    export interface BaseDocumentItemLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        documentNumber: string;
        documentId: integer;
        document: BaseDocument;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        quantityInStockUnit: string;
        unitToStockUnitConversionFactor: string;
    }
    export interface BaseDocumentItemLineExtension$Mutations {
        setDimension: Node$Operation<
            {
                baseDocumentItemLine: string;
                storedDimensions?: string;
                storedAttributes?: string;
            },
            boolean
        >;
    }
    export interface BaseDocumentItemLineExtension$Lookups {
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface BaseDocumentItemLineExtension$Operations {
        mutations: BaseDocumentItemLineExtension$Mutations;
        lookups(dataOrId: string | { data: BaseDocumentItemLineInput }): BaseDocumentItemLineExtension$Lookups;
        getDefaults: GetDefaultsOperation<BaseDocumentItemLine>;
    }
    export interface BaseDocumentLineInquiryExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        user: User;
        company: Company;
        site: Site;
        itemCategory: ItemCategory;
        commodityCode: string;
        fromItem: Item;
        toItem: Item;
        date: string;
        anyLines: ClientCollection<BaseDocumentItemLine>;
        postingClass: PostingClass;
    }
    export interface BaseDocumentLineInquiryInputExtension {
        _constructor?: string;
        user?: integer | string;
        company?: integer | string;
        site?: integer | string;
        itemCategory?: integer | string;
        commodityCode?: string;
        fromItem?: integer | string;
        toItem?: integer | string;
        date?: string;
        postingClass?: integer | string;
    }
    export interface BaseDocumentLineInquiryBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        user: User;
        company: Company;
        site: Site;
        itemCategory: ItemCategory;
        commodityCode: string;
        fromItem: Item;
        toItem: Item;
        date: string;
        lines: ClientCollection<BaseDocumentItemLine>;
        postingClass: PostingClass;
    }
    export interface BaseDocumentLineInquiryExtension$Lookups {
        postingClass: QueryOperation<PostingClass>;
    }
    export interface BaseDocumentLineInquiryExtension$Operations {
        lookups(dataOrId: string | { data: BaseDocumentLineInquiryInput }): BaseDocumentLineInquiryExtension$Lookups;
    }
    export interface CompanyExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        name: string;
        description: string;
        legislation: Legislation;
        chartOfAccount: ChartOfAccount;
        hierarchyChartContent: TextStream;
        siren: string;
        naf: string;
        rcs: string;
        legalForm: LegalForm;
        country: Country;
        currency: Currency;
        priceScale: integer;
        addresses: ClientCollection<CompanyAddress>;
        primaryAddress: CompanyAddress;
        primaryContact: CompanyContact;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        customerOnHoldCheck: CustomerOnHoldType;
        contacts: ClientCollection<CompanyContact>;
        postingClass: PostingClass;
        taxEngine: TaxEngine;
        doStockPosting: boolean;
        doWipPosting: boolean;
        doArPosting: boolean;
        doApPosting: boolean;
        doNonAbsorbedPosting: boolean;
        attributeTypes: ClientCollection<CompanyAttributeType>;
        dimensionTypes: ClientCollection<CompanyDimensionType>;
        defaultAttributes: ClientCollection<CompanyDefaultAttribute>;
        defaultDimensions: ClientCollection<CompanyDefaultDimension>;
        datevConsultantNumber: integer;
        datevCustomerNumber: integer;
        bankAccount: BankAccount;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface CompanyInputExtension {
        id?: string;
        isActive?: boolean | string;
        name?: string;
        description?: string;
        legislation?: integer | string;
        chartOfAccount?: integer | string;
        siren?: string;
        naf?: string;
        rcs?: string;
        legalForm?: LegalForm;
        country?: integer | string;
        currency?: integer | string;
        addresses?: Partial<CompanyAddressInput>[];
        sequenceNumberId?: string;
        customerOnHoldCheck?: CustomerOnHoldType;
        contacts?: Partial<CompanyContactInput>[];
        postingClass?: integer | string;
        taxEngine?: TaxEngine;
        doStockPosting?: boolean | string;
        doWipPosting?: boolean | string;
        doNonAbsorbedPosting?: boolean | string;
        attributeTypes?: Partial<CompanyAttributeTypeInput>[];
        dimensionTypes?: Partial<CompanyDimensionTypeInput>[];
        defaultAttributes?: Partial<CompanyDefaultAttributeInput>[];
        defaultDimensions?: Partial<CompanyDefaultDimensionInput>[];
        datevConsultantNumber?: integer | string;
        datevCustomerNumber?: integer | string;
        bankAccount?: integer | string;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface CompanyBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        name: string;
        description: string;
        legislation: Legislation;
        chartOfAccount: ChartOfAccount;
        hierarchyChartContent: TextStream;
        siren: string;
        naf: string;
        rcs: string;
        legalForm: LegalForm;
        country: Country;
        currency: Currency;
        priceScale: integer;
        addresses: ClientCollection<CompanyAddressBinding>;
        primaryAddress: CompanyAddress;
        primaryContact: CompanyContact;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        customerOnHoldCheck: CustomerOnHoldType;
        contacts: ClientCollection<CompanyContactBinding>;
        postingClass: PostingClass;
        taxEngine: TaxEngine;
        doStockPosting: boolean;
        doWipPosting: boolean;
        doArPosting: boolean;
        doApPosting: boolean;
        doNonAbsorbedPosting: boolean;
        attributeTypes: ClientCollection<CompanyAttributeTypeBinding>;
        dimensionTypes: ClientCollection<CompanyDimensionTypeBinding>;
        defaultAttributes: ClientCollection<CompanyDefaultAttributeBinding>;
        defaultDimensions: ClientCollection<CompanyDefaultDimensionBinding>;
        datevConsultantNumber: integer;
        datevCustomerNumber: integer;
        bankAccount: BankAccount;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface CompanyExtension$Lookups {
        postingClass: QueryOperation<PostingClass>;
        bankAccount: QueryOperation<BankAccount>;
    }
    export interface CompanyExtension$Operations {
        lookups(dataOrId: string | { data: CompanyInput }): CompanyExtension$Lookups;
    }
    export interface CustomerExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        billToCustomer: Customer;
        billToAddress: BusinessEntityAddress;
        payByCustomer: Customer;
        payByAddress: BusinessEntityAddress;
        deliveryAddresses: ClientCollection<BusinessEntityAddress>;
        primaryShipToAddress: BusinessEntityAddress;
        isOnHold: boolean;
        items: ClientCollection<ItemCustomer>;
        itemPrices: ClientCollection<ItemCustomerPrice>;
        creditLimit: string;
        displayStatus: CustomerDisplayStatus;
        datevId: integer;
        postingClass: PostingClass;
        storedDimensions: string;
        storedAttributes: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        analyticalData: AnalyticalData;
    }
    export interface CustomerInputExtension {
        isActive?: boolean | string;
        businessEntity?: BusinessEntityInput;
        primaryAddress?: integer | string;
        internalNote?: TextStream;
        paymentTerm?: integer | string;
        minimumOrderAmount?: decimal | string;
        category?: integer | string;
        billToCustomer?: integer | string;
        billToAddress?: integer | string;
        payByCustomer?: integer | string;
        payByAddress?: integer | string;
        isOnHold?: boolean | string;
        items?: Partial<ItemCustomerInput>[];
        creditLimit?: decimal | string;
        datevId?: integer | string;
        postingClass?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        analyticalData?: integer | string;
    }
    export interface CustomerBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        billToCustomer: Customer;
        billToAddress: BusinessEntityAddress;
        payByCustomer: Customer;
        payByAddress: BusinessEntityAddress;
        deliveryAddresses: ClientCollection<BusinessEntityAddress>;
        primaryShipToAddress: BusinessEntityAddress;
        isOnHold: boolean;
        items: ClientCollection<ItemCustomerBinding>;
        itemPrices: ClientCollection<ItemCustomerPrice>;
        creditLimit: string;
        displayStatus: CustomerDisplayStatus;
        datevId: integer;
        postingClass: PostingClass;
        storedDimensions: any;
        storedAttributes: any;
        _attachments: ClientCollection<AttachmentAssociation>;
        analyticalData: AnalyticalData;
    }
    export interface CustomerExtension$Lookups {
        postingClass: QueryOperation<PostingClass>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface CustomerExtension$Operations {
        lookups(dataOrId: string | { data: CustomerInput }): CustomerExtension$Lookups;
    }
    export interface DetailedResourceExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        resourceGroup: GroupResource;
        postingClass: PostingClass;
        _syncTick: string;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategory>;
    }
    export interface DetailedResourceInputExtension {
        _constructor?: string;
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        activeRange?: string;
        site?: integer | string;
        resourceImage?: BinaryStream;
        weeklyShift?: integer | string;
        resourceGroup?: integer | string;
        postingClass?: integer | string;
        _syncTick?: decimal | string;
        efficiency?: decimal | string;
        location?: integer | string;
        resourceCostCategories?: Partial<ResourceCostCategoryInput>[];
    }
    export interface DetailedResourceBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        resourceGroup: GroupResource;
        postingClass: PostingClass;
        _syncTick: string;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategoryBinding>;
    }
    export interface DetailedResourceExtension$Lookups {
        postingClass: QueryOperation<PostingClass>;
    }
    export interface DetailedResourceExtension$Operations {
        lookups(dataOrId: string | { data: DetailedResourceInput }): DetailedResourceExtension$Lookups;
    }
    export interface ItemExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergen>;
        classifications: ClientCollection<ItemClassifications>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPrice>;
        itemSites: ClientCollection<ItemSite>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPrice>;
        capacity: integer;
        landedCostItem: LandedCostItem;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        inStockQuantity: string;
        inTransitStockQuantity: string;
        itemTaxGroup: ItemTaxGroup;
        postingClass: PostingClass;
        storedDimensions: string;
        storedAttributes: string;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
        analyticalData: AnalyticalData;
    }
    export interface ItemInputExtension {
        name?: string;
        description?: string;
        status?: ItemStatus;
        type?: ItemType;
        isBought?: boolean | string;
        isManufactured?: boolean | string;
        isSold?: boolean | string;
        eanNumber?: string;
        commodityCode?: string;
        stockUnit?: integer | string;
        volumeUnit?: integer | string;
        volume?: decimal | string;
        weightUnit?: integer | string;
        weight?: decimal | string;
        density?: decimal | string;
        image?: BinaryStream;
        allergens?: Partial<ItemAllergenInput>[];
        classifications?: Partial<ItemClassificationsInput>[];
        category?: integer | string;
        isStockManaged?: boolean | string;
        isPhantom?: boolean | string;
        isBomRevisionManaged?: boolean | string;
        bomRevisionSequenceNumber?: integer | string;
        isPotencyManagement?: boolean | string;
        isTraceabilityManagement?: boolean | string;
        supplierPrices?: Partial<ItemSupplierPriceInput>[];
        itemSites?: Partial<ItemSiteInput>[];
        suppliers?: Partial<ItemSupplierInput>[];
        customers?: Partial<ItemCustomerInput>[];
        purchaseUnit?: integer | string;
        purchaseUnitToStockUnitConversion?: decimal | string;
        serialNumberManagement?: SerialNumberManagement;
        serialNumberSequenceNumber?: integer | string;
        serialNumberUsage?: SerialNumberUsage;
        salesUnit?: integer | string;
        salesUnitToStockUnitConversion?: decimal | string;
        minimumSalesQuantity?: decimal | string;
        maximumSalesQuantity?: decimal | string;
        currency?: integer | string;
        basePrice?: decimal | string;
        minimumPrice?: decimal | string;
        customerPrices?: Partial<ItemCustomerPriceInput>[];
        capacity?: integer | string;
        landedCostItem?: LandedCostItemInput;
        itemTaxGroup?: integer | string;
        postingClass?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        id?: string;
        lotManagement?: LotManagement;
        lotSequenceNumber?: integer | string;
        isExpiryManaged?: boolean | string;
        analyticalData?: integer | string;
    }
    export interface ItemBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergenBinding>;
        classifications: ClientCollection<ItemClassificationsBinding>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPriceBinding>;
        itemSites: ClientCollection<ItemSiteBinding>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversion: string;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversion: string;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPriceBinding>;
        capacity: integer;
        landedCostItem: LandedCostItemBinding;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        inStockQuantity: string;
        inTransitStockQuantity: string;
        itemTaxGroup: ItemTaxGroup;
        postingClass: PostingClass;
        storedDimensions: any;
        storedAttributes: any;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
        analyticalData: AnalyticalData;
    }
    export interface ItemExtension$Lookups {
        postingClass: QueryOperation<PostingClass>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface ItemExtension$Operations {
        lookups(dataOrId: string | { data: ItemInput }): ItemExtension$Lookups;
    }
    export interface LegislationExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        defaultChartOfAccount: ChartOfAccount;
        countries: ClientCollection<Country>;
        companies: ClientCollection<Company>;
        taxes: ClientCollection<Tax>;
        doStockPosting: boolean;
        doNonAbsorbedPosting: boolean;
        doLandedCostGoodsInTransitPosting: boolean;
        doApPosting: boolean;
        doArPosting: boolean;
        doNonStockVariancePosting: boolean;
    }
    export interface LegislationInputExtension {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        isActive?: boolean | string;
        doStockPosting?: boolean | string;
        doNonAbsorbedPosting?: boolean | string;
        doLandedCostGoodsInTransitPosting?: boolean | string;
        doApPosting?: boolean | string;
        doArPosting?: boolean | string;
        doNonStockVariancePosting?: boolean | string;
    }
    export interface LegislationBindingExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        defaultChartOfAccount: ChartOfAccount;
        countries: ClientCollection<Country>;
        companies: ClientCollection<Company>;
        taxes: ClientCollection<Tax>;
        doStockPosting: boolean;
        doNonAbsorbedPosting: boolean;
        doLandedCostGoodsInTransitPosting: boolean;
        doApPosting: boolean;
        doArPosting: boolean;
        doNonStockVariancePosting: boolean;
    }
    export interface SiteExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
        siteGroups: ClientCollection<SiteGroup>;
        groupRoleSites: ClientCollection<GroupRoleSite>;
        hierarchyChartContent: TextStream;
        businessEntity: BusinessEntity;
        financialCurrency: Currency;
        isFinance: boolean;
        isPurchase: boolean;
        isInventory: boolean;
        isSales: boolean;
        isManufacturing: boolean;
        isProjectManagement: boolean;
        stockSite: Site;
        itemSites: ClientCollection<ItemSite>;
        primaryAddress: BusinessEntityAddress;
        financialSite: Site;
        isLocationManaged: boolean;
        defaultLocation: Location;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        timeZone: string;
        country: Country;
        taxIdNumber: string;
        siret: string;
        currency: Currency;
        defaultStockStatus: StockStatus;
        storedDimensions: string;
        storedAttributes: string;
        _syncTick: string;
        analyticalData: AnalyticalData;
    }
    export interface SiteInputExtension {
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        legalCompany?: integer | string;
        businessEntity?: BusinessEntityInput;
        isFinance?: boolean | string;
        isPurchase?: boolean | string;
        isInventory?: boolean | string;
        isSales?: boolean | string;
        isManufacturing?: boolean | string;
        isProjectManagement?: boolean | string;
        primaryAddress?: integer | string;
        financialSite?: integer | string;
        isLocationManaged?: boolean | string;
        defaultLocation?: integer | string;
        sequenceNumberId?: string;
        timeZone?: string;
        defaultStockStatus?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        _syncTick?: decimal | string;
        analyticalData?: integer | string;
    }
    export interface SiteBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
        siteGroups: ClientCollection<SiteGroup>;
        groupRoleSites: ClientCollection<GroupRoleSite>;
        hierarchyChartContent: TextStream;
        businessEntity: BusinessEntity;
        financialCurrency: Currency;
        isFinance: boolean;
        isPurchase: boolean;
        isInventory: boolean;
        isSales: boolean;
        isManufacturing: boolean;
        isProjectManagement: boolean;
        stockSite: Site;
        itemSites: ClientCollection<ItemSite>;
        primaryAddress: BusinessEntityAddress;
        financialSite: Site;
        isLocationManaged: boolean;
        defaultLocation: Location;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        timeZone: string;
        country: Country;
        taxIdNumber: string;
        siret: string;
        currency: Currency;
        defaultStockStatus: StockStatus;
        storedDimensions: any;
        storedAttributes: any;
        _syncTick: string;
        analyticalData: AnalyticalData;
    }
    export interface SiteExtension$Lookups {
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface SiteExtension$Operations {
        lookups(dataOrId: string | { data: SiteInput }): SiteExtension$Lookups;
    }
    export interface SupplierExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        supplierType: SupplierType;
        standardIndustrialClassification: StandardIndustrialClassification;
        paymentMethod: string;
        parent: Supplier;
        incoterm: Incoterm;
        certificates: ClientCollection<SupplierCertificate>;
        items: ClientCollection<ItemSupplier>;
        itemPrices: ClientCollection<ItemSupplierPrice>;
        billBySupplier: Supplier;
        billByAddress: BusinessEntityAddress;
        payToSupplier: Supplier;
        payToAddress: BusinessEntityAddress;
        returnToSupplier: Supplier;
        returnToAddress: BusinessEntityAddress;
        deliveryMode: DeliveryMode;
        datevId: integer;
        postingClass: PostingClass;
        storedDimensions: string;
        storedAttributes: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        analyticalData: AnalyticalData;
    }
    export interface SupplierInputExtension {
        isActive?: boolean | string;
        businessEntity?: BusinessEntityInput;
        primaryAddress?: integer | string;
        internalNote?: TextStream;
        paymentTerm?: integer | string;
        minimumOrderAmount?: decimal | string;
        category?: integer | string;
        supplierType?: SupplierType;
        standardIndustrialClassification?: integer | string;
        paymentMethod?: string;
        parent?: integer | string;
        incoterm?: integer | string;
        certificates?: Partial<SupplierCertificateInput>[];
        items?: Partial<ItemSupplierInput>[];
        billBySupplier?: integer | string;
        billByAddress?: integer | string;
        payToSupplier?: integer | string;
        payToAddress?: integer | string;
        returnToSupplier?: integer | string;
        returnToAddress?: integer | string;
        deliveryMode?: integer | string;
        datevId?: integer | string;
        postingClass?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        analyticalData?: integer | string;
    }
    export interface SupplierBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        supplierType: SupplierType;
        standardIndustrialClassification: StandardIndustrialClassification;
        paymentMethod: string;
        parent: Supplier;
        incoterm: Incoterm;
        certificates: ClientCollection<SupplierCertificateBinding>;
        items: ClientCollection<ItemSupplierBinding>;
        itemPrices: ClientCollection<ItemSupplierPrice>;
        billBySupplier: Supplier;
        billByAddress: BusinessEntityAddress;
        payToSupplier: Supplier;
        payToAddress: BusinessEntityAddress;
        returnToSupplier: Supplier;
        returnToAddress: BusinessEntityAddress;
        deliveryMode: DeliveryMode;
        datevId: integer;
        postingClass: PostingClass;
        storedDimensions: any;
        storedAttributes: any;
        _attachments: ClientCollection<AttachmentAssociation>;
        analyticalData: AnalyticalData;
    }
    export interface SupplierExtension$Lookups {
        postingClass: QueryOperation<PostingClass>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface SupplierExtension$Operations {
        lookups(dataOrId: string | { data: SupplierInput }): SupplierExtension$Lookups;
    }
    export interface TaxExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        name: string;
        primaryExternalReference: string;
        secondaryExternalReference: string;
        taxCategory: TaxCategory;
        country: Country;
        isReverseCharge: boolean;
        jurisdictionName: string;
        legalMention: string;
        taxValues: ClientCollection<TaxValue>;
        isUsed: boolean;
        type: TaxType;
        postingClass: PostingClass;
        postingKey: integer;
        legislation: Legislation;
    }
    export interface TaxInputExtension {
        _vendor?: integer | string;
        id?: string;
        isActive?: boolean | string;
        name?: string;
        primaryExternalReference?: string;
        secondaryExternalReference?: string;
        taxCategory?: integer | string;
        country?: integer | string;
        isReverseCharge?: boolean | string;
        jurisdictionName?: string;
        legalMention?: string;
        taxValues?: Partial<TaxValueInput>[];
        type?: TaxType;
        postingClass?: integer | string;
        postingKey?: integer | string;
        legislation?: integer | string;
    }
    export interface TaxBindingExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        name: string;
        primaryExternalReference: string;
        secondaryExternalReference: string;
        taxCategory: TaxCategory;
        country: Country;
        isReverseCharge: boolean;
        jurisdictionName: string;
        legalMention: string;
        taxValues: ClientCollection<TaxValueBinding>;
        isUsed: boolean;
        type: TaxType;
        postingClass: PostingClass;
        postingKey: integer;
        legislation: Legislation;
    }
    export interface TaxExtension$Lookups {
        postingClass: QueryOperation<PostingClass>;
    }
    export interface TaxExtension$Operations {
        lookups(dataOrId: string | { data: TaxInput }): TaxExtension$Lookups;
    }
    export interface Package {
        '@sage/xtrem-finance-data/Account': Account$Operations;
        '@sage/xtrem-finance-data/AccountAttributeType': AccountAttributeType$Operations;
        '@sage/xtrem-finance-data/AccountDimensionType': AccountDimensionType$Operations;
        '@sage/xtrem-finance-data/AccountingStaging': AccountingStaging$Operations;
        '@sage/xtrem-finance-data/AccountingStagingAmount': AccountingStagingAmount$Operations;
        '@sage/xtrem-finance-data/AccountingStagingDocumentTax': AccountingStagingDocumentTax$Operations;
        '@sage/xtrem-finance-data/AccountingStagingLineTax': AccountingStagingLineTax$Operations;
        '@sage/xtrem-finance-data/AccountsPayableInvoiceLineStaging': AccountsPayableInvoiceLineStaging$Operations;
        '@sage/xtrem-finance-data/AccountsReceivableInvoiceLineStaging': AccountsReceivableInvoiceLineStaging$Operations;
        '@sage/xtrem-finance-data/AnalyticalData': AnalyticalData$Operations;
        '@sage/xtrem-finance-data/Attribute': Attribute$Operations;
        '@sage/xtrem-finance-data/AttributeType': AttributeType$Operations;
        '@sage/xtrem-finance-data/BankAccount': BankAccount$Operations;
        '@sage/xtrem-finance-data/BaseFinanceDocument': BaseFinanceDocument$Operations;
        '@sage/xtrem-finance-data/BaseFinanceLineDimension': BaseFinanceLineDimension$Operations;
        '@sage/xtrem-finance-data/BaseOpenItem': BaseOpenItem$Operations;
        '@sage/xtrem-finance-data/BasePaymentDocument': BasePaymentDocument$Operations;
        '@sage/xtrem-finance-data/CloseReason': CloseReason$Operations;
        '@sage/xtrem-finance-data/CompanyAttributeType': CompanyAttributeType$Operations;
        '@sage/xtrem-finance-data/CompanyDefaultAttribute': CompanyDefaultAttribute$Operations;
        '@sage/xtrem-finance-data/CompanyDefaultDimension': CompanyDefaultDimension$Operations;
        '@sage/xtrem-finance-data/CompanyDimensionType': CompanyDimensionType$Operations;
        '@sage/xtrem-finance-data/DatevConfiguration': DatevConfiguration$Operations;
        '@sage/xtrem-finance-data/Dimension': Dimension$Operations;
        '@sage/xtrem-finance-data/DimensionDefinitionLevelAndDefault': DimensionDefinitionLevelAndDefault$Operations;
        '@sage/xtrem-finance-data/DimensionType': DimensionType$Operations;
        '@sage/xtrem-finance-data/FinanceTransaction': FinanceTransaction$Operations;
        '@sage/xtrem-finance-data/FinanceTransactionLine': FinanceTransactionLine$Operations;
        '@sage/xtrem-finance-data/Journal': Journal$Operations;
        '@sage/xtrem-finance-data/JournalEntryType': JournalEntryType$Operations;
        '@sage/xtrem-finance-data/JournalEntryTypeLine': JournalEntryTypeLine$Operations;
        '@sage/xtrem-finance-data/PaymentTracking': PaymentTracking$Operations;
        '@sage/xtrem-finance-data/PostingClass': PostingClass$Operations;
        '@sage/xtrem-finance-data/PostingClassDefinition': PostingClassDefinition$Operations;
        '@sage/xtrem-finance-data/PostingClassLine': PostingClassLine$Operations;
        '@sage/xtrem-finance-data/PostingClassLineDetail': PostingClassLineDetail$Operations;
        '@sage/xtrem-finance-data/BaseFinanceLine': BaseFinanceLine$Operations;
        '@sage/xtrem-finance-data/PaymentDocumentLine': PaymentDocumentLine$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremImportExport$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-finance-data-api' {
    export type * from '@sage/xtrem-finance-data-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type {
        BaseBusinessRelationBindingExtension,
        BaseBusinessRelationExtension,
        BaseBusinessRelationInputExtension,
        BaseDocumentBindingExtension,
        BaseDocumentExtension,
        BaseDocumentInputExtension,
        BaseDocumentItemLineBindingExtension,
        BaseDocumentItemLineExtension,
        BaseDocumentItemLineExtension$Lookups,
        BaseDocumentItemLineExtension$Mutations,
        BaseDocumentItemLineExtension$Operations,
        BaseDocumentItemLineInputExtension,
        BaseDocumentLineInquiryBindingExtension,
        BaseDocumentLineInquiryExtension,
        BaseDocumentLineInquiryExtension$Lookups,
        BaseDocumentLineInquiryExtension$Operations,
        BaseDocumentLineInquiryInputExtension,
        CustomerBindingExtension,
        CustomerExtension,
        CustomerExtension$Lookups,
        CustomerExtension$Operations,
        CustomerInputExtension,
        DetailedResourceBindingExtension,
        DetailedResourceExtension,
        DetailedResourceExtension$Lookups,
        DetailedResourceExtension$Operations,
        DetailedResourceInputExtension,
        ItemBindingExtension,
        ItemExtension,
        ItemExtension$Lookups,
        ItemExtension$Operations,
        ItemInputExtension,
        SupplierBindingExtension,
        SupplierExtension,
        SupplierExtension$Lookups,
        SupplierExtension$Operations,
        SupplierInputExtension,
    } from '@sage/xtrem-finance-data-api';
    export interface BaseBusinessRelation extends BaseBusinessRelationExtension {}
    export interface BaseBusinessRelationBinding extends BaseBusinessRelationBindingExtension {}
    export interface BaseBusinessRelationInput extends BaseBusinessRelationInputExtension {}
    export interface BaseDocument extends BaseDocumentExtension {}
    export interface BaseDocumentBinding extends BaseDocumentBindingExtension {}
    export interface BaseDocumentInput extends BaseDocumentInputExtension {}
    export interface BaseDocumentItemLine extends BaseDocumentItemLineExtension {}
    export interface BaseDocumentItemLineBinding extends BaseDocumentItemLineBindingExtension {}
    export interface BaseDocumentItemLineInput extends BaseDocumentItemLineInputExtension {}
    export interface BaseDocumentItemLine$Lookups extends BaseDocumentItemLineExtension$Lookups {}
    export interface BaseDocumentItemLine$Mutations extends BaseDocumentItemLineExtension$Mutations {}
    export interface BaseDocumentItemLine$Operations extends BaseDocumentItemLineExtension$Operations {}
    export interface BaseDocumentLineInquiry extends BaseDocumentLineInquiryExtension {}
    export interface BaseDocumentLineInquiryBinding extends BaseDocumentLineInquiryBindingExtension {}
    export interface BaseDocumentLineInquiryInput extends BaseDocumentLineInquiryInputExtension {}
    export interface BaseDocumentLineInquiry$Lookups extends BaseDocumentLineInquiryExtension$Lookups {}
    export interface BaseDocumentLineInquiry$Operations extends BaseDocumentLineInquiryExtension$Operations {}
    export interface Customer extends CustomerExtension {}
    export interface CustomerBinding extends CustomerBindingExtension {}
    export interface CustomerInput extends CustomerInputExtension {}
    export interface Customer$Lookups extends CustomerExtension$Lookups {}
    export interface Customer$Operations extends CustomerExtension$Operations {}
    export interface DetailedResource extends DetailedResourceExtension {}
    export interface DetailedResourceBinding extends DetailedResourceBindingExtension {}
    export interface DetailedResourceInput extends DetailedResourceInputExtension {}
    export interface DetailedResource$Lookups extends DetailedResourceExtension$Lookups {}
    export interface DetailedResource$Operations extends DetailedResourceExtension$Operations {}
    export interface Item extends ItemExtension {}
    export interface ItemBinding extends ItemBindingExtension {}
    export interface ItemInput extends ItemInputExtension {}
    export interface Item$Lookups extends ItemExtension$Lookups {}
    export interface Item$Operations extends ItemExtension$Operations {}
    export interface Supplier extends SupplierExtension {}
    export interface SupplierBinding extends SupplierBindingExtension {}
    export interface SupplierInput extends SupplierInputExtension {}
    export interface Supplier$Lookups extends SupplierExtension$Lookups {}
    export interface Supplier$Operations extends SupplierExtension$Operations {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type {
        CompanyBindingExtension,
        CompanyExtension,
        CompanyExtension$Lookups,
        CompanyExtension$Operations,
        CompanyInputExtension,
        SiteBindingExtension,
        SiteExtension,
        SiteExtension$Lookups,
        SiteExtension$Operations,
        SiteInputExtension,
    } from '@sage/xtrem-finance-data-api';
    export interface Company extends CompanyExtension {}
    export interface CompanyBinding extends CompanyBindingExtension {}
    export interface CompanyInput extends CompanyInputExtension {}
    export interface Company$Lookups extends CompanyExtension$Lookups {}
    export interface Company$Operations extends CompanyExtension$Operations {}
    export interface Site extends SiteExtension {}
    export interface SiteBinding extends SiteBindingExtension {}
    export interface SiteInput extends SiteInputExtension {}
    export interface Site$Lookups extends SiteExtension$Lookups {}
    export interface Site$Operations extends SiteExtension$Operations {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type {
        LegislationBindingExtension,
        LegislationExtension,
        LegislationInputExtension,
    } from '@sage/xtrem-finance-data-api';
    export interface Legislation extends LegislationExtension {}
    export interface LegislationBinding extends LegislationBindingExtension {}
    export interface LegislationInput extends LegislationInputExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type {
        TaxBindingExtension,
        TaxExtension,
        TaxExtension$Lookups,
        TaxExtension$Operations,
        TaxInputExtension,
    } from '@sage/xtrem-finance-data-api';
    export interface Tax extends TaxExtension {}
    export interface TaxBinding extends TaxBindingExtension {}
    export interface TaxInput extends TaxInputExtension {}
    export interface Tax$Lookups extends TaxExtension$Lookups {}
    export interface Tax$Operations extends TaxExtension$Operations {}
}
