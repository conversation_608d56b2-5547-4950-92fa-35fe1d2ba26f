"id";"tax_engine";"do_stock_posting";"do_wip_posting";"do_non_absorbed_posting";"datev_consultant_number";"datev_customer_number"
"US001";"avalaraAvaTax";"Y";"Y";"Y";;
"US002";"avalaraAvaTax";"Y";"Y";"Y";;
"US003";"avalaraAvaTax";"Y";"Y";"Y";;
"US004";"avalaraAvaTax";"Y";"Y";"Y";;
"US005";"avalaraAvaTax";"Y";"Y";"Y";;
"US006";"avalaraAvaTax";"Y";"Y";"Y";;
"US007";"avalaraAvaTax";"Y";"Y";"Y";;
"US008";"avalaraAvaTax";"Y";"Y";"Y";;
"US009";"avalaraAvaTax";"Y";"Y";"Y";;
"US010";"avalaraAvaTax";"Y";"Y";"Y";;
"US011";"avalaraAvaTax";"Y";"Y";"Y";;
"US012";"avalaraAvaTax";"Y";"Y";"Y";;
"S1";"genericTaxCalculation";"Y";"N";"N";;
"S2";"genericTaxCalculation";"N";"N";"N";;
"S3";"genericTaxCalculation";"N";"N";"N";;
"S4";"genericTaxCalculation";"N";"N";"N";;
"US017";"avalaraAvaTax";"Y";"Y";"Y";;
"500";"genericTaxCalculation";"Y";"Y";"Y";;
"700";"genericTaxCalculation";"Y";"Y";"Y";;
"CAC01";"genericTaxCalculation";"Y";"Y";"N";;
"RX001";"genericTaxCalculation";"Y";"Y";"Y";;
"RX002";"genericTaxCalculation";"Y";"Y";"Y";;
"RX003";"genericTaxCalculation";"Y";"Y";"Y";;
"RX004";"genericTaxCalculation";"Y";"Y";"Y";;
"RX005";"genericTaxCalculation";"Y";"Y";"Y";;
"RX006";"genericTaxCalculation";"Y";"Y";"Y";;
"UT01";"genericTaxCalculation";"Y";"Y";"Y";;
"UT02";"genericTaxCalculation";"Y";"N";"N";"1640";"1"
"DE02";"genericTaxCalculation";"Y";"Y";"N";;
"RM_USCPY_01";"genericTaxCalculation";"Y";"Y";"Y";;
