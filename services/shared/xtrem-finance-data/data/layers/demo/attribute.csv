"id";"attribute_type";"attribute_restricted_to_id";"is_active";"name";"attribute_restricted_to";"site";"item"
"10000";"project";;"Y";"General Overhead";;;
"10025";"project";;"Y";"General Overhead-Current";;;
"10076";"project";;"Y";"PPM Project";;"501";
"1000";"employee";;"Y";"<PERSON>, Emma";;;
"1001";"employee";;"Y";"<PERSON>, Tom";;;
"1002";"employee";;"Y";"<PERSON>, Chuck";;;
"1003";"employee";;"Y";"Te<PERSON>, <PERSON>";;;
"1004";"employee";;"Y";"Evans, Chelsea";;;
"1005";"employee";;"Y";"<PERSON>, <PERSON>";;;
"1006";"employee";;"Y";"<PERSON>, <PERSON>";;;
"1007";"employee";;"Y";"<PERSON><PERSON><PERSON>, <PERSON>";;;
"1008";"employee";;"Y";"<PERSON>, <PERSON>";;;
"1009";"employee";;"Y";"<PERSON>, <PERSON>";;;
"1010";"employee";;"Y";"<PERSON>, <PERSON>";;;
"100";"task";"10000";"Y";"Kick off (10000)";"10000|project|";;
"200";"task";"10000";"Y";"Analysis (10000)";"10000|project|";;
"300";"task";"10000";"Y";"Development (10000)";"10000|project|";;"PUR-SERVICE"
"400";"task";"10000";"Y";"QA (10000)";"10000|project|";;"PUR-SERVICE"
"500";"task";"10000";"Y";"Sign off (10000)";"10000|project|";;"PUR-SERVICE"
"100";"task";"10025";"Y";"Kick off (10025)";"10025|project|";;
"200";"task";"10025";"Y";"Analysis (10025)";"10025|project|";;
"300";"task";"10025";"Y";"Development (10025)";"10025|project|";;"PUR-SERVICE"
"400";"task";"10025";"Y";"QA (10025)";"10025|project|";;"PUR-SERVICE"
"500";"task";"10025";"Y";"Sign off (10025)";"10025|project|";;"PUR-SERVICE"
"2001";"employee";;"Y";"Adams, Chloe";;;
"2002";"employee";;"Y";"Martinez, Ella";;;
"2003";"employee";;"Y";"Roberts, Tyler";;;
"2004";"employee";;"Y";"Mitchell, Christopher";;;
