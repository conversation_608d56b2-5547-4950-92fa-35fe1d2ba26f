{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "!chartOfAccount", "locale": "", "dataType": "reference", "isCustom": false, "description": "chart of account (#id)"}, {"_id": "20", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "30", "path": "*name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "40", "path": "*isDirectEntryForbidden", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is direct entry forbidden (false/true)"}, {"_id": "50", "path": "isControl", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is control (false/true)"}, {"_id": "60", "path": "taxManagement", "locale": "", "dataType": "enum(other,includingTax,excludingTax,tax,reverseCharge)", "isCustom": false, "description": "tax management"}, {"_id": "70", "path": "frpKey", "locale": "", "dataType": "string", "isCustom": false, "description": "frp key"}, {"_id": "80", "path": "uIntacctId", "locale": "", "dataType": "string", "isCustom": false, "description": "u intacct id"}, {"_id": "90", "path": "recordNo", "locale": "", "dataType": "integer", "isCustom": false, "description": "record no"}, {"_id": "100", "path": "#attributeTypes", "locale": "", "dataType": "collection", "isCustom": false, "description": "attribute types"}, {"_id": "110", "path": "*attributeType", "locale": "", "dataType": "reference", "isCustom": false, "description": "attribute type (#id)"}, {"_id": "120", "path": "*isRequired", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is required (false/true)"}, {"_id": "130", "path": "#dimensionTypes", "locale": "", "dataType": "collection", "isCustom": false, "description": "dimension types"}, {"_id": "140", "path": "*dimensionType", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension type (#docProperty)"}, {"_id": "150", "path": "*isRequired#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is required (false/true)"}]}