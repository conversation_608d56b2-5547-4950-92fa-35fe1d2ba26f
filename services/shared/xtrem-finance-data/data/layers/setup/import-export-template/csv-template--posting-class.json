{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "*type", "locale": "", "dataType": "enum(item,supplier,customer,tax,company,header,line,resource)", "isCustom": false, "description": "type"}, {"_id": "20", "path": "*name", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "30", "path": "isDetailed", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is detailed (true/false)"}, {"_id": "40", "path": "isStockItemAllowed", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is stock item allowed (false/true)"}, {"_id": "50", "path": "isNonStockItemAllowed", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is non stock item allowed (false/true)"}, {"_id": "60", "path": "isServiceItemAllowed", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is service item allowed (false/true)"}, {"_id": "70", "path": "isLandedCostItemAllowed", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is landed cost item allowed (false/true)"}, {"_id": "80", "path": "#lines", "locale": "", "dataType": "collection", "isCustom": false, "description": "lines"}, {"_id": "90", "path": "chartOfAccount", "locale": "", "dataType": "reference", "isCustom": false, "description": "chart of account (#id)"}, {"_id": "100", "path": "*definition", "locale": "", "dataType": "reference", "isCustom": false, "description": "definition (#legislation|postingClassType|id)"}, {"_id": "110", "path": "*account", "locale": "", "dataType": "reference", "isCustom": false, "description": "account (#id|chartOfAccount)"}]}