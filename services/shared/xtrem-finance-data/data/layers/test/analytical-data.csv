"_id";"financial_site";"business_site";"stock_site";"supplier";"customer";"project";"task";"item";"dimension_01";"dimension_02";"dimension_03";"dimension_04";"dimension_05"
"1";;;;;;"AttPROJ|project|";"Task1|task|AttPROJ";;"300|dimensionType01";"CHANNELVALUE1|dimensionType02";"MANUFACTURING|dimensionType03";"DIMTYPE2VALUE2|dimensionType04";"COMMERCIAL|dimensionType05"
"2";;;;;;"AttPROJ|project|";"Task1|task|AttPROJ";;;;;;
"3";;;;;;;;;;;"WAREHOUSE|dimensionType03";;"COMMERCIAL|dimensionType05"
"4";;;;;;;;;;;;;
"5";;;;;;"AttPROJ|project|";"Task1|task|AttPROJ";;"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"6";;;;;;"AttPROJ|project|";;;"300|dimensionType01";"CHANNELVALUE1|dimensionType02";"MANUFACTURING|dimensionType03";"DIMTYPE2VALUE2|dimensionType04";"RETAIL|dimensionType05"
"7";;;;;;"AttPROJ|project|";;;"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"9";;;;;;"AttPROJ2|project|";;;"300|dimensionType01";;"MANUFACTURING|dimensionType03";"DIMTYPE2VALUE2|dimensionType04";
"11";;;;;;"AttPROJ|project|";;;;;"MANUFACTURING|dimensionType03";;"RETAIL|dimensionType05"
"15";"700";"700";"700";"700";;"AttPROJ2|project|";;"Muesli";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";"DIMTYPE1VALUE1|dimensionType03";"DIMTYPE2VALUE2|dimensionType04";"RETAIL|dimensionType05"
"16";"DEP1-S01";"DEP1-S01";"DEP1-S01";;;;;"STOAVC";;;;;
"17";"RX001";;"RX001";;;;;"HC001";;;;;
"18";"US001";"US001";"US001";"US017";;;;"STOAVC";;;;;
"19";"US001";"US001";"US001";"US016";;"AttPROJ|project|";;"STOAVC";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"20";"US001";"US001";"US001";"US016";;"AttPROJ|project|";;"STOFIFO";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"21";"US001";"US001";;"US017";;;;"LandedCost001";"300|dimensionType01";;;;
"22";"US001";"US001";;"US016";;;;"LandedCost004";;;;;
"23";"US001";;"US001";"LECLERC";;;;"RSV9";;;;;
"24";"US001";;"US001";"LECLERC";;;;"STOAVC";;;;;
"25";"US001";;"US001";;;;;"Muesli";;;;;
"30";"US001";;"US001";;;;;"5467";"300|dimensionType01";;"WAREHOUSE|dimensionType03";;"RETAIL|dimensionType05"
"31";"US001";;"US002";"US018";;"AttPROJ|project|";"Task1|task|AttPROJ";"5467";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";"WAREHOUSE|dimensionType03";;"RETAIL|dimensionType05"
"32";"US001";;"US002";"US018";;"AttPROJ|project|";;"5467";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";"WAREHOUSE|dimensionType03";;"RETAIL|dimensionType05"
"33";"US001";;"US002";"US018";;"AttPROJ|project|";;"5467";;;"WAREHOUSE|dimensionType03";;"RETAIL|dimensionType05"
"34";"US001";;"US002";"US018";;"AttPROJ|project|";;"7625";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";"MANUFACTURING|dimensionType03";;"COMMERCIAL|dimensionType05"
"35";"US001";;"US002";"US018";;"AttPROJ|project|";;"7625";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";"WAREHOUSE|dimensionType03";;"RETAIL|dimensionType05"
"36";"US001";;"US002";"US018";;"AttPROJ|project|";;"7625";;;"WAREHOUSE|dimensionType03";;"RETAIL|dimensionType05"
"37";;;;;;"AttPROJ|project|";;;;;"DIMTYPE1VALUE1|dimensionType03";"DIMTYPE2VALUE2|dimensionType04";
"38";;;;;;"AttPROJ|project|";;;;;"DIMTYPE1VALUE2|dimensionType03";"DIMTYPE2VALUE2|dimensionType04";
"39";;;;;;"AttPROJ|project|";;;;;"DIMTYPE1VALUE2|dimensionType03";;
"40";;;;;;"AttPROJ2|project|";;;;;"DIMTYPE1VALUE1|dimensionType03";"DIMTYPE2VALUE2|dimensionType04";
"42";;;;;;;;;"300|dimensionType01";;;;
"46";;;;;;"AttPROJ|project|";;;;;;"DIMTYPE2VALUE2|dimensionType04";
"56";;;;;;"AttPROJ|project|";;;"300|dimensionType01";;;;
"62";;;;;;"AttPROJ2|project|";;;;;"100|dimensionType03";;
"63";;;;;;;;;"300|dimensionType01";;"DIMTYPE1VALUE1|dimensionType03";;
"67";;;;;;"AttPROJ|project|";;;;"CHANNELVALUE1|dimensionType02";;;
"78";"700";"700";"700";"700";;"AttPROJ2|project|";"Task2|task|AttPROJ2";"Muesli";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";"DIMTYPE1VALUE1|dimensionType03";"DIMTYPE2VALUE2|dimensionType04";"RETAIL|dimensionType05"
"79";"ETS2-S02";;"ETS2-S02";"LECLERC";;"AttPROJ|project|";;"NonStockManagedItem";;;"MANUFACTURING|dimensionType03";;"COMMERCIAL|dimensionType05"
"82";"US001";;"US001";"500";;"AttPROJ|project|";;"5467";;;"WAREHOUSE|dimensionType03";;"RETAIL|dimensionType05"
"83";"US001";;"US001";"500";;"AttPROJ|project|";;"7625";;;"MANUFACTURING|dimensionType03";;"COMMERCIAL|dimensionType05"
"84";"US001";;"US001";"500";;"AttPROJ|project|";;"7625";;;"WAREHOUSE|dimensionType03";;"RETAIL|dimensionType05"
"87";"ETS2-S02";"ETS2-S02";"ETS2-S02";;"US017";;;"Consulting01";;;;;
"88";"ETS2-S02";"ETS2-S02";"ETS2-S02";;"US017";;;"SalesItem81";;;;;
"90";"US001";;"US002";"US018";;"AttPROJ|project|";"Task1|task|AttPROJ";"7625";;;"MANUFACTURING|dimensionType03";;"COMMERCIAL|dimensionType05"
"91";"US001";;"US002";"US018";;"AttPROJ|project|";;"7625";;;"MANUFACTURING|dimensionType03";;"COMMERCIAL|dimensionType05"
"97";;;;;;"AttPROJ2|project|";;;"300|dimensionType01";"CHANNELVALUE1|dimensionType02";"DIMTYPE1VALUE1|dimensionType03";"DIMTYPE2VALUE2|dimensionType04";"RETAIL|dimensionType05"
"107";;;;;;"AttPROJ|project|";;;;;;;
"108";;;;;;"AttPROJ|project|";"Task1|task|AttPROJ";;;"CHANNELVALUE1|dimensionType02";;;
"109";"DES01";"DES01";"DES01";;"DECUS01";;;"NonStockManagedItem";;;;;
"110";"DES01";"DES01";;"DESUP01";;;;"NonStockManagedItem";;;;;
"111";"DES01";;"DES01";;;"AttPROJ|project|";"Task1|task|AttPROJ";"Milk";"300|dimensionType01";"300|dimensionType02";"MANUFACTURING|dimensionType03";;
"112";"DES01";;"DES01";;;;;"Milk";;;;;
"113";;;;;;"AttPROJ|project|";;;"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;"DIMTYPE2VALUE2|dimensionType04";
"114";;;;;;"AttPROJ|project|";;;"300|dimensionType01";"CHANNELVALUE1|dimensionType02";"WAREHOUSE|dimensionType03";"DIMTYPE2VALUE2|dimensionType04";"COMMERCIAL|dimensionType05"
"115";;;;;;;;;"300|dimensionType01";;"WAREHOUSE|dimensionType03";;"COMMERCIAL|dimensionType05"
"116";"US001";"US001";"US001";;;"AttPROJ|project|";"Task1|task|AttPROJ";;"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"117";"US001";"US001";"US001";;;"AttPROJ|project|";"Task1|task|AttPROJ";"Chair";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"118";"US001";;"US001";;;"AttPROJ|project|";"Task1|task|AttPROJ";"Muesli";;"CHANNELVALUE1|dimensionType02";;;
"119";"CAS01";;"CAS01";;;;;"Milk";;;;;
"120";"CAS01";;"CAS01";"CAS01";"CAS02";;;"Milk";;;;;
"121";"DES01";"DES01";"DES01";;"DECUS01";;;"Milk";;;;;
"122";"CAS01";;"CAS01";;;;;"STOSTD";;;;;
"123";"CAS01";;"CAS01";;;;;"STOAVC";;;;;
"124";"CAS01";;"CAS02";;;;;"STOSTD";;;;;
"125";"CAS01";;"CAS02";;;;;"STOAVC";;;;;
"126";;;;;;;;;;;"WAREHOUSE|dimensionType03";;
"127";"US004";;"US004";;;;;"SIMPLE_ITEM";;;;;
"128";"US004";"US004";"US004";;"US019";;;"SIMPLE_ITEM";;;;;
"129";"700";"700";"700";;"US019";;;"Service001";;;;;
"130";"US001";"US001";"US001";"US016";;"AttPROJ|project|";"Task1|task|AttPROJ";"RM_ITEM_001";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"131";"US001";"US001";;"US016";;"AttPROJ|project|";"Task1|task|AttPROJ";"LandedCost003";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"132";"US001";"US001";"US001";"US016";;"AttPROJ|project|";"Task1|task|AttPROJ";"RM_ITEM_002";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"133";"US001";;"US001";;;"AttPROJ|project|";"Task1|task|AttPROJ";"RM_ITEM_004";;"CHANNELVALUE1|dimensionType02";;;
"134";"US001";;"US001";;;"AttPROJ|project|";"Task1|task|AttPROJ";"RM_ITEM_003";;"CHANNELVALUE1|dimensionType02";;;
"135";"US001";;"US001";;;"AttPROJ|project|";"Task1|task|AttPROJ";;"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"136";"US001";;"US001";;;"AttPROJ|project|";"Task1|task|AttPROJ";"RM_ITEM_003";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"137";"US001";;"US001";;;"AttPROJ|project|";"Task1|task|AttPROJ";"RM_ITEM_004";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"138";"US001";;"US001";;;"AttPROJ|project|";"Task1|task|AttPROJ";"RM_ITEM_005";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"139";"US001";;"US001";;;"AttPROJ|project|";"Task1|task|AttPROJ";"AI_ITEM_001";;"CHANNELVALUE1|dimensionType02";;;
"140";"DEP1-S01";;"DEP1-S01";;;;;"AI_ITEM_001";"300|dimensionType01";;;;
"141";"DEP1-S01";"DEP1-S01";"DEP1-S01";;"DECUS01";"AttPROJ|project|";;"AI_ITEM_001";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;"DIMTYPE2VALUE2|dimensionType04";
"142";"DEP1-S01";"DEP1-S01";"DEP1-S01";"DESUP01";;"AttPROJ|project|";;"AI_ITEM_001";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;"DIMTYPE2VALUE2|dimensionType04";
"143";"US001";"US001";"US001";;"CUST07";"AttPROJ|project|";;"AI_ITEM_001";;;;;
"144";"DES01";"DES01";;"US017";;;;"NonStockManagedItem";;;;;
"145";"DES01";"DES01";"DES01";"US017";;;;"NonStockManagedItem";;;;;
"146";"CAS01";;"CAS01";;;;;"STOFIFO";;;;;
"147";"CAS01";;"CAS02";"CAS01";"CAS02";;;"Milk";;;;;
"148";"US001";"US001";;"RX005";;"AttPROJ|project|";"Task1|task|AttPROJ";"Service001";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"149";"US001";"US001";;"US016";;"AttPROJ|project|";"Task1|task|AttPROJ";"Service001";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"150";"US001";"US001";;"US016";;"AttPROJ|project|";"Task1|task|AttPROJ";"NonStockManagedItem";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"151";"US001";"US001";"US001";"US016";;"AttPROJ|project|";"Task1|task|AttPROJ";"Service001";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"152";"US001";"US001";"US001";"US016";;"AttPROJ|project|";"Task1|task|AttPROJ";"NonStockManagedItem";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"153";"US001";"US001";;"LECLERC";;"AttPROJ|project|";"Task1|task|AttPROJ";"Service001";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"154";;;;;;"AttPROJ|project|";"Task1|task|AttPROJ";;;"300|dimensionType02";;;
"155";"US001";"US001";"US001";;"RX005";"AttPROJ|project|";;"RM_ITEM_001";;;;;
"156";"RM_USFCY_01";;"RM_USFCY_01";;;;;"RM_ITEM_001";;;;;
"157";"RM_USFCY_01";;"RM_USFCY_01";;;;;"RM_ITEM_003";;;;;
"158";"RM_USFCY_01";;"RM_USFCY_01";;;;;"RM_ITEM_005";;;;;
"159";"RM_USFCY_01";;"RM_USFCY_01";;;;;"RM_ITEM_006";;;;;
"160";"RM_USFCY_01";"RM_USFCY_01";"RM_USFCY_01";;"US019";;;"RM_ITEM_001";;;;;
"161";"RM_USFCY_01";"RM_USFCY_01";"RM_USFCY_01";;"US019";;;"RM_ITEM_006";;;;;
"162";"RM_USFCY_01";"RM_USFCY_01";"RM_USFCY_01";"US_SUP_01";;;;"RM_ITEM_006";;;;;
"163";"RM_USFCY_01";"RM_USFCY_01";"RM_USFCY_01";"US_SUP_01";;;;"RM_ITEM_001";;;;;
"164";"CAS01";"CAS01";;"US016";;;;"LandedCost003";;;;;
"165";;;;;;;;;"300|dimensionType01";;"MANUFACTURING|dimensionType03";;"RETAIL|dimensionType05"
"166";"US001";"US001";"US001";"LECLERC";;"AttPROJ|project|";"Task1|task|AttPROJ";"SamA20";"300|dimensionType01";"CHANNELVALUE1|dimensionType02";;;
"167";"DES01";"DES01";;"DESUP01";;;;"LandedCost001";;;;;
