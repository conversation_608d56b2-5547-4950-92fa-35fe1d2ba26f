@xtrem_finance_data
Feature: smoke-test-data

    #Case without navigation panel full width
    Scenario Outline: std \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed
        Examples:
            | Page                                                             | Title                                  |
            | @sage/xtrem-finance-data/AttributeType/eyJfaWQiOiIxIn0=          | Attribute type Business site           |
            | @sage/xtrem-finance-data/DimensionType/eyJfaWQiOiIyIn0=          | Dimension type Channel                 |
            | @sage/xtrem-finance-data/JournalEntryType/eyJfaWQiOiI0In0=       | Journal entry type Stock receipt       |
            | @sage/xtrem-finance-data/PostingClassDefinition/eyJfaWQiOiIyNiJ9 | Posting class definition Item          |
            # Opening pages having extensions in the current package
            | @sage/xtrem-master-data/Customer/eyJfaWQiOiIxOCJ9                | Customer Dépot de TOULOUSE - Sud Ouest |
            | @sage/xtrem-master-data/Supplier/eyJfaWQiOiI0In0=                | Supplier Siège social S01 PARIS        |
            | @sage/xtrem-master-data/Item/eyJfaWQiOiI0MyJ9                    | Item A bottle of milk                  |
