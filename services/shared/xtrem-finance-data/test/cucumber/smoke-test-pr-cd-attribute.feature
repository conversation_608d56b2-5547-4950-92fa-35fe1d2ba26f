@xtrem_master_data
Feature: smoke-test-pr-cd-attribute

    Scenario: Create Attribute
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Attribute"
        Then the "Attributes" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in a text field
        And the user selects the "name" labelled text field on the main page
        And the user writes "AttPROJ10 name" in the text field
        And the user selects the "id" bound text field on the main page
        And the user writes "AttPROJ10" in the text field
        #Click Save Crud Button
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: Attribute delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Attribute"
        Then the "Attributes" titled page is displayed
        #Search record on navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "id" bound column in the table field with value "AttPROJ10"
        And the user selects the row 1 of the table field
        And the user clicks the "ID" labelled nested field of the selected row in the table field
        Then the "Attribute AttPROJ10 name" titled page is displayed
        #Click Delete Crud Button
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
