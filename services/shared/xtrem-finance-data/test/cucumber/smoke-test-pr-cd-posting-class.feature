@xtrem_finance_data
Feature:  smoke-test-pr-cd-posting-class

    Scenario: Posting class creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/PostingClass"
        Then the "Posting classes" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Raw materials" in the text field
        And the user selects the "Item type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Stock item" in the multi dropdown field
        #Adding a line to grid
        And the user selects the "Lines" labelled nested grid field on the main page
        And the user clicks the "Add line" labelled action of the nested grid field
        And the user selects the "Lines" labelled nested grid field on the main page
        And the user selects row with text "" in column with header "Chart of accounts" in the nested grid field
        And the user writes "US chart of accounts" in the "Chart of accounts" labelled nested reference field of the selected row in the nested grid field
        And the user stores the value of the "Chart of accounts" labelled nested reference field of the selected row in the nested grid field with key "US chart of accounts"
        And the user writes "Stock" in the "Account type name" labelled nested reference field of the selected row in the nested grid field
        And the user stores the value of the "Account type name" labelled nested reference field of the selected row in the nested grid field with key "Stock"
        And the user writes "Accounts Payable" in the "Account" labelled nested reference field of the selected row in the nested grid field
        And the user stores the value of the "Account" labelled nested reference field of the selected row in the nested grid field with key "Accounts Payable"
        #Click Save Crud Button
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: Posting class deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/PostingClass"
        Then the "Posting classes" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "type" bound nested field of the selected row in the table field
        #Click Create business action
        #When the user opens the navigation panel
        #Search record on navigation panel
        And the user searches for "Raw materials" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #Click Delete Crud Button
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
