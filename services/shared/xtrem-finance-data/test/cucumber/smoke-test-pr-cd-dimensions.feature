@xtrem_finance_data
Feature: smoke-test-pr-cd-dimensions

    Scenario: Create Dimension
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Dimension"
        Then the "Dimensions" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        # Fill in a some fields
        And the user selects the "id" bound text field on the main page
        And the user writes "SMOKETEST" in the text field
        And the user selects the "name" labelled text field on the main page
        And the user writes "Smoke test" in the text field
        # Click Save CRUD Button
        And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: Delete Dimension
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Dimension"
        Then the "Dimensions" titled page is displayed
        # Search for the record on navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "id" bound column in the table field with value "SMOKETEST"
        And the user selects the row 1 of the table field
        And the user clicks the "ID" labelled nested field of the selected row in the table field
        Then the "Dimension Smoke test" titled page is displayed
        # Click Delete CRUD Button
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        # Verify Deletion
        Then a toast containing text "Record deleted" is displayed
