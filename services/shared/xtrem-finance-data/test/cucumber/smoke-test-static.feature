@xtrem_finance_data
Feature: smoke-test-static

    #Case with navigation panel full width without create business action
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                            | Title                     |
            | @sage/xtrem-finance-data/AttributeType          | Attribute types           |
            | @sage/xtrem-finance-data/JournalEntryType       | Journal entry types       |
            | @sage/xtrem-finance-data/PostingClassDefinition | Posting class definitions |
            | @sage/xtrem-finance-data/Dimension              | Dimensions                |
            | @sage/xtrem-finance-data/DimensionType          | Dimension types           |
            | @sage/xtrem-finance-data/Attribute              | Attributes                |
            | @sage/xtrem-finance-data/PostingClass           | Posting classes           |
            | @sage/xtrem-finance-data/Journal                | Journals                  |
            | @sage/xtrem-finance-data/Account                | Accounts                  |

    #Case with navigation panel full width without create business action
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                             | Title                   |
            | @sage/xtrem-tax/TaxZone          | Tax zones               |
            | @sage/xtrem-tax/TaxDetermination | Tax determination rules |
            | @sage/xtrem-tax/CountryGroup     | Country groups          |
            | @sage/xtrem-tax/ItemTaxGroup     | Item tax groups         |
            | @sage/xtrem-tax/Tax              | Taxes                   |
            | @sage/xtrem-tax/TaxSolution      | Tax solutions           |
            | @sage/xtrem-tax/TaxCategory      | Tax categories          |

    #Case with navigation panel full width
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                    | NavigationPanelTitle | Title            |
            | @sage/xtrem-finance-data/Account        | Accounts             | Account          |
            | @sage/xtrem-finance-data/Attribute      | Attributes           | Attribute        |
            | @sage/xtrem-finance-data/Dimension      | Dimensions           | Dimension        |
            | @sage/xtrem-finance-data/DimensionType  | Dimension types      | Dimension type   |
            | @sage/xtrem-finance-data/Journal        | Journals             | Journal          |
            | @sage/xtrem-finance-data/PostingClass   | Posting classes      | Posting class    |
            # Opening pages having extensions in the current package
            | @sage/xtrem-master-data/Company         | Companies            | Company          |
            | @sage/xtrem-master-data/Item            | Items                | Item             |
            | @sage/xtrem-master-data/LaborResource   | Labor resources      | Labor resource   |
            | @sage/xtrem-master-data/MachineResource | Machine resources    | Machine resource |
            | @sage/xtrem-master-data/ToolResource    | Tool resources       | Tool resource    |
            | @sage/xtrem-tax/Tax                     | Taxes                | Tax              |

    #Case with navigation panel full width with multi action button
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled multi action button on the navigation panel
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                             | NavigationPanelTitle | Title    |
            # Opening pages having extensions in the current package
            | @sage/xtrem-master-data/Customer | Customers            | Customer |
            | @sage/xtrem-master-data/Supplier | Suppliers            | Supplier |

    # Positive test to ensure that the error dialog is definitely exist and open
    Scenario: sts \ xtrem-finance-data \ desktop \ check an error dialog appears when the page does not load
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Wazup"
        Then an error dialog appears on the main page
