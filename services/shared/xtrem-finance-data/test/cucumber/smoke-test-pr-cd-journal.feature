@xtrem_finance_data
Feature:  smoke-test-pr-cd-journal

    Scenario: Journal creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Journal"
        Then the "Journals" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Add ID
        And the user selects the "ID *" labelled text field on the main page
        And the user writes "TA" in the text field
        #Add Name
        And the user selects the "Name *" labelled text field on the main page
        And the user writes "Test adding" in the text field
        #Select the legislation
        And the user selects the "Legislation *" labelled reference field on the main page
        And the user writes "Fr" in the reference field
        And the user selects "France" in the reference field
        #Select the sequence number
        And the user selects the "Sequence number" labelled reference field on the main page
        And the user writes "Sal" in the reference field
        And the user selects "Sales" in the reference field
        #Click Save Crud Button
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: Journal deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Journal"
        Then the "Journals" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "id" bound nested field of the selected row in the table field
        #Click Create business action
        #When the user opens the navigation panel
        #Search record on navigation panel
        And the user searches for "Test adding" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        #Click Delete Crud Button
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
