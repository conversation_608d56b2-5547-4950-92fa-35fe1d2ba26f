@xtrem_finance_data
Feature: smoke-test-pr-cd-dimension-type

    Scenario: Create Dimension Type
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/DimensionType"
        Then the "Dimension types" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        # Fill in a some fields
        And the user selects the "name" labelled text field on the main page
        And the user writes "SMOKETEST" in the text field
        And the user selects the "Document property" labelled select field on the main page
        And the user writes "Dimension type 09" in the select field
        And the user selects "Dimension type 09" in the select field
        # Click Save CRUD Button
        And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: Delete Dimension type
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/DimensionType"
        Then the "Dimension types" titled page is displayed
        # Search for the record on navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "SMOKETEST"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Then the "Dimension type SMOKETEST" titled page is displayed
        # Click Delete CRUD Button
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        # Verify Deletion
        Then a toast containing text "Record deleted" is displayed
