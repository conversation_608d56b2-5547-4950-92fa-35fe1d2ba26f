mutation {
    xtremFinanceData {
        account {
            create(
                data: {
                    id: "1000"
                    chartOfAccount: "#TEST_PT_DEFAULT"
                    isActive: true
                    name: "PT Account 1000"
                    isDirectEntryForbidden: false
                    isControl: false
                    taxManagement: "tax"
                    attributeTypes: { account: "_id:-1", attributeType: "#financialSite", isRequired: true }
                    dimensionTypes: { account: "_id:-1", dimensionType: "#dimensionType03", isRequired: true }
                }
            ) {
                id
                chartOfAccount {
                    isActive
                    name
                    legislation {
                        id
                        name
                        isActive
                    }
                }
                isActive
                name
                isDirectEntryForbidden
                attributeTypes {
                    query(filter: "{ account : {id : '3'}}") {
                        edges {
                            node {
                                attributeType {
                                    id
                                    name
                                    isActive
                                }
                                isRequired
                            }
                        }
                    }
                }
                dimensionTypes {
                    query(filter: "{ account : {id : '3'}}") {
                        edges {
                            node {
                                isRequired
                                dimensionType {
                                    name
                                    isActive
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
