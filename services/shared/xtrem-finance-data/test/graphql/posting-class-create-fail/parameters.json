{"Posting class create test fail 1": {"input": {"properties": {"type": "item", "isDetailed": true, "name": "Posting class create test", "lines": [{"definition": "#US|item|GoodsReceivedNotInvoiced", "account": "#1|TEST_US_DEFAULT", "chartOfAccount": "#TEST_US_DEFAULT"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClass", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "At least one line allows stock items.", "path": [], "severity": 3}, {"severity": 3, "path": ["lines", "-**********", "definition"], "message": "If the posting class is detailed, only posting class definitions with the Detailed checkbox selected are allowed. If the posting class is not detailed, only posting class definitions without the Detailed checkbox selected are allowed."}]}}]}}, "Posting class create test fail 2": {"input": {"properties": {"type": "item", "name": "Semi finished goods", "isDetailed": true, "lines": [{"definition": "#US|item|GoodsReceivedNotInvoiced", "account": "#1|TEST_US_DEFAULT", "chartOfAccount": "#TEST_US_DEFAULT"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClass", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "At least one line allows stock items.", "path": [], "severity": 3}, {"severity": 3, "path": ["lines", "-**********", "definition"], "message": "If the posting class is detailed, only posting class definitions with the Detailed checkbox selected are allowed. If the posting class is not detailed, only posting class definitions without the Detailed checkbox selected are allowed."}]}}]}}, "Posting class create test fail 3": {"input": {"properties": {"type": "supplier", "name": "Supplier", "isDetailed": true, "lines": [{"definition": "#US|item|Overhead", "account": "#1|TEST_US_DEFAULT", "chartOfAccount": "#TEST_US_DEFAULT"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClass", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "-**********", "definition"], "message": "The posting class definition must have the supplier type."}]}}]}}, "Posting class create test fail 4": {"input": {"properties": {"type": "item", "name": "Raw materials", "isDetailed": true, "lines": [{"definition": "#US|item|Stock", "account": "#5|TEST_US_DEFAULT", "chartOfAccount": "#US_DEFAULT"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClass", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "At least one line allows stock items.", "path": [], "severity": 3}, {"severity": 3, "path": ["lines", "-**********", "account"], "message": "The record is not valid. You need to select a different record."}]}}]}}, "Posting class create test fail 5": {"input": {"properties": {"type": "item", "name": "Raw materials", "isDetailed": true, "lines": [{"chartOfAccount": "#TEST_US_DEFAULT", "definition": "#US|item|Stock", "account": "#7|TEST_US_DEFAULT"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClass", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "At least one line allows stock items.", "path": [], "severity": 3}, {"severity": 3, "path": ["lines", "-**********", "account"], "message": "The record cannot be referenced because it is inactive."}]}}]}}, "Posting class create test fail 6": {"input": {"properties": {"type": "item", "name": "Materials and finished goods (standard rate)", "isDetailed": true, "lines": [{"definition": "#US|item|Stock", "account": "#1|TEST_US_DEFAULT", "chartOfAccount": "#TEST_US_DEFAULT"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClass", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "At least one line allows stock items.", "path": [], "severity": 3}, {"severity": 3, "path": [], "message": "The operation failed because the record already exists."}]}}]}}, "Posting class create test fail 7": {"input": {"properties": {"type": "item", "name": "Raw materials", "isDetailed": true, "lines": [{"definition": "#US|item|Stock", "account": "#1|TEST_US_DEFAULT", "chartOfAccount": "#TEST_US_DEFAULT"}, {"definition": "#US|item|SalesRevenueAccrual", "account": "#1|TEST_US_DEFAULT", "chartOfAccount": "#TEST_US_DEFAULT"}, {"definition": "#US|item|LandedCostExpense", "account": "#1|TEST_US_DEFAULT", "chartOfAccount": "#TEST_US_DEFAULT"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClass", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "At least one line allows stock items.", "path": [], "severity": 3}, {"message": "At least one line allows non stock items.", "path": [], "severity": 3}, {"message": "At least one line allows service items.", "path": [], "severity": 3}, {"message": "At least one line allows landed cost items.", "path": [], "severity": 3}]}}]}, "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}}, "Posting class create test fail 8": {"input": {"properties": {"type": "supplier", "name": "Supplier", "isDetailed": true, "isStockItemAllowed": true, "isNonStockItemAllowed": true, "isServiceItemAllowed": true, "isLandedCostItemAllowed": true, "lines": [{"definition": "#US|supplier|ApGrni", "account": "#1|TEST_US_DEFAULT", "chartOfAccount": "#TEST_US_DEFAULT"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClass", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The Stock items are not allowed if the posting class type is not item or the posting class is not detailed.", "path": ["isStockItemAllowed"], "severity": 3}, {"message": "The Non stock items are not allowed if the posting class type is not item or the posting class is not detailed.", "path": ["isNonStockItemAllowed"], "severity": 3}, {"message": "The Service items are not allowed if the posting class type is not item or the posting class is not detailed.", "path": ["isServiceItemAllowed"], "severity": 3}, {"message": "The Landed cost items are not allowed if the posting class type is not item or the posting class is not detailed.", "path": ["isLandedCostItemAllowed"], "severity": 3}]}}]}, "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}}, "Cannot create a posting class with details on posting class lines if can't be any secondary criteria": {"input": {"properties": {"type": "item", "name": "Raw materials", "isDetailed": true, "isStockItemAllowed": true, "lines": [{"definition": "#US|item|Stock", "account": "#1|TEST_US_DEFAULT", "chartOfAccount": "#TEST_US_DEFAULT", "details": {"account": "#1|TEST_US_DEFAULT"}}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClass", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "-**********", "details", "-**********"], "message": "This posting class does not have the second criteria option selected."}]}}], "data": {"xtremFinanceData": {"postingClass": {"create": null}}}, "extensions": {"diagnoses": [{"severity": 3, "path": ["lines", "-**********", "details", "-**********"], "message": "This posting class does not have the second criteria option selected."}]}}, "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}}, "You need to enter a tax property when the secondary posting class line is tax.": {"input": {"properties": {"type": "item", "name": "Raw materials", "isDetailed": true, "isStockItemAllowed": true, "lines": [{"definition": "#US|item|Stock", "account": "#1|TEST_US_DEFAULT", "chartOfAccount": "#TEST_US_DEFAULT", "details": {"account": "#1|TEST_US_DEFAULT", "tax": null}}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClass", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "-**********", "details", "-**********"], "message": "This posting class does not have the second criteria option selected."}]}}], "data": {"xtremFinanceData": {"postingClass": {"create": null}}}, "extensions": {"diagnoses": [{"severity": 3, "path": ["lines", "-**********", "details", "-**********"], "message": "This posting class does not have the second criteria option selected."}]}}, "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}}, "Account on the line level cant be null if posting class line have a secondary criteria and no line details": {"input": {"properties": {"id": "FR_PURCHASE_EXPENSES", "type": "item", "name": "Purchase Expenses", "isDetailed": true, "isStockItemAllowed": true, "lines": [{"definition": "#FR|item|PurchaseExpense", "account": null, "chartOfAccount": "#FR_DEFAULT"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClass", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "-**********", "account"], "message": "You need to enter an account when there are no line details."}]}}], "data": {"xtremFinanceData": {"postingClass": {"create": null}}}, "extensions": {"diagnoses": [{"severity": 3, "path": ["lines", "-**********", "account"], "message": "You need to enter an account when there are no line details."}]}}, "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}}, "Account on the line level cant be null if posting class line don't have a secondary criteria": {"input": {"properties": {"id": "US_RAW_MATERIALS", "type": "item", "name": "Raw materials", "isDetailed": true, "isStockItemAllowed": true, "lines": [{"definition": "#US|item|Stock", "account": null, "chartOfAccount": "#TEST_US_DEFAULT"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClass", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "-**********", "account"], "message": "You need to enter an account when there are no line details."}]}}], "data": {"xtremFinanceData": {"postingClass": {"create": null}}}, "extensions": {"diagnoses": [{"severity": 3, "path": ["lines", "-**********", "account"], "message": "You need to enter an account when there are no line details."}]}}, "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}}}