mutation {
    xtremFinanceData {
        postingClass {
             create(data: {{inputParameters}}){
                type
                name
                isStockItemAllowed
                isNonStockItemAllowed
                isServiceItemAllowed
                isLandedCostItemAllowed
                lines {
                    query {
                        edges {
                            node {
                                chartOfAccount {
                                    name
                                    legislation {
                                        id
                                    }
                                }
                                definition {
                                    postingClassType
                                }
                                isStockItemAllowed
                                isNonStockItemAllowed
                                isServiceItemAllowed
                                isLandedCostItemAllowed
                                details {
                                    query {
                                        edges {
                                            node {
                                                account {
                                                    id
                                                }
                                                tax {
                                                    id
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
