{"Create a new posting class": {"input": {"properties": {"id": "US_RAW_MATERIALS", "type": "item", "name": "Raw materials", "isDetailed": true, "isStockItemAllowed": true, "lines": [{"definition": "#US|item|Stock", "account": "#1|TEST_US_DEFAULT", "chartOfAccount": "#TEST_US_DEFAULT"}]}}, "output": {"create": {"type": "item", "name": "Raw materials", "isStockItemAllowed": true, "isNonStockItemAllowed": false, "isServiceItemAllowed": false, "isLandedCostItemAllowed": false, "lines": {"query": {"edges": [{"node": {"chartOfAccount": {"name": "Test US chart of accounts", "legislation": {"id": "US"}}, "definition": {"postingClassType": "item"}, "isStockItemAllowed": true, "isNonStockItemAllowed": false, "isServiceItemAllowed": false, "isLandedCostItemAllowed": false, "details": {"query": {"edges": []}}}}]}}}}, "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}}, "Create a new posting class with posting class line detail": {"input": {"properties": {"id": "FR_PURCHASE_EXPENSES", "type": "item", "name": "Purchase Expenses", "isDetailed": true, "isStockItemAllowed": true, "lines": [{"definition": "#FR|item|PurchaseExpense", "account": "#********|FR_DEFAULT", "chartOfAccount": "#FR_DEFAULT", "details": {"account": "#********|FR_DEFAULT", "tax": "#FR_TVA_NORMAL_DEDUCTIBLE_FA_INTRASTAT"}}]}}, "output": {"create": {"type": "item", "name": "Purchase Expenses", "isStockItemAllowed": true, "isNonStockItemAllowed": false, "isServiceItemAllowed": false, "isLandedCostItemAllowed": false, "lines": {"query": {"edges": [{"node": {"chartOfAccount": {"name": "FR chart of accounts", "legislation": {"id": "FR"}}, "definition": {"postingClassType": "item"}, "isStockItemAllowed": true, "isNonStockItemAllowed": true, "isServiceItemAllowed": true, "isLandedCostItemAllowed": false, "details": {"query": {"edges": [{"node": {"account": {"id": "********"}, "tax": {"id": "FR_TVA_NORMAL_DEDUCTIBLE_FA_INTRASTAT"}}}]}}}}]}}}}, "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}}}