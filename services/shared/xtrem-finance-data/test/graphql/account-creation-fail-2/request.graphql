mutation {
    xtremFinanceData {
        account {
            create(
                data: {
                    id: "3"
                    chartOfAccount: "#TEST_US_DEFAULT"
                    isActive: true
                    name: "US Account 2"
                    isDirectEntryForbidden: false
                    isControl: false
                    taxManagement: "other"
                    attributeTypes: { attributeType: "#financialSite", isRequired: true }
                    dimensionTypes: { dimensionType: "#dimensionType06", isRequired: true }
                }
            ) {
                id
                chartOfAccount {
                    isActive
                    name
                    legislation {
                        id
                        name
                        isActive
                    }
                }
                isActive
                name
                isDirectEntryForbidden
                attributeTypes {
                    query(filter: "{ account : {id : '3'}}") {
                        edges {
                            node {
                                attributeType {
                                    id
                                    name
                                    isActive
                                }
                                isRequired
                            }
                        }
                    }
                }
                dimensionTypes {
                    query(filter: "{ account : {id : '3'}}") {
                        edges {
                            node {
                                isRequired
                                dimensionType {
                                    name
                                    isActive
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
