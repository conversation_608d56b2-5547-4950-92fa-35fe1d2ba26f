{"Testing control filter": {"input": {"properties": {"_id": "#TEST_ITEM", "lines": [{"definition": "#FR|item|PurchaseExpense", "account": "#********|FR_DEFAULT", "chartOfAccount": "#FR_DEFAULT", "details": {"account": "#********|FR_DEFAULT", "tax": "#DE_VAT_PURCHASES_HOME_REDUCED_RATE"}}]}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClass", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "-**********", "details", "-**********", "tax"], "message": "The record is not valid. You need to select a different record."}]}}]}, "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}}}