{
    xtremFinanceData {
        account {
            query(filter: "{ id : '1'}") {
                edges {
                    node {
                        id
                        name
                        isActive
                        chartOfAccount {
                            name
                            isActive
                        }
                        attributeTypes {
                            query {
                                edges {
                                    node {
                                        account {
                                            id
                                        }
                                        attributeType {
                                            id
                                            name
                                            isActive
                                        }
                                        isRequired
                                    }
                                }
                            }
                        }
                        dimensionTypes {
                            query {
                                edges {
                                    node {
                                        account {
                                            id
                                        }
                                        dimensionType {
                                            name
                                            isActive
                                        }
                                        isRequired
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
