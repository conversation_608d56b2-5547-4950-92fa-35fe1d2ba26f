mutation {
    xtremFinanceData {
        account {
            create(
                data: {
                    _id: "-1"
                    id: "999"
                    chartOfAccount: "#TEST_US_DEFAULT"
                    isActive: true
                    name: "US Account 999"
                    isDirectEntryForbidden: false
                    isControl: false
                    taxManagement: "other"
                    attributeTypes: { account: "_id:-1", attributeType: "#financialSite", isRequired: true }
                    dimensionTypes: { account: "_id:-1", dimensionType: "#dimensionType03", isRequired: true }
                }
            ) {
                id
                chartOfAccount {
                    isActive
                    name
                    legislation {
                        id
                        name
                        isActive
                    }
                }
                isActive
                name
                isDirectEntryForbidden
                attributeTypes {
                    query(filter: "{ account : {id : 6}}") {
                        edges {
                            node {
                                attributeType {
                                    id
                                    name
                                    isActive
                                }
                                isRequired
                            }
                        }
                    }
                }
                dimensionTypes {
                    query(filter: "{ account : {id : 6}}") {
                        edges {
                            node {
                                isRequired
                                dimensionType {
                                    name
                                    isActive
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
