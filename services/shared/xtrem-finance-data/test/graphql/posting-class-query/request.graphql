query {
    xtremFinanceData {
        postingClass {
            query(filter: "{_id:'#TEST_ITEM'}") {
                edges {
                    node {
                        type
                        name
                        isStockItemAllowed
                        isNonStockItemAllowed
                        isServiceItemAllowed
                        isLandedCostItemAllowed
                        lines {
                            query {
                                edges {
                                    node {
                                        chartOfAccount {
                                            name
                                            legislation {
                                                id
                                            }
                                        }
                                        definition {
                                            postingClassType
                                        }
                                        isStockItemAllowed
                                        isNonStockItemAllowed
                                        isServiceItemAllowed
                                        isLandedCostItemAllowed
                                        details {
                                            query {
                                                edges {
                                                    node {
                                                        account {
                                                            id
                                                        }
                                                        tax {
                                                            id
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
