mutation {
    xtremFinanceData {
        attributeType {
            update(
                data: {
                    _id: "#employee"
                    isActive: false
                    name: "Employee new"
                    id: "employee"
                    nodeLink: "attribute"
                    queryFilter: "{ \"attributeType\": { \"id\": \"employee\" } }"
                }
            ) {
                id
                name
            }
        }
    }
}
