{"The header journal and header posting date are mandatory on journal entry type for pre journal target document type.": {"input": {"properties": {"_id": "#US|purchaseReceipt|journalEntry", "headerJournal": null, "headerPostingDate": null}}, "output": {"diagnoses": [{"severity": 3, "path": ["headerJournal"], "message": "The header journal is mandatory."}, {"severity": 3, "path": ["headerPostingDate"], "message": "The header posting date is mandatory."}]}}, "The account type and header amount type must be empty on journal entry type for pre journal target document type.": {"input": {"properties": {"_id": "#US|purchaseReceipt|journalEntry", "headerAccountType": "#US|supplier|Ap", "headerAmountType": "amountIncludingTax"}}, "output": {"diagnoses": [{"severity": 3, "path": ["headerAccountType"], "message": "The record is not valid. You need to select a different record."}, {"severity": 3, "path": ["headerAccountType"], "message": "The header account type must be empty."}, {"severity": 3, "path": ["headerAmountType"], "message": "The header amount type must be empty."}]}}, "The header journal and header posting date must be empty on journal entry type for ap and ar invoice target document type.": {"input": {"properties": {"_id": "#FR|salesInvoice|accountsReceivableInvoice", "headerJournal": "#FR|ACH", "headerPostingDate": "documentDate"}}, "output": {"diagnoses": [{"severity": 3, "path": ["headerJournal"], "message": "The header journal must be empty."}, {"severity": 3, "path": ["headerPostingDate"], "message": "The header posting date must be empty."}]}}, "The header account type and header amount type are mandatory on journal entry type for ap and ar invoice target document type.": {"input": {"properties": {"_id": "#FR|salesInvoice|accountsReceivableInvoice", "headerAccountType": null, "headerAmountType": null}}, "output": {"diagnoses": [{"severity": 3, "path": ["headerAccountType"], "message": "The header account type is mandatory."}, {"severity": 3, "path": ["headerAmountType"], "message": "The header amount type is mandatory."}]}}}