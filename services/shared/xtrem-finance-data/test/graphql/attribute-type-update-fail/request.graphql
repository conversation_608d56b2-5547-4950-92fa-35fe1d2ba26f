mutation {
    xtremFinanceData {
        attributeType {
            update(
                data: {
                    _id: "#financialSite"
                    isActive: false
                    name: "Employee"
                    id: "employee"
                    nodeLink: "attribute"
                    queryFilter: "{ \"attributeType\": { \"id\": \"Employee\" } }"
                }
            ) {
                id
            }
        }
    }
}
