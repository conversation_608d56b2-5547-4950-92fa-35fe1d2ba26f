mutation {
    xtremFinanceData {
        attributeType {
            create(
                data: {
                    _id: "5"
                    isActive: false
                    name: "Employee new"
                    id: "employee"
                    nodeLink: "attribute"
                    queryFilter: "{ 'attributeType': { 'id' = 'Employee' } }"
                }
            ) {
                id
                name
            }
        }
    }
}
