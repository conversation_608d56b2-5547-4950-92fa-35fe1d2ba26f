{
    xtremFinanceData {
        journalEntryType {
            query(filter: "{ _id : '#GB|purchaseReceipt|journalEntry'}") {
                edges {
                    node {
                        isActive
                        name
                        legislation {
                            name
                            id
                            isActive
                        }
                        documentType
                        immediatePosting
                        headerJournal {
                            id
                            name
                        }
                        headerPostingDate
                        headerDescription
                        lines {
                            query {
                                edges {
                                    node {
                                        journalEntryType {
                                            isActive
                                            name
                                            legislation {
                                                name
                                                id
                                                isActive
                                            }
                                        }
                                        accountType {
                                            postingClassType
                                            accountTypeName
                                            isDetailed
                                        }
                                        movementType
                                        amountType
                                        sign
                                        commonReference
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
