{"Update a existing posting class": {"input": {"properties": {"_id": "#TEST_ITEM", "lines": [{"definition": "#US|item|Stock", "account": "#1|TEST_US_DEFAULT", "chartOfAccount": "#TEST_US_DEFAULT"}]}}, "output": {"update": {"type": "item", "name": "Testing posting class", "isStockItemAllowed": true, "isNonStockItemAllowed": true, "isServiceItemAllowed": true, "isLandedCostItemAllowed": true, "lines": {"query": {"edges": [{"node": {"chartOfAccount": {"name": "Test US chart of accounts", "legislation": {"id": "US"}}, "definition": {"postingClassType": "item"}, "isStockItemAllowed": true, "isNonStockItemAllowed": false, "isServiceItemAllowed": false, "isLandedCostItemAllowed": false, "details": {"query": {"edges": []}}}}]}}}}, "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}}}