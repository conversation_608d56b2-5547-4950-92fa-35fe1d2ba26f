{"Frozen property check": {"input": {"properties": {"_id": "#US|supplier|Ap", "accountTypeName": "New name"}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClassDefinition", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["accountTypeName"], "message": "PostingClassDefinition.accountTypeName: cannot set value on frozen property"}]}}]}}, "You cannot change the 'Detailed' switch setting because the Default posting class is linked to this definition.": {"input": {"properties": {"_id": "#US|supplier|Ap", "isDetailed": true}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClassDefinition", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": [], "message": "You cannot change the 'Detailed' switch setting because the Default posting class is linked to this definition."}]}}]}}, "You need to delete the posting class line details for this definition before removing the Additional criteria.": {"input": {"properties": {"_id": "#FR|item|PurchaseExpense", "additionalCriteria": null}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremFinanceData", "postingClassDefinition", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["additionalCriteria"], "message": "You need to delete the posting class line details for this definition before removing the Additional criteria."}]}}]}}}