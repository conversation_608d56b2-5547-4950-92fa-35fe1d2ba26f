import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { FakeDocument } from './fake-document';

@decorators.subNode<FakeDocumentLine>({
    extends: () => xtremMasterData.nodes.BaseDocumentItemLine,
})
export class FakeDocumentLine extends xtremMasterData.nodes.BaseDocumentItemLine {
    @decorators.referencePropertyOverride<FakeDocumentLine, 'document'>({
        node: () => FakeDocument,
    })
    override readonly document: Reference<FakeDocument>;
}
