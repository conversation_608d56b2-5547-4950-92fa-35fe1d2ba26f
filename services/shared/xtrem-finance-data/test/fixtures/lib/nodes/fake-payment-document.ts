import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '../../../../lib';

@decorators.subNode<FakePaymentDocument>({
    extends: () => xtremFinanceData.nodes.BasePaymentDocument,
    isPublished: true,
    canSearch: true,
    canRead: true,
})
export class FakePaymentDocument extends xtremFinanceData.nodes.BasePaymentDocument {
    @decorators.referencePropertyOverride<FakePaymentDocument, 'businessRelation'>({
        node: () => xtremMasterData.nodes.Customer,
    })
    override businessRelation: Reference<xtremMasterData.nodes.Customer>;
}
