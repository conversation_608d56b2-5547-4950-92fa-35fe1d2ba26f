import type { Collection, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '../../../../lib';
import { FakeDocumentLine } from './index';

@decorators.subNode<FakeDocument>({
    extends: () => xtremMasterData.nodes.BaseDocument,
})
export class FakeDocument extends xtremMasterData.nodes.BaseDocument {
    @decorators.collectionPropertyOverride<FakeDocument, 'lines'>({
        node: () => FakeDocumentLine,
    })
    override readonly lines: Collection<FakeDocumentLine>;

    @decorators.enumProperty<FakeDocument, 'fakeFinancialStatus'>({
        isStored: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
        defaultValue: 'notRecorded',
    })
    readonly fakeFinancialStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.enumPropertyOverride<FakeDocument, 'financeIntegrationStatus'>({
        getValue() {
            return this.fakeFinancialStatus;
        },
    })
    override readonly financeIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.referenceProperty<FakeDocument, 'paymentTracking'>({
        isPublished: true,
        node: () => xtremFinanceData.nodes.PaymentTracking,
        lookupAccess: true,
        isVital: true,
        isNullable: true,
        reverseReference: 'document',
    })
    readonly paymentTracking: Reference<xtremFinanceData.nodes.PaymentTracking | null>;
}
