import type { Context } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinanceData from '../../../../lib';

export async function getCurrencyId(context: Context, naturalKey: string): Promise<number> {
    return (await context.tryRead(xtremMasterData.nodes.Currency, { _id: `#${naturalKey}` }))?._id ?? 0;
}

export async function getAccountId(context: Context, naturalKey: string): Promise<number> {
    return (await context.tryRead(xtremFinanceData.nodes.Account, { _id: `#${naturalKey}` }))?._id ?? 0;
}

export async function getPaymentTermId(context: Context, naturalKey: string): Promise<number> {
    return (await context.tryRead(xtremMasterData.nodes.PaymentTerm, { _id: `#${naturalKey}` }))?._id ?? 0;
}

export async function getItemId(context: Context, naturalKey: string): Promise<number> {
    return (await context.read(xtremMasterData.nodes.Item, { _id: `#${naturalKey}` }))?._id ?? 0;
}

export async function getSupplierId(context: Context, naturalKey: string): Promise<number> {
    return (await context.read(xtremMasterData.nodes.Supplier, { _id: `#${naturalKey}` }))?._id ?? 0;
}

export async function getSiteId(context: Context, naturalKey: string): Promise<number> {
    return (await context.read(xtremSystem.nodes.Site, { _id: `#${naturalKey}` }))?._id ?? 0;
}

export async function getCustomerId(context: Context, naturalKey: string): Promise<number> {
    return (await context.read(xtremMasterData.nodes.Customer, { _id: `#${naturalKey}` }))?._id ?? 0;
}

export async function getStagingLineData(context: Context): Promise<xtremFinanceData.interfaces.StagingLineData> {
    const financialSite = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
    const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
    const customer = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' });
    const supplier = await context.read(xtremMasterData.nodes.Supplier, { _id: '#500' });
    const tax = await context.read(xtremTax.nodes.Tax, { id: 'FR001' });

    return {
        item,
        customer,
        supplier,
        tax,
        resource: null,
        account: null,
        documentNumber: 'DOC001',
        documentType: 'salesInvoice',
        movementType: 'document',
        amountType: 'amount',
        financialSite,
    };
}

export async function getNotificationPayloadForJournalEntry(
    context: Context,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument[]> {
    return [
        {
            batchId: '9991',
            batchSize: 1,
            documentSysId: 14,
            documentNumber: 'MISC_RECEIPT14',
            documentDate: '2021-08-23',
            documentType: 'miscellaneousStockReceipt',
            targetDocumentType: 'journalEntry',
            currencySysId: await getCurrencyId(context, 'USD'),
            financialSiteSysId: 1,
            documentLines: [
                {
                    baseDocumentLineSysId: 2802,
                    movementType: 'stockJournal',
                    sourceDocumentNumber: '',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    companyFxRate: 1,
                    companyFxRateDivisor: 1,
                    fxRateDate: '2021-08-23',
                    customerSysId: await getCustomerId(context, 'US019'),
                    supplierSysId: await getSupplierId(context, '500'),
                    itemSysId: await getSiteId(context, 'US004'),
                    accountSysId: await getAccountId(context, '40900|TEST_US_DEFAULT'),
                    storedDimensions: { dimensionType03: 'DIMTYPE1VALUE1', dimensionType04: 'DIMTYPE2VALUE2' },
                    storedAttributes: {
                        project: 'AttPROJ',
                        task: '',
                        employee: '',
                        stockSite: 'US001',
                        financialSite: 'US001',
                        item: 'Muesli',
                    },
                    amounts: [{ amountType: 'amount', amount: 144.0, taxSysId: 1, documentLineType: 'documentLine' }],
                },
            ],
        },
    ];
}

export async function getNotificationPayloadForAccountsReceivableInvoice(
    context: Context,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument[]> {
    return [
        {
            batchId: '9997',
            batchSize: 1,
            documentSysId: 6,
            documentNumber: 'SI6',
            documentDate: '2021-11-29',
            documentType: 'salesInvoice',
            targetDocumentType: 'accountsReceivableInvoice',
            currencySysId: await getCurrencyId(context, 'USD'),
            financialSiteSysId: 1,
            taxes: [{ baseTaxSysId: 1 }, { baseTaxSysId: 2 }],
            documentLines: [
                {
                    baseDocumentLineSysId: 1506,
                    movementType: 'document',
                    sourceDocumentNumber: '',
                    customerSysId: await getCustomerId(context, 'US019'),
                    currencySysId: await getCurrencyId(context, 'USD'),
                    companyFxRate: 1,
                    companyFxRateDivisor: 1,
                    fxRateDate: '2021-11-29',
                    itemSysId: await getItemId(context, 'Consulting01'), // 84, // Consulting01
                    storedDimensions: { dimensionType03: 'MANUFACTURING', dimensionType04: 'DIMTYPE2VALUE2' },
                    storedAttributes: {
                        project: 'AttPROJ2',
                        task: '',
                        employee: '',
                        stockSite: 'US001',
                        financialSite: 'US001',
                        item: 'Consulting01',
                    },
                    amounts: [{ amountType: 'amountExcludingTax', amount: 4.0, documentLineType: 'documentLine' }],
                    taxes: [{ baseTaxSysId: 1 }, { baseTaxSysId: 2 }],
                },
            ],
        },
    ];
}
