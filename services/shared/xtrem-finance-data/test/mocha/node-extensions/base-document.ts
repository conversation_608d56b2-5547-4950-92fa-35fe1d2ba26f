import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { FakeDocument } from '../../fixtures/lib/nodes';

describe('Fake distribution document', () => {
    it('Create a new fake base document - test the finance integrationEngine ', () =>
        Test.withContext(async context => {
            const fakeDoc = await context.create(FakeDocument, { site: '#US010' });
            assert.isTrue(await fakeDoc.$.trySave());

            assert.equal(fakeDoc.$.factory.name, 'FakeDocument');

            // This need to be tested for now we get invalid input value
            // for enum xtrem_finance_data_test.finance_document_type_enum: "FakeDocument"
            // assert.equal(await fakeDoc.financeIntegrationStatus, 'INTEGRATED');
        }));
});
