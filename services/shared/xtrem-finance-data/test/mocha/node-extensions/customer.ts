import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';

describe('Test node extension CustomerExtension', () => {
    it('Check controls with no errors', () =>
        Test.withContext(
            async context => {
                const customer = await context.read(
                    xtremMasterData.nodes.Customer,
                    { _id: '#US022' },
                    { forUpdate: true },
                );
                await customer.$.set({ datevId: 10000 });
                assert.isOk(await customer.$.control());
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check controls on not unique datevId', () =>
        Test.withContext(
            async context => {
                const customer = await context.read(
                    xtremMasterData.nodes.Customer,
                    { _id: '#US022' },
                    { forUpdate: true },
                );
                await customer.$.set({ datevId: 10099 });
                await customer.$.save();
                const customer1 = await context.read(
                    xtremMasterData.nodes.Customer,
                    { _id: '#US024' },
                    { forUpdate: true },
                );
                await customer1.$.set({ datevId: 10099 });
                await assert.isRejected(customer1.$.save());

                assert.deepEqual(customer1.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['datevId'],
                        message: 'The DATEV ID needs to be unique.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check controls on datevId out of the valid range', () =>
        Test.withContext(
            async context => {
                const customer = await context.read(
                    xtremMasterData.nodes.Customer,
                    { _id: '#US022' },
                    { forUpdate: true },
                );
                await customer.$.set({ datevId: 1000000000 });
                await assert.isRejected(customer.$.save());

                assert.deepEqual(customer.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['datevId'],
                        message: 'The DATEV ID needs to be a number between 10000 and 69999.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));
});
