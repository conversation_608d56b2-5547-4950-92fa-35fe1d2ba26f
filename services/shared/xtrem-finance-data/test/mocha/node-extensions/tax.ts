import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';
import { assert } from 'chai';

describe('Test node extension TaxExtension', () => {
    it('Check controls with no errors', () =>
        Test.withContext(
            async context => {
                const tax = await context.read(
                    xtremTax.nodes.Tax,
                    { _id: '#DE_VAT_SALES_HOME_STANDARD_RATE' },
                    { forUpdate: true },
                );
                await tax.$.set({ postingKey: 40 });
                assert.isOk(await tax.$.control());
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check controls on invalid range for postingKey', () =>
        Test.withContext(
            async context => {
                const tax = await context.read(
                    xtremTax.nodes.Tax,
                    { _id: '#DE_VAT_SALES_HOME_STANDARD_RATE' },
                    { forUpdate: true },
                );
                await tax.$.set({ postingKey: 40000 });
                await assert.isRejected(tax.$.save());
                assert.deepEqual(tax.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['postingKey'],
                        message: 'The Posting key needs to be a number between 1 and 9999.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check controls on postingKey for country !== DE', () =>
        Test.withContext(
            async context => {
                const tax = await context.read(
                    xtremTax.nodes.Tax,
                    { _id: '#FR_TVA_NORMAL_DEDUCTIBLE_ON_PAYMENT' },
                    { forUpdate: true },
                );
                await tax.$.set({ postingKey: 5000 });
                await assert.isRejected(tax.$.save());
                assert.deepEqual(tax.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['postingKey'],
                        message: 'You can only enter a value if the country is Germany.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));
});
