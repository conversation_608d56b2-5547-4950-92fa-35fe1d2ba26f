import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';

describe('Test node extension SupplierExtension', () => {
    it('Check controls with no errors', () =>
        Test.withContext(
            async context => {
                const supplier = await context.read(
                    xtremMasterData.nodes.Supplier,
                    { _id: '#US017' },
                    { forUpdate: true },
                );
                await supplier.$.set({ datevId: 70000 });
                assert.isOk(await supplier.$.control());
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check controls on not unique datevId', () =>
        Test.withContext(
            async context => {
                const supplier = await context.read(
                    xtremMasterData.nodes.Supplier,
                    { _id: '#US017' },
                    { forUpdate: true },
                );
                await supplier.$.set({ datevId: 70099 });
                await supplier.$.save();
                const supplier1 = await context.read(
                    xtremMasterData.nodes.Supplier,
                    { _id: '#US018' },
                    { forUpdate: true },
                );
                await supplier1.$.set({ datevId: 70099 });
                await assert.isRejected(supplier1.$.save());

                assert.deepEqual(supplier.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['datevId'],
                        message: 'The DATEV ID needs to be unique.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check controls on datevId out of the valid range', () =>
        Test.withContext(
            async context => {
                const supplier = await context.read(
                    xtremMasterData.nodes.Supplier,
                    { _id: '#US017' },
                    { forUpdate: true },
                );
                await supplier.$.set({ datevId: 1000000000 });
                await assert.isRejected(supplier.$.save());

                assert.deepEqual(supplier.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['datevId'],
                        message: 'The DATEV ID needs to be a number between 70000 and 99999.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check controls on supplier for duplicate dimension values', () =>
        Test.withContext(
            async context => {
                const supplier = await context.read(
                    xtremMasterData.nodes.Supplier,
                    { _id: '#LECLERC' },
                    { forUpdate: true },
                );
                await supplier.$.set({ storedDimensions: { dimensionType02: '300' } });
                assert.isOk(await supplier.$.control());
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));
});
