import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';

describe('Test node extension CompanyExtension', () => {
    before(() => {});

    it('Check controls with no errors', () =>
        Test.withContext(
            async context => {
                const company = await context.read(xtremSystem.nodes.Company, { _id: '#UT02' }, { forUpdate: true });
                await company.$.set({ datevConsultantNumber: 1001, datevCustomerNumber: 99999 });
                assert.isOk(await company.$.control());
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check controls on invalid ranges for datevConsultantNumber and datevCustomerNumber', () =>
        Test.withContext(
            async context => {
                const company = await context.read(xtremSystem.nodes.Company, { _id: '#UT02' }, { forUpdate: true });
                await company.$.set({ datevConsultantNumber: 100, datevCustomerNumber: 9999999 });
                await assert.isRejected(company.$.save());
                assert.deepEqual(company.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['datevConsultantNumber'],
                        message: 'Enter a number between 1001 and 9999999.',
                    },
                    {
                        severity: 3,
                        path: ['datevCustomerNumber'],
                        message: 'Enter a number between 1 and 99999.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check controls on mandatory datevConsultantNumber and datevCustomerNumber', () =>
        Test.withContext(
            async context => {
                const company = await context.read(xtremSystem.nodes.Company, { _id: '#DE02' });
                await assert.isRejected(company.$.save());
                assert.deepEqual(company.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['datevConsultantNumber'],
                        message: 'If the legislation is Germany, this number is required.',
                    },
                    {
                        severity: 3,
                        path: ['datevCustomerNumber'],
                        message: 'If the legislation is Germany, this number is required.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check legislation for germany  mandatory datevConsultantNumber and datevCustomerNumber', () =>
        Test.withContext(
            async context => {
                const company = await context.read(xtremSystem.nodes.Company, { _id: '#US001' }, { forUpdate: true });
                await company.$.set({ datevConsultantNumber: 1001, datevCustomerNumber: 99999 });

                await assert.isRejected(company.$.save());
                assert.deepEqual(company.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['datevConsultantNumber'],
                        message: 'This number is only for German legislation.',
                    },
                    {
                        severity: 3,
                        path: ['datevCustomerNumber'],
                        message: 'This number is only for German legislation.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Should allow valid datev numbers when legislation is DE', () =>
        Test.withContext(async context => {
            const company = await context.read(xtremSystem.nodes.Company, { _id: '#DE02' }, { forUpdate: true });
            assert.deepEqual(await (await company.legislation).id, 'DE');
            await company.$.set({
                datevConsultantNumber: 1001,
                datevCustomerNumber: 9999,
            });

            await assert.isFulfilled(company.$.save());
        }));

    it('Check control on doWipPosting for French legislation', () =>
        Test.withContext(async context => {
            const company = await context.read(xtremSystem.nodes.Company, { _id: '#S1' }, { forUpdate: true });
            await company.$.set({ doWipPosting: true });
            await assert.isRejected(company.$.save());
            assert.deepEqual(company.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['doWipPosting'],
                    message: 'You cannot post WIP for this legislation.',
                },
            ]);
        }));
});
