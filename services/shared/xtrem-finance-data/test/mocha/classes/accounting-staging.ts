import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib';
import {
    getAccountId,
    getCurrencyId,
    getCustomerId,
    getItemId,
    getPaymentTermId,
    getSiteId,
} from '../../fixtures/lib/functions/common';

describe('Accounting staging', () => {
    it('Creation accounting staging class instance', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '9991',
                    batchSize: 1,
                    documentSysId: 14,
                    documentNumber: 'MISC_RECEIPT14',
                    documentDate: '2021-08-23',
                    documentType: 'miscellaneousStockReceipt',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: await getSiteId(context, '500'),
                    dueDate: '2021-08-24',
                    isPrinted: true,
                    supplierDocumentDate: '2021-08-22',
                    supplierDocumentNumber: 'SUP_NUMBER',
                    paymentTermSysId: await getPaymentTermId(context, 'TEST_NET_15_CUSTOMER'),
                    taxCalculationStatus: 'failed',
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2802,
                            movementType: 'stockJournal',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Muesli'),
                            customerSysId: await getCustomerId(context, 'US019'),
                            providerSiteSysId: await getSiteId(context, '700'),
                            recipientSiteSysId: await getSiteId(context, 'US001'),
                            taxDate: '2021-08-23',
                            accountSysId: await getAccountId(context, '40900|TEST_US_DEFAULT'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 144.0, documentLineType: 'documentLine' }],
                        },
                    ],
                };

                const accountStagingLines = await xtremFinanceData.classes.AccountingStaging.getAccountingStagingLines(
                    context,
                    {
                        financeIntegrationDocument: financeDocument,
                        originNotificationId: '1234',
                        replyTopic: 'StockReceipt/accountingInterface',
                        isProcessed: false,
                    },
                );

                assert.equal(accountStagingLines.length, 1);

                assert.equal(accountStagingLines[0].batchId, '9991');
                assert.equal(accountStagingLines[0].batchSize, 1);
                assert.equal(accountStagingLines[0].sourceDocumentNumber, '');
                assert.equal(accountStagingLines[0].documentType, 'miscellaneousStockReceipt');
                assert.equal(accountStagingLines[0].movementType, 'stockJournal');
                assert.equal(accountStagingLines[0].targetDocumentType, 'journalEntry');
                assert.equal(await (await accountStagingLines[0].itemPostingClass)?.id, 'ITEM_GOODS_NORMAL');
                assert.equal(await (await accountStagingLines[0].customerPostingClass)?.id, 'CUSTOMER_DOMESTIC');
                assert.equal(await accountStagingLines[0].supplierPostingClass, null);
                assert.equal(accountStagingLines[0].companyFxRate, 1);
                assert.equal(accountStagingLines[0].companyFxRateDivisor, 1);
                assert.equal(accountStagingLines[0].fxRateDate.toString(), '2021-08-23');
                assert.equal(accountStagingLines[0].isProcessed, false);
                assert.isNotNull(accountStagingLines[0].storedComputedAttributes);
                assert.equal(accountStagingLines[0].documentNumber, 'MISC_RECEIPT14');
                assert.equal(accountStagingLines[0].documentDate.toString(), '2021-08-23');
                assert.equal(await (await accountStagingLines[0].item)?.id, 'Muesli');
                assert.equal(await (await accountStagingLines[0].account)?.id, '40900');
                assert.isNotNull(accountStagingLines[0].storedDimensions);
                assert.isNotNull(accountStagingLines[0].storedAttributes);
                assert.equal(accountStagingLines[0].dueDate?.toString(), '2021-08-24');
                assert.equal(accountStagingLines[0].isPrinted, true);
                assert.equal(accountStagingLines[0].supplierDocumentDate?.toString(), '2021-08-22');
                assert.equal(accountStagingLines[0].supplierDocumentNumber, 'SUP_NUMBER');
                assert.equal((await accountStagingLines[0].paymentTerm)?._id, 2);
                assert.equal(accountStagingLines[0].taxCalculationStatus, 'failed');
                assert.equal(await (await (await accountStagingLines[0].customer)?.businessEntity)?.id, 'US019');
                assert.equal(await (await accountStagingLines[0].providerSite)?.id, '700');
                assert.equal(await (await accountStagingLines[0].recipientSite)?.id, 'US001');
                assert.equal(accountStagingLines[0].taxDate?.toString(), '2021-08-23');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Creation accounting staging record that fails test', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '9991-A',
                    batchSize: 1,
                    documentSysId: 14,
                    documentNumber: 'MISC_RECEIPT14',
                    documentDate: '2021-08-23',
                    documentType: 'miscellaneousStockReceipt',
                    targetDocumentType: 'journalEntry',
                    currencySysId: 999,
                    financialSiteSysId: 999999,
                    paymentTermSysId: 999999,
                    documentLines: [
                        {
                            baseDocumentLineSysId: 999999,
                            movementType: 'stockJournal',
                            sourceDocumentNumber: '',
                            stockJournalSysId: 999999,
                            currencySysId: 999,
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: 999999,
                            accountSysId: 999999,
                            customerSysId: 999999,
                            supplierSysId: 999999,
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [
                                {
                                    amountType: 'amount',
                                    amount: 144.0,
                                    baseTaxSysId: 1,
                                    taxSysId: 999999,
                                    documentLineType: 'documentLine',
                                },
                            ],
                        },
                    ],
                };

                const accountStagingLines = await xtremFinanceData.classes.AccountingStaging.getAccountingStagingLines(
                    context,
                    {
                        financeIntegrationDocument: financeDocument,
                        originNotificationId: '1234',
                        replyTopic: 'StockReceipt/accountingInterface',
                        isProcessed: false,
                    },
                );

                assert.equal(
                    accountStagingLines[0].financeTransactionData.validationMessages[0].message,
                    'The  site could not be found.',
                );
                assert.equal(
                    accountStagingLines[0].financeTransactionData.validationMessages[1].message,
                    'The 999999 payment term could not be found.',
                );
                assert.equal(
                    accountStagingLines[0].financeTransactionData.validationMessages[2].message,
                    'The base document line 999999 could not be found.',
                );
                assert.equal(
                    accountStagingLines[0].financeTransactionData.validationMessages[3].message,
                    'The stock journal 999999 could not be found.',
                );
                assert.equal(
                    accountStagingLines[0].financeTransactionData.validationMessages[4].message,
                    'The 999999 item could not be found.',
                );
                assert.equal(
                    accountStagingLines[0].financeTransactionData.validationMessages[5].message,
                    'The 999999 account could not be found.',
                );
                assert.equal(
                    accountStagingLines[0].financeTransactionData.validationMessages[6].message,
                    'The 999999 customer could not be found.',
                );
                assert.equal(
                    accountStagingLines[0].financeTransactionData.validationMessages[7].message,
                    'The 999999 supplier could not be found.',
                );
                assert.equal(
                    accountStagingLines[0].financeTransactionData.validationMessages[8].message,
                    'The 999 currency could not be found.',
                );
                assert.equal(
                    accountStagingLines[0].financeTransactionData.validationMessages[9].message,
                    'The 999999 tax could not be found.',
                );
                assert.equal(
                    accountStagingLines[0].financeTransactionData.validationMessages[10].message,
                    'The tax line 1 could not be found.',
                );
            },
            {
                today: '2021-08-23',
            },
        ));
});
