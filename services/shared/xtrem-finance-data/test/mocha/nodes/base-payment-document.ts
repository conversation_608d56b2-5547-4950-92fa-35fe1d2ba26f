import { date, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib/index';
import { FakePaymentDocument } from '../../fixtures/lib/nodes';
import { createFakePaymentDocumentWithDates } from '../functions/create-fake-payment-document';

describe('Fake BasePaymentDocument node - test ', () => {
    it('Create the fake BasePaymentDocument without error', () =>
        Test.withContext(
            async context => {
                const originalOpenItem = await context
                    .query(xtremFinanceData.nodes.BaseOpenItem, { filter: { documentNumber: 'SI1' }, first: 1 })
                    .elementAt(0);
                const fakeDocument = await context.create(FakePaymentDocument, {
                    number: 'RCTE0001',
                    bankAccount: '#BAN',
                    financialSite: '#US006',
                    businessRelation: '#US019',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    postingDate: date.today(),
                    currency: '#USD',
                    exchangeRateDate: date.today(),
                    companyExchangeRate: 1,
                    companyExchangeRateDivisor: 1,
                    postingStatus: 'draft',
                    amount: 100,
                    amountBankCurrency: 100,
                    lines: [
                        {
                            amount: 100,
                            origin: 'invoice',
                            originalOpenItem,
                            originalNodeFactory: '#FakeDocument',
                        },
                    ],
                });
                await fakeDocument.$.save();
                const fakeDocumentLine = await fakeDocument.lines.at(0);

                assert.strictEqual(await fakeDocument.financialSiteName, 'Chem. Boston');
                assert.strictEqual(await fakeDocument.postingStatus, 'draft');
                assert.strictEqual(await fakeDocument.number, 'RCTE0001');
                assert.strictEqual(await fakeDocumentLine?.origin, 'invoice');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the fake BasePaymentDocument with wrong financialSite', () =>
        Test.withContext(
            async context => {
                const originalOpenItem = await context
                    .query(xtremFinanceData.nodes.BaseOpenItem, { filter: { documentNumber: 'SI1' }, first: 1 })
                    .elementAt(0);
                const fakeDocument = await context.create(FakePaymentDocument, {
                    number: 'RCTE0001',
                    bankAccount: '#BAN',
                    financialSite: '#US005',
                    businessRelation: '#US019',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    postingDate: date.today(),
                    currency: '#USD',
                    exchangeRateDate: date.today(),
                    companyExchangeRate: 1,
                    companyExchangeRateDivisor: 1,
                    postingStatus: 'draft',
                    amount: 100,
                    amountBankCurrency: 100,
                    lines: [
                        {
                            amount: 100,
                            origin: 'invoice',
                            originalOpenItem,
                            originalNodeFactory: '#FakeDocument',
                        },
                    ],
                });

                await assert.isRejected(fakeDocument.$.save());
                assert.deepEqual(fakeDocument.$.context.diagnoses, [
                    {
                        message:
                            'The financial site of the document needs to be the same as the financial site of the bank account.',
                        path: [],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the fake BasePaymentDocument with zero amounts', () =>
        Test.withContext(
            async context => {
                const originalOpenItem = await context
                    .query(xtremFinanceData.nodes.BaseOpenItem, { filter: { documentNumber: 'SI1' }, first: 1 })
                    .elementAt(0);
                const fakeDocument = await context.create(FakePaymentDocument, {
                    number: 'RCTE0001',
                    bankAccount: '#BAN',
                    financialSite: '#US006',
                    businessRelation: '#US019',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    postingDate: date.today(),
                    currency: '#USD',
                    exchangeRateDate: date.today(),
                    companyExchangeRate: 1,
                    companyExchangeRateDivisor: 1,
                    postingStatus: 'draft',
                    amount: 0,
                    lines: [
                        {
                            amount: 30,
                            origin: 'creditMemo',
                            originalOpenItem,
                            originalNodeFactory: '#FakeDocument',
                        },
                        {
                            amount: 100,
                            origin: 'invoice',
                            originalOpenItem,
                            originalNodeFactory: '#FakeDocument',
                        },
                    ],
                });

                await assert.isRejected(fakeDocument.$.save());
                assert.deepEqual(fakeDocument.$.context.diagnoses, [
                    {
                        message: 'The payment amount of the document 0 needs to be positive.',
                        path: [],
                        severity: 3,
                    },
                    {
                        message: 'The amount in bank currency of the document 0 needs to be positive.',
                        path: [],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the fake BasePaymentDocument with different bank currency', () =>
        Test.withContext(
            async context => {
                const originalOpenItem = await context
                    .query(xtremFinanceData.nodes.BaseOpenItem, { filter: { documentNumber: 'SI1' }, first: 1 })
                    .elementAt(0);
                const fakeDocument = await context.create(FakePaymentDocument, {
                    number: 'RCTE0001',
                    bankAccount: '#BAN',
                    financialSite: '#US006',
                    businessRelation: '#US019',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    postingDate: date.today(),
                    currency: '#USD',
                    exchangeRateDate: date.today(),
                    companyExchangeRate: 1,
                    companyExchangeRateDivisor: 1,
                    postingStatus: 'draft',
                    amount: 185,
                    amountBankCurrency: 34.96,
                    lines: [
                        {
                            amount: 21.25,
                            origin: 'invoice',
                            originalOpenItem,
                            originalNodeFactory: '#FakeDocument',
                        },
                        {
                            amount: 8.04,
                            origin: 'creditMemo',
                            originalOpenItem,
                            originalNodeFactory: '#FakeDocument',
                        },
                        {
                            amount: 86.79,
                            origin: 'invoice',
                            originalOpenItem,
                            originalNodeFactory: '#FakeDocument',
                        },
                        {
                            amount: 21.25,
                            origin: 'invoice',
                            originalOpenItem,
                            originalNodeFactory: '#FakeDocument',
                        },
                        {
                            amount: 21.25,
                            origin: 'invoice',
                            originalOpenItem,
                            originalNodeFactory: '#FakeDocument',
                        },
                        {
                            amount: 21.25,
                            origin: 'invoice',
                            originalOpenItem,
                            originalNodeFactory: '#FakeDocument',
                        },
                        {
                            amount: 21.25,
                            origin: 'invoice',
                            originalOpenItem,
                            originalNodeFactory: '#FakeDocument',
                        },
                    ],
                });
                await fakeDocument.$.save();
                const fakeDocumentLine1 = await fakeDocument.lines.at(0);
                const fakeDocumentLine2 = await fakeDocument.lines.at(1);
                const fakeDocumentLine3 = await fakeDocument.lines.at(2);
                const fakeDocumentLine4 = await fakeDocument.lines.at(3);
                const fakeDocumentLine5 = await fakeDocument.lines.at(4);
                const fakeDocumentLine6 = await fakeDocument.lines.at(5);
                const fakeDocumentLine7 = await fakeDocument.lines.at(6);

                assert.strictEqual(await fakeDocument.financialSiteName, 'Chem. Boston');
                assert.strictEqual(await fakeDocument.postingStatus, 'draft');
                assert.strictEqual(await fakeDocument.number, 'RCTE0001');
                assert.strictEqual(await fakeDocumentLine1?.origin, 'invoice');
                assert.strictEqual(await fakeDocumentLine2?.origin, 'creditMemo');
                assert.strictEqual(await fakeDocumentLine3?.origin, 'invoice');
                assert.strictEqual((await fakeDocumentLine1?.amountBankCurrency)?.toString(), '4.02');
                assert.strictEqual((await fakeDocumentLine2?.amountBankCurrency)?.toString(), '1.52');
                assert.strictEqual((await fakeDocumentLine3?.amountBankCurrency)?.toString(), '16.4');
                assert.strictEqual((await fakeDocumentLine4?.amountBankCurrency)?.toString(), '4.02');
                assert.strictEqual((await fakeDocumentLine5?.amountBankCurrency)?.toString(), '4.02');
                assert.strictEqual((await fakeDocumentLine6?.amountBankCurrency)?.toString(), '4.02');
                assert.strictEqual((await fakeDocumentLine7?.amountBankCurrency)?.toString(), '4');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));
});

describe('Fake BasePaymentDocument node - voidDate control', () => {
    it('Control should fail because voidDate is null', () =>
        Test.withContext(
            async context => {
                const voidDate = null;
                const paymentDate = date.today();
                const fakeDocument = await createFakePaymentDocumentWithDates(context, voidDate, paymentDate);
                await assert.isRejected(fakeDocument.$.save());
                assert.deepEqual(fakeDocument.$.context.diagnoses, [
                    {
                        message: 'The void date is mandatory.',
                        path: ['voidDate'],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Control should fail because voidDate happens before paymentDate', () =>
        Test.withContext(
            async context => {
                const voidDate = date.parse('2024-11-21');
                const paymentDate = date.parse('2024-11-22');
                const fakeDocument = await createFakePaymentDocumentWithDates(context, voidDate, paymentDate);
                await assert.isRejected(fakeDocument.$.save());
                assert.deepEqual(fakeDocument.$.context.diagnoses, [
                    {
                        message: 'The void date needs to be after the payment date.',
                        path: ['voidDate'],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Control should be successfull because voidDate happens in the same day as paymentDate', () =>
        Test.withContext(
            async context => {
                const voidDate = date.parse('2024-11-22');
                const paymentDate = date.parse('2024-11-22');
                const fakeDocument = await createFakePaymentDocumentWithDates(context, voidDate, paymentDate);
                assert.isTrue(await fakeDocument.$.trySave());
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Control should be successfull because voidDate happens after paymentDate', () =>
        Test.withContext(
            async context => {
                const voidDate = date.parse('2024-11-23');
                const paymentDate = date.parse('2024-11-22');
                const fakeDocument = await createFakePaymentDocumentWithDates(context, voidDate, paymentDate);
                assert.isTrue(await fakeDocument.$.trySave());
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));
});
