import { Test } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';

describe('Company node', () => {
    before(() => {});

    it('Create US company node to check presettings for posting', () =>
        Test.withContext(async context => {
            const company = await context.create(xtremSystem.nodes.Company, {
                id: 'US999',
                name: 'American Company',
                description: 'American company for unit testing',
                legislation: { id: 'US' },
                country: { id: 'US' },
                currency: { id: 'USD' },
            });
            await company.$.set({
                addresses: [
                    {
                        isActive: true,
                        isPrimary: true,
                        name: 'Main',
                    },
                ],
            });
            await company.$.save();

            // presetting checks
            assert.deepEqual(company.$.context.diagnoses, []);
            assert.equal(await company.id, 'US999');
            assert.isFalse(await company.doApPosting);
            assert.isFalse(await company.doArPosting);
            assert.isTrue(await company.doStockPosting);
            assert.isTrue(await company.doNonAbsorbedPosting);
            assert.isTrue(await company.doWipPosting);
        }));

    it('Update company and check finance flags', () =>
        Test.withContext(async context => {
            const UScompany = await context.read(
                xtremSystem.nodes.Company,
                {
                    _id: '#US017',
                },
                { forUpdate: true },
            );

            const FRLegislation = await context.read(xtremStructure.nodes.Legislation, {
                _id: '#FR',
            });

            await UScompany.$.set({
                legislation: FRLegislation,
                legalForm: 'SARL',
                naf: '58.29A',
                siren: '*********',
                rcs: 'RCS Paris A *********',
            });
            await assert.isRejected(UScompany.$.save());
            assert.deepEqual(UScompany.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['doWipPosting'],
                    message: 'You cannot post WIP for this legislation.',
                },
                {
                    severity: 3,
                    path: ['doNonAbsorbedPosting'],
                    message: 'Non absorbed posting not allowed if there is no stock posting.',
                },
                {
                    severity: 3,
                    path: ['doNonAbsorbedPosting'],
                    message: 'You cannot post non-absorbed amounts for this legislation.',
                },
            ]);

            await UScompany.$.set({
                doNonAbsorbedPosting: false,
            });
            assert.isFalse(await UScompany.$.control());
            assert.isFalse(await UScompany.doStockPosting);
            assert.isFalse(await UScompany.doNonAbsorbedPosting);
        }));

    it('Create FR company node to check presetting', () =>
        Test.withContext(async context => {
            const company = await context.create(xtremSystem.nodes.Company, {
                id: 'FR999',
                name: 'Franchising company',
                description: 'French company for unit testing',
                legislation: { id: 'FR' },
                country: { id: 'FR' },
                currency: { id: 'EUR' },
                legalForm: 'SARL',
                naf: '58.29A',
                siren: '*********',
                rcs: 'RCS Paris A *********',
                addresses: [
                    {
                        isActive: true,
                        isPrimary: true,
                        name: 'Main',
                    },
                ],
            });

            await company.$.save();

            assert.deepEqual(company.$.context.diagnoses, []);
            assert.equal(await company.id, 'FR999');
            assert.isTrue(await company.doApPosting);
            assert.isTrue(await company.doArPosting);
            assert.isFalse(await company.doStockPosting);
            assert.isFalse(await company.doWipPosting);
        }));

    it('Create UK company node to check presettings for posting', () =>
        Test.withContext(async context => {
            const company = await context.create(xtremSystem.nodes.Company, {
                id: 'UK999',
                name: 'British Company',
                description: 'British company for unit testing',
                legislation: { id: 'GB' },
                country: { id: 'GB' },
                currency: { id: 'GBP' },
            });
            await company.$.set({
                addresses: [
                    {
                        isActive: true,
                        isPrimary: true,
                        name: 'Main',
                    },
                ],
            });
            await company.$.save();

            assert.deepEqual(company.$.context.diagnoses, []);
            assert.equal(await company.id, 'UK999');
            assert.isFalse(await company.doApPosting);
            assert.isFalse(await company.doArPosting);
            assert.isTrue(await company.doStockPosting);
            assert.isTrue(await company.doWipPosting);
        }));
    it('Create ZA company node to check presettings for posting', () =>
        Test.withContext(async context => {
            const company = await context.create(xtremSystem.nodes.Company, {
                id: 'ZA999',
                name: 'South African Company',
                description: 'South African company for unit testing',
                legislation: { id: 'ZA' },
                country: { id: 'ZA' },
                currency: { id: 'ZAR' },
            });
            await company.$.set({
                addresses: [
                    {
                        isActive: true,
                        isPrimary: true,
                        name: 'Main',
                    },
                ],
            });
            await company.$.save();

            assert.deepEqual(company.$.context.diagnoses, []);
            assert.equal(await company.id, 'ZA999');
            assert.isFalse(await company.doApPosting);
            assert.isFalse(await company.doArPosting);
            assert.isTrue(await company.doStockPosting);
            assert.isTrue(await company.doWipPosting);
        }));

    it('Site - attribute restricted to check', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { _id: '#US001' }, { forUpdate: true });
            await site.$.set({
                storedAttributes: {
                    project: '',
                    task: 'Task1',
                    employee: '',
                },
            });

            await assert.isRejected(site.$.save());
            assert.deepEqual(site.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'The Project attribute needs to be filled in.',
                },
            ]);
        }));

    it('Create company node to check discrepancy on defaulting project and task', () =>
        Test.withContext(async context => {
            const company = await context.create(xtremSystem.nodes.Company, {
                id: 'UK999',
                name: 'UK Company',
                description: 'UK company for unit testing',
                legislation: { id: 'GB' },
                country: { id: 'GB' },
                currency: { id: 'GBP' },
                defaultAttributes: [
                    {
                        dimensionDefinitionLevel: 'salesDirect',
                        masterDataDefault: 'site',
                        attributeType: { id: 'project' },
                    },
                    {
                        dimensionDefinitionLevel: 'salesDirect',
                        masterDataDefault: 'customer',
                        attributeType: { id: 'task' },
                    },
                ],
            });
            await company.$.set({
                addresses: [
                    {
                        isActive: true,
                        isPrimary: true,
                        name: 'Main',
                    },
                ],
            });
            await assert.isRejected(company.$.save());

            assert.deepEqual(company.$.context.diagnoses, [
                {
                    message: 'The project and task attributes need to default from the same origin.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));

    it('Create company node to check discrepancy on defaulting only task', () =>
        Test.withContext(async context => {
            const company = await context.create(xtremSystem.nodes.Company, {
                id: 'UK999',
                name: 'UK Company',
                description: 'UK company for unit testing',
                legislation: { id: 'GB' },
                country: { id: 'GB' },
                currency: { id: 'GBP' },
                defaultAttributes: [
                    {
                        dimensionDefinitionLevel: 'salesDirect',
                        masterDataDefault: 'customer',
                        attributeType: { id: 'task' },
                    },
                ],
            });
            await company.$.set({
                addresses: [
                    {
                        isActive: true,
                        isPrimary: true,
                        name: 'Main',
                    },
                ],
            });
            await assert.isRejected(company.$.save());

            assert.deepEqual(company.$.context.diagnoses, [
                {
                    message: 'The project and task attributes need to default from the same origin.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));

    it('Create company node without discrepancy on defaulting project and task', () =>
        Test.withContext(async context => {
            const company = await context.create(xtremSystem.nodes.Company, {
                id: 'UK999',
                name: 'UK Company',
                description: 'UK company for unit testing',
                legislation: { id: 'GB' },
                country: { id: 'GB' },
                currency: { id: 'GBP' },
                defaultAttributes: [
                    {
                        dimensionDefinitionLevel: 'salesDirect',
                        masterDataDefault: 'site',
                        attributeType: { id: 'project' },
                    },
                    {
                        dimensionDefinitionLevel: 'salesDirect',
                        masterDataDefault: 'site',
                        attributeType: { id: 'task' },
                    },
                ],
            });
            await company.$.set({
                addresses: [
                    {
                        isActive: true,
                        isPrimary: true,
                        name: 'Main',
                    },
                ],
            });
            assert.isOk(await company.$.control());
        }));
});
