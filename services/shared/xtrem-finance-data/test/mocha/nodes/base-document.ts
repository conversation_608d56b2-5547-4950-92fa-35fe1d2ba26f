import { Test } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib';
import { FakeDocument } from '../../fixtures/lib/nodes';

describe('Fake documents', () => {
    it('SetDimensions on a fake document', () =>
        Test.withContext(async context => {
            const fakeDoc = await context.read(FakeDocument, { _id: '#fin-01' }, { forUpdate: true });
            assert.isNotEmpty(fakeDoc);

            assert.equal(await fakeDoc.status, 'draft');
            assert.equal(await fakeDoc.fakeFinancialStatus, 'toBeRecorded');
            assert.equal(await fakeDoc.financeIntegrationStatus, 'toBeRecorded');

            await fakeDoc.$.set({ status: 'closed' });

            await fakeDoc.$.save();

            const firstLine = (await fakeDoc.lines.elementAt(0)) as xtremMasterData.nodes.BaseDocumentItemLine;

            assert.isNotEmpty(firstLine);

            const isDone = await xtremFinanceData.nodeExtensions.BaseDocumentItemLineExtension.setDimension(
                context,
                firstLine,
                JSON.stringify({}),
                JSON.stringify({}),
            );

            assert.isTrue(isDone);

            await fakeDoc.$.set({ canUpdateClosedDocument: true, fakeFinancialStatus: 'posted' });

            await fakeDoc.$.save();

            await assert.isRejected(
                xtremFinanceData.nodeExtensions.BaseDocumentItemLineExtension.setDimension(
                    context,
                    firstLine,
                    JSON.stringify({}),
                    JSON.stringify({}),
                ),
                'You cannot update dimension on a closed document.',
            );
        }, {}));
});
