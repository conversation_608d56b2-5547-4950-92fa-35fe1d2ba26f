import { date, Test, ValidationSeverity } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib/index';
import { FakeOpenItem } from '../../fixtures/lib/nodes';

describe('Fake BaseOpenItem node - test ', () => {
    it('Create the fake BaseOpenItem without error', () =>
        Test.withContext(
            async context => {
                const fakeOpenItem = await context.create(FakeOpenItem, {
                    businessEntity: '#US019',
                    businessEntityPayment: '#US019',
                    transactionAmountDue: 100,
                    transactionAmountPaid: 100,
                    companyAmountDue: 100,
                    companyAmountPaid: 100,
                    financialSiteAmountDue: 100,
                    financialSiteAmountPaid: 100,
                    status: 'paid',
                    dueDate: date.today(),
                    type: 'customer',
                    currency: '#USD',
                    documentSysId: 1,
                    documentNumber: 'SI1',
                    documentType: 'salesInvoice',
                    closeReason: '#Payment',
                });
                await fakeOpenItem.$.save();

                assert.strictEqual(await fakeOpenItem.documentNumber, 'SI1');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the fake BaseOpenItem with missing close reason', () =>
        Test.withContext(
            async context => {
                const fakeOpenItem = await context.create(FakeOpenItem, {
                    businessEntity: '#US019',
                    businessEntityPayment: '#US019',
                    transactionAmountDue: 100,
                    transactionAmountPaid: 100,
                    companyAmountDue: 100,
                    companyAmountPaid: 100,
                    financialSiteAmountDue: 100,
                    financialSiteAmountPaid: 100,
                    status: 'paid',
                    dueDate: date.today(),
                    type: 'customer',
                    currency: '#USD',
                    documentSysId: 1,
                    documentNumber: 'SI1',
                    documentType: 'salesInvoice',
                });

                await assert.isRejected(fakeOpenItem.$.save());
                const errors = fakeOpenItem.$.context.diagnoses.filter(d => d.severity === ValidationSeverity.error);
                assert.equal(errors.length, 1);
                assert.deepEqual(errors[0], {
                    path: [],
                    severity: ValidationSeverity.error,
                    message: 'The close reason is mandatory.',
                });

                await assert.isRejected(fakeOpenItem.$.save(), 'The record was not created.');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the fake BaseOpenItem with wrong amounts', () =>
        Test.withContext(
            async context => {
                const fakeOpenItem = await context.create(FakeOpenItem, {
                    businessEntity: '#US019',
                    businessEntityPayment: '#US019',
                    transactionAmountDue: 100,
                    transactionAmountPaid: 110,
                    companyAmountDue: 100,
                    companyAmountPaid: 110,
                    financialSiteAmountDue: 100,
                    financialSiteAmountPaid: 110,
                    status: 'paid',
                    dueDate: date.today(),
                    type: 'customer',
                    currency: '#USD',
                    documentSysId: 1,
                    documentNumber: 'SI1',
                    documentType: 'salesInvoice',
                    closeReason: '#Payment',
                });

                await assert.isRejected(fakeOpenItem.$.save());
                const errors = fakeOpenItem.$.context.diagnoses.filter(d => d.severity === ValidationSeverity.error);
                assert.equal(errors.length, 3);
                assert.deepEqual(errors, [
                    {
                        path: [],
                        severity: ValidationSeverity.error,
                        message:
                            'The transaction amount paid needs to be lower than or equal to the transaction amount due.',
                    },
                    {
                        path: [],
                        severity: ValidationSeverity.error,
                        message: 'The company amount paid needs to be lower than or equal to the company amount due.',
                    },
                    {
                        path: [],
                        severity: ValidationSeverity.error,
                        message:
                            'The financial site amount paid needs to be lower than or equal to the financial site amount due.',
                    },
                ]);

                await assert.isRejected(fakeOpenItem.$.save(), 'The record was not created.');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the fake BaseOpenItem with negative forced amount', () =>
        Test.withContext(
            async context => {
                const fakeOpenItem = await context.create(FakeOpenItem, {
                    businessEntity: '#US019',
                    businessEntityPayment: '#US019',
                    transactionAmountDue: 100,
                    transactionAmountPaid: 50,
                    companyAmountDue: 100,
                    companyAmountPaid: 50,
                    financialSiteAmountDue: 100,
                    financialSiteAmountPaid: 50,
                    status: 'partiallyPaid',
                    dueDate: date.today(),
                    type: 'customer',
                    currency: '#USD',
                    documentSysId: 1,
                    documentNumber: 'SI1',
                    documentType: 'salesInvoice',
                    closeReason: '#Forced close',
                    forcedAmountPaid: -100,
                });

                await assert.isRejected(fakeOpenItem.$.save());
                const errors = fakeOpenItem.$.context.diagnoses.filter(d => d.severity === ValidationSeverity.error);
                assert.equal(errors.length, 1);
                assert.deepEqual(errors, [
                    {
                        path: ['forcedAmountPaid'],
                        severity: ValidationSeverity.error,
                        message: 'The forced amount paid needs to be greater than or equal to 0.',
                    },
                ]);

                await assert.isRejected(fakeOpenItem.$.save(), 'The record was not created.');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Update the fake BaseOpenItem with wrong forced amounts', () =>
        Test.withContext(
            async context => {
                const fakeOpenItem = await context
                    .query(FakeOpenItem, {
                        filter: { documentType: 'salesInvoice', documentNumber: 'SI1' },
                        first: 1,
                        forUpdate: true,
                    })
                    .elementAt(0);
                await fakeOpenItem.$.set({ forcedAmountPaid: 3000 });
                await assert.isRejected(fakeOpenItem.$.save());
                const errors = fakeOpenItem.$.context.diagnoses.filter(d => d.severity === ValidationSeverity.error);
                assert.equal(errors.length, 1);
                assert.deepEqual(errors, [
                    {
                        path: [],
                        severity: ValidationSeverity.error,
                        message: 'The forced amount paid needs to be between 0 and 2400.',
                    },
                ]);

                await assert.isRejected(fakeOpenItem.$.save(), 'The record was not updated.');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Update the fake BaseOpenItem to paid with missing close reason', () =>
        Test.withContext(
            async context => {
                const fakeOpenItem = await context
                    .query(FakeOpenItem, {
                        filter: { documentType: 'salesInvoice', documentNumber: 'SI1' },
                        first: 1,
                        forUpdate: true,
                    })
                    .elementAt(0);
                await fakeOpenItem.$.set({ forcedAmountPaid: 2400, closeReason: null });
                await assert.isRejected(fakeOpenItem.$.save());
                const errors = fakeOpenItem.$.context.diagnoses.filter(d => d.severity === ValidationSeverity.error);
                assert.equal(errors.length, 1);
                assert.deepEqual(errors, [
                    {
                        path: [],
                        severity: ValidationSeverity.error,
                        message: 'The close reason is mandatory.',
                    },
                ]);

                await assert.isRejected(fakeOpenItem.$.save(), 'The record was not updated.');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Remove the close reason from a fully paid fake BaseOpenItem', () =>
        Test.withContext(
            async context => {
                const fakeOpenItem = await context
                    .query(FakeOpenItem, {
                        filter: { documentType: 'salesInvoice', documentNumber: 'SI2' },
                        first: 1,
                        forUpdate: true,
                    })
                    .elementAt(0);
                await fakeOpenItem.$.set({ closeReason: null });
                await assert.isRejected(fakeOpenItem.$.save());
                const errors = fakeOpenItem.$.context.diagnoses.filter(d => d.severity === ValidationSeverity.error);
                assert.equal(errors.length, 1);
                assert.deepEqual(errors, [
                    {
                        path: [],
                        severity: ValidationSeverity.error,
                        message: 'The close reason is mandatory.',
                    },
                ]);

                await assert.isRejected(fakeOpenItem.$.save(), 'The record was not updated.');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));
});
