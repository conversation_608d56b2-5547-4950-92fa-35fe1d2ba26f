import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';

describe('ItemDefaultAttributesDimensions', () => {
    it('Create item master default attribute and dimension', () =>
        Test.withContext(async context => {
            const itemDefaultAttributeDimension = await context.read(
                xtremMasterData.nodes.Item,
                { _id: '#Muesli' },
                { forUpdate: true },
            );
            await itemDefaultAttributeDimension.$.set({
                storedAttributes: { employee: '', task: 'Task1', project: 'AttPROJ' },
                storedDimensions: { dimensionType01: '300', dimensionType02: 'CHANNELVALUE1' },
            });

            await itemDefaultAttributeDimension.$.save();
            assert.instanceOf(itemDefaultAttributeDimension, xtremMasterData.nodes.Item);
            assert.equal(
                JSON.stringify(await itemDefaultAttributeDimension.storedDimensions),
                JSON.stringify({
                    dimensionType01: '300',
                    dimensionType02: 'CHANNELVALUE1',
                }),
            );
            assert.equal(
                JSON.stringify(await itemDefaultAttributeDimension.storedAttributes),
                JSON.stringify({
                    employee: '',
                    task: 'Task1',
                    project: 'AttPROJ',
                }),
            );
        }));
});
