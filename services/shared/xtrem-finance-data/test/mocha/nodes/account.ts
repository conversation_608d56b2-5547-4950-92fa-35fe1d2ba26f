import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib/index';

describe('Test node Account', () => {
    it('Check controls with no errors', () =>
        Test.withContext(
            async context => {
                const account = await context.read(
                    xtremFinanceData.nodes.Account,
                    { _id: '#3800|DE_DEFAULT' },
                    { forUpdate: true },
                );
                await account.$.set({ isAutomaticAccount: true, tax: '#DE_VAT_SALES_HOME_MISC_RATE', datevId: 1001 });
                assert.isOk(await account.$.control());
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check controls on wrong tax reference', () =>
        Test.withContext(
            async context => {
                const account = await context.read(
                    xtremFinanceData.nodes.Account,
                    { _id: '#3800|DE_DEFAULT' },
                    { forUpdate: true },
                );
                await account.$.set({ isAutomaticAccount: true, tax: '#ZA_VAT_SALES_EXEMPT_RATE', datevId: 1001 });
                await assert.isRejected(account.$.save());
                assert.deepEqual(account.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['tax'],
                        message: 'You need to assign a tax code linked with one of these countries: DE.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check controls on not unique datevId', () =>
        Test.withContext(
            async context => {
                const account = await context.read(
                    xtremFinanceData.nodes.Account,
                    { _id: '#3800|DE_DEFAULT' },
                    { forUpdate: true },
                );
                await account.$.set({ datevId: 1001 });
                await account.$.save();
                const account1 = await context.read(
                    xtremFinanceData.nodes.Account,
                    { _id: '#3830|DE_DEFAULT' },
                    { forUpdate: true },
                );
                await account1.$.set({ datevId: 1001 });
                await assert.isRejected(account1.$.save());

                assert.deepEqual(account1.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['datevId'],
                        message: 'The DATEV ID needs to be unique.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check controls on datevId out of the valid range', () =>
        Test.withContext(
            async context => {
                const account = await context.read(
                    xtremFinanceData.nodes.Account,
                    { _id: '#3800|DE_DEFAULT' },
                    { forUpdate: true },
                );
                await account.$.set({ datevId: ********** });
                await assert.isRejected(account.$.save());

                assert.deepEqual(account.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['datevId'],
                        message: 'The DATEV ID needs to be a number between 1 and 9999.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));
});
