import { Test, ValidationSeverity } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib/index';

describe('Attribute type node', () => {
    it('Set attribute type task active when project is inactive', () =>
        Test.withContext(async context => {
            const attributeTypeProject = await context.read(
                xtremFinanceData.nodes.AttributeType,
                { id: 'dummyProject' },
                { forUpdate: true },
            );
            await attributeTypeProject.$.set({ isActive: false });
            await attributeTypeProject.$.save();

            const attributeTypeTask = await context.read(
                xtremFinanceData.nodes.AttributeType,
                { id: 'dummyTask' },
                { forUpdate: true },
            );
            await attributeTypeTask.$.set({ isActive: true });
            await assert.isRejected(attributeTypeTask.$.save());

            const errors = attributeTypeTask.$.context.diagnoses.filter(d => d.severity === ValidationSeverity.error);

            assert.equal(errors.length, 1);
            assert.deepEqual(errors[0], {
                path: [],
                severity: ValidationSeverity.error,
                message:
                    'You can only set the attribute type to active if the restricted to attribute type dummyProject is active.',
            });
        }));

    it('Set attribute type projct inactive when task is active', () =>
        Test.withContext(async context => {
            const attributeTypeTask = await context.read(
                xtremFinanceData.nodes.AttributeType,
                { id: 'task' },
                { forUpdate: true },
            );
            await attributeTypeTask.$.set({ isActive: true });
            await attributeTypeTask.$.save();

            const attributeTypeProject = await context.read(
                xtremFinanceData.nodes.AttributeType,
                { id: 'project' },
                { forUpdate: true },
            );
            await attributeTypeProject.$.set({ isActive: false });
            await assert.isRejected(attributeTypeProject.$.save());
            const errors = attributeTypeProject.$.context.diagnoses.filter(
                d => d.severity === ValidationSeverity.error,
            );

            assert.equal(errors.length, 1);
            assert.deepEqual(errors[0], {
                path: [],
                severity: ValidationSeverity.error,
                message: 'The attribute type is in use. You cannot deactivate it.',
            });
        }));
});
