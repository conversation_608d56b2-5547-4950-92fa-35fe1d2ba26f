import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib';

describe('Posting class definition node controls', () => {
    it('Update secondary criteria', () =>
        Test.withContext(async context => {
            const customerPostingClassDefinition = await context.read(
                xtremFinanceData.nodes.PostingClassDefinition,
                { _id: '#US|supplier|Ap' },
                { forUpdate: true },
            );

            await customerPostingClassDefinition.$.set({ additionalCriteria: 'tax' });
            await assert.isRejected(customerPostingClassDefinition.$.save(), 'The record was not updated.');

            assert.deepEqual(customerPostingClassDefinition.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['additionalCriteria'],
                    message: 'You cannot enter a secondary criteria.',
                },
            ]);

            const itemPostingClassDefinition = await context.read(
                xtremFinanceData.nodes.PostingClassDefinition,
                { _id: '#FR|item|SalesRevenue' },
                { forUpdate: true },
            );

            await itemPostingClassDefinition.$.set({ additionalCriteria: 'item' });
            await assert.isRejected(itemPostingClassDefinition.$.save(), 'The record was not updated.');

            assert.deepEqual(itemPostingClassDefinition.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['additionalCriteria'],
                    message: 'The secondary criteria needs to be tax.',
                },
            ]);

            const taxPostingClassDefinition = await context.read(
                xtremFinanceData.nodes.PostingClassDefinition,
                { _id: '#PT|tax|additionalCriteriaTest' },
                { forUpdate: true },
            );

            await taxPostingClassDefinition.$.set({ additionalCriteria: 'tax' });
            await assert.isRejected(taxPostingClassDefinition.$.save(), 'The record was not updated.');

            assert.deepEqual(taxPostingClassDefinition.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['canHaveAdditionalCriteria'],
                    message: 'You cannot enter a secondary criteria for this posting class type: tax.',
                },
                {
                    severity: 3,
                    path: ['additionalCriteria'],
                    message: 'You need to select different criteria.',
                },
                {
                    severity: 3,
                    path: ['additionalCriteria'],
                    message:
                        'The tax solutions for this legislation need to have at least one tax category that is mandatory.',
                },
            ]);
        }));
});
