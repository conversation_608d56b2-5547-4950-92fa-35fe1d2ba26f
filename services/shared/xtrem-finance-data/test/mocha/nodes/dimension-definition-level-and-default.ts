import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib/index';

describe('Dimension definition level and default node', () => {
    before(() => {});

    it('Reading Dimension definition level and default node', () =>
        Test.withContext(async context => {
            const dimensionDefinitionManufacturing = await context.read(
                xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault,
                { dimensionDefinitionLevel: 'manufacturingDirect', masterDataDefault: 'site' },
            );
            assert.instanceOf(
                dimensionDefinitionManufacturing,
                xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault,
            );
            assert.strictEqual(await dimensionDefinitionManufacturing.dimensionDefinitionLevel, 'manufacturingDirect');
            assert.strictEqual(await dimensionDefinitionManufacturing.masterDataDefault, 'site');
        }));

    it('Query getDefaultAttributesAndDimensions', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { _id: '#DEP1-S01' });
            const company = await context.read(xtremSystem.nodes.Company, { _id: '#S1' });
            const customer = await context.read(xtremMasterData.nodes.Customer, { businessEntity: '#US017' });
            const attributesAndDimensions =
                await xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault.getDefaultAttributesAndDimensions(
                    context,
                    {
                        dimensionDefinitionLevel: 'salesDirect',
                        onlyFromItem: false,
                        companyId: company._id,
                        siteId: site._id,
                        customerId: customer._id,
                    },
                );
            assert.strictEqual(
                attributesAndDimensions.dimensions,
                '{"dimensionType01":"300","dimensionType02":"CHANNELVALUE1","dimensionType03":"MANUFACTURING","dimensionType04":"DIMTYPE2VALUE2","dimensionType05":"RETAIL"}',
            );
            assert.strictEqual(attributesAndDimensions.attributes, '{"project":"AttPROJ"}');
        }));

    it('Query getDefaultAttributesAndDimensions with supplier', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { _id: '#DEP1-S01' });
            const company = await context.read(xtremSystem.nodes.Company, { _id: '#S1' });
            const supplier = await context.read(xtremMasterData.nodes.Supplier, { businessEntity: '#US017' });
            const attributesAndDimensions =
                await xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault.getDefaultAttributesAndDimensions(
                    context,
                    {
                        dimensionDefinitionLevel: 'purchasingDirect',
                        onlyFromItem: false,
                        companyId: company._id,
                        siteId: site._id,
                        supplierId: supplier._id,
                    },
                );
            assert.strictEqual(
                attributesAndDimensions.dimensions,
                '{"dimensionType01":"300","dimensionType02":"CHANNELVALUE1","dimensionType03":"WAREHOUSE","dimensionType04":"DIMTYPE2VALUE2","dimensionType05":"COMMERCIAL"}',
            );
            assert.strictEqual(attributesAndDimensions.attributes, '{"project":"AttPROJ"}');
        }));

    it('Query getDefaultAttributesAndDimensions with customer', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { _id: '#DEP1-S01' });
            const company = await context.read(xtremSystem.nodes.Company, { _id: '#S1' });
            const customer = await context.read(xtremMasterData.nodes.Customer, { businessEntity: '#US017' });
            const attributesAndDimensions =
                await xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault.getDefaultAttributesAndDimensions(
                    context,
                    {
                        dimensionDefinitionLevel: 'salesDirect',
                        onlyFromItem: false,
                        companyId: company._id,
                        siteId: site._id,
                        customerId: customer._id,
                    },
                );
            assert.strictEqual(
                attributesAndDimensions.dimensions,
                '{"dimensionType01":"300","dimensionType02":"CHANNELVALUE1","dimensionType03":"MANUFACTURING","dimensionType04":"DIMTYPE2VALUE2","dimensionType05":"RETAIL"}',
            );
            assert.strictEqual(attributesAndDimensions.attributes, '{"project":"AttPROJ"}');
        }));

    it('Query getDefaultAttributesAndDimensions with item', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { _id: '#DEP1-S01' });
            const company = await context.read(xtremSystem.nodes.Company, { _id: '#S1' });
            const item = await context.read(xtremMasterData.nodes.Item, { _id: '#Muesli' });
            const attributesAndDimensions =
                await xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault.getDefaultAttributesAndDimensions(
                    context,
                    {
                        dimensionDefinitionLevel: 'manufacturingDirect',
                        onlyFromItem: false,
                        companyId: company._id,
                        siteId: site._id,
                        itemId: item._id,
                    },
                );
            assert.strictEqual(
                attributesAndDimensions.dimensions,
                '{"dimensionType01":"300","dimensionType02":"CHANNELVALUE1","dimensionType03":"MANUFACTURING","dimensionType04":"DIMTYPE2VALUE2","dimensionType05":"RETAIL"}',
            );
            assert.strictEqual(attributesAndDimensions.attributes, '{"project":"AttPROJ","task":"Task1"}');
        }));

    it('Query getDefaultAttributesAndDimensions from item only', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { _id: '#DEP1-S01' });
            const company = await context.read(xtremSystem.nodes.Company, { _id: '#S1' });
            const item = await context.read(xtremMasterData.nodes.Item, { _id: '#Muesli' });
            const attributesAndDimensions =
                await xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault.getDefaultAttributesAndDimensions(
                    context,
                    {
                        dimensionDefinitionLevel: 'manufacturingDirect',
                        onlyFromItem: true,
                        companyId: company._id,
                        siteId: site._id,
                        itemId: item._id,
                    },
                );
            assert.strictEqual(
                attributesAndDimensions.dimensions,
                '{"dimensionType01":"300","dimensionType02":"CHANNELVALUE1","dimensionType03":"MANUFACTURING","dimensionType04":"DIMTYPE2VALUE2"}',
            );
            assert.strictEqual(attributesAndDimensions.attributes, '{"project":"AttPROJ","task":"Task1"}');
        }));

    it('Query getDefaultAttributesAndDimensionsOrderToOrder', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { _id: '#DEP1-S01' });
            const company = await context.read(xtremSystem.nodes.Company, { _id: '#S1' });
            const attributesAndDimensions =
                await xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault.getDefaultAttributesAndDimensionsOrderToOrder(
                    context,
                    {
                        dimensionDefinitionLevel: 'manufacturingOrderToOrder',
                        onlyFromItem: false,
                        companyId: company._id,
                        siteId: site._id,
                        storedAttributes: '{"project":"AttPROJ2"}',
                        storedDimensions: '{"dimensionType03":"MANUFACTURING"}',
                    },
                );
            assert.strictEqual(
                attributesAndDimensions.dimensions,
                '{"dimensionType01":"300","dimensionType03":"MANUFACTURING"}',
            );
            assert.strictEqual(attributesAndDimensions.attributes, '{"project":"AttPROJ2"}');
        }));

    it('Query getDefaultAttributesAndDimensionsOrderToOrder with supplier', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { _id: '#DEP1-S01' });
            const company = await context.read(xtremSystem.nodes.Company, { _id: '#S1' });
            const supplier = await context.read(xtremMasterData.nodes.Supplier, { businessEntity: '#US017' });
            const attributesAndDimensions =
                await xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault.getDefaultAttributesAndDimensionsOrderToOrder(
                    context,
                    {
                        dimensionDefinitionLevel: 'purchasingOrderToOrder',
                        onlyFromItem: false,
                        companyId: company._id,
                        siteId: site._id,
                        supplierId: supplier._id,
                        storedAttributes: '{"project":"AttPROJ2"}',
                        storedDimensions: '{"dimensionType03":"MANUFACTURING"}',
                    },
                );
            assert.strictEqual(
                attributesAndDimensions.dimensions,
                '{"dimensionType01":"300","dimensionType03":"MANUFACTURING","dimensionType05":"COMMERCIAL"}',
            );
            assert.strictEqual(attributesAndDimensions.attributes, '{"project":"AttPROJ2"}');
        }));

    it('Query getDefaultAttributesAndDimensionsOrderToOrder with item', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { _id: '#DEP1-S01' });
            const company = await context.read(xtremSystem.nodes.Company, { _id: '#S1' });
            const item = await context.read(xtremMasterData.nodes.Item, { _id: '#Muesli' });
            const attributesAndDimensions =
                await xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault.getDefaultAttributesAndDimensionsOrderToOrder(
                    context,
                    {
                        dimensionDefinitionLevel: 'manufacturingOrderToOrder',
                        onlyFromItem: false,
                        companyId: company._id,
                        siteId: site._id,
                        itemId: item._id,
                        storedAttributes: '{"project":"AttPROJ2"}',
                        storedDimensions: '{"dimensionType03":"MANUFACTURING"}',
                    },
                );
            assert.strictEqual(
                attributesAndDimensions.dimensions,
                '{"dimensionType01":"300","dimensionType03":"MANUFACTURING","dimensionType05":"COMMERCIAL"}',
            );
            assert.strictEqual(attributesAndDimensions.attributes, '{"project":"AttPROJ2"}');
        }));

    it('Query getAttributesAndDimensionsFromItem', () =>
        Test.withContext(async context => {
            const company = await context.read(xtremSystem.nodes.Company, { _id: '#700' });
            const attributesAndDimensions =
                await xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault.getAttributesAndDimensionsFromItem(
                    context,
                    {
                        dimensionDefinitionLevel: 'salesDirect',
                        companyId: company._id,
                    },
                );
            assert.strictEqual(
                JSON.stringify(attributesAndDimensions),
                JSON.stringify(['Project', 'Task', 'Department', 'Channel', 'Dimension type 1', 'Dimension type 2']),
            );
        }));
});
