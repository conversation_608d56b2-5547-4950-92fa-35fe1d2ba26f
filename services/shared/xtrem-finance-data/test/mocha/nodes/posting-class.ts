import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib';

describe('Posting class node controls', () => {
    before(() => {});

    it('Everything well defined', () =>
        Test.withContext(async context => {
            const postingClass = await context.create(xtremFinanceData.nodes.PostingClass, {
                type: 'item',
                name: '"{""fr-FR"": """", ""en"":""Default"", ""en-US"":""Default""}"',
                isStockItemAllowed: true,
                isDetailed: true,
                lines: [
                    {
                        _action: 'create',
                        chartOfAccount: '#TEST_US_DEFAULT',
                        definition: '#US|item|Overhead',
                        account: '#20680|TEST_US_DEFAULT',
                    },
                ],
            });
            /* does not throw */ await (() => postingClass.$.save())();
        }));

    it('Creation without lines should result in failure', () =>
        Test.withContext(async context => {
            const postingClass = await context.create(xtremFinanceData.nodes.PostingClass, {
                type: 'item',
                name: '"{""fr-FR"": """", ""en"":""Default"", ""en-US"":""Default""}"',
                isDetailed: true,
            });
            await assert.isRejected(postingClass.$.save(), 'The record was not created.');
            assert.deepEqual(postingClass.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'The posting class needs at least one line.',
                },
            ]);
        }));

    it('Only one non-detailed posting class per account entry type allowed', () =>
        Test.withContext(async context => {
            const postingClass = await context.create(xtremFinanceData.nodes.PostingClass, {
                type: 'item',
                name: '"{""fr-FR"": """", ""en"":""Default"", ""en-US"":""Default""}"',
                isDetailed: false,
                lines: [
                    {
                        _action: 'create',
                        chartOfAccount: '#TEST_US_DEFAULT',
                        definition: '#US|item|GoodsReceivedNotInvoiced',
                        account: '#20680|TEST_US_DEFAULT',
                    },
                ],
            });
            await assert.isRejected(postingClass.$.save());
            assert.deepEqual(postingClass.$.context.diagnoses, [
                {
                    message: 'Only one non-detailed posting class of type item is allowed.',
                    path: ['isDetailed'],
                    severity: 3,
                },
            ]);
        }));
});
