import type { Context } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type { integer } from '@sage/xtrem-shared';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../index';

async function setDatevId(options: {
    context: Context;
    accountDatevId: integer;
    customerDatevId: integer;
    supplierDatevId: integer;
}): Promise<void> {
    const account = await options.context.read(
        xtremFinanceData.nodes.Account,
        { _id: '#3800|DE_DEFAULT' },
        { forUpdate: true },
    );
    await account.$.set({ datevId: options.accountDatevId });
    await account.$.save();

    const customer = await options.context.read(xtremMasterData.nodes.Customer, { _id: '#US019' }, { forUpdate: true });
    await customer.$.set({ datevId: options.customerDatevId });
    await customer.$.save();

    const supplier = await options.context.read(xtremMasterData.nodes.Supplier, { _id: '#US017' }, { forUpdate: true });
    await supplier.$.set({ datevId: options.supplierDatevId });
    await supplier.$.save();
}

describe('Test node DatevConfiguration', () => {
    it('Check controls with no errors', () =>
        Test.withContext(
            async context => {
                const configuration = await context.read(
                    xtremFinanceData.nodes.DatevConfiguration,
                    { _id: '#DATEV' },
                    { forUpdate: true },
                );
                await configuration.$.set({ accountLength: 8, customerSupplierLength: 9, skrCoa: 4 });
                assert.isOk(await configuration.$.control());
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check controls on invalid ranges for accountLength and customerSupplierLength and skrCoa', () =>
        Test.withContext(
            async context => {
                const configuration = await context.read(
                    xtremFinanceData.nodes.DatevConfiguration,
                    { _id: '#DATEV' },
                    { forUpdate: true },
                );
                await configuration.$.set({ accountLength: 14, customerSupplierLength: 15, skrCoa: 100 });
                await assert.isRejected(configuration.$.save());
                assert.deepEqual(configuration.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['accountLength'],
                        message: 'Enter a number between 4 and 8.',
                    },
                    {
                        severity: 3,
                        path: ['skrCoa'],
                        message: 'Enter a number between 1 and 99.',
                    },
                    {
                        severity: 3,
                        path: ['customerSupplierLength'],
                        message: 'Enter a number between 5 and 9.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check controls on accountLength not being consistant with customerSupplierLength', () =>
        Test.withContext(
            async context => {
                const configuration = await context.read(
                    xtremFinanceData.nodes.DatevConfiguration,
                    { _id: '#DATEV' },
                    { forUpdate: true },
                );
                await configuration.$.set({ accountLength: 4, customerSupplierLength: 6 });
                await assert.isRejected(configuration.$.save());
                assert.deepEqual(configuration.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['accountLength'],
                        message: 'The account length does not match the customer and supplier ID length.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check computed properties customerRangeStart, customerRangeEnd, supplierRangeStart and supplierRangeEnd', () =>
        Test.withContext(
            async context => {
                const configuration = await context.read(xtremFinanceData.nodes.DatevConfiguration, { _id: '#DATEV' });
                assert.deepEqual(await configuration.customerRangeStart, 10000);
                assert.deepEqual(await configuration.customerRangeEnd, 69999);
                assert.deepEqual(await configuration.supplierRangeStart, 70000);
                assert.deepEqual(await configuration.supplierRangeEnd, 99999);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check mutation datevConfigurationControlsOnSave without warnings', () =>
        Test.withContext(
            async context => {
                await setDatevId({
                    context,
                    accountDatevId: 1000,
                    customerDatevId: 10000,
                    supplierDatevId: 70000,
                });
                const results = await xtremFinanceData.nodes.DatevConfiguration.datevConfigurationControlsOnSave(
                    context,
                    {
                        accountLength: 4,
                        customerRangeStart: 10000,
                        customerRangeEnd: 70001,
                        supplierRangeStart: 70000,
                        supplierRangeEnd: 99999,
                    },
                );

                assert.deepEqual(results, []);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check mutation datevConfigurationControlsOnSave with length warnings', () =>
        Test.withContext(
            async context => {
                await setDatevId({
                    context,
                    accountDatevId: 1000,
                    customerDatevId: 10000,
                    supplierDatevId: 70000,
                });
                const results = await xtremFinanceData.nodes.DatevConfiguration.datevConfigurationControlsOnSave(
                    context,
                    {
                        accountLength: 5,
                        customerRangeStart: 100000,
                        customerRangeEnd: 699999,
                        supplierRangeStart: 700000,
                        supplierRangeEnd: 999999,
                    },
                );

                assert.deepEqual(results, [
                    'The DATEV ID length for certain accounts does not match the account length. They need to be the same.',
                    'The DATEV ID length for certain customers does not match the customer and supplier ID length. They need to be the same.',
                    'The DATEV ID length for certain suppliers does not match the customer and supplier ID length. They need to be the same.',
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));

    it('Check mutation datevConfigurationControlsOnSave with range warnings', () =>
        Test.withContext(
            async context => {
                await setDatevId({
                    context,
                    accountDatevId: 1000,
                    customerDatevId: 10000,
                    supplierDatevId: 70000,
                });
                const results = await xtremFinanceData.nodes.DatevConfiguration.datevConfigurationControlsOnSave(
                    context,
                    {
                        accountLength: 4,
                        customerRangeStart: 70000,
                        customerRangeEnd: 99999,
                        supplierRangeStart: 10000,
                        supplierRangeEnd: 69999,
                    },
                );

                assert.deepEqual(results, [
                    'There are DATEV IDs for certain customers that are outside the customer ID range. They need to be within the range.',
                    'There are DATEV IDs for certain suppliers that are outside the supplier ID range. They need to be within the range.',
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption] },
        ));
});
