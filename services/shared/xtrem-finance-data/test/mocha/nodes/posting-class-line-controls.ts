import { asyncArray, Test } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib';

describe('Posting class line node controls', () => {
    before(() => {});

    it('Everything well defined', () =>
        Test.withContext(async context => {
            const postingClassLine = await context.create(xtremFinanceData.nodes.PostingClassLine, {
                postingClass: 1,
                chartOfAccount: '#TEST_US_DEFAULT',
                definition: '#US|item|StockReceipt',
                account: '#20680|TEST_US_DEFAULT',
            });
            /* does not throw */ await (() => postingClassLine.$.control())();
        }));

    it('Chart of account inactive should result in failure', () =>
        Test.withContext(async context => {
            const postingClassLine = await context.create(xtremFinanceData.nodes.PostingClassLine, {
                postingClass: 1,
                chartOfAccount: '#TEST_US_DEFAULT_INACTIVE',
                definition: '#FR|item|PurchaseExpense',
                account: '#20680|TEST_US_DEFAULT',
            });
            await assert.isRejected(postingClassLine.$.save());
        }));

    it('Posting class definition legislation differing from chart of account should result in failure', () =>
        Test.withContext(async context => {
            const postingClassLine = await context.create(xtremFinanceData.nodes.PostingClassLine, {
                postingClass: 1,
                chartOfAccount: '#FR_DEFAULT',
                definition: '#FR|item|PurchaseExpense',
                account: '#20680|TEST_US_DEFAULT',
            });
            await assert.isRejected(postingClassLine.$.save());
        }));

    it('Posting class definition used twice for a chart of account should result in failure', () =>
        Test.withContext(async context => {
            const postingClassLine = await context.create(xtremFinanceData.nodes.PostingClassLine, {
                postingClass: 1,
                chartOfAccount: await context.read(xtremStructure.nodes.ChartOfAccount, { _id: '#TEST_US_DEFAULT' }),
                definition: '#US|item|GoodsReceivedNotInvoiced',
                account: '13',
            });
            await assert.isRejected(postingClassLine.$.save());
        }));

    it('Account not included in the given chart of account should result in failure', () =>
        Test.withContext(async context => {
            const postingClassLine = await context.create(xtremFinanceData.nodes.PostingClassLine, {
                postingClass: 1,
                chartOfAccount: '#TEST_US_DEFAULT',
                definition: '#US|item|StockReceipt',
                account: 300,
            });
            await assert.isRejected(postingClassLine.$.save());
        }));

    it('Posting class type "line" not assignable', () =>
        Test.withContext(async context => {
            const postingClassLine = await context.create(xtremFinanceData.nodes.PostingClassLine, {
                postingClass: '#TEST_LINE',
                chartOfAccount: '#TEST_US_DEFAULT',
                definition: '#US|line|Account',
                account: '#1|TEST_US_DEFAULT',
            });
            await assert.isRejected(postingClassLine.$.save());
            assert.deepEqual(postingClassLine.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['definition'],
                    message: 'The record is not valid. You need to select a different record.',
                },
            ]);
        }));

    it('Posting class type "header" not assignable', () =>
        Test.withContext(async context => {
            const postingClassLine = await context.create(xtremFinanceData.nodes.PostingClassLine, {
                postingClass: '#TEST_HEADER',
                chartOfAccount: '#TEST_US_DEFAULT',
                definition: '#US|header|Account',
                account: '#1|TEST_US_DEFAULT',
            });

            await assert.isRejected(postingClassLine.$.save());
            assert.deepEqual(postingClassLine.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['definition'],
                    message: 'The record is not valid. You need to select a different record.',
                },
            ]);
        }));

    it('Posting class line getPostingClassAccounts', () =>
        Test.withContext(async context => {
            const postingClassDefinition = await context.read(xtremFinanceData.nodes.PostingClassDefinition, {
                _id: '#US|item|PurchaseExpense',
            });

            const postingClassAccounts = await xtremFinanceData.nodes.PostingClassLine.getPostingClassAccounts(
                context,
                postingClassDefinition,
            );

            await asyncArray(postingClassAccounts).forEach(async postingClassAccount => {
                const { postingClass } = postingClassAccount;
                assert.equal(await postingClass.type, await postingClassDefinition.postingClassType);
                assert.equal(await postingClass.isDetailed, await postingClassDefinition.isDetailed);
            });
        }));
});
