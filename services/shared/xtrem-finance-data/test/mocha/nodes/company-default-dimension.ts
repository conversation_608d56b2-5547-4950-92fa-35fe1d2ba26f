import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../index';

describe('CompanyDefaultDimensionNode', () => {
    before(() => {});

    it('Create company default dimension', () =>
        Test.withContext(async context => {
            const companyDefaultDimension = await context.create(xtremFinanceData.nodes.CompanyDefaultDimension, {
                company: '#US003',
                dimensionType: '#dimensionType01',
                dimensionDefinitionLevel: 'manufacturingDirect',
                masterDataDefault: 'site',
            });
            await companyDefaultDimension.$.save();
            assert.instanceOf(companyDefaultDimension, xtremFinanceData.nodes.CompanyDefaultDimension);
            assert.equal(await companyDefaultDimension.dimensionDefinitionLevel, 'manufacturingDirect');
            assert.equal(await companyDefaultDimension.masterDataDefault, 'site');
        }));
});
