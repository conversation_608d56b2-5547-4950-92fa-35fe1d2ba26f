import { Test, ValidationSeverity } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib/index';

describe('Attribute node', () => {
    it('Remove attributeRestrictedTo from attribute where attributeTypeRestrictedTo is set', () =>
        Test.withContext(async context => {
            const attribute = await context.read(
                xtremFinanceData.nodes.Attribute,
                { id: 'Task1' },
                { forUpdate: true },
            );
            await attribute.$.set({ attributeRestrictedTo: null });
            await assert.isRejected(attribute.$.save());

            const errors = attribute.$.context.diagnoses.filter(d => d.severity === ValidationSeverity.error);

            assert.equal(errors.length, 1);
            assert.deepEqual(errors[0], {
                path: [],
                severity: ValidationSeverity.error,
                message: 'You need to set the "Restricted to" to a valid attribute.',
            });
        }));

    it('Force attributeRestrictedTo to null for attribute type where attributeTypeRestrictedTo is not set', () =>
        Test.withContext(async context => {
            const newAttribute = await context.create(xtremFinanceData.nodes.Attribute, {
                id: 'Project1',
                name: 'Project 1',
                attributeType: 'project',
                attributeRestrictedTo: '#Task1|task|AttPROJ',
                site: '#500',
                item: '#Chair',
            });
            await newAttribute.$.save();

            const attribute = await context.read(xtremFinanceData.nodes.Attribute, { id: 'Project1' });
            assert.equal(await attribute.attributeRestrictedTo, null);
            assert.equal(await attribute.attributeRestrictedToId, '');
            assert.equal(await (await attribute.site)?.id, '500');
            assert.equal(await attribute.item, null);
        }));

    it('Force site and item to null for attribute type where isLinkedToSite and isLinkedToItem are both false', () =>
        Test.withContext(async context => {
            const newAttribute = await context.create(xtremFinanceData.nodes.Attribute, {
                id: 'Employee1',
                name: 'Employee 1',
                attributeType: 'employee',
                attributeRestrictedTo: '#Task1|task|AttPROJ',
                site: '#500',
                item: '#Chair',
            });
            await newAttribute.$.save();

            const attribute = await context.read(xtremFinanceData.nodes.Attribute, { id: 'Employee1' });
            assert.equal(await attribute.attributeRestrictedTo, null);
            assert.equal(await attribute.attributeRestrictedToId, '');
            assert.equal(await attribute.site, null);
            assert.equal(await attribute.item, null);
        }));

    it('Keep item for attribute type where isLinkedToItem is true', () =>
        Test.withContext(async context => {
            const newAttribute = await context.create(xtremFinanceData.nodes.Attribute, {
                id: 'Task99',
                name: 'Task 99',
                attributeType: 'task',
                attributeRestrictedTo: '#AttPROJ|project|',
                item: '#Chair',
            });
            await newAttribute.$.save();

            const attribute = await context.read(xtremFinanceData.nodes.Attribute, { id: 'Task99' });
            assert.equal(await (await attribute.attributeRestrictedTo)?.id, 'AttPROJ');
            assert.equal(await attribute.attributeRestrictedToId, 'AttPROJ');
            assert.equal(await attribute.site, null);
            assert.equal(await (await attribute.item)?.id, 'Chair');
        }));
});
