import type { Context } from '@sage/xtrem-core';
import { Test, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib';
import { FakeDocument } from '../../fixtures/lib/nodes/fake-document';

describe('Finance Transaction node', () => {
    before(() => {});

    async function getCurrencyId(context: Context, naturalKey: string): Promise<number> {
        return (await context.tryRead(xtremMasterData.nodes.Currency, { _id: `#${naturalKey}` }))?._id ?? 0;
    }

    async function getAccountId(context: Context, naturalKey: string): Promise<number> {
        return (await context.tryRead(xtremFinanceData.nodes.Account, { _id: `#${naturalKey}` }))?._id ?? 0;
    }

    async function getSupplierId(context: Context, naturalKey: string): Promise<number> {
        return (await context.read(xtremMasterData.nodes.Supplier, { _id: `#${naturalKey}` }))?._id ?? 0;
    }

    async function getSiteId(context: Context, naturalKey: string): Promise<number> {
        return (await context.read(xtremSystem.nodes.Site, { _id: `#${naturalKey}` }))?._id ?? 0;
    }

    async function getCustomerId(context: Context, naturalKey: string): Promise<number> {
        return (await context.read(xtremMasterData.nodes.Customer, { _id: `#${naturalKey}` }))?._id ?? 0;
    }

    it('Update finance transaction status with an invalid status', () =>
        Test.withContext(async context => {
            const financeTransactionRecord: xtremFinanceData.interfaces.FinanceTransactionRecord = {
                batchId: 'cdf35002-6b75-4e4b-8a41-7ab11c18a52A',
                documentNumber: 'MISC_RECEIPT1',
                documentType: 'miscellaneousStockReceipt',
                targetDocumentType: 'journalEntry',
            };

            const financeTransaction = await context.read(
                xtremFinanceData.nodes.FinanceTransaction,
                {
                    ...financeTransactionRecord,
                },
                { forUpdate: true },
            );

            await financeTransaction.$.set({
                status: 'recorded',
            });

            await assert.isRejected(financeTransaction.$.save());

            const errors = financeTransaction.$.context.diagnoses.filter(d => d.severity === ValidationSeverity.error);
            assert.equal(errors.length, 1);
            assert.deepEqual(errors[0], {
                path: [],
                severity: ValidationSeverity.error,
                message: 'The finance transaction status cannot be updated from submitted to recorded.',
            });

            await assert.isRejected(financeTransaction.$.save(), 'The record was not updated.');
        }));

    it('Create finance transaction with payment tracking reference', () =>
        Test.withContext(async context => {
            const fakeDocument = await context.read(FakeDocument, { _id: '#fin-01' });
            const paymentTracking = await context.read(xtremFinanceData.nodes.PaymentTracking, {
                document: fakeDocument._id,
            });
            const payload: xtremFinanceData.interfaces.FinanceIntegrationDocument[] = [
                {
                    batchId: '9991',
                    batchSize: 1,
                    documentSysId: fakeDocument._id,
                    documentNumber: 'fin-01',
                    documentDate: '2021-08-23',
                    documentType: 'purchaseInvoice',
                    targetDocumentType: 'accountsPayableInvoice',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: (await context.tryRead(xtremSystem.nodes.Site, { _id: '#US001' }))?._id ?? 0,
                    documentLines: [
                        {
                            baseDocumentLineSysId: (await fakeDocument.lines.elementAt(0))._id,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            customerSysId: await getCustomerId(context, 'US019'),
                            supplierSysId: await getSupplierId(context, '500'),
                            itemSysId: await getSiteId(context, 'US004'),
                            accountSysId: await getAccountId(context, '40900|TEST_US_DEFAULT'),
                            storedDimensions: { dimensionType03: 'DIMTYPE1VALUE1', dimensionType04: 'DIMTYPE2VALUE2' },
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [
                                { amountType: 'amount', amount: 144.0, taxSysId: 1, documentLineType: 'documentLine' },
                            ],
                        },
                    ],
                },
            ];
            const notifications = await xtremFinanceData.functions.sendNotificationsToAccountingEngine({
                context,
                notificationsPayload: payload,
                replyTopic: 'FakeDocument/accountingInterface',
                isUpdate: false,
                paymentTracking,
            });

            const financeTransactionRecord: xtremFinanceData.interfaces.FinanceTransactionRecord = {
                batchId: notifications.batchId,
                documentNumber: 'fin-01',
                documentType: 'purchaseInvoice',
                targetDocumentType: 'accountsPayableInvoice',
            };

            const financeTransaction = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                ...financeTransactionRecord,
            });

            // check if the finance transaction was created with the correct payment tracking reference
            assert.equal(await (await (await financeTransaction.paymentTracking)?.document)?.number, 'fin-01');
        }));
});
