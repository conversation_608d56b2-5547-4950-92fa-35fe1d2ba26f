import { Test, ValidationSeverity } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib';

describe('Test node JournalEntryType', () => {
    before(() => {});

    it('Check control on legislation', () =>
        Test.withContext(async context => {
            const entryType = await context.create(xtremFinanceData.nodes.JournalEntryType, {
                isActive: true,
                name: 'Test1',
                legislation: { id: 'GB' },
                documentType: 'salesShipment',
                immediatePosting: false,
                headerJournal: '#GB|SJ',
                headerPostingDate: 'documentDate',
                headerDescription: 'documentType',
                lines: [
                    {
                        _id: -1,
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'FR' },
                            postingClassType: 'item',
                            id: 'Stock',
                        },
                        isStockItemAllowed: true,
                        contraJournalEntryTypeLine: {
                            _id: -2,
                        },
                    },
                    {
                        _id: -2,
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'FR' },
                            postingClassType: 'item',
                            id: 'StockVariation',
                        },
                        amountType: 'amount',
                        sign: 'D',
                        isStockItemAllowed: true,
                        contraJournalEntryTypeLine: {
                            _id: -1,
                        },
                    },
                ],
            });
            // We expect an error to be thrown when trying to save because of the missing entry in targetDocumentType.
            await assert.isRejected(entryType.$.save());
            assert.deepEqual(entryType.$.context.diagnoses, [
                {
                    severity: 4,
                    path: ['targetDocumentType'],
                    message: 'JournalEntryType.targetDocumentType: property is required',
                },
            ]);

            // Now set the targetDocumentType
            await entryType.$.set({ targetDocumentType: 'journalEntry' });

            // We expect an error to be thrown when trying to save because of the legislation mismatch between lines and header
            await assert.isRejected(entryType.$.save());
            assert.deepEqual(entryType.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1', 'accountType'],
                    message:
                        'The legislation FR of the account type is different from the legislation GB of the journal entry type.',
                },
                {
                    severity: 4,
                    path: ['lines', '-1', 'amountType'],
                    message: 'JournalEntryTypeLine.amountType: property is required',
                },
            ]);

            // With the correct legislation on the header we expect the save to work without error
            const entryType1 = await context.create(xtremFinanceData.nodes.JournalEntryType, {
                isActive: true,
                name: 'Test2',
                legislation: { id: 'FR' },
                documentType: 'salesInvoice',
                targetDocumentType: 'journalEntry',
                immediatePosting: false,
                headerJournal: '#GB|SJ',
                headerPostingDate: 'documentDate',
                headerDescription: 'documentType',
                lines: [
                    {
                        _id: -3,
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'FR' },
                            postingClassType: 'item',
                            accountTypeName: 'Stock',
                        },
                        amountType: 'amount',
                        sign: 'C',
                        isStockItemAllowed: true,
                        contraJournalEntryTypeLine: {
                            _id: -4,
                        },
                    },
                    {
                        _id: -4,
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'FR' },
                            postingClassType: 'item',
                            accountTypeName: 'Stock',
                        },
                        amountType: 'amount',
                        sign: 'D',
                        isStockItemAllowed: true,
                        contraJournalEntryTypeLine: {
                            _id: -3,
                        },
                    },
                ],
            });
            await entryType1.$.save();

            // Re-read and check legislation.
            const savedEntryType = await context.read(xtremFinanceData.nodes.JournalEntryType, {
                legislation: '#US',
                documentType: 'salesInvoice',
                targetDocumentType: 'journalEntry',
            });
            assert.strictEqual(await (await savedEntryType?.legislation)?.id, 'US');
        }));

    it('Check controls on journal entry type line', () =>
        Test.withContext(async context => {
            const entryType = await context.create(xtremFinanceData.nodes.JournalEntryType, {
                isActive: true,
                name: 'Test2',
                legislation: { id: 'GB' },
                documentType: 'miscellaneousStockReceipt',
                targetDocumentType: 'journalEntry',
                immediatePosting: false,
                headerJournal: '#GB|IJ',
                headerPostingDate: 'documentDate',
                headerDescription: 'documentType',
                lines: [
                    {
                        _id: -5,
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'GB' },
                            postingClassType: 'item',
                            id: 'Stock',
                        },
                        amountType: 'amount',
                        sign: 'D',
                        isStockItemAllowed: false,
                        isNonStockItemAllowed: false,
                        isServiceItemAllowed: false,
                        isLandedCostItemAllowed: false,
                        contraJournalEntryTypeLine: {
                            _id: -6,
                        },
                    },
                    {
                        _id: -6,
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'GB' },
                            postingClassType: 'item',
                            id: 'Stock',
                        },
                        amountType: 'amount',
                        sign: 'C',
                        isStockItemAllowed: true,
                        contraJournalEntryTypeLine: {
                            _id: -5,
                        },
                    },
                ],
            });
            await assert.isRejected(entryType.$.save());

            assert.deepEqual(entryType.$.context.diagnoses, [
                {
                    message:
                        'At least one of Stock items, Non stock items, Service items or Landed cost items must be set for Account type with Posting class type Item.',
                    path: ['lines', '-5', 'accountType'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'You need to select Stock items for this movement type: Stock journal.',
                    path: ['lines', '-5', 'isStockItemAllowed'],
                    severity: ValidationSeverity.error,
                },
            ]);

            await entryType.$.set({
                lines: [
                    {
                        _id: -7,
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'GB' },
                            postingClassType: 'item',
                            id: 'Stock',
                        },
                        amountType: 'amount',
                        sign: 'D',
                        isStockItemAllowed: false,
                        isNonStockItemAllowed: true,
                        isServiceItemAllowed: true,
                        isLandedCostItemAllowed: true,
                        contraJournalEntryTypeLine: {
                            _id: -8,
                        },
                    },
                    {
                        _id: -8,
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'GB' },
                            postingClassType: 'item',
                            id: 'Stock',
                        },
                        amountType: 'amount',
                        sign: 'C',
                        isStockItemAllowed: true,
                        contraJournalEntryTypeLine: {
                            _id: -7,
                        },
                    },
                ],
            });
            await assert.isRejected(entryType.$.save());

            assert.deepEqual(entryType.$.context.diagnoses, [
                {
                    message: 'You need to select Stock items for this movement type: Stock journal.',
                    path: ['lines', '-7', 'isStockItemAllowed'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'The Non stock items may only be set for movement type Document.',
                    path: ['lines', '-7', 'isNonStockItemAllowed'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'The Non stock items are not allowed by the account type.',
                    path: ['lines', '-7', 'isNonStockItemAllowed'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'The Service items may only be set for movement type Document.',
                    path: ['lines', '-7', 'isServiceItemAllowed'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'The Service items are not allowed by the account type.',
                    path: ['lines', '-7', 'isServiceItemAllowed'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'The Landed cost items may only be set for movement type Document.',
                    path: ['lines', '-7', 'isLandedCostItemAllowed'],
                    severity: ValidationSeverity.error,
                },
                {
                    message: 'The Landed cost items are not allowed by the account type.',
                    path: ['lines', '-7', 'isLandedCostItemAllowed'],
                    severity: ValidationSeverity.error,
                },
            ]);

            await entryType.$.set({
                lines: [
                    {
                        _id: -9,
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'GB' },
                            postingClassType: 'item',
                            id: 'Stock',
                        },
                        amountType: 'amount',
                        sign: 'D',
                        isStockItemAllowed: true,
                        isNonStockItemAllowed: false,
                        isServiceItemAllowed: false,
                        isLandedCostItemAllowed: false,
                        contraJournalEntryTypeLine: {
                            _id: -10,
                        },
                    },
                    {
                        _id: -10,
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'GB' },
                            postingClassType: 'item',
                            id: 'Stock',
                        },
                        amountType: 'amount',
                        sign: 'C',
                        isStockItemAllowed: true,
                        contraJournalEntryTypeLine: {
                            _id: -9,
                        },
                    },
                ],
            });
            assert.isOk(await entryType.$.control());

            await entryType.$.set({
                lines: [
                    {
                        _id: -11,
                        movementType: 'document',
                        accountType: {
                            legislation: { id: 'GB' },
                            postingClassType: 'item',
                            id: 'ShippedNotInvoiced',
                        },
                        amountType: 'amountExcludingTax',
                        sign: 'D',
                        isStockItemAllowed: true,
                        isNonStockItemAllowed: true,
                        isServiceItemAllowed: true,
                        isLandedCostItemAllowed: false,
                        contraJournalEntryTypeLine: {
                            _id: -12,
                        },
                    },
                    {
                        _id: -12,
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'GB' },
                            postingClassType: 'item',
                            id: 'Stock',
                        },
                        amountType: 'amount',
                        sign: 'C',
                        isStockItemAllowed: true,
                        contraJournalEntryTypeLine: {
                            _id: -11,
                        },
                    },
                ],
            });
            assert.isOk(await entryType.$.control());
        }));

    it('Check control on contraJournalEntryTypeLine', () =>
        Test.withContext(async context => {
            const entryType = await context.create(xtremFinanceData.nodes.JournalEntryType, {
                isActive: true,
                name: 'Test2',
                legislation: { id: 'GB' },
                documentType: 'miscellaneousStockReceipt',
                targetDocumentType: 'journalEntry',
                immediatePosting: false,
                headerJournal: '#GB|IJ',
                headerPostingDate: 'documentDate',
                headerDescription: 'documentType',
                lines: [
                    {
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'GB' },
                            postingClassType: 'item',
                            id: 'Stock',
                        },
                        amountType: 'amount',
                        sign: 'D',
                        isStockItemAllowed: true,
                        isNonStockItemAllowed: false,
                        isServiceItemAllowed: false,
                        isLandedCostItemAllowed: false,
                        contraJournalEntryTypeLine: '#GB|workInProgress|journalEntry|3010',
                    },
                    {
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'GB' },
                            postingClassType: 'item',
                            id: 'Stock',
                        },
                        amountType: 'amount',
                        sign: 'C',
                        isStockItemAllowed: true,
                        contraJournalEntryTypeLine: '#US|miscellaneousStockReceipt|journalEntry|100',
                    },
                ],
            });
            await assert.isRejected(entryType.$.save());
            assert.deepEqual(entryType.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message:
                        'The contra journal entry type on the line needs to be the same as the journal entry type.',
                },
            ]);
        }));

    it('Check forcing of empty contraJournalEntryTypeLine for targetDocumentType !== journalEntry', () =>
        Test.withContext(async context => {
            const entryType = await context.create(xtremFinanceData.nodes.JournalEntryType, {
                isActive: true,
                name: 'Test',
                legislation: { id: 'FR' },
                documentType: 'apInvoice',
                targetDocumentType: 'accountsPayableInvoice',
                immediatePosting: false,
                headerDescription: 'documentType',
                headerAccountType: {
                    legislation: { id: 'FR' },
                    postingClassType: 'supplier',
                    id: 'Ap',
                },
                headerAmountType: 'amountIncludingTax',
                lines: [
                    {
                        _id: -1,
                        movementType: 'document',
                        accountType: {
                            legislation: { id: 'FR' },
                            postingClassType: 'item',
                            id: 'Stock',
                        },
                        amountType: 'amount',
                        isStockItemAllowed: true,
                        isNonStockItemAllowed: false,
                        isServiceItemAllowed: false,
                        isLandedCostItemAllowed: false,
                        contraJournalEntryTypeLine: {
                            _id: -2,
                        },
                    },
                    {
                        _id: -2,
                        movementType: 'document',
                        accountType: {
                            legislation: { id: 'FR' },
                            postingClassType: 'item',
                            id: 'Stock',
                        },
                        amountType: 'amount',
                        isStockItemAllowed: true,
                        contraJournalEntryTypeLine: {
                            _id: -1,
                        },
                    },
                ],
            });
            await entryType.$.save();
            // Re-read and check contra account
            const savedEntryType = await context.read(xtremFinanceData.nodes.JournalEntryType, {
                legislation: '#FR',
                documentType: 'apInvoice',
                targetDocumentType: 'accountsPayableInvoice',
            });
            assert.strictEqual(await (await savedEntryType.lines.elementAt(0)).contraJournalEntryTypeLine, null);
        }));

    it('Check mandatory contraJournalEntryTypeLine for targetDocumentType === journalEntry', () =>
        Test.withContext(async context => {
            const entryType = await context.create(xtremFinanceData.nodes.JournalEntryType, {
                isActive: true,
                name: 'Test2',
                legislation: { id: 'GB' },
                documentType: 'miscellaneousStockReceipt',
                targetDocumentType: 'journalEntry',
                immediatePosting: false,
                headerJournal: '#GB|IJ',
                headerPostingDate: 'documentDate',
                headerDescription: 'documentType',
                lines: [
                    {
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'GB' },
                            postingClassType: 'item',
                            id: 'Stock',
                        },
                        amountType: 'amount',
                        sign: 'D',
                        isStockItemAllowed: true,
                        isNonStockItemAllowed: false,
                        isServiceItemAllowed: false,
                        isLandedCostItemAllowed: false,
                    },
                    {
                        movementType: 'stockJournal',
                        accountType: {
                            legislation: { id: 'GB' },
                            postingClassType: 'item',
                            id: 'Stock',
                        },
                        amountType: 'amount',
                        sign: 'C',
                        isStockItemAllowed: true,
                    },
                ],
            });
            await assert.isRejected(entryType.$.save());
            assert.deepEqual(entryType.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-**********', 'contraJournalEntryTypeLine'],
                    message: 'If the target document type is journal entry, the contra account is required.',
                },
                {
                    severity: 3,
                    path: ['lines', '-**********', 'contraJournalEntryTypeLine'],
                    message: 'If the target document type is journal entry, the contra account is required.',
                },
            ]);
        }));
});
