import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../index';

describe('CompanyDefaultAttributeNode', () => {
    before(() => {});

    it('Create company default attribute', () =>
        Test.withContext(async context => {
            const companyDefaultAttribute = await context.create(xtremFinanceData.nodes.CompanyDefaultAttribute, {
                company: '#US006',
                attributeType: '#project',
                dimensionDefinitionLevel: 'manufacturingDirect',
                masterDataDefault: 'site',
            });
            await companyDefaultAttribute.$.save();
            assert.instanceOf(companyDefaultAttribute, xtremFinanceData.nodes.CompanyDefaultAttribute);
            assert.equal(await companyDefaultAttribute.dimensionDefinitionLevel, 'manufacturingDirect');
            assert.equal(await companyDefaultAttribute.masterDataDefault, 'site');
        }));
});
