import type { Context } from '@sage/xtrem-core';
import { AsyncArray, date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib';
import * as testLib from '../../fixtures/lib';

describe('Dowstream finance documents control', () => {
    before(() => {});

    async function createSalesDocument(context: Context): Promise<xtremFinanceData.interfaces.SalesFinanceDocument> {
        const financialSite = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
        const transactionCurrency = await context.read(xtremMasterData.nodes.Currency, { id: 'USD' });
        const billToCustomer = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' });

        const document = {
            _id: 14,
            number: 'SALDOC01',
            documentDate: date.today(),
            financialSite,
            transactionCurrency,
            billToCustomer,
            fxRateDate: date.today(),
            companyFxRate: 2,
            companyFxRateDivisor: 4,
        } as unknown as xtremFinanceData.interfaces.SalesFinanceDocument;
        return document;
    }

    async function createSalesShipmentLines(
        context: Context,
        itemId: string,
    ): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock[]> {
        const item = await context.read(xtremMasterData.nodes.Item, {
            id: itemId,
        });
        const documentLine1 = {
            _id: 131,
            item,
            computedAttributes: null,
            storedAttributes: null,
            storedDimensions: null,
            amountExcludingTax: 100,
            stockMovements: new AsyncArray(() => [
                { movementAmount: 90, orderAmount: 90 },
                { movementAmount: 80, orderAmount: 80 },
            ]),
        } as unknown as xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock;
        const documentLine2 = {
            _id: 132,
            item,
            computedAttributes: null,
            storedAttributes: null,
            storedDimensions: null,
            amountExcludingTax: 100,
            stockMovements: new AsyncArray(() => [
                { movementAmount: 90, orderAmount: 80 },
                { movementAmount: 100, orderAmount: 120 },
            ]),
        } as unknown as xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock;

        const documentLines: xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock[] = [];
        documentLines.push(documentLine1);
        documentLines.push(documentLine2);
        return documentLines;
    }

    it('Create notifications from a sales shipment', () =>
        Test.withContext(async context => {
            const document = await createSalesDocument(context);
            let documentLines = await createSalesShipmentLines(context, 'Muesli');
            let notificationPayload = await xtremFinanceData.functions.getSalesShipmentNotificationPayload(
                document,
                documentLines,
            );

            // Test of the function getTotalMovementAmounts result
            assert.deepEqual(notificationPayload[0].documentLines[0].amounts[0], {
                amountType: 'amount',
                amount: -170,
                documentLineType: 'documentLine',
            });
            assert.deepEqual(notificationPayload[0].documentLines[1].amounts[0], {
                amountType: 'amount',
                amount: -190,
                documentLineType: 'documentLine',
            });

            let createFinanceDocumentsReturn =
                await xtremFinanceData.functions.FinanceDocumentGeneration.financeDownstreamDocumentControlFromNotificationPayload(
                    context,
                    notificationPayload,
                );

            assert.equal(createFinanceDocumentsReturn.validationMessages.length, 0);

            documentLines = await createSalesShipmentLines(context, 'StockItem03');
            notificationPayload = await xtremFinanceData.functions.getSalesShipmentNotificationPayload(
                document,
                documentLines,
            );
            createFinanceDocumentsReturn =
                await xtremFinanceData.functions.FinanceDocumentGeneration.financeDownstreamDocumentControlFromNotificationPayload(
                    context,
                    notificationPayload,
                );

            assert.notEqual(createFinanceDocumentsReturn.validationMessages.length, 0);
            assert.equal(
                createFinanceDocumentsReturn.validationMessages[0].message,
                'Enter a posting class for the StockItem03 item.',
            );
        }));

    it('Notification payload incorrect, cannot find customer, supplier, currency, item and tax', () =>
        Test.withContext(async context => {
            const notificationPayload = await testLib.functions.Common.getNotificationPayloadForJournalEntry(context);

            notificationPayload[0].documentLines[0].currencySysId = 9999;
            notificationPayload[0].documentLines[0].customerSysId = 9999;
            notificationPayload[0].documentLines[0].supplierSysId = 9999;
            notificationPayload[0].documentLines[0].itemSysId = 9999;
            notificationPayload[0].documentLines[0].amounts[0].taxSysId = 9999;

            const createFinanceDocumentsReturn =
                await xtremFinanceData.functions.FinanceDocumentGeneration.financeDownstreamDocumentControlFromNotificationPayload(
                    context,
                    notificationPayload,
                );

            assert.equal(createFinanceDocumentsReturn.validationMessages.length, 5);
        }));

    it('Notification payload incorrect, cannot find site', () =>
        Test.withContext(async context => {
            const notificationPayload = await testLib.functions.Common.getNotificationPayloadForJournalEntry(context);

            notificationPayload[0].financialSiteSysId = 9999;

            const createFinanceDocumentsReturn =
                await xtremFinanceData.functions.FinanceDocumentGeneration.financeDownstreamDocumentControlFromNotificationPayload(
                    context,
                    notificationPayload,
                );

            assert.equal(createFinanceDocumentsReturn.validationMessages.length, 1);
        }));

    it('Notification payload incorrect, cannot find ', () =>
        Test.withContext(async context => {
            const notificationPayload =
                await testLib.functions.Common.getNotificationPayloadForAccountsReceivableInvoice(context);

            const createFinanceDocumentsReturn =
                await xtremFinanceData.functions.FinanceDocumentGeneration.financeDownstreamDocumentControlFromNotificationPayload(
                    context,
                    notificationPayload,
                );

            assert.equal(createFinanceDocumentsReturn.validationMessages.length, 0);
        }));
});
