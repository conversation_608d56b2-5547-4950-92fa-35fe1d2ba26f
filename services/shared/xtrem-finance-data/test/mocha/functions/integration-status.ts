import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib';

describe('Integration status', () => {
    before(() => {});

    /**
     * Creates an object with all data to create an instance of xtremFinanceData.nodes.FinanceTransaction
     * @param status: xtremFinanceData.enums.FinanceIntegrationStatus
     * @param documentType: xtremFinanceData.enums.FinanceDocumentType
     * @param targetDocumentType: xtremFinanceData.enums.TargetDocumentType
     * @return NodeCreateData<xtremFinanceData.nodes.FinanceTransaction>
     */
    async function _getNewFinanceIntegrationStatusNodeData(
        context: Context,
        status: xtremFinanceData.enums.FinanceIntegrationStatus,
        documentType: xtremFinanceData.enums.FinanceDocumentType,
        targetDocumentType: xtremFinanceData.enums.TargetDocumentType,
    ): Promise<NodeCreateData<xtremFinanceData.nodes.FinanceTransaction>> {
        const financialSite = await context.read(xtremSystem.nodes.Site, { _id: 'US001' });
        return {
            batchId: '7a52c916-8735-40ba-b8ff-65bb3055e033',
            documentNumber: 'DOC001',
            documentType,
            targetDocumentType,
            targetDocumentNumber: '',
            status,
            message: '',
            financialSite,
        };
    }

    it('Integration status with error and not recorded', () =>
        Test.withContext(async context => {
            const financeIntegrationStatus1Data: NodeCreateData<xtremFinanceData.nodes.FinanceTransaction> =
                await _getNewFinanceIntegrationStatusNodeData(
                    context,
                    'failed',
                    'miscellaneousStockReceipt',
                    'journalEntry',
                );
            const financeIntegrationStatus1 = await context.create(
                xtremFinanceData.nodes.FinanceTransaction,
                financeIntegrationStatus1Data,
            );
            await financeIntegrationStatus1.$.save();

            const financeIntegrationStatus2Data = await _getNewFinanceIntegrationStatusNodeData(
                context,
                'notRecorded',
                'miscellaneousStockReceipt',
                'accountsReceivableInvoice',
            );
            const financeIntegrationStatus2 = await context.create(
                xtremFinanceData.nodes.FinanceTransaction,
                financeIntegrationStatus2Data,
            );
            await financeIntegrationStatus2.$.save();

            const financeIntegrationStatus3Data = await _getNewFinanceIntegrationStatusNodeData(
                context,
                'pending',
                'miscellaneousStockReceipt',
                'accountsPayableInvoice',
            );
            const financeIntegrationStatus3 = await context.create(
                xtremFinanceData.nodes.FinanceTransaction,
                financeIntegrationStatus3Data,
            );
            await financeIntegrationStatus3.$.save();

            assert.equal(
                await xtremFinanceData.functions.getDocumentIntegrationStatus(
                    context,
                    'miscellaneousStockReceipt',
                    'DOC001',
                ),
                'notRecorded',
            );
        }));

    it('Integration status with error first', () =>
        Test.withContext(async context => {
            const financeIntegrationStatus1Data: NodeCreateData<xtremFinanceData.nodes.FinanceTransaction> =
                await _getNewFinanceIntegrationStatusNodeData(
                    context,
                    'failed',
                    'miscellaneousStockReceipt',
                    'journalEntry',
                );
            const financeIntegrationStatus1 = await context.create(
                xtremFinanceData.nodes.FinanceTransaction,
                financeIntegrationStatus1Data,
            );
            await financeIntegrationStatus1.$.save();

            const financeIntegrationStatus2Data = await _getNewFinanceIntegrationStatusNodeData(
                context,
                'error',
                'miscellaneousStockReceipt',
                'accountsReceivableInvoice',
            );
            const financeIntegrationStatus2 = await context.create(
                xtremFinanceData.nodes.FinanceTransaction,
                financeIntegrationStatus2Data,
            );
            await financeIntegrationStatus2.$.save();

            const financeIntegrationStatus3Data = await _getNewFinanceIntegrationStatusNodeData(
                context,
                'pending',
                'miscellaneousStockReceipt',
                'accountsPayableInvoice',
            );
            const financeIntegrationStatus3 = await context.create(
                xtremFinanceData.nodes.FinanceTransaction,
                financeIntegrationStatus3Data,
            );
            await financeIntegrationStatus3.$.save();

            assert.equal(
                await xtremFinanceData.functions.getDocumentIntegrationStatus(
                    context,
                    'miscellaneousStockReceipt',
                    'DOC001',
                ),
                'error',
            );
        }));

    it('Integration status with error', () =>
        Test.withContext(async context => {
            const financeIntegrationStatus1Data: NodeCreateData<xtremFinanceData.nodes.FinanceTransaction> =
                await _getNewFinanceIntegrationStatusNodeData(
                    context,
                    'failed',
                    'miscellaneousStockReceipt',
                    'journalEntry',
                );
            const financeIntegrationStatus1 = await context.create(
                xtremFinanceData.nodes.FinanceTransaction,
                financeIntegrationStatus1Data,
            );
            await financeIntegrationStatus1.$.save();

            const financeIntegrationStatus2Data = await _getNewFinanceIntegrationStatusNodeData(
                context,
                'notRecorded',
                'miscellaneousStockReceipt',
                'accountsReceivableInvoice',
            );
            const financeIntegrationStatus2 = await context.create(
                xtremFinanceData.nodes.FinanceTransaction,
                financeIntegrationStatus2Data,
            );
            await financeIntegrationStatus2.$.save();

            const financeIntegrationStatus3Data = await _getNewFinanceIntegrationStatusNodeData(
                context,
                'pending',
                'miscellaneousStockReceipt',
                'accountsPayableInvoice',
            );
            const financeIntegrationStatus3 = await context.create(
                xtremFinanceData.nodes.FinanceTransaction,
                financeIntegrationStatus3Data,
            );
            await financeIntegrationStatus3.$.save();

            assert.equal(
                await xtremFinanceData.functions.getDocumentIntegrationStatus(
                    context,
                    'miscellaneousStockReceipt',
                    'DOC001',
                ),
                'notRecorded',
            );
        }));

    it('Integration status with no record', () =>
        Test.withContext(async context => {
            assert.equal(
                await xtremFinanceData.functions.getDocumentIntegrationStatus(
                    context,
                    'miscellaneousStockReceipt',
                    'DOC001',
                ),
                'toBeRecorded',
            );
        }));

    it('Integration status with no errors', () =>
        Test.withContext(async context => {
            const financeIntegrationStatus1Data: NodeCreateData<xtremFinanceData.nodes.FinanceTransaction> =
                await _getNewFinanceIntegrationStatusNodeData(
                    context,
                    'posted',
                    'miscellaneousStockReceipt',
                    'journalEntry',
                );
            const financeIntegrationStatus1 = await context.create(
                xtremFinanceData.nodes.FinanceTransaction,
                financeIntegrationStatus1Data,
            );
            await financeIntegrationStatus1.$.save();

            const financeIntegrationStatus2Data = await _getNewFinanceIntegrationStatusNodeData(
                context,
                'recording',
                'miscellaneousStockReceipt',
                'accountsReceivableInvoice',
            );
            const financeIntegrationStatus2 = await context.create(
                xtremFinanceData.nodes.FinanceTransaction,
                financeIntegrationStatus2Data,
            );
            await financeIntegrationStatus2.$.save();

            const financeIntegrationStatus3Data = await _getNewFinanceIntegrationStatusNodeData(
                context,
                'submitted',
                'miscellaneousStockReceipt',
                'accountsPayableInvoice',
            );
            const financeIntegrationStatus3 = await context.create(
                xtremFinanceData.nodes.FinanceTransaction,
                financeIntegrationStatus3Data,
            );
            await financeIntegrationStatus3.$.save();

            assert.equal(
                await xtremFinanceData.functions.getDocumentIntegrationStatus(
                    context,
                    'miscellaneousStockReceipt',
                    'DOC001',
                ),
                'recording',
            );

            assert.equal(
                await (
                    await xtremFinanceData.functions.getFinanceTransactionFromOrigin(
                        context,
                        1,
                        'miscellaneousStockReceipt',
                    )
                )?.status,
                'submitted',
            );
        }));

    it('Get finance integration status from finance integration app status', () =>
        Test.withContext(() => {
            assert.equal(
                xtremFinanceData.functions.getFinanceIntegrationStatusFromFinanceIntegrationAppStatus('success'),
                'posted',
            );

            assert.equal(
                xtremFinanceData.functions.getFinanceIntegrationStatusFromFinanceIntegrationAppStatus('error'),
                'failed',
            );

            assert.equal(
                xtremFinanceData.functions.getFinanceIntegrationStatusFromFinanceIntegrationAppStatus('pending'),
                'submitted',
            );

            assert.equal(
                xtremFinanceData.functions.getFinanceIntegrationStatusFromFinanceIntegrationAppStatus('not'),
                'recorded',
            );
        }));

    it('Get finance integration app status from finance integration status', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { _id: 'US001' });
            assert.equal(
                await xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(context, {
                    financeIntegrationStatus: 'toBeRecorded',
                    externalIntegration: true,
                    financialSite: await xtremFinanceData.functions.getFinancialSite(site),
                    documentType: 'salesInvoice',
                    targetDocumentType: 'journalEntry',
                }),
                'notPosted',
            );

            assert.equal(
                await xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(context, {
                    financeIntegrationStatus: 'recording',
                    externalIntegration: true,
                    financialSite: await xtremFinanceData.functions.getFinancialSite(site),
                    documentType: 'salesInvoice',
                    targetDocumentType: 'journalEntry',
                }),
                'generationInProgress',
            );

            assert.equal(
                await xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(context, {
                    financeIntegrationStatus: 'pending',
                    externalIntegration: true,
                    financialSite: await xtremFinanceData.functions.getFinancialSite(site),
                    documentType: 'salesInvoice',
                    targetDocumentType: 'journalEntry',
                }),
                'generationInProgress',
            );

            assert.equal(
                await xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(context, {
                    financeIntegrationStatus: 'error',
                    externalIntegration: true,
                    financialSite: await xtremFinanceData.functions.getFinancialSite(site),
                    documentType: 'salesInvoice',
                    targetDocumentType: 'journalEntry',
                }),
                'generationError',
            );

            assert.equal(
                await xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(context, {
                    financeIntegrationStatus: 'recorded',
                    externalIntegration: true,
                    financialSite: await xtremFinanceData.functions.getFinancialSite(site),
                    documentType: 'salesInvoice',
                    targetDocumentType: 'journalEntry',
                }),
                'generated',
            );

            assert.equal(
                await xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(context, {
                    financeIntegrationStatus: 'recorded',
                    externalIntegration: false,
                    financialSite: await xtremFinanceData.functions.getFinancialSite(site),
                    documentType: 'salesInvoice',
                    targetDocumentType: 'journalEntry',
                }),
                'posted',
            );

            assert.equal(
                await xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(context, {
                    financeIntegrationStatus: 'notRecorded',
                    externalIntegration: true,
                    financialSite: await xtremFinanceData.functions.getFinancialSite(site),
                    documentType: 'salesInvoice',
                    targetDocumentType: 'journalEntry',
                }),
                'generationError',
            );

            assert.equal(
                await xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(context, {
                    financeIntegrationStatus: 'submitted',
                    externalIntegration: true,
                    financialSite: await xtremFinanceData.functions.getFinancialSite(site),
                    documentType: 'salesInvoice',
                    targetDocumentType: 'journalEntry',
                }),
                'postingInProgress',
            );

            assert.equal(
                await xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(context, {
                    financeIntegrationStatus: 'posted',
                    externalIntegration: true,
                    financialSite: await xtremFinanceData.functions.getFinancialSite(site),
                    documentType: 'salesInvoice',
                    targetDocumentType: 'journalEntry',
                }),
                'posted',
            );

            assert.equal(
                await xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(context, {
                    financeIntegrationStatus: 'failed',
                    externalIntegration: true,
                    financialSite: await xtremFinanceData.functions.getFinancialSite(site),
                    documentType: 'salesInvoice',
                    targetDocumentType: 'journalEntry',
                }),
                'postingError',
            );

            assert.equal(
                await xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(context, {
                    financeIntegrationStatus: 'pending',
                    externalIntegration: true,
                    financialSite: await xtremFinanceData.functions.getFinancialSite(site),
                    documentType: 'purchaseReceipt',
                    targetDocumentType: 'journalEntry',
                }),
                'toBeGenerated',
            );
        }));

    it('Get finance integration app information', () =>
        Test.withContext(async context => {
            assert.equal(
                await xtremFinanceData.functions.getFinanceIntegrationApp(context, 'IJ001', 'journalEntry'),
                'intacct',
            );

            assert.equal(
                await xtremFinanceData.functions.getFinanceIntegrationAppRecordId(context, 'IJ002', 'journalEntry'),
                '321',
            );

            assert.equal(
                await xtremFinanceData.functions.getFinanceIntegrationAppUrl(context, 'IJ002', 'journalEntry'),
                'http://something-else.com',
            );
        }));

    it('Status mapping', () =>
        Test.withContext(() => {
            assert.equal(xtremFinanceData.functions.statusMapping('toBeRecorded', false), 'inProgress');
            assert.equal(xtremFinanceData.functions.statusMapping('recording', false), 'inProgress');
            assert.equal(xtremFinanceData.functions.statusMapping('recording', false), 'inProgress');
            assert.equal(xtremFinanceData.functions.statusMapping('error', false), 'error');
            assert.equal(xtremFinanceData.functions.statusMapping('recorded', true), 'inProgress');
            assert.equal(xtremFinanceData.functions.statusMapping('recorded', false), 'posted');
            assert.equal(xtremFinanceData.functions.statusMapping('notRecorded', false), 'error');
            assert.equal(xtremFinanceData.functions.statusMapping('submitted', false), 'inProgress');
            assert.equal(xtremFinanceData.functions.statusMapping('posted', false), 'posted');
            assert.equal(xtremFinanceData.functions.statusMapping('failed', false), 'error');
        }));

    it('canUpdateFinanceTransactionStatus control', () =>
        Test.withContext(() => {
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('recording', 'pending'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('recording', 'error'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('recording', 'recorded'), false);
            assert.equal(
                xtremFinanceData.functions.canUpdateFinanceTransactionStatus('recording', 'notRecorded'),
                false,
            );
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('recording', 'submitted'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('recording', 'posted'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('recording', 'failed'), false);

            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('pending', 'error'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('pending', 'recorded'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('pending', 'notRecorded'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('pending', 'submitted'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('pending', 'posted'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('pending', 'failed'), false);

            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('error', 'pending'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('error', 'recorded'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('error', 'notRecorded'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('error', 'submitted'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('error', 'posted'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('error', 'failed'), false);

            assert.equal(
                xtremFinanceData.functions.canUpdateFinanceTransactionStatus('notRecorded', 'recorded'),
                false,
            );
            assert.equal(
                xtremFinanceData.functions.canUpdateFinanceTransactionStatus('notRecorded', 'submitted'),
                false,
            );
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('notRecorded', 'posted'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('notRecorded', 'failed'), false);

            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('recorded', 'submitted'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('recorded', 'posted'), false);
            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('recorded', 'failed'), false);

            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('failed', 'posted'), false);

            assert.equal(xtremFinanceData.functions.canUpdateFinanceTransactionStatus('recorded', 'recording'), true);
        }));
});
