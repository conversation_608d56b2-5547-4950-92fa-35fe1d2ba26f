import type { Context } from '@sage/xtrem-core';
import { date } from '@sage/xtrem-core';
import * as xtremFinanceData from '../../../lib/index';
import { FakePaymentDocument } from '../../fixtures/lib/nodes';

export async function createFakePaymentDocumentWithDates(
    context: Context,
    voidDate: date | null,
    paymentDate: date,
): Promise<FakePaymentDocument> {
    const originalOpenItem = await context
        .query(xtremFinanceData.nodes.BaseOpenItem, { filter: { documentNumber: 'SI1' }, first: 1 })
        .elementAt(0);
    return context.create(FakePaymentDocument, {
        number: 'RCTE0001',
        bankAccount: '#BAN',
        financialSite: '#US006',
        businessRelation: '#US019',
        paymentMethod: 'cash',
        reference: 'Payment reference',
        paymentDate,
        voidDate,
        isVoided: true,
        postingDate: date.today(),
        currency: '#USD',
        exchangeRateDate: date.today(),
        companyExchangeRate: 1,
        companyExchangeRateDivisor: 1,
        postingStatus: 'draft',
        amount: 100,
        amountBankCurrency: 100,
        lines: [
            {
                amount: 100,
                origin: 'invoice',
                originalOpenItem,
                originalNodeFactory: '#FakeDocument',
            },
        ],
    });
}
