import type { Collection, Context } from '@sage/xtrem-core';
import { AsyncArray, Test, assertDeepPartialMatch, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import type * as xtremTax from '@sage/xtrem-tax';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib';

describe('Accounting staging', () => {
    before(() => {});

    async function createStockDocument(context: Context): Promise<{
        document: xtremFinanceData.interfaces.FinanceOriginDocument;
        documentLines: xtremFinanceData.interfaces.FinanceOriginDocumentLine[];
    }> {
        const financialSite = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
        const transactionCurrency = await context.read(xtremMasterData.nodes.Currency, { id: 'USD' });
        const item = await context.read(xtremMasterData.nodes.Item, { id: 'Muesli' });

        const document = {
            _id: 14,
            number: 'DOC001',
            documentDate: date.today(),
            financialSite,
            transactionCurrency,
            fxRateDate: date.today(),
            companyFxRate: 2,
            companyFxRateDivisor: 4,
        } as unknown as xtremFinanceData.interfaces.FinanceOriginDocument;

        const documentLine1 = {
            _id: 131,
            item,
            site: financialSite,
            stockMovements: new AsyncArray(() => [
                { movementAmount: 20, orderAmount: 20 },
                { movementAmount: 20, orderAmount: 25 },
            ]),
            computedAttributes: null,
            storedAttributes: null,
            storedDimensions: null,
        } as unknown as xtremFinanceData.interfaces.FinanceOriginDocumentLine;

        const documentLine2 = {
            _id: 132,
            item,
            site: financialSite,
            stockMovements: new AsyncArray(() => [
                { movementAmount: 20, orderAmount: 20 },
                { movementAmount: 30, orderAmount: 30 },
            ]),
            computedAttributes: null,
            storedAttributes: null,
            storedDimensions: null,
        } as unknown as xtremFinanceData.interfaces.FinanceOriginDocumentLine;
        const documentLines: xtremFinanceData.interfaces.FinanceOriginDocumentLine[] = [];

        documentLines.push(documentLine1);
        documentLines.push(documentLine2);

        return { document, documentLines };
    }

    async function createApInvoice(context: Context): Promise<{
        document: xtremFinanceData.interfaces.ApInvoice;
        documentLines: xtremFinanceData.interfaces.ApArInvoiceLine[];
    }> {
        const financialSite = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
        const transactionCurrency = await context.read(xtremMasterData.nodes.Currency, { id: 'USD' });
        const billBySupplier = await context.read(xtremMasterData.nodes.Supplier, { _id: '#500' });
        const headerAccount = await context.read(xtremFinanceData.nodes.Account, { _id: '#1|TEST_US_DEFAULT' });
        const line1Account = await context.read(xtremFinanceData.nodes.Account, { _id: '#355000|TEST_US_DEFAULT' });
        const line2Account = await context.read(xtremFinanceData.nodes.Account, { _id: '#603000|TEST_US_DEFAULT' });

        const document: xtremFinanceData.interfaces.ApInvoice = {
            _id: 1,
            number: 'AP-**********',
            financialSite,
            transactionCurrency,
            billBySupplier,
            documentDate: date.today(),
            fxRateDate: date.today(),
            companyFxRate: 2,
            companyFxRateDivisor: 4,
            account: headerAccount,
            purchaseDocumentNumber: 'PI1',
            purchaseDocumentSysId: 1001,
        } as unknown as xtremFinanceData.interfaces.ApInvoice;

        const documentLine1 = {
            _id: 1,
            amountExcludingTax: 1000,
            account: line1Account,
            financialSite,
            currency: transactionCurrency,
            storedAttributes: null,
            storedDimensions: null,
        } as unknown as xtremFinanceData.interfaces.ApArInvoiceLine;

        const documentLine2 = {
            _id: 1,
            amountExcludingTax: 2000,
            account: line2Account,
            financialSite,
            currency: transactionCurrency,
            storedAttributes: null,
            storedDimensions: null,
        } as unknown as xtremFinanceData.interfaces.ApArInvoiceLine;

        const documentLines: xtremFinanceData.interfaces.ApArInvoiceLine[] = [];

        documentLines.push(documentLine1);
        documentLines.push(documentLine2);

        return { document, documentLines };
    }

    async function createArInvoice(context: Context): Promise<{
        document: xtremFinanceData.interfaces.ArInvoice;
        documentLines: xtremFinanceData.interfaces.ApArInvoiceLine[];
    }> {
        const financialSite = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
        const transactionCurrency = await context.read(xtremMasterData.nodes.Currency, { id: 'USD' });
        const billToCustomer = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' });
        const headerAccount = await context.read(xtremFinanceData.nodes.Account, { _id: '#1|TEST_US_DEFAULT' });
        const line1Account = await context.read(xtremFinanceData.nodes.Account, { _id: '#355000|TEST_US_DEFAULT' });
        const line2Account = await context.read(xtremFinanceData.nodes.Account, { _id: '#603000|TEST_US_DEFAULT' });

        const document: xtremFinanceData.interfaces.ArInvoice = {
            _id: 1,
            number: 'AR-**********',
            financialSite,
            transactionCurrency,
            billToCustomer,
            documentDate: date.today(),
            fxRateDate: date.today(),
            companyFxRate: 2,
            companyFxRateDivisor: 4,
            account: headerAccount,
            salesDocumentNumber: 'SI4',
            salesDocumentSysId: 4,
        } as any; // TODO: convert to node instance

        const documentLine1 = {
            _id: 1,
            amountExcludingTax: 1000,
            account: line1Account,
            financialSite,
            currency: transactionCurrency,
            storedAttributes: null,
            storedDimensions: null,
            taxes: [] as unknown as Collection<xtremTax.nodes.BaseLineTax>,
        } as unknown as xtremFinanceData.interfaces.ApArInvoiceLine;

        const documentLine2 = {
            _id: 1,
            amountExcludingTax: 2000,
            account: line2Account,
            financialSite,
            currency: transactionCurrency,
            storedAttributes: null,
            storedDimensions: null,
            taxes: [] as unknown as Collection<xtremTax.nodes.BaseLineTax>,
        } as unknown as xtremFinanceData.interfaces.ApArInvoiceLine;

        const documentLines: xtremFinanceData.interfaces.ApArInvoiceLine[] = [];

        documentLines.push(documentLine1);
        documentLines.push(documentLine2);

        return { document, documentLines };
    }

    it('Get stock amount by evaluation method', () =>
        Test.withContext(() => {
            let amount = xtremFinanceData.functions.getStockLineAmount('StockReceiptLine', 1000);
            // for a miscellaneous stock receipt and the method "average cost" on the item-site for Chair/US001
            // since we refactor the getStockLineAmount ans use always the movement amount, we expect 1000
            assert.equal(amount, 1000);

            amount = xtremFinanceData.functions.getStockLineAmount('StockAdjustmentLine', 112);
            // for a stock adjustment and the method "standard cost" on the item-site for Milk/US002
            // we expect the movement amount to be taken, which is 112
            assert.equal(amount, 112);

            amount = xtremFinanceData.functions.getStockLineAmount('StockIssueLine', -2);
            assert.equal(amount, 2);
        }));

    // TODO: Check if can be done
    it.skip('Create notifications from an ap invoice', () =>
        Test.withContext(
            async context => {
                const { document, documentLines } = await createApInvoice(context);

                const notifications = await xtremFinanceData.functions.accountsPayableInvoiceNotification(
                    context,
                    document,
                    documentLines,
                );

                assert.equal(notifications.batchSize, 4);

                const financeTransactionRecord: xtremFinanceData.interfaces.FinanceTransactionRecord = {
                    batchId: notifications.batchId,
                    documentNumber: await document.number,
                    documentType: 'apInvoice',
                    targetDocumentType: 'journalEntry',
                };

                const financeIntegrationStatus = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                    ...financeTransactionRecord,
                });

                assert.equal(await financeIntegrationStatus.message, '');

                const replyPayload: xtremFinanceData.interfaces.FinanceTransactionData = {
                    ...financeTransactionRecord,
                    validationMessages: [],
                    targetDocumentNumber: '',
                    targetDocumentSysId: 0,
                    status: 'recorded',
                };

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '4434';
                (context as any)._contextValues.replyTopic = 'AccountsPayableInvoice/accountingInterface';
                await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, replyPayload);

                const newFinanceIntegrationStatus = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                    ...financeTransactionRecord,
                });

                assert.equal(await newFinanceIntegrationStatus.status, 'recorded');
            },
            {
                today: '2021-08-23',
            },
        ));
    // TODO: Check if can be done
    it.skip('Create notifications from an ar invoice', () =>
        Test.withContext(
            async context => {
                const { document, documentLines } = await createArInvoice(context);

                const notifications = await xtremFinanceData.functions.accountsReceivableInvoiceNotification(
                    context,
                    document,
                    documentLines,
                );

                assert.equal(notifications.batchSize, 4);

                const financeTransactionRecord: xtremFinanceData.interfaces.FinanceTransactionRecord = {
                    batchId: notifications.batchId,
                    documentNumber: await document.number,
                    documentType: 'arInvoice',
                    targetDocumentType: 'journalEntry',
                };

                const financeIntegrationStatus = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                    ...financeTransactionRecord,
                });

                assert.equal(await financeIntegrationStatus.message, '');

                const replyPayload: xtremFinanceData.interfaces.FinanceTransactionData = {
                    ...financeTransactionRecord,
                    targetDocumentNumber: '',
                    targetDocumentSysId: 0,
                    validationMessages: [],
                    status: 'recorded',
                };

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '4434';
                (context as any)._contextValues.replyTopic = 'AccountsReceivableInvoice/accountingInterface';
                await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, replyPayload);

                const newFinanceIntegrationStatus = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                    ...financeTransactionRecord,
                });

                assert.equal(await newFinanceIntegrationStatus.status, 'recorded');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('get movements amounts', () =>
        Test.withContext(async context => {
            const { documentLines } = await createStockDocument(context);
            assert.deepEqual(await xtremFinanceData.functions.getStockMovementsAmounts(documentLines[0]), {
                movementAmount: 40,
                orderAmount: 45,
            });
            assert.deepEqual(await xtremFinanceData.functions.getStockMovementsAmounts(documentLines[1]), {
                movementAmount: 50,
                orderAmount: 50,
            });
        }));

    it('React to finance integration reply - invalid status', () =>
        Test.withContext(async context => {
            const replyPayload: xtremFinanceData.interfaces.FinanceTransactionData = {
                batchId: 'cdf35002-6b75-4e4b-8a41-7ab11c18a52A',
                documentNumber: 'MISC_RECEIPT1',
                documentType: 'miscellaneousStockReceipt',
                targetDocumentType: 'journalEntry',
                validationMessages: [],
                targetDocumentNumber: '',
                targetDocumentSysId: 0,
                status: 'recorded',
            };

            // hack to make the state of the context when been triggered by a notification
            (context as any)._contextValues.notificationId = '14434';
            (context as any)._contextValues.replyTopic = 'PurchaseReturn/accountingInterface';
            assert.equal(await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, replyPayload), false);
        }));

    describe('getFilteredDocumentLines', () => {
        it('should filter out document lines with amounts equal to 0 (for workOrderClose)', () => {
            const documents = [
                {
                    documentNumber: 'TEST1',
                    sourceDocumentType: 'workOrderClose',
                    documentLines: [
                        {
                            amounts: [{ amount: 0 }, { amount: 0 }],
                        },
                        {
                            amounts: [{ amount: 100 }, { amount: 150 }],
                        },
                        {
                            amounts: [{ amount: 0 }, { amount: 200 }, { amount: 0 }, { amount: 250 }, { amount: 0 }],
                        },
                    ],
                },
                {
                    documentNumber: 'TEST2',
                    sourceDocumentType: 'workOrderClose',
                    documentLines: [{ amounts: [{ amount: 0 }, { amount: 0 }] }],
                },
                {
                    documentNumber: 'TEST3',
                    sourceDocumentType: 'workOrderClose',
                    documentLines: [{ amounts: [{ amount: 300 }, { amount: 350 }] }],
                },
            ] as xtremFinanceData.interfaces.FinanceIntegrationDocument[];

            const result = xtremFinanceData.functions.getFilteredNotificationsPayload(documents);

            assertDeepPartialMatch(result, [
                {
                    documentNumber: 'TEST1',
                    documentLines: [
                        { amounts: [{ amount: 100 }, { amount: 150 }] },
                        { amounts: [{ amount: 200 }, { amount: 250 }] },
                    ],
                },
                {
                    documentNumber: 'TEST3',
                    documentLines: [{ amounts: [{ amount: 300 }, { amount: 350 }] }],
                },
            ]);
        });

        it('should filter out document lines with all amounts equal to 0 (for workOrderClose)', () => {
            const documents = [
                {
                    documentNumber: 'TEST4',
                    sourceDocumentType: 'workOrderClose',
                    documentLines: [
                        {
                            amounts: [{ amount: 0 }, { amount: 0 }],
                        },
                        {
                            amounts: [{ amount: 0 }],
                        },
                    ],
                },
            ] as xtremFinanceData.interfaces.FinanceIntegrationDocument[];

            const result = xtremFinanceData.functions.getFilteredNotificationsPayload(documents);

            assert.equal(result.length, 0);
        });

        it('should not filter out document lines although amounts equal to 0 (everything except workOrderClose)', () => {
            const documents = [
                {
                    documentNumber: 'TEST1',
                    sourceDocumentType: 'materialTracking',
                    documentLines: [
                        {
                            amounts: [{ amount: 0 }, { amount: 0 }],
                        },
                        {
                            amounts: [{ amount: 100 }, { amount: 150 }],
                        },
                        {
                            amounts: [{ amount: 0 }, { amount: 200 }, { amount: 0 }, { amount: 250 }, { amount: 0 }],
                        },
                    ],
                },
                {
                    documentNumber: 'TEST2',
                    sourceDocumentType: 'materialTracking',
                    documentLines: [{ amounts: [{ amount: 0 }, { amount: 0 }] }],
                },
                {
                    documentNumber: 'TEST3',
                    sourceDocumentType: 'materialTracking',
                    documentLines: [{ amounts: [{ amount: 300 }, { amount: 350 }] }],
                },
            ] as xtremFinanceData.interfaces.FinanceIntegrationDocument[];

            const result = xtremFinanceData.functions.getFilteredNotificationsPayload(documents);

            assertDeepPartialMatch(result, [
                {
                    documentNumber: 'TEST1',
                    documentLines: [
                        { amounts: [{ amount: 0 }, { amount: 0 }] },
                        { amounts: [{ amount: 100 }, { amount: 150 }] },
                        { amounts: [{ amount: 0 }, { amount: 200 }, { amount: 0 }, { amount: 250 }, { amount: 0 }] },
                    ],
                },
                {
                    documentNumber: 'TEST2',
                    documentLines: [{ amounts: [{ amount: 0 }, { amount: 0 }] }],
                },
                {
                    documentNumber: 'TEST3',
                    documentLines: [{ amounts: [{ amount: 300 }, { amount: 350 }] }],
                },
            ]);
        });
    });
});
