import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib';
import * as testLib from '../../fixtures/lib';

describe('Accounting staging', () => {
    before(() => {});

    it('Get a posting class for a given journal entry type line', () =>
        Test.withContext(async context => {
            const stagingLineData = await testLib.functions.Common.getStagingLineData(context);
            const createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn = {
                documentsCreated: [],
                validationMessages: [] as xtremFinanceData.interfaces.ValidationMessage[],
            };

            const journalEntryType = await context.read(xtremFinanceData.nodes.JournalEntryType, {
                _id: '#US|salesInvoice|accountsReceivableInvoice',
            });

            const postingClass = await xtremFinanceData.functions.AccountingEngineCommon.getEntryTypeLinePostingClass(
                context,
                (await journalEntryType.lines.toArray())[0],
                stagingLineData,
                createFinanceDocumentsReturn,
            );

            assert.instanceOf(postingClass, xtremFinanceData.nodes.PostingClass);
        }));

    it('Get a posting class for a given journal entry type line - no posting class found', () =>
        Test.withContext(async context => {
            const stagingLineData = await testLib.functions.Common.getStagingLineData(context);

            const createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn = {
                documentsCreated: [],
                validationMessages: [] as xtremFinanceData.interfaces.ValidationMessage[],
            };

            const journalEntryType = await context.read(xtremFinanceData.nodes.JournalEntryType, {
                _id: '#FR|apInvoice|journalEntry',
            });

            const postingClass = await xtremFinanceData.functions.AccountingEngineCommon.getEntryTypeLinePostingClass(
                context,
                (await journalEntryType.lines.toArray())[2],
                stagingLineData,
                createFinanceDocumentsReturn,
            );

            assert.isNull(postingClass);
        }));
});
