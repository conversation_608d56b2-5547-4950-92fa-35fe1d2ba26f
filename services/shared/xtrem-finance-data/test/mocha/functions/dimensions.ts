import type { NodeQueryFilter, Property } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib';

describe('Test functions in dimensions.ts', () => {
    before(() => {});

    it('Function xtremFinanceData.functions.checkValueInactive()', async () => {
        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.checkValueInactive(
                context,
                xtremFinanceData.nodes.Dimension,
                'DIMTYPE1VALUE1',
                {
                    dimensionType: { docProperty: 'dimensionType03', isActive: true },
                },
            );
            assert.deepEqual(value, false);
        });

        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.checkValueInactive(
                context,
                xtremFinanceData.nodes.Dimension,
                'DIMTYPE1VALUE1',
                {
                    dimensionType: { docProperty: 'dimensionType02', isActive: true },
                },
            );
            assert.deepEqual(value, true);
        });

        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.checkValueInactive(
                context,
                xtremFinanceData.nodes.Attribute,
                'AttEMPL',
                {
                    attributeType: { id: 'employee', isActive: false },
                },
            );
            assert.deepEqual(value, true);
        });
    });

    it('Function xtremFinanceData.functions.checkReferenceValueInactive()', () =>
        Test.withContext(async context => {
            const value = await xtremFinanceData.functions.checkReferenceValueInactive(
                context,
                xtremSystem.nodes.Site,
                '#UnitTest',
                {},
            );
            assert.deepEqual(value, true);
        }));

    it('Function xtremFinanceData.functions.checkReferenceItemValueInactive()', () =>
        Test.withContext(async context => {
            const value = await xtremFinanceData.functions.checkReferenceItemValueInactive(
                context,
                xtremMasterData.nodes.Item,
                '1',
                {},
            );
            assert.deepEqual(value, false);
        }));

    it('Function xtremFinanceData.functions.checkDimensionTypeActive()', async () => {
        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.checkDimensionTypeActive(
                context,
                xtremFinanceData.nodes.DimensionType,
                'dimensionType03',
                'good',
            );
            assert.deepEqual(value, 'good');
        });

        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.checkDimensionTypeActive(
                context,
                xtremFinanceData.nodes.DimensionType,
                'dimensionType06',
                'good',
            );
            assert.deepEqual(value, undefined);
        });
    });

    it('Function xtremFinanceData.functions.checkAttributeTypeActive()', async () => {
        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.checkAttributeTypeActive(
                context,
                xtremFinanceData.nodes.AttributeType,
                'financialSite',
                'good',
            );
            assert.deepEqual(value, 'good');
        });

        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.checkAttributeTypeActive(
                context,
                xtremFinanceData.nodes.AttributeType,
                'employee',
                'good',
            );
            assert.deepEqual(value, undefined);
        });
    });

    it('Function xtremFinanceData.functions.getFinancialSite() - US001', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, {
                id: 'US001',
            });
            const value = await xtremFinanceData.functions.getFinancialSite(site);
            assert.deepEqual(await value.id, 'US001');
        }));
    it('Function xtremFinanceData.functions.getFinancialSite() - US010', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, {
                id: 'US010',
            });
            const value = await xtremFinanceData.functions.getFinancialSite(site);
            assert.deepEqual(await value.id, 'US001');
        }));

    it('Function xtremFinanceData.functions.getFinancialSite() - NOFSITE', () =>
        Test.withContext(async context => {
            const site = await context.create(xtremSystem.nodes.Site, {
                id: 'NOFSITE',
                isFinance: false,
                financialSite: null,
                businessEntity: 13,
            });
            const value = await xtremFinanceData.functions.getFinancialSite(site);
            assert.deepEqual(await value.id, 'NOFSITE');
        }));

    it('Function xtremFinanceData.functions.computeGenericAttributes()', () =>
        Test.withContext(async context => {
            const financialSite = await context.read(xtremSystem.nodes.Site, { id: 'US010' });
            const stockSite = await context.read(xtremSystem.nodes.Site, { id: 'US010' });
            const stockSiteNonInventory = await context.read(xtremSystem.nodes.Site, { id: 'US012' });
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Muesli' });

            let value = await xtremFinanceData.functions.computeGenericAttributes(context, {});
            assert.deepEqual(value, {});

            value = await xtremFinanceData.functions.computeGenericAttributes(context, { stockSite });
            assert.deepEqual(value, { stockSite: 'US010', financialSite: 'US001', businessSite: 'US001' });

            value = await xtremFinanceData.functions.computeGenericAttributes(context, { stockSite, item });

            assert.deepEqual(value, {
                stockSite: 'US010',
                financialSite: 'US001',
                item: 'Muesli',
                businessSite: 'US001',
            });

            value = await xtremFinanceData.functions.computeGenericAttributes(context, { financialSite });
            assert.deepEqual(value, { financialSite: 'US001', businessSite: 'US001' });

            value = await xtremFinanceData.functions.computeGenericAttributes(context, {
                stockSite: stockSiteNonInventory,
            });
            assert.deepEqual(value, { financialSite: 'US012', businessSite: 'US012' });

            const specialSite = await context.create(xtremSystem.nodes.Site, {
                id: 'SPECIAL',
                isFinance: false,
                financialSite: null,
                businessEntity: 13,
            });
            value = await xtremFinanceData.functions.computeGenericAttributes(context, { stockSite: specialSite });
            assert.deepEqual(value, { stockSite: 'SPECIAL', financialSite: undefined });
        }));

    it('Function xtremFinanceData.functions.checkAttributeValue()', async () => {
        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.checkAttributeValue(context, 'project', 'AttPROJ2');
            assert.deepEqual(value, true);
        });

        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.checkAttributeValue(context, 'employee', 'AttPROJ2');
            assert.deepEqual(value, false);
        });

        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.checkAttributeValue(context, 'employee', 'AttEMPL');
            assert.deepEqual(value, false);
        });

        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.checkAttributeValue(context, 'customer', 'AttEMPL');
            assert.deepEqual(value, false);
        });

        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.checkAttributeValue(context, 'supplier', 'AttEMPL');
            assert.deepEqual(value, false);
        });
    });

    it('Function xtremFinanceData.functions.getTypeFilter()', () =>
        Test.withContext(async context => {
            const attributeType = await context.read(xtremFinanceData.nodes.AttributeType, {
                id: 'businessSite',
            });
            const dimensionType = await context.read(xtremFinanceData.nodes.DimensionType, {
                docProperty: 'dimensionType03',
            });

            const attributeTypeFactory = context.application.getFactoryByConstructor(
                xtremFinanceData.nodes.AttributeType,
            );
            const attributeTypeProperties: Property[] = attributeTypeFactory.properties.filter(
                prop => prop.name === 'name',
            );

            let value = await xtremFinanceData.functions.getTypeFilter(attributeType, attributeTypeProperties);
            assert.deepEqual(value, {
                _or: [{ name: { businessSite: { _ne: null } } }],
            } as NodeQueryFilter<xtremFinanceData.nodes.AttributeType>);

            const dimensionTypeFactory = context.application.getFactoryByConstructor(
                xtremFinanceData.nodes.DimensionType,
            );

            const dimensionTypeProperties: Property[] = dimensionTypeFactory.properties.filter(
                prop => prop.name === 'name',
            );

            value = await xtremFinanceData.functions.getTypeFilter(dimensionType, dimensionTypeProperties);
            assert.deepEqual(value, {
                _or: [{ name: { dimensionType03: { _ne: null } } }],
            } as NodeQueryFilter<xtremFinanceData.nodes.DimensionType>);
        }));

    it('Function xtremFinanceData.functions.getAttributeValueFilter()', () =>
        Test.withContext(async context => {
            const attribute = await context.read(xtremFinanceData.nodes.Attribute, {
                id: 'AttPROJ',
            });

            const attributeFactory = context.application.getFactoryByConstructor(xtremFinanceData.nodes.Attribute);
            const properties: Property[] = attributeFactory.properties.filter(prop => prop.name === 'name');

            const value = await xtremFinanceData.functions.getAttributeValueFilter(attribute, properties);
            assert.deepEqual(value, {
                _or: [{ name: { project: { _eq: 'AttPROJ' } } }],
            } as NodeQueryFilter<xtremFinanceData.nodes.Attribute>);
        }));

    it('Function xtremFinanceData.functions.getDimensionValueFilter()', () =>
        Test.withContext(async context => {
            const dimension = await context.read(xtremFinanceData.nodes.Dimension, {
                id: 'DIMTYPE1VALUE1',
            });
            const dimensionFactory = context.application.getFactoryByConstructor(xtremFinanceData.nodes.Dimension);
            const properties: Property[] = dimensionFactory.properties.filter(prop => prop.name === 'name');
            const value = await xtremFinanceData.functions.getDimensionValueFilter(dimension, properties);
            assert.deepEqual(value, {
                _or: [{ name: { dimensionType03: { _eq: 'DIMTYPE1VALUE1' } } }],
            } as NodeQueryFilter<xtremFinanceData.nodes.Dimension>);
        }));

    it('Function xtremFinanceData.functions.checkTypeOrValue()', async () => {
        await Test.withContext(async context => {
            const attribute = await context.read(xtremFinanceData.nodes.Attribute, {
                id: 'AttPROJ5',
            });
            const value = await xtremFinanceData.functions.checkTypeOrValue(
                attribute,
                xtremFinanceData.dataTypes.storedAttributesDataType,
            );
            assert.deepEqual(value, false);
        });

        await Test.withContext(async context => {
            const dimensionType = await context.read(xtremFinanceData.nodes.DimensionType, {
                docProperty: 'dimensionType07',
            });
            const value = await xtremFinanceData.functions.checkTypeOrValue(
                dimensionType,
                xtremFinanceData.dataTypes.storedDimensionsDataType,
            );
            assert.deepEqual(value, false);
        });

        // CLARIFY (DNE): How do we provoke a true on this function? Is it possible at all?
    });

    it('Function xtremFinanceData.functions.getDimensionValue()', async () => {
        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.getDimensionValue(context, 'dimensionType20', null);
            assert.deepEqual(value, null);
        });

        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.getDimensionValue(context, 'dimensionType03', {
                dimensionType03: 'WAREHOUSE',
                dimensionType05: 'RETAIL',
            });
            assert.deepEqual(await value?.id, 'WAREHOUSE');
        });

        await Test.withContext(async context => {
            const value = await xtremFinanceData.functions.getDimensionValue(context, 'dimensionType03', {});
            assert.deepEqual(value, null);
        });
    });

    it('Function xtremFinanceData.functions.mandatoryCompanyDimensionAttributes() task mandatory project (restricted on) empty', async () => {
        await Test.withContext(async context => {
            const company = await context.read(xtremSystem.nodes.Company, { id: '700' }, { forUpdate: true });
            await company.$.set({
                attributeTypes: [{ attributeType: 'task', isRequired: true }],
            });
            await company.$.save();
            const companyId = company._id;

            assert.equal(
                JSON.stringify(
                    await xtremFinanceData.functions.mandatoryCompanyDimensionAttributes(context, {
                        companyId,
                        storedAttributes: { task: '', project: 'Test', employee: '' },
                        storedDimensions: {},
                        createFinanceDocumentsReturn: {
                            documentsCreated: [],
                            validationMessages: [],
                        },
                        item: null,
                        documentNumber: 'Test',
                        isTaxAmount: false,
                    }),
                ),
                JSON.stringify(['Task']),
            );
        });
    });

    it('Function xtremFinanceData.functions.mandatoryCompanyDimensionAttributes() project mandatory storedAttributes empty', async () => {
        await Test.withContext(async context => {
            const company = await context.read(xtremSystem.nodes.Company, { id: '700' }, { forUpdate: true });
            await company.$.set({
                attributeTypes: [{ attributeType: 'project', isRequired: true }],
            });
            await company.$.save();
            assert.equal(company._id, 19);

            assert.equal(
                JSON.stringify(
                    await xtremFinanceData.functions.mandatoryCompanyDimensionAttributes(context, {
                        companyId: 19,
                        storedAttributes: { task: 'Test', project: '', employee: '' },
                        storedDimensions: {},
                        createFinanceDocumentsReturn: {
                            documentsCreated: [],
                            validationMessages: [],
                        },
                        item: null,
                        documentNumber: 'Test',
                        isTaxAmount: false,
                    }),
                ),
                JSON.stringify(['Project']),
            );
        });
    });

    it('Function xtremFinanceData.functions.mandatoryCompanyDimensionAttributes() Task mandatory storedAttributes empty', async () => {
        await Test.withContext(async context => {
            const company = await context.read(xtremSystem.nodes.Company, { id: '700' }, { forUpdate: true });
            await company.$.set({
                attributeTypes: [{ attributeType: 'task', isRequired: true }],
            });
            await company.$.save();
            assert.equal(company._id, 19);

            assert.equal(
                JSON.stringify(
                    await xtremFinanceData.functions.mandatoryCompanyDimensionAttributes(context, {
                        companyId: 19,
                        storedAttributes: { task: '', project: '', employee: '' },
                        storedDimensions: {},
                        createFinanceDocumentsReturn: {
                            documentsCreated: [],
                            validationMessages: [],
                        },
                        item: null,
                        documentNumber: 'Test',
                        isTaxAmount: false,
                    }),
                ),
                JSON.stringify([]),
            );
        });
    });
});
