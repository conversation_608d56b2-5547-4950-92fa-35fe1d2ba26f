import type { JsonDataType, ValidationContext } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremFinanceData from '../../../lib';
import * as testLib from '../../fixtures/lib';

describe('FinanceData data types', () => {
    before(() => {});

    it('Cover StoredAttributesDataType', () =>
        Test.withContext(async context => {
            const myAttribute = await context.read(xtremFinanceData.nodes.Attribute, {
                id: 'AttPROJ',
            });
            const myStoredAttribute = new xtremFinanceData.dataTypes.StoredAttributesDataType();
            await myStoredAttribute.controlValue(
                myAttribute,
                {} as ValidationContext,
                {} as xtremMasterData.interfaces.StoredAttributes,
            );
            await myStoredAttribute.controlValue(
                {} as JsonDataType,
                {} as ValidationContext,
                {} as xtremMasterData.interfaces.StoredAttributes,
            );

            // Test all the failing controls
            const myInterface: xtremMasterData.interfaces.StoredAttributes = {
                abc: 'abc',
                financialSite: 'US0011',
                stockSite: 'US0011',
                project: 'AttPROJ1',
                task: '',
                employee: 'AttEMPL',
                businessSite: 'BS1',
                manufacturingSite: 'MS1',
                customer: 'CUST011',
                supplier: 'SUPP011',
                item: 'IT011',
            };
            const myNode: testLib.nodes.FakeNode = await context.create(testLib.nodes.FakeNode, {
                storedAttributes: myInterface,
            });
            assert.isFalse(await myNode.$.control());
        }));

    it('Cover StoredDimensionsDataType', () =>
        Test.withContext(async context => {
            const myDimension = await context.read(xtremFinanceData.nodes.Dimension, {
                id: 'COMMERCIAL',
            });
            const myStoredDimension = new xtremFinanceData.dataTypes.StoredDimensionsDataType();
            await myStoredDimension.controlValue(myDimension, {} as ValidationContext, {} as object);
            await myStoredDimension.controlValue({} as JsonDataType, {} as ValidationContext, {} as object);

            // Test all the failing controls
            const myDimensionObject = { dimensionType03: 'DIMTYPE1VALUE1', dimensionType06: 'DIMTYPE6VALUE' };
            const myNode: testLib.nodes.FakeNode = await context.create(testLib.nodes.FakeNode, {
                storedDimensions: myDimensionObject,
            });
            assert.isFalse(await myNode.$.control());
        }));
});
