{"name": "@sage/xtrem-finance-data", "description": "XTREM Finance data", "version": "58.0.2", "xtrem": {"scope": "finance"}, "keywords": ["xtrem-service"], "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "files": ["build", "data", "routing.json"], "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-authorization": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-communication": "workspace:*", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-landed-cost": "workspace:*", "@sage/xtrem-master-data": "workspace:*", "@sage/xtrem-metadata": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-stock-data": "workspace:*", "@sage/xtrem-structure": "workspace:*", "@sage/xtrem-synchronization": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-tax": "workspace:*", "@sage/xtrem-ui": "workspace:*", "axios": "^1.11.0", "lodash": "^4.17.21", "mime-types": "^3.0.0", "uuid": "^11.0.0"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@sage/xtrem-finance-data-api": "workspace:*", "@sage/xtrem-master-data-api": "workspace:*", "@sage/xtrem-routing": "workspace:*", "@sage/xtrem-structure-api": "workspace:*", "@sage/xtrem-system-api": "workspace:*", "@sage/xtrem-tax-api": "workspace:*", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/lodash": "^4.14.198", "@types/mime-types": "^3.0.0", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/uuid": "^10.0.0", "c8": "^10.1.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "mocha": "^10.8.2", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.3"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "xtrem compile --binary --prod", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "extract:demo:data": "xtrem layers --extract demo", "extract:qa:data": "xtrem layers --extract qa", "lint": "xtrem lint", "load:demo:data": "xtrem layers --load setup,demo", "load:qa:data": "xtrem layers --load setup,qa", "load:setup:data": "xtrem layers --load setup", "load:test:data": "xtrem layers --load setup,test", "qa:cucumber": "xtrem test test/cucumber/* --integration --noTimeout --layers=qa", "qa:cucumber:browser": "xtrem test test/cucumber/* --integration --browser --noTimeout --layers=qa", "sonarqube:scanner": "pnpm test:ci && pnpm dlx sonarqube-scanner && rm -rf coverage .nyc_output .scannerwork junit-report-*.xml", "start": "xtrem start", "test": "xtrem test --noTimeout --unit --graphql --layers=test", "test:ci": "xtrem test --noTimeout --unit --ci --layers=test", "test:graphql": "xtrem test --noTimeout --graphql --layers=test", "test:smoke": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test", "test:smoke:ci": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test --ci", "test:smoke:static": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test", "test:smoke:static:ci": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test --ci", "test:unit": "xtrem test --noTimeout --unit --layers=test", "xtrem": "xtrem", "view-report": "node ../../../scripts/allure/view-report.js"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}