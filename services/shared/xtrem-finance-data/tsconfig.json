{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "application.ts", "lib/**/*", "test/**/*.ts", "test/**/*.json", "api/api.d.ts"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/page-extensions/**/*", "lib/page-fragments/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png", "lib/client-functions/**/*"], "references": [{"path": "../../../platform/shared/xtrem-async-helper"}, {"path": "../../../platform/system/xtrem-authorization"}, {"path": "../../../platform/front-end/xtrem-client"}, {"path": "../../../platform/system/xtrem-communication"}, {"path": "../../../platform/back-end/xtrem-config"}, {"path": "../../../platform/back-end/xtrem-core"}, {"path": "../../../platform/shared/xtrem-decimal"}, {"path": "../xtrem-landed-cost"}, {"path": "../xtrem-master-data"}, {"path": "../../../platform/system/xtrem-metadata"}, {"path": "../../../platform/shared/xtrem-shared"}, {"path": "../xtrem-stock-data"}, {"path": "../xtrem-structure"}, {"path": "../../../platform/system/xtrem-synchronization"}, {"path": "../../../platform/system/xtrem-system"}, {"path": "../xtrem-tax"}, {"path": "../../../platform/front-end/xtrem-ui"}, {"path": "../../../platform/back-end/eslint-plugin-xtrem"}, {"path": "../../../platform/cli/xtrem-cli"}, {"path": "api"}, {"path": "../xtrem-master-data/api"}, {"path": "../../../platform/system/xtrem-routing"}, {"path": "../xtrem-structure/api"}, {"path": "../../../platform/system/xtrem-system/api"}, {"path": "../xtrem-tax/api"}]}