{"@sage/xtrem-finance-data": [{"topic": "Account/asyncExport/start", "queue": "import-export", "sourceFileName": "account.ts"}, {"topic": "AccountAttributeType/asyncExport/start", "queue": "import-export", "sourceFileName": "account-attribute-type.ts"}, {"topic": "AccountDimensionType/asyncExport/start", "queue": "import-export", "sourceFileName": "account-dimension-type.ts"}, {"topic": "AccountingStaging/asyncExport/start", "queue": "import-export", "sourceFileName": "accounting-staging.ts"}, {"topic": "AccountingStagingAmount/asyncExport/start", "queue": "import-export", "sourceFileName": "accounting-staging-amount.ts"}, {"topic": "AccountingStagingDocumentTax/asyncExport/start", "queue": "import-export", "sourceFileName": "accounting-staging-document-tax.ts"}, {"topic": "AccountingStagingLineTax/asyncExport/start", "queue": "import-export", "sourceFileName": "accounting-staging-line-tax.ts"}, {"topic": "AccountsPayableInvoiceLineStaging/asyncExport/start", "queue": "import-export", "sourceFileName": "accounts-payable-invoice-line-staging.ts"}, {"topic": "AccountsReceivableInvoiceLineStaging/asyncExport/start", "queue": "import-export", "sourceFileName": "accounts-receivable-invoice-line-staging.ts"}, {"topic": "AnalyticalData/asyncExport/start", "queue": "import-export", "sourceFileName": "analytical-data.ts"}, {"topic": "Attribute/asyncExport/start", "queue": "import-export", "sourceFileName": "attribute.ts"}, {"topic": "AttributeType/asyncExport/start", "queue": "import-export", "sourceFileName": "attribute-type.ts"}, {"topic": "BankAccount/asyncExport/start", "queue": "import-export", "sourceFileName": "bank-account.ts"}, {"topic": "CloseReason/asyncExport/start", "queue": "import-export", "sourceFileName": "close-reason.ts"}, {"topic": "CompanyAttributeType/asyncExport/start", "queue": "import-export", "sourceFileName": "company-attribute-type.ts"}, {"topic": "CompanyDefaultAttribute/asyncExport/start", "queue": "import-export", "sourceFileName": "company-default-attribute.ts"}, {"topic": "CompanyDefaultDimension/asyncExport/start", "queue": "import-export", "sourceFileName": "company-default-dimension.ts"}, {"topic": "CompanyDimensionType/asyncExport/start", "queue": "import-export", "sourceFileName": "company-dimension-type.ts"}, {"topic": "DatevConfiguration/asyncExport/start", "queue": "import-export", "sourceFileName": "datev-configuration.ts"}, {"topic": "Dimension/asyncExport/start", "queue": "import-export", "sourceFileName": "dimension.ts"}, {"topic": "DimensionDefinitionLevelAndDefault/asyncExport/start", "queue": "import-export", "sourceFileName": "dimension-definition-level-and-default.ts"}, {"topic": "DimensionType/asyncExport/start", "queue": "import-export", "sourceFileName": "dimension-type.ts"}, {"topic": "FinanceTransaction/asyncExport/start", "queue": "import-export", "sourceFileName": "finance-transaction.ts"}, {"topic": "FinanceTransactionLine/asyncExport/start", "queue": "import-export", "sourceFileName": "finance-transaction-line.ts"}, {"topic": "Journal/asyncExport/start", "queue": "import-export", "sourceFileName": "journal.ts"}, {"topic": "JournalEntryType/asyncExport/start", "queue": "import-export", "sourceFileName": "journal-entry-type.ts"}, {"topic": "JournalEntryTypeLine/asyncExport/start", "queue": "import-export", "sourceFileName": "journal-entry-type-line.ts"}, {"topic": "PaymentDocumentLine/asyncExport/start", "queue": "import-export", "sourceFileName": "payment-document-line.ts"}, {"topic": "PaymentTracking/asyncExport/start", "queue": "import-export", "sourceFileName": "payment-tracking.ts"}, {"topic": "PostingClass/asyncExport/start", "queue": "import-export", "sourceFileName": "posting-class.ts"}, {"topic": "PostingClassDefinition/asyncExport/start", "queue": "import-export", "sourceFileName": "posting-class-definition.ts"}, {"topic": "PostingClassLine/asyncExport/start", "queue": "import-export", "sourceFileName": "posting-class-line.ts"}, {"topic": "PostingClassLineDetail/asyncExport/start", "queue": "import-export", "sourceFileName": "posting-class-line-detail.ts"}]}