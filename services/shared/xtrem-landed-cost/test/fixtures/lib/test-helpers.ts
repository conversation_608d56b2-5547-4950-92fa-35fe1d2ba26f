import type { Context, NodeCreateData, NodeUpdateData, UpdateAction } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremLandedCost from '../../../lib';
import type { FakeAllocatingLandedCostDocumentLine } from './nodes';
import {
    FakeAllocatedLandedCostOrderDocument,
    FakeAllocatedLandedCostTransactionDocument,
    FakeAllocatingLandedCostDocument,
} from './nodes';

/**
 * Create a landed cost allocating document with allocations linked to the allocatedDocument
 *                - line0:
 *                   -> LandedCost002 amount=100
 *                   -> LandedCost003 amount=150
 *               - line1:
 *                   -> LandedCost001 amount=200
 *                   -> LandedCost002 amount=250
 *                   -> LandedCost003 amount=300
 * @param context
 * @param allocatedDocument
 * @returns
 */
export async function createFakeAllocatingDocument(
    context: Context,
    number: string,
    allocatedDocument: xtremLandedCost.interfaces.LandedCostAllocatedDocumentHeader<
        | xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction
        | xtremLandedCost.enums.LandedCostDocumentTypeEnum.order
    >,
) {
    const line0 = await allocatedDocument.landedCostAssignableLines.elementAt(0);
    const line1 = await allocatedDocument.landedCostAssignableLines.elementAt(1);

    const allocatingDocument = await context.create(FakeAllocatingLandedCostDocument, {
        number,
        lines: [
            {
                item: '#LandedCost001', // type = customs
                lineAmount: 500,
                landedCost: {
                    allocationMethod: 'manual',
                    allocations: [
                        {
                            costAmount: 200,
                            costAmountInCompanyCurrency: 200,
                            allocatedDocumentLine: line1,
                        },
                    ],
                },
            },
            {
                item: '#LandedCost002', // type = freight
                lineAmount: 500,
                landedCost: {
                    allocationMethod: 'manual',
                    allocations: [
                        {
                            costAmount: 100,
                            costAmountInCompanyCurrency: 100,
                            allocatedDocumentLine: line0,
                        },
                        {
                            costAmount: 250,
                            costAmountInCompanyCurrency: 250,
                            allocatedDocumentLine: line1,
                        },
                    ],
                },
            },
            {
                item: '#LandedCost003', // type = freight
                lineAmount: 500,
                landedCost: {
                    allocationMethod: 'manual',
                    allocations: [
                        {
                            costAmount: 150,
                            costAmountInCompanyCurrency: 150,
                            allocatedDocumentLine: line0,
                        },
                        {
                            costAmount: 300,
                            costAmountInCompanyCurrency: 300,
                            allocatedDocumentLine: line1,
                        },
                    ],
                },
            },
        ],
    });

    await allocatingDocument.$.save();
    return allocatingDocument;
}

export async function prepareFakeCreditDocumentLine(
    sourceAllocatingDocumentLine: FakeAllocatingLandedCostDocumentLine,
): Promise<{
    creditDocumentLine: NodeCreateData<FakeAllocatingLandedCostDocumentLine>;
    creditedAllocations: NodeUpdateData<xtremLandedCost.nodes.LandedCostAllocation>[];
}> {
    const landedCost = await sourceAllocatingDocumentLine.landedCost;
    if (!landedCost) {
        throw new Error('The function createFakeCreditDocumentLine expects to have landed costs');
    }

    const creditAllocations: NodeCreateData<xtremLandedCost.nodes.LandedCostAllocation>[] = [];
    const creditedAllocations: NodeUpdateData<xtremLandedCost.nodes.LandedCostAllocation>[] = [];

    await landedCost.allocations.forEach(async allocation => {
        const costAmount = await allocation.costAmount;
        const costAmountInCompanyCurrency = await allocation.costAmountInCompanyCurrency;
        creditAllocations.push({
            costAmount,
            costAmountInCompanyCurrency,
            allocatedDocumentLine: await allocation.allocatedDocumentLine,
            creditedCostAmount: 0,
            creditedCostAmountInCompanyCurrency: 0,
            sourceAllocationLine: allocation,
        });
        creditedAllocations.push({
            _action: 'update',
            _id: allocation._id,
            creditedCostAmount: costAmount,
            creditedCostAmountInCompanyCurrency: costAmountInCompanyCurrency,
        });
    });

    const creditDocumentLine: NodeCreateData<FakeAllocatingLandedCostDocumentLine> = {
        item: await sourceAllocatingDocumentLine.item,
        lineAmount: await sourceAllocatingDocumentLine.lineAmount,
        landedCost: {
            allocationMethod: 'manual',
            allocations: creditAllocations,
        },
    };

    return { creditDocumentLine, creditedAllocations };
}

export async function createFakeCreditDocument(
    context: Context,
    number: string,
    allocatingDocument: FakeAllocatingLandedCostDocument,
) {
    const writableAllocatingDocument = await xtremMasterData.functions.getWritableNode(
        context,
        FakeAllocatingLandedCostDocument,
        allocatingDocument._id,
    );
    const creditDocumentLines: NodeCreateData<FakeAllocatingLandedCostDocumentLine>[] = [];
    await writableAllocatingDocument.lines.forEach(async line => {
        const { creditDocumentLine, creditedAllocations } = await prepareFakeCreditDocumentLine(line);
        creditDocumentLines.push(creditDocumentLine);
        await line.$.set({ landedCost: { allocations: creditedAllocations } });
    });
    await writableAllocatingDocument.$.save();
    assert.deepEqual(writableAllocatingDocument.$.context.diagnoses, []);

    const creditDocument = await context.create(FakeAllocatingLandedCostDocument, {
        number: number || 'CREDIT_DOC',
        lines: creditDocumentLines,
    });
    await creditDocument.$.save();
    assert.deepEqual(creditDocument.$.context.diagnoses, []);

    return creditDocument;
}

/**
 * returns LandedCostLine to create based on allocations.
 * Consider that actualAllocatedCostAmountInCompanyCurrency = 1/10 * actualCostAmountInCompanyCurrency
 * @param allocatedDocumentLine
 * @param allocatingDocument
 * @returns
 */
function getLandedCostLines(
    allocatedDocumentLine: xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<
        | xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction
        | xtremLandedCost.enums.LandedCostDocumentTypeEnum.order
    >,
    allocatingDocument: FakeAllocatingLandedCostDocument,
) {
    return allocatingDocument.lines.reduce(
        async (allocations, line) => {
            const landedCost = await line.landedCost;
            if (!landedCost) return allocations;

            const x = landedCost.allocations.filter(
                async allocation => (await allocation.allocatedDocumentLine)._id === allocatedDocumentLine._id,
            );
            const y: NodeCreateData<
                xtremLandedCost.nodes.LandedCostLine & {
                    _action: UpdateAction;
                }
            >[] = await x
                .map(async allocation => {
                    const actualAllocatedCostAmountInCompanyCurrency =
                        xtremLandedCost.functions.typingLib.isInstanceOfLandedCostAllocatedOrderDocumentLine(
                            await allocation.allocatedDocumentLine,
                        )
                            ? (await allocation.costAmountInCompanyCurrency) / 10
                            : 0;
                    return {
                        _action: 'create' as UpdateAction,
                        landedCost: await line.item,
                        landedCostAllocation: allocation,
                        actualCostAmountInCompanyCurrency: await allocation.costAmountInCompanyCurrency,
                        actualAllocatedCostAmountInCompanyCurrency,
                    };
                })
                .toArray();
            return allocations.concat(y);
        },
        [] as NodeCreateData<
            xtremLandedCost.nodes.LandedCostLine & {
                _action: UpdateAction;
            }
        >[],
    );
}

/**
 * Create a FakeAllocatedDocument and returns it
 * If landed costs are requested, the allocating landed cost document is also returned
 */
export async function createFakeAllocatedDocument<
    T extends
        | xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction
        | xtremLandedCost.enums.LandedCostDocumentTypeEnum.order,
>(
    context: Context,
    documentType: T,
    data: NodeCreateData<FakeAllocatedLandedCostTransactionDocument | FakeAllocatedLandedCostOrderDocument> = {
        number: 'TEST',
        lines: [
            {
                item: '#Chair',
                quantityInStockUnitForLandedCostAllocation: 10,
                amountForLandedCostAllocation: 123,
            },
            {
                item: '#ChairLeg',
                quantityInStockUnitForLandedCostAllocation: 40,
                amountForLandedCostAllocation: 456,
            },
            {
                item: '#ChairSeat',
                quantityInStockUnitForLandedCostAllocation: 12,
                amountForLandedCostAllocation: 789,
            },
        ],
    },
): Promise<
    T extends xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction
        ? xtremLandedCost.interfaces.LandedCostAllocatedDocumentHeader<xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction>
        : xtremLandedCost.interfaces.LandedCostAllocatedDocumentHeader<xtremLandedCost.enums.LandedCostDocumentTypeEnum.order>
> {
    const documentClass =
        documentType === xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction
            ? FakeAllocatedLandedCostTransactionDocument
            : FakeAllocatedLandedCostOrderDocument;

    const allocatedDocument = await context.create(documentClass, data);
    await allocatedDocument.$.save();
    assert.deepEqual(allocatedDocument.$.context.diagnoses, []);
    // The type assertion (as any) is used here because TypeScript is not be able to infer the exact type due to the conditional nature.
    return allocatedDocument as any;
}

export async function createFakeAllocatedDocumentWithLandedCosts<
    T extends
        | xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction
        | xtremLandedCost.enums.LandedCostDocumentTypeEnum.order,
>(
    context: Context,
    documentType: T,
    data?: NodeCreateData<FakeAllocatedLandedCostTransactionDocument | FakeAllocatedLandedCostOrderDocument>,
): Promise<{
    allocatedDocument: xtremLandedCost.interfaces.LandedCostAllocatedDocumentHeader<T>;
    allocatingDocument: FakeAllocatingLandedCostDocument;
}> {
    // 1- Create the allocated document lines without landed costs
    const allocatedDocument = await createFakeAllocatedDocument(context, documentType, data);

    // 2- Create the document allocating landed cost to the previous document
    const allocatingDocument = await createFakeAllocatingDocument(context, 'TEST', allocatedDocument);

    // 3- create LandedCostLine records for the document line from the allocations
    const line0 = await allocatedDocument.landedCostAssignableLines.elementAt(0);
    const line1 = await allocatedDocument.landedCostAssignableLines.elementAt(1);

    await line0.$.set({
        landedCostLines: await getLandedCostLines(line0, allocatingDocument),
    });
    await line0.$.save();
    await line1.$.set({
        landedCostLines: await getLandedCostLines(line1, allocatingDocument),
    });
    await line1.$.save();

    assert.deepEqual(await line0.landedCostLines.length, 2);
    assert.deepEqual(await line1.landedCostLines.length, 3);

    // The type assertion (as any) is used here because TypeScript is not be able to infer the exact type due to the conditional nature.
    return { allocatedDocument: allocatedDocument as any, allocatingDocument };
}
