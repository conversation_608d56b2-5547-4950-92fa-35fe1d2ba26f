import type { Collection, Reference, decimal } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as fakePackage from '..';
import * as xtremLandedCost from '../../../../lib';

@decorators.subNode<FakeAllocatedLandedCostOrderDocumentLine>({
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
})
export class FakeAllocatedLandedCostOrderDocumentLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements
        xtremLandedCost.interfaces
            .LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.order>
{
    @decorators.referenceProperty<FakeAllocatedLandedCostOrderDocumentLine, 'document'>({
        isStored: true,
        isVitalParent: true,
        node: () => fakePackage.nodes.FakeAllocatedLandedCostOrderDocument,
    })
    override readonly document: Reference<fakePackage.nodes.FakeAllocatedLandedCostOrderDocument>;

    @decorators.referenceProperty<FakeAllocatedLandedCostOrderDocumentLine, 'item'>({
        isStored: true,
        node: () => xtremMasterData.nodes.Item,
    })
    readonly item: Promise<xtremMasterData.nodes.Item>;

    @decorators.decimalProperty<FakeAllocatedLandedCostOrderDocumentLine, 'amountForLandedCostAllocation'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly amountForLandedCostAllocation: Promise<decimal>;

    @decorators.decimalProperty<FakeAllocatedLandedCostOrderDocumentLine, 'quantityInStockUnitForLandedCostAllocation'>(
        {
            isStored: true,
            dataType: () => xtremMasterData.dataTypes.baseDecimal,
        },
    )
    readonly quantityInStockUnitForLandedCostAllocation: Promise<decimal>;

    @decorators.referenceProperty<FakeAllocatedLandedCostOrderDocumentLine, 'companyCurrency'>({
        node: () => xtremMasterData.nodes.Currency,
        computeValue() {
            return this.$.context.read(xtremMasterData.nodes.Currency, { _id: '#EUR' });
        },
    })
    readonly companyCurrency: Promise<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<FakeAllocatedLandedCostOrderDocumentLine, 'actualLandedCostInCompanyCurrency'>({
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        getValue() {
            return xtremLandedCost.nodes.LandedCostLine.getTotalActualCostAmountInCompanyCurrency(this);
        },
    })
    readonly actualLandedCostInCompanyCurrency: Promise<decimal>;

    @decorators.collectionProperty<FakeAllocatedLandedCostOrderDocumentLine, 'landedCostLines'>({
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremLandedCost.nodes.LandedCostLine,
    })
    readonly landedCostLines: Collection<xtremLandedCost.nodes.LandedCostLine>;

    @decorators.decimalProperty<FakeAllocatedLandedCostOrderDocumentLine, 'remainingQuantityToProcessForLandedCost'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        async control(cx, val) {
            await cx.error.if(val).is.greater.than(await this.quantityInStockUnitForLandedCostAllocation);
        },
    })
    readonly remainingQuantityToProcessForLandedCost: Promise<decimal>;

    @decorators.jsonProperty<FakeAllocatedLandedCostOrderDocumentLine, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.jsonProperty<FakeAllocatedLandedCostOrderDocumentLine, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<FakeAllocatedLandedCostOrderDocumentLine, 'computedAttributes'>({
        isPublished: true,
        async getValue() {
            return {
                item: await this.item,
            };
        },
    })
    readonly computedAttributes: Promise<object>;
}
