import type { Collection, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as fakePackage from '..';

@decorators.node<FakeAllocatingLandedCostDocument>({
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    storage: 'sql',
})
export class FakeAllocatingLandedCostDocument extends Node {
    @decorators.stringProperty<FakeAllocatingLandedCostDocument, 'number'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        defaultValue() {
            return xtremMasterData.functions.getDocumentNumberPlaceholder();
        },
        deferredDefaultValue() {
            return xtremMasterData.functions.allocateDocumentNumber(this);
        },
    })
    readonly number: Promise<string>;

    @decorators.collectionProperty<FakeAllocatingLandedCostDocument, 'lines'>({
        isVital: true,
        reverseReference: 'document',
        node: () => fakePackage.nodes.FakeAllocatingLandedCostDocumentLine,
    })
    readonly lines: Collection<fakePackage.nodes.FakeAllocatingLandedCostDocumentLine>;

    @decorators.referenceProperty<FakeAllocatingLandedCostDocument, 'currency'>({
        isStored: true,
        node: () => xtremMasterData.nodes.Currency,

        defaultValue() {
            return this.$.context.read(xtremMasterData.nodes.Currency, { id: 'USD' });
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<FakeAllocatingLandedCostDocument, 'companyCurrency'>({
        isStored: true,
        node: () => xtremMasterData.nodes.Currency,

        defaultValue() {
            return this.$.context.read(xtremMasterData.nodes.Currency, { id: 'EUR' });
        },
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;
}
