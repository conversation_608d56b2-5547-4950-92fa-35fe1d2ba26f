import type { decimal, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as fakePackage from '..';
import * as xtremLandedCost from '../../../../lib';

@decorators.subNode<FakeAllocatingLandedCostDocumentLine>({
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
})
export class FakeAllocatingLandedCostDocumentLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements xtremLandedCost.interfaces.DocumentLineAllocatingLandedCost
{
    @decorators.referenceProperty<FakeAllocatingLandedCostDocumentLine, 'document'>({
        isStored: true,
        isVitalParent: true,
        node: () => fakePackage.nodes.FakeAllocatingLandedCostDocument,
    })
    override readonly document: Reference<fakePackage.nodes.FakeAllocatingLandedCostDocument>;

    @decorators.referenceProperty<FakeAllocatingLandedCostDocumentLine, 'item'>({
        isStored: true,
        node: () => xtremMasterData.nodes.Item,
    })
    readonly item: Promise<xtremMasterData.nodes.Item>;

    @decorators.decimalProperty<FakeAllocatingLandedCostDocumentLine, 'lineAmount'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly lineAmount: Promise<decimal>;

    @decorators.referenceProperty<FakeAllocatingLandedCostDocumentLine, 'landedCost'>({
        isPublished: true,
        isNullable: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremLandedCost.nodes.LandedCostDocumentLine,
        dependsOn: ['lineAmount'],

        async defaultValue() {
            if ((await (await this.item).type) !== 'landedCost') {
                return null;
            }
            return { documentLine: this };
        },
    })
    readonly landedCost: Reference<xtremLandedCost.nodes.LandedCostDocumentLine | null>;

    @decorators.referenceProperty<FakeAllocatingLandedCostDocumentLine, 'currency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await this.document).currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<FakeAllocatingLandedCostDocumentLine, 'companyCurrency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await this.document).companyCurrency;
        },
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    getLandedCostAmountToAllocate(): Promise<decimal> {
        return this.lineAmount;
    }

    // eslint-disable-next-line class-methods-use-this
    convertAmountInCompanyCurrency(amount: decimal): Promise<decimal> {
        return Promise.resolve(amount * 1.2);
    }
}
