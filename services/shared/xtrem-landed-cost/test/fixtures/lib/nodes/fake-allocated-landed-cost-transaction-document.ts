import type { Collection } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as fakePackage from '..';
import type * as xtremLandedCost from '../../../../lib';
import { LandedCostBaseDocument } from '../../../../lib/nodes';

@decorators.subNode<FakeAllocatedLandedCostTransactionDocument>({
    extends: () => LandedCostBaseDocument,
})
export class FakeAllocatedLandedCostTransactionDocument
    extends LandedCostBaseDocument
    implements
        xtremLandedCost.interfaces
            .LandedCostAllocatedDocumentHeader<xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction>
{
    @decorators.collectionProperty<FakeAllocatedLandedCostTransactionDocument, 'lines'>({
        isVital: true,
        reverseReference: 'document',
        node: () => fakePackage.nodes.FakeAllocatedLandedCostTransactionDocumentLine,
    })
    readonly lines: Collection<fakePackage.nodes.FakeAllocatedLandedCostTransactionDocumentLine>;

    get landedCostAssignableLines() {
        return this.lines;
    }
}
