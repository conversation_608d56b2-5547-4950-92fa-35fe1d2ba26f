import type { decimal, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as fakePackage from '..';

@decorators.subNode<FakeNotAllocableLandedCostDocumentLine>({
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
})
export class FakeNotAllocableLandedCostDocumentLine extends xtremMasterData.nodes.BaseDocumentLine {
    @decorators.referenceProperty<FakeNotAllocableLandedCostDocumentLine, 'document'>({
        isStored: true,
        isVitalParent: true,
        node: () => fakePackage.nodes.FakeNotAllocableLandedCostDocument,
    })
    override readonly document: Reference<fakePackage.nodes.FakeNotAllocableLandedCostDocument>;

    @decorators.referenceProperty<FakeNotAllocableLandedCostDocumentLine, 'item'>({
        isStored: true,
        node: () => xtremMasterData.nodes.Item,
    })
    readonly item: Promise<xtremMasterData.nodes.Item>;

    @decorators.decimalProperty<FakeNotAllocableLandedCostDocumentLine, 'quantityInStockUnit'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly quantityInStockUnit: Promise<decimal>;
}
