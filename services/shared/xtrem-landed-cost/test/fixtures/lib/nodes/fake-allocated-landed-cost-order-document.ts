import type { Collection } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as fakePackage from '..';
import type * as xtremLandedCost from '../../../../lib';
import { LandedCostBaseDocument } from '../../../../lib/nodes';

@decorators.subNode<FakeAllocatedLandedCostOrderDocument>({
    extends: () => LandedCostBaseDocument,
})
export class FakeAllocatedLandedCostOrderDocument
    extends LandedCostBaseDocument
    implements
        xtremLandedCost.interfaces
            .LandedCostAllocatedDocumentHeader<xtremLandedCost.enums.LandedCostDocumentTypeEnum.order>
{
    @decorators.collectionProperty<FakeAllocatedLandedCostOrderDocument, 'lines'>({
        isVital: true,
        reverseReference: 'document',
        node: () => fakePackage.nodes.FakeAllocatedLandedCostOrderDocumentLine,
    })
    readonly lines: Collection<fakePackage.nodes.FakeAllocatedLandedCostOrderDocumentLine>;

    get landedCostAssignableLines() {
        return this.lines;
    }
}
