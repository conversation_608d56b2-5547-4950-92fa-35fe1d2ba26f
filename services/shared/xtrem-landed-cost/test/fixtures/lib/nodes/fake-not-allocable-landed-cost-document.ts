import type { Collection } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as fakePackage from '..';

@decorators.node<FakeNotAllocableLandedCostDocument>({
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    storage: 'sql',
})
export class FakeNotAllocableLandedCostDocument extends Node {
    @decorators.stringProperty<FakeNotAllocableLandedCostDocument, 'number'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly number: Promise<string>;

    @decorators.collectionProperty<FakeNotAllocableLandedCostDocument, 'lines'>({
        isVital: true,
        reverseReference: 'document',
        node: () => fakePackage.nodes.FakeNotAllocableLandedCostDocumentLine,
    })
    readonly lines: Collection<fakePackage.nodes.FakeNotAllocableLandedCostDocumentLine>;
}
