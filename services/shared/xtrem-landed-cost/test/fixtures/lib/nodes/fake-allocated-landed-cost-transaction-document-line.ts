import type { Collection, Reference, decimal } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as fakePackage from '..';
import * as xtremLandedCost from '../../../../lib';

@decorators.subNode<FakeAllocatedLandedCostTransactionDocumentLine>({
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
})
export class FakeAllocatedLandedCostTransactionDocumentLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements
        xtremLandedCost.interfaces
            .LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction>
{
    @decorators.referenceProperty<FakeAllocatedLandedCostTransactionDocumentLine, 'document'>({
        isStored: true,
        isVitalParent: true,
        node: () => fakePackage.nodes.FakeAllocatedLandedCostTransactionDocument,
    })
    override readonly document: Reference<fakePackage.nodes.FakeAllocatedLandedCostTransactionDocument>;

    @decorators.referenceProperty<FakeAllocatedLandedCostTransactionDocumentLine, 'item'>({
        isStored: true,
        node: () => xtremMasterData.nodes.Item,
    })
    readonly item: Promise<xtremMasterData.nodes.Item>;

    @decorators.decimalProperty<FakeAllocatedLandedCostTransactionDocumentLine, 'amountForLandedCostAllocation'>({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly amountForLandedCostAllocation: Promise<decimal>;

    @decorators.decimalProperty<
        FakeAllocatedLandedCostTransactionDocumentLine,
        'quantityInStockUnitForLandedCostAllocation'
    >({
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly quantityInStockUnitForLandedCostAllocation: Promise<decimal>;

    @decorators.referenceProperty<FakeAllocatedLandedCostTransactionDocumentLine, 'companyCurrency'>({
        node: () => xtremMasterData.nodes.Currency,
        computeValue() {
            return this.$.context.read(xtremMasterData.nodes.Currency, { _id: '#EUR' });
        },
    })
    readonly companyCurrency: Promise<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<FakeAllocatedLandedCostTransactionDocumentLine, 'actualLandedCostInCompanyCurrency'>({
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        getValue() {
            return xtremLandedCost.nodes.LandedCostLine.getTotalActualCostAmountInCompanyCurrency(this);
        },
    })
    readonly actualLandedCostInCompanyCurrency: Promise<decimal>;

    @decorators.collectionProperty<FakeAllocatedLandedCostTransactionDocumentLine, 'landedCostLines'>({
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremLandedCost.nodes.LandedCostLine,
    })
    readonly landedCostLines: Collection<xtremLandedCost.nodes.LandedCostLine>;

    @decorators.referenceProperty<FakeAllocatedLandedCostTransactionDocumentLine, 'orderLine'>({
        isStored: true,
        isNullable: true,
        node: () => fakePackage.nodes.FakeAllocatedLandedCostOrderDocumentLine,
    })
    readonly orderLine: Reference<fakePackage.nodes.FakeAllocatedLandedCostOrderDocumentLine | null>;

    @decorators.jsonProperty<FakeAllocatedLandedCostTransactionDocumentLine, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.jsonProperty<FakeAllocatedLandedCostTransactionDocumentLine, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<FakeAllocatedLandedCostTransactionDocumentLine, 'computedAttributes'>({
        isPublished: true,
        async getValue() {
            return {
                item: await this.item,
            };
        },
    })
    readonly computedAttributes: Promise<object>;

    async getOrderDocumentLine(): Promise<
        | xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.order>
        | undefined
    > {
        return (await this.orderLine) ?? undefined;
    }
}
