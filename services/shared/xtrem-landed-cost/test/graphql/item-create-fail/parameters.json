{"test creating a new item with landed cost extension - wrong allcation rule volume unit": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}, "input": {"properties": {"id": "LandedCostItem", "name": "Landed cost item", "status": "active", "description": "Landed cost item", "isBought": true, "stockUnit": "#LITER", "type": "landedCost", "lotManagement": "notManaged", "serialNumberManagement": "notManaged", "salesUnit": "#LITER", "salesUnitToStockUnitConversion": "1.5", "landedCostItem": {"landedCostType": "freight", "allocationRule": "byVolume", "allocationRuleUnit": "#EACH"}}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 7}], "path": ["xtremMasterData", "item", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["landedCostItem", "allocationRuleUnit"], "message": "The record is not valid. You need to select a different record."}, {"severity": 3, "path": ["landedCostItem", "allocationRuleUnit"], "message": "The EACH unit is not a unit of volume."}]}}]}}}