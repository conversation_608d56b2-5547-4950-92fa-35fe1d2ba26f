{"test creating a new item with landed cost extension - success": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}, "input": {"properties": {"id": "LandedCostItem", "name": "Landed cost item", "status": "active", "description": "Landed cost item", "isBought": true, "stockUnit": "#LITER", "type": "landedCost", "lotManagement": "notManaged", "serialNumberManagement": "notManaged", "salesUnit": "#LITER", "salesUnitToStockUnitConversion": "1.5", "landedCostItem": {"landedCostType": "freight", "allocationRule": "byVolume", "allocationRuleUnit": "#LITER"}}}, "output": {"create": {"id": "LandedCostItem", "name": "Landed cost item", "landedCostItem": {"landedCostType": "freight", "allocationRule": "byVolume", "allocationRuleUnit": {"id": "LITER"}}}}}}