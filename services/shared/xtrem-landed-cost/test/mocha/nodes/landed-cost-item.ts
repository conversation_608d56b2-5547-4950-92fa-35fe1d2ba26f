import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';

describe('Landed Cost Item node', () => {
    before(() => {});

    it('Create item node with landed cost extension - success', () =>
        Test.withContext(
            async context => {
                const itemLandedCost = await context.create(xtremMasterData.nodes.Item, {
                    id: 'LandedCostItem',
                    name: 'Landed cost item',
                    status: 'active',
                    description: 'Landed cost item',
                    isBought: true,
                    stockUnit: { id: 'LITER' },
                    type: 'landedCost',
                    lotManagement: 'notManaged',
                    serialNumberManagement: 'notManaged',
                    salesUnit: { id: 'LITER' },
                    salesUnitToStockUnitConversion: 1.5,
                    landedCostItem: {
                        landedCostType: 'freight',
                        allocationRule: 'byVolume',
                        allocationRuleUnit: {
                            id: 'LITER',
                        },
                    },
                });
                assert.isOk(await itemLandedCost.$.control());
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Create item node with landed cost extension - wrong allocation rule volume unit', () =>
        Test.withContext(
            async context => {
                const itemLandedCost = await context.create(xtremMasterData.nodes.Item, {
                    id: 'LandedCostItem',
                    name: 'Landed cost item',
                    status: 'active',
                    description: 'Landed cost item',
                    isBought: true,
                    stockUnit: { id: 'LITER' },
                    type: 'landedCost',
                    lotManagement: 'notManaged',
                    serialNumberManagement: 'notManaged',
                    salesUnit: { id: 'LITER' },
                    salesUnitToStockUnitConversion: 1.5,
                    landedCostItem: {
                        landedCostType: 'freight',
                        allocationRule: 'byVolume',
                        allocationRuleUnit: {
                            id: 'EACH',
                        },
                    },
                });
                await assert.isRejected(itemLandedCost.$.save());
                assert.deepEqual(itemLandedCost.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['landedCostItem', 'allocationRuleUnit'],
                        message: 'The record is not valid. You need to select a different record.',
                    },
                    {
                        severity: 3,
                        path: ['landedCostItem', 'allocationRuleUnit'],
                        message: 'The EACH unit is not a unit of volume.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Create item node with landed cost extension - wrong allocation rule weight unit', () =>
        Test.withContext(
            async context => {
                const itemLandedCost = await context.create(xtremMasterData.nodes.Item, {
                    id: 'LandedCostItem',
                    name: 'Landed cost item',
                    status: 'active',
                    description: 'Landed cost item',
                    isBought: true,
                    stockUnit: { id: 'GRAM' },
                    type: 'landedCost',
                    lotManagement: 'notManaged',
                    serialNumberManagement: 'notManaged',
                    salesUnit: { id: 'GRAM' },
                    salesUnitToStockUnitConversion: 1.5,
                    landedCostItem: {
                        landedCostType: 'freight',
                        allocationRule: 'byWeight',
                        allocationRuleUnit: {
                            id: 'EACH',
                        },
                    },
                });
                await assert.isRejected(itemLandedCost.$.save());
                assert.deepEqual(itemLandedCost.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['landedCostItem', 'allocationRuleUnit'],
                        message: 'The record is not valid. You need to select a different record.',
                    },
                    {
                        severity: 3,
                        path: ['landedCostItem', 'allocationRuleUnit'],
                        message: 'The EACH unit is not a unit of weight.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    // test to ensure that isStockManaged, isBought, isManufactured and isSold is handled according to the business rules for landedCost
    it('Create Item node -  landedCost item business rules applied', () =>
        Test.withContext(
            async context => {
                const itemLandedCost = await context.create(xtremMasterData.nodes.Item, {
                    id: 'LandedCostItem',
                    name: 'Landed cost item',
                    status: 'active',
                    description: 'Landed cost item',
                    isStockManaged: true,
                    isBought: false,
                    isManufactured: true,
                    isSold: true,
                    stockUnit: { id: 'GRAM' },
                    type: 'landedCost',
                    lotManagement: 'notManaged',
                    serialNumberManagement: 'notManaged',
                    salesUnit: { id: 'GRAM' },
                    salesUnitToStockUnitConversion: 1.5,
                });
                await itemLandedCost.$.save();

                assert.deepEqual(itemLandedCost.$.context.diagnoses, []);
                assert.isTrue(await context.exists(xtremMasterData.nodes.Item, { id: 'LandedCostItem' }));
                assert.equal(await itemLandedCost.supplierPrices.length, 0);
                assert.equal(await itemLandedCost.customerPrices.length, 0);
                assert.strictEqual(await itemLandedCost.isStockManaged, false);
                assert.strictEqual(await itemLandedCost.isSold, false);
                assert.strictEqual(await itemLandedCost.isManufactured, false);
                assert.strictEqual(await itemLandedCost.isBought, true);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));
});
