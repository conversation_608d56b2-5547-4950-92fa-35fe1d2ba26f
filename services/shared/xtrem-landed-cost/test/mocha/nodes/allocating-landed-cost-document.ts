import type { Context } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremLandedCost from '../../../lib';
import type { LandedCostAllocation } from '../../../lib/nodes';
import { LandedCostDocumentLine } from '../../../lib/nodes';
import { testHelpers } from '../../fixtures/lib';
import {
    FakeAllocatedLandedCostTransactionDocument,
    FakeAllocatingLandedCostDocument,
    FakeNotAllocableLandedCostDocument,
} from '../../fixtures/lib/nodes';

describe('Allocating Landed Cost Document', () => {
    before(() => {});

    async function createFakeNotAllocableDocumentLines(context: Context) {
        const allocatedDocument = await context.create(FakeNotAllocableLandedCostDocument, {
            number: 'TEST',
            lines: [
                {
                    item: '#Chair',
                    quantityInStockUnit: 10,
                },
            ],
        });
        await allocatedDocument.$.save();
        assert.deepEqual(allocatedDocument.$.context.diagnoses, []);
        return allocatedDocument;
    }

    it('Create an allocating landed cost document line for a landed cost item - success', () =>
        Test.withContext(
            async context => {
                const landedCostDocument = await context.create(FakeAllocatingLandedCostDocument, {
                    lines: [
                        {
                            item: '#LandedCost002',
                            lineAmount: 100,
                            landedCost: {
                                allocationMethod: 'manual',
                            },
                        },
                    ],
                });
                await landedCostDocument.$.save();
                assert.deepEqual(landedCostDocument.$.context.diagnoses, []);
                assert.deepEqual(
                    await (
                        await (
                            await landedCostDocument.lines.elementAt(0)
                        ).landedCost
                    )?.allocationMethod,
                    'manual',
                );
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Create an allocating landed cost document line for a non landed cost item with landed cost properties - fail', () =>
        Test.withContext(
            async context => {
                const landedCostDocument = await context.create(FakeAllocatingLandedCostDocument, {
                    lines: [
                        {
                            item: '#Chair',
                            lineAmount: 100,
                            landedCost: {
                                allocationMethod: 'manual',
                            },
                        },
                    ],
                });
                await assert.isRejected(landedCostDocument.$.save());
                assert.deepEqual(landedCostDocument.$.context.diagnoses, [
                    {
                        message:
                            'This document line cannot have landed cost properties because the Chair item is not a landed cost.',
                        path: ['lines', '-1000000002', 'landedCost'],
                        severity: 3,
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Create an allocating landed cost document line and its allocations - success', () =>
        Test.withContext(
            async context => {
                // simulate an invoice with 1 line for a landed cost
                // The allocation of this landed cost is done on 3 receipt lines

                // create 3 pseudo-receipts
                const allocatedDocument = await testHelpers.createFakeAllocatedDocument(
                    context,
                    xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction,
                );

                const landedCostAllocationAmounts = {
                    //
                    input: [
                        {
                            costAmount: 30,
                            costAmountInCompanyCurrency: 36,
                            allocatedDocumentLine: await allocatedDocument.landedCostAssignableLines.elementAt(0),
                        },
                        {
                            costAmount: 40,
                            costAmountInCompanyCurrency: 50,
                            allocatedDocumentLine: await allocatedDocument.landedCostAssignableLines.elementAt(1),
                        },
                        {
                            costAmount: 30,
                            costAmountInCompanyCurrency: 33,
                            allocatedDocumentLine: await allocatedDocument.landedCostAssignableLines.elementAt(2),
                        },
                    ],
                    // conversion factor in FakeAllocatingLandedCostDocumentLine = 1.2
                    // => total amount in company currency = (30+40+30) * 1.2 = 120
                    // As 36+50+33 = 119 => variance 120-119=1 is added to the highest value (50)
                    output: [
                        {
                            costAmount: 30,
                            costAmountInCompanyCurrency: 36,
                            allocatedDocumentType: 'fakeAllocatedLandedCostTransactionDocument',
                        },
                        {
                            costAmount: 40,
                            costAmountInCompanyCurrency: 51,
                            allocatedDocumentType: 'fakeAllocatedLandedCostTransactionDocument',
                        },
                        {
                            costAmount: 30,
                            costAmountInCompanyCurrency: 33,
                            allocatedDocumentType: 'fakeAllocatedLandedCostTransactionDocument',
                        },
                    ],
                };
                // Allocate landed cost amounts to these 2 pseudo-receipt lines
                const landedCostDocument = await context.create(FakeAllocatingLandedCostDocument, {
                    lines: [
                        {
                            item: '#LandedCost002',
                            lineAmount: landedCostAllocationAmounts.input.reduce(
                                (sum, input) => sum + input.costAmount,
                                0,
                            ),
                            landedCost: {
                                allocationMethod: 'manual',
                                // At this step, the last input is not used. It is done later
                                allocations: landedCostAllocationAmounts.input.slice(0, -1),
                            },
                        },
                    ],
                });
                await landedCostDocument.$.save();
                assert.deepEqual(landedCostDocument.$.context.diagnoses, []);

                // complete the previous allocation
                const landedCostAllocations = (await (await landedCostDocument.lines.elementAt(0)).landedCost)
                    ?.allocations;

                assert.isNotNull(landedCostAllocations ?? null);
                if (!landedCostAllocations) return;

                await landedCostAllocations.append({
                    allocatedDocumentLine: await allocatedDocument.landedCostAssignableLines.elementAt(0),
                    costAmount: 30,
                    costAmountInCompanyCurrency: 33,
                });
                await landedCostDocument.$.save();
                assert.deepEqual(landedCostDocument.$.context.diagnoses, []);

                assert.deepEqual(await landedCostAllocations.length, landedCostAllocationAmounts.output.length);

                await landedCostAllocations.forEach(async (landedCostAllocation, index) => {
                    const message = `index=${index}`;
                    assert.deepEqual(
                        Number(await landedCostAllocation.costAmount),
                        Number(landedCostAllocationAmounts.output[index].costAmount),
                        message,
                    );
                    assert.deepEqual(
                        Number(await landedCostAllocation.costAmountInCompanyCurrency),
                        Number(landedCostAllocationAmounts.output[index].costAmountInCompanyCurrency),
                        message,
                    );
                    assert.deepEqual(
                        Number(await landedCostAllocation.allocatedDocumentType),
                        Number(landedCostAllocationAmounts.output[index].allocatedDocumentType),
                        message,
                    );
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Create an allocating landed cost document line and its allocations - fails', () =>
        Test.withContext(
            async context => {
                // simulate an invoice with 1 line for a landed cost
                // The allocation of this landed cost is done on 3 receipt lines

                // create 3 pseudo-receipts
                const allocatedDocument = await testHelpers.createFakeAllocatedDocument(
                    context,
                    xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction,
                );

                const landedCostAllocationAmounts = {
                    //
                    input: [
                        {
                            costAmount: 30,
                            costAmountInCompanyCurrency: 36,
                            allocatedDocumentLine: await allocatedDocument.landedCostAssignableLines.elementAt(0),
                        },
                        {
                            costAmount: 40,
                            costAmountInCompanyCurrency: 50,
                            allocatedDocumentLine: await allocatedDocument.landedCostAssignableLines.elementAt(1),
                        },
                        {
                            costAmount: 30,
                            costAmountInCompanyCurrency: 33,
                            allocatedDocumentLine: await allocatedDocument.landedCostAssignableLines.elementAt(2),
                        },
                    ],
                };
                // Allocate landed cost amounts to these 3 pseudo-receipt lines
                let landedCostDocument = await context.create(FakeAllocatingLandedCostDocument, {
                    lines: [
                        {
                            item: '#LandedCost002',
                            // 100-1 = 99
                            lineAmount:
                                landedCostAllocationAmounts.input.reduce((sum, input) => sum + input.costAmount, 0) - 1,
                            landedCost: {
                                allocationMethod: 'manual',
                                allocations: landedCostAllocationAmounts.input,
                            },
                        },
                    ],
                });
                await assert.isRejected(landedCostDocument.$.save());
                assert.deepEqual(landedCostDocument.$.context.diagnoses, [
                    {
                        message: 'The total of allocated amounts ($100) cannot exceed the amount to allocate $99.',
                        path: ['lines', '-1000000006', 'landedCost'],
                        severity: 3,
                    },
                ]);

                const allocatedDocumentWithNotStockManagedItem = await context.create(
                    FakeAllocatedLandedCostTransactionDocument,
                    {
                        number: 'TEST',
                        lines: [
                            {
                                item: '#NonStockManagedItem',
                                quantityInStockUnitForLandedCostAllocation: 10,
                            },
                        ],
                    },
                );

                await allocatedDocumentWithNotStockManagedItem.$.save();
                assert.deepEqual(allocatedDocumentWithNotStockManagedItem.$.context.diagnoses, []);

                landedCostDocument = await context.create(FakeAllocatingLandedCostDocument, {
                    lines: [
                        {
                            item: '#LandedCost002',
                            lineAmount: 100,
                            landedCost: {
                                allocationMethod: 'manual',
                                allocations: [
                                    {
                                        costAmount: 30,
                                        costAmountInCompanyCurrency: 36,
                                        allocatedDocumentLine:
                                            await allocatedDocumentWithNotStockManagedItem.lines.elementAt(0),
                                    },
                                ],
                            },
                        },
                    ],
                });

                await assert.isRejected(landedCostDocument.$.save());
                assert.deepEqual(landedCostDocument.$.context.diagnoses, [
                    {
                        message:
                            'The item NonStockManagedItem is not stock managed. You cannot allocate it to a landed cost.',
                        path: ['lines', '-1000000014', 'landedCost', 'allocations', '-1000000016'],
                        severity: 3,
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Create an allocating landed cost document line and its allocations on wrong document line - fails', () =>
        Test.withContext(
            async context => {
                // simulate an invoice with 1 line for a landed cost
                // The allocation of this landed cost is done on 3 receipt lines

                // create 3 pseudo-receipts
                const allocatedDocument = await createFakeNotAllocableDocumentLines(context);

                const landedCostAllocationAmounts = {
                    //
                    input: [
                        {
                            costAmount: 30,
                            costAmountInCompanyCurrency: 36,
                            allocatedDocumentLine: await allocatedDocument.lines.elementAt(0),
                        },
                    ],
                };
                // Allocate landed cost amounts to these 3 pseudo-receipt lines
                const landedCostDocument = await context.create(FakeAllocatingLandedCostDocument, {
                    lines: [
                        {
                            item: '#LandedCost002',
                            lineAmount: landedCostAllocationAmounts.input.reduce(
                                (sum, input) => sum + input.costAmount,
                                0,
                            ),
                            landedCost: {
                                allocationMethod: 'manual',
                                allocations: landedCostAllocationAmounts.input,
                            },
                        },
                    ],
                });
                await assert.isRejected(landedCostDocument.$.save());
                assert.deepEqual(landedCostDocument.$.context.diagnoses, [
                    {
                        message: 'You cannot allocate landed costs to this document type.',
                        path: ['lines', '-1000000004', 'landedCost', 'allocations', '-1000000006'],
                        severity: 3,
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Create an allocating landed cost document line and run the query queryAllocations - success', () =>
        Test.withContext(
            async context => {
                // simulate an invoice with 1 line for a landed cost
                // The allocation of this landed cost is done on 3 receipt lines

                // create 3 pseudo-receipts
                const allocatedDocument = await testHelpers.createFakeAllocatedDocument(
                    context,
                    xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction,
                );

                const allocatedDocumentLines = await allocatedDocument.landedCostAssignableLines.toArray();
                const landedCostAllocationAmounts = {
                    //
                    input: [
                        {
                            costAmount: 30,
                            costAmountInCompanyCurrency: 36,
                            allocatedDocumentLine: allocatedDocumentLines[0],
                        },
                        {
                            costAmount: 40,
                            costAmountInCompanyCurrency: 50,
                            // The consistency between the document type and the node is done by the package of the node
                            // Here, we use the wrong type to check the saving only, not the consistency,
                            allocatedDocumentLine: allocatedDocumentLines[1],
                        },
                        {
                            costAmount: 30,
                            costAmountInCompanyCurrency: 33,
                            allocatedDocumentLine: allocatedDocumentLines[2],
                        },
                    ],
                };

                const expectedAllocations = [
                    {
                        _id: 4,
                        allocatedDocumentType: 'fakeAllocatedLandedCostTransactionDocument',
                        allocatedDocumentLine: {
                            _id: allocatedDocumentLines[0]._id,
                            documentNumber: 'TEST',
                            documentId: allocatedDocument._id,
                        },
                        item: {
                            id: 'Chair',
                            name: 'Chair',
                            stockUnit: {
                                id: 'EACH',
                                symbol: 'each',
                                decimalDigits: 0,
                            },
                        },
                        costAmount: 30,
                        costAmountInCompanyCurrency: 36,
                        documentLineAmountInCompanyCurrency: 123,
                        documentLineQuantityInStockUnit: 10,
                        documentLineWeight: 3,
                        documentLineVolume: 0,
                    },
                    {
                        _id: 5,
                        allocatedDocumentType: 'fakeAllocatedLandedCostTransactionDocument',
                        allocatedDocumentLine: {
                            _id: allocatedDocumentLines[1]._id,
                            documentNumber: 'TEST',
                            documentId: allocatedDocument._id,
                        },
                        item: {
                            id: 'ChairLeg',
                            name: 'Chair leg',
                            stockUnit: {
                                id: 'EACH',
                                symbol: 'each',
                                decimalDigits: 0,
                            },
                        },
                        costAmount: 40,
                        costAmountInCompanyCurrency: 51,
                        documentLineAmountInCompanyCurrency: 456,
                        documentLineQuantityInStockUnit: 40,
                        documentLineWeight: 12,
                        documentLineVolume: 0,
                    },
                    {
                        _id: 6,
                        allocatedDocumentType: 'fakeAllocatedLandedCostTransactionDocument',
                        allocatedDocumentLine: {
                            _id: allocatedDocumentLines[2]._id,
                            documentNumber: 'TEST',
                            documentId: allocatedDocument._id,
                        },
                        item: {
                            id: 'ChairSeat',
                            name: 'Chair seat',
                            stockUnit: {
                                id: 'EACH',
                                symbol: 'each',
                                decimalDigits: 0,
                            },
                        },
                        costAmount: 30,
                        costAmountInCompanyCurrency: 33,
                        documentLineAmountInCompanyCurrency: 789,
                        documentLineQuantityInStockUnit: 12,
                        documentLineWeight: 3.6,
                        documentLineVolume: 0,
                    },
                ];
                // Allocate landed cost amounts to these 3 pseudo-receipt lines
                const landedCostDocument = await context.create(FakeAllocatingLandedCostDocument, {
                    lines: [
                        {
                            item: '#LandedCost002',
                            lineAmount: landedCostAllocationAmounts.input.reduce(
                                (sum, input) => sum + input.costAmount,
                                0,
                            ),
                            landedCost: {
                                allocationMethod: 'manual',
                                allocations: landedCostAllocationAmounts.input,
                            },
                        },
                    ],
                });
                await landedCostDocument.$.save();

                const landedCostDocumentLine = await (await landedCostDocument.lines.elementAt(0)).landedCost;

                assert.isNotNull(landedCostDocumentLine);
                if (!landedCostDocumentLine) return;

                const allocations = await LandedCostDocumentLine.queryAllocations(
                    context,
                    String(landedCostDocumentLine._id),
                );

                assert.deepEqual(allocations.length, expectedAllocations.length);
                allocations.forEach(allocation => {
                    const expectedAllocation =
                        expectedAllocations.find(expected => allocation.item.id === expected.item.id) ?? null;
                    assert.isNotNull(expectedAllocation);
                    if (!expectedAllocation) return;
                    assert.deepEqual(allocation.allocatedDocumentType, expectedAllocation.allocatedDocumentType);
                    assert.deepEqual(allocation.allocatedDocumentLine, expectedAllocation.allocatedDocumentLine);
                    assert.deepEqual(Number(allocation.costAmount), Number(expectedAllocation.costAmount));
                    assert.deepEqual(
                        Number(allocation.costAmountInCompanyCurrency),
                        Number(expectedAllocation.costAmountInCompanyCurrency),
                    );
                    assert.deepEqual(
                        Number(allocation.documentLineAmountInCompanyCurrency),
                        Number(expectedAllocation.documentLineAmountInCompanyCurrency),
                    );
                    assert.deepEqual(
                        Number(allocation.documentLineQuantityInStockUnit),
                        Number(expectedAllocation.documentLineQuantityInStockUnit),
                    );
                    assert.deepEqual(
                        Number(allocation.documentLineWeight),
                        Number(expectedAllocation.documentLineWeight),
                    );
                    assert.deepEqual(
                        Number(allocation.documentLineVolume),
                        Number(expectedAllocation.documentLineVolume),
                    );
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Update landed cost allocations - success', () =>
        Test.withContext(
            async context => {
                // simulate an invoice with 1 line for a landed cost
                // The allocation of this landed cost is done on 3 receipt lines

                // create 3 pseudo-receipts
                const allocatedDocument = await testHelpers.createFakeAllocatedDocument(
                    context,
                    xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction,
                );

                // Allocate landed cost amounts to these 2 pseudo-receipt lines
                const landedCostDocument = await context.create(FakeAllocatingLandedCostDocument, {
                    lines: [
                        {
                            item: '#LandedCost002',
                            lineAmount: 500,
                            landedCost: {
                                allocationMethod: 'manual',
                                allocations: [
                                    {
                                        costAmount: 30,
                                        costAmountInCompanyCurrency: 36,
                                        allocatedDocumentLine:
                                            await allocatedDocument.landedCostAssignableLines.elementAt(0),
                                    },
                                    {
                                        costAmount: 230,
                                        costAmountInCompanyCurrency: 236,
                                        allocatedDocumentLine:
                                            await allocatedDocument.landedCostAssignableLines.elementAt(1),
                                    },
                                ],
                            },
                        },
                    ],
                });

                await landedCostDocument.$.save();
                assert.deepEqual(landedCostDocument.$.context.diagnoses, []);

                // complete the previous allocation by using the function LandedCostDocumentLine.updateAllocations
                const landedCostDocumentLine = (await landedCostDocument.lines.elementAt(0)).landedCost;
                assert.isNotNull(await landedCostDocumentLine);
                if (!(await landedCostDocumentLine)) return;

                const updatedAllocation = await (
                    await landedCostDocumentLine
                )?.allocations?.find(
                    async alloc =>
                        (await alloc.allocatedDocumentLine)._id ===
                        (await allocatedDocument.landedCostAssignableLines.elementAt(0))._id,
                );
                assert.isDefined(updatedAllocation);
                if (!updatedAllocation) return;

                const lastId = (
                    await context.query(xtremLandedCost.nodes.LandedCostAllocation, { last: 1 }).elementAt(0)
                )._id;

                await LandedCostDocumentLine.updateAllocations(context, {
                    landedCostDocumentLine: landedCostDocumentLine as Promise<LandedCostDocumentLine>,
                    allocationMethod: 'automatic',
                    allocationUpdates: [
                        {
                            _id: (
                                (await (
                                    await landedCostDocumentLine
                                )?.allocations.elementAt(0)) as xtremLandedCost.nodes.LandedCostAllocation
                            )._id,
                            _action: 'update',
                            allocatedDocumentLine: (await allocatedDocument.landedCostAssignableLines.elementAt(0))._id,
                            allocatedAmount: 33, // 30 -> 33
                            allocatedAmountInCompanyCurrency: 37, // 36 -> 37
                        },
                        {
                            _id: -1,
                            _action: 'create',
                            allocatedDocumentLine: (await allocatedDocument.landedCostAssignableLines.elementAt(1))._id,
                            allocatedAmount: 123,
                            allocatedAmountInCompanyCurrency: 132,
                        },
                        {
                            _id: (
                                (await (await landedCostDocumentLine)?.allocations.elementAt(1)) as LandedCostAllocation
                            )._id,
                            _action: 'delete',
                        },
                    ],
                });

                const actualAllocations = ((await landedCostDocumentLine) as LandedCostDocumentLine).allocations;

                const expectedAllocations = [
                    {
                        _id: updatedAllocation._id,
                        allocatedDocumentType: 'fakeAllocatedLandedCostTransactionDocument',
                        allocatedDocumentLine: (await allocatedDocument.landedCostAssignableLines.elementAt(0))._id,
                        costAmount: 33,
                        costAmountInCompanyCurrency: 37,
                    },
                    {
                        _id: lastId + 1,
                        allocatedDocumentType: 'fakeAllocatedLandedCostTransactionDocument',
                        allocatedDocumentLine: (await allocatedDocument.landedCostAssignableLines.elementAt(1))._id,
                        costAmount: 123,
                        costAmountInCompanyCurrency: 132,
                    },
                ];
                assert.deepEqual(await actualAllocations.length, expectedAllocations.length);

                await actualAllocations.forEach(async (actualAllocation, index) => {
                    const message = `index=${index}`;
                    // _id are tested because they are known
                    assert.deepEqual(Number(actualAllocation._id), Number(expectedAllocations[index]._id), message);
                    assert.deepEqual(
                        Number(actualAllocation.allocatedDocumentType),
                        Number(expectedAllocations[index].allocatedDocumentType),
                        message,
                    );
                    assert.deepEqual(
                        Number(await actualAllocation.allocatedDocumentLine),
                        Number(expectedAllocations[index].allocatedDocumentLine),
                        message,
                    );
                    assert.deepEqual(
                        Number(await actualAllocation.costAmount),
                        Number(expectedAllocations[index].costAmount),
                        message,
                    );
                    assert.deepEqual(
                        Number(await actualAllocation.costAmountInCompanyCurrency),
                        Number(expectedAllocations[index].costAmountInCompanyCurrency),
                        message,
                    );
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Create credit for landed cost allocations - success', () =>
        Test.withContext(
            async context => {
                // simulate an invoice with 1 line for a landed cost
                // The allocation of this landed cost is done on 3 receipt lines

                // create 3 pseudo-receipts
                const { allocatingDocument } = await testHelpers.createFakeAllocatedDocumentWithLandedCosts(
                    context,
                    xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction,
                );

                const creditDocument = await testHelpers.createFakeCreditDocument(
                    context,
                    'CREDIT',
                    allocatingDocument,
                );

                const allocatingDocument2 = await context.read(FakeAllocatingLandedCostDocument, {
                    _id: allocatingDocument._id,
                });
                // createFakeCreditDocument creates full credit => check values credited is the total amount
                await allocatingDocument2.lines.forEach(async line => {
                    await (
                        await line.landedCost
                    )?.allocations.forEach(async (allocation, index) => {
                        assert.deepEqual(
                            await allocation.creditedCostAmount,
                            await allocation.costAmount,
                            `item=${await (await line.item).id} index=${index}`,
                        );
                    });
                });

                assert.deepEqual(await creditDocument.lines.length, 3);

                await creditDocument.lines.forEach(async creditLine => {
                    const messageLine = `item.id=${await (await creditLine.item).id}`;
                    const landedCost = await creditLine.landedCost;
                    assert.isNotNull(landedCost, messageLine);
                    if (!landedCost) return;

                    await landedCost.allocations.forEach(async (creditAllocation, index) => {
                        const message = `${messageLine} allocation index=${index}`;
                        const sourceAllocationLine = await creditAllocation.sourceAllocationLine;
                        assert.isNotNull(sourceAllocationLine, message);
                        if (!sourceAllocationLine) return;

                        assert.deepEqual(
                            await creditAllocation.costAmount,
                            await sourceAllocationLine.creditedCostAmount,
                            message,
                        );
                        assert.deepEqual(
                            await creditAllocation.costAmountInCompanyCurrency,
                            await sourceAllocationLine.creditedCostAmountInCompanyCurrency,
                            message,
                        );
                    });
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Update credit for landed cost allocations - fails', () =>
        Test.withContext(
            async context => {
                // simulate an invoice with 1 line for a landed cost
                // The allocation of this landed cost is done on 3 receipt lines

                // create 3 pseudo-receipts
                const { allocatingDocument } = await testHelpers.createFakeAllocatedDocumentWithLandedCosts(
                    context,
                    xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction,
                );

                await testHelpers.createFakeCreditDocument(context, 'CREDIT', allocatingDocument);

                const writableAllocatingDocument = await xtremMasterData.functions.getWritableNode(
                    context,
                    FakeAllocatingLandedCostDocument,
                    allocatingDocument._id,
                );

                const line0 = await writableAllocatingDocument.lines.elementAt(0);
                const line1 = await writableAllocatingDocument.lines.elementAt(1);
                const landedCost0 = await line0.landedCost;
                assert.isNotNull(landedCost0);
                if (!landedCost0) throw new Error('landedCost0 is null');
                const landedCost1 = await line1.landedCost;
                assert.isNotNull(landedCost1);
                if (!landedCost1) throw new Error('landedCost1 is null');

                const allocation00 = await landedCost0.allocations.elementAt(0);
                const allocation10 = await landedCost1.allocations.elementAt(0);
                const allocation11 = await landedCost1.allocations.elementAt(1);
                await writableAllocatingDocument.$.set({
                    lines: [
                        {
                            _action: 'update',
                            _id: line0._id,
                            landedCost: {
                                allocations: [
                                    {
                                        _id: allocation00._id,
                                        creditedCostAmount: -10,
                                        creditedCostAmountInCompanyCurrency: -5,
                                    },
                                ],
                            },
                        },
                        {
                            _action: 'update',
                            _id: line1._id,
                            landedCost: {
                                allocations: [
                                    {
                                        _id: allocation10._id,
                                        // creditCostAmount becomes less than costAmount => error
                                        creditedCostAmount: (await allocation10.costAmount) + 1,
                                        creditedCostAmountInCompanyCurrency:
                                            (await allocation10.costAmountInCompanyCurrency) + 1,
                                    },
                                    {
                                        _id: allocation11._id,
                                        // costAmount becomes less than creditCostAmount => error
                                        costAmount: (await allocation11.costAmount) - 1,
                                        costAmountInCompanyCurrency:
                                            (await allocation11.costAmountInCompanyCurrency) - 1,
                                        // sourceAllocationLine cannot be assigned if a credited amount exists
                                        sourceAllocationLine: allocation00,
                                    },
                                ],
                            },
                        },
                    ],
                });

                const line0Str = line0._id.toString(); // 31
                const allocation0 = allocation00._id.toString(); // 20
                const line1Str = line1._id.toString();
                const allocation1 = allocation10._id.toString();
                const allocation2 = allocation11._id.toString();

                await assert.isRejected(writableAllocatingDocument.$.save());
                assert.deepEqual(
                    writableAllocatingDocument.$.context.diagnoses,
                    [
                        {
                            message: 'value must not be negative',
                            path: ['lines', line0Str, 'landedCost', 'allocations', allocation0, 'creditedCostAmount'],
                            severity: 3,
                        },
                        {
                            message: 'value must not be negative',
                            path: [
                                'lines',
                                line0Str,
                                'landedCost',
                                'allocations',
                                allocation0,
                                'creditedCostAmountInCompanyCurrency',
                            ],
                            severity: 3,
                        },
                        {
                            message: 'value must not be greater than 100',
                            path: ['lines', line1Str, 'landedCost', 'allocations', allocation1, 'creditedCostAmount'],
                            severity: 3,
                        },
                        {
                            message: 'value must not be greater than 100',
                            path: [
                                'lines',
                                line1Str,
                                'landedCost',
                                'allocations',
                                allocation1,
                                'creditedCostAmountInCompanyCurrency',
                            ],
                            severity: 3,
                        },
                        {
                            message: 'value must not be greater than 249',
                            path: ['lines', line1Str, 'landedCost', 'allocations', allocation2, 'creditedCostAmount'],
                            severity: 3,
                        },
                        {
                            message: 'You can only credit purchase invoice landed cost records.',
                            path: ['lines', line1Str, 'landedCost', 'allocations', allocation2, 'creditedCostAmount'],
                            severity: 3,
                        },
                        {
                            message: 'value must not be greater than 249',
                            path: [
                                'lines',
                                line1Str,
                                'landedCost',
                                'allocations',
                                allocation2,
                                'creditedCostAmountInCompanyCurrency',
                            ],
                            severity: 3,
                        },
                        {
                            message: 'You can only credit purchase invoice landed cost records.',
                            path: [
                                'lines',
                                `${line1._id}`,
                                'landedCost',
                                'allocations',
                                `${allocation11._id}`,
                                'creditedCostAmountInCompanyCurrency',
                            ],
                            severity: 3,
                        },
                    ],
                    JSON.stringify(writableAllocatingDocument.$.context.diagnoses),
                );

                await assert.isRejected(writableAllocatingDocument.$.save());
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));
});
