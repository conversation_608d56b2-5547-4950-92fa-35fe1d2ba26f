import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremLandedCost from '../../../lib';
import { testHelpers } from '../../fixtures/lib';

describe('Allocated Landed Cost Document', () => {
    before(() => {});

    it('Create an allocated landed cost document line and get the total landed cost', () =>
        Test.withContext(
            async context => {
                // create 3 pseudo-receipts and assign some landed costs
                const { allocatedDocument } = await testHelpers.createFakeAllocatedDocumentWithLandedCosts(
                    context,
                    xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction,
                );

                const lines = await allocatedDocument.landedCostAssignableLines.toArray();
                assert.deepEqual(await lines[0].landedCostLines.length, 2);
                assert.deepEqual(await lines[1].landedCostLines.length, 3);

                assert.deepEqual(Number(await lines[0].actualLandedCostInCompanyCurrency), 250);
                assert.deepEqual(Number(await lines[1].actualLandedCostInCompanyCurrency), 750);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));
});
