import { Test, asyncArray } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremLandedCost from '../../../lib';
import { testHelpers } from '../../fixtures/lib';
import { FakeAllocatedLandedCostOrderDocument } from '../../fixtures/lib/nodes';

describe('Landed cost document line lib', () => {
    before(() => {});

    it('test getSortedLandedCostAllocations', () =>
        Test.withContext(
            async context => {
                /* Create an order with 3 lines
                The first 2 lines have LandedCostLine records (see function createFakeAllocatingDocument)
                - line0:
                    -> LandedCost002 amount=100
                    -> LandedCost003 amount=150
                - line1:
                    -> LandedCost001 amount=200
                    -> LandedCost002 amount=250
                    -> LandedCost003 amount=300
                 */
                let fakeOrderDocument = (
                    await testHelpers.createFakeAllocatedDocumentWithLandedCosts(
                        context,
                        xtremLandedCost.enums.LandedCostDocumentTypeEnum.order,
                    )
                ).allocatedDocument;

                // create more invoices with landed costs
                await testHelpers.createFakeAllocatingDocument(context, 'INV231201', fakeOrderDocument);
                await testHelpers.createFakeAllocatingDocument(context, 'INV231202', fakeOrderDocument);

                const fakeOrderDocumentId = fakeOrderDocument._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                fakeOrderDocument = await context.read(FakeAllocatedLandedCostOrderDocument, {
                    _id: fakeOrderDocumentId,
                });

                const allocations =
                    await xtremLandedCost.functions.landedCostAllocationLib.getSortedLandedCostAllocations(
                        context,
                        await fakeOrderDocument.landedCostAssignableLines.elementAt(0),
                    );

                assert.deepEqual(
                    await asyncArray(allocations)
                        .map(async allocation => (await (await allocation.line).documentLine).documentNumber)
                        .toArray(),
                    ['INV231201', 'INV231201', 'INV231202', 'INV231202', 'TEST', 'TEST'],
                );
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption],
            },
        ));
});
