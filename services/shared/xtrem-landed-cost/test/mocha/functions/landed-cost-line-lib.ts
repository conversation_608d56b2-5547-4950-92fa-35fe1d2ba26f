import type { decimal } from '@sage/xtrem-core';
import { Test, asyncArray } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremLandedCost from '../../../lib';
import { testHelpers } from '../../fixtures/lib';

describe('Landed cost document line lib', () => {
    before(() => {});

    it('Get summary of landed costs per type', () =>
        Test.withContext(
            async context => {
                const { allocatedDocument: fakeDocument } =
                    await testHelpers.createFakeAllocatedDocumentWithLandedCosts(
                        context,
                        xtremLandedCost.enums.LandedCostDocumentTypeEnum.order,
                    );

                const aggregateLandedCosts: {
                    landedCostType: string;
                    actualCostAmountInCompanyCurrency: decimal;
                    actualAllocatedCostAmountInCompanyCurrency: decimal;
                }[] = await xtremLandedCost.functions.landedCostLineLib.getLandedCostsPerType(
                    fakeDocument.landedCostAssignableLines,
                );

                assert.deepEqual(aggregateLandedCosts.length, 2);

                const expectedResults = [
                    {
                        landedCostType: 'freight',
                        actualCostAmountInCompanyCurrency: 800,
                        actualAllocatedCostAmountInCompanyCurrency: 80,
                    },
                    {
                        landedCostType: 'customs',
                        actualCostAmountInCompanyCurrency: 200,
                        actualAllocatedCostAmountInCompanyCurrency: 20,
                    },
                ];
                expectedResults.forEach((expectedResult, index) => {
                    const message = `index=${index}`;
                    assert.deepEqual(
                        aggregateLandedCosts[index].landedCostType,
                        expectedResult.landedCostType,
                        message,
                    );
                    assert.deepEqual(
                        Number(aggregateLandedCosts[index].actualCostAmountInCompanyCurrency),
                        expectedResult.actualCostAmountInCompanyCurrency,
                        message,
                    );
                    assert.deepEqual(
                        Number(aggregateLandedCosts[index].actualAllocatedCostAmountInCompanyCurrency),
                        expectedResult.actualAllocatedCostAmountInCompanyCurrency,
                        message,
                    );
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('test propagateLandedCostLinesFromOrder & getLandedCostLinesFromOrderLine', () =>
        Test.withContext(
            async context => {
                /* Create an order with 3 lines
                The first 2 lines have LandedCostLine records (see function createFakeAllocatingDocument)
                - line0:
                    -> LandedCost002 amount=100
                    -> LandedCost003 amount=150
                - line1:
                    -> LandedCost001 amount=200
                    -> LandedCost002 amount=250
                    -> LandedCost003 amount=300
                 */
                const { allocatedDocument: fakeOrderDocument } =
                    await testHelpers.createFakeAllocatedDocumentWithLandedCosts(
                        context,
                        xtremLandedCost.enums.LandedCostDocumentTypeEnum.order,
                        {
                            number: 'TEST',
                            lines: [
                                // 3 = remaining qty considering the transaction that is created just after (quantity of the order/2 = 10/2=5)
                                {
                                    item: '#Chair',
                                    quantityInStockUnitForLandedCostAllocation: 10,
                                    amountForLandedCostAllocation: 987,
                                    remainingQuantityToProcessForLandedCost: 3,
                                },
                                // 14 = remaining qty considering the transaction that is created just after (quantity of the order/2 = 40/2=20)
                                {
                                    item: '#ChairLeg',
                                    quantityInStockUnitForLandedCostAllocation: 40,
                                    amountForLandedCostAllocation: 988,
                                    remainingQuantityToProcessForLandedCost: 14,
                                },
                                // 3 = remaining qty considering the transaction that is created just after (quantity of the order/2 = 12/2=6)
                                {
                                    item: '#ChairSeat',
                                    quantityInStockUnitForLandedCostAllocation: 12,
                                    amountForLandedCostAllocation: 999,
                                    remainingQuantityToProcessForLandedCost: 3,
                                },
                            ],
                        },
                    );
                // receive half of the ordered quantity
                const fakeTransactionDocument = await testHelpers.createFakeAllocatedDocument(
                    context,
                    xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction,
                    {
                        number: 'TEST_RECEIPT',
                        lines: await fakeOrderDocument.landedCostAssignableLines
                            .map(async orderLine => {
                                return {
                                    item: await orderLine.item,
                                    orderLine,
                                    amountForLandedCostAllocation: (await orderLine.amountForLandedCostAllocation) / 2,
                                    quantityInStockUnitForLandedCostAllocation:
                                        (await orderLine.quantityInStockUnitForLandedCostAllocation) / 2,
                                };
                            })
                            .toArray(),
                    },
                );

                await xtremLandedCost.functions.landedCostLineLib.propagateLandedCostLinesFromOrder(
                    context,
                    fakeTransactionDocument.landedCostAssignableLines,
                );

                const expectedResults = [
                    {
                        documentLineId: (await fakeTransactionDocument.landedCostAssignableLines.elementAt(0))._id,
                        landedCostLines: [
                            {
                                landedCost: { id: 'LandedCost002' },
                                // 56.25 = 90 (not yet allocated amount) * 5 (transaction qty) /8 (remaining qty in the order before the transaction)
                                actualCostAmountInCompanyCurrency: 56.25,
                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                            },
                            {
                                // 84.38 = 135 (not yet allocated amount) * 5 (transaction qty) /8 (remaining qty in the order before the transaction)
                                landedCost: { id: 'LandedCost003' },
                                actualCostAmountInCompanyCurrency: 84.38,
                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                            },
                        ],
                    },
                    {
                        documentLineId: (await fakeTransactionDocument.landedCostAssignableLines.elementAt(1))._id,
                        landedCostLines: [
                            {
                                landedCost: { id: 'LandedCost001' },
                                actualCostAmountInCompanyCurrency: 105.88,
                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                            },
                            {
                                landedCost: { id: 'LandedCost002' },
                                actualCostAmountInCompanyCurrency: 132.35,
                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                            },
                            {
                                landedCost: { id: 'LandedCost003' },
                                actualCostAmountInCompanyCurrency: 158.82,
                                actualAllocatedCostAmountInCompanyCurrency: 0.0,
                            },
                        ],
                    },
                    {
                        documentLineId: (await fakeTransactionDocument.landedCostAssignableLines.elementAt(2))._id,
                        landedCostLines: [],
                    },
                    {
                        documentLineId: (await fakeOrderDocument.landedCostAssignableLines.elementAt(0))._id,
                        landedCostLines: [
                            {
                                landedCost: { id: 'LandedCost002' },
                                actualCostAmountInCompanyCurrency: 100.0,
                                actualAllocatedCostAmountInCompanyCurrency: 66.25,
                            },
                            {
                                landedCost: { id: 'LandedCost003' },
                                actualCostAmountInCompanyCurrency: 150.0,
                                actualAllocatedCostAmountInCompanyCurrency: 99.38,
                            },
                        ],
                    },
                    {
                        documentLineId: (await fakeOrderDocument.landedCostAssignableLines.elementAt(1))._id,
                        landedCostLines: [
                            {
                                landedCost: { id: 'LandedCost001' },
                                actualCostAmountInCompanyCurrency: 200.0,
                                actualAllocatedCostAmountInCompanyCurrency: 125.88,
                            },
                            {
                                landedCost: { id: 'LandedCost002' },
                                actualCostAmountInCompanyCurrency: 250.0,
                                actualAllocatedCostAmountInCompanyCurrency: 157.35,
                            },
                            {
                                landedCost: { id: 'LandedCost003' },
                                actualCostAmountInCompanyCurrency: 300.0,
                                actualAllocatedCostAmountInCompanyCurrency: 188.82,
                            },
                        ],
                    },
                    {
                        documentLineId: (await fakeOrderDocument.landedCostAssignableLines.elementAt(2))._id,
                        landedCostLines: [],
                    },
                ];
                await asyncArray(expectedResults).forEach(async expectedResult => {
                    const actualResults = await context.select(
                        xtremLandedCost.nodes.LandedCostLine,
                        {
                            landedCost: { id: true },
                            actualCostAmountInCompanyCurrency: true,
                            actualAllocatedCostAmountInCompanyCurrency: true,
                        },
                        {
                            filter: {
                                documentLine: expectedResult.documentLineId,
                            },
                        },
                    );
                    actualResults.forEach(actualResult => {
                        const expectedResultLine = expectedResult.landedCostLines.find(
                            line => line.landedCost.id === actualResult.landedCost.id,
                        );
                        assert.deepEqual(actualResult, expectedResultLine);
                    });
                });
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption],
            },
        ));
});
