import { asyncArray } from '@sage/xtrem-async-helper';
import type { ExtractArray, decimal } from '@sage/xtrem-client';
import type { GraphApi, LandedCostAllocationBinding, LandedCostBaseDocument } from '@sage/xtrem-landed-cost-api';
import type { Currency, Item } from '@sage/xtrem-master-data-api';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import * as ui from '@sage/xtrem-ui';

import type * as LandedCostInterfaces from '../client-functions/interfaces';
import type { QueryAllocationResult } from '../client-functions/landed-cost-allocation-helpers';
import { clearNestedGrid } from '../client-functions/landed-cost-allocation-helpers';

@ui.decorators.page<LandedCostDocumentLinePickList>({
    title: 'Add lines',
    mode: 'default',
    module: 'xtrem-landed-cost',
    isTransient: true,
    businessActions() {
        return [this.cancel, this.confirm];
    },
    onLoad() {
        this.companyId.value = this.$.queryParameters.companyId as string;
        this.siteId.value = this.$.queryParameters.siteId as string;
        this.alreadyPickedLineIds.value = this.$.queryParameters.alreadyPickedLineIds as string;
        this.allocationRule.value = this.$.queryParameters.allocationRule as string;
        this.allocationRuleUnitId.value = this.$.queryParameters.allocationRuleUnitId as string;
        this.documentType.value = this.$.queryParameters.documentType as string;
    },

    onError(error: string | (Error & { errors: Array<any> })) {
        return utils.formatError(this, error);
    },
})
export class LandedCostDocumentLinePickList extends ui.Page<GraphApi> {
    @ui.decorators.textField<LandedCostDocumentLinePickList>({
        isHidden: true,
    })
    companyId: ui.fields.Text;

    @ui.decorators.textField<LandedCostDocumentLinePickList>({
        isHidden: true,
    })
    siteId: ui.fields.Text;

    @ui.decorators.textField<LandedCostDocumentLinePickList>({
        isHidden: true,
    })
    alreadyPickedLineIds: ui.fields.Text;

    @ui.decorators.textField<LandedCostDocumentLinePickList>({
        isHidden: true,
    })
    allocationRule: ui.fields.Text;

    @ui.decorators.textField<LandedCostDocumentLinePickList>({
        isHidden: true,
    })
    allocationRuleUnitId: ui.fields.Text;

    @ui.decorators.textField<LandedCostDocumentLinePickList>({
        isHidden: true,
    })
    documentType: ui.fields.Text;

    @ui.decorators.pageAction<LandedCostDocumentLinePickList>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.pageAction<LandedCostDocumentLinePickList>({
        title: 'Add',
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                const selectedDocumentLines = utils.returnSelectedChildrenFromNestedGrid(this.documents);

                const documentLines = await asyncArray(selectedDocumentLines)
                    .map(rowId => this.getFormattedReturn(this.documents.getRecordValue(rowId, 1)))
                    .toArray();

                this.$.finish(documentLines);
            } else {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            }
        },
        isDisabled() {
            if (this.documents.value.length > 0) {
                return !this.documents.selectedRecords.some(el => el.length > 0);
            }
            return true;
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.section<LandedCostDocumentLinePickList>({
        isTitleHidden: true,
    })
    lineSection: ui.containers.Section;

    @ui.decorators.block<LandedCostDocumentLinePickList>({
        parent() {
            return this.lineSection;
        },
        title: 'Criteria',
        width: 'large',
    })
    lineBlock: ui.containers.Block;

    @ui.decorators.referenceField<LandedCostDocumentLinePickList, Item>({
        parent() {
            return this.lineBlock;
        },
        title: 'Item',
        lookupDialogTitle: 'Select item',
        minLookupCharacters: 3,
        node: '@sage/xtrem-master-data/Item',
        shouldSuggestionsIncludeColumns: true,
        helperTextField: 'id',
        valueField: 'name',
        isAutoSelectEnabled: true,
        width: 'small',
        onChange() {
            clearNestedGrid(this);
        },
        filter() {
            return {
                isStockManaged: true,
                itemSites: {
                    _atLeast: 1,
                    site: {
                        legalCompany: { _id: { _eq: this.companyId.value } },
                    },
                },
            };
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
        ],
    })
    item: ui.fields.Reference<Item>;

    @ui.decorators.block<LandedCostDocumentLinePickList>({
        parent() {
            return this.lineSection;
        },
        title: '',
        width: 'large',
    })
    resultsBlock: ui.containers.Block;

    @ui.decorators.nestedGridField<
        LandedCostDocumentLinePickList,
        [LandedCostInterfaces.DocumentNestedGrid, LandedCostInterfaces.DocumentLineNestedGrid]
    >({
        parent() {
            return this.resultsBlock;
        },
        bind: 'documents',
        levels: [
            {
                node: '@sage/xtrem-landed-cost/LandedCostBaseDocument',
                childProperty: 'lines',
                columns: [
                    ui.nestedFields.text<LandedCostDocumentLinePickList, LandedCostInterfaces.DocumentNestedGrid>({
                        title: 'Number',
                        bind: 'number',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric<LandedCostDocumentLinePickList, LandedCostInterfaces.DocumentNestedGrid>({
                        title: 'Total tax excluded Amount',
                        bind: 'totalAmountExcludingTax',
                        isReadOnly: true,
                        canFilter: false,
                        scale(_rowId, rowData) {
                            return utils.getScaleValue(2, rowData?.currency?.decimalDigits);
                        },
                        prefix(_rowId, rowData) {
                            return rowData?.currency?.symbol || '';
                        },
                    }),
                    ui.nestedFields.technical<
                        LandedCostDocumentLinePickList,
                        LandedCostInterfaces.DocumentNestedGrid,
                        Currency
                    >({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            },
            {
                node: '@sage/xtrem-master-data/BaseDocumentLine',
                orderBy: { _sortValue: +1 },
                columns: [
                    ui.nestedFields.technical({ bind: '_sortValue' }),
                    ui.nestedFields.reference<
                        LandedCostDocumentLinePickList,
                        LandedCostInterfaces.DocumentLineNestedGrid,
                        Item
                    >({
                        bind: 'item',
                        title: 'Item name',
                        node: '@sage/xtrem-master-data/Item',
                        isReadOnly: true,
                        valueField: 'name',
                        columns: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'weight' }),
                            ui.nestedFields.technical({ bind: 'volume' }),
                            ui.nestedFields.technical({
                                bind: 'stockUnit',
                                node: '@sage/xtrem-master-data/UnitOfMeasure',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'symbol' }),
                                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                ],
                            }),
                            ui.nestedFields.technical({
                                bind: 'purchaseUnit',
                                node: '@sage/xtrem-master-data/UnitOfMeasure',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'symbol' }),
                                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                ],
                            }),
                            ui.nestedFields.technical({
                                bind: 'weightUnit',
                                node: '@sage/xtrem-master-data/UnitOfMeasure',
                                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                            }),
                            ui.nestedFields.technical({
                                bind: 'volumeUnit',
                                node: '@sage/xtrem-master-data/UnitOfMeasure',
                                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                            }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'quantityInStockUnit' }),

                    ui.nestedFields.numeric<
                        LandedCostDocumentLinePickList,
                        LandedCostInterfaces.DocumentLineNestedGrid
                    >({
                        title: 'Line tax excluded amount',
                        isReadOnly: true,
                        bind: 'amountExcludingTax',
                        scale(_rowId, rowData) {
                            return utils.getScaleValue(2, rowData?.currency?.decimalDigits);
                        },
                        prefix(_rowId, rowData) {
                            return rowData?.currency?.symbol || '';
                        },
                    }),
                    ui.nestedFields.technical<
                        LandedCostDocumentLinePickList,
                        LandedCostInterfaces.DocumentLineNestedGrid,
                        Currency
                    >({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical<
                        LandedCostDocumentLinePickList,
                        LandedCostInterfaces.DocumentLineNestedGrid,
                        LandedCostBaseDocument
                    >({
                        bind: 'document',
                        node: '@sage/xtrem-landed-cost/LandedCostBaseDocument',
                        nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
                    }),
                ],
            },
        ],
    })
    documents: ui.fields.NestedGrid<
        [LandedCostInterfaces.DocumentNestedGrid, LandedCostInterfaces.DocumentLineNestedGrid],
        LandedCostDocumentLinePickList
    >;

    async getFormattedReturn(
        line: ui.PartialNodeWithId<LandedCostInterfaces.DocumentLineNestedGrid> | null,
    ): Promise<Partial<ExtractArray<QueryAllocationResult>>> {
        return {
            allocatedDocumentType: (this.documentType.value ??
                '') as LandedCostAllocationBinding['allocatedDocumentType'],
            allocatedDocumentLine: this.getFormattedAllocatedDocumentLine(line),
            item: this.getFormattedItem(line),
            costAmount: '0',
            costAmountInCompanyCurrency: '0',
            documentLineAmountInCompanyCurrency: Number(line?.amountExcludingTax ?? 0).toString(),
            documentLineQuantityInStockUnit: Number(line?.quantityInStockUnit ?? 0).toString(),
            documentLineWeight: (
                await this.getLandedCostWeight(
                    Number(line?.quantityInStockUnit ?? 0),
                    line?.item?.weightUnit?._id ?? '',
                    Number(line?.item?.weight ?? 0),
                )
            ).toString(),
            documentLineVolume: (
                await this.getLandedCostVolume(
                    Number(line?.quantityInStockUnit ?? 0),
                    line?.item?.volumeUnit?._id ?? '',
                    Number(line?.item?.volume ?? 0),
                )
            ).toString(),
        };
    }

    // eslint-disable-next-line class-methods-use-this
    getFormattedAllocatedDocumentLine(line: ui.PartialNodeWithId<LandedCostInterfaces.DocumentLineNestedGrid> | null) {
        return {
            _id: line?._id ?? '',
            documentNumber: line?.document?.number ?? '',
            documentId: Number(line?.document?._id ?? 0),
        };
    }

    // eslint-disable-next-line class-methods-use-this
    getFormattedItem(line: ui.PartialNodeWithId<LandedCostInterfaces.DocumentLineNestedGrid> | null) {
        return {
            id: line?.item?.id ?? '',
            name: line?.item?.name ?? '',
            stockUnit: {
                id: line?.item?.stockUnit?.id ?? '',
                symbol: line?.item?.stockUnit?.symbol ?? '',
                decimalDigits: line?.item?.stockUnit?.decimalDigits ?? 0,
            },
        };
    }

    async getLandedCostWeight(quantityInStockUnit: decimal, weightUnitId: string, weight: decimal): Promise<decimal> {
        if (weightUnitId !== this.allocationRuleUnitId.value && this.allocationRule.value === 'byWeight') {
            const result = await this.$.graph
                .node('@sage/xtrem-master-data/UnitOfMeasure')
                .queries.convertFromTo(false, {
                    fromUnit: weightUnitId,
                    toUnit: this.allocationRuleUnitId.value ?? '',
                    quantity: Number(quantityInStockUnit * weight),
                })
                .execute();
            return Number(result);
        }
        return quantityInStockUnit * weight;
    }

    async getLandedCostVolume(quantityInStockUnit: decimal, volumeUnitId: string, volume: decimal): Promise<decimal> {
        if (volumeUnitId !== this.allocationRuleUnitId.value && this.allocationRule.value === 'byVolume') {
            const result = await this.$.graph
                .node('@sage/xtrem-master-data/UnitOfMeasure')
                .queries.convertFromTo(false, {
                    fromUnit: volumeUnitId,
                    toUnit: this.allocationRuleUnitId.value ?? '',
                    quantity: Number(quantityInStockUnit * volume),
                })
                .execute();
            return Number(result);
        }
        return quantityInStockUnit * volume;
    }

    getThis() {
        return this as LandedCostDocumentLinePickList;
    }

    // eslint-disable-next-line class-methods-use-this
    isSelectingPurchaseReceipt() {
        return false;
    }

    // eslint-disable-next-line class-methods-use-this
    isSelectingPurchaseOrder() {
        return false;
    }

    // eslint-disable-next-line class-methods-use-this
    isSelectingStockTransferReceipt() {
        return false;
    }
}
