import type { integer } from '@sage/xtrem-client';
import type { GraphApi, LandedCostAllocationBinding, LandedCostAllocationMethod } from '@sage/xtrem-landed-cost-api';
import type { Currency, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import { getCurrencyInfo } from '@sage/xtrem-master-data/build/lib/client-functions/common';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { LineValidationMessage } from '@sage/xtrem-master-data/build/lib/shared-functions/interfaces/finance-integration';
import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-master-data/lib/client-functions/page-functions';
import type { Company, Site } from '@sage/xtrem-system-api';
import { setOrderOfPageTableHeaderBusinessActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { tokens } from '@sage/xtrem-ui';
import { upperFirst } from 'lodash';
import { getDocumentPageName, getDocumentTypeName } from '../client-functions/common';
import * as displayButtons from '../client-functions/display-buttons-landed-cost-allocation-panel';
import type * as LandedCostInterfaces from '../client-functions/interfaces';
import {
    calculateCostAmounts,
    getQueryParameters,
    isDirtyPage,
    mapAllocationsToPage,
    refreshPanel,
    updateTotalAllocatedAmount,
    updateTotalDistributionValue,
} from '../client-functions/landed-cost-allocation-helpers';

@ui.decorators.page<LandedCostAllocationPanel, LandedCostAllocationBinding>({
    title: 'Landed cost allocation',
    mode: 'default',
    isTransient: true,
    businessActions() {
        return [this.save, this.cancel];
    },
    async onLoad() {
        await this.init();
        this._manageDisplayApplicativePageActions();
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
})
export class LandedCostAllocationPanel extends ui.Page<GraphApi> {
    headerBusinessActions: ui.PageAction[] = [];

    private panelParameters: LandedCostInterfaces.LandedCostAllocationParameters;

    private lineValidationFinanceErrors: LineValidationMessage[];

    @ui.decorators.referenceField<LandedCostAllocationPanel, Company>({
        node: '@sage/xtrem-system/Company',
        valueField: '_id',
        isHidden: true,
    })
    company: ui.fields.Reference<Company>;

    @ui.decorators.referenceField<LandedCostAllocationPanel, Site>({
        node: '@sage/xtrem-system/Site',
        valueField: '_id',
        isHidden: true,
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<LandedCostAllocationPanel, Currency>({
        node: '@sage/xtrem-master-data/Currency',
        valueField: 'symbol',
        isHidden: true,
        columns: [ui.nestedFields.technical({ bind: 'decimalDigits' })],
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.referenceField<LandedCostAllocationPanel, Currency>({
        node: '@sage/xtrem-master-data/Currency',
        valueField: 'symbol',
        isHidden: true,
        columns: [ui.nestedFields.technical({ bind: 'decimalDigits' })],
    })
    companyCurrency: ui.fields.Reference<Currency>;

    @ui.decorators.section<LandedCostAllocationPanel>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<LandedCostAllocationPanel>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'General',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.numericField<LandedCostAllocationPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Amount to allocate',
        isDisabled: true,
        unit() {
            return this.currency.value;
        },
    })
    amountToAllocate: ui.fields.Numeric;

    @ui.decorators.numericField<LandedCostAllocationPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Allocated amount',
        isDisabled: true,
        unit() {
            return this.currency.value;
        },
    })
    allocatedAmount: ui.fields.Numeric;

    @ui.decorators.separatorField<LandedCostAllocationPanel>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    mainSeparatorLine1: ui.fields.Separator;

    @ui.decorators.numericField<LandedCostAllocationPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Amount to allocate in company currency',
        isDisabled: true,
        scale() {
            return MasterDataUtils.getScaleValue(2, this.companyCurrency.value?.decimalDigits);
        },
        prefix() {
            return this.companyCurrency.value?.symbol ?? '';
        },
    })
    amountToAllocateInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.numericField<LandedCostAllocationPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Allocated amount in company currency',
        isDisabled: true,
        scale() {
            return MasterDataUtils.getScaleValue(2, this.companyCurrency.value?.decimalDigits);
        },
        prefix() {
            return this.companyCurrency.value?.symbol ?? '';
        },
    })
    allocatedAmountInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.separatorField<LandedCostAllocationPanel>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    mainSeparatorLine2: ui.fields.Separator;

    @ui.decorators.dropdownListField<LandedCostAllocationPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Allocation rule',
        optionType: '@sage/xtrem-landed-cost/AllocationRule',
        isDisabled: true,
    })
    allocationRule: ui.fields.DropdownList;

    @ui.decorators.referenceField<LandedCostAllocationPanel, UnitOfMeasure>({
        parent() {
            return this.mainBlock;
        },
        title: 'Allocation rule unit',
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
        valueField: 'name',
        helperTextField: 'symbol',
        isDisabled: true,
        columns: [
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'symbol' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
        ],
        isHidden() {
            return !['byWeight', 'byVolume'].includes(this.allocationRule.value ?? '');
        },
    })
    allocationRuleUnit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.dropdownListField<LandedCostAllocationPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Allocation method',
        optionType: '@sage/xtrem-landed-cost/LandedCostAllocationMethod',
        isDisabled: true,
    })
    allocationMethod: ui.fields.DropdownList;

    @ui.decorators.numericField<LandedCostAllocationPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Total receipt amount in company currency',
        isDisabled: true,
        isHidden() {
            return this.allocationRule.value !== 'byAmount';
        },
        scale() {
            return MasterDataUtils.getScaleValue(2, this.companyCurrency.value?.decimalDigits);
        },
        prefix() {
            return this.companyCurrency.value?.symbol ?? '';
        },
    })
    totalReceiptAmountInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.numericField<LandedCostAllocationPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Total weight',
        isDisabled: true,
        isHidden() {
            return this.allocationRule.value !== 'byWeight';
        },
        scale() {
            return MasterDataUtils.getScaleValue(2, this.allocationRuleUnit.value?.decimalDigits);
        },
        postfix() {
            return this.allocationRuleUnit.value?.symbol ?? '';
        },
    })
    totalWeight: ui.fields.Numeric;

    @ui.decorators.numericField<LandedCostAllocationPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Total volume',
        isDisabled: true,
        isHidden() {
            return this.allocationRule.value !== 'byVolume';
        },
        scale() {
            return MasterDataUtils.getScaleValue(2, this.allocationRuleUnit.value?.decimalDigits);
        },
        postfix() {
            return this.allocationRuleUnit.value?.symbol ?? '';
        },
    })
    totalVolume: ui.fields.Numeric;

    @ui.decorators.numericField<LandedCostAllocationPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Total quantity',
        isDisabled: true,
        isHidden: true, // makes no sense as unit of items can be different
    })
    totalQuantityInStockUnit: ui.fields.Numeric;

    // save the initial lines to be used in extension => must be a field and not only a property
    @ui.decorators.tableField<LandedCostAllocationPanel, LandedCostInterfaces.LandedCostAllocationPageBinding>({
        columns: [],
    })
    initialLines: ui.fields.Table<LandedCostInterfaces.LandedCostAllocationPageBinding>;

    @ui.decorators.tableField<LandedCostAllocationPanel, LandedCostInterfaces.LandedCostAllocationPageBinding>({
        title: 'Allocated documents',
        canUserHideColumns: false,
        canSelect: false,
        // TODO: XT-62301
        // add the canAddNewLine decorator below
        // when we will be able to have both drop down action button + phantom row hidden
        // canAddNewLine: true,
        // isPhantomRowHidden: true,
        warningMessage() {
            if (this.lineValidationFinanceErrors.length) {
                return this.lineValidationFinanceErrors
                    .map(lineValidation => '* '.concat(lineValidation.message))
                    .join('\n');
            }
            return '';
        },
        orderBy: {
            allocatedDocumentLine: { documentNumber: +1 },
            item: +1,
        },
        columns: [
            ui.nestedFields.icon({
                bind: '_id', // link to a non-transient field
                title() {
                    return ' ';
                },
                size: 'small',
                color: tokens.colorsSemanticCaution500,
                map(_value?, rowValue?) {
                    return this.lineValidationFinanceErrors?.some(
                        lineValidation =>
                            lineValidation.sourceDocumentNumber === rowValue?.allocatedDocumentLine?.documentNumber,
                    )
                        ? 'warning'
                        : '';
                },
                isHidden() {
                    return !this.lineValidationFinanceErrors.length;
                },
            }),
            ui.nestedFields.label({
                title: 'Document type',
                bind: 'allocatedDocumentType',
                map(value) {
                    return getDocumentTypeName(upperFirst(value) ?? '');
                },
            }),
            ui.nestedFields.technical({
                bind: 'allocatedDocumentLine',
                node: '@sage/xtrem-master-data/BaseDocumentLine',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'documentNumber' }),
                    ui.nestedFields.technical({ bind: 'documentId' }),
                ],
            }),
            ui.nestedFields.link({
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                title: 'Document number',
                isFullWidth: true,
                map(_value, rowData?: ui.PartialCollectionValue<LandedCostInterfaces.LandedCostAllocationPageBinding>) {
                    return String(rowData?.allocatedDocumentLine?.documentNumber);
                },
                onClick(_id, rowData: ui.PartialCollectionValue<LandedCostInterfaces.LandedCostAllocationPageBinding>) {
                    const page = getDocumentPageName(upperFirst(rowData?.allocatedDocumentType) ?? '');

                    const fromLandedCostPanel = this.lineValidationFinanceErrors?.some(
                        lineValidation =>
                            lineValidation.sourceDocumentNumber === rowData?.allocatedDocumentLine?.documentNumber,
                    );

                    return this.$.dialog.page(
                        page.id,
                        { _id: rowData?.allocatedDocumentLine?.documentId ?? '', fromLandedCostPanel },
                        {
                            fullScreen: true,
                            resolveOnCancel: true,
                        },
                    );
                },
            }),
            ui.nestedFields.reference({
                bind: 'item',
                isReadOnly: true,
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({
                        bind: 'stockUnit',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Allocated amount',
                bind: 'costAmount',
                onChange() {
                    this.allocationMethod.value = 'manual';
                    updateTotalAllocatedAmount(this, this.panelParameters);
                    this.manageDisplayButtonAllocateLandedCostAction();
                    this.save.isDisabled = !isDirtyPage(this);
                },
                unit() {
                    return this.currency.value;
                },
            }),
            ui.nestedFields.numeric({
                title: 'Allocated amount in company currency',
                bind: 'costAmountInCompanyCurrency',
                isReadOnly: true,
                isHiddenOnMainField: true,
                prefix() {
                    return this.companyCurrency.value?.symbol ?? '';
                },
                scale() {
                    return MasterDataUtils.getScaleValue(2, this.companyCurrency.value?.decimalDigits);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Amount in company currency',
                bind: 'documentLineAmountInCompanyCurrency',
                isReadOnly: true,
                isHidden() {
                    return this.allocationRule.value !== 'byAmount';
                },
                scale() {
                    return MasterDataUtils.getScaleValue(2, this.companyCurrency.value?.decimalDigits);
                },
                prefix() {
                    return this.companyCurrency.value?.symbol || '';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'documentLineQuantityInStockUnit',
                isReadOnly: true,
                isHidden() {
                    return this.allocationRule.value !== 'byQuantity';
                },
                scale(_rowId, rowData) {
                    return MasterDataUtils.getScaleValue(2, rowData?.item?.stockUnit?.decimalDigits);
                },
                postfix(_rowId, rowData) {
                    return rowData?.item?.stockUnit?.symbol || '';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Weight',
                bind: 'documentLineWeight',
                isReadOnly: true,
                isHidden() {
                    return this.allocationRule.value !== 'byWeight';
                },
                scale() {
                    return MasterDataUtils.getScaleValue(2, this.allocationRuleUnit.value?.decimalDigits);
                },
                postfix() {
                    return this.allocationRuleUnit.value?.symbol || '';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Volume',
                bind: 'documentLineVolume',
                isReadOnly: true,
                isHidden() {
                    return this.allocationRule.value !== 'byVolume';
                },
                scale() {
                    return MasterDataUtils.getScaleValue(2, this.allocationRuleUnit.value?.decimalDigits);
                },
                postfix() {
                    return this.allocationRuleUnit.value?.symbol || '';
                },
            }),
            ui.nestedFields.technical({
                bind: 'distributionValue',
            }),
        ],
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isHidden() {
                    return !this.panelParameters.isEditable;
                },
                onClick(rowId: string) {
                    this.lines.removeRecord(rowId);
                    refreshPanel(this, this.panelParameters);
                },
            },
        ],
        fieldActions() {
            return setOrderOfPageTableHeaderBusinessActions({
                actions: [this.allocateLandedCost],
            });
        },
        parent() {
            return this.mainSection;
        },
        addItemActions() {
            return this.headerBusinessActions;
        },
    })
    lines: ui.fields.Table<LandedCostInterfaces.LandedCostAllocationPageBinding>;

    @ui.decorators.pageAction<LandedCostAllocationPanel>({
        title: 'Allocate landed cost',
        icon: 'refresh',
        isHidden: true,
        onClick() {
            calculateCostAmounts(this, this.panelParameters);
            this.allocationMethod.value = 'automatic';
            this.save.isDisabled = !isDirtyPage(this);
            this.manageDisplayButtonAllocateLandedCostAction();
        },
    })
    allocateLandedCost: ui.PageAction;

    manageDisplayButtonAllocateLandedCostAction() {
        this.allocateLandedCost.isDisabled = displayButtons.isDisabledButtonAllocateLandedCostAction({
            parameters: {
                numberOfLines: this.lines.value.length,
                allocationMethod: this.allocationMethod.value as LandedCostAllocationMethod,
                isEditable: this.panelParameters.isEditable,
            },
        });

        this.allocateLandedCost.isHidden = displayButtons.isHiddenButtonAllocateLandedCostAction({
            parameters: {
                numberOfLines: this.lines.value.length,
                allocationMethod: this.allocationMethod.value as LandedCostAllocationMethod,
                isEditable: this.panelParameters.isEditable,
            },
        });
    }

    @ui.decorators.pageAction<LandedCostAllocationPanel>({
        title: 'Save',
        isDisabled: true,
        async onClick() {
            if (isDirtyPage(this)) {
                let confirmation = true;
                let isFullyAllocated = true;
                if ((this.amountToAllocate.value ?? 0) > (this.allocatedAmount.value ?? 0)) {
                    isFullyAllocated = false;
                    confirmation = await confirmDialogWithAcceptButtonText(
                        this,
                        ui.localize(
                            '@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__insufficient_allocations',
                            'Allocated amount insufficient',
                        ),
                        ui.localize(
                            '@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__total_allocated_lower_than_amount_to_allocate',
                            'The total allocated amount is lower than the landed cost amount to allocate.',
                        ),
                        ui.localize('@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__confirm', 'Confirm'),
                    );
                }

                if (confirmation) {
                    await this.saveAllocations();
                    this.$.finish({ output: { modified: true, isFullyAllocated } });
                }
            }
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<LandedCostAllocationPanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish({ output: { modified: false } });
        },
    })
    cancel: ui.PageAction;

    private async init() {
        const queryParams = getQueryParameters(this.$.queryParameters.args);
        if (!queryParams) {
            throw new Error('Parameters are missing.');
        }
        this.panelParameters = queryParams;
        this.lineValidationFinanceErrors = queryParams.lineValidationFinanceErrors ?? [];
        await this.setTransientValues();

        await this.loadExistingLines();

        updateTotalAllocatedAmount(this, this.panelParameters);
        updateTotalDistributionValue(this);

        this.$.setPageClean();

        this.initialLines.value = this.lines.value;
    }

    private _manageDisplayApplicativePageActions() {
        this.manageDisplayButtonAllocateLandedCostAction();
    }

    private async setTransientValues() {
        this.company.value = { _id: this.panelParameters.company };
        this.site.value = { _id: this.panelParameters.site };
        let currency = await getCurrencyInfo(this, this.panelParameters.currency);
        this.currency.value = { ...currency, decimalDigits: Number(currency.decimalDigits) };
        currency = await getCurrencyInfo(this, this.panelParameters.companyCurrency);
        this.companyCurrency.value = { ...currency, decimalDigits: Number(currency.decimalDigits) };

        const landedCostDocumentLine = await this.$.graph
            .node('@sage/xtrem-landed-cost/LandedCostDocumentLine')
            .read(
                {
                    costAmountToAllocate: true,
                    costAmountToAllocateInCompanyCurrency: true,
                    allocationRule: true,
                    allocationRuleUnit: {
                        _id: true,
                        id: true,
                        name: true,
                        symbol: true,
                        decimalDigits: true,
                    },
                    allocationMethod: true,
                },

                this.panelParameters.landedCostDocumentLine,
            )
            .execute();

        if (!landedCostDocumentLine) {
            return;
        }

        this.amountToAllocate.value = Number(landedCostDocumentLine.costAmountToAllocate);
        this.amountToAllocateInCompanyCurrency.value = Number(
            landedCostDocumentLine.costAmountToAllocateInCompanyCurrency,
        );
        this.allocationRule.value = landedCostDocumentLine.allocationRule;
        this.allocationRuleUnit.value = {
            _id: landedCostDocumentLine.allocationRuleUnit?._id ?? '',
            id: landedCostDocumentLine.allocationRuleUnit?.id ?? '',
            name: landedCostDocumentLine.allocationRuleUnit?.name ?? '',
            symbol: landedCostDocumentLine.allocationRuleUnit?.symbol ?? '',
            decimalDigits: Number(landedCostDocumentLine.allocationRuleUnit?.decimalDigits ?? 0),
        };
        this.allocationMethod.value = landedCostDocumentLine.allocationMethod;
    }

    async loadExistingLines() {
        const landedCostAllocations = await this.$.graph
            .node('@sage/xtrem-landed-cost/LandedCostDocumentLine')
            .queries.queryAllocations(
                {
                    _id: true,
                    allocatedDocumentType: true,
                    allocatedDocumentLine: {
                        _id: true,
                        documentNumber: true,
                        documentId: true,
                    },

                    item: {
                        id: true,
                        name: true,
                        stockUnit: {
                            id: true,
                            symbol: true,
                            decimalDigits: true,
                        },
                    },
                    costAmount: true,
                    costAmountInCompanyCurrency: true,
                    documentLineAmountInCompanyCurrency: true,
                    documentLineQuantityInStockUnit: true,
                    documentLineWeight: true,
                    documentLineVolume: true,
                },
                {
                    landedCostDocumentLineId: this.panelParameters.landedCostDocumentLine,
                },
            )
            .execute();

        mapAllocationsToPage(this, landedCostAllocations);
    }

    async saveAllocations() {
        this.$.loader.isHidden = false;

        const { existingLineIds, allocationUpdates } = this.lines.value.reduce(
            (result, line) => {
                const lineId = Number(line._id);
                const allocation: LandedCostInterfaces.LandedCostAllocationOutput = {
                    _action: lineId > 0 ? 'update' : 'create',
                    _id: line._id,
                    allocatedDocumentLine: String(line.allocatedDocumentLine?._id),
                    allocatedAmount: Number(line.costAmount),
                    allocatedAmountInCompanyCurrency: Number(line.costAmountInCompanyCurrency),
                };
                result.allocationUpdates.push(allocation);

                if (lineId > 0) {
                    result.existingLineIds.push(lineId);
                }
                return result;
            },
            { existingLineIds: [], allocationUpdates: [] } as {
                existingLineIds: integer[];
                allocationUpdates: LandedCostInterfaces.LandedCostAllocationOutput[];
            },
        );

        this.initialLines.value.forEach(line => {
            const lineId = Number(line._id);
            if (line._id && lineId > 0 && !existingLineIds.includes(lineId)) {
                allocationUpdates.push({ _action: 'delete', _id: line._id });
            }
        }, [] as integer[]);

        await this.$.graph
            .node('@sage/xtrem-landed-cost/LandedCostDocumentLine')
            .mutations.updateAllocations(true, {
                allocationData: {
                    landedCostDocumentLine: this.panelParameters.landedCostDocumentLine,
                    allocationMethod: this.allocationMethod.value as LandedCostAllocationMethod,
                    allocationUpdates,
                },
            })
            .execute();

        this.$.loader.isHidden = true;
    }
}
