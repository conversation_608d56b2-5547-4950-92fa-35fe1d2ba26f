import type { decimal } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { GraphApi, LandedCostLine, LandedCostLineBinding } from '@sage/xtrem-landed-cost-api';
import type { Currency } from '@sage/xtrem-master-data-api';
import { getCurrencyInfo } from '@sage/xtrem-master-data/build/lib/client-functions/common';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import * as ui from '@sage/xtrem-ui';
import { getDocumentPageName } from '../client-functions/common';
import type * as LandedCostInterfaces from '../client-functions/interfaces';

@ui.decorators.page<LandedCostSummary, LandedCostLineBinding>({
    title: 'Landed costs',
    mode: 'default',
    businessActions() {
        return [this.cancel];
    },
    async onLoad() {
        await this.init();
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
})
export class LandedCostSummary extends ui.Page<GraphApi> {
    queryParams: LandedCostInterfaces.LandedCostSummaryParameters;

    @ui.decorators.referenceField<LandedCostSummary, Currency>({
        node: '@sage/xtrem-master-data/Currency',
        valueField: 'symbol',
        isHidden: true,
        columns: [ui.nestedFields.technical({ bind: 'decimalDigits' })],
    })
    companyCurrency: ui.fields.Reference<Currency>;

    @ui.decorators.section<LandedCostSummary>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<LandedCostSummary>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Total in company currency',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.numericField<LandedCostSummary>({
        parent() {
            return this.mainBlock;
        },
        title: 'Actual landed costs',
        isDisabled: true,
        scale() {
            return MasterDataUtils.getScaleValue(2, this.companyCurrency.value?.decimalDigits);
        },
        prefix() {
            return this.companyCurrency.value?.symbol ?? '';
        },
    })
    actualLandedCostInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.tableField<LandedCostSummary, LandedCostInterfaces.LandedCostLinePageBinding>({
        title: 'Landed costs in company currency',
        canSelect: false,
        orderBy: {
            landedCost: { name: +1 },
        },
        columns: [
            ui.nestedFields.reference({
                title: 'Landed cost',
                bind: 'landedCost',
                isDisabled: true,
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            ui.nestedFields.reference({
                bind: 'landedCostAllocation',
                isHidden: true,
                node: '@sage/xtrem-master-data/Item',
                valueField: { line: { documentLine: { documentNumber: true } } },
                columns: [
                    ui.nestedFields.technical({
                        bind: { line: { documentLine: { _constructor: true, documentId: true } } },
                    }),
                ],
            }),
            ui.nestedFields.link({
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                title: 'Document number',
                isFullWidth: true,
                map(_value, rowData?: ui.PartialCollectionValue<LandedCostInterfaces.LandedCostLinePageBinding>) {
                    return String(rowData?.landedCostAllocation?.line?.documentLine?.documentNumber);
                },
                onClick(_id, rowData: ui.PartialCollectionValue<LandedCostInterfaces.LandedCostLinePageBinding>) {
                    const page = getDocumentPageName(
                        rowData?.landedCostAllocation?.line?.documentLine?._constructor ?? '',
                    );

                    return this.$.dialog.page(
                        page.id,
                        { _id: rowData?.landedCostAllocation?.line?.documentLine?.documentId ?? '' },
                        {
                            fullScreen: true,
                            resolveOnCancel: true,
                        },
                    );
                },
            }),
            ui.nestedFields.numeric({
                title: 'Actual cost amount',
                bind: 'actualCostAmountInCompanyCurrency',
                prefix() {
                    return this.companyCurrency.value?.symbol ?? '';
                },
                scale() {
                    return MasterDataUtils.getScaleValue(2, this.companyCurrency.value?.decimalDigits);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Actual allocated cost amount',
                bind: 'actualAllocatedCostAmountInCompanyCurrency',
                isHidden() {
                    return !!this.queryParams.hideAllocatedAmountColumn;
                },
                prefix() {
                    return this.companyCurrency.value?.symbol ?? '';
                },
                scale() {
                    return MasterDataUtils.getScaleValue(2, this.companyCurrency.value?.decimalDigits);
                },
            }),
        ],

        isReadOnly: true,
        parent() {
            return this.mainSection;
        },
    })
    lines: ui.fields.Table<LandedCostInterfaces.LandedCostLinePageBinding>;

    @ui.decorators.pageAction<LandedCostSummary>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish({});
        },
    })
    cancel: ui.PageAction;

    private async init() {
        this.loadQueryParams(this.$.queryParameters.args);
        await this.setTransientValues();

        await this.loadExistingLines();

        this.$.setPageClean();
    }

    private loadQueryParams(panelArgs: string | number | boolean) {
        const queryParams = MasterDataUtils.tryParseJSON<LandedCostInterfaces.LandedCostSummaryParameters>(
            panelArgs as string,
        );
        if (!queryParams) return;

        this.queryParams = queryParams;
    }

    private async setTransientValues() {
        const currency = await getCurrencyInfo(this, this.queryParams.companyCurrency);
        this.companyCurrency.value = { ...currency, decimalDigits: Number(currency.decimalDigits) };
    }

    async loadExistingLines() {
        let total: decimal = 0;
        this.lines.value = extractEdges<LandedCostLine>(
            await this.$.graph
                .node('@sage/xtrem-landed-cost/LandedCostLine')
                .query(
                    ui.queryUtils.edgesSelector<LandedCostLine>(
                        {
                            _id: true,
                            landedCost: {
                                name: true,
                            },
                            landedCostAllocation: {
                                line: {
                                    documentLine: {
                                        _constructor: true,
                                        documentNumber: true,
                                        documentId: true,
                                    },
                                },
                            },
                            actualCostAmountInCompanyCurrency: true,
                            actualAllocatedCostAmountInCompanyCurrency: true,
                        },
                        {
                            filter: { documentLine: this.queryParams.documentLineId },
                        },
                    ),
                )
                .execute(),
        ).map((line): LandedCostInterfaces.LandedCostLinePageBinding => {
            const actualCostAmountInCompanyCurrency = Number(line.actualCostAmountInCompanyCurrency);
            total += actualCostAmountInCompanyCurrency;
            return {
                _id: line._id,
                landedCost: {
                    name: line.landedCost.name,
                },
                landedCostAllocation: line.landedCostAllocation,
                actualCostAmountInCompanyCurrency,
                actualAllocatedCostAmountInCompanyCurrency: Number(line.actualAllocatedCostAmountInCompanyCurrency),
            };
        });
        this.actualLandedCostInCompanyCurrency.value = total;
    }
}
