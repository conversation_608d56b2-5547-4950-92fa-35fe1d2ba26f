import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-landed-cost-allocation-panel';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-landed-cost-allocation-panel';

export function isHiddenButtonAllocateLandedCostAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.AllocateLandedCostParameters>,
) {
    return (
        data.parameters?.allocationMethod === 'automatic' ||
        data.parameters?.numberOfLines === 0 ||
        !data.parameters?.isEditable
    );
}

export function isDisabledButtonAllocateLandedCostAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.AllocateLandedCostParameters>,
) {
    return isHiddenButtonAllocateLandedCostAction(data);
}

export function isHiddenButtonSelectFromReceiptAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.SelectFromReceiptParameters>,
) {
    return !data.parameters?.isEditable;
}

export function isDisabledButtonSelectFromReceiptAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.SelectFromReceiptParameters>,
) {
    return isHiddenButtonSelectFromReceiptAction(data);
}
