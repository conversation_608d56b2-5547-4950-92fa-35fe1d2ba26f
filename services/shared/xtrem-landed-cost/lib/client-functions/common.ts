import * as ui from '@sage/xtrem-ui';
import type * as LandedCostInterfaces from './interfaces';

function getErrorMessage(constructorName: string) {
    return ui.localize(
        '@sage/xtrem-landed-cost/pages__landed_cost__unknown_document_line_type',
        'The {{constructorName}} type is not managed.',
        { constructorName },
    );
}

/**
 * return the label to display in function of the document line type
 */
export function getDocumentTypeName(nodeName: string): string {
    switch (nodeName) {
        case 'PurchaseInvoice':
        case 'PurchaseInvoiceLine':
            return ui.localize('@sage/xtrem-landed-cost/document_type_name_purchase_invoice', 'Purchase invoice');

        case 'PurchaseOrder':
        case 'PurchaseOrderLine':
            return ui.localize('@sage/xtrem-landed-cost/document_type_name_purchase_order', 'Purchase order');

        case 'PurchaseReceipt':
        case 'PurchaseReceiptLine':
            return ui.localize('@sage/xtrem-landed-cost/document_type_name_purchase_receipt', 'Purchase receipt');

        case 'StockTransferReceipt':
        case 'StockTransferReceiptLine':
            return ui.localize(
                '@sage/xtrem-landed-cost/document_type_name_stock_transfer_receipt',
                'Stock transfer receipt',
            );

        default:
            throw new Error(getErrorMessage(nodeName));
    }
}

export function getDocumentPageName(nodeName: string): LandedCostInterfaces.DocumentPage {
    const name = getDocumentTypeName(nodeName);
    switch (nodeName) {
        case 'PurchaseInvoice':
        case 'PurchaseInvoiceLine': {
            return { id: '@sage/xtrem-purchasing/PurchaseInvoice', name };
        }
        case 'PurchaseOrder':
        case 'PurchaseOrderLine': {
            return { id: '@sage/xtrem-purchasing/PurchaseOrder', name };
        }
        case 'PurchaseReceipt':
        case 'PurchaseReceiptLine': {
            return { id: '@sage/xtrem-purchasing/PurchaseReceipt', name };
        }
        case 'StockTransferReceipt':
        case 'StockTransferLine': {
            return { id: '@sage/xtrem-supply-chain/StockTransferReceipt', name };
        }
        default:
            throw new Error(getErrorMessage(nodeName));
    }
}
