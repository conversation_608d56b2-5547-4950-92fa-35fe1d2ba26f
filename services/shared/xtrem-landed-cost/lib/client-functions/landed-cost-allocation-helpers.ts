import type { ExtractArray, OperationResultType } from '@sage/xtrem-client';
import type { GraphApi, LandedCostDocumentLine$Operations } from '@sage/xtrem-landed-cost-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { convertAmount } from '@sage/xtrem-master-data/build/lib/shared-functions/common';
import type { LineValidationMessage } from '@sage/xtrem-master-data/build/lib/shared-functions/interfaces/finance-integration';
import { RoundedValueGenerator } from '@sage/xtrem-master-data/build/lib/shared-functions/rounded-value-generator';
import type * as ui from '@sage/xtrem-ui';
import type { ExtensionMembers } from '@sage/xtrem-ui/build/lib/service/page-extension';
import { isEqual } from 'lodash';
import type { LandedCostAllocationPanel } from '../pages/landed-cost-allocation-panel';
import type { LandedCostDocumentLinePickList } from '../pages/landed-cost-document-line-pick-list';
import type * as LandedCostInterfaces from './interfaces';

export type QueryAllocationResult = OperationResultType<
    LandedCostDocumentLine$Operations['queries']['queryAllocations']
>;

export async function editLandedCostAllocation(
    pageInstance: ui.Page<GraphApi>,
    args: LandedCostInterfaces.LandedCostAllocationParameters,
    lineValidationFinanceErrors?: LineValidationMessage[],
): Promise<{ modified: boolean; isFullyAllocated?: boolean }> {
    return (
        await pageInstance.$.dialog.page(
            '@sage/xtrem-landed-cost/LandedCostAllocationPanel',
            { args: JSON.stringify({ ...args, lineValidationFinanceErrors: lineValidationFinanceErrors || [] }) },
            {
                resolveOnCancel: true,
                size: 'large',
            },
        )
    ).output;
}

function getDistributionValue(
    landedCostAllocationPanel: ExtensionMembers<LandedCostAllocationPanel>,
    existingLine: ExtractArray<QueryAllocationResult>,
) {
    switch (landedCostAllocationPanel.allocationRule.value) {
        case 'byAmount':
            return existingLine.documentLineAmountInCompanyCurrency;
        case 'byWeight':
            return existingLine.documentLineWeight;
        case 'byVolume':
            return existingLine.documentLineVolume;
        case 'byQuantity':
        default:
            return existingLine.documentLineQuantityInStockUnit;
    }
}

export function mapAllocationsToPage(
    landedCostAllocationPanel: ExtensionMembers<LandedCostAllocationPanel>,
    landedCostAllocations: QueryAllocationResult,
) {
    landedCostAllocations.forEach(existingLine => {
        landedCostAllocationPanel.lines.addOrUpdateRecordValue({
            _id: existingLine._id?.toString() ?? undefined,
            allocatedDocumentType: existingLine.allocatedDocumentType,
            allocatedDocumentLine: {
                _id: existingLine.allocatedDocumentLine._id,
                documentNumber: existingLine.allocatedDocumentLine.documentNumber,
                documentId: Number(existingLine.allocatedDocumentLine.documentId),
            },
            item: {
                name: existingLine.item.name,
                id: existingLine.item.id,
                stockUnit: {
                    id: existingLine.item.stockUnit.id,
                    symbol: existingLine.item.stockUnit.symbol,
                    decimalDigits: Number(existingLine.item.stockUnit.decimalDigits),
                },
            },
            costAmount: existingLine.costAmount,
            costAmountInCompanyCurrency: existingLine.costAmountInCompanyCurrency,
            documentLineAmountInCompanyCurrency: existingLine.documentLineAmountInCompanyCurrency,
            documentLineQuantityInStockUnit: existingLine.documentLineQuantityInStockUnit,
            documentLineWeight: existingLine.documentLineWeight,
            documentLineVolume: existingLine.documentLineVolume,
            distributionValue: getDistributionValue(landedCostAllocationPanel, existingLine),
        });
    });
}

export function getQueryParameters(panelArgs: string | number | boolean) {
    if (panelArgs && typeof panelArgs === 'string') {
        const queryParams = MasterDataUtils.tryParseJSON<
            LandedCostInterfaces.LandedCostAllocationParameters & {
                lineValidationFinanceErrors: LineValidationMessage[];
            }
        >(panelArgs);
        if (
            queryParams &&
            queryParams.company &&
            queryParams.currency &&
            queryParams.site &&
            queryParams.companyCurrency &&
            queryParams.currencyConversion &&
            queryParams.currencyConversion.rate &&
            queryParams.currencyConversion.rateDivisor &&
            queryParams.currencyConversion.currencyDecimals &&
            queryParams.currencyConversion.companyCurrencyDecimals &&
            queryParams.landedCostDocumentLine &&
            queryParams.allocatingDocumentLine
        ) {
            // default the isEditable value to false
            queryParams.isEditable = queryParams.isEditable ?? false;

            return queryParams;
        }
    }
    return null;
}

/**
 * refresh the amounts in company currency
 */
export function updateAllocatedAmountsInCompanyCurrency(
    landedCostAllocationPanel: ExtensionMembers<LandedCostAllocationPanel>,
    panelParameters: LandedCostInterfaces.LandedCostAllocationParameters,
) {
    const max = { value: -1, id: '0' };

    landedCostAllocationPanel.lines.value.forEach(line => {
        const costAmount = Number(line.costAmount ?? 0);
        line.costAmountInCompanyCurrency = convertAmount(
            costAmount,
            panelParameters.currencyConversion.rate,
            panelParameters.currencyConversion.rateDivisor,
            panelParameters.currencyConversion.currencyDecimals,
            panelParameters.currencyConversion.companyCurrencyDecimals,
        ).toString();
        if (max.value < costAmount) {
            max.value = costAmount;
            max.id = line._id;
        }
        landedCostAllocationPanel.lines.addOrUpdateRecordValue(line);
    });
}

/**
 * refresh the totals "allocatedAmount" and "allocatedAmountInCompanyCurrency"
 */
export function updateTotalAllocatedAmount(
    landedCostAllocationPanel: ExtensionMembers<LandedCostAllocationPanel>,
    panelParameters: LandedCostInterfaces.LandedCostAllocationParameters,
) {
    updateAllocatedAmountsInCompanyCurrency(landedCostAllocationPanel, panelParameters);
    const total = landedCostAllocationPanel.lines.value.reduce(
        (totals, line) => {
            totals.allocatedAmount += Number(line.costAmount ?? 0);
            totals.allocatedAmountInCompanyCurrency += Number(line.costAmountInCompanyCurrency ?? 0);
            return totals;
        },
        { allocatedAmount: 0, allocatedAmountInCompanyCurrency: 0 },
    );

    landedCostAllocationPanel.allocatedAmount.value = total.allocatedAmount;
    landedCostAllocationPanel.allocatedAmountInCompanyCurrency.value = total.allocatedAmountInCompanyCurrency;

    // The goal here is to avoid discrepancy due to rounding
    if (total.allocatedAmount === landedCostAllocationPanel.amountToAllocate.value) {
        total.allocatedAmountInCompanyCurrency = landedCostAllocationPanel.amountToAllocateInCompanyCurrency.value ?? 0;
    }
}

/**
 * calculate the total of the 4 possible distribution values (weight, volume, quantity and amount)
 */
export function updateTotalDistributionValue(landedCostAllocationPanel: ExtensionMembers<LandedCostAllocationPanel>) {
    const totals = landedCostAllocationPanel.lines.value.reduce(
        (sum, line) => {
            sum.amount += Number(line.documentLineAmountInCompanyCurrency ?? 0);
            sum.quantity += Number(line.documentLineQuantityInStockUnit ?? 0);
            sum.weight += Number(line.documentLineWeight ?? 0);
            sum.volume += Number(line.documentLineVolume ?? 0);
            return sum;
        },
        { amount: 0, quantity: 0, weight: 0, volume: 0 },
    );

    landedCostAllocationPanel.totalReceiptAmountInCompanyCurrency.value = totals.amount;
    landedCostAllocationPanel.totalQuantityInStockUnit.value = totals.quantity;
    landedCostAllocationPanel.totalWeight.value = totals.weight;
    landedCostAllocationPanel.totalVolume.value = totals.volume;
}

export function clearNestedGrid(landedCostAllocationPanel: ExtensionMembers<LandedCostDocumentLinePickList>) {
    landedCostAllocationPanel.documents.value.forEach(gridLine => {
        landedCostAllocationPanel.documents.removeRecord(gridLine._id, 0);
    });
}

/**
 * calculates the cost amounts in function of distribution values (weight, volume, quantity or amount)
 */
export function calculateCostAmounts(
    landedCostAllocationPanel: ExtensionMembers<LandedCostAllocationPanel>,
    panelParameters: LandedCostInterfaces.LandedCostAllocationParameters,
) {
    updateTotalDistributionValue(landedCostAllocationPanel);
    const roundedValuesGenerator = new RoundedValueGenerator(
        landedCostAllocationPanel.amountToAllocate.value ?? 0,
        landedCostAllocationPanel.lines.value.map(line => Number(line.distributionValue ?? 0)),
        panelParameters.currencyConversion.currencyDecimals,
    );
    const values = roundedValuesGenerator.getValues();
    landedCostAllocationPanel.lines.value.forEach((line, index) => {
        line.costAmount = (values[index] ?? 0).toString();
        landedCostAllocationPanel.lines.setRecordValue(line);
    });
    updateTotalAllocatedAmount(landedCostAllocationPanel, panelParameters);
}

export function isDirtyPage(landedCostAllocationPanel: ExtensionMembers<LandedCostAllocationPanel>) {
    return !isEqual(landedCostAllocationPanel.initialLines.value, landedCostAllocationPanel.lines.value);
}

export function refreshPanel(
    landedCostAllocationPanel: ExtensionMembers<LandedCostAllocationPanel>,
    panelParameters: LandedCostInterfaces.LandedCostAllocationParameters,
) {
    if (landedCostAllocationPanel.allocationMethod.value === 'automatic') {
        calculateCostAmounts(landedCostAllocationPanel, panelParameters);
    } else {
        updateTotalAllocatedAmount(landedCostAllocationPanel, panelParameters);
        updateTotalDistributionValue(landedCostAllocationPanel);
    }
    landedCostAllocationPanel.manageDisplayButtonAllocateLandedCostAction();
    landedCostAllocationPanel.save.isDisabled = !isDirtyPage(landedCostAllocationPanel);
}

export async function openLandedCostDocumentLinePickList(
    landedCostAllocationPanel: ExtensionMembers<LandedCostAllocationPanel>,
    panelParameters: LandedCostInterfaces.LandedCostAllocationParameters,
    documentType: string,
) {
    const lineIds = landedCostAllocationPanel.lines.value
        .filter(line => line.allocatedDocumentType === documentType)
        .map(line => line.allocatedDocumentLine?._id ?? '');

    const changedLines: QueryAllocationResult = await landedCostAllocationPanel.$.dialog.page(
        '@sage/xtrem-landed-cost/LandedCostDocumentLinePickList',
        {
            companyId: landedCostAllocationPanel.company.value?._id ?? '',
            siteId: landedCostAllocationPanel.site.value?._id ?? '',
            alreadyPickedLineIds: JSON.stringify(lineIds),
            allocationRule: landedCostAllocationPanel.allocationRule.value ?? '',
            allocationRuleUnitId: landedCostAllocationPanel.allocationRuleUnit.value?._id ?? '',
            documentType,
        },
        {
            resolveOnCancel: true,
            size: 'large',
        },
    );
    if (changedLines?.length) {
        mapAllocationsToPage(landedCostAllocationPanel, changedLines);
        refreshPanel(landedCostAllocationPanel, panelParameters);
    }
}
