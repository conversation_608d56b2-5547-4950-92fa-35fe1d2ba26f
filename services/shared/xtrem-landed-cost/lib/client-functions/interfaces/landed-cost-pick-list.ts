import type { integer } from '@sage/xtrem-client';

export interface Unit {
    _id: string;
    id: string;
    symbol: string;
    decimalDigits: integer;
}

export interface Currency {
    id: string;
    symbol: string;
    decimalDigits: integer;
}

export interface DocumentLineNestedGrid {
    _id: string;
    _sortValue: string;
    item: {
        id: string;
        name: string;
        weight: string;
        volume: string;
        stockUnit: Unit;
        weightUnit: Unit;
        volumeUnit: Unit;
    };
    quantityInStockUnit: string;
    amountExcludingTax: string;
    currency: Currency;
    document: {
        _id: string;
        number: string;
    };
}

export interface DocumentNestedGrid {
    _id: string;
    number: string;
    totalAmountExcludingTax: string;
    currency: Currency;
    lines: DocumentLineNestedGrid;
}
