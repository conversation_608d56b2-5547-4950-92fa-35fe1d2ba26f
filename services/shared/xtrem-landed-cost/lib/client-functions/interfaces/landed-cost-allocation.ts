import type { ClientNode, decimal, integer } from '@sage/xtrem-client';
import type { LandedCostAllocationBinding, LandedCostDocumentLineBinding } from '@sage/xtrem-landed-cost-api';
import type { BaseDocumentLine, BaseDocumentLineBinding, CurrencyBinding, Item } from '@sage/xtrem-master-data-api';
import type { CompanyBinding, SiteBinding } from '@sage/xtrem-system-api';

export interface LandedCostAllocationParameters {
    isEditable: boolean;
    company: CompanyBinding['_id'];
    site: SiteBinding['_id'];
    currency: CurrencyBinding['_id'];
    companyCurrency: CurrencyBinding['_id'];
    currencyConversion: {
        rate: decimal;
        rateDivisor: decimal;
        currencyDecimals: integer;
        companyCurrencyDecimals: integer;
    };
    landedCostDocumentLine: LandedCostDocumentLineBinding['_id'];
    allocatingDocumentLine: BaseDocumentLineBinding['_id'];
}

export type LandedCostAllocationOutput = {
    _id: string;
} & (
    | { _action: 'delete' }
    | {
          _action: 'create' | 'update';
          allocatedDocumentLine: BaseDocumentLine['_id'];
          allocatedAmount: decimal;
          allocatedAmountInCompanyCurrency: decimal;
      }
);

export interface LandedCostAllocationPageBinding extends ClientNode {
    allocatedDocumentType: string;
    allocatedDocumentLine: {
        _id: BaseDocumentLine['_id'];
        documentNumber: BaseDocumentLine['documentNumber'];
        documentId: BaseDocumentLine['documentId'];
    };
    item: Item;
    costAmount: LandedCostAllocationBinding['costAmount'];
    costAmountInCompanyCurrency: LandedCostAllocationBinding['costAmountInCompanyCurrency'];
    documentLineAmountInCompanyCurrency: LandedCostAllocationBinding['documentLineAmountInCompanyCurrency'];
    documentLineQuantityInStockUnit: LandedCostAllocationBinding['documentLineQuantityInStockUnit'];
    documentLineWeight: LandedCostAllocationBinding['documentLineWeight'];
    documentLineVolume: LandedCostAllocationBinding['documentLineVolume'];
    distributionValue: string;
}

export interface DocumentPage {
    id: string;
    name: string;
}
