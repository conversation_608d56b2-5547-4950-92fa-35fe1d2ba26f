import type { ClientNode, decimal, integer } from '@sage/xtrem-client';
import type { LandedCostAllocation, LandedCostLineBinding, LandedCostType } from '@sage/xtrem-landed-cost-api';
import type { BaseDocumentLine, CurrencyBinding, Item } from '@sage/xtrem-master-data-api';

export interface LandedCostSummaryParameters {
    companyCurrency: CurrencyBinding['_id'];
    documentLineId: LandedCostLineBinding['documentLine']['_id'];
    hideAllocatedAmountColumn?: true;
}

// Interface used for the result of the query on LandedCostLine
export interface LandedCostLine {
    _id: string;
    landedCost: { name: Item['name'] };
    landedCostAllocation: {
        line: {
            allocatedDocumentType: LandedCostAllocation['allocatedDocumentType'];
            documentLine: {
                documentNumber: BaseDocumentLine['documentNumber'];
                documentId: BaseDocumentLine['documentId'];
            };
        };
    };
    actualCostAmountInCompanyCurrency: decimal;
    actualAllocatedCostAmountInCompanyCurrency?: decimal;
}

export interface LandedCostLinePageBinding extends ClientNode {
    landedCost: { name: string };
    landedCostAllocation: {
        line: { documentLine: { documentNumber: string; _constructor: string; documentId: integer } };
    };
    actualCostAmountInCompanyCurrency: decimal;
    actualAllocatedCostAmountInCompanyCurrency?: decimal;
}

// Same structure as the type JsonAggregateLandedCostTypes used in Purchase Order
export interface LandedCostTypeSummaryLine {
    landedCostType: string;
    actualCostAmountInCompanyCurrency: string;
    actualAllocatedCostAmountInCompanyCurrency: string;
}

export interface LandedCostTypeSummaryLineBinding extends ClientNode {
    landedCostType: LandedCostType;
    actualCostAmountInCompanyCurrency: decimal;
    actualAllocatedCostAmountInCompanyCurrency: decimal;
}
