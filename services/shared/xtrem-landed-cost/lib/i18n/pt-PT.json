{"@sage/xtrem-landed-cost/data_types__allocation_rule_enum__name": "", "@sage/xtrem-landed-cost/data_types__landed_cost_allocation_method_enum__name": "", "@sage/xtrem-landed-cost/data_types__landed_cost_allocation_update_action_enum__name": "", "@sage/xtrem-landed-cost/data_types__landed_cost_document_type_enum__name": "", "@sage/xtrem-landed-cost/data_types__landed_cost_type_enum__name": "", "@sage/xtrem-landed-cost/document_type_name_purchase_invoice": "", "@sage/xtrem-landed-cost/document_type_name_purchase_order": "", "@sage/xtrem-landed-cost/document_type_name_purchase_receipt": "", "@sage/xtrem-landed-cost/document_type_name_stock_transfer_receipt": "", "@sage/xtrem-landed-cost/enums__allocation_rule__byAmount": "", "@sage/xtrem-landed-cost/enums__allocation_rule__byQuantity": "", "@sage/xtrem-landed-cost/enums__allocation_rule__byVolume": "", "@sage/xtrem-landed-cost/enums__allocation_rule__byWeight": "", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_method__automatic": "", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_method__manual": "", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__create": "", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__delete": "", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__update": "", "@sage/xtrem-landed-cost/enums__landed_cost_document_type__order": "", "@sage/xtrem-landed-cost/enums__landed_cost_document_type__transaction": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__administration": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__crating": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__currencyConversion": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__customs": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__demurrage": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__duty": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__freight": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__handling": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__insurance": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__other": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__portCharges": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__processing": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__storage": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__tariffs": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__taxes": "", "@sage/xtrem-landed-cost/enums__landed_cost_type__transportation": "", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostAllocationRule": "", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostAllocationRuleUnit": "", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostItem": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__item_must_be_stock_managed": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__node_name": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__only_credit_purchase_invoice_landed_cost": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__allocatedDocumentLine": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__allocatedDocumentType": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__costAmount": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__costAmountInCompanyCurrency": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__creditedCostAmount": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__creditedCostAmountInCompanyCurrency": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineAmountInCompanyCurrency": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineQuantityInStockUnit": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineVolume": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineWeight": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__item": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__landedCostItem": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__line": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__sourceAllocationLine": "", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__wrong_document_type": "", "@sage/xtrem-landed-cost/nodes__landed_cost_base_document__node_name": "", "@sage/xtrem-landed-cost/nodes__landed_cost_base_document__property__number": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__inconsistency_id_action": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__inconsistency_id_document_line": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__item_must_be_landed_cost": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations__failed": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations__parameter__allocationData": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__node_name": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationMethod": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationRule": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationRuleUnit": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocations": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__costAmountToAllocate": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__costAmountToAllocateInCompanyCurrency": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__documentLine": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__totalAmountAllocated": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations__failed": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations__parameter__landedCostDocumentLineId": "", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__sum_of_details_cannot_exceed_amount_to_allocate": "", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport": "", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-landed-cost/nodes__landed_cost_item__node_name": "", "@sage/xtrem-landed-cost/nodes__landed_cost_item__not-a-volume-measure": "", "@sage/xtrem-landed-cost/nodes__landed_cost_item__not-a-weight-measure": "", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__allocationRule": "", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__allocationRuleUnit": "", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__item": "", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__landedCostType": "", "@sage/xtrem-landed-cost/nodes__landed_cost_line__allocation_and_document_line_not_consistent": "", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport": "", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-landed-cost/nodes__landed_cost_line__node_name": "", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__actualAllocatedCostAmountInCompanyCurrency": "", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__actualCostAmountInCompanyCurrency": "", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__companyCurrency": "", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__documentLine": "", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__landedCost": "", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__landedCostAllocation": "", "@sage/xtrem-landed-cost/package__name": "", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__allocationRule__title": "", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__allocationRuleUnit__title": "", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__type__title": "", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRule____title": "", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____columns__title__name": "", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____columns__title__symbol": "", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____title": "", "@sage/xtrem-landed-cost/page-extensions__item_extension__landedCostBlock____title": "", "@sage/xtrem-landed-cost/page-extensions__item_extension__landedCostType____title": "", "@sage/xtrem-landed-cost/pages__landed_cost__unknown_document_line_type": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocatedAmount____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocatedAmountInCompanyCurrency____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocateLandedCost____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationMethod____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationRule____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationRuleUnit____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__amountToAllocate____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__amountToAllocateInCompanyCurrency____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__cancel____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__confirm": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__insufficient_allocations": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title___id": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__allocatedDocumentType": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__costAmount": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__costAmountInCompanyCurrency": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineAmountInCompanyCurrency": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineQuantityInStockUnit": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineVolume": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineWeight": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____dropdownActions__title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__mainBlock____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__mainSection____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__save____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__total_allocated_lower_than_amount_to_allocate": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalQuantityInStockUnit____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalReceiptAmountInCompanyCurrency____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalVolume____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalWeight____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__cancel____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__confirm____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__amountExcludingTax": "", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__item__name": "", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__number": "", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__totalAmountExcludingTax": "", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____columns__title__category__name": "", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____lookupDialogTitle": "", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__lineBlock____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__resultsBlock____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_summary____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_summary__actualLandedCostInCompanyCurrency____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_summary__cancel____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title___id": "", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__actualAllocatedCostAmountInCompanyCurrency": "", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__actualCostAmountInCompanyCurrency": "", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__landedCost__name": "", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_summary__mainBlock____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_summary__mainSection____title": ""}