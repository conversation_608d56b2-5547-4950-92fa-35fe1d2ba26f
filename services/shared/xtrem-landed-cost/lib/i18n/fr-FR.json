{"@sage/xtrem-landed-cost/data_types__allocation_rule_enum__name": "Enum d'unité de règle d'allocation", "@sage/xtrem-landed-cost/data_types__landed_cost_allocation_method_enum__name": "Enum méthode d'allocation frais d'approche", "@sage/xtrem-landed-cost/data_types__landed_cost_allocation_update_action_enum__name": "Enum d'action de mise à jour d'allocation de frais d'approche", "@sage/xtrem-landed-cost/data_types__landed_cost_document_type_enum__name": "Enum type document frais d'approche", "@sage/xtrem-landed-cost/data_types__landed_cost_type_enum__name": "Enum type de frais d'approche", "@sage/xtrem-landed-cost/document_type_name_purchase_invoice": "Facture d'achat", "@sage/xtrem-landed-cost/document_type_name_purchase_order": "Commande d'achat", "@sage/xtrem-landed-cost/document_type_name_purchase_receipt": "<PERSON><PERSON><PERSON> d'<PERSON>", "@sage/xtrem-landed-cost/document_type_name_stock_transfer_receipt": "Réception de transfert de stock", "@sage/xtrem-landed-cost/enums__allocation_rule__byAmount": "Par montant", "@sage/xtrem-landed-cost/enums__allocation_rule__byQuantity": "Par quantité", "@sage/xtrem-landed-cost/enums__allocation_rule__byVolume": "Par volume", "@sage/xtrem-landed-cost/enums__allocation_rule__byWeight": "Par poids", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_method__automatic": "Automatique", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_method__manual": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__update": "Mettre à jour", "@sage/xtrem-landed-cost/enums__landed_cost_document_type__order": "Commande", "@sage/xtrem-landed-cost/enums__landed_cost_document_type__transaction": "Transaction", "@sage/xtrem-landed-cost/enums__landed_cost_type__administration": "Gestion", "@sage/xtrem-landed-cost/enums__landed_cost_type__crating": "Emballage", "@sage/xtrem-landed-cost/enums__landed_cost_type__currencyConversion": "Conversion de devise", "@sage/xtrem-landed-cost/enums__landed_cost_type__customs": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_type__demurrage": "Surestaries", "@sage/xtrem-landed-cost/enums__landed_cost_type__duty": "Droits d’importation", "@sage/xtrem-landed-cost/enums__landed_cost_type__freight": "Fret", "@sage/xtrem-landed-cost/enums__landed_cost_type__handling": "Man<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_type__insurance": "Assurance", "@sage/xtrem-landed-cost/enums__landed_cost_type__other": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_type__portCharges": "Frais portuaires", "@sage/xtrem-landed-cost/enums__landed_cost_type__processing": "Opérations diverses", "@sage/xtrem-landed-cost/enums__landed_cost_type__storage": "Stockage", "@sage/xtrem-landed-cost/enums__landed_cost_type__tariffs": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_type__taxes": "Taxes", "@sage/xtrem-landed-cost/enums__landed_cost_type__transportation": "Transport", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostAllocationRule": "Règle d'allocation frais d'approche", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostAllocationRuleUnit": "Unité de règle d'allocation frais d'approche", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostItem": "Article de type frais d'approche", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__item_must_be_stock_managed": "L'article {{item}} n'est pas géré en stock. Vous ne pouvez pas l'allouer à un frais d'approche.", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__node_name": "Allocation de frais d'approche", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__only_credit_purchase_invoice_landed_cost": "Vous pouvez uniquement créditer des enregistrements de frais d'approche de factures d'achat.", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__allocatedDocumentLine": "Ligne de document alloué", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__allocatedDocumentType": "Type de document alloué", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__costAmount": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__costAmountInCompanyCurrency": "<PERSON>ant de coût en devise société", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__creditedCostAmount": "<PERSON><PERSON> coût crédité", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__creditedCostAmountInCompanyCurrency": "Montant du coût crédité en devise société", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineAmountInCompanyCurrency": "Montant de ligne de document en devise société", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineQuantityInStockUnit": "Quantité de ligne de document en unité de stock", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineVolume": "Volume de ligne de document", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineWeight": "Poids de ligne de document", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__item": "Article", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__landedCostItem": "Article de type frais d'approche", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__line": "Ligne", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__sourceAllocationLine": "Ligne d'allocation d'origine", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__wrong_document_type": "Vous ne pouvez pas allouer des frais d'approche à ce type de document.", "@sage/xtrem-landed-cost/nodes__landed_cost_base_document__node_name": "Document de base des frais d'approche", "@sage/xtrem-landed-cost/nodes__landed_cost_base_document__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__inconsistency_id_action": "Incohérence détectée entre l'allocation ({{allocationId}}) et l'action demandée ({{action}})", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__inconsistency_id_document_line": "L'allocation ({{allocationId}}) n'existe pas pour la ligne de document {{lineId}}.", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__item_must_be_landed_cost": "Cette ligne de document ne peut pas avoir de propriétés de frais d'approche car l'article {{item}} n'est pas de type frais d'approche.", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations": "Mettre à jour les allocations", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations__failed": "Échec de création des emplacements en masse.", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations__parameter__allocationData": "Données d'allocation", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__node_name": "Ligne de document de frais d'approche", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationMethod": "Mode d'allocation", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationRule": "Règle d'allocation", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationRuleUnit": "Unité de règle d'allocation", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocations": "Allocations", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__costAmountToAllocate": "<PERSON><PERSON> coût à allouer", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__costAmountToAllocateInCompanyCurrency": "<PERSON><PERSON> coût à allouer en devise société", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__documentLine": "Ligne de document", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__landedCostAllocationMethod": "Landed cost allocation method", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__landedCostAllocationRule": "Landed cost allocation rule", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__landedCostAllocationRuleUnit": "Landed cost allocation rule unit", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__totalAmountAllocated": "Montant total alloué", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations": "Requête allocations", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations__failed": "Échec de la requête d'allocations", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations__parameter__landedCostDocumentLineId": "Code de ligne de document de frais d'approche", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__sum_of_details_cannot_exceed_amount_to_allocate": "Les montants alloués totaux ({{amountAllocated}}) ne peuvent pas dépasser le montant à allouer {{amountToAllocate}}.", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-landed-cost/nodes__landed_cost_item__node_name": "Article de type frais d'approche", "@sage/xtrem-landed-cost/nodes__landed_cost_item__not-a-volume-measure": "L'unité {{unitOfMeasure}} n'est pas une unité de volume.", "@sage/xtrem-landed-cost/nodes__landed_cost_item__not-a-weight-measure": "L'unité {{unitOfMeasure}} n'est pas une unité de poids.", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__allocationRule": "Règle d'allocation", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__allocationRuleUnit": "Unité de règle d'allocation", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__item": "Article", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__landedCostType": "Type de frais d'approche", "@sage/xtrem-landed-cost/nodes__landed_cost_line__allocation_and_document_line_not_consistent": "L'allocation de frais d'approche n'est pas cohérente avec la ligne de document.", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-landed-cost/nodes__landed_cost_line__node_name": "Ligne de frais d'approche", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__actualAllocatedCostAmountInCompanyCurrency": "Montant du coût alloué réel en devise société", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__actualCostAmountAllocatedInCompanyCurrency": "<PERSON><PERSON> coût réel alloué en devise société", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__actualCostAmountInCompanyCurrency": "<PERSON><PERSON> du coût réel en devise société", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__companyCurrency": "<PERSON><PERSON> socié<PERSON>", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__documentLine": "Ligne de document", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__landedCost": "Frais d'approche", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__landedCostAllocation": "Allocation de frais d'approche", "@sage/xtrem-landed-cost/package__name": "Frais d'approche Sage SDMO", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__allocationRule__title": "Règle d'allocation des frais d'approche", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__allocationRuleUnit__title": "Unité d'allocation des frais d'approche", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__type__title": "Type de frais d'approche", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRule____title": "Règle d'allocation", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____columns__title__name": "Nom", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____columns__title__symbol": "Symbole", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____title": "Unité d'allocation", "@sage/xtrem-landed-cost/page-extensions__item_extension__landedCostBlock____title": "Frais d'approche", "@sage/xtrem-landed-cost/page-extensions__item_extension__landedCostType____title": "Type", "@sage/xtrem-landed-cost/pages__landed_cost__unknown_document_line_type": "Le type {{constructor<PERSON>ame}} n'est pas géré.", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel____title": "Allocation de frais d'approche", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocatedAmount____title": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocatedAmountInCompanyCurrency____title": "<PERSON><PERSON> alloué en devise société", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocateLandedCost____title": "Allouer frais d'approche", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationMethod____title": "Mode d'allocation", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationRule____title": "Règle d'allocation", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationRuleUnit____title": "Unité de règle d'allocation", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__amountToAllocate____title": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__amountToAllocateInCompanyCurrency____title": "<PERSON><PERSON> <PERSON> allouer en devise société", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__cancel____title": "Annuler", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__confirm": "Confirmer", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__insufficient_allocations": "<PERSON><PERSON> alloué insuffisant", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title___id": "N° doc", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__allocatedDocumentType": "Type doc", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__costAmount": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__costAmountInCompanyCurrency": "<PERSON><PERSON> alloué en devise société", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineAmountInCompanyCurrency": "Montant en devise société", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineQuantityInStockUnit": "Qté US", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineVolume": "Volume", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineWeight": "Poids", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentType": "Type doc", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____title": "Documents alloués", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__mainBlock____title": "Général", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__mainSection____title": "Général", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__save____title": "Enregistrer", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__total_allocated_lower_than_amount_to_allocate": "Le montant total alloué est inférieur au montant de frais d'approche à allouer.", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalQuantityInStockUnit____title": "Quantité totale", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalReceiptAmountInCompanyCurrency____title": "Montant de réception total en devise société", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalVolume____title": "Volume total", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalWeight____title": "Poids total", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list____title": "Ajouter des lignes", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__cancel____title": "Annuler", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__confirm____title": "Ajouter", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__amountExcludingTax": "Montant HT ligne", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__item__name": "Nom article", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__lineAmountExcludingTax": "Montant HT ligne", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__number": "N°", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__totalAmountExcludingTax": "Montant total hors taxe", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____title": "Article", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__lineBlock____title": "Critères", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__resultsBlock____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_summary____title": "Frais d'approche", "@sage/xtrem-landed-cost/pages__landed_cost_summary__actualLandedCostInCompanyCurrency____title": "Frais d'approche réels", "@sage/xtrem-landed-cost/pages__landed_cost_summary__cancel____title": "Annuler", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title___id": "N° doc", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__actualAllocatedCostAmountInCompanyCurrency": "<PERSON><PERSON> de coût alloué réel", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__actualCostAmountAllocatedInCompanyCurrency": "<PERSON><PERSON> de coût alloué réel", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__actualCostAmountInCompanyCurrency": "<PERSON><PERSON> coût réel", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__landedCost__name": "Frais d'approche", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____title": "Fais d'approche en devise société", "@sage/xtrem-landed-cost/pages__landed_cost_summary__mainBlock____title": "Total en devise société", "@sage/xtrem-landed-cost/pages__landed_cost_summary__mainSection____title": "Général"}