{"@sage/xtrem-landed-cost/data_types__allocation_rule_enum__name": "Allocation rule enum", "@sage/xtrem-landed-cost/data_types__landed_cost_allocation_method_enum__name": "Landed cost allocation method enum", "@sage/xtrem-landed-cost/data_types__landed_cost_allocation_update_action_enum__name": "Landed cost allocation update action enum", "@sage/xtrem-landed-cost/data_types__landed_cost_document_type_enum__name": "Landed cost document type enum", "@sage/xtrem-landed-cost/data_types__landed_cost_type_enum__name": "Landed cost type enum", "@sage/xtrem-landed-cost/document_type_name_purchase_invoice": "Purchase invoice", "@sage/xtrem-landed-cost/document_type_name_purchase_order": "Purchase order", "@sage/xtrem-landed-cost/document_type_name_purchase_receipt": "Purchase receipt", "@sage/xtrem-landed-cost/document_type_name_stock_transfer_receipt": "Stock transfer receipt", "@sage/xtrem-landed-cost/enums__allocation_rule__byAmount": "By amount", "@sage/xtrem-landed-cost/enums__allocation_rule__byQuantity": "By quantity", "@sage/xtrem-landed-cost/enums__allocation_rule__byVolume": "By volume", "@sage/xtrem-landed-cost/enums__allocation_rule__byWeight": "By weight", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_method__automatic": "Automatic", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_method__manual": "Manual", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__create": "Create", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__delete": "Delete", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__update": "Update", "@sage/xtrem-landed-cost/enums__landed_cost_document_type__order": "Order", "@sage/xtrem-landed-cost/enums__landed_cost_document_type__transaction": "Transaction", "@sage/xtrem-landed-cost/enums__landed_cost_type__administration": "Administration", "@sage/xtrem-landed-cost/enums__landed_cost_type__crating": "Crating", "@sage/xtrem-landed-cost/enums__landed_cost_type__currencyConversion": "Currency conversion", "@sage/xtrem-landed-cost/enums__landed_cost_type__customs": "Customs", "@sage/xtrem-landed-cost/enums__landed_cost_type__demurrage": "Demurrage", "@sage/xtrem-landed-cost/enums__landed_cost_type__duty": "Duty", "@sage/xtrem-landed-cost/enums__landed_cost_type__freight": "Freight", "@sage/xtrem-landed-cost/enums__landed_cost_type__handling": "Handling", "@sage/xtrem-landed-cost/enums__landed_cost_type__insurance": "Insurance", "@sage/xtrem-landed-cost/enums__landed_cost_type__other": "Other", "@sage/xtrem-landed-cost/enums__landed_cost_type__portCharges": "Port charges", "@sage/xtrem-landed-cost/enums__landed_cost_type__processing": "Processing", "@sage/xtrem-landed-cost/enums__landed_cost_type__storage": "Storage", "@sage/xtrem-landed-cost/enums__landed_cost_type__tariffs": "Tariffs", "@sage/xtrem-landed-cost/enums__landed_cost_type__taxes": "Taxes", "@sage/xtrem-landed-cost/enums__landed_cost_type__transportation": "Transportation", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostAllocationRule": "Landed cost allocation rule", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostAllocationRuleUnit": "Landed cost allocation rule unit", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostItem": "Landed cost item", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport": "Export", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__item_must_be_stock_managed": "The item {{item}} is not stock managed. You cannot allocate it to a landed cost.", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__node_name": "Landed cost allocation", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__only_credit_purchase_invoice_landed_cost": "You can only credit purchase invoice landed cost records.", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__allocatedDocumentLine": "Allocated document line", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__allocatedDocumentType": "Allocated document type", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__costAmount": "Cost amount", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__costAmountInCompanyCurrency": "Cost amount in company currency", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__creditedCostAmount": "Credited cost amount", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__creditedCostAmountInCompanyCurrency": "Credited cost amount in company currency", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineAmountInCompanyCurrency": "Document line amount in company currency", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineQuantityInStockUnit": "Document line quantity in stock unit", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineVolume": "Document line volume", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineWeight": "Document line weight", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__item": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__landedCostItem": "Landed cost item", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__line": "Line", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__sourceAllocationLine": "Source allocation line", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__wrong_document_type": "You cannot allocate landed costs to this document type.", "@sage/xtrem-landed-cost/nodes__landed_cost_base_document__node_name": "Landed cost base document", "@sage/xtrem-landed-cost/nodes__landed_cost_base_document__property__number": "Number", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__inconsistency_id_action": "Inconsistency detected between the allocation ({{allocationId}}) and the requested action ({{action}})", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__inconsistency_id_document_line": "The allocation ({{allocationId}}) does not exist for the document line {{lineId}}.", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__item_must_be_landed_cost": "This document line cannot have landed cost properties because the {{item}} item is not a landed cost.", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations": "Update allocations", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations__failed": "Update allocations failed.", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations__parameter__allocationData": "Allocation data", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__node_name": "Landed cost document line", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationMethod": "Allocation method", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationRule": "Allocation rule", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationRuleUnit": "Allocation rule unit", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocations": "Allocations", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__costAmountToAllocate": "Cost amount to allocate", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__costAmountToAllocateInCompanyCurrency": "Cost amount to allocate in company currency", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__documentLine": "Document line", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__landedCostAllocationMethod": "Landed cost allocation method", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__landedCostAllocationRule": "Landed cost allocation rule", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__landedCostAllocationRuleUnit": "Landed cost allocation rule unit", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__totalAmountAllocated": "Total amount allocated", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations": "Query allocations", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations__failed": "Query allocations failed.", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations__parameter__landedCostDocumentLineId": "Landed cost document line ID", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__sum_of_details_cannot_exceed_amount_to_allocate": "The total of the allocated amounts ({{amountAllocated}}) cannot exceed the amount to allocate {{amountToAllocate}}.", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport": "Export", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-landed-cost/nodes__landed_cost_item__node_name": "Landed cost item", "@sage/xtrem-landed-cost/nodes__landed_cost_item__not-a-volume-measure": "The {{unitOfMeasure}} unit is not a unit of volume.", "@sage/xtrem-landed-cost/nodes__landed_cost_item__not-a-weight-measure": "The {{unitOfMeasure}} unit is not a unit of weight.", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__allocationRule": "Allocation rule", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__allocationRuleUnit": "Allocation rule unit", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__item": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__landedCostType": "Landed cost type", "@sage/xtrem-landed-cost/nodes__landed_cost_line__allocation_and_document_line_not_consistent": "The landed cost allocation is not consistent with the document line.", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-landed-cost/nodes__landed_cost_line__node_name": "Landed cost line", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__actualAllocatedCostAmountInCompanyCurrency": "Actual allocated cost amount in company currency", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__actualCostAmountAllocatedInCompanyCurrency": "Actual cost amount allocated in company currency", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__actualCostAmountInCompanyCurrency": "Actual cost amount in company currency", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__companyCurrency": "Company currency", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__documentLine": "Document line", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__landedCost": "Landed cost", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__landedCostAllocation": "Landed cost allocation", "@sage/xtrem-landed-cost/package__name": "Sage DMO landed cost", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__allocationRule__title": "Landed cost allocation rule", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__allocationRuleUnit__title": "Landed cost allocation unit", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__type__title": "Landed cost type", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRule____title": "Allocation rule", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____columns__title__name": "Name", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____columns__title__symbol": "Symbol", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____title": "Allocation unit", "@sage/xtrem-landed-cost/page-extensions__item_extension__landedCostBlock____title": "Landed costs", "@sage/xtrem-landed-cost/page-extensions__item_extension__landedCostType____title": "Type", "@sage/xtrem-landed-cost/pages__landed_cost__unknown_document_line_type": "The type {{constructorName}} is not managed.", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel____title": "Landed cost allocation", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocatedAmount____title": "Allocated amount", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocatedAmountInCompanyCurrency____title": "Allocated amount in company currency", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocateLandedCost____title": "Allocate landed cost", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationMethod____title": "Allocation method", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationRule____title": "Allocation rule", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationRuleUnit____title": "Allocation rule unit", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__amountToAllocate____title": "Amount to allocate", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__amountToAllocateInCompanyCurrency____title": "Amount to allocate in company currency", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__cancel____title": "Cancel", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__confirm": "Confirm", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__insufficient_allocations": "Allocated amount insufficient", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title___id": "Document number", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__allocatedDocumentType": "Document type", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__costAmount": "Allocated amount", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__costAmountInCompanyCurrency": "Allocated amount in company currency", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineAmountInCompanyCurrency": "Amount in company currency", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineQuantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineVolume": "Volume", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineWeight": "Weight", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentType": "Document type", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____dropdownActions__title": "Delete", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____title": "Allocated documents", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__mainBlock____title": "General", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__mainSection____title": "General", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__save____title": "Save", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__total_allocated_lower_than_amount_to_allocate": "The total allocated amount is lower than the landed cost amount to allocate.", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalQuantityInStockUnit____title": "Total quantity", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalReceiptAmountInCompanyCurrency____title": "Total receipt amount in company currency", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalVolume____title": "Total volume", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalWeight____title": "Total weight", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list____title": "Add lines", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__cancel____title": "Cancel", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__confirm____title": "Add", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__amountExcludingTax": "Line VAT excluded amount", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__item__name": "Item name", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__lineAmountExcludingTax": "Line tax excluded amount", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__number": "Number", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__totalAmountExcludingTax": "Total tax excluded amount", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____columns__title__category__name": "Category", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____lookupDialogTitle": "Select item", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____title": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__lineBlock____title": "Criteria", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__resultsBlock____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_summary____title": "Landed costs", "@sage/xtrem-landed-cost/pages__landed_cost_summary__actualLandedCostInCompanyCurrency____title": "Actual landed costs", "@sage/xtrem-landed-cost/pages__landed_cost_summary__cancel____title": "Cancel", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title___id": "Document number", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__actualAllocatedCostAmountInCompanyCurrency": "Actual allocated cost amount", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__actualCostAmountAllocatedInCompanyCurrency": "Actual allocated cost amount", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__actualCostAmountInCompanyCurrency": "Actual cost amount", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__landedCost__name": "Landed cost", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____title": "Landed costs in company currency", "@sage/xtrem-landed-cost/pages__landed_cost_summary__mainBlock____title": "Total in company currency", "@sage/xtrem-landed-cost/pages__landed_cost_summary__mainSection____title": "General"}