{"@sage/xtrem-landed-cost/data_types__allocation_rule_enum__name": "Enum Zuteilungsregel", "@sage/xtrem-landed-cost/data_types__landed_cost_allocation_method_enum__name": "Enum Methode Zuteilung Einstandskosten", "@sage/xtrem-landed-cost/data_types__landed_cost_allocation_update_action_enum__name": "Enum Aktion Aktualisierung Zuteilung Einstandskosten", "@sage/xtrem-landed-cost/data_types__landed_cost_document_type_enum__name": "Enum Dokumenttyp Einstandskosten", "@sage/xtrem-landed-cost/data_types__landed_cost_type_enum__name": "Enum Typ Einstandskosten", "@sage/xtrem-landed-cost/document_type_name_purchase_invoice": "Einkaufsrechnung", "@sage/xtrem-landed-cost/document_type_name_purchase_order": "Bestellung", "@sage/xtrem-landed-cost/document_type_name_purchase_receipt": "Wareneingang", "@sage/xtrem-landed-cost/document_type_name_stock_transfer_receipt": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__allocation_rule__byAmount": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__allocation_rule__byQuantity": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__allocation_rule__byVolume": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__allocation_rule__byWeight": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_method__automatic": "Automatisch", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_method__manual": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__create": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__delete": "Löschen", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__update": "Aktualisieren", "@sage/xtrem-landed-cost/enums__landed_cost_document_type__order": "Auftrag", "@sage/xtrem-landed-cost/enums__landed_cost_document_type__transaction": "Transaktion", "@sage/xtrem-landed-cost/enums__landed_cost_type__administration": "Verwaltung", "@sage/xtrem-landed-cost/enums__landed_cost_type__crating": "Verpackung", "@sage/xtrem-landed-cost/enums__landed_cost_type__currencyConversion": "Währungsumrechnung", "@sage/xtrem-landed-cost/enums__landed_cost_type__customs": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_type__demurrage": "Liegekosten", "@sage/xtrem-landed-cost/enums__landed_cost_type__duty": "Abgaben", "@sage/xtrem-landed-cost/enums__landed_cost_type__freight": "Fracht", "@sage/xtrem-landed-cost/enums__landed_cost_type__handling": "Handling", "@sage/xtrem-landed-cost/enums__landed_cost_type__insurance": "Versicherung", "@sage/xtrem-landed-cost/enums__landed_cost_type__other": "Sonstige", "@sage/xtrem-landed-cost/enums__landed_cost_type__portCharges": "Hafengebühren", "@sage/xtrem-landed-cost/enums__landed_cost_type__processing": "Bearbeitung", "@sage/xtrem-landed-cost/enums__landed_cost_type__storage": "Lagerkosten", "@sage/xtrem-landed-cost/enums__landed_cost_type__tariffs": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_type__taxes": "Steuern", "@sage/xtrem-landed-cost/enums__landed_cost_type__transportation": "Transportkosten", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostAllocationRule": "Zuteilungsregel Einstandskosten", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostAllocationRuleUnit": "Einheit Zuteilungsregel Einstandskosten", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostItem": "Einstandskostenartikel", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport": "Export", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__item_must_be_stock_managed": "Der Artikel {{item}} wird nicht bestandsgeführt. Sie können ihm keine Einstandskosten zuteilen.", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__node_name": "Zuteilung Einstandskosten", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__only_credit_purchase_invoice_landed_cost": "Sie können nur Einstandskostendatensätze zu Bestellungen gutschreiben.", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__allocatedDocumentLine": "Zugeteilte Dokumentzeile", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__allocatedDocumentType": "Zugeteilter <PERSON>", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__costAmount": "Kosenbetrag", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__costAmountInCompanyCurrency": "Kostenbetrag in Unternehmenswährung", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__creditedCostAmount": "Gutgeschriebener Kostenbetrag", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__creditedCostAmountInCompanyCurrency": "Gutgeschriebener Kostenbetrag in Unternehmenswährung", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineAmountInCompanyCurrency": "Dokumentzeilenbetrag in Unternehmenswährung", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineQuantityInStockUnit": "Dokumentzeilenmenge in Lagereinheit", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineVolume": "Volumen Dokumentzeile", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineWeight": "Gewicht Dokumentzeile", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__item": "Artikel", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__landedCostItem": "Einstandskostenartikel", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__line": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__sourceAllocationLine": "Zeile Zuteilung Ursprung", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__wrong_document_type": "Sie können diesem Dokumenttyp keine Einstandskosten zuteilen.", "@sage/xtrem-landed-cost/nodes__landed_cost_base_document__node_name": "Basisdokument Einstandskosten", "@sage/xtrem-landed-cost/nodes__landed_cost_base_document__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__inconsistency_id_action": "Inkonsistenz zwischen der Zuteilung ({{allocationId}}) und der angeforderten Aktion ({{action}}) festgestellt", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__inconsistency_id_document_line": "Die Zuteilung ({{allocationId}}) ist für die Dokumentzeile {{lineId}} nicht vorhanden.", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__item_must_be_landed_cost": "Diese Dokumentzeile kann keine Einstandskosteneigenschaften haben, da der Artikel {{item}} kein Einstandskostenartikel ist.", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations": "Zuteilungen aktualisieren", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations__failed": "Zuteilungen aktualisieren fehlgeschlagen.", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations__parameter__allocationData": "Zuteilungssdaten", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__node_name": "Dokumentzeile Einstandskosten", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationMethod": "Zuteilungsmethode", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationRule": "Zuteilungsregel", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationRuleUnit": "Einheit Zuteilungsregel", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocations": "Zuteilungen", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__costAmountToAllocate": "Zuzuteilender <PERSON>betrag", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__costAmountToAllocateInCompanyCurrency": "Zuzuteilender Kostenbetrag in Unternehmenswährung", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__documentLine": "Dokumentzeile", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__landedCostAllocationMethod": "Landed cost allocation method", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__landedCostAllocationRule": "Landed cost allocation rule", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__landedCostAllocationRuleUnit": "Landed cost allocation rule unit", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__totalAmountAllocated": "Zugeteilter Gesamtbetrag", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations": "Zuteilungen abfragen", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations__failed": "Zuteilungen abfragen fehlgeschlagen.", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations__parameter__landedCostDocumentLineId": "ID Dokumentzeile Einstandskosten", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__sum_of_details_cannot_exceed_amount_to_allocate": "Die Summe der zugeteilten Beträge ({{amountAllocated}}) darf den zuzuteilenden Betrag {{amountToAllocate}} nicht überschreiten.", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport": "Export", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-landed-cost/nodes__landed_cost_item__node_name": "Einstandskostenartikel", "@sage/xtrem-landed-cost/nodes__landed_cost_item__not-a-volume-measure": "Die Einheit {{unitOfMeasure}} ist keine Volumeneinheit.", "@sage/xtrem-landed-cost/nodes__landed_cost_item__not-a-weight-measure": "Die Einheit {{unitOfMeasure}} ist keine Gewichtseinheit.", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__allocationRule": "Zuteilungsregel", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__allocationRuleUnit": "Einheit Zuteilungsregel", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__item": "Artikel", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__landedCostType": "Typ Einstandskosten", "@sage/xtrem-landed-cost/nodes__landed_cost_line__allocation_and_document_line_not_consistent": "Die Zuteilung der Einstandskosten stimmt nicht mit der Dokumentzeile überein.", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-landed-cost/nodes__landed_cost_line__node_name": "Einstandskostenzeile", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__actualAllocatedCostAmountInCompanyCurrency": "Ist-Betrag zugeteilte Kosten in Unternehmenswährung", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__actualCostAmountAllocatedInCompanyCurrency": "Zugeteilter Ist-Kostenbetrag in Unternehmenswährung", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__actualCostAmountInCompanyCurrency": "Istkostenbetrag in Unternehmenswährung", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__companyCurrency": "Unternehmenswährung", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__documentLine": "Dokumentzeile", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__landedCost": "Einstandskosten", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__landedCostAllocation": "Zuteilung Einstandskosten", "@sage/xtrem-landed-cost/package__name": "Einstandskosten Sage DMO", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__allocationRule__title": "Zuteilungsregel Einstandskosten", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__allocationRuleUnit__title": "Zuteilungseinheit Einstandskosten", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__type__title": "Typ Einstandskosten", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRule____title": "Zuteilungsregel", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____columns__title__name": "Name", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____columns__title__symbol": "Symbol", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____title": "Zuteilungseinheit", "@sage/xtrem-landed-cost/page-extensions__item_extension__landedCostBlock____title": "Einstandskosten", "@sage/xtrem-landed-cost/page-extensions__item_extension__landedCostType____title": "<PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost__unknown_document_line_type": "<PERSON> {{constructor<PERSON><PERSON>}} wird nicht verwaltet.", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel____title": "Zuteilung Einstandskosten", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocatedAmount____title": "Zugeteilter Betrag", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocatedAmountInCompanyCurrency____title": "Zugeteilter Betrag in Unternehmenswährung", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocateLandedCost____title": "Einstandskosten zuteilen", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationMethod____title": "Zuteilungsmethode", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationRule____title": "Zuteilungsregel", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationRuleUnit____title": "Einheit Zuteilungsregel", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__amountToAllocate____title": "Zuzuteilender Betrag", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__amountToAllocateInCompanyCurrency____title": "Zuzuteilender Betrag in Unternehmenswährung", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__cancel____title": "Abbrechen", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__confirm": "Bestätigen", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__insufficient_allocations": "Zugeteilter Betrag unzureichend", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title___id": "Dokumentnummer", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__allocatedDocumentType": "Dokumenttyp", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__costAmount": "Zugeteilter Betrag", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__costAmountInCompanyCurrency": "Zugeteilter Betrag in Unternehmenswährung", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineAmountInCompanyCurrency": "Betrag in Unternehmenswährung", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineQuantityInStockUnit": "Menge in Lagereinheit", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineVolume": "Volumen", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineWeight": "Gewicht", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentType": "Dokumenttyp", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____dropdownActions__title": "Löschen", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____title": "Zugeteilte Dokumente", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__mainBlock____title": "Allgemein", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__mainSection____title": "Allgemein", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__save____title": "Speichern", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__total_allocated_lower_than_amount_to_allocate": "Der zugeteilte Gesamtbetrag ist kleiner als der zuzuteilende Einstandskostenbetrag.", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalQuantityInStockUnit____title": "<PERSON>ge gesamt", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalReceiptAmountInCompanyCurrency____title": "Gesamteingangsbetrag in Unternehmenswährung", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalVolume____title": "Gesamtvolumen", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalWeight____title": "Gesamtgewicht", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list____title": "<PERSON>eilen hinzufügen", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__cancel____title": "Abbrechen", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__confirm____title": "Hinzufügen", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__amountExcludingTax": "Zeilenbetrag exkl. Steuern", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__item__name": "Artikelname", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__totalAmountExcludingTax": "Gesamtbetrag exkl. Steuern", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____columns__title__category__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____lookupDialogTitle": "Artikel auswählen", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____title": "Artikel", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__lineBlock____title": "Kriterien", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__resultsBlock____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_summary____title": "Einstandskosten", "@sage/xtrem-landed-cost/pages__landed_cost_summary__actualLandedCostInCompanyCurrency____title": "Ist-Einstandskosten", "@sage/xtrem-landed-cost/pages__landed_cost_summary__cancel____title": "Abbrechen", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title___id": "Dokumentnummer", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__actualAllocatedCostAmountInCompanyCurrency": "Ist-Betrag zugeteilte Kosten", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__actualCostAmountAllocatedInCompanyCurrency": "Ist-Betrag zugeteilte Kosten", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__actualCostAmountInCompanyCurrency": "Ist-Kostenbetrag", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__landedCost__name": "Einstandskosten", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____title": "Einstandskosten in Unternehmenswährung", "@sage/xtrem-landed-cost/pages__landed_cost_summary__mainBlock____title": "Summe in Unternehmenswährung", "@sage/xtrem-landed-cost/pages__landed_cost_summary__mainSection____title": "Allgemein"}