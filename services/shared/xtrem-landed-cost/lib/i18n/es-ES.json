{"@sage/xtrem-landed-cost/data_types__allocation_rule_enum__name": "Regla de asignación", "@sage/xtrem-landed-cost/data_types__landed_cost_allocation_method_enum__name": "Método de asignación de gastos de entrega", "@sage/xtrem-landed-cost/data_types__landed_cost_allocation_update_action_enum__name": "Método de asignación de gastos de entrega", "@sage/xtrem-landed-cost/data_types__landed_cost_document_type_enum__name": "Id. de línea de documento de gastos de entrega", "@sage/xtrem-landed-cost/data_types__landed_cost_type_enum__name": "Tipo de gastos de entrega", "@sage/xtrem-landed-cost/document_type_name_purchase_invoice": "Factura de compra", "@sage/xtrem-landed-cost/document_type_name_purchase_order": "Pedido de compra", "@sage/xtrem-landed-cost/document_type_name_purchase_receipt": "Recepción de compra", "@sage/xtrem-landed-cost/document_type_name_stock_transfer_receipt": "Recepción de transferencia de stock", "@sage/xtrem-landed-cost/enums__allocation_rule__byAmount": "Por importe", "@sage/xtrem-landed-cost/enums__allocation_rule__byQuantity": "Por cantidad", "@sage/xtrem-landed-cost/enums__allocation_rule__byVolume": "Por volumen", "@sage/xtrem-landed-cost/enums__allocation_rule__byWeight": "Por peso", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_method__automatic": "Automática", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_method__manual": "Manual", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__delete": "Eliminar", "@sage/xtrem-landed-cost/enums__landed_cost_allocation_update_action__update": "Actualizar", "@sage/xtrem-landed-cost/enums__landed_cost_document_type__order": "Pedido", "@sage/xtrem-landed-cost/enums__landed_cost_document_type__transaction": "Transacción", "@sage/xtrem-landed-cost/enums__landed_cost_type__administration": "Gestión", "@sage/xtrem-landed-cost/enums__landed_cost_type__crating": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_type__currencyConversion": "Conversión de divisas", "@sage/xtrem-landed-cost/enums__landed_cost_type__customs": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_type__demurrage": "Sobrestadía", "@sage/xtrem-landed-cost/enums__landed_cost_type__duty": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_type__freight": "Flet<PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_type__handling": "Manipulación", "@sage/xtrem-landed-cost/enums__landed_cost_type__insurance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_type__other": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_type__portCharges": "Derechos portuarios", "@sage/xtrem-landed-cost/enums__landed_cost_type__processing": "Procesamiento", "@sage/xtrem-landed-cost/enums__landed_cost_type__storage": "Almacenamiento", "@sage/xtrem-landed-cost/enums__landed_cost_type__tariffs": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/enums__landed_cost_type__taxes": "Impuestos", "@sage/xtrem-landed-cost/enums__landed_cost_type__transportation": "Transporte", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostAllocationRule": "Regla de asignación de gastos de entrega", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostAllocationRuleUnit": "Unidad de regla de asignación de gastos de entrega", "@sage/xtrem-landed-cost/node-extensions__item_extension__property__landedCostItem": "Artículo de gastos de entrega", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__item_must_be_stock_managed": "El artículo {{item}} no se gestiona en stock. No puedes asignarlo a unos gastos de entrega.", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__node_name": "Asignación de gastos de entrega", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__only_credit_purchase_invoice_landed_cost": "Solo puedes abonar gastos de entrega de una factura de compra.", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__allocatedDocumentLine": "Línea de documento asignado", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__allocatedDocumentType": "Tipo de documento asignado", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__costAmount": "Importe de gastos", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__costAmountInCompanyCurrency": "Importe de gastos en divisa de sociedad", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__creditedCostAmount": "Importe de gastos abonado", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__creditedCostAmountInCompanyCurrency": "Importe de gastos abonado en divisa de sociedad", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineAmountInCompanyCurrency": "Importe de línea de documento en divisa de sociedad", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineQuantityInStockUnit": "Cantidad de línea de documento en unidad de stock", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineVolume": "Volumen de línea de documento", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__documentLineWeight": "Peso de línea de documento", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__landedCostItem": "Artículo de gastos de entrega", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__line": "Lín<PERSON>", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__property__sourceAllocationLine": "Línea de asignación de origen", "@sage/xtrem-landed-cost/nodes__landed_cost_allocation__wrong_document_type": "No puedes asignar gastos de entrega a este tipo de documento.", "@sage/xtrem-landed-cost/nodes__landed_cost_base_document__node_name": "Documento base de gastos de entrega", "@sage/xtrem-landed-cost/nodes__landed_cost_base_document__property__number": "Número", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__inconsistency_id_action": "Incoherencia entre la asignación {{allocationId}} y la acción solicitada ({{action}})", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__inconsistency_id_document_line": "La asignación {{allocationId}} no existe en la línea de documento {{lineId}}.", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__item_must_be_landed_cost": "El artículo {{item}} no corresponde a gastos de entrega. La línea de documento no puede tener propiedades de gastos de entrega.", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations": "Actualizar asignaciones", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations__failed": "Error al actualizar las asignaciones", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__mutation__updateAllocations__parameter__allocationData": "Datos de asignación", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__node_name": "Línea de documento de gastos de entrega", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationMethod": "Método de asignación", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationRule": "Regla de asignación", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocationRuleUnit": "Unidad de regla de asignación", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__allocations": "Asignaciones", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__costAmountToAllocate": "Importe de gastos por asignar", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__costAmountToAllocateInCompanyCurrency": "Importe de gastos por asignar en divisa de sociedad", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__documentLine": "Línea de documento", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__landedCostAllocationMethod": "Landed cost allocation method", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__landedCostAllocationRule": "Landed cost allocation rule", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__landedCostAllocationRuleUnit": "Landed cost allocation rule unit", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__property__totalAmountAllocated": "Importe asignado", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations": "Consulta de asignaciones", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations__failed": "Error al consultar las asignaciones", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__query__queryAllocations__parameter__landedCostDocumentLineId": "Id. de línea de documento de gastos de entrega", "@sage/xtrem-landed-cost/nodes__landed_cost_document_line__sum_of_details_cannot_exceed_amount_to_allocate": "El importe asignado ({{amountAllocated}}) no puede ser superior al importe por asignar ({{amountToAllocate}}).", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-landed-cost/nodes__landed_cost_item__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-landed-cost/nodes__landed_cost_item__node_name": "Artículo de gastos de entrega", "@sage/xtrem-landed-cost/nodes__landed_cost_item__not-a-volume-measure": "La unidad {{unitOfMeasure}} no es una unidad de volumen.", "@sage/xtrem-landed-cost/nodes__landed_cost_item__not-a-weight-measure": "La unidad {{unitOfMeasure}} no es una unidad de peso.", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__allocationRule": "Regla de asignación", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__allocationRuleUnit": "Unidad de regla de asignación", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/nodes__landed_cost_item__property__landedCostType": "Tipo de gastos de entrega", "@sage/xtrem-landed-cost/nodes__landed_cost_line__allocation_and_document_line_not_consistent": "La asignación de los gastos de entrega no es coherente con la línea de documento.", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-landed-cost/nodes__landed_cost_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-landed-cost/nodes__landed_cost_line__node_name": "Línea de gastos de entrega", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__actualAllocatedCostAmountInCompanyCurrency": "Importe asignado real en divisa de sociedad", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__actualCostAmountAllocatedInCompanyCurrency": "Importe de coste real asignado en divisa de sociedad", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__actualCostAmountInCompanyCurrency": "Importe real en divisa de sociedad", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__companyCurrency": "Divisa de sociedad", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__documentLine": "Línea de documento", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__landedCost": "Gastos de entrega", "@sage/xtrem-landed-cost/nodes__landed_cost_line__property__landedCostAllocation": "Asignación de gastos de entrega", "@sage/xtrem-landed-cost/package__name": "Gastos de entrega en Sage DMO", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__allocationRule__title": "Regla de asignación de gastos de entrega", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__allocationRuleUnit__title": "Unidad de asignación de gastos de entrega", "@sage/xtrem-landed-cost/page-extensions__item_extension____navigationPanel__listItem__type__title": "Tipo de gastos de entrega", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRule____title": "Regla de asignación", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____columns__title__name": "Nombre", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____columns__title__symbol": "Símbolo", "@sage/xtrem-landed-cost/page-extensions__item_extension__allocationRuleUnit____title": "Unidad de asignación", "@sage/xtrem-landed-cost/page-extensions__item_extension__landedCostBlock____title": "Gastos de entrega", "@sage/xtrem-landed-cost/page-extensions__item_extension__landedCostType____title": "Tipo", "@sage/xtrem-landed-cost/pages__landed_cost__unknown_document_line_type": "El tipo {{constructorName}} no es compatible.", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel____title": "Asignación de gastos de entrega", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocatedAmount____title": "Importe asignado", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocatedAmountInCompanyCurrency____title": "Importe asignado en divisa de sociedad", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocateLandedCost____title": "<PERSON>ignar gas<PERSON> de entrega", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationMethod____title": "Método de asignación", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationRule____title": "Regla de asignación", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__allocationRuleUnit____title": "Unidad de regla de asignación", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__amountToAllocate____title": "Importe por asignar", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__amountToAllocateInCompanyCurrency____title": "Importe por asignar en divisa de sociedad", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__confirm": "Confirmar", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__insufficient_allocations": "Importe asignado insuficiente", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title___id": "Número de documento", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__allocatedDocumentType": "Tipo de documento", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__costAmount": "Importe asignado", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__costAmountInCompanyCurrency": "Importe asignado en divisa de sociedad", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineAmountInCompanyCurrency": "Importe en divisa de sociedad", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineQuantityInStockUnit": "Cantidad en unidad de stock", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineVolume": "Volumen", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentLineWeight": "Peso", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____columns__title__documentType": "Tipo de documento", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____dropdownActions__title": "Eliminar", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__lines____title": "Documentos asignados", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__mainBlock____title": "General", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__mainSection____title": "General", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__save____title": "Guardar", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__total_allocated_lower_than_amount_to_allocate": "El importe asignado es inferior al importe de gastos de entrega por asignar.", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalQuantityInStockUnit____title": "Cantidad total", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalReceiptAmountInCompanyCurrency____title": "Importe de recepción en divisa de sociedad", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalVolume____title": "Volumen total", "@sage/xtrem-landed-cost/pages__landed_cost_allocation_panel__totalWeight____title": "Peso total", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__confirm____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__amountExcludingTax": "Importe sin impuestos de línea", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__item__name": "Nombre de artículo", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__lineAmountExcludingTax": "Importe sin impuestos de línea", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__number": "Número", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__documents____levels__columns__title__totalAmountExcludingTax": "Total sin impuestos", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____columns__title__category__name": "Categoría", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__lineBlock____title": "Criterios", "@sage/xtrem-landed-cost/pages__landed_cost_document_line_pick_list__resultsBlock____title": "", "@sage/xtrem-landed-cost/pages__landed_cost_summary____title": "Gastos de entrega", "@sage/xtrem-landed-cost/pages__landed_cost_summary__actualLandedCostInCompanyCurrency____title": "Gastos de entrega reales", "@sage/xtrem-landed-cost/pages__landed_cost_summary__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title___id": "Número de documento", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__actualAllocatedCostAmountInCompanyCurrency": "Importe asignado real", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__actualCostAmountAllocatedInCompanyCurrency": "Importe asignado real", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__actualCostAmountInCompanyCurrency": "Importe real", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____columns__title__landedCost__name": "Gastos de entrega", "@sage/xtrem-landed-cost/pages__landed_cost_summary__lines____title": "Gastos de entrega en divisa de sociedad", "@sage/xtrem-landed-cost/pages__landed_cost_summary__mainBlock____title": "Total en divisa de sociedad", "@sage/xtrem-landed-cost/pages__landed_cost_summary__mainSection____title": "General"}