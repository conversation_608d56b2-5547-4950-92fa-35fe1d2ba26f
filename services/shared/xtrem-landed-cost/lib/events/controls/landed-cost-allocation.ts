import type { decimal, ValidationContext } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremLandedCost from '../..';

export async function controlAllocatedItem(cx: ValidationContext, item: xtremMasterData.nodes.Item) {
    if (!(await item.isStockManaged)) {
        cx.error.addLocalized(
            '@sage/xtrem-landed-cost/nodes__landed_cost_allocation__item_must_be_stock_managed',
            'The item {{item}} is not stock managed. You cannot allocate it to a landed cost.',
            { item: await item.id },
        );
    }
}

export function controlAllocatedDocumentLine(
    cx: ValidationContext,
    documentLine: xtremMasterData.nodes.BaseDocumentLine,
) {
    if (!xtremLandedCost.functions.typingLib.isInstanceOfLandedCostAllocatedDocumentLine(documentLine)) {
        cx.error.addLocalized(
            '@sage/xtrem-landed-cost/nodes__landed_cost_allocation__wrong_document_type',
            'You cannot allocate landed costs to this document type.',
        );
    }
}

export async function controlCreditedAmount(
    cx: ValidationContext,
    value: {
        amount: decimal;
        creditedAmount: decimal;
        sourceAllocationLine: xtremLandedCost.nodes.LandedCostAllocation | null;
    },
) {
    await cx.error.if(value.creditedAmount).is.greater.than(value.amount);
    await cx.error.if(value.creditedAmount).is.negative();
    if (value.sourceAllocationLine && value.creditedAmount !== 0) {
        cx.error.addLocalized(
            '@sage/xtrem-landed-cost/nodes__landed_cost_allocation__only_credit_purchase_invoice_landed_cost',
            'You can only credit purchase invoice landed cost records.',
        );
    }
}
