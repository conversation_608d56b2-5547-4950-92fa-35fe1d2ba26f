import type { ValidationContext } from '@sage/xtrem-core';
import { Logger } from '@sage/xtrem-core';
import * as xtremLandedCost from '../..';

const logger = Logger.getLogger(__filename, 'landed-cost-line');

export async function controlConsistency(cx: ValidationContext, landedCostLine: xtremLandedCost.nodes.LandedCostLine) {
    // Control to be sure that a landed cost line is created using an allocation already assigned
    // to the current document line or to the corresponding order line.
    const documentLine = await landedCostLine.documentLine;

    const allocatedDocumentLine = await (await landedCostLine.landedCostAllocation).allocatedDocumentLine;

    if (documentLine._id !== allocatedDocumentLine._id) {
        logger.verbose(
            () =>
                `The allocatedDocumentLine (_id=${allocatedDocumentLine._id} of the landed cost allocation is not assigned to the documentLine (_id=${documentLine._id})`,
        );
        const orderDocumentLine =
            (xtremLandedCost.functions.typingLib.isInstanceOfLandedCostAllocatedTransactionDocumentLine(documentLine) &&
                (await documentLine.getOrderDocumentLine())) ||
            undefined;
        if (orderDocumentLine?._id !== allocatedDocumentLine._id) {
            logger.verbose(
                () =>
                    `The allocatedDocumentLine (_id=${allocatedDocumentLine._id} of the landed cost allocation is not assigned to the order of the documentLine (_id=${orderDocumentLine?._id})`,
            );
            const orderDocumentLine2 =
                (xtremLandedCost.functions.typingLib.isInstanceOfLandedCostAllocatedTransactionDocumentLine(
                    allocatedDocumentLine,
                ) &&
                    (await allocatedDocumentLine.getOrderDocumentLine())) ||
                undefined;
            if (orderDocumentLine2?._id !== documentLine._id) {
                logger.verbose(
                    () =>
                        `The order of the allocatedDocumentLine (_id=${orderDocumentLine2?._id} of the landed cost allocation is not assigned to the documentLine (_id=${documentLine._id})`,
                );
                cx.error.addLocalized(
                    '@sage/xtrem-landed-cost/nodes__landed_cost_line__allocation_and_document_line_not_consistent',
                    'The landed cost allocation is not consistent with the document line.',
                );
            }
        }
    }
}
