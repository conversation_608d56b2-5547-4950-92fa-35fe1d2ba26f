import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type { decimal } from '@sage/xtrem-shared';

export async function controlItem(cx: ValidationContext, item: xtremMasterData.nodes.Item) {
    if ((await item.type) !== 'landedCost') {
        cx.error.addLocalized(
            '@sage/xtrem-landed-cost/nodes__landed_cost_document_line__item_must_be_landed_cost',
            'This document line cannot have landed cost properties because the {{item}} item is not a landed cost.',
            { item: await item.id },
        );
    }
}

export async function controlTotalAmountAllocated(
    cx: ValidationContext,
    amountAllocated: decimal,
    amountToAllocate: decimal,
    currency: xtremMasterData.nodes.Currency,
) {
    if (amountAllocated > amountToAllocate) {
        cx.error.addLocalized(
            '@sage/xtrem-landed-cost/nodes__landed_cost_document_line__sum_of_details_cannot_exceed_amount_to_allocate',
            'The total of allocated amounts ({{currencySymbol}}{{amountAllocated}}) cannot exceed the amount to allocate {{currencySymbol}}{{amountToAllocate}}.',
            {
                currencySymbol: await currency.symbol,
                amountAllocated,
                amountToAllocate,
            },
        );
    }
}
