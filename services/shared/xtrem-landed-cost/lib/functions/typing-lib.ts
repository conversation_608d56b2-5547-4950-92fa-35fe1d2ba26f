import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as _ from 'lodash';
import type * as xtremLandedCost from '..';

export function isInstanceOfLandedCostAllocatedDocumentLine(
    line: xtremMasterData.nodes.BaseDocumentLine,
): line is xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum> {
    return _.hasIn(line, 'amountForLandedCostAllocation');
}

export function isInstanceOfLandedCostAllocatedTransactionDocumentLine(
    line: xtremMasterData.nodes.BaseDocumentLine,
): line is xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction> {
    return _.hasIn(line, 'getOrderDocumentLine');
}

export function isInstanceOfLandedCostAllocatedOrderDocumentLine(
    line: xtremMasterData.nodes.BaseDocumentLine,
): line is xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.order> {
    return _.hasIn(line, 'remainingQuantityToProcessForLandedCost');
}
