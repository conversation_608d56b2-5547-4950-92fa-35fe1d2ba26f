import type { Context } from '@sage/xtrem-core';
import { Logger, asyncArray } from '@sage/xtrem-core';
import * as xtremLandedCost from '..';

const logger = Logger.getLogger(__filename, 'landed-cost-allocation-lib');

export async function getSortedLandedCostAllocations(
    context: Context,
    documentLine: xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum>,
): Promise<xtremLandedCost.nodes.LandedCostAllocation[]> {
    const landedCostAllocations = await context
        .query(xtremLandedCost.nodes.LandedCostAllocation, {
            filter: { allocatedDocumentLine: documentLine._id },
        })
        // it's not possible to order directly in the query because the documentNumber is a calculated property
        .sort(async (a, b) =>
            (await (await (await a.line).documentLine).documentNumber).localeCompare(
                await (
                    await (
                        await b.line
                    ).documentLine
                ).documentNumber,
            ),
        )
        .toArray();
    await logger.verboseAsync(
        async () =>
            `   allocations of documentLine._id=${documentLine._id} = ${JSON.stringify(
                await asyncArray(landedCostAllocations)
                    .map(async alloc => ({
                        _id: alloc._id,
                        line: {
                            documentLine: {
                                documentNumber: await (await (await alloc.line).documentLine).documentNumber,
                            },
                        },
                    }))
                    .toArray(),
                null,
                4,
            )}`,
    );
    return landedCostAllocations;
}
