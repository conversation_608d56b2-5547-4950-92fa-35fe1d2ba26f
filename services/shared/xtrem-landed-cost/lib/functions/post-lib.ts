import type { Context, decimal } from '@sage/xtrem-core';
import { Logger } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremLandedCost from '..';

const logger = Logger.getLogger(__filename, 'post-lib');

async function createOrUpdateLandedCostLine(
    context: Context,
    updateData: {
        documentLine: xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum>;
        landedCost: xtremMasterData.nodes.Item;
        landedCostAllocation: xtremLandedCost.nodes.LandedCostAllocation;
        allocatedCostAmountInCompanyCurrency: decimal;
        isOrderLineLinkedToReceiptLine?: boolean;
    },
) {
    await logger.verboseAsync(
        async () =>
            `createOrUpdateLandedCostLine updateData=${JSON.stringify(
                {
                    documentLine: updateData.documentLine._id,
                    landedCost: await updateData.landedCost.id,
                    landedCostAllocation: {
                        _id: updateData.landedCostAllocation._id,
                        line: {
                            documentLine: {
                                documentNumber: await (
                                    await (
                                        await updateData.landedCostAllocation.line
                                    ).documentLine
                                ).documentNumber,
                            },
                        },
                    },
                    allocatedCostAmountInCompanyCurrency: updateData.allocatedCostAmountInCompanyCurrency,
                    isOrderLineLinkedToReceiptLine: updateData.isOrderLineLinkedToReceiptLine,
                },
                null,
                4,
            )}`,
    );

    const existingLandedCostLine = await context.tryRead(
        xtremLandedCost.nodes.LandedCostLine,
        {
            documentLine: updateData.documentLine,
            landedCost: updateData.landedCost,
            landedCostAllocation: updateData.landedCostAllocation,
        },
        { forUpdate: true },
    );

    if (existingLandedCostLine && !updateData.isOrderLineLinkedToReceiptLine) {
        return;
    }

    const landedCostLine =
        existingLandedCostLine ??
        (await context.create(xtremLandedCost.nodes.LandedCostLine, {
            documentLine: updateData.documentLine,
            landedCost: updateData.landedCost,
            landedCostAllocation: updateData.landedCostAllocation,
            actualCostAmountInCompanyCurrency: updateData.allocatedCostAmountInCompanyCurrency,
        }));

    await logger.verboseAsync(
        async () => `landedCostLine.landedCost.id = ${await (await landedCostLine.landedCost)?.id}`,
    );
    await logger.verboseAsync(
        async () => `actualCostAmountInCompanyCurrency = ${await landedCostLine.actualCostAmountInCompanyCurrency}`,
    );

    if (updateData.isOrderLineLinkedToReceiptLine) {
        logger.verbose(
            () => `actualAllocatedCostAmountInCompanyCurrency += ${updateData.allocatedCostAmountInCompanyCurrency}`,
        );
        await landedCostLine.$.set({
            actualAllocatedCostAmountInCompanyCurrency:
                (await landedCostLine.actualAllocatedCostAmountInCompanyCurrency) +
                updateData.allocatedCostAmountInCompanyCurrency,
        });
    }

    await logger.verboseAsync(
        async () =>
            `-> landedCostLine = ${JSON.stringify(
                {
                    documentLine: (await landedCostLine.documentLine)._id,
                    landedCost: await (await landedCostLine.landedCost).id,
                    landedCostAllocation: {
                        _id: (await landedCostLine.landedCostAllocation)._id,
                        allocatedDocumentLine: (await (await landedCostLine.landedCostAllocation).allocatedDocumentLine)
                            ._id,
                        line: {
                            documentLine: {
                                documentNumber: await (
                                    await (
                                        await (
                                            await landedCostLine.landedCostAllocation
                                        ).line
                                    ).documentLine
                                ).documentNumber,
                            },
                        },
                    },
                    actualCostAmountInCompanyCurrency: await landedCostLine.actualCostAmountInCompanyCurrency,
                    actualAllocatedCostAmountInCompanyCurrency:
                        await landedCostLine.actualAllocatedCostAmountInCompanyCurrency,
                },
                null,
                4,
            )}`,
    );
    await landedCostLine.$.save();
}

export async function manageLandedCostAllocationsOnDocumentLinePost(
    context: Context,
    documentLine: xtremLandedCost.interfaces.DocumentLineAllocatingLandedCost,
) {
    await (
        await documentLine.landedCost
    )?.allocations.forEach(async landedCostAllocation => {
        const allocatedDocumentLine = await landedCostAllocation.getAllocatedDocumentLine();
        const commonLandedCostUpdateData: Parameters<typeof createOrUpdateLandedCostLine>[1] = {
            documentLine: allocatedDocumentLine,
            landedCost: await landedCostAllocation.landedCostItem,
            landedCostAllocation,
            allocatedCostAmountInCompanyCurrency: await landedCostAllocation.costAmountInCompanyCurrency,
        };

        await createOrUpdateLandedCostLine(context, commonLandedCostUpdateData);

        if (
            xtremLandedCost.functions.typingLib.isInstanceOfLandedCostAllocatedTransactionDocumentLine(
                commonLandedCostUpdateData.documentLine,
            )
        ) {
            const orderDocumentLine = await commonLandedCostUpdateData.documentLine.getOrderDocumentLine();

            if (orderDocumentLine) {
                await createOrUpdateLandedCostLine(context, {
                    ...commonLandedCostUpdateData,
                    documentLine: orderDocumentLine,
                    isOrderLineLinkedToReceiptLine: true,
                });
            }
        }
    });
}
