import type {
    AsyncArray,
    AsyncArrayReader,
    Collection,
    Context,
    NodeUpdateData,
    decimal,
    integer,
} from '@sage/xtrem-core';
import { Logger, NodeStatus, asyncArray } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as _ from 'lodash';
import * as xtremLandedCost from '..';

const logger = Logger.getLogger(__filename, 'landed-cost-line-lib');

/**
 * Returns an array of total cost amount per type of landed cost for a document
 * @param documentLines collection of lines containing a property landedCostLines
 * @returns
 */
export async function getLandedCostsPerType(
    documentLines:
        | AsyncArray<
              xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum>
          >
        | Collection<
              xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum>
          >,
): Promise<
    {
        landedCostType: string;
        actualCostAmountInCompanyCurrency: decimal;
        actualAllocatedCostAmountInCompanyCurrency: decimal;
    }[]
> {
    logger.verbose(() => 'getLandedCostsPerType');
    await logger.verboseAsync(async () => `documentLines.length=${await documentLines.length}`);
    const sumPerType = await documentLines.reduce(
        async (cum, line) => {
            await line.landedCostLines.forEach(async landedCostLine => {
                const landedCostItem = await (await landedCostLine.landedCost).landedCostItem;
                if (landedCostItem) {
                    const type = await landedCostItem.landedCostType;
                    await logger.verboseAsync(
                        async () => `type = ${type} +=${await landedCostLine.actualCostAmountInCompanyCurrency}`,
                    );
                    cum[type] = {
                        actualCostAmountInCompanyCurrency:
                            (cum[type] ? cum[type].actualCostAmountInCompanyCurrency : 0) +
                            (await landedCostLine.actualCostAmountInCompanyCurrency),
                        actualAllocatedCostAmountInCompanyCurrency:
                            (cum[type] ? cum[type].actualAllocatedCostAmountInCompanyCurrency : 0) +
                            (await landedCostLine.actualAllocatedCostAmountInCompanyCurrency),
                    };
                    logger.verbose(() => `cum = ${JSON.stringify(cum, null, 4)}`);
                }
            });
            return cum;
        },
        {} as Record<
            string,
            {
                actualCostAmountInCompanyCurrency: decimal;
                actualAllocatedCostAmountInCompanyCurrency: decimal;
            }
        >,
    );
    const arraySumPerType = Object.keys(sumPerType).map(type => ({
        landedCostType: type,
        actualCostAmountInCompanyCurrency: sumPerType[type].actualCostAmountInCompanyCurrency,
        actualAllocatedCostAmountInCompanyCurrency: sumPerType[type].actualAllocatedCostAmountInCompanyCurrency,
    }));

    logger.verbose(() => `return ${JSON.stringify(arraySumPerType)}`);
    return arraySumPerType;
}

/**
 * returns the landed cost lines of an order line to create corresponding landed cost lines of a transaction document line (purchase receipt line)
 * @param context
 * @param order
 * @param destinationDocumentLine
 * @returns
 */
export async function getLandedCostLinesFromOrderLine(
    context: Context,
    order: {
        documentLine: xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.order>; // e.g. PurchaseOrderLine
        remainingQuantityToProcess: decimal;
    },
    destinationDocumentLine: xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction>, // e.g. PurchaseReceiptLine
): Promise<{
    transactionDocumentLandedCostLines: NodeUpdateData<xtremLandedCost.nodes.LandedCostLine>[];
    orderDocumentLandedCostLines: NodeUpdateData<xtremLandedCost.nodes.LandedCostLine>[];
}> {
    if (!(await context.isServiceOptionEnabled(xtremMasterData.serviceOptions.landedCostOption))) {
        return { transactionDocumentLandedCostLines: [], orderDocumentLandedCostLines: [] };
    }

    logger.verbose(
        () =>
            `getLandedCostLinesFromOrderLine ${JSON.stringify(
                {
                    order: {
                        documentLine: order.documentLine._id,
                        remainingQuantityToProcess: order.remainingQuantityToProcess,
                    },
                    destinationDocumentLine: destinationDocumentLine._id,
                },
                null,
                4,
            )}`,
    );

    const transactionDocumentLandedCostLines: NodeUpdateData<xtremLandedCost.nodes.LandedCostLine>[] = [];
    const orderDocumentLandedCostLines: NodeUpdateData<xtremLandedCost.nodes.LandedCostLine>[] = [];
    await order.documentLine.landedCostLines.forEach(async landedCostLine => {
        if ((await (await landedCostLine.landedCostAllocation).allocatedDocumentLine)._id !== order.documentLine._id) {
            logger.verbose(
                () => `landed cost line (_id=${landedCostLine._id}) linked to an allocation on another document`,
            );
            return;
        }
        logger.verbose(
            () => `landed cost line (_id=${landedCostLine._id}) linked to an allocation on this order document line`,
        );
        const costAllocable =
            (await landedCostLine.actualCostAmountInCompanyCurrency) -
            (await landedCostLine.actualAllocatedCostAmountInCompanyCurrency);
        logger.verbose(() => `  costAllocable=${costAllocable}`);
        const landedCostAllocation = await landedCostLine.landedCostAllocation;

        const actualCostAmountInCompanyCurrency = Math.min(
            costAllocable,
            (costAllocable * (await destinationDocumentLine.quantityInStockUnitForLandedCostAllocation)) /
                order.remainingQuantityToProcess,
        );
        await logger.verboseAsync(
            async () =>
                `  destinationDocumentLine.quantityInStockUnitForLandedCostAllocation=${await destinationDocumentLine.quantityInStockUnitForLandedCostAllocation}`,
        );
        logger.verbose(() => `  order.remainingQuantityToProcess=${order.remainingQuantityToProcess}`);
        logger.verbose(() => `  actualCostAmountInCompanyCurrency=${actualCostAmountInCompanyCurrency}`);
        if (actualCostAmountInCompanyCurrency === 0) {
            return;
        }

        const existingLandedCostLine = await destinationDocumentLine.landedCostLines.find(
            async transactLandedCostLine =>
                (await transactLandedCostLine.landedCost)._id === (await landedCostLine.landedCost)._id &&
                (await transactLandedCostLine.landedCostAllocation)._id === landedCostAllocation._id,
        );

        transactionDocumentLandedCostLines.push({
            _action: existingLandedCostLine ? 'update' : 'create',
            actualCostAmountInCompanyCurrency:
                ((await existingLandedCostLine?.actualCostAmountInCompanyCurrency) ?? 0) +
                actualCostAmountInCompanyCurrency,
            documentLine: destinationDocumentLine,
            landedCost: await landedCostLine.landedCost,
            landedCostAllocation,
            actualAllocatedCostAmountInCompanyCurrency: 0,
        });
        logger.verbose(
            () =>
                `  --> create or update landed cost line for the transaction doc line: ${JSON.stringify(
                    transactionDocumentLandedCostLines[transactionDocumentLandedCostLines.length - 1],
                    null,
                    4,
                )}`,
        );

        orderDocumentLandedCostLines.push({
            _action: 'update',
            _id: landedCostLine._id,
            actualAllocatedCostAmountInCompanyCurrency:
                (await landedCostLine.actualAllocatedCostAmountInCompanyCurrency) + actualCostAmountInCompanyCurrency,
        });
        logger.verbose(
            () =>
                `  --> update landed cost line for the order doc line: ${JSON.stringify(
                    {
                        actualAllocatedCostAmountInCompanyCurrency:
                            orderDocumentLandedCostLines[orderDocumentLandedCostLines.length - 1],
                    },
                    null,
                    4,
                )}`,
        );
    });

    return {
        transactionDocumentLandedCostLines,
        orderDocumentLandedCostLines,
    };
}

export async function checkLandedCostForPosting(
    context: Context,
    transactionDocument: xtremLandedCost.interfaces.LandedCostAllocatedDocumentHeader<xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction>,
) {
    await logger.verboseAsync(async () => `checkLandedCostForPosting document=${await transactionDocument.number}`);
    const notPostedAllocatingDocuments = await transactionDocument.landedCostAssignableLines.reduce(
        async (invoices, transactionLine) => {
            const orderLine = await transactionLine.getOrderDocumentLine();
            if (orderLine) {
                // 1- get the landed cost allocations linked to the order line
                const orderLineAllocations =
                    await xtremLandedCost.functions.landedCostAllocationLib.getSortedLandedCostAllocations(
                        context,
                        orderLine,
                    );
                if (orderLineAllocations.length) {
                    // 2- Look for the LandedCostLine records linked to the order's allocations
                    const existingLines = await context.select(
                        xtremLandedCost.nodes.LandedCostLine,
                        { landedCostAllocation: { _id: true } },
                        {
                            filter: {
                                landedCostAllocation: {
                                    _in: orderLineAllocations.map(alloc => alloc._id),
                                },
                            },
                        },
                    );
                    logger.verbose(() => `existing LandedCostLine = ${JSON.stringify(existingLines, null, 4)}`);
                    const notPostedInvoiceLinesAllocations = asyncArray(orderLineAllocations).filter(
                        alloc => !existingLines.find(existLine => existLine.landedCostAllocation._id === alloc._id),
                    );
                    await logger.verboseAsync(
                        async () =>
                            `orderLine allocations of not posted InvoiceLines = ${JSON.stringify(
                                await notPostedInvoiceLinesAllocations.toArray(),
                                null,
                                4,
                            )}`,
                    );
                    return invoices.concat(
                        await notPostedInvoiceLinesAllocations
                            .map(async alloc => (await (await alloc.line).documentLine).documentNumber)
                            .toArray(),
                    );
                }
            }

            return invoices;
        },
        [] as string[],
    );

    return _.uniq(notPostedAllocatingDocuments);
}

async function updateOrderDocumentLandedCostLines(
    context: Context,
    orderDocumentLandedCostLines: NodeUpdateData<xtremLandedCost.nodes.LandedCostLine>[],
    orderLine?: xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.order>,
) {
    if (!orderDocumentLandedCostLines.length) {
        logger.verbose(() => `No landed cost lines to update`);
        return;
    }
    if (orderLine) {
        logger.verbose(
            () => `update orderLine(${orderLine._id}) with : ${JSON.stringify(orderDocumentLandedCostLines, null, 4)}`,
        );
        await orderLine.$.set({ landedCostLines: orderDocumentLandedCostLines });
        await logger.verboseAsync(
            async () =>
                ` -> orderLine.landedCostLines= ${JSON.stringify(await orderLine.landedCostLines.toArray(), null, 4)}`,
        );
        await orderLine.$.save();
    } else {
        await asyncArray(orderDocumentLandedCostLines).forEach(async ordLandedCostLine => {
            logger.verbose(() => `update order LandedCostLine: ${JSON.stringify(ordLandedCostLine)}`);
            const landedCostLine = await context.read(
                xtremLandedCost.nodes.LandedCostLine,
                { _id: ordLandedCostLine._id },
                { forUpdate: true },
            );
            await landedCostLine.$.set({ ...ordLandedCostLine });
            await landedCostLine.$.save();
        });
    }
}

/**
 * create transaction landed cost lines from the corresponding order line
 * The order line is already updated considering the quantity of the transaction
 * @param transactionDocumentLines list of transaction document lines (i.e. purchase receipt lines) for which the propagation of order's landed costs is requested
 * @param options
 * @param  options.save - save the transaction line with new LandedCostLine records or not ? (if not, must be saved afterwards)
 * @param  options.orderLine - If specified, the remaining amount of this order will be dispatched on all transactionDocumentLines.
 * Otherwise, the amount to apply to each transaction will be calculated in function of its quantity.
 * This orderLine must be writable to update associated landed cost lines.
 */
export async function propagateLandedCostLinesFromOrder<
    DocumentType extends xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction,
>(
    context: Context,
    transactionDocumentLines:
        | AsyncArrayReader<xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<DocumentType>>
        | Collection<xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<DocumentType>>,
    options: {
        save: boolean;
        orderLine?: xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.order>;
    } = { save: true },
): Promise<{
    updatedLandedCostLines: xtremLandedCost.interfaces.LandedCostLineUpdate[];
    updatedTransactionDocumentLines: xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<DocumentType>[];
}> {
    const updatedLandedCostLines: xtremLandedCost.interfaces.LandedCostLineUpdate[] = [];
    const updatedTransactionDocumentLines: xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<DocumentType>[] =
        [];
    const transactionLineMap: Record<integer, boolean> = {};

    await logger.verboseAsync(
        async () =>
            `propagateLandedCostLinesFromOrder transactionDocument._id=${await transactionDocumentLines
                .map(line => line._id)
                .toArray()} options=${JSON.stringify(
                { save: options.save, orderLine: { _id: options.orderLine?._id } },
                null,
                4,
            )}`,
    );

    let quantityToProcess = 0;
    if (options.orderLine) {
        quantityToProcess = await transactionDocumentLines.sum(line => line.quantityInStockUnitForLandedCostAllocation);
    }
    await transactionDocumentLines.forEach(async transactionDocumentLine => {
        const orderLine = options.orderLine ?? (await transactionDocumentLine.getOrderDocumentLine());
        if (!orderLine) {
            return;
        }

        const { transactionDocumentLandedCostLines, orderDocumentLandedCostLines } =
            await xtremLandedCost.functions.landedCostLineLib.getLandedCostLinesFromOrderLine(
                context,
                {
                    documentLine: orderLine,
                    remainingQuantityToProcess: options.orderLine
                        ? quantityToProcess
                        : ((await orderLine.remainingQuantityToProcessForLandedCost) ?? 0) +
                          // the quantity of the current receipt line must be added as it has already been considered into the orderLine posted received quantity
                          (await transactionDocumentLine.quantityInStockUnitForLandedCostAllocation),
                },
                transactionDocumentLine,
            );

        if (!transactionDocumentLandedCostLines.length) {
            return;
        }

        quantityToProcess -= await transactionDocumentLine.quantityInStockUnitForLandedCostAllocation;

        await transactionDocumentLine.$.set({
            landedCostLines: transactionDocumentLandedCostLines,
        } as NodeUpdateData<xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<DocumentType>>);

        // get the generated/modified LandedCostLine records and the corresponding variances
        await transactionDocumentLine.landedCostLines
            .filter(landedCostLine => [NodeStatus.added, NodeStatus.modified].includes(landedCostLine.$.status))
            .forEach(async landedCostLine => {
                updatedLandedCostLines.push({
                    landedCostLine,
                    varianceAmount:
                        (await landedCostLine.actualCostAmountInCompanyCurrency) -
                        (landedCostLine.$.status === NodeStatus.modified
                            ? await (
                                  await landedCostLine.$.old
                              ).actualCostAmountInCompanyCurrency
                            : 0),
                });
                if (!transactionLineMap[transactionDocumentLine._id]) {
                    transactionLineMap[transactionDocumentLine._id] = true;
                    updatedTransactionDocumentLines.push(transactionDocumentLine);
                }
            });

        if (options.save) {
            await transactionDocumentLine.$.save();
        }

        await updateOrderDocumentLandedCostLines(context, orderDocumentLandedCostLines, options.orderLine);
    });

    return { updatedLandedCostLines, updatedTransactionDocumentLines };
}
