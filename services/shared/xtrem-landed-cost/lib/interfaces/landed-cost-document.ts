import type { Collection, JsonType, Reference, decimal } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremLandedCost from '..';

/**
 * document line that assigns landed costs to other documents (e.g. purchase invoice line or credit memo line)
 */
export interface DocumentLineAllocatingLandedCost extends xtremMasterData.nodes.BaseDocumentLine {
    item: Reference<xtremMasterData.nodes.Item>;
    landedCost:
        | Reference<xtremLandedCost.nodes.LandedCostDocumentLine>
        | Reference<xtremLandedCost.nodes.LandedCostDocumentLine | null>;
    getLandedCostAmountToAllocate(): Promise<decimal>;
    convertAmountInCompanyCurrency(amount: decimal): Promise<decimal>;
    currency: Reference<xtremMasterData.nodes.Currency>;
    companyCurrency: Reference<xtremMasterData.nodes.Currency>;
}

export interface DocumentAllocatingLandedCost extends xtremMasterData.interfaces.Document {
    lines: Collection<DocumentLineAllocatingLandedCost>;
}
/**
 * document line for which landed cost can be assigned (e.g. purchase receipt line or purchase order line)
 */
export type LandedCostAllocatedDocumentLine<T extends xtremLandedCost.enums.LandedCostDocumentTypeEnum> =
    xtremMasterData.nodes.BaseDocumentLine & {
        site?: Reference<xtremSystem.nodes.Site>;
        item: Reference<xtremMasterData.nodes.Item>;
        amountForLandedCostAllocation: Promise<decimal>;
        quantityInStockUnitForLandedCostAllocation: Promise<decimal>;
        companyCurrency: Reference<xtremMasterData.nodes.Currency>;
        actualLandedCostInCompanyCurrency?: Promise<decimal>;
        landedCostLines: Collection<xtremLandedCost.nodes.LandedCostLine>;
        storedDimensions: Promise<object | null>;
        storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;
        computedAttributes: Promise<object>;
    } & (T extends xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction
            ? {
                  getOrderDocumentLine: () => Promise<
                      | LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.order>
                      | undefined
                  >;
              }
            : T extends xtremLandedCost.enums.LandedCostDocumentTypeEnum.order
              ? {
                    remainingQuantityToProcessForLandedCost?: Promise<decimal>;
                }
              : {});

export type LandedCostAllocatedDocumentHeader<T extends xtremLandedCost.enums.LandedCostDocumentTypeEnum> =
    xtremLandedCost.nodes.LandedCostBaseDocument & {
        landedCostAssignableLines: Collection<LandedCostAllocatedDocumentLine<T>>;
    };

// used to obtain aggregate of landed cost types (in Purchase order or Purchase Receipt)
export type JsonAggregateLandedCostTypes = Array<
    JsonType<{
        landedCostType: string;
        actualCostAmountInCompanyCurrency: decimal;
        actualAllocatedCostAmountInCompanyCurrency: decimal;
    }>
>;
