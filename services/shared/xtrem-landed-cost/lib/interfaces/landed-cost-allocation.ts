import type { decimal, integer } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';

export type DataForCreateUpdateAllocationAction = {
    _id: integer;
    _action: 'create' | 'update';
    allocatedDocumentLine: xtremMasterData.nodes.BaseDocumentLine['_id'];
    allocatedAmount: decimal;
    allocatedAmountInCompanyCurrency: decimal;
};

export type DataForDeleteAllocationAction = {
    _id: integer;
    _action: 'delete';
};

export type DataForAllocationAction = DataForCreateUpdateAllocationAction | DataForDeleteAllocationAction;
