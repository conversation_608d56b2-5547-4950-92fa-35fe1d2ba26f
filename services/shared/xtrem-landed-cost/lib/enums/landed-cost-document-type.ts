import { EnumDataType } from '@sage/xtrem-core';

export enum LandedCostDocumentTypeEnum {
    transaction = 1, // i.e. purchase receipt
    order, // i.e. purchase order
}

export type LandedCostDocumentType = keyof typeof LandedCostDocumentTypeEnum;

export const landedCostDocumentTypeDataType = new EnumDataType<LandedCostDocumentType>({
    enum: LandedCostDocumentTypeEnum,
    filename: __filename,
});
