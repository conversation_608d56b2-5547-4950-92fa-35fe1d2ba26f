import { EnumDataType } from '@sage/xtrem-core';

export enum LandedCostAllocationUpdateActionEnum {
    create,
    update,
    delete,
}

export type LandedCostAllocationUpdateAction = keyof typeof LandedCostAllocationUpdateActionEnum;

export const LandedCostAllocationUpdateActionDataType = new EnumDataType<LandedCostAllocationUpdateAction>({
    enum: LandedCostAllocationUpdateActionEnum,
    filename: __filename,
});
