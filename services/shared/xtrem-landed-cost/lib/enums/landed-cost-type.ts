import { EnumDataType } from '@sage/xtrem-core';

export enum LandedCostTypeEnum {
    freight = 1,
    customs = 2,
    duty = 3,
    tariffs = 4,
    taxes = 5,
    insurance = 6,
    administration = 7,
    handling = 8,
    processing = 9,
    transportation = 10,
    storage = 11,
    currencyConversion = 12,
    crating = 13,
    demurrage = 14,
    portCharges = 15,
    other = 16,
}

export type LandedCostType = keyof typeof LandedCostTypeEnum;

export const landedCostTypeDataType = new EnumDataType<LandedCostType>({
    enum: LandedCostTypeEnum,
    filename: __filename,
});
