import type { Graph<PERSON>pi } from '@sage/xtrem-landed-cost-api';
import type { UnitOfMeasureBinding } from '@sage/xtrem-master-data-api';
import type { Item } from '@sage/xtrem-master-data/build/lib/pages/item';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<ItemExtension>({
    extends: '@sage/xtrem-master-data/Item',
    navigationPanel: {
        listItem: {
            type: ui.nestedFields.dropdownList({
                bind: { landedCostItem: { landedCostType: true } },
                title: 'Landed cost type',
                isHiddenOnMainField: true,
            }),
            allocationRule: ui.nestedFields.dropdownList({
                bind: { landedCostItem: { allocationRule: true } },
                title: 'Landed cost allocation rule',
                isHiddenOnMainField: true,
            }),
            allocationRuleUnit: ui.nestedFields.reference({
                bind: { landedCostItem: { allocationRuleUnit: true } },
                title: 'Landed cost  allocation unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                isHiddenOnMainField: true,
            }),
        },
    },

    onLoad() {
        if (!this.$.isServiceOptionEnabled('landedCostOption') || this.type.value !== 'landedCost') {
            this.landedCostBlock.isHidden = true;
        }
        this.managementSectionDependsOnBlocks.push(this.landedCostBlock);
        this.managementSection.isHidden = this.isManagementSectionHidden();

        const shouldDisable = this.type.value === 'landedCost' || this.type.value === 'service';
        this.toggleItemPropertiesBasedOnTypeExtension(shouldDisable);
    },
})
export class ItemExtension extends ui.PageExtension<Item, GraphApi> {
    @ui.decorators.block<ItemExtension>({
        parent() {
            return this.managementSection;
        },
        title: 'Landed costs',
        width: 'extra-large',
    })
    landedCostBlock: ui.containers.Block;

    @ui.decorators.dropdownListFieldOverride<ItemExtension>({
        onChangeAfter() {
            this.landedCostBlock.isHidden = this.type.value !== 'landedCost';
        },
    })
    type: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<ItemExtension>({
        parent() {
            return this.landedCostBlock;
        },
        title: 'Type',
        optionType: '@sage/xtrem-landed-cost/LandedCostType',
        isMandatory: true,
        bind: { landedCostItem: { landedCostType: true } },
    })
    landedCostType: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<ItemExtension>({
        parent() {
            return this.landedCostBlock;
        },
        title: 'Allocation rule',
        optionType: '@sage/xtrem-landed-cost/AllocationRule',
        isMandatory: true,
        bind: { landedCostItem: { allocationRule: true } },
        onChange() {
            this.allocationRuleUnit.value = null;
        },
    })
    allocationRule: ui.fields.DropdownList;

    @ui.decorators.referenceField<ItemExtension, UnitOfMeasureBinding>({
        parent() {
            return this.landedCostBlock;
        },
        title: 'Allocation unit',
        bind: { landedCostItem: { allocationRuleUnit: true } },
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        valueField: 'name',
        helperTextField: 'symbol',
        minLookupCharacters: 0,
        filter() {
            return this.allocationRule.value === 'byVolume' ? { type: { _eq: 'volume' } } : { type: { _eq: 'weight' } };
        },
        isMandatory() {
            return !!['byVolume', 'byWeight'].includes(this.allocationRule.value || '');
        },
        isHidden() {
            return !['byVolume', 'byWeight'].includes(this.allocationRule.value || '');
        },
        columns: [
            ui.nestedFields.text({
                title: 'Name',
                bind: 'name',
            }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.text({
                title: 'Symbol',
                bind: 'symbol',
            }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
        ],
    })
    allocationRuleUnit: ui.fields.Reference<UnitOfMeasureBinding>;
}

declare module '@sage/xtrem-master-data/build/lib/pages/item' {
    export interface Item extends ItemExtension {}
}
