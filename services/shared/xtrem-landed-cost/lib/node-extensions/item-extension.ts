import type { Reference } from '@sage/xtrem-core';
import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremLandedCost from '..';

@decorators.nodeExtension<ItemExtension>({
    extends: () => xtremMasterData.nodes.Item,
    async prepare() {
        if ((await this.type) === 'landedCost') {
            await this.$.set({ isStockManaged: false, isBought: true, isManufactured: false, isSold: false });
        }
    },
    async saveBegin() {
        if (
            (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.landedCostOption)) &&
            (await this.type) !== 'landedCost'
        ) {
            await this.$.set({
                landedCostItem: null,
            });
        }
    },
})
export class ItemExtension extends NodeExtension<xtremMasterData.nodes.Item> {
    @decorators.referenceProperty<ItemExtension, 'landedCostItem'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        isPublished: true,
        isVital: true,
        isNullable: true,
        reverseReference: 'item',
        node: () => xtremLandedCost.nodes.LandedCostItem,
    })
    readonly landedCostItem: Reference<xtremLandedCost.nodes.LandedCostItem | null>;

    @decorators.enumProperty<ItemExtension, 'landedCostAllocationRule'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremLandedCost.enums.allocationRuleDataType,
        async getValue() {
            return (await this.landedCostItem)?.allocationRule ?? null;
        },
    })
    readonly landedCostAllocationRule: Promise<xtremLandedCost.enums.AllocationRule | null>;

    @decorators.referenceProperty<ItemExtension, 'landedCostAllocationRuleUnit'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        async getValue() {
            return (await this.landedCostItem)?.allocationRuleUnit ?? null;
        },
    })
    readonly landedCostAllocationRuleUnit: Reference<xtremMasterData.nodes.UnitOfMeasure | null>;
}
declare module '@sage/xtrem-master-data/lib/nodes/item' {
    export interface Item extends ItemExtension {}
}
