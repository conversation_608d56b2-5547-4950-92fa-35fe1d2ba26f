import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

/**
 * Temporary document header node used in landed costs
 * Waiting for a BaseDocument node declared in master-data package
 */
@decorators.node<LandedCostBaseDocument>({
    storage: 'sql',
    isPublished: true,
    isAbstract: true,
})
export class LandedCostBaseDocument extends Node {
    @decorators.stringProperty<LandedCostBaseDocument, 'number'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.code,
    })
    readonly number: Promise<string>;
}
