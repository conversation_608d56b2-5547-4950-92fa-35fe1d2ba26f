import type { Reference, decimal } from '@sage/xtrem-core';
import { Node, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { lowerFirst } from 'lodash';
import * as xtremLandedCost from '..';

@decorators.node<LandedCostAllocation>({
    serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
    package: 'xtrem-landed-cost',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    isVitalCollectionChild: true,
    async controlBegin(cx) {
        const documentLine = await this.getAllocatedDocumentLine();

        xtremLandedCost.events.controls.controlAllocatedDocumentLine(cx, documentLine);

        await xtremLandedCost.events.controls.controlAllocatedItem(cx, await documentLine.item);
    },
})
export class LandedCostAllocation extends Node {
    /**
     * e.g. PurchaseInvoiceLine
     */
    @decorators.referenceProperty<LandedCostAllocation, 'line'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => xtremLandedCost.nodes.LandedCostDocumentLine,
    })
    readonly line: Reference<xtremLandedCost.nodes.LandedCostDocumentLine>;

    /**
     * e.g. PurchaseReceiptLine
     */
    @decorators.referenceProperty<LandedCostAllocation, 'allocatedDocumentLine'>({
        isPublished: true,
        isStored: true,
        node: () => xtremMasterData.nodes.BaseDocumentLine,
    })
    readonly allocatedDocumentLine: Reference<xtremMasterData.nodes.BaseDocumentLine>;

    async getAllocatedDocumentLine() {
        return (await this.allocatedDocumentLine) as xtremMasterData.nodes.BaseDocumentLine &
            xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum>;
    }

    @decorators.stringProperty<LandedCostAllocation, 'allocatedDocumentType'>({
        isPublished: true,
        async computeValue() {
            return lowerFirst((await (await this.getAllocatedDocumentLine()).document).$.factory.name);
        },
    })
    readonly allocatedDocumentType: Promise<string>;

    @decorators.referenceProperty<LandedCostAllocation, 'landedCostItem'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Item,
        async computeValue() {
            return (await (await this.line).getDocumentLine()).item;
        },
    })
    landedCostItem: Reference<xtremMasterData.nodes.Item>;

    @decorators.decimalProperty<LandedCostAllocation, 'costAmount'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
    })
    readonly costAmount: Promise<decimal>;

    @decorators.decimalProperty<LandedCostAllocation, 'costAmountInCompanyCurrency'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
    })
    readonly costAmountInCompanyCurrency: Promise<decimal>;

    @decorators.referenceProperty<LandedCostAllocation, 'item'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Item,
        async computeValue() {
            return (await this.getAllocatedDocumentLine()).item;
        },
    })
    readonly item: Promise<xtremMasterData.nodes.Item | null>;

    @decorators.decimalProperty<LandedCostAllocation, 'documentLineAmountInCompanyCurrency'>({
        isPublished: true,
        isStoredOutput: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        async defaultValue() {
            return (await this.getAllocatedDocumentLine()).amountForLandedCostAllocation;
        },
        updatedValue: useDefaultValue,
    })
    readonly documentLineAmountInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<LandedCostAllocation, 'documentLineQuantityInStockUnit'>({
        isPublished: true,
        isStoredOutput: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async defaultValue() {
            return (await this.getAllocatedDocumentLine()).quantityInStockUnitForLandedCostAllocation;
        },
        updatedValue: useDefaultValue,
    })
    readonly documentLineQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<LandedCostAllocation, 'documentLineWeight'>({
        isPublished: true,
        isStoredOutput: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async defaultValue() {
            const item: xtremMasterData.nodes.Item | null = await this.item;
            const line = await this.line;
            const rule = await line.allocationRule;
            const ruleUnit = await line.allocationRuleUnit;
            if (!item || rule !== 'byWeight' || !ruleUnit) {
                return 0;
            }

            return xtremMasterData.functions.convertFromTo(
                await item.weightUnit,
                ruleUnit,
                (await this.documentLineQuantityInStockUnit) * (await item.weight),
                false,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly documentLineWeight: Promise<decimal>;

    @decorators.decimalProperty<LandedCostAllocation, 'documentLineVolume'>({
        isPublished: true,
        isStoredOutput: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async defaultValue() {
            const item: xtremMasterData.nodes.Item | null = await this.item;
            const rule = await (await this.line).allocationRule;
            const ruleUnit = await (await this.line).allocationRuleUnit;
            if (!item || rule !== 'byVolume' || !ruleUnit) {
                return 0;
            }

            return xtremMasterData.functions.convertFromTo(
                await item.volumeUnit,
                ruleUnit,
                (await this.documentLineQuantityInStockUnit) * (await item.volume),
                false,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly documentLineVolume: Promise<decimal>;

    @decorators.referenceProperty<LandedCostAllocation, 'sourceAllocationLine'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => LandedCostAllocation,
    })
    readonly sourceAllocationLine: Reference<LandedCostAllocation | null>;

    @decorators.decimalProperty<LandedCostAllocation, 'creditedCostAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        async control(cx) {
            await xtremLandedCost.events.controls.controlCreditedAmount(cx, {
                amount: await this.costAmount,
                creditedAmount: await this.creditedCostAmount,
                sourceAllocationLine: await this.sourceAllocationLine,
            });
        },
    })
    readonly creditedCostAmount: Promise<decimal>;

    @decorators.decimalProperty<LandedCostAllocation, 'creditedCostAmountInCompanyCurrency'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        async control(cx) {
            await xtremLandedCost.events.controls.controlCreditedAmount(cx, {
                amount: await this.costAmountInCompanyCurrency,
                creditedAmount: await this.creditedCostAmountInCompanyCurrency,
                sourceAllocationLine: await this.sourceAllocationLine,
            });
        },
    })
    readonly creditedCostAmountInCompanyCurrency: Promise<decimal>;
}
