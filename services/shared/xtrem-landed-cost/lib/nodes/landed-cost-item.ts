import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremLandedCost from '../index';

@decorators.node<LandedCostItem>({
    package: 'xtrem-landed-cost',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    isVitalReferenceChild: true,
    serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
})
export class LandedCostItem extends Node {
    @decorators.referenceProperty<LandedCostItem, 'item'>({
        node: () => xtremMasterData.nodes.Item,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        lookupAccess: true,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.enumProperty<LandedCostItem, 'landedCostType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremLandedCost.enums.landedCostTypeDataType,
    })
    readonly landedCostType: Promise<xtremLandedCost.enums.LandedCostType>;

    @decorators.enumProperty<LandedCostItem, 'allocationRule'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremLandedCost.enums.allocationRuleDataType,
    })
    readonly allocationRule: Promise<xtremLandedCost.enums.AllocationRule>;

    @decorators.referenceProperty<LandedCostItem, 'allocationRuleUnit'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        filters: {
            control: {
                async type() {
                    const allocationRule = await this.allocationRule;
                    if (allocationRule === 'byWeight') {
                        return { _eq: 'weight' };
                    }
                    if (allocationRule === 'byVolume') {
                        return { _eq: 'volume' };
                    }
                    return {};
                },
            },
        },
        async control(cx, val) {
            if ((await this.allocationRule) === 'byVolume' && (await val?.type) !== 'volume') {
                cx.error.addLocalized(
                    '@sage/xtrem-landed-cost/nodes__landed_cost_item__not-a-volume-measure',
                    'The {{unitOfMeasure}} unit is not a unit of volume.',
                    {
                        unitOfMeasure: await val?.id,
                    },
                );
            }
            if ((await this.allocationRule) === 'byWeight' && (await val?.type) !== 'weight') {
                cx.error.addLocalized(
                    '@sage/xtrem-landed-cost/nodes__landed_cost_item__not-a-weight-measure',
                    'The {{unitOfMeasure}} unit is not a unit of weight.',
                    {
                        unitOfMeasure: await val?.id,
                    },
                );
            }
        },
    })
    readonly allocationRuleUnit: Reference<xtremMasterData.nodes.UnitOfMeasure | null>;
}
