import type { Reference, decimal } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { registerSqlFunction } from '@sage/xtrem-shared';
import * as xtremLandedCost from '..';

/**
 * Landed cost values assigned to a document line (Purchase order or Purchase receipt)
 */
@decorators.node<LandedCostLine>({
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    canDeleteMany: true,
    indexes: [
        {
            isUnique: true,
            orderBy: {
                documentLine: 1,
                landedCost: 1,
                landedCostAllocation: 1,
            },
        },
    ],
    serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
    async controlBegin(cx) {
        await xtremLandedCost.events.controls.controlConsistency(cx, this);
    },
})
export class LandedCostLine extends Node {
    /**
     * the document line for which the landed cost is assigned.
     * Can be a Purchase order or purchase receipt for example
     */
    @decorators.referenceProperty<LandedCostLine, 'documentLine'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => xtremMasterData.nodes.BaseDocumentLine,
    })
    readonly documentLine: Reference<xtremMasterData.nodes.BaseDocumentLine>;

    async getDocumentLine() {
        return (await this
            .documentLine) as xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum>;
    }

    @decorators.referenceProperty<LandedCostLine, 'landedCost'>({
        isPublished: true,
        isStored: true,
        filters: {
            control: {
                type: 'landedCost',
            },
        },
        node: () => xtremMasterData.nodes.Item,
    })
    readonly landedCost: Reference<xtremMasterData.nodes.Item>;

    @decorators.referenceProperty<LandedCostLine, 'landedCostAllocation'>({
        isPublished: true,
        isStored: true,
        node: () => xtremLandedCost.nodes.LandedCostAllocation,
    })
    readonly landedCostAllocation: Reference<xtremLandedCost.nodes.LandedCostAllocation>;

    // needed for rounding in cost amounts
    @decorators.referenceProperty<LandedCostLine, 'companyCurrency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        async computeValue() {
            return (await this.getDocumentLine()).companyCurrency;
        },
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency | null>;

    @decorators.decimalProperty<LandedCostLine, 'actualCostAmountInCompanyCurrency'>({
        isPublished: true,
        isStored: true,
        async control(cx, val) {
            await cx.error.if(val).is.less.than(0);
        },
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
    })
    readonly actualCostAmountInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<LandedCostLine, 'actualAllocatedCostAmountInCompanyCurrency'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['actualCostAmountInCompanyCurrency'],
        async control(cx, val) {
            await cx.error.if(val).is.less.than(0);
            await cx.error.if(val).is.greater.than(await this.actualCostAmountInCompanyCurrency);
        },
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
    })
    readonly actualAllocatedCostAmountInCompanyCurrency: Promise<decimal>;

    static getTotalActualCostAmountInCompanyCurrency(
        documentLine: xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum>,
    ): Promise<decimal> {
        return documentLine.landedCostLines.sum(line => line.actualCostAmountInCompanyCurrency);
    }
}

registerSqlFunction(
    'xtremLandedCost.nodes.LandedCostLine.getTotalActualCostAmountInCompanyCurrency',
    LandedCostLine.getTotalActualCostAmountInCompanyCurrency,
);
