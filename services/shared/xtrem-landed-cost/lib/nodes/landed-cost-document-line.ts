import type { Collection, Context, Reference, decimal, integer } from '@sage/xtrem-core';
import { BusinessRuleError, Logger, Node, asyncArray, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremLandedCost from '..';

const logger = Logger.getLogger(__filename, 'landed-cost');

/**
 * landed cost properties needed to assign landed costs to other documents
 */
@decorators.node<LandedCostDocumentLine>({
    package: 'xtrem-landed-cost',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    isVitalReferenceChild: true,
    indexes: [{ orderBy: { documentLine: +1 }, isUnique: true }],
    serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
    async controlBegin(cx) {
        const documentLine = await this.getDocumentLine();
        await xtremLandedCost.events.controls.controlItem(cx, await documentLine.item);

        await xtremLandedCost.events.controls.controlTotalAmountAllocated(
            cx,
            await this.totalAmountAllocated,
            await this.costAmountToAllocate,
            await documentLine.currency,
        );
    },
    async prepare() {
        if (!(await this.allocationRuleUnit) && ['byWeight', 'byVolume'].includes((await this.allocationRule) ?? '')) {
            const documentLine = await this.getDocumentLine();
            await this.$.set({
                allocationRuleUnit: await (await documentLine.item).landedCostAllocationRuleUnit,
            });
        }
        if (
            (await this.allocations.length) > 0 &&
            (await this.totalAmountAllocated) === (await this.costAmountToAllocate)
        ) {
            let max = await this.allocations.elementAt(0);
            const sumAllocatedInCompanyCurrency = await this.allocations.sum(async allocation => {
                const costAmountInCompanyCurrency = await allocation.costAmountInCompanyCurrency;
                if (costAmountInCompanyCurrency > (await max.costAmountInCompanyCurrency)) {
                    max = allocation;
                }
                return costAmountInCompanyCurrency;
            });
            const totalDifference = (await this.costAmountToAllocateInCompanyCurrency) - sumAllocatedInCompanyCurrency;
            if (totalDifference !== 0) {
                await max.$.set({
                    costAmountInCompanyCurrency: (await max.costAmountInCompanyCurrency) + totalDifference,
                });
            }
        }
    },
})
export class LandedCostDocumentLine extends Node {
    @decorators.referenceProperty<LandedCostDocumentLine, 'documentLine'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => xtremMasterData.nodes.BaseDocumentLine,
    })
    readonly documentLine: Reference<xtremMasterData.nodes.BaseDocumentLine>;

    async getDocumentLine() {
        return (await this.documentLine) as xtremLandedCost.interfaces.DocumentLineAllocatingLandedCost;
    }

    @decorators.decimalProperty<LandedCostDocumentLine, 'costAmountToAllocate'>({
        isPublished: true,
        dependsOn: ['documentLine'],
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        async computeValue() {
            const documentLine = await this.getDocumentLine();
            if ((await (await documentLine.item).type) !== 'landedCost') {
                return 0;
            }

            return documentLine.getLandedCostAmountToAllocate();
        },
    })
    readonly costAmountToAllocate: Promise<decimal>;

    @decorators.decimalProperty<LandedCostDocumentLine, 'costAmountToAllocateInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        dependsOn: ['costAmountToAllocate'],
        async computeValue() {
            const documentLine = await this.getDocumentLine();
            if ((await (await documentLine.item).type) !== 'landedCost') {
                return 0;
            }
            return documentLine.convertAmountInCompanyCurrency(await this.costAmountToAllocate);
        },
    })
    readonly costAmountToAllocateInCompanyCurrency: Promise<decimal>;

    @decorators.enumProperty<LandedCostDocumentLine, 'allocationRule'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremLandedCost.enums.allocationRuleDataType,

        async defaultValue() {
            const documentLine = await this.getDocumentLine();
            return (await documentLine.item).landedCostAllocationRule;
        },
    })
    readonly allocationRule: Promise<xtremLandedCost.enums.AllocationRule | null>;

    @decorators.referenceProperty<LandedCostDocumentLine, 'allocationRuleUnit'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,

        async defaultValue() {
            const documentLine = await this.getDocumentLine();
            return (await documentLine.item).landedCostAllocationRuleUnit;
        },
    })
    readonly allocationRuleUnit: Promise<xtremMasterData.nodes.UnitOfMeasure | null>;

    @decorators.enumProperty<LandedCostDocumentLine, 'allocationMethod'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremLandedCost.enums.LandedCostAllocationMethodDataType,
        defaultValue() {
            return 'automatic';
        },
    })
    readonly allocationMethod: Promise<xtremLandedCost.enums.LandedCostAllocationMethod | null>;

    @decorators.collectionProperty<LandedCostDocumentLine, 'allocations'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'line',
        node: () => xtremLandedCost.nodes.LandedCostAllocation,
    })
    readonly allocations: Collection<xtremLandedCost.nodes.LandedCostAllocation>;

    @decorators.decimalProperty<LandedCostDocumentLine, 'totalAmountAllocated'>({
        isPublished: true,
        dependsOn: ['costAmountToAllocate'],
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        getValue() {
            return this.allocations.sum(allocation => allocation.costAmount);
        },
    })
    readonly totalAmountAllocated: Promise<decimal>;

    @decorators.query<typeof LandedCostDocumentLine, 'queryAllocations'>({
        isPublished: true,
        parameters: [
            {
                name: 'landedCostDocumentLineId',
                type: 'string',
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    _id: 'integer',
                    allocatedDocumentType: 'string',
                    allocatedDocumentLine: {
                        type: 'object',
                        properties: {
                            _id: 'string',
                            documentNumber: 'string',
                            documentId: 'integer',
                        },
                    },
                    item: {
                        type: 'object',
                        properties: {
                            id: 'string',
                            name: 'string',
                            stockUnit: {
                                type: 'object',
                                properties: {
                                    id: 'string',
                                    symbol: 'string',
                                    decimalDigits: 'integer',
                                },
                            },
                        },
                    },
                    costAmount: 'decimal',
                    costAmountInCompanyCurrency: 'decimal',
                    documentLineAmountInCompanyCurrency: 'decimal',
                    documentLineQuantityInStockUnit: 'decimal',
                    documentLineWeight: 'decimal',
                    documentLineVolume: 'decimal',
                },
            },
            isMandatory: true,
        },
    })
    static async queryAllocations(
        context: Context,
        landedCostDocumentLineId: string,
    ): Promise<
        {
            _id: integer;
            allocatedDocumentType: string;
            allocatedDocumentLine: {
                _id: integer;
                documentNumber: string;
                documentId: integer;
            };
            item: {
                id: string;
                name: string;
                stockUnit: {
                    id: string;
                    symbol: string;
                    decimalDigits: integer;
                };
            };
            costAmount: decimal;
            costAmountInCompanyCurrency: decimal;
            documentLineAmountInCompanyCurrency: decimal;
            documentLineQuantityInStockUnit: decimal;
            documentLineWeight: decimal;
            documentLineVolume: decimal;
        }[]
    > {
        logger.verbose(() => `queryAllocations got ${landedCostDocumentLineId}`);
        const landedCostDocumentLine = await context.read(LandedCostDocumentLine, { _id: landedCostDocumentLineId });
        const res = await landedCostDocumentLine.allocations
            .map(async allocation => {
                const allocatedDocumentLine = await allocation.getAllocatedDocumentLine();
                const item = await allocatedDocumentLine.item;
                const stockUnit = await item.stockUnit;
                return {
                    _id: allocation._id,
                    allocatedDocumentType: await allocation.allocatedDocumentType,
                    allocatedDocumentLine: {
                        _id: allocatedDocumentLine._id,
                        documentNumber: await allocatedDocumentLine.documentNumber,
                        documentId: await allocatedDocumentLine.documentId,
                    },
                    item: {
                        id: await item.id,
                        name: await item.name,
                        stockUnit: {
                            id: await stockUnit.id,
                            symbol: await stockUnit.symbol,
                            decimalDigits: await stockUnit.decimalDigits,
                        },
                    },
                    costAmount: await allocation.costAmount,
                    costAmountInCompanyCurrency: await allocation.costAmountInCompanyCurrency,
                    documentLineAmountInCompanyCurrency: await allocation.documentLineAmountInCompanyCurrency,
                    documentLineQuantityInStockUnit: await allocation.documentLineQuantityInStockUnit,
                    documentLineWeight: await allocation.documentLineWeight,
                    documentLineVolume: await allocation.documentLineVolume,
                };
            })
            .toArray();
        logger.verbose(() => `queryAllocations -> ${JSON.stringify(res, null, 4)}`);
        return res;
    }

    @decorators.mutation<typeof LandedCostDocumentLine, 'updateAllocations'>({
        isPublished: true,
        parameters: [
            {
                isMandatory: true,
                name: 'allocationData',
                type: 'object',
                properties: {
                    landedCostDocumentLine: {
                        isMandatory: true,
                        isWritable: true,
                        type: 'reference',
                        node: () => xtremLandedCost.nodes.LandedCostDocumentLine,
                    },
                    allocationMethod: {
                        isMandatory: true,
                        type: 'enum',
                        dataType: () => xtremLandedCost.enums.LandedCostAllocationMethodDataType,
                    },
                    allocationUpdates: {
                        isMandatory: true,
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                _action: {
                                    isMandatory: true,
                                    type: 'enum',
                                    dataType: () => xtremLandedCost.enums.LandedCostAllocationUpdateActionDataType,
                                },
                                _id: {
                                    isMandatory: true,
                                    type: 'integer',
                                },
                                allocatedDocumentLine: 'integer',
                                allocatedAmount: 'decimal',
                                allocatedAmountInCompanyCurrency: 'decimal',
                            },
                        },
                    },
                },
            },
        ],
        return: 'boolean',
    })
    static async updateAllocations(
        context: Context,
        allocationData: {
            landedCostDocumentLine: Reference<xtremLandedCost.nodes.LandedCostDocumentLine>;
            allocationMethod: xtremLandedCost.enums.LandedCostAllocationMethod;
            allocationUpdates: xtremLandedCost.interfaces.DataForAllocationAction[];
        },
    ): Promise<boolean> {
        logger.verbose(() => `updateAllocations`);
        logger.verbose(() => `allocationData = ${JSON.stringify(allocationData, null, 4)}`);

        const landedCostDocumentLine = await allocationData.landedCostDocumentLine;

        await this.controlAllocations(context, landedCostDocumentLine, allocationData.allocationUpdates);

        if (allocationData.allocationUpdates.length) {
            await landedCostDocumentLine.$.set({
                allocationMethod: allocationData.allocationMethod,
                allocations: allocationData.allocationUpdates.map(allocationToUpdate => {
                    const commonData: { _action: 'create' | 'update' | 'delete'; _id: integer } = {
                        _action: allocationToUpdate._action,
                        _id: allocationToUpdate._id,
                    };
                    if (allocationToUpdate._action === 'delete') {
                        return commonData;
                    }
                    return {
                        ...commonData,
                        allocatedDocumentLine: allocationToUpdate.allocatedDocumentLine,
                        costAmount: allocationToUpdate.allocatedAmount,
                        costAmountInCompanyCurrency: allocationToUpdate.allocatedAmountInCompanyCurrency,
                    };
                }),
            });
            await landedCostDocumentLine.$.save();
        }
        return true;
    }

    static async controlAllocations(
        context: Context,
        landedCostDocumentLine: xtremLandedCost.nodes.LandedCostDocumentLine,
        allocationUpdates: xtremLandedCost.interfaces.DataForAllocationAction[],
    ) {
        await asyncArray(allocationUpdates).forEach(async allocationUpdate => {
            if (
                (allocationUpdate._id >= 0 && allocationUpdate._action === 'create') ||
                (allocationUpdate._id <= 0 && allocationUpdate._action !== 'create')
            ) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-landed-cost/nodes__landed_cost_document_line__inconsistency_id_action',
                        'Inconsistency detected between the allocation ({{allocationId}}) and the requested action ({{action}})',
                        { allocationId: allocationUpdate._id, action: allocationUpdate._action },
                    ),
                );
            }
            if (
                allocationUpdate._id > 0 &&
                (await landedCostDocumentLine.allocations.findIndex(
                    allocation => allocation._id === allocationUpdate._id,
                )) === -1
            ) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-landed-cost/nodes__landed_cost_document_line__inconsistency_id_document_line',
                        'The allocation ({{allocationId}}) does not exist for the document line {{lineId}}',
                        { allocationId: allocationUpdate._id, lineId: landedCostDocumentLine._id },
                    ),
                );
            }
        });
    }
}
