declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        BaseDocumentLine,
        BomRevisionSequence,
        Currency,
        Item,
        ItemAllergen,
        ItemAllergenBinding,
        ItemAllergenInput,
        ItemCategory,
        ItemClassifications,
        ItemClassificationsBinding,
        ItemClassificationsInput,
        ItemCustomer,
        ItemCustomerInput,
        ItemCustomerPrice,
        ItemCustomerPriceBinding,
        ItemCustomerPriceInput,
        ItemInput,
        ItemSite,
        ItemSiteBinding,
        ItemSiteInput,
        ItemSupplier,
        ItemSupplierInput,
        ItemSupplierPrice,
        ItemSupplierPriceBinding,
        ItemSupplierPriceInput,
        Package as SageXtremMasterData$Package,
        SequenceNumber,
        UnitOfMeasure,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type { Package as SageXtremStructure$Package } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type { Package as SageXtremSystem$Package, User } from '@sage/xtrem-system-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        GetDefaultsOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface AllocationRule$Enum {
        byAmount: 1;
        byQuantity: 2;
        byWeight: 3;
        byVolume: 4;
    }
    export type AllocationRule = keyof AllocationRule$Enum;
    export interface LandedCostAllocationMethod$Enum {
        automatic: 0;
        manual: 1;
    }
    export type LandedCostAllocationMethod = keyof LandedCostAllocationMethod$Enum;
    export interface LandedCostAllocationUpdateAction$Enum {
        create: 0;
        update: 1;
        delete: 2;
    }
    export type LandedCostAllocationUpdateAction = keyof LandedCostAllocationUpdateAction$Enum;
    export interface LandedCostDocumentType$Enum {
        transaction: 1;
        order: 2;
    }
    export type LandedCostDocumentType = keyof LandedCostDocumentType$Enum;
    export interface LandedCostType$Enum {
        freight: 1;
        customs: 2;
        duty: 3;
        tariffs: 4;
        taxes: 5;
        insurance: 6;
        administration: 7;
        handling: 8;
        processing: 9;
        transportation: 10;
        storage: 11;
        currencyConversion: 12;
        crating: 13;
        demurrage: 14;
        portCharges: 15;
        other: 16;
    }
    export type LandedCostType = keyof LandedCostType$Enum;
    export interface LandedCostAllocation extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        line: LandedCostDocumentLine;
        allocatedDocumentLine: BaseDocumentLine;
        allocatedDocumentType: string;
        landedCostItem: Item;
        costAmount: string;
        costAmountInCompanyCurrency: string;
        item: Item;
        documentLineAmountInCompanyCurrency: string;
        documentLineQuantityInStockUnit: string;
        documentLineWeight: string;
        documentLineVolume: string;
        sourceAllocationLine: LandedCostAllocation;
        creditedCostAmount: string;
        creditedCostAmountInCompanyCurrency: string;
    }
    export interface LandedCostAllocationInput extends VitalClientNodeInput {
        allocatedDocumentLine?: integer | string;
        costAmount?: decimal | string;
        costAmountInCompanyCurrency?: decimal | string;
        sourceAllocationLine?: integer | string;
        creditedCostAmount?: decimal | string;
        creditedCostAmountInCompanyCurrency?: decimal | string;
    }
    export interface LandedCostAllocationBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        line: LandedCostDocumentLine;
        allocatedDocumentLine: BaseDocumentLine;
        allocatedDocumentType: string;
        landedCostItem: Item;
        costAmount: string;
        costAmountInCompanyCurrency: string;
        item: Item;
        documentLineAmountInCompanyCurrency: string;
        documentLineQuantityInStockUnit: string;
        documentLineWeight: string;
        documentLineVolume: string;
        sourceAllocationLine: LandedCostAllocation;
        creditedCostAmount: string;
        creditedCostAmountInCompanyCurrency: string;
    }
    export interface LandedCostAllocation$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface LandedCostAllocation$Lookups {
        allocatedDocumentLine: QueryOperation<BaseDocumentLine>;
        landedCostItem: QueryOperation<Item>;
        item: QueryOperation<Item>;
        sourceAllocationLine: QueryOperation<LandedCostAllocation>;
    }
    export interface LandedCostAllocation$Operations {
        query: QueryOperation<LandedCostAllocation>;
        read: ReadOperation<LandedCostAllocation>;
        aggregate: {
            read: AggregateReadOperation<LandedCostAllocation>;
            query: AggregateQueryOperation<LandedCostAllocation>;
        };
        asyncOperations: LandedCostAllocation$AsyncOperations;
        lookups(dataOrId: string | { data: LandedCostAllocationInput }): LandedCostAllocation$Lookups;
        getDefaults: GetDefaultsOperation<LandedCostAllocation>;
    }
    export interface LandedCostBaseDocument extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
    }
    export interface LandedCostBaseDocumentInput extends ClientNodeInput {
        _constructor?: string;
        number?: string;
    }
    export interface LandedCostBaseDocumentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        number: string;
    }
    export interface LandedCostBaseDocument$Operations {}
    export interface LandedCostDocumentLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: BaseDocumentLine;
        costAmountToAllocate: string;
        costAmountToAllocateInCompanyCurrency: string;
        allocationRule: AllocationRule;
        allocationRuleUnit: UnitOfMeasure;
        allocationMethod: LandedCostAllocationMethod;
        allocations: ClientCollection<LandedCostAllocation>;
        totalAmountAllocated: string;
    }
    export interface LandedCostDocumentLineInput extends VitalClientNodeInput {
        allocationRule?: AllocationRule;
        allocationRuleUnit?: integer | string;
        allocationMethod?: LandedCostAllocationMethod;
        allocations?: Partial<LandedCostAllocationInput>[];
    }
    export interface LandedCostDocumentLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: BaseDocumentLine;
        costAmountToAllocate: string;
        costAmountToAllocateInCompanyCurrency: string;
        allocationRule: AllocationRule;
        allocationRuleUnit: UnitOfMeasure;
        allocationMethod: LandedCostAllocationMethod;
        allocations: ClientCollection<LandedCostAllocationBinding>;
        totalAmountAllocated: string;
    }
    export interface LandedCostDocumentLine$Queries {
        queryAllocations: Node$Operation<
            {
                landedCostDocumentLineId?: string;
            },
            {
                _id: integer;
                allocatedDocumentType: string;
                allocatedDocumentLine: {
                    _id: string;
                    documentNumber: string;
                    documentId: integer;
                };
                item: {
                    id: string;
                    name: string;
                    stockUnit: {
                        id: string;
                        symbol: string;
                        decimalDigits: integer;
                    };
                };
                costAmount: string;
                costAmountInCompanyCurrency: string;
                documentLineAmountInCompanyCurrency: string;
                documentLineQuantityInStockUnit: string;
                documentLineWeight: string;
                documentLineVolume: string;
            }[]
        >;
    }
    export interface LandedCostDocumentLine$Mutations {
        updateAllocations: Node$Operation<
            {
                allocationData: {
                    landedCostDocumentLine: integer | string;
                    allocationMethod: LandedCostAllocationMethod;
                    allocationUpdates: {
                        _action: LandedCostAllocationUpdateAction;
                        _id: integer | string;
                        allocatedDocumentLine?: integer | string;
                        allocatedAmount?: decimal | string;
                        allocatedAmountInCompanyCurrency?: decimal | string;
                    }[];
                };
            },
            boolean
        >;
    }
    export interface LandedCostDocumentLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface LandedCostDocumentLine$Lookups {
        allocationRuleUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface LandedCostDocumentLine$Operations {
        query: QueryOperation<LandedCostDocumentLine>;
        read: ReadOperation<LandedCostDocumentLine>;
        aggregate: {
            read: AggregateReadOperation<LandedCostDocumentLine>;
            query: AggregateQueryOperation<LandedCostDocumentLine>;
        };
        queries: LandedCostDocumentLine$Queries;
        mutations: LandedCostDocumentLine$Mutations;
        asyncOperations: LandedCostDocumentLine$AsyncOperations;
        lookups(dataOrId: string | { data: LandedCostDocumentLineInput }): LandedCostDocumentLine$Lookups;
        getDefaults: GetDefaultsOperation<LandedCostDocumentLine>;
    }
    export interface LandedCostItem extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: Item;
        landedCostType: LandedCostType;
        allocationRule: AllocationRule;
        allocationRuleUnit: UnitOfMeasure;
    }
    export interface LandedCostItemInput extends VitalClientNodeInput {
        landedCostType?: LandedCostType;
        allocationRule?: AllocationRule;
        allocationRuleUnit?: integer | string;
    }
    export interface LandedCostItemBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: Item;
        landedCostType: LandedCostType;
        allocationRule: AllocationRule;
        allocationRuleUnit: UnitOfMeasure;
    }
    export interface LandedCostItem$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface LandedCostItem$Lookups {
        allocationRuleUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface LandedCostItem$Operations {
        query: QueryOperation<LandedCostItem>;
        read: ReadOperation<LandedCostItem>;
        aggregate: {
            read: AggregateReadOperation<LandedCostItem>;
            query: AggregateQueryOperation<LandedCostItem>;
        };
        asyncOperations: LandedCostItem$AsyncOperations;
        lookups(dataOrId: string | { data: LandedCostItemInput }): LandedCostItem$Lookups;
        getDefaults: GetDefaultsOperation<LandedCostItem>;
    }
    export interface LandedCostLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: BaseDocumentLine;
        landedCost: Item;
        landedCostAllocation: LandedCostAllocation;
        companyCurrency: Currency;
        actualCostAmountInCompanyCurrency: string;
        actualAllocatedCostAmountInCompanyCurrency: string;
    }
    export interface LandedCostLineInput extends VitalClientNodeInput {
        landedCost?: integer | string;
        landedCostAllocation?: integer | string;
        actualCostAmountInCompanyCurrency?: decimal | string;
        actualAllocatedCostAmountInCompanyCurrency?: decimal | string;
    }
    export interface LandedCostLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: BaseDocumentLine;
        landedCost: Item;
        landedCostAllocation: LandedCostAllocation;
        companyCurrency: Currency;
        actualCostAmountInCompanyCurrency: string;
        actualAllocatedCostAmountInCompanyCurrency: string;
    }
    export interface LandedCostLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface LandedCostLine$Lookups {
        landedCost: QueryOperation<Item>;
        landedCostAllocation: QueryOperation<LandedCostAllocation>;
        companyCurrency: QueryOperation<Currency>;
    }
    export interface LandedCostLine$Operations {
        query: QueryOperation<LandedCostLine>;
        read: ReadOperation<LandedCostLine>;
        aggregate: {
            read: AggregateReadOperation<LandedCostLine>;
            query: AggregateQueryOperation<LandedCostLine>;
        };
        asyncOperations: LandedCostLine$AsyncOperations;
        lookups(dataOrId: string | { data: LandedCostLineInput }): LandedCostLine$Lookups;
        getDefaults: GetDefaultsOperation<LandedCostLine>;
    }
    export interface ItemExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergen>;
        classifications: ClientCollection<ItemClassifications>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPrice>;
        itemSites: ClientCollection<ItemSite>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPrice>;
        capacity: integer;
        landedCostItem: LandedCostItem;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
    }
    export interface ItemInputExtension {
        name?: string;
        description?: string;
        status?: ItemStatus;
        type?: ItemType;
        isBought?: boolean | string;
        isManufactured?: boolean | string;
        isSold?: boolean | string;
        eanNumber?: string;
        commodityCode?: string;
        stockUnit?: integer | string;
        volumeUnit?: integer | string;
        volume?: decimal | string;
        weightUnit?: integer | string;
        weight?: decimal | string;
        density?: decimal | string;
        image?: BinaryStream;
        allergens?: Partial<ItemAllergenInput>[];
        classifications?: Partial<ItemClassificationsInput>[];
        category?: integer | string;
        isStockManaged?: boolean | string;
        isPhantom?: boolean | string;
        isBomRevisionManaged?: boolean | string;
        bomRevisionSequenceNumber?: integer | string;
        isPotencyManagement?: boolean | string;
        isTraceabilityManagement?: boolean | string;
        supplierPrices?: Partial<ItemSupplierPriceInput>[];
        itemSites?: Partial<ItemSiteInput>[];
        suppliers?: Partial<ItemSupplierInput>[];
        customers?: Partial<ItemCustomerInput>[];
        purchaseUnit?: integer | string;
        purchaseUnitToStockUnitConversion?: decimal | string;
        serialNumberManagement?: SerialNumberManagement;
        serialNumberSequenceNumber?: integer | string;
        serialNumberUsage?: SerialNumberUsage;
        salesUnit?: integer | string;
        salesUnitToStockUnitConversion?: decimal | string;
        minimumSalesQuantity?: decimal | string;
        maximumSalesQuantity?: decimal | string;
        currency?: integer | string;
        basePrice?: decimal | string;
        minimumPrice?: decimal | string;
        customerPrices?: Partial<ItemCustomerPriceInput>[];
        capacity?: integer | string;
        landedCostItem?: LandedCostItemInput;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        id?: string;
        lotManagement?: LotManagement;
        lotSequenceNumber?: integer | string;
        isExpiryManaged?: boolean | string;
    }
    export interface ItemBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergenBinding>;
        classifications: ClientCollection<ItemClassificationsBinding>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPriceBinding>;
        itemSites: ClientCollection<ItemSiteBinding>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversion: string;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversion: string;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPriceBinding>;
        capacity: integer;
        landedCostItem: LandedCostItemBinding;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
    }
    export interface ItemExtension$Lookups {
        landedCostAllocationRuleUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface ItemExtension$Operations {
        lookups(dataOrId: string | { data: ItemInput }): ItemExtension$Lookups;
    }
    export interface Package {
        '@sage/xtrem-landed-cost/LandedCostAllocation': LandedCostAllocation$Operations;
        '@sage/xtrem-landed-cost/LandedCostBaseDocument': LandedCostBaseDocument$Operations;
        '@sage/xtrem-landed-cost/LandedCostDocumentLine': LandedCostDocumentLine$Operations;
        '@sage/xtrem-landed-cost/LandedCostItem': LandedCostItem$Operations;
        '@sage/xtrem-landed-cost/LandedCostLine': LandedCostLine$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremImportExport$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-landed-cost-api' {
    export type * from '@sage/xtrem-landed-cost-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-landed-cost-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type {
        ItemBindingExtension,
        ItemExtension,
        ItemExtension$Lookups,
        ItemExtension$Operations,
        ItemInputExtension,
    } from '@sage/xtrem-landed-cost-api';
    export interface Item extends ItemExtension {}
    export interface ItemBinding extends ItemBindingExtension {}
    export interface ItemInput extends ItemInputExtension {}
    export interface Item$Lookups extends ItemExtension$Lookups {}
    export interface Item$Operations extends ItemExtension$Operations {}
}
