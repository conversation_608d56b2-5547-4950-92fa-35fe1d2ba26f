import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, Uuid } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import type { RawAxiosRequestHeaders } from 'axios';
import { createHash } from 'crypto';
import type * as xtremSageNetwork from '../index.js';
import { RequestMain } from './request-main.js';

/**
 *  RequestApiV6 class for Sage network
 */
export class RequestApiV6 extends RequestMain {
    protected xExecutionID: string;

    protected requestHeader: string[] = [];

    /**
     * Default api is organisations else it will come from params.api
     */
    get api() {
        return this.params?.api ?? 'organisations';
    }

    /**
     * Constructor method by default is the GET
     * @param context
     * @param params default api is organisations
     */
    constructor(
        context: Context,
        public params?: xtremSageNetwork.interfaces.AuthenticationParams,
    ) {
        super(context, params);
        if (params?.executionId) {
            this.xExecutionID = params.executionId;
        } else {
            this.xExecutionID = Uuid.generate().toString('-');
        }
        this.uCompany = params?.company;
    }

    protected uCompany: xtremSystem.nodes.Company | undefined;

    get company(): Promise<xtremSystem.nodes.Company | undefined> {
        return (async () => {
            if (this.uCompany) {
                return this.uCompany;
            }

            this.uCompany = await this.context
                .query(xtremSystem.nodes.Company, {
                    filter: {
                        _and: [
                            // TODO: We shouldn't need to do this check for null, see ticket XT-88794.
                            { sageNetworkCompany: { _ne: null } },
                            { sageNetworkCompany: { companyId: { _ne: '' } } },
                        ],
                    },
                })
                .at(0);

            if (!this.uCompany) {
                this.logger.warn(() => ` No company created for the organisation`);
            }

            return this.uCompany;
        })();
    }

    async uploadData(
        file: xtremSageNetwork.interfaces.SageNetworkFileData,
    ): Promise<xtremSystem.interfaces.RequestGenericResponse<xtremSageNetwork.interfaces.UploadFileData>> {
        if (!file.content) {
            throw new BusinessRuleError(`UploadData: No data to upload`);
        }
        this.logger.debug(() => `requestAPIV6:uploadUrl start body ${JSON.stringify(file)}`);
        this.params = { api: 'other' };
        const url = `${this.v6ApiURL}v6/uploads`;
        this.parameters = {
            method: 'POST',
            withToken: true,
            url,
            data: {},
        };
        const uploadUrl = await this.execute<xtremSageNetwork.interfaces.UploadUrl>();
        this.logAxiosResponse('requestAPIV6:uploadUrl POST', uploadUrl);

        this.parameters = {
            method: 'PUT',
            url: uploadUrl.data.$url,
            data: file.content,
            noHeaders: true,
        };
        const uploadDataResult = await this.execute<string>();
        this.logAxiosResponse('requestAPIV6:uploadDataResult POST', uploadDataResult);
        return RequestApiV6.createUploadDataObject(file, uploadUrl);
    }

    async startReport(
        reportType: string,
        body: xtremSageNetwork.interfaces.StartReportData,
    ): Promise<xtremSystem.interfaces.RequestGenericResponse<string>> {
        this.logger.debug(() => `requestAPIV6:startReport start reportType ${reportType} body ${JSON.stringify(body)}`);
        this.params = { api: 'other' };
        const url = `${this.v6ApiURL}v6/report-types/${reportType}/reports`;
        this.parameters = {
            method: 'POST',
            withToken: true,
            url,
            data: body,
        };
        const startReportResult = await this.execute<string>();
        this.logAxiosResponse('requestAPIV6:startReport POST', startReportResult);
        return startReportResult;
    }

    async getOperationStatus(location: string) {
        this.logger.debug(() => `requestAPIV6:getOperationStatus start location ${location}`);

        this.params = { api: 'other' };
        this.parameters = {
            method: 'GET',
            withToken: true,
            url: location,
            data: '',
        };
        const operationStatusResult = await this.execute<xtremSageNetwork.interfaces.OperationStatus>();
        this.logAxiosResponse('requestAPIV6:getOperationStatus', operationStatusResult);
        return operationStatusResult;
    }

    async getReportStatus(reportType: string, guid: string) {
        this.logger.debug(() => `requestAPIV6:getReportStatus start reportType ${reportType} guid ${guid}`);
        const url = `${this.v6ApiURL}v6/report-types/${reportType}/operations/${guid}`;
        this.params = { api: 'other' };
        this.parameters = {
            method: 'GET',
            withToken: true,
            url,
            data: '',
        };
        const reportStatus = await this.execute<xtremSageNetwork.interfaces.ReportStatus>();
        this.logAxiosResponse('requestAPIV6:getReportStatus', reportStatus);

        return reportStatus;
    }

    async downloadResult(reportType: string, guid: string, fileId: string) {
        this.logger.debug(
            () => `requestAPIV6:downloadResult start reportType ${reportType} guid ${guid} fileId${fileId}`,
        );
        const url = `${this.v6ApiURL}v6/report-types/${reportType}/reports/${guid}/files/${fileId}`;
        this.params = { api: 'other' };
        this.parameters = {
            method: 'GET',
            withToken: true,
            url,
            data: '',
        };
        const downloadUrl = await this.execute<xtremSageNetwork.interfaces.DownloadUrl>();
        this.logAxiosResponse('requestAPIV6:getOperationStatus', downloadUrl);
        this.parameters = {
            method: 'GET',
            url: downloadUrl.data.$jwt,
            data: {},
            noHeaders: true,
        };
        const downloadResult = await this.execute<string>();
        this.logAxiosResponse('requestAPIV6:getOperationStatus', downloadResult);

        return downloadResult;
    }

    /** extend headers as it's not the same as authentication part  */
    override async getHeaders(): Promise<RawAxiosRequestHeaders> {
        const headers = await super.getHeaders();
        const company = await this.company;
        const user = `${this.context.tenantId}-${(await this.context.user)?.email}`;
        const activeInstance = await this.activeInstance;
        this.logger.debug(() => `requestv6:getHeaders company=${JSON.stringify(company)}`);
        const authenticationHeader = this.parameters?.withToken
            ? await this.getBearerAuthentication(company)
            : this.getSignedRequest();

        const allHeaders = {
            ...headers,
            ...authenticationHeader,
            'X-Request-ID': Uuid.generate().toString('-'),
            'X-Api-User-ID': createHash('md5').update(user).digest('hex'),
            'X-Application-Version': this.context.application.version,
            'X-Business-ID': await activeInstance.organizationId,
            'X-Company-ID': (await (await company?.sageNetworkCompany)?.companyId) ?? '',
            'X-Signatory': this.signatory,
            'X-Business-Country-Code': 'US',
            'X-Execution-ID': this.xExecutionID,
            'X-Application': this.xV6Application,
            'X-Api-Key': this.v6ApiKey,
        };
        this.logger.debug(() => `requestv6:getHeaders headers=${JSON.stringify(allHeaders)}`);
        return allHeaders;
    }

    static createUploadDataObject(
        file: xtremSageNetwork.interfaces.SageNetworkFileData,
        uploadUrl: xtremSystem.interfaces.RequestGenericResponse<xtremSageNetwork.interfaces.UploadUrl>,
    ) {
        const newUploadData: xtremSageNetwork.interfaces.UploadFileData = {
            id: uploadUrl.data.id,
            fileName: file.fileName,
            contentType: file.contentType,
            type: file.type,
        };

        return {
            ...uploadUrl,
            data: newUploadData,
        };
    }
}
