import type { Context, NodePayloadData } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import { loggers } from '../functions/loggers';
import type * as xtremSageNetwork from '../index';
import { RequestMain } from './request-main';

/**
 *  Authentication class for Sage network
 */
export class Authentication extends RequestMain {
    /**
     * Check if the organization is created & stored
     */
    get isOrganizationCreated(): Promise<boolean> {
        return (async () => {
            const activeInstance = await this.activeInstance;
            await loggers.authentication.debugAsync(
                async () =>
                    `authentication:isOrganizationCreated - isActive:${await activeInstance.isActive}, externalId:${await activeInstance.externalId}, primarySigningKey:${await activeInstance.primarySigningKey}`,
            );
            return (
                (await (await this.activeInstance).isActive) &&
                (await (await this.activeInstance).externalId) !== '' &&
                (await (await this.activeInstance).primarySigningKey) !== ''
            );
        })();
    }

    // Overide RequestMain get the authURL from organization ( xtrem-config.yml ) & the api
    override get url(): string {
        return `${this.authURL}/${this.api}`;
    }

    /**
     * Default api is organizations else it will come from params.api
     */
    get api() {
        return this.params?.api ?? 'organisations';
    }

    /**
     * Constructor method by default is the GET
     * @param context
     * @param params default api is organizations
     */
    constructor(
        context: Context,
        public params?: xtremSageNetwork.interfaces.AuthenticationParams,
    ) {
        super(context, { method: 'GET', withToken: false, ...params });
    }

    /** Update diagnoses if not active & no organization info set   */
    hasOrganizationBeenCreated(): Promise<boolean> {
        return this.isOrganizationCreated;
    }

    /**
     *  Create the organization ( 1 Organization = 1 Tenant )
     * @param organization
     * @returns
     */
    async createOrganization(
        organization?: xtremSageNetwork.interfaces.Organization,
    ): Promise<NodePayloadData<xtremSageNetwork.nodes.Organization>> {
        const organizationData = organization || (await (await this.activeInstance).organizationPayload);
        this.parameters = {
            method: 'POST',
            withToken: false,
            data: { ...organizationData, sageCRMId: organizationData.sageCrmId },
        };
        const createdOrganization = (await this.execute<xtremSageNetwork.interfaces.Organization>()).data;
        loggers.authentication.debug(
            () =>
                `authentication:createOrganization - ${createdOrganization.organisationId} - ${createdOrganization.externalId}`,
        );
        return createdOrganization;
    }

    /**
     *  Create the organization token
     *  TODO : Check if we can define a time there
     *  using organizationId & the signingKey received when create the organization
     * @returns token
     */
    async createOrganizationToken(): Promise<string> {
        await this.throwIfOrganizationNotCreated();

        this.params = { api: 'accesstoken' };
        this.parameters = {
            method: 'GET',
            withToken: false,
            additionalHeaders: { 'X-Organisation-Id': await (await this.activeInstance).organizationId },
            signingKey: await (await this.activeInstance).$.decryptValue('primarySigningKey'),
        };

        const result = await this.execute<{ jwt: string }>();
        loggers.authentication.debug(() => `authentication:createOrganizationToken - ${JSON.stringify(result.errors)}`);
        return result.data.jwt;
    }

    /**
     * Get the organization link to the token
     * @returns Organization or message error
     */
    async getOrganization(): Promise<xtremSageNetwork.interfaces.Organization> {
        await this.throwIfOrganizationNotCreated();
        this.params = { api: 'organisations' };
        this.parameters = {
            method: 'GET',
            withToken: true,
        };
        const organization = (await this.execute<xtremSageNetwork.interfaces.Organization>()).data;
        loggers.authentication.debug(
            () => `authentication:getOrganization - ${organization.organisationId} - ${organization.externalId}`,
        );
        return organization;
    }

    /**
     *  Create the company token
     *  TODO : Check if we can define a time there
     *  using company & the signingKey received when create the company
     * @returns token
     */
    async createCompanyToken(companyId: string): Promise<string> {
        await this.throwIfOrganizationNotCreated();

        this.params = { api: 'accesstoken' };
        this.parameters = {
            method: 'GET',
            withToken: false,
            additionalHeaders: {
                'X-Organization-Id': await (await this.activeInstance).organizationId,
                'X-Company-Id': companyId,
            },
            signingKey: await (await this.activeInstance).$.decryptValue('primarySigningKey'),
        };

        const result = await this.execute<{ jwt: string }>();
        if (result.status !== 200) {
            loggers.authentication.debug(() => `authentication:createCompanyToken - ${JSON.stringify(result.errors)}`);
        }
        return result.data.jwt;
    }

    /**
     * static - Create a company
     * @param company company payload
     * @returns Company info
     */
    async createCompany(company: xtremSageNetwork.interfaces.Company): Promise<xtremSageNetwork.interfaces.Company> {
        await this.throwIfOrganizationNotCreated();

        this.params = { api: 'companies' };
        this.parameters = {
            method: 'POST',
            withToken: true,
            data: company,
        };
        const result = await this.execute<xtremSageNetwork.interfaces.Company>();

        return result.data;
    }

    /** Get the list of the company of the organization */
    async getCompanies(): Promise<xtremSageNetwork.interfaces.Company[]> {
        await this.throwIfOrganizationNotCreated();

        this.params = { api: 'companies' };
        this.parameters = {
            method: 'GET',
            withToken: true,
        };

        const result = (await this.execute<xtremSageNetwork.interfaces.Company[]>()).data;

        this.throwIfDiagnose();

        return result;
    }

    /**
     * Get the info of a specific company
     * @param companyId company ID
     * @returns Company info
     */
    async getCompany(companyId: string): Promise<xtremSageNetwork.interfaces.Company[]> {
        this.params = { api: 'companies' };
        this.parameters = {
            method: 'GET',
            withToken: true,
            url: `${this.authURL}/companies/${companyId}`,
        };

        const company = (await this.execute<xtremSageNetwork.interfaces.Company[]>()).data;
        loggers.authentication.debug(() => `authentication:getCompany - ${JSON.stringify(company)}`);

        return company;
    }

    /**
     *  Update a company
     * @param company company payload
     * @returns
     */
    async updateCompany(company: xtremSageNetwork.interfaces.Company): Promise<xtremSageNetwork.interfaces.Company[]> {
        const data = xtremStructure.functions.getPathValue(company).map(line => {
            return {
                op: 'replace',
                ...line,
            };
        });

        this.parameters = {
            method: 'PATCH',
            withToken: true,
            url: `${this.authURL}/companies/${company.externalId}`,
            data,
        };

        const updatedCompany = (await this.execute<xtremSageNetwork.interfaces.Company[]>()).data;
        loggers.authentication.debug(() => `authentication:updateCompany - ${JSON.stringify(updatedCompany)}`);
        return updatedCompany;
    }

    async throwIfOrganizationNotCreated() {
        if (!(await this.hasOrganizationBeenCreated())) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-sage-network/organization-not-created',
                    'The Sage Network organization creation failed.',
                ),
            );
        }
    }
    /** START OF STATICS  */

    /**
     * static - Create the organization
     * @returns Organization
     */
    static async createOrganization(context: Context): Promise<NodePayloadData<xtremSageNetwork.nodes.Organization>> {
        const createOrg = await new Authentication(context).createOrganization();
        loggers.authentication.debug(() => `authentication:createOrganization - ${createOrg.organizationId}`);
        return createOrg;
    }

    /**
     * Get the token of the organization
     * @returns the token
     */
    static async getOrganizationToken(context: Context): Promise<string> {
        const token = await new Authentication(context).createOrganizationToken();
        loggers.authentication.debug(() => `authentication:getOrganizationToken - ${JSON.stringify(token)}`);

        return token;
    }

    /**
     * Get the current organization in case of error return a message
     * @returns
     */
    static async getOrganization(context: Context): Promise<xtremSageNetwork.interfaces.Organization> {
        const getOrg = await new Authentication(context).getOrganization();
        loggers.authentication.debug(() => `authentication:getOrganization - ${JSON.stringify(getOrg)}`);

        return getOrg;
    }

    /**
     * static - Create a company
     * @returns Company info
     */
    static async createCompany(
        context: Context,
        company: xtremSageNetwork.interfaces.Company,
    ): Promise<xtremSageNetwork.interfaces.Company> {
        const companyCreated = await new Authentication(context).createCompany(company);
        loggers.authentication.debug(() => `authentication:createCompany - ${companyCreated.companyId}`);

        return companyCreated;
    }

    /**
     * Get company list
     * @returns
     */
    static async getCompanies(context: Context): Promise<xtremSageNetwork.interfaces.Company[]> {
        const companies = await new Authentication(context).getCompanies();
        loggers.authentication.debug(() => `authentication:getCompanies - ${JSON.stringify(companies)}`);

        return companies;
    }

    /**
     * Get a company with companyId ( set with _id )
     * @returns
     */
    static async getCompany(context: Context, companyId: string): Promise<xtremSageNetwork.interfaces.Company[]> {
        const company = await new Authentication(context).getCompany(companyId);
        loggers.authentication.debug(() => `authentication:getCompany - ${JSON.stringify(company)}`);

        return company;
    }

    /**
     * Get a company with companyId ( set with _id )
     * @returns
     */
    static async updateCompany(
        context: Context,
        company: xtremSageNetwork.interfaces.Company,
    ): Promise<xtremSageNetwork.interfaces.Company[]> {
        const companyUpdate = await new Authentication(context).updateCompany(company);
        loggers.authentication.debug(() => `authentication:updateCompany - ${JSON.stringify(companyUpdate)}`);

        return companyUpdate;
    }

    /**
     * Get the token of the company
     * @returns the token
     */
    static async getCompanyToken(context: Context, companyId: string): Promise<string> {
        const token = await new Authentication(context).createCompanyToken(companyId);
        loggers.authentication.debug(() => `authentication:getCompanyToken - ${JSON.stringify(token)}`);

        return token;
    }
}
