import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, SystemError, Uuid } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import type { RawAxiosRequestHeaders } from 'axios';
import * as xtremSageNetwork from '..';
import { loggers } from '../functions/loggers';
import type { RequestMainConstructor } from '../interfaces';

/**
 * Class Request Main linked to the organization main instance
 */
export class RequestMain extends xtremSystem.classes.RequestGeneric {
    private static _config: xtremSageNetwork.interfaces.Package | null;

    private uActiveInstance: xtremSageNetwork.nodes.Organization;

    /** get the Organization default instance */
    get activeInstance() {
        if (this.uActiveInstance) {
            return this.uActiveInstance;
        }
        return this.activeInstanceRefresh();
    }

    async activeInstanceRefresh(): Promise<xtremSageNetwork.nodes.Organization> {
        const instance = await xtremSageNetwork.nodes.Organization.defaultInstance(
            this.context,
            this.parameters?.defaultOrganizationId,
        );
        if (!instance || !(await instance.isActive)) {
            throw new BusinessRuleError(
                this.context.localize('@sage/xtrem-sage-network/no-active-configuration', 'No active configuration'),
            );
        }
        this.uActiveInstance = instance;
        return this.uActiveInstance;
    }

    /**
     *  Get the xtrem-sage-network package configuration
     */
    getSageNetworkPackage() {
        const packageConfig =
            this.context.configuration.getPackageConfig<xtremSageNetwork.interfaces.FullPackage>(
                '@sage/xtrem-sage-network',
            );
        if (!packageConfig) {
            throw new SystemError('Missing package config @sage/xtrem-sage-network');
        }

        const isSandboxMode = packageConfig.apiMode === 'sandbox';
        RequestMain._config = {
            application: packageConfig.application,
            v6Application: packageConfig.v6Application,
            signingKey: packageConfig.signingKey,
            apiMode: packageConfig.apiMode,
            signatory: packageConfig.signatory,
            v6ApiKey: isSandboxMode ? packageConfig.v6ApiKeySandbox : packageConfig.v6ApiKeyProd,
            authURL: isSandboxMode ? packageConfig.authURLSandbox : packageConfig.authURLProd,
            apiURL: isSandboxMode ? packageConfig.apiURLSandbox : packageConfig.apiURLProd,
            lookupURL: isSandboxMode ? packageConfig.lookupURLSandbox : packageConfig.lookupURLProd,
            v6ApiURL: isSandboxMode ? packageConfig.v6ApiURLSandbox : packageConfig.v6ApiURLProd,
        };
    }

    /**
     * xApplication coming from configuration package, can be overloaded with parameters
     */
    get xApplication(): string {
        const xApplication = this.parameters?.application ?? RequestMain._config?.application;
        if (!xApplication) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-sage-network/no-application-api-configuration',
                    'No application for the Sage Network API in the configuration.',
                ),
            );
        }
        return xApplication;
    }

    /**
     * xV6Application coming from configuration package, can be overloaded with parameters
     */
    get xV6Application(): string {
        const xV6Application = this.parameters?.v6Application ?? RequestMain._config?.v6Application;
        if (!xV6Application) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-sage-network/no-v6-application-api-configuration',
                    'No application for the Sage Network V6 API in the configuration.',
                ),
            );
        }
        return xV6Application;
    }

    /**
     * signatory coming from configuration package, can be overloaded with parameters
     */
    get signatory(): string {
        const signatory = this.parameters?.signatory ?? RequestMain._config?.signatory;
        if (!signatory) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-sage-network/no-signatory-api-configuration',
                    'No signatory for the Sage Network API in the configuration.',
                ),
            );
        }
        return signatory;
    }

    /**
     * authentication url coming from configuration package, can be overloaded with parameters
     */
    get authURL(): string {
        const authUrl = this.parameters?.authURL ?? RequestMain._config?.authURL;
        if (!authUrl) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-sage-network/no-auth-url-api-configuration',
                    'No URL for the Sage Network authentication API in the configuration.',
                ),
            );
        }
        return authUrl;
    }

    /**
     * Api url coming from configuration package, can be overloaded with parameters
     */
    get apiURL(): string {
        const apiUrl = this.parameters?.apiURL ?? RequestMain._config?.apiURL;
        if (!apiUrl) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-sage-network/no-api-url-api-configuration',
                    'No URL for the Sage Network API in the configuration.',
                ),
            );
        }
        return apiUrl;
    }

    /**
     * SignKey coming from configuration package, can be overloaded with parameters
     */
    get apiMode(): string {
        const apiMode = this.parameters?.apiMode ?? RequestMain._config?.apiMode;
        if (!apiMode) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-sage-network/no-api-mode-api-configuration',
                    'No API mode for the Sage Network API in the configuration.',
                ),
            );
        }
        return apiMode;
    }

    /**
     * lookup id url coming from configuration package, can be overloaded with parameters
     */
    get lookupURL(): string {
        const lookupURL = this.parameters?.lookupURL ?? RequestMain._config?.lookupURL;
        if (!lookupURL) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-sage-network/no-lookup-url-api-configuration',
                    'No URL for the Sage Network lookup API in the configuration.',
                ),
            );
        }
        return lookupURL;
    }

    /**
     * v6 api url coming from configuration package, can be overloaded with parameters
     */
    get v6ApiURL(): string {
        const v6ApiUrl = this.parameters?.v6ApiURL ?? RequestMain._config?.v6ApiURL;
        if (!v6ApiUrl) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-sage-network/no-v6-api-url-api-configuration',
                    'No URL for the Sage Network V6 API in the configuration.',
                ),
            );
        }
        return v6ApiUrl;
    }

    /**
     * v6 api key coming from configuration package, can be overloaded with parameters
     */
    get v6ApiKey(): string {
        const v6ApiKey = this.parameters?.v6ApiKey ?? RequestMain._config?.v6ApiKey;
        if (!v6ApiKey) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-sage-network/no-v6-api-key-api-configuration',
                    'No API key for the Sage Network V6 API in the configuration.',
                ),
            );
        }
        return v6ApiKey;
    }

    /**
     * SignKey coming from configuration package, can be overloaded with parameters
     */
    get signKey(): string {
        const signKey = this.parameters?.signingKey ?? RequestMain._config?.signingKey;
        if (!signKey) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-sage-network/no-signing-key-api-configuration',
                    'No signing key for the Sage Network API in the configuration.',
                ),
            );
        }
        return signKey;
    }

    /**
     * XNonce coming from instance or default to generated uuid
     */
    get xNonce(): string {
        return this.parameters?.xNonceUuid ?? Uuid.generate().toString();
    }

    /**
     * Algorithm to use (sha1 or sha256) when creating the signature
     */
    // get algorithm() {
    //     return this.parameters?.algorithm ?? undefined;
    // }

    /**
     * Are we using notifications or polling
     */
    get notificationsEnabled() {
        return this.parameters?.notificationsEnabled || false;
    }

    /**
     *
     * @param context
     * @param parameters if no signingKey get the signingKey of the config file
     */
    constructor(
        public readonly context: Context,
        public override parameters?: RequestMainConstructor,
    ) {
        super({
            ...parameters,
            logger: parameters?.logger ?? loggers.requestMain,
        });
        if (!RequestMain._config) {
            this.getSageNetworkPackage();
        }
        this.headers = {
            ...this.headers,
            ...parameters?.additionalHeaders,
        };
    }

    /**
     *  Sign the request with the sign key
     * @returns Signature of the request & Nonce uuid
     */
    getSignedRequest(): { 'X-Signature': string; 'X-Nonce': string } {
        /** Throw if no signingKey  */
        xtremSageNetwork.functions.authenticationFunctions.checkSigningKey(this.context, this.signKey);

        const bodyAndParameters = xtremSageNetwork.functions.authenticationFunctions.bodyAndParametersEncode(
            this.urlParameters,
            this.data,
        );

        const baseString = {
            endpointUrl: this.url,
            method: this.method,
            bodyAndParameters,
            xNonceUuid: this.xNonce,
        };
        this.logger.debug(() => `RequestMain:getSignedRequest baseString=${JSON.stringify(baseString)}`);
        this.logger.debug(() => `RequestMain:getSignedRequest signKey=${JSON.stringify(this.signKey)}`);
        const xSignature = xtremSageNetwork.functions.authenticationFunctions.generateXSignature({
            baseString,
            signingKey: this.signKey,
        });
        this.authHeader.push('X-Nonce', 'X-Signature');
        return { 'X-Signature': xSignature.xSignature, 'X-Nonce': xSignature.xNonceUuid };
    }

    /**
     *  if the organization token isn't valid  return the new saved one else decrypt & returns
     * @returns
     */
    private async getOrganizationToken(): Promise<string> {
        if (!(await (await this.activeInstance).isTokenValid)) {
            const token = await xtremSageNetwork.nodes.Organization.saveToken(this.context);
            // Need to clear the cache
            await this.activeInstanceRefresh();
            return token;
        }
        return (await this.activeInstance).$.decryptValue('token');
    }

    /**
     *  if the organization token isn't valid  return the new saved one else decrypt & returns
     * @returns
     */
    private async getCompanyToken(company: xtremSystem.nodes.Company): Promise<string> {
        if (!(await (await company.sageNetworkCompany)?.isTokenValid)) {
            return xtremSageNetwork.nodes.SageNetworkCompany.saveToken(this.context, company);
        }
        const sageNetworkCompany = await company.sageNetworkCompany;
        return sageNetworkCompany?.$.decryptValue('token') ?? '';
    }

    /**
     *  Search the token  & return Authorization Bearer
     * if the token isn't valid & the context is writable , renew the token
     * @returns
     */
    async getBearerAuthentication(company?: xtremSystem.nodes.Company): Promise<{ Authorization: string }> {
        if (company) {
            return {
                Authorization: `Bearer ${await this.getCompanyToken(company)}`,
            };
        }
        return {
            Authorization: `Bearer ${await this.getOrganizationToken()}`,
        };
    }

    /**
     *  Get the authentication header ( Bearer or Sign )
     *  add the X-Application
     * @returns
     */
    async getHeaders(): Promise<RawAxiosRequestHeaders> {
        this.logger.debug(() => `RequestMain:getHeaders`);
        const authenticationHeader = this.parameters?.withToken
            ? await this.getBearerAuthentication()
            : this.getSignedRequest();
        this.logger.debug(() => `RequestMain:getHeaders authenticationHeader=${JSON.stringify(authenticationHeader)}`);

        return {
            ...this.headers,
            'X-Application': this.xApplication,
            ...authenticationHeader,
            ...this.parameters?.additionalHeaders,
        };
    }

    /**
     *  Overloading generic request
     * generateXSignature  & Execute axios call
     * @returns
     */
    override async execute<T extends {}>(): Promise<xtremSystem.interfaces.RequestGenericResponse<T>> {
        this.logger.debug(() => `RequestMain:execute`);
        if (this.method !== 'PUT') {
            this.headers = await this.getHeaders();
        }

        this.logger.debug(() => `RequestMain:execute request.method=${JSON.stringify(this.method)}`);
        this.logger.debug(() => `RequestMain:execute request.headers=${JSON.stringify(this.headers)}`);

        const result = await super.execute<T>();

        // Will only output debug if not status 202 (Accepted) as we will have to call again
        this.logAxiosResponse('RequestMain:execute', result);

        // If there are errors returned then we need to throw an error
        if (result.errors.length) {
            throw new BusinessRuleError(RequestMain.getErrorMessage(result.errors));
        }
        return result;
    }

    /**
     * Throw an BusinessRuleError if diagnose is filled
     */
    throwIfDiagnose() {
        if (this.context.diagnoses.length) {
            throw new BusinessRuleError(this.context.diagnoses.map(diag => `${diag.message}`).join(' \n '));
        }
    }

    static getErrorMessage(errors: xtremSystem.interfaces.RequestGenericError[]): string {
        return errors
            .map(error => {
                let errorProperty = '';
                if (error.property) {
                    errorProperty = `(${error.property})-`;
                }
                return `${errorProperty}${error.description}`;
            })
            .join(' \n ');
    }

    static resetConfig() {
        RequestMain._config = null;
    }
}
