import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, Uuid } from '@sage/xtrem-core';
import * as crypto from 'crypto';
import type { BaseString, GenerateXsignature, XsignatureAndUuid } from '../interfaces';
import { loggers } from './loggers';

/** Following how to generate an X-Signature :
 *   https://developers.sage.com/payments-out/guides/generate-x-signature/
 * */

/**
 *  Encoding parameters & body
 * @param parameters
 * @param body
 * @returns Use this value in the base string instead of the {JSON body and path parameters} placeholder.
 */
export function bodyAndParametersEncode(urlParameters: string, body?: any): string {
    let stringToEncode = urlParameters ? `${encodeURIComponent(urlParameters)}&` : '';
    if (body) {
        /** Encode JSON body in Base64:  equivalent to BTOA */
        const encodedBody = `body=${encodeURIComponent(Buffer.from(JSON.stringify(body), 'binary').toString('base64'))}`;
        stringToEncode = `${stringToEncode}${encodedBody}`;
    }
    loggers.authentication.debug(() => `bodyAndParametersEncode : ${stringToEncode}`);
    return stringToEncode;
}

/**
 *
 * @param method // is the HTTP method used in the request for which you generate X-Signature.
 * @param enpointUrl // is the endpoint URL used in the request, percent-encoded.
 * @param xNonceUuid // is the value you use in the X-Nonce header of your request. This is an arbitrary unique value such as a GUID, percent-encoded.
 * @param bodyAndParameters // JSON body and path parameters
 */
export function getBaseString(base: BaseString) {
    const xNonceUuid = base.xNonceUuid ?? '';
    return `${base.method.toUpperCase()}&${encodeURIComponent(base.endpointUrl)}&${encodeURIComponent(
        base.bodyAndParameters ?? '',
    )}&${encodeURIComponent(xNonceUuid)}`;
}

export function encryptXSignature(baseString: string, signingKey: string) {
    /**
     * var hash = CryptoJS.HmacSHA1(baseString, signingKey + '&null');
     * var signature = CryptoJS.enc.Base64.stringify(hash);
     *  */
    // TODO: Should really use sha256, but sha1 is a requirement from the authentication service
    // SageId team manages the authentication service and have been asked about updating this to sha256
    return crypto.createHmac('sha1', `${signingKey}&null`).update(baseString).digest('base64');
}

export function generateXSignature(params: GenerateXsignature): XsignatureAndUuid {
    const uuid = params.baseString.xNonceUuid || Uuid.generate().toString();
    const baseString = getBaseString({ ...params.baseString, xNonceUuid: uuid });
    loggers.authentication.debug(
        () => `Base string to encrypt : ${baseString} \n signingKey:${params.signingKey} \n uuid:${uuid}`,
    );

    return { xSignature: encryptXSignature(baseString, params.signingKey), xNonceUuid: uuid };
}

/**
 *  Throw BusinessRuleError if no signing key
 * @param context
 * @param signingKey
 */
export function checkSigningKey(context: Context, signingKey?: string) {
    if (!signingKey || signingKey.length <= 0) {
        throw new BusinessRuleError(context.localize('@sage/xtrem-sage-network/no-sigining-key', 'No signing key.'));
    }
}
