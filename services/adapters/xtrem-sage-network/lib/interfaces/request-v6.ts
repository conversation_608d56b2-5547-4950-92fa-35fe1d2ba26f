export interface UploadUrl {
    $title: string;
    $updated: string;
    $url: string;
    id: string;
    location: string;
}

export interface UploadFileData {
    id: string;
    fileName: string;
    contentType: string;
    type: string;
}

export interface SageNetworkFileData {
    fileId: string;
    fileName: string;
    contentType: string;
    type: string;
    content?: string;
}

export interface SageNetworkFilter {
    field: string;
    operator: string;
    value: string;
}

export interface StartReportData {
    context: {
        files: UploadFileData[];
        notification?: {
            url: string;
            method: string;
        };
    };
}

export interface DownloadUrl {
    $jwt: string;
}

export interface OperationStatus {
    stage: string;
    expiresAt: string;
    metadata: {
        reportType: string;
        reportVersion: string;
        reportInternalId: string;
    };
    files: [
        {
            fileType: string;
            contentType: string;
            fileName: string;
            url: string;
            eTag: string;
        },
    ];
}

export interface ReportStatus extends OperationStatus {
    currentLayoutId: string;
    reportDataChecksum: string;
    newUISessionUrl: string;
    dataRootUrl: string;
    currentLayoutDataUrl: string;
    currentLayoutDataChecksum: string;
    actionsRootUrl: string;
}
