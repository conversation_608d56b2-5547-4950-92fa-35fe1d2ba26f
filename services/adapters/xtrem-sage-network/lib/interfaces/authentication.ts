import type { Method } from 'axios';

export interface BaseString {
    method: Method;
    endpointUrl: string;
    bodyAndParameters?: string;
    xNonceUuid?: string;
}

export interface GenerateXsignature {
    baseString: BaseString;
    signingKey: string;
    uuid?: string;
}

export interface XsignatureAndUuid {
    xSignature: string;
    xNonceUuid: string;
}

export interface Organization {
    name: string;
    sageCrmId: string;
    primaryCountry: string;
    adminEmail: string;
    defaultLanguage: string;
    externalId: string;
    /** return by the create Organization */
    primarySigningKey?: string;
    /** return by the create Organization */
    organisationId?: string;
}

export interface Address {
    addressLine1: string;
    addressLine2: string;
    addressLine3: string;
    addressLine4: string;
    countrySubdivision: string;
    postalCode: string;
    country: string;
}

export interface Company {
    name: string;
    /** External Company ID needed to create on SageNetwork */
    externalId: string;
    /** Company ID Coming from SageNetwork when created  */
    companyId?: string;
    taxNumber: string;
    standardIndustrialCode: string;
    contactTelNo: string;
    contactEmail: string;
    address: Address;
    logoUrl: string;
    organizationId: string;
}
