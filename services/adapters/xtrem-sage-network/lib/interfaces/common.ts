import type * as xtremSystem from '@sage/xtrem-system';

type SignatoryType = 'sagepay' | 'sage_global_services' | 'sage_test' | 'sage-intacct' | 'sage-accounts-uk';
type ApiType = 'organisations' | 'companies' | 'accesstoken' | 'other';

export interface Package {
    application: string;
    signingKey: string;
    authURL: string;
    apiURL: string;
    signatory: string;
    /** For id-validation-look-up */
    lookupURL: string;
    /** Current mode */
    apiMode: 'production' | 'sandbox';
    /** For v6 compliance services */
    v6ApiURL: string;
    v6Application: string;
    v6ApiKey: string;
}

export interface FullPackage extends Package {
    v6ApiKeySandbox: string;
    v6ApiKeyProd: string;
    /** All urls for testing and production */
    authURLSandbox: string;
    authURLProd: string;
    apiURLSandbox: string;
    apiURLProd: string;
    lookupURLSandbox: string;
    lookupURLProd: string;
    v6ApiURLProd: string;
    v6ApiURLSandbox: string;
}

export interface HeaderParametersCommon {
    'Content-Type': 'application/json';
    /** Constant string identifying calling application */
    'X-Signatory': SignatoryType;
    /** The username or internal application ID that the customer used to sign in to the client machine  */
    'X-Api-User-ID': string;
    /** Organization/ business/ desktop installation unique identifier. Represents an authorised installation of a Sage product. */
    'X-Business-ID': string;
    /** Company/ dataset unique identifier in the source system.
     * A Company is an entity that issues invoices, receives payments etc.
     * A Sage installation can be used for multiple companies. */
    'X-Company-ID': string;
    /** Name of the connecting application */
    'X-Application': string;
    /** Application version(s) – comma separated app modules' codes/versions. Used for troubleshooting and reporting. */
    'X-Application-Version': string;

    /** This is a unique token (such as a GUID) that your application should generate for each request. */
    'X-Request-ID': string;
}

export interface RequestConstructor extends xtremSystem.interfaces.RequestGenericConstructor {
    additionalHeaders?: any;
    withToken?: boolean;
    signingKey?: string;
}

export interface RequestMainConstructor extends RequestConstructor {
    algorithm?: string;
    xNonceUuid?: string;
    notificationsEnabled?: boolean;
    application?: string;
    signingKey?: string;
    authURL?: string;
    apiURL?: string;
    signatory?: string;
    lookupURL?: string;
    apiMode?: 'production' | 'sandbox';
    v6ApiURL?: string;
    v6Application?: string;
    v6ApiKey?: string;
    defaultOrganizationId?: string;
}

export interface AuthenticationParams {
    api: ApiType;
    url?: string;
    company?: xtremSystem.nodes.Company;
    executionId?: string;
    defaultOrganizationId?: string;
}
