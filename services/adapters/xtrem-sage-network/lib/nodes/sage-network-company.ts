import type { Context, Reference } from '@sage/xtrem-core';
import { Node, Uuid, datetime, decorators } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { loggers } from '../functions/loggers';
import * as xtremSageNetwork from '../index.js';

@decorators.node<SageNetworkCompany>({
    isPublished: true,
    isVitalReferenceChild: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    storage: 'sql',
    serviceOptions: () => [xtremSageNetwork.serviceOptions.sageNetworkOption],
})
export class SageNetworkCompany extends Node {
    @decorators.referenceProperty<SageNetworkCompany, 'company'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremSystem.nodes.Company,
    })
    readonly company: Reference<xtremSystem.nodes.Company>;

    /**
     * External ID of a company for sageNetwork
     */
    @decorators.stringProperty<SageNetworkCompany, 'externalId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.uuid,
    })
    readonly externalId: Promise<string>;

    /**
     * CompanyID comming from sageNetwork when creating
     */
    @decorators.stringProperty<SageNetworkCompany, 'companyId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.uuid,
    })
    readonly companyId: Promise<string>;

    /**
     *  Payload to create an organization
     */
    @decorators.jsonProperty<SageNetworkCompany, 'companyPayload'>({
        isPublished: true,
        async computeValue() {
            const company = await this.company;
            return SageNetworkCompany.getCompanyPayload(company, await this.externalId);
        },
    })
    readonly companyPayload: Promise<xtremSageNetwork.interfaces.Company>;

    /**
     *  deliver by getOrganizationToken
     */
    @decorators.stringProperty<SageNetworkCompany, 'token'>({
        isStored: true,
        isPublished: true,
        isStoredEncrypted: true,
        dataType: () => xtremSystem.dataTypes.description,
        exportValue: '',
    })
    readonly token: Promise<string>;

    /**
     *  Creation of the token
     */
    @decorators.datetimeProperty<SageNetworkCompany, 'tokenCreation'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly tokenCreation: Promise<datetime | null>;

    /**
     *  is the company created in the Sage Network ?
     */
    @decorators.booleanProperty<SageNetworkCompany, 'isCreated'>({
        isPublished: true,
        async getValue() {
            return (await this.externalId) !== '';
        },
    })
    readonly isCreated: Promise<boolean>;

    /**
     *  is the token valid ?
     *  Token lifetime is 20 min
     */
    @decorators.booleanProperty<SageNetworkCompany, 'isTokenValid'>({
        isPublished: true,
        async computeValue() {
            await loggers.org.debugAsync(
                async () =>
                    `token end of life : ${
                        datetime.now().compare((await this.tokenCreation) || datetime.now().addMinutes(-20)) / 60000
                    }mins `,
            );

            const tokenCreation = await this.tokenCreation;
            if (!tokenCreation) {
                return false;
            }

            return !!tokenCreation && datetime.now().compare(tokenCreation) < 1200000;
        },
    })
    readonly isTokenValid: Promise<boolean>;

    /**
     * Get the token from sagesageNetwork & Save the token to be able to use it
     * @param context
     * @returns
     */
    @decorators.mutation<typeof SageNetworkCompany, 'saveToken'>({
        isPublished: true,
        parameters: [
            {
                name: 'company',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => xtremSystem.nodes.Company,
            },
        ],
        return: 'string',
    })
    static async saveToken(context: Context, company: xtremSystem.nodes.Company): Promise<string> {
        const token = await xtremSageNetwork.classes.Authentication.getCompanyToken(
            context,
            (await (await company.sageNetworkCompany)?.companyId) ?? '',
        );

        if (!company.$.isEffectivelyReadonly) {
            await company.$.set({
                sageNetworkCompany: { token, tokenCreation: datetime.now() },
            });
            await company.$.save();
        }

        return token;
    }

    /**
     * Call to create Company / Authentication Class
     * @param context
     * @returns
     */
    @decorators.mutation<typeof SageNetworkCompany, 'createCompany'>({
        isPublished: true,
        parameters: [{ name: 'id', type: 'string' }],
        return: 'boolean',
    })
    static async createCompany(context: Context, id: string): Promise<boolean> {
        const company = await context.read(xtremSystem.nodes.Company, { id }, { forUpdate: true });
        const sageNetworkCompany = await company.sageNetworkCompany;
        let companyPayload;
        if (sageNetworkCompany) {
            companyPayload = await sageNetworkCompany.companyPayload;
        } else {
            companyPayload = await SageNetworkCompany.getCompanyPayload(company, '');
        }
        const companyCreated = await xtremSageNetwork.classes.Authentication.createCompany(context, companyPayload);
        await loggers.org.debugAsync(
            async () =>
                `createSageNetworkCompany companySFEID=${await (
                    await company.sageNetworkCompany
                )?.companyId}, externalId=${companyCreated.externalId}`,
        );
        if ((await (await company.sageNetworkCompany)?.externalId) !== companyCreated.externalId) {
            await company.$.set({ sageNetworkCompany: { externalId: companyCreated.externalId } });
        }
        loggers.org.debug(() => `createSageNetworkCompany companyId=${companyCreated.companyId}`);
        await company.$.set({ sageNetworkCompany: { companyId: companyCreated.companyId } });
        const result = await company.$.trySave();
        loggers.org.debug(
            () =>
                `createSageNetworkCompany company saved=${result} diagnoses=${JSON.stringify(
                    company.$.context.diagnoses,
                )}`,
        );
        return result;
    }

    static async getCompanyPayload(
        company: xtremSystem.nodes.Company,
        externalId: string,
    ): Promise<xtremSageNetwork.interfaces.Company> {
        return {
            name: await company.name,
            externalId: externalId !== '' ? externalId : Uuid.generate().toString('-'),
            taxNumber: (await (await company.financialSite)?.taxIdNumber) ?? '',
            standardIndustrialCode: '',
            contactTelNo: (await (await company.primaryContact)?.locationPhoneNumber) ?? '',
            contactEmail: (await (await company.primaryContact)?.email) ?? '',
            logoUrl: '',
            organizationId: '',
            address: {
                addressLine1: (await (await company.primaryAddress)?.addressLine1) ?? '',
                addressLine2: (await (await company.primaryAddress)?.addressLine2) ?? '',
                addressLine3: '',
                addressLine4: '',
                countrySubdivision: (await (await company.primaryAddress)?.region) ?? '',
                postalCode: (await (await company.primaryAddress)?.postcode) ?? '',
                country: (await (await (await company.primaryAddress)?.country)?.iso31661Alpha3) ?? '',
            },
        };
    }
}
