import type { Collection, Context } from '@sage/xtrem-core';
import { Node, Test, Uuid, datetime, decorators } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { dataTypes } from '@sage/xtrem-system';
import { loggers } from '../functions/loggers';
import * as xtremSageNetwork from '../index.js';

@decorators.node<Organization>({
    package: 'xtrem-sage-network',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
    serviceOptions: () => [xtremSageNetwork.serviceOptions.sageNetworkOption],
})
export class Organization extends Node {
    /** Get the tenant name from SysTenant info  */
    static async getSystenantName(context: Context): Promise<string> {
        return (
            (await (await context.tryRead(xtremSystem.nodes.SysTenant, { tenantId: context.tenantId }))?.name) ??
            Test.defaultTenantName
        );
    }

    @decorators.stringProperty<Organization, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
    })
    readonly id: Promise<string>;

    @decorators.booleanProperty<Organization, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        provides: ['isActive'],
        exportValue: false,
    })
    readonly isActive: Promise<boolean>;

    /** Check if Organisation is connected (using token ) */
    @decorators.booleanProperty<Organization, 'isConnected'>({
        isPublished: true,
        async computeValue() {
            if (await this.$.decryptValue('token')) {
                return Organization.getOrganization(this.$.context);
            }
            return false;
        },
    })
    readonly isConnected: Promise<boolean>;

    /** Will be the name of the tenant */
    @decorators.stringProperty<Organization, 'name'>({
        isPublished: true,
        dataType: () => dataTypes.id,
        computeValue() {
            return Organization.getSystenantName(this.$.context);
        },
    })
    readonly name: Promise<string>;

    /**
     * return by create Organization : format like 15d1d904-**************-a68583463183
     */
    @decorators.stringProperty<Organization, 'organizationId'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.uuid,
        exportValue: '',
    })
    readonly organizationId: Promise<string>;

    /**
     * return by create Organization : format like 1234599860
     */
    @decorators.stringProperty<Organization, 'sageCrmId'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
        exportValue: '',
    })
    readonly sageCrmId: Promise<string>;

    /**
     * return by create Organization : format like FRA
     */
    @decorators.stringProperty<Organization, 'primaryCountry'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
    })
    readonly primaryCountry: Promise<string>;

    /**
     * return by create Organization : <NAME_EMAIL>
     */
    @decorators.stringProperty<Organization, 'adminEmail'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
        exportValue: '',
    })
    readonly adminEmail: Promise<string>;

    /**
     * return by create Organization : format like EN
     */
    @decorators.stringProperty<Organization, 'defaultLanguage'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
    })
    readonly defaultLanguage: Promise<string>;

    /**
     * return by create Organization : UUID format like 15d1d904-**************-a68583463183
     */
    @decorators.stringProperty<Organization, 'externalId'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.uuid,
        exportValue: '',
    })
    readonly externalId: Promise<string>;

    /**
     * primary Signing Key to sign request Format like : AE08EEC3942AD5B7AEFCC262DF84EFEBAD48DFFEBC8F70278DB1F98B0F19473B
     */
    @decorators.stringProperty<Organization, 'primarySigningKey'>({
        isStored: true,
        isStoredEncrypted: true,
        dataType: () => dataTypes.password,
        exportValue: '',
    })
    readonly primarySigningKey: Promise<string>;

    /**
     *  deliver by getOrganizationToken
     */
    @decorators.stringProperty<Organization, 'token'>({
        isStored: true,
        isStoredEncrypted: true,
        dataType: () => dataTypes.description,
        exportValue: '',
    })
    readonly token: Promise<string>;

    /**
     *  Creation of the token
     */
    @decorators.datetimeProperty<Organization, 'tokenCreation'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly tokenCreation: Promise<datetime | null>;

    /**
     *  is the token valid ?
     *  Token lifetime is 20 min
     */
    @decorators.booleanProperty<Organization, 'isTokenValid'>({
        isPublished: true,
        async computeValue() {
            await loggers.org.debugAsync(
                async () =>
                    `token end of life : ${
                        datetime.now().compare((await this.tokenCreation) || datetime.now().addMinutes(-20)) / 60000
                    }mins `,
            );

            const tokenCreation = await this.tokenCreation;
            if (!tokenCreation) {
                return false;
            }

            return !!tokenCreation && datetime.now().compare(tokenCreation) < 1200000;
        },
    })
    readonly isTokenValid: Promise<boolean>;

    /**
     * get the default record
     *
     */
    @decorators.query<typeof Organization, 'defaultInstance'>({
        isPublished: true,
        parameters: [{ name: 'defaultId', type: 'string', isMandatory: false }],
        return: {
            type: 'instance',
            node: () => Organization,
        },
    })
    static async defaultInstance(context: Context, defaultId?: string): Promise<Organization | null> {
        const organizationInstance = await context.tryRead(xtremSageNetwork.nodes.Organization, {
            id: defaultId ?? 'DEFAULT',
        });
        await loggers.org.debugAsync(
            async () =>
                `defaultInstance : defaultId: ${defaultId} id: ${await organizationInstance?.id} orgId: ${await organizationInstance?.organizationId} ${await organizationInstance?.externalId}`,
        );
        return organizationInstance;
    }

    /**
     *  Payload to create an organization
     */
    @decorators.jsonProperty<Organization, 'organizationPayload'>({
        isPublished: true,
        async computeValue() {
            return {
                name: await this.name,
                adminEmail:
                    (await this.adminEmail) !== '' ? await this.adminEmail : ((await this.$.context.user)?.email ?? ''),
                defaultLanguage:
                    (await this.defaultLanguage) !== ''
                        ? await this.defaultLanguage
                        : ((await this.$.context.user)?.locale?.substring(3) ?? 'EN'),
                externalId: (await this.externalId) !== '' ? await this.externalId : Uuid.generate().toString('-'),
                sageCrmId: (await this.sageCrmId) !== '' ? await this.sageCrmId : (this.$.context.tenantId ?? ''),
                primaryCountry: (await this.primaryCountry) !== '' ? await this.primaryCountry : 'FRA',
            };
        },
    })
    readonly organizationPayload: Promise<xtremSageNetwork.interfaces.Organization>;

    @decorators.collectionProperty<Organization, 'companies'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Company,
        getFilter() {
            return {};
        },
    })
    companies: Collection<xtremSystem.nodes.Company>;

    /**
     * Call to create Organization / Request Class
     * @param context
     * @returns
     */
    @decorators.mutation<typeof Organization, 'createOrganization'>({
        isPublished: true,
        parameters: [],
        return: 'boolean',
    })
    static async createOrganization(context: Context): Promise<boolean> {
        const organization = await xtremSageNetwork.classes.Authentication.createOrganization(context);

        const organizationInstance = await context.read(
            xtremSageNetwork.nodes.Organization,
            { id: 'DEFAULT' },
            { forUpdate: true },
        );

        await organizationInstance.$.set(organization);
        await organizationInstance.$.save();

        return true;
    }

    /**
     *  Reset the organization
     * @param context
     * @returns
     */
    @decorators.mutation<typeof Organization, 'resetOrganization'>({
        isPublished: true,
        parameters: [],
        return: 'boolean',
    })
    static async resetOrganization(context: Context): Promise<boolean> {
        const organizationInstance = await context.read(
            xtremSageNetwork.nodes.Organization,
            { id: 'DEFAULT' },
            { forUpdate: true },
        );
        await organizationInstance.$.set({
            organizationId: '',
            primarySigningKey: '',
            token: '',
        });

        return organizationInstance.$.trySave();
    }

    /**
     * Get the token from sage work & Save the token to be able to use it
     * @param context
     * @returns
     */
    @decorators.mutation<typeof Organization, 'saveToken'>({
        isPublished: true,
        parameters: [],
        return: 'string',
    })
    static async saveToken(context: Context): Promise<string> {
        const token = await xtremSageNetwork.classes.Authentication.getOrganizationToken(context);
        if (context.isWritable) {
            const organisationInstance = await context.read(
                xtremSageNetwork.nodes.Organization,
                { id: 'DEFAULT' },
                { forUpdate: true },
            );

            if (!organisationInstance.$.isEffectivelyReadonly) {
                await organisationInstance.$.set({ token, tokenCreation: datetime.now() });
                await organisationInstance.$.save();
            }
        }

        return token;
    }

    @decorators.query<typeof Organization, 'getOrganization'>({
        isPublished: true,
        parameters: [],
        return: 'boolean',
    })
    static async getOrganization(context: Context): Promise<boolean> {
        return (await xtremSageNetwork.classes.Authentication.getOrganization(context)).name !== '';
    }
}
