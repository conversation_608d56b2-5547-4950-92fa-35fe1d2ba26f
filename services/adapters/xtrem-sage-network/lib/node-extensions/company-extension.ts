import type { Reference } from '@sage/xtrem-core';
import { NodeExtension, decorators } from '@sage/xtrem-core';
import '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSageNetwork from '..';

@decorators.nodeExtension<CompanyExtension>({
    extends: () => xtremSystem.nodes.Company,
})
export class CompanyExtension extends NodeExtension<xtremSystem.nodes.Company> {
    @decorators.referenceProperty<CompanyExtension, 'sageNetworkCompany'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'company',
        node: () => xtremSageNetwork.nodes.SageNetworkCompany,
        isNullable: true,
        lookupAccess: true,
        excludedFromPayload: true,
        serviceOptions: () => [xtremSageNetwork.serviceOptions.sageNetworkOption],
    })
    readonly sageNetworkCompany: Reference<xtremSageNetwork.nodes.SageNetworkCompany | null>;

    @decorators.referenceProperty<CompanyExtension, 'financialSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        isNullable: true,
        lookupAccess: true,
        serviceOptions: () => [xtremSageNetwork.serviceOptions.sageNetworkOption],
        getValue() {
            return this.sites.takeOne(site => site.isFinance);
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site | null>;
}
declare module '@sage/xtrem-system/lib/nodes/company' {
    export interface Company extends CompanyExtension {}
}
