{"@sage/xtrem-sage-network/activity__organization__name": "Organization", "@sage/xtrem-sage-network/data_types__api_v_6_stage_enum__name": "Api v 6 stage enum", "@sage/xtrem-sage-network/data_types__request_status_enum__name": "Request status enum", "@sage/xtrem-sage-network/enums__api_v_6_stage__DownloadResults": "Download results", "@sage/xtrem-sage-network/enums__api_v_6_stage__DownloadUrl": "Download url", "@sage/xtrem-sage-network/enums__api_v_6_stage__OperationStatus": "Operation status", "@sage/xtrem-sage-network/enums__api_v_6_stage__ReportStatus": "Report status", "@sage/xtrem-sage-network/enums__api_v_6_stage__StartReport": "Start report", "@sage/xtrem-sage-network/enums__api_v_6_stage__UploadUrl": "Upload url", "@sage/xtrem-sage-network/enums__request_status__awaitingResult": "Awaiting result", "@sage/xtrem-sage-network/enums__request_status__error": "Error", "@sage/xtrem-sage-network/enums__request_status__inProgress": "In progress", "@sage/xtrem-sage-network/enums__request_status__pending": "Pending", "@sage/xtrem-sage-network/enums__request_status__success": "Success", "@sage/xtrem-sage-network/enums__request_status__uploadFailed": "Upload failed", "@sage/xtrem-sage-network/menu_item__organization": "Sage Network", "@sage/xtrem-sage-network/no-active-configuration": "No active configuration", "@sage/xtrem-sage-network/no-api-mode-api-configuration": "No API mode for the Sage Network API in the configuration.", "@sage/xtrem-sage-network/no-api-url-api-configuration": "No URL for the Sage Network API in the configuration.", "@sage/xtrem-sage-network/no-application-api-configuration": "No application for the Sage Network API in the configuration.", "@sage/xtrem-sage-network/no-auth-url-api-configuration": "No URL for the Sage Network authentication API in the configuration.", "@sage/xtrem-sage-network/no-lookup-url-api-configuration": "No URL for the Sage Network lookup API in the configuration.", "@sage/xtrem-sage-network/no-sigining-key": "No signing key.", "@sage/xtrem-sage-network/no-signatory-api-configuration": "No signatory for the Sage Network API in the configuration.", "@sage/xtrem-sage-network/no-signing-key-api-configuration": "No signing key for the Sage Network API in the configuration.", "@sage/xtrem-sage-network/no-v6-api-key-api-configuration": "No API key for the Sage Network V6 API in the configuration.", "@sage/xtrem-sage-network/no-v6-api-url-api-configuration": "No URL for the Sage Network V6 API in the configuration.", "@sage/xtrem-sage-network/no-v6-application-api-configuration": "No application for the Sage Network V6 API in the configuration.", "@sage/xtrem-sage-network/node-extensions__company_extension__property__financialSite": "Financial site", "@sage/xtrem-sage-network/node-extensions__company_extension__property__sageNetworkCompany": "Sage network company", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport": "Export", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sage-network/nodes__organization__mutation__createOrganization": "Create organization", "@sage/xtrem-sage-network/nodes__organization__mutation__createOrganization__failed": "Create organization failed.", "@sage/xtrem-sage-network/nodes__organization__mutation__resetOrganization": "Reset organization", "@sage/xtrem-sage-network/nodes__organization__mutation__resetOrganization__failed": "Reset organization failed.", "@sage/xtrem-sage-network/nodes__organization__mutation__saveToken": "Save token", "@sage/xtrem-sage-network/nodes__organization__mutation__saveToken__failed": "Save token failed.", "@sage/xtrem-sage-network/nodes__organization__node_name": "Organization", "@sage/xtrem-sage-network/nodes__organization__property__adminEmail": "Admin email", "@sage/xtrem-sage-network/nodes__organization__property__companies": "Companies", "@sage/xtrem-sage-network/nodes__organization__property__defaultLanguage": "Default language", "@sage/xtrem-sage-network/nodes__organization__property__externalId": "External id", "@sage/xtrem-sage-network/nodes__organization__property__id": "Id", "@sage/xtrem-sage-network/nodes__organization__property__isActive": "Is active", "@sage/xtrem-sage-network/nodes__organization__property__isConnected": "Is connected", "@sage/xtrem-sage-network/nodes__organization__property__isTokenValid": "Is token valid", "@sage/xtrem-sage-network/nodes__organization__property__name": "Name", "@sage/xtrem-sage-network/nodes__organization__property__organizationId": "Organization id", "@sage/xtrem-sage-network/nodes__organization__property__organizationPayload": "Organization payload", "@sage/xtrem-sage-network/nodes__organization__property__primaryCountry": "Primary country", "@sage/xtrem-sage-network/nodes__organization__property__primarySigningKey": "Primary signing key", "@sage/xtrem-sage-network/nodes__organization__property__sageCrmId": "Sage crm id", "@sage/xtrem-sage-network/nodes__organization__property__token": "Token", "@sage/xtrem-sage-network/nodes__organization__property__tokenCreation": "Token creation", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance": "Default instance", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance__failed": "Default instance failed.", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance__parameter__defaultId": "Default id", "@sage/xtrem-sage-network/nodes__organization__query__getOrganization": "Get organization", "@sage/xtrem-sage-network/nodes__organization__query__getOrganization__failed": "Get organization failed.", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport": "Export", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany": "Create company", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany__failed": "Create company failed.", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany__parameter__id": "Id", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken": "Save token", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken__failed": "Save token failed.", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken__parameter__company": "Company", "@sage/xtrem-sage-network/nodes__sage_network_company__node_name": "Sage network company", "@sage/xtrem-sage-network/nodes__sage_network_company__property__company": "Company", "@sage/xtrem-sage-network/nodes__sage_network_company__property__companyId": "Company id", "@sage/xtrem-sage-network/nodes__sage_network_company__property__companyPayload": "Company payload", "@sage/xtrem-sage-network/nodes__sage_network_company__property__externalId": "External id", "@sage/xtrem-sage-network/nodes__sage_network_company__property__isCreated": "Is created", "@sage/xtrem-sage-network/nodes__sage_network_company__property__isTokenValid": "Is token valid", "@sage/xtrem-sage-network/nodes__sage_network_company__property__token": "Token", "@sage/xtrem-sage-network/nodes__sage_network_company__property__tokenCreation": "Token creation", "@sage/xtrem-sage-network/organization-not-created": "The Sage Network organization creation failed.", "@sage/xtrem-sage-network/package__name": "Sage xtrem sage network", "@sage/xtrem-sage-network/pages__organization____title": "Sage Network configuration", "@sage/xtrem-sage-network/pages__organization__adminEmail____title": "Admin email", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__name": "Name", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__sageNetworkCompany__externalId": "External ID", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__sageNetworkCompany__isCreated": "Created", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__taxEngine": "Tax engine", "@sage/xtrem-sage-network/pages__organization__companies____dropdownActions__title": "Create", "@sage/xtrem-sage-network/pages__organization__companies____title": "Companies", "@sage/xtrem-sage-network/pages__organization__companySection____title": "Company", "@sage/xtrem-sage-network/pages__organization__createOrganization____title": "Create organization", "@sage/xtrem-sage-network/pages__organization__defaultLanguage____title": "Default language", "@sage/xtrem-sage-network/pages__organization__getToken____title": "Get token", "@sage/xtrem-sage-network/pages__organization__isActive____title": "Active", "@sage/xtrem-sage-network/pages__organization__isConnected____title": "Connected", "@sage/xtrem-sage-network/pages__organization__mainSection____title": "General", "@sage/xtrem-sage-network/pages__organization__name____title": "Name", "@sage/xtrem-sage-network/pages__organization__primaryCountry____title": "Country", "@sage/xtrem-sage-network/pages__organization__resetOrganization____title": "Reset organization", "@sage/xtrem-sage-network/pages__organization__sageCrmId____title": "Sage CRM ID", "@sage/xtrem-sage-network/permission__create__name": "Create", "@sage/xtrem-sage-network/permission__default_instance__name": "Default instance", "@sage/xtrem-sage-network/permission__delete__name": "Delete", "@sage/xtrem-sage-network/permission__read__name": "Read", "@sage/xtrem-sage-network/permission__update__name": "Update", "@sage/xtrem-sage-network/service_options__sage_network_option__name": "Sage network option"}