{"@sage/xtrem-sage-network/activity__organization__name": "Organización", "@sage/xtrem-sage-network/data_types__api_v_6_stage_enum__name": "API V6 stage enum", "@sage/xtrem-sage-network/data_types__request_status_enum__name": "Request status enum", "@sage/xtrem-sage-network/enums__api_v_6_stage__DownloadResults": "Resultados de descarga", "@sage/xtrem-sage-network/enums__api_v_6_stage__DownloadUrl": "URL de descarga", "@sage/xtrem-sage-network/enums__api_v_6_stage__OperationStatus": "Estado de operación", "@sage/xtrem-sage-network/enums__api_v_6_stage__ReportStatus": "Estado de devolución", "@sage/xtrem-sage-network/enums__api_v_6_stage__StartReport": "Start report", "@sage/xtrem-sage-network/enums__api_v_6_stage__UploadUrl": "URL de subida", "@sage/xtrem-sage-network/enums__request_status__awaitingResult": "En espera de resultados", "@sage/xtrem-sage-network/enums__request_status__error": "Error", "@sage/xtrem-sage-network/enums__request_status__inProgress": "En curso", "@sage/xtrem-sage-network/enums__request_status__pending": "Pendiente", "@sage/xtrem-sage-network/enums__request_status__success": "<PERSON><PERSON>rma<PERSON>", "@sage/xtrem-sage-network/enums__request_status__uploadFailed": "Error de subida", "@sage/xtrem-sage-network/menu_item__organization": "Sage Network", "@sage/xtrem-sage-network/no-active-configuration": "No hay ninguna configuración activa.", "@sage/xtrem-sage-network/no-api-mode-api-configuration": "No hay ningún modo de API para Sage Network en la configuración.", "@sage/xtrem-sage-network/no-api-url-api-configuration": "No hay ninguna URL de API para Sage Network en la configuración.", "@sage/xtrem-sage-network/no-application-api-configuration": "No hay ninguna URL de la API de búsqueda para Sage Network en la configuración.", "@sage/xtrem-sage-network/no-auth-url-api-configuration": "No hay ninguna URL de la API de autenticación para Sage Network en la configuración.", "@sage/xtrem-sage-network/no-lookup-url-api-configuration": "No hay ninguna URL de la API de búsqueda para Sage Network en la configuración.", "@sage/xtrem-sage-network/no-sigining-key": "No hay ninguna clave de firma.", "@sage/xtrem-sage-network/no-signatory-api-configuration": "No hay ninguna URL de la API de búsqueda para Sage Network en la configuración.", "@sage/xtrem-sage-network/no-signing-key-api-configuration": "No hay ninguna URL de la API de búsqueda para Sage Network en la configuración.", "@sage/xtrem-sage-network/no-v6-api-key-api-configuration": "No hay ninguna clave de API V6 para Sage Network en la configuración.", "@sage/xtrem-sage-network/no-v6-api-url-api-configuration": "No hay ninguna URL de API V6 para Sage Network en la configuración.", "@sage/xtrem-sage-network/no-v6-application-api-configuration": "No hay ninguna URL de la API de búsqueda para Sage Network en la configuración.", "@sage/xtrem-sage-network/node-extensions__company_extension__property__financialSite": "Planta financiera", "@sage/xtrem-sage-network/node-extensions__company_extension__property__sageNetworkCompany": "Sociedad en Sage Network", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-sage-network/nodes__organization__mutation__createOrganization": "Crear organización", "@sage/xtrem-sage-network/nodes__organization__mutation__createOrganization__failed": "Error al crear la organización.", "@sage/xtrem-sage-network/nodes__organization__mutation__resetOrganization": "Restablecer organización", "@sage/xtrem-sage-network/nodes__organization__mutation__resetOrganization__failed": "Error al restablecer la organización.", "@sage/xtrem-sage-network/nodes__organization__mutation__saveToken": "Guardar token", "@sage/xtrem-sage-network/nodes__organization__mutation__saveToken__failed": "Error al guardar el token.", "@sage/xtrem-sage-network/nodes__organization__node_name": "Organización", "@sage/xtrem-sage-network/nodes__organization__property__adminEmail": "E-mail de administrador", "@sage/xtrem-sage-network/nodes__organization__property__apiMode": "API mode", "@sage/xtrem-sage-network/nodes__organization__property__apiURL": "URL de API", "@sage/xtrem-sage-network/nodes__organization__property__authURL": "URL de autenticación", "@sage/xtrem-sage-network/nodes__organization__property__companies": "Sociedades", "@sage/xtrem-sage-network/nodes__organization__property__defaultLanguage": "Idioma por defecto", "@sage/xtrem-sage-network/nodes__organization__property__externalId": "Id. externo", "@sage/xtrem-sage-network/nodes__organization__property__id": "Id.", "@sage/xtrem-sage-network/nodes__organization__property__isActive": "Activa", "@sage/xtrem-sage-network/nodes__organization__property__isConnected": "Conexión establecida", "@sage/xtrem-sage-network/nodes__organization__property__isTokenValid": "Token válido", "@sage/xtrem-sage-network/nodes__organization__property__lookupURL": "URL de búsqueda", "@sage/xtrem-sage-network/nodes__organization__property__name": "Nombre", "@sage/xtrem-sage-network/nodes__organization__property__organization": "Organización", "@sage/xtrem-sage-network/nodes__organization__property__organizationId": "Id. de organización", "@sage/xtrem-sage-network/nodes__organization__property__organizationPayload": "Organization payload", "@sage/xtrem-sage-network/nodes__organization__property__primaryCountry": "<PERSON><PERSON> principal", "@sage/xtrem-sage-network/nodes__organization__property__primarySigningKey": "Clave de firma principal", "@sage/xtrem-sage-network/nodes__organization__property__sageCrmId": "Id. en CRM de Sage", "@sage/xtrem-sage-network/nodes__organization__property__signatory": "Signatory", "@sage/xtrem-sage-network/nodes__organization__property__signingKey": "Clave de firma", "@sage/xtrem-sage-network/nodes__organization__property__token": "Token", "@sage/xtrem-sage-network/nodes__organization__property__tokenCreation": "Creación de token", "@sage/xtrem-sage-network/nodes__organization__property__v6ApiKey": "V6 API key", "@sage/xtrem-sage-network/nodes__organization__property__v6ApiURL": "URL de API", "@sage/xtrem-sage-network/nodes__organization__property__xApplication": "Aplicación X", "@sage/xtrem-sage-network/nodes__organization__property__xV6Application": "Aplicación X", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance": "Instancia por defecto", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance__failed": "Error de instancia por defecto.", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance__parameter__defaultId": "Id. por defecto", "@sage/xtrem-sage-network/nodes__organization__query__getOrganization": "Obtener organización", "@sage/xtrem-sage-network/nodes__organization__query__getOrganization__failed": "Error al obtener la organización.", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany": "Create company", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany__failed": "Error al validar la sociedad.", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany__parameter__id": "Id", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken": "Guardar token", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken__failed": "Error al guardar el token.", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken__parameter__company": "Sociedad", "@sage/xtrem-sage-network/nodes__sage_network_company__node_name": "Sociedad en Sage Network", "@sage/xtrem-sage-network/nodes__sage_network_company__property__company": "Sociedad", "@sage/xtrem-sage-network/nodes__sage_network_company__property__companyId": "Id. de sociedad", "@sage/xtrem-sage-network/nodes__sage_network_company__property__companyPayload": "Company payload", "@sage/xtrem-sage-network/nodes__sage_network_company__property__externalId": "Id. externo", "@sage/xtrem-sage-network/nodes__sage_network_company__property__isCreated": "<PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/nodes__sage_network_company__property__isTokenValid": "Token válido", "@sage/xtrem-sage-network/nodes__sage_network_company__property__token": "Token", "@sage/xtrem-sage-network/nodes__sage_network_company__property__tokenCreation": "Creación de token", "@sage/xtrem-sage-network/organization-not-created": "Ha habido un error al crear la organización en Sage Network.", "@sage/xtrem-sage-network/package__name": "Sage DMO Sage Network", "@sage/xtrem-sage-network/pages__organization____title": "Configuración de Sage Network", "@sage/xtrem-sage-network/pages__organization__adminEmail____title": "E-mail de administrador", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__name": "Nombre", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__sageNetworkCompany__externalId": "Id. externo", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__sageNetworkCompany__isCreated": "<PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__taxEngine": "Motor de impuestos", "@sage/xtrem-sage-network/pages__organization__companies____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/pages__organization__companies____title": "Sociedades", "@sage/xtrem-sage-network/pages__organization__companySection____title": "Sociedad", "@sage/xtrem-sage-network/pages__organization__createOrganization____title": "Crear organización", "@sage/xtrem-sage-network/pages__organization__defaultLanguage____title": "Idioma por defecto", "@sage/xtrem-sage-network/pages__organization__getToken____title": "Obtener token", "@sage/xtrem-sage-network/pages__organization__isActive____title": "Activa", "@sage/xtrem-sage-network/pages__organization__isConnected____title": "Conexión establecida", "@sage/xtrem-sage-network/pages__organization__mainSection____title": "General", "@sage/xtrem-sage-network/pages__organization__name____title": "Nombre", "@sage/xtrem-sage-network/pages__organization__primaryCountry____title": "<PERSON><PERSON>", "@sage/xtrem-sage-network/pages__organization__resetOrganization____title": "Restablecer organización", "@sage/xtrem-sage-network/pages__organization__sageCrmId____title": "Id. en CRM de Sage", "@sage/xtrem-sage-network/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/permission__default_instance__name": "Instancia por defecto", "@sage/xtrem-sage-network/permission__delete__name": "Eliminar", "@sage/xtrem-sage-network/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-sage-network/permission__update__name": "Actualizar", "@sage/xtrem-sage-network/service_options__sage_network_option__name": "Opción de Sage Network"}