{"@sage/xtrem-sage-network/activity__organization__name": "Organisation", "@sage/xtrem-sage-network/data_types__api_v_6_stage_enum__name": "Enum Phase API V6", "@sage/xtrem-sage-network/data_types__request_status_enum__name": "Enum <PERSON>ungsstatus", "@sage/xtrem-sage-network/enums__api_v_6_stage__DownloadResults": "Download Ergebnisse", "@sage/xtrem-sage-network/enums__api_v_6_stage__DownloadUrl": "Download-URL", "@sage/xtrem-sage-network/enums__api_v_6_stage__OperationStatus": "Status Arbeitsgang", "@sage/xtrem-sage-network/enums__api_v_6_stage__ReportStatus": "Reportstatus", "@sage/xtrem-sage-network/enums__api_v_6_stage__StartReport": "Report starten", "@sage/xtrem-sage-network/enums__api_v_6_stage__UploadUrl": "Upload-URL", "@sage/xtrem-sage-network/enums__request_status__awaitingResult": "<PERSON><PERSON><PERSON><PERSON> auss<PERSON>", "@sage/xtrem-sage-network/enums__request_status__error": "<PERSON><PERSON>", "@sage/xtrem-sage-network/enums__request_status__inProgress": "In Bearbeitung", "@sage/xtrem-sage-network/enums__request_status__pending": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/enums__request_status__success": "Erfolgreich", "@sage/xtrem-sage-network/enums__request_status__uploadFailed": "Hochladen fehlgeschlagen", "@sage/xtrem-sage-network/menu_item__organization": "Sage Network", "@sage/xtrem-sage-network/no-active-configuration": "Keine aktive Konfiguration", "@sage/xtrem-sage-network/no-api-mode-api-configuration": "Kein API-Modus für die Sage Network-API in der Konfiguration.", "@sage/xtrem-sage-network/no-api-url-api-configuration": "Keine URL für die Sage Network-API in der Konfiguration.", "@sage/xtrem-sage-network/no-application-api-configuration": "<PERSON>ine Anwendung für die Sage Network-API in der Konfiguration.", "@sage/xtrem-sage-network/no-auth-url-api-configuration": "Keine URL für die Sage Network-Authentifizierungs-API in der Konfiguration.", "@sage/xtrem-sage-network/no-lookup-url-api-configuration": "Keine URL für die API-Suche Sage Network in der Konfiguration.", "@sage/xtrem-sage-network/no-sigining-key": "<PERSON><PERSON>.", "@sage/xtrem-sage-network/no-signatory-api-configuration": "<PERSON><PERSON> Unterzeichner für die Sage Network-API in der Konfiguration.", "@sage/xtrem-sage-network/no-signing-key-api-configuration": "<PERSON><PERSON>ei<PERSON>nungsschlüssel für die Sage Network-API in der Konfiguration.", "@sage/xtrem-sage-network/no-v6-api-key-api-configuration": "Kein API-Schlüssel für die Sage Network-V6-API in der Konfiguration.", "@sage/xtrem-sage-network/no-v6-api-url-api-configuration": "Keine URL für die Sage Network-V6-API in der Konfiguration.", "@sage/xtrem-sage-network/no-v6-application-api-configuration": "<PERSON>ine Anwendung für die Sage Network-V6-API in der Konfiguration.", "@sage/xtrem-sage-network/node-extensions__company_extension__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-sage-network/node-extensions__company_extension__property__sageNetworkCompany": "Unternehmen Sage Network", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport": "Export", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sage-network/nodes__organization__mutation__createOrganization": "Organisation erstellen", "@sage/xtrem-sage-network/nodes__organization__mutation__createOrganization__failed": "Organisation erstellen fehlgeschlagen.", "@sage/xtrem-sage-network/nodes__organization__mutation__resetOrganization": "Organisation zurücksetzen", "@sage/xtrem-sage-network/nodes__organization__mutation__resetOrganization__failed": "Organisation zurücksetzen fehlgeschlagen.", "@sage/xtrem-sage-network/nodes__organization__mutation__saveToken": "Token speichern", "@sage/xtrem-sage-network/nodes__organization__mutation__saveToken__failed": "Token speichern fehlgeschlagen.", "@sage/xtrem-sage-network/nodes__organization__node_name": "Organisation", "@sage/xtrem-sage-network/nodes__organization__property__adminEmail": "E-Mail-<PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/nodes__organization__property__apiMode": "API-Modus", "@sage/xtrem-sage-network/nodes__organization__property__apiURL": "URL API", "@sage/xtrem-sage-network/nodes__organization__property__authURL": "URL Authentifizierung", "@sage/xtrem-sage-network/nodes__organization__property__companies": "Unternehmen", "@sage/xtrem-sage-network/nodes__organization__property__defaultLanguage": "Standardsprache", "@sage/xtrem-sage-network/nodes__organization__property__externalId": "Externe ID", "@sage/xtrem-sage-network/nodes__organization__property__id": "ID", "@sage/xtrem-sage-network/nodes__organization__property__isActive": "Ist aktiv", "@sage/xtrem-sage-network/nodes__organization__property__isConnected": "Ist verbunden", "@sage/xtrem-sage-network/nodes__organization__property__isTokenValid": "Ist Gültigkeit Token", "@sage/xtrem-sage-network/nodes__organization__property__lookupURL": "URL Suche", "@sage/xtrem-sage-network/nodes__organization__property__name": "Name", "@sage/xtrem-sage-network/nodes__organization__property__organization": "Organisation", "@sage/xtrem-sage-network/nodes__organization__property__organizationId": "ID Organisation", "@sage/xtrem-sage-network/nodes__organization__property__organizationPayload": "Payload Organisation", "@sage/xtrem-sage-network/nodes__organization__property__primaryCountry": "Primäres Land", "@sage/xtrem-sage-network/nodes__organization__property__primarySigningKey": "Primärer Signaturschlüssel", "@sage/xtrem-sage-network/nodes__organization__property__sageCrmId": "ID Sage CRM", "@sage/xtrem-sage-network/nodes__organization__property__signatory": "Unterzeichner", "@sage/xtrem-sage-network/nodes__organization__property__signingKey": "Zeichnungsschlüssel", "@sage/xtrem-sage-network/nodes__organization__property__token": "Token", "@sage/xtrem-sage-network/nodes__organization__property__tokenCreation": "Token-Erstellung", "@sage/xtrem-sage-network/nodes__organization__property__v6ApiKey": "API-Schlüssel V6", "@sage/xtrem-sage-network/nodes__organization__property__v6ApiURL": "URL API V6", "@sage/xtrem-sage-network/nodes__organization__property__xApplication": "X Anwendung", "@sage/xtrem-sage-network/nodes__organization__property__xV6Application": "X Anwendung V6", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance": "Standardinstanz", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance__failed": "Standardinstanz fehlgeschlagen.", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance__parameter__defaultId": "Standard-ID", "@sage/xtrem-sage-network/nodes__organization__query__getOrganization": "Organisation abrufen", "@sage/xtrem-sage-network/nodes__organization__query__getOrganization__failed": "Organisation abrufen fehlgeschlagen.", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport": "Export", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany": "Unternehmen erstellen", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany__failed": "Unternehmen erstellen fehlgeschlagen.", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany__parameter__id": "ID", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken": "Token speichern", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken__failed": "Token speichern fehlgeschlagen.", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken__parameter__company": "Unternehmen", "@sage/xtrem-sage-network/nodes__sage_network_company__node_name": "Unternehmen Sage Network", "@sage/xtrem-sage-network/nodes__sage_network_company__property__company": "Unternehmen", "@sage/xtrem-sage-network/nodes__sage_network_company__property__companyId": "Unternehmens-ID", "@sage/xtrem-sage-network/nodes__sage_network_company__property__companyPayload": "Payload Unternehmen", "@sage/xtrem-sage-network/nodes__sage_network_company__property__externalId": "Externe ID", "@sage/xtrem-sage-network/nodes__sage_network_company__property__isCreated": "<PERSON>t erstellt", "@sage/xtrem-sage-network/nodes__sage_network_company__property__isTokenValid": "Ist Gültigkeit Token", "@sage/xtrem-sage-network/nodes__sage_network_company__property__token": "Token", "@sage/xtrem-sage-network/nodes__sage_network_company__property__tokenCreation": "Token-Erstellung", "@sage/xtrem-sage-network/organization-not-created": "Die Erstellung der Sage Network-Organisation ist fehlgeschlagen.", "@sage/xtrem-sage-network/package__name": "Sage DMO Sage Network", "@sage/xtrem-sage-network/pages__organization____title": "Konfiguration Sage Network", "@sage/xtrem-sage-network/pages__organization__adminEmail____title": "E-Mail-<PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__name": "Name", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__sageNetworkCompany__externalId": "Externe ID", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__sageNetworkCompany__isCreated": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__taxEngine": "Steuermodul", "@sage/xtrem-sage-network/pages__organization__companies____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/pages__organization__companies____title": "Unternehmen", "@sage/xtrem-sage-network/pages__organization__companySection____title": "Unternehmen", "@sage/xtrem-sage-network/pages__organization__createOrganization____title": "Organisation erstellen", "@sage/xtrem-sage-network/pages__organization__defaultLanguage____title": "Standardsprache", "@sage/xtrem-sage-network/pages__organization__getToken____title": "Token abrufen", "@sage/xtrem-sage-network/pages__organization__isActive____title": "Aktiv", "@sage/xtrem-sage-network/pages__organization__isConnected____title": "Verbunden", "@sage/xtrem-sage-network/pages__organization__mainSection____title": "Allgemein", "@sage/xtrem-sage-network/pages__organization__name____title": "Name", "@sage/xtrem-sage-network/pages__organization__primaryCountry____title": "Land", "@sage/xtrem-sage-network/pages__organization__resetOrganization____title": "Organisation zurücksetzen", "@sage/xtrem-sage-network/pages__organization__sageCrmId____title": "ID Sage CRM", "@sage/xtrem-sage-network/permission__create__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/permission__default_instance__name": "Standardinstanz", "@sage/xtrem-sage-network/permission__delete__name": "Löschen", "@sage/xtrem-sage-network/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-sage-network/permission__update__name": "Aktualisierung", "@sage/xtrem-sage-network/service_options__sage_network_option__name": "Sage Network-Option"}