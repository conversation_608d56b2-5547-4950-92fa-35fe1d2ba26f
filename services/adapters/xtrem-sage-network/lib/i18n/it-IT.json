{"@sage/xtrem-sage-network/activity__organization__name": "", "@sage/xtrem-sage-network/data_types__api_v_6_stage_enum__name": "", "@sage/xtrem-sage-network/data_types__request_status_enum__name": "", "@sage/xtrem-sage-network/enums__api_v_6_stage__DownloadResults": "", "@sage/xtrem-sage-network/enums__api_v_6_stage__DownloadUrl": "", "@sage/xtrem-sage-network/enums__api_v_6_stage__OperationStatus": "", "@sage/xtrem-sage-network/enums__api_v_6_stage__ReportStatus": "", "@sage/xtrem-sage-network/enums__api_v_6_stage__StartReport": "", "@sage/xtrem-sage-network/enums__api_v_6_stage__UploadUrl": "", "@sage/xtrem-sage-network/enums__request_status__awaitingResult": "", "@sage/xtrem-sage-network/enums__request_status__error": "", "@sage/xtrem-sage-network/enums__request_status__inProgress": "", "@sage/xtrem-sage-network/enums__request_status__pending": "", "@sage/xtrem-sage-network/enums__request_status__success": "", "@sage/xtrem-sage-network/enums__request_status__uploadFailed": "", "@sage/xtrem-sage-network/menu_item__organization": "", "@sage/xtrem-sage-network/no-active-configuration": "", "@sage/xtrem-sage-network/no-api-mode-api-configuration": "", "@sage/xtrem-sage-network/no-api-url-api-configuration": "", "@sage/xtrem-sage-network/no-application-api-configuration": "", "@sage/xtrem-sage-network/no-auth-url-api-configuration": "", "@sage/xtrem-sage-network/no-lookup-url-api-configuration": "", "@sage/xtrem-sage-network/no-sigining-key": "", "@sage/xtrem-sage-network/no-signatory-api-configuration": "", "@sage/xtrem-sage-network/no-signing-key-api-configuration": "", "@sage/xtrem-sage-network/no-v6-api-key-api-configuration": "", "@sage/xtrem-sage-network/no-v6-api-url-api-configuration": "", "@sage/xtrem-sage-network/no-v6-application-api-configuration": "", "@sage/xtrem-sage-network/node-extensions__company_extension__property__financialSite": "", "@sage/xtrem-sage-network/node-extensions__company_extension__property__sageNetworkCompany": "", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport": "", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-sage-network/nodes__organization__mutation__createOrganization": "", "@sage/xtrem-sage-network/nodes__organization__mutation__createOrganization__failed": "", "@sage/xtrem-sage-network/nodes__organization__mutation__resetOrganization": "", "@sage/xtrem-sage-network/nodes__organization__mutation__resetOrganization__failed": "", "@sage/xtrem-sage-network/nodes__organization__mutation__saveToken": "", "@sage/xtrem-sage-network/nodes__organization__mutation__saveToken__failed": "", "@sage/xtrem-sage-network/nodes__organization__node_name": "", "@sage/xtrem-sage-network/nodes__organization__property__adminEmail": "", "@sage/xtrem-sage-network/nodes__organization__property__companies": "", "@sage/xtrem-sage-network/nodes__organization__property__defaultLanguage": "", "@sage/xtrem-sage-network/nodes__organization__property__externalId": "", "@sage/xtrem-sage-network/nodes__organization__property__id": "", "@sage/xtrem-sage-network/nodes__organization__property__isActive": "", "@sage/xtrem-sage-network/nodes__organization__property__isConnected": "", "@sage/xtrem-sage-network/nodes__organization__property__isTokenValid": "", "@sage/xtrem-sage-network/nodes__organization__property__name": "", "@sage/xtrem-sage-network/nodes__organization__property__organizationId": "", "@sage/xtrem-sage-network/nodes__organization__property__organizationPayload": "", "@sage/xtrem-sage-network/nodes__organization__property__primaryCountry": "", "@sage/xtrem-sage-network/nodes__organization__property__primarySigningKey": "", "@sage/xtrem-sage-network/nodes__organization__property__sageCrmId": "", "@sage/xtrem-sage-network/nodes__organization__property__token": "", "@sage/xtrem-sage-network/nodes__organization__property__tokenCreation": "", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance": "", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance__failed": "", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance__parameter__defaultId": "", "@sage/xtrem-sage-network/nodes__organization__query__getOrganization": "", "@sage/xtrem-sage-network/nodes__organization__query__getOrganization__failed": "", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport": "", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany": "", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany__failed": "", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany__parameter__id": "", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken": "", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken__failed": "", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken__parameter__company": "", "@sage/xtrem-sage-network/nodes__sage_network_company__node_name": "", "@sage/xtrem-sage-network/nodes__sage_network_company__property__company": "", "@sage/xtrem-sage-network/nodes__sage_network_company__property__companyId": "", "@sage/xtrem-sage-network/nodes__sage_network_company__property__companyPayload": "", "@sage/xtrem-sage-network/nodes__sage_network_company__property__externalId": "", "@sage/xtrem-sage-network/nodes__sage_network_company__property__isCreated": "", "@sage/xtrem-sage-network/nodes__sage_network_company__property__isTokenValid": "", "@sage/xtrem-sage-network/nodes__sage_network_company__property__token": "", "@sage/xtrem-sage-network/nodes__sage_network_company__property__tokenCreation": "", "@sage/xtrem-sage-network/organization-not-created": "", "@sage/xtrem-sage-network/package__name": "", "@sage/xtrem-sage-network/pages__organization____title": "", "@sage/xtrem-sage-network/pages__organization__adminEmail____title": "", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__name": "", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__sageNetworkCompany__externalId": "", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__sageNetworkCompany__isCreated": "", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__taxEngine": "", "@sage/xtrem-sage-network/pages__organization__companies____dropdownActions__title": "", "@sage/xtrem-sage-network/pages__organization__companies____title": "", "@sage/xtrem-sage-network/pages__organization__companySection____title": "", "@sage/xtrem-sage-network/pages__organization__createOrganization____title": "", "@sage/xtrem-sage-network/pages__organization__defaultLanguage____title": "", "@sage/xtrem-sage-network/pages__organization__getToken____title": "", "@sage/xtrem-sage-network/pages__organization__isActive____title": "", "@sage/xtrem-sage-network/pages__organization__isConnected____title": "", "@sage/xtrem-sage-network/pages__organization__mainSection____title": "", "@sage/xtrem-sage-network/pages__organization__name____title": "", "@sage/xtrem-sage-network/pages__organization__primaryCountry____title": "", "@sage/xtrem-sage-network/pages__organization__resetOrganization____title": "", "@sage/xtrem-sage-network/pages__organization__sageCrmId____title": "", "@sage/xtrem-sage-network/permission__create__name": "", "@sage/xtrem-sage-network/permission__default_instance__name": "", "@sage/xtrem-sage-network/permission__delete__name": "", "@sage/xtrem-sage-network/permission__read__name": "", "@sage/xtrem-sage-network/permission__update__name": "", "@sage/xtrem-sage-network/service_options__sage_network_option__name": ""}