{"@sage/xtrem-sage-network/activity__organization__name": "Organisation", "@sage/xtrem-sage-network/data_types__api_v_6_stage_enum__name": "Enum étape API V6", "@sage/xtrem-sage-network/data_types__request_status_enum__name": "Enum statut de demande", "@sage/xtrem-sage-network/enums__api_v_6_stage__DownloadResults": "Télécharger les résultats", "@sage/xtrem-sage-network/enums__api_v_6_stage__DownloadUrl": "URL de téléchargement", "@sage/xtrem-sage-network/enums__api_v_6_stage__OperationStatus": "Statut d'opération", "@sage/xtrem-sage-network/enums__api_v_6_stage__ReportStatus": "Statut d'édition", "@sage/xtrem-sage-network/enums__api_v_6_stage__StartReport": "Lancer l'édition", "@sage/xtrem-sage-network/enums__api_v_6_stage__UploadUrl": "URL de téléchargement", "@sage/xtrem-sage-network/enums__request_status__awaitingResult": "En attente de résultat", "@sage/xtrem-sage-network/enums__request_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/enums__request_status__inProgress": "En cours", "@sage/xtrem-sage-network/enums__request_status__pending": "En attente", "@sage/xtrem-sage-network/enums__request_status__success": "Su<PERSON>ès", "@sage/xtrem-sage-network/enums__request_status__uploadFailed": "Échec du téléchargement", "@sage/xtrem-sage-network/menu_item__organization": "Sage Network", "@sage/xtrem-sage-network/no-active-configuration": "Aucune configuration active", "@sage/xtrem-sage-network/no-api-mode-api-configuration": "Pas de mode API pour l’API Sage Network dans la configuration.", "@sage/xtrem-sage-network/no-api-url-api-configuration": "Pas d’URL pour l’API Sage Network dans la configuration.", "@sage/xtrem-sage-network/no-application-api-configuration": "Pas d'application pour l’API Sage Network dans la configuration.", "@sage/xtrem-sage-network/no-auth-url-api-configuration": "Pas d’URL pour l’API d'authentification de Sage Network dans la configuration.", "@sage/xtrem-sage-network/no-lookup-url-api-configuration": "Pas d’URL pour l’API de recherche de Sage Network dans la configuration.", "@sage/xtrem-sage-network/no-sigining-key": "Pas de clé de signature.", "@sage/xtrem-sage-network/no-signatory-api-configuration": "Pas d'application pour l’API Sage Network V6 dans la configuration.", "@sage/xtrem-sage-network/no-signing-key-api-configuration": "Pas de clé de signature pour l’API Sage Network dans la configuration.", "@sage/xtrem-sage-network/no-v6-api-key-api-configuration": "Pas de clé d’API pour l’API Sage Network V6 dans la configuration.", "@sage/xtrem-sage-network/no-v6-api-url-api-configuration": "Pas d’URL pour l’API Sage Network V6 dans la configuration.", "@sage/xtrem-sage-network/no-v6-application-api-configuration": "Pas d'appliction pour l’API Sage Network V6 dans la configuration.", "@sage/xtrem-sage-network/node-extensions__company_extension__property__financialSite": "Site financier", "@sage/xtrem-sage-network/node-extensions__company_extension__property__sageNetworkCompany": "Société Sage Network", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-sage-network/nodes__organization__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-sage-network/nodes__organization__mutation__createOrganization": "Créer l'organisation", "@sage/xtrem-sage-network/nodes__organization__mutation__createOrganization__failed": "Échec de création d'organisation.", "@sage/xtrem-sage-network/nodes__organization__mutation__resetOrganization": "Réinitialiser l'organisation", "@sage/xtrem-sage-network/nodes__organization__mutation__resetOrganization__failed": "Échec de création d'organisation.", "@sage/xtrem-sage-network/nodes__organization__mutation__saveToken": "Enregis<PERSON>r le jeton", "@sage/xtrem-sage-network/nodes__organization__mutation__saveToken__failed": "Échec de sauvegarde du jeton", "@sage/xtrem-sage-network/nodes__organization__node_name": "Organisation", "@sage/xtrem-sage-network/nodes__organization__property__adminEmail": "E-mail admin", "@sage/xtrem-sage-network/nodes__organization__property__apiMode": "Mode API", "@sage/xtrem-sage-network/nodes__organization__property__apiURL": "URL API", "@sage/xtrem-sage-network/nodes__organization__property__authURL": "URL d'authentification", "@sage/xtrem-sage-network/nodes__organization__property__companies": "Sociétés", "@sage/xtrem-sage-network/nodes__organization__property__defaultLanguage": "Langue par défaut", "@sage/xtrem-sage-network/nodes__organization__property__externalId": "Code externe", "@sage/xtrem-sage-network/nodes__organization__property__id": "Code", "@sage/xtrem-sage-network/nodes__organization__property__isActive": "Active", "@sage/xtrem-sage-network/nodes__organization__property__isConnected": "Connectée", "@sage/xtrem-sage-network/nodes__organization__property__isTokenValid": "Valid<PERSON> jeton", "@sage/xtrem-sage-network/nodes__organization__property__lookupURL": "URL de recherche", "@sage/xtrem-sage-network/nodes__organization__property__name": "Nom", "@sage/xtrem-sage-network/nodes__organization__property__organization": "Organisation", "@sage/xtrem-sage-network/nodes__organization__property__organizationId": "Code d'organisation", "@sage/xtrem-sage-network/nodes__organization__property__organizationPayload": "Charge organisation", "@sage/xtrem-sage-network/nodes__organization__property__primaryCountry": "Pays principal", "@sage/xtrem-sage-network/nodes__organization__property__primarySigningKey": "Clé de signature principale", "@sage/xtrem-sage-network/nodes__organization__property__sageCrmId": "Code Sage CRM", "@sage/xtrem-sage-network/nodes__organization__property__signatory": "Signataire", "@sage/xtrem-sage-network/nodes__organization__property__signingKey": "Clé de signature", "@sage/xtrem-sage-network/nodes__organization__property__token": "<PERSON><PERSON>", "@sage/xtrem-sage-network/nodes__organization__property__tokenCreation": "Création de jetons", "@sage/xtrem-sage-network/nodes__organization__property__v6ApiKey": "Clé API V6", "@sage/xtrem-sage-network/nodes__organization__property__v6ApiURL": "URL API V6", "@sage/xtrem-sage-network/nodes__organization__property__xApplication": "Application X", "@sage/xtrem-sage-network/nodes__organization__property__xV6Application": "Application X v6", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance": "Instance par défaut", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance__failed": "Échec de l'instance par défaut.", "@sage/xtrem-sage-network/nodes__organization__query__defaultInstance__parameter__defaultId": "Code par défaut", "@sage/xtrem-sage-network/nodes__organization__query__getOrganization": "Obtenir l'organisation", "@sage/xtrem-sage-network/nodes__organization__query__getOrganization__failed": "Échec de création d'organisation.", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-sage-network/nodes__sage_network_company__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany": "<PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany__failed": "Échec de création d'organisation", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__createCompany__parameter__id": "Code", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken": "Enregis<PERSON>r le jeton", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken__failed": "Échec de sauvegarde du jeton", "@sage/xtrem-sage-network/nodes__sage_network_company__mutation__saveToken__parameter__company": "Société", "@sage/xtrem-sage-network/nodes__sage_network_company__node_name": "Société Sage Network", "@sage/xtrem-sage-network/nodes__sage_network_company__property__company": "Société", "@sage/xtrem-sage-network/nodes__sage_network_company__property__companyId": "Code société", "@sage/xtrem-sage-network/nodes__sage_network_company__property__companyPayload": "Charge société", "@sage/xtrem-sage-network/nodes__sage_network_company__property__externalId": "Code externe", "@sage/xtrem-sage-network/nodes__sage_network_company__property__isCreated": "Création", "@sage/xtrem-sage-network/nodes__sage_network_company__property__isTokenValid": "Valid<PERSON> jeton", "@sage/xtrem-sage-network/nodes__sage_network_company__property__token": "<PERSON><PERSON>", "@sage/xtrem-sage-network/nodes__sage_network_company__property__tokenCreation": "Création de jetons", "@sage/xtrem-sage-network/organization-not-created": "L'organisation Sage Network n'a pas été créée.", "@sage/xtrem-sage-network/package__name": "Sage DMO Sage Network", "@sage/xtrem-sage-network/pages__organization____title": "Configuration Sage Network", "@sage/xtrem-sage-network/pages__organization__adminEmail____title": "E-mail admin", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__name": "Nom", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__sageNetworkCompany__externalId": "Code externe", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__sageNetworkCompany__isCreated": "<PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/pages__organization__companies____columns__title__taxEngine": "Moteur de taxe", "@sage/xtrem-sage-network/pages__organization__companies____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/pages__organization__companies____title": "Sociétés", "@sage/xtrem-sage-network/pages__organization__companySection____title": "Société", "@sage/xtrem-sage-network/pages__organization__createOrganization____title": "Créer l'organisation", "@sage/xtrem-sage-network/pages__organization__defaultLanguage____title": "Langue par défaut", "@sage/xtrem-sage-network/pages__organization__getToken____title": "<PERSON><PERSON><PERSON><PERSON> le jeton", "@sage/xtrem-sage-network/pages__organization__isActive____title": "Active", "@sage/xtrem-sage-network/pages__organization__isConnected____title": "Connectée", "@sage/xtrem-sage-network/pages__organization__mainSection____title": "Général", "@sage/xtrem-sage-network/pages__organization__name____title": "Nom", "@sage/xtrem-sage-network/pages__organization__primaryCountry____title": "Pays", "@sage/xtrem-sage-network/pages__organization__resetOrganization____title": "Réinitialiser l'organisation", "@sage/xtrem-sage-network/pages__organization__sageCrmId____title": "Code Sage CRM", "@sage/xtrem-sage-network/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/permission__default_instance__name": "Instance par défaut", "@sage/xtrem-sage-network/permission__delete__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-sage-network/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-sage-network/permission__update__name": "Modifier", "@sage/xtrem-sage-network/service_options__sage_network_option__name": "Option Sage Network"}