import type { Graph<PERSON>pi, Organization as OrganizationNode } from '@sage/xtrem-sage-network-api';
import type { Company } from '@sage/xtrem-system-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { sageNetwork } from '../menu-items/sage-network';

@ui.decorators.page<Organization, OrganizationNode>({
    module: 'xtrem-sage-network',
    node: '@sage/xtrem-sage-network/Organization',
    title: 'Sage Network configuration',
    menuItem: sageNetwork,
    mode: 'tabs',
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
        });
        this.createOrganization.isDisabled = isDirty;
        this.getToken.isDisabled = isDirty;
        this.resetOrganization.isDisabled = isDirty;
    },
    businessActions() {
        return [this.createOrganization, this.getToken, this.resetOrganization, this.$standardSaveAction];
    },
    async defaultEntry() {
        return (
            (
                await this.$.graph
                    .node('@sage/xtrem-sage-network/Organization')
                    .queries.defaultInstance({ _id: true }, {})
                    .execute()
            )?._id || null
        );
    },
    onError(error) {
        // TODO : Better handling of errors ( go on error.errors array filter with path )
        return error.message;
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
        });
    },
})
export class Organization extends ui.Page<GraphApi, OrganizationNode> {
    @ui.decorators.section<Organization>({
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<Organization>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.section<Organization>({
        title: 'Company',
    })
    companySection: ui.containers.Section;

    @ui.decorators.switchField<Organization>({
        parent() {
            return this.mainBlock;
        },
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<Organization>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    fieldSeparatorIsActive: ui.fields.Separator;

    @ui.decorators.textField<Organization>({
        isHidden: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<Organization>({
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
        isReadOnly: true,
    })
    name: ui.fields.Text;

    @ui.decorators.checkboxField<Organization>({
        parent() {
            return this.mainBlock;
        },
        title: 'Connected',
        isReadOnly: true,
    })
    isConnected: ui.fields.Checkbox;

    @ui.decorators.textField<Organization>({
        isHidden: true,
    })
    organizationId: ui.fields.Text;

    @ui.decorators.textField<Organization>({
        isHidden: true,
    })
    externalId: ui.fields.Text;

    @ui.decorators.textField<Organization>({
        parent() {
            return this.mainBlock;
        },
        title: 'Sage CRM ID',
    })
    sageCrmId: ui.fields.Text;

    @ui.decorators.textField<Organization>({
        parent() {
            return this.mainBlock;
        },
        title: 'Country',
    })
    primaryCountry: ui.fields.Text;

    @ui.decorators.textField<Organization>({
        parent() {
            return this.mainBlock;
        },
        title: 'Admin email',
    })
    adminEmail: ui.fields.Text;

    @ui.decorators.textField<Organization>({
        parent() {
            return this.mainBlock;
        },
        title: 'Default language',
    })
    defaultLanguage: ui.fields.Text;

    @ui.decorators.tableField<Organization, Company>({
        parent() {
            return this.companySection;
        },
        title: 'Companies',
        node: '@sage/xtrem-system/Company',
        pageSize: 10,
        canSelect: false,
        isReadOnly: true,
        displayMode: ui.fields.TableDisplayMode.compact,
        columns: [
            ui.nestedFields.technical({
                bind: 'id',
            }),
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
            }),
            ui.nestedFields.text({
                bind: { sageNetworkCompany: { externalId: true } },
                title: 'External ID',
            }),
            ui.nestedFields.select({
                optionType: '@sage/xtrem-finance-data/TaxEngine',
                bind: 'taxEngine',
                title: 'Tax engine',
            }),
            ui.nestedFields.checkbox({
                bind: { sageNetworkCompany: { isCreated: true } },
                title: 'Created',
            }),
            ui.nestedFields.technical({
                bind: 'sageNetworkCompany',
                nestedFields: [
                    ui.nestedFields.technical({
                        bind: 'externalId',
                    }),
                ],
            }),
        ],
        dropdownActions: [
            {
                icon: 'create',
                title: 'Create',
                isDisabled(_rowId, rowData) {
                    return rowData.sageNetworkCompany?.isCreated ?? false;
                },
                onError(error) {
                    return error.message;
                },
                async onClick(rowId, rowData) {
                    await this.$.graph
                        .node('@sage/xtrem-sage-network/SageNetworkCompany')
                        .mutations.createCompany(true, { id: rowData.id })
                        .execute();
                    await this.companies.refreshRecord(rowId);
                },
            },
        ],
    })
    companies: ui.fields.Table<Company>;

    @ui.decorators.pageAction<Organization>({
        icon: 'analysis',
        title: 'Create organization',
        isHidden() {
            return this.organizationId.value !== '';
        },
        onError(error) {
            return error.message;
        },
        async onClick() {
            await this.$.graph
                .node('@sage/xtrem-sage-network/Organization')
                .mutations.createOrganization(true, true)
                .execute();
            await this.$.router.refresh();
        },
    })
    createOrganization: ui.PageAction;

    @ui.decorators.pageAction<Organization>({
        icon: 'analysis',
        title: 'Reset organization',
        isHidden() {
            return this.organizationId.value === '';
        },
        onError(error) {
            return error.message;
        },
        async onClick() {
            await this.$.graph
                .node('@sage/xtrem-sage-network/Organization')
                .mutations.resetOrganization(true, true)
                .execute();
            await this.$.router.refresh();
        },
    })
    resetOrganization: ui.PageAction;

    @ui.decorators.pageAction<Organization>({
        icon: 'analysis',
        title: 'Get token',
        isHidden() {
            return this.organizationId.value === '';
        },
        async onClick() {
            await this.$.graph.node('@sage/xtrem-sage-network/Organization').mutations.saveToken(true, true).execute();
            await this.$.router.refresh();
        },
    })
    getToken: ui.PageAction;
}
