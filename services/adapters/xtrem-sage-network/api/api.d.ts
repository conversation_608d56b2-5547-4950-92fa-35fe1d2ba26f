declare module '@sage/xtrem-sage-network-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type {
        BankAccount,
        CompanyAttributeType,
        CompanyAttributeTypeBinding,
        CompanyAttributeTypeInput,
        CompanyDefaultAttribute,
        CompanyDefaultAttributeBinding,
        CompanyDefaultAttributeInput,
        CompanyDefaultDimension,
        CompanyDefaultDimensionBinding,
        CompanyDefaultDimensionInput,
        CompanyDimensionType,
        CompanyDimensionTypeBinding,
        CompanyDimensionTypeInput,
        Package as SageXtremFinanceData$Package,
        PostingClass,
    } from '@sage/xtrem-finance-data-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type { Package as SageXtremLandedCost$Package } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        CompanyAddress,
        CompanyAddressBinding,
        CompanyAddressInput,
        CompanyContact,
        CompanyContactBinding,
        CompanyContactInput,
        Currency,
        Package as SageXtremMasterData$Package,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type { Package as SageXtremStockData$Package } from '@sage/xtrem-stock-data-api';
    import type {
        ChartOfAccount,
        Country,
        Legislation,
        Package as SageXtremStructure$Package,
    } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type {
        Company,
        CompanyInput,
        Package as SageXtremSystem$Package,
        Site,
        SysVendor,
        User,
    } from '@sage/xtrem-system-api';
    import type { Package as SageXtremTax$Package } from '@sage/xtrem-tax-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface ApiV6Stage$Enum {
        UploadUrl: 1;
        StartReport: 2;
        OperationStatus: 3;
        ReportStatus: 4;
        DownloadUrl: 5;
        DownloadResults: 6;
    }
    export type ApiV6Stage = keyof ApiV6Stage$Enum;
    export interface RequestStatus$Enum {
        pending: 0;
        inProgress: 1;
        awaitingResult: 2;
        success: 3;
        error: 4;
        uploadFailed: 5;
    }
    export type RequestStatus = keyof RequestStatus$Enum;
    export interface Organization extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        isConnected: boolean;
        name: string;
        organizationId: string;
        sageCrmId: string;
        primaryCountry: string;
        adminEmail: string;
        defaultLanguage: string;
        externalId: string;
        tokenCreation: string;
        isTokenValid: boolean;
        organizationPayload: string;
        companies: ClientCollection<Company>;
    }
    export interface OrganizationInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        isActive?: boolean | string;
        organizationId?: string;
        sageCrmId?: string;
        primaryCountry?: string;
        adminEmail?: string;
        defaultLanguage?: string;
        externalId?: string;
        tokenCreation?: string;
    }
    export interface OrganizationBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        isConnected: boolean;
        name: string;
        organizationId: string;
        sageCrmId: string;
        primaryCountry: string;
        adminEmail: string;
        defaultLanguage: string;
        externalId: string;
        tokenCreation: string;
        isTokenValid: boolean;
        organizationPayload: any;
        companies: ClientCollection<Company>;
    }
    export interface Organization$Queries {
        defaultInstance: Node$Operation<
            {
                defaultId?: string;
            },
            Organization
        >;
        getOrganization: Node$Operation<{}, boolean>;
    }
    export interface Organization$Mutations {
        createOrganization: Node$Operation<{}, boolean>;
        resetOrganization: Node$Operation<{}, boolean>;
        saveToken: Node$Operation<{}, string>;
    }
    export interface Organization$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Organization$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface Organization$Operations {
        query: QueryOperation<Organization>;
        read: ReadOperation<Organization>;
        aggregate: {
            read: AggregateReadOperation<Organization>;
            query: AggregateQueryOperation<Organization>;
        };
        queries: Organization$Queries;
        create: CreateOperation<OrganizationInput, Organization>;
        getDuplicate: GetDuplicateOperation<Organization>;
        update: UpdateOperation<OrganizationInput, Organization>;
        updateById: UpdateByIdOperation<OrganizationInput, Organization>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: Organization$Mutations;
        asyncOperations: Organization$AsyncOperations;
        lookups(dataOrId: string | { data: OrganizationInput }): Organization$Lookups;
        getDefaults: GetDefaultsOperation<Organization>;
    }
    export interface SageNetworkCompany extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        company: Company;
        externalId: string;
        companyId: string;
        companyPayload: string;
        token: string;
        tokenCreation: string;
        isCreated: boolean;
        isTokenValid: boolean;
    }
    export interface SageNetworkCompanyInput extends VitalClientNodeInput {
        externalId?: string;
        companyId?: string;
        token?: string;
        tokenCreation?: string;
    }
    export interface SageNetworkCompanyBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        company: Company;
        externalId: string;
        companyId: string;
        companyPayload: any;
        token: string;
        tokenCreation: string;
        isCreated: boolean;
        isTokenValid: boolean;
    }
    export interface SageNetworkCompany$Mutations {
        saveToken: Node$Operation<
            {
                company: string;
            },
            string
        >;
        createCompany: Node$Operation<
            {
                id?: string;
            },
            boolean
        >;
    }
    export interface SageNetworkCompany$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SageNetworkCompany$Operations {
        query: QueryOperation<SageNetworkCompany>;
        read: ReadOperation<SageNetworkCompany>;
        aggregate: {
            read: AggregateReadOperation<SageNetworkCompany>;
            query: AggregateQueryOperation<SageNetworkCompany>;
        };
        create: CreateOperation<SageNetworkCompanyInput, SageNetworkCompany>;
        getDuplicate: GetDuplicateOperation<SageNetworkCompany>;
        update: UpdateOperation<SageNetworkCompanyInput, SageNetworkCompany>;
        updateById: UpdateByIdOperation<SageNetworkCompanyInput, SageNetworkCompany>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: SageNetworkCompany$Mutations;
        asyncOperations: SageNetworkCompany$AsyncOperations;
        getDefaults: GetDefaultsOperation<SageNetworkCompany>;
    }
    export interface CompanyExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        name: string;
        description: string;
        legislation: Legislation;
        chartOfAccount: ChartOfAccount;
        hierarchyChartContent: TextStream;
        siren: string;
        naf: string;
        rcs: string;
        legalForm: LegalForm;
        country: Country;
        currency: Currency;
        priceScale: integer;
        addresses: ClientCollection<CompanyAddress>;
        primaryAddress: CompanyAddress;
        primaryContact: CompanyContact;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        customerOnHoldCheck: CustomerOnHoldType;
        contacts: ClientCollection<CompanyContact>;
        postingClass: PostingClass;
        taxEngine: TaxEngine;
        doStockPosting: boolean;
        doWipPosting: boolean;
        doArPosting: boolean;
        doApPosting: boolean;
        doNonAbsorbedPosting: boolean;
        attributeTypes: ClientCollection<CompanyAttributeType>;
        dimensionTypes: ClientCollection<CompanyDimensionType>;
        defaultAttributes: ClientCollection<CompanyDefaultAttribute>;
        defaultDimensions: ClientCollection<CompanyDefaultDimension>;
        datevConsultantNumber: integer;
        datevCustomerNumber: integer;
        bankAccount: BankAccount;
        sageNetworkCompany: SageNetworkCompany;
        financialSite: Site;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface CompanyInputExtension {
        id?: string;
        isActive?: boolean | string;
        name?: string;
        description?: string;
        legislation?: integer | string;
        chartOfAccount?: integer | string;
        siren?: string;
        naf?: string;
        rcs?: string;
        legalForm?: LegalForm;
        country?: integer | string;
        currency?: integer | string;
        addresses?: Partial<CompanyAddressInput>[];
        sequenceNumberId?: string;
        customerOnHoldCheck?: CustomerOnHoldType;
        contacts?: Partial<CompanyContactInput>[];
        postingClass?: integer | string;
        taxEngine?: TaxEngine;
        doStockPosting?: boolean | string;
        doWipPosting?: boolean | string;
        doNonAbsorbedPosting?: boolean | string;
        attributeTypes?: Partial<CompanyAttributeTypeInput>[];
        dimensionTypes?: Partial<CompanyDimensionTypeInput>[];
        defaultAttributes?: Partial<CompanyDefaultAttributeInput>[];
        defaultDimensions?: Partial<CompanyDefaultDimensionInput>[];
        datevConsultantNumber?: integer | string;
        datevCustomerNumber?: integer | string;
        bankAccount?: integer | string;
        sageNetworkCompany?: SageNetworkCompanyInput;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface CompanyBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        name: string;
        description: string;
        legislation: Legislation;
        chartOfAccount: ChartOfAccount;
        hierarchyChartContent: TextStream;
        siren: string;
        naf: string;
        rcs: string;
        legalForm: LegalForm;
        country: Country;
        currency: Currency;
        priceScale: integer;
        addresses: ClientCollection<CompanyAddressBinding>;
        primaryAddress: CompanyAddress;
        primaryContact: CompanyContact;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        customerOnHoldCheck: CustomerOnHoldType;
        contacts: ClientCollection<CompanyContactBinding>;
        postingClass: PostingClass;
        taxEngine: TaxEngine;
        doStockPosting: boolean;
        doWipPosting: boolean;
        doArPosting: boolean;
        doApPosting: boolean;
        doNonAbsorbedPosting: boolean;
        attributeTypes: ClientCollection<CompanyAttributeTypeBinding>;
        dimensionTypes: ClientCollection<CompanyDimensionTypeBinding>;
        defaultAttributes: ClientCollection<CompanyDefaultAttributeBinding>;
        defaultDimensions: ClientCollection<CompanyDefaultDimensionBinding>;
        datevConsultantNumber: integer;
        datevCustomerNumber: integer;
        bankAccount: BankAccount;
        sageNetworkCompany: SageNetworkCompanyBinding;
        financialSite: Site;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface CompanyExtension$Lookups {
        financialSite: QueryOperation<Site>;
    }
    export interface CompanyExtension$Operations {
        lookups(dataOrId: string | { data: CompanyInput }): CompanyExtension$Lookups;
    }
    export interface Package {
        '@sage/xtrem-sage-network/Organization': Organization$Operations;
        '@sage/xtrem-sage-network/SageNetworkCompany': SageNetworkCompany$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremFinanceData$Package,
            SageXtremImportExport$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-sage-network-api' {
    export type * from '@sage/xtrem-sage-network-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-sage-network-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type {
        CompanyBindingExtension,
        CompanyExtension,
        CompanyExtension$Lookups,
        CompanyExtension$Operations,
        CompanyInputExtension,
    } from '@sage/xtrem-sage-network-api';
    export interface Company extends CompanyExtension {}
    export interface CompanyBinding extends CompanyBindingExtension {}
    export interface CompanyInput extends CompanyInputExtension {}
    export interface Company$Lookups extends CompanyExtension$Lookups {}
    export interface Company$Operations extends CompanyExtension$Operations {}
}
