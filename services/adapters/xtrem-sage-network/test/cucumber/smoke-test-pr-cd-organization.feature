@xtrem_master_data
Feature: smoke-test-pr-cd-organization

    Scenario: Update organization details
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sage-network/Organization"
        # Goes straight to the record view
        Then the "Sage Network configuration" titled page is displayed
        # Check the existing data
        And the user selects the "Sage CRM ID" labelled text field on the main page
        And the value of the text field is "123456789900"
        And the user selects the "Country" labelled text field on the main page
        And the value of the text field is "GBR"
        And the user selects the "admin email" labelled text field on the main page
        And the value of the text field is "<EMAIL>"
        # Amend the data
        And the user selects the "Sage CRM ID" labelled text field on the main page
        And the user writes "987654321" in the text field
        And the user selects the "Country" labelled text field on the main page
        And the user writes "FRA" in the text field
        And the user selects the "admin email" labelled text field on the main page
        And the user writes "<EMAIL>" in the text field
        #Click Save Crud Button
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then the "Sage Network configuration" titled page is displayed

    Sc<PERSON><PERSON>: Revert the changes to the organization details
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sage-network/Organization"
        # Goes straight to the record view
        Then the "Sage Network configuration" titled page is displayed
        # Check the existing data
        And the user selects the "Sage CRM ID" labelled text field on the main page
        And the value of the text field is "987654321"
        And the user selects the "Country" labelled text field on the main page
        And the value of the text field is "FRA"
        And the user selects the "admin email" labelled text field on the main page
        And the value of the text field is "<EMAIL>"
        # Amend the data
        And the user selects the "Sage CRM ID" labelled text field on the main page
        And the user writes "123456789900" in the text field
        And the user selects the "Country" labelled text field on the main page
        And the user writes "GBE" in the text field
        And the user selects the "admin email" labelled text field on the main page
        And the user writes "<EMAIL>" in the text field
        #Click Save Crud Button
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then the "Sage Network configuration" titled page is displayed
