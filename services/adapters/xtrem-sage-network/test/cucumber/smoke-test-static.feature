@xtrem_finance
Feature: smoke-test-static

    #Case without navigation panel full width
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                  | Title                      |
            | @sage/xtrem-sage-network/Organization | Sage Network configuration |

    # Positive test to ensure that the error dialog is definitely exist and openA
    Scenario: sts \ xtrem-finance \ desktop \ check an error dialog appears when the page does not load
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Wazup"
        Then an error dialog appears on the main page
