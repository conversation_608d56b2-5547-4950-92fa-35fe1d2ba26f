import type { Context, NodePayloadData } from '@sage/xtrem-core';
import { ConfigManager, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremSageNetwork from '../../../index.js';
import { Organization } from '../../../lib/nodes/organization.js';

describe('Organization test ', () => {
    it('Organization test read record', async () => {
        await Test.withContext(async context => {
            const org = await context.read(xtremSageNetwork.nodes.Organization, { id: 'DEFAULT' });
            assert.equal(await org.id, 'DEFAULT');
            assert.equal(await org.name, 'Tenant for tests (automatic creation)');
            assert.equal(await org.isTokenValid, false);
            assert.equal(await org.isConnected, false);
            assert.isAbove(await org.companies.length, 0);

            assert.deepEqual(await org.organizationPayload, {
                name: await org.name,
                adminEmail: await org.adminEmail,
                defaultLanguage: await org.defaultLanguage,
                externalId: await org.externalId,
                sageCrmId: await org.sageCrmId,
                primaryCountry: await org.primaryCountry,
            });
        });
    });
    it('Organization test read record - with no data', async () => {
        await Test.withContext(async context => {
            const org = await context.read(xtremSageNetwork.nodes.Organization, { id: 'TEST' });
            assert.equal(await org.id, 'TEST');
            assert.equal((await org.organizationPayload).name, 'Tenant for tests (automatic creation)');
            assert.equal((await org.organizationPayload).adminEmail, '<EMAIL>');
            assert.equal((await org.organizationPayload).defaultLanguage, 'EN');
            assert.equal((await org.organizationPayload).externalId.includes('-'), true);
            assert.equal((await org.organizationPayload).sageCrmId, '777777777777777777777');
            assert.equal((await org.organizationPayload).primaryCountry, 'FRA');
        });
    });
    it('Organization test default instance', async () => {
        await Test.withContext(async context => {
            const defaultOrg = await xtremSageNetwork.nodes.Organization.defaultInstance(context);
            const org = await context.read(xtremSageNetwork.nodes.Organization, { id: 'DEFAULT' });

            assert.deepEqual(org, defaultOrg);
        });
    });
    it('Organization test config function', async () => {
        const config = { ...ConfigManager.current };
        if (config.packages) {
            delete config.packages['@sage/xtrem-sage-network'];
        }
        await Test.withContext(
            context => {
                xtremSageNetwork.classes.RequestApiV6.resetConfig();

                assert.throws(() => new xtremSageNetwork.classes.RequestMain(context), '');
            },
            {
                config,
            },
        );
    });
    it('Organization test resetOrganization', () =>
        Test.withContext(async context => {
            const org = await context.read(xtremSageNetwork.nodes.Organization, { id: 'DEFAULT' });
            assert.equal(await org.id, 'DEFAULT');
            assert.equal(await org.name, 'Tenant for tests (automatic creation)');
            assert.equal(await org.isTokenValid, false);
            assert.equal(await org.isConnected, false);
            assert.equal(await org.organizationId, '7c964f54-14ba-4c1e-822a-314f322b0b7c');
            assert.isAbove(await org.companies.length, 0);
            await Organization.resetOrganization(context);
            const org2 = await context.read(xtremSageNetwork.nodes.Organization, { id: 'DEFAULT' });
            assert.equal(await org2.id, 'DEFAULT');
            assert.equal(await org2.name, 'Tenant for tests (automatic creation)');
            assert.equal(await org2.isTokenValid, false);
            assert.equal(await org2.isConnected, false);
            assert.equal(await org2.organizationId, '');
            assert.equal(await org2.primarySigningKey, '');
            assert.isAbove(await org2.companies.length, 0);
        }));
    it('Organization test - createOrganization', () =>
        Test.withContext(async context => {
            const createOrganizationStub = sinon
                .stub(xtremSageNetwork.classes.Authentication, 'createOrganization')
                .callsFake((passedContext: Context) => {
                    if (passedContext) {
                        return Promise.resolve({
                            sageCrmId: 'crmId',
                            adminEmail: '<EMAIL>',
                            defaultLanguage: 'EN',
                            primaryCountry: 'GBR',
                            externalId: 'externalId',
                            organizationId: 'organizationId',
                            primarySigningKey: 'primarySigningKey',
                        } as NodePayloadData<xtremSageNetwork.nodes.Organization>);
                    }
                    throw new Error('Error');
                });

            const result = await xtremSageNetwork.nodes.Organization.createOrganization(context);
            assert.isTrue(result);
            createOrganizationStub.restore();
        }));
    it('Organization test - getOrganization', () =>
        Test.withContext(async context => {
            const getOrganizationStub = sinon
                .stub(xtremSageNetwork.classes.Authentication, 'getOrganization')
                .callsFake((passedContext: Context) => {
                    if (passedContext) {
                        return Promise.resolve({
                            name: 'name',
                            sageCrmId: 'crmId',
                            adminEmail: '<EMAIL>',
                            defaultLanguage: 'EN',
                            primaryCountry: 'GBR',
                            externalId: 'externalId',
                            organizationId: 'organizationId',
                            primarySigningKey: 'primarySigningKey',
                        });
                    }
                    throw new Error('Error');
                });

            const result = await xtremSageNetwork.nodes.Organization.getOrganization(context);
            assert.isTrue(result);
            getOrganizationStub.restore();
        }));
});
