import type { Context } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremSageNetwork from '../../../index.js';

describe('Sage network company test ', () => {
    it('Save company token test', async () => {
        await Test.withContext(async context => {
            const company = await context.read(xtremSystem.nodes.Company, { id: 'US001' }, { forUpdate: true });
            const getCompanyTokenStub = sinon
                .stub(xtremSageNetwork.classes.Authentication, 'getCompanyToken')
                .callsFake((passedContext: Context, companyId: string) => {
                    if (companyId && passedContext) {
                        return Promise.resolve('Token 1234');
                    }
                    throw new Error('Error');
                });
            await xtremSageNetwork.nodes.SageNetworkCompany.saveToken(context, company);
            const company2 = await context.read(xtremSystem.nodes.Company, { id: 'US001' }, { forUpdate: true });
            assert.equal(await (await company2.sageNetworkCompany)?.$.decryptValue('token'), 'Token 1234');
            getCompanyTokenStub.restore();
        });
    });
    it('SageNetworkCompany test createSageNetworkCompany', () =>
        Test.withContext(async context => {
            const oldCreateCompany = xtremSageNetwork.classes.Authentication.createCompany;
            xtremSageNetwork.classes.Authentication.createCompany = sinon.stub().resolves({
                name: 'US Process Manufacturing 001',
                address: {
                    addressLine1: '1 Some street',
                    addressLine2: 'Some building',
                    addressLine3: '',
                    addressLine4: '',
                    country: 'ZAF',
                    countrySubdivision: 'Gauteng',
                    postalCode: '0123',
                },
                contactEmail: '<EMAIL>',
                contactTelNo: '+27821234567',
                companyId: '123456',
                externalId: 'aea72a01-2e3c-4587-98b1-86e00357c437',
                standardIndustrialCode: '',
                taxNumber: '',
                logoUrl: '',
                organisationId: '7c964f54-14ba-4c1e-822a-314f322b0b7c',
            });
            const result = await xtremSageNetwork.nodes.SageNetworkCompany.createCompany(context, 'US001');
            assert.equal(result, true);
            const company = await context.read(xtremSystem.nodes.Company, { id: 'US001' });
            assert.equal(await (await company.sageNetworkCompany)?.externalId, 'aea72a01-2e3c-4587-98b1-86e00357c437');
            assert.equal(await (await company.sageNetworkCompany)?.companyId, '123456');
            xtremSageNetwork.classes.Authentication.createCompany = oldCreateCompany;
        }));
});
