import { ConfigManager, Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremSageNetwork from '../../../index.js';

describe('Request Main class ', () => {
    const defaultConfig = {
        apiMode: 'sandbox',
        signingKey: '111111111',
        v6Application: 'sdmo',
        application: 'sdmo',
        signatory: 'sdmo',
        v6ApiKeySandbox: 'xxxxxxx',
    };

    it('Request main test config function', async () => {
        const config = { ...ConfigManager.current };
        if (!config.packages) {
            return;
        }
        delete config.packages['@sage/xtrem-sage-network'];
        xtremSageNetwork.classes.RequestApiV6.resetConfig();
        await Test.withContext(
            context => {
                assert.throws(
                    () => new xtremSageNetwork.classes.RequestMain(context),
                    'Missing package config @sage/xtrem-sage-network',
                );
            },
            {
                config,
            },
        );
    });

    it('Request main test config function - pass in config', async () => {
        const config = { ...ConfigManager.current };
        if (!config.packages) {
            return;
        }
        delete config.packages['@sage/xtrem-sage-network'];
        xtremSageNetwork.classes.RequestApiV6.resetConfig();
        config.packages['@sage/xtrem-sage-network'] = {
            ...defaultConfig,
            signingKey: '111111111',
            v6Application: 'sdmo',
        };
        await Test.withContext(
            context => {
                const requestMain2 = new xtremSageNetwork.classes.RequestMain(context, {
                    apiMode: 'sandbox',
                    signingKey: 'signKey',
                    application: 'application',
                    v6Application: 'v6application',
                    signatory: 'signatory',
                    authURL: 'authUrl',
                    v6ApiKey: 'v6ApiKey',
                    apiURL: 'apiUrl',
                    lookupURL: 'lookupUrl',
                    url: 'url',
                    v6ApiURL: 'v6ApiUrl',
                    xNonceUuid: 'xNonce',
                    notificationsEnabled: true,
                });
                assert.equal(requestMain2.apiMode, 'sandbox');
                assert.equal(requestMain2.signKey, 'signKey');
                assert.equal(requestMain2.xApplication, 'application');
                assert.equal(requestMain2.xV6Application, 'v6application');
                assert.equal(requestMain2.signatory, 'signatory');
                assert.equal(requestMain2.authURL, 'authUrl');
                assert.equal(requestMain2.v6ApiKey, 'v6ApiKey');
                assert.equal(requestMain2.apiURL, 'apiUrl');
                assert.equal(requestMain2.lookupURL, 'lookupUrl');
                assert.equal(requestMain2.url, 'url');
                assert.equal(requestMain2.v6ApiURL, 'v6ApiUrl');
                assert.equal(requestMain2.xNonce, 'xNonce');
                assert.equal(requestMain2.notificationsEnabled, true);
            },
            { config },
        );
    });

    it(' Request Main Class - activeInstance', () =>
        Test.withContext(async context => {
            const requestMain = new xtremSageNetwork.classes.RequestMain(context);

            const org = await requestMain.activeInstance;

            assert.equal(await org.name, 'Tenant for tests (automatic creation)');
        }));
    it(' Request Main Class - no activeInstance', () =>
        Test.withContext(async context => {
            const requestMain = new xtremSageNetwork.classes.RequestMain(context);
            const org = await context.read(xtremSageNetwork.nodes.Organization, { id: 'DEFAULT' }, { forUpdate: true });
            await org.$.delete();

            await assert.isRejected(requestMain.activeInstanceRefresh(), '');
        }));
    it(' Request Main Class - activeInstanceRefresh', () =>
        Test.withContext(async context => {
            const requestMain = new xtremSageNetwork.classes.RequestMain(context);

            const org = await requestMain.activeInstanceRefresh();

            assert.equal(await org.name, 'Tenant for tests (automatic creation)');
        }));
    it(' Request Main Class - getSignedRequest', () =>
        Test.withContext(async context => {
            const requestMain = new xtremSageNetwork.classes.RequestMain(context, {
                signingKey: '12345',
                xNonceUuid: 'XXXXXXX',
            });
            const org = await requestMain.activeInstance;
            assert.equal(await org.name, 'Tenant for tests (automatic creation)');
            const signedRequest = requestMain.getSignedRequest();
            assert.equal(signedRequest['X-Signature'], 'j++1RsgQwG1Q+GymBHFAMaIa4kA=');
            assert.equal(signedRequest['X-Nonce'], 'XXXXXXX');
        }));
    it(' Request Main Class - getBearerAuthentication', () =>
        Test.withContext(async context => {
            const requestMain = new xtremSageNetwork.classes.RequestMain(context, { xNonceUuid: 'XXXXXXX' });
            const org = await requestMain.activeInstance;
            assert.equal(await org.name, 'Tenant for tests (automatic creation)');
            // Have to add the any as this is a private method
            const mockAuth = sinon.stub(requestMain, <any>'getOrganizationToken');
            mockAuth.resolves('New organization token');
            // Check the getOrganizationToken method
            const authorization = await requestMain.getBearerAuthentication();
            assert.equal(authorization.Authorization, 'Bearer New organization token');
            const mockAuth2 = sinon.stub(requestMain, <any>'getCompanyToken');
            mockAuth2.resolves('New company token');
            // Check the getCompanyToken method
            const authorization2 = await requestMain.getBearerAuthentication(
                await (await requestMain.activeInstance).companies.at(0),
            );
            assert.equal(authorization2.Authorization, 'Bearer New company token');
            // Check getCompanyToken method with a new token
            const oldSaveToken = xtremSageNetwork.nodes.SageNetworkCompany.saveToken;
            mockAuth2.restore();
            xtremSageNetwork.nodes.SageNetworkCompany.saveToken = sinon.stub().resolves('Another new company token');
            const readOnlyCompany = await (await requestMain.activeInstance).companies.at(0);
            const company = await context.read(
                xtremSystem.nodes.Company,
                { _id: readOnlyCompany?._id },
                { forUpdate: true },
            );
            await company?.$.set({ sageNetworkCompany: { tokenCreation: null } });
            const authorization3 = await requestMain.getBearerAuthentication(company);
            assert.equal(authorization3.Authorization, 'Bearer Another new company token');
            xtremSageNetwork.nodes.SageNetworkCompany.saveToken = oldSaveToken;
        }));
    it(' Request Main Class - getHeaders - with token', () =>
        Test.withContext(async context => {
            xtremSageNetwork.classes.RequestApiV6.resetConfig();

            const requestMain = new xtremSageNetwork.classes.RequestMain(context, {
                withToken: true,
                xNonceUuid: 'XXXXXXX',
            });
            const org = await requestMain.activeInstance;
            assert.equal(await org.name, 'Tenant for tests (automatic creation)');
            // Have to add the any as this is a private method
            const mockAuth = sinon.stub(requestMain, 'getBearerAuthentication');
            mockAuth.resolves({ Authorization: 'New bearer token' });
            // Check the getOrganizationToken method
            const headers = await requestMain.getHeaders();
            assert.equal(
                JSON.stringify(headers),
                '{"Content-Type":"application/json","X-Application":"sdmo","Authorization":"New bearer token"}',
            );
            mockAuth.restore();
        }));
    it(' Request Main Class - getHeaders - without token', () =>
        Test.withContext(async context => {
            xtremSageNetwork.classes.RequestApiV6.resetConfig();

            const requestMain = new xtremSageNetwork.classes.RequestMain(context, {
                withToken: false,
                xNonceUuid: 'XXXXXXX',
            });
            const org = await requestMain.activeInstance;
            assert.equal(await org.name, 'Tenant for tests (automatic creation)');
            // Have to add the any as this is a private method
            const mockAuth = sinon.stub(requestMain, 'getSignedRequest');
            mockAuth.returns({ 'X-Signature': 'New signature', 'X-Nonce': 'XXXXXXX' });
            // Check the getOrganizationToken method
            const headers = await requestMain.getHeaders();
            assert.equal(
                JSON.stringify(headers),
                '{"Content-Type":"application/json","X-Application":"sdmo","X-Signature":"New signature","X-Nonce":"XXXXXXX"}',
            );
            mockAuth.restore();
        }));
    it(' Request Main Class - no application', async () => {
        const config = { ...ConfigManager.current };
        if (!config.packages) {
            return;
        }
        delete config.packages['@sage/xtrem-sage-network'];
        config.packages['@sage/xtrem-sage-network'] = {
            testProperty: '1',
        };

        xtremSageNetwork.classes.RequestApiV6.resetConfig();

        await Test.withContext(context => {
            const req = new xtremSageNetwork.classes.RequestMain(context);
            assert.throws(() => req.apiMode, 'No API mode for the Sage Network API in the configuration');
            assert.throws(() => req.apiURL, 'No URL for the Sage Network API in the configuration.');
            assert.throws(() => req.authURL, 'No URL for the Sage Network authentication API in the configuration.');
            assert.throws(() => req.lookupURL, 'No URL for the Sage Network lookup API in the configuration.');
            assert.throws(() => req.signKey, 'No signing key for the Sage Network API in the configuration.');
            assert.throws(() => req.signatory, 'No signatory for the Sage Network API in the configuration.');
            assert.throws(() => req.v6ApiKey, 'No API key for the Sage Network V6 API in the configuration.');
            assert.throws(() => req.v6ApiURL, 'No URL for the Sage Network V6 API in the configuration.');
            assert.throws(() => req.xApplication, 'No application for the Sage Network API in the configuration.');
            assert.throws(() => req.xV6Application, 'No application for the Sage Network V6 API in the configuration.');
        });
    });
});
