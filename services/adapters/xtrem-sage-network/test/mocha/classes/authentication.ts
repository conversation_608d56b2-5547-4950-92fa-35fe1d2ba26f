import { ConfigManager, SystemError, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremSageNetwork from '../../../index.js';

const defaultConfig = {
    apiMode: 'sandbox',
    signingKey: '111111111',
    v6Application: 'sdmo',
    application: 'sdmo',
    signatory: 'sdmo',
    v6ApiKeySandbox: 'xxxxxxx',
    authURLSandbox: 'https://sage.com/auth',
    apiURLSandbox: 'https://sage.com/api',
    lookupURLSandbox: 'https://sage.com/lookup',
    v6ApiURLSandbox: 'https://sage.com/v6api',
};

const config = { ...ConfigManager.current };
if (!config.packages) {
    throw new Error('No packages in config');
}
delete config.packages['@sage/xtrem-sage-network'];
config.packages['@sage/xtrem-sage-network'] = {
    ...defaultConfig,
};

describe('Authentication class ', () => {
    it('Authentication Class - checkOrganization not active', () =>
        Test.withContext(
            async context => {
                // reset
                const auth = new xtremSageNetwork.classes.Authentication(context, {
                    api: 'organisations',
                    defaultOrganizationId: 'TEST',
                });
                await assert.isRejected(auth.hasOrganizationBeenCreated(), 'No active configuration');
            },
            { config },
        ));
    it('Authentication Class - checkOrganization no ids so false', () =>
        Test.withContext(
            async context => {
                // reset
                const auth = new xtremSageNetwork.classes.Authentication(context, {
                    api: 'organisations',
                    defaultOrganizationId: 'TESTNOIDS',
                });
                const orgCreated = await auth.hasOrganizationBeenCreated();
                assert.isFalse(orgCreated);
            },
            { config },
        ));
    it('Authentication Class - checkOrganization true', () =>
        Test.withContext(
            async context => {
                const auth = new xtremSageNetwork.classes.Authentication(context, {
                    api: 'organisations',
                });
                const orgCreated = await auth.isOrganizationCreated;
                assert.isTrue(orgCreated);
                const wasCreated = await auth.hasOrganizationBeenCreated();
                assert.isTrue(wasCreated);
                assert.equal(context.diagnoses.length, 0);
            },
            { config },
        ));
    it('Authentication Class - createOrganization', () =>
        Test.withContext(
            async context => {
                const auth = new xtremSageNetwork.classes.Authentication(context);
                const mockAuth = sinon.stub(auth, 'execute');
                mockAuth.resolves({
                    data: { name: 'noSysTenantName' },
                    errors: [],
                    status: 200,
                    statusText: 'OK',
                });
                const org = await auth.getOrganization();

                const organization = await auth.createOrganization(org);
                assert.equal(organization.name, 'noSysTenantName');
                mockAuth.restore();
            },
            { config },
        ));

    it(' Authentication Class - createOrganizationToken', () =>
        Test.withContext(
            async context => {
                const auth = new xtremSageNetwork.classes.Authentication(context);
                const mockAuth = sinon.stub(auth, 'execute');
                mockAuth.resolves({
                    data: { jwt: 'dummyToken' },
                    errors: [],
                    status: 200,
                    statusText: 'OK',
                });
                const token = await auth.createOrganizationToken();
                assert.equal(token, 'dummyToken');
                mockAuth.restore();
            },
            { config },
        ));
    it(' Authentication Class - createOrganizationToken fail', () =>
        Test.withContext(async context => {
            const auth = new xtremSageNetwork.classes.Authentication(context, {
                api: 'organisations',
                defaultOrganizationId: 'TESTNOIDS',
            });
            await assert.isRejected(auth.createOrganizationToken(), 'The Sage Network organization creation failed.');
        }));
    it(' Authentication Class - createCompanyToken', () =>
        Test.withContext(async context => {
            const auth = new xtremSageNetwork.classes.Authentication(context);
            const mockAuth = sinon.stub(auth, 'execute');
            mockAuth.resolves({
                data: { jwt: 'dummyToken' },
                errors: [],
                status: 200,
                statusText: 'OK',
            });
            const token = await auth.createCompanyToken('companyId');
            assert.equal(token, 'dummyToken');
            mockAuth.restore();
        }));
    it(' Authentication Class - createCompanyToken fail', () =>
        Test.withContext(
            async context => {
                const auth = new xtremSageNetwork.classes.Authentication(context, {
                    api: 'organisations',
                    defaultOrganizationId: 'TESTNOIDS',
                });
                await assert.isRejected(
                    auth.createCompanyToken('companyId'),
                    'The Sage Network organization creation failed.',
                );
            },
            { config },
        ));

    it(' Authentication Class - createCompany', () =>
        Test.withContext(
            async context => {
                const auth = new xtremSageNetwork.classes.Authentication(context);
                const mockAuth = sinon.stub(auth, 'execute');
                mockAuth.resolves({
                    data: {
                        name: 'US Process Manufacturing 001',
                        externalId: '*********',
                        taxNumber: '',
                        standardIndustrialCode: '',
                        contactTelNo: '+27821234567',
                        contactEmail: '<EMAIL>',
                        logoUrl: '',
                        organizationId: '',
                        address: {
                            addressLine1: '1 Some street',
                            addressLine2: 'Some building',
                            addressLine3: '',
                            addressLine4: '',
                            countrySubdivision: '',
                            postalCode: '0123',
                            country: 'ZAF',
                        },
                    },
                    errors: [],
                    status: 200,
                    statusText: 'OK',
                });

                await auth.createCompany({
                    name: 'US Process Manufacturing 001',
                    externalId: '*********',
                    taxNumber: '',
                    standardIndustrialCode: '',
                    contactTelNo: '+27821234567',
                    contactEmail: '<EMAIL>',
                    logoUrl: '',
                    organizationId: '',
                    address: {
                        addressLine1: '1 Some street',
                        addressLine2: 'Some building',
                        addressLine3: '',
                        addressLine4: '',
                        countrySubdivision: '',
                        postalCode: '0123',
                        country: 'ZAF',
                    },
                });

                mockAuth.resolves({
                    data: [
                        {
                            name: 'US Process Manufacturing 001',
                            externalId: '*********',
                            taxNumber: '',
                            standardIndustrialCode: '',
                            contactTelNo: '+27821234567',
                            contactEmail: '<EMAIL>',
                            address: {
                                addressLine1: '1 Some street',
                                addressLine2: 'Some building',
                                addressLine3: '',
                                addressLine4: '',
                                countrySubdivision: '',
                                postalCode: '0123',
                                country: 'ZAF',
                            },
                        },
                    ],

                    errors: [],
                    status: 200,
                    statusText: 'OK',
                });

                const getCompany = await auth.getCompany('US001');
                assert.equal(getCompany[0].name, 'US Process Manufacturing 001');
                assert.equal(getCompany[0].externalId, '*********');
                mockAuth.restore();
            },
            { config },
        ));

    it(' Authentication Class - createCompany failure', () =>
        Test.withContext(
            async context => {
                const auth = new xtremSageNetwork.classes.Authentication(context);
                const mockAuth = sinon.stub(auth, 'execute');
                mockAuth.rejects(
                    new SystemError(
                        `Internal Server Error 500 : There was an error [{"code":"500","description":"There was an error"}]`,
                    ),
                );

                await assert.isRejected(
                    auth.createCompany({
                        name: 'US Process Manufacturing 001',
                        externalId: '*********',
                        taxNumber: '',
                        standardIndustrialCode: '',
                        contactTelNo: '+27821234567',
                        contactEmail: '<EMAIL>',
                        logoUrl: '',
                        organizationId: '',
                        address: {
                            addressLine1: '1 Some street',
                            addressLine2: 'Some building',
                            addressLine3: '',
                            addressLine4: '',
                            countrySubdivision: '',
                            postalCode: '0123',
                            country: 'ZAF',
                        },
                    }),
                    'There was an error',
                );
                mockAuth.restore();
            },
            { config },
        ));

    it(' Authentication Class - updateCompany', () =>
        Test.withContext(
            async context => {
                const auth = new xtremSageNetwork.classes.Authentication(context);
                const mockAuth = sinon.stub(auth, 'execute');

                mockAuth.resolves({
                    data: [
                        {
                            companyId: 'dc4e28ca-97eb-4bf6-a62f-d13cdc2c0787',
                            name: 'US Process Manufacturing 002',
                            externalId: '*********',
                            taxNumber: '',
                            standardIndustrialCode: '',
                            contactTelNo: '+27821234567',
                            contactEmail: '<EMAIL>',
                            logoUrl: '',
                            organizationId: '',
                            address: {
                                addressLine1: '1 Some street',
                                addressLine2: 'Some building',
                                addressLine3: '',
                                addressLine4: '',
                                countrySubdivision: '',
                                postalCode: '0123',
                                country: 'ZAF',
                            },
                        },
                    ],

                    errors: [],
                    status: 200,
                    statusText: 'OK',
                });

                const updatedCompany = await auth.updateCompany({
                    companyId: 'dc4e28ca-97eb-4bf6-a62f-d13cdc2c0787',
                    name: 'US Process Manufacturing 002',
                    externalId: '*********',
                    taxNumber: '',
                    standardIndustrialCode: '',
                    contactTelNo: '+27821234567',
                    contactEmail: '<EMAIL>',
                    logoUrl: '',
                    organizationId: '',
                    address: {
                        addressLine1: '1 Some street',
                        addressLine2: 'Some building',
                        addressLine3: '',
                        addressLine4: '',
                        countrySubdivision: '',
                        postalCode: '0123',
                        country: 'ZAF',
                    },
                });

                assert.equal(updatedCompany[0].name, 'US Process Manufacturing 002');
            },
            { config },
        ));
    it(' Authentication Class - getCompanies', () =>
        Test.withContext(
            async context => {
                const auth = new xtremSageNetwork.classes.Authentication(context);
                const mockAuth = sinon.stub(auth, 'execute');
                mockAuth.resolves({
                    data: [
                        {
                            name: 'Company 1',
                            externalId: '*********',
                            taxNumber: '',
                            standardIndustrialCode: '',
                            contactTelNo: '+27821234567',
                            contactEmail: '<EMAIL>',
                            logoUrl: '',
                            organizationId: '',
                            address: {
                                addressLine1: '1 Some street',
                                addressLine2: 'Some building',
                                addressLine3: '',
                                addressLine4: '',
                                countrySubdivision: '',
                                postalCode: '0123',
                                country: 'ZAF',
                            },
                        },
                        {
                            name: 'Company 2',
                            externalId: '*********',
                            taxNumber: '',
                            standardIndustrialCode: '',
                            contactTelNo: '+27821234567',
                            contactEmail: '<EMAIL>',
                            logoUrl: '',
                            organizationId: '',
                            address: {
                                addressLine1: '1 Some street',
                                addressLine2: 'Some building',
                                addressLine3: '',
                                addressLine4: '',
                                countrySubdivision: '',
                                postalCode: '0123',
                                country: 'ZAF',
                            },
                        },
                    ],
                    errors: [],
                    status: 200,
                    statusText: 'OK',
                });
                const companies = await auth.getCompanies();
                assert.deepEqual(companies, [
                    {
                        name: 'Company 1',
                        externalId: '*********',
                        taxNumber: '',
                        standardIndustrialCode: '',
                        contactTelNo: '+27821234567',
                        contactEmail: '<EMAIL>',
                        logoUrl: '',
                        organizationId: '',
                        address: {
                            addressLine1: '1 Some street',
                            addressLine2: 'Some building',
                            addressLine3: '',
                            addressLine4: '',
                            countrySubdivision: '',
                            postalCode: '0123',
                            country: 'ZAF',
                        },
                    },
                    {
                        name: 'Company 2',
                        externalId: '*********',
                        taxNumber: '',
                        standardIndustrialCode: '',
                        contactTelNo: '+27821234567',
                        contactEmail: '<EMAIL>',
                        logoUrl: '',
                        organizationId: '',
                        address: {
                            addressLine1: '1 Some street',
                            addressLine2: 'Some building',
                            addressLine3: '',
                            addressLine4: '',
                            countrySubdivision: '',
                            postalCode: '0123',
                            country: 'ZAF',
                        },
                    },
                ]);
                mockAuth.restore();
            },
            { config },
        ));
    it(' Authentication Class - getCompanies failure', () =>
        Test.withContext(
            async context => {
                const auth = new xtremSageNetwork.classes.Authentication(context);
                const mockAuth = sinon.stub(auth, 'execute');
                mockAuth.rejects(
                    new SystemError(
                        `Internal Server Error 500 : There was an error [{"code":"500","description":"There was an error"}]`,
                    ),
                );
                await assert.isRejected(auth.getCompanies(), 'There was an error');
                mockAuth.restore();
            },
            { config },
        ));
    it(' Authentication Class - getCompanies failure with organization', () =>
        Test.withContext(
            async context => {
                const auth = new xtremSageNetwork.classes.Authentication(context, {
                    api: 'companies',
                    defaultOrganizationId: 'TESTNOIDS',
                });
                await assert.isRejected(auth.getCompanies(), 'The Sage Network organization creation failed.');
            },
            { config },
        ));
});
