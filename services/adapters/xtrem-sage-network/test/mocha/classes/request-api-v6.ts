import { ConfigManager, Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremSageNetwork from '../../../index.js';

describe('Request Api v6 class ', () => {
    const defaultConfig = {
        apiMode: 'sandbox',
        signingKey: '*********',
        v6Application: 'sdmo',
        application: 'sdmo',
        signatory: 'sdmo',
        v6ApiKeySandbox: 'xxxxxxx',
    };

    it('Request Api v6 - constructor', () =>
        Test.withContext(async context => {
            const company = await context.read(xtremSystem.nodes.Company, { id: 'US001' });
            const apiv6 = new xtremSageNetwork.classes.RequestApiV6(context, {
                api: 'other',
                url: 'https://api.sage.com/v6',
            });
            assert.equal(apiv6.parameters?.url, 'https://api.sage.com/v6');
            assert.equal(apiv6.api, 'other');
            const apiv6_2 = new xtremSageNetwork.classes.RequestApiV6(context, {
                api: 'other',
                url: 'https://api.sage.com/v6',
                executionId: '12345',
                company,
            });
            assert.equal(await (await apiv6_2.company)?.id, 'US001');
        }));
    it('Request Api v6 - getCompany', () =>
        Test.withContext(async context => {
            const company = await context.read(xtremSystem.nodes.Company, { id: 'US002' });
            const apiv6 = new xtremSageNetwork.classes.RequestApiV6(context, {
                api: 'other',
                url: 'https://api.sage.com/v6',
                executionId: '12345',
                company,
            });
            assert.equal(await (await apiv6.company)?.id, 'US002');
            // Default company
            const apiv6_2 = new xtremSageNetwork.classes.RequestApiV6(context, {
                api: 'other',
                url: 'https://api.sage.com/v6',
            });
            assert.equal(await (await apiv6_2.company)?.id, 'US001');
            // Default company not available
            const company2 = await context.read(xtremSystem.nodes.Company, { id: 'US001' }, { forUpdate: true });
            await company2.$.set({ sageNetworkCompany: null });
            await company2.$.save();
            const apiv6_21 = new xtremSageNetwork.classes.RequestApiV6(context, {
                api: 'other',
                url: 'https://api.sage.com/v6',
            });
            assert.equal(await (await apiv6_21.company)?.id, undefined);
            const newCompany = await context.create(xtremSystem.nodes.Company, {
                id: 'NewCompany',
                name: 'New Company',
                sageNetworkCompany: { companyId: 'Sage Company Id' },
                legislation: '#US',
                country: '#US',
                currency: '#USD',
                addresses: [{ name: 'add1', addressLine1: '1', addressLine2: '2', city: 'New York', isActive: true }],
            });
            await newCompany.$.save();
            const apiv6_3 = new xtremSageNetwork.classes.RequestApiV6(context, {
                api: 'other',
                url: 'https://api.sage.com/v6',
            });
            assert.equal(await (await apiv6_3.company)?.id, 'NewCompany');
            assert.equal(await (await (await apiv6_3.company)?.sageNetworkCompany)?.companyId, 'Sage Company Id');
        }));
    it('Request Api v6 - uploadData', () =>
        Test.withContext(async context => {
            const apiv6 = new xtremSageNetwork.classes.RequestApiV6(context);
            await assert.isRejected(
                apiv6.uploadData({ fileId: 'string', fileName: '', contentType: '', type: '', content: '' }),
                'UploadData: No data to upload',
            );
            // Have to add the any as this is a private method
            const mockApiv6 = sinon.stub(apiv6, 'execute');
            mockApiv6.onCall(0).resolves({
                data: { fileId: 'string', fileName: '', contentType: '', type: '', content: 'Basic content' },
                errors: [],
                status: 200,
                statusText: 'OK',
            });
            mockApiv6.onCall(1).resolves({
                data: { fileId: 'string', fileName: '', contentType: '', type: '', content: 'Basic content' },
                errors: [],
                status: 200,
                statusText: 'OK',
            });
            const updatedData = await apiv6.uploadData({
                fileId: 'string',
                fileName: '',
                contentType: '',
                type: '',
                content: 'Basic content',
            });
            assert.equal(updatedData.data?.fileName, '');
            mockApiv6.restore();
        }));

    it('Request Api v6 - startReport', () =>
        Test.withContext(async context => {
            const apiv6 = new xtremSageNetwork.classes.RequestApiV6(context);
            const mockApiv6 = sinon.stub(apiv6, 'execute');
            mockApiv6.resolves({
                data: JSON.stringify({
                    fileId: 'string',
                    fileName: '',
                    contentType: '',
                    type: '',
                    content: 'Basic content',
                }),
                errors: [],
                status: 200,
                statusText: 'OK',
            });
            const startReportResult = await apiv6.startReport('reportType', {
                context: { files: [], notification: { url: '', method: '' } },
            });
            assert.equal(
                startReportResult.data,
                JSON.stringify({ fileId: 'string', fileName: '', contentType: '', type: '', content: 'Basic content' }),
            );

            mockApiv6.restore();
        }));
    it('Request Api v6 - getOperationStatus', () =>
        Test.withContext(async context => {
            const apiv6 = new xtremSageNetwork.classes.RequestApiV6(context);
            const mockApiv6 = sinon.stub(apiv6, 'execute');
            const result = {
                data: {
                    stage: '',
                    expiresAt: '',
                    metadata: {
                        reportType: '',
                        reportVersion: '',
                        reportInternalId: '',
                    },
                    files: [
                        {
                            fileType: '',
                            contentType: '',
                            fileName: '',
                            url: '',
                            eTag: '',
                        },
                    ],
                },
                errors: [],
                status: 200,
                statusText: 'OK',
            };
            mockApiv6.resolves(result);
            const startReportResult = await apiv6.getOperationStatus('location');
            assert.deepEqual(startReportResult.data, {
                stage: '',
                expiresAt: '',
                metadata: {
                    reportType: '',
                    reportVersion: '',
                    reportInternalId: '',
                },
                files: [
                    {
                        fileType: '',
                        contentType: '',
                        fileName: '',
                        url: '',
                        eTag: '',
                    },
                ],
            });

            mockApiv6.restore();
        }));
    it('Request Api v6 - downloadResult', () =>
        Test.withContext(async context => {
            const apiv6 = new xtremSageNetwork.classes.RequestApiV6(context);
            const mockApiv6 = sinon.stub(apiv6, 'execute');
            mockApiv6.resolves({
                data: 'jwt info',
                errors: [],
                status: 200,
                statusText: 'OK',
            });
            const startReportResult = await apiv6.downloadResult('reportType', 'guid', 'fileId');
            assert.equal(startReportResult.data, 'jwt info');

            mockApiv6.restore();
        }));
    it('Request Api v6 - getHeaders', async () => {
        const config = { ...ConfigManager.current };
        if (!config.packages) {
            return;
        }
        delete config.packages['@sage/xtrem-sage-network'];
        config.packages['@sage/xtrem-sage-network'] = {
            ...defaultConfig,
            signingKey: '*********',
            v6Application: 'sdmo',
        };
        await Test.withContext(
            async context => {
                const company = await context.read(xtremSystem.nodes.Company, { id: 'US002' });
                xtremSageNetwork.classes.RequestApiV6.resetConfig();
                const apiv6 = new xtremSageNetwork.classes.RequestApiV6(context, {
                    api: 'other',
                    url: 'https://api.sage.com/v6',
                    executionId: '12345',
                    company,
                });
                const org = await apiv6.activeInstance;
                assert.equal(await org.name, 'Tenant for tests (automatic creation)');
                const headers = await apiv6.getHeaders();
                assert.equal(headers['X-Application'], 'sdmo');
            },
            {
                config,
            },
        );
    });
});
