import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSageNetwork from '../../../index.js';

describe('Test common functions  ', () => {
    before(() => {});

    it('bodyAndParametersEncode  ', () => {
        const body = {
            object: 'blablablablabla',
        };

        const encodedeString = xtremSageNetwork.functions.authenticationFunctions.bodyAndParametersEncode('', body);

        assert.deepEqual(encodedeString, 'body=eyJvYmplY3QiOiJibGFibGFibGFibGFibGEifQ%3D%3D');
    });
    it('Check signing key', async () => {
        await Test.withContext(context => {
            assert.throws(
                () => xtremSageNetwork.functions.authenticationFunctions.checkSigningKey(context),
                'No signing key.',
            );
        });
    });
    it('Generate xSignature', () => {
        const signature = xtremSageNetwork.functions.authenticationFunctions.generateXSignature({
            baseString: {
                method: 'PUT',
                endpointUrl: 'endpoint',
                bodyAndParameters: 'body',
            },
            signingKey: 'signingKey',
        });
        assert.equal(signature.xNonceUuid.includes('-'), true);
    });
    it('Get base string', () => {
        const baseString = xtremSageNetwork.functions.authenticationFunctions.getBaseString({
            method: 'PUT',
            endpointUrl: 'endpoint',
            bodyAndParameters: 'body',
        });
        assert.equal(baseString, 'PUT&endpoint&body&');
    });
});
