import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';

describe('Company test ', () => {
    before(() => {});
    it(' Company create  ', () =>
        Test.withContext(async context => {
            const company = await context.read(xtremSystem.nodes.Company, { id: 'S1' }, { forUpdate: true });
            await company.$.set({ sageNetworkCompany: { externalId: '*********' } });
            assert.deepEqual(await company.id, 'S1');
            assert.deepEqual(
                await (
                    await company.sageNetworkCompany
                )?.companyPayload,
                {
                    name: 'Société S1',
                    externalId: '*********',
                    taxNumber: 'FR*********12',
                    standardIndustrialCode: '',
                    contactTelNo: '',
                    contactEmail: '',
                    organizationId: '',
                    logoUrl: '',
                    address: {
                        addressLine1: '',
                        addressLine2: '',
                        addressLine3: '',
                        addressLine4: '',
                        countrySubdivision: '',
                        postalCode: '',
                        country: 'FRA',
                    },
                },
                JSON.stringify(await company.sageNetworkCompany),
            );
        }));
});
