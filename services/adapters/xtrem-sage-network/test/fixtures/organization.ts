import type { Context } from '@sage/xtrem-core';
import { datetime } from '@sage/xtrem-core';
import * as xtremSageNetwork from '../../index.js';

/**
 * Set the token & dateCreation
 * @param context
 */
export async function setDummyToken(context: Context): Promise<void> {
    const organization = await context.read(
        xtremSageNetwork.nodes.Organization,
        { id: 'DEFAULT' },
        { forUpdate: true },
    );
    await organization.$.set({
        token: 'dummyToken',
        tokenCreation: datetime.now(),
    });

    await organization.$.save();
}
