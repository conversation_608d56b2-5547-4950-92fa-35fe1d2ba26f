declare module '@sage/xtrem-intacct-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type {
        Package as SageXtremAuthorization$Package,
        UserBillingRole,
        UserBillingRoleBinding,
        UserBillingRoleInput,
        UserGroup,
        UserGroupBinding,
        UserGroupInput,
    } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package, SysMessageHistory } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type { Package as SageXtremMasterData$Package } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type {
        ChartOfAccount,
        Country,
        Legislation,
        Package as SageXtremStructure$Package,
    } from '@sage/xtrem-structure-api';
    import type {
        BaseMapping,
        Package as SageXtremSynchronization$Package,
        ThirdPartyApplication,
    } from '@sage/xtrem-synchronization-api';
    import type {
        Package as SageXtremSystem$Package,
        SysClientUserSettings,
        SysClientUserSettingsBinding,
        SysClientUserSettingsInput,
        User,
        UserPreferences,
        UserPreferencesBinding,
        UserPreferencesInput,
    } from '@sage/xtrem-system-api';
    import type { Package as SageXtremTax$Package, TaxCategory } from '@sage/xtrem-tax-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        integer,
    } from '@sage/xtrem-client';
    export interface ListenerStatus$Enum {
        received: 0;
        error: 1;
        done: 2;
        notImplentedYet: 3;
    }
    export type ListenerStatus = keyof ListenerStatus$Enum;
    export interface PropertyType$Enum {
        none: 1;
        description: 2;
        text: 3;
        number: 4;
        intacctId: 5;
        name: 6;
    }
    export type PropertyType = keyof PropertyType$Enum;
    export interface RequestType$Enum {
        create: 1;
        read: 2;
        update: 3;
        delete: 4;
        createUpdate: 5;
    }
    export type RequestType = keyof RequestType$Enum;
    export interface TransactionIntegrationLevel$Enum {
        topLevel: 0;
        entityLevel: 1;
    }
    export type TransactionIntegrationLevel = keyof TransactionIntegrationLevel$Enum;
    export interface Intacct extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        isRequestAsynchronous: boolean;
        isContactListCleaned: boolean;
        isCategoryNameClean: boolean;
        isDisplayContactHidden: boolean;
        senderId: string;
        senderPassword: string;
        companyId: string;
        userId: string;
        userPassword: string;
        isFullFilledAuthentification: boolean;
        endpointUrl: string;
        entityId: string;
        sessionId: string;
        sessionExpiration: string;
        controlIdTableName: string;
        policyId: string;
        asyncTimeout: integer;
        infoXTreeMAuditTrail: string;
        mustXtreemAuditTrail: string;
        isXTreeMAuditTrail: boolean;
        chartOfAccount: ChartOfAccount;
        taxSolution: string;
        messageHistory: ClientCollection<SysMessageHistory>;
        entityList: string;
        transactionIntegrationLevel: TransactionIntegrationLevel;
        lines: ClientCollection<IntacctLine>;
        legislation: Legislation;
        taxCategory: TaxCategory;
    }
    export interface IntacctInput extends ClientNodeInput {
        id?: string;
        isActive?: boolean | string;
        isRequestAsynchronous?: boolean | string;
        isContactListCleaned?: boolean | string;
        isCategoryNameClean?: boolean | string;
        isDisplayContactHidden?: boolean | string;
        companyId?: string;
        userId?: string;
        userPassword?: string;
        entityId?: string;
        sessionId?: string;
        sessionExpiration?: string;
        chartOfAccount?: integer | string;
        taxSolution?: string;
        entityList?: string;
        transactionIntegrationLevel?: TransactionIntegrationLevel;
        lines?: Partial<IntacctLineInput>[];
        legislation?: integer | string;
        taxCategory?: integer | string;
    }
    export interface IntacctBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        isRequestAsynchronous: boolean;
        isContactListCleaned: boolean;
        isCategoryNameClean: boolean;
        isDisplayContactHidden: boolean;
        senderId: string;
        senderPassword: string;
        companyId: string;
        userId: string;
        userPassword: string;
        isFullFilledAuthentification: boolean;
        endpointUrl: string;
        entityId: string;
        sessionId: string;
        sessionExpiration: string;
        controlIdTableName: string;
        policyId: string;
        asyncTimeout: integer;
        infoXTreeMAuditTrail: any;
        mustXtreemAuditTrail: any;
        isXTreeMAuditTrail: boolean;
        chartOfAccount: ChartOfAccount;
        taxSolution: string;
        messageHistory: ClientCollection<SysMessageHistory>;
        entityList: any;
        transactionIntegrationLevel: TransactionIntegrationLevel;
        lines: ClientCollection<IntacctLineBinding>;
        legislation: Legislation;
        taxCategory: TaxCategory;
    }
    export interface Intacct$Queries {
        defaultInstance: Node$Operation<
            {
                isThrowing?: boolean | string;
            },
            Intacct
        >;
        getEntities: Node$Operation<
            {
                intacctInstanceId?: string;
            },
            {
                name: string;
                id: string;
            }[]
        >;
    }
    export interface Intacct$Mutations {
        refreshEntities: Node$Operation<
            {
                intacctInstanceId?: string;
            },
            {
                name: string;
                id: string;
            }[]
        >;
        getEntitiesAsynchronous: Node$Operation<
            {
                intacctInstanceId?: string;
            },
            {
                controlId: string;
                status: string;
                xml: string;
            }
        >;
        sendEvent: Node$Operation<
            {
                messageKind: string;
                dataIntacctEvent: {
                    companyId?: string;
                    object?: string;
                    change?: string;
                    recordNumber?: integer | string;
                    recordId?: string;
                    intacctIdName?: string;
                };
                dataBase64?: string;
            },
            boolean
        >;
        sendXmlRequest: Node$Operation<
            {
                entityId: string;
                xmlQuery: string;
                isSynchronous: boolean | string;
            },
            string
        >;
        retry: Node$Operation<
            {
                sysMessageHistoryId: string;
            },
            boolean
        >;
    }
    export interface Intacct$AsyncOperations {
        installXtreemAuditTrail: AsyncOperation<
            {
                intacctInstanceId?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Intacct$Lookups {
        chartOfAccount: QueryOperation<ChartOfAccount>;
        legislation: QueryOperation<Legislation>;
        taxCategory: QueryOperation<TaxCategory>;
    }
    export interface Intacct$Operations {
        query: QueryOperation<Intacct>;
        read: ReadOperation<Intacct>;
        aggregate: {
            read: AggregateReadOperation<Intacct>;
            query: AggregateQueryOperation<Intacct>;
        };
        queries: Intacct$Queries;
        create: CreateOperation<IntacctInput, Intacct>;
        getDuplicate: GetDuplicateOperation<Intacct>;
        update: UpdateOperation<IntacctInput, Intacct>;
        updateById: UpdateByIdOperation<IntacctInput, Intacct>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: Intacct$Mutations;
        asyncOperations: Intacct$AsyncOperations;
        lookups(dataOrId: string | { data: IntacctInput }): Intacct$Lookups;
        getDefaults: GetDefaultsOperation<Intacct>;
    }
    export interface IntacctLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        intacct: Intacct;
        country: Country;
        taxSolution: string;
        taxCategory: TaxCategory;
    }
    export interface IntacctLineInput extends VitalClientNodeInput {
        country?: integer | string;
        taxSolution?: string;
        taxCategory?: integer | string;
    }
    export interface IntacctLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        intacct: Intacct;
        country: Country;
        taxSolution: string;
        taxCategory: TaxCategory;
    }
    export interface IntacctLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntacctLine$Lookups {
        country: QueryOperation<Country>;
        taxCategory: QueryOperation<TaxCategory>;
    }
    export interface IntacctLine$Operations {
        query: QueryOperation<IntacctLine>;
        read: ReadOperation<IntacctLine>;
        aggregate: {
            read: AggregateReadOperation<IntacctLine>;
            query: AggregateQueryOperation<IntacctLine>;
        };
        create: CreateOperation<IntacctLineInput, IntacctLine>;
        getDuplicate: GetDuplicateOperation<IntacctLine>;
        update: UpdateOperation<IntacctLineInput, IntacctLine>;
        updateById: UpdateByIdOperation<IntacctLineInput, IntacctLine>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctLine$AsyncOperations;
        lookups(dataOrId: string | { data: IntacctLineInput }): IntacctLine$Lookups;
        getDefaults: GetDefaultsOperation<IntacctLine>;
    }
    export interface IntacctNodeState extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: string;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: string;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
    }
    export interface IntacctNodeStateInput extends ClientNodeInput {
        _constructor?: string;
        integration?: integer | string;
        node?: integer | string;
        sysId?: string;
        naturalKey?: string;
        state?: IntegrationState;
        url?: string;
        secondaryPageLink?: string;
        lastMessage?: string;
        notificationTrackingIds?: string[];
        creationStamp?: string;
        updateStamp?: string;
        version?: integer | string;
        difference?: string;
        intacctId?: string;
        recordNo?: integer | string;
    }
    export interface IntacctNodeStateBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: any;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: any;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
    }
    export interface IntacctNodeState$AsyncOperations {
        synchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        forceSynchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        resetThirdPartyId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        updateSysId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface IntacctNodeState$Lookups {
        integration: QueryOperation<ThirdPartyApplication>;
        mapping: QueryOperation<BaseMapping>;
        node: QueryOperation<MetaNodeFactory>;
        intacctConfiguration: QueryOperation<Intacct>;
    }
    export interface IntacctNodeState$Operations {
        create: CreateOperation<IntacctNodeStateInput, IntacctNodeState>;
        getDuplicate: GetDuplicateOperation<IntacctNodeState>;
        asyncOperations: IntacctNodeState$AsyncOperations;
        lookups(dataOrId: string | { data: IntacctNodeStateInput }): IntacctNodeState$Lookups;
        getDefaults: GetDefaultsOperation<IntacctNodeState>;
    }
    export interface IntacctOptionManagement extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
    }
    export interface IntacctOptionManagementInput extends ClientNodeInput {}
    export interface IntacctOptionManagementBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
    }
    export interface IntacctOptionManagement$Queries {
        isServiceOptionActiveFunction: Node$Operation<{}, boolean>;
    }
    export interface IntacctOptionManagement$Mutations {
        serviceOptionChange: Node$Operation<{}, boolean>;
    }
    export interface IntacctOptionManagement$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntacctOptionManagement$Operations {
        queries: IntacctOptionManagement$Queries;
        update: UpdateOperation<IntacctOptionManagementInput, IntacctOptionManagement>;
        updateById: UpdateByIdOperation<IntacctOptionManagementInput, IntacctOptionManagement>;
        mutations: IntacctOptionManagement$Mutations;
        asyncOperations: IntacctOptionManagement$AsyncOperations;
        getDefaults: GetDefaultsOperation<IntacctOptionManagement>;
    }
    export interface UserExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        email: string;
        firstName: string;
        lastName: string;
        isActive: boolean;
        photo: BinaryStream;
        displayName: string;
        userType: UserType;
        isAdministrator: boolean;
        isDemoPersona: boolean;
        isApiUser: boolean;
        preferences: UserPreferences;
        clientSettings: ClientCollection<SysClientUserSettings>;
        billingRole: UserBillingRole;
        authorizationGroup: ClientCollection<UserGroup>;
        groupDisplay: string;
        intacctId: string;
        recordNo: integer;
        isIntacct: boolean;
        _attachments: ClientCollection<AttachmentAssociation>;
        isOperatorUser: boolean;
    }
    export interface UserInputExtension {
        email?: string;
        firstName?: string;
        lastName?: string;
        isActive?: boolean | string;
        photo?: BinaryStream;
        userType?: UserType;
        isAdministrator?: boolean | string;
        isDemoPersona?: boolean | string;
        isApiUser?: boolean | string;
        operatorCode?: string;
        preferences?: UserPreferencesInput;
        clientSettings?: Partial<SysClientUserSettingsInput>[];
        billingRole?: UserBillingRoleInput;
        authorizationGroup?: Partial<UserGroupInput>[];
        intacctId?: string;
        recordNo?: integer | string;
        isIntacct?: boolean | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        isOperatorUser?: boolean | string;
    }
    export interface UserBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        email: string;
        firstName: string;
        lastName: string;
        isActive: boolean;
        photo: BinaryStream;
        displayName: string;
        userType: UserType;
        isAdministrator: boolean;
        isDemoPersona: boolean;
        isApiUser: boolean;
        operatorCode: string;
        preferences: UserPreferencesBinding;
        clientSettings: ClientCollection<SysClientUserSettingsBinding>;
        billingRole: UserBillingRoleBinding;
        authorizationGroup: ClientCollection<UserGroupBinding>;
        groupDisplay: string;
        intacctId: string;
        recordNo: integer;
        isIntacct: boolean;
        _attachments: ClientCollection<AttachmentAssociation>;
        isOperatorUser: boolean;
    }
    export interface Package {
        '@sage/xtrem-intacct/Intacct': Intacct$Operations;
        '@sage/xtrem-intacct/IntacctLine': IntacctLine$Operations;
        '@sage/xtrem-intacct/IntacctNodeState': IntacctNodeState$Operations;
        '@sage/xtrem-intacct/IntacctOptionManagement': IntacctOptionManagement$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremImportExport$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-intacct-api' {
    export type * from '@sage/xtrem-intacct-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { UserBindingExtension, UserExtension, UserInputExtension } from '@sage/xtrem-intacct-api';
    export interface User extends UserExtension {}
    export interface UserBinding extends UserBindingExtension {}
    export interface UserInput extends UserInputExtension {}
}
