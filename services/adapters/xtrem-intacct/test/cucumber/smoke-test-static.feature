@xtrem_intacct
Feature: smoke-test-static

    #Case with navigation panel full width
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "<Title>" titled page is displayed
        # Opening pages having extensions in the current package
        Examples:
            | Page                           | Title | NavigationPanelTitle |
            | @sage/xtrem-authorization/User | User  | Users                |

    # Positive test to ensure that the error dialog is definitely exist and open
    Scenario: sts \ xtrem-intacct \ desktop \ check an error dialog appears when the page does not load
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Wazup"
        And the user waits 4 seconds
# And no dialogs are displayed
# # Then an error dialog appears on the main page
