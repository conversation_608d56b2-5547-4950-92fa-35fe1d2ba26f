import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacct from '../../../index';

describe(' Intacct   ', () => {
    before(() => {});

    it(' Intacct Node - throw  ', () =>
        Test.withContext(async context => {
            const intacctConfiguration = await context.read(xtremIntacct.nodes.Intacct, { id: 'XTREM' });
            assert.doesNotThrow(
                () => intacctConfiguration.getIntacctConfig(),
                'Intacct Gateway configuration not found.',
            );
        }));
    it(' Intacct Node - isXTreeMAuditTrail  ', () =>
        Test.withContext(
            async context => {
                const intacctConfiguration = await context.read(xtremIntacct.nodes.Intacct, { id: 'XTREM' });

                assert.equal(await intacctConfiguration.isXTreeMAuditTrail, true);
            },
            {
                scenario: 'is-xtrem-audit-trail',
                directory: __dirname,
            },
        ));
    it(' Intacct Node - statics  ', () =>
        Test.withContext(context => {
            Test.patchConfig({
                packages: {
                    '@sage/xtrem-intacct-gateway': {
                        senderId: 'senderUser',
                        senderPassword: 'pass1234',
                        controlIdTableName: 'table',
                    },
                },
            });
            const packageConfg = xtremIntacct.nodes.Intacct.getIntacctPackage(context);
            assert.isNotNull(packageConfg);
        }));

    /**
     *  the patchConfig will be effective after the iteration !
     */
    it(' Intacct Node  ', async () => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-intacct-gateway': {
                    senderId: 'senderUser',
                    senderPassword: 'pass1234',
                    controlIdTableName: 'table',
                    policyId: 'xt_async_1',
                    defaultAsyncTimeout: 360,
                },
            },
        });
        await Test.withContext(async context => {
            const intacctConfiguration = await context.read(xtremIntacct.nodes.Intacct, { id: 'XTREM' });

            assert.equal(await intacctConfiguration.controlIdTableName, 'table');
            assert.equal(await intacctConfiguration.senderId, 'senderUser');

            const intacctConfig = intacctConfiguration.getIntacctConfig();

            assert.equal(intacctConfig.controlIdTableName, 'table');
            assert.equal(intacctConfig.senderId, 'senderUser');
            assert.equal(intacctConfig.senderPassword, 'pass1234');
            assert.equal(intacctConfig.defaultAsyncTimeout, 360);

            assert.equal(await intacctConfiguration.asyncTimeout, 360);

            assert.equal(intacctConfig.policyId, 'xt_async_1');

            const client = await intacctConfiguration.getClient();

            assert.equal(client.companyId, 'Int-X3-WD');
            assert.equal(client.userId, 'xtremapiAll');
        });
    });

    it('intacct Query getEntitiesAsynchronous   ', async () => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-intacct-gateway': {
                    senderId: 'senderUser',
                    senderPassword: 'pass1234',
                    controlIdTableName: '',
                },
            },
        });
        await Test.withContext(
            async context => {
                const result = await xtremIntacct.nodes.Intacct.getEntitiesAsynchronous(context);
                assert.equal(result.status, 'success');
            },
            {
                scenario: 'query-AsynchronousEntities',
                directory: __dirname,
            },
        );
    });

    it('intacct Query getEntities   ', () =>
        Test.withContext(
            async context => {
                const result = await xtremIntacct.nodes.Intacct.getEntities(context);
                assert.isArray(result);
            },
            {
                scenario: 'query-Entities',
                directory: __dirname,
            },
        ));

    it(' Get Default instance  ', () =>
        Test.withContext(async context => {
            const defaultInstance = (await xtremIntacct.nodes.Intacct.defaultInstance(context))!;
            assert.equal(await defaultInstance.id, 'XTREM');

            const xtremInstance = await context.read(xtremIntacct.nodes.Intacct, { id: 'XTREM' }, { forUpdate: true });
            await xtremInstance.$.set({ isActive: false });
            await xtremInstance.$.save();

            assert.equal(await xtremIntacct.nodes.Intacct.defaultInstance(context), null);
        }));

    it('Can not activate a second instance ', () =>
        Test.withContext(async context => {
            const defaultInstance = (await xtremIntacct.nodes.Intacct.defaultInstance(context))!;
            assert.equal(await defaultInstance.id, 'XTREM');

            const xtremInstance = await context.read(xtremIntacct.nodes.Intacct, { id: 'DEMO' }, { forUpdate: true });
            await xtremInstance.$.set({ isActive: true });
            await assert.isRejected(xtremInstance.$.save());
        }));

    it('Query the node XTREM - XtreemAuditTrail ', async () => {
        await Test.withContext(
            async context => {
                const intacctConfiguration = await context.read(xtremIntacct.nodes.Intacct, { id: 'XTREM' });

                assert.deepEqual(await intacctConfiguration.mustXtreemAuditTrail, {
                    DESCRIPTION: 'Tenant for tests (automatic creation)',
                    VERSION: '1.8',
                });
                assert.deepEqual(await intacctConfiguration.infoXTreeMAuditTrail, {
                    DESCRIPTION: 'Tenant for tests',
                    VERSION: '1.8',
                });
            },
            {
                scenario: 'info-xtreem-audit-trail',
                directory: __dirname,
            },
        );
    });

    it('Duplicate country on intacct line ', () =>
        Test.withContext(async context => {
            const xtremInstance = await context.read(xtremIntacct.nodes.Intacct, { id: 'DEMO' }, { forUpdate: true });
            await xtremInstance.$.set({
                lines: [
                    {
                        _action: 'update',
                        _id: 4,
                        taxSolution: 'test',
                    },
                    {
                        _action: 'create',
                        country: '#US',
                        taxSolution: 'test1',
                    },
                ],
            });
            await assert.isRejected(xtremInstance.$.save());

            assert.deepEqual(xtremInstance.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'There are duplicate countries. Make sure that each line has a unique country.',
                },
            ]);
        }));

    it('Country without tax solution on intacct line ', () =>
        Test.withContext(async context => {
            const xtremInstance = await context.read(xtremIntacct.nodes.Intacct, { id: 'DEMO' }, { forUpdate: true });
            await xtremInstance.$.set({
                lines: [
                    {
                        _action: 'update',
                        _id: 4,
                        taxSolution: 'test',
                    },
                    {
                        _action: 'create',
                        country: '#ES',
                    },
                ],
            });
            await assert.isRejected(xtremInstance.$.save());

            assert.deepEqual(xtremInstance.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000001', 'country'],
                    message: 'You cannot select a country without a tax solution.',
                },
            ]);
        }));
});
