import { assert } from 'chai';
import * as IntacctAdapter from '../../../index';

describe('Test common specific i-functions  ', () => {
    before(() => {});

    it('withoutFunction Function  ', () => {
        assert.equal(
            IntacctAdapter.functions.withoutFunction(
                `<request><function controlid="c7a53c6e-06ac-423b-90ff-7576ff9a2b83"><lookup><object>ITEM</object></lookup></function></request>`,
            ),
            '<lookup><object>ITEM</object></lookup>',
        );
        assert.equal(
            IntacctAdapter.functions.withoutFunction(
                `<request>
                    <function controlid="c7a53c6e-06ac-423b-90ff-7576ff9a2b83">
                        <lookup><object>ITEM</object></lookup>
                    </function>
                </request>`,
            ),
            '<lookup><object>ITEM</object></lookup>',
        );
        assert.equal(
            IntacctAdapter.functions.withoutFunction(
                `<request>
                  <function controlid="c7a53c6e-06ac-423b-90ff-7576ff9a2b83">
                    <lookup><object>ITEM</object></lookup>
                  </function>
                </request>`,
            ),
            '<lookup><object>ITEM</object></lookup>',
        );
    });
});
