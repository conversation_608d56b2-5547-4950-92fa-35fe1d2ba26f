<response><control status="success" senderid="X3_mfg" controlid="1622713168709" uniqueid="false" dtdversion="3.0"></control><operation><authentication status="success" userid="xtremapiAll" companyid="Int-X3-WD" locationid="" sessiontimestamp="2021-06-03T09:39:29+00:00" sessiontimeout="2021-06-03T21:39:29+00:00"></authentication><result status="success" function="lookup" controlid="27c5cf9c-084c-402f-b67a-690d0eb829b3"><data><$ listtype="All" count="1"></$><Type><$ Name="ITEM" DocumentType=""></$><Fields><Field ID="RECORDNO" LABEL="Record number" DESCRIPTION="Record Number" REQUIRED="false" READONLY="false" DATATYPE="INTEGER" ISCUSTOM="false"></Field><Field ID="ITEMID" LABEL="Item ID" DESCRIPTION="Item Unique Identifier" REQUIRED="true" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="STATUS" LABEL="Status" DESCRIPTION="Active/Inactive" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="active" VALIDVALUE="inactive"></VALIDVALUES></Field><Field ID="MRR" LABEL="MRR" DESCRIPTION="MRR" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="NAME" LABEL="Name" DESCRIPTION="name" REQUIRED="true" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="EXTENDED_DESCRIPTION" LABEL="Extended description" DESCRIPTION="Extended Description" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="PODESCRIPTION" LABEL="Description on purchase transactions" DESCRIPTION="Extended Description" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="SODESCRIPTION" LABEL="Description on sales transactions" DESCRIPTION="Extended Description" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="CNDEFAULTBUNDLE" LABEL="Contract default bundle" DESCRIPTION="No description specified" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="CNMEACATEGORYNAME" LABEL="Contract fair value category" DESCRIPTION="No description specified" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="PRODUCTLINEID" LABEL="Product line ID" DESCRIPTION="Product Line ID" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="CYCLE" LABEL="Inventory cycle" DESCRIPTION="Inventory Cycle" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="PRODUCTTYPE" LABEL="Product type" DESCRIPTION="Product Type" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="Item for Resale" VALIDVALUE="Item not for Resale" VALIDVALUE="Discontinued" VALIDVALUE="Service" VALIDVALUE="Misc Charge"></VALIDVALUES></Field><Field ID="SUBSTITUTEID" LABEL="Substitute item" DESCRIPTION="Substitute Item ID" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="SHIP_WEIGHT" LABEL="Shipping weight" DESCRIPTION="Shipping Weight" REQUIRED="false" READONLY="false" DATATYPE="DECIMAL" ISCUSTOM="false"></Field><Field ID="WHENLASTSOLD" LABEL="Date last sold" DESCRIPTION="Date Last Sold" REQUIRED="false" READONLY="true" DATATYPE="DATE" ISCUSTOM="false"></Field><Field ID="WHENLASTRECEIVED" LABEL="Date last received" DESCRIPTION="Date Last Received" REQUIRED="false" READONLY="true" DATATYPE="DATE" ISCUSTOM="false"></Field><Field ID="ALLOW_BACKORDER" LABEL="Allow backorder" DESCRIPTION="Allow Backorder" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="TAXABLE" LABEL="Taxable" DESCRIPTION="Taxable" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="COST_METHOD" LABEL="Cost method" DESCRIPTION="Cost Method" REQUIRED="true" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="Standard" VALIDVALUE="Average" VALIDVALUE="FIFO" VALIDVALUE="LIFO"></VALIDVALUES></Field><Field ID="STANDARD_COST" LABEL="Standard cost" DESCRIPTION="Standard Cost" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="UOMGRP" LABEL="Unit of measure" DESCRIPTION="Unique Name for the Unit Group" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="DEFAULT_WAREHOUSE" LABEL="Default warehouse" DESCRIPTION="Default Warehouse" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="GLGROUP" LABEL="GL group" DESCRIPTION="GL Group" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="NOTE" LABEL="Note" DESCRIPTION="Note" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="INV_PRECISION" LABEL="Inventory" DESCRIPTION="Inventory" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="PO_PRECISION" LABEL="Purchasing" DESCRIPTION="Purchasing" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="SO_PRECISION" LABEL="Sales" DESCRIPTION="Sales" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="ITEMTYPE" LABEL="Item type" DESCRIPTION="Item Type" REQUIRED="true" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="Inventory" VALIDVALUE="Non-Inventory" VALIDVALUE="Non-Inventory (Purchase only)" VALIDVALUE="Non-Inventory (Sales only)" VALIDVALUE="Kit" VALIDVALUE="Stockable Kit"></VALIDVALUES></Field><Field ID="ENABLE_SERIALNO" LABEL="Enable serial tracking" DESCRIPTION="Enable Serial Tracking" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="SERIAL_MASKKEY" LABEL="Serial number mask" DESCRIPTION="Serial Number Mask" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="ENABLE_LOT_CATEGORY" LABEL="Enable lot tracking" DESCRIPTION="Enable Lot Tracking" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="LOT_CATEGORYKEY" LABEL="Lot category" DESCRIPTION="Lot Category" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="ENABLE_BINS" LABEL="Enable bin tracking" DESCRIPTION="Enable Bin Tracking" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="ENABLE_EXPIRATION" LABEL="Enable expiration tracking" DESCRIPTION="Enable Expiration Tracking" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="UPC" LABEL="UPC" DESCRIPTION="UPC" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="REVPOSTING" LABEL="Revenue posting" DESCRIPTION="" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="Component Level" VALIDVALUE="Kit Level"></VALIDVALUES></Field><Field ID="REVPRINTING" LABEL="Print format" DESCRIPTION="" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="Individual Components" VALIDVALUE="Kit"></VALIDVALUES></Field><Field ID="BASEPRICE" LABEL="Base price" DESCRIPTION="Base Price" REQUIRED="false" READONLY="false" DATATYPE="DECIMAL" ISCUSTOM="false"></Field><Field ID="VSOECATEGORY" LABEL="Fair value category" DESCRIPTION="Fair Value Category" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="Product - Specified" VALIDVALUE="Software" VALIDVALUE="Product - Unspecified" VALIDVALUE="Upgrade - Unspecified" VALIDVALUE="Upgrade - Specified" VALIDVALUE="Services" VALIDVALUE="Post Contract Support(PCS)"></VALIDVALUES></Field><Field ID="VSOEDLVRSTATUS" LABEL="Default delivery status" DESCRIPTION="Default Delivery Status" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="Delivered" VALIDVALUE="Undelivered"></VALIDVALUES></Field><Field ID="VSOEREVDEFSTATUS" LABEL="Default deferral status" DESCRIPTION="Default Deferral Status" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="Defer until item is delivered" VALIDVALUE="Defer bundle until item is delivered"></VALIDVALUES></Field><Field ID="HASSTARTENDDATES" LABEL="Item has start and end date" DESCRIPTION="Item Has Start and End Date " REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="TERMPERIOD" LABEL="Periods measured in" DESCRIPTION="Term Period" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="Days" VALIDVALUE="Weeks" VALIDVALUE="Months" VALIDVALUE="Years"></VALIDVALUES></Field><Field ID="TOTALPERIODS" LABEL="Number of periods" DESCRIPTION="Default Number of Periods" REQUIRED="false" READONLY="false" DATATYPE="INTEGER" ISCUSTOM="false"></Field><Field ID="COMPUTEFORSHORTTERM" LABEL="Allow to prorate price" DESCRIPTION="Compute Price for Short Term " REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="TAXCODE" LABEL="Tax code" DESCRIPTION="Tax Code" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="WHENCREATED" LABEL="When created" DESCRIPTION="timestamp marking last time this was created." REQUIRED="false" READONLY="true" DATATYPE="TIMESTAMP" ISCUSTOM="false"></Field><Field ID="WHENMODIFIED" LABEL="When modified" DESCRIPTION="timestamp marking last time this was changed." REQUIRED="false" READONLY="true" DATATYPE="TIMESTAMP" ISCUSTOM="false"></Field><Field ID="CREATEDBY" LABEL="Created by" DESCRIPTION="User who created this." REQUIRED="false" READONLY="true" DATATYPE="INTEGER" ISCUSTOM="false"></Field><Field ID="MODIFIEDBY" LABEL="Modified by" DESCRIPTION="User who modified this." REQUIRED="false" READONLY="true" DATATYPE="INTEGER" ISCUSTOM="false"></Field><Field ID="GLGRPKEY" LABEL="GL group record number" DESCRIPTION="No description specified" REQUIRED="false" READONLY="false" DATATYPE="INTEGER" ISCUSTOM="false"></Field><Field ID="UOMGRPKEY" LABEL="UOM group record number" DESCRIPTION="No description specified" REQUIRED="false" READONLY="false" DATATYPE="INTEGER" ISCUSTOM="false"></Field><Field ID="DROPSHIP" LABEL="Available for drop ship" DESCRIPTION="Available for drop ship" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="BUYTOORDER" LABEL="Available for buy to order" DESCRIPTION="Available for buy to order" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="DEFCONTRACTDELIVERYSTATUS" LABEL="Default contract delivery status" DESCRIPTION="Default contract delivery status" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="Delivered" VALIDVALUE="Undelivered"></VALIDVALUES></Field><Field ID="DEFCONTRACTDEFERRALSTATUS" LABEL="Default contract deferral status" DESCRIPTION="Default deferral status" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="Defer revenue until item is delivered" VALIDVALUE="Defer revenue until all items are delivered"></VALIDVALUES></Field><Field ID="ENABLE_REPLENISHMENT" LABEL="Enable replenishment for this item" DESCRIPTION="No description specified" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="REPLENISHMENT_METHOD" LABEL="Replenishment method" DESCRIPTION="Replenishment method" REQUIRED="false" READONLY="false" DATATYPE="ENUM" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="Demand forecast by single value" VALIDVALUE="Reorder point" VALIDVALUE="Demand forecast by fluctuating values"></VALIDVALUES></Field><Field ID="DEFAULT_REPLENISHMENT_UOM" LABEL="Units of measure default" DESCRIPTION="No description specified" REQUIRED="false" READONLY="false" DATATYPE="ENUM" ISCUSTOM="false"></Field><Field ID="REORDER_POINT" LABEL="Reorder point" DESCRIPTION="No description specified" REQUIRED="false" READONLY="false" DATATYPE="INTEGER" ISCUSTOM="false"></Field><Field ID="SAFETY_STOCK" LABEL="Safety stock" DESCRIPTION="No description specified" REQUIRED="false" READONLY="false" DATATYPE="INTEGER" ISCUSTOM="false"></Field><Field ID="REORDER_QTY" LABEL="Quantity to reorder" DESCRIPTION="No description specified" REQUIRED="false" READONLY="false" DATATYPE="INTEGER" ISCUSTOM="false"></Field><Field ID="MAX_ORDER_QTY" LABEL="Maximum order quantity" DESCRIPTION="No description specified" REQUIRED="false" READONLY="false" DATATYPE="INTEGER" ISCUSTOM="false"></Field><Field ID="AUTOPRINTLABEL" LABEL="Auto print label" DESCRIPTION="Auto print label" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="ALLOWMULTIPLETAXGRPS" LABEL="Enable multiple item tax groups, one per each tax solution" DESCRIPTION="Enable multiple item tax groups, one per each tax solution" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="WEIGHTUOM" LABEL="Weight UOM" DESCRIPTION="Weight UOM" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="NETWEIGHT" LABEL="Net weight" DESCRIPTION="Net weight" REQUIRED="false" READONLY="false" DATATYPE="DECIMAL" ISCUSTOM="false"></Field><Field ID="LWHUOM" LABEL="Length, Width, Height UOM" DESCRIPTION="Length, Width, Height UOM" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="LENGTH" LABEL="Length" DESCRIPTION="Length" REQUIRED="false" READONLY="false" DATATYPE="DECIMAL" ISCUSTOM="false"></Field><Field ID="WIDTH" LABEL="Width" DESCRIPTION="Width" REQUIRED="false" READONLY="false" DATATYPE="DECIMAL" ISCUSTOM="false"></Field><Field ID="HEIGHT" LABEL="Height" DESCRIPTION="Height" REQUIRED="false" READONLY="false" DATATYPE="DECIMAL" ISCUSTOM="false"></Field><Field ID="THICKNESSUOM" LABEL="Thickness UOM" DESCRIPTION="Thickness UOM" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="THICKNESS" LABEL="Thickness" DESCRIPTION="Thickness" REQUIRED="false" READONLY="false" DATATYPE="DECIMAL" ISCUSTOM="false"></Field><Field ID="MINIMUMTHICKNESS" LABEL="Minimum thickness" DESCRIPTION="Minimum Thickness" REQUIRED="false" READONLY="false" DATATYPE="DECIMAL" ISCUSTOM="false"></Field><Field ID="MAXIMUMTHICKNESS" LABEL="Maximum thickness" DESCRIPTION="Maximum Thickness" REQUIRED="false" READONLY="false" DATATYPE="DECIMAL" ISCUSTOM="false"></Field><Field ID="AREAUOM" LABEL="Area UOM" DESCRIPTION="Area UOM" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="AREA" LABEL="Area" DESCRIPTION="Area" REQUIRED="false" READONLY="false" DATATYPE="DECIMAL" ISCUSTOM="false"></Field><Field ID="VOLUMEUOM" LABEL="Volume UOM" DESCRIPTION="Volume UOM" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="VOLUME" LABEL="Volume" DESCRIPTION="Volume" REQUIRED="false" READONLY="false" DATATYPE="DECIMAL" ISCUSTOM="false"></Field><Field ID="DIAMETERUOM" LABEL="Diameter UOM" DESCRIPTION="Diameter UOM" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="INNERDIAMETER" LABEL="Inner diameter" DESCRIPTION="Inner diameter" REQUIRED="false" READONLY="false" DATATYPE="DECIMAL" ISCUSTOM="false"></Field><Field ID="OUTERDIAMETER" LABEL="Outer diameter" DESCRIPTION="Outer diameter" REQUIRED="false" READONLY="false" DATATYPE="DECIMAL" ISCUSTOM="false"></Field><Field ID="DUROMETER" LABEL="Durometer" DESCRIPTION="Durometer" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="UPC12" LABEL="UPC-12" DESCRIPTION="UPC-12" REQUIRED="false" READONLY="false" DATATYPE="INTEGER" ISCUSTOM="false"></Field><Field ID="EAN13" LABEL="EAN-13" DESCRIPTION="EAN-13 European Article Number" REQUIRED="false" READONLY="false" DATATYPE="INTEGER" ISCUSTOM="false"></Field><Field ID="SAFETYITEM" LABEL="Safety item" DESCRIPTION="Safety Item" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="RESTRICTEDITEM" LABEL="Restricted item" DESCRIPTION="Restricted Item" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="COMPLIANTITEM" LABEL="Compliant item" DESCRIPTION="Compliant Item" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="CONDITION" LABEL="Condition" DESCRIPTION="Condition" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="ENGINEERINGALERT" LABEL="Engineering alert" DESCRIPTION="Engineering alert" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="SPECIFICATION1" LABEL="Specification 1" DESCRIPTION="Specification 1" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="SPECIFICATION2" LABEL="Specification 2" DESCRIPTION="Specification 2" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="SPECIFICATION3" LABEL="Specification 3" DESCRIPTION="Specification 3" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="ENGINEERINGAPPROVAL" LABEL="Engineering approval" DESCRIPTION="Engineering approval" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="QUALITYCONTROLAPPROVAL" LABEL="Quality control approval" DESCRIPTION="Quality control approval" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="SALESAPPROVAL" LABEL="Sales approval" DESCRIPTION="Sales approval" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="PRIMARYCOUNTRYOFORIGIN" LABEL="Primary country of origin" DESCRIPTION="Primary country of origin" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="BRAND" LABEL="Brand" DESCRIPTION="Brand" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="SUBBRAND" LABEL="Sub brand" DESCRIPTION="Sub brand" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="CATEGORY" LABEL="Category" DESCRIPTION="Category" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="SUBCATEGORY" LABEL="Sub category" DESCRIPTION="Sub category" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="CATALOGREF" LABEL="Catalog reference" DESCRIPTION="Catalog reference" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="COLOR" LABEL="Color" DESCRIPTION="Color" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="STYLE" LABEL="Style" DESCRIPTION="Style" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="SIZE1" LABEL="Size 1" DESCRIPTION="Size 1" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="SIZE2" LABEL="Size 2" DESCRIPTION="Size 2" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="GIFTCARD" LABEL="Gift card" DESCRIPTION="Gift Card" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="WEBENABLED" LABEL="Web enabled" DESCRIPTION="Web Enabled" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="false"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="WEBNAME" LABEL="Web name" DESCRIPTION="Web Name" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="WEBSHORTDESC" LABEL="Web short description" DESCRIPTION="Web short description" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="WEBLONGDESC" LABEL="Web long description" DESCRIPTION="Web long description" REQUIRED="false" READONLY="false" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="MEGAENTITYKEY" LABEL="Created at - Entity key" DESCRIPTION="No description specified" REQUIRED="false" READONLY="true" DATATYPE="INTEGER" ISCUSTOM="false"></Field><Field ID="MEGAENTITYID" LABEL="Created at - Entity ID" DESCRIPTION="No description specified" REQUIRED="false" READONLY="true" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="MEGAENTITYNAME" LABEL="Created at - Entity name" DESCRIPTION="No description specified" REQUIRED="false" READONLY="true" DATATYPE="TEXT" ISCUSTOM="false"></Field><Field ID="XTREEM" LABEL="XTreeM" DESCRIPTION="No description specified" REQUIRED="false" READONLY="false" DATATYPE="BOOLEAN" ISCUSTOM="true"><VALIDVALUES VALIDVALUE="true" VALIDVALUE="false"></VALIDVALUES></Field><Field ID="XTREEM_ID" LABEL="XTreeM_id" DESCRIPTION="Instance _id in XtreeM" REQUIRED="false" READONLY="false" DATATYPE="DECIMAL" ISCUSTOM="true"></Field><Field ID="RECORD_URL" LABEL="Record URL" DESCRIPTION=" A portable, user-independent, deep-link URL for viewing this record" REQUIRED="false" READONLY="true" DATATYPE="TEXT" ISCUSTOM="false"></Field></Fields><Relationships><Relationship OBJECTPATH="GLGROUP" OBJECTNAME="ITEMGLGROUP" LABEL="Item GL group" RELATIONSHIPTYPE="MANY2ONE" RELATEDBY="GLGRPKEY"></Relationship><Relationship OBJECTPATH="PRODUCTLINE" OBJECTNAME="PRODUCTLINE" LABEL="Product line" RELATIONSHIPTYPE="MANY2ONE" RELATEDBY="PRODUCTLINEID"></Relationship><Relationship OBJECTPATH="TAXGROUP" OBJECTNAME="TAXGROUP" LABEL="Item tax group" RELATIONSHIPTYPE="MANY2ONE" RELATEDBY="TAXGROUPKEY"></Relationship><Relationship OBJECTPATH="CNMEACATEGORY" OBJECTNAME="MEACATEGORY" LABEL="Billing template" RELATIONSHIPTYPE="MANY2ONE" RELATEDBY="CNMEACATEGORYNAME"></Relationship><Relationship OBJECTPATH="CNBILLINGTEMPLATE" OBJECTNAME="CONTRACTBILLINGTEMPLATE" LABEL="Billing template" RELATIONSHIPTYPE="MANY2ONE" RELATEDBY="CNBILLINGTEMPLATENAME"></Relationship><Relationship OBJECTPATH="CNREVENUETEMPLATE" OBJECTNAME="CONTRACTREVENUETEMPLATE" LABEL="Revenue template # 1" RELATIONSHIPTYPE="MANY2ONE" RELATEDBY="CNREVENUETEMPLATENAME"></Relationship><Relationship OBJECTPATH="CNREVENUE2TEMPLATE" OBJECTNAME="CONTRACTREVENUETEMPLATE" LABEL="Revenue template # 2" RELATIONSHIPTYPE="MANY2ONE" RELATEDBY="CNREVENUE2TEMPLATENAME"></Relationship><Relationship OBJECTPATH="CNEXPENSETEMPLATE" OBJECTNAME="CONTRACTEXPENSETEMPLATE" LABEL="Expense template # 1" RELATIONSHIPTYPE="MANY2ONE" RELATEDBY="CNEXPENSETEMPLATENAME"></Relationship><Relationship OBJECTPATH="CNEXPENSE2TEMPLATE" OBJECTNAME="CONTRACTEXPENSETEMPLATE" LABEL="Expense template # 2" RELATIONSHIPTYPE="MANY2ONE" RELATEDBY="CNEXPENSE2TEMPLATENAME"></Relationship><Relationship OBJECTPATH="MELOCATION" OBJECTNAME="LOCATION" LABEL="Created At Entity Information" RELATIONSHIPTYPE="MANY2ONE" RELATEDBY="MEGAENTITYID"></Relationship></Relationships></Type></data></result></operation></response>