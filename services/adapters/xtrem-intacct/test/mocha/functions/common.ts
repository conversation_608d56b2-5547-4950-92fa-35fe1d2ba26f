import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as IntacctAdapter from '../../../index';

describe('Test common functions  ', () => {
    before(() => {});

    it(' isIntacctActive  ', () =>
        Test.withContext(async context => {
            assert.isTrue(await IntacctAdapter.functions.isIntacctActive(context));
        }));
    it(' isIntacctActive  ', async () => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-intacct-gateway': { senderId: 'senderUser', senderPassword: 'pass1234' },
            },
        });
        await Test.withContext(async context => {
            assert.isTrue(await IntacctAdapter.functions.isIntacctActive(context));
        });
    });

    it(' getIntacctNode Geting user node  ', () =>
        Test.withContext(context => {
            const xtremNode = IntacctAdapter.functions.getIntacctNode(context, 'user');
            assert.equal(xtremNode.name, 'User');
        }));

    it(' desactiveAll before 2 after 0  ', () =>
        Test.withContext(async context => {
            const userNode = IntacctAdapter.functions.getIntacctNode(context, 'user');

            assert.equal(await context.query(userNode, { filter: { isIntacct: true, isActive: true } }).length, 2);

            await IntacctAdapter.functions.disableAll(context, userNode, { isIntacct: true });

            assert.equal(await context.query(userNode, { filter: { isIntacct: true, isActive: true } }).length, 0);
        }));

    it(' installApplicationXtreemAuditTrail function   ', async () => {
        Test.patchConfig({
            app: 'testApp',
            packages: {
                '@sage/xtrem-intacct-gateway': {
                    senderId: 'senderUser',
                    senderPassword: 'pass1234',
                    intacctEventApiGatewayUrl: 'https://url.dummy/v1/intacctEvent',
                },
            },
        });

        await Test.withContext(
            context => (() => IntacctAdapter.functions.installApplicationXtreemAuditTrail(context))(),
            {
                scenario: 'instal-object',
                directory: __dirname,
            },
        );
    });
});
