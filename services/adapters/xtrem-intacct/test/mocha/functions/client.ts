import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as IntacctAdapter from '../../../index';

describe('Test Call Api functions  ', () => {
    before(() => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-intacct-gateway': { senderId: 'senderUser', senderPassword: 'pass1234' },
            },
        });
    });

    it(' Call function  test mocks inspect     ', () =>
        Test.withContext(
            async context => {
                const result = await new IntacctAdapter.classes.sdk.Functions.Lookup<{
                    status: string;
                    functionName: string;
                }>(context, 'ITEM', {
                    resultFunction: gl => gl,
                }).execute();
                /**  using any because by default execute return an array ( because we use gl.data )
                 * there we want  all the result json object so we cast
                 * Unit test usage only ! ! */

                assert.equal(result.status, 'success');
                assert.equal(result.functionName, 'lookup');
            },
            {
                scenario: 'Lookup-ITEM',
                directory: __dirname,
            },
        ));

    it(' Call function  test mocks inspect asynchronous   ', () =>
        Test.withContext(
            async context => {
                const result = await new IntacctAdapter.classes.sdk.Functions.Lookup(context, 'ITEM', {
                    resultFunction: data => data,
                }).executeAsync();

                assert.equal(result.status, 'success');
            },
            {
                scenario: 'Lookup-ITEM',
                directory: __dirname,
            },
        ));
});
