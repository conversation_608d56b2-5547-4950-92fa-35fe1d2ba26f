import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacct from '../../../index';

describe(' Read by Name Function   ', () => {
    it('Escape comma characters', () =>
        Test.withContext(async context => {
            const query = await new xtremIntacct.classes.sdk.Functions.ReadByName(context, {
                objectName: 'CONTACT',
                filter: ['Me,You'],
            }).xmlWithoutFunction(false);

            assert.deepEqual(
                query,
                '<readByName><object>CONTACT</object><keys>Me\\,You</keys><fields>*</fields><returnFormat>xml</returnFormat></readByName>',
            );
        }));
    it(' Read Casino', () =>
        Test.withContext(
            async context => {
                const list = await new xtremIntacct.classes.sdk.Functions.ReadByName(context, {
                    objectName: 'VENDOR',
                    fields: ['VENDORID'],
                    filter: ['CASINO'],
                }).execute();
                assert.isArray(list);
                assert.deepEqual(list, [{ VENDORID: 'CASINO' }]);
            },
            {
                scenario: 'read-by-name-casino',
                directory: __dirname,
            },
        ));
    it(' Read Casino - intacctInstance Options pass to the super ', () =>
        Test.withContext(async context => {
            const intacctInstance = await context.read(xtremIntacct.nodes.Intacct, { id: 'DEV' });
            assert.equal(await intacctInstance.companyId, 'Int-X3-WD-2');

            const listRequest = new xtremIntacct.classes.sdk.Functions.ReadByName(context, {
                intacctInstanceId: 'DEV',
                objectName: 'VENDOR',
                fields: ['VENDORID'],
                filter: ['CASINO'],
            });

            assert.equal(await (await listRequest.intacctInstance).companyId, 'Int-X3-WD-2');
        }));
    it(' ReadByName - throw ', () =>
        Test.withContext(context => {
            assert.throw(
                () =>
                    new xtremIntacct.classes.sdk.Functions.ReadByName(context, {
                        objectName: '',
                        fields: ['VENDORID'],
                        filter: ['CASINO'],
                    }),
                'No objectName',
            );
            assert.throw(
                () =>
                    new xtremIntacct.classes.sdk.Functions.ReadByName(context, {
                        objectName: 'VENDOR',
                        fields: ['VENDORID', { name: 'RECORDNO' }],
                        filter: ['CASINO'],
                    }),
                'Field must be string',
            );
        }));
});
