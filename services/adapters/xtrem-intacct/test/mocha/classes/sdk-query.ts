import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremIntacct from '../../../index';

const dirname = __dirname.replace('/build/', '/');

/**
 * Todo : Add real request mocked
 */

describe('Generic Classes For Intacct  ', () => {
    before(() => {});
    it('SDK Query Class request ', () =>
        Test.withContext(
            async context => {
                const result = await new xtremIntacct.classes.sdk.Functions.Query(context, {
                    objectName: 'VENDOR',
                    fields: [
                        { name: 'GLGROUP', type: 'select' },
                        { name: 'GLGROUP', type: 'count' },
                    ],
                }).execute();

                assert.isArray(result);

                assert.deepEqual(result, [
                    { GLGROUP: 'Direct Materials', 'COUNT.GLGROUP': '6' },
                    { GLGROUP: '', 'COUNT.GLGROUP': '0' },
                ]);
            },
            {
                scenario: 'query-count-vendor',
                directory: dirname,
            },
        ));
    it(' Query Class filter ', () =>
        Test.withContext(
            async context => {
                const result = await new xtremIntacct.classes.sdk.Functions.Query(context, {
                    objectName: 'VENDOR',
                    fields: [{ name: 'NAME', type: 'select' }, { name: 'VENDTYPE' }],
                    filter: new xtremIntacct.classes.sdk.Functions.QueryOperator.AndOperator([
                        new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('GLGROUP').like('Direct Materials'),
                        new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('VENDTYPE').like(
                            'Indirect Purchases',
                        ),
                    ]),
                    orderBy: [{ name: 'NAME', isAscending: true }],
                }).execute();

                assert.isArray(result);

                assert.deepEqual(result, [
                    { NAME: 'Massachusetts Department of Revenue', VENDTYPE: 'Indirect Purchases' },
                ]);
            },
            {
                scenario: 'query-vendor-filter',
                directory: dirname,
            },
        ));

    it('Get Data Api', () =>
        Test.withContext(
            async context => {
                const data = await xtremIntacct.functions.getIntacctData(context, {
                    fields: [
                        'VENDORID',
                        'NAME',
                        'VENDORACCOUNTNO',
                        'TAXID',
                        'CURRENCY',
                        'DISPLAYCONTACT.MAILADDRESS.COUNTRYCODE',
                    ],
                    objectName: 'VENDOR',
                    filters: [
                        {
                            where: 'VENDORID',
                            whereValue: '1234',
                        },
                    ],
                });
                assert.deepEqual(data, [
                    {
                        VENDORID: '1234',
                        NAME: 'My Supplier',
                        VENDORACCOUNTNO: '',
                        TAXID: '*************',
                        CURRENCY: 'EUR',
                        'DISPLAYCONTACT.MAILADDRESS.COUNTRYCODE': 'FR',
                    },
                ]);
            },
            {
                scenario: 'get-data-map',
                directory: __dirname,
            },
        ));
    it.skip('Get Data Api - multiple query', () =>
        // Find a way how to test this using sinon / sandbox
        Test.withContext(
            async context => {
                const sandbox = sinon.createSandbox();
                await sandbox.stub(xtremIntacct.classes.sdk.Functions.Query.prototype, 'execute').call(() => null);
                const queryExecuteSpy = sinon.spy(xtremIntacct.classes.sdk.Functions.Query.prototype, 'execute');

                const data = await xtremIntacct.functions.getIntacctData(context, {
                    fields: ['VENDORID'],
                    objectName: 'VENDOR',
                    pageSize: 5,
                });
                assert.equal(queryExecuteSpy.getCalls().length, 57);
                assert.equal(data.length, 285);
            },
            {
                scenario: 'get-data-multiple-query',
                directory: __dirname,
            },
        ));
    it('SDK Query Item request with specific intacctInstance - no query ', () =>
        Test.withContext(async context => {
            const intacctInstance = await context.read(xtremIntacct.nodes.Intacct, { id: 'QA' });
            assert.equal(await intacctInstance.companyId, 'Int-X3-WD-3');

            const query = new xtremIntacct.classes.sdk.Functions.Query(context, {
                intacctInstanceId: 'DEV',
                objectName: 'ITEM',
                fields: ['NAME'],
            });

            assert.equal(await (await query.intacctInstance).companyId, 'Int-X3-WD-2');
        }));
});
