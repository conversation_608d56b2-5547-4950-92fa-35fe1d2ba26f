import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacct from '../../../index';

describe('Lookup tests', () => {
    it(' Lookup Request ', () =>
        Test.withContext(async context => {
            const lookup = new xtremIntacct.classes.sdk.Functions.Lookup(context, 'VENDOR', {});
            assert.match(
                await lookup.xmlWithoutFunction(false),
                /<lookup><object>VENDOR<\/object><\/lookup>/gm,
                await lookup.xmlWithoutFunction(false),
            );
        }));

    it(' Lookup execution  ', () =>
        Test.withContext(
            async context => {
                const lookup = await new xtremIntacct.classes.sdk.Functions.Lookup(context, 'SODOCUMENT', {}).execute();
                assert.isArray(lookup);
            },
            {
                scenario: 'lookup-SODOCUMENT',
                directory: __dirname,
            },
        ));
});
