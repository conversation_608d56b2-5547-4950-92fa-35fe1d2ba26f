import * as IA from '@intacct/intacct-sdk';
import { BusinessRuleError, SystemError } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import * as xtremIntacct from '../../../index';

const dirname = __dirname.replace('/build/', '/');

describe('OnlineResponse', () => {
    it('should parse response & return the result', () => {
        const xmlPath = path.resolve(dirname, 'xml-requests/response-vendor.xml');
        const xml = fs.readFileSync(xmlPath).toString();

        const response = new xtremIntacct.classes.sdk.Xml.OnlineResponse(xml);

        assert.equal(response.getResult().data.length, 6);
    });

    it('should parse response and verify acknowledgement is success', () => {
        const xml = `<?xml version="1.0" encoding="UTF-8"?>
<response>
      <control>
            <status>success</status>
            <senderid>testsenderid</senderid>
            <controlid>ControlIdHere</controlid>
            <uniqueid>false</uniqueid>
            <dtdversion>3.0</dtdversion>
      </control>
      <operation>
            <authentication>
                  <status>success</status>
                  <userid>fakeuser</userid>
                  <companyid>fakecompany</companyid>
                  <locationid></locationid>
                  <sessiontimestamp>2015-10-22T20:58:27-07:00</sessiontimestamp>
                  <sessiontimeout>2015-10-23T20:58:27-07:00</sessiontimeout>
            </authentication>
            <result>
                  <status>success</status>
                  <function>getAPISession</function>
                  <controlid>testControlId</controlid>
                  <data>
                        <api>
                              <sessionid>fAkESesSiOnId..</sessionid>
                              <endpoint>https://api.intacct.com/ia/xml/xmlgw.phtml</endpoint>
                              <locationid></locationid>
                        </api>
                  </data>
            </result>
      </operation>
</response>`;

        const response = new xtremIntacct.classes.sdk.Xml.OnlineResponse(xml);

        assert.isArray(response.results);
        assert.instanceOf(response.results[0], IA.Xml.Response.Result);
    });

    it('should throw exception with missing operation block', () => {
        assert.throws(
            () => {
                const xml = `<?xml version="1.0" encoding="UTF-8"?>
<response>
      <control>
            <status>success</status>
            <senderid>testsenderid</senderid>
            <controlid>ControlIdHere</controlid>
            <uniqueid>false</uniqueid>
            <dtdversion>3.0</dtdversion>
      </control>
</response>`;
                return new xtremIntacct.classes.sdk.Xml.OnlineResponse(xml);
            },
            SystemError,
            'Response block is missing operation block',
        );
    });
    it('should throw exception with authentication failure', () => {
        assert.throws(
            () => {
                const xml = `<?xml version="1.0" encoding="UTF-8"?>
<response>
      <control>
            <status>success</status>
            <senderid>testsenderid</senderid>
            <controlid>ControlIdHere</controlid>
            <uniqueid>false</uniqueid>
            <dtdversion>3.0</dtdversion>
      </control>
      <operation>
            <authentication>
                  <status>failure</status>
                  <userid>fakeuser</userid>
                  <companyid>fakecompany</companyid>
                  <locationid></locationid>
            </authentication>
            <errormessage>
                  <error>
                        <errorno>XL03000006</errorno>
                        <description></description>
                        <description2>Sign-in information is incorrect</description2>
                        <correction></correction>
                  </error>
            </errormessage>
      </operation>
</response>`;
                return new xtremIntacct.classes.sdk.Xml.OnlineResponse(xml);
            },
            BusinessRuleError,
            'XL03000006 Sign-in information is incorrect',
        );
    });
    it('should throw exception with missing authentication block', () => {
        assert.throws(
            () => {
                const xml = `<?xml version="1.0" encoding="UTF-8"?>
<response>
      <control>
            <status>success</status>
            <senderid>testsenderid</senderid>
            <controlid>ControlIdHere</controlid>
            <uniqueid>false</uniqueid>
            <dtdversion>3.0</dtdversion>
      </control>
      <operation><authentication></authentication></operation>
</response>`;
                return new xtremIntacct.classes.sdk.Xml.OnlineResponse(xml);
            },
            SystemError,
            'Authentication block is missing from operation element',
        );
    });
    it('should throw exception with missing result block', () => {
        assert.throws(
            () => {
                const xml = `<?xml version="1.0" encoding="UTF-8"?>
<response>
      <control>
            <status>success</status>
            <senderid>testsenderid</senderid>
            <controlid>ControlIdHere</controlid>
            <uniqueid>false</uniqueid>
            <dtdversion>3.0</dtdversion>
      </control>
      <operation>
            <authentication>
                  <status>success</status>
                  <userid>fakeuser</userid>
                  <companyid>fakecompany</companyid>
                  <locationid></locationid>
                  <sessiontimestamp>2015-10-22T20:58:27-07:00</sessiontimestamp>
                  <sessiontimeout>2015-10-23T20:58:27-07:00</sessiontimeout>
            </authentication>
      </operation>
</response>`;
                return new xtremIntacct.classes.sdk.Xml.OnlineResponse(xml);
            },
            Error,
            'Result block is missing from operation element',
        );
    });
    it('should throw response exception with errors', () => {
        assert.throws(
            () => {
                const xml = `<?xml version="1.0" encoding="UTF-8"?>
<response>
    <control>
        <status>failure</status>
        <senderid></senderid>
        <controlid></controlid>
    </control>
    <errormessage>
        <error>
            <errorno>PL04000055</errorno>
            <description></description>
            <description2>This company is a demo company and has expired.</description2>
            <correction></correction>
        </error>
    </errormessage>
</response>`;
                return new xtremIntacct.classes.sdk.Xml.OnlineResponse(xml);
            },
            BusinessRuleError,
            'PL04000055 This company is a demo company and has expired.',
        );
    });
    it('should return failure status and errors', () => {
        const xml = `<?xml version="1.0" encoding="UTF-8"?>
<response>
      <control>
            <status>success</status>
            <senderid>testsenderid</senderid>
            <controlid>ControlIdHere</controlid>
            <uniqueid>false</uniqueid>
            <dtdversion>3.0</dtdversion>
      </control>
      <operation>
            <authentication>
                  <status>success</status>
                  <userid>fakeuser</userid>
                  <companyid>fakecompany</companyid>
                  <locationid></locationid>
                  <sessiontimestamp>2015-10-25T11:07:22-07:00</sessiontimestamp>
                  <sessiontimeout>2015-10-26T10:08:34-07:00</sessiontimeout>
            </authentication>
            <result>
                  <status>failure</status>
                  <function>readByQuery</function>
                  <controlid>testControlId</controlid>
                  <errormessage>
                        <error>
                              <errorno>Query Failed</errorno>
                              <description></description>
                              <description2>Object definition BADOBJECT not found</description2>
                              <correction></correction>
                        </error>
                  </errormessage>
            </result>
      </operation>
</response>`;

        const response = new xtremIntacct.classes.sdk.Xml.OnlineResponse(xml);

        const result = response.results[0];
        assert.equal(result.status, 'failure');
        assert.isArray(result.errors);
        assert.isString(result.errors[0]);
    });
});
