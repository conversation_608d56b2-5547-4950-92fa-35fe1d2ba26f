{"_status": "success", "_functionName": "lookup", "_controlId": "f856eaf9-06f6-429b-9ff8-ef990522a8fb", "_listType": "All", "_count": 1, "_data": [{"$": {"Name": "XTreeM_Audit_Trail", "DocumentType": ""}, "Fields": {"Field": [{"ID": "name", "LABEL": "XTreeM_Audit_Trail", "DESCRIPTION": "", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "true"}, {"ID": "created<PERSON>y", "LABEL": "Created By", "DESCRIPTION": "", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "INTEGER", "ISCUSTOM": "true"}, {"ID": "createdAt", "LABEL": "Created At", "DESCRIPTION": "", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TIMESTAMP", "ISCUSTOM": "true"}, {"ID": "updatedBy", "LABEL": "Updated By", "DESCRIPTION": "", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "INTEGER", "ISCUSTOM": "true"}, {"ID": "updatedAt", "LABEL": "Updated At", "DESCRIPTION": "", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TIMESTAMP", "ISCUSTOM": "true"}, {"ID": "id", "LABEL": "ID", "DESCRIPTION": "", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "INTEGER", "ISCUSTOM": "true"}, {"ID": "object_name", "LABEL": "Object Name", "DESCRIPTION": "", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "true"}, {"ID": "change_made", "LABEL": "Change made", "DESCRIPTION": "", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "true"}, {"ID": "record_id", "LABEL": "Record ID", "DESCRIPTION": "", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "true"}, {"ID": "record_number", "LABEL": "Record Number", "DESCRIPTION": "", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "true"}]}, "Relationships": {}}]}