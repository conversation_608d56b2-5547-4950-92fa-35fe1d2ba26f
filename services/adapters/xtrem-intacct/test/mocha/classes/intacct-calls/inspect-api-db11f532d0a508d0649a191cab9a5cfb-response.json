{"_status": "success", "_functionName": "inspect", "_controlId": "56a3255a-d1bc-4351-9777-1cd277fedfb5", "_listType": "All", "_count": 1, "_data": [{"$": {"typename": "region"}, "$value": "Region"}, {"$": {"typename": "stock_site"}, "$value": "Stock site"}, {"$": {"typename": "xtreem_audit_trail"}, "$value": "XTreeM_Audit_Trail"}, {"$": {"typename": "APADJUSTMENT"}, "$value": "AP adjustment"}, {"$": {"typename": "APADJUSTMENTITEM"}, "$value": "AP adjustment detail"}, {"$": {"typename": "APBILL"}, "$value": "AP bill"}, {"$": {"typename": "APBILLITEM"}, "$value": "AP bill detail"}, {"$": {"typename": "APBILLPAYMENT"}, "$value": "AP bill payment"}, {"$": {"typename": "APPAYMENT"}, "$value": "AP payment"}, {"$": {"typename": "APPAYMENTITEM"}, "$value": "AP payment detail"}, {"$": {"typename": "ARADJUSTMENT"}, "$value": "AR adjustment"}, {"$": {"typename": "ARADJUSTMENTITEM"}, "$value": "AR adjustment detail"}, {"$": {"typename": "ARINVOICE"}, "$value": "AR invoice"}, {"$": {"typename": "ARINVOICEITEM"}, "$value": "AR invoice detail"}, {"$": {"typename": "ARINVOICEPAYMENT"}, "$value": "AR invoice payment"}, {"$": {"typename": "ARPAYMENT"}, "$value": "AR payment"}, {"$": {"typename": "ARPAYMENTITEM"}, "$value": "AR payment detail"}, {"$": {"typename": "CLASS"}, "$value": "Channel"}, {"$": {"typename": "CNSACCOUNT"}, "$value": "Consolidation account"}, {"$": {"typename": "CNSACCTBAL"}, "$value": "Consolidation account balance"}, {"$": {"typename": "CNSPERIOD"}, "$value": "Consolidation period"}, {"$": {"typename": "COMPANY"}, "$value": "Company information"}, {"$": {"typename": "COMPANYPREF"}, "$value": "Company preference"}, {"$": {"typename": "CUSTOMER"}, "$value": "Customer"}, {"$": {"typename": "DEPARTMENT"}, "$value": "Department"}, {"$": {"typename": "EEXPENSES"}, "$value": "Employee expense"}, {"$": {"typename": "EEXPENSESITEM"}, "$value": "Employee expense detail"}, {"$": {"typename": "EEXPENSESPAYMENT"}, "$value": "Employee expenses payment"}, {"$": {"typename": "EMPLOYEE"}, "$value": "Employee"}, {"$": {"typename": "EPPAYMENT"}, "$value": "Employee expense reimbursement"}, {"$": {"typename": "EPPAYMENTITEM"}, "$value": "Employee payment detail"}, {"$": {"typename": "EXCHANGERATE"}, "$value": "Exchange rate"}, {"$": {"typename": "EXCHANGERATEENTRY"}, "$value": "Exchange rate entry"}, {"$": {"typename": "GLACCOUNT"}, "$value": "GL account"}, {"$": {"typename": "GLBATCH"}, "$value": "GL batch"}, {"$": {"typename": "GLENTRY"}, "$value": "GL entry"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inventory document"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inventory document detail"}, {"$": {"typename": "ITEM"}, "$value": "<PERSON><PERSON>"}, {"$": {"typename": "LOCATION"}, "$value": "Location"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Purchasing document"}, {"$": {"typename": "PODOCUMENTAPPROVAL"}, "$value": "Purchasing approval history"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Purchasing document detail"}, {"$": {"typename": "PROJECT"}, "$value": "Project"}, {"$": {"typename": "RENEWALMACRO"}, "$value": "Renewal template"}, {"$": {"typename": "REVRECSCHEDULE"}, "$value": "Revenue recognition schedule"}, {"$": {"typename": "REVRECSCHEDULEENTRY"}, "$value": "Revenue recognition schedule entry"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales document"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales document detail"}, {"$": {"typename": "SUBSIDIARY"}, "$value": "Subsidiary"}, {"$": {"typename": "TRXCURRENCIES"}, "$value": "Currency and format setup"}, {"$": {"typename": "USERINFO"}, "$value": "User"}, {"$": {"typename": "VENDOR"}, "$value": "<PERSON><PERSON><PERSON>"}, {"$": {"typename": "WAREHOUSE"}, "$value": "Warehouse"}, {"$": {"typename": "WFPMBATCH"}, "$value": "Wells Fargo Payment Manager summary"}, {"$": {"typename": "WFPMPAYMENTREQUEST"}, "$value": "Wells Fargo payment request"}, {"$": {"typename": "VENDTYPE"}, "$value": "Vendor type"}, {"$": {"typename": "TASK"}, "$value": "Task"}, {"$": {"typename": "TIMESHEET"}, "$value": "Timesheet"}, {"$": {"typename": "TIMESHEETENTRY"}, "$value": "Timesheet entry"}, {"$": {"typename": "APTERM"}, "$value": "AP term"}, {"$": {"typename": "ARTERM"}, "$value": "AR term"}, {"$": {"typename": "ARPAYMENTBATCH"}, "$value": "AR payment summary"}, {"$": {"typename": "GLJOURNAL"}, "$value": "GL journal"}, {"$": {"typename": "ALLOCATION"}, "$value": "Allocation"}, {"$": {"typename": "APBILLBATCH"}, "$value": "AP bill summary"}, {"$": {"typename": "ARINVOICEBATCH"}, "$value": "AR invoice summary"}, {"$": {"typename": "ARACCOUNTLABEL"}, "$value": "AR account label"}, {"$": {"typename": "APACCOUNTLABEL"}, "$value": "AP account label"}, {"$": {"typename": "CONTACT"}, "$value": "Contact"}, {"$": {"typename": "CREDITCARD"}, "$value": "Credit card"}, {"$": {"typename": "CHECKINGACCOUNT"}, "$value": "Checking account"}, {"$": {"typename": "SAVINGSACCOUNT"}, "$value": "Savings account"}, {"$": {"typename": "BANKACCOUNT"}, "$value": "Bank account"}, {"$": {"typename": "STATJOURNAL"}, "$value": "Statistical journal"}, {"$": {"typename": "APPAYMENTREQUEST"}, "$value": "AP payment request"}, {"$": {"typename": "APRECURBILL"}, "$value": "AP recurring bill"}, {"$": {"typename": "BILLABLEEXPENSES"}, "$value": "Billable expense"}, {"$": {"typename": "CHECKLAYOUT"}, "$value": "Check layout"}, {"$": {"typename": "RECURDOCUMENTENTRY"}, "$value": "Recurring document entry"}, {"$": {"typename": "EPPAYMENTREQUEST"}, "$value": "Pending reimbursement"}, {"$": {"typename": "EXCHANGERATETYPES"}, "$value": "Exchange rate type"}, {"$": {"typename": "EXPENSESAPPROVAL"}, "$value": "Expense approval"}, {"$": {"typename": "FINANCIALACCOUNT"}, "$value": "Financial account"}, {"$": {"typename": "GAAPADJJRNL"}, "$value": "GAAP adjustment journal"}, {"$": {"typename": "GLDETAIL"}, "$value": "General Ledger detail"}, {"$": {"typename": "IERELATION"}, "$value": "Inter-entity relationship"}, {"$": {"typename": "INVOICERUN"}, "$value": "Invoice run"}, {"$": {"typename": "INVRECURDOCUMENT"}, "$value": "Recurring inventory transaction"}, {"$": {"typename": "LOCATIONENTITY"}, "$value": "Entity"}, {"$": {"typename": "PORECURDOCUMENT"}, "$value": "Recurring purchasing transaction"}, {"$": {"typename": "PROJECTRESOURCES"}, "$value": "Project resource"}, {"$": {"typename": "PROJECTSTATUS"}, "$value": "Project status"}, {"$": {"typename": "PROJECTTYPE"}, "$value": "Project type"}, {"$": {"typename": "RECURGLBATCH"}, "$value": "Recurring journal entry"}, {"$": {"typename": "RECURGLENTRY"}, "$value": "Recurring journal entry details"}, {"$": {"typename": "REVRECCHANGELOG"}, "$value": "Revenue recognition change history"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Recurring Order Entry transaction"}, {"$": {"typename": "STKITDOCUMENT"}, "$value": "Build and disassemble kits transaction"}, {"$": {"typename": "STKITDOCUMENTENTRY"}, "$value": "Build and disassemble kits transaction detail"}, {"$": {"typename": "TASKRESOURCES"}, "$value": "Task resource"}, {"$": {"typename": "TAXADJJRNL"}, "$value": "Tax adjustment journal"}, {"$": {"typename": "TIMESHEETAPPROVAL"}, "$value": "Timesheet approval history"}, {"$": {"typename": "TIMETYPE"}, "$value": "Time type"}, {"$": {"typename": "BUDGETHEADER"}, "$value": "Budget"}, {"$": {"typename": "GLBUDGET"}, "$value": "GL budget"}, {"$": {"typename": "REPORTINGPERIOD"}, "$value": "Reporting period"}, {"$": {"typename": "STATACCOUNT"}, "$value": "Statistical account"}, {"$": {"typename": "ACCTTITLEBYLOC"}, "$value": "Account title by entity"}, {"$": {"typename": "REVRECTEMPLATE"}, "$value": "Revenue recognition template"}, {"$": {"typename": "REVRECTEMPLENTRY"}, "$value": "Revenue recognition template entry"}, {"$": {"typename": "REVRECTEMPLMILESTONE"}, "$value": "Revenue recognition template entry"}, {"$": {"typename": "REVRECSCHEDULEENTRYTASK"}, "$value": "Revenue recognition schedule entry task detail"}, {"$": {"typename": "EARNINGTYPE"}, "$value": "Earning type"}, {"$": {"typename": "EMPLOYEERATE"}, "$value": "Employee rate"}, {"$": {"typename": "AUDUSERTRAIL"}, "$value": "User permissions activity"}, {"$": {"typename": "ACTIVITYLOG"}, "$value": "Activity trail"}, {"$": {"typename": "COMMENTS"}, "$value": "Comment"}, {"$": {"typename": "GLACCOUNTBALANCE"}, "$value": "GL account balance"}, {"$": {"typename": "BILLINGTEMPLATE"}, "$value": "Billing template"}, {"$": {"typename": "BILLINGSCHEDULE"}, "$value": "Billing schedule"}, {"$": {"typename": "JOURNAL"}, "$value": "Journal"}, {"$": {"typename": "OPENBOOKS"}, "$value": "Open book log"}, {"$": {"typename": "CLOSEBOOKS"}, "$value": "Close book log"}, {"$": {"typename": "UOM"}, "$value": "Unit of measure"}, {"$": {"typename": "UOMDETAIL"}, "$value": "Unit of measure detail"}, {"$": {"typename": "INVPRICELIST"}, "$value": "Price list"}, {"$": {"typename": "INVPRICELISTENTRY"}, "$value": "Price list entry"}, {"$": {"typename": "SOPRICELIST"}, "$value": "Order Entry price list"}, {"$": {"typename": "SOPRICELISTENTRY"}, "$value": "SO price list entry"}, {"$": {"typename": "POPRICELIST"}, "$value": "PO price list"}, {"$": {"typename": "POPRICELISTENTRY"}, "$value": "PO price list entry"}, {"$": {"typename": "GLACCTGRP"}, "$value": "GL account group"}, {"$": {"typename": "ACCTRANGE"}, "$value": "GL account group range"}, {"$": {"typename": "GLACCTGRPMEMBER"}, "$value": "GL account group member"}, {"$": {"typename": "GLCOMPGRPMEMBER"}, "$value": "GL computation group member"}, {"$": {"typename": "GLCOACATMEMBER"}, "$value": "GL account category"}, {"$": {"typename": "ALLOCATIONENTRY"}, "$value": "Allocation entry"}, {"$": {"typename": "EMPLOYEETYPE"}, "$value": "Employee type"}, {"$": {"typename": "EMPLOYEEENTITYCONTACTS"}, "$value": "Employee entity contact"}, {"$": {"typename": "GLRESOLVE"}, "$value": "GL entry resolve"}, {"$": {"typename": "ACHBANK"}, "$value": "ACH bank"}, {"$": {"typename": "AISLE"}, "$value": "Aisle"}, {"$": {"typename": "BIN"}, "$value": "Bin"}, {"$": {"typename": "ICROW"}, "$value": "Row"}, {"$": {"typename": "PRODUCTLINE"}, "$value": "Product line"}, {"$": {"typename": "ITEMVENDOR"}, "$value": "Item/vendor info"}, {"$": {"typename": "ITEMWAREHOUSEINFO"}, "$value": "Item and warehouse info"}, {"$": {"typename": "ITEMCOMPONENT"}, "$value": "Kit components"}, {"$": {"typename": "GLACCTGRPHIERARCHY"}, "$value": "Account groups hierarchy"}, {"$": {"typename": "TAXGROUP"}, "$value": "Contact tax group"}, {"$": {"typename": "ITEMTAXGROUP"}, "$value": "Item tax group"}, {"$": {"typename": "ACCTLABELTAXGROUP"}, "$value": "AR account label tax group"}, {"$": {"typename": "LOCATIONGROUP"}, "$value": "Location group"}, {"$": {"typename": "DEPARTMENTGROUP"}, "$value": "Department group"}, {"$": {"typename": "VENDORGROUP"}, "$value": "Vendor group"}, {"$": {"typename": "CUSTOMERGROUP"}, "$value": "Customer group"}, {"$": {"typename": "PROJECTGROUP"}, "$value": "Project group"}, {"$": {"typename": "EMPLOYEEGROUP"}, "$value": "Employee group"}, {"$": {"typename": "CLASSGROUP"}, "$value": "Channel group"}, {"$": {"typename": "ITEMGROUP"}, "$value": "Item group"}, {"$": {"typename": "EEACCOUNTLABEL"}, "$value": "Expense type"}, {"$": {"typename": "USERROLES"}, "$value": "User role"}, {"$": {"typename": "KITCOSTING"}, "$value": "Kit costing"}, {"$": {"typename": "CUSTOMERVISIBILITY"}, "$value": "Customer visibility"}, {"$": {"typename": "VENDORVISIBILITY"}, "$value": "Vendor visibility"}, {"$": {"typename": "CUSTTYPE"}, "$value": "Customer type"}, {"$": {"typename": "TERRITORYGROUP"}, "$value": "Territory group"}, {"$": {"typename": "TERRITORYGRPMEMBER"}, "$value": "Territory group member"}, {"$": {"typename": "GLDOCDETAIL"}, "$value": "General <PERSON><PERSON> document detail"}, {"$": {"typename": "POSITIONSKILL"}, "$value": "Positions and skills"}, {"$": {"typename": "EMPLOYEEPOSITIONSKILL"}, "$value": "Employee positions and skills "}, {"$": {"typename": "OUTOFOFFICE"}, "$value": "Out of office"}, {"$": {"typename": "EMPLOYEEOUTOFOFFICE"}, "$value": "Employee out of office"}, {"$": {"typename": "PROJECTTOTALS"}, "$value": "Project totals"}, {"$": {"typename": "PRTAXENTRY"}, "$value": "Prtaxentry"}, {"$": {"typename": "TRANSTMPLENTRY"}, "$value": "Line items"}, {"$": {"typename": "INVDOCUMENTSUBTOTALS"}, "$value": "Document subtotals"}, {"$": {"typename": "PODOCUMENTSUBTOTALS"}, "$value": "Purchasing document subtotals"}, {"$": {"typename": "SODOCUMENTSUBTOTALS"}, "$value": "Order Entry transaction subtotals"}, {"$": {"typename": "INVRECURSUBTOTALS"}, "$value": "Inv recurring subtotals"}, {"$": {"typename": "PORECURSUBTOTALS"}, "$value": "PO recurring subtotals"}, {"$": {"typename": "SORECURSUBTOTALS"}, "$value": "SO recurring subtotals"}, {"$": {"typename": "APRECURBILLENTRY"}, "$value": "Recurring AP bill detail"}, {"$": {"typename": "TRANSACTIONRULE"}, "$value": "Transaction rule"}, {"$": {"typename": "TRANSACTIONRULEDETAIL"}, "$value": "Transaction rule detail"}, {"$": {"typename": "PROJECTTRANSACTIONRULE"}, "$value": "Project transaction rule"}, {"$": {"typename": "EXPENSEPAYMENTTYPE"}, "$value": "Expense payment type"}, {"$": {"typename": "POAPPROVALRULE"}, "$value": "Purchasing approval rule"}, {"$": {"typename": "POAPPROVALRULEDETAIL"}, "$value": "Purchasing approval rule details"}, {"$": {"typename": "POAPPROVALPOLICY"}, "$value": "Purchasing approval policy"}, {"$": {"typename": "POAPPROVALPOLICYDETAIL"}, "$value": "Purchasing approval policy details"}, {"$": {"typename": "POAPPROVALDELEGATE"}, "$value": "Approval delegate"}, {"$": {"typename": "POAPPROVALDELEGATEDETAIL"}, "$value": "Manage delegates"}, {"$": {"typename": "POAPPROVALRULESET"}, "$value": "Value approval rule set"}, {"$": {"typename": "SOSUBTOTALTEMPLATE"}, "$value": "Order Entry subtotal template"}, {"$": {"typename": "SOSUBTOTALTEMPLATEDETAIL"}, "$value": "Subtotal template detail"}, {"$": {"typename": "POSUBTOTALTEMPLATE"}, "$value": "Purchasing subtotal template"}, {"$": {"typename": "POSUBTOTALTEMPLATEDETAIL"}, "$value": "Subtotal template detail"}, {"$": {"typename": "APAPPROVALRULE"}, "$value": "AP approval rule"}, {"$": {"typename": "APAPPROVALPOLICY"}, "$value": "AP approval policy"}, {"$": {"typename": "APAPPROVALRULESET"}, "$value": "Value approval rule set"}, {"$": {"typename": "INTERENTITYSETUP"}, "$value": "Inter-entity setup"}, {"$": {"typename": "ENTITYACCTDEFAULT"}, "$value": "Inter-entity default account mapping"}, {"$": {"typename": "ENTITYACCTOVERRIDE"}, "$value": "Inter-entity relationship"}, {"$": {"typename": "ITEMCROSSREF"}, "$value": "Item cross reference"}, {"$": {"typename": "ARRECURINVOICEENTRY"}, "$value": "Recurring AR invoice detail"}, {"$": {"typename": "APADVANCE"}, "$value": "AP advance"}, {"$": {"typename": "APADVANCEITEM"}, "$value": "AP advance detail"}, {"$": {"typename": "ARADVANCE"}, "$value": "AR advance"}, {"$": {"typename": "ARADVANCEITEM"}, "$value": "AR advance detail"}, {"$": {"typename": "ARRECURINVOICE"}, "$value": "Recurring AR invoice"}, {"$": {"typename": "EXPENSEADJUSTMENTS"}, "$value": "Expense adjustments"}, {"$": {"typename": "EXPENSEADJUSTMENTSITEM"}, "$value": "Expense adjustments detail"}, {"$": {"typename": "TRANSTMPLBATCH"}, "$value": "Transaction template"}, {"$": {"typename": "PRENTRY"}, "$value": "PR Entry"}, {"$": {"typename": "CCTRANSACTION"}, "$value": "Credit card transaction"}, {"$": {"typename": "CCTRANSACTIONENTRY"}, "$value": "Credit card transaction entry"}, {"$": {"typename": "OTHERRECEIPTS"}, "$value": "Other receipts"}, {"$": {"typename": "OTHERRECEIPTSENTRY"}, "$value": "Other receipts entry"}, {"$": {"typename": "CREDITCARDFEE"}, "$value": "Credit card charges and other fees"}, {"$": {"typename": "CREDITCARDFEEENTRY"}, "$value": "Credit card charges and other fees entry"}, {"$": {"typename": "BANKFEE"}, "$value": "Bank interest and charges"}, {"$": {"typename": "BANKFEEENTRY"}, "$value": "Bank interest and charges entry"}, {"$": {"typename": "FUNDSTRANSFER"}, "$value": "Funds transfer"}, {"$": {"typename": "FUNDSTRANSFERENTRY"}, "$value": "Funds transfer entry"}, {"$": {"typename": "CHARGEPAYOFF"}, "$value": "Charge payoffs"}, {"$": {"typename": "CHARGEPAYOFFENTRY"}, "$value": "Charge payoffs details"}, {"$": {"typename": "DEPOSIT"}, "$value": "Deposits"}, {"$": {"typename": "DEPOSITENTRY"}, "$value": "Deposits details"}, {"$": {"typename": "ARRECORD"}, "$value": "AR record"}, {"$": {"typename": "ARDETAIL"}, "$value": "AR detail"}, {"$": {"typename": "USERADJBOOK"}, "$value": "User-defined book"}, {"$": {"typename": "USERADJJRNL"}, "$value": "User-defined journal"}, {"$": {"typename": "DDSJOB"}, "$value": "Data Delivery Service job"}, {"$": {"typename": "APIUSAGEDETAIL"}, "$value": "API usage detail"}, {"$": {"typename": "REPORTINGACHEADER"}, "$value": "Reporting accounts"}, {"$": {"typename": "REPORTINGAC"}, "$value": "Reporting accounts"}, {"$": {"typename": "VENDAGING"}, "$value": "Vendor aging report"}, {"$": {"typename": "CUSTAGING"}, "$value": "Customer aging report"}, {"$": {"typename": "EMAILTEMPLATE"}, "$value": "Email template"}, {"$": {"typename": "APRECORD"}, "$value": "AP record"}, {"$": {"typename": "APDETAIL"}, "$value": "AP detail"}, {"$": {"typename": "CMRECORD"}, "$value": "Cash Management record"}, {"$": {"typename": "CMDETAIL"}, "$value": "Cash Management detail"}, {"$": {"typename": "EERECORD"}, "$value": "Employee expense record"}, {"$": {"typename": "EEDETAIL"}, "$value": "Employee expense detail"}, {"$": {"typename": "USERRIGHTS"}, "$value": "User permissions"}, {"$": {"typename": "GLBUDGETHEADER"}, "$value": "Budget"}, {"$": {"typename": "GLBUDGETITEM"}, "$value": "GL budget"}, {"$": {"typename": "APDISCOUNT"}, "$value": "AP discount"}, {"$": {"typename": "APPOSTEDADVANCE"}, "$value": "AP posted advance"}, {"$": {"typename": "BILLBACKTEMPLATE"}, "$value": "<PERSON> back template"}, {"$": {"typename": "CUSTOMERENTITYCONTACTS"}, "$value": "Customer entity contacts"}, {"$": {"typename": "DOCRECALLS"}, "$value": "Docrecalls"}, {"$": {"typename": "DOCUMENTPARINVGL"}, "$value": "Inventory GL definitions"}, {"$": {"typename": "DOCUMENTPARPRGL"}, "$value": "GL definitions"}, {"$": {"typename": "DOCUMENTPARSUBTOTAL"}, "$value": "Document params subtotal"}, {"$": {"typename": "INVDOCUMENTPARTOTALS"}, "$value": "Document parameters total"}, {"$": {"typename": "INITOPENITEMS"}, "$value": "Initial open items"}, {"$": {"typename": "INVDOCUMENTPARAMS"}, "$value": "Inventory transaction definition"}, {"$": {"typename": "ITEMGLGROUP"}, "$value": "Item GL group"}, {"$": {"typename": "MYTIMESHEET"}, "$value": "My timesheet"}, {"$": {"typename": "MYTIMESHEETENTRY"}, "$value": "My timesheet entry"}, {"$": {"typename": "PODOCUMENTPARAMS"}, "$value": "Purchase transaction definition"}, {"$": {"typename": "PARTNERFIELDMAP"}, "$value": "Partner field map"}, {"$": {"typename": "ROLEUSERS"}, "$value": "Role users"}, {"$": {"typename": "ROLES"}, "$value": "Roles"}, {"$": {"typename": "SODOCUMENTPARAMS"}, "$value": "SO transaction definition"}, {"$": {"typename": "SUMMARYBYENTITY"}, "$value": "Summary by entity"}, {"$": {"typename": "TAXDETAIL"}, "$value": "Tax detail"}, {"$": {"typename": "USERGROUP"}, "$value": "User group"}, {"$": {"typename": "VENDORACCTNOLOCHEAD"}, "$value": "Vendor entity account numbers"}, {"$": {"typename": "VENDORENTITYCONTACTS"}, "$value": "Vendor entity contacts"}, {"$": {"typename": "APIUSAGESUMMARY"}, "$value": "API usage summary"}, {"$": {"typename": "APPYMT"}, "$value": "AP payables payment"}, {"$": {"typename": "APPYMTENTRY"}, "$value": "New AP payment line detail"}, {"$": {"typename": "CONTRACT"}, "$value": "Contract"}, {"$": {"typename": "CONTRACTDETAIL"}, "$value": "Contract line"}, {"$": {"typename": "CONTRACTREVENUETEMPLATE"}, "$value": "Revenue template"}, {"$": {"typename": "CONTRACTBILLINGTEMPLATE"}, "$value": "Contract billing template"}, {"$": {"typename": "CONTRACTBILLINGTEMPLATEENTRY"}, "$value": "Contract billing template entry"}, {"$": {"typename": "CONTRACTREVENUESCHEDULE"}, "$value": "Contract revenue schedule 1"}, {"$": {"typename": "CONTRACTREVENUE2SCHEDULE"}, "$value": "Contract revenue schedule 2"}, {"$": {"typename": "CONTRACTREVENUESCHEDULEENTRY"}, "$value": "Contract revenue schedule entry"}, {"$": {"typename": "CONTRACTBILLINGSCHEDULE"}, "$value": "Contract billing schedule"}, {"$": {"typename": "CONTRACTBILLINGSCHEDULEENTRY"}, "$value": "Contract billing schedule entry"}, {"$": {"typename": "CONTRACTREVENUEGLCONFIG"}, "$value": "Contract posting configuration - revenue"}, {"$": {"typename": "CONTRACTEXPENSEGLCONFIG"}, "$value": "Contract posting configuration expense"}, {"$": {"typename": "CONTRACTEXPENSETEMPLATE"}, "$value": "Contract expense template"}, {"$": {"typename": "CONTRACTEXPENSE"}, "$value": "Contract expense"}, {"$": {"typename": "CONTRACTEXPENSESCHEDULE"}, "$value": "Contract expense schedule 1"}, {"$": {"typename": "CONTRACTEXPENSESCHEDULEENTRY"}, "$value": "Contract expense schedule entry"}, {"$": {"typename": "CONTRACTUSAGE"}, "$value": "Contract usage data"}, {"$": {"typename": "CONTRACTSCHFORECAST"}, "$value": "Contract schedule forecast"}, {"$": {"typename": "CONTRACTMEAPRICELIST"}, "$value": "MEA price list"}, {"$": {"typename": "CONTRACTMEAITEMPRICELIST"}, "$value": "MEA price list entry"}, {"$": {"typename": "CONTRACTMEAITEMPRICELISTENTRY"}, "$value": "MEA price list entry detail"}, {"$": {"typename": "CONTRACTPRICELIST"}, "$value": "Billing price list"}, {"$": {"typename": "CONTRACTITEMPRICELIST"}, "$value": "Billing price list entry"}, {"$": {"typename": "CONTRACTITEMPRICELISTENTRY"}, "$value": "Billing price list entry detail"}, {"$": {"typename": "CONTRACTUSAGEBILLING"}, "$value": "Contract usage billing"}, {"$": {"typename": "CUSTOMEREMAILTEMPLATE"}, "$value": "Customer email template"}, {"$": {"typename": "CONTRACTEXPENSE2SCHEDULE"}, "$value": "Contract expense schedule 2"}, {"$": {"typename": "CONTRACTITEMPRCLSTENTYTIER"}, "$value": "Billing price list entry detail tier"}, {"$": {"typename": "CONTRACTCOMPLIANCETASKITEM"}, "$value": "Contract compliance task item"}, {"$": {"typename": "CONTRACTCOMPLIANCETASK"}, "$value": "Contract compliance checklist"}, {"$": {"typename": "NOTE"}, "$value": "Note"}, {"$": {"typename": "CONTRACTCOMPLIANCENOTE"}, "$value": "Contract compliance note"}, {"$": {"typename": "CONTRACTRESOLVE"}, "$value": "Contract subledger links"}, {"$": {"typename": "CONTRACTMEABUNDLE"}, "$value": "Contract MEA allocation scheme"}, {"$": {"typename": "CONTRACTMEABUNDLEENTRY"}, "$value": "Contract MEA bundle entry"}, {"$": {"typename": "WAREHOUSEGROUP"}, "$value": "Warehouse group"}, {"$": {"typename": "CONTRACTGROUP"}, "$value": "Contract or contract group"}, {"$": {"typename": "DROPSHIPHISTORY"}, "$value": "Drop ship history"}, {"$": {"typename": "APPYMTDETAIL"}, "$value": "AP payables payment detail"}, {"$": {"typename": "ROLEGROUPS"}, "$value": "Role groups"}, {"$": {"typename": "ROLEPOLICYASSIGNMENT"}, "$value": "Role policy assignment"}, {"$": {"typename": "MEMBERUSERGROUP"}, "$value": "Member user groups"}, {"$": {"typename": "CUSTOMROLEPOLASSIGNMENT"}, "$value": "Custom role policy assignment"}, {"$": {"typename": "ROLEASSIGNMENT"}, "$value": "Role assignments"}, {"$": {"typename": "CONTRACTALLOCATIONFORBUNDLE"}, "$value": "Contract MEA allocation details"}, {"$": {"typename": "CONTRACTALLOCATIONDETAIL"}, "$value": "Contract MEA allocation details"}, {"$": {"typename": "CONTRACTMRRRESOLVE"}, "$value": "Contract MRR link"}, {"$": {"typename": "RENEWALPRICINGOVERRIDE"}, "$value": "Custom renewal amounts"}, {"$": {"typename": "AUDITHISTORY"}, "$value": "Audit history"}, {"$": {"typename": "CONTRACTNEGATIVEBILLING"}, "$value": "Contract negative billing"}, {"$": {"typename": "CONTRACTNEGATIVEBILLINGENTRY"}, "$value": "Contract negative billing entry"}, {"$": {"typename": "GENINVOICEPREBILL"}, "$value": "Generate invoices preview snapshot run"}, {"$": {"typename": "GENINVOICEPREBILLHEADER"}, "$value": "Generate invoices preview snapshot invoice"}, {"$": {"typename": "GENINVOICEPREBILLLINE"}, "$value": "Generate invoices preview snapshot line"}, {"$": {"typename": "GENINVOICEPREVIEW"}, "$value": "Generate invoices"}, {"$": {"typename": "GENINVOICEPREVIEWHEADER"}, "$value": "Generate invoices preview header"}, {"$": {"typename": "GENINVOICEPREVIEWLINE"}, "$value": "Generate invoices preview line"}, {"$": {"typename": "GENINVOICERUN"}, "$value": "Generate invoices run"}, {"$": {"typename": "GENINVOICEPOLICY"}, "$value": "Invoice policy"}, {"$": {"typename": "CONTRACTREVENUETEMPLATEENTRY"}, "$value": "Contract revenue template entry"}, {"$": {"typename": "ICTRANSFER"}, "$value": "Warehouse transfer"}, {"$": {"typename": "ICTRANSFERITEM"}, "$value": "Warehouse transfer items"}, {"$": {"typename": "DOCUMENTENTRYTRACKDETAIL"}, "$value": "Document entry tracking details"}, {"$": {"typename": "SCITEMGLGROUP"}, "$value": "Scitemglgroup"}, {"$": {"typename": "SCPURCHASINGDOC"}, "$value": "Scpurchasingdoc"}, {"$": {"typename": "OBSPCTCOMPLETED"}, "$value": "Observed percent completed"}, {"$": {"typename": "USERRESTRICTION"}, "$value": "User restriction"}, {"$": {"typename": "COSTHISTORY"}, "$value": "Cost history"}, {"$": {"typename": "INVHLTHRUN"}, "$value": "Maintain inventory valuation"}, {"$": {"typename": "MEACATEGORY"}, "$value": "MEA fair value category"}, {"$": {"typename": "ADVAUDITHISTORY"}, "$value": "Advanced audit history"}, {"$": {"typename": "JOBQUEUERECORD"}, "$value": "Offline job queue"}, {"$": {"typename": "BANKACCTRECON"}, "$value": "Bank reconciliation"}, {"$": {"typename": "LANDEDCOSTHISTORY"}, "$value": "Landed cost history"}, {"$": {"typename": "REPLENISHMENT"}, "$value": "Replenishment report"}, {"$": {"typename": "COGSCLOSEDJE"}, "$value": "COGS closed JE"}, {"$": {"typename": "GLACCTALLOCATION"}, "$value": "GL account allocation"}, {"$": {"typename": "GLACCTALLOCATIONSOURCE"}, "$value": "GL account allocation source"}, {"$": {"typename": "GLACCTALLOCATIONBASIS"}, "$value": "GL account allocation basis"}, {"$": {"typename": "GLACCTALLOCATIONTARGET"}, "$value": "GL account allocation target"}, {"$": {"typename": "GLACCTALLOCATIONREVERSE"}, "$value": "GL account allocation reverse"}, {"$": {"typename": "GLACCTALLOCATIONRUN"}, "$value": "Allocation log"}, {"$": {"typename": "GLACCTALLOCATIONGRP"}, "$value": "Account allocation group"}, {"$": {"typename": "GLACCTALLOCATIONGRPMEMBER"}, "$value": "Account allocation group member"}, {"$": {"typename": "GLACCTALLOCATIONSOURCEADJBOOKS"}, "$value": "GL account allocation source adjustment book"}, {"$": {"typename": "GLACCTALLOCATIONBASISADJBOOKS"}, "$value": "GL account allocation basis adjustment books"}, {"$": {"typename": "JOURNALSEQNUM"}, "$value": "Accounting sequence"}, {"$": {"typename": "JOURNALSEQNUMENTRY"}, "$value": "Accounting sequence number entry"}, {"$": {"typename": "COSTTYPE"}, "$value": "Cost type"}, {"$": {"typename": "COSTTYPEGROUP"}, "$value": "Cost type group"}, {"$": {"typename": "COSTTYPEGRPMEMBER"}, "$value": "Cost type group members"}, {"$": {"typename": "COSTTYPENGROUPPICK"}, "$value": "Cost type or cost type group"}, {"$": {"typename": "COSTTYPEPICK"}, "$value": "Cost type"}, {"$": {"typename": "STANDARDCOSTTYPE"}, "$value": "Standard cost type"}, {"$": {"typename": "ACCUMULATIONTYPE"}, "$value": "Accumulation type"}, {"$": {"typename": "STANDARDTASK"}, "$value": "Standard task"}, {"$": {"typename": "REPLENISHFORECAST"}, "$value": "Replenishment forecast table"}, {"$": {"typename": "BANKACCTTXNFEED"}, "$value": "Bank account transaction feed"}, {"$": {"typename": "BANKACCTTXNRECORD"}, "$value": "Bank account transaction feed records"}, {"$": {"typename": "TASKGROUP"}, "$value": "Task group"}, {"$": {"typename": "GCBOOK"}, "$value": "IGC book"}, {"$": {"typename": "GCBOOKENTITY"}, "$value": "Global Consolidations book entities"}, {"$": {"typename": "GCBOOKELIMACCOUNT"}, "$value": "Global Consolidations book elimination accounts"}, {"$": {"typename": "GCBOOKACCTRATETYPE"}, "$value": "Global Consolidations book rate types"}, {"$": {"typename": "GCBOOKADJJOURNAL"}, "$value": "Global Consolidations adj book journals"}, {"$": {"typename": "PODOCUMENTLCESTENTRY"}, "$value": "Document estimate landed cost entry"}, {"$": {"typename": "TASKGRPMEMBER"}, "$value": "Task group members"}, {"$": {"typename": "TASKNGROUPPICK"}, "$value": "Task group"}, {"$": {"typename": "TASKPICK"}, "$value": "Task"}, {"$": {"typename": "PRODUCTIONUNITS"}, "$value": "Production units"}, {"$": {"typename": "PJESTIMATE"}, "$value": "Project estimate"}, {"$": {"typename": "PJESTIMATEENTRY"}, "$value": "Project estimate entry"}, {"$": {"typename": "PJESTIMATETYPE"}, "$value": "Estimate type"}, {"$": {"typename": "COSTCHANGEHISTORY"}, "$value": "Cost change history"}, {"$": {"typename": "RECURGLACCTALLOCATION"}, "$value": "Recurring GL account allocation"}, {"$": {"typename": "CONTRACTTYPE"}, "$value": "Contract type"}, {"$": {"typename": "REPLENISHFORECASTDETAIL"}, "$value": "Replenishment forecast detail table"}, {"$": {"typename": "ARPYMT"}, "$value": "AR receivables payment"}, {"$": {"typename": "ARPYMTDETAIL"}, "$value": "AR receivables payment details"}, {"$": {"typename": "ARPYMTENTRY"}, "$value": "AR receivables payment line detail"}, {"$": {"typename": "CONTRACTRSLVADDLDATA"}, "$value": "Contract resolve additional data"}, {"$": {"typename": "CONTRACTACPRUN"}, "$value": "Process contract schedules"}, {"$": {"typename": "GLACCTGRPPURPOSE"}, "$value": "Account group purpose"}, {"$": {"typename": "INVENTORYTOTALDETAIL"}, "$value": "Inventory total detail"}, {"$": {"typename": "APPOSTEDADVANCEENTRY"}, "$value": "AP posted advance line detail"}, {"$": {"typename": "BANKACCTRECONRECORD"}, "$value": "Bank reconciliation records"}, {"$": {"typename": "BUYTOORDERHISTORY"}, "$value": "Buy-to-order history"}, {"$": {"typename": "APRETAINAGERELEASE"}, "$value": "AP retainage release"}, {"$": {"typename": "APRETAINAGERELEASEENTRY"}, "$value": "AP retainage release entry"}, {"$": {"typename": "ARRETAINAGERELEASE"}, "$value": "AR retainage release"}, {"$": {"typename": "ARRETAINAGERELEASEENTRY"}, "$value": "AR retainage release entry"}, {"$": {"typename": "ARRELEASEABLERECORD"}, "$value": "AR releasable retainage record"}, {"$": {"typename": "APRELEASEABLERECORD"}, "$value": "AP releasable retainage record"}, {"$": {"typename": "CONTRACTACPRUNENTRY"}, "$value": "Contract schedules processing results entry"}, {"$": {"typename": "ZONE"}, "$value": "Zone"}, {"$": {"typename": "BINSIZE"}, "$value": "Bin size"}, {"$": {"typename": "BINFACE"}, "$value": "Bin face"}, {"$": {"typename": "GLREPORTTYPE"}, "$value": "Report type"}, {"$": {"typename": "GLREPORTAUDIENCE"}, "$value": "Report audience"}, {"$": {"typename": "ICCYCLECOUNT"}, "$value": "Cycle counts"}, {"$": {"typename": "ICCYCLECOUNTENTRY"}, "$value": "Cycle counts entry"}, {"$": {"typename": "PTAPPLICATION"}, "$value": "Platform application"}, {"$": {"typename": "CREDITACCTRECON"}, "$value": "Credit card reconciliation"}, {"$": {"typename": "CREDITACCTRECONRECORD"}, "$value": "Credit reconciliation records"}, {"$": {"typename": "IETRECONCILIATIONS"}, "$value": "Inter-entity transactions"}, {"$": {"typename": "FINANCIALINSTITUTION"}, "$value": "Financial institution"}, {"$": {"typename": "AVAILABLEINVENTORY"}, "$value": "Available inventory"}, {"$": {"typename": "CHANGEREQUESTTYPE"}, "$value": "Change request type"}, {"$": {"typename": "CHANGEREQUESTSTATUS"}, "$value": "Change request status"}, {"$": {"typename": "CHANGEREQUEST"}, "$value": "Change request"}, {"$": {"typename": "CHANGEREQUESTENTRY"}, "$value": "Change request entry"}, {"$": {"typename": "PRGLPOSTING"}, "$value": "Link PR and GL"}, {"$": {"typename": "GLIETPOSTING"}, "$value": "Link PR and GL"}, {"$": {"typename": "PROVIDERPAYMENTMETHOD"}, "$value": "Provider payment method"}, {"$": {"typename": "PROVIDERBANKACCOUNT"}, "$value": "Provider bank account"}, {"$": {"typename": "PROVIDERVENDOR"}, "$value": "Provider vendor"}, {"$": {"typename": "PROJECTCHANGEORDER"}, "$value": "Project change order"}, {"$": {"typename": "DEGLPOSTING"}, "$value": "Link doc entry and GL entry"}, {"$": {"typename": "DEGLSUBTOTALPOSTING"}, "$value": "Link doc entry subtotal and GL entry"}, {"$": {"typename": "PRIORPERIODCOGSPOSTING"}, "$value": "Link prior periods COGS adjustments and GL entry"}, {"$": {"typename": "INVENTORYWORKQUEUE"}, "$value": "Work queue"}, {"$": {"typename": "BANKTXNRULE"}, "$value": "Bank transaction rule"}, {"$": {"typename": "BANKTXNRULEATTR"}, "$value": "Bank transaction rule attribute"}, {"$": {"typename": "BANKTXNRULESET"}, "$value": "Bank transaction rule set"}, {"$": {"typename": "BANKTXNRULEMAP"}, "$value": "Bank transaction rule and rule set map"}, {"$": {"typename": "BANKTXNRULERUN"}, "$value": "Rule run"}, {"$": {"typename": "VENDOREMAILTEMPLATE"}, "$value": "Vendor email template"}, {"$": {"typename": "RUNOBJECTSUMMARY"}, "$value": "Run object summary"}, {"$": {"typename": "INVENTORYWQORDER"}, "$value": "Orders in fulfillment"}, {"$": {"typename": "CONTRACTMEAINSTRUCTION"}, "$value": "Contract MEA instruction"}, {"$": {"typename": "CONTRACTMEAINSTPART"}, "$value": "Contract MEA instruction part"}, {"$": {"typename": "GCOWNERSHIPSTRUCTURE"}, "$value": "Ownership structure"}, {"$": {"typename": "GCOWNERSHIPENTITY"}, "$value": "Ownership structure entity"}, {"$": {"typename": "GCOWNERSHIPCHILDENTITY"}, "$value": "Ownership structure child entity"}, {"$": {"typename": "GCOWNERSHIPSTRUCTUREDETAIL"}, "$value": "Ownership structure detail"}, {"$": {"typename": "EMPLOYEEPOSITION"}, "$value": "Employee position"}, {"$": {"typename": "LABORCLASS"}, "$value": "Labor channel"}, {"$": {"typename": "LABORSHIFT"}, "$value": "Labor shift"}, {"$": {"typename": "LABORUNION"}, "$value": "Labor union"}, {"$": {"typename": "RATETABLE"}, "$value": "Project contract rate table"}, {"$": {"typename": "RATETABLEAPENTRY"}, "$value": "Project contract rate table AP entry"}, {"$": {"typename": "RATETABLEGLENTRY"}, "$value": "Project contract rate table GL entry"}, {"$": {"typename": "RATETABLEEXPENSEENTRY"}, "$value": "Project contract rate table employee expense entry"}, {"$": {"typename": "RATETABLECCENTRY"}, "$value": "Project contract rate table credit card entry"}, {"$": {"typename": "RATETABLEPOENTRY"}, "$value": "Project contract rate table PO entry"}, {"$": {"typename": "BANKTXNRULEFILTERATTR"}, "$value": "Bank transaction rule filter attribute"}, {"$": {"typename": "BANKTXNRULEGROUPATTR"}, "$value": "Bank transaction rule group attribute"}, {"$": {"typename": "BANKTXNRULEMATCHATTR"}, "$value": "Bank transaction rule match attribute"}, {"$": {"typename": "AROPENSUMMARY"}, "$value": "AR summary"}, {"$": {"typename": "ARCLOSESUMMARY"}, "$value": "AR summary"}, {"$": {"typename": "APOPENSUMMARY"}, "$value": "AP summary"}, {"$": {"typename": "APCLOSESUMMARY"}, "$value": "AP summary"}, {"$": {"typename": "PROJECTCONTRACTTYPE"}, "$value": "Project contract type"}, {"$": {"typename": "PROJECTCONTRACT"}, "$value": "Project contract"}, {"$": {"typename": "PROJECTCONTRACTLINE"}, "$value": "Project contract line"}, {"$": {"typename": "PROJECTCONTRACTLINEENTRY"}, "$value": "Project contract line entry"}, {"$": {"typename": "PCLTASK"}, "$value": "Project contract line task"}, {"$": {"typename": "GLRESTRICTIONRELEASE"}, "$value": "GL restriction release"}, {"$": {"typename": "INVENTORYWQDETAIL"}, "$value": "Lines in fulfillment"}, {"$": {"typename": "RATETABLETSENTRY"}, "$value": "Project contract rate table timesheet entry"}, {"$": {"typename": "EVERGREENMACRO"}, "$value": "Evergreen template"}, {"$": {"typename": "SODOCCONVERTTO"}, "$value": "Sodocconvertto"}, {"$": {"typename": "SAASCHANGETYPE"}, "$value": "SaaS metrics change type"}, {"$": {"typename": "SAASSCHEDULE"}, "$value": "SaaS Metrics schedule"}, {"$": {"typename": "CONTRACTSCHEDULESRESOLVE"}, "$value": "Contract schedule entry resolve"}, {"$": {"typename": "CMRULEGLBATCHTMPL"}, "$value": "Transaction template for journal entry"}, {"$": {"typename": "GIRUNSUMMARY"}, "$value": "Run object summary"}, {"$": {"typename": "DUNNINGDEFINITION"}, "$value": "Dunning level"}, {"$": {"typename": "DUNNINGCUSTOMER"}, "$value": "Dunning customer"}, {"$": {"typename": "DUNNINGINVOICE"}, "$value": "Dunning invoice"}, {"$": {"typename": "DUNNINGNOTICE"}, "$value": "Dunning notice"}, {"$": {"typename": "CMRULECCTXNTMPL"}, "$value": "Transaction template for credit card transaction"}, {"$": {"typename": "SAASSCHEDULEENTRY"}, "$value": "SaaS metrics schedule entry"}, {"$": {"typename": "VENDORBANKFILEDETAIL"}, "$value": "Vendor bank file detail"}, {"$": {"typename": "DEPRSCHRUNSUMMARY"}, "$value": "Run object summary"}, {"$": {"typename": "FILE1099"}, "$value": "1099 e-file submissions"}, {"$": {"typename": "FILE1099SUBMISSIONLOG"}, "$value": "1099 e-file submission status"}, {"$": {"typename": "PAYROLLREPORTTIMECARD"}, "$value": "Payroll report time card"}, {"$": {"typename": "PAYROLLREPORTPTOACTIVITY"}, "$value": "Payroll report PTO activity"}, {"$": {"typename": "PAYROLLREPORTTAX"}, "$value": "Payroll report tax"}, {"$": {"typename": "PAYROLLREPORTCHECK"}, "$value": "Payroll report check"}, {"$": {"typename": "PAYROLLREPORTPAYMODIFIER"}, "$value": "Payroll report pay modifier"}, {"$": {"typename": "PAYROLLREPORTGROSSPAY"}, "$value": "Payroll report gross pay"}, {"$": {"typename": "OAUTHUSER"}, "$value": "OAuth user"}, {"$": {"typename": "CMRULECCTXNENTRYTMPL"}, "$value": "CM rule CC txn entry template"}, {"$": {"typename": "ARPOSTEDOVERPAYMENT"}, "$value": "AR posted overpayment"}, {"$": {"typename": "ARPOSTEDOVERPAYMENTENTRY"}, "$value": "AR posted overpayment line detail"}, {"$": {"typename": "IAFORM1099TYPE"}, "$value": "Form 1099 type"}, {"$": {"typename": "IAFORM1099BOX"}, "$value": "Form 1099 box"}, {"$": {"typename": "IATPARTYPE"}, "$value": "TPAR type"}, {"$": {"typename": "IATPARBOX"}, "$value": "TPAR box"}, {"$": {"typename": "VENDORTPARINITIALBALANCE"}, "$value": "Vendor TPAR initial balance"}, {"$": {"typename": "COMPLIANCETYPE"}, "$value": "Compliance type"}, {"$": {"typename": "COMPLIANCEDEFINITION"}, "$value": "Compliance definition"}, {"$": {"typename": "COMPLIANCERECORD"}, "$value": "Compliance record"}, {"$": {"typename": "GENERATERECEIPT"}, "$value": "Receive payments from bank transactions"}, {"$": {"typename": "ASSIGNENTITYTOBANKTRANSACTION"}, "$value": "Assignentitytobanktransaction"}, {"$": {"typename": "STATUTORYREPORTINGPERIOD"}, "$value": "Lock closed period"}, {"$": {"typename": "EMPLOYEEBANKFILEDETAIL"}, "$value": "Employee bank file detail"}, {"$": {"typename": "COMPLIANCEDEFASSOCIATIONS"}, "$value": "Compliance definition association"}, {"$": {"typename": "CMRULEARADVANCETMPL"}, "$value": "Transaction template for AR advance"}, {"$": {"typename": "CMRULEARADVANCENTRYTMPL"}, "$value": "AR advance template entry"}, {"$": {"typename": "PAYROLLREPORTTAXSETUP"}, "$value": "Payroll report tax setup"}, {"$": {"typename": "PCBINVSUMMARY"}, "$value": "Project contract billing invoice summary"}, {"$": {"typename": "PCBINVDETAIL"}, "$value": "Project contract billing invoice detail"}, {"$": {"typename": "BANKTXNASSIGNRULE"}, "$value": "Bank transaction assignment rule"}, {"$": {"typename": "BANKTXNASSIGNRULEATTR"}, "$value": "Bank transaction assignment rule attribute"}, {"$": {"typename": "COMPLIANCERECORDDETAIL"}, "$value": "Compliance record detail"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adj Increase Machine Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adj Increase Material Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adjustment Decrease Qty"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adjustment Decrease Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adjustment Decrease Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adjustment Increase Qty"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adjustment Increase Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adjustment Increase Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Beginning Balance"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inv Damaged Goods Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inv Scrap Spoilage Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inv Transfer In Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inv Transfer Out Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inventory Issue Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inventory Receipt Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inventory Shipper Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "SYS-LC Actuals Adj Incr"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "SYS-LC Estimates Adj Incr"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "SYS-LC Estimates Rev Adj <PERSON>r"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "SYS-Warehouse Transfer In"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "SYS-Warehouse Transfer Out"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adj Increase Machine Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adj Increase Material Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adjustment Decrease Qty Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adjustment Decrease Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adjustment Decrease Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adjustment Increase Qty Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adjustment Increase Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adjustment Increase Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Beginning Balance Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inv Damaged Goods Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inv Scrap Spoilage Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inv Transfer In Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inv Transfer Out Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inventory Issue Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inventory Receipt Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inventory Shipper Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "SYS-LC Actuals Adj Incr Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "SYS-LC Estimates Adj Incr Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "SYS-LC Estimates Rev Adj <PERSON>r Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "SYS-Warehouse Transfer In Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "SYS-Warehouse Transfer Out Detail"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Credit Memo"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Invoice"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Invoice - Adv tax 100"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Invoice - Advanced tax"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Invoice - Simple tax"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Invoice-Inventory"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Order"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Order-Inventory"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales quote"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Return-Inventory"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Return-Inventory ship"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Shipper-Inventory"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Shipper-Inventory stock"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Credit Memo Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Invoice Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Invoice - Adv tax 100 Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Invoice - Advanced tax Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Invoice - Simple tax Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Invoice-Inventory Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Order Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Order-Inventory Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales quote Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Return-Inventory Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Return-Inventory ship Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Shipper-Inventory Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Shipper-Inventory stock Detail"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Fulfillment"}, {"$": {"typename": "PODOCUMENT"}, "$value": "PO Receiver-Inventory"}, {"$": {"typename": "PODOCUMENT"}, "$value": "PO Receiver-Inventory XT"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Purchase Order"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Purchase Order AM"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Purchase Order-Inventory"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Purchase Order-Inventory XT"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Purchase Requisition"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Purchasing Debit Memo"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Return"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Return_Inventory"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Vendor Invoice"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Vendor Invoice - simple tax"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Vendor Invoice-Inventory"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Fulfillment Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "PO Receiver-Inventory Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "PO Receiver-Inventory XT Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Purchase Order Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Purchase Order AM Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Purchase Order-Inventory Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Purchase Order-Inventory XT Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Purchase Requisition Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Purchasing Debit Memo Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Return Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Return_Inventory Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Vendor Invoice Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Vendor Invoice - simple tax Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Vendor Invoice-Inventory Detail"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Credit Memo"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Invoice"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Invoice - Adv tax 100"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Invoice - Advanced tax"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Invoice - Simple tax"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Invoice-Inventory"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Order"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Order-Inventory"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales quote"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Return-Inventory"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Return-Inventory ship"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Shipper-Inventory"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Shipper-Inventory stock"}]}