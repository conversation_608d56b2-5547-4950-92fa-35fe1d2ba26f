{"_status": "success", "_functionName": "lookup", "_controlId": "2f86cab4-f3fd-4665-bc04-0de6bbbb44da", "_listType": "All", "_count": 1, "_data": [{"$": {"Name": "SODOCUMENT", "DocumentType": ""}, "Fields": {"Field": [{"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "Record Number", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DOCNO", "LABEL": "Document number", "DESCRIPTION": "Document Number", "REQUIRED": "true", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DOCID", "LABEL": "Document ID", "DESCRIPTION": "Document ID", "REQUIRED": "true", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CREATEDFROM", "LABEL": "Converted from", "DESCRIPTION": "Converted From", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "STATE", "LABEL": "State", "DESCRIPTION": "State", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Submitted", "Approved", "Partially Approved", "Declined", "Draft", "Pending", "Closed", "In Progress", "Converted", "Partially Converted", "Converted By Line", "Partially Converted By Line", "Exception"]}, "ISCUSTOM": "false"}, {"ID": "CLOSED", "LABEL": "Closed", "DESCRIPTION": "Closed", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "WHENCREATED", "LABEL": "Date", "DESCRIPTION": "Date created", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "AUWHENCREATED", "LABEL": "Audit when created", "DESCRIPTION": "Audit timestamp when record was created.", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TIMESTAMP", "ISCUSTOM": "false"}, {"ID": "CREATEDBY", "LABEL": "Created by", "DESCRIPTION": "User who created this.", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DESCRIPTION": "User who modified this.", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "WHENMODIFIED", "LABEL": "Modified date", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TIMESTAMP", "ISCUSTOM": "false"}, {"ID": "WHENDUE", "LABEL": "Date due", "DESCRIPTION": "Date Due", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Inactive", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "inactive"]}, "ISCUSTOM": "false"}, {"ID": "PONUMBER", "LABEL": "Reference number", "DESCRIPTION": "PO Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "VENDORDOCNO", "LABEL": "Vendor document number", "DESCRIPTION": "Vendor Document Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DOCPARID", "LABEL": "Type", "DESCRIPTION": "Document Template ID", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DOCPARKEY", "LABEL": "Transaction defintion key", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DOCPARCLASS", "LABEL": "Transaction definition template class", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "UPDATES_INV", "LABEL": "Affects Inventory", "DESCRIPTION": "Affects Inventory", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["No", "Quantity", "Value", "Quantity and Value"]}, "ISCUSTOM": "false"}, {"ID": "TERM.NAME", "LABEL": "Payment terms", "DESCRIPTION": "Payment Terms", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "NOTE", "LABEL": "Note", "DESCRIPTION": "Note", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "WAREHOUSE.LOCATIONID", "LABEL": "De<PERSON>ult warehouse", "DESCRIPTION": "Default site", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPVIA", "LABEL": "Ship via", "DESCRIPTION": "Shipping Method", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "USER", "LABEL": "User", "DESCRIPTION": "User", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CREATEDUSER", "LABEL": "created<PERSON>ser", "DESCRIPTION": "created<PERSON>ser", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "USERID", "LABEL": "Modified by", "DESCRIPTION": "Modified By", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CREATEDUSERID", "LABEL": "Created by", "DESCRIPTION": "Created By", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.CONTACTNAME", "LABEL": "Customer", "DESCRIPTION": "Customer", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.PREFIX", "LABEL": "Contact - Prefix", "DESCRIPTION": "Mr./Ms./Mrs.", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.FIRSTNAME", "LABEL": "Contact - first name", "DESCRIPTION": "First Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.INITIAL", "LABEL": "Contact - MI", "DESCRIPTION": "Middle Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.LASTNAME", "LABEL": "Contact - last name", "DESCRIPTION": "Last Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.COMPANYNAME", "LABEL": "Contact - company name", "DESCRIPTION": "Full name of the company", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.PRINTAS", "LABEL": "Contact - print s", "DESCRIPTION": "Name as appears on official documents", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.PHONE1", "LABEL": "Contact - phone 1", "DESCRIPTION": "Primary phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.PHONE2", "LABEL": "Contact - phone 2", "DESCRIPTION": "Seconday phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.CELLPHONE", "LABEL": "Contact - mobile", "DESCRIPTION": "Cellular Phone Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.PAGER", "LABEL": "Contact - pager", "DESCRIPTION": "Pager", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.FAX", "LABEL": "Contact - fax", "DESCRIPTION": "Fax", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.EMAIL1", "LABEL": "Contact - email 1", "DESCRIPTION": "Primary email address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.EMAIL2", "LABEL": "Contact - email 2", "DESCRIPTION": "Secondary Email Address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.URL1", "LABEL": "Contact - URL 1", "DESCRIPTION": "Primary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.URL2", "LABEL": "Contact - URL 2", "DESCRIPTION": "Secondary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.VISIBLE", "LABEL": "Visible", "DESCRIPTION": "Visible", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "CONTACT.MAILADDRESS.ADDRESS1", "LABEL": "Contact address - addr1", "DESCRIPTION": "Address Line 1", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.MAILADDRESS.ADDRESS2", "LABEL": "Contact address - addr2", "DESCRIPTION": "Address Line 2", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.MAILADDRESS.CITY", "LABEL": "Contact address - city", "DESCRIPTION": "City", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.MAILADDRESS.STATE", "LABEL": "Contact address - State/Territory", "DESCRIPTION": "State/Province", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.MAILADDRESS.ZIP", "LABEL": "Contact address - Zip code/Post code", "DESCRIPTION": "Zip/Postal Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.MAILADDRESS.COUNTRY", "LABEL": "Contact address - country", "DESCRIPTION": "Country", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACT.MAILADDRESS.COUNTRYCODE", "LABEL": "Contact address - country code", "DESCRIPTION": "Country Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["US", "AF", "AX", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG", "AR", "AM", "AW", "AU", "AT", "AZ", "BS", "BH", "BD", "BB", "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BQ", "BA", "BW", "BV", "BR", "IO", "BN", "BG", "BF", "BI", "KH", "CM", "CA", "CV", "KY", "CF", "TD", "CL", "CN", "CX", "CC", "CO", "KM", "CG", "CD", "CK", "CR", "CI", "HR", "CU", "CW", "CY", "CZ", "DK", "DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE", "SZ", "ET", "FK", "FO", "FJ", "FI", "FR", "GF", "PF", "TF", "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP", "GU", "GT", "GG", "GN", "GW", "GY", "HT", "HM", "HN", "HK", "HU", "IS", "IN", "ID", "IR", "IQ", "IE", "IM", "IL", "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI", "KR", "KP", "XK", "KW", "KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT", "LU", "MO", "MK", "MG", "MW", "MY", "MV", "ML", "MT", "MH", "MQ", "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "ME", "MS", "MA", "MZ", "MM", "NA", "NR", "NP", "NL", "AN", "NC", "NZ", "NI", "NE", "NG", "NU", "NF", "MP", "NO", "OM", "PK", "PW", "PS", "PA", "PG", "PY", "PE", "PH", "PN", "PL", "PT", "PR", "QA", "RE", "RO", "RU", "RW", "BL", "SH", "KN", "LC", "MF", "PM", "VC", "WS", "SM", "ST", "SA", "SN", "RS", "SC", "SL", "SG", "SX", "SK", "SI", "SB", "SO", "ZA", "GS", "ES", "LK", "SD", "SS", "SR", "SJ", "SE", "CH", "SY", "TW", "TJ", "TZ", "TH", "TL", "TG", "TK", "TO", "TT", "TN", "TR", "TM", "TC", "TV", "UG", "UA", "AE", "GB", "UM", "UY", "UZ", "VU", "VA", "VE", "VN", "VG", "VI", "WF", "EH", "YE", "ZM", "ZW"]}, "ISCUSTOM": "false"}, {"ID": "SHIPTOKEY", "LABEL": "Ship-to contact key", "DESCRIPTION": "Ship-to contact key", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.CONTACTNAME", "LABEL": "Ship-to contact name", "DESCRIPTION": "Ship to contact name", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.PREFIX", "LABEL": "Ship-to contact - prefix", "DESCRIPTION": "Mr./Ms./Mrs.", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.FIRSTNAME", "LABEL": "Ship-to contact - first name", "DESCRIPTION": "First Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.INITIAL", "LABEL": "Ship-to contact - MI", "DESCRIPTION": "Middle Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.LASTNAME", "LABEL": "Ship-to contact - last name", "DESCRIPTION": "Last Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.COMPANYNAME", "LABEL": "Ship-to contact - company name", "DESCRIPTION": "Full name of the company", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.PRINTAS", "LABEL": "Ship-to contact - print as", "DESCRIPTION": "Name as appears on official documents", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.PHONE1", "LABEL": "Ship-to contact - phone 1", "DESCRIPTION": "Primary phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.PHONE2", "LABEL": "Ship-to contact - phone 2", "DESCRIPTION": "Seconday phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.CELLPHONE", "LABEL": "Ship-to contact - mobile", "DESCRIPTION": "Cellular Phone Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.PAGER", "LABEL": "Ship-to contact - pager", "DESCRIPTION": "Pager", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.FAX", "LABEL": "Ship-to contact - fax", "DESCRIPTION": "Fax", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.EMAIL1", "LABEL": "Ship-to contact - email 1", "DESCRIPTION": "Primary email address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.EMAIL2", "LABEL": "Ship-to contact - email 2", "DESCRIPTION": "Secondary Email Address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.URL1", "LABEL": "Ship-to contact - URL 1", "DESCRIPTION": "Primary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.URL2", "LABEL": "Ship-to contact - URL 2", "DESCRIPTION": "Secondary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.VISIBLE", "LABEL": "Visible", "DESCRIPTION": "Visible", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "SHIPTO.MAILADDRESS.ADDRESS1", "LABEL": "Ship-to contact address - addr1", "DESCRIPTION": "Address Line 1", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.MAILADDRESS.ADDRESS2", "LABEL": "Ship-to contact address - addr2", "DESCRIPTION": "Address Line 2", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.MAILADDRESS.CITY", "LABEL": "Ship-to contact address - city", "DESCRIPTION": "City", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.MAILADDRESS.STATE", "LABEL": "Ship-to contact address - State/Territory", "DESCRIPTION": "State/Province", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.MAILADDRESS.ZIP", "LABEL": "Ship-to contact address - Zip code/Post code", "DESCRIPTION": "Zip/Postal Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.MAILADDRESS.COUNTRY", "LABEL": "Ship-to contact address - country", "DESCRIPTION": "Country", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPTO.MAILADDRESS.COUNTRYCODE", "LABEL": "Ship-to contact address - country code", "DESCRIPTION": "Country Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["US", "AF", "AX", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG", "AR", "AM", "AW", "AU", "AT", "AZ", "BS", "BH", "BD", "BB", "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BQ", "BA", "BW", "BV", "BR", "IO", "BN", "BG", "BF", "BI", "KH", "CM", "CA", "CV", "KY", "CF", "TD", "CL", "CN", "CX", "CC", "CO", "KM", "CG", "CD", "CK", "CR", "CI", "HR", "CU", "CW", "CY", "CZ", "DK", "DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE", "SZ", "ET", "FK", "FO", "FJ", "FI", "FR", "GF", "PF", "TF", "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP", "GU", "GT", "GG", "GN", "GW", "GY", "HT", "HM", "HN", "HK", "HU", "IS", "IN", "ID", "IR", "IQ", "IE", "IM", "IL", "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI", "KR", "KP", "XK", "KW", "KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT", "LU", "MO", "MK", "MG", "MW", "MY", "MV", "ML", "MT", "MH", "MQ", "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "ME", "MS", "MA", "MZ", "MM", "NA", "NR", "NP", "NL", "AN", "NC", "NZ", "NI", "NE", "NG", "NU", "NF", "MP", "NO", "OM", "PK", "PW", "PS", "PA", "PG", "PY", "PE", "PH", "PN", "PL", "PT", "PR", "QA", "RE", "RO", "RU", "RW", "BL", "SH", "KN", "LC", "MF", "PM", "VC", "WS", "SM", "ST", "SA", "SN", "RS", "SC", "SL", "SG", "SX", "SK", "SI", "SB", "SO", "ZA", "GS", "ES", "LK", "SD", "SS", "SR", "SJ", "SE", "CH", "SY", "TW", "TJ", "TZ", "TH", "TL", "TG", "TK", "TO", "TT", "TN", "TR", "TM", "TC", "TV", "UG", "UA", "AE", "GB", "UM", "UY", "UZ", "VU", "VA", "VE", "VN", "VG", "VI", "WF", "EH", "YE", "ZM", "ZW"]}, "ISCUSTOM": "false"}, {"ID": "BILLTOKEY", "LABEL": "Bill-to contact key", "DESCRIPTION": "Bill-to contact key", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.CONTACTNAME", "LABEL": "Bill-to contact name", "DESCRIPTION": "Bill to contact Contact", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.PREFIX", "LABEL": "Bill-to contact - prefix", "DESCRIPTION": "Mr./Ms./Mrs.", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.FIRSTNAME", "LABEL": "Bill-to contact - first name", "DESCRIPTION": "First Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.INITIAL", "LABEL": "Bill-to contact - MI", "DESCRIPTION": "Middle Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.LASTNAME", "LABEL": "Bill-to contact - last name", "DESCRIPTION": "Last Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.COMPANYNAME", "LABEL": "Bill-to contact - company name", "DESCRIPTION": "Full name of the company", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.PRINTAS", "LABEL": "Bill-to contact - print as", "DESCRIPTION": "Name as appears on official documents", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.PHONE1", "LABEL": "Bill-to contact - phone 1", "DESCRIPTION": "Primary phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.PHONE2", "LABEL": "Bill-to contact - phone 2", "DESCRIPTION": "Seconday phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.CELLPHONE", "LABEL": "Bill-to contact - mobile", "DESCRIPTION": "Cellular Phone Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.PAGER", "LABEL": "Bill-to contact - pager", "DESCRIPTION": "Pager", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.FAX", "LABEL": "Bill-to contact - fax", "DESCRIPTION": "Fax", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.EMAIL1", "LABEL": "Bill-to contact - email 1", "DESCRIPTION": "Primary email address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.EMAIL2", "LABEL": "Bill-to contact - email 2", "DESCRIPTION": "Secondary Email Address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.URL1", "LABEL": "Bill-to contact - URL 1", "DESCRIPTION": "Primary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.URL2", "LABEL": "Bill-to contact - URL 2", "DESCRIPTION": "Secondary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.VISIBLE", "LABEL": "Visible", "DESCRIPTION": "Visible", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "BILLTO.MAILADDRESS.ADDRESS1", "LABEL": "Bill-to contact address - addr1", "DESCRIPTION": "Address Line 1", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.MAILADDRESS.ADDRESS2", "LABEL": "Bill-to contact address - addr2", "DESCRIPTION": "Address Line 2", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.MAILADDRESS.CITY", "LABEL": "Bill-to contact address - city", "DESCRIPTION": "City", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.MAILADDRESS.STATE", "LABEL": "Bill-to contact address - State/Territory", "DESCRIPTION": "State/Province", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.MAILADDRESS.ZIP", "LABEL": "Bill-to contact address - Zip code/Post code", "DESCRIPTION": "Zip/Postal Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.MAILADDRESS.COUNTRY", "LABEL": "Bill-to contact address - country", "DESCRIPTION": "Country", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BILLTO.MAILADDRESS.COUNTRYCODE", "LABEL": "Bill-to contact address - country code", "DESCRIPTION": "Country Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["US", "AF", "AX", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG", "AR", "AM", "AW", "AU", "AT", "AZ", "BS", "BH", "BD", "BB", "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BQ", "BA", "BW", "BV", "BR", "IO", "BN", "BG", "BF", "BI", "KH", "CM", "CA", "CV", "KY", "CF", "TD", "CL", "CN", "CX", "CC", "CO", "KM", "CG", "CD", "CK", "CR", "CI", "HR", "CU", "CW", "CY", "CZ", "DK", "DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE", "SZ", "ET", "FK", "FO", "FJ", "FI", "FR", "GF", "PF", "TF", "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP", "GU", "GT", "GG", "GN", "GW", "GY", "HT", "HM", "HN", "HK", "HU", "IS", "IN", "ID", "IR", "IQ", "IE", "IM", "IL", "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI", "KR", "KP", "XK", "KW", "KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT", "LU", "MO", "MK", "MG", "MW", "MY", "MV", "ML", "MT", "MH", "MQ", "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "ME", "MS", "MA", "MZ", "MM", "NA", "NR", "NP", "NL", "AN", "NC", "NZ", "NI", "NE", "NG", "NU", "NF", "MP", "NO", "OM", "PK", "PW", "PS", "PA", "PG", "PY", "PE", "PH", "PN", "PL", "PT", "PR", "QA", "RE", "RO", "RU", "RW", "BL", "SH", "KN", "LC", "MF", "PM", "VC", "WS", "SM", "ST", "SA", "SN", "RS", "SC", "SL", "SG", "SX", "SK", "SI", "SB", "SO", "ZA", "GS", "ES", "LK", "SD", "SS", "SR", "SJ", "SE", "CH", "SY", "TW", "TJ", "TZ", "TH", "TL", "TG", "TK", "TO", "TT", "TN", "TR", "TM", "TC", "TV", "UG", "UA", "AE", "GB", "UM", "UY", "UZ", "VU", "VA", "VE", "VN", "VG", "VI", "WF", "EH", "YE", "ZM", "ZW"]}, "ISCUSTOM": "false"}, {"ID": "MESSAGE", "LABEL": "Message", "DESCRIPTION": "Ms<PERSON><PERSON>", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PRRECORDKEY", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "INVBATCHKEY", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PRINVBATCHKEY", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "ADDGLBATCHKEY", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PRINTED", "LABEL": "Printed", "DESCRIPTION": "Document Printed", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Yes", "No"]}, "ISCUSTOM": "false"}, {"ID": "BACKORDER", "LABEL": "Back order?", "DESCRIPTION": "Back Order", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Yes", "No"]}, "ISCUSTOM": "false"}, {"ID": "SUBTOTAL", "LABEL": "Subtotal", "DESCRIPTION": "Subtotal", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "TOTAL", "LABEL": "Total", "DESCRIPTION": "Total", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "ENTGLGROUP", "LABEL": "ENTGLGROUP", "DESCRIPTION": "ENTGLGROUP", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "CURRENCY", "LABEL": "Txn currency", "DESCRIPTION": "Defaulting <PERSON><PERSON><PERSON><PERSON>", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "EXCHRATEDATE", "LABEL": "Exchange rate date", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "EXCHRATETYPES.NAME", "LABEL": "Exchange rate type", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "EXCHRATE", "LABEL": "Exchange rate", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "SCHOPKEY", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SALESCONTRACT", "LABEL": "Sales contract", "DESCRIPTION": "Sales Contract", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "USEDASCONTRACT", "LABEL": "Used as contract", "DESCRIPTION": "Used as Contract", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "TRX_SUBTOTAL", "LABEL": "Transaction subtotal", "DESCRIPTION": "Transaction Subtotal", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "TRX_TOTAL", "LABEL": "Transaction total", "DESCRIPTION": "Transaction Total", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "EXCH_RATE_TYPE_ID", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RENEWEDDOC", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "BASECURR", "LABEL": "Base currency", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SYSTEMGENERATED", "LABEL": "Scheduler generated", "DESCRIPTION": "True/False", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "INVOICERUNKEY", "LABEL": "Invoicerunkey", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "DOCPAR_IN_OUT", "LABEL": "Increases/decreases inventory", "DESCRIPTION": "Increases/Decreases Inventory", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Increase", "Decrease"]}, "ISCUSTOM": "false"}, {"ID": "WHENPOSTED", "LABEL": "GL posting date", "DESCRIPTION": "GL Posting Date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "PRINTEDUSERID", "LABEL": "Last delivered by", "DESCRIPTION": "Last delivered by", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DATEPRINTED", "LABEL": "Last delivered date", "DESCRIPTION": "Last delivered date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TIMESTAMP", "ISCUSTOM": "false"}, {"ID": "PRINTEDBY", "LABEL": "Printed by", "DESCRIPTION": "Printed by", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "ADJ", "LABEL": "Adjustment", "DESCRIPTION": "Adjustment", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "TAXSOLUTIONID", "LABEL": "Tax solution", "DESCRIPTION": "Tax solution", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CUSTVENDID", "LABEL": "Customer ID", "DESCRIPTION": "Customer ID", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CUSTVENDNAME", "LABEL": "Customer name", "DESCRIPTION": "Customer Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CUSTVENDEMAILOPTIN", "LABEL": "Accepts emailed invoices", "DESCRIPTION": "Accepts emailed invoices", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "HASPOSTEDREVREC", "LABEL": "Has posted rev rec", "DESCRIPTION": "Has posted rev rec", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTRACTID", "LABEL": "Contract ID", "DESCRIPTION": "Contract ID", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTRACTDESC", "LABEL": "Contract description", "DESCRIPTION": "Contract", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "TRX_TOTALPAID", "LABEL": "Total paid", "DESCRIPTION": "Total Paid", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "TOTALPAID", "LABEL": "Base total paid", "DESCRIPTION": "(Includes Echange Rate Gain and Loss) Base Total Paid", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "TRX_TOTALENTERED", "LABEL": "Invoice amount", "DESCRIPTION": "Invoice Amount", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "TOTALENTERED", "LABEL": "Base invoice amount", "DESCRIPTION": "Base Invoice Amount", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "TRX_TOTALDUE", "LABEL": "Amount due", "DESCRIPTION": "Amount Due", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "TOTALDUE", "LABEL": "Base amount due", "DESCRIPTION": "Base Amount Due", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "PAYMENTSTATUS", "LABEL": "Payment status", "DESCRIPTION": "Payment Status", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SIGN_FLAG", "LABEL": "Sign flag", "DESCRIPTION": "Sign Flag", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "VSOEPRICELIST", "LABEL": "Fair value price list", "DESCRIPTION": "Fair Value Price Schedule", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "VSOEPRCLSTKEY", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "ORIGDOCDATE", "LABEL": "Original invoice date", "DESCRIPTION": "", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "HASADVBILLING", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "INVOICERUN_EXPENSEPRICEMAR<PERSON>UP", "LABEL": "Expense price % markup", "DESCRIPTION": "Expense Price % Markup", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "INVOICERUN_DESCRIPTION", "LABEL": "Description", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PROJECTKEY", "LABEL": "Project key", "DESCRIPTION": "Project Key", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "PROJECT", "LABEL": "Project", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PROJECTNAME", "LABEL": "Project name", "DESCRIPTION": "Project Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CNCONTRACTID", "LABEL": "Contract ID", "DESCRIPTION": "Contract ID", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CNCONTRACTNAME", "LABEL": "Contract name", "DESCRIPTION": "Contract Name", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "NEEDBYDATE", "LABEL": "Need by date", "DESCRIPTION": "Need by date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "SHIPBYDATE", "LABEL": "Ship by date", "DESCRIPTION": "Ship by date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "CANCELAFTERDATE", "LABEL": "Cancel after date", "DESCRIPTION": "Cancel after date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "DONOTSHIPBEFOREDATE", "LABEL": "Do not ship before date", "DESCRIPTION": "Do not ship before date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "DONOTSHIPAFTERDATE", "LABEL": "Do not ship after date", "DESCRIPTION": "Do not ship after date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "SERVICEDELIVERYDATE", "LABEL": "Service delivery date", "DESCRIPTION": "Service delivery date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "TRACKINGNUMBER", "LABEL": "Tracking number", "DESCRIPTION": "Tracking number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SHIPPEDDATE", "LABEL": "Shipped date", "DESCRIPTION": "Shipped date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "CUSTOMERPONUMBER", "LABEL": "Customer PO number", "DESCRIPTION": "Customer PO number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETAINAGEPERCENTAGE", "LABEL": "Default retainage percentage", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "PERCENT", "ISCUSTOM": "false"}, {"ID": "SCOPE", "LABEL": "<PERSON><PERSON>", "DESCRIPTION": "<PERSON><PERSON>", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INCLUSIONS", "LABEL": "Inclusions", "DESCRIPTION": "Inclusions", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "EXCLUSIONS", "LABEL": "Exclusions", "DESCRIPTION": "Exclusions", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "TERMS", "LABEL": "Terms", "DESCRIPTION": "Terms", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "SCHEDULESTARTDATE", "LABEL": "Scheduled start date", "DESCRIPTION": "Scheduled start date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "ACTUALSTARTDATE", "LABEL": "Actual start date", "DESCRIPTION": "Actual start date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "SCHEDULEDCOMPLETIONDATE", "LABEL": "Scheduled completion date", "DESCRIPTION": "Scheduled completion date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "REVISEDCOMPLETIONDATE", "LABEL": "Revised completion date", "DESCRIPTION": "Revised completion date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "SUBSTANTIALCOMPLETIONDATE", "LABEL": "Substantial completion date", "DESCRIPTION": "Substantial completion date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "ACTUALCOMPLETIONDATE", "LABEL": "Actual completion date", "DESCRIPTION": "Actual completion date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "NOTICETOPROCEED", "LABEL": "Notice to proceed", "DESCRIPTION": "Notice to proceed", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "RESPONSEDUE", "LABEL": "Response due", "DESCRIPTION": "Response due", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "EXECUTEDON", "LABEL": "Executed on", "DESCRIPTION": "Executed on", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "SCHEDULEIMPACT", "LABEL": "Schedule impact", "DESCRIPTION": "Schedule impact", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALREFNO", "LABEL": "Internal reference no", "DESCRIPTION": "Internal reference no", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALINITIATEDBYKEY", "LABEL": "Initiated by key", "DESCRIPTION": "Initiated by key", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALINITIATEDBY", "LABEL": "Internal initiated by", "DESCRIPTION": "Internal initiated by", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALINITIATEDBYNAME", "LABEL": "Internal initiated by name", "DESCRIPTION": "internal initiated by name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALVERBALBYKEY", "LABEL": "Verbal by key", "DESCRIPTION": "Verbal by key", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALVERBALBY", "LABEL": "Internal verbal by", "DESCRIPTION": "Internal verbal by", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALVERBALBYNAME", "LABEL": "Internal verbal by name", "DESCRIPTION": "internal verbal by name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALISSUEDBYKEY", "LABEL": "Issued by key", "DESCRIPTION": "Issued by key", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALISSUEDBY", "LABEL": "Internal issued by", "DESCRIPTION": "Internal issued by", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALISSUEDBYNAME", "LABEL": "Internal issued by name", "DESCRIPTION": "internal issued by name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALISSUEDON", "LABEL": "Internal issued on", "DESCRIPTION": "Internal issued on", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "INTERNALAPPROVEDBYKEY", "LABEL": "Approved by key", "DESCRIPTION": "Approved by key", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALAPPROVEDBY", "LABEL": "Internal approved by", "DESCRIPTION": "Internal approved by", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALAPPROVEDBYNAME", "LABEL": "Internal approved by name", "DESCRIPTION": "internal approved by name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALAPPROVEDON", "LABEL": "Internal approved on", "DESCRIPTION": "Internal approved on", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "INTERNALSIGNEDBYKEY", "LABEL": "Signed by key", "DESCRIPTION": "Signed by key", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALSIGNEDBY", "LABEL": "Internal signed by", "DESCRIPTION": "Internal signed by", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALSIGNEDBYNAME", "LABEL": "Internal signed by name", "DESCRIPTION": "internal signed by name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALSIGNEDON", "LABEL": "Internal signed on", "DESCRIPTION": "Internal signed on", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "INTERNALSOURCE", "LABEL": "Internal source", "DESCRIPTION": "Internal source", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "INTERNALSOURCEREFNO", "LABEL": "Internal source reference no", "DESCRIPTION": "Internal source reference no", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "EXTERNALREFNO", "LABEL": "External reference no", "DESCRIPTION": "External refernce no", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "EXTERNALVERBALBYKEY", "LABEL": "Verbal by key", "DESCRIPTION": "Verbal by key", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "EXTERNALVERBALBY", "LABEL": "External verbal by", "DESCRIPTION": "External verbal by", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "EXTERNALAPPROVEDBYKEY", "LABEL": "Approved by key", "DESCRIPTION": "Approved by key", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "EXTERNALAPPROVEDBY", "LABEL": "External approved by", "DESCRIPTION": "External approved by", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "EXTERNALAPPROVEDON", "LABEL": "External approved on", "DESCRIPTION": "External approved on", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "EXTERNALSIGNEDBYKEY", "LABEL": "Signed by key", "DESCRIPTION": "Signed by key", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "EXTERNALSIGNEDBY", "LABEL": "External signed by", "DESCRIPTION": "External signed by", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "EXTERNALSIGNEDON", "LABEL": "External signed on", "DESCRIPTION": "External signed on", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "PERFORMANCEBONDREQUIRED", "LABEL": "Performance bond required", "DESCRIPTION": "Performance bond required", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "PERFORMANCEBONDRECEIVED", "LABEL": "Performance bond received", "DESCRIPTION": "Performance bond received", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "PERFORMANCEBONDAMOUNT", "LABEL": "Performance bond amount", "DESCRIPTION": "Performance bond amount", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "PERFORMANCESURETYCOMP<PERSON>YKEY", "LABEL": "Surety company key", "DESCRIPTION": "Surety companykey", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PERFORMANCESURETYCOMPANY", "LABEL": "Performance surety company", "DESCRIPTION": "Performance surety company", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PERFORMANCESURETYCOMPANYNAME", "LABEL": "Surety company name", "DESCRIPTION": "Surety company name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYMENTBONDREQUIRED", "LABEL": "Payment bond required", "DESCRIPTION": "Payment bond required", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "PAYMENTBONDRECEIVED", "LABEL": "Payment bond received", "DESCRIPTION": "Payment bond received", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "PAYMENTBONDAMOUNT", "LABEL": "Payment bond amount", "DESCRIPTION": "Payment bond amount", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "PAYMENTSURETYCOMPANYKEY", "LABEL": "Surety company key", "DESCRIPTION": "surety companykey", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYMENTSURETYCOMPANY", "LABEL": "Payment surety company", "DESCRIPTION": "Payment surety company", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYMENTSURETYCOMPANYNAME", "LABEL": "Surety company name", "DESCRIPTION": "Surety company name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "HASCHANGE", "LABEL": "Change applied", "DESCRIPTION": "Change applied", "REQUIRED": "true", "READONLY": "true", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "REVISEDTOTAL", "LABEL": "Revised total", "DESCRIPTION": "Revised total", "REQUIRED": "true", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "REVISEDSUBTOTAL", "LABEL": "Revised subtotal", "DESCRIPTION": "Revised subtotal", "REQUIRED": "true", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "TRX_REVISEDTOTAL", "LABEL": "Transaction revised total", "DESCRIPTION": "Transaction revised total", "REQUIRED": "true", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "TRX_REVISEDSUBTOTAL", "LABEL": "Transaction revised subtotal", "DESCRIPTION": "Transaction revised subtotal", "REQUIRED": "true", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "POSTEDCHANGESTOTAL", "LABEL": "Posted changes total", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "RELATEDDOCNO", "LABEL": "Source document number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CHANGELOGNUMBER", "LABEL": "Change log number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "PROJECTCONTRACTID", "LABEL": "Project contract ID", "DESCRIPTION": "Project contract id", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PROJECTCONTRACTKEY", "LABEL": "Project contract key", "DESCRIPTION": "Project contract key", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "PCBEXTERNALREFNO", "LABEL": "External reference", "DESCRIPTION": "External reference", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PCBDESCRIPTION", "LABEL": "Contract description", "DESCRIPTION": "Contract description", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PCBDATE", "LABEL": "Contract date", "DESCRIPTION": "Contract date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "ARCHITECTKEY", "LABEL": "Architect key", "DESCRIPTION": "Architect  key", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "BILLTHROUGHDATE", "LABEL": "Billing through date", "DESCRIPTION": "Billing through date", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "BILLAPPLICATIONNO", "LABEL": "Billing application no.", "DESCRIPTION": "Billing application no.", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "ORGCONTRACTAMT", "LABEL": "Original contract amount", "DESCRIPTION": "Original contract amount", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "NETCHANGES", "LABEL": "Net changes", "DESCRIPTION": "Net changes", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "REVISEDCONTRACTAMT", "LABEL": "Revised contract amount", "DESCRIPTION": "Revised contract amount", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "TOTALCOMPLETEDTODATE", "LABEL": "Total completed to date", "DESCRIPTION": "Total completed to date", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "RETCOMPLETEDAMT", "LABEL": "Retainage from completed amounts", "DESCRIPTION": "Retainage from completed amounts", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "RETSTOREDMATERIALS", "LABEL": "Retainage from stored materials", "DESCRIPTION": "Retainage from stored materials", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "TOTALRETAINAGE", "LABEL": "Total retainage", "DESCRIPTION": "Total retainage", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "TOTALEARNEDLESSRET", "LABEL": "Total earned less retainage", "DESCRIPTION": "Total earned less retainage", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "LESSPRIORAPPLICATION", "LABEL": "Less prior applications", "DESCRIPTION": "Less prior applications", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "CURRENTAMTDUE", "LABEL": "Current amount due", "DESCRIPTION": "Current amount due", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "BALANCETOFINISH", "LABEL": "Balance to finish", "DESCRIPTION": "Balance to finish", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "TCAPMADDITION", "LABEL": "Total changes approved in prior months (Additions)", "DESCRIPTION": "Total changes approved in prior months (Additions)", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "TCAPMDEDUCTION", "LABEL": "Total changes approved in prior months (Deductions)", "DESCRIPTION": "Total changes approved in prior months (Deductions)", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "TCATMADDITION", "LABEL": "Total changes approved this month (Additions)", "DESCRIPTION": "Total changes approved this month (Additions) ", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "TCATMDEDUCTION", "LABEL": "Total changes approved this month (Deductions)", "DESCRIPTION": "Total changes approved this month (Deductions", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "TOTALNETCHANGESADDITION", "LABEL": "Total Net Changes (Additions)", "DESCRIPTION": "Total Net Changes (Additions)", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "TOTALNETCHANGESDEDUCTION", "LABEL": "Total Net Changes (Deductions)", "DESCRIPTION": "Total Net Changes (Deductions", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "TOTALNETCHANGES", "LABEL": "Total Net Changes", "DESCRIPTION": "Total Net Changes", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "CURRENCY", "ISCUSTOM": "false"}, {"ID": "ARCHITECT", "LABEL": "Architect", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}]}, "Relationships": {"Relationship": [{"OBJECTPATH": "BILLTO", "OBJECTNAME": "CONTACT", "LABEL": "Bill-to contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "BILLTO.CONTACTNAME"}, {"OBJECTPATH": "SHIPTO", "OBJECTNAME": "CONTACT", "LABEL": "Ship-to contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "SHIPTO.CONTACTNAME"}, {"OBJECTPATH": "WAREHOUSE", "OBJECTNAME": "WAREHOUSE", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "WAREHOUSE.LOCATIONID"}, {"OBJECTPATH": "SHIPVIA", "OBJECTNAME": "SHIPMETHOD", "LABEL": "Ship via", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "SHIPVIA"}, {"OBJECTPATH": "USERINFO", "OBJECTNAME": "USERINFO", "LABEL": "Last updated User", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "USERID"}, {"OBJECTPATH": "USERINFO2", "OBJECTNAME": "USERINFO", "LABEL": "Created by user", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CREATEDUSERID"}, {"OBJECTPATH": "USERINFO3", "OBJECTNAME": "USERINFO", "LABEL": "Printed by user", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PRINTEDUSERID"}, {"OBJECTPATH": "CONTACT", "OBJECTNAME": "CONTACT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CONTACT.CONTACTNAME"}, {"OBJECTPATH": "CUSTOMER", "OBJECTNAME": "CUSTOMER", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CUSTVENDID"}, {"OBJECTPATH": "ARINVOICE", "OBJECTNAME": "ARINVOICE", "LABEL": "", "RELATIONSHIPTYPE": "ONE2ONE", "RELATEDBY": "PRRECORDKEY"}, {"OBJECTPATH": "TERM", "OBJECTNAME": "ARTERM", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "TERM.NAME"}, {"OBJECTPATH": "INVOICERUN", "OBJECTNAME": "INVOICERUN", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "INVOICERUNKEY"}, {"OBJECTPATH": "PROJECT", "OBJECTNAME": "PROJECT", "LABEL": "Project", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PROJECT"}, {"OBJECTPATH": "CONTRACT", "OBJECTNAME": "CONTRACT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CNCONTRACTKEY"}, {"OBJECTPATH": "GENINVOICEPREVIEWHEADER", "OBJECTNAME": "GENINVOICEPREVIEWHEADER", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PREVIEWHEADERKEY"}, {"OBJECTPATH": "DOCUMENTPARAMS", "OBJECTNAME": "SODOCUMENTPARAMS", "LABEL": "Transaction Definition", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "DOCPARKEY"}, {"OBJECTPATH": "MELOCATION", "OBJECTNAME": "LOCATION", "LABEL": "Created At Entity Information", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MEGAENTITYID"}]}}]}