{"_status": "success", "_functionName": "inspect", "_controlId": "091becf3-d155-4b5b-8edb-513d4cca83a2", "_listType": "All", "_count": 1, "_data": [{"$": {"Name": "SODOCUMENT"}, "Fields": {"Field": ["RECORDNO", "DOCNO", "DOCID", "CREATEDFROM", "STATE", "CLOSED", "WHENCREATED", "AUWHENCREATED", "CREATEDBY", "MODIFIEDBY", "WHENMODIFIED", "WHENDUE", "STATUS", "PONUMBER", "VENDORDOCNO", "DOCPARID", "DOCPARKEY", "DOCPARCLASS", "UPDATES_INV", "TERM.NAME", "NOTE", "WAREHOUSE.LOCATIONID", "SHIPVIA", "USER", "CREATEDUSER", "USERID", "CREATEDUSERID", "CONTACT.CONTACTNAME", "CONTACT.PREFIX", "CONTACT.FIRSTNAME", "CONTACT.INITIAL", "CONTACT.LASTNAME", "CONTACT.COMPANYNAME", "CONTACT.PRINTAS", "CONTACT.PHONE1", "CONTACT.PHONE2", "CONTACT.CELLPHONE", "CONTACT.PAGER", "CONTACT.FAX", "CONTACT.EMAIL1", "CONTACT.EMAIL2", "CONTACT.URL1", "CONTACT.URL2", "CONTACT.VISIBLE", "CONTACT.MAILADDRESS.ADDRESS1", "CONTACT.MAILADDRESS.ADDRESS2", "CONTACT.MAILADDRESS.CITY", "CONTACT.MAILADDRESS.STATE", "CONTACT.MAILADDRESS.ZIP", "CONTACT.MAILADDRESS.COUNTRY", "CONTACT.MAILADDRESS.COUNTRYCODE", "SHIPTOKEY", "SHIPTO.CONTACTNAME", "SHIPTO.PREFIX", "SHIPTO.FIRSTNAME", "SHIPTO.INITIAL", "SHIPTO.LASTNAME", "SHIPTO.COMPANYNAME", "SHIPTO.PRINTAS", "SHIPTO.PHONE1", "SHIPTO.PHONE2", "SHIPTO.CELLPHONE", "SHIPTO.PAGER", "SHIPTO.FAX", "SHIPTO.EMAIL1", "SHIPTO.EMAIL2", "SHIPTO.URL1", "SHIPTO.URL2", "SHIPTO.VISIBLE", "SHIPTO.MAILADDRESS.ADDRESS1", "SHIPTO.MAILADDRESS.ADDRESS2", "SHIPTO.MAILADDRESS.CITY", "SHIPTO.MAILADDRESS.STATE", "SHIPTO.MAILADDRESS.ZIP", "SHIPTO.MAILADDRESS.COUNTRY", "SHIPTO.MAILADDRESS.COUNTRYCODE", "BILLTOKEY", "BILLTO.CONTACTNAME", "BILLTO.PREFIX", "BILLTO.FIRSTNAME", "BILLTO.INITIAL", "BILLTO.LASTNAME", "BILLTO.COMPANYNAME", "BILLTO.PRINTAS", "BILLTO.PHONE1", "BILLTO.PHONE2", "BILLTO.CELLPHONE", "BILLTO.PAGER", "BILLTO.FAX", "BILLTO.EMAIL1", "BILLTO.EMAIL2", "BILLTO.URL1", "BILLTO.URL2", "BILLTO.VISIBLE", "BILLTO.MAILADDRESS.ADDRESS1", "BILLTO.MAILADDRESS.ADDRESS2", "BILLTO.MAILADDRESS.CITY", "BILLTO.MAILADDRESS.STATE", "BILLTO.MAILADDRESS.ZIP", "BILLTO.MAILADDRESS.COUNTRY", "BILLTO.MAILADDRESS.COUNTRYCODE", "MESSAGE", "PRRECORDKEY", "INVBATCHKEY", "PRINVBATCHKEY", "ADDGLBATCHKEY", "PRINTED", "BACKORDER", "SUBTOTAL", "TOTAL", "ENTGLGROUP", "CURRENCY", "EXCHRATEDATE", "EXCHRATETYPES.NAME", "EXCHRATE", "SCHOPKEY", "SALESCONTRACT", "USEDASCONTRACT", "TRX_SUBTOTAL", "TRX_TOTAL", "EXCH_RATE_TYPE_ID", "RENEWEDDOC", "BASECURR", "SYSTEMGENERATED", "INVOICERUNKEY", "DOCPAR_IN_OUT", "WHENPOSTED", "PRINTEDUSERID", "DATEPRINTED", "PRINTEDBY", "ADJ", "TAXSOLUTIONID", "CUSTVENDID", "CUSTVENDNAME", "CUSTVENDEMAILOPTIN", "HASPOSTEDREVREC", "CONTRACTID", "CONTRACTDESC", "TRX_TOTALPAID", "TOTALPAID", "TRX_TOTALENTERED", "TOTALENTERED", "TRX_TOTALDUE", "TOTALDUE", "PAYMENTSTATUS", "SIGN_FLAG", "VSOEPRICELIST", "VSOEPRCLSTKEY", "ORIGDOCDATE", "HASADVBILLING", "INVOICERUN_EXPENSEPRICEMAR<PERSON>UP", "INVOICERUN_DESCRIPTION", "PROJECTKEY", "PROJECT", "PROJECTNAME", "PREVIEWHEADERKEY", "CNCONTRACTID", "CNCONTRACTNAME", "NEEDBYDATE", "SHIPBYDATE", "CANCELAFTERDATE", "DONOTSHIPBEFOREDATE", "DONOTSHIPAFTERDATE", "SERVICEDELIVERYDATE", "TRACKINGNUMBER", "SHIPPEDDATE", "CUSTOMERPONUMBER", "RETAINAGEPERCENTAGE", "SCOPE", "INCLUSIONS", "EXCLUSIONS", "TERMS", "SCHEDULESTARTDATE", "ACTUALSTARTDATE", "SCHEDULEDCOMPLETIONDATE", "REVISEDCOMPLETIONDATE", "SUBSTANTIALCOMPLETIONDATE", "ACTUALCOMPLETIONDATE", "NOTICETOPROCEED", "RESPONSEDUE", "EXECUTEDON", "SCHEDULEIMPACT", "INTERNALREFNO", "INTERNALINITIATEDBYKEY", "INTERNALINITIATEDBY", "INTERNALINITIATEDBYNAME", "INTERNALVERBALBYKEY", "INTERNALVERBALBY", "INTERNALVERBALBYNAME", "INTERNALISSUEDBYKEY", "INTERNALISSUEDBY", "INTERNALISSUEDBYNAME", "INTERNALISSUEDON", "INTERNALAPPROVEDBYKEY", "INTERNALAPPROVEDBY", "INTERNALAPPROVEDBYNAME", "INTERNALAPPROVEDON", "INTERNALSIGNEDBYKEY", "INTERNALSIGNEDBY", "INTERNALSIGNEDBYNAME", "INTERNALSIGNEDON", "INTERNALSOURCE", "INTERNALSOURCEREFNO", "EXTERNALREFNO", "EXTERNALVERBALBYKEY", "EXTERNALVERBALBY", "EXTERNALAPPROVEDBYKEY", "EXTERNALAPPROVEDBY", "EXTERNALAPPROVEDON", "EXTERNALSIGNEDBYKEY", "EXTERNALSIGNEDBY", "EXTERNALSIGNEDON", "PERFORMANCEBONDREQUIRED", "PERFORMANCEBONDRECEIVED", "PERFORMANCEBONDAMOUNT", "PERFORMANCESURETYCOMP<PERSON>YKEY", "PERFORMANCESURETYCOMPANY", "PERFORMANCESURETYCOMPANYNAME", "PAYMENTBONDREQUIRED", "PAYMENTBONDRECEIVED", "PAYMENTBONDAMOUNT", "PAYMENTSURETYCOMPANYKEY", "PAYMENTSURETYCOMPANY", "PAYMENTSURETYCOMPANYNAME", "HASCHANGE", "REVISEDTOTAL", "REVISEDSUBTOTAL", "TRX_REVISEDTOTAL", "TRX_REVISEDSUBTOTAL", "POSTEDCHANGESTOTAL", "RELATEDDOCNO", "ENABLEDOCCHANGE", "MEGAENTITYKEY", "MEGAENTITYID", "MEGAENTITYNAME", "RECORD_URL"]}}]}