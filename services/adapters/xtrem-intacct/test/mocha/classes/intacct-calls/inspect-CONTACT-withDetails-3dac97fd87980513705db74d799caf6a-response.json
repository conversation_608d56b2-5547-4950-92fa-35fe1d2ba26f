{"_status": "success", "_functionName": "inspect", "_controlId": "b7aa0830-4bf6-4411-a7a9-14e24eb92fc7", "_listType": "All", "_count": 1, "_data": [{"$": {"Name": "CONTACT"}, "Attributes": {"SingularName": "Contact", "PluralName": "Contacts", "Description": ""}, "Fields": {"Field": [{"Name": "RECORDNO", "GroupName": "", "dataName": "Pt_FieldInt", "externalDataName": "integer", "isRequired": "false", "isReadOnly": "true", "maxLength": "8", "DisplayLabel": "Record number", "Description": "Record number", "id": "999508200000902"}, {"Name": "CONTACTNAME", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "true", "isReadOnly": "false", "maxLength": "200", "DisplayLabel": "Contact name", "Description": "Unique name to be used to identify the contact in lists", "id": "999508200000001"}, {"Name": "COMPANYNAME", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "100", "DisplayLabel": "Company name", "Description": "Full name of the company", "id": "999508200000002"}, {"Name": "PREFIX", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "15", "DisplayLabel": "Prefix", "Description": "Prefix", "id": "999508200000003"}, {"Name": "FIRSTNAME", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "40", "DisplayLabel": "First name", "Description": "First name", "id": "999508200000004"}, {"Name": "LASTNAME", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "40", "DisplayLabel": "Last name", "Description": "Last name", "id": "999508200000005"}, {"Name": "INITIAL", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "40", "DisplayLabel": "Middle name", "Description": "Middle name", "id": "999508200000006"}, {"Name": "PRINTAS", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "true", "isReadOnly": "false", "maxLength": "200", "DisplayLabel": "Print as", "Description": "Name as appears on official documents", "id": "999508200000007"}, {"Name": "TAXABLE", "GroupName": "", "dataName": "Pt_FieldBoolean", "externalDataName": "boolean", "isRequired": "false", "isReadOnly": "false", "maxLength": "0", "DisplayLabel": "Taxable", "Description": "Taxable", "id": "999508200000008"}, {"Name": "TAXGROUP", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "0", "DisplayLabel": "Contact tax group", "Description": "Tax group", "id": "999508200000009"}, {"Name": "PHONE1", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "30", "DisplayLabel": "Primary phone", "Description": "Primary phone", "id": "999508200000013"}, {"Name": "PHONE2", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "30", "DisplayLabel": "Secondary phone", "Description": "Secondary phone", "id": "999508200000014"}, {"Name": "CELLPHONE", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "30", "DisplayLabel": "Mobile phone", "Description": "Mobile phone number", "id": "999508200000015"}, {"Name": "PAGER", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "30", "DisplayLabel": "Pager", "Description": "Pager", "id": "999508200000016"}, {"Name": "FAX", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "30", "DisplayLabel": "Fax", "Description": "Fax", "id": "999508200000017"}, {"Name": "EMAIL1", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "200", "DisplayLabel": "Primary email address", "Description": "Primary email address", "id": "999508200000018"}, {"Name": "EMAIL2", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "200", "DisplayLabel": "Secondary email addresses", "Description": "Secondary email address", "id": "999508200000019"}, {"Name": "URL1", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "256", "DisplayLabel": "Primary URL", "Description": "Primary URL", "id": "999508200000020"}, {"Name": "URL2", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "256", "DisplayLabel": "Secondary URL", "Description": "Secondary URL", "id": "999508200000021"}, {"Name": "VISIBLE", "GroupName": "", "dataName": "Pt_FieldBoolean", "externalDataName": "boolean", "isRequired": "false", "isReadOnly": "false", "maxLength": "0", "DisplayLabel": "Visible", "Description": "Visible", "id": "999508200000022"}, {"Name": "MAILADDRESS.ADDRESS1", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "255", "DisplayLabel": "Address line 1", "Description": "Address line 1", "id": "999508200000301"}, {"Name": "MAILADDRESS.ADDRESS2", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "255", "DisplayLabel": "Address line 2", "Description": "Address line 2", "id": "999508200000302"}, {"Name": "MAILADDRESS.ADDRESS3", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "100", "DisplayLabel": "Address line 3", "Description": "Address line 3", "id": "999508200000310"}, {"Name": "MAILADDRESS.CITY", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "80", "DisplayLabel": "City", "Description": "City", "id": "999508200000303"}, {"Name": "MAILADDRESS.STATE", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "40", "DisplayLabel": "State or territory", "Description": "State or territory", "id": "999508200000304"}, {"Name": "MAILADDRESS.ZIP", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "30", "DisplayLabel": "ZIP or postal code", "Description": "ZIP or postal code", "id": "999508200000305"}, {"Name": "MAILADDRESS.COUNTRY", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "60", "DisplayLabel": "Country", "Description": "Country", "id": "999508200000306"}, {"Name": "STATUS", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "0", "DisplayLabel": "Status", "Description": "Active or inactive", "id": "999508200000900"}, {"Name": "MAILADDRESS.COUNTRYCODE", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "0", "DisplayLabel": "Country code", "Description": "Country code", "id": "999508200000307"}, {"Name": "MAILADDRESS.LATITUDE", "GroupName": "", "dataName": "Pt_FieldDouble", "externalDataName": "number", "isRequired": "false", "isReadOnly": "false", "maxLength": "12", "DisplayLabel": "Latitude", "Description": "Latitude", "id": "999508200000308"}, {"Name": "MAILADDRESS.LONGITUDE", "GroupName": "", "dataName": "Pt_FieldDouble", "externalDataName": "number", "isRequired": "false", "isReadOnly": "false", "maxLength": "13", "DisplayLabel": "Longitude", "Description": "Longitude", "id": "999508200000309"}, {"Name": "PRICESCHEDULE", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "0", "DisplayLabel": "Price schedule", "Description": "Price schedule", "id": "999508200000010"}, {"Name": "DISCOUNT", "GroupName": "", "dataName": "Pt_FieldDouble", "externalDataName": "number", "isRequired": "false", "isReadOnly": "false", "maxLength": "10", "DisplayLabel": "Discount percent", "Description": "Discount", "id": "999508200000011"}, {"Name": "PRICELIST", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "0", "DisplayLabel": "Price list", "Description": "Contact price list", "id": "999508200000012"}, {"Name": "PRICELISTKEY", "GroupName": "", "dataName": "Pt_FieldInt", "externalDataName": "integer", "isRequired": "false", "isReadOnly": "false", "maxLength": "8", "DisplayLabel": "Price list record number", "Description": "Price list record number", "id": "999508200000027"}, {"Name": "TAXID", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "20", "DisplayLabel": "Tax ID", "Description": "Tax identification number", "id": "999508200000025"}, {"Name": "SIRET", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "20", "DisplayLabel": "SIRET", "Description": "SIRET", "id": "999508200000031"}, {"Name": "TAXGROUPKEY", "GroupName": "", "dataName": "Pt_FieldInt", "externalDataName": "integer", "isRequired": "false", "isReadOnly": "false", "maxLength": "8", "DisplayLabel": "Tax group record number", "Description": "Tax group record number", "id": "999508200000026"}, {"Name": "TAXSOLUTIONID", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "0", "DisplayLabel": "Tax solution", "Description": "Tax solution", "id": "999508200000029"}, {"Name": "TAXSCHEDULE", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "false", "maxLength": "0", "DisplayLabel": "Default tax schedule", "Description": "Default tax schedule", "id": "999508200000030"}, {"Name": "PRICESCHEDULEKEY", "GroupName": "", "dataName": "Pt_FieldInt", "externalDataName": "integer", "isRequired": "false", "isReadOnly": "false", "maxLength": "8", "DisplayLabel": "Price schedule record number", "Description": "Price schedule record number", "id": "999508200000028"}, {"Name": "WHENCREATED", "GroupName": "", "dataName": "Pt_FieldDateTime", "externalDataName": "datetime", "isRequired": "false", "isReadOnly": "true", "maxLength": "22", "DisplayLabel": "When created", "Description": "Timestamp marking last time this was created.", "id": "999508200000970"}, {"Name": "WHENMODIFIED", "GroupName": "", "dataName": "Pt_FieldDateTime", "externalDataName": "datetime", "isRequired": "false", "isReadOnly": "true", "maxLength": "22", "DisplayLabel": "When modified", "Description": "Timestamp marking last time this was changed.", "id": "999508200000969"}, {"Name": "CREATEDBY", "GroupName": "", "dataName": "Pt_FieldInt", "externalDataName": "integer", "isRequired": "false", "isReadOnly": "true", "maxLength": "0", "DisplayLabel": "Created by", "Description": "User who created this.", "id": "999508200000971"}, {"Name": "MODIFIEDBY", "GroupName": "", "dataName": "Pt_FieldInt", "externalDataName": "integer", "isRequired": "false", "isReadOnly": "true", "maxLength": "0", "DisplayLabel": "Modified by", "Description": "User who modified this.", "id": "999508200000972"}, {"Name": "MEGAENTITYKEY", "GroupName": "", "dataName": "Pt_FieldInt", "externalDataName": "integer", "isRequired": "false", "isReadOnly": "true", "maxLength": "0", "DisplayLabel": "Created at - Entity key", "Description": "Created at - Entity key", "id": "999508200000973"}, {"Name": "MEGAENTITYID", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "true", "maxLength": "20", "DisplayLabel": "Created at - Entity ID", "Description": "Created at - Entity ID", "id": "999508200000974"}, {"Name": "MEGAENTITYNAME", "GroupName": "", "dataName": "Pt_FieldString", "externalDataName": "string", "isRequired": "false", "isReadOnly": "true", "maxLength": "40", "DisplayLabel": "Created at - Entity name", "Description": "Created at - Entity name", "id": "999508200000980"}, {"Name": "RECORD_URL", "GroupName": "", "dataName": "Pt_FieldDummy", "externalDataName": "string", "isRequired": "false", "isReadOnly": "true", "maxLength": "0", "DisplayLabel": "Record URL", "Description": "A portable, user-independent, deep-link URL for viewing this record", "id": "999508200000979"}]}}]}