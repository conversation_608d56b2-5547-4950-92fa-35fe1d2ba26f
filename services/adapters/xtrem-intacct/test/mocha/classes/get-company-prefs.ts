import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacct from '../../../index';

describe('Get company prefs Testing', () => {
    it('Get Company Prefs Request From SDK', () =>
        Test.withContext(async context => {
            const lookup = new xtremIntacct.classes.sdk.Functions.GetCompanyPrefs(
                context,
                {
                    synchronous: true,
                },
                'AR',
            );

            assert.match(
                await lookup.xmlWithoutFunction(true),
                /<get_companyprefs application="AR"\/>/gm,
                await lookup.xmlWithoutFunction(true),
            );
        }));

    it('Get all company refs for AR', () =>
        Test.withContext(
            async context => {
                const lookup = await new xtremIntacct.classes.sdk.Functions.GetCompanyPrefs(
                    context,
                    {
                        synchronous: true,
                    },
                    'AR',
                ).execute();

                assert.isArray(lookup);
            },
            {
                scenario: 'company-pref-ar',
                directory: __dirname,
            },
        ));
});
