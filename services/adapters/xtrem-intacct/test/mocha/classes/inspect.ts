import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as XMLBuilder from 'xmlbuilder';
import * as xtremIntacct from '../../../lib';

describe('Test Inspect function', () => {
    it(' Inspect function All Intacct Objects', () =>
        Test.withContext(
            async context => {
                const inspectAll = new xtremIntacct.classes.sdk.Functions.Inspect(context, '*');
                const allObject = await inspectAll.allObject();
                assert.isArray(allObject);
                assert.deepEqual(
                    allObject.at(0),
                    {
                        name: '1099 e-file submission status',
                        object: 'FILE1099SUBMISSIONLOG',
                    },
                    JSON.stringify(allObject.at(0)),
                );
                assert.deepEqual(
                    allObject.at(1),
                    {
                        name: '1099 e-file submissions',
                        object: 'FILE1099',
                    },
                    JSON.stringify(allObject.at(1)),
                );
            },
            {
                scenario: 'inspect-api',
                directory: __dirname,
                testAttributes: {
                    isCreationMode: false,
                },
            },
        ));
    it('Inspect Request ', () =>
        Test.withContext(async context => {
            const inspect = new xtremIntacct.classes.sdk.Functions.Inspect(context, 'VENDOR');

            const xmlInspect = await inspect.xmlWithoutFunction(false);
            assert.equal(xmlInspect, '<inspect><object>VENDOR</object></inspect>', xmlInspect);

            const inspectWithDetail = new xtremIntacct.classes.sdk.Functions.Inspect(context, 'VENDOR');
            inspectWithDetail.withDetail = true;
            const xmlInspectWithDetail = await inspectWithDetail.xmlWithoutFunction(false);
            assert.equal(
                xmlInspectWithDetail,
                '<inspect detail="1"><object>VENDOR</object></inspect>',
                xmlInspectWithDetail,
            );
        }));
    it(' Inspect Request ', () =>
        Test.withContext(context => {
            const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));
            const inspect = new xtremIntacct.classes.sdk.Functions.Inspect(context, 'VENDOR');

            inspect.writeXml(xml);
            assert.match(
                xml.flush(),
                /<request><function controlid="\w*-\w*-\w*-\w*-\w*"><inspect><object>VENDOR<\/object><\/inspect><\/function><\/request>/gm,
                xml.flush(true),
            );
        }));

    it('Inspect execution', () =>
        Test.withContext(
            async context => {
                const inspect = await new xtremIntacct.classes.sdk.Functions.Inspect(context, 'SODOCUMENT').execute();
                assert.isNotArray(inspect);
                assert.equal(inspect.Fields.Field.length, 236);
            },
            {
                scenario: 'inspect-SODOCUMENT',
                directory: __dirname,
            },
        ));
    it('Inspect execution with detail', () =>
        Test.withContext(
            async context => {
                const inspect = await new xtremIntacct.classes.sdk.Functions.Inspect(
                    context,
                    'CONTACT',
                ).executeWithDetail();

                assert.isNotArray(inspect);
                assert.equal(inspect.Fields.Field.length, 49);
                const contactName = inspect.Fields.Field.find(field => field.Name === 'CONTACTNAME');
                assert.equal(contactName?.maxLength, 200);
            },
            {
                scenario: 'inspect-CONTACT-withDetails',
                directory: __dirname,
                testAttributes: { isCreationMode: true },
            },
        ));
});
