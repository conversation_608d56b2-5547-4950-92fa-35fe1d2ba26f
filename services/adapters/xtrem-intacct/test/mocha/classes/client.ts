import * as IA from '@intacct/intacct-sdk';
import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacct from '../../../index';

describe(' Intacct Client Classes ', () => {
    before(() => {});

    it('Client - session ', () =>
        Test.withContext(
            async context => {
                /** Create the session  */

                const fakeConfig = new IA.ClientConfig();
                fakeConfig.senderId = 'testsender';
                fakeConfig.senderPassword = 'testsendpass';
                fakeConfig.sessionId = 'testsession..';

                const client = new xtremIntacct.classes.sdk.Client(context, fakeConfig);

                assert.isNotNull(client);

                // Test with throws instead of isRejected because the exception is thrown synchronously
                await assert.isRejected(
                    client.executeOnlineRequest(new xtremIntacct.classes.sdk.Functions.GetList(context, {}, 'test')),

                    'no request config',
                );

                const requestConfig = new IA.RequestConfig();
                requestConfig.controlId = '1234';

                const data = await client.executeOnlineRequest(
                    new xtremIntacct.classes.sdk.Functions.Inspect(context, 'XTreeM_Audit_Trail', {
                        controlID: '4321',
                    }),
                    requestConfig,
                );

                assert.instanceOf(data, xtremIntacct.classes.sdk.Xml.OnlineResponse);
            },
            {
                mocks: ['axios'],
                scenario: 'intacct-client-session-id',
                directory: __dirname,
            },
        ));

    it('Client - password ', () =>
        Test.withContext(
            async context => {
                /** Create the session  */

                const fakeConfig = new IA.ClientConfig();
                fakeConfig.senderId = 'testsender';
                fakeConfig.senderPassword = 'testsendpass';
                fakeConfig.companyId = 'TestCompany';
                fakeConfig.userId = 'testsession..';
                fakeConfig.userPassword = 'testpass';

                const client = new xtremIntacct.classes.sdk.Client(context, fakeConfig);

                assert.isNotNull(client);

                const requestConfig = new IA.RequestConfig();
                requestConfig.controlId = '1234';

                const data = await client.executeOnlineRequest(
                    new xtremIntacct.classes.sdk.Functions.Inspect(context, 'XTreeM_Audit_Trail', {
                        controlID: '4321',
                    }),
                    requestConfig,
                );

                assert.instanceOf(data, xtremIntacct.classes.sdk.Xml.OnlineResponse);
            },
            {
                mocks: ['axios'],
                scenario: 'intacct-client-password',
                directory: __dirname,
            },
        ));

    it('Throw errors ', () =>
        Test.withContext(context => {
            /** Create the session  */
            assert.throw(() => new xtremIntacct.classes.sdk.Client(context), 'no ClientConfig');

            const fakeConfig = new IA.ClientConfig();
            fakeConfig.senderId = 'testsender';
            fakeConfig.senderPassword = 'testsendpass';
            fakeConfig.userId = 'testsession..';
            fakeConfig.userPassword = 'testpass';

            assert.throw(
                () => new xtremIntacct.classes.sdk.Client(context, fakeConfig),
                'Required Company ID not supplied in config or env variable "INTACCT_COMPANY_ID"',
            );
        }));
});
