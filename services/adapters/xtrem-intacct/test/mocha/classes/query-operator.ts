import { assert } from 'chai';
import * as XMLBuilder from 'xmlbuilder';
import * as xtremIntacct from '../../../index';

describe(' Query Operators   ', () => {
    before(() => {});
    // TODO : unskip specific filters

    it('Filter query - between ', () => {
        const filterToto = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('toto');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        filterToto.between('a', 'c');

        filterToto.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><between><field>toto</field><value>a</value><value>c</value></between></request>',
            xml.flush(),
        );
    });
    it('Filter query - equal to ', () => {
        const filterToto = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('toto');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        filterToto.equalTo('test');

        filterToto.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><equalto><field>toto</field><value>test</value></equalto></request>',
            xml.flush(),
        );
    });
    it.skip('Filter query - equal to for getList ', () => {
        const filterToto = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('toto');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        filterToto.equalToSign('test');

        filterToto.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><between><field>toto</field><value>a</value><value>c</value></between></request>',
            xml.flush(),
        );
    });
    it('Filter query - greather than ', () => {
        const filterToto = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('toto');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        filterToto.greaterThan('10');

        filterToto.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><greaterthan><field>toto</field><value>10</value></greaterthan></request>',
            xml.flush(),
        );
    });
    it('Filter query - greather than or equal to ', () => {
        const filterToto = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('toto');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        filterToto.greaterThanOrEqualTo('10');

        filterToto.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><greaterthanorequalto><field>toto</field><value>10</value></greaterthanorequalto></request>',
            xml.flush(),
        );
    });
    it('Filter query - less than ', () => {
        const filterToto = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('toto');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        filterToto.lessThan('10');

        filterToto.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><lessthan><field>toto</field><value>10</value></lessthan></request>',
            xml.flush(),
        );
    });
    it('Filter query - less than or equal to', () => {
        const filterToto = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('toto');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        filterToto.lessThanOrEqualTo('10');

        filterToto.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><lessthanorequalto><field>toto</field><value>10</value></lessthanorequalto></request>',
            xml.flush(),
        );
    });
    it.skip('Filter query - less than  or equal to for getList', () => {
        const filterToto = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('toto');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        filterToto.lessThanOrEqualToSign('10');

        filterToto.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><between><field>toto</field><value>a</value><value>c</value></between></request>',
            xml.flush(),
        );
    });

    it.skip('Filter query - less than   for getList', () => {
        const filterToto = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('toto');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        filterToto.lessThanSign('10');

        filterToto.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><between><field>toto</field><value>a</value><value>c</value></between></request>',
            xml.flush(),
        );
    });

    it('Filter query - like ', () => {
        const filterToto = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('toto');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        filterToto.like('Bonjo');

        filterToto.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><like><field>toto</field><value>Bonjo</value></like></request>',
            xml.flush(),
        );
    });

    it('Filter query - not equal to  ', () => {
        const filterToto = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('toto');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        filterToto.notEqualTo('Bonjo');

        filterToto.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><notequalto><field>toto</field><value>Bonjo</value></notequalto></request>',
            xml.flush(),
        );
    });

    it('Filter query - not in  ', () => {
        const filterToto = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('toto');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        filterToto.notIn(['Bonjour', 'Salut']);

        filterToto.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><notin><field>toto</field><value>Bonjour</value><value>Salut</value></notin></request>',
            xml.flush(),
        );
    });

    it('Filter query - in  ', () => {
        const filterToto = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('toto');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        filterToto.in(['Bonjour', 'Salut']);

        filterToto.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><in><field>toto</field><value>Bonjour</value><value>Salut</value></in></request>',
            xml.flush(),
        );
    });

    it('Filter query - not like  ', () => {
        const filterToto = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('toto');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        filterToto.notLike('Bonjour');

        filterToto.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><notlike><field>toto</field><value>Bonjour</value></notlike></request>',
            xml.flush(),
        );
    });
});
