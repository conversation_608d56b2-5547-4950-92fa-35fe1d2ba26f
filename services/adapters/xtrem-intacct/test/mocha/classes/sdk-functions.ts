import type * as IA from '@intacct/intacct-sdk';
import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import * as XMLBuilder from 'xmlbuilder';
import * as xtremIntacct from '../../../index';

const dirname = __dirname.replace('/build/', '/');

/**
 * Without the execute for now, we need to mock the sdk part
 */
describe(' I Function Classes from SDK   ', () => {
    before(() => {});

    it('XmlQuery Ifunction Class', () =>
        Test.withContext(context => {
            const xmlPath = path.resolve(dirname, 'xml-requests/vendor-query-count.xml');
            const xmlQuery = fs.readFileSync(xmlPath);
            const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

            const iFunction = new xtremIntacct.classes.sdk.Functions.XmlQuery(context, xmlQuery.toString());

            iFunction.writeXml(xml);
            assert.include(xml.flush(), xmlQuery.toString(), xml.flush(true));
        }));

    it('XmlQuery Ifunction Class execute', () =>
        Test.withContext(
            async context => {
                const xmlPath = path.resolve(dirname, 'xml-requests/vendor-query-where.xml');
                const xmlQuery = fs.readFileSync(xmlPath);

                const iFunctionResult = await new xtremIntacct.classes.sdk.Functions.XmlQuery<{ NAME: string }[]>(
                    context,
                    xmlQuery.toString(),
                ).execute();

                assert.equal(
                    iFunctionResult[0].NAME,
                    'Massachusetts Department of Revenue',
                    JSON.stringify(iFunctionResult),
                );
            },
            {
                scenario: 'query-where-double',
                directory: dirname,
            },
        ));

    it('Inspect Ifunction Class', () =>
        Test.withContext(async context => {
            const iFunction = new xtremIntacct.classes.sdk.Functions.Inspect(context, 'XTreeM_Audit_Trail');

            await iFunction.xmlWithoutFunction(true);
            assert.match(
                await iFunction.xmlWithoutFunction(true),
                /<inspect>\n\s*<object>XTreeM_Audit_Trail<\/object>\n\s*<\/inspect>/gm,
                await iFunction.xmlWithoutFunction(true),
            );
        }));

    it('Lookup Ifunction Class', () =>
        Test.withContext(
            async context => {
                function resultFunction(result: IA.Xml.Response.Result): string[] {
                    return result.data.map(gl => gl.$.Name);
                }

                const lookup = new xtremIntacct.classes.sdk.Functions.Lookup(context, 'XTreeM_Audit_Trail', {
                    resultFunction,
                });

                await lookup.xmlWithoutFunction(true);
                assert.match(
                    await lookup.xmlWithoutFunction(true),
                    /<lookup>\n\s*<object>XTreeM_Audit_Trail<\/object>\n\s*<\/lookup>/gm,
                    await lookup.xmlWithoutFunction(true),
                );
                const result = await lookup.execute();

                assert.deepEqual(result, ['XTreeM_Audit_Trail'], JSON.stringify(result));
            },
            {
                scenario: 'lookup-all',
                directory: dirname,
            },
        ));

    it('Install application Ifunction Class with tenantID ', () =>
        Test.withContext(async context => {
            const iFunction = new xtremIntacct.classes.sdk.Functions.InstallApplication(
                context,
                `<body><tenantId>MyTenantName</tenantId></body>`,
            );

            await iFunction.xmlWithoutFunction(true);

            const installAppRegex =
                /<installApp>\n\s*<appxml><!\[CDATA\[<body><tenantId>MyTenantName<\/tenantId><\/body>\]\]><\/appxml>\n\s*<\/installApp>/gm;

            assert.match(
                await iFunction.xmlWithoutFunction(true),
                installAppRegex,
                await iFunction.xmlWithoutFunction(true),
            );
        }));
});
