import { assert } from 'chai';
import * as xtremIntacct from '../../../index';

describe('OfflineResponse', () => {
    it('should parse offline response ', () => {
        const xml = `<?xml version="1.0" encoding="UTF-8"?>
            <response>
                <acknowledgement><status>success</status></acknowledgement>
                <control>
                    <status>success</status>
                    <senderid>X3_mfg</senderid>
                    <controlid>Eoeitj787xZggLdAJ162k</controlid>
                    <uniqueid>false</uniqueid>
                    <dtdversion>3.0</dtdversion>
                    <policyid>xt_async_1</policyid>
                </control>
            </response>`;

        const offlineResponse = new xtremIntacct.classes.sdk.Xml.OfflineResponse(xml);

        assert.instanceOf(offlineResponse, xtremIntacct.classes.sdk.Xml.OfflineResponse);
    });
});
