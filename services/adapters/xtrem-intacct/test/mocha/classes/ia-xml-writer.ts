import { date, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as XMLBuilder from 'xmlbuilder';
import * as xtremIntacct from '../../../index';

describe(' IA Xml Writer Classes ', () => {
    before(() => {});

    it('IA Xml Writer Classes  statics function getValue  ', () =>
        Test.withContext(
            () => {
                assert.deepEqual(xtremIntacct.classes.sdk.IaXmlWriter.getValue(true), 'true');
                assert.deepEqual(xtremIntacct.classes.sdk.IaXmlWriter.getValue(false), 'false');
                assert.deepEqual(xtremIntacct.classes.sdk.IaXmlWriter.getValue('myString'), 'myString');
                assert.deepEqual(xtremIntacct.classes.sdk.IaXmlWriter.getValue(2), '2');
                /**
                 *  TODO : unit test dates getValue for IaXmlWriter
                 * assert.deepEqual(
                 *     xtremIntacct.classes.sdk.IaXmlWriter.getValue(Date.parse('2020-08-17')),
                 *     '1597622400000',
                 * );
                 * assert.deepEqual(xtremIntacct.classes.sdk.IaXmlWriter.getValue(date.today()), '08/17/2020');
                 */
                assert.deepEqual(xtremIntacct.classes.sdk.IaXmlWriter.getValue({ text: 'emptyObject' }), '');
            },
            { today: '2020-08-17' },
        ));

    it('IA Xml Writer Classes - doc function ', () => {
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));
        xml.writeXmlString("<startElement value='test'><bonjour>Toto</bonjour></startElement>");

        const xmlDoc = xml.doc();
        assert.equal(
            xmlDoc.toString(),
            '<?xml version="1.0"?><request><startElement value=\'test\'><bonjour>Toto</bonjour></startElement></request>',
            xmlDoc.toString(),
        );
    });

    it('IA Xml Writer Classes - flushToJson function ', async () => {
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));
        xml.writeXmlString("<startElement value='test'><bonjour>Toto</bonjour></startElement>");
        const jsonData = await xml.flushToJson();
        assert.deepEqual(
            jsonData,
            { request: { startElement: { value: 'test', bonjour: 'Toto' } } },
            JSON.stringify(jsonData),
        );
    });

    it('IA Xml Writer Classes - Add XML string ', () => {
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));
        xml.writeXmlString("<startElement value='test'><bonjour>Toto</bonjour></startElement>");

        const result = "<request><startElement value='test'><bonjour>Toto</bonjour></startElement></request>";

        assert.match(
            xml.flush(true).replace(/\s/g, ''),
            new RegExp(result.toString().replace(/\s/g, ''), 'gmiu'),
            xml.flush(true),
        );
    });

    it('IA Xml Writer Classes - Add string ', () => {
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));
        xml.writeString("<startElement value='test'><bonjour>Toto</bonjour></startElement>");

        const result =
            "<request>&lt;startElement value='test'&gt;&lt;bonjour&gt;Toto&lt;/bonjour&gt;&lt;/startElement&gt;</request>";
        assert.match(
            xml.flush(true).replace(/\s/g, ''),
            new RegExp(result.toString().replace(/\s/g, ''), 'gmiu'),
            xml.flush(true),
        );
    });

    it(' IA Xml Writer Classes start end with an Attribute ', () => {
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        xml.writeStartEndWithAttibute('test', 'line', '2');

        const result = '<request><test line="2" /></request>';

        assert.match(xml.flush(true).replace(/\s/g, ''), new RegExp(result.toString().replace(/\s/g, ''), 'gmiu'));
    });

    it(' IA Xml Writer Classes start element end ', () => {
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        xml.writeStartElement('startElement');
        xml.writeElement('bonjour', null, true);
        xml.writeElement('bonjour 2 ', null); // will not be write
        xml.writeEndElement();

        const result = '<request><startElement><bonjour /></startElement></request>';

        assert.match(xml.flush(true).replace(/\s/g, ''), new RegExp(result.toString().replace(/\s/g, ''), 'gmiu'));
    });

    it(' IA Xml Writer Classes date ', () => {
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        xml.writeStartElement('startElement');
        xml.writeElementDate('myDate', date.make(2020, 10, 2), 'base');
        xml.writeEndElement();

        const result = '<request><startElement><myDate>10/02/2020</myDate></startElement></request>';

        assert.match(xml.flush(true).replace(/\s/g, ''), new RegExp(result.toString().replace(/\s/g, ''), 'gmiu'));
    });

    it(' IA Xml Writer Classes date ', () => {
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

        xml.writeStartElement('startElement');
        xml.writeDateSplitElements(date.make(2020, 10, 2));
        xml.writeEndElement();

        const result = '<request><startElement><year>2020</year><month>10</month><day>2</day></startElement></request>';

        assert.match(xml.flush(true).replace(/\s/g, ''), new RegExp(result.toString().replace(/\s/g, ''), 'gmiu'));
    });
});
