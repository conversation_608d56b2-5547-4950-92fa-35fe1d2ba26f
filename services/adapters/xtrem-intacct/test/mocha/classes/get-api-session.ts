import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacct from '../../../index';

describe('ApiSessionCreate', () => {
    it(' Api session create  ', () =>
        Test.withContext(async context => {
            const lookup = new xtremIntacct.classes.sdk.Functions.ApiSessionCreate(context, {});
            assert.match(
                await lookup.xmlWithoutFunction(false),
                /<getAPISession\/>/gm,
                await lookup.xmlWithoutFunction(false),
            );
        }));
});
