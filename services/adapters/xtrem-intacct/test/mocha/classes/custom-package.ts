import { assert } from 'chai';
import * as xtremIntacct from '../../../index';

describe(' GetList Testing  ', () => {
    before(() => {});

    it(' Custom package class creation  - addSmartLink ', () => {
        const myCustomPackage = new xtremIntacct.classes.CustomPackage({
            name: 'myPackage',
            author: 'benoit',
            description: 'my custom customer smart event package',
            apiUser: 'apiUser',
        });

        myCustomPackage.addSmartLink({
            id: 'XTREM_CUSTOMER_CREATE',
            changeMade: 'create',
            events: ['add'],
            object: 'CUSTOMER',
            objectId: 'CUSTOMERID',
            isSynchronous: true,
        });

        assert.isNotEmpty(myCustomPackage.toString(false));
        assert.isNotEmpty(myCustomPackage.toString(true));
    });

    it(' Custom package class creation  - addSmarlinkCreateUpdateDelete ', () => {
        const myCustomPackage = new xtremIntacct.classes.CustomPackage({
            name: 'myPackage',
            author: 'benoit',
            description: 'my custom customer smart event package',
            apiUser: 'apiUser',
        });

        myCustomPackage.addSmarlinkCreateUpdateDelete({
            object: 'CUSTOMER',
            objectId: 'CUSTOMERID',
            isSynchronous: true,
        });
    });

    it(' Custom package class creation  - throw errors  ', () => {
        const myCustomPackage = new xtremIntacct.classes.CustomPackage({
            name: 'myPackage',
            author: 'benoit',
            description: 'my custom customer smart event package',
            apiUser: 'apiUser',
        });

        assert.throw(
            () => myCustomPackage.addSmartLink({ object: 'object', objectId: 'objectID' }),
            'No changeMade on IsmartLink - object',
        );

        assert.throw(
            () => myCustomPackage.addSmartLink({ object: 'object', objectId: 'objectID', changeMade: 'create' }),
            'No events on IsmartLink - XTREEM_object_CREATE',
        );
    });
});
