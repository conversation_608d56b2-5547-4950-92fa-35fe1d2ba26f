import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacct from '../../../index';

describe(' GetList Testing  ', () => {
    before(() => {});

    it(' GetList Request From SDK ', () =>
        Test.withContext(async context => {
            const lookup = new xtremIntacct.classes.sdk.Functions.GetList(
                context,
                {
                    synchronous: true,
                },
                'test',
            );

            assert.match(
                await lookup.xmlWithoutFunction(true),
                /<get_list object="test"\/>/gm,
                await lookup.xmlWithoutFunction(true),
            );
        }));
    // TODO : add an execution of testList
});
