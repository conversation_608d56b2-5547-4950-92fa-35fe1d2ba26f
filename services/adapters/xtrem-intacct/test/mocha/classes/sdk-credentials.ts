import { ClientConfig } from '@intacct/intacct-sdk';
import { assert } from 'chai';
import * as xtremIntacct from '../../../index';

describe(' Endpoint Class ', () => {
    before(() => {});

    it('should return the default endpoint', () => {
        const config = new ClientConfig();
        const endpoint = new xtremIntacct.classes.sdk.Credentials.Endpoint(config);

        assert.equal(endpoint.url, 'https://api.intacct.com/ia/xml/xmlgw.phtml');
    });

    it('should return the default endpoint if the endpoint URL from the config is null', () => {
        const config = new ClientConfig();
        config.endpointUrl = '';
        const endpoint = new xtremIntacct.classes.sdk.Credentials.Endpoint(config);

        assert.equal(endpoint.url, 'https://api.intacct.com/ia/xml/xmlgw.phtml');
    });

    it('grabs INTACCT_ENDPOINT_URL from the env', () => {
        process.env.INTACCT_ENDPOINT_URL = 'https://envunittest.intacct.com/ia/xml/xmlgw.phtml';
        const config = new ClientConfig();
        const endpoint = new xtremIntacct.classes.sdk.Credentials.Endpoint(config);
        assert.equal(endpoint.url, 'https://envunittest.intacct.com/ia/xml/xmlgw.phtml');
        process.env.INTACCT_ENDPOINT_URL = ''; // Reset the process.env
    });

    it('grab the endpoint URL from the config', () => {
        const config = new ClientConfig();
        config.endpointUrl = 'https://configtest.intacct.com/ia/xml/xmlgw.phtml';
        const endpoint = new xtremIntacct.classes.sdk.Credentials.Endpoint(config);

        assert.equal(endpoint.url, 'https://configtest.intacct.com/ia/xml/xmlgw.phtml');
    });

    it('should throw exception on invalid Endpoint URL', () => {
        assert.throws(
            () => {
                const config = new ClientConfig();
                config.endpointUrl = 'https://www.example.com/xmlgw.phtml';
                return new xtremIntacct.classes.sdk.Credentials.Endpoint(config);
            },
            Error,
            'Endpoint URL is not a valid intacct.com domain name.',
        );
    });

    it('should allow FQDN Endpoint URL', () => {
        const config = new ClientConfig();
        config.endpointUrl = 'https://api.intacct.com./ia/xml/xmlgw.phtml';
        const endpoint = new xtremIntacct.classes.sdk.Credentials.Endpoint(config);

        assert.equal(endpoint.url, 'https://api.intacct.com./ia/xml/xmlgw.phtml');
    });
});
