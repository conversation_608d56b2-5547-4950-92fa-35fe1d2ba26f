import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacct from '../../../index';

describe(' DefaultQuery  ', () => {
    before(() => {});

    it(' Throw businessRuleError ', () =>
        Test.withContext(context => {
            assert.throw(
                () => new xtremIntacct.classes.sdk.Functions.Query(context, { objectName: '' }),
                /^There is no object name to query.$/,
            );
            assert.throw(
                () => new xtremIntacct.classes.sdk.Functions.Query(context, { objectName: 'TEST' }),
                /^There are no fields to query on TEST.$/,
            );
        }));
});
