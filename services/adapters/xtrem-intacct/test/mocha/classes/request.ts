import * as IA from '@intacct/intacct-sdk';
import type { Context } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as XMLBuilder from 'xmlbuilder';
import * as xtremIntacct from '../../../index';

function getFakeClientConfig(): IA.ClientConfig {
    const fakeConfig = new IA.ClientConfig();
    fakeConfig.senderId = 'testsender';
    fakeConfig.senderPassword = 'testsendpass';
    fakeConfig.companyId = 'TestCompany';
    fakeConfig.userId = 'testsession..';
    fakeConfig.userPassword = 'testpass';
    return fakeConfig;
}
function getFakeRequestConfig() {
    const requestConfig = new IA.RequestConfig();
    requestConfig.controlId = '1234';
    return requestConfig;
}

describe(' Read by Name Function   ', () => {
    before(() => {});
    it(' Control Block ', () => {
        const controlBlock = new xtremIntacct.classes.sdk.Request.ControlBlock(
            {} as Context,
            getFakeClientConfig(),
            getFakeRequestConfig(),
        );
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));
        controlBlock.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><control><senderid>testsender</senderid><password>testsendpass</password><controlid>1234</controlid><uniqueid>false</uniqueid><dtdversion>3.0</dtdversion><includewhitespace>false</includewhitespace></control></request>',
        );
    });
    it(' Login  Block ', () => {
        const controlBlock = new xtremIntacct.classes.sdk.Request.LoginAuthentication(
            {} as Context,
            'testsession',
            'TestCompany',
            'testpass',
            'entityId',
        );
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));
        controlBlock.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><authentication><login><userid>testsession</userid><companyid>TestCompany</companyid><password>testpass</password><locationid>entityId</locationid></login></authentication></request>',
        );

        assert.throw(
            () =>
                new xtremIntacct.classes.sdk.Request.LoginAuthentication(
                    {} as Context,
                    'testsession',
                    'TestCompany',
                    '',
                    'entityId',
                ),
        );
        assert.throw(
            () =>
                new xtremIntacct.classes.sdk.Request.LoginAuthentication(
                    {} as Context,
                    '',
                    'TestCompany',
                    'testpass',
                    'entityId',
                ),
        );
        assert.throw(
            () =>
                new xtremIntacct.classes.sdk.Request.LoginAuthentication(
                    {} as Context,
                    'testsession',
                    '',
                    'testpass',
                    'entityId',
                ),
        );
    });
    it(' Operation  Block ', async () => {
        const operationBlock = new xtremIntacct.classes.sdk.Request.OperationBlock(
            {} as Context,
            getFakeClientConfig(),
            getFakeRequestConfig(),
            'operation',
        );
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));
        await operationBlock.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><operation transaction="false"><authentication><login><userid>testsession..</userid><companyid>TestCompany</companyid><password>testpass</password></login></authentication><content>operation</content></operation></request>',
        );
    });
    it(' Request  Block ', async () => {
        const requestBlock = new xtremIntacct.classes.sdk.Request.RequestBlock(
            {} as Context,
            getFakeClientConfig(),
            getFakeRequestConfig(),
            'operation',
        );
        assert.deepEqual(
            await requestBlock.writeXml(),
            '<?xml version="1.0" encoding="utf-8"?><request><control><senderid>testsender</senderid><password>testsendpass</password><controlid>1234</controlid><uniqueid>false</uniqueid><dtdversion>3.0</dtdversion><includewhitespace>false</includewhitespace></control><operation transaction="false"><authentication><login><userid>testsession..</userid><companyid>TestCompany</companyid><password>testpass</password></login></authentication><content>operation</content></operation></request>',
        );
    });
    it(' SessionAuthentication  Block ', () => {
        const sessionAuth = new xtremIntacct.classes.sdk.Request.SessionAuthentication('1234');
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));
        sessionAuth.writeXml(xml);
        assert.deepEqual(
            xml.flush(),
            '<?xml version="1.0"?><request><authentication><sessionid>1234</sessionid></authentication></request>',
        );
    });
});
