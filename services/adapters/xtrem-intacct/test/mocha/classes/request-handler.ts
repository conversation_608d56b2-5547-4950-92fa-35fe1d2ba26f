import * as IA from '@intacct/intacct-sdk';
import { Test } from '@sage/xtrem-core';
import * as xtremIntacct from '../../../index';

function getFakeRequestConfig() {
    const requestConfig = new IA.RequestConfig();
    requestConfig.controlId = '1234';
    return requestConfig;
}

function getFakeClientConfig(): IA.ClientConfig {
    const fakeConfig = new IA.ClientConfig();
    fakeConfig.senderId = 'testsender';
    fakeConfig.senderPassword = 'testsendpass';
    fakeConfig.companyId = 'TestCompany';
    fakeConfig.userId = 'testsession..';
    fakeConfig.userPassword = 'testpass';
    return fakeConfig;
}

describe(' Request Handler   ', () => {
    before(() => {});
    it(' Class request', () =>
        Test.withContext(
            async context => {
                const requestHandler = new xtremIntacct.classes.sdk.Request.RequestHandler(
                    context,
                    getFakeClientConfig(),
                    getFakeRequestConfig(),
                );

                await requestHandler.executeOnline(
                    new xtremIntacct.classes.sdk.Functions.Query(context, {
                        objectName: 'VENDOR',
                        fields: [
                            { name: 'GLGROUP', type: 'select' },
                            { name: 'GLGROUP', type: 'count' },
                        ],
                        controlID: '4321',
                    }),
                );
            },
            {
                mocks: ['axios'],
                scenario: 'request-handler',
                directory: __dirname,
            },
        ));
});
