import { SystemError } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacct from '../../../index';

/**
 * Without the execute for now, we need to mock the sdk part
 */
describe(' I Function Classes from SDK   ', () => {
    before(() => {});

    it('should set fieldName for filter', () => {
        const expected = `<?xml version="1.0" encoding="utf-8"?>
<test>
    <equalto>
        <field>CUSTOMERID</field>
        <value>1</value>
    </equalto>
</test>`;

        const filter = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('CUSTOMERID').equalTo('1');

        assert.equal(xtremIntacct.classes.sdk.Xml.XmlObjectTestHelper.CompareXml(filter), expected);
    });
    it('should throw error for missing fieldName for filter', () => {
        assert.throws(
            () => {
                new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('').equalTo('9');
            },
            SystemError,
            'fieldName is required for Filter.',
        );
    });
});
