import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import * as xtremIntacct from '../../../index';

const dirname = __dirname.replace('/build/', '/');

/**
 * Without the execute for now, we need to mock the sdk part
 */
describe(' Axios mocked query    ', () => {
    before(() => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-intacct-gateway': {
                    senderId: 'XT_mfg',
                    senderPassword: 'dummy',
                },
            },
        });
    });
    it('XmlQuery Ifunction Class execute', () =>
        Test.withContext(
            async context => {
                const xmlPath = path.resolve(dirname, 'xml-requests/vendor-query-where-glgroup.xml');
                const xmlQuery = fs.readFileSync(xmlPath);

                const iFunctionResult = await new xtremIntacct.classes.sdk.Functions.XmlQuery<{}[]>(
                    context,
                    xmlQuery.toString(),
                ).execute();

                assert.equal(iFunctionResult.length, 6, JSON.stringify(iFunctionResult));
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'xml-vendor-query-where',
                directory: __dirname,
            },
        ));
});
