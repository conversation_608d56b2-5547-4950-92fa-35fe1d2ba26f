import * as xtremCommunication from '@sage/xtrem-communication';
import { datetime, Test, TextStream } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacct from '../../../index';

describe(' DefaultQuery Ifunction & Abstract-function ', () => {
    before(() => {});

    it(' Asynchronous - CallBack Context ', () =>
        Test.withContext(async context => {
            const defaultQuery = new xtremIntacct.classes.sdk.Functions.DefaultQuery(context);
            const { lineId, ...defaultCallBackContext } = await defaultQuery.defaultCallBackContext;
            assert.isString(lineId);

            assert.deepEqual(
                defaultCallBackContext,
                {
                    function: 'defaultQuery',
                    parameters: '',
                    nodeName: '',
                    intacctIdName: '',
                    userId: (await context.user)!._id,
                    retry: 0,
                },
                JSON.stringify(await defaultQuery.defaultCallBackContext, null, 4),
            );
        }));
    it(' DefaultQuery - properties  ', () =>
        Test.withContext(async context => {
            const defaultQuery = new xtremIntacct.classes.sdk.Functions.DefaultQuery(context);
            assert.deepEqual(
                await (
                    await defaultQuery.intacctInstance
                ).id,
                'XTREM',
                await (
                    await defaultQuery.intacctInstance
                ).id,
            );
            /** False because there is no scenario associated */
            assert.deepEqual(
                defaultQuery.isUnitTest,
                false,
                `isUnitTest :${defaultQuery.isUnitTest ? 'true' : 'false'} `,
            );
            assert.equal(defaultQuery.options.synchronous, true, JSON.stringify(defaultQuery.options, null, 4));
            assert.isFunction(defaultQuery.options.resultFunction, JSON.stringify(defaultQuery.options, null, 4));

            assert.deepEqual(
                (await defaultQuery.client).config?.companyId,
                'Int-X3-WD',
                (await defaultQuery.client).config?.companyId,
            );
            assert.deepEqual(
                (await defaultQuery.client).config?.userId,
                'xtremapiAll',
                (await defaultQuery.client).config?.userId,
            );
        }));
    it(' DefaultQuery - unitTest For Async  ', () =>
        Test.withContext(
            context => {
                const defaultQuery = new xtremIntacct.classes.sdk.Functions.DefaultQuery(context);
                assert.deepEqual(
                    defaultQuery.isUnitTest,
                    true,
                    `isUnitTest :${defaultQuery.isUnitTest ? 'true' : 'false'} `,
                );
            },
            {
                scenario: 'test',
            },
        ));
    it(' DefaultQuery - managing Communication History   ', () =>
        Test.withContext(
            async context => {
                const randomControlID = 'randomControlID';
                const contextCallback: xtremIntacct.sharedFunctions.interfaces.ContextCallback = {
                    function: 'testing',
                    intacctIdName: 'toto',
                    nodeName: 'tata',
                    parameters: { _id: 1 },
                    userId: (await context.user)!._id,
                    retry: 0,
                };
                const defaultQuery = new xtremIntacct.classes.sdk.Functions.DefaultQuery(context);

                await defaultQuery.createMessageHistory(contextCallback, randomControlID);

                let historyLine = await context.read(xtremCommunication.nodes.SysMessageHistory, {
                    integrationSolution: 'intacct',
                    id: randomControlID,
                });

                assert.equal((await historyLine.sentRequest).toString(), '<request/>');
                assert.equal(await historyLine.status, 'notSent');
                assert.equal(await historyLine.context, contextCallback);
                assert.equal(await historyLine.integrationSolution, 'intacct');

                await defaultQuery.updateMessageHistory(randomControlID, {
                    status: 'notSent',
                    communicationDiagnoses: { error: 'unitTest' },
                });

                historyLine = await context.read(xtremCommunication.nodes.SysMessageHistory, {
                    integrationSolution: 'intacct',
                    id: randomControlID,
                });

                assert.deepEqual(await historyLine.communicationDiagnoses, { error: 'unitTest' });

                await defaultQuery.updateMessageHistory(randomControlID, {
                    status: 'sent',
                });

                historyLine = await context.read(xtremCommunication.nodes.SysMessageHistory, {
                    integrationSolution: 'intacct',
                    id: randomControlID,
                });
                assert.deepEqual(await historyLine.status, 'sent');
                assert.deepEqual(await historyLine.sendStamp, datetime.now());

                await defaultQuery.updateMessageHistory(randomControlID, {
                    receivedRequest: TextStream.fromString('<xml>Test</xml>', 'text/xml'),
                });

                historyLine = await context.read(xtremCommunication.nodes.SysMessageHistory, {
                    integrationSolution: 'intacct',
                    id: randomControlID,
                });
                assert.deepEqual(await historyLine.status, 'received');
                assert.deepEqual(await historyLine.receivedStamp, datetime.now());

                assert.deepEqual((await historyLine.receivedRequest).toString(), '<xml>Test</xml>');

                await defaultQuery.updateMessageHistory(randomControlID, {
                    status: 'success',
                });
            },
            { now: '2020-01-01T10:00:00' },
        ));
});
