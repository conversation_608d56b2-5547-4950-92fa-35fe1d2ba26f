{"request": {"url": "https://api.intacct.com/ia/xml/xmlgw.phtml", "method": "POST", "timeout": 30000, "data": "<?xml version=\"1.0\" encoding=\"utf-8\"?><request><control><senderid>testsender</senderid><uniqueid>false</uniqueid><dtdversion>3.0</dtdversion><includewhitespace>false</includewhitespace></control><operation transaction=\"false\"><authentication><login><userid>testsession..</userid><companyid>TestCompany</companyid></login></authentication><content><function><inspect><object>XTreeM_Audit_Trail</object></inspect></function></content></operation></request>", "headers": {"Content-Type": "application/xml", "Accept-Encoding": "gzip", "User-Agent": "intacct-sdk-js-client/xtrem-1.0.0"}}, "response": {"headers": {"isMock": true}, "status": 200, "data": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><response><control><status>success</status><senderid>X3_mfg</senderid><controlid>1633526616212</controlid><uniqueid>false</uniqueid><dtdversion>3.0</dtdversion></control><operation><authentication><status>success</status><userid>xtremapiAll</userid><companyid>Int-X3-WD</companyid><locationid></locationid><sessiontimestamp>2021-10-06T13:23:36+00:00</sessiontimestamp><sessiontimeout>2021-10-07T01:23:36+00:00</sessiontimeout></authentication><result><status>success</status><function>inspect</function><controlid>cba6a905-c53a-430e-b6e4-fc9bea3f8074</controlid><data listtype=\"All\" count=\"1\"><Type Name=\"xtreem_audit_trail\"><Fields><Field>name</Field><Field>comment</Field><Field>createdBy</Field><Field>createdAt</Field><Field>updatedBy</Field><Field>updatedAt</Field><Field>id</Field><Field>RECORD_URL</Field><Field>object_name</Field><Field>change_made</Field><Field>record_id</Field><Field>record_number</Field></Fields></Type></data></result></operation></response>"}}