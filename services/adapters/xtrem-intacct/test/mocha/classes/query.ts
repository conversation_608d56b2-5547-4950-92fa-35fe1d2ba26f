import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacct from '../../../index';

describe('Test Query class  ', () => {
    before(() => {});

    it(' Query item on entityId 100 with showPrivate true  ', () =>
        Test.withContext(
            async context => {
                const filter = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('ITEMID').equalTo('0001');
                const defaultQuery = new xtremIntacct.classes.sdk.Functions.Query(context, {
                    entityId: '100',
                    objectName: 'ITEM',
                    fields: ['ITEMID', 'RECORDNO'],
                    filter,
                });
                defaultQuery.assignShowPrivate(true);

                const result = (await defaultQuery.execute()) as { ITEMID: string; RECORDNO: number }[];
                assert.isArray(result);
                assert.equal(result.length, 1);
            },
            {
                scenario: 'request-item',
                directory: __dirname,
            },
        ));
    it(' Query item on entityId 100 ', () =>
        Test.withContext(
            async context => {
                const entityQuery = new xtremIntacct.classes.sdk.Functions.Query(context, {
                    entityId: '100',
                    objectName: 'LOCATIONENTITY',
                    fields: ['LOCATIONID', 'NAME'],
                    resultFunction: result =>
                        result.data.map(field => {
                            return {
                                name: field.LOCATIONID,
                                locationId: field.NAME,
                            };
                        }),
                });
                const { client } = entityQuery;
                const { config } = await client;

                assert.equal(config?.entityId, '100');

                const entityQueryResult = await entityQuery.execute();

                assert.isArray(entityQueryResult);
                assert.equal(entityQueryResult.length, 1);
            },
            {
                scenario: 'query-entity-100',
                directory: __dirname,
            },
        ));
});
