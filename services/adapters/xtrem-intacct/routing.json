{"@sage/xtrem-intacct": [{"topic": "Intacct/asyncExport/start", "queue": "import-export", "sourceFileName": "intacct.ts"}, {"topic": "Intacct/installXtreemAuditTrail/start", "queue": "intacct", "sourceFileName": "intacct.ts"}, {"topic": "IntacctLine/asyncExport/start", "queue": "import-export", "sourceFileName": "intacct-line.ts"}, {"topic": "IntacctNode/asyncExport/start", "queue": "import-export", "sourceFileName": "intacct-node.ts"}, {"topic": "IntacctOptionManagement/asyncExport/start", "queue": "import-export", "sourceFileName": "intacct-option-management.ts"}]}