import type { ValidationContext } from '@sage/xtrem-core';
import { uniq } from 'lodash';
import type * as xtremIntacct from '../../index';

/** There are duplicate countries. Make sure that each line has a unique country. */
export async function checkDuplicateCountries(intacct: xtremIntacct.nodes.Intacct, cx: ValidationContext) {
    // check if there are no duplicate entries for a country in the lines
    const countryIds: string[] = uniq(
        await intacct.lines
            .map(async line => {
                return (await line.country).id;
            })
            .toArray(),
    );
    if (countryIds.length !== (await intacct.lines.length)) {
        cx.error.addLocalized(
            '@sage/xtrem-intacct/nodes__intacct__duplicate_countries',
            'There are duplicate countries. Make sure that each line has a unique country.',
        );
    }
}
