import type { Graph<PERSON>pi, Intacct } from '@sage/xtrem-intacct-api';
import type { OptionManagementBase as OptionManagementBasePage } from '@sage/xtrem-structure/build/lib/pages/option-management-base';
import * as ui from '@sage/xtrem-ui';
import { intacctActivationDeactivationMessage, legislationIdsWithApArPosting } from '../client-functions/common';

@ui.decorators.pageExtension<OptionManagementBaseExtension>({
    extends: '@sage/xtrem-structure/OptionManagementBase',
    async onLoad() {
        await this.checkIntacct();
        this.numberOfSaveAfter += 1;

        if (this.isIntacctServiceOptionActiveBoolean) {
            this.defaultInstance = (await this.$.graph
                .node('@sage/xtrem-intacct/Intacct')
                .queries.defaultInstance({ _id: true, companyId: true, userId: true }, false)
                .execute()) as Intacct;
            if (this.defaultInstance) {
                this.intacctInfo.value = ui.localize(
                    '@sage/xtrem-intacct/connectedIntacctInstance',
                    'Intacct is connected to {{intacctCompanyId}} with user {{intacctUser}}',
                    {
                        intacctCompanyId: this.defaultInstance.companyId,
                        intacctUser: this.defaultInstance.userId,
                    },
                );
            } else {
                this.intacctInfo.value = ui.localize(
                    '@sage/xtrem-intacct/disconnected',
                    'Intacct not active or disconnected',
                );
            }
        }
    },
})
export class OptionManagementBaseExtension extends ui.PageExtension<OptionManagementBasePage, GraphApi> {
    @ui.decorators.block<OptionManagementBaseExtension>({
        parent() {
            return this.financeSection;
        },
        title: 'Intacct',
        width: 'small',
    })
    intacctBlock: ui.containers.Block;

    @ui.decorators.switchField<OptionManagementBaseExtension>({
        isTransient: true,
        parent() {
            return this.intacctBlock;
        },
        title: 'Active',
    })
    isIntacctServiceOptionActive: ui.fields.Switch;

    @ui.decorators.linkField<OptionManagementBaseExtension>({
        isTransient: true,
        isHidden() {
            return !this.isIntacctServiceOptionActiveBoolean;
        },
        parent() {
            return this.intacctBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-intacct/configuration-page', 'Configuration page');
        },
        page: '@sage/xtrem-intacct/Intacct',
        title: 'Sage Intacct configuration page',
        isTitleHidden: true,
    })
    intacctConfigurationPage: ui.fields.Link;

    isIntacctServiceOptionActiveBoolean: boolean;

    defaultInstance: Intacct;

    async checkIntacct() {
        this.isIntacctServiceOptionActiveBoolean =
            (await this.$.graph
                .node('@sage/xtrem-intacct/IntacctOptionManagement')
                .queries.isServiceOptionActiveFunction(true, false)
                .execute()) || false;
        this.isIntacctServiceOptionActive.value = this.isIntacctServiceOptionActiveBoolean;

        this.intacctConfigurationPage.isHidden = !this.isIntacctServiceOptionActiveBoolean;
        this.intacctInfo.isHidden = !this.isIntacctServiceOptionActiveBoolean;
    }

    async intacctSaveOverload() {
        if (this.isIntacctServiceOptionActive.value !== this.isIntacctServiceOptionActiveBoolean) {
            if (!this.isIntacctServiceOptionActive.value && this.defaultInstance?._id) {
                await this.$.graph
                    .node('@sage/xtrem-intacct/Intacct')
                    .updateById(
                        { id: true },
                        { _id: this.defaultInstance._id, data: { _id: this.defaultInstance._id, isActive: false } },
                    )
                    .execute();
            }
            await intacctActivationDeactivationMessage({
                page: this,
                isActive: this.isIntacctServiceOptionActive.value || false,
                legislationIds: legislationIdsWithApArPosting,
            });
            await this.$.graph
                .node('@sage/xtrem-intacct/IntacctOptionManagement')
                .mutations.serviceOptionChange(true, true)
                .execute();
        }
    }

    @ui.decorators.pageActionOverride<OptionManagementBaseExtension>({
        /** https://jira.sage.com/browse/XT-77852 */
        onClickAfter() {
            (async () => {
                await this.intacctSaveOverload();
                await this.checkIntacct();
                this.$.setPageClean();
                await this.hardRefresh();
            })().catch(e => ui.console.error(e));
        },
    })
    save: ui.PageAction;

    @ui.decorators.textField<OptionManagementBaseExtension>({
        isTransient: true,
        parent() {
            return this.intacctBlock;
        },
        width: 'large',
        title: 'Info',
        isReadOnly: true,
        isHidden() {
            return !this.isIntacctServiceOptionActiveBoolean;
        },
    })
    intacctInfo: ui.fields.Text;
}

declare module '@sage/xtrem-structure/build/lib/pages/option-management-base' {
    interface OptionManagementBase extends OptionManagementBaseExtension {}
}
