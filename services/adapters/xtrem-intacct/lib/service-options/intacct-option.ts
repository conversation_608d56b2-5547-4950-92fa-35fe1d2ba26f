import { ServiceOption } from '@sage/xtrem-core';
import { IntacctOptionHooks } from '../classes/intacct-hooks';

export const intacctOption = new ServiceOption({
    __filename,
    status: 'released',
    description: 'Sage Intacct integration option',
    isSubscribable: false,
    isHidden: true,
    isActiveByDefault: true,
    onEnabled(context) {
        return IntacctOptionHooks.onEnabled(context);
    },
    onDisabled(context) {
        return IntacctOptionHooks.onDisabled(context);
    },
});
