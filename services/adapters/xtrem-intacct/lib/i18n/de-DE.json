{"@sage/xtrem-intacct/activity__intacct__name": "Sage Intacct", "@sage/xtrem-intacct/activity__intacct_option_management__name": "Optionsverwaltung Sage Intacct", "@sage/xtrem-intacct/asynchronous_test": "Asynchroner Test", "@sage/xtrem-intacct/audit-trail": "Audittrail Sage DMO", "@sage/xtrem-intacct/audit-trail-install": "Möchten Sie den Audittrail Sage DMO installieren oder aktualisieren?", "@sage/xtrem-intacct/audit-trail-install-failed": "Installation Audittrail Sage DMO fehlgeschlagen.", "@sage/xtrem-intacct/audit-trail-install-success": "Audittrail Sage DMO installiert.", "@sage/xtrem-intacct/audit-trail-mismatch": "Konflikt Audittrail Sage DMO", "@sage/xtrem-intacct/audit-trail-update": "Möchten Sie den Audittrail Sage DMO aktualisieren?\n                 Von: {{from}}\n                 Nach: {{to}}", "@sage/xtrem-intacct/client_functions__common__ap_ar_posting_to_gl_activation": "Sie sind dabei, die Buchung von Eingangs-/Ausgangsrechnungen in das Hauptbuch für Unternehmen {{companies}} zu aktivieren.  Sie müssen die Steuerverwaltung auf der zugehörigen Kontenliste überprüfen.", "@sage/xtrem-intacct/client_functions__common__ap_ar_posting_to_gl_deactivation": "<PERSON>e sind dabei, die Buchung von Eingangs-/Ausgangsrechnungen in das Hauptbuch für Unternehmen {{companies}} zu deaktivieren.", "@sage/xtrem-intacct/client_functions__common__information": "Informationen", "@sage/xtrem-intacct/company-id-required": "Einstellungen Integration Sage Intacct: Eine Unternehmens-ID ist erforderlich und kann nicht leer sein.", "@sage/xtrem-intacct/configuration-page": "Konfigurationsseite", "@sage/xtrem-intacct/connectedIntacctInstance": "Sage Intacct ist mit {{intacctCompanyId}} mit dem Benutzer {{intacctUser}} verbunden.", "@sage/xtrem-intacct/data_types__intacct_company_id_data_type__name": "Datentyp ID Unternehmen Intacct", "@sage/xtrem-intacct/data_types__intacct_property_data_type__name": "Datentyp Eigenschaft Intacct", "@sage/xtrem-intacct/data_types__intacct_record_name_data_type__name": "Datentyp Datensatzname Intacct", "@sage/xtrem-intacct/data_types__intacct_session_id_data_type__name": "Datentyp Sitzungs-ID Intacct", "@sage/xtrem-intacct/data_types__listener_status_enum__name": "Enum Status Listener", "@sage/xtrem-intacct/data_types__property_type_enum__name": "Enum Typ Eigenschaft", "@sage/xtrem-intacct/data_types__request_type_enum__name": "<PERSON><PERSON>", "@sage/xtrem-intacct/data_types__transaction_integration_level_enum__name": "Enum Ebene Integration Transaktion", "@sage/xtrem-intacct/data_types__type_field_enum__name": "<PERSON><PERSON>", "@sage/xtrem-intacct/disconnected": "Sage Intacct ist inaktiv oder nicht verbunden.", "@sage/xtrem-intacct/enums__field_type__average": "Durchschnitt", "@sage/xtrem-intacct/enums__field_type__count": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__field_type__max": "<PERSON>.", "@sage/xtrem-intacct/enums__field_type__min": "<PERSON>.", "@sage/xtrem-intacct/enums__field_type__select": "Auswählen", "@sage/xtrem-intacct/enums__field_type__sum": "Gesamt", "@sage/xtrem-intacct/enums__listener_status__done": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__listener_status__error": "<PERSON><PERSON>", "@sage/xtrem-intacct/enums__listener_status__fail": "<PERSON><PERSON>", "@sage/xtrem-intacct/enums__listener_status__notImplentedYet": "Noch nicht implementiert", "@sage/xtrem-intacct/enums__listener_status__received": "Eingegangen", "@sage/xtrem-intacct/enums__listener_status__toDelete": "<PERSON><PERSON>", "@sage/xtrem-intacct/enums__property_type__description": "Bezeichnung", "@sage/xtrem-intacct/enums__property_type__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct/enums__property_type__name": "Name", "@sage/xtrem-intacct/enums__property_type__none": "<PERSON><PERSON>", "@sage/xtrem-intacct/enums__property_type__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__property_type__text": "Text", "@sage/xtrem-intacct/enums__request_type__create": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__request_type__createUpdate": "Erstellen/Aktualisieren", "@sage/xtrem-intacct/enums__request_type__delete": "Löschen", "@sage/xtrem-intacct/enums__request_type__read": "<PERSON><PERSON>", "@sage/xtrem-intacct/enums__request_type__update": "Aktualisieren", "@sage/xtrem-intacct/enums__transaction_definition_type__order": "Auftrag", "@sage/xtrem-intacct/enums__transaction_definition_type__receipt": "Wareneingang", "@sage/xtrem-intacct/enums__transaction_definition_type__receiptClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__transaction_definition_type__returnReceiptInvoiced": "Eingang Retoure faktur<PERSON>t", "@sage/xtrem-intacct/enums__transaction_definition_type__returnReceiptNotInvoiced": "Eingang Retoure nicht fakturiert", "@sage/xtrem-intacct/enums__transaction_integration_level__entityLevel": "Entitätsebene", "@sage/xtrem-intacct/enums__transaction_integration_level__topLevel": "Obere Ebene", "@sage/xtrem-intacct/enums__type_field__average": "Durchschnitt", "@sage/xtrem-intacct/enums__type_field__count": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__type_field__max": "Max", "@sage/xtrem-intacct/enums__type_field__min": "<PERSON>.", "@sage/xtrem-intacct/enums__type_field__select": "Auswählen", "@sage/xtrem-intacct/enums__type_field__sum": "Summe", "@sage/xtrem-intacct/install-intacct": "Sage Intacct-Anwendung installiert: {{info}}", "@sage/xtrem-intacct/menu_item__intacct": "Sage Intacct", "@sage/xtrem-intacct/menu_item__intacct-config": "Konfiguration Intacct", "@sage/xtrem-intacct/menu_item__intacct-transaction": "Transaktionen Intacct", "@sage/xtrem-intacct/no-fields-query": "Es gibt keine abzufragenden Felder für {{objectName}}.", "@sage/xtrem-intacct/no-intacct-instance": "Keine aktive Sage Intacct-Instanz gefunden.", "@sage/xtrem-intacct/no-object-query": "Es gibt keinen abzufragenden Objektnamen.", "@sage/xtrem-intacct/no-xtreem_audit_trail-template": "Keine xtremAuditTrail-Dateivorlage in {{path}}. Überprüfen Sie den Dateipfad.", "@sage/xtrem-intacct/node-extensions__user_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct/node-extensions__user_extension__property__isIntacct": "Sage Intacct", "@sage/xtrem-intacct/node-extensions__user_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail": "Audittrail xtreem installieren", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail__failed": "Audittrail xtreem installieren fehlgeschlagen.", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail__parameter__intacctInstanceId": "Instanz-ID Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__duplicate_countries": "<PERSON>s gibt do<PERSON> Lä<PERSON>. <PERSON><PERSON><PERSON>, dass jede Zeile ein eindeutiges Land hat.", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous": "Asynchrone Entitäten abrufen", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous__failed": "Asynchrone Entitäten abrufen fehlgeschlagen.", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous__parameter__intacctInstanceId": "Instanz-ID Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__mutation__installXtreemAuditTrail": "Audittrail Xtreem installieren", "@sage/xtrem-intacct/nodes__intacct__mutation__installXtreemAuditTrail__parameter__intacctInstanceId": "Instanz-ID Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities": "Entitäten aktualisieren", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities__failed": "Entitäten aktualisieren fehlgeschlagen.", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities__parameter__intacctInstanceId": "Instanz-ID Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__mutation__retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/nodes__intacct__mutation__retry__failed": "Wiederholen fehlgeschlagen.", "@sage/xtrem-intacct/nodes__intacct__mutation__retry__parameter__sysMessageHistoryId": "ID Systemmeldungsverlauf", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent": "Event senden", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__failed": "Event senden fehlgeschlagen.", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__dataBase64": "Datenbank 64", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__dataIntacctEvent": "Sage Intacct Datenevent", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__messageKind": "Meldungsar<PERSON>", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest": "XML-Anforderung senden", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__failed": "XML-Anforderung senden fehlgeschlagen.", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__entityId": "Entitäts-ID", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__isSynchronous": "Synchron", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__xmlQuery": "XML-Abfrage", "@sage/xtrem-intacct/nodes__intacct__node_name": "Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__property__asyncTimeout": "Asynchroner Timeout", "@sage/xtrem-intacct/nodes__intacct__property__chartOfAccount": "Kontenrahmen", "@sage/xtrem-intacct/nodes__intacct__property__companyId": "Unternehmens-ID", "@sage/xtrem-intacct/nodes__intacct__property__controlIdTableName": "Tabellenname Prüf-ID", "@sage/xtrem-intacct/nodes__intacct__property__country": "Land", "@sage/xtrem-intacct/nodes__intacct__property__endpointUrl": "Endpoint-URL", "@sage/xtrem-intacct/nodes__intacct__property__entityId": "Entitäts-ID", "@sage/xtrem-intacct/nodes__intacct__property__entityList": "Entitätsliste", "@sage/xtrem-intacct/nodes__intacct__property__id": "ID", "@sage/xtrem-intacct/nodes__intacct__property__infoXTreeMAuditTrail": "Information Audittrail Sage DMO", "@sage/xtrem-intacct/nodes__intacct__property__isActive": "Aktiv", "@sage/xtrem-intacct/nodes__intacct__property__isCategoryNameClean": "Ist Kategoriename löschen", "@sage/xtrem-intacct/nodes__intacct__property__isContactListCleaned": "Ist Kontaktliste gelöscht", "@sage/xtrem-intacct/nodes__intacct__property__isDisplayContactHidden": "Ist Kontaktanzeige verborgen", "@sage/xtrem-intacct/nodes__intacct__property__isFullFilledAuthentification": "Authentifizierung vollständig ausgefüllt", "@sage/xtrem-intacct/nodes__intacct__property__isRequestAsynchronous": "Ist Anforderung asynchron", "@sage/xtrem-intacct/nodes__intacct__property__isXTreeMAuditTrail": "Audittrail Sage DMO", "@sage/xtrem-intacct/nodes__intacct__property__legislation": "Rechtsordnung", "@sage/xtrem-intacct/nodes__intacct__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/nodes__intacct__property__messageHistory": "Meldungsver<PERSON><PERSON>", "@sage/xtrem-intacct/nodes__intacct__property__mustXtreemAuditTrail": "Muss Audittrail Sage DMO sein", "@sage/xtrem-intacct/nodes__intacct__property__policyId": "Richtlinien-ID", "@sage/xtrem-intacct/nodes__intacct__property__senderId": "Absender-ID", "@sage/xtrem-intacct/nodes__intacct__property__senderPassword": "Passwort Absender", "@sage/xtrem-intacct/nodes__intacct__property__sessionExpiration": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/nodes__intacct__property__sessionId": "Sitzungs-ID", "@sage/xtrem-intacct/nodes__intacct__property__taxCategory": "Steuerkategorie", "@sage/xtrem-intacct/nodes__intacct__property__taxSolution": "Steuerlösung", "@sage/xtrem-intacct/nodes__intacct__property__transactionIntegrationLevel": "Ebene Integration Transaktion", "@sage/xtrem-intacct/nodes__intacct__property__userId": "Benutzer-ID", "@sage/xtrem-intacct/nodes__intacct__property__userPassword": "Passwort <PERSON>utzer", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance": "Standardinstanz", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance__failed": "Standardinstanz fehlgeschlagen.", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance__parameter__isThrowing": "Ausgeben", "@sage/xtrem-intacct/nodes__intacct__query__getEntities": "Entitäten abrufen", "@sage/xtrem-intacct/nodes__intacct__query__getEntities__failed": "Entitäten abrufen fehlgeschlagen.", "@sage/xtrem-intacct/nodes__intacct__query__getEntities__parameter__intacctInstanceId": "Instanz-ID Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct/nodes__intacct_line__error_no_tax_solution": "<PERSON>e können kein Land ohne Steuerlösung auswählen.", "@sage/xtrem-intacct/nodes__intacct_line__node_name": "Zeile Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_line__property__country": "Land", "@sage/xtrem-intacct/nodes__intacct_line__property__intacct": "Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_line__property__taxCategory": "Steuerkategorie", "@sage/xtrem-intacct/nodes__intacct_line__property__taxSolution": "Steuerlösung", "@sage/xtrem-intacct/nodes__intacct_node__node_name": "Node Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_node_state__node_name": "Status Node Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_node_state__property__intacctConfiguration": "Konfiguration Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_node_state__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_node_state__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct/nodes__intacct_option_management__mutation__serviceOptionChange": "Änderung Dienstoption", "@sage/xtrem-intacct/nodes__intacct_option_management__mutation__serviceOptionChange__failed": "Änderung Dienstoption fehlgeschlagen.", "@sage/xtrem-intacct/nodes__intacct_option_management__node_name": "Optionsverwaltung Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_option_management__query__isServiceOptionActiveFunction": "Funktion Dienstoption aktiv", "@sage/xtrem-intacct/nodes__intacct_option_management__query__isServiceOptionActiveFunction__failed": "Ist Funktion Dienstoption aktiv fehlgeschlagen.", "@sage/xtrem-intacct/nodes__intacct/to-many-retry": "<PERSON><PERSON> viele Versuche: {{retry}}", "@sage/xtrem-intacct/notAsynchronousFunction": "'Call Api' ist eine synchrone Funktion, bitte 'aCallAPI' verwenden", "@sage/xtrem-intacct/notSynchronousFunction": "'asynchronous Call' ist eine asynchrone Funktion, bitte 'Call' verwenden", "@sage/xtrem-intacct/only-one-intacct-instance": "<PERSON>s kann nur eine Sage Intacct-Instanz aktiv sein.", "@sage/xtrem-intacct/package__name": "Konfiguration Sage Intacct", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctConfigurationPage____title": "Konfigurationsseite Sage Intacct", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctInfo____title": "Info", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__isIntacctServiceOptionActive____title": "Aktiv", "@sage/xtrem-intacct/pages__intacct____navigationPanel__listItem__line2__title": "Unternehmens-ID", "@sage/xtrem-intacct/pages__intacct____navigationPanel__listItem__titleRight__title": "Aktiv", "@sage/xtrem-intacct/pages__intacct____objectTypePlural": "Konfigurationen Sage Intacct", "@sage/xtrem-intacct/pages__intacct____objectTypeSingular": "Konfiguration Sage Intacct", "@sage/xtrem-intacct/pages__intacct____title": "Konfiguration", "@sage/xtrem-intacct/pages__intacct__addLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__asynchronousTest____title": "Asynchroner Test", "@sage/xtrem-intacct/pages__intacct__auditTrailBlock____title": "Audittrail", "@sage/xtrem-intacct/pages__intacct__cancel": "Abbrechen", "@sage/xtrem-intacct/pages__intacct__change____title": "Änderung", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____lookupDialogTitle": "Kontenrahmen auswählen", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____placeholder": "Kontenrahmen auswählen", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____title": "Kontenrahmen", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title": "", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__attributes": "Attribute", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__communicationDiagnoses": "Diagnosen", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__context": "Kontext", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__nodeId": "Node-ID", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__nodeName": "Node-Name", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__receivedStamp": "Eingegangen", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__sendStamp": "Gesendet", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__sentId": "ID", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__status": "Status", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__userEmail": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__communicationHistoryBlock____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__communicationSection____title": "Kommunikation", "@sage/xtrem-intacct/pages__intacct__companyId____title": "Unternehmens-ID", "@sage/xtrem-intacct/pages__intacct__configurationBlock____title": "Konfiguration", "@sage/xtrem-intacct/pages__intacct__confirm_update": "Änderungen ausführen", "@sage/xtrem-intacct/pages__intacct__contactListBlock____title": "Kontakt", "@sage/xtrem-intacct/pages__intacct__country____columns__title__id": "ID", "@sage/xtrem-intacct/pages__intacct__country____columns__title__legislation___id": "Rechtsordnung", "@sage/xtrem-intacct/pages__intacct__country____columns__title__name": "", "@sage/xtrem-intacct/pages__intacct__country____lookupDialogTitle": "", "@sage/xtrem-intacct/pages__intacct__country____placeholder": "", "@sage/xtrem-intacct/pages__intacct__country____title": "", "@sage/xtrem-intacct/pages__intacct__dataToSend____title": "Daten senden", "@sage/xtrem-intacct/pages__intacct__defaultBlock____title": "Standard", "@sage/xtrem-intacct/pages__intacct__defaultSection____title": "Standard", "@sage/xtrem-intacct/pages__intacct__detailPanelCommunicationHeaderSection____title": "Kommunikation", "@sage/xtrem-intacct/pages__intacct__detailPanelDiagnoseBlock____title": "Details Diagnosen", "@sage/xtrem-intacct/pages__intacct__detailPanelDiagnoseSection____title": "Diagnosen", "@sage/xtrem-intacct/pages__intacct__detailPanelGeneralBlock____title": "Allgemein", "@sage/xtrem-intacct/pages__intacct__detailPanelReceiveGridRowBlock____title": "Empfangene Infodetails", "@sage/xtrem-intacct/pages__intacct__detailPanelReceiveSection____title": "Eingegangen", "@sage/xtrem-intacct/pages__intacct__detailPanelSendBlock____title": "Block senden", "@sage/xtrem-intacct/pages__intacct__detailPanelSendGridRowBlock____title": "Infodetails senden", "@sage/xtrem-intacct/pages__intacct__detailPanelSendSection____title": "Gesendet", "@sage/xtrem-intacct/pages__intacct__duplicate_error": "<PERSON>s gibt do<PERSON> Lä<PERSON>. <PERSON><PERSON><PERSON>, dass jede Zeile ein eindeutiges Land hat.", "@sage/xtrem-intacct/pages__intacct__endpointUrl____title": "Endpoint-URL", "@sage/xtrem-intacct/pages__intacct__entitites____title": "Entitäten", "@sage/xtrem-intacct/pages__intacct__eventBlock____title": "Ereignisse. Test SQS-Queues", "@sage/xtrem-intacct/pages__intacct__eventSection____title": "API", "@sage/xtrem-intacct/pages__intacct__historicBlock____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__id____title": "ID", "@sage/xtrem-intacct/pages__intacct__infoBox____title": "Testbox", "@sage/xtrem-intacct/pages__intacct__infoXTreeMAuditTrail____title": "Info", "@sage/xtrem-intacct/pages__intacct__install_xtreem_audit_trail_button_text": "Audittrail Sage DMO installieren oder aktualisieren", "@sage/xtrem-intacct/pages__intacct__isActive____title": "Aktiv", "@sage/xtrem-intacct/pages__intacct__isCategoryNameClean____title": "Kategoriename Kontaktliste", "@sage/xtrem-intacct/pages__intacct__isContactListCleaned____title": "Kontaktliste zurücksetzen", "@sage/xtrem-intacct/pages__intacct__isDisplayContactHidden____title": "Kontaktanzeige aus Kontaktliste ausschließen", "@sage/xtrem-intacct/pages__intacct__isFullFilledAuthentification____title": "Authentifizierung vollständig ausgefüllt", "@sage/xtrem-intacct/pages__intacct__isSynchronous____title": "Synchron", "@sage/xtrem-intacct/pages__intacct__isXTreeMAuditTrail____title": "Audittrail Sage DMO", "@sage/xtrem-intacct/pages__intacct__legislation____lookupDialogTitle": "Rechtsordnung auswählen", "@sage/xtrem-intacct/pages__intacct__legislation____placeholder": "Auswählen ...", "@sage/xtrem-intacct/pages__intacct__lines____columns__columns__country__title": "ISO 3166-1 Alpha-2", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__country__taxSolution__name": "Steuerlösung SDMO", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__taxCategory__name": "Steuerkategorie", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__taxSolution": "Steuerlösung Sage Intacct", "@sage/xtrem-intacct/pages__intacct__lines____dropdownActions__title": "Zeile löschen", "@sage/xtrem-intacct/pages__intacct__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__allDiagnose": "Diagnosen", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__attributes": "Attribute", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__payload": "Payload", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__status": "Status", "@sage/xtrem-intacct/pages__intacct__mainBlock____title": "Konfiguration", "@sage/xtrem-intacct/pages__intacct__mainSection____title": "Allgemein", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__attributes": "Attribute", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__communicationDiagnoses": "Diagnosen", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__context": "Kontext", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__context__2": "Kontext", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__receivedStamp": "Eingegangen", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__sendStamp": "Gesendet", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__status": "Status", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__messageHistory____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__messageHistory____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__messageHistoryBlock____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__messageKind____title": "Meldungstyp", "@sage/xtrem-intacct/pages__intacct__mustXtreemAuditTrail____title": "Info", "@sage/xtrem-intacct/pages__intacct__objectEvent____title": "Objekt", "@sage/xtrem-intacct/pages__intacct__policyId____title": "Richtlinien-ID", "@sage/xtrem-intacct/pages__intacct__purgeCommunicationData____title": "Löschen", "@sage/xtrem-intacct/pages__intacct__purgeData____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__querySection____title": "API: Abfrage", "@sage/xtrem-intacct/pages__intacct__recordId____title": "Datensatz-ID", "@sage/xtrem-intacct/pages__intacct__recordNumber____title": "Datensatznummer", "@sage/xtrem-intacct/pages__intacct__refresh_entity_list": "Entitäten aktualisieren", "@sage/xtrem-intacct/pages__intacct__refreshCommunicationData____title": "Aktualisieren", "@sage/xtrem-intacct/pages__intacct__refreshData____title": "Daten aktualisieren", "@sage/xtrem-intacct/pages__intacct__request____title": "XML-Anforderung", "@sage/xtrem-intacct/pages__intacct__response____title": "XML-Antwort", "@sage/xtrem-intacct/pages__intacct__save____title": "Speichern", "@sage/xtrem-intacct/pages__intacct__saveAction____title": "Speichern", "@sage/xtrem-intacct/pages__intacct__send_data_button_text": "Senden", "@sage/xtrem-intacct/pages__intacct__send_request_button_text": "Senden", "@sage/xtrem-intacct/pages__intacct__senderId____title": "Absender-ID", "@sage/xtrem-intacct/pages__intacct__synchronousTest____title": "Synchroner Test", "@sage/xtrem-intacct/pages__intacct__sysCommunicationSection____title": "Listener", "@sage/xtrem-intacct/pages__intacct__taxCategory____columns__title__name": "Name", "@sage/xtrem-intacct/pages__intacct__taxCategory____lookupDialogTitle": "Steuerkategorie auswählen", "@sage/xtrem-intacct/pages__intacct__taxCategory____placeholder": "Steuerkategorie auswählen", "@sage/xtrem-intacct/pages__intacct__taxCategory____title": "Steuerkategorie", "@sage/xtrem-intacct/pages__intacct__taxSolution____title": "Steuerlösung", "@sage/xtrem-intacct/pages__intacct__transactionIntegrationLevel____title": "Ebene Integration Transaktion", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level": "<PERSON><PERSON> wird em<PERSON><PERSON>, {{num}} nicht gebuchte Transaktionen abzuschließen, um Fehler zu vermeiden. Abbrechen, um nicht gebuchte Transaktionen abzuschließen oder Änderungen nur auf zukünftige Buchungen anzuwenden.", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level_success": "Transaktionen wurden bereits in Sage Intacct gebucht. Änderungen nur auf zukünftige Buchungen anwenden führt zu Inkonsistenzen mit früheren Buchungen.", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level_title": "Buchungseinstellungen ändern", "@sage/xtrem-intacct/pages__intacct__userId____title": "Benutzer-ID", "@sage/xtrem-intacct/pages__intacct__userPassword____title": "Passwort-ID", "@sage/xtrem-intacct/pages__template_panel____title": "Transaktionsvorlage ", "@sage/xtrem-intacct/pages__template_panel__cancel____title": "Abbrechen", "@sage/xtrem-intacct/pages__template_panel__confirm____title": "OK", "@sage/xtrem-intacct/pages__template_panel__description____title": "Bezeichnung", "@sage/xtrem-intacct/pages__template_panel__name____title": "Name", "@sage/xtrem-intacct/pages__transaction_definition____title": "Einstellungen Transaktionsdefinition", "@sage/xtrem-intacct/pages__transaction_definition__purchase____columns__title": "Name", "@sage/xtrem-intacct/pages__transaction_definition__purchase____title": "Einkaufstransaktion", "@sage/xtrem-intacct/pages__transaction_definition__saveAction____title": "Speichern", "@sage/xtrem-intacct/pages__transaction_definition__transactionBlock____title": "Transaktionen", "@sage/xtrem-intacct/pages__transaction_definition__transactionSection____title": "Transaktionen", "@sage/xtrem-intacct/pages-confirm-cancel": "Abbrechen", "@sage/xtrem-intacct/pages-confirm-no": "<PERSON><PERSON>", "@sage/xtrem-intacct/pages-confirm-purge": "Löschen", "@sage/xtrem-intacct/pages-confirm-yes": "<PERSON>a", "@sage/xtrem-intacct/permission__create__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/permission__default_instance__name": "Standardinstanz", "@sage/xtrem-intacct/permission__delete__name": "Löschen", "@sage/xtrem-intacct/permission__get_entities__name": "Entitäten abrufen", "@sage/xtrem-intacct/permission__get_entities_asynchronous__name": "Asynchrone Entitäten abrufen", "@sage/xtrem-intacct/permission__install_xtreem_audit_trail__name": "Audittrail xtreem installieren", "@sage/xtrem-intacct/permission__is_service_option_active_function__name": "Ist Funktion Dienstoption aktiv", "@sage/xtrem-intacct/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-intacct/permission__retry__name": "<PERSON><PERSON><PERSON> versuchen", "@sage/xtrem-intacct/permission__send_event__name": "Event senden", "@sage/xtrem-intacct/permission__send_xml_request__name": "XML-Anforderung senden", "@sage/xtrem-intacct/permission__service_option_change__name": "Änderung Dienstoption", "@sage/xtrem-intacct/permission__update__name": "Aktualisieren", "@sage/xtrem-intacct/purge-sys-message-history": "Möchten Sie den Meldungsverlauf löschen?", "@sage/xtrem-intacct/purge-sys-message-history-title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/sender-id-required": "Erfassen Sie die Absender-ID.", "@sage/xtrem-intacct/sender-password-required": "Das Absenderpasswort ist erforderlich.", "@sage/xtrem-intacct/service_options__intacct_option__name": "Sage Intacct-Option", "@sage/xtrem-intacct/synchronous_test": "Synchroner Test", "@sage/xtrem-intacct/user-id-required": "Einstellungen Integration Sage Intacct: Ein<PERSON> Benutzer-ID ist erforderlich und kann nicht leer sein.", "@sage/xtrem-intacct/user-password-required": "Einstellungen Integration Sage Intacct: Ein Benutzerpasswort ist erforderlich und kann nicht leer sein.", "@sage/xtrem-intacct/valid-credentials-need": "Erfassen Sie gültige Sage Intacct-Anmeldedaten.", "@sage/xtrem-intacct/xtrem-object-invalid": "{{nodeName}} ist kein Node."}