{"@sage/xtrem-intacct/activity__intacct__name": "Sage Intacct", "@sage/xtrem-intacct/asynchronous_test": "Asynchronous test", "@sage/xtrem-intacct/audit-trail": "Sage DMO audit trail", "@sage/xtrem-intacct/audit-trail-install": "Do you want to install or update Sage DMO audit trail?", "@sage/xtrem-intacct/audit-trail-install-failed": "Sage DMO audit trail installation failed.", "@sage/xtrem-intacct/audit-trail-install-success": "Sage DMO audit trail installed.", "@sage/xtrem-intacct/audit-trail-mismatch": "Sage DMO audit trail mismatch", "@sage/xtrem-intacct/audit-trail-update": "Do you want to update Sage DMO audit trail?\n                 From: {{from}}\n                 To: {{to}}", "@sage/xtrem-intacct/client_functions__common__ap_ar_posting_to_gl_activation": "You are going to activate the AP/AR posting to the G/L on company {{companies}}. You need to review the tax management on the associated list of accounts.", "@sage/xtrem-intacct/client_functions__common__ap_ar_posting_to_gl_deactivation": "You are going to deactivate the AP/AR posting to G/L on company {{companies}}.", "@sage/xtrem-intacct/client_functions__common__information": "Information", "@sage/xtrem-intacct/company-id-required": "Sage Intacct integration settings: A company ID is required and cannot be blank.", "@sage/xtrem-intacct/configuration-page": "Configuration page", "@sage/xtrem-intacct/connectedIntacctInstance": "Sage Intacct is connected to {{intacctCompanyId}} with the {{intacctUser}} user.", "@sage/xtrem-intacct/data_types__intacct_company_id_data_type__name": "Intacct company ID data type", "@sage/xtrem-intacct/data_types__intacct_property_data_type__name": "Intacct property data type", "@sage/xtrem-intacct/data_types__intacct_record_name_data_type__name": "Intacct record name data type", "@sage/xtrem-intacct/data_types__intacct_session_id_data_type__name": "Intacct session ID data type", "@sage/xtrem-intacct/data_types__listener_status_enum__name": "Listener status enum", "@sage/xtrem-intacct/data_types__property_type_enum__name": "Property type enum", "@sage/xtrem-intacct/data_types__request_type_enum__name": "Request type enum", "@sage/xtrem-intacct/data_types__transaction_integration_level_enum__name": "Transaction integration level enum", "@sage/xtrem-intacct/data_types__type_field_enum__name": "Type field enum", "@sage/xtrem-intacct/disconnected": "Sage Intacct is inactive or disconnected.", "@sage/xtrem-intacct/enums__listener_status__done": "Done", "@sage/xtrem-intacct/enums__listener_status__error": "Error", "@sage/xtrem-intacct/enums__listener_status__notImplentedYet": "Not implemented yet", "@sage/xtrem-intacct/enums__listener_status__received": "Received", "@sage/xtrem-intacct/enums__property_type__description": "Description", "@sage/xtrem-intacct/enums__property_type__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct/enums__property_type__name": "Name", "@sage/xtrem-intacct/enums__property_type__none": "None", "@sage/xtrem-intacct/enums__property_type__number": "Number", "@sage/xtrem-intacct/enums__property_type__text": "Text", "@sage/xtrem-intacct/enums__request_type__create": "Create", "@sage/xtrem-intacct/enums__request_type__createUpdate": "Create/Update", "@sage/xtrem-intacct/enums__request_type__delete": "Delete", "@sage/xtrem-intacct/enums__request_type__read": "Read", "@sage/xtrem-intacct/enums__request_type__update": "Update", "@sage/xtrem-intacct/enums__transaction_definition_type__order": "Order", "@sage/xtrem-intacct/enums__transaction_definition_type__receipt": "Receipt", "@sage/xtrem-intacct/enums__transaction_definition_type__receiptClose": "Receipt closing", "@sage/xtrem-intacct/enums__transaction_definition_type__returnReceiptInvoiced": "Return receipt invoiced", "@sage/xtrem-intacct/enums__transaction_definition_type__returnReceiptNotInvoiced": "Return receipt not invoiced", "@sage/xtrem-intacct/enums__transaction_integration_level__entityLevel": "Entity level", "@sage/xtrem-intacct/enums__transaction_integration_level__topLevel": "Top level", "@sage/xtrem-intacct/enums__type_field__average": "Average", "@sage/xtrem-intacct/enums__type_field__count": "Count", "@sage/xtrem-intacct/enums__type_field__max": "Max", "@sage/xtrem-intacct/enums__type_field__min": "<PERSON>.", "@sage/xtrem-intacct/enums__type_field__select": "Select", "@sage/xtrem-intacct/enums__type_field__sum": "Sum", "@sage/xtrem-intacct/install-intacct": "Sage Intacct application installed: {{info}}", "@sage/xtrem-intacct/menu_item__intacct": "Sage Intacct", "@sage/xtrem-intacct/menu_item__intacct-config": "Intacct configuration", "@sage/xtrem-intacct/menu_item__intacct-transaction": "Intacct transactions", "@sage/xtrem-intacct/no-fields-query": "There are no fields to query on {{objectName}}.", "@sage/xtrem-intacct/no-intacct-instance": "No active Sage Intacct instance found.", "@sage/xtrem-intacct/no-object-query": "There is no object name to query.", "@sage/xtrem-intacct/no-xtreem_audit_trail-template": "No xtremAuditTrail file template in {{path}}. Check the file path.", "@sage/xtrem-intacct/node-extensions__user_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct/node-extensions__user_extension__property__isIntacct": "Sage Intacct", "@sage/xtrem-intacct/node-extensions__user_extension__property__recordNo": "Record number", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail": "Install xtreem audit trail", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail__failed": "Install xtreem audit trail failed.", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail__parameter__intacctInstanceId": "Sage Intacct instance ID", "@sage/xtrem-intacct/nodes__intacct__duplicate_countries": "There are duplicate countries. Make sure that each line has a unique country.", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous": "Get entities asynchronous", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous__failed": "Get entities asynchronous failed.", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous__parameter__intacctInstanceId": "Sage Intacct instance ID", "@sage/xtrem-intacct/nodes__intacct__mutation__installXtreemAuditTrail": "Install Xtreem audit trail", "@sage/xtrem-intacct/nodes__intacct__mutation__installXtreemAuditTrail__parameter__intacctInstanceId": "Sage Intacct instance ID", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities": "Refresh entities", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities__failed": "Refresh entities failed.", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities__parameter__intacctInstanceId": "Sage Intacct instance ID", "@sage/xtrem-intacct/nodes__intacct__mutation__retry": "Retry", "@sage/xtrem-intacct/nodes__intacct__mutation__retry__failed": "<PERSON><PERSON> failed.", "@sage/xtrem-intacct/nodes__intacct__mutation__retry__parameter__sysMessageHistoryId": "System message history ID", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent": "Send event", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__failed": "Send event failed.", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__dataBase64": "Database 64", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__dataIntacctEvent": "Sage Intacct data event", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__messageKind": "Message kind", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest": "Send XML request", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__failed": "Send xml request failed.", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__entityId": "Entity ID", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__isSynchronous": "Synchronous", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__xmlQuery": "XML query", "@sage/xtrem-intacct/nodes__intacct__node_name": "Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__property__asyncTimeout": "Asynchronous timeout", "@sage/xtrem-intacct/nodes__intacct__property__chartOfAccount": "Chart of accounts", "@sage/xtrem-intacct/nodes__intacct__property__companyId": "Company ID", "@sage/xtrem-intacct/nodes__intacct__property__controlIdTableName": "Control ID table name", "@sage/xtrem-intacct/nodes__intacct__property__endpointUrl": "Endpoint URL", "@sage/xtrem-intacct/nodes__intacct__property__entityId": "Entity ID", "@sage/xtrem-intacct/nodes__intacct__property__entityList": "Entity list", "@sage/xtrem-intacct/nodes__intacct__property__id": "ID", "@sage/xtrem-intacct/nodes__intacct__property__infoXTreeMAuditTrail": "Info Sage DMO audit trail", "@sage/xtrem-intacct/nodes__intacct__property__isActive": "Active", "@sage/xtrem-intacct/nodes__intacct__property__isCategoryNameClean": "Is category name clean", "@sage/xtrem-intacct/nodes__intacct__property__isContactListCleaned": "Is contact list cleaned", "@sage/xtrem-intacct/nodes__intacct__property__isDisplayContactHidden": "Is display contact hidden", "@sage/xtrem-intacct/nodes__intacct__property__isFullFilledAuthentification": "Fulfilled authentification", "@sage/xtrem-intacct/nodes__intacct__property__isRequestAsynchronous": "Is request asynchronous", "@sage/xtrem-intacct/nodes__intacct__property__isXTreeMAuditTrail": "Sage DMO audit trail", "@sage/xtrem-intacct/nodes__intacct__property__legislation": "Legislation", "@sage/xtrem-intacct/nodes__intacct__property__lines": "Lines", "@sage/xtrem-intacct/nodes__intacct__property__messageHistory": "Message history", "@sage/xtrem-intacct/nodes__intacct__property__mustXtreemAuditTrail": "Must be Sage DMO audit trail", "@sage/xtrem-intacct/nodes__intacct__property__policyId": "Policy ID", "@sage/xtrem-intacct/nodes__intacct__property__senderId": "Sender ID", "@sage/xtrem-intacct/nodes__intacct__property__senderPassword": "Sender password", "@sage/xtrem-intacct/nodes__intacct__property__sessionExpiration": "Session expiration", "@sage/xtrem-intacct/nodes__intacct__property__sessionId": "Session ID", "@sage/xtrem-intacct/nodes__intacct__property__taxCategory": "Tax category", "@sage/xtrem-intacct/nodes__intacct__property__taxSolution": "Tax solution", "@sage/xtrem-intacct/nodes__intacct__property__transactionIntegrationLevel": "Transaction integration level", "@sage/xtrem-intacct/nodes__intacct__property__userId": "User ID", "@sage/xtrem-intacct/nodes__intacct__property__userPassword": "User password", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance": "Default instance", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance__failed": "Default instance failed.", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance__parameter__isThrowing": "Throwing", "@sage/xtrem-intacct/nodes__intacct__query__getEntities": "Get entities", "@sage/xtrem-intacct/nodes__intacct__query__getEntities__failed": "Get entities failed.", "@sage/xtrem-intacct/nodes__intacct__query__getEntities__parameter__intacctInstanceId": "Sage Intacct instance ID", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct/nodes__intacct_line__error_no_tax_solution": "You cannot select a country without a tax solution.", "@sage/xtrem-intacct/nodes__intacct_line__node_name": "Sage Intacct line", "@sage/xtrem-intacct/nodes__intacct_line__property__country": "Country", "@sage/xtrem-intacct/nodes__intacct_line__property__intacct": "Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_line__property__taxCategory": "Tax category", "@sage/xtrem-intacct/nodes__intacct_line__property__taxSolution": "Tax solution", "@sage/xtrem-intacct/nodes__intacct_node__node_name": "Sage Intacct node", "@sage/xtrem-intacct/nodes__intacct_node_state__node_name": "Sage Intacct node state", "@sage/xtrem-intacct/nodes__intacct_node_state__property__intacctConfiguration": "Sage Intacct configuration", "@sage/xtrem-intacct/nodes__intacct_node_state__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct/nodes__intacct_node_state__property__recordNo": "Record number", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct/nodes__intacct_option_management__mutation__serviceOptionChange": "Service option change", "@sage/xtrem-intacct/nodes__intacct_option_management__mutation__serviceOptionChange__failed": "Service option change failed.", "@sage/xtrem-intacct/nodes__intacct_option_management__node_name": "Sage Intacct option management", "@sage/xtrem-intacct/nodes__intacct_option_management__query__isServiceOptionActiveFunction": "Service option active function", "@sage/xtrem-intacct/nodes__intacct_option_management__query__isServiceOptionActiveFunction__failed": "Is service option active function failed.", "@sage/xtrem-intacct/nodes__intacct/to-many-retry": "Too many attempts: {{retry}}", "@sage/xtrem-intacct/notAsynchronousFunction": "Call Api is a synchronous function please use aCallAPI", "@sage/xtrem-intacct/notSynchronousFunction": "asynchronous Call is a asynchronous function please use Call", "@sage/xtrem-intacct/only-one-intacct-instance": "You can only have one Sage Intacct instance active.", "@sage/xtrem-intacct/package__name": "Sage Intacct configuration", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctConfigurationPage____title": "Sage Intacct configuration page", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctInfo____title": "Info", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__isIntacctServiceOptionActive____title": "Active", "@sage/xtrem-intacct/pages__intacct____navigationPanel__listItem__line2__title": "Company ID", "@sage/xtrem-intacct/pages__intacct____navigationPanel__listItem__titleRight__title": "Active", "@sage/xtrem-intacct/pages__intacct____objectTypePlural": "Sage Intacct configurations", "@sage/xtrem-intacct/pages__intacct____objectTypeSingular": "Sage Intacct configuration", "@sage/xtrem-intacct/pages__intacct____title": "Configuration", "@sage/xtrem-intacct/pages__intacct__addLine____title": "Add line", "@sage/xtrem-intacct/pages__intacct__asynchronousTest____title": "Asynchronous test", "@sage/xtrem-intacct/pages__intacct__auditTrailBlock____title": "Audit trail", "@sage/xtrem-intacct/pages__intacct__cancel": "Cancel", "@sage/xtrem-intacct/pages__intacct__change____title": "Change", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____lookupDialogTitle": "Select chart of account", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____placeholder": "Select chart of accounts", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____title": "Chart of accounts", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__userEmail": "User", "@sage/xtrem-intacct/pages__intacct__communicationSection____title": "Communication", "@sage/xtrem-intacct/pages__intacct__companyId____title": "Company ID", "@sage/xtrem-intacct/pages__intacct__configurationBlock____title": "Configuration", "@sage/xtrem-intacct/pages__intacct__confirm_update": "Apply changes", "@sage/xtrem-intacct/pages__intacct__contactListBlock____title": "Contact", "@sage/xtrem-intacct/pages__intacct__country____columns__title__id": "ID", "@sage/xtrem-intacct/pages__intacct__country____columns__title__legislation___id": "Legislation", "@sage/xtrem-intacct/pages__intacct__country____columns__title__name": "", "@sage/xtrem-intacct/pages__intacct__country____lookupDialogTitle": "", "@sage/xtrem-intacct/pages__intacct__country____placeholder": "", "@sage/xtrem-intacct/pages__intacct__country____title": "", "@sage/xtrem-intacct/pages__intacct__dataToSend____title": "Send data", "@sage/xtrem-intacct/pages__intacct__defaultBlock____title": "De<PERSON>ults", "@sage/xtrem-intacct/pages__intacct__defaultSection____title": "De<PERSON>ults", "@sage/xtrem-intacct/pages__intacct__detailPanelCommunicationHeaderSection____title": "Communication", "@sage/xtrem-intacct/pages__intacct__detailPanelDiagnoseBlock____title": "Diagnoses details", "@sage/xtrem-intacct/pages__intacct__detailPanelDiagnoseSection____title": "Diagnoses", "@sage/xtrem-intacct/pages__intacct__detailPanelReceiveGridRowBlock____title": "Received info details", "@sage/xtrem-intacct/pages__intacct__detailPanelReceiveSection____title": "Received", "@sage/xtrem-intacct/pages__intacct__detailPanelSendBlock____title": "Send block", "@sage/xtrem-intacct/pages__intacct__detailPanelSendGridRowBlock____title": "Send info details", "@sage/xtrem-intacct/pages__intacct__detailPanelSendSection____title": "<PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__duplicate_error": "There are duplicate countries. Make sure that each line has a unique country.", "@sage/xtrem-intacct/pages__intacct__endpointUrl____title": "Endpoint URL", "@sage/xtrem-intacct/pages__intacct__entitites____title": "Entities", "@sage/xtrem-intacct/pages__intacct__eventBlock____title": "Events. Test SQS queues", "@sage/xtrem-intacct/pages__intacct__eventSection____title": "API", "@sage/xtrem-intacct/pages__intacct__historicBlock____title": "History", "@sage/xtrem-intacct/pages__intacct__id____title": "ID", "@sage/xtrem-intacct/pages__intacct__infoBox____title": "Test box", "@sage/xtrem-intacct/pages__intacct__infoXTreeMAuditTrail____title": "Info", "@sage/xtrem-intacct/pages__intacct__install_xtreem_audit_trail_button_text": "Install or update Sage DMO audit trail", "@sage/xtrem-intacct/pages__intacct__isActive____title": "Active", "@sage/xtrem-intacct/pages__intacct__isCategoryNameClean____title": "Contact list category name", "@sage/xtrem-intacct/pages__intacct__isContactListCleaned____title": "Reset contact list", "@sage/xtrem-intacct/pages__intacct__isDisplayContactHidden____title": "Exclude display contact from the contact list", "@sage/xtrem-intacct/pages__intacct__isFullFilledAuthentification____title": "Full filled authentification", "@sage/xtrem-intacct/pages__intacct__isSynchronous____title": "Synchronous", "@sage/xtrem-intacct/pages__intacct__isXTreeMAuditTrail____title": "Sage DMO audit trail", "@sage/xtrem-intacct/pages__intacct__legislation____lookupDialogTitle": "Select legislation", "@sage/xtrem-intacct/pages__intacct__legislation____placeholder": "Select ...", "@sage/xtrem-intacct/pages__intacct__lines____columns__columns__country__title": "ISO 3166-1 alpha-2", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__country__taxSolution__name": "SDMO tax solution", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__taxCategory__name": "Tax category", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__taxSolution": "Sage Intacct tax solution", "@sage/xtrem-intacct/pages__intacct__lines____dropdownActions__title": "Delete line", "@sage/xtrem-intacct/pages__intacct__lines____title": "Lines", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__allDiagnose": "Diagnoses", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__attributes": "Attributes", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__payload": "Payload", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__status": "Status", "@sage/xtrem-intacct/pages__intacct__mainBlock____title": "Configuration", "@sage/xtrem-intacct/pages__intacct__mainSection____title": "General", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__attributes": "Attributes", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__context": "Context", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__context__2": "Context", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__receivedStamp": "Received", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__sendStamp": "<PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__status": "Status", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__user": "User", "@sage/xtrem-intacct/pages__intacct__messageHistory____dropdownActions__title": "Retry", "@sage/xtrem-intacct/pages__intacct__messageHistory____title": "History", "@sage/xtrem-intacct/pages__intacct__messageKind____title": "Message type", "@sage/xtrem-intacct/pages__intacct__mustXtreemAuditTrail____title": "Info", "@sage/xtrem-intacct/pages__intacct__objectEvent____title": "Object", "@sage/xtrem-intacct/pages__intacct__policyId____title": "Policy ID", "@sage/xtrem-intacct/pages__intacct__purgeCommunicationData____title": "Delete", "@sage/xtrem-intacct/pages__intacct__purgeData____title": "Purge history", "@sage/xtrem-intacct/pages__intacct__querySection____title": "API: Query", "@sage/xtrem-intacct/pages__intacct__recordId____title": "Record ID", "@sage/xtrem-intacct/pages__intacct__recordNumber____title": "Record number", "@sage/xtrem-intacct/pages__intacct__refresh_entity_list": "Refresh entities", "@sage/xtrem-intacct/pages__intacct__refreshCommunicationData____title": "Refresh", "@sage/xtrem-intacct/pages__intacct__refreshData____title": "Refresh data", "@sage/xtrem-intacct/pages__intacct__request____title": "XML request", "@sage/xtrem-intacct/pages__intacct__response____title": "XML response", "@sage/xtrem-intacct/pages__intacct__save____title": "Save", "@sage/xtrem-intacct/pages__intacct__send_data_button_text": "Send", "@sage/xtrem-intacct/pages__intacct__send_request_button_text": "Send", "@sage/xtrem-intacct/pages__intacct__senderId____title": "Sender ID", "@sage/xtrem-intacct/pages__intacct__synchronousTest____title": "Synchronous test", "@sage/xtrem-intacct/pages__intacct__sysCommunicationSection____title": "Listener", "@sage/xtrem-intacct/pages__intacct__taxCategory____columns__title__name": "Name", "@sage/xtrem-intacct/pages__intacct__taxCategory____lookupDialogTitle": "Select tax category", "@sage/xtrem-intacct/pages__intacct__taxCategory____placeholder": "Select tax category", "@sage/xtrem-intacct/pages__intacct__taxCategory____title": "Tax category", "@sage/xtrem-intacct/pages__intacct__taxSolution____title": "Tax solution", "@sage/xtrem-intacct/pages__intacct__transactionIntegrationLevel____title": "Transaction integration level", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level": "It is recommended to complete {{num}} unposted transactions to avoid errors. Cancel to complete unposted transactions or apply changes to future posts only.", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level_success": "Transactions were already posted to Sage Intacct. Apply changes to future posts only, creating inconsistencies with previous posts.", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level_title": "Change posting settings", "@sage/xtrem-intacct/pages__intacct__userId____title": "User ID", "@sage/xtrem-intacct/pages__intacct__userPassword____title": "Password ID", "@sage/xtrem-intacct/pages__template_panel____title": "Transaction template ", "@sage/xtrem-intacct/pages__template_panel__cancel____title": "Cancel", "@sage/xtrem-intacct/pages__template_panel__confirm____title": "OK", "@sage/xtrem-intacct/pages__template_panel__description____title": "Description", "@sage/xtrem-intacct/pages__template_panel__name____title": "Name", "@sage/xtrem-intacct/pages__transaction_definition____title": "Transaction definition setup", "@sage/xtrem-intacct/pages__transaction_definition__purchase____columns__title": "Name", "@sage/xtrem-intacct/pages__transaction_definition__purchase____title": "Purchase transaction", "@sage/xtrem-intacct/pages__transaction_definition__saveAction____title": "Save", "@sage/xtrem-intacct/pages__transaction_definition__transactionBlock____title": "Transactions", "@sage/xtrem-intacct/pages__transaction_definition__transactionSection____title": "Transactions", "@sage/xtrem-intacct/pages-confirm-cancel": "Cancel", "@sage/xtrem-intacct/pages-confirm-no": "No", "@sage/xtrem-intacct/pages-confirm-purge": "Delete", "@sage/xtrem-intacct/pages-confirm-yes": "Yes", "@sage/xtrem-intacct/permission__manage__name": "Manage", "@sage/xtrem-intacct/permission__read__name": "Read", "@sage/xtrem-intacct/purge-sys-message-history": "Do you want to delete the message history?", "@sage/xtrem-intacct/purge-sys-message-history-title": "Delete history", "@sage/xtrem-intacct/sender-id-required": "Enter the sender ID.", "@sage/xtrem-intacct/sender-password-required": "The sender password is mandatory.", "@sage/xtrem-intacct/service_options__intacct_option__name": "Sage Intacct option", "@sage/xtrem-intacct/synchronous_test": "Synchronous test", "@sage/xtrem-intacct/user-id-required": "Sage Intacct integration settings: A user ID is required and cannot be blank.", "@sage/xtrem-intacct/user-password-required": "Sage Intacct integration settings: A user password is required and cannot be blank.", "@sage/xtrem-intacct/valid-credentials-need": "Enter valid Sage Intacct credentials.", "@sage/xtrem-intacct/xtrem-object-invalid": "{{node<PERSON><PERSON>}} is not a node."}