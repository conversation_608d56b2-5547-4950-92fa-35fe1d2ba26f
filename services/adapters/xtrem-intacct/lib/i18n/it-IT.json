{"@sage/xtrem-intacct/activity__intacct__name": "", "@sage/xtrem-intacct/asynchronous_test": "", "@sage/xtrem-intacct/audit-trail": "", "@sage/xtrem-intacct/audit-trail-install": "", "@sage/xtrem-intacct/audit-trail-install-failed": "", "@sage/xtrem-intacct/audit-trail-install-success": "", "@sage/xtrem-intacct/audit-trail-mismatch": "", "@sage/xtrem-intacct/audit-trail-update": "", "@sage/xtrem-intacct/client_functions__common__ap_ar_posting_to_gl_activation": "", "@sage/xtrem-intacct/client_functions__common__ap_ar_posting_to_gl_deactivation": "", "@sage/xtrem-intacct/client_functions__common__information": "", "@sage/xtrem-intacct/company-id-required": "", "@sage/xtrem-intacct/configuration-page": "", "@sage/xtrem-intacct/connectedIntacctInstance": "", "@sage/xtrem-intacct/data_types__intacct_company_id_data_type__name": "", "@sage/xtrem-intacct/data_types__intacct_property_data_type__name": "", "@sage/xtrem-intacct/data_types__intacct_record_name_data_type__name": "", "@sage/xtrem-intacct/data_types__intacct_session_id_data_type__name": "", "@sage/xtrem-intacct/data_types__listener_status_enum__name": "", "@sage/xtrem-intacct/data_types__property_type_enum__name": "", "@sage/xtrem-intacct/data_types__request_type_enum__name": "", "@sage/xtrem-intacct/data_types__transaction_integration_level_enum__name": "", "@sage/xtrem-intacct/data_types__type_field_enum__name": "", "@sage/xtrem-intacct/disconnected": "", "@sage/xtrem-intacct/enums__listener_status__done": "", "@sage/xtrem-intacct/enums__listener_status__error": "", "@sage/xtrem-intacct/enums__listener_status__notImplentedYet": "", "@sage/xtrem-intacct/enums__listener_status__received": "", "@sage/xtrem-intacct/enums__property_type__description": "", "@sage/xtrem-intacct/enums__property_type__intacctId": "", "@sage/xtrem-intacct/enums__property_type__name": "", "@sage/xtrem-intacct/enums__property_type__none": "", "@sage/xtrem-intacct/enums__property_type__number": "", "@sage/xtrem-intacct/enums__property_type__text": "", "@sage/xtrem-intacct/enums__request_type__create": "", "@sage/xtrem-intacct/enums__request_type__createUpdate": "", "@sage/xtrem-intacct/enums__request_type__delete": "", "@sage/xtrem-intacct/enums__request_type__read": "", "@sage/xtrem-intacct/enums__request_type__update": "", "@sage/xtrem-intacct/enums__transaction_integration_level__entityLevel": "", "@sage/xtrem-intacct/enums__transaction_integration_level__topLevel": "", "@sage/xtrem-intacct/enums__type_field__average": "", "@sage/xtrem-intacct/enums__type_field__count": "", "@sage/xtrem-intacct/enums__type_field__max": "", "@sage/xtrem-intacct/enums__type_field__min": "", "@sage/xtrem-intacct/enums__type_field__select": "", "@sage/xtrem-intacct/enums__type_field__sum": "", "@sage/xtrem-intacct/install-intacct": "", "@sage/xtrem-intacct/menu_item__intacct": "", "@sage/xtrem-intacct/no-fields-query": "", "@sage/xtrem-intacct/no-object-query": "", "@sage/xtrem-intacct/no-xtreem_audit_trail-template": "", "@sage/xtrem-intacct/node-extensions__user_extension__property__intacctId": "", "@sage/xtrem-intacct/node-extensions__user_extension__property__isIntacct": "", "@sage/xtrem-intacct/node-extensions__user_extension__property__recordNo": "", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport": "", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail": "", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail__failed": "", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail__parameter__intacctInstanceId": "", "@sage/xtrem-intacct/nodes__intacct__duplicate_countries": "", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous": "", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous__failed": "", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous__parameter__intacctInstanceId": "", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities": "", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities__failed": "", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities__parameter__intacctInstanceId": "", "@sage/xtrem-intacct/nodes__intacct__mutation__retry": "", "@sage/xtrem-intacct/nodes__intacct__mutation__retry__failed": "", "@sage/xtrem-intacct/nodes__intacct__mutation__retry__parameter__sysMessageHistoryId": "", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent": "", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__failed": "", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__dataBase64": "", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__dataIntacctEvent": "", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__messageKind": "", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest": "", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__failed": "", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__entityId": "", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__isSynchronous": "", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__xmlQuery": "", "@sage/xtrem-intacct/nodes__intacct__node_name": "", "@sage/xtrem-intacct/nodes__intacct__property__asyncTimeout": "", "@sage/xtrem-intacct/nodes__intacct__property__chartOfAccount": "", "@sage/xtrem-intacct/nodes__intacct__property__companyId": "", "@sage/xtrem-intacct/nodes__intacct__property__controlIdTableName": "", "@sage/xtrem-intacct/nodes__intacct__property__endpointUrl": "", "@sage/xtrem-intacct/nodes__intacct__property__entityId": "", "@sage/xtrem-intacct/nodes__intacct__property__entityList": "", "@sage/xtrem-intacct/nodes__intacct__property__id": "", "@sage/xtrem-intacct/nodes__intacct__property__infoXTreeMAuditTrail": "", "@sage/xtrem-intacct/nodes__intacct__property__isActive": "", "@sage/xtrem-intacct/nodes__intacct__property__isCategoryNameClean": "", "@sage/xtrem-intacct/nodes__intacct__property__isContactListCleaned": "", "@sage/xtrem-intacct/nodes__intacct__property__isDisplayContactHidden": "", "@sage/xtrem-intacct/nodes__intacct__property__isFullFilledAuthentification": "", "@sage/xtrem-intacct/nodes__intacct__property__isRequestAsynchronous": "", "@sage/xtrem-intacct/nodes__intacct__property__isXTreeMAuditTrail": "", "@sage/xtrem-intacct/nodes__intacct__property__legislation": "", "@sage/xtrem-intacct/nodes__intacct__property__lines": "", "@sage/xtrem-intacct/nodes__intacct__property__messageHistory": "", "@sage/xtrem-intacct/nodes__intacct__property__mustXtreemAuditTrail": "", "@sage/xtrem-intacct/nodes__intacct__property__policyId": "", "@sage/xtrem-intacct/nodes__intacct__property__senderId": "", "@sage/xtrem-intacct/nodes__intacct__property__senderPassword": "", "@sage/xtrem-intacct/nodes__intacct__property__sessionExpiration": "", "@sage/xtrem-intacct/nodes__intacct__property__sessionId": "", "@sage/xtrem-intacct/nodes__intacct__property__taxCategory": "", "@sage/xtrem-intacct/nodes__intacct__property__taxSolution": "", "@sage/xtrem-intacct/nodes__intacct__property__transactionIntegrationLevel": "", "@sage/xtrem-intacct/nodes__intacct__property__userId": "", "@sage/xtrem-intacct/nodes__intacct__property__userPassword": "", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance": "", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance__failed": "", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance__parameter__isThrowing": "", "@sage/xtrem-intacct/nodes__intacct__query__getEntities": "", "@sage/xtrem-intacct/nodes__intacct__query__getEntities__failed": "", "@sage/xtrem-intacct/nodes__intacct__query__getEntities__parameter__intacctInstanceId": "", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport": "", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-intacct/nodes__intacct_line__error_no_tax_solution": "", "@sage/xtrem-intacct/nodes__intacct_line__node_name": "", "@sage/xtrem-intacct/nodes__intacct_line__property__country": "", "@sage/xtrem-intacct/nodes__intacct_line__property__intacct": "", "@sage/xtrem-intacct/nodes__intacct_line__property__taxCategory": "", "@sage/xtrem-intacct/nodes__intacct_line__property__taxSolution": "", "@sage/xtrem-intacct/nodes__intacct_node__node_name": "", "@sage/xtrem-intacct/nodes__intacct_node_state__node_name": "", "@sage/xtrem-intacct/nodes__intacct_node_state__property__intacctConfiguration": "", "@sage/xtrem-intacct/nodes__intacct_node_state__property__intacctId": "", "@sage/xtrem-intacct/nodes__intacct_node_state__property__recordNo": "", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport": "", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-intacct/nodes__intacct_option_management__mutation__serviceOptionChange": "", "@sage/xtrem-intacct/nodes__intacct_option_management__mutation__serviceOptionChange__failed": "", "@sage/xtrem-intacct/nodes__intacct_option_management__node_name": "", "@sage/xtrem-intacct/nodes__intacct_option_management__query__isServiceOptionActiveFunction": "", "@sage/xtrem-intacct/nodes__intacct_option_management__query__isServiceOptionActiveFunction__failed": "", "@sage/xtrem-intacct/nodes__intacct/to-many-retry": "", "@sage/xtrem-intacct/only-one-intacct-instance": "", "@sage/xtrem-intacct/package__name": "", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctBlock____title": "", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctConfigurationPage____title": "", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctInfo____title": "", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__isIntacctServiceOptionActive____title": "", "@sage/xtrem-intacct/pages__intacct____navigationPanel__listItem__line2__title": "", "@sage/xtrem-intacct/pages__intacct____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-intacct/pages__intacct____objectTypePlural": "", "@sage/xtrem-intacct/pages__intacct____objectTypeSingular": "", "@sage/xtrem-intacct/pages__intacct____title": "", "@sage/xtrem-intacct/pages__intacct__addLine____title": "", "@sage/xtrem-intacct/pages__intacct__asynchronousTest____title": "", "@sage/xtrem-intacct/pages__intacct__auditTrailBlock____title": "", "@sage/xtrem-intacct/pages__intacct__cancel": "", "@sage/xtrem-intacct/pages__intacct__change____title": "", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____lookupDialogTitle": "", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____placeholder": "", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____title": "", "@sage/xtrem-intacct/pages__intacct__communicationSection____title": "", "@sage/xtrem-intacct/pages__intacct__companyId____title": "", "@sage/xtrem-intacct/pages__intacct__configurationBlock____title": "", "@sage/xtrem-intacct/pages__intacct__confirm_update": "", "@sage/xtrem-intacct/pages__intacct__contactListBlock____title": "", "@sage/xtrem-intacct/pages__intacct__dataToSend____title": "", "@sage/xtrem-intacct/pages__intacct__defaultBlock____title": "", "@sage/xtrem-intacct/pages__intacct__defaultSection____title": "", "@sage/xtrem-intacct/pages__intacct__detailPanelCommunicationHeaderSection____title": "", "@sage/xtrem-intacct/pages__intacct__detailPanelDiagnoseBlock____title": "", "@sage/xtrem-intacct/pages__intacct__detailPanelDiagnoseSection____title": "", "@sage/xtrem-intacct/pages__intacct__detailPanelReceiveGridRowBlock____title": "", "@sage/xtrem-intacct/pages__intacct__detailPanelReceiveSection____title": "", "@sage/xtrem-intacct/pages__intacct__detailPanelSendBlock____title": "", "@sage/xtrem-intacct/pages__intacct__detailPanelSendGridRowBlock____title": "", "@sage/xtrem-intacct/pages__intacct__detailPanelSendSection____title": "", "@sage/xtrem-intacct/pages__intacct__duplicate_error": "", "@sage/xtrem-intacct/pages__intacct__endpointUrl____title": "", "@sage/xtrem-intacct/pages__intacct__entitites____title": "", "@sage/xtrem-intacct/pages__intacct__eventBlock____title": "", "@sage/xtrem-intacct/pages__intacct__eventSection____title": "", "@sage/xtrem-intacct/pages__intacct__id____title": "", "@sage/xtrem-intacct/pages__intacct__infoBox____title": "", "@sage/xtrem-intacct/pages__intacct__infoXTreeMAuditTrail____title": "", "@sage/xtrem-intacct/pages__intacct__install_xtreem_audit_trail_button_text": "", "@sage/xtrem-intacct/pages__intacct__isActive____title": "", "@sage/xtrem-intacct/pages__intacct__isCategoryNameClean____title": "", "@sage/xtrem-intacct/pages__intacct__isContactListCleaned____title": "", "@sage/xtrem-intacct/pages__intacct__isDisplayContactHidden____title": "", "@sage/xtrem-intacct/pages__intacct__isFullFilledAuthentification____title": "", "@sage/xtrem-intacct/pages__intacct__isSynchronous____title": "", "@sage/xtrem-intacct/pages__intacct__isXTreeMAuditTrail____title": "", "@sage/xtrem-intacct/pages__intacct__legislation____lookupDialogTitle": "", "@sage/xtrem-intacct/pages__intacct__legislation____placeholder": "", "@sage/xtrem-intacct/pages__intacct__lines____columns__columns__country__title": "", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__country__taxSolution__name": "", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__taxCategory__name": "", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__taxSolution": "", "@sage/xtrem-intacct/pages__intacct__lines____dropdownActions__title": "", "@sage/xtrem-intacct/pages__intacct__lines____title": "", "@sage/xtrem-intacct/pages__intacct__mainBlock____title": "", "@sage/xtrem-intacct/pages__intacct__mainSection____title": "", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__attributes": "", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__context": "", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__context__2": "", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__receivedStamp": "", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__sendStamp": "", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__status": "", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__user": "", "@sage/xtrem-intacct/pages__intacct__messageHistory____dropdownActions__title": "", "@sage/xtrem-intacct/pages__intacct__messageHistory____title": "", "@sage/xtrem-intacct/pages__intacct__messageKind____title": "", "@sage/xtrem-intacct/pages__intacct__mustXtreemAuditTrail____title": "", "@sage/xtrem-intacct/pages__intacct__objectEvent____title": "", "@sage/xtrem-intacct/pages__intacct__policyId____title": "", "@sage/xtrem-intacct/pages__intacct__purgeCommunicationData____title": "", "@sage/xtrem-intacct/pages__intacct__querySection____title": "", "@sage/xtrem-intacct/pages__intacct__recordId____title": "", "@sage/xtrem-intacct/pages__intacct__recordNumber____title": "", "@sage/xtrem-intacct/pages__intacct__refresh_entity_list": "", "@sage/xtrem-intacct/pages__intacct__refreshCommunicationData____title": "", "@sage/xtrem-intacct/pages__intacct__request____title": "", "@sage/xtrem-intacct/pages__intacct__response____title": "", "@sage/xtrem-intacct/pages__intacct__save____title": "", "@sage/xtrem-intacct/pages__intacct__send_data_button_text": "", "@sage/xtrem-intacct/pages__intacct__send_request_button_text": "", "@sage/xtrem-intacct/pages__intacct__senderId____title": "", "@sage/xtrem-intacct/pages__intacct__synchronousTest____title": "", "@sage/xtrem-intacct/pages__intacct__transactionIntegrationLevel____title": "", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level": "", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level_success": "", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level_title": "", "@sage/xtrem-intacct/pages__intacct__userId____title": "", "@sage/xtrem-intacct/pages__intacct__userPassword____title": "", "@sage/xtrem-intacct/pages-confirm-cancel": "", "@sage/xtrem-intacct/pages-confirm-no": "", "@sage/xtrem-intacct/pages-confirm-purge": "", "@sage/xtrem-intacct/pages-confirm-yes": "", "@sage/xtrem-intacct/permission__manage__name": "", "@sage/xtrem-intacct/permission__read__name": "", "@sage/xtrem-intacct/purge-sys-message-history": "", "@sage/xtrem-intacct/purge-sys-message-history-title": "", "@sage/xtrem-intacct/sender-id-required": "", "@sage/xtrem-intacct/sender-password-required": "", "@sage/xtrem-intacct/service_options__intacct_option__name": "", "@sage/xtrem-intacct/synchronous_test": "", "@sage/xtrem-intacct/user-id-required": "", "@sage/xtrem-intacct/user-password-required": "", "@sage/xtrem-intacct/valid-credentials-need": "", "@sage/xtrem-intacct/xtrem-object-invalid": ""}