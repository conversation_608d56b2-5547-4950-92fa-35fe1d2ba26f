{"@sage/xtrem-intacct/activity__intacct__name": "Sage Intacct", "@sage/xtrem-intacct/activity__intacct_option_management__name": "Gestion des options Sage Intacct", "@sage/xtrem-intacct/asynchronous_test": "Test asynchrone", "@sage/xtrem-intacct/audit-trail": "Piste d'audit Sage DMO", "@sage/xtrem-intacct/audit-trail-install": "Voulez-vous installer ou mettre à jour la piste d'audit Sage DMO ?", "@sage/xtrem-intacct/audit-trail-install-failed": "Échec de l'installation de la piste d'audit Sage DMO", "@sage/xtrem-intacct/audit-trail-install-success": "Piste d'audit Sage DMO installée", "@sage/xtrem-intacct/audit-trail-mismatch": "Incohérence piste d'audit Sage DMO", "@sage/xtrem-intacct/audit-trail-update": "V<PERSON><PERSON><PERSON>-vous mettre à jour la piste d'audit Sage DMO ?\n                 De : {{from}}\n                 A : {{to}}", "@sage/xtrem-intacct/client_functions__common__ap_ar_posting_to_gl_activation": "Vous êtes sur le point d'activer la comptabilisation tiers au grand-livre pour la société {{companies}}. Vous devez vérifier la gestion des taxes pour la liste des comptes associés.", "@sage/xtrem-intacct/client_functions__common__ap_ar_posting_to_gl_deactivation": "Vous êtes sur le point de désactiver la comptabilisation tiers au grand-livre pour la société {{companies}}.", "@sage/xtrem-intacct/client_functions__common__information": "Information", "@sage/xtrem-intacct/company-id-required": "Réglages d'intégration Sage Intacct : un code société est demandé et ne peut pas être vide.", "@sage/xtrem-intacct/configuration-page": "Page de configuration", "@sage/xtrem-intacct/connectedIntacctInstance": "Sage Intacct est connecté à {{intacctCompanyId}} avec l'utilisateur {{intacctUser}}.", "@sage/xtrem-intacct/data_types__intacct_company_id_data_type__name": "Type de données code société Intacct", "@sage/xtrem-intacct/data_types__intacct_property_data_type__name": "Type de données propriété Intacct", "@sage/xtrem-intacct/data_types__intacct_record_name_data_type__name": "Type de données nom enregistrement Intacct", "@sage/xtrem-intacct/data_types__intacct_session_id_data_type__name": "Type de données code session Intacct", "@sage/xtrem-intacct/data_types__listener_status_enum__name": "Enum statut listener", "@sage/xtrem-intacct/data_types__property_type_enum__name": "Enum type propriété", "@sage/xtrem-intacct/data_types__request_type_enum__name": "Enum type demande", "@sage/xtrem-intacct/data_types__transaction_integration_level_enum__name": "Enum de niveau d'intégration de transaction", "@sage/xtrem-intacct/data_types__type_field_enum__name": "Enum champ type", "@sage/xtrem-intacct/disconnected": "Sage Intacct est inactif ou déconnecté.", "@sage/xtrem-intacct/enums__field_type__average": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__field_type__count": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__field_type__max": "<PERSON><PERSON>", "@sage/xtrem-intacct/enums__field_type__min": "Mini", "@sage/xtrem-intacct/enums__field_type__select": "Sélection", "@sage/xtrem-intacct/enums__field_type__sum": "Somme", "@sage/xtrem-intacct/enums__listener_status__done": "Done", "@sage/xtrem-intacct/enums__listener_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__listener_status__notImplentedYet": "Pas encore mis en oeuvre", "@sage/xtrem-intacct/enums__listener_status__received": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__property_type__description": "Description", "@sage/xtrem-intacct/enums__property_type__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct/enums__property_type__name": "Nom", "@sage/xtrem-intacct/enums__property_type__none": "Aucun", "@sage/xtrem-intacct/enums__property_type__number": "Nombre", "@sage/xtrem-intacct/enums__property_type__text": "Texte", "@sage/xtrem-intacct/enums__request_type__create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__request_type__createUpdate": "<PERSON><PERSON>er / Mettre à jour", "@sage/xtrem-intacct/enums__request_type__delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__request_type__read": "<PERSON><PERSON>", "@sage/xtrem-intacct/enums__request_type__update": "Modifier", "@sage/xtrem-intacct/enums__transaction_definition_type__order": "Commande", "@sage/xtrem-intacct/enums__transaction_definition_type__receipt": "Ré<PERSON>", "@sage/xtrem-intacct/enums__transaction_definition_type__receiptClose": "Solde réception", "@sage/xtrem-intacct/enums__transaction_definition_type__returnReceiptInvoiced": "Réception retour facturée", "@sage/xtrem-intacct/enums__transaction_definition_type__returnReceiptNotInvoiced": "Réception retour non facturée", "@sage/xtrem-intacct/enums__transaction_integration_level__entityLevel": "Niveau entité", "@sage/xtrem-intacct/enums__transaction_integration_level__topLevel": "Niveau supérieur", "@sage/xtrem-intacct/enums__type_field__average": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__type_field__count": "Comptage", "@sage/xtrem-intacct/enums__type_field__max": "<PERSON>.", "@sage/xtrem-intacct/enums__type_field__min": "Mini", "@sage/xtrem-intacct/enums__type_field__select": "Sélection", "@sage/xtrem-intacct/enums__type_field__sum": "Somme", "@sage/xtrem-intacct/install-intacct": "Application Sage Intacct installée : {{info}}", "@sage/xtrem-intacct/menu_item__intacct": "Sage Intacct", "@sage/xtrem-intacct/no-fields-query": "Il n'existe aucun champ à interroger sur {{objectName}}.", "@sage/xtrem-intacct/no-intacct-instance": "Pas d'instance Sage Intacct active trouvée.", "@sage/xtrem-intacct/no-object-query": "Il n'existe aucun nom d'objet à interroger.", "@sage/xtrem-intacct/no-xtreem_audit_trail-template": "<PERSON><PERSON><PERSON> modèle de fichier xtremAuditTrail sous {{path}}. Vérifiez le chemin du fichier.", "@sage/xtrem-intacct/node-extensions__user_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct/node-extensions__user_extension__property__isIntacct": "Sage Intacct", "@sage/xtrem-intacct/node-extensions__user_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail": "Installer la piste d'audit xtreem", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail__failed": "Échec d'installation de la piste d'audit Xtreem.", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail__parameter__intacctInstanceId": "Code instance Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__duplicate_countries": "Il existe des pays en double. Assurez-vous que chaque ligne est associée à un pays unique.", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous": "Obtenir entités asynchrones", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous__failed": "Échec d'obtention d'entités asynchrones.", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous__parameter__intacctInstanceId": "Code instance Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__mutation__installXtreemAuditTrail": "Installer la piste d'audit Xtreem", "@sage/xtrem-intacct/nodes__intacct__mutation__installXtreemAuditTrail__parameter__intacctInstanceId": "Code instance Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities": "Actualiser les entités", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities__failed": "Échec d'actualisation des entités.", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities__parameter__intacctInstanceId": "Code instance Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__mutation__retry": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/nodes__intacct__mutation__retry__failed": "Échec de la nouvelle tentative.", "@sage/xtrem-intacct/nodes__intacct__mutation__retry__parameter__sysMessageHistoryId": "Code historique des messages système", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent": "Émission d'événement", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__failed": "Échec de l'envoi d'e-mails.", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__dataBase64": "Base de données 64", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__dataIntacctEvent": "Événement données Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__messageKind": "Type de message", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest": "Envoyer re<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__failed": "Échec d'envoi des requête XML.", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__entityId": "Code entité", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__isSynchronous": "Synchrone", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__xmlQuery": "Requête XML", "@sage/xtrem-intacct/nodes__intacct__node_name": "Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__property__asyncTimeout": "<PERSON><PERSON><PERSON> d'expiration asynchrone", "@sage/xtrem-intacct/nodes__intacct__property__chartOfAccount": "Plan comptable", "@sage/xtrem-intacct/nodes__intacct__property__companyId": "Code société", "@sage/xtrem-intacct/nodes__intacct__property__controlIdTableName": "Nom de la table des codes de contrôle", "@sage/xtrem-intacct/nodes__intacct__property__country": "Pays", "@sage/xtrem-intacct/nodes__intacct__property__endpointUrl": "URL de point de connexion", "@sage/xtrem-intacct/nodes__intacct__property__entityId": "Code entité", "@sage/xtrem-intacct/nodes__intacct__property__entityList": "Liste d'entités", "@sage/xtrem-intacct/nodes__intacct__property__id": "Code", "@sage/xtrem-intacct/nodes__intacct__property__infoXTreeMAuditTrail": "Infos piste d'audit Sage DMO", "@sage/xtrem-intacct/nodes__intacct__property__isActive": "Active", "@sage/xtrem-intacct/nodes__intacct__property__isCategoryNameClean": "Nom de catégorie propre", "@sage/xtrem-intacct/nodes__intacct__property__isContactListCleaned": "Liste de contacts nettoyée", "@sage/xtrem-intacct/nodes__intacct__property__isDisplayContactHidden": "Affichage contacts ma<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/nodes__intacct__property__isFullFilledAuthentification": "Authentification accomplie", "@sage/xtrem-intacct/nodes__intacct__property__isRequestAsynchronous": "De<PERSON>e asynchrone", "@sage/xtrem-intacct/nodes__intacct__property__isXTreeMAuditTrail": "Piste d'audit Sage DMO", "@sage/xtrem-intacct/nodes__intacct__property__legislation": "Législation", "@sage/xtrem-intacct/nodes__intacct__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/nodes__intacct__property__messageHistory": "Historique des messages", "@sage/xtrem-intacct/nodes__intacct__property__mustXtreemAuditTrail": "Doit être une piste d'audit Sage DMO", "@sage/xtrem-intacct/nodes__intacct__property__policyId": "Code police", "@sage/xtrem-intacct/nodes__intacct__property__senderId": "Code émetteur", "@sage/xtrem-intacct/nodes__intacct__property__senderPassword": "Mot de passe de l'expéditeur", "@sage/xtrem-intacct/nodes__intacct__property__sessionExpiration": "Expiration de session", "@sage/xtrem-intacct/nodes__intacct__property__sessionId": "ID de session", "@sage/xtrem-intacct/nodes__intacct__property__taxCategory": "Catégorie de taxe", "@sage/xtrem-intacct/nodes__intacct__property__taxSolution": "Solution de taxe", "@sage/xtrem-intacct/nodes__intacct__property__transactionIntegrationLevel": "Niveau d'intégration de transaction", "@sage/xtrem-intacct/nodes__intacct__property__userId": "Code utilisateur", "@sage/xtrem-intacct/nodes__intacct__property__userPassword": "Mot de passe utilisateur", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance": "Instance par défaut", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance__failed": "Échec de l'instance par défaut.", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance__parameter__isThrowing": "Throwing", "@sage/xtrem-intacct/nodes__intacct__query__getEntities": "Entités", "@sage/xtrem-intacct/nodes__intacct__query__getEntities__failed": "Échec d'actualisation des entités.", "@sage/xtrem-intacct/nodes__intacct__query__getEntities__parameter__intacctInstanceId": "Code instance Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct/nodes__intacct_line__error_no_tax_solution": "Vous ne pouvez sélectionner un pays dépourvu de solution de taxe.", "@sage/xtrem-intacct/nodes__intacct_line__node_name": "Line Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_line__property__country": "Pays", "@sage/xtrem-intacct/nodes__intacct_line__property__intacct": "Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_line__property__taxCategory": "Catégorie de taxe", "@sage/xtrem-intacct/nodes__intacct_line__property__taxSolution": "Solution de taxe", "@sage/xtrem-intacct/nodes__intacct_node__node_name": "Node Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_node_state__node_name": "Etat node Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_node_state__property__intacctConfiguration": "Configuration Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_node_state__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_node_state__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct/nodes__intacct_option_management__mutation__serviceOptionChange": "Modification option de service", "@sage/xtrem-intacct/nodes__intacct_option_management__mutation__serviceOptionChange__failed": "Échec de modification des options de service.", "@sage/xtrem-intacct/nodes__intacct_option_management__node_name": "Gestion des options Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_option_management__query__isServiceOptionActiveFunction": "Fonction options de service active", "@sage/xtrem-intacct/nodes__intacct_option_management__query__isServiceOptionActiveFunction__failed": "Échec de la fonction des options de service active.", "@sage/xtrem-intacct/nodes__intacct/to-many-retry": "Trop de tentatives :{{retry}}", "@sage/xtrem-intacct/notAsynchronousFunction": "\"Call Api\" est une fonction asynchrone. Utiliser \"aCallAPI\".", "@sage/xtrem-intacct/notSynchronousFunction": "\"asynchronous Call\" est une fonction asynchrone. Utiliser \"Call\".", "@sage/xtrem-intacct/only-one-intacct-instance": "Vous pouvez avoir uniquement une instance Sage Intacct active.", "@sage/xtrem-intacct/package__name": "Configuration Sage Intacct", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctConfigurationPage____title": "Page Sage Intacct Manufacturing", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctInfo____title": "Infos", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__isIntacctServiceOptionActive____title": "Active", "@sage/xtrem-intacct/pages__intacct____navigationPanel__listItem__line2__title": "Code société", "@sage/xtrem-intacct/pages__intacct____navigationPanel__listItem__titleRight__title": "Actif", "@sage/xtrem-intacct/pages__intacct____objectTypePlural": "Configurations Sage Intacct", "@sage/xtrem-intacct/pages__intacct____objectTypeSingular": "Configurations Sage Intacct", "@sage/xtrem-intacct/pages__intacct____title": "Configuration", "@sage/xtrem-intacct/pages__intacct__addLine____title": "<PERSON><PERSON><PERSON> ligne", "@sage/xtrem-intacct/pages__intacct__asynchronousTest____title": "Test asynchrone", "@sage/xtrem-intacct/pages__intacct__auditTrailBlock____title": "Piste d'audit", "@sage/xtrem-intacct/pages__intacct__cancel": "Annuler", "@sage/xtrem-intacct/pages__intacct__change____title": "Changement", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____lookupDialogTitle": "Sélectionner le plan comptable", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____placeholder": "Sélectionner le plan comptable.", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____title": "Plan comptable", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title": "", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__attributes": "Attributs", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__communicationDiagnoses": "Diagnostics", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__context": "Contexte", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__nodeId": "ID node", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__nodeName": "Nom node", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__receivedStamp": "Ré<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__sendStamp": "Envoyée", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__sentId": "ID", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__status": "Statut", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__user": "Utilisa<PERSON>ur", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__userEmail": "Utilisa<PERSON>ur", "@sage/xtrem-intacct/pages__intacct__communicationHistoryBlock____title": "Historique", "@sage/xtrem-intacct/pages__intacct__communicationSection____title": "Communication", "@sage/xtrem-intacct/pages__intacct__companyId____title": "Code société", "@sage/xtrem-intacct/pages__intacct__configurationBlock____title": "Configuration", "@sage/xtrem-intacct/pages__intacct__confirm_update": "Appliquer changements", "@sage/xtrem-intacct/pages__intacct__contactListBlock____title": "Contact", "@sage/xtrem-intacct/pages__intacct__country____columns__title__id": "Code", "@sage/xtrem-intacct/pages__intacct__country____columns__title__legislation___id": "Législation", "@sage/xtrem-intacct/pages__intacct__country____columns__title__name": "", "@sage/xtrem-intacct/pages__intacct__country____lookupDialogTitle": "", "@sage/xtrem-intacct/pages__intacct__country____placeholder": "", "@sage/xtrem-intacct/pages__intacct__country____title": "", "@sage/xtrem-intacct/pages__intacct__dataToSend____title": "Envoyer les données", "@sage/xtrem-intacct/pages__intacct__defaultBlock____title": "Valeurs par défaut", "@sage/xtrem-intacct/pages__intacct__defaultSection____title": "Valeurs par défaut", "@sage/xtrem-intacct/pages__intacct__detailPanelCommunicationHeaderSection____title": "Communication", "@sage/xtrem-intacct/pages__intacct__detailPanelDiagnoseBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__detailPanelDiagnoseSection____title": "Diagnostics", "@sage/xtrem-intacct/pages__intacct__detailPanelGeneralBlock____title": "Général", "@sage/xtrem-intacct/pages__intacct__detailPanelReceiveGridRowBlock____title": "Détails infos reçues", "@sage/xtrem-intacct/pages__intacct__detailPanelReceiveSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__detailPanelSendBlock____title": "Bloc Envoyé", "@sage/xtrem-intacct/pages__intacct__detailPanelSendGridRowBlock____title": "Envoyer les détails.", "@sage/xtrem-intacct/pages__intacct__detailPanelSendSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__duplicate_error": "Il existe des pays en double. Assurez-vous que chaque ligne est associée à un pays unique.", "@sage/xtrem-intacct/pages__intacct__endpointUrl____title": "URL de point de connexion", "@sage/xtrem-intacct/pages__intacct__entitites____title": "Entités", "@sage/xtrem-intacct/pages__intacct__eventBlock____title": "Événements. Test queues SQS", "@sage/xtrem-intacct/pages__intacct__eventSection____title": "API", "@sage/xtrem-intacct/pages__intacct__historicBlock____title": "Historique", "@sage/xtrem-intacct/pages__intacct__id____title": "Code", "@sage/xtrem-intacct/pages__intacct__infoBox____title": "Informations de test", "@sage/xtrem-intacct/pages__intacct__infoXTreeMAuditTrail____title": "Infos", "@sage/xtrem-intacct/pages__intacct__install_xtreem_audit_trail_button_text": "Installez ou mettez à jour la piste d'audit Sage DMO.", "@sage/xtrem-intacct/pages__intacct__isActive____title": "Actif", "@sage/xtrem-intacct/pages__intacct__isCategoryNameClean____title": "Nom de catégorie de la liste de contacts", "@sage/xtrem-intacct/pages__intacct__isContactListCleaned____title": "Réinitialiser la liste de contacts", "@sage/xtrem-intacct/pages__intacct__isDisplayContactHidden____title": "Exclure l'affichage du contact de la liste de contacts", "@sage/xtrem-intacct/pages__intacct__isFullFilledAuthentification____title": "Information d'authentification renseignées", "@sage/xtrem-intacct/pages__intacct__isSynchronous____title": "Synchrone", "@sage/xtrem-intacct/pages__intacct__isXTreeMAuditTrail____title": "Piste d'audit Sage DMO", "@sage/xtrem-intacct/pages__intacct__legislation____lookupDialogTitle": "Sélectionner la législation", "@sage/xtrem-intacct/pages__intacct__legislation____placeholder": "Sélectionner...", "@sage/xtrem-intacct/pages__intacct__lines____columns__columns__country__title": "ISO 3166-1 alpha-2", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__country__taxSolution__name": "Solution de taxe SDMO", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__taxCategory__name": "Catégorie de taxe", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__taxSolution": "Solution de taxe Sage Intacct", "@sage/xtrem-intacct/pages__intacct__lines____dropdownActions__title": "Supp<PERSON>er ligne", "@sage/xtrem-intacct/pages__intacct__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__allDiagnose": "Diagnostics", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__attributes": "Attributs", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__payload": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__status": "Statut", "@sage/xtrem-intacct/pages__intacct__mainBlock____title": "Configuration", "@sage/xtrem-intacct/pages__intacct__mainSection____title": "Général", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__attributes": "Attributs", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__communicationDiagnoses": "Diagnostics", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__context": "Contexte", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__context__2": "Contexte", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__receivedStamp": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__sendStamp": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__status": "Statut", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__user": "Utilisa<PERSON>ur", "@sage/xtrem-intacct/pages__intacct__messageHistory____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__messageHistory____title": "Historique", "@sage/xtrem-intacct/pages__intacct__messageKind____title": "Type de message", "@sage/xtrem-intacct/pages__intacct__mustXtreemAuditTrail____title": "Infos", "@sage/xtrem-intacct/pages__intacct__objectEvent____title": "Objet", "@sage/xtrem-intacct/pages__intacct__policyId____title": "Code police", "@sage/xtrem-intacct/pages__intacct__purgeCommunicationData____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__purgeData____title": "Purger l'historique", "@sage/xtrem-intacct/pages__intacct__querySection____title": "API : requête", "@sage/xtrem-intacct/pages__intacct__recordId____title": "Code enregistrement", "@sage/xtrem-intacct/pages__intacct__recordNumber____title": "N° enregistrement", "@sage/xtrem-intacct/pages__intacct__refresh_entity_list": "Actualiser les entités", "@sage/xtrem-intacct/pages__intacct__refreshCommunicationData____title": "Actualiser", "@sage/xtrem-intacct/pages__intacct__refreshData____title": "Ra<PERSON><PERSON><PERSON><PERSON> les données", "@sage/xtrem-intacct/pages__intacct__request____title": "Requête XML", "@sage/xtrem-intacct/pages__intacct__response____title": "Réponse XML", "@sage/xtrem-intacct/pages__intacct__save____title": "Enregistrer", "@sage/xtrem-intacct/pages__intacct__saveAction____title": "Enregistrer", "@sage/xtrem-intacct/pages__intacct__send_data_button_text": "Envoyer", "@sage/xtrem-intacct/pages__intacct__send_request_button_text": "Envoyer", "@sage/xtrem-intacct/pages__intacct__senderId____title": "Code émetteur", "@sage/xtrem-intacct/pages__intacct__synchronousTest____title": "Test synchrone", "@sage/xtrem-intacct/pages__intacct__sysCommunicationSection____title": "Listener", "@sage/xtrem-intacct/pages__intacct__taxCategory____columns__title__name": "Nom", "@sage/xtrem-intacct/pages__intacct__taxCategory____lookupDialogTitle": "Sélectionner la catégorie de taxe", "@sage/xtrem-intacct/pages__intacct__taxCategory____placeholder": "Sélectionner la catégorie de taxe", "@sage/xtrem-intacct/pages__intacct__taxCategory____title": "Catégorie de taxe", "@sage/xtrem-intacct/pages__intacct__taxSolution____title": "Solution de taxe", "@sage/xtrem-intacct/pages__intacct__transactionIntegrationLevel____title": "Niveau d'intégration de transaction", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level": "Il est recommandé de finaliser {{num}} transactions non comptabilisées pour éviter les erreurs. Annulez pour finaliser les transactions non comptabilisées ou appliquez des modifications à des comptabilisations futures uniquement.", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level_success": "Des transactions ont déjà été comptabilisées dans Sage Intacct. Appliquez des modifications aux comptabilisations futures uniquement, ce qui crée des incohérences avec les comptabilisations précédentes.", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level_title": "Changer les paramètres de comptabilisation", "@sage/xtrem-intacct/pages__intacct__userId____title": "Code utilisateur", "@sage/xtrem-intacct/pages__intacct__userPassword____title": "Code mot de passe", "@sage/xtrem-intacct/pages__transaction_definition____title": "Définition de transaction", "@sage/xtrem-intacct/pages__transaction_definition__purchase____columns__title": "Nom", "@sage/xtrem-intacct/pages__transaction_definition__purchase____title": "Transaction d'achat", "@sage/xtrem-intacct/pages__transaction_definition__saveAction____title": "Enregistrer", "@sage/xtrem-intacct/pages__transaction_definition__transactionBlock____title": "Transactions", "@sage/xtrem-intacct/pages__transaction_definition__transactionSection____title": "Transactions", "@sage/xtrem-intacct/pages-confirm-cancel": "Annuler", "@sage/xtrem-intacct/pages-confirm-no": "Non", "@sage/xtrem-intacct/pages-confirm-purge": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages-confirm-yes": "O<PERSON>", "@sage/xtrem-intacct/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/permission__default_instance__name": "Instance par défaut", "@sage/xtrem-intacct/permission__delete__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/permission__get_entities__name": "Entités", "@sage/xtrem-intacct/permission__get_entities_asynchronous__name": "Obtenir entités asynchrones", "@sage/xtrem-intacct/permission__install_xtreem_audit_trail__name": "Installer la piste d'audit xtreem", "@sage/xtrem-intacct/permission__is_service_option_active_function__name": "Fonction option de service active", "@sage/xtrem-intacct/permission__manage__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-intacct/permission__retry__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/permission__send_event__name": "Émission d'événement", "@sage/xtrem-intacct/permission__send_xml_request__name": "Envoyer re<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/permission__service_option_change__name": "Modification option de service", "@sage/xtrem-intacct/permission__update__name": "Mettre à jour", "@sage/xtrem-intacct/purge-sys-message-history": "Voulez-vous supprimer l'historique des messages ?", "@sage/xtrem-intacct/purge-sys-message-history-title": "Effacer l'historique", "@sage/xtrem-intacct/sender-id-required": "Renseignez le code émetteur.", "@sage/xtrem-intacct/sender-password-required": "Renseignez le mot de passe de l'émetteur.", "@sage/xtrem-intacct/service_options__intacct_option__name": "Option Sage Intacct", "@sage/xtrem-intacct/synchronous_test": "Test synchrone", "@sage/xtrem-intacct/user-id-required": "Réglages d'intégration Sage Intacct : un code utilisateur est demandé et ne peut pas être vide.", "@sage/xtrem-intacct/user-password-required": "Réglages d'intégration Sage Intacct : un mot de passe utilisateur est nécessaire et ne peut pas être vide.", "@sage/xtrem-intacct/valid-credentials-need": "Renseigner des identifiants Sage Intacct valides", "@sage/xtrem-intacct/xtrem-object-invalid": "{{node<PERSON><PERSON>}} n'est pas un node."}