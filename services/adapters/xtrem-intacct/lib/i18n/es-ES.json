{"@sage/xtrem-intacct/activity__intacct__name": "Sage Intacct", "@sage/xtrem-intacct/activity__intacct_option_management__name": "Gestión de opciones de Sage Intacct", "@sage/xtrem-intacct/asynchronous_test": "Prueba asincrónica", "@sage/xtrem-intacct/audit-trail": "Pista de auditoría de Sage DMO", "@sage/xtrem-intacct/audit-trail-install": "¿Quieres instalar o actualizar la pista de auditoría de Sage DMO?", "@sage/xtrem-intacct/audit-trail-install-failed": "La pista de auditoría de Sage DMO no se ha instalado.", "@sage/xtrem-intacct/audit-trail-install-success": "La pista de auditoría de Sage DMO se ha instalado.", "@sage/xtrem-intacct/audit-trail-mismatch": "Error de coincidencia de la pista de auditoría de Sage DMO", "@sage/xtrem-intacct/audit-trail-update": "¿Quieres actualizar la pista de auditoría de Sage DMO? \n                 De: {{from}}\n                 A: {{to}}", "@sage/xtrem-intacct/client_functions__common__ap_ar_posting_to_gl_activation": "Vas a activar la contabilización de documentos de proveedor y de cliente en el libro mayor para la sociedad {{companies}}. Revisa la gestión de impuestos en la lista de cuentas asociada.", "@sage/xtrem-intacct/client_functions__common__ap_ar_posting_to_gl_deactivation": "Vas a desactivar la contabilización de documentos de proveedor y de cliente en el libro mayor para la sociedad {{companies}}.", "@sage/xtrem-intacct/client_functions__common__information": "Información", "@sage/xtrem-intacct/company-id-required": "Configuración de integración con Sage Intacct: introduce el identificador de la sociedad.", "@sage/xtrem-intacct/configuration-page": "Página de configuración", "@sage/xtrem-intacct/connectedIntacctInstance": "Sage Intacct está conectado a la sociedad {{intacctCompanyId}} con el usuario {{intacctUser}}.", "@sage/xtrem-intacct/data_types__intacct_company_id_data_type__name": "Intacct company ID data type", "@sage/xtrem-intacct/data_types__intacct_property_data_type__name": "Intacct property data type", "@sage/xtrem-intacct/data_types__intacct_record_name_data_type__name": "Intacct record name data type", "@sage/xtrem-intacct/data_types__intacct_session_id_data_type__name": "Intacct session ID data type", "@sage/xtrem-intacct/data_types__listener_status_enum__name": "Item status enum", "@sage/xtrem-intacct/data_types__property_type_enum__name": "Property type enum", "@sage/xtrem-intacct/data_types__request_type_enum__name": "Request type enum", "@sage/xtrem-intacct/data_types__transaction_integration_level_enum__name": "Nivel de integración de transacción", "@sage/xtrem-intacct/data_types__type_field_enum__name": "Type field enum", "@sage/xtrem-intacct/disconnected": "Sage Intacct está inactivo o desconectado.", "@sage/xtrem-intacct/enums__field_type__average": "Promedio", "@sage/xtrem-intacct/enums__field_type__count": "Recuento", "@sage/xtrem-intacct/enums__field_type__max": "Máx.", "@sage/xtrem-intacct/enums__field_type__min": "<PERSON><PERSON>.", "@sage/xtrem-intacct/enums__field_type__select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__field_type__sum": "<PERSON><PERSON>", "@sage/xtrem-intacct/enums__listener_status__done": "Realizado", "@sage/xtrem-intacct/enums__listener_status__error": "Error", "@sage/xtrem-intacct/enums__listener_status__fail": "Error", "@sage/xtrem-intacct/enums__listener_status__notImplentedYet": "Sin implementar", "@sage/xtrem-intacct/enums__listener_status__received": "Recibido", "@sage/xtrem-intacct/enums__listener_status__toDelete": "Por eliminar", "@sage/xtrem-intacct/enums__property_type__description": "Descripción", "@sage/xtrem-intacct/enums__property_type__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct/enums__property_type__name": "Nombre", "@sage/xtrem-intacct/enums__property_type__none": "Ninguna", "@sage/xtrem-intacct/enums__property_type__number": "Número", "@sage/xtrem-intacct/enums__property_type__text": "Texto", "@sage/xtrem-intacct/enums__request_type__create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__request_type__createUpdate": "<PERSON><PERSON><PERSON> o <PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__request_type__delete": "Eliminar", "@sage/xtrem-intacct/enums__request_type__read": "<PERSON><PERSON>", "@sage/xtrem-intacct/enums__request_type__update": "Actualizar", "@sage/xtrem-intacct/enums__transaction_definition_type__order": "Pedido", "@sage/xtrem-intacct/enums__transaction_definition_type__receipt": "Recepción", "@sage/xtrem-intacct/enums__transaction_definition_type__receiptClose": "Cierre de recepción", "@sage/xtrem-intacct/enums__transaction_definition_type__returnReceiptInvoiced": "Recepción de devolución facturada", "@sage/xtrem-intacct/enums__transaction_definition_type__returnReceiptNotInvoiced": "Recepción de devolución sin facturar", "@sage/xtrem-intacct/enums__transaction_integration_level__entityLevel": "Nivel de entidad", "@sage/xtrem-intacct/enums__transaction_integration_level__topLevel": "Nivel superior", "@sage/xtrem-intacct/enums__type_field__average": "Promedio", "@sage/xtrem-intacct/enums__type_field__count": "Recuento", "@sage/xtrem-intacct/enums__type_field__max": "Máximo", "@sage/xtrem-intacct/enums__type_field__min": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/enums__type_field__select": "Selección", "@sage/xtrem-intacct/enums__type_field__sum": "<PERSON><PERSON>", "@sage/xtrem-intacct/install-intacct": "Aplicación de Sage Intacct instalada: {{info}}", "@sage/xtrem-intacct/menu_item__intacct": "Sage Intacct", "@sage/xtrem-intacct/menu_item__intacct-config": "Configuración en Intacct", "@sage/xtrem-intacct/menu_item__intacct-transaction": "Transacciones en Intacct", "@sage/xtrem-intacct/no-fields-query": "No hay ningún campo que consultar en {{objectName}}.", "@sage/xtrem-intacct/no-intacct-instance": "No se ha encontrado ninguna instancia de Sage Intacct activa.", "@sage/xtrem-intacct/no-object-query": "No hay ningún nombre de objeto que consultar.", "@sage/xtrem-intacct/no-xtreem_audit_trail-template": "No hay ninguna plantilla del archivo xtremAuditTrail en {{path}}. Comprueba la ruta del archivo.", "@sage/xtrem-intacct/node-extensions__user_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct/node-extensions__user_extension__property__isIntacct": "Sage Intacct", "@sage/xtrem-intacct/node-extensions__user_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail": "Instalar pista de auditoría de XTreeM", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail__failed": "Install xtreem audit trail failed.", "@sage/xtrem-intacct/nodes__intacct__asyncMutation__installXtreemAuditTrail__parameter__intacctInstanceId": "Id. de instancia en Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__duplicate_countries": "Hay países duplicados. Asigna un país diferente a cada línea.", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous": "Obtener entidades asincrónicas", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous__failed": "Error al obtener las entidades asincrónicas", "@sage/xtrem-intacct/nodes__intacct__mutation__getEntitiesAsynchronous__parameter__intacctInstanceId": "Id. de instancia en Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__mutation__installXtreemAuditTrail": "Instalar pista de auditoría de XTreeM", "@sage/xtrem-intacct/nodes__intacct__mutation__installXtreemAuditTrail__parameter__intacctInstanceId": "Id. de instancia en Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities": "Actualizar entidades", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities__failed": "Error al actualizar las entidades", "@sage/xtrem-intacct/nodes__intacct__mutation__refreshEntities__parameter__intacctInstanceId": "Id. de instancia en Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__mutation__retry": "Reintentar", "@sage/xtrem-intacct/nodes__intacct__mutation__retry__failed": "Error al reintentar", "@sage/xtrem-intacct/nodes__intacct__mutation__retry__parameter__sysMessageHistoryId": "Id. de historial de mensajes de sistema", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent": "Enviar evento", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__failed": "Error al enviar el evento", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__dataBase64": "Base de datos 64", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__dataIntacctEvent": "Evento de datos en Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__mutation__sendEvent__parameter__messageKind": "Message kind", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest": "Enviar solicitud XML", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__failed": "Error al enviar la solicitud XML", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__entityId": "Id. de entidad", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__isSynchronous": "Sincrónica", "@sage/xtrem-intacct/nodes__intacct__mutation__sendXmlRequest__parameter__xmlQuery": "Consulta XML", "@sage/xtrem-intacct/nodes__intacct__node_name": "Sage Intacct", "@sage/xtrem-intacct/nodes__intacct__property__asyncTimeout": "Tiempo de espera asincrónico", "@sage/xtrem-intacct/nodes__intacct__property__chartOfAccount": "Plan de cuentas", "@sage/xtrem-intacct/nodes__intacct__property__companyId": "Id. de sociedad", "@sage/xtrem-intacct/nodes__intacct__property__controlIdTableName": "Nombre de tabla de ids. de control", "@sage/xtrem-intacct/nodes__intacct__property__country": "<PERSON><PERSON>", "@sage/xtrem-intacct/nodes__intacct__property__endpointUrl": "URL de punto de conexión", "@sage/xtrem-intacct/nodes__intacct__property__entityId": "Id. de entidad", "@sage/xtrem-intacct/nodes__intacct__property__entityList": "Lista de entidades", "@sage/xtrem-intacct/nodes__intacct__property__id": "Id.", "@sage/xtrem-intacct/nodes__intacct__property__infoXTreeMAuditTrail": "Info. pista de auditoría de Sage DMO", "@sage/xtrem-intacct/nodes__intacct__property__isActive": "Activa", "@sage/xtrem-intacct/nodes__intacct__property__isCategoryNameClean": "Nombre de categoría limpio", "@sage/xtrem-intacct/nodes__intacct__property__isContactListCleaned": "Lista de contactos limpia", "@sage/xtrem-intacct/nodes__intacct__property__isDisplayContactHidden": "Contacto de visualización oculto", "@sage/xtrem-intacct/nodes__intacct__property__isFullFilledAuthentification": "Información de autenticación completa", "@sage/xtrem-intacct/nodes__intacct__property__isRequestAsynchronous": "Solicitud asincrónica", "@sage/xtrem-intacct/nodes__intacct__property__isXTreeMAuditTrail": "Pista de auditoría de Sage DMO", "@sage/xtrem-intacct/nodes__intacct__property__legislation": "Legislación", "@sage/xtrem-intacct/nodes__intacct__property__lines": "Líneas", "@sage/xtrem-intacct/nodes__intacct__property__messageHistory": "Historial de mensajes", "@sage/xtrem-intacct/nodes__intacct__property__mustXtreemAuditTrail": "Debe ser pista de auditoría de Sage DMO", "@sage/xtrem-intacct/nodes__intacct__property__policyId": "Id. de directiva", "@sage/xtrem-intacct/nodes__intacct__property__senderId": "Id. de remitente", "@sage/xtrem-intacct/nodes__intacct__property__senderPassword": "Contraseña de emisor", "@sage/xtrem-intacct/nodes__intacct__property__sessionExpiration": "Caducidad de sesión", "@sage/xtrem-intacct/nodes__intacct__property__sessionId": "Id. de sesi<PERSON>", "@sage/xtrem-intacct/nodes__intacct__property__taxCategory": "Categoría de impuesto", "@sage/xtrem-intacct/nodes__intacct__property__taxSolution": "Solución de impuestos", "@sage/xtrem-intacct/nodes__intacct__property__transactionIntegrationLevel": "Nivel de integración de transacción", "@sage/xtrem-intacct/nodes__intacct__property__userId": "<PERSON>d. de usuario", "@sage/xtrem-intacct/nodes__intacct__property__userPassword": "Contraseña de usuario", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance": "Instancia por defecto", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance__failed": "Error de instancia por defecto", "@sage/xtrem-intacct/nodes__intacct__query__defaultInstance__parameter__isThrowing": "Throwing", "@sage/xtrem-intacct/nodes__intacct__query__getEntities": "Obtener entidades", "@sage/xtrem-intacct/nodes__intacct__query__getEntities__failed": "Error al obtener las entidades", "@sage/xtrem-intacct/nodes__intacct__query__getEntities__parameter__intacctInstanceId": "Id. de instancia en Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct/nodes__intacct_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct/nodes__intacct_line__error_no_tax_solution": "No puedes seleccionar un país sin una solución de impuestos.", "@sage/xtrem-intacct/nodes__intacct_line__node_name": "Línea en Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_line__property__country": "<PERSON><PERSON>", "@sage/xtrem-intacct/nodes__intacct_line__property__intacct": "Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_line__property__taxCategory": "Categoría de impuesto", "@sage/xtrem-intacct/nodes__intacct_line__property__taxSolution": "Solución de impuestos", "@sage/xtrem-intacct/nodes__intacct_node__node_name": "Nodo en Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_node_state__node_name": "Estado de nodo en Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_node_state__property__intacctConfiguration": "Configuración de Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_node_state__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_node_state__property__recordNo": "Número de registro", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct/nodes__intacct_option_management__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct/nodes__intacct_option_management__mutation__serviceOptionChange": "Cambiar opción de servicio", "@sage/xtrem-intacct/nodes__intacct_option_management__mutation__serviceOptionChange__failed": "Error al cambiar la opción de servicio", "@sage/xtrem-intacct/nodes__intacct_option_management__node_name": "Gestión de opciones de Sage Intacct", "@sage/xtrem-intacct/nodes__intacct_option_management__query__isServiceOptionActiveFunction": "Service option active function", "@sage/xtrem-intacct/nodes__intacct_option_management__query__isServiceOptionActiveFunction__failed": "Is service option active function failed.", "@sage/xtrem-intacct/nodes__intacct/to-many-retry": "Demasiados intentos. {{retry}}", "@sage/xtrem-intacct/notAsynchronousFunction": "Call Api es una función sincrónica. Utiliza aCallAPI.", "@sage/xtrem-intacct/notSynchronousFunction": "asynchronous Call es una función asincrónica. Utiliza Call.", "@sage/xtrem-intacct/only-one-intacct-instance": "Solo puedes tener una instancia de Sage Intacct activa.", "@sage/xtrem-intacct/package__name": "Configuración de Sage Intacct", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctConfigurationPage____title": "Página de configuración de Sage Intacct", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__intacctInfo____title": "Información", "@sage/xtrem-intacct/page-extensions__option_management_base_extension__isIntacctServiceOptionActive____title": "Activa", "@sage/xtrem-intacct/pages__intacct____navigationPanel__listItem__line2__title": "Id. de sociedad", "@sage/xtrem-intacct/pages__intacct____navigationPanel__listItem__titleRight__title": "Activa", "@sage/xtrem-intacct/pages__intacct____objectTypePlural": "Configuración de Sage Intacct", "@sage/xtrem-intacct/pages__intacct____objectTypeSingular": "Configuración de Sage Intacct", "@sage/xtrem-intacct/pages__intacct____title": "Configuración", "@sage/xtrem-intacct/pages__intacct__addLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__asynchronousTest____title": "Prueba asincrónica", "@sage/xtrem-intacct/pages__intacct__auditTrailBlock____title": "Pista de auditoría", "@sage/xtrem-intacct/pages__intacct__cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__change____title": "Cambio", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____lookupDialogTitle": "Seleccionar plan de cuentas", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____placeholder": "Seleccionar plan de cuentas", "@sage/xtrem-intacct/pages__intacct__chartOfAccount____title": "Plan de cuentas", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title": "", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__attributes": "Atributos", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__communicationDiagnoses": "Diagnós<PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__context": "Contexto", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__nodeId": "<PERSON><PERSON><PERSON> de nodo", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__nodeName": "Nombre de nodo", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__receivedStamp": "Recibida", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__sendStamp": "Enviada", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__sentId": "Id.", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__status": "Estado", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__user": "Usuario", "@sage/xtrem-intacct/pages__intacct__communicationHistory____columns__title__userEmail": "Usuario", "@sage/xtrem-intacct/pages__intacct__communicationHistoryBlock____title": "Historial", "@sage/xtrem-intacct/pages__intacct__communicationSection____title": "Comunicación", "@sage/xtrem-intacct/pages__intacct__companyId____title": "Id. de sociedad", "@sage/xtrem-intacct/pages__intacct__configurationBlock____title": "Configuración", "@sage/xtrem-intacct/pages__intacct__confirm_update": "Aplicar cambios", "@sage/xtrem-intacct/pages__intacct__contactListBlock____title": "Contacto", "@sage/xtrem-intacct/pages__intacct__country____columns__title__id": "Id.", "@sage/xtrem-intacct/pages__intacct__country____columns__title__legislation___id": "Legislación", "@sage/xtrem-intacct/pages__intacct__country____columns__title__name": "", "@sage/xtrem-intacct/pages__intacct__country____lookupDialogTitle": "", "@sage/xtrem-intacct/pages__intacct__country____placeholder": "", "@sage/xtrem-intacct/pages__intacct__country____title": "", "@sage/xtrem-intacct/pages__intacct__dataToSend____title": "Env<PERSON>", "@sage/xtrem-intacct/pages__intacct__defaultBlock____title": "Valores predeterminados", "@sage/xtrem-intacct/pages__intacct__defaultSection____title": "Valores predeterminados", "@sage/xtrem-intacct/pages__intacct__detailPanelCommunicationHeaderSection____title": "Comunicación", "@sage/xtrem-intacct/pages__intacct__detailPanelDiagnoseBlock____title": "Diagnós<PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__detailPanelDiagnoseSection____title": "Diagnós<PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__detailPanelGeneralBlock____title": "General", "@sage/xtrem-intacct/pages__intacct__detailPanelReceiveGridRowBlock____title": "Detalles de recepción", "@sage/xtrem-intacct/pages__intacct__detailPanelReceiveSection____title": "Recepción", "@sage/xtrem-intacct/pages__intacct__detailPanelSendBlock____title": "Envío", "@sage/xtrem-intacct/pages__intacct__detailPanelSendGridRowBlock____title": "Detalles de envío", "@sage/xtrem-intacct/pages__intacct__detailPanelSendSection____title": "Envío", "@sage/xtrem-intacct/pages__intacct__duplicate_error": "Hay países duplicados. Asigna un país diferente a cada línea.", "@sage/xtrem-intacct/pages__intacct__endpointUrl____title": "URL de punto de conexión", "@sage/xtrem-intacct/pages__intacct__entitites____title": "Entidades", "@sage/xtrem-intacct/pages__intacct__eventBlock____title": "Events. Colas SQS de prueba", "@sage/xtrem-intacct/pages__intacct__eventSection____title": "API", "@sage/xtrem-intacct/pages__intacct__historicBlock____title": "Historial", "@sage/xtrem-intacct/pages__intacct__id____title": "Id.", "@sage/xtrem-intacct/pages__intacct__infoBox____title": "Cuadro de prueba", "@sage/xtrem-intacct/pages__intacct__infoXTreeMAuditTrail____title": "Información", "@sage/xtrem-intacct/pages__intacct__install_xtreem_audit_trail_button_text": "Instalar o actualizar pista de auditoría de Sage DMO", "@sage/xtrem-intacct/pages__intacct__isActive____title": "Activa", "@sage/xtrem-intacct/pages__intacct__isCategoryNameClean____title": "Nombre de categoría en lista de contactos", "@sage/xtrem-intacct/pages__intacct__isContactListCleaned____title": "Restablecer lista de contactos", "@sage/xtrem-intacct/pages__intacct__isDisplayContactHidden____title": "Excluir contacto de visualización de la lista", "@sage/xtrem-intacct/pages__intacct__isFullFilledAuthentification____title": "Información de autenticación completa", "@sage/xtrem-intacct/pages__intacct__isSynchronous____title": "Sincrónica", "@sage/xtrem-intacct/pages__intacct__isXTreeMAuditTrail____title": "Pista de auditoría de Sage DMO", "@sage/xtrem-intacct/pages__intacct__legislation____lookupDialogTitle": "Seleccionar legislación", "@sage/xtrem-intacct/pages__intacct__legislation____placeholder": "Seleccionar...", "@sage/xtrem-intacct/pages__intacct__lines____columns__columns__country__title": "Código ISO 3166-1 alfa-2", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__country__taxSolution__name": "Solución de impuestos en Sage DMO", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__taxCategory__name": "Categoría de impuesto", "@sage/xtrem-intacct/pages__intacct__lines____columns__title__taxSolution": "Solución de impuestos en Sage Intacct", "@sage/xtrem-intacct/pages__intacct__lines____dropdownActions__title": "Eliminar línea", "@sage/xtrem-intacct/pages__intacct__lines____title": "Líneas", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__allDiagnose": "Diagnós<PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__attributes": "Atributos", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__payload": "Payload", "@sage/xtrem-intacct/pages__intacct__listenerHistory____columns__title__status": "Estado", "@sage/xtrem-intacct/pages__intacct__mainBlock____title": "Configuración", "@sage/xtrem-intacct/pages__intacct__mainSection____title": "General", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__attributes": "Atributos", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__communicationDiagnoses": "Diagnós<PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__context": "Contexto", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__context__2": "Contexto", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__receivedStamp": "Recepción", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__sendStamp": "Envío", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__status": "Estado", "@sage/xtrem-intacct/pages__intacct__messageHistory____columns__title__user": "Usuario", "@sage/xtrem-intacct/pages__intacct__messageHistory____dropdownActions__title": "Reintentar", "@sage/xtrem-intacct/pages__intacct__messageHistory____title": "Historial", "@sage/xtrem-intacct/pages__intacct__messageKind____title": "Tipo de mensaje", "@sage/xtrem-intacct/pages__intacct__mustXtreemAuditTrail____title": "Información", "@sage/xtrem-intacct/pages__intacct__objectEvent____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__policyId____title": "Id. de directiva", "@sage/xtrem-intacct/pages__intacct__purgeCommunicationData____title": "Eliminar", "@sage/xtrem-intacct/pages__intacct__purgeData____title": "Historial de depuraciones", "@sage/xtrem-intacct/pages__intacct__querySection____title": "Consulta API", "@sage/xtrem-intacct/pages__intacct__recordId____title": "Id. de registro", "@sage/xtrem-intacct/pages__intacct__recordNumber____title": "Número de registro", "@sage/xtrem-intacct/pages__intacct__refresh_entity_list": "Actualizar entidades", "@sage/xtrem-intacct/pages__intacct__refreshCommunicationData____title": "Actualizar", "@sage/xtrem-intacct/pages__intacct__refreshData____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__intacct__request____title": "Solicitud XML", "@sage/xtrem-intacct/pages__intacct__response____title": "Respuesta XML", "@sage/xtrem-intacct/pages__intacct__save____title": "Guardar", "@sage/xtrem-intacct/pages__intacct__saveAction____title": "Guardar", "@sage/xtrem-intacct/pages__intacct__send_data_button_text": "Enviar", "@sage/xtrem-intacct/pages__intacct__send_request_button_text": "Enviar", "@sage/xtrem-intacct/pages__intacct__senderId____title": "Id. de remitente", "@sage/xtrem-intacct/pages__intacct__synchronousTest____title": "Prueba sincrónica", "@sage/xtrem-intacct/pages__intacct__sysCommunicationSection____title": "Listener", "@sage/xtrem-intacct/pages__intacct__taxCategory____columns__title__name": "Nombre", "@sage/xtrem-intacct/pages__intacct__taxCategory____lookupDialogTitle": "Seleccionar categoría de impuesto", "@sage/xtrem-intacct/pages__intacct__taxCategory____placeholder": "Seleccionar categoría de impuesto", "@sage/xtrem-intacct/pages__intacct__taxCategory____title": "Categoría de impuesto", "@sage/xtrem-intacct/pages__intacct__taxSolution____title": "Solución de impuestos", "@sage/xtrem-intacct/pages__intacct__transactionIntegrationLevel____title": "Nivel de integración de transacción", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level": "Es recomendable que finalices las {{num}} transacciones sin contabilizar para evitar errores. Cancela para finalizarlas o aplica los cambios únicamente a las contabilizaciones futuras.", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level_success": "Algunas transacciones ya se han contabilizado en Sage Intacct. Aplica los cambios únicamente a las contabilizaciones futuras para evitar incoherencias.", "@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level_title": "Cambiar configuración de contabilización", "@sage/xtrem-intacct/pages__intacct__userId____title": "<PERSON>d. de usuario", "@sage/xtrem-intacct/pages__intacct__userPassword____title": "Id. de contraseña", "@sage/xtrem-intacct/pages__template_panel____title": "Plantilla de transacción ", "@sage/xtrem-intacct/pages__template_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages__template_panel__confirm____title": "Aceptar", "@sage/xtrem-intacct/pages__template_panel__description____title": "Descripción", "@sage/xtrem-intacct/pages__template_panel__name____title": "Nombre", "@sage/xtrem-intacct/pages__transaction_definition____title": "Ajuste de definición de transacción", "@sage/xtrem-intacct/pages__transaction_definition__purchase____columns__title": "Nombre", "@sage/xtrem-intacct/pages__transaction_definition__purchase____title": "Transacción de compra", "@sage/xtrem-intacct/pages__transaction_definition__saveAction____title": "Guardar", "@sage/xtrem-intacct/pages__transaction_definition__transactionBlock____title": "Transacciones", "@sage/xtrem-intacct/pages__transaction_definition__transactionSection____title": "Transacciones", "@sage/xtrem-intacct/pages-confirm-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/pages-confirm-no": "No", "@sage/xtrem-intacct/pages-confirm-purge": "Eliminar", "@sage/xtrem-intacct/pages-confirm-yes": "Sí", "@sage/xtrem-intacct/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct/permission__default_instance__name": "Instancia por defecto", "@sage/xtrem-intacct/permission__delete__name": "Eliminar", "@sage/xtrem-intacct/permission__get_entities__name": "Obtener entidades", "@sage/xtrem-intacct/permission__get_entities_asynchronous__name": "Obtener entidades asincrónicas", "@sage/xtrem-intacct/permission__install_xtreem_audit_trail__name": "Instalar pista de auditoría de XTreeM", "@sage/xtrem-intacct/permission__is_service_option_active_function__name": "Service option active function", "@sage/xtrem-intacct/permission__manage__name": "Gestionar", "@sage/xtrem-intacct/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-intacct/permission__retry__name": "Reintentar", "@sage/xtrem-intacct/permission__send_event__name": "Enviar evento", "@sage/xtrem-intacct/permission__send_xml_request__name": "Enviar solicitud XML", "@sage/xtrem-intacct/permission__service_option_change__name": "Cambiar opción de servicio", "@sage/xtrem-intacct/permission__update__name": "Actualizar", "@sage/xtrem-intacct/purge-sys-message-history": "¿Quieres eliminar el historial de mensajes?", "@sage/xtrem-intacct/purge-sys-message-history-title": "Eliminar historial", "@sage/xtrem-intacct/sender-id-required": "Introduce el identificador del emisor.", "@sage/xtrem-intacct/sender-password-required": "Introduce la contraseña del emisor.", "@sage/xtrem-intacct/service_options__intacct_option__name": "Opción de Sage Intacct", "@sage/xtrem-intacct/synchronous_test": "Prueba sincrónica", "@sage/xtrem-intacct/user-id-required": "Configuración de integración con Sage Intacct: introduce el identificador del usuario.", "@sage/xtrem-intacct/user-password-required": "Configuración de integración con Sage Intacct: introduce la contraseña del usuario.", "@sage/xtrem-intacct/valid-credentials-need": "Introduce credenciales válidas de Sage Intacct.", "@sage/xtrem-intacct/xtrem-object-invalid": "{{nodeName}} no es un nodo."}