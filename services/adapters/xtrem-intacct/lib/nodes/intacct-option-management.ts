import type { Context } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import { intacctOption } from '../service-options/intacct-option';

@decorators.subNode<IntacctOptionManagement>({
    extends: () => xtremStructure.nodes.BaseOptionManagement,
    isPublished: true,
})
export class IntacctOptionManagement extends xtremStructure.nodes.BaseOptionManagement {
    @decorators.query<typeof IntacctOptionManagement, 'isServiceOptionActiveFunction'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'boolean',
        },
    })
    static isServiceOptionActiveFunction(context: Context) {
        return IntacctOptionManagement.baseIsServiceOptionActiveFunction(context, intacctOption);
    }

    @decorators.mutation<typeof IntacctOptionManagement, 'serviceOptionChange'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'boolean',
        },
    })
    static async serviceOptionChange(context: Context): Promise<boolean> {
        const serviceOptionState = await IntacctOptionManagement.baseServiceOptionChange(context, intacctOption);
        await context.serviceOptionManager.setServiceOptionActive(
            context,
            xtremStructure.serviceOptions.openItemPageOption,
            !serviceOptionState,
        );
        /** Disable or Activate intacct-gateway package  */
        if (serviceOptionState) {
            await xtremSystem.nodes.SysPackAllocation.activate(context, '@sage/xtrem-intacct-finance');
            await xtremSystem.nodes.SysPackAllocation.activate(context, '@sage/xtrem-intacct-gateway');
        } else {
            await xtremSystem.nodes.SysPackAllocation.deactivate(context, '@sage/xtrem-intacct-finance');
            await xtremSystem.nodes.SysPackAllocation.deactivate(context, '@sage/xtrem-intacct-gateway');
        }
        return serviceOptionState;
    }
}
