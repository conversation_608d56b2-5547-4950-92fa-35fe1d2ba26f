import type { AnyR<PERSON>ord, AsyncResponse, Context, NodeQueryFilter } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import type { integer } from '@sage/xtrem-shared';
import * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremIntacct from '..';

@decorators.subNode<IntacctNodeState>({
    extends: () => xtremSynchronization.nodes.SynchronizationState,
    canCreate: true,
    isAbstract: true,
    isPublished: true,
})
export class IntacctNodeState extends xtremSynchronization.nodes.SynchronizationState {
    @decorators.stringProperty<IntacctNodeState, 'intacctId'>({
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
        isStored: true,
    })
    readonly intacctId: Promise<string>;

    @decorators.integerProperty<IntacctNodeState, 'recordNo'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly recordNo: Promise<integer | null>;

    @decorators.referenceProperty<IntacctNodeState, 'intacctConfiguration'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremIntacct.nodes.Intacct,
        computeValue() {
            return xtremIntacct.nodes.Intacct.defaultInstance(this.$.context);
        },
    })
    intacctConfiguration: Promise<xtremIntacct.nodes.Intacct | null>;

    override async resetThirdPartyIdBulkMutation(): Promise<boolean> {
        await this.$.context.batch.logMessage('info', `Resetting intacctId \n before : ${await this.intacctId}`);
        await this.$.set({ intacctId: await this.getIntacctId() } as any); //  intacctId isn't available in the current context
        await this.$.context.batch.logMessage('info', `After : ${await this.intacctId}`);
        await this.$.save();
        return true;
    }

    async getIntacctId(): Promise<string> {
        throw this.$.factory.systemError(`getIntacctId method not implemented ${await this.$.getNaturalKeyValue()}`);
    }

    /**
     *  used to filter intacct data on mapping page
     * @param _context
     * @returns
     */
    static intacctFilter(context: Context): AsyncResponse<xtremIntacct.interfaces.WhereFields[]> {
        context.logger.debug(() => 'Not yet implemented');

        return [];
    }

    /**
     *  used to filter xtrem data on  mapping page
     * @param filterValue
     * @returns
     */
    static xtremFilter(filterValue: string): NodeQueryFilter<any> {
        return { id: { _regex: filterValue } };
    }

    /**
     * Specific init Payload to save intacct objects
     * @param payload
     * @returns
     */
    static initPayload(_context: Context, payload: any): AsyncResponse<AnyRecord> {
        return payload;
    }

    /**
     * Specific delete function from intacct objects
     * @param xtremSysId
     * @returns
     */
    static deactivateRecord(_context: Context, xtremSysId: number) {
        return xtremSysId;
    }
}
