import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremIntacct from '../../index';

@decorators.node<IntacctLine>({
    package: 'xtrem-intacct-gateway',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { intacct: 1, country: 1 }, isUnique: true }],
    serviceOptions: () => [xtremIntacct.serviceOptions.intacctOption],
})
export class IntacctLine extends Node {
    @decorators.referenceProperty<IntacctLine, 'intacct'>({
        node: () => xtremIntacct.nodes.Intacct,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
    })
    readonly intacct: Reference<xtremIntacct.nodes.Intacct>;

    /** country of the intacct instance  */
    @decorators.referenceProperty<IntacctLine, 'country'>({
        isStored: true,
        isPublished: true,
        node: () => xtremStructure.nodes.Country,
        async control(cx, val: xtremStructure.nodes.Country) {
            if (!(await val.taxSolution)) {
                cx.error.addLocalized(
                    '@sage/xtrem-intacct/nodes__intacct_line__error_no_tax_solution',
                    'You cannot select a country without a tax solution.',
                );
            }
        },
    })
    readonly country: Reference<xtremStructure.nodes.Country>;

    @decorators.stringProperty<IntacctLine, 'taxSolution'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
        lookupAccess: true,
    })
    readonly taxSolution: Promise<string>;

    @decorators.referenceProperty<IntacctLine, 'taxCategory'>({
        isStored: true,
        isPublished: true,
        node: () => xtremTax.nodes.TaxCategory,
        dependsOn: ['country'],

        async defaultValue() {
            return (await (await (await this.country)?.legislation)?.id) === 'US'
                ? this.$.context.read(xtremTax.nodes.TaxCategory, { id: 'PLACEHOLDER' })
                : this.$.context.read(xtremTax.nodes.TaxCategory, { id: 'VAT' });
        },
        async isFrozen() {
            return (await (await (await this.country)?.legislation)?.id) !== 'US';
        },
    })
    readonly taxCategory: Reference<xtremTax.nodes.TaxCategory>;
}
