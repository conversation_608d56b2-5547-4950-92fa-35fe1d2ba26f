import type * as xtremCommunication from '@sage/xtrem-communication';
import type { AnyRecord, AsyncResponse, Context, datetime, integer, NodeQueryFilter } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, Node } from '@sage/xtrem-core';
import type * as xtremIntacct from '..';

/** DEPRECATED - will be deleted  */

@decorators.node<IntacctNode>({
    package: 'xtrem-intacct-gateway',
})
export class IntacctNode extends Node {
    getIntacctNode(): xtremIntacct.nodes.IntacctNodeState {
        const error = 'getIntacctNode not implemented';
        this.$.context.logger.error(error);
        throw new BusinessRuleError(error);
    }

    /**
     *  Standard Properties needed For intacct Mapping  -- all of them will be deleted
     */
    intacctId: Promise<string>;

    isActive: Promise<boolean>;

    pCanUpdateFromExternalIntegration: boolean;

    isIntacct: Promise<boolean>;

    recordNo: Promise<integer>;

    intacctIntegrationState: Promise<xtremCommunication.enums.IntegrationState>;

    intacctLastIntegrationDate: Promise<datetime>;

    skipCallIntacctApi?: boolean;

    excludeRecord?: Promise<boolean>;

    intacctUrl: Promise<string>;

    pExternalIntegrationMessage?: string;

    /**
     *  used to filter intacct data
     * @param _context
     * @returns
     */
    static intacctFilter(context: Context): AsyncResponse<xtremIntacct.interfaces.WhereFields[]> {
        context.logger.debug(() => 'Not yet implemented');

        return [];
    }

    static xtremFilter(filterValue: string): NodeQueryFilter<any> {
        return { id: { _regex: filterValue } };
    }

    /**
     * Specific init Payload to save intacct objects
     * @param payload
     * @returns
     */
    static initPayload(_context: Context, payload: any): AsyncResponse<AnyRecord> {
        return payload;
    }

    /**
     * Specific delete function from intacct objects
     * @param xtremSysId
     * @returns
     */
    static deactivateRecord(_context: Context, xtremSysId: number) {
        return xtremSysId;
    }
}
