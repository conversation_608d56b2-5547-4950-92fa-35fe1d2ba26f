import type * as IA from '@intacct/intacct-sdk';
import * as xtremCommunication from '@sage/xtrem-communication';
import type { AnyRecord, Collection, Context, datetime, Reference } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, Logger, Node, SystemError, Uuid } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremIntacct from '../../index';
import { noIntacctInstance } from '../functions/messages';

const logger = Logger.getLogger(__filename, 'intacct');
@decorators.node<Intacct>({
    package: 'xtrem-intacct-gateway',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
    serviceOptions: () => [xtremIntacct.serviceOptions.intacctOption],
    async controlEnd(cx) {
        if (
            (await this.isActive) &&
            (await this.$.context.query(Intacct, { filter: { isActive: true, id: { _not: { _eq: await this.id } } } })
                .length) > 0
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-intacct/only-one-intacct-instance',
                'You can have only one intacct instance active.',
            );
        }
        if ((await this.isActive) && !(await this.isFullFilledAuthentification)) {
            xtremIntacct.functions.Messages.validCredentialsNeeded(cx);
        }
        if ((await this.isActive) && (await this.isFullFilledAuthentification)) {
            try {
                await new xtremIntacct.classes.sdk.Functions.ValidCredentials(
                    this.$.context,
                    this,
                    await this.getClient(), // If not it will pass the wrong ClientConfig ( the saved one )
                ).executeResult();
            } catch (ex) {
                // Returned message : ********** This user is in LOCKEDOUT state [Support ID: AWe18EB030%7EY2pcnP0t4Bf-t7TW3WRPpgAAAA8]
                const message = (ex.message as string)
                    .replace(/PL\d*\s/, '')
                    .replace(/\[Support ID:[^[\]]*\]/, '')
                    .replace(/XL\d*\s/, '');
                xtremIntacct.functions.Messages.validCredentialsNeeded(cx, message);
            }
        }
        // check if there are no duplicate entries for a country in the lines
        await xtremIntacct.events.controls.Intacct.checkDuplicateCountries(this, cx);
    },
    async saveEnd() {
        await this.$.context.serviceOptionManager.setServiceOptionActive(
            this.$.context,
            xtremStructure.serviceOptions.intacctActivationOption,
            await this.isActive,
        );
        await this.$.context.serviceOptionManager.setServiceOptionActive(
            this.$.context,
            xtremStructure.serviceOptions.openItemPageOption,
            !(await this.isActive),
        );
    },
})
export class Intacct extends Node {
    @decorators.stringProperty<Intacct, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
    })
    readonly id: Promise<string>;

    @decorators.booleanProperty<Intacct, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.booleanProperty<Intacct, 'isRequestAsynchronous'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
    })
    readonly isRequestAsynchronous: Promise<boolean>;

    @decorators.booleanProperty<Intacct, 'isContactListCleaned'>({
        isPublished: true,
        isStored: true,
        defaultValue: false,
    })
    isContactListCleaned: Promise<boolean>;

    @decorators.booleanProperty<Intacct, 'isCategoryNameClean'>({
        isPublished: true,
        isStored: true,
        defaultValue: false,
    })
    isCategoryNameClean: Promise<boolean>;

    /** exclude the display contact from contact list ( yes by default) */
    @decorators.booleanProperty<Intacct, 'isDisplayContactHidden'>({
        isPublished: true,
        isStored: true,
        defaultValue: true,
    })
    isDisplayContactHidden: Promise<boolean>;

    _Config: xtremIntacct.interfaces.IaConfiguration | undefined;

    _sessionConfig: IA.ClientConfig;

    _sessionId: string;

    _sessionExpiration: datetime;

    /** dateFormat from intacct api  */
    static dateFormat = 'MM/DD/YYYY HH:mm:ss';

    static getIntacctPackage(context: Context): xtremIntacct.interfaces.IaConfiguration | undefined {
        return context.configuration.getPackageConfig<xtremIntacct.interfaces.IaConfiguration>(
            '@sage/xtrem-intacct-gateway',
        );
    }

    /**
     * Retrive what is in @sage/xtrem-intacct-gateway
     * Warn : if in cache we dont get the conf again
     * @returns IaConfiguration
     */
    getIntacctConfig(): xtremIntacct.interfaces.IaConfiguration {
        if (this._Config) {
            return this._Config;
        }

        this._Config =
            this.$.context.configuration.getPackageConfig<xtremIntacct.interfaces.IaConfiguration>(
                '@sage/xtrem-intacct-gateway',
            );

        if (!this._Config) {
            logger.error(
                () =>
                    `Couldn't find a valid configuration for the '@sage/xtrem-intacct-gateway' package.
                Add a valid configuration under 'packages->@sage/xtrem-intacct-gateway' in your 'xtrem-config.yml'.`,
            );
            if (!this.$.context.testMode) {
                throw new SystemError('Intacct Gateway configuration not found.');
            } else {
                return {
                    controlIdTableName: '',
                    senderId: '',
                    senderPassword: '',
                    endpointUrl: '',
                    policyId: '',
                    defaultAsyncTimeout: 240,
                };
            }
        }
        return this._Config;
    }

    /**
     * Get companyId userID & userPassword from the database
     * Get senderId & senderPassword from the xtrem-config file
     * @param entityId
     * @returns
     */
    async getClient(entityId?: string): Promise<IA.ClientConfig> {
        const { defaultAsyncTimeout, policyId, controlIdTableName, ...interFaceConfig } = this.getIntacctConfig();
        const newConfig = { ...interFaceConfig };
        newConfig.entityId = entityId;
        newConfig.companyId = await this.companyId;
        newConfig.userId = await this.userId;
        newConfig.userPassword = await this.$.decryptValue('userPassword');

        logger.debug(
            () =>
                `Intacct ClientConfig\n \t-companyId: '${newConfig.companyId || ''}'\n \t-senderId:'${
                    newConfig.senderId || ''
                }'\n \t-userId:'${newConfig.userId || ''}\n \t-userPassword lenght ${
                    newConfig.userPassword?.length || ''
                }'`,
        );
        return newConfig as IA.ClientConfig;
    }

    /** Request for all communication with intacct, always the same - not related to specific tenant   */
    @decorators.stringProperty<Intacct, 'senderId'>({
        isPublished: true,
        dataType: () => dataTypes.code,
        computeValue() {
            return this.getIntacctConfig().senderId;
        },
    })
    readonly senderId: Promise<string>;

    /**
     * password for SenderId
     */
    @decorators.stringProperty<Intacct, 'senderPassword'>({
        isPublished: true,
        isStoredEncrypted: true,
        dataType: () => dataTypes.password,
        getValue() {
            return '************';
        },
    })
    readonly senderPassword: Promise<string>;

    /**
     * Refer to the instance of intacct
     */
    @decorators.stringProperty<Intacct, 'companyId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctCompanyIDDataType,
        exportValue: '',
    })
    readonly companyId: Promise<string>;

    /**
     * UserId of webservice Declared in intacct instance
     */
    @decorators.stringProperty<Intacct, 'userId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctRecordNameDataType,
        exportValue: '',
    })
    readonly userId: Promise<string>;

    /**
     *  password for entityId
     */
    @decorators.stringProperty<Intacct, 'userPassword'>({
        isStoredEncrypted: true,
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.password,
        exportValue: '',
    })
    readonly userPassword: Promise<string>;

    @decorators.booleanProperty<Intacct, 'isFullFilledAuthentification'>({
        isPublished: true,
        async getValue() {
            return !!(await this.userId) && !!(await this.userPassword) && !!(await this.companyId);
        },
    })
    readonly isFullFilledAuthentification: Promise<boolean>;

    /**
     *  Url of the intacct api
     */
    @decorators.stringProperty<Intacct, 'endpointUrl'>({
        isPublished: true,
        dataType: () => dataTypes.url,
        computeValue() {
            return (
                this.getIntacctConfig().endpointUrl || xtremIntacct.classes.sdk.Credentials.Endpoint.DEFAULT_ENDPOINT
            );
        },
    })
    readonly endpointUrl: Promise<string>;

    /**
     *  Entity Id of the user ID, returned by the getSessionID
     */
    @decorators.stringProperty<Intacct, 'entityId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctRecordNameDataType,
        exportValue: '',
    })
    readonly entityId: Promise<string>;

    /**
     *  Url of the intacct api
     */
    @decorators.stringProperty<Intacct, 'sessionId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctSessionIDDataType,
        exportValue: '',
    })
    readonly sessionId: Promise<string>;

    @decorators.datetimeProperty<Intacct, 'sessionExpiration'>({
        isStored: true,
        isPublished: true,
    })
    readonly sessionExpiration: Promise<datetime>;

    @decorators.stringProperty<Intacct, 'controlIdTableName'>({
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
        computeValue() {
            return this.getIntacctConfig().controlIdTableName || '';
        },
    })
    readonly controlIdTableName: Promise<string>;

    @decorators.stringProperty<Intacct, 'policyId'>({
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
        computeValue() {
            // befou-test-tenant on cluster-ci
            if (this.$.context.tenantId === 'qsFTjOwO8RAY_oa8G4xP4') {
                return 'xt_async_2';
            }
            return this.getIntacctConfig().policyId || '';
        },
    })
    readonly policyId: Promise<string>;

    @decorators.integerProperty<Intacct, 'asyncTimeout'>({
        isPublished: true,
        computeValue() {
            return this.getIntacctConfig().defaultAsyncTimeout || 240;
        },
    })
    readonly asyncTimeout: Promise<number>;

    @decorators.jsonProperty<Intacct, 'infoXTreeMAuditTrail'>({
        isPublished: true,
        async computeValue() {
            if (await this.isFullFilledAuthentification) {
                const ptApplication = await new xtremIntacct.classes.sdk.Functions.ReadByName<
                    {
                        DESCRIPTION: string;
                        VERSION: string;
                    }[]
                >(this.$.context, {
                    intacctInstanceId: await this.id,
                    objectName: 'PTAPPLICATION',
                    fields: ['DESCRIPTION', 'VERSION'],
                    filter: ['xtreem_audit_trail'],
                }).execute();
                return ptApplication && ptApplication.length ? ptApplication[0] : { DESCRIPTION: '', VERSION: '' };
            }
            return { DESCRIPTION: '', VERSION: '' };
        },
    })
    readonly infoXTreeMAuditTrail: Promise<{ DESCRIPTION: string; VERSION: string }>;

    @decorators.jsonProperty<Intacct, 'mustXtreemAuditTrail'>({
        isPublished: true,
        async computeValue() {
            return {
                DESCRIPTION: await xtremIntacct.functions.getXtreemTenantName(this.$.context),
                VERSION: xtremIntacct.functions.getVersionXtreemAuditTrail(this.$.context),
            };
        },
    })
    readonly mustXtreemAuditTrail: Promise<{ DESCRIPTION: string; VERSION: string }>;

    /**
     * Check if the customPlatformObject XTreeMAuditTrail is available on the current instance
     */
    @decorators.booleanProperty<Intacct, 'isXTreeMAuditTrail'>({
        isPublished: true,
        async computeValue() {
            return (await this.isFullFilledAuthentification)
                ? !!(await new xtremIntacct.classes.sdk.Functions.Inspect(this.$.context, 'XTreeM_Audit_Trail', {
                      intacctInstanceId: await this.id,
                  }).execute())
                : false;
        },
    })
    readonly isXTreeMAuditTrail: Promise<boolean>;

    /** legislation of the intacct instance  */
    @decorators.referenceProperty<Intacct, 'legislation'>({
        isStored: true,
        isPublished: true,
        node: () => xtremStructure.nodes.Legislation,
        dependsOn: ['chartOfAccount'],
        async defaultValue() {
            return (await this.chartOfAccount).legislation;
        },
    })
    readonly legislation: Reference<xtremStructure.nodes.Legislation>;

    /** chartOfAccount of the intacct instance  */
    @decorators.referenceProperty<Intacct, 'chartOfAccount'>({
        isStored: true,
        isPublished: true,
        node: () => xtremStructure.nodes.ChartOfAccount,
    })
    readonly chartOfAccount: Reference<xtremStructure.nodes.ChartOfAccount>;

    /** Intacct tax solution */
    @decorators.stringProperty<Intacct, 'taxSolution'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly taxSolution: Promise<string>;

    @decorators.referenceProperty<Intacct, 'taxCategory'>({
        isStored: true,
        isPublished: true,
        node: () => xtremTax.nodes.TaxCategory,
        dependsOn: ['legislation'],

        async defaultValue() {
            return (await (await this.legislation).id) === 'US'
                ? this.$.context.read(xtremTax.nodes.TaxCategory, { id: 'PLACEHOLDER' })
                : this.$.context.read(xtremTax.nodes.TaxCategory, { id: 'VAT' });
        },
        async isFrozen() {
            return (await (await this.legislation).id) !== 'US';
        },
    })
    readonly taxCategory: Reference<xtremTax.nodes.TaxCategory>;

    @decorators.collectionProperty<Intacct, 'messageHistory'>({
        isPublished: true,
        node: () => xtremCommunication.nodes.SysMessageHistory,
        getFilter() {
            return {
                integrationSolution: 'intacct',
            };
        },
        orderBy: { _id: -1 },
    })
    readonly messageHistory: Collection<xtremCommunication.nodes.SysMessageHistory>;

    @decorators.jsonProperty<Intacct, 'entityList'>({
        isPublished: true,
        isStored: true,
    })
    readonly entityList: Promise<{ list: Array<{ id: string; name: string }> }>;

    @decorators.enumProperty<Intacct, 'transactionIntegrationLevel'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremIntacct.enums.transactionIntegrationLevelDataType,
        lookupAccess: true,
        defaultValue: 'topLevel',
    })
    readonly transactionIntegrationLevel: Promise<xtremIntacct.enums.TransactionIntegrationLevel>;

    @decorators.collectionProperty<Intacct, 'lines'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'intacct',
        node: () => xtremIntacct.nodes.IntacctLine,
    })
    readonly lines: Collection<xtremIntacct.nodes.IntacctLine>;

    /**
     *  get the intacct default connected instance
     * @param context
     * @returns
     */
    @decorators.query<typeof Intacct, 'defaultInstance'>({
        isPublished: true,
        parameters: [
            {
                name: 'isThrowing',
                type: 'boolean',
                isMandatory: false,
            },
        ],

        return: {
            type: 'instance',
            node: () => Intacct,
        },
    })
    static async defaultInstance(context: Context, isThrowing = false): Promise<Intacct | null> {
        const intacctInstance = await context
            .query(xtremIntacct.nodes.Intacct, { filter: { isActive: true } })
            .toArray();
        if (intacctInstance.length > 1) {
            throw new SystemError(
                context.localize(
                    '@sage/xtrem-intacct/only-one-intacct-instance',
                    'You can have only one intacct instance active.',
                ),
            );
        }
        if (isThrowing && intacctInstance.length === 0) {
            throw new BusinessRuleError(noIntacctInstance(context));
        }
        return intacctInstance.length ? intacctInstance[0] : null;
    }

    /**
     *  get All Entities of the current instance
     * @param context
     * @returns
     */
    @decorators.query<typeof Intacct, 'getEntities'>({
        isPublished: true,
        // TODO: TYPO in name
        parameters: [
            {
                name: 'intacctInstanceId',
                type: 'string',
                isMandatory: false,
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    name: 'string',
                    id: 'string',
                },
            },
            isMandatory: true,
        },
    })
    static getEntities(context: Context, intacctInstanceId?: string): Promise<{ name: string; id: string }[]> {
        return new xtremIntacct.classes.sdk.Functions.Query(context, {
            objectName: 'LOCATIONENTITY',
            fields: ['LOCATIONID', 'NAME'],
            resultFunction: result =>
                result.data.map(field => {
                    return {
                        id: field.LOCATIONID,
                        name: field.NAME,
                    };
                }),
            intacctInstanceId,
        }).execute();
    }

    /**
     *  refresh All Entities of the current instance
     * @param context
     * @returns
     */
    @decorators.mutation<typeof Intacct, 'refreshEntities'>({
        isPublished: true,
        // TODO: TYPO in name
        parameters: [
            {
                name: 'intacctInstanceId',
                type: 'string',
                isMandatory: false,
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    name: 'string',
                    id: 'string',
                },
            },
            isMandatory: true,
        },
    })
    static async refreshEntities(context: Context, intacctInstanceId: string): Promise<{ name: string; id: string }[]> {
        const intacctInstance = await context.read(Intacct, { id: intacctInstanceId }, { forUpdate: true });

        const intacctEntityCount = (
            await new xtremIntacct.classes.sdk.Functions.Query(context, {
                objectName: 'LOCATIONENTITY',
                fields: [{ name: 'LOCATIONID', type: 'count' }],
                resultFunction: result => result.data.map(field => Number(field['COUNT.LOCATIONID'])),
                intacctInstanceId,
            }).execute()
        )[0];
        if (
            (await intacctInstance.entityList).list &&
            intacctEntityCount === (await intacctInstance.entityList).list.length
        ) {
            return (await intacctInstance.entityList).list;
        }
        const entityList = await Intacct.getEntities(context, intacctInstanceId);

        await intacctInstance.$.set({ entityList: { list: entityList } });
        await intacctInstance.$.save();

        return entityList;
    }

    @decorators.mutation<typeof Intacct, 'getEntitiesAsynchronous'>({
        isPublished: true,
        parameters: [{ name: 'intacctInstanceId', type: 'string', isMandatory: false }],
        return: {
            type: 'object',
            properties: {
                controlId: 'string',
                status: 'string',
                xml: 'string',
            },
        },
    })
    static async getEntitiesAsynchronous(
        context: Context,
        intacctInstanceId?: string,
    ): Promise<{ status: string; controlId: string; xml: string }> {
        // TODO : use SDK function query & unit test it
        const result = await new xtremIntacct.classes.sdk.Functions.Query(context, {
            objectName: 'LOCATIONENTITY',
            fields: ['LOCATIONID', 'NAME'],
            synchronous: false,
            intacctInstanceId,
        }).executeAsync();
        return {
            status: result.status,
            controlId: result.control.controlId,
            xml: JSON.stringify(result.xml),
        };
    }

    @decorators.mutation<typeof Intacct, 'sendEvent'>({
        isPublished: true,
        parameters: [
            {
                name: 'messageKind',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'dataIntacctEvent',
                type: 'object',
                properties: {
                    companyId: 'string',
                    object: 'string',
                    change: 'string',
                    recordNumber: 'integer',
                    recordId: 'string',
                    intacctIdName: 'string',
                },
                isMandatory: true,
            },
            {
                name: 'dataBase64',
                type: 'string',
                isMandatory: false,
            },
        ],
        return: 'boolean',
    })
    static async sendEvent(
        context: Context,
        messageKind: string,
        dataIntacctEvent: xtremIntacct.interfaces.IntacctEventPayload,
        dataBase64?: string,
    ): Promise<boolean> {
        let dataPayload = {};
        if (messageKind === 'IntacctEvent' && dataIntacctEvent) {
            dataPayload = {
                companyId: dataIntacctEvent.companyId,
                object: dataIntacctEvent.object,
                change: dataIntacctEvent.change,
                recordNumber: dataIntacctEvent.recordNumber,
                recordId: dataIntacctEvent.recordId,
                lineId: Uuid.generate().toString(),
            };
        } else {
            dataPayload = {
                context: {
                    version: 1,
                    function: '',
                    nodeName: dataIntacctEvent.object,
                    parameters: { _id: dataIntacctEvent.recordId },
                    intacctIdName: dataIntacctEvent.intacctIdName,
                    userId: (await context.user)!._id,
                    retry: 0,
                } as xtremIntacct.sharedFunctions.interfaces.ContextCallback,
                intacctResponse: dataBase64 || '',
            };
        }

        await context.send(xtremIntacct.queues.intacctSend, {
            payload: dataPayload,
            attributes: {
                tenantId: context.tenantId!,
                email: (await context.user)!.email,
                locale: context.currentLocale,
                MessageKind: messageKind,
            },
        });
        return true;
    }

    @decorators.mutation<typeof Intacct, 'sendXmlRequest'>({
        isPublished: true,
        parameters: [
            {
                name: 'entityId',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'xmlQuery',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'isSynchronous',
                type: 'boolean',
                isMandatory: true,
            },
        ],
        return: 'string',
    })
    static async sendXmlRequest(
        context: Context,
        entityId: string,
        xmlQuery: string,
        isSynchronous: true,
    ): Promise<string> {
        const request = new xtremIntacct.classes.sdk.Functions.XmlQuery<AnyRecord[]>(context, xmlQuery.toString(), {
            synchronous: isSynchronous,
            entityId,
        });
        const result = JSON.stringify(isSynchronous ? await request.execute() : await request.executeAsync());
        return `${result || ''}${request.diagnosesMessages()}`;
    }

    @decorators.asyncMutation<typeof Intacct, 'installXtreemAuditTrail'>({
        isPublished: true,
        parameters: [
            {
                name: 'intacctInstanceId',
                type: 'string',
                isMandatory: false,
            },
        ],
        return: 'boolean',
    })
    static async installXtreemAuditTrail(context: Context, intacctInstanceId?: string): Promise<boolean> {
        await context.batch.updateProgress({ totalCount: 1, detail: 'install' });
        await xtremIntacct.functions.installApplicationXtreemAuditTrail(context, intacctInstanceId);
        await context.batch.updateProgress({ successCount: 1 });
        return true;
    }

    /**
     * Retry the request
     * @param context
     * @param sysMessageHistoryId
     * @returns true if retry false if not
     */
    @decorators.mutation<typeof Intacct, 'retry'>({
        isPublished: true,
        return: 'boolean',
        parameters: [{ name: 'sysMessageHistoryId', type: 'string', isMandatory: true }],
    })
    static async retry(context: Context, sysMessageHistoryId: string): Promise<boolean> {
        const history = await context.tryRead(xtremCommunication.nodes.SysMessageHistory, {
            integrationSolution: 'intacct',
            id: sysMessageHistoryId,
        });
        if (history) {
            const retry =
                ((await history.context) as xtremIntacct.sharedFunctions.interfaces.ContextCallback).retry || 0 + 1;
            if (retry < 5) {
                await new xtremIntacct.classes.sdk.Functions.XmlQuery(context, (await history.sentRequest).toString(), {
                    synchronous: false,
                }).executeAsync({
                    ...(await history.context),
                    retry,
                } as xtremIntacct.sharedFunctions.interfaces.ContextCallback);
                return true;
            }
            throw new BusinessRuleError(
                context.localize('@sage/xtrem-intacct/nodes__intacct/to-many-retry', 'Too many attempts: {{retry}}.', {
                    retry,
                }),
            );
        }
        return false;
    }
}
