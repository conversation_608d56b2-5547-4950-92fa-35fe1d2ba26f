import type { Filter, integer } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { SysMessageHistory } from '@sage/xtrem-communication-api';
import type { GraphApi, IntacctLine, Intacct as IntacctNode } from '@sage/xtrem-intacct-api';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { ChartOfAccount, Country, Legislation } from '@sage/xtrem-structure-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import { labelAsText } from '@sage/xtrem-system/build/lib/client-functions/color-pattern';
import * as SystemDevTools from '@sage/xtrem-system/build/lib/client-functions/dev-tools';
import type { TaxCategory } from '@sage/xtrem-tax-api';
import * as ui from '@sage/xtrem-ui';
import {
    auditTrailInstallUpdate,
    auditTrailMismatchControl,
    auditTrailTitle,
    installXtreemAuditTrailExecute,
    options,
    parseInfo,
} from '../client-functions/audit-trail-management';
import { intacctActivationDeactivationMessage, optionsForPurge } from '../client-functions/common';
import { intacct } from '../menu-items/intacct';
import type { ContextCallback } from '../shared-functions/interfaces';

/**
 * Please update the corresponding cucumber functional test
 * {@link /xtrem/xtrem/services/functional-tests/xtrem-0-prerequisites-test/test/prerequisites-crud-intacct-config.feature}
 * and {@link /xtrem/xtrem/services/functional-tests/xtrem-reference-data-test/test/reference-data-crud-customer-001.feature}
 * and {@link /xtrem/xtrem/services/functional-tests/xtrem-reference-data-test/test/reference-data-crud-supplier-001.feature}
 */
@ui.decorators.page<Intacct, IntacctNode>({
    module: 'xtrem-intacct-gateway',
    node: '@sage/xtrem-intacct/Intacct',
    title: 'Configuration',
    objectTypeSingular: 'Configuration',
    objectTypePlural: 'Configurations',
    idField() {
        return this.id;
    },
    menuItem: intacct,
    mode: 'tabs',
    async defaultEntry() {
        return (
            (
                await this.$.graph
                    .node('@sage/xtrem-intacct/Intacct')
                    .queries.defaultInstance({ _id: true }, {})
                    .execute()
            )?._id || null
        );
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'id' }),
            titleRight: ui.nestedFields.switch({ bind: 'isActive', title: 'Active' }),
            line2: ui.nestedFields.text({ bind: 'companyId', title: 'Company ID' }),
        },
    },
    businessActions() {
        return [this.synchronousTest, this.asynchronousTest, this.save];
    },
    createAction() {
        return this.$standardNewAction;
    },
    detailPanel() {
        return {
            header: this.detailPanelCommunicationHeaderSection,
            sections: [this.detailPanelSendSection, this.detailPanelReceiveSection, this.detailPanelDiagnoseSection],
            footerActions: [],
            isHidden: true,
            isTitleHidden: true,
        };
    },
    onError(error) {
        return utils.formatError(this, error);
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.save,
        });
        this.$.loader.isHidden = true;
        if (this.isActive.value && this.isXTreeMAuditTrail.value && (await auditTrailMismatchControl(this))) {
            await installXtreemAuditTrailExecute(this);
        }
        this.fullFilledAuthentification();

        this.isSynchronous.value = true;
        this.isDevToolActive = await SystemDevTools.areDevToolActive(this, true);
        /** Hide API & API query sections with devTool service option */
        this.eventSection.isHidden = !this.isDevToolActive;
        this.querySection.isHidden = !this.isDevToolActive;

        const entityListArray: { list?: { id: string; name: string }[] } = this.entityList.value
            ? JSON.parse(this.entityList.value)
            : { list: [] };

        this.selectEntityList.options = entityListArray.list?.map(entity => entity.id) || [];
        this.isConfigurationActive = this.isActive.value || false;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.save,
        });
        this.synchronousTest.isDisabled = isDirty;
        this.asynchronousTest.isDisabled = isDirty;
    },
})
export class Intacct extends ui.Page<GraphApi> {
    isConfigurationActive: boolean;

    @ui.decorators.pageAction<Intacct>({
        title: 'Save',
        async onClick() {
            if (this.isActive.value !== this.isConfigurationActive) {
                await intacctActivationDeactivationMessage({
                    page: this,
                    isActive: this.isActive.value || false,
                    legislationIds: this.legislation.value?.id ? [this.legislation.value.id] : [],
                });
                this.isConfigurationActive = this.isActive.value || false;
            }
            return this.$standardSaveAction.execute(true);
        },
    })
    save: ui.PageAction;

    isDevToolActive = false;

    @ui.decorators.section<Intacct>({
        title: 'Intacct',
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.section<Intacct>({
        title: 'Defaults',
    })
    defaultSection: ui.containers.Section;

    @ui.decorators.section<Intacct>({
        title: 'Communication',
    })
    detailPanelCommunicationHeaderSection: ui.containers.Section;

    @ui.decorators.section<Intacct>({
        title: 'Sent',
    })
    detailPanelSendSection: ui.containers.Section;

    @ui.decorators.section<Intacct>({
        title: 'Receive',
    })
    detailPanelReceiveSection: ui.containers.Section;

    @ui.decorators.section<Intacct>({
        title: 'Diagnoses',
    })
    detailPanelDiagnoseSection: ui.containers.Section;

    @ui.decorators.block<Intacct>({
        parent() {
            return this.mainSection;
        },
        title: 'Configuration',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.block<Intacct>({
        parent() {
            return this.mainSection;
        },
        title: 'Configuration',
        isTitleHidden: true,
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.block<Intacct>({
        parent() {
            return this.mainSection;
        },
        title: 'Audit trail',
        isTitleHidden: true,
    })
    auditTrailBlock: ui.containers.Block;

    @ui.decorators.block<Intacct>({
        parent() {
            return this.defaultSection;
        },
        title: 'Defaults',
        isTitleHidden: true,
        width: 'large',
    })
    defaultBlock: ui.containers.Block;

    @ui.decorators.block<Intacct>({
        parent() {
            return this.defaultSection;
        },
        title: 'Contact',
        width: 'small',
    })
    contactListBlock: ui.containers.Block;

    @ui.decorators.textField<Intacct>({})
    _id: ui.fields.Text;

    @ui.decorators.switchField<Intacct>({
        parent() {
            return this.mainBlock;
        },
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<Intacct>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorIsActive: ui.fields.Separator;

    @ui.decorators.textField<Intacct>({
        parent() {
            return this.mainBlock;
        },
        title: 'ID',
    })
    id: ui.fields.Text;

    @ui.decorators.textField<Intacct>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        title: 'Endpoint URL',
        width: 'medium',
    })
    endpointUrl: ui.fields.Text;

    @ui.decorators.textField<Intacct>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        title: 'Sender ID',
    })
    senderId: ui.fields.Text;

    @ui.decorators.textField<Intacct>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Company ID',
    })
    companyId: ui.fields.Text;

    @ui.decorators.textField<Intacct>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        title: 'Policy Id',
    })
    policyId: ui.fields.Text;

    @ui.decorators.switchField<Intacct>({
        parent() {
            return this.auditTrailBlock;
        },
        title: 'Sage DMO audit trail',
        isReadOnly: true,
    })
    isXTreeMAuditTrail: ui.fields.Switch;

    @ui.decorators.labelField<Intacct>({
        parent() {
            return this.auditTrailBlock;
        },
        title: 'Info',
        borderColor() {
            return labelAsText('borderColor');
        },
        map(value) {
            return parseInfo(value);
        },
        width: 'medium',
    })
    infoXTreeMAuditTrail: ui.fields.Label;

    @ui.decorators.textField<Intacct>({
        parent() {
            return this.auditTrailBlock;
        },
        isHidden: true,
        title: 'Info',
    })
    mustXtreemAuditTrail: ui.fields.Text;

    @ui.decorators.buttonField<Intacct>({
        parent() {
            return this.auditTrailBlock;
        },
        isTransient: true,
        map() {
            return ui.localize(
                '@sage/xtrem-intacct/pages__intacct__install_xtreem_audit_trail_button_text',
                'Install or update Sage DMO audit trail',
            );
        },
        async onClick() {
            if (
                await utils.confirmDialogToBoolean(
                    this.$.dialog.confirmation('warn', auditTrailTitle, auditTrailInstallUpdate, options),
                )
            ) {
                await installXtreemAuditTrailExecute(this);
            }
        },
    })
    installXTreeMAuditTrail: ui.fields.Button;

    @ui.decorators.separatorField<Intacct>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    firstSeparator: ui.fields.Separator;

    @ui.decorators.referenceField<Intacct, Legislation>({
        parent() {
            return this.defaultBlock;
        },
        lookupDialogTitle: 'Select legislation',
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
        placeholder: 'Select ...',
        isMandatory: true,
        async onChange() {
            await this.$.fetchDefaults(['taxCategory']);
        },
    })
    legislation: ui.fields.Reference<Legislation>;

    @ui.decorators.referenceField<Intacct, ChartOfAccount>({
        parent() {
            return this.defaultBlock;
        },
        title: 'Chart of account',
        lookupDialogTitle: 'Select chart of account',
        node: '@sage/xtrem-structure/ChartOfAccount',
        valueField: 'name',
        columns: [ui.nestedFields.text({ bind: 'name' })],
        placeholder: 'Select chart of account',
        isMandatory: true,
    })
    chartOfAccount: ui.fields.Reference<ChartOfAccount>;

    @ui.decorators.dropdownListField<Intacct>({
        parent() {
            return this.defaultBlock;
        },
        title: 'Transaction integration level',
        optionType: '@sage/xtrem-intacct/TransactionIntegrationLevel',
        async onChange() {
            const successFinanceIntegrationRecords = await this.getFinanceIntegrationRecordCount(true);
            const noSuccessFinanceIntegrationRecords = await this.getFinanceIntegrationRecordCount(false);

            if (successFinanceIntegrationRecords + noSuccessFinanceIntegrationRecords > 0) {
                const message =
                    noSuccessFinanceIntegrationRecords === 0
                        ? ui.localize(
                              '@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level_success',
                              'Transactions were already posted to Sage Intacct. Apply changes to future posts only, creating inconsistencies with previous posts.',
                          )
                        : ui.localize(
                              '@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level',
                              'It is recommended to complete {{num}} unposted transactions to avoid errors. Cancel to complete unposted transactions or apply changes to future posts only.',
                              { num: noSuccessFinanceIntegrationRecords },
                          );
                const confirmation = await utils.confirmDialogToBoolean(
                    this.$.dialog.confirmation(
                        'warn',
                        ui.localize(
                            '@sage/xtrem-intacct/pages__intacct__update_transaction_integration_level_title',
                            'Change posting settings',
                        ),
                        message,
                        {
                            acceptButton: {
                                text: ui.localize(
                                    '@sage/xtrem-intacct/pages__intacct__confirm_update',
                                    'Apply changes',
                                ),
                            },
                            cancelButton: {
                                text: ui.localize('@sage/xtrem-intacct/pages__intacct__cancel', 'Cancel'),
                            },
                        },
                    ),
                );
                if (!confirmation) {
                    this.transactionIntegrationLevel.value =
                        this.transactionIntegrationLevel.value === 'entityLevel' ? 'topLevel' : 'entityLevel';
                }
            }
        },
    })
    transactionIntegrationLevel: ui.fields.DropdownList;

    @ui.decorators.switchField<Intacct>({
        parent() {
            return this.contactListBlock;
        },
        width: 'large',
        title: 'Reset contact list',
    })
    isContactListCleaned: ui.fields.Switch;

    @ui.decorators.switchField<Intacct>({
        parent() {
            return this.contactListBlock;
        },
        width: 'large',
        title: 'Contact list category name',
    })
    isCategoryNameClean: ui.fields.Switch;

    @ui.decorators.switchField<Intacct>({
        parent() {
            return this.contactListBlock;
        },
        width: 'large',
        title: 'Exclude display contact from the contact list',
    })
    isDisplayContactHidden: ui.fields.Switch;

    @ui.decorators.tableField<Intacct, IntacctLine>({
        parent() {
            return this.defaultBlock;
        },
        bind: 'lines',
        title: 'Lines',
        isTitleHidden: true,
        node: '@sage/xtrem-intacct/IntacctLine',
        canSelect: false,
        orderBy: {
            country: { name: +1 },
        },
        columns: [
            ui.nestedFields.reference<Intacct, IntacctLine, Country>({
                bind: 'country',
                minLookupCharacters: 0,
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({
                        bind: 'legislation',
                        node: '@sage/xtrem-structure/Legislation',
                        nestedFields: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                    ui.nestedFields.technical({
                        bind: 'taxSolution',
                        node: '@sage/xtrem-tax/TaxSolution',
                        nestedFields: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                ],
                validation(_val, rowData: IntacctLine) {
                    if (
                        this.lines.value.some(
                            line =>
                                rowData._id !== line._id && // don't check actual row itself
                                rowData.country.id === line.country?.id,
                        )
                    ) {
                        return ui.localize(
                            '@sage/xtrem-intacct/pages__intacct__duplicate_error',
                            'There are duplicate countries. Make sure that each line has a unique country.',
                        );
                    }
                    return undefined;
                },
                async onChange(_id: number, rowData: ui.PartialNodeWithId<IntacctLine>) {
                    // we do this because fetchDefaults does not work for lines
                    const filter: Filter<TaxCategory> =
                        rowData.country?.legislation?.id === 'US'
                            ? {
                                  id: 'PLACEHOLDER',
                              }
                            : {
                                  id: 'VAT',
                              };

                    [rowData.taxCategory] = extractEdges(
                        await this.$.graph
                            .node('@sage/xtrem-tax/TaxCategory')
                            .query(
                                ui.queryUtils.edgesSelector(
                                    { _id: true, id: true, isActive: true, name: true, description: true },
                                    { filter, first: 1 },
                                ),
                            )
                            .execute(),
                    );
                    this.lines.setRecordValue(rowData);
                },
            }),
            ui.nestedFields.text<Intacct, IntacctLine>({
                title: 'SDMO tax solution',
                bind: { country: { taxSolution: { name: true } } },
            }),
            ui.nestedFields.text<Intacct, IntacctLine>({
                title: 'Sage Intacct tax solution',
                bind: 'taxSolution',
            }),
            ui.nestedFields.reference<Intacct, IntacctLine, TaxCategory>({
                bind: 'taxCategory',
                title: 'Tax category',
                node: '@sage/xtrem-tax/TaxCategory',
                tunnelPage: '@sage/xtrem-tax-data/TaxCategory',
                valueField: 'name',
                columns: [ui.nestedFields.text({ bind: 'name' })],
                isDisabled(_id: number, rowData: IntacctLine) {
                    return rowData.country?.legislation?.id !== 'US';
                },
            }),
        ],
        fieldActions() {
            return [this.addLine];
        },
        dropdownActions: [
            {
                icon: 'delete',
                title: 'Delete line',
                isDestructive: true,
                onClick(rowId: string) {
                    this.lines.removeRecord(rowId);
                },
            },
        ],
    })
    lines: ui.fields.Table<IntacctLine>;

    @ui.decorators.pageAction<Intacct>({
        icon: 'add',
        title: 'Add line',
        onClick() {
            this.lines.addRecord({});
        },
    })
    addLine: ui.PageAction;

    async getFinanceIntegrationRecordCount(checkForSuccess: boolean): Promise<integer> {
        return extractEdges(
            await this.$.graph
                .node('@sage/xtrem-synchronization/SynchronizationState')
                .aggregate.query(
                    ui.queryUtils.edgesSelector(
                        {
                            group: {
                                integration: { id: { _by: 'value' } },
                                node: { package: { name: { _by: 'value' } } },
                                state: { _by: 'value' },
                            },
                            values: {
                                _id: { distinctCount: true },
                            },
                        },
                        {
                            filter: {
                                integration: { id: { _eq: 'intacct' } },
                                node: { package: { name: { _eq: '@sage/xtrem-finance' } } },
                                state: checkForSuccess ? { _eq: 'success' } : { _ne: 'success' },
                            },
                        },
                    ),
                )
                .execute(),
        ).reduce((qty, record) => qty + (record.values._id?.distinctCount || 0), 0);
    }

    @ui.decorators.textField<Intacct>({
        parent() {
            return this.configurationBlock;
        },
        title: 'User ID',
    })
    userId: ui.fields.Text;

    @ui.decorators.textField<Intacct>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Password',
        isPassword: true,
    })
    userPassword: ui.fields.Text;

    @ui.decorators.switchField<Intacct>({
        parent() {
            return this.configurationBlock;
        },
        title: ' Authentication information complete',
        isDisabled: true,
        isHidden: true,
    })
    isFullFilledAuthentification: ui.fields.Switch;

    @ui.decorators.dropdownListField<Intacct>({
        parent() {
            return this.mainBlock;
        },
        title: 'Entities',
        isHidden: true,
        isTransient: true,
        options: ['', '100', '200', '300'],
    })
    entitites: ui.fields.DropdownList;

    @ui.decorators.textAreaField<Intacct>({
        parent() {
            return this.mainBlock;
        },
        title: 'Test result :',
        isFullWidth: true,
        isTransient: true,
        isHidden: true,
    })
    infoBox: ui.fields.TextArea;

    @ui.decorators.section<Intacct>({
        title: 'API',
        isTitleHidden: true,
    })
    eventSection: ui.containers.Section;

    @ui.decorators.block<Intacct>({
        parent() {
            return this.eventSection;
        },
        title: 'SQS event simulation',
    })
    eventBlock: ui.containers.Block;

    @ui.decorators.dropdownListField<Intacct>({
        parent() {
            return this.eventBlock;
        },
        title: 'Message type',
        isTransient: true,
        options: ['IntacctEvent', 'IntacctCallback'],
        onChange() {
            this.recordNumber.isDisabled = this.messageKind.value !== 'IntacctEvent';
            this.dataToSend.isDisabled = this.messageKind.value !== 'IntacctCallback';
        },
    })
    messageKind: ui.fields.DropdownList;

    @ui.decorators.textField<Intacct>({
        parent() {
            return this.eventBlock;
        },
        title: 'Object',
        isTransient: true,
    })
    objectEvent: ui.fields.Text;

    @ui.decorators.textField<Intacct>({
        parent() {
            return this.eventBlock;
        },
        isHidden: true,
        isTransient: true,
    })
    intacctIdName: ui.fields.Text;

    @ui.decorators.dropdownListField<Intacct>({
        parent() {
            return this.eventBlock;
        },
        title: 'Change',
        isTransient: true,
        options: ['create', 'update', 'delete', 'desynchronized'],
    })
    change: ui.fields.DropdownList;

    @ui.decorators.textField<Intacct>({
        parent() {
            return this.eventBlock;
        },
        title: 'Record ID',
        isTransient: true,
    })
    recordId: ui.fields.Text;

    @ui.decorators.textField<Intacct>({
        parent() {
            return this.eventBlock;
        },
        title: 'Record number',
        isTransient: true,
    })
    recordNumber: ui.fields.Text;

    @ui.decorators.textAreaField<Intacct>({
        parent() {
            return this.eventBlock;
        },
        title: 'Send data :',
        isFullWidth: true,
        isTransient: true,
    })
    dataToSend: ui.fields.TextArea;

    @ui.decorators.buttonField<Intacct>({
        isTransient: true,
        map() {
            return ui.localize('@sage/xtrem-intacct/pages__intacct__send_data_button_text', 'Send');
        },
        parent() {
            return this.eventBlock;
        },
        async onClick() {
            await this.$.graph
                .node('@sage/xtrem-intacct/Intacct')
                .mutations.sendEvent(true, {
                    dataIntacctEvent: {
                        companyId: this.companyId.value || '',
                        change: this.change.value ? this.change.value : '',
                        object: this.objectEvent.value ? this.objectEvent.value : '',
                        recordId: this.recordId.value ? this.recordId.value : '',
                        recordNumber: this.recordNumber.value ? this.recordNumber.value : 0,
                        intacctIdName: this.intacctIdName.value || '',
                    },
                    dataBase64: this.dataToSend.value || '',
                    messageKind: this.messageKind.value ? this.messageKind.value : '',
                })
                .execute();
        },
    })
    sendData: ui.fields.Button;

    @ui.decorators.section<Intacct>({
        title: 'API - Query',
        isTitleHidden: true,
    })
    querySection: ui.containers.Section;

    @ui.decorators.block<Intacct>({
        parent() {
            return this.querySection;
        },
    })
    xmlBlock: ui.containers.Block;

    @ui.decorators.textAreaField<Intacct>({
        parent() {
            return this.xmlBlock;
        },
        title: 'XML request',
        isTransient: true,
        isFullWidth: true,
        rows: 20,
    })
    request: ui.fields.TextArea;

    @ui.decorators.switchField<Intacct>({
        isTransient: true,
        parent() {
            return this.xmlBlock;
        },
        title: 'Synchronous',
    })
    isSynchronous: ui.fields.Switch;

    @ui.decorators.labelField<Intacct>({
        isHidden: true,
    })
    entityList: ui.fields.Label;

    @ui.decorators.selectField<Intacct>({
        parent() {
            return this.xmlBlock;
        },
        isTransient: true,
    })
    selectEntityList: ui.fields.Select;

    @ui.decorators.buttonField<Intacct>({
        isTransient: true,
        parent() {
            return this.xmlBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-intacct/pages__intacct__refresh_entity_list', 'Refresh entities');
        },
        async onClick() {
            await this.refreshEntityList();
        },
    })
    refreshEntityListButton: ui.fields.Button;

    async refreshEntityList() {
        if (this.id.value) {
            const entityList = (await this.$.graph
                .node('@sage/xtrem-intacct/Intacct')
                .mutations.refreshEntities({ id: true, name: true }, { intacctInstanceId: this.id.value })
                .execute()) as Array<{ name: string; id: string }>;
            this.selectEntityList.options = entityList.map(entity => entity.id);
            await this.$.router.refresh();
        }
    }

    @ui.decorators.buttonField<Intacct>({
        isTransient: true,
        map() {
            return ui.localize('@sage/xtrem-intacct/pages__intacct__send_request_button_text', 'Send');
        },
        parent() {
            return this.xmlBlock;
        },
        async onClick() {
            if (this.request.value) {
                this.response.value = await this.$.graph
                    .node('@sage/xtrem-intacct/Intacct')
                    .mutations.sendXmlRequest(true, {
                        entityId: this.selectEntityList.value || '',
                        isSynchronous: this.isSynchronous.value || false,
                        xmlQuery: this.request.value,
                    })
                    .execute();
            }
        },
    })
    sendRequest: ui.fields.Button;

    @ui.decorators.textAreaField<Intacct>({
        parent() {
            return this.xmlBlock;
        },
        title: 'XML response',
        isTransient: true,
        isFullWidth: true,
    })
    response: ui.fields.TextArea;

    @ui.decorators.section<Intacct>({
        title: 'Communication',
        isTitleHidden: true,
    })
    communicationSection: ui.containers.Section;

    @ui.decorators.tableField<Intacct, SysMessageHistory>({
        parent() {
            return this.communicationSection;
        },
        title: 'History',
        node: '@sage/xtrem-communication/SysMessageHistory',
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({
                bind: 'sendStamp',
                title: 'Sent',
                size: 'large',
                isFullWidth: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'receivedStamp',
                title: 'Received',
                size: 'large',
                isFullWidth: true,
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                bind: 'status',
                title: 'status',
                optionType: '@sage/xtrem-communication/CommunicationState',
            }),
            ui.nestedFields.label({
                bind: 'context',
                title: 'Node',
                borderColor() {
                    return labelAsText('borderColor');
                },
                map(fieldValue) {
                    const contextData = JSON.parse(fieldValue) as ContextCallback;
                    return `${contextData.function || ''} ${contextData.nodeName || ''} - ${
                        JSON.stringify(contextData.parameters) || ''
                    }`;
                },
            }),
            ui.nestedFields.label({
                title: 'User',
                bind: 'user',
                borderColor() {
                    return labelAsText('borderColor');
                },
                isHiddenOnMainField: true,
                isHiddenMobile: true,
                map(_fieldValue, rowData) {
                    return `${JSON.parse(rowData.user).firstName || ''} ${JSON.parse(rowData.user).lastName || ''}`;
                },
            }),
            ui.nestedFields.text({ bind: 'context', title: 'context', isExcludedFromMainField: true, size: 'large' }),
            ui.nestedFields.text({
                bind: 'attributes',
                title: 'attributes',
                isExcludedFromMainField: true,
                size: 'large',
            }),
            ui.nestedFields.technical({ bind: { sentRequest: { value: true } } }),
            ui.nestedFields.technical({ bind: { receivedRequest: { value: true } } }),
            ui.nestedFields.technical({ bind: 'communicationDiagnoses' }),
        ],

        onRowClick(_id: string, rowData) {
            if (this.$.detailPanel) {
                this.$.detailPanel.isHidden = false;
            }

            this.detailPanelSendGridRowBlock.isHidden = false;
            this.detailPanelReceiveGridRowBlock.isHidden = false;
            this.detailPanelSendGridRowBlock.selectedRecordId = _id;
            this.detailPanelReceiveGridRowBlock.selectedRecordId = _id;
            this.communicationDiagnoses.value = JSON.parse(rowData.communicationDiagnoses).messages?.join('\n') || '';
            this.sentRequest.value = rowData.sentRequest?.value || '';
            this.receivedRequest.value = rowData.receivedRequest?.value || '';
        },
        dropdownActions: [
            {
                icon: 'play',
                title: 'Retry',
                isDisabled(rowId, rowItem) {
                    return (
                        rowItem.status !== 'error' ||
                        (rowItem.attributes ? JSON.parse(rowItem.attributes) : { MessageKind: '' }).MessageKind ===
                            'IntacctEvent' ||
                        ['accounts_receivable_invoice', 'accounts_payable_invoice', 'journal_entry'].includes(
                            (rowItem.context ? JSON.parse(rowItem.context) : { nodeName: '' }).nodeName,
                        )
                    );
                },
                async onClick(rowId, rowItem) {
                    if (rowItem.id) {
                        await this.$.graph
                            .node('@sage/xtrem-intacct/Intacct')
                            .mutations.retry(true, { sysMessageHistoryId: rowItem.id })
                            .execute();
                    }
                },
            },
        ],
        fieldActions() {
            return [this.refreshCommunicationData, this.purgeCommunicationData];
        },
    })
    messageHistory: ui.fields.Table<SysMessageHistory>;

    @ui.decorators.gridRowBlock<Intacct>({
        title: 'Send details',
        boundTo() {
            return this.messageHistory;
        },
        parent() {
            return this.detailPanelSendSection;
        },
        fieldFilter(columnId: string) {
            const columns = ['sendStamp'];
            return columns.includes(columnId);
        },
    })
    detailPanelSendGridRowBlock: ui.containers.GridRowBlock;

    @ui.decorators.block<Intacct>({
        title: 'Send block',
        isTitleHidden: true,
        parent() {
            return this.detailPanelSendSection;
        },
    })
    detailPanelSendBlock: ui.containers.Block;

    @ui.decorators.textAreaField<Intacct>({
        parent() {
            return this.detailPanelSendBlock;
        },
        isReadOnly: true,
        isTransient: true,
        width: 'large',
        rows: 10,
    })
    sentRequest: ui.fields.TextArea;

    @ui.decorators.block<Intacct>({
        title: 'Diagnoses details',
        isTitleHidden: true,
        parent() {
            return this.detailPanelDiagnoseSection;
        },
    })
    detailPanelDiagnoseBlock: ui.containers.Block;

    @ui.decorators.textAreaField<Intacct>({
        parent() {
            return this.detailPanelDiagnoseBlock;
        },
        isReadOnly: true,
        isTransient: true,
        width: 'large',
        rows: 10,
    })
    communicationDiagnoses: ui.fields.TextArea;

    @ui.decorators.gridRowBlock<Intacct>({
        title: 'Receive details',
        boundTo() {
            return this.messageHistory;
        },
        parent() {
            return this.detailPanelReceiveSection;
        },
        fieldFilter(columnId: string) {
            return ['receivedStamp'].includes(columnId);
        },
    })
    detailPanelReceiveGridRowBlock: ui.containers.GridRowBlock;

    @ui.decorators.block<Intacct>({
        isTitleHidden: true,
        parent() {
            return this.detailPanelReceiveSection;
        },
    })
    detailPanelReceiveBlock: ui.containers.Block;

    @ui.decorators.textAreaField<Intacct>({
        parent() {
            return this.detailPanelReceiveBlock;
        },
        isReadOnly: true,
        isTransient: true,
        width: 'large',
        rows: 10,
    })
    receivedRequest: ui.fields.TextArea;

    @ui.decorators.pageAction<Intacct>({
        icon: 'cross',
        title: 'Delete',
        isDestructive: true,
        async onClick() {
            if (
                await utils.confirmDialogToBoolean(
                    this.$.dialog.confirmation(
                        'warn',
                        ui.localize('@sage/xtrem-intacct/purge-sys-message-history-title', 'Delete history'),
                        ui.localize(
                            '@sage/xtrem-intacct/purge-sys-message-history',
                            `You are about to delete the message history.`,
                        ),
                        optionsForPurge,
                    ),
                )
            ) {
                await this.$.graph
                    .node('@sage/xtrem-communication/SysMessageHistory')
                    .mutations.purge(true, { integrationSolution: 'intacct' })
                    .execute();

                await this.messageHistory.refresh();
            }
        },
    })
    purgeCommunicationData: ui.PageAction;

    @ui.decorators.pageAction<Intacct>({
        icon: 'refresh',
        title: 'Refresh',
        async onClick() {
            await this.messageHistory.refresh();
        },
    })
    refreshCommunicationData: ui.PageAction;

    @ui.decorators.pageAction<Intacct>({
        title: 'Synchronous test',
        async onClick() {
            if (this.id.value) {
                this.infoBox.value = JSON.stringify(
                    await this.$.graph
                        .node('@sage/xtrem-intacct/Intacct')
                        .queries.getEntities({ id: true, name: true }, { intacctInstanceId: this.id.value })
                        .execute(),
                );
                await this.$.dialog.message(
                    'info',
                    ui.localize('@sage/xtrem-intacct/synchronous_test', 'Synchronous test'),
                    this.infoBox.value,
                );
            }
        },
    })
    synchronousTest: ui.PageAction;

    @ui.decorators.pageAction<Intacct>({
        title: 'Asynchronous test',
        async onClick() {
            if (this.id.value) {
                this.infoBox.value = JSON.stringify(
                    await this.$.graph
                        .node('@sage/xtrem-intacct/Intacct')
                        .mutations.getEntitiesAsynchronous(
                            { controlId: true, status: true, xml: true },
                            { intacctInstanceId: this.id.value },
                        )
                        .execute(),
                );
                await this.$.dialog.message(
                    'info',
                    ui.localize('@sage/xtrem-intacct/asynchronous_test', 'Asynchronous test'),
                    this.infoBox.value,
                );
            }
        },
    })
    asynchronousTest: ui.PageAction;

    fullFilledAuthentification() {
        this.auditTrailBlock.isHidden = !this.isFullFilledAuthentification.value || false;
        this.asynchronousTest.isDisabled = !this.isFullFilledAuthentification.value || false;
        this.synchronousTest.isDisabled = !this.isFullFilledAuthentification.value || false;
        this.sendRequest.isDisabled = !this.isFullFilledAuthentification.value || false;
    }
}
