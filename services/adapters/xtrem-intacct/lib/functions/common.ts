import type { Context } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, Logger } from '@sage/xtrem-core';
import * as xtremIntacct from '../index';

const logger = Logger.getLogger(__filename, 'intacct');

/**
 * Check if Intacct must be active or not checking if the XTREM instance of the node Intacct as isActive set to true
 * @param context
 */
export async function isIntacctActive(context: Context): Promise<boolean> {
    const defaultInstance = await xtremIntacct.nodes.Intacct.defaultInstance(context);
    logger.debug(() => (defaultInstance ? 'Intacct Active ! ' : 'Intacct not Active ! '));

    if (!defaultInstance) {
        return false;
    }
    const intacctConfig = context.configuration.getPackageConfig('@sage/xtrem-intacct-gateway');
    if (!intacctConfig) {
        logger.error(() => 'No intacct Configuration - intacct desactivated');
    }
    if (context.testMode) {
        logger.warn(() => `TestMode - ${intacctConfig ? 'with' : 'without'} intacct config `);
    }
    return !!intacctConfig || context.testMode;
}

export function getIntacctNode(context: Context, nodeName: string) {
    const xtremNode = context.introspection.getNodeFromTableName(nodeName) as typeof xtremIntacct.nodes.IntacctNode;
    if (!xtremNode) {
        throw new BusinessRuleError(
            context.localize('@sage/xtrem-intacct/xtrem-object-invalid', `{{nodeName}} is not a node.`, {
                nodeName,
            }),
        );
    }
    return xtremNode;
}

export async function disableAll(
    context: Context,
    xtremNode: typeof xtremIntacct.nodes.IntacctNode,
    filter?: any,
): Promise<void> {
    const allXtremData = await context
        .query<xtremIntacct.nodes.IntacctNode>(xtremNode, {
            filter,
            forUpdate: true,
        })
        .toArray();
    await asyncArray(allXtremData).forEach(async (data: xtremIntacct.nodes.IntacctNode) => {
        await data.$.set({ isActive: false });
        if (await data.$.trySave()) {
            logger.error(() => JSON.stringify(context.diagnoses, null, 4));
        }
    });
}

export function isLocalImplementation(context: Context): boolean {
    return context.tenantId === '7'.repeat(21);
}
