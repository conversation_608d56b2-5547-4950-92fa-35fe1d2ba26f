export const xmlVersion = /<\?xml\sversion="1.0"\?>/;
export const xmlVersionWithSpace = /<\?xml\sversion="1.0"\s\?>/;
export const regexFunctionXmlBegin = /<request>[\n\r\s]+<function\scontrolid="\w*-\w*-\w*-\w*-\w*">[\n\r\s]/;
// eslint-disable-next-line @sage/redos/no-vulnerable
export const regexFunctionXmlEnd = /[\n\r\s]+<\/function>[\n\r\s]+<\/request>/;

/**
 * regexFunctionXmlBeginSpace because on ci we have an issue with async
 */
export const regexFunctionXmlBeginSpace = /<request>[\n\r\s]?<function\scontrolid="\w*-\w*-\w*-\w*-\w*">[\n\r\s]?/;
export const regexFunctionXmlEndSpace = /[\n\r\s]?<\/function>[\n\r\s]?<\/request>/;

/**
 *  Used to delete request & function xml tags for stock in results
 * @param xml
 * @returns
 */
export function withoutFunction(xml: string) {
    return xml
        .replace(xmlVersion, '')
        .replace(regexFunctionXmlBegin, '')
        .replace(regexFunctionXmlBeginSpace, '')
        .replace(regexFunctionXmlEndSpace, '')
        .replace(regexFunctionXmlEnd, '')
        .replace(xmlVersionWithSpace, '')
        .replaceAll(/\n\s\s\s\s\s\s\s\s/gim, '\n')
        .trim();
}
