import type * as IA from '@intacct/intacct-sdk';
import type { AnyRecord, Context } from '@sage/xtrem-core';
import { BusinessRuleError, Logger } from '@sage/xtrem-core';
import * as xtremIntacct from '../index';
import type { GetIntacctDataParameters } from '../interfaces/function';

const logger = Logger.getLogger(__filename, 'intacct');

/**
 * Helpers for query class !
 */

/** return the queryOperator Filter
 *  - beetwen ( need a second value )
 *  - like with %%
 */
function getFilter(filter: xtremIntacct.interfaces.WhereFields): xtremIntacct.classes.sdk.Interfaces.Ifilter {
    switch (filter.type) {
        case 'notEqualTo':
            return new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(filter.where).notEqualTo(
                filter.whereValue,
            );
        case 'beetwen':
            if (!filter.secondWhereValue) {
                throw new BusinessRuleError('No second value for between operator');
            }
            return new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(filter.where).between(
                filter.whereValue,
                filter.secondWhereValue,
            );
        case 'like':
            return new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(filter.where).like(filter.whereValue);
        case 'in':
            if (!filter.whereValueArray) {
                throw new BusinessRuleError('No where value array for in operator');
            }
            return new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(filter.where).in(filter.whereValueArray);
        case 'equalTo':
        default:
            return new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(filter.where).equalTo(filter.whereValue);
    }
}

/** For now only like  */
export function manageFiltersForIntacct(
    filters?: xtremIntacct.interfaces.WhereFields[],
): xtremIntacct.classes.sdk.Interfaces.Ifilter | undefined {
    if (!filters || filters.length === 0) {
        return undefined;
    }

    /** Filter for intacct query  */
    if (filters.length > 1) {
        // We add and operator for all filters
        return new xtremIntacct.classes.sdk.Functions.QueryOperator.AndOperator(
            filters.map(filter => {
                return getFilter(filter);
            }),
        );
    }
    if (filters.length === 1) {
        return getFilter(filters[0]);
    }

    return undefined;
}

interface ResultIntacctData<A> {
    info: {
        totalCount: number;
    };
    data: A[];
}

async function resultFunction<A>(result: IA.Xml.Response.Result) {
    await Promise.resolve();
    return { info: { totalCount: result.totalCount }, data: result.data as A[] };
}

/**
 *  Get the data from intacct api using Query Class
 * @param context
 * @param fields  List Of field
 * @param objectName objectName api
 * @param field where Field
 * @param value where Value
 */
export async function getIntacctData<T extends AnyRecord>(
    context: Context,
    parameters: GetIntacctDataParameters,
): Promise<T[]> {
    // TODO : Use the filter from sdk package
    const filter = manageFiltersForIntacct(parameters.filters);

    const query = new xtremIntacct.classes.sdk.Functions.Query<ResultIntacctData<T>>(context, {
        fields: parameters.fields,
        objectName: parameters.objectName,
        filter,
        resultFunction,
        pageSize: parameters.pageSize,
    });

    query.assignShowPrivate(parameters.isPrivateShow || false);

    const intacctResult = await query.execute();

    const dataArray = intacctResult.data;
    let numberOfRequest = 1;

    if (!dataArray) {
        throw new BusinessRuleError(`${query.diagnosesMessages()}`);
    }

    while (intacctResult.info.totalCount > dataArray.length) {
        query.assignOffset(dataArray.length);
        dataArray.push(...(await query.execute()).data);
        numberOfRequest += 1;
    }

    logger.debug(
        () =>
            ` Query return ${dataArray.length} lines for ${parameters.objectName}: with filter ${JSON.stringify(
                parameters.filters,
            )} Include private : ${parameters.isPrivateShow || false} -  numberOfRequest : ${numberOfRequest}`,
    );

    logger.debug(() => `Returned data to Create in xtrem : ${JSON.stringify(intacctResult.data)}`);

    return dataArray;
}
