import * as IA from '@intacct/intacct-sdk';
import type { AnyValue, TestConfig } from '@sage/xtrem-core';
import { Logger, SystemError } from '@sage/xtrem-core';
import { createHash } from 'crypto';
import * as fs from 'fs';
import * as _ from 'lodash';
import * as path from 'path';

const logger = Logger.getLogger(__filename, 'call');

export function canCreateFile(testConfig: TestConfig | undefined): testConfig is TestConfig {
    if (!testConfig) {
        throw new SystemError(`No testConfig`);
    }
    const { scenario, testAttributes } = testConfig;
    if (!testAttributes?.isCreationMode) {
        throw new SystemError(`No mocks for ${scenario} use testAttributes.isCreationMode`);
    }
    return true;
}

/**
 *  Mock function for Intacct request : add a scenario name , & directory
 * it will create for you the request & response file to mock
 * {    scenario: 'inspect-all',
        directory: __dirname,
    },
 * @param testConfig
 * @param xmlRequest
 * @param returnJson // return xml if false json if true
 * @returns
 */
export function mockRequest<T extends AnyValue>(
    testConfig: TestConfig | undefined,
    xmlRequest: string,
): IA.Xml.Response.Result | T | null {
    if (!testConfig) {
        throw new SystemError('Not testConfig ! ');
    }
    logger.debug(() => ` mockRequest - request : ${xmlRequest}`);
    const { scenario, directory } = testConfig;
    if (!directory || !scenario) {
        throw new SystemError(`Test config missing for ${scenario} ${directory}`);
    }
    const hash = createHash('md5').update(xmlRequest).digest('hex');

    const requestFile = path.resolve(directory, 'intacct-calls', `${scenario}-${hash}-request.xml`);
    const responseFile = path.resolve(directory, 'intacct-calls', `${scenario}-${hash}-response.${'json'}`);

    fs.mkdirSync(path.resolve(directory, 'intacct-calls'), { recursive: true });

    let request = '';
    if (fs.existsSync(requestFile)) {
        request = fs.readFileSync(requestFile).toString();
    }
    if (request !== xmlRequest) {
        canCreateFile(testConfig);
        logger.warn(() => `new Request for ${scenario} : ${xmlRequest} `);
        fs.writeFileSync(requestFile, xmlRequest, { encoding: 'utf-8' });
    }
    if (fs.existsSync(responseFile)) {
        const result = fs.readFileSync(responseFile).toString();
        const resultObject = JSON.parse(result);
        return new IA.Xml.Response.Result({
            ...JSON.parse(result),
            status: resultObject._status,
            function: resultObject._functionName,
            controlid: resultObject._controlId,
            listtype: resultObject._listType,
            count: resultObject._count,
            data: resultObject._data || [],
        });
    }
    logger.warn(() => ` ${responseFile} not exist`);
    return null;
}

/**
 *  Mock functions : write the answer of the query to the json responsefile ( for objects result )
 * or in XML responseFile ( for XML result // Asynchronous mode )
 * @param testConfig
 * @param result
 * @param query
 */
export function writeResponse(testConfig: TestConfig | undefined, result: AnyValue, xmlWithoutFunction: string) {
    if (!canCreateFile(testConfig)) {
        return;
    }
    const { scenario, directory, testAttributes } = testConfig;
    if (!testAttributes?.isCreationMode) {
        throw new SystemError(`No mocks for ${scenario} use testAttributes.isCreationMode`);
    }
    logger.debug(() => ` Writting ${directory} ${scenario} response for mocks `);

    const hash = createHash('md5').update(xmlWithoutFunction).digest('hex');

    fs.writeFileSync(
        path.resolve(
            directory!,
            'intacct-calls',
            `${scenario}-${hash}-response.${_.isObject(result) ? 'json' : 'xml'}`,
        ),
        _.isObject(result) ? JSON.stringify(result, null, 4) : (result as string),
    );
}
