import { ConfigManager } from '@sage/xtrem-config';
import type { AnyRecord, Context } from '@sage/xtrem-core';
import { BusinessRuleError, Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as fs from 'fs';
import * as jsXml from 'js-xml';
import * as path from 'path';
import * as xtremIntacct from '..';

/** get the Xtreem audit trail file install application  */
function getXMLTemplateFile(context: Context): string {
    /** path to the xml Xtreem Audit Trail application  */
    const xmlInstallPath = path.resolve(__dirname, '../../../data/install-application/xtreem_audit_trail.xml');

    if (!fs.existsSync(xmlInstallPath)) {
        xtremIntacct.functions.Messages.noXtremAuditTrailFileTemplate(context, xmlInstallPath);
    }
    return fs.readFileSync(xmlInstallPath).toString();
}

/** Return string **ClusterName** **tenantName**  */
export async function getXtreemTenantName(context: Context): Promise<string> {
    /**
     * Get the instance name with the tenant id
     */
    const xtreemTenantName =
        (await (await context.tryRead(xtremSystem.nodes.SysTenant, { tenantId: context.tenantId! }))?.name) ||
        Test.defaultTenantName;
    const { app, clusterId } = ConfigManager.current;

    return `${clusterId || ''} ${xtreemTenantName} ${app || ''}`.trim();
}

export async function installApplicationXtreemAuditTrail(context: Context, intacctInstanceId?: string): Promise<void> {
    const xmlTemplateFile = getXMLTemplateFile(context);

    const url =
        context.configuration
            .getPackageConfig('@sage/xtrem-intacct-gateway', {
                intacctEventApiGatewayUrl: 'https://url/v1/intacctEvent',
            })
            ?.intacctEventApiGatewayUrl?.toString() || '';

    if (url === '') {
        throw new BusinessRuleError(' intacctEventApiGatewayUrl not set in xtrem-config');
    }

    const { app, clusterId } = ConfigManager.current;

    let intacctTargetUrl = `${url}?cluster=${clusterId || 'cluster-ci'}&amp;xtremTenantId=${context.tenantId}`;

    if (app) {
        intacctTargetUrl += `&amp;app=${app.replace(/_/g, '-')}`;
    }

    const xmlFile = xmlTemplateFile
        .replace('{{intacctTargetUrl}}', intacctTargetUrl)
        .replace('{{xtreemTenantName}}', await getXtreemTenantName(context));

    const install = new xtremIntacct.classes.sdk.Functions.InstallApplication(context, xmlFile, {
        intacctInstanceId,
    });

    const { result } = await install.executeResult();

    const message = `${context.localize(
        '@sage/xtrem-intacct/install-intacct',
        '{{status}}, Intacct application installed : {{info}}',
        {
            status: result.status,
            info: `${result && result.data ? result.data.join(',') : ''} - ${
                result && result.errors ? result.errors.join(',') : ''
            }`,
        },
    )}`;

    if (result.status !== 'success') {
        const xmlInstall = await install.xmlWithoutFunction(true);
        await context.batch.logMessage('error', `${xmlInstall.substring(0, 800)}`, { data: { xmlInstall } });
        if (result.errors) {
            await context.batch.logMessage('error', `${result.errors.join('\n')}`);
        }
        throw new BusinessRuleError(message);
    }

    await context.batch.logMessage('info', message);
}

function readXtreemAuditTrail<T extends AnyRecord>(context: Context): T {
    const templateFile = getXMLTemplateFile(context);

    return jsXml.parse(templateFile);
}

export function getVersionXtreemAuditTrail(context: Context): string {
    const document = readXtreemAuditTrail<{ Application: { $: { version: string } } }>(context);
    return document.Application.$.version || '';
}
