import type { Context, ValidationContext } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';

/**  No xtremAuditTrail file template - {{path}}' is invalid  */
export function noXtremAuditTrailFileTemplate(context: Context, xmlInstallPath: string) {
    throw new BusinessRuleError(
        context.localize(
            '@sage/xtrem-intacct/no-xtreem_audit_trail-template',
            `No xtremAuditTrail file template - {{path}}' is invalid`,
            { path: xmlInstallPath },
        ),
    );
}
export function validCredentialsNeeded(cx: ValidationContext, intacctError?: string) {
    cx.error.addLocalized('@sage/xtrem-intacct/valid-credentials-need', 'Enter valid Sage Intacct credentials.');
    if (intacctError) {
        intacctError.split('\n').forEach(error => cx.error.add(error));
    }
}

export function noIntacctInstance(context: Context) {
    return context.localize('@sage/xtrem-intacct/no-intacct-instance', 'There is no active intacct instance');
}
