import type { Context } from '@sage/xtrem-core';

export abstract class IntacctOptionHooks {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    public static onEnabled(_context: Context): Promise<void> {
        throw new Error('Not implemented');
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    public static onDisabled(_context: Context): Promise<void> {
        throw new Error('Not implemented');
    }
}
