import * as IA from '@intacct/intacct-sdk';
import type { AnyValue, Context } from '@sage/xtrem-core';
import { Logger, SystemError } from '@sage/xtrem-core';
import type { Interfaces, Xml } from './_index';
import { Request } from './_index';

export class Client {
    /**
     * @type {string}
     */
    protected static readonly PROFILE_ENV_NAME = 'INTACCT_PROFILE';

    public logger = Logger.getLogger(__filename, 'client');

    /**
     * @param {ClientConfig} config
     */
    constructor(
        public context: Context,
        public config?: IA.ClientConfig,
    ) {
        if (!config) {
            throw new SystemError('no ClientConfig');
        }
        this.logger.debug(
            () => `SessionID : ${config.sessionId} UserID : ${config.userId} CompanyId : ${config.companyId} `,
        );

        if (
            config.credentials instanceof IA.Credentials.SessionCredentials ||
            config.credentials instanceof IA.Credentials.LoginCredentials
        ) {
            // Do not try and load credentials if they are already set in config
        } else if (config.sessionId !== undefined) {
            // Load the session credentials
            config.credentials = new IA.Credentials.SessionCredentials(
                config,
                new IA.Credentials.SenderCredentials(config),
            );
        } else {
            // Load the login credentials
            config.credentials = new IA.Credentials.LoginCredentials(
                config,
                new IA.Credentials.SenderCredentials(config),
            );
        }
        this.config = config;
    }

    /**
     * Create the RequestHandler
     * Execute the request
     * @param {Ifunction[]} functions
     * @param {RequestConfig} requestConfig
     * @returns {Promise<OnlineResponse>}
     */
    public executeOnlineRequest<T extends AnyValue>(
        functions: Interfaces.Ifunction<T> | Interfaces.Ifunction<T>[],
        requestConfig?: IA.RequestConfig,
    ): Promise<Xml.OnlineResponse> {
        if (!requestConfig) {
            return Promise.reject(new SystemError('no request config'));
        }

        const requestHandler = new Request.RequestHandler(this.context, this.config!, requestConfig, this.logger);

        return requestHandler.executeOnline(functions);
    }

    /**
     * @param {Ifunction[]} functions
     * @param {RequestConfig} requestConfig
     * @returns {Promise<OfflineResponse>}
     */
    public executeOfflineRequest<T extends AnyValue>(
        functions: Interfaces.Ifunction<T> | Interfaces.Ifunction<T>[],
        requestConfig?: IA.RequestConfig,
        logger?: Logger,
    ): Promise<Xml.OfflineResponse<T>> {
        if (!requestConfig) {
            throw new SystemError('no request config');
        }

        const requestHandler = new Request.RequestHandler<T>(this.context, this.config!, requestConfig, logger);

        return requestHandler.executeOffline(functions);
    }
}
