import type { AnyR<PERSON><PERSON>, AnyValue, Dict, LocalizeLocale } from '@sage/xtrem-core';
import { date } from '@sage/xtrem-core';
import * as _ from 'lodash';
import * as xml2js from 'xml2js';
import type * as xmlBuilder from 'xmlbuilder';

// This function intends to convert the xml2js format to the original xml2json format which is simpler but can lead to collisions
function convertToXml2JsonFormat(obj: AnyRecord) {
    return _.transform(obj, (result: Dict<AnyValue>, value: AnyRecord, key: string) => {
        let val: AnyRecord = value;
        if (key === '$') {
            Object.entries(value).forEach(([k, v]) => {
                result[k] = v;
            });
            return;
        }
        if (Array.isArray(value) && value.length === 1) {
            [val] = value;
        }
        result[key] = _.isObject(val) ? convertToXml2JsonFormat(val) : val;
    });
}

/**
 *  IaXmlWriter  Refactor class from  SDK intacct used to write xml request
 */
export class IaXmlWriter {
    public static readonly intacctDateFormat = 'MM/DD/YYYY';

    public static readonly intacctDateTimeFormat = 'MM/DD/YYYY HH:mm:ss';

    public static readonly intacctMultiSelectGlue = '#~#';

    private _writer: xmlBuilder.XMLElement;

    constructor(xml: xmlBuilder.XMLElement) {
        this._writer = xml;
    }

    static getValue(value: AnyValue) {
        switch (typeof value) {
            case 'string':
                return value;

            case 'number':
                return value.toString();

            case 'boolean':
                return value ? 'true' : 'false';

            case 'object':
                if (value instanceof date) {
                    return value.format(IaXmlWriter.intacctDateTimeFormat, 'base');
                }
                if (value instanceof Date) {
                    return date.fromJsDate(value).format(IaXmlWriter.intacctDateTimeFormat, 'base');
                }
                return '';
            default:
                return '';
        }
    }

    public async flushToJson(): Promise<AnyRecord> {
        const json = await xml2js.parseStringPromise(this.flush());
        return convertToXml2JsonFormat(json);
    }

    /**
     *  used by the intacct sdk - used to get the xml
     * @param pretty will indent the xml
     * @returns
     */
    public flush(pretty = false): string {
        if (pretty) {
            return this._writer.doc().end({
                pretty: true,
                indent: '    ',
            });
        }
        return this._writer.doc().end({
            pretty: false,
        });
    }

    public doc(): xmlBuilder.XMLDocument {
        return this._writer.doc();
    }

    /**
     *  open element : <localName>
     * @param localName
     */
    public writeStartElement(localName: string): void {
        this._writer = this._writer.element(localName);
    }

    /**
     * </ element>
     */
    public writeEndElement(): void {
        this._writer = this._writer.up();
    }

    /**
     *  < localName attribute=*value />
     * @param localName
     * @param attribute
     * @param value
     */
    public writeStartEndWithAttibute(localName: string, attribute: string, value: string): void {
        this._writer = this._writer.element(localName);
        this._writer.attribute(attribute, value);
    }

    /**
     *  <localName> value </localname>
     * @param localName
     * @param value
     * @param writeNull true==>  will write <localName></localname> if value is null
     * @returns
     */
    public writeElement(localName: string, value: AnyValue, writeNull?: boolean): void {
        if (!(typeof value === 'boolean' && !value) && !value && !writeNull) {
            return;
        }
        this._writer.element(localName, IaXmlWriter.getValue(value)).up();
    }

    /**
     *  <localName> date in format </localname>
     * @param localName
     * @param value
     * @param format  default : MM/DD/YYYY
     * @param writeNull will write <localName></localname> if value is null
     * @returns
     */
    public writeElementDate(
        localName: string,
        value: date,
        locale: LocalizeLocale,
        format?: string,
        writeNull?: boolean,
    ): void {
        if (!value && !writeNull) {
            return;
        }
        this._writer.element(localName, value.format(IaXmlWriter.intacctDateFormat, locale) || '');
    }

    /**
     *  add an attribute to the prévious element
     * @param localName  <previousElement localName=value>
     * @param value
     * @param writeNull <previousElement localName=''> ( to be verify )
     * @returns
     */
    public writeAttribute(localName: string, value: AnyValue, writeNull?: boolean) {
        if (!(typeof value === 'boolean' && !value) && !value && !writeNull) {
            return;
        }
        this._writer.attribute(localName, IaXmlWriter.getValue(value));
    }

    /**
     *   <year>2020</year><month>02</month><day>25</day>
     * @param myDate
     * @param writeNull
     * @returns
     */
    public writeDateSplitElements(myDate: date, writeNull?: boolean) {
        if (!myDate && !writeNull) {
            return;
        }

        this.writeElement('year', myDate.year, writeNull);
        this.writeElement('month', myDate.month, writeNull);
        this.writeElement('day', myDate.day, writeNull);
    }

    public writeString(xml: string) {
        if (xml) {
            this._writer.text(xml);
        }
    }

    public writeXmlString(xml: string) {
        if (xml) {
            this._writer.raw(xml);
        }
    }
}
