import type { Context } from '@sage/xtrem-core';
import type * as xtremIntacct from '../../../index';
import type { IaXmlWriter } from '../_index';
import { DefaultQuery } from './default-query';

export class ApiSessionCreate extends DefaultQuery<{}> {
    constructor(
        public override context: Context,
        __options?: xtremIntacct.interfaces.CallOptions<{}>,
        public entityId?: string,
    ) {
        super(context, __options);
    }

    public override writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('function');
        xml.writeAttribute('controlid', this.controlId, true);

        xml.writeStartElement('getAPISession');

        if (this.entityId != null) {
            xml.writeElement('locationid', this.entityId, true);
        }

        xml.writeEndElement(); // getAPISession

        xml.writeEndElement(); // function
    }
}
