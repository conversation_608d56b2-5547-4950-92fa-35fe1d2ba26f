import type { AnyValue, Context } from '@sage/xtrem-core';
import type * as xtremIntacct from '../../../index';
import type { IaXmlWriter } from '../_index';
import { DefaultQuery } from './_index';

export class XmlQuery<T extends AnyValue> extends DefaultQuery<T> {
    constructor(
        public override context: Context,
        public xmlQuery: string,
        options?: xtremIntacct.interfaces.CallOptions<T>,
    ) {
        super(context, options || {});
    }

    public override writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('function');
        xml.writeAttribute('controlid', this.controlId, true);

        xml.writeXmlString(this.xmlQuery);

        xml.writeEndElement(); // function
    }
}
