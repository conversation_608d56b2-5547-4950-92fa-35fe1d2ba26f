import type * as IA from '@intacct/intacct-sdk';
import type { AnyValue, Context } from '@sage/xtrem-core';
import type * as xtremIntacct from '../../../index';
import type { IaXmlWriter } from '../_index';
import { Client } from '../_index';
import { DefaultQuery } from './_index';

/**
 * Valid the credentials of the customClient
 */
export class ValidCredentials<T extends AnyValue> extends DefaultQuery<T> {
    constructor(
        public override context: Context,
        private readonly intacctInstanceToValidate: xtremIntacct.nodes.Intacct,
        private readonly customClient?: IA.ClientConfig,
    ) {
        super(context, {});
    }

    override get intacctInstance() {
        return this.intacctInstanceToValidate;
    }

    override get client(): Promise<Client> {
        return (async () => {
            return new Client(
                this.context,
                this.customClient || (await this.intacctInstance.getClient(this.options?.entityId || '')),
            );
        })();
    }

    public override writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('function');
        xml.writeAttribute('controlid', this.controlId, true);
        xml.writeStartElement('getAPISession');
        xml.writeEndElement(); // close getAPISession
        xml.writeEndElement(); // close function
    }
}
