import type { AnyValue, Context } from '@sage/xtrem-core';
import type * as xtremIntacct from '../../../index';
import type { IaXmlWriter } from '../_index';
import { DefaultQuery } from './_index';

export class Delete<T extends AnyValue> extends DefaultQuery<T> {
    constructor(
        public override context: Context,
        public parameters: { objectName: string; recordNo: string },
        options?: xtremIntacct.interfaces.CallOptions<T>,
    ) {
        super(context, options);
    }

    public override writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('function');
        xml.writeAttribute('controlid', this.controlId, true);

        xml.writeStartElement('delete');

        xml.writeElement('object', this.parameters.objectName, true);
        xml.writeElement('keys', this.parameters.recordNo, true);

        xml.writeEndElement(); // delete

        xml.writeEndElement(); // function
    }
}
