import type { Result } from '@intacct/intacct-sdk/dist/Xml/Response';
import type { Context } from '@sage/xtrem-core';
import type * as xtremIntacct from '../../../index';
import type { IaXmlWriter } from '../_index';
import { DefaultQuery } from './_index';

export class InstallApplication extends DefaultQuery<Result> {
    constructor(
        public override context: Context,
        public xmlApplication: string,
        __options?: xtremIntacct.interfaces.CallOptions<Result>,
    ) {
        super(context, __options);

        this.options.resultFunction = result => result;
    }

    public override writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('function');
        xml.writeAttribute('controlid', this.controlId, true);

        xml.writeStartElement('installApp');
        xml.writeStartElement('appxml');

        xml.writeXmlString(`<![CDATA[${this.xmlApplication}]]>`);

        xml.writeEndElement(); // appxml
        xml.writeEndElement(); // installApp
        xml.writeEndElement(); // function
    }
}
