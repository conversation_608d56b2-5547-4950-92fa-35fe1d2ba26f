import type { AnyValue, Context } from '@sage/xtrem-core';
import type * as xtremIntacct from '../../../index';
import type { IaXmlWriter } from '../_index';
import { DefaultQuery } from './_index';

export class GetCompanyPrefs<T extends AnyValue> extends DefaultQuery<T> {
    /**
     * XML Call getCompanyPref
     * @param application Application name
     */
    constructor(
        public override readonly context: Context,
        queryOptions: xtremIntacct.interfaces.CallOptions<T>,
        public application?: string,
    ) {
        super(context, queryOptions);
    }

    public override writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('function');
        xml.writeAttribute('controlid', this.controlId, true);
        xml.writeStartElement('get_companyprefs');
        xml.writeAttribute('application', this.application, true);
        xml.writeEndElement(); // get_companyprefs
        xml.writeEndElement(); // function
    }
}
