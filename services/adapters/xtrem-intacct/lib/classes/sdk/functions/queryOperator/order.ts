import type { IaXmlWriter } from '../../_index';

interface OrderConstructor {
    fieldName: string;
    direction: 'ascending' | 'descending';
}

export class OrderDirection {
    constructor(public options: OrderConstructor) {}

    public writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('order');
        xml.writeElement('field', this.options.fieldName, false);
        xml.writeElement(this.options.direction, null, true);
        xml.writeEndElement(); // order
    }
}
