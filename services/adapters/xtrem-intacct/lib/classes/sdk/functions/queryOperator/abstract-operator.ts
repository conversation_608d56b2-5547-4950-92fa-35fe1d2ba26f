import { SystemError } from '@sage/xtrem-core';
import type { IaXmlWriter, Interfaces } from '../../_index';

export abstract class AbstractOperator implements Interfaces.Ifilter {
    public static readonly OR = 'or';

    public static readonly AND = 'and';

    constructor(private readonly filters?: Interfaces.Ifilter[]) {
        this.filters = filters || [];
    }

    /**
     * Adds filter to list in this
     *
     * @param filter
     *
     * @return Ifilter
     */
    public addFilter(filter: Interfaces.Ifilter): Interfaces.Ifilter {
        if (filter) {
            this.filters!.push(filter);

            return this;
        }
        throw new SystemError('filter cannot be null');
    }

    /**
     * Returns the and/or operator
     *
     * @return {string}
     */
    public abstract getOperator(): string;

    public writeXml(xml: IaXmlWriter): void {
        if (this.filters && this.filters.length >= 2) {
            xml.writeStartElement(this.getOperator());
            this.filters.forEach(filter => filter.writeXml(xml));
            xml.writeEndElement(); // filter
        } else {
            throw new SystemError(`Two or more FilterInterface objects required for ${this.getOperator()}`);
        }
    }
}
