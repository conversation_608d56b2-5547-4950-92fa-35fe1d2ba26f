import type * as xtremIntacct from '../../../../index';
import type { IaXmlWriter, Interfaces } from '../../_index';

interface FieldConstructor {
    fieldName: string;
    function?: xtremIntacct.enums.TypeField;
}

export class Field implements Interfaces.Iselect, Interfaces.IxmlObject {
    constructor(public options: FieldConstructor) {
        if (!this.options.fieldName) {
            throw new Error('Field name cannot be empty or null. Provide a field name for the builder.');
        }
    }

    getFunction(): string {
        switch (this.options.function) {
            case 'average':
                return 'avg';
            case 'min':
                return 'min';
            case 'max':
                return 'max';
            case 'count':
                return 'count';
            case 'sum':
                return 'sum';
            case 'select':
            default:
                return 'field';
        }
    }

    public writeXml(xml: IaXmlWriter): void {
        xml.writeElement(this.getFunction(), this.options.fieldName, false);
    }
}
