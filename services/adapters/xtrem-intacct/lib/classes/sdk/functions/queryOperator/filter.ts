import { SystemError } from '@sage/xtrem-core';
import { isArray, isNil } from 'lodash';
import type { IaXmlWriter, Interfaces } from '../../_index';

export class Filter implements Interfaces.Ifilter {
    public static readonly EQUAL_TO = 'equalto';

    public static readonly NOT_EQUAL_TO = 'notequalto';

    public static readonly LESS_THAN = 'lessthan';

    public static readonly LESS_THAN_OR_EQUAL_TO = 'lessthanorequalto';

    public static readonly GREATER_THAN = 'greaterthan';

    public static readonly GREATER_THAN_OR_EQUAL_TO = 'greaterthanorequalto';

    public static readonly BETWEEN = 'between';

    public static readonly IN = 'in';

    public static readonly NOT_IN = 'notin';

    public static readonly LIKE = 'like';

    public static readonly NOT_LIKE = 'notlike';

    public static readonly IS_NULL = 'isnull';

    public static readonly IS_NOT_NULL = 'isnotnull';

    public static readonly LESS_THAN_SIGN = '&gt;';

    public static readonly LESS_THAN_OR_EQUAL_TO_SIGN = '&gt;=';

    public static readonly EQUAL_TO_SIGN = '=';

    private value: string | string[];

    private operation: string;

    constructor(public readonly fieldName: string) {
        if (!fieldName) {
            throw new SystemError('fieldName is required for Filter.');
        }
    }

    /**
     * Sets given value and operation to equal
     * /!\ old version of the api !  for get-list use
     * @param value
     * @return Ifilter
     */
    public equalToSign(value: string): Interfaces.Ifilter {
        if (!value) {
            throw new SystemError('null not allowed. Provide string value for equalToSign function.');
        }

        this.value = value;
        this.operation = Filter.EQUAL_TO_SIGN;

        return this;
    }

    /**
     * Sets given value and operation to less than for this filter
     * /!\ old version of the api !  for get-list use
     * @param value
     * @return Ifilter
     */
    public lessThanSign(value: string): Interfaces.Ifilter {
        if (!value) {
            throw new SystemError('null not allowed. Provide string value for lessThanSign function.');
        }

        this.value = value;
        this.operation = Filter.LESS_THAN_SIGN;

        return this;
    }

    /**
     * Sets given value and operation to less than or equal to for this filter
     * /!\ old version of the api !  for get-list use
     * @param value
     * @return Ifilter
     */
    public lessThanOrEqualToSign(value: string): Interfaces.Ifilter {
        if (!value) {
            throw new SystemError('null not allowed. Provide string value for lessThanOrEqualTo function.');
        }

        this.value = value;
        this.operation = Filter.LESS_THAN_OR_EQUAL_TO_SIGN;

        return this;
    }

    /**
     * Sets given value and operation to equal to for this filter
     * @param value
     * @return Interfaces.Ifilter
     */
    public equalTo(value: string): Interfaces.Ifilter {
        if (!value) {
            throw new SystemError('null not allowed. Provide string value for equalTo function.');
        }

        this.value = value;
        this.operation = Filter.EQUAL_TO;

        return this;
    }

    /**
     * Sets given value and operation to not equal to for this filter
     * @param value
     * @return Interfaces.Ifilter
     */
    public notEqualTo(value: string): Interfaces.Ifilter {
        if (!value) {
            throw new SystemError('null not allowed. Provide string value for notEqualTo function.');
        }

        this.value = value;
        this.operation = Filter.NOT_EQUAL_TO;

        return this;
    }

    /**
     * Sets given value and operation to less than for this filter
     * @param value
     * @return Interfaces.Ifilter
     */
    public lessThan(value: string): Interfaces.Ifilter {
        if (!value) {
            throw new SystemError('null not allowed. Provide string value for lessThan function.');
        }

        this.value = value;
        this.operation = Filter.LESS_THAN;

        return this;
    }

    /**
     * Sets given value and operation to less than or equal to for this filter
     * @param value
     * @return Interfaces.Ifilter
     */
    public lessThanOrEqualTo(value: string): Interfaces.Ifilter {
        if (!value) {
            throw new SystemError('null not allowed. Provide string value for lessThanOrEqualTo function.');
        }

        this.value = value;
        this.operation = Filter.LESS_THAN_OR_EQUAL_TO;

        return this;
    }

    /**
     * Sets given value and operation to greater than for this filter
     * @param value
     * @return Interfaces.Ifilter
     */
    public greaterThan(value: string): Interfaces.Ifilter {
        if (value == null) {
            throw new SystemError('null not allowed. Provide string value for greaterThan function.');
        }

        this.value = value;
        this.operation = Filter.GREATER_THAN;

        return this;
    }

    /**
     * Sets given value and operation to greater than or equal to for this filter
     * @param value
     * @return Interfaces.Ifilter
     */
    public greaterThanOrEqualTo(value: string): Interfaces.Ifilter {
        if (!value) {
            throw new SystemError('null not allowed. Provide string value for greaterThanOrEqualTo function.');
        }

        this.value = value;
        this.operation = Filter.GREATER_THAN_OR_EQUAL_TO;

        return this;
    }

    /**
     * Sets given value and operation to between for this filter.  Only 2 values allowed.
     * @param min
     * @param max
     * @return Interfaces.Ifilter
     */
    public between(min: string, max: string): Interfaces.Ifilter {
        if (!min || !max) {
            throw new SystemError('Two strings expected for between filter');
        }

        this.value = [min, max];
        this.operation = Filter.BETWEEN;

        return this;
    }

    /**
     * Sets given values and operation to in for this filter.
     * @param values
     * @return Interfaces.Ifilter
     */
    public in(values: string[]): Interfaces.Ifilter {
        if (!values || values.length === 0) {
            throw new SystemError('At least 1 string in array expected for in for filter');
        }

        this.value = values;
        this.operation = Filter.IN;

        return this;
    }

    /**
     * Sets given values and operation to not in for this filter.
     * @param values
     * @return Interfaces.Ifilter
     */
    public notIn(values: string[]): Interfaces.Ifilter {
        if (!values || values.length === 0) {
            throw new SystemError('At least 1 string in array expected for notIn for filter');
        }

        this.value = values;
        this.operation = Filter.NOT_IN;

        return this;
    }

    /**
     * Sets given values and operation to like for this filter.
     * @param value
     * @return Interfaces.Ifilter
     */
    public like(value: string): Interfaces.Ifilter {
        if (!value) {
            throw new SystemError('null not allowed. Provide string value for like function.');
        }

        this.value = value;
        this.operation = Filter.LIKE;

        return this;
    }

    /**
     * Sets given values and operation to not like for this filter.
     * @param value
     * @return Interfaces.Ifilter
     */
    public notLike(value: string): Interfaces.Ifilter {
        if (!value) {
            throw new SystemError('null not allowed. Provide string value for notLike function.');
        }

        this.value = value;
        this.operation = Filter.NOT_LIKE;

        return this;
    }

    /**
     * Sets given values and operation to is null for this filter.
     * @return Interfaces.Ifilter
     */
    public isNull(): Interfaces.Ifilter {
        this.operation = Filter.IS_NULL;

        return this;
    }

    /**
     * Sets given values and operation to is not null for this filter.
     * @return Interfaces.Ifilter
     */
    public isNotNull(): Interfaces.Ifilter {
        this.operation = Filter.IS_NOT_NULL;

        return this;
    }

    public writeXml(xml: IaXmlWriter): void {
        if (this.operation) {
            xml.writeStartElement(this.operation);
        } else {
            throw new SystemError('Filter requires 1 method be called before calling writeXml');
        }
        xml.writeElement('field', this.fieldName, false);
        if (!isNil(this.value)) {
            if (isArray(this.value)) {
                this.value.forEach(value => {
                    xml.writeElement('value', value, true);
                });
            } else {
                xml.writeElement('value', this.value, true);
            }
        }
        xml.writeEndElement(); // close tag from this.operation
    }
}
