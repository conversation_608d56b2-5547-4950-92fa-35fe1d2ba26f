import type * as IA from '@intacct/intacct-sdk';
import type { AsyncResponse, Context } from '@sage/xtrem-core';
import { SystemError } from '@sage/xtrem-core';
import type * as xtremIntacct from '../../..';
import type { IaXmlWriter } from '../_index';
import type { InspectObject, InspectResult, InspectWithDetailsResult } from '../interfaces/inspect';
import { DefaultQuery } from './_index';

function resultFunction(result: IA.Xml.Response.Result) {
    return result?.data?.at(0) as AsyncResponse<InspectResult>;
}

function functionForAllObject(result: IA.Xml.Response.Result): InspectObject[] {
    return result.data
        .filter(array => !array.Operation)
        .map(gl => {
            return { name: gl.$value, object: gl.$.typename };
        })
        .sort((a, b) => (a.name < b.name ? -1 : 1));
}

export class Inspect extends DefaultQuery<InspectResult> {
    withDetail: boolean;

    constructor(
        public override context: Context,
        public objectName: string,
        options?: xtremIntacct.interfaces.CallOptions<InspectResult>,
    ) {
        super(context, { ...options, resultFunction });
        this.withDetail = false;
    }

    public override writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('function');
        xml.writeAttribute('controlid', this.controlId, true);

        xml.writeStartElement('inspect');
        if (this.withDetail) {
            xml.writeAttribute('detail', 1);
        }

        xml.writeElement('object', this.objectName, true);

        xml.writeEndElement(); // close inspect

        xml.writeEndElement(); // function
    }

    async allFields(): Promise<InspectResult> {
        if (this.objectName === '*') {
            throw new SystemError(`${this.objectName}`);
        }
        const { result } = await this.executeResult();

        return resultFunction(result);
    }

    /** WithDetail set to true change completly the result of the function */
    async executeWithDetail(): Promise<InspectWithDetailsResult> {
        this.withDetail = true;
        const { result } = await this.executeResult();
        return result.data.at(0) as InspectWithDetailsResult;
    }

    async allObject(): Promise<InspectObject[]> {
        if (this.objectName !== '*') {
            throw new SystemError(`${this.objectName}`);
        }
        const { result } = await this.executeResult();
        return functionForAllObject(result);
    }
}
