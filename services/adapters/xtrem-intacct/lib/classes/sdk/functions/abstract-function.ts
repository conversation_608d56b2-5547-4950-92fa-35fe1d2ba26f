import type * as IA from '@intacct/intacct-sdk';
import type { AnyValue, AsyncResponse, Context, Diagnose } from '@sage/xtrem-core';
import { BusinessRuleError, Logger, SystemError, Uuid } from '@sage/xtrem-core';
import * as XMLBuilder from 'xmlbuilder';
import * as xtremIntacct from '../../../index';
import type { Interfaces } from '../_index';
import { IaXmlWriter } from '../_index';
/**
 * Rewriting intacct SDK // step by step
 */

export abstract class AbstractFunction<T extends AnyValue> implements Interfaces.Ifunction<T> {
    // eslint-disable-next-line class-methods-use-this
    private defaultResultFunction = (result: IA.Xml.Response.Result) => result.data as T;

    protected _controlId: string;

    public isUnitTest: boolean;

    private _xmlWithoutFunction: string;

    private _intacctInstance: xtremIntacct.nodes.Intacct;

    public diagnoses: Diagnose[];

    public packageConfig: xtremIntacct.interfaces.IntacctPackageConfig | undefined;

    /**
     * Intacct instance : By default the active intacct instance, can be overload by intacctInstanceId option
     */
    get intacctInstance(): AsyncResponse<xtremIntacct.nodes.Intacct> {
        return (async () => {
            if (this._intacctInstance) {
                return this._intacctInstance;
            }
            const intacctInstance = this.options.intacctInstanceId
                ? await this.context.read(xtremIntacct.nodes.Intacct, { id: this.options.intacctInstanceId })
                : (await xtremIntacct.nodes.Intacct.defaultInstance(this.context))!;
            if (!intacctInstance) {
                throw new BusinessRuleError(
                    this.context.localize(
                        '@sage/xtrem-intacct/no-intacct-instance',
                        'There is no active intacct instance',
                    ),
                );
            }
            this._intacctInstance = intacctInstance;
            return intacctInstance;
        })();
    }

    async setIntacctInstance(value: xtremIntacct.nodes.Intacct): Promise<void> {
        if (this.options.intacctInstanceId) {
            await this.logger.warnAsync(
                async () => `Changing intacctInstance ${this.options.intacctInstanceId} to ${await value.id} `,
            );
        }
        this._intacctInstance = value;
    }

    /**
     * Ifunction Logger
     */
    public logger: Logger;

    get controlId(): string {
        return this._controlId;
    }

    set controlId(controlId: string) {
        /**
         * Need to use intacct-controlid-provider when
         */
        this._controlId = controlId || Uuid.generate().toString();

        if (this._controlId.length < 1 || this._controlId.length > 256) {
            throw new SystemError('Function control ID must be between 1 and 256 characters in length.');
        }
    }

    /**
     * for async request
     * context that will be sent to the prepareAsyncIntacctQuery that will be retrieve when receive
     */
    abstract defaultCallBackContext: AsyncResponse<AnyValue>;

    constructor(
        public readonly context: Context,
        public options: xtremIntacct.interfaces.CallOptions<T>,
    ) {
        this.isUnitTest =
            context.testMode &&
            !!context.testConfig &&
            !!context.testConfig.scenario &&
            /**  Case when we have real axios mock */
            !context.testConfig.mocks &&
            !context.testConfig.testAttributes?.createMockData;

        this.packageConfig =
            context.configuration.getPackageConfig<xtremIntacct.interfaces.IntacctPackageConfig>(
                '@sage/xtrem-intacct-gateway',
            );

        this.logger = Logger.getLogger(__filename, 'Ifunction');

        this.options =
            options && Object.keys(options).includes('synchronous') ? options : { ...options, synchronous: true };

        if (!this.options.resultFunction) {
            this.options.resultFunction = this.defaultResultFunction;
        }
        this.diagnoses = [];
        this.controlId = this.options.controlID!;
    }

    public abstract writeXml(xml: IaXmlWriter): AsyncResponse<void>;

    public abstract execute(): Promise<T>;

    public abstract executeAsync(): Promise<IA.Xml.OfflineResponse | xtremIntacct.classes.sdk.Xml.OfflineResponse<T>>;

    public async xmlWithoutFunction(prettier: boolean): Promise<string> {
        if (this._xmlWithoutFunction) {
            return this._xmlWithoutFunction;
        }
        const xml = new IaXmlWriter(XMLBuilder.create('request', {}));
        await this.writeXml(xml);
        this._xmlWithoutFunction = xtremIntacct.functions.withoutFunction(xml.flush(prettier));
        return this._xmlWithoutFunction;
    }

    public diagnosesMessages() {
        return this.diagnoses.map(diag => diag.message.toString()).join('\n');
    }
}
