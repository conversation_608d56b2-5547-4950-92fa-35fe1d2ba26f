import type { AnyValue, Context } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import type * as xtremIntacct from '../../../index';
import type { Ifilter, Iorder, IqueryFunction, Iselect } from '../interfaces/_index';
import type { IaXmlWriter } from '../_index';
import { DefaultQuery } from './default-query';
import { Field, OrderDirection } from './queryOperator/_index';

export class Query<T extends AnyValue> extends DefaultQuery<T> implements IqueryFunction {
    public selectFields: Iselect[];

    public fromObject: string;

    public filter: Ifilter;

    public docParId: string;

    public orderBy: Iorder[];

    public caseInsensitive: boolean;

    public showPrivate: boolean;

    public pageSize: number;

    public offset: number;

    override get defaultCallBackContext(): Promise<xtremIntacct.sharedFunctions.interfaces.ContextCallback> {
        return (async () => {
            return { ...(await super.defaultCallBackContext), function: 'query', parameters: this.fromObject };
        })();
    }

    constructor(
        public override context: Context,
        __options?: xtremIntacct.interfaces.Query<T>,
    ) {
        super(context, __options);

        if (!__options?.objectName) {
            throw new BusinessRuleError(
                context.localize('@sage/xtrem-intacct/no-object-query', 'There is no object name to query.'),
            );
        }
        if (!__options?.fields?.length) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-intacct/no-fields-query',
                    'There are no fields to query on {{objectName}}.',
                    { objectName: __options?.objectName },
                ),
            );
        }

        this.fromObject = __options?.objectName;
        this.pageSize = this.options?.pageSize || this.packageConfig?.defaultPageSize || 1000;
        this.showPrivate = false;
        this.caseInsensitive = false;

        this.selectFields = __options?.fields?.map(field => {
            if (typeof field === 'string') {
                return new Field({ fieldName: field });
            }
            return new Field({ fieldName: field.name, function: field.type });
        });

        if (__options.filter) {
            this.filter = __options.filter as Ifilter;
        }

        /**
         * orderBy Fields :
         *
         */
        if (__options.orderBy) {
            this.orderBy = __options.orderBy.map((field: xtremIntacct.interfaces.OrderFields | string) => {
                if (typeof field === 'string') {
                    return new OrderDirection({ fieldName: field, direction: 'ascending' });
                }
                if (field.isAscending) {
                    return new OrderDirection({ fieldName: field.name, direction: 'ascending' });
                }
                return new OrderDirection({ fieldName: field.name, direction: 'descending' });
            }) as Iorder[];
        }
    }

    /**
     * Add Field list for this query
     * @param {Iselect[]} selectFields
     *
     * @return IqueryFunction
     */
    public assignSelectFields(selectFields: Iselect[]): IqueryFunction {
        this.selectFields = selectFields;

        return this;
    }

    /**
     * Add object name for given fromObject for this query
     * @param {string} fromObject
     *
     * @return IqueryFunction
     */
    public assignFromObject(fromObject: string): IqueryFunction {
        this.fromObject = fromObject;

        return this;
    }

    /**
     * Add docParId for this query
     * @param {string} docParId
     *
     * @return IqueryFunction
     */
    public assignDocParId(docParId: string): IqueryFunction {
        this.docParId = docParId;

        return this;
    }

    /**
     * Add Filter for this query
     * @param filter
     */
    public assignFilter(filter: Ifilter): IqueryFunction {
        this.filter = filter;

        return this;
    }

    /**
     * Add Order by list for this query
     * @param {Iorder[]} orderBy
     *
     * @return Iorder[]
     */
    public assignOrderBy(orderBy: Iorder[]): IqueryFunction {
        this.orderBy = orderBy;

        return this;
    }

    /**
     * Add case insensitive for this query
     * @param {boolean} caseInsensitive
     *
     * @return IqueryFunction
     */
    public assignCaseInsensitive(caseInsensitive: boolean): IqueryFunction {
        this.caseInsensitive = caseInsensitive;

        return this;
    }

    /**
     * Add show private for this query
     * @param showPrivate
     *
     * @return IqueryFunction
     */
    public assignShowPrivate(showPrivate: boolean): IqueryFunction {
        this.showPrivate = showPrivate;

        return this;
    }

    /**
     * Add page size for this query
     * @param {number} pageSize
     *
     * @return IqueryFunction
     */
    public assignPageSize(pageSize: number): IqueryFunction {
        this.pageSize = pageSize;

        return this;
    }

    /**
     * Add offset for this query
     * @param {number} offset
     *
     * @return IqueryFunction
     */
    public assignOffset(offset: number): IqueryFunction {
        this.offset = offset;

        return this;
    }

    public override writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('function');
        xml.writeAttribute('controlid', this.controlId, true);

        xml.writeStartElement('query');

        if (this.selectFields != null && this.selectFields.length > 0) {
            xml.writeStartElement('select');
            this.selectFields.forEach(select => {
                select.writeXml(xml);
            });

            xml.writeEndElement(); // select
        }

        xml.writeElement('object', this.fromObject, false);
        xml.writeElement('docparid', this.docParId);

        if (this.filter) {
            xml.writeStartElement('filter');
            this.filter.writeXml(xml);
            xml.writeEndElement(); // filter
        }

        if (this.orderBy != null && this.orderBy.length > 0) {
            xml.writeStartElement('orderby');
            this.orderBy.forEach(order => {
                order.writeXml(xml);
            });
            xml.writeEndElement(); // orderby
        }

        xml.writeStartElement('options');
        xml.writeElement('caseinsensitive', this.caseInsensitive ? 'true' : 'false');
        xml.writeElement('showprivate', this.showPrivate ? 'true' : 'false');
        xml.writeEndElement(); // options

        xml.writeElement('pagesize', this.pageSize);
        xml.writeElement('offset', this.offset);
        xml.writeEndElement(); // query
        xml.writeEndElement(); // function
    }
}
