import * as IA from '@intacct/intacct-sdk';
import type { IntacctControlIdProvider } from '@sage/intacct-controlid-provider';
import { IntacctControlIdBuilder } from '@sage/intacct-controlid-provider';
import * as xtremCommunication from '@sage/xtrem-communication';
import { ConfigManager } from '@sage/xtrem-config';
import type { AnyValue, Context, NodeUpdateData } from '@sage/xtrem-core';
import {
    Diagnose,
    SystemError,
    TextStream,
    ThirdPartyRequestAuditor,
    Uuid,
    ValidationSeverity,
    withRethrow,
} from '@sage/xtrem-core';
import * as xtremIntacct from '../../../index';
import type { IaXmlWriter } from '../_index';
import { Client } from '../_index';
import { AbstractFunction } from './abstract-function';

const thirdPartyId = 'Intacct';

const newIntacctSingleRecordRead = (context: Context): ThirdPartyRequestAuditor =>
    ThirdPartyRequestAuditor.newSingleRecordRead(context, thirdPartyId);

export class DefaultQuery<T extends AnyValue> extends AbstractFunction<T> {
    private _client: Client;

    get defaultCallBackContext(): Promise<xtremIntacct.sharedFunctions.interfaces.ContextCallback> {
        return (async () => {
            const user = await this.context.user;
            if (!user) {
                throw new SystemError('No user on the context');
            }
            return {
                function: 'defaultQuery',
                parameters: '',
                nodeName: '',
                intacctIdName: '',
                userId: user._id,
                lineId: Uuid.generate().toString(),
                retry: 0,
            };
        })();
    }

    get client(): Promise<Client> {
        return (async () => {
            this._client = this._client
                ? this._client
                : new Client(this.context, await (await this.intacctInstance).getClient(this.options?.entityId || ''));
            return this._client;
        })();
    }

    constructor(
        public override context: Context,
        options?: xtremIntacct.interfaces.CallOptions<T>,
    ) {
        super(context, options || {});

        if (!this.options.resultFunction) {
            throw new SystemError('No resultFunction ');
        }
    }

    public writeXml(xml: IaXmlWriter): void {
        this.logger.warn(() => `Not implemented : ${xml.flush()}`);
    }

    /**
     *  ##Launch the executeOnlineRequest & return the Result
     *   #
     * @returns  IA.Xml.Response.Result
     */
    async executeResult(): Promise<{ isMock: boolean; result: IA.Xml.Response.Result }> {
        /**
         * Mocks part Reading
         */
        if (this.isUnitTest) {
            const resultMock = xtremIntacct.functions.Client.mockRequest(
                this.context.testConfig,
                await this.xmlWithoutFunction(true),
            );
            if (resultMock && resultMock instanceof IA.Xml.Response.Result) {
                return { isMock: true, result: resultMock };
            }
            this.logger.debug(() => `resultMock : ${resultMock}`);
        }

        const requestConfig = new IA.RequestConfig();

        requestConfig.policyId = (await (await this.intacctInstance).policyId) || '';

        await this.logger.debugAsync(
            async () =>
                `executeResult - Client conf : ${(await this.client).config?.entityId} - ${
                    (await this.client).config?.companyId
                } \n ${await this.xmlWithoutFunction(true)}`,
        );

        const response = await (await this.client).executeOnlineRequest(this, requestConfig);
        const result = response.getResult();
        /**
         * Mocks part - Writting
         */
        if (this.isUnitTest) {
            xtremIntacct.functions.Client.writeResponse(
                this.context.testConfig,
                result,
                await this.xmlWithoutFunction(true),
            );
        }

        return { isMock: false, result };
    }

    execute(): Promise<T> {
        const intacctRequestAuditor = newIntacctSingleRecordRead(this.context);
        return withRethrow(
            async () => {
                const { result } = await this.executeResult();

                if (result.status === 'failure') {
                    intacctRequestAuditor.recordFailure();
                    this.logger.error(() => `Status : ${result.status} function : ${result.functionName} `);
                    if (result.errors) {
                        this.logger.error(() => `Errors :  ${result.errors.join('\n')}`);
                        this.diagnoses.push(
                            ...result.errors.map(
                                error => new Diagnose(ValidationSeverity.error, [result.functionName], error),
                            ),
                        );
                    } else {
                        this.logger.error(() => `NYI ! Errors :  ${JSON.stringify(result)}`);
                    }
                }

                const { resultFunction } = this.options;
                if (!resultFunction) {
                    throw new SystemError('No resultFunction ');
                }

                await this.logger.debugAsync(async () => `response : ${JSON.stringify(await resultFunction(result))}`);

                intacctRequestAuditor.recordSuccess();
                return resultFunction(result);
            },
            err => {
                intacctRequestAuditor.recordFailure();
                return err;
            },
        );
    }

    async createMessageHistory(
        callBackContext: xtremIntacct.sharedFunctions.interfaces.ContextCallback,
        controlId: string,
    ): Promise<void> {
        await xtremCommunication.nodes.SysMessageHistory.createOrUpdateMessageHistory(this.context, {
            integrationSolution: 'intacct',
            id: controlId,
            context: callBackContext,
            sentRequest: TextStream.fromString(await this.xmlWithoutFunction(false), 'text/xml'),
        });
    }

    async updateMessageHistory(
        controlId: string,
        data: NodeUpdateData<xtremCommunication.nodes.SysMessageHistory>,
    ): Promise<void> {
        await xtremCommunication.nodes.SysMessageHistory.createOrUpdateMessageHistory(this.context, {
            integrationSolution: 'intacct',
            id: controlId,
            ...data,
        });
    }

    /**
     * Send the request to the intacctSend queue
     * @param callBackContext the context send to the getControlId
     * @param resultMock  The answer from intacct
     */
    async localMockReceivedRequest(
        callBackContext: xtremIntacct.sharedFunctions.interfaces.ContextCallback,
        resultMock: IA.Xml.Response.Result | string,
        controlId: string,
    ): Promise<void> {
        await this.context.send(xtremIntacct.queues.intacctSend, {
            payload: { context: callBackContext, intacctResponse: resultMock },
            attributes: {
                TenandId: this.context.tenantId || '7'.repeat(21),
                MessageKind: 'IntacctCallback',
                ControlId: controlId,
                locale: this.context.currentLocale,
            },
        });
    }

    /**
     * Execute asynchronous api call data => data return
     * @param callBackContext Will be send to the asynchronous
     */
    async executeAsync(
        optionalCallBackContext?: xtremIntacct.sharedFunctions.interfaces.ContextCallback,
    ): Promise<IA.Xml.OfflineResponse | xtremIntacct.classes.sdk.Xml.OfflineResponse<T>> {
        const callBackContext = optionalCallBackContext || (await this.defaultCallBackContext);
        const requestConfig = new IA.RequestConfig();

        if (this.isUnitTest) {
            if (!this.context.testConfig) {
                // if isUnitTest is true testConfig can not be null
                throw new SystemError('No testConfig on the context');
            }
            let resultMock = xtremIntacct.functions.Client.mockRequest(
                this.context.testConfig,
                await this.xmlWithoutFunction(true),
            );
            if (!resultMock) {
                this.logger.warn(`${requestConfig.controlId} -  No mock result executeOnlineRequest `);
                resultMock = (await (await this.client).executeOnlineRequest(this, requestConfig)).xml;
                xtremIntacct.functions.Client.writeResponse(
                    this.context.testConfig,
                    resultMock,
                    await this.xmlWithoutFunction(true),
                );
            }
            /**
             *  Send the result mocked into the sqs Query & return the default Offline response
             */
            if (resultMock && resultMock instanceof IA.Xml.Response.Result) {
                // TODO For unit testing : directily call the intacctCallback function from listener ( function to extend ? )
                return DefaultQuery.offlineResponseForUnitTest('mockedControlId');
            }
            throw new SystemError(`Issue not handle ${JSON.stringify(resultMock)}`);
        }

        requestConfig.policyId = (await (await this.intacctInstance).policyId) || 'xt_async_1';
        requestConfig.encoding = 'UTF-8';
        requestConfig.controlId = await this.getControlId(callBackContext);

        await this.logger.debugAsync(() => this.xmlWithoutFunction(true));

        await this.createMessageHistory(callBackContext, requestConfig.controlId);
        /**
         * Local mode implementation
         * Returning always a default offlineResponse
         */
        if (xtremIntacct.functions.isLocalImplementation(this.context)) {
            this.logger.warn(() => `Using Local implementation : execute Online , send the result to listner `);
            const resultMock = (await (await this.client).executeOnlineRequest(this, requestConfig)).body;
            this.logger.debug(() => 'Send to queue');
            if (this.context.isWritable) {
                await this.localMockReceivedRequest(callBackContext, resultMock, requestConfig.controlId);
            } else {
                this.logger.error(() => `Context is readonly can't send the result to the queue  `);
            }
            await this.updateMessageHistory(requestConfig.controlId, {
                status: 'sent',
                communicationDiagnoses: { isLocalImplementation: true },
            });
            return DefaultQuery.offlineResponseForUnitTest(requestConfig.controlId);
        }

        const result = await (await this.client).executeOfflineRequest<T>(this, requestConfig);

        this.logger.debug(() => `result status : ${result.status} controlId:  ${result.control.controlId} `);

        if (result.status !== 'success') {
            await this.updateMessageHistory(requestConfig.controlId, {
                status: 'notSent',
                communicationDiagnoses: result.xml,
            });
            this.logger.error(() => JSON.stringify(result.xml, null, 4));
        } else {
            await this.updateMessageHistory(requestConfig.controlId, {
                status: 'sent',
            });
        }
        return result;
    }

    async getControlId(callBackContext: xtremIntacct.sharedFunctions.interfaces.ContextCallback): Promise<string> {
        let controlIdProvider: IntacctControlIdProvider;
        if (!ConfigManager.current.clusterId) {
            this.logger.error(() => `No ClusterId on current configuration cluster-ci is used `);
        }

        try {
            if (
                (await (await this.intacctInstance).controlIdTableName) &&
                (await (await this.intacctInstance).controlIdTableName) !== 'localDev'
            ) {
                await this.logger.debugAsync(
                    async () =>
                        `*** getCloudImplementation ***
                        dynamoDBTableName:'${await (await this.intacctInstance).controlIdTableName}'
                        clusterId:${ConfigManager.current.clusterId || 'cluster-ci (default)'}`,
                );
                controlIdProvider = IntacctControlIdBuilder.getCloudImplementation({
                    app: ConfigManager.current.app,
                    clusterId: ConfigManager.current.clusterId || 'cluster-ci',
                    dynamoDBTableName: await (await this.intacctInstance).controlIdTableName,
                    awsConfigOverride: {
                        region: ConfigManager.current.aws?.region || 'eu-west-1',
                    },
                });
            } else {
                this.logger.debug(() => `*** getLocalDevImplementation *** \n  `);
                controlIdProvider = IntacctControlIdBuilder.getLocalDevImplementation();
            }
            this.logger.debug(() => `*** prepareAsyncIntacctQuery *** \n  with ${this.context.tenantId} tenantID`);

            return await controlIdProvider.prepareAsyncIntacctQuery(
                this.context.tenantId || '',
                await (
                    await this.intacctInstance
                ).asyncTimeout,
                {
                    ...callBackContext,
                    version: 1,
                },
            );
        } catch (ex) {
            this.logger.error(`getControlId function : ${ex}`);
            return Uuid.generate().toString();
        }
    }

    static offlineResponseForUnitTest(controlId: string): IA.Xml.OfflineResponse {
        return new IA.Xml.OfflineResponse(`<
        response>
            <acknowledgement><status>success</status></acknowledgement>
            <control>
                <status>success</status>
                <senderid>testsenderid</senderid>
                <controlid>${controlId}</controlid>
                <uniqueid>false</uniqueid>
                <dtdversion>3.0</dtdversion>
            </control>
        </response>`);
    }
}
