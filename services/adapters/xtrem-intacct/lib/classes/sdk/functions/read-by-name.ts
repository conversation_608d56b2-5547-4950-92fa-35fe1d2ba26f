import type { AnyValue, Context } from '@sage/xtrem-core';
import { SystemError } from '@sage/xtrem-core';
import type * as xtremIntacct from '../../../index';
import type { IaXmlWriter } from '../_index';
import { DefaultQuery } from './_index';

export class ReadByName<T extends AnyValue> extends DefaultQuery<T> {
    public static readonly MAX_KEY_COUNT = 100;

    public objectName: string;

    public fields: string[] | undefined;

    public docParId: string;

    private _names: string[];

    get names(): string[] {
        return this._names;
    }

    set names(names: string[]) {
        if (names !== null && names.length > ReadByName.MAX_KEY_COUNT) {
            throw new SystemError(`Names count cannot exceed ${ReadByName.MAX_KEY_COUNT}`);
        }

        this._names = names;
    }

    constructor(
        public override context: Context,
        queryOptions?: xtremIntacct.interfaces.Query<T>,
    ) {
        super(context, queryOptions);

        if (!queryOptions?.objectName) {
            throw new SystemError('No objectName');
        }

        if (Array.isArray(queryOptions.filter) && queryOptions.filter.every(item => typeof item === 'string')) {
            this.names = queryOptions.filter;
        }

        this.objectName = queryOptions?.objectName;

        this.fields = queryOptions.fields?.map(field => {
            if (typeof field !== 'string') {
                throw new SystemError('Field must be string');
            }
            return field;
        });
    }

    getKeys() {
        return this.names.map(name => name.replace(',', '\\,')).join(',') || '';
    }

    public override writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('function');
        xml.writeAttribute('controlid', this.controlId, true);

        xml.writeStartElement('readByName');

        xml.writeElement('object', this.objectName, true);
        xml.writeElement('keys', this.getKeys(), true);
        xml.writeElement(
            'fields',
            this.fields !== undefined && this.fields.length > 0 ? this.fields.join(',') : '*',
            true,
        );
        xml.writeElement('returnFormat', 'xml');
        xml.writeElement('docparid', this.docParId);

        xml.writeEndElement(); // readByName

        xml.writeEndElement(); // function
    }
}
