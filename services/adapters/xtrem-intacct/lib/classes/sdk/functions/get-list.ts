import type { AnyValue, Context } from '@sage/xtrem-core';
import type * as xtremIntacct from '../../../index';
import type { IaXmlWriter } from '../_index';
import type { QueryOperator } from './_index';
import { DefaultQuery } from './_index';

/**
 * Extend intacct abstractFunction to create the get_list xml api request using objectName & Filter
 */
export class GetList<T extends AnyValue> extends DefaultQuery<T> {
    /**
     * XML Call getList
     * @param objectName ogject you want the list
     * @param filter can be array of filter
     */
    constructor(
        public override readonly context: Context,
        ___options: xtremIntacct.interfaces.CallOptions<T>,
        public objectName: string,
        public filter?: QueryOperator.Filter,
    ) {
        super(context, ___options);
    }

    public override writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('function');
        xml.writeAttribute('controlid', this.controlId, true);
        xml.writeStartElement('get_list');
        xml.writeAttribute('object', this.objectName, true);
        if (this.filter) {
            xml.writeStartElement('filter');
            this.filter.writeXml(xml);
            xml.writeEndElement(); // filter
        }
        xml.writeEndElement(); // get_list
        xml.writeEndElement(); // function
    }
}
