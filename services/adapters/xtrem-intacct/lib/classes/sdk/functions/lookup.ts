import type { AnyValue, Context } from '@sage/xtrem-core';
import { SystemError } from '@sage/xtrem-core';
import type * as xtremIntacct from '../../../index';
import type { IaXmlWriter } from '../_index';
import { DefaultQuery } from './_index';

/**
 * Lookup intacct api :
 *
 */
export class Lookup<T extends AnyValue> extends DefaultQuery<T> {
    constructor(
        public override context: Context,
        public objectName: string,
        lookupOptions: xtremIntacct.interfaces.CallOptions<T>,
    ) {
        super(context, lookupOptions);
        if (!objectName) {
            throw new SystemError('objectName empty for Lookup');
        }
    }

    public override writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('function');
        xml.writeAttribute('controlid', this.controlId, true);

        xml.writeStartElement('lookup');

        xml.writeElement('object', this.objectName, true);

        xml.writeEndElement(); // read

        xml.writeEndElement(); // function
    }
}
