import type * as IA from '@intacct/intacct-sdk';
/**
 * Copyright 2020 Sage Intacct, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"). You may not
 * use this file except in compliance with the License. You may obtain a copy
 * of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "LICENSE" file accompanying this file. This file is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import * as url from 'url';

export class Endpoint {
    public static readonly DEFAULT_ENDPOINT = 'https://api.intacct.com/ia/xml/xmlgw.phtml';

    public static readonly ENDPOINT_URL_ENV_NAME = 'INTACCT_ENDPOINT_URL';

    public static readonly DOMAIN_NAME = 'intacct.com';

    public static readonly FULL_QUALIFIED_DOMAIN_NAME = `${Endpoint.DOMAIN_NAME}.`;

    private static isDomainValid(hostname: string): boolean {
        const checkMainDomain = `.${Endpoint.DOMAIN_NAME}`;
        const checkFQDNDomain = `.${Endpoint.FULL_QUALIFIED_DOMAIN_NAME}`;
        return (
            hostname.substr(-checkMainDomain.length) === checkMainDomain ||
            hostname.substr(-checkFQDNDomain.length) === checkFQDNDomain
        );
    }

    private _url: string;

    get url(): string {
        return this._url;
    }

    set url(address: string) {
        this._url = address && address.length > 0 ? address : Endpoint.DEFAULT_ENDPOINT;

        const parsedUrl = url.parse(this._url);
        if (parsedUrl && parsedUrl.hostname && !Endpoint.isDomainValid(parsedUrl.hostname)) {
            throw new Error(`Endpoint URL is not a valid ${Endpoint.DOMAIN_NAME} domain name.`);
        }
    }

    constructor(config: IA.ClientConfig) {
        this.url = config.endpointUrl || process.env[Endpoint.ENDPOINT_URL_ENV_NAME] || Endpoint.DEFAULT_ENDPOINT;
    }
}
