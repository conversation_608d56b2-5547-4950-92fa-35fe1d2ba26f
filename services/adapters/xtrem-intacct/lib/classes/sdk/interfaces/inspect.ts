type ExternalDataName = 'integer' | 'string' | 'boolean' | 'datetime';

interface InspectBase {
    $: {
        Name: string;
    };
}

export interface InspectResult extends InspectBase {
    Fields: {
        Field: string[];
    };
}

export interface InspectWithDetailsResult extends Omit<InspectResult, 'Fields'> {
    Attributes: {
        SingularName: string;
        PluralName: string;
        Description: '';
    };
    Fields: {
        Field: [
            {
                Name: string;
                GroupName: string;
                dataName: string;
                externalDataName: ExternalDataName;
                isRequired: boolean;
                isReadOnly: boolean;
                maxLength: number;
                DisplayLabel: string;
                Description: string;
                id: number;
            },
        ];
    };
}

export interface InspectObject {
    name: string;
    object: string;
}
