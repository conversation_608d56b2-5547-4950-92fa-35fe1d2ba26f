import type * as IA from '@intacct/intacct-sdk';
import type { AnyValue, AsyncResponse } from '@sage/xtrem-core';
import type * as xtremIntacct from '../../../index';
import type { IaXmlWriter } from '../_index';
/**
 * Rewritting sdk : step by step // not in use
 */

export interface Ifunction<T extends AnyValue> {
    controlId: string;

    writeXml(xml: IaXmlWriter): AsyncResponse<void>;
    execute(): Promise<T>;
    executeAsync(): Promise<IA.Xml.OfflineResponse | xtremIntacct.classes.sdk.Xml.OfflineResponse<T>>;
}
