import type { Ifilter } from './i-filter';
import type { Iorder } from './i-order';
import type { Iselect } from './i-select';

export interface IqueryFunction {
    selectFields: Iselect[];

    fromObject: string;

    docParId: string;

    filter: Ifilter;

    orderBy: Iorder[];

    caseInsensitive: boolean;

    showPrivate: boolean;

    pageSize: number;

    offset: number;

    assignSelectFields(selectFields: Iselect[]): IqueryFunction;

    assignFromObject(fromObject: string): IqueryFunction;

    assignDocParId(docParId: string): IqueryFunction;

    assignFilter(filter: Ifilter): IqueryFunction;

    assignOrderBy(orderBy: Iorder[]): IqueryFunction;

    assignCaseInsensitive(caseInsensitive: boolean): IqueryFunction;

    assignShowPrivate(showPrivate: boolean): IqueryFunction;

    assignPageSize(pageSize: number): IqueryFunction;

    assignOffset(offset: number): IqueryFunction;
}
