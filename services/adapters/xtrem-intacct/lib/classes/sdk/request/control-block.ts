import type * as IA from '@intacct/intacct-sdk';
import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, Logger, SystemError } from '@sage/xtrem-core';
import type { IaXmlWriter, Interfaces } from '../_index';

export class ControlBlock implements Interfaces.IxmlObject {
    private _senderId: string;

    get senderId(): string {
        return this._senderId;
    }

    set senderId(senderId: string) {
        if (!senderId) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-intacct/sender-id-required',
                    'Sender ID is required and cannot be blank',
                ),
            );
        }
        this._senderId = senderId;
    }

    private _password: string;

    get password(): string {
        return this._password;
    }

    set password(password: string) {
        if (!password) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-intacct/sender-password-required',
                    'Sender Password is required and cannot be blank',
                ),
            );
        }
        this._password = password;
    }

    private _controlId: string;

    get controlId(): string {
        return this._controlId;
    }

    set controlId(controlId: string) {
        this._controlId = controlId || Date.now().toString();

        if (controlId.length < 1 || controlId.length > 256) {
            throw new SystemError('Request control ID must be between 1 and 256 characters in length.');
        }
    }

    private _uniqueId: boolean;

    get uniqueId(): boolean {
        return this._uniqueId;
    }

    set uniqueId(uniqueId: boolean) {
        this._uniqueId = uniqueId;
    }

    private _dtdVersion: string;

    get dtdVersion(): string {
        return this._dtdVersion;
    }

    private _policyId: string;

    get policyId(): string {
        return this._policyId;
    }

    set policyId(policyId: string) {
        this._policyId = policyId;
    }

    private _includeWhitespace: boolean;

    get includeWhitespace(): boolean {
        return this._includeWhitespace;
    }

    set includeWhitespace(includeWhitespace: boolean) {
        this._includeWhitespace = includeWhitespace;
    }

    constructor(
        public context: Context,
        clientConfig: IA.ClientConfig,
        requestConfig: IA.RequestConfig,
        public logger: Logger = Logger.getLogger(__filename, 'block'),
    ) {
        this.logger.debug(() => `senderId : ${clientConfig.senderId}`);
        this.senderId = clientConfig.senderId;
        this.password = clientConfig.senderPassword;
        this.controlId = requestConfig.controlId;
        this.uniqueId = requestConfig.uniqueId;
        this.policyId = requestConfig.policyId;
        this.includeWhitespace = false;
        this._dtdVersion = '3.0';
    }

    public writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('control');
        xml.writeElement('senderid', this.senderId);
        xml.writeElement('password', this.password);
        xml.writeElement('controlid', this.controlId);
        xml.writeElement('uniqueid', this.uniqueId === true ? 'true' : 'false');
        xml.writeElement('dtdversion', this.dtdVersion);
        if (this.policyId) {
            xml.writeElement('policyid', this.policyId);
        }
        xml.writeElement('includewhitespace', this.includeWhitespace === true ? 'true' : 'false');
        xml.writeEndElement(); // control
    }
}
