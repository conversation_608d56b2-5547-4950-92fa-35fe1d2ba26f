import type * as IA from '@intacct/intacct-sdk';
import type { AnyValue, Context } from '@sage/xtrem-core';
import { Logger } from '@sage/xtrem-core';
import * as xmlBuilder from 'xmlbuilder';
import type { Interfaces } from '../_index';
import { IaXmlWriter, Request } from '../_index';

export class RequestBlock<T extends AnyValue> {
    public controlBlock: Request.ControlBlock;

    public operationBlock: Request.OperationBlock<T>;

    private _encoding: string;

    get encoding(): string {
        return this._encoding;
    }

    set encoding(encoding: string) {
        // TODO Can this validate it is a supported encoding by the system?
        this._encoding = encoding || 'utf-8';
    }

    constructor(
        public context: Context,
        clientConfig: IA.ClientConfig,
        requestConfig: IA.RequestConfig,
        content: string | Interfaces.Ifunction<T> | Interfaces.Ifunction<T>[],
        public logger: Logger = Logger.getLogger(__filename, 'block'),
    ) {
        this.logger.debug(() => `RequestBlock : control & Operation Block  `);
        this.encoding = requestConfig.encoding;
        this.controlBlock = new Request.ControlBlock(this.context, clientConfig, requestConfig, logger);
        this.operationBlock = new Request.OperationBlock(this.context, clientConfig, requestConfig, content, logger);
    }

    public async writeXml(): Promise<string> {
        const xml = new IaXmlWriter(
            xmlBuilder.create(
                'request',
                {
                    version: '1.0',
                    encoding: this.encoding,
                },
                {},
                { invalidCharReplacement: '' },
            ),
        );

        this.controlBlock.writeXml(xml);
        await this.operationBlock.writeXml(xml);

        return xml.flush(false);
    }
}
