import { SystemError } from '@sage/xtrem-core';
import type { IaXmlWriter, Interfaces } from '../_index';

export class SessionAuthentication implements Interfaces.Iauthentication {
    private _sessionId: string;

    get sessionId(): string {
        return this._sessionId;
    }

    set sessionId(sessionId: string) {
        if (!sessionId) {
            throw new SystemError('Session ID is required and cannot be blank');
        }
        this._sessionId = sessionId;
    }

    constructor(sessionId: string) {
        this.sessionId = sessionId;
    }

    public writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('authentication');
        xml.writeElement('sessionid', this.sessionId);
        xml.writeEndElement(); // authentication
    }
}
