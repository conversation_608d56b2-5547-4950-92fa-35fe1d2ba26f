import type { AnyValue, Context } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';

/** to clean password from xml use for mocks  */
function dataCleaner(data: string): string {
    return data
        .replace(/<password>[^<>]+<\/password>/g, '') // delete the password control & auth
        .replace(/\scontrolid="\S+"/, '') // delete the controlId on function field
        .replace(/<controlid>\d+<\/controlid>/, ''); // delete the control id on control field
}

export class HttpClientHandler {
    constructor(
        public context: Context,
        public options: AxiosRequestConfig,
    ) {}

    public postAsync<T extends AnyValue>(): Promise<AxiosResponse<T>> {
        return xtremSystem.functions.axiosFetcher<T>(this.context, this.options, dataCleaner);
    }
}
