import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import type { IaXmlWriter, Interfaces } from '../_index';

export class LoginAuthentication implements Interfaces.Iauthentication {
    private _companyId: string;

    private _entityId: string | undefined;

    private _userId: string;

    private _password: string;

    get companyId(): string {
        return this._companyId;
    }

    set companyId(companyId: string) {
        if (!companyId) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-intacct/company-id-required',
                    'Sage Intacct integration settings: A company ID is required and cannot be blank.',
                ),
            );
        }
        this._companyId = companyId;
    }

    get entityId(): string {
        return this._entityId || '';
    }

    set entityId(entityId: string) {
        this._entityId = entityId;
    }

    get userId(): string {
        return this._userId;
    }

    set userId(userId: string) {
        if (!userId) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-intacct/user-id-required',
                    'Sage Intacct integration settings: A user ID is required and cannot be blank.',
                ),
            );
        }
        this._userId = userId;
    }

    get password(): string {
        return this._password;
    }

    set password(password: string) {
        if (!password) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-intacct/user-password-required',
                    'Sage Intacct integration settings: A user password is required and cannot be blank.',
                ),
            );
        }
        this._password = password;
    }

    constructor(
        public context: Context,
        userId: string,
        companyId: string,
        userPassword: string,
        entityId?: string,
    ) {
        this.companyId = companyId;
        this.userId = userId;
        this.password = userPassword;
        this.entityId = entityId !== undefined ? entityId : '';
    }

    public writeXml(xml: IaXmlWriter): void {
        xml.writeStartElement('authentication');
        xml.writeStartElement('login');
        xml.writeElement('userid', this.userId);
        xml.writeElement('companyid', this.companyId);
        xml.writeElement('password', this.password);
        if (this.entityId != null) {
            xml.writeElement('locationid', this.entityId);
        }
        xml.writeEndElement(); // login
        xml.writeEndElement(); // authentication
    }
}
