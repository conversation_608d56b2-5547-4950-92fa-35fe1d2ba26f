import type * as IA from '@intacct/intacct-sdk';
import type { AnyValue, Context } from '@sage/xtrem-core';
import { Logger, SystemError } from '@sage/xtrem-core';
import type { AxiosRequestConfig } from 'axios';
import * as mimeTypes from 'mime-types';
import type { Interfaces } from '../_index';
import { Credentials, Request, Xml } from '../_index';

export class RequestHandler<T extends AnyValue> {
    public readonly version: string;

    public clientConfig: IA.ClientConfig;

    public requestConfig: IA.RequestConfig;

    public endpointUrl: string;

    public logger: Logger;

    constructor(
        public context: Context,
        clientConfig: IA.ClientConfig,
        requestConfig: IA.RequestConfig,
        _logger?: Logger,
    ) {
        this.version = 'xtrem-1.0.0';

        if (clientConfig.endpointUrl) {
            this.endpointUrl = clientConfig.endpointUrl;
        } else {
            const endpoint = new Credentials.Endpoint(clientConfig);
            this.endpointUrl = endpoint.url;
        }
        this.clientConfig = clientConfig;

        this.requestConfig = requestConfig;

        this.logger = _logger || Logger.getLogger(__filename, 'client');
    }

    public async executeOnline(
        content: Interfaces.Ifunction<T> | Interfaces.Ifunction<T>[] | string,
    ): Promise<Xml.OnlineResponse> {
        if (this.requestConfig.policyId != null && this.requestConfig.policyId !== '') {
            this.requestConfig.policyId = '';
        }

        const request = new Request.RequestBlock(
            this.context,
            this.clientConfig,
            this.requestConfig,
            content,
            this.logger,
        );

        const body = await this.execute(await request.writeXml());
        this.logger.debug(() => ` executed : ${body}`);
        return new Xml.OnlineResponse(body);
    }

    public async executeOffline(
        content: Interfaces.Ifunction<T> | Interfaces.Ifunction<T>[],
    ): Promise<Xml.OfflineResponse<T>> {
        if (!this.requestConfig.policyId) {
            throw new SystemError('Required Policy ID not supplied in config for offline request');
        }

        const request = new Request.RequestBlock(
            this.context,
            this.clientConfig,
            this.requestConfig,
            content,
            this.logger,
        );

        const body = await this.execute(await request.writeXml());
        return new Xml.OfflineResponse(body);
    }

    private static getHttpClient(context: Context, options: AxiosRequestConfig): Request.HttpClientHandler {
        return new Request.HttpClientHandler(context, options);
    }

    private async execute(xml: string): Promise<string> {
        const httpClient = RequestHandler.getHttpClient(this.context, {
            url: this.endpointUrl,
            method: 'POST',
            timeout: this.requestConfig.maxTimeout,
            data: xml,
            headers: {
                'Content-Type': 'application/xml',
                'Accept-Encoding': 'gzip',
                'User-Agent': `intacct-sdk-js-client/${this.version}`,
            },
            /** As axios don't know this properties we don't have to keep it
             * Un-comment if it's cause side effects
             */
            // simple: false,
            // resolveWithFullResponse: true,
            // gzip: true,
            // size: 0,
        });

        const result = await httpClient.postAsync<string>();

        this.logger.debug(() => ` Status = ${result.status}`);

        if (!(result.status >= 400 && result.status < 600)) {
            return result.data;
        }

        if (this.requestConfig.noRetryServerErrorCodes.indexOf(result.status) !== -1) {
            // Do not retry this explicitly set 500 level server error
            throw new SystemError(`${result.status} ${result.data} ${httpClient.options} , ${result}`);
        }

        // TODO: axios TS API does not have get method. Can we use result.headers['content-type'] instead?
        const contentTypeObj = mimeTypes.contentType(result.headers['content-type']);

        if (['text/xml', 'application/xml'].includes(contentTypeObj.toString())) {
            /** mimeTypes */
            return result.data;
        }
        throw new SystemError(`${result.status} ${result.data} ${httpClient.options} , ${result}`);
    }

    private static delay(ms: number): Promise<void> {
        return new Promise<void>(resolve => {
            setTimeout(resolve, ms);
        });
    }

    private static async exponentialDelay(retries: number) {
        const delay = (2 ** retries - 1) * 1000;
        await RequestHandler.delay(delay);
    }
}
