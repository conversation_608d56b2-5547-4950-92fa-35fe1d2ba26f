import * as IA from '@intacct/intacct-sdk';
import type { AnyValue, Context } from '@sage/xtrem-core';
import { asyncArray, Logger, SystemError } from '@sage/xtrem-core';
import * as _ from 'lodash';
import type { IaXmlWriter, Interfaces } from '../_index';
import { LoginAuthentication, SessionAuthentication } from './_index';

export class OperationBlock<T extends AnyValue> implements Interfaces.IxmlObject {
    public transaction: boolean;

    public authentication: Interfaces.Iauthentication;

    public content: string | Interfaces.Ifunction<T> | Interfaces.Ifunction<T>[];

    constructor(
        public context: Context,
        clientConfig: IA.ClientConfig,
        requestConfig: IA.RequestConfig,
        content: string | Interfaces.Ifunction<T> | Interfaces.Ifunction<T>[],
        public logger: Logger = Logger.getLogger(__filename, 'block'),
    ) {
        this.transaction = requestConfig.transaction;

        const { credentials } = clientConfig;

        if (credentials != null && credentials instanceof IA.Credentials.SessionCredentials) {
            this.authentication = new SessionAuthentication(credentials.sessionId);
        } else if (credentials != null && credentials instanceof IA.Credentials.LoginCredentials) {
            this.logger.debug(() => `LoginCredentials : ${credentials.companyId} - ${credentials.userId}`);
            this.authentication = new LoginAuthentication(
                this.context,
                credentials.userId,
                credentials.companyId,
                credentials.password,
                credentials.entityId,
            );
        } else if (clientConfig.sessionId != null) {
            this.authentication = new SessionAuthentication(clientConfig.sessionId);
        } else if (clientConfig.companyId != null && clientConfig.userId != null && clientConfig.userPassword != null) {
            this.logger.debug(() => `LoginAuthentication : ${clientConfig.companyId} - ${clientConfig.userId}`);
            this.authentication = new LoginAuthentication(
                this.context,
                clientConfig.userId,
                clientConfig.companyId,
                clientConfig.userPassword,
                clientConfig.entityId,
            );
        } else {
            throw new SystemError(
                'Authentication credentials [Company ID, User ID, and User Password] or [Session ID] ' +
                    'are required and cannot be blank.',
            );
        }

        this.content = content;
    }

    public async writeXml(xml: IaXmlWriter): Promise<void> {
        xml.writeStartElement('operation');
        xml.writeAttribute('transaction', this.transaction === true ? 'true' : 'false');

        this.authentication.writeXml(xml);

        xml.writeStartElement('content');

        if (_.isArray(this.content)) {
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            asyncArray(this.content).forEach(async apiFunction => {
                await apiFunction.writeXml(xml);
            });
        } else if (typeof this.content === 'string') {
            xml.writeString(this.content);
        } else {
            await this.content.writeXml(xml);
        }

        xml.writeEndElement(); // content

        xml.writeEndElement(); // operation
    }
}
