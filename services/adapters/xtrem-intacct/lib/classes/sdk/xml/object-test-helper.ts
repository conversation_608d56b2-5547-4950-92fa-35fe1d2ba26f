import * as XML<PERSON>uilder from 'xmlbuilder';
import * as sdk from '../_index';

/**
 * Used on unit testing : return the xml string of the IxmlObject
 */
export class XmlObjectTestHelper {
    public static CompareXml(apiFunction: sdk.Interfaces.IxmlObject): string {
        const xml = new sdk.IaXmlWriter(
            XMLBuilder.create('test', {
                version: '1.0',
                encoding: 'utf-8',
            }),
        );
        apiFunction.writeXml(xml);

        return xml.flush(true);
    }
}
