import * as IA from '@intacct/intacct-sdk';
import type { AnyValue } from '@sage/xtrem-core';
import { BusinessRuleError, Logger, SystemError } from '@sage/xtrem-core';
import * as jsXml from 'js-xml';

export abstract class AbstractResponse<T extends AnyValue> {
    protected _xml: { response: IA.Xml.AbstractResponse & { errormessage: object } & T };

    get xml(): { response: IA.Xml.AbstractResponse & { errormessage: object } & T } {
        return this._xml;
    }

    private _control: IA.Xml.Response.Control;

    public logger: Logger;

    get control(): IA.Xml.Response.Control {
        return this._control;
    }

    constructor(public body: string) {
        this.logger = Logger.getLogger(__filename, 'response');

        this.logger.debug(() => `${body}`);

        this._xml = jsXml.parse(body);

        this.logger.debug(() => ` body parsed : ${JSON.stringify(this.xml, null, 4)}`);

        if (!this.xml.response) {
            throw new SystemError('Response XML is missing root response element');
        }

        if (!this.xml.response.control) {
            throw new SystemError('Response block is missing control element');
        }

        this._control = new IA.Xml.Response.Control(this.xml.response.control);

        if (this.control.status !== 'success') {
            let errors: string[] = [];
            if (this.xml.response.errormessage) {
                const errorMessage = new IA.Xml.Response.ErrorMessage(this.xml.response.errormessage);
                errors = errorMessage.errors;
            }

            throw new BusinessRuleError(`${errors.join('\n')}`);
        }
    }
}
