import * as IA from '@intacct/intacct-sdk';
import { BusinessRuleError, SystemError } from '@sage/xtrem-core';
import * as _ from 'lodash';
import { AbstractResponse } from './_index';

export class OnlineResponse extends AbstractResponse<{
    operation: {
        authentication: object;
        result: object[];
        errormessage: object;
    };
}> {
    private _authentication: IA.Xml.Response.Authentication;

    get authentication(): IA.Xml.Response.Authentication {
        return this._authentication;
    }

    private _results: IA.Xml.Response.Result[];

    get results(): IA.Xml.Response.Result[] {
        return this._results;
    }

    constructor(body: string) {
        super(body);
        this._results = [];

        const operation = this.xml.response?.operation;
        if (!operation) {
            throw new SystemError('Response block is missing operation block');
        }

        if (!operation.authentication) {
            throw new SystemError('Authentication block is missing from operation element');
        }
        this._authentication = new IA.Xml.Response.Authentication(operation.authentication);
        if (this._authentication.status !== 'success') {
            const errorMessage = new IA.Xml.Response.ErrorMessage(operation.errormessage);

            throw new BusinessRuleError(`${errorMessage.errors.join('\n')}`);
        }
        if (!operation.result) {
            throw new SystemError('Result block is missing from operation element');
        }

        if (operation.result) {
            if (_.isArray(operation.result)) {
                operation.result.forEach(element => {
                    this._results.push(new IA.Xml.Response.Result(element));
                });
            } else {
                this._results.push(new IA.Xml.Response.Result(operation.result));
            }
        }
    }

    public getResult(key = 0): IA.Xml.Response.Result {
        return this._results[key];
    }
}
