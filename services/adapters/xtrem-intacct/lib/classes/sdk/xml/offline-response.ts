import type { AnyValue } from '@sage/xtrem-core';
import { SystemError } from '@sage/xtrem-core';
import { AbstractResponse } from './_index';

export class OfflineResponse<T extends AnyValue> extends AbstractResponse<T & { acknowledgement: { status: string } }> {
    private _status: string;

    get status(): string {
        return this._status;
    }

    constructor(body: string) {
        super(body);

        this.logger!.debug(() => ` offline response parsed : ${JSON.stringify(this.xml, null, 4)}`);

        if (!this.xml.response.acknowledgement) {
            throw new SystemError('Response block is missing acknowledgement block');
        }
        if (!this.xml.response.acknowledgement.status) {
            throw new SystemError('Acknowledgement block is missing status element');
        }
        this._status = this.xml.response.acknowledgement.status;
    }
}
