import { SystemError } from '@sage/xtrem-core';
import * as XMLBuilder from 'xmlbuilder';

export interface IsmartLink {
    /** id : smartLinkId  */
    id?: string;
    /** object : always in maj */
    object: string;
    /** events only one are allowed for now  */
    events?: string[];
    /** changeMade for the type of modificiation ( related with the event ) */
    changeMade?: string;
    /** objectId intacctId column name */
    objectId: string;
    /** apiActionBody will insert a custom body instead of the bodyTemplate */
    apiActionBody?: string;
    /** needed for apiAction */
    isSynchronous?: boolean;
}

export class CustomPackage {
    public xml: XMLBuilder.XMLElement;

    public customFields: XMLBuilder.XMLElement;

    public smartLinks: XMLBuilder.XMLElement;

    public customReports: XMLBuilder.XMLElement;

    public apiUser: string;

    public static bodyTemplate = `<create> 
                            <XTreeM_Audit_Trail> 
                                <object_name>{{object_name}}</object_name> 
                                <record_id>{!{{object_name}}.{{objectId}}!}</record_id> 
                                <record_number>{!{{object_name}}.RECORDNO!}</record_number> 
                                <change_made>{{changeMade}}</change_made> 
                            </XTreeM_Audit_Trail> 
                        </create>`;

    /**
     * ## Intacct custom package generator
     *  Info for intacct package description :
     *  @name
     *  @description
     *  <AUTHOR>
    constructor(custom: { name: string; description: string; author: string; apiUser: string }) {
        this.xml = XMLBuilder.create('customErpPackage', {
            version: '1.0',
            encoding: 'utf-8',
        });
        this.apiUser = custom.apiUser;
        this.xml.attribute('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance');
        this.xml = this.xml.element('packageDescription');
        this.xml.element('name', custom.name).up();
        this.xml.element('description', custom.description).up();
        this.xml.element('author', custom.author).up();
        this.xml = this.xml.up(); // packageDescription

        this.smartLinks = this.xml.element('smartLinks');

        /**
         * Keep for future dev : there we can add customFields
         */
        // this.customFields = this.xml.element('customFields');
    }

    public static getBodyTemplate(smartLink: IsmartLink) {
        return this.bodyTemplate
            .replace(/{{object_name}}/gim, smartLink.object.toUpperCase())
            .replace(/{{objectId}}/gim, smartLink.objectId)
            .replace(/{{changeMade}}/gim, smartLink.changeMade || 'set');
    }

    addSmarlinkCreateUpdateDelete(smartLink: IsmartLink) {
        this.addSmartLink({ ...smartLink, changeMade: 'create', events: ['add'] });
        this.addSmartLink({ ...smartLink, changeMade: 'delete', events: ['delete'] });
        this.addSmartLink({ ...smartLink, changeMade: 'update', events: ['set'] });
    }

    addSmartLink(smartLink: IsmartLink) {
        if (!smartLink.changeMade) {
            throw new SystemError(`No changeMade on IsmartLink - ${smartLink.object} `);
        }

        const smartLinkID = smartLink.id
            ? smartLink.id
            : `XTREEM_${smartLink.object}_${smartLink.changeMade.toUpperCase()}`;

        if (!smartLink.events) {
            throw new SystemError(`No events on IsmartLink - ${smartLinkID} `);
        }

        this.smartLinks = this.smartLinks.element('smartLink');
        this.smartLinks.element('smartLinkId', smartLinkID).up();
        this.smartLinks.element('type', 'workflow').up();
        this.smartLinks.element('ownerObject', smartLink.object.toLowerCase()).up();

        this.smartLinks = this.smartLinks.element('events');
        smartLink.events.forEach(event => {
            this.smartLinks.element('event', event);
        });
        this.smartLinks = this.smartLinks.up(); // event
        this.smartLinks = this.smartLinks.element('renderDetails').element('workflow');
        this.smartLinks.element('condition', `{!CURRENTUSER.LOGINID!} != '${this.apiUser}'`).up();

        this.smartLinks = this.smartLinks.element('action').element('apiAction');

        this.smartLinks
            .element(
                'body',
                undefined,
                smartLink.apiActionBody ? smartLink.apiActionBody : CustomPackage.getBodyTemplate(smartLink),
            )
            .up();
        this.smartLinks.element('synchronousApiAction', smartLink.isSynchronous || true).up();
        this.smartLinks = this.smartLinks.up().up().up().up(); // renderDetails , workflow , action , apiAction
        this.smartLinks = this.smartLinks.up(); // packageDescription
    }

    toString(isPretty?: boolean) {
        if (isPretty) {
            return this.xml.doc().end({
                pretty: true,
                indent: '    ',
            });
        }
        return this.xml.doc().end({
            pretty: false,
        });
    }
}
