import * as xtremCommunication from '@sage/xtrem-communication';
import { Activity } from '@sage/xtrem-core';
import * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremTax from '@sage/xtrem-tax';
import { Intacct } from '../nodes/intacct';

export const intacct = new Activity({
    description: 'Configuration',
    node: () => Intacct,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [
            {
                operations: ['defaultInstance', 'getEntities', 'getEntitiesAsynchronous'],
                on: [() => Intacct],
            },
            { operations: ['lookup'], on: [() => xtremTax.nodes.TaxSolution] },
            { operations: ['lookup'], on: [() => xtremSynchronization.nodes.SynchronizationState] },
        ],
        manage: [
            {
                operations: [
                    'create',
                    'update',
                    'delete',
                    'defaultInstance',
                    'getEntities',
                    'getEntitiesAsynchronous',
                    'sendEvent',
                    'sendXmlRequest',
                    'installXtreemAuditTrail',
                    'refreshEntities',
                    'retry',
                ],
                on: [() => Intacct],
            },
            { operations: ['lookup'], on: [() => xtremTax.nodes.TaxSolution] },
            { operations: ['lookup'], on: [() => xtremSynchronization.nodes.SynchronizationState] },
            { operations: ['purge'], on: [() => xtremCommunication.nodes.SysMessageHistory] },
        ],
    },
});
