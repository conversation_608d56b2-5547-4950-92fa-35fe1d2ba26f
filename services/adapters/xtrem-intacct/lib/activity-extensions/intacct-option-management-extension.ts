import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremIntacct from '../index';

export const intacctOptionManagementExtension = new ActivityExtension({
    extends: xtremStructure.activities.baseOptionManagement,
    __filename,
    permissions: [],
    operationGrants: {
        read: [
            { operations: ['defaultInstance', 'lookup'], on: [() => xtremIntacct.nodes.Intacct] },
            { operations: ['isServiceOptionActiveFunction'], on: [() => xtremIntacct.nodes.IntacctOptionManagement] },
        ],
        update: [
            { operations: ['serviceOptionChange'], on: [() => xtremIntacct.nodes.IntacctOptionManagement] },
            { operations: ['defaultInstance', 'lookup'], on: [() => xtremIntacct.nodes.Intacct] },
        ],
    },
});
