import type { integer } from '@sage/xtrem-core';
import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremIntacct from '../../index';

@decorators.nodeExtension<UserExtension>({
    extends: () => xtremSystem.nodes.User,
})
export class UserExtension extends NodeExtension<xtremSystem.nodes.User> {
    @decorators.stringProperty<UserExtension, 'intacctId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly intacctId: Promise<string>;

    @decorators.integerProperty<UserExtension, 'recordNo'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly recordNo: Promise<integer | null>;

    @decorators.booleanProperty<UserExtension, 'isIntacct'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isIntacct: Promise<boolean>;
}
declare module '@sage/xtrem-system/lib/nodes/user' {
    export interface User extends UserExtension {}
}
