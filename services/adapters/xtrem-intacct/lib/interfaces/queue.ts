export interface IntacctEventPayload {
    companyId: string;
    object: string;
    change: string;
    recordNumber: number;
    recordId: string;
    intacctIdName: string;
    lineId?: string;
}

export interface MessageAttributes {
    AwsRequestId: string;
    S3Payload: boolean;
    TenantId: string;
    TimedOut: false;
    receiveHandle: string;
    messageId: string;
    ControlId: string;
}
