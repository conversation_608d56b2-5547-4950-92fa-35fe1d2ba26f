import type * as IA from '@intacct/intacct-sdk';
import type { integer } from '@sage/xtrem-core';

/**
 * Example for intacct package config on xtrem-config :
 * packages:
 *   '@sage/xtrem-intacct-gateway':
 *       senderId: XT_mfg
 *       senderPassword: ***********
 *       policyID: xt_async_pdna
 *       intacctEventApiGatewayUrl: https://av0tr8xjsc.execute-api.eu-west-1.amazonaws.com/v1/intacctEvent
 *       defaultAsyncTimeout: 240
 *       importLinesCount: 1
 *       defaultPageSize: 100
 *       controlIdTableName:
 */

export interface IntacctPackageConfig {
    senderId: string;
    senderPassword: string;
    policyID: string;
    intacctEventApiGatewayUrl: string;
    controlIdTableName: string;
    defaultAsyncTimeout: number;
    importLinesCount: number;
    defaultPageSize: number;
}

export interface IaConfigurationBase {
    /** from xtrem-config ==> XT_mfg */
    senderId: string;
    /** from xtrem-config  */
    senderPassword: string;
    companyId?: string;
    userId?: string;
    endpointUrl: string;
    entityId?: string;
    /** xt_async_1 ==> for dev env (dev-sagextrem.com) */
    policyId: string;
}
/** senderId / senderPassword /  endpointUrl /  controlIdTableName / timeout */
export interface IaConfiguration extends IaConfigurationBase {
    userPassword?: string;
    /** dev-eu-intacct-callback (xtrem-config) */
    controlIdTableName: string;
    defaultAsyncTimeout: integer;
}

export interface IaSessionConfiguration extends IaConfigurationBase {
    sessionId: string;
    profileName: string;
    credentials: IA.Credentials.ICredentials;
}
