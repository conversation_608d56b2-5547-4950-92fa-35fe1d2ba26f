import type * as IA from '@intacct/intacct-sdk';
import type { AnyValue, AsyncResponse } from '@sage/xtrem-core';
import type * as xtremIntacct from '../../index';

export interface CallOptions<T extends AnyValue> {
    controlID?: string;
    entityId?: string;
    synchronous?: boolean;
    /**
     * Function to handle result
     */
    resultFunction?: (result: IA.Xml.Response.Result) => AsyncResponse<T>;
    /**
     *  Intacct instance : By default the active intacct instance, can be overload by intacctInstanceId option
     */
    intacctInstanceId?: string;
    pageSize?: number;
}

/**
 * Query for Ifunction folder
 */
export interface Query<T extends AnyValue> extends CallOptions<T> {
    objectName: string;
    filter?: string[] | IA.Functions.Common.NewQuery.QueryFilter.IFilter | xtremIntacct.classes.sdk.Interfaces.Ifilter; // not use on sdk !
    fields?: (QueryFields | string)[];
    orderBy?: (OrderFields | string)[];
    transactionName?: string;
}

export interface QueryFields {
    name: string;
    type?: xtremIntacct.enums.TypeField;
}

export interface OrderFields {
    name: string;
    isAscending?: true;
}

export type FilterType = 'like' | 'beetwen' | 'notEqualTo' | 'equalTo' | 'in';
export interface WhereFields {
    where: string;
    whereValue: string;
    /** for beetwen filter type */
    secondWhereValue?: string;
    /** filter type :  defaulted to equalTo  */
    type?: FilterType;
    /** for 'in' filter type */
    whereValueArray?: string[];
}

export interface GetIntacctDataParameters {
    fields: string[];
    objectName: string;
    isPrivateShow?: boolean;
    filters?: xtremIntacct.interfaces.WhereFields[];
    pageSize?: number;
}
