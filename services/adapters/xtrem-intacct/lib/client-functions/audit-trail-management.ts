import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import * as ui from '@sage/xtrem-ui';
import type { Intacct as GraphApiIntacct } from '../pages/intacct';

/** Intacct custom platfom object : xtreem_audit_trail management functions  */

const auditTrailMissmatch = ui.localize('@sage/xtrem-intacct/audit-trail-mismatch', 'Sage DMO audit trail mismatch');

export const auditTrailTitle = ui.localize('@sage/xtrem-intacct/audit-trail', 'Sage DMO audit trail');
const auditTrailInstalled = ui.localize(
    '@sage/xtrem-intacct/audit-trail-install-success',
    'Sage DMO audit trail installed.',
);
const auditTrailInstalledFail = ui.localize(
    '@sage/xtrem-intacct/audit-trail-install-failed',
    'Sage DMO audit trail installation failed.',
);

export const auditTrailInstallUpdate = ui.localize(
    '@sage/xtrem-intacct/audit-trail-install',
    'Do you want to install or update Sage DMO audit trail?',
);

export async function installXtreemAuditTrailExecute(page: GraphApiIntacct) {
    if (!page.id.value) {
        return;
    }
    page.installXTreeMAuditTrail.isDisabled = true;

    const installResult = await page.$.graph
        .node('@sage/xtrem-intacct/Intacct')
        .asyncOperations.installXtreemAuditTrail.runToCompletion(true, { intacctInstanceId: page.id.value })
        .execute();

    await page.$.dialog.message(
        installResult ? 'success' : 'error',
        auditTrailTitle,
        installResult ? auditTrailInstalled : auditTrailInstalledFail,
    );

    page.infoXTreeMAuditTrail.helperText = installResult ? '' : auditTrailMissmatch;
    await page.$.router.refresh();

    page.installXTreeMAuditTrail.isDisabled = false;
}

/**
 * Used on installXTreeMAuditTrail & Purge history
 * - Yes
 * - No
 */
export const options: ui.dialogs.DialogOptions = {
    acceptButton: {
        text: ui.localize('@sage/xtrem-intacct/pages-confirm-yes', 'Yes'),
    },
    cancelButton: {
        text: ui.localize('@sage/xtrem-intacct/pages-confirm-no', 'No'),
    },
    resolveOnCancel: false,
};

export function parseInfo(value: string | null): string {
    if (!value || value === '{}') {
        return '';
    }
    return `${JSON.parse(value).DESCRIPTION || ''} - ${JSON.parse(value).VERSION || ''}`;
}

export function auditTrailMismatchControl(page: GraphApiIntacct) {
    if (page.infoXTreeMAuditTrail.value === page.mustXtreemAuditTrail.value) {
        return false;
    }
    page.infoXTreeMAuditTrail.helperText = auditTrailMissmatch;

    return utils.confirmDialogToBoolean(
        page.$.dialog.confirmation(
            'warn',
            auditTrailMissmatch,
            ui.localize(
                '@sage/xtrem-intacct/audit-trail-update',
                `Do you want to update Sage DMO audit trail?
                 From: {{from}}
                 To: {{to}}`,
                {
                    from: parseInfo(page.infoXTreeMAuditTrail.value || '{}'),
                    to: parseInfo(page.mustXtreemAuditTrail.value || '{}'),
                },
            ),
            options,
        ),
    );
}
