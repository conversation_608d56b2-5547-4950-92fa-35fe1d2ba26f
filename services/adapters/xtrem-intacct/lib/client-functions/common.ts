import { extractEdges } from '@sage/xtrem-client';
import type { GraphApi } from '@sage/xtrem-intacct-api';
import * as ui from '@sage/xtrem-ui';

export const optionsForPurge: ui.dialogs.DialogOptions = {
    acceptButton: {
        text: ui.localize('@sage/xtrem-intacct/pages-confirm-purge', 'Delete'),
    },
    cancelButton: {
        text: ui.localize('@sage/xtrem-intacct/pages-confirm-cancel', 'Cancel'),
    },
    resolveOnCancel: false,
};

export const legislationIdsWithApArPosting = ['FR', 'DE'];

async function getListCompaniesByLegislation(parameters: {
    legislationIds: string[];
    page: ui.Page<GraphApi>;
}): Promise<string[]> {
    return extractEdges(
        await parameters.page.$.graph
            .node('@sage/xtrem-structure/Legislation')
            .query(
                ui.queryUtils.edgesSelector(
                    { companies: { query: ui.queryUtils.edgesSelector({ id: true }) } },
                    { filter: { id: { _in: parameters.legislationIds } } },
                ),
            )
            .execute(),
    ).flatMap(legislation => legislation.companies.map(company => company.id));
}

export async function intacctActivationDeactivationMessage(parameters: {
    page: ui.Page<GraphApi>;
    isActive: boolean;
    legislationIds: string[];
}): Promise<void> {
    if (parameters.legislationIds.some(legislationId => legislationIdsWithApArPosting.includes(legislationId))) {
        const message = parameters.isActive
            ? ui.localize(
                  '@sage/xtrem-intacct/client_functions__common__ap_ar_posting_to_gl_deactivation',
                  'You are going to deactivate the AP/AR posting to G/L on company {{companies}}.',
                  { companies: (await getListCompaniesByLegislation(parameters)).join(',') },
              )
            : ui.localize(
                  '@sage/xtrem-intacct/client_functions__common__ap_ar_posting_to_gl_activation',
                  'You are going to activate the AP/AR posting to the G/L on company {{companies}}. You need to review the tax management on the associated list of accounts.',
                  { companies: (await getListCompaniesByLegislation(parameters)).join(',') },
              );
        return parameters.page.$.dialog.message(
            'info',
            ui.localize('@sage/xtrem-intacct/client_functions__common__information', 'Information'),
            message,
            { resolveOnCancel: true },
        );
    }
    return undefined;
}
