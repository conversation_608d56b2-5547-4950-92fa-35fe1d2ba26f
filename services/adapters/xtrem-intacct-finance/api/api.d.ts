declare module '@sage/xtrem-intacct-finance-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package, SysMessageHistory } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type {
        AccountsPayableInvoice,
        AccountsPayableInvoiceLine,
        AccountsPayableInvoiceLineBinding,
        AccountsPayableInvoiceLineDimension,
        AccountsPayableInvoiceLineDimensionBinding,
        AccountsPayableInvoiceLineDimensionInput,
        AccountsPayableInvoiceLineInput,
        AccountsPayableInvoiceLineTax,
        AccountsPayableInvoiceLineTaxBinding,
        AccountsPayableInvoiceLineTaxInput,
        AccountsPayableInvoiceTax,
        AccountsPayableInvoiceTaxBinding,
        AccountsPayableInvoiceTaxInput,
        AccountsPayableOpenItem,
        AccountsReceivableAdvance,
        AccountsReceivableAdvanceLine,
        AccountsReceivableAdvanceLineBinding,
        AccountsReceivableAdvanceLineInput,
        AccountsReceivableInvoice,
        AccountsReceivableInvoiceLine,
        AccountsReceivableInvoiceLineBinding,
        AccountsReceivableInvoiceLineDimension,
        AccountsReceivableInvoiceLineDimensionBinding,
        AccountsReceivableInvoiceLineDimensionInput,
        AccountsReceivableInvoiceLineInput,
        AccountsReceivableInvoiceLineTax,
        AccountsReceivableInvoiceLineTaxBinding,
        AccountsReceivableInvoiceLineTaxInput,
        AccountsReceivableInvoiceTax,
        AccountsReceivableInvoiceTaxBinding,
        AccountsReceivableInvoiceTaxInput,
        AccountsReceivableOpenItem,
        AccountsReceivablePayment,
        AccountsReceivablePaymentLine,
        AccountsReceivablePaymentLineBinding,
        AccountsReceivablePaymentLineInput,
        JournalEntry,
        JournalEntryLine,
        JournalEntryLineBinding,
        JournalEntryLineDimension,
        JournalEntryLineDimensionBinding,
        JournalEntryLineDimensionInput,
        JournalEntryLineInput,
        JournalEntryLineStaging,
        JournalEntryLineStagingBinding,
        JournalEntryLineStagingInput,
        Package as SageXtremFinance$Package,
    } from '@sage/xtrem-finance-api';
    import type {
        Account,
        AccountAttributeType,
        AccountAttributeTypeBinding,
        AccountAttributeTypeInput,
        AccountDimensionType,
        AccountDimensionTypeBinding,
        AccountDimensionTypeInput,
        AccountsPayableInvoiceLineStaging,
        AccountsPayableInvoiceLineStagingBinding,
        AccountsPayableInvoiceLineStagingInput,
        AccountsReceivableInvoiceLineStaging,
        AccountsReceivableInvoiceLineStagingBinding,
        AccountsReceivableInvoiceLineStagingInput,
        AnalyticalData,
        Attribute,
        AttributeType,
        BankAccount,
        BaseFinanceLineDimension,
        BaseFinanceLineDimensionBinding,
        BaseFinanceLineDimensionInput,
        CompanyAttributeType,
        CompanyAttributeTypeBinding,
        CompanyAttributeTypeInput,
        CompanyDefaultAttribute,
        CompanyDefaultAttributeBinding,
        CompanyDefaultAttributeInput,
        CompanyDefaultDimension,
        CompanyDefaultDimensionBinding,
        CompanyDefaultDimensionInput,
        CompanyDimensionType,
        CompanyDimensionTypeBinding,
        CompanyDimensionTypeInput,
        Dimension,
        DimensionType,
        FinanceTransaction,
        Journal,
        JournalEntryTypeLine,
        Package as SageXtremFinanceData$Package,
        PaymentTracking,
        PostingClass,
    } from '@sage/xtrem-finance-data-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type {
        Intacct,
        IntacctLine,
        IntacctLineBinding,
        IntacctLineInput,
        Package as SageXtremIntacct$Package,
    } from '@sage/xtrem-intacct-api';
    import type {
        LandedCostItem,
        LandedCostItemBinding,
        LandedCostItemInput,
        Package as SageXtremLandedCost$Package,
    } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        Address,
        BomRevisionSequence,
        BusinessEntity,
        BusinessEntityAddress,
        BusinessEntityAddressBinding,
        BusinessEntityAddressInput,
        BusinessEntityContact,
        BusinessEntityContactBinding,
        BusinessEntityContactInput,
        BusinessEntityInput,
        CompanyAddress,
        CompanyAddressBinding,
        CompanyAddressInput,
        CompanyContact,
        CompanyContactBinding,
        CompanyContactInput,
        Contact,
        Currency,
        Customer,
        CustomerBinding,
        CustomerInput,
        CustomerSupplierCategory,
        DeliveryDetail,
        DeliveryDetailBinding,
        DeliveryDetailInput,
        DeliveryMode,
        Incoterm,
        Item,
        ItemAllergen,
        ItemAllergenBinding,
        ItemAllergenInput,
        ItemCategory,
        ItemClassifications,
        ItemClassificationsBinding,
        ItemClassificationsInput,
        ItemCustomer,
        ItemCustomerBinding,
        ItemCustomerInput,
        ItemCustomerPrice,
        ItemCustomerPriceBinding,
        ItemCustomerPriceInput,
        ItemSite,
        ItemSiteBinding,
        ItemSiteInput,
        ItemSupplier,
        ItemSupplierBinding,
        ItemSupplierInput,
        ItemSupplierPrice,
        ItemSupplierPriceBinding,
        ItemSupplierPriceInput,
        Package as SageXtremMasterData$Package,
        PaymentTerm,
        SequenceNumber,
        StandardIndustrialClassification,
        Supplier,
        SupplierBinding,
        SupplierCertificate,
        SupplierCertificateBinding,
        SupplierCertificateInput,
        SupplierInput,
        UnitOfMeasure,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type { Package as SageXtremStockData$Package } from '@sage/xtrem-stock-data-api';
    import type {
        ChartOfAccount,
        Country,
        Legislation,
        Package as SageXtremStructure$Package,
    } from '@sage/xtrem-structure-api';
    import type {
        BaseMapping,
        Package as SageXtremSynchronization$Package,
        ThirdPartyApplication,
    } from '@sage/xtrem-synchronization-api';
    import type {
        Package as SageXtremSystem$Package,
        Site,
        SiteBinding,
        SiteInput,
        SysNoteAssociation,
        SysNoteAssociationInput,
        SysVendor,
        User,
    } from '@sage/xtrem-system-api';
    import type {
        BaseTax,
        ItemTaxGroup,
        Package as SageXtremTax$Package,
        Tax,
        TaxCategory,
        TaxInput,
        TaxValue,
        TaxValueBinding,
        TaxValueInput,
    } from '@sage/xtrem-tax-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface FeedLineMatchingStatus$Enum {
        lookingForMatches: 0;
        matched: 1;
        draftMatch: 2;
        unmatched: 3;
        multipleMatches: 4;
        matchFound: 5;
    }
    export type FeedLineMatchingStatus = keyof FeedLineMatchingStatus$Enum;
    export interface FeedRecordStatus$Enum {
        matched: 0;
        unmatched: 1;
        lookingForExistingJournalEntries: 2;
        journalEntryFound: 3;
        journalEntriesFound: 4;
        draftMatch: 5;
        readyForPosting: 6;
        journalEntryPosted: 7;
        journalEntryGenerationInProgress: 8;
        journalEntryGenerationError: 9;
        journalEntryGenerated: 10;
        journalEntryPostingInProgress: 11;
        journalEntryPostingError: 12;
        draftArMatch: 13;
        readyForArPaymentGeneration: 14;
        arPaymentGenerationInProgress: 15;
        arPaymentPostingInProgress: 16;
        arPaymentPosted: 17;
        readyForArAdvanceGeneration: 18;
        arAdvancePosted: 19;
        partialArMatch: 20;
        draftArAdvance: 21;
        arPaymentGenerated: 22;
        arAdvanceGenerated: 23;
        arPaymentPostingError: 24;
        arAdvancePostingInProgress: 25;
        arAdvancePostingError: 26;
    }
    export type FeedRecordStatus = keyof FeedRecordStatus$Enum;
    export interface IntacctMatchingStatus$Enum {
        unmatched: 0;
        cleared: 1;
        matched: 2;
        partiallyMatched: 3;
        selectToMatch: 4;
        selectToUnmatch: 5;
        ignored: 6;
        draftMatched: 7;
    }
    export type IntacctMatchingStatus = keyof IntacctMatchingStatus$Enum;
    export interface IntacctMatchingType$Enum {
        equals: 0;
        contains: 1;
        regularExpression: 2;
    }
    export type IntacctMatchingType = keyof IntacctMatchingType$Enum;
    export interface IntacctRecordTransactionType$Enum {
        deposit: 0;
        withdrawal: 1;
    }
    export type IntacctRecordTransactionType = keyof IntacctRecordTransactionType$Enum;
    export interface FinanceListener extends ClientNode {}
    export interface FinanceListenerInput extends ClientNodeInput {}
    export interface FinanceListenerBinding extends ClientNode {}
    export interface FinanceListener$Mutations {
        retryFinanceDocument: Node$Operation<
            {
                financeTransaction?: string;
                financeDocumentSysId: integer | string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
    }
    export interface FinanceListener$Operations {
        mutations: FinanceListener$Mutations;
        getDefaults: GetDefaultsOperation<FinanceListener>;
    }
    export interface IntacctAccountsPayableInvoiceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: AccountsPayableInvoiceLine;
        inclusiveTax: boolean;
        departmentId: string;
        projectId: string;
        employeeId: string;
        taskId: string;
        customerId: string;
        vendorId: string;
        itemId: string;
        classId: string;
        signedAmountExcludingTax: string;
        memo: string;
        taxEntries: string;
    }
    export interface IntacctAccountsPayableInvoiceLineInput extends VitalClientNodeInput {}
    export interface IntacctAccountsPayableInvoiceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: AccountsPayableInvoiceLine;
        inclusiveTax: boolean;
        departmentId: string;
        projectId: string;
        employeeId: string;
        taskId: string;
        customerId: any;
        vendorId: any;
        itemId: any;
        classId: string;
        signedAmountExcludingTax: string;
        memo: string;
        taxEntries: any;
    }
    export interface IntacctAccountsPayableInvoiceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntacctAccountsPayableInvoiceLine$Operations {
        query: QueryOperation<IntacctAccountsPayableInvoiceLine>;
        read: ReadOperation<IntacctAccountsPayableInvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<IntacctAccountsPayableInvoiceLine>;
            query: AggregateQueryOperation<IntacctAccountsPayableInvoiceLine>;
        };
        create: CreateOperation<IntacctAccountsPayableInvoiceLineInput, IntacctAccountsPayableInvoiceLine>;
        getDuplicate: GetDuplicateOperation<IntacctAccountsPayableInvoiceLine>;
        update: UpdateOperation<IntacctAccountsPayableInvoiceLineInput, IntacctAccountsPayableInvoiceLine>;
        updateById: UpdateByIdOperation<IntacctAccountsPayableInvoiceLineInput, IntacctAccountsPayableInvoiceLine>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctAccountsPayableInvoiceLine$AsyncOperations;
        getDefaults: GetDefaultsOperation<IntacctAccountsPayableInvoiceLine>;
    }
    export interface IntacctAccountsReceivableAdvanceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: AccountsReceivableAdvanceLine;
        projectId: string;
        customerId: string;
        vendorId: string;
        itemId: string;
        departmentId: string;
        classId: string;
    }
    export interface IntacctAccountsReceivableAdvanceLineInput extends VitalClientNodeInput {}
    export interface IntacctAccountsReceivableAdvanceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: AccountsReceivableAdvanceLine;
        projectId: string;
        customerId: string;
        vendorId: string;
        itemId: string;
        departmentId: string;
        classId: string;
    }
    export interface IntacctAccountsReceivableAdvanceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntacctAccountsReceivableAdvanceLine$Operations {
        query: QueryOperation<IntacctAccountsReceivableAdvanceLine>;
        read: ReadOperation<IntacctAccountsReceivableAdvanceLine>;
        aggregate: {
            read: AggregateReadOperation<IntacctAccountsReceivableAdvanceLine>;
            query: AggregateQueryOperation<IntacctAccountsReceivableAdvanceLine>;
        };
        create: CreateOperation<IntacctAccountsReceivableAdvanceLineInput, IntacctAccountsReceivableAdvanceLine>;
        getDuplicate: GetDuplicateOperation<IntacctAccountsReceivableAdvanceLine>;
        update: UpdateOperation<IntacctAccountsReceivableAdvanceLineInput, IntacctAccountsReceivableAdvanceLine>;
        updateById: UpdateByIdOperation<
            IntacctAccountsReceivableAdvanceLineInput,
            IntacctAccountsReceivableAdvanceLine
        >;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctAccountsReceivableAdvanceLine$AsyncOperations;
        getDefaults: GetDefaultsOperation<IntacctAccountsReceivableAdvanceLine>;
    }
    export interface IntacctAccountsReceivableInvoiceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: AccountsReceivableInvoiceLine;
        inclusiveTax: boolean;
        departmentId: string;
        projectId: string;
        employeeId: string;
        taskId: string;
        customerId: string;
        vendorId: string;
        itemId: string;
        classId: string;
        signedAmountExcludingTax: string;
        memo: string;
        taxEntries: string;
    }
    export interface IntacctAccountsReceivableInvoiceLineInput extends VitalClientNodeInput {}
    export interface IntacctAccountsReceivableInvoiceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: AccountsReceivableInvoiceLine;
        inclusiveTax: boolean;
        departmentId: string;
        projectId: string;
        employeeId: string;
        taskId: string;
        customerId: any;
        vendorId: any;
        itemId: any;
        classId: string;
        signedAmountExcludingTax: string;
        memo: string;
        taxEntries: any;
    }
    export interface IntacctAccountsReceivableInvoiceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntacctAccountsReceivableInvoiceLine$Operations {
        query: QueryOperation<IntacctAccountsReceivableInvoiceLine>;
        read: ReadOperation<IntacctAccountsReceivableInvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<IntacctAccountsReceivableInvoiceLine>;
            query: AggregateQueryOperation<IntacctAccountsReceivableInvoiceLine>;
        };
        create: CreateOperation<IntacctAccountsReceivableInvoiceLineInput, IntacctAccountsReceivableInvoiceLine>;
        getDuplicate: GetDuplicateOperation<IntacctAccountsReceivableInvoiceLine>;
        update: UpdateOperation<IntacctAccountsReceivableInvoiceLineInput, IntacctAccountsReceivableInvoiceLine>;
        updateById: UpdateByIdOperation<
            IntacctAccountsReceivableInvoiceLineInput,
            IntacctAccountsReceivableInvoiceLine
        >;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctAccountsReceivableInvoiceLine$AsyncOperations;
        getDefaults: GetDefaultsOperation<IntacctAccountsReceivableInvoiceLine>;
    }
    export interface IntacctAccountsReceivablePaymentLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: AccountsReceivablePaymentLine;
        arInvoiceRecordNo: integer;
    }
    export interface IntacctAccountsReceivablePaymentLineInput extends VitalClientNodeInput {
        arInvoiceRecordNo?: integer | string;
    }
    export interface IntacctAccountsReceivablePaymentLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: AccountsReceivablePaymentLine;
        arInvoiceRecordNo: integer;
    }
    export interface IntacctAccountsReceivablePaymentLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntacctAccountsReceivablePaymentLine$Operations {
        query: QueryOperation<IntacctAccountsReceivablePaymentLine>;
        read: ReadOperation<IntacctAccountsReceivablePaymentLine>;
        aggregate: {
            read: AggregateReadOperation<IntacctAccountsReceivablePaymentLine>;
            query: AggregateQueryOperation<IntacctAccountsReceivablePaymentLine>;
        };
        create: CreateOperation<IntacctAccountsReceivablePaymentLineInput, IntacctAccountsReceivablePaymentLine>;
        getDuplicate: GetDuplicateOperation<IntacctAccountsReceivablePaymentLine>;
        update: UpdateOperation<IntacctAccountsReceivablePaymentLineInput, IntacctAccountsReceivablePaymentLine>;
        updateById: UpdateByIdOperation<
            IntacctAccountsReceivablePaymentLineInput,
            IntacctAccountsReceivablePaymentLine
        >;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctAccountsReceivablePaymentLine$AsyncOperations;
        getDefaults: GetDefaultsOperation<IntacctAccountsReceivablePaymentLine>;
    }
    export interface IntacctBankAccountMatching extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        bankAccount: BankAccount;
        priority: integer;
        keyword: string;
        account: Account;
        transactionType: IntacctRecordTransactionType;
        tax: Tax;
        location: Site;
        storedAttributes: string;
        storedDimensions: string;
        analyticalData: AnalyticalData;
        type: IntacctMatchingType;
    }
    export interface IntacctBankAccountMatchingInput extends ClientNodeInput {
        bankAccount?: integer | string;
        priority?: integer | string;
        keyword?: string;
        account?: integer | string;
        transactionType?: IntacctRecordTransactionType;
        tax?: integer | string;
        location?: integer | string;
        storedAttributes?: string;
        storedDimensions?: string;
        analyticalData?: integer | string;
        type?: IntacctMatchingType;
        updateAccountTaxManagement?: boolean | string;
    }
    export interface IntacctBankAccountMatchingBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        bankAccount: BankAccount;
        priority: integer;
        keyword: string;
        account: Account;
        transactionType: IntacctRecordTransactionType;
        tax: Tax;
        location: Site;
        storedAttributes: any;
        storedDimensions: any;
        analyticalData: AnalyticalData;
        type: IntacctMatchingType;
        updateAccountTaxManagement: boolean;
    }
    export interface IntacctBankAccountMatching$Queries {
        queryIntacctDocument: Node$Operation<
            {
                parameters?: {
                    date?: string | null;
                    description?: string | null;
                    account?: string | null;
                    amount?: (decimal | string) | null;
                };
                documentType: string;
            },
            {
                recordNo: string | null;
                description: string | null;
                documentType: string | null;
                location: string | null;
                journal: string | null;
                date: string | null;
                amount: string | null;
                account: string | null;
                url: string | null;
                batchNo: string | null;
                entityId: string | null;
            }[]
        >;
        queryIntacctArInvoice: Node$Operation<
            {
                parameters?: {
                    customerId?: string | null;
                    currencyId?: string | null;
                    orderField?: {
                        name?: string | null;
                        isAscending?: (boolean | string) | null;
                    };
                    documentNos?: string[];
                    megaEntityId?: string | null;
                };
            },
            {
                recordNo: integer | null;
                invoiceNo: string | null;
                customerName: string | null;
                customerId: string | null;
                referenceNumber: string | null;
                date: string | null;
                dueDate: string | null;
                totalAmount: string | null;
                totalDue: string | null;
                totalPaid: string | null;
                term: PaymentTerm | null;
                entityId: string | null;
            }[]
        >;
        queryIntacctCustomers: Node$Operation<
            {},
            {
                customerName: string | null;
                customerId: string | null;
            }[]
        >;
    }
    export interface IntacctBankAccountMatching$Mutations {
        bulkSave: Node$Operation<
            {
                intacctBankAccountMatchings?: IntacctBankAccountMatchingInput[];
            },
            boolean
        >;
    }
    export interface IntacctBankAccountMatching$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntacctBankAccountMatching$Lookups {
        bankAccount: QueryOperation<BankAccount>;
        account: QueryOperation<Account>;
        tax: QueryOperation<Tax>;
        location: QueryOperation<Site>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface IntacctBankAccountMatching$Operations {
        query: QueryOperation<IntacctBankAccountMatching>;
        read: ReadOperation<IntacctBankAccountMatching>;
        aggregate: {
            read: AggregateReadOperation<IntacctBankAccountMatching>;
            query: AggregateQueryOperation<IntacctBankAccountMatching>;
        };
        queries: IntacctBankAccountMatching$Queries;
        create: CreateOperation<IntacctBankAccountMatchingInput, IntacctBankAccountMatching>;
        getDuplicate: GetDuplicateOperation<IntacctBankAccountMatching>;
        update: UpdateOperation<IntacctBankAccountMatchingInput, IntacctBankAccountMatching>;
        updateById: UpdateByIdOperation<IntacctBankAccountMatchingInput, IntacctBankAccountMatching>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: IntacctBankAccountMatching$Mutations;
        asyncOperations: IntacctBankAccountMatching$AsyncOperations;
        lookups(dataOrId: string | { data: IntacctBankAccountMatchingInput }): IntacctBankAccountMatching$Lookups;
        getDefaults: GetDefaultsOperation<IntacctBankAccountMatching>;
    }
    export interface IntacctImportSession extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        description: string;
        mapObject: IntacctMap;
        dateCreation: string;
        queryParameters: string;
        numberOfLinesToImport: integer;
        linesImported: integer;
        status: ImportStatus;
        bankAccount: BankAccount;
        transactionFeed: ClientCollection<IntacctBankAccountTransactionFeed>;
    }
    export interface IntacctImportSessionInput extends ClientNodeInput {
        description?: string;
        mapObject?: integer | string;
        dateCreation?: string;
        queryParameters?: string;
        numberOfLinesToImport?: integer | string;
        status?: ImportStatus;
        bankAccount?: integer | string;
        transactionFeed?: Partial<IntacctBankAccountTransactionFeedInput>[];
    }
    export interface IntacctImportSessionBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        description: string;
        mapObject: IntacctMap;
        dateCreation: string;
        queryParameters: any;
        numberOfLinesToImport: integer;
        linesImported: integer;
        status: ImportStatus;
        bankAccount: BankAccount;
        transactionFeed: ClientCollection<IntacctBankAccountTransactionFeedBinding>;
    }
    export interface IntacctImportSession$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntacctImportSession$Lookups {
        mapObject: QueryOperation<IntacctMap>;
        bankAccount: QueryOperation<BankAccount>;
    }
    export interface IntacctImportSession$Operations {
        query: QueryOperation<IntacctImportSession>;
        read: ReadOperation<IntacctImportSession>;
        aggregate: {
            read: AggregateReadOperation<IntacctImportSession>;
            query: AggregateQueryOperation<IntacctImportSession>;
        };
        create: CreateOperation<IntacctImportSessionInput, IntacctImportSession>;
        getDuplicate: GetDuplicateOperation<IntacctImportSession>;
        update: UpdateOperation<IntacctImportSessionInput, IntacctImportSession>;
        updateById: UpdateByIdOperation<IntacctImportSessionInput, IntacctImportSession>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctImportSession$AsyncOperations;
        lookups(dataOrId: string | { data: IntacctImportSessionInput }): IntacctImportSession$Lookups;
        getDefaults: GetDefaultsOperation<IntacctImportSession>;
    }
    export interface IntacctJournalEntryLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: JournalEntryLine;
        absoluteTransactionAmount: string;
        sign: integer;
        locationWhenNoSplit: string;
        allocationSplit: string;
        dimensionSplit: string;
        excludeRecord: boolean;
        taxEntries: string;
    }
    export interface IntacctJournalEntryLineInput extends VitalClientNodeInput {}
    export interface IntacctJournalEntryLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentLine: JournalEntryLine;
        absoluteTransactionAmount: string;
        sign: integer;
        locationWhenNoSplit: string;
        allocationSplit: string;
        dimensionSplit: any;
        excludeRecord: boolean;
        taxEntries: any;
    }
    export interface IntacctJournalEntryLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntacctJournalEntryLine$Operations {
        query: QueryOperation<IntacctJournalEntryLine>;
        read: ReadOperation<IntacctJournalEntryLine>;
        aggregate: {
            read: AggregateReadOperation<IntacctJournalEntryLine>;
            query: AggregateQueryOperation<IntacctJournalEntryLine>;
        };
        create: CreateOperation<IntacctJournalEntryLineInput, IntacctJournalEntryLine>;
        getDuplicate: GetDuplicateOperation<IntacctJournalEntryLine>;
        update: UpdateOperation<IntacctJournalEntryLineInput, IntacctJournalEntryLine>;
        updateById: UpdateByIdOperation<IntacctJournalEntryLineInput, IntacctJournalEntryLine>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctJournalEntryLine$AsyncOperations;
        getDefaults: GetDefaultsOperation<IntacctJournalEntryLine>;
    }
    export interface IntacctListener extends ClientNode {}
    export interface IntacctListenerInput extends ClientNodeInput {}
    export interface IntacctListenerBinding extends ClientNode {}
    export interface IntacctListener$AsyncOperations {
        deleteIntacct: AsyncOperation<
            {
                intacctNode?: string;
                recordNo?: string;
            },
            string
        >;
        synchronizeNode: AsyncOperation<
            {
                intacctNode?: string;
            },
            {
                _id: string;
                sysId: string;
                state: string;
                node: {
                    title: string;
                };
            }
        >;
    }
    export interface IntacctListener$Operations {
        asyncOperations: IntacctListener$AsyncOperations;
    }
    export interface MapLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        collectionName: string;
        mapHeader: IntacctMap;
        line: IntacctMap;
        propertyHeader: string;
        propertyLine: string;
    }
    export interface MapLineInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        collectionName?: string;
        line?: integer | string;
        propertyHeader?: string;
        propertyLine?: string;
    }
    export interface MapLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        collectionName: string;
        mapHeader: IntacctMap;
        line: IntacctMap;
        propertyHeader: string;
        propertyLine: string;
    }
    export interface MapLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MapLine$Lookups {
        _vendor: QueryOperation<SysVendor>;
        line: QueryOperation<IntacctMap>;
    }
    export interface MapLine$Operations {
        query: QueryOperation<MapLine>;
        read: ReadOperation<MapLine>;
        aggregate: {
            read: AggregateReadOperation<MapLine>;
            query: AggregateQueryOperation<MapLine>;
        };
        asyncOperations: MapLine$AsyncOperations;
        lookups(dataOrId: string | { data: MapLineInput }): MapLine$Lookups;
        getDefaults: GetDefaultsOperation<MapLine>;
    }
    export interface MapProperty extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        map: IntacctMap;
        name: string;
        type: PropertyType;
    }
    export interface MapPropertyInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        name?: string;
        type?: PropertyType;
    }
    export interface MapPropertyBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        map: IntacctMap;
        name: string;
        type: PropertyType;
    }
    export interface MapProperty$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MapProperty$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface MapProperty$Operations {
        query: QueryOperation<MapProperty>;
        read: ReadOperation<MapProperty>;
        aggregate: {
            read: AggregateReadOperation<MapProperty>;
            query: AggregateQueryOperation<MapProperty>;
        };
        asyncOperations: MapProperty$AsyncOperations;
        lookups(dataOrId: string | { data: MapPropertyInput }): MapProperty$Lookups;
        getDefaults: GetDefaultsOperation<MapProperty>;
    }
    export interface IntacctCashBookManagement extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
    }
    export interface IntacctCashBookManagementInput extends ClientNodeInput {}
    export interface IntacctCashBookManagementBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
    }
    export interface IntacctCashBookManagement$Queries {
        isServiceOptionActiveFunction: Node$Operation<{}, boolean>;
    }
    export interface IntacctCashBookManagement$Mutations {
        serviceOptionChange: Node$Operation<{}, boolean>;
    }
    export interface IntacctCashBookManagement$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntacctCashBookManagement$Operations {
        queries: IntacctCashBookManagement$Queries;
        update: UpdateOperation<IntacctCashBookManagementInput, IntacctCashBookManagement>;
        updateById: UpdateByIdOperation<IntacctCashBookManagementInput, IntacctCashBookManagement>;
        mutations: IntacctCashBookManagement$Mutations;
        asyncOperations: IntacctCashBookManagement$AsyncOperations;
        getDefaults: GetDefaultsOperation<IntacctCashBookManagement>;
    }
    export interface IntacctBankAccountTransactionFeed extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        lines: ClientCollection<IntacctBankAccountTransactionFeedLine>;
        importSession: IntacctImportSession;
        bankAccount: BankAccount;
        intacctId: string;
        entity: string;
        entityName: string;
        accountType: string;
        accountFeedKey: integer;
        transactionId: integer;
        accountReconKey: integer;
        postingDate: string;
        reconcilitationDate: string;
        transactionType: IntacctRecordTransactionType;
        documentType: string;
        documentNumber: string;
        payee: string;
        amount: string;
        amountToMatch: string;
        description: string;
        cleared: IntacctMatchingStatus;
        targetDocumentType: TargetDocumentType;
        currency: Currency;
        feedType: string;
        account: Account;
        storedAttributes: string;
        computedAttributes: string;
        storedDimensions: string;
        analyticalData: AnalyticalData;
        arMatch: string;
        jsonArInvoices: string;
        journalEntryNumber: string;
        financeIntegrationApp: FinanceIntegrationApp;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppUrl: string;
        financeIntegrationAppRecordId: string;
        financeDocumentType: FinanceDocumentType;
        financeDocumentCreatedNumber: string;
        financeDocumentCreatedSysId: integer;
        financeDocumentGenerationErrorMessage: string;
        paymentDate: string;
        receiptDate: string;
        payToCustomerId: string;
        payToCustomerName: string;
        paymentMethod: string;
    }
    export interface IntacctBankAccountTransactionFeedInput extends VitalClientNodeInput {
        number?: string;
        lines?: Partial<IntacctBankAccountTransactionFeedLineInput>[];
        bankAccount?: integer | string;
        intacctId?: string;
        entity?: string;
        entityName?: string;
        accountType?: string;
        accountFeedKey?: integer | string;
        transactionId?: integer | string;
        accountReconKey?: integer | string;
        postingDate?: string;
        reconcilitationDate?: string;
        transactionType?: IntacctRecordTransactionType;
        documentType?: string;
        documentNumber?: string;
        payee?: string;
        amount?: decimal | string;
        amountToMatch?: decimal | string;
        description?: string;
        cleared?: IntacctMatchingStatus;
        currency?: integer | string;
        feedType?: string;
        account?: integer | string;
        storedAttributes?: string;
        storedDimensions?: string;
        analyticalData?: integer | string;
        arMatch?: string;
        jsonArInvoices?: string;
        journalEntryNumber?: string;
        paymentDate?: string;
        receiptDate?: string;
        payToCustomerId?: string;
        payToCustomerName?: string;
        paymentMethod?: string;
    }
    export interface IntacctBankAccountTransactionFeedBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        lines: ClientCollection<IntacctBankAccountTransactionFeedLineBinding>;
        importSession: IntacctImportSession;
        bankAccount: BankAccount;
        intacctId: string;
        entity: string;
        entityName: string;
        accountType: string;
        accountFeedKey: integer;
        transactionId: integer;
        accountReconKey: integer;
        postingDate: string;
        reconcilitationDate: string;
        transactionType: IntacctRecordTransactionType;
        documentType: string;
        documentNumber: string;
        payee: string;
        amount: string;
        amountToMatch: string;
        description: string;
        cleared: IntacctMatchingStatus;
        targetDocumentType: TargetDocumentType;
        currency: Currency;
        feedType: string;
        account: Account;
        storedAttributes: any;
        computedAttributes: any;
        storedDimensions: any;
        analyticalData: AnalyticalData;
        arMatch: any;
        jsonArInvoices: any;
        journalEntryNumber: string;
        financeIntegrationApp: FinanceIntegrationApp;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppUrl: string;
        financeIntegrationAppRecordId: string;
        financeDocumentType: FinanceDocumentType;
        financeDocumentCreatedNumber: string;
        financeDocumentCreatedSysId: integer;
        financeDocumentGenerationErrorMessage: string;
        paymentDate: string;
        receiptDate: string;
        payToCustomerId: string;
        payToCustomerName: string;
        paymentMethod: string;
    }
    export interface IntacctBankAccountTransactionFeed$Queries {
        getMatchLine: Node$Operation<
            {
                transactionFeed?: {
                    description?: string | null;
                };
                matchingRules?: IntacctBankAccountMatchingInput[];
            },
            {
                isMatched: FeedLineMatchingStatus;
                account: Account;
                tax: Tax;
                location: Site;
            }
        >;
    }
    export interface IntacctBankAccountTransactionFeed$Mutations {
        setCleared: Node$Operation<
            {
                recordNo: integer | string;
                cleared?: IntacctMatchingStatus;
            },
            boolean
        >;
    }
    export interface IntacctBankAccountTransactionFeed$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntacctBankAccountTransactionFeed$Lookups {
        bankAccount: QueryOperation<BankAccount>;
        currency: QueryOperation<Currency>;
        account: QueryOperation<Account>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface IntacctBankAccountTransactionFeed$Operations {
        query: QueryOperation<IntacctBankAccountTransactionFeed>;
        read: ReadOperation<IntacctBankAccountTransactionFeed>;
        aggregate: {
            read: AggregateReadOperation<IntacctBankAccountTransactionFeed>;
            query: AggregateQueryOperation<IntacctBankAccountTransactionFeed>;
        };
        queries: IntacctBankAccountTransactionFeed$Queries;
        create: CreateOperation<IntacctBankAccountTransactionFeedInput, IntacctBankAccountTransactionFeed>;
        getDuplicate: GetDuplicateOperation<IntacctBankAccountTransactionFeed>;
        update: UpdateOperation<IntacctBankAccountTransactionFeedInput, IntacctBankAccountTransactionFeed>;
        updateById: UpdateByIdOperation<IntacctBankAccountTransactionFeedInput, IntacctBankAccountTransactionFeed>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: IntacctBankAccountTransactionFeed$Mutations;
        asyncOperations: IntacctBankAccountTransactionFeed$AsyncOperations;
        lookups(
            dataOrId: string | { data: IntacctBankAccountTransactionFeedInput },
        ): IntacctBankAccountTransactionFeed$Lookups;
        getDefaults: GetDefaultsOperation<IntacctBankAccountTransactionFeed>;
    }
    export interface IntacctBankAccountTransactionFeedLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: IntacctBankAccountTransactionFeed;
        attributesAndDimensions: ClientCollection<BaseFinanceLineDimension>;
        status: FeedLineMatchingStatus;
        intacctJournalEntryRecordNo: string;
        intacctJournalEntryUrl: string;
        amount: string;
        account: Account;
        tax: Tax;
        taxAmount: string;
        taxRate: string;
        location: Site;
        storedAttributes: string;
        computedAttributes: string;
        storedDimensions: string;
        analyticalData: AnalyticalData;
        intacctJournalEntryBatchNo: string;
    }
    export interface IntacctBankAccountTransactionFeedLineInput extends VitalClientNodeInput {
        attributesAndDimensions?: Partial<BaseFinanceLineDimensionInput>[];
        status?: FeedLineMatchingStatus;
        intacctJournalEntryRecordNo?: string;
        intacctJournalEntryUrl?: string;
        amount?: decimal | string;
        account?: integer | string;
        tax?: integer | string;
        taxAmount?: decimal | string;
        taxRate?: decimal | string;
        location?: integer | string;
        storedAttributes?: string;
        storedDimensions?: string;
        analyticalData?: integer | string;
        intacctJournalEntryBatchNo?: string;
    }
    export interface IntacctBankAccountTransactionFeedLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: IntacctBankAccountTransactionFeed;
        attributesAndDimensions: ClientCollection<BaseFinanceLineDimensionBinding>;
        status: FeedLineMatchingStatus;
        intacctJournalEntryRecordNo: string;
        intacctJournalEntryUrl: string;
        amount: string;
        account: Account;
        tax: Tax;
        taxAmount: string;
        taxRate: string;
        location: Site;
        storedAttributes: any;
        computedAttributes: any;
        storedDimensions: any;
        analyticalData: AnalyticalData;
        intacctJournalEntryBatchNo: string;
    }
    export interface IntacctBankAccountTransactionFeedLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntacctBankAccountTransactionFeedLine$Lookups {
        account: QueryOperation<Account>;
        tax: QueryOperation<Tax>;
        location: QueryOperation<Site>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface IntacctBankAccountTransactionFeedLine$Operations {
        query: QueryOperation<IntacctBankAccountTransactionFeedLine>;
        read: ReadOperation<IntacctBankAccountTransactionFeedLine>;
        aggregate: {
            read: AggregateReadOperation<IntacctBankAccountTransactionFeedLine>;
            query: AggregateQueryOperation<IntacctBankAccountTransactionFeedLine>;
        };
        asyncOperations: IntacctBankAccountTransactionFeedLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: IntacctBankAccountTransactionFeedLineInput },
        ): IntacctBankAccountTransactionFeedLine$Lookups;
        getDefaults: GetDefaultsOperation<IntacctBankAccountTransactionFeedLine>;
    }
    export interface IntacctBankAccountTransactionFeedLineDimension extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        originLine: IntacctBankAccountTransactionFeedLine;
        storedAttributes: string;
        storedDimensions: string;
        analyticalData: AnalyticalData;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        financialSite: Site;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: Customer;
        supplier: Supplier;
        project: Attribute;
        task: Attribute;
        employee: Attribute;
        item: Item;
    }
    export interface IntacctBankAccountTransactionFeedLineDimensionInput extends VitalClientNodeInput {
        storedAttributes?: string;
        storedDimensions?: string;
        analyticalData?: integer | string;
    }
    export interface IntacctBankAccountTransactionFeedLineDimensionBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        originLine: IntacctBankAccountTransactionFeedLine;
        storedAttributes: any;
        storedDimensions: any;
        analyticalData: AnalyticalData;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        financialSite: Site;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: Customer;
        supplier: Supplier;
        project: Attribute;
        task: Attribute;
        employee: Attribute;
        item: Item;
    }
    export interface IntacctBankAccountTransactionFeedLineDimension$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntacctBankAccountTransactionFeedLineDimension$Lookups {
        analyticalData: QueryOperation<AnalyticalData>;
        dimension01: QueryOperation<Dimension>;
        dimension02: QueryOperation<Dimension>;
        dimension03: QueryOperation<Dimension>;
        dimension04: QueryOperation<Dimension>;
        dimension05: QueryOperation<Dimension>;
        dimension06: QueryOperation<Dimension>;
        dimension07: QueryOperation<Dimension>;
        dimension08: QueryOperation<Dimension>;
        dimension09: QueryOperation<Dimension>;
        dimension10: QueryOperation<Dimension>;
        dimension11: QueryOperation<Dimension>;
        dimension12: QueryOperation<Dimension>;
        dimension13: QueryOperation<Dimension>;
        dimension14: QueryOperation<Dimension>;
        dimension15: QueryOperation<Dimension>;
        dimension16: QueryOperation<Dimension>;
        dimension17: QueryOperation<Dimension>;
        dimension18: QueryOperation<Dimension>;
        dimension19: QueryOperation<Dimension>;
        dimension20: QueryOperation<Dimension>;
        financialSite: QueryOperation<Site>;
        businessSite: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        manufacturingSite: QueryOperation<Site>;
        customer: QueryOperation<Customer>;
        supplier: QueryOperation<Supplier>;
        project: QueryOperation<Attribute>;
        task: QueryOperation<Attribute>;
        employee: QueryOperation<Attribute>;
        item: QueryOperation<Item>;
    }
    export interface IntacctBankAccountTransactionFeedLineDimension$Operations {
        query: QueryOperation<IntacctBankAccountTransactionFeedLineDimension>;
        read: ReadOperation<IntacctBankAccountTransactionFeedLineDimension>;
        aggregate: {
            read: AggregateReadOperation<IntacctBankAccountTransactionFeedLineDimension>;
            query: AggregateQueryOperation<IntacctBankAccountTransactionFeedLineDimension>;
        };
        asyncOperations: IntacctBankAccountTransactionFeedLineDimension$AsyncOperations;
        lookups(
            dataOrId: string | { data: IntacctBankAccountTransactionFeedLineDimensionInput },
        ): IntacctBankAccountTransactionFeedLineDimension$Lookups;
        getDefaults: GetDefaultsOperation<IntacctBankAccountTransactionFeedLineDimension>;
    }
    export interface IntacctMap extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        application: ThirdPartyApplication;
        nodeFactory: MetaNodeFactory;
        synchronizationDirection: SynchronizationDirection;
        thirdPartyObjectName: string;
        isActive: boolean;
        canDelete: boolean;
        canCreate: boolean;
        canUpdate: boolean;
        notificationId: string;
        mappedPropertyNames: string[];
        id: string;
        intacctDescription: string;
        isSmartEvent: boolean;
        isPrivateShow: boolean;
        editableFields: string[];
        additionnalLink: string;
        intacctFilter: string;
        specificFields: ClientCollection<MapProperty>;
        lines: ClientCollection<MapLine>;
        relationMapping: string;
    }
    export interface IntacctMapInput extends ClientNodeInput {
        _vendor?: integer | string;
        application?: integer | string;
        nodeFactory?: integer | string;
        synchronizationDirection?: SynchronizationDirection;
        thirdPartyObjectName?: string;
        isActive?: boolean | string;
        canDelete?: boolean | string;
        canCreate?: boolean | string;
        canUpdate?: boolean | string;
        notificationId?: string;
        id?: string;
        intacctDescription?: string;
        isSmartEvent?: boolean | string;
        isPrivateShow?: boolean | string;
        editableFields?: string[];
        specificFields?: Partial<MapPropertyInput>[];
        lines?: Partial<MapLineInput>[];
        relationMapping?: string;
    }
    export interface IntacctMapBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        application: ThirdPartyApplication;
        nodeFactory: MetaNodeFactory;
        synchronizationDirection: SynchronizationDirection;
        thirdPartyObjectName: string;
        isActive: boolean;
        canDelete: boolean;
        canCreate: boolean;
        canUpdate: boolean;
        notificationId: string;
        mappedPropertyNames: string[];
        id: string;
        intacctDescription: string;
        isSmartEvent: boolean;
        isPrivateShow: boolean;
        editableFields: string[];
        additionnalLink: any;
        intacctFilter: string;
        specificFields: ClientCollection<MapPropertyBinding>;
        lines: ClientCollection<MapLineBinding>;
        relationMapping: any;
    }
    export interface IntacctMap$Queries {
        getAvailableXtremObjectList: Node$Operation<{}, string[]>;
        getObject: Node$Operation<
            {
                object?: string;
                docparid?: string;
                isUpdate?: boolean | string;
            },
            {
                name: string;
                documentType: string;
                xtremObject: string;
                fields: {
                    DATATYPE: string;
                    DESCRIPTION: string;
                    ID: string;
                    ISCUSTOM: boolean;
                    CREATEONLY: boolean;
                    VALIDVALUES: {
                        VALIDVALUE: string[];
                    } | null;
                    LABEL: string;
                    READONLY: boolean;
                    REQUIRED: boolean;
                    xtremProperty: string;
                    isEditable: boolean;
                    xtremDefaultProperty: string;
                }[];
                relationshipFields: {
                    DATATYPE: string;
                    DESCRIPTION: string;
                    ID: string;
                    ISCUSTOM: boolean;
                    CREATEONLY: boolean;
                    VALIDVALUES: {
                        VALIDVALUE: string[];
                    } | null;
                    LABEL: string;
                    READONLY: boolean;
                    REQUIRED: boolean;
                    xtremProperty: string;
                    isEditable: boolean;
                    xtremDefaultProperty: string;
                }[];
                relationships: {
                    LABEL: string;
                    OBJECTNAME: string;
                    OBJECTPATH: string;
                    RELATEDBY: string;
                    RELATIONSHIPTYPE: string;
                    xtremProperty: string;
                }[];
            }
        >;
        getIntacctTransactionsList: Node$Operation<
            {},
            {
                name: string;
                object: string;
            }[]
        >;
        getDataIntacct: Node$Operation<
            {
                name?: string;
                transaction?: string;
                maxData?: integer | string;
                filters?: {
                    where?: string;
                    whereValue?: string;
                }[];
            },
            {
                _id: string;
                id: string;
                name: string;
                description: string;
                whenModified: string;
                whenCreated: string;
                megaEntityId: string;
                isLinked: boolean;
                xtremSysId: string;
                url: string;
                xtremID: string;
                xtremName: string;
                xtremDescription: string;
                integrationStatus: string;
            }[]
        >;
    }
    export interface IntacctMap$Mutations {
        writeStructure: Node$Operation<
            {
                object: string;
            },
            boolean
        >;
        getStructure: Node$Operation<
            {
                content?: string;
            },
            string
        >;
        deleteXtrem: Node$Operation<
            {
                intacctName: string;
                intacctIdValue?: string;
                xtremSysId?: string;
            },
            {
                message: string;
                path: string;
                severity: string;
            }[]
        >;
        createUpdateAllIntacct: Node$Operation<
            {
                intacctName: string;
            },
            string
        >;
        getIntacctUrl: Node$Operation<
            {
                _id: integer | string;
                nodeName: string;
            },
            string
        >;
    }
    export interface IntacctMap$AsyncOperations {
        updateCustomMapping: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        xtreemMassCreationJob: AsyncOperation<
            {
                data?: {
                    intacctName?: string;
                    intacctIdValue?: string;
                    xtremSysId?: string;
                    isThrowingDiagnose?: boolean | string;
                };
            },
            {
                created: integer | null;
                updated: integer | null;
                error: integer | null;
                message: string | null;
            }
        >;
        intacctMassCreationJob: AsyncOperation<
            {
                intacctName?: string;
                type?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntacctMap$Lookups {
        application: QueryOperation<ThirdPartyApplication>;
        nodeFactory: QueryOperation<MetaNodeFactory>;
    }
    export interface IntacctMap$Operations {
        query: QueryOperation<IntacctMap>;
        read: ReadOperation<IntacctMap>;
        aggregate: {
            read: AggregateReadOperation<IntacctMap>;
            query: AggregateQueryOperation<IntacctMap>;
        };
        queries: IntacctMap$Queries;
        create: CreateOperation<IntacctMapInput, IntacctMap>;
        getDuplicate: GetDuplicateOperation<IntacctMap>;
        update: UpdateOperation<IntacctMapInput, IntacctMap>;
        updateById: UpdateByIdOperation<IntacctMapInput, IntacctMap>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: IntacctMap$Mutations;
        asyncOperations: IntacctMap$AsyncOperations;
        lookups(dataOrId: string | { data: IntacctMapInput }): IntacctMap$Lookups;
        getDefaults: GetDefaultsOperation<IntacctMap>;
    }
    export interface IntacctSynchronizationState extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: string;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: string;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
    }
    export interface IntacctSynchronizationStateInput extends ClientNodeInput {
        _constructor?: string;
        integration?: integer | string;
        node?: integer | string;
        sysId?: string;
        naturalKey?: string;
        state?: IntegrationState;
        url?: string;
        secondaryPageLink?: string;
        lastMessage?: string;
        notificationTrackingIds?: string[];
        creationStamp?: string;
        updateStamp?: string;
        version?: integer | string;
        difference?: string;
        intacctId?: string;
        recordNo?: integer | string;
    }
    export interface IntacctSynchronizationStateBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: any;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: any;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
    }
    export interface IntacctSynchronizationState$AsyncOperations {
        synchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        forceSynchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        resetThirdPartyId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        updateSysId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface IntacctSynchronizationState$Lookups {
        integration: QueryOperation<ThirdPartyApplication>;
        mapping: QueryOperation<BaseMapping>;
        node: QueryOperation<MetaNodeFactory>;
        intacctConfiguration: QueryOperation<Intacct>;
    }
    export interface IntacctSynchronizationState$Operations {
        create: CreateOperation<IntacctSynchronizationStateInput, IntacctSynchronizationState>;
        getDuplicate: GetDuplicateOperation<IntacctSynchronizationState>;
        duplicate: DuplicateOperation<string, IntacctSynchronizationStateInput, IntacctSynchronizationState>;
        asyncOperations: IntacctSynchronizationState$AsyncOperations;
        lookups(dataOrId: string | { data: IntacctSynchronizationStateInput }): IntacctSynchronizationState$Lookups;
        getDefaults: GetDefaultsOperation<IntacctSynchronizationState>;
    }
    export interface IntacctContact extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: string;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: string;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        contact: BusinessEntityAddress;
        name: string;
        printAs: string;
        prefix: string;
        status: string;
    }
    export interface IntacctContactInput extends VitalClientNodeInput {
        integration?: integer | string;
        node?: integer | string;
        sysId?: string;
        naturalKey?: string;
        state?: IntegrationState;
        url?: string;
        secondaryPageLink?: string;
        lastMessage?: string;
        notificationTrackingIds?: string[];
        creationStamp?: string;
        updateStamp?: string;
        version?: integer | string;
        difference?: string;
        intacctId?: string;
        recordNo?: integer | string;
    }
    export interface IntacctContactBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: any;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: any;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        contact: BusinessEntityAddress;
        name: string;
        printAs: string;
        prefix: string;
        status: string;
    }
    export interface IntacctContact$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        synchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        forceSynchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        resetThirdPartyId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        updateSysId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface IntacctContact$Lookups {
        integration: QueryOperation<ThirdPartyApplication>;
        mapping: QueryOperation<BaseMapping>;
        node: QueryOperation<MetaNodeFactory>;
        intacctConfiguration: QueryOperation<Intacct>;
    }
    export interface IntacctContact$Operations {
        query: QueryOperation<IntacctContact>;
        read: ReadOperation<IntacctContact>;
        aggregate: {
            read: AggregateReadOperation<IntacctContact>;
            query: AggregateQueryOperation<IntacctContact>;
        };
        create: CreateOperation<IntacctContactInput, IntacctContact>;
        getDuplicate: GetDuplicateOperation<IntacctContact>;
        update: UpdateOperation<IntacctContactInput, IntacctContact>;
        updateById: UpdateByIdOperation<IntacctContactInput, IntacctContact>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctContact$AsyncOperations;
        lookups(dataOrId: string | { data: IntacctContactInput }): IntacctContact$Lookups;
        getDefaults: GetDefaultsOperation<IntacctContact>;
    }
    export interface IntacctItem extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: string;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: string;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        item: Item;
        status: string;
        type: string;
    }
    export interface IntacctItemInput extends VitalClientNodeInput {
        integration?: integer | string;
        node?: integer | string;
        sysId?: string;
        naturalKey?: string;
        state?: IntegrationState;
        url?: string;
        secondaryPageLink?: string;
        lastMessage?: string;
        notificationTrackingIds?: string[];
        creationStamp?: string;
        updateStamp?: string;
        version?: integer | string;
        difference?: string;
        intacctId?: string;
        recordNo?: integer | string;
        status?: string;
    }
    export interface IntacctItemBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: any;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: any;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        item: Item;
        status: string;
        type: string;
    }
    export interface IntacctItem$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        synchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        forceSynchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        resetThirdPartyId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        updateSysId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface IntacctItem$Lookups {
        integration: QueryOperation<ThirdPartyApplication>;
        mapping: QueryOperation<BaseMapping>;
        node: QueryOperation<MetaNodeFactory>;
        intacctConfiguration: QueryOperation<Intacct>;
    }
    export interface IntacctItem$Operations {
        query: QueryOperation<IntacctItem>;
        read: ReadOperation<IntacctItem>;
        aggregate: {
            read: AggregateReadOperation<IntacctItem>;
            query: AggregateQueryOperation<IntacctItem>;
        };
        create: CreateOperation<IntacctItemInput, IntacctItem>;
        getDuplicate: GetDuplicateOperation<IntacctItem>;
        duplicate: DuplicateOperation<string, IntacctItemInput, IntacctItem>;
        update: UpdateOperation<IntacctItemInput, IntacctItem>;
        updateById: UpdateByIdOperation<IntacctItemInput, IntacctItem>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctItem$AsyncOperations;
        lookups(dataOrId: string | { data: IntacctItemInput }): IntacctItem$Lookups;
        getDefaults: GetDefaultsOperation<IntacctItem>;
    }
    export interface IntacctAccountsPayableInvoice extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: string;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: string;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        externalIntegration: string;
        document: AccountsPayableInvoice;
        billBySupplier: string;
        taxSolutionId: string;
        billToPayToContactName: string;
        shipToReturnToContactName: string;
    }
    export interface IntacctAccountsPayableInvoiceInput extends VitalClientNodeInput {
        integration?: integer | string;
        node?: integer | string;
        sysId?: string;
        naturalKey?: string;
        state?: IntegrationState;
        url?: string;
        secondaryPageLink?: string;
        lastMessage?: string;
        notificationTrackingIds?: string[];
        creationStamp?: string;
        updateStamp?: string;
        version?: integer | string;
        difference?: string;
        intacctId?: string;
        recordNo?: integer | string;
    }
    export interface IntacctAccountsPayableInvoiceBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: any;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: any;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        externalIntegration: any;
        document: AccountsPayableInvoice;
        billBySupplier: any;
        taxSolutionId: string;
        billToPayToContactName: any;
        shipToReturnToContactName: any;
    }
    export interface IntacctAccountsPayableInvoice$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        synchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        forceSynchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        resetThirdPartyId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        updateSysId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface IntacctAccountsPayableInvoice$Lookups {
        integration: QueryOperation<ThirdPartyApplication>;
        mapping: QueryOperation<BaseMapping>;
        node: QueryOperation<MetaNodeFactory>;
        intacctConfiguration: QueryOperation<Intacct>;
    }
    export interface IntacctAccountsPayableInvoice$Operations {
        query: QueryOperation<IntacctAccountsPayableInvoice>;
        read: ReadOperation<IntacctAccountsPayableInvoice>;
        aggregate: {
            read: AggregateReadOperation<IntacctAccountsPayableInvoice>;
            query: AggregateQueryOperation<IntacctAccountsPayableInvoice>;
        };
        create: CreateOperation<IntacctAccountsPayableInvoiceInput, IntacctAccountsPayableInvoice>;
        getDuplicate: GetDuplicateOperation<IntacctAccountsPayableInvoice>;
        update: UpdateOperation<IntacctAccountsPayableInvoiceInput, IntacctAccountsPayableInvoice>;
        updateById: UpdateByIdOperation<IntacctAccountsPayableInvoiceInput, IntacctAccountsPayableInvoice>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctAccountsPayableInvoice$AsyncOperations;
        lookups(dataOrId: string | { data: IntacctAccountsPayableInvoiceInput }): IntacctAccountsPayableInvoice$Lookups;
        getDefaults: GetDefaultsOperation<IntacctAccountsPayableInvoice>;
    }
    export interface IntacctAccountsReceivableAdvance extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: string;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: string;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        externalIntegration: string;
        document: AccountsReceivableAdvance;
        paymentMethod: string;
        bankFeed: IntacctBankAccountTransactionFeed;
    }
    export interface IntacctAccountsReceivableAdvanceInput extends VitalClientNodeInput {
        integration?: integer | string;
        node?: integer | string;
        sysId?: string;
        naturalKey?: string;
        state?: IntegrationState;
        url?: string;
        secondaryPageLink?: string;
        lastMessage?: string;
        notificationTrackingIds?: string[];
        creationStamp?: string;
        updateStamp?: string;
        version?: integer | string;
        difference?: string;
        intacctId?: string;
        recordNo?: integer | string;
        paymentMethod?: string;
        bankFeed?: integer | string;
    }
    export interface IntacctAccountsReceivableAdvanceBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: any;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: any;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        externalIntegration: any;
        document: AccountsReceivableAdvance;
        paymentMethod: string;
        bankFeed: IntacctBankAccountTransactionFeed;
    }
    export interface IntacctAccountsReceivableAdvance$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        synchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        forceSynchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        resetThirdPartyId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        updateSysId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface IntacctAccountsReceivableAdvance$Lookups {
        integration: QueryOperation<ThirdPartyApplication>;
        mapping: QueryOperation<BaseMapping>;
        node: QueryOperation<MetaNodeFactory>;
        intacctConfiguration: QueryOperation<Intacct>;
        bankFeed: QueryOperation<IntacctBankAccountTransactionFeed>;
    }
    export interface IntacctAccountsReceivableAdvance$Operations {
        query: QueryOperation<IntacctAccountsReceivableAdvance>;
        read: ReadOperation<IntacctAccountsReceivableAdvance>;
        aggregate: {
            read: AggregateReadOperation<IntacctAccountsReceivableAdvance>;
            query: AggregateQueryOperation<IntacctAccountsReceivableAdvance>;
        };
        create: CreateOperation<IntacctAccountsReceivableAdvanceInput, IntacctAccountsReceivableAdvance>;
        getDuplicate: GetDuplicateOperation<IntacctAccountsReceivableAdvance>;
        update: UpdateOperation<IntacctAccountsReceivableAdvanceInput, IntacctAccountsReceivableAdvance>;
        updateById: UpdateByIdOperation<IntacctAccountsReceivableAdvanceInput, IntacctAccountsReceivableAdvance>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctAccountsReceivableAdvance$AsyncOperations;
        lookups(
            dataOrId: string | { data: IntacctAccountsReceivableAdvanceInput },
        ): IntacctAccountsReceivableAdvance$Lookups;
        getDefaults: GetDefaultsOperation<IntacctAccountsReceivableAdvance>;
    }
    export interface IntacctAccountsReceivableInvoice extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: string;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: string;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        externalIntegration: string;
        document: AccountsReceivableInvoice;
        billToCustomer: string;
        taxSolutionId: string;
    }
    export interface IntacctAccountsReceivableInvoiceInput extends VitalClientNodeInput {
        integration?: integer | string;
        node?: integer | string;
        sysId?: string;
        naturalKey?: string;
        state?: IntegrationState;
        url?: string;
        secondaryPageLink?: string;
        lastMessage?: string;
        notificationTrackingIds?: string[];
        creationStamp?: string;
        updateStamp?: string;
        version?: integer | string;
        difference?: string;
        intacctId?: string;
        recordNo?: integer | string;
    }
    export interface IntacctAccountsReceivableInvoiceBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: any;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: any;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        externalIntegration: any;
        document: AccountsReceivableInvoice;
        billToCustomer: any;
        taxSolutionId: string;
    }
    export interface IntacctAccountsReceivableInvoice$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        synchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        forceSynchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        resetThirdPartyId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        updateSysId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface IntacctAccountsReceivableInvoice$Lookups {
        integration: QueryOperation<ThirdPartyApplication>;
        mapping: QueryOperation<BaseMapping>;
        node: QueryOperation<MetaNodeFactory>;
        intacctConfiguration: QueryOperation<Intacct>;
    }
    export interface IntacctAccountsReceivableInvoice$Operations {
        query: QueryOperation<IntacctAccountsReceivableInvoice>;
        read: ReadOperation<IntacctAccountsReceivableInvoice>;
        aggregate: {
            read: AggregateReadOperation<IntacctAccountsReceivableInvoice>;
            query: AggregateQueryOperation<IntacctAccountsReceivableInvoice>;
        };
        create: CreateOperation<IntacctAccountsReceivableInvoiceInput, IntacctAccountsReceivableInvoice>;
        getDuplicate: GetDuplicateOperation<IntacctAccountsReceivableInvoice>;
        update: UpdateOperation<IntacctAccountsReceivableInvoiceInput, IntacctAccountsReceivableInvoice>;
        updateById: UpdateByIdOperation<IntacctAccountsReceivableInvoiceInput, IntacctAccountsReceivableInvoice>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctAccountsReceivableInvoice$AsyncOperations;
        lookups(
            dataOrId: string | { data: IntacctAccountsReceivableInvoiceInput },
        ): IntacctAccountsReceivableInvoice$Lookups;
        getDefaults: GetDefaultsOperation<IntacctAccountsReceivableInvoice>;
    }
    export interface IntacctAccountsReceivablePayment extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: string;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: string;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        externalIntegration: string;
        document: AccountsReceivablePayment;
        paymentMethod: string;
        bankFeed: IntacctBankAccountTransactionFeed;
    }
    export interface IntacctAccountsReceivablePaymentInput extends VitalClientNodeInput {
        integration?: integer | string;
        node?: integer | string;
        sysId?: string;
        naturalKey?: string;
        state?: IntegrationState;
        url?: string;
        secondaryPageLink?: string;
        lastMessage?: string;
        notificationTrackingIds?: string[];
        creationStamp?: string;
        updateStamp?: string;
        version?: integer | string;
        difference?: string;
        intacctId?: string;
        recordNo?: integer | string;
        paymentMethod?: string;
        bankFeed?: integer | string;
    }
    export interface IntacctAccountsReceivablePaymentBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: any;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: any;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        externalIntegration: any;
        document: AccountsReceivablePayment;
        paymentMethod: string;
        bankFeed: IntacctBankAccountTransactionFeed;
    }
    export interface IntacctAccountsReceivablePayment$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        synchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        forceSynchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        resetThirdPartyId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        updateSysId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface IntacctAccountsReceivablePayment$Lookups {
        integration: QueryOperation<ThirdPartyApplication>;
        mapping: QueryOperation<BaseMapping>;
        node: QueryOperation<MetaNodeFactory>;
        intacctConfiguration: QueryOperation<Intacct>;
        bankFeed: QueryOperation<IntacctBankAccountTransactionFeed>;
    }
    export interface IntacctAccountsReceivablePayment$Operations {
        query: QueryOperation<IntacctAccountsReceivablePayment>;
        read: ReadOperation<IntacctAccountsReceivablePayment>;
        aggregate: {
            read: AggregateReadOperation<IntacctAccountsReceivablePayment>;
            query: AggregateQueryOperation<IntacctAccountsReceivablePayment>;
        };
        create: CreateOperation<IntacctAccountsReceivablePaymentInput, IntacctAccountsReceivablePayment>;
        getDuplicate: GetDuplicateOperation<IntacctAccountsReceivablePayment>;
        update: UpdateOperation<IntacctAccountsReceivablePaymentInput, IntacctAccountsReceivablePayment>;
        updateById: UpdateByIdOperation<IntacctAccountsReceivablePaymentInput, IntacctAccountsReceivablePayment>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctAccountsReceivablePayment$AsyncOperations;
        lookups(
            dataOrId: string | { data: IntacctAccountsReceivablePaymentInput },
        ): IntacctAccountsReceivablePayment$Lookups;
        getDefaults: GetDefaultsOperation<IntacctAccountsReceivablePayment>;
    }
    export interface IntacctCustomer extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: string;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: string;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        hideDisplayContact: boolean;
        status: string;
        primaryContact: string;
        contactList: string;
        parent: Customer;
        billToAddress: string;
        shipToAddress: string;
    }
    export interface IntacctCustomerInput extends VitalClientNodeInput {
        integration?: integer | string;
        node?: integer | string;
        sysId?: string;
        naturalKey?: string;
        state?: IntegrationState;
        url?: string;
        secondaryPageLink?: string;
        lastMessage?: string;
        notificationTrackingIds?: string[];
        creationStamp?: string;
        updateStamp?: string;
        version?: integer | string;
        difference?: string;
        intacctId?: string;
        recordNo?: integer | string;
    }
    export interface IntacctCustomerBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: any;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: any;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        hideDisplayContact: boolean;
        status: string;
        primaryContact: any;
        contactList: any;
        parent: Customer;
        billToAddress: any;
        shipToAddress: any;
    }
    export interface IntacctCustomer$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        synchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        forceSynchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        resetThirdPartyId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        updateSysId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface IntacctCustomer$Lookups {
        integration: QueryOperation<ThirdPartyApplication>;
        mapping: QueryOperation<BaseMapping>;
        node: QueryOperation<MetaNodeFactory>;
        intacctConfiguration: QueryOperation<Intacct>;
    }
    export interface IntacctCustomer$Operations {
        query: QueryOperation<IntacctCustomer>;
        read: ReadOperation<IntacctCustomer>;
        aggregate: {
            read: AggregateReadOperation<IntacctCustomer>;
            query: AggregateQueryOperation<IntacctCustomer>;
        };
        create: CreateOperation<IntacctCustomerInput, IntacctCustomer>;
        getDuplicate: GetDuplicateOperation<IntacctCustomer>;
        update: UpdateOperation<IntacctCustomerInput, IntacctCustomer>;
        updateById: UpdateByIdOperation<IntacctCustomerInput, IntacctCustomer>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctCustomer$AsyncOperations;
        lookups(dataOrId: string | { data: IntacctCustomerInput }): IntacctCustomer$Lookups;
        getDefaults: GetDefaultsOperation<IntacctCustomer>;
    }
    export interface IntacctJournalEntry extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: string;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: string;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        externalIntegration: string;
        document: JournalEntry;
        taxImplications: string;
        taxSolutionId: string;
    }
    export interface IntacctJournalEntryInput extends VitalClientNodeInput {
        integration?: integer | string;
        node?: integer | string;
        sysId?: string;
        naturalKey?: string;
        state?: IntegrationState;
        url?: string;
        secondaryPageLink?: string;
        lastMessage?: string;
        notificationTrackingIds?: string[];
        creationStamp?: string;
        updateStamp?: string;
        version?: integer | string;
        difference?: string;
        intacctId?: string;
        recordNo?: integer | string;
    }
    export interface IntacctJournalEntryBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: any;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: any;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        externalIntegration: any;
        document: JournalEntry;
        taxImplications: string;
        taxSolutionId: string;
    }
    export interface IntacctJournalEntry$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        synchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        forceSynchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        resetThirdPartyId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        updateSysId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface IntacctJournalEntry$Lookups {
        integration: QueryOperation<ThirdPartyApplication>;
        mapping: QueryOperation<BaseMapping>;
        node: QueryOperation<MetaNodeFactory>;
        intacctConfiguration: QueryOperation<Intacct>;
    }
    export interface IntacctJournalEntry$Operations {
        query: QueryOperation<IntacctJournalEntry>;
        read: ReadOperation<IntacctJournalEntry>;
        aggregate: {
            read: AggregateReadOperation<IntacctJournalEntry>;
            query: AggregateQueryOperation<IntacctJournalEntry>;
        };
        create: CreateOperation<IntacctJournalEntryInput, IntacctJournalEntry>;
        getDuplicate: GetDuplicateOperation<IntacctJournalEntry>;
        update: UpdateOperation<IntacctJournalEntryInput, IntacctJournalEntry>;
        updateById: UpdateByIdOperation<IntacctJournalEntryInput, IntacctJournalEntry>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctJournalEntry$AsyncOperations;
        lookups(dataOrId: string | { data: IntacctJournalEntryInput }): IntacctJournalEntry$Lookups;
        getDefaults: GetDefaultsOperation<IntacctJournalEntry>;
    }
    export interface IntacctSupplier extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: string;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: string;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        hideDisplayContact: boolean;
        status: string;
        primaryContact: string;
        contactList: string;
        parent: Supplier;
        payToAddress: string;
        returnToAddress: string;
    }
    export interface IntacctSupplierInput extends VitalClientNodeInput {
        integration?: integer | string;
        node?: integer | string;
        sysId?: string;
        naturalKey?: string;
        state?: IntegrationState;
        url?: string;
        secondaryPageLink?: string;
        lastMessage?: string;
        notificationTrackingIds?: string[];
        creationStamp?: string;
        updateStamp?: string;
        version?: integer | string;
        difference?: string;
        intacctId?: string;
        recordNo?: integer | string;
    }
    export interface IntacctSupplierBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: any;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: any;
        pageLink: string;
        sysIdLink: string;
        intacctId: string;
        recordNo: integer;
        intacctConfiguration: Intacct;
        entityId: string;
        hideDisplayContact: boolean;
        status: string;
        primaryContact: any;
        contactList: any;
        parent: Supplier;
        payToAddress: any;
        returnToAddress: any;
    }
    export interface IntacctSupplier$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
        synchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        forceSynchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        resetThirdPartyId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        updateSysId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface IntacctSupplier$Lookups {
        integration: QueryOperation<ThirdPartyApplication>;
        mapping: QueryOperation<BaseMapping>;
        node: QueryOperation<MetaNodeFactory>;
        intacctConfiguration: QueryOperation<Intacct>;
    }
    export interface IntacctSupplier$Operations {
        query: QueryOperation<IntacctSupplier>;
        read: ReadOperation<IntacctSupplier>;
        aggregate: {
            read: AggregateReadOperation<IntacctSupplier>;
            query: AggregateQueryOperation<IntacctSupplier>;
        };
        create: CreateOperation<IntacctSupplierInput, IntacctSupplier>;
        getDuplicate: GetDuplicateOperation<IntacctSupplier>;
        update: UpdateOperation<IntacctSupplierInput, IntacctSupplier>;
        updateById: UpdateByIdOperation<IntacctSupplierInput, IntacctSupplier>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: IntacctSupplier$AsyncOperations;
        lookups(dataOrId: string | { data: IntacctSupplierInput }): IntacctSupplier$Lookups;
        getDefaults: GetDefaultsOperation<IntacctSupplier>;
    }
    export interface AccountExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        chartOfAccount: ChartOfAccount;
        isActive: boolean;
        name: string;
        composedDescription: string;
        isDirectEntryForbidden: boolean;
        isControl: boolean;
        attributeTypes: ClientCollection<AccountAttributeType>;
        dimensionTypes: ClientCollection<AccountDimensionType>;
        taxManagement: TaxManagement;
        isAutomaticAccount: boolean;
        tax: Tax;
        datevId: integer;
        intacctId: string;
        recordNo: integer;
        statusIntacct: string;
        isRequiredLocation: boolean;
        isRequiredItem: boolean;
        isRequiredCustomer: boolean;
        isRequiredSupplier: boolean;
        isRequiredProject: boolean;
        isRequiredEmploye: boolean;
        isRequiredTask: boolean;
        isRequiredDepartement: boolean;
        isRequiredClass: boolean;
    }
    export interface AccountInputExtension {
        _vendor?: integer | string;
        id?: string;
        chartOfAccount?: integer | string;
        isActive?: boolean | string;
        name?: string;
        isDirectEntryForbidden?: boolean | string;
        isControl?: boolean | string;
        attributeTypes?: Partial<AccountAttributeTypeInput>[];
        dimensionTypes?: Partial<AccountDimensionTypeInput>[];
        taxManagement?: TaxManagement;
        isAutomaticAccount?: boolean | string;
        tax?: integer | string;
        datevId?: integer | string;
        intacctId?: string;
        recordNo?: integer | string;
    }
    export interface AccountBindingExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        chartOfAccount: ChartOfAccount;
        isActive: boolean;
        name: string;
        composedDescription: string;
        isDirectEntryForbidden: boolean;
        isControl: boolean;
        attributeTypes: ClientCollection<AccountAttributeTypeBinding>;
        dimensionTypes: ClientCollection<AccountDimensionTypeBinding>;
        taxManagement: TaxManagement;
        isAutomaticAccount: boolean;
        tax: Tax;
        datevId: integer;
        intacctId: string;
        recordNo: integer;
        statusIntacct: string;
        isRequiredLocation: boolean;
        isRequiredItem: boolean;
        isRequiredCustomer: boolean;
        isRequiredSupplier: boolean;
        isRequiredProject: boolean;
        isRequiredEmploye: boolean;
        isRequiredTask: boolean;
        isRequiredDepartement: boolean;
        isRequiredClass: boolean;
    }
    export interface AccountsPayableInvoiceExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        financialSite: Site;
        financialSiteName: string;
        financialSiteTaxIdNumber: string;
        type: FinanceDocumentType;
        number: string;
        invoiceDate: string;
        postingDate: string;
        billBySupplier: Supplier;
        billBySupplierName: string;
        billBySupplierTaxIdNumber: string;
        payToSupplier: Supplier;
        payToSupplierLinkedAddress: BusinessEntityAddress;
        returnLinkedAddress: BusinessEntityAddress;
        currency: Currency;
        paymentTerm: PaymentTerm;
        postingStatus: JournalStatus;
        origin: AccountsPayableReceivableInvoiceOrigin;
        supplierDocumentNumber: string;
        supplierDocumentDate: string;
        reference: string;
        description: string;
        account: Account;
        dueDate: string;
        fxRateDate: string;
        totalCompanyAmountExcludingTax: string;
        totalCompanyAmountIncludingTax: string;
        totalCompanyTaxAmount: string;
        rateDescription: string;
        taxCalculationStatus: TaxCalculationStatus;
        purchaseDocumentNumber: string;
        purchaseDocumentSysId: integer;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsPayableInvoiceLine>;
        openItems: ClientCollection<AccountsPayableOpenItem>;
        taxes: ClientCollection<AccountsPayableInvoiceTax>;
        transactionCurrency: Currency;
        documentDate: string;
        postingDetails: ClientCollection<FinanceTransaction>;
        paymentTracking: PaymentTracking;
        intacctDocument: IntacctAccountsPayableInvoice;
        totalAmountExcludingTax: string;
        totalTaxAmount: string;
        totalTaxAmountAdjusted: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountIncludingTax: string;
    }
    export interface AccountsPayableInvoiceInputExtension {
        financialSite?: integer | string;
        financialSiteName?: string;
        financialSiteTaxIdNumber?: string;
        type?: FinanceDocumentType;
        number?: string;
        invoiceDate?: string;
        postingDate?: string;
        billBySupplier?: integer | string;
        billBySupplierName?: string;
        billBySupplierTaxIdNumber?: string;
        payToSupplier?: integer | string;
        payToSupplierLinkedAddress?: integer | string;
        returnLinkedAddress?: integer | string;
        currency?: integer | string;
        paymentTerm?: integer | string;
        postingStatus?: JournalStatus;
        origin?: AccountsPayableReceivableInvoiceOrigin;
        supplierDocumentNumber?: string;
        supplierDocumentDate?: string;
        reference?: string;
        description?: string;
        account?: integer | string;
        dueDate?: string;
        fxRateDate?: string;
        taxCalculationStatus?: TaxCalculationStatus;
        purchaseDocumentNumber?: string;
        purchaseDocumentSysId?: integer | string;
        lines?: Partial<AccountsPayableInvoiceLineInput>[];
        taxes?: Partial<AccountsPayableInvoiceTaxInput>[];
        paymentTracking?: integer | string;
        intacctDocument?: IntacctAccountsPayableInvoiceInput;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
    }
    export interface AccountsPayableInvoiceBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        financialSite: Site;
        financialSiteName: string;
        financialSiteTaxIdNumber: string;
        type: FinanceDocumentType;
        number: string;
        invoiceDate: string;
        postingDate: string;
        billBySupplier: Supplier;
        billBySupplierName: string;
        billBySupplierTaxIdNumber: string;
        payToSupplier: Supplier;
        payToSupplierLinkedAddress: BusinessEntityAddress;
        returnLinkedAddress: BusinessEntityAddress;
        currency: Currency;
        paymentTerm: PaymentTerm;
        postingStatus: JournalStatus;
        origin: AccountsPayableReceivableInvoiceOrigin;
        supplierDocumentNumber: string;
        supplierDocumentDate: string;
        reference: string;
        description: string;
        account: Account;
        dueDate: string;
        fxRateDate: string;
        totalCompanyAmountExcludingTax: string;
        totalCompanyAmountIncludingTax: string;
        totalCompanyTaxAmount: string;
        rateDescription: string;
        taxCalculationStatus: TaxCalculationStatus;
        purchaseDocumentNumber: string;
        purchaseDocumentSysId: integer;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsPayableInvoiceLineBinding>;
        openItems: ClientCollection<AccountsPayableOpenItem>;
        taxes: ClientCollection<AccountsPayableInvoiceTaxBinding>;
        transactionCurrency: Currency;
        documentDate: string;
        postingDetails: ClientCollection<FinanceTransaction>;
        paymentTracking: PaymentTracking;
        intacctDocument: IntacctAccountsPayableInvoiceBinding;
        totalAmountExcludingTax: string;
        totalTaxAmount: string;
        totalTaxAmountAdjusted: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountIncludingTax: string;
    }
    export interface AccountsPayableInvoiceLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsPayableInvoice;
        financialSite: Site;
        taxDate: string;
        recipientSite: Site;
        account: Account;
        documentLineType: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType: AccountsPayableReceivableInvoiceLineType;
        currency: Currency;
        amountExcludingTax: string;
        taxLineTaxAmount: string;
        taxDetail: string;
        description: string;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        attributesAndDimensions: ClientCollection<AccountsPayableInvoiceLineDimension>;
        accountingStagingLines: ClientCollection<AccountsPayableInvoiceLineStaging>;
        taxes: ClientCollection<AccountsPayableInvoiceLineTax>;
        uiTaxes: string;
        storedDimensions: string;
        storedAttributes: string;
        intacctDocumentLine: IntacctAccountsPayableInvoiceLine;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxAmountAdjusted: string;
        amountIncludingTax: string;
    }
    export interface AccountsPayableInvoiceLineInputExtension {
        financialSite?: integer | string;
        taxDate?: string;
        recipientSite?: integer | string;
        account?: integer | string;
        documentLineType?: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType?: AccountsPayableReceivableInvoiceLineType;
        currency?: integer | string;
        amountExcludingTax?: decimal | string;
        taxLineTaxAmount?: decimal | string;
        taxDetail?: string;
        description?: string;
        sourceDocumentNumber?: string;
        attributesAndDimensions?: Partial<AccountsPayableInvoiceLineDimensionInput>[];
        accountingStagingLines?: Partial<AccountsPayableInvoiceLineStagingInput>[];
        taxes?: Partial<AccountsPayableInvoiceLineTaxInput>[];
        uiTaxes?: string;
        intacctDocumentLine?: IntacctAccountsPayableInvoiceLineInput;
        taxableAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        amountIncludingTax?: decimal | string;
    }
    export interface AccountsPayableInvoiceLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsPayableInvoice;
        financialSite: Site;
        taxDate: string;
        recipientSite: Site;
        account: Account;
        documentLineType: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType: AccountsPayableReceivableInvoiceLineType;
        currency: Currency;
        amountExcludingTax: string;
        taxLineTaxAmount: string;
        taxDetail: string;
        description: string;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        attributesAndDimensions: ClientCollection<AccountsPayableInvoiceLineDimensionBinding>;
        accountingStagingLines: ClientCollection<AccountsPayableInvoiceLineStagingBinding>;
        taxes: ClientCollection<AccountsPayableInvoiceLineTaxBinding>;
        uiTaxes: any;
        storedDimensions: any;
        storedAttributes: any;
        intacctDocumentLine: IntacctAccountsPayableInvoiceLineBinding;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxAmountAdjusted: string;
        amountIncludingTax: string;
    }
    export interface AccountsReceivableAdvanceExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        bankAccount: BankAccount;
        financialSite: Site;
        financialSiteName: string;
        payToCustomerId: string;
        payToCustomerName: string;
        reference: string;
        description: string;
        postingDate: string;
        paymentDate: string;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        fxRateDate: string;
        advanceAmount: string;
        advanceCompanyAmount: string;
        postingStatus: JournalStatus;
        financeIntegrationStatus: PostingStatus;
        financeIntegrationApp: FinanceIntegrationApp;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsReceivableAdvanceLine>;
        intacctDocument: IntacctAccountsReceivableAdvance;
    }
    export interface AccountsReceivableAdvanceInputExtension {
        number?: string;
        bankAccount?: integer | string;
        financialSite?: integer | string;
        financialSiteName?: string;
        payToCustomerId?: string;
        payToCustomerName?: string;
        reference?: string;
        description?: string;
        postingDate?: string;
        paymentDate?: string;
        currency?: integer | string;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        fxRateDate?: string;
        advanceAmount?: decimal | string;
        advanceCompanyAmount?: decimal | string;
        postingStatus?: JournalStatus;
        lines?: Partial<AccountsReceivableAdvanceLineInput>[];
        intacctDocument?: IntacctAccountsReceivableAdvanceInput;
    }
    export interface AccountsReceivableAdvanceBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        bankAccount: BankAccount;
        financialSite: Site;
        financialSiteName: string;
        payToCustomerId: string;
        payToCustomerName: string;
        reference: string;
        description: string;
        postingDate: string;
        paymentDate: string;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        fxRateDate: string;
        advanceAmount: string;
        advanceCompanyAmount: string;
        postingStatus: JournalStatus;
        financeIntegrationStatus: PostingStatus;
        financeIntegrationApp: FinanceIntegrationApp;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsReceivableAdvanceLineBinding>;
        intacctDocument: IntacctAccountsReceivableAdvanceBinding;
    }
    export interface AccountsReceivableAdvanceExtension$Mutations {
        createArAdvance: Node$Operation<
            {
                data: {
                    bankAccount?: integer | string;
                    financialSite?: integer | string;
                    payToCustomerId: string;
                    payToCustomerName: string;
                    description?: string;
                    paymentMethod: string;
                    postingDate: string;
                    paymentDate: string;
                    currency?: integer | string;
                    companyFxRate: decimal | string;
                    companyFxRateDivisor: decimal | string;
                    fxRateDate: string;
                    advanceAmount: decimal | string;
                    advanceCompanyAmount: decimal | string;
                    bankFeed?: integer | string;
                    arMatch: {
                        isArMatch?: boolean | string;
                        customerId?: string;
                        matchingReasons?: string;
                        arPaymentType?: string;
                    };
                    lines?: {
                        financialSite?: integer | string;
                        currency?: integer | string;
                        account?: integer | string;
                        advanceAmount: decimal | string;
                        advanceCompanyAmount: decimal | string;
                        storedAttributes?: string;
                        storedDimensions?: string;
                    }[];
                };
            },
            AccountsReceivableAdvance
        >;
    }
    export interface AccountsReceivableAdvanceExtension$Operations {
        mutations: AccountsReceivableAdvanceExtension$Mutations;
        getDefaults: GetDefaultsOperation<AccountsReceivableAdvance>;
    }
    export interface AccountsReceivableAdvanceLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsReceivableAdvance;
        financialSite: Site;
        account: Account;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        advanceAmount: string;
        advanceCompanyAmount: string;
        description: string;
        storedAttributes: string;
        computedAttributes: string;
        storedDimensions: string;
        analyticalData: AnalyticalData;
        hasAttributesOrDimenionsChanged: boolean;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: string;
        supplier: Supplier;
        project: Attribute;
        employee: Attribute;
        task: Attribute;
        item: Item;
        intacctDocumentLine: IntacctAccountsReceivableAdvanceLine;
    }
    export interface AccountsReceivableAdvanceLineInputExtension {
        financialSite?: integer | string;
        account?: integer | string;
        currency?: integer | string;
        advanceAmount?: decimal | string;
        advanceCompanyAmount?: decimal | string;
        description?: string;
        storedAttributes?: string;
        storedDimensions?: string;
        analyticalData?: integer | string;
        hasAttributesOrDimenionsChanged?: boolean | string;
        intacctDocumentLine?: IntacctAccountsReceivableAdvanceLineInput;
    }
    export interface AccountsReceivableAdvanceLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsReceivableAdvance;
        financialSite: Site;
        account: Account;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        advanceAmount: string;
        advanceCompanyAmount: string;
        description: string;
        storedAttributes: any;
        computedAttributes: any;
        storedDimensions: any;
        analyticalData: AnalyticalData;
        hasAttributesOrDimenionsChanged: boolean;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: string;
        supplier: Supplier;
        project: Attribute;
        employee: Attribute;
        task: Attribute;
        item: Item;
        intacctDocumentLine: IntacctAccountsReceivableAdvanceLineBinding;
    }
    export interface AccountsReceivableInvoiceExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        financialSite: Site;
        taxEngine: TaxEngine;
        financialSiteName: string;
        financialSiteTaxIdNumber: string;
        type: FinanceDocumentType;
        number: string;
        invoiceDate: string;
        postingDate: string;
        billToCustomer: Customer;
        billToCustomerName: string;
        billToCustomerTaxIdNumber: string;
        currency: Currency;
        paymentTerm: PaymentTerm;
        postingStatus: JournalStatus;
        paymentStatus: OpenItemStatus;
        isPrinted: boolean;
        origin: AccountsPayableReceivableInvoiceOrigin;
        reference: string;
        description: string;
        account: Account;
        dueDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        totalTaxAmountAdjusted: string;
        fxRateDate: string;
        totalCompanyAmountExcludingTax: string;
        totalCompanyAmountIncludingTax: string;
        totalCompanyTaxAmount: string;
        rateDescription: string;
        salesDocumentNumber: string;
        salesDocumentSysId: integer;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsReceivableInvoiceLine>;
        openItems: ClientCollection<AccountsReceivableOpenItem>;
        taxes: ClientCollection<AccountsReceivableInvoiceTax>;
        transactionCurrency: Currency;
        documentDate: string;
        postingDetails: ClientCollection<FinanceTransaction>;
        intacctDocument: IntacctAccountsReceivableInvoice;
        totalAmountExcludingTax: string;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountIncludingTax: string;
    }
    export interface AccountsReceivableInvoiceInputExtension {
        financialSite?: integer | string;
        financialSiteName?: string;
        financialSiteTaxIdNumber?: string;
        type?: FinanceDocumentType;
        number?: string;
        invoiceDate?: string;
        postingDate?: string;
        billToCustomer?: integer | string;
        billToCustomerName?: string;
        billToCustomerTaxIdNumber?: string;
        currency?: integer | string;
        paymentTerm?: integer | string;
        postingStatus?: JournalStatus;
        paymentStatus?: OpenItemStatus;
        isPrinted?: boolean | string;
        origin?: AccountsPayableReceivableInvoiceOrigin;
        reference?: string;
        description?: string;
        account?: integer | string;
        dueDate?: string;
        taxCalculationStatus?: TaxCalculationStatus;
        totalTaxAmountAdjusted?: decimal | string;
        fxRateDate?: string;
        salesDocumentNumber?: string;
        salesDocumentSysId?: integer | string;
        lines?: Partial<AccountsReceivableInvoiceLineInput>[];
        taxes?: Partial<AccountsReceivableInvoiceTaxInput>[];
        intacctDocument?: IntacctAccountsReceivableInvoiceInput;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
    }
    export interface AccountsReceivableInvoiceBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        financialSite: Site;
        taxEngine: TaxEngine;
        financialSiteName: string;
        financialSiteTaxIdNumber: string;
        type: FinanceDocumentType;
        number: string;
        invoiceDate: string;
        postingDate: string;
        billToCustomer: Customer;
        billToCustomerName: string;
        billToCustomerTaxIdNumber: string;
        currency: Currency;
        paymentTerm: PaymentTerm;
        postingStatus: JournalStatus;
        paymentStatus: OpenItemStatus;
        isPrinted: boolean;
        origin: AccountsPayableReceivableInvoiceOrigin;
        reference: string;
        description: string;
        account: Account;
        dueDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        totalTaxAmountAdjusted: string;
        fxRateDate: string;
        totalCompanyAmountExcludingTax: string;
        totalCompanyAmountIncludingTax: string;
        totalCompanyTaxAmount: string;
        rateDescription: string;
        salesDocumentNumber: string;
        salesDocumentSysId: integer;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsReceivableInvoiceLineBinding>;
        openItems: ClientCollection<AccountsReceivableOpenItem>;
        taxes: ClientCollection<AccountsReceivableInvoiceTaxBinding>;
        transactionCurrency: Currency;
        documentDate: string;
        postingDetails: ClientCollection<FinanceTransaction>;
        intacctDocument: IntacctAccountsReceivableInvoiceBinding;
        totalAmountExcludingTax: string;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountIncludingTax: string;
    }
    export interface AccountsReceivableInvoiceExtension$Mutations {
        updateOpenItemFromIntacct: Node$Operation<
            {
                arInvoiceSysId: integer | string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
    }
    export interface AccountsReceivableInvoiceExtension$Operations {
        mutations: AccountsReceivableInvoiceExtension$Mutations;
        getDefaults: GetDefaultsOperation<AccountsReceivableInvoice>;
    }
    export interface AccountsReceivableInvoiceLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsReceivableInvoice;
        financialSite: Site;
        taxDate: string;
        providerSite: Site;
        account: Account;
        documentLineType: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType: AccountsPayableReceivableInvoiceLineType;
        currency: Currency;
        amountExcludingTax: string;
        taxLineTaxAmount: string;
        taxDetail: string;
        quantity: string;
        quantityInSalesUnit: string;
        description: string;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        attributesAndDimensions: ClientCollection<AccountsReceivableInvoiceLineDimension>;
        accountingStagingLines: ClientCollection<AccountsReceivableInvoiceLineStaging>;
        taxes: ClientCollection<AccountsReceivableInvoiceLineTax>;
        uiTaxes: string;
        storedDimensions: string;
        storedAttributes: string;
        intacctDocumentLine: IntacctAccountsReceivableInvoiceLine;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxAmountAdjusted: string;
        amountIncludingTax: string;
        netPriceIncludingTax: string;
    }
    export interface AccountsReceivableInvoiceLineInputExtension {
        financialSite?: integer | string;
        taxDate?: string;
        providerSite?: integer | string;
        account?: integer | string;
        documentLineType?: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType?: AccountsPayableReceivableInvoiceLineType;
        currency?: integer | string;
        amountExcludingTax?: decimal | string;
        taxLineTaxAmount?: decimal | string;
        taxDetail?: string;
        quantity?: decimal | string;
        description?: string;
        sourceDocumentNumber?: string;
        attributesAndDimensions?: Partial<AccountsReceivableInvoiceLineDimensionInput>[];
        accountingStagingLines?: Partial<AccountsReceivableInvoiceLineStagingInput>[];
        taxes?: Partial<AccountsReceivableInvoiceLineTaxInput>[];
        uiTaxes?: string;
        intacctDocumentLine?: IntacctAccountsReceivableInvoiceLineInput;
        netPriceIncludingTax?: decimal | string;
    }
    export interface AccountsReceivableInvoiceLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsReceivableInvoice;
        financialSite: Site;
        taxDate: string;
        providerSite: Site;
        account: Account;
        documentLineType: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType: AccountsPayableReceivableInvoiceLineType;
        currency: Currency;
        amountExcludingTax: string;
        taxLineTaxAmount: string;
        taxDetail: string;
        quantity: string;
        quantityInSalesUnit: string;
        description: string;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        attributesAndDimensions: ClientCollection<AccountsReceivableInvoiceLineDimensionBinding>;
        accountingStagingLines: ClientCollection<AccountsReceivableInvoiceLineStagingBinding>;
        taxes: ClientCollection<AccountsReceivableInvoiceLineTaxBinding>;
        uiTaxes: any;
        storedDimensions: any;
        storedAttributes: any;
        intacctDocumentLine: IntacctAccountsReceivableInvoiceLineBinding;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxAmountAdjusted: string;
        amountIncludingTax: string;
        netPriceIncludingTax: string;
    }
    export interface AccountsReceivablePaymentExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        bankAccount: BankAccount;
        financialSite: Site;
        financialSiteName: string;
        payToCustomerId: string;
        payToCustomerName: string;
        reference: string;
        description: string;
        postingDate: string;
        paymentDate: string;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        fxRateDate: string;
        paymentAmount: string;
        paymentCompanyAmount: string;
        postingStatus: JournalStatus;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsReceivablePaymentLine>;
        intacctDocument: IntacctAccountsReceivablePayment;
    }
    export interface AccountsReceivablePaymentInputExtension {
        number?: string;
        bankAccount?: integer | string;
        financialSite?: integer | string;
        financialSiteName?: string;
        payToCustomerId?: string;
        payToCustomerName?: string;
        reference?: string;
        description?: string;
        postingDate?: string;
        paymentDate?: string;
        currency?: integer | string;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        fxRateDate?: string;
        paymentAmount?: decimal | string;
        paymentCompanyAmount?: decimal | string;
        postingStatus?: JournalStatus;
        lines?: Partial<AccountsReceivablePaymentLineInput>[];
        intacctDocument?: IntacctAccountsReceivablePaymentInput;
    }
    export interface AccountsReceivablePaymentBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        bankAccount: BankAccount;
        financialSite: Site;
        financialSiteName: string;
        payToCustomerId: string;
        payToCustomerName: string;
        reference: string;
        description: string;
        postingDate: string;
        paymentDate: string;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        fxRateDate: string;
        paymentAmount: string;
        paymentCompanyAmount: string;
        postingStatus: JournalStatus;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsReceivablePaymentLineBinding>;
        intacctDocument: IntacctAccountsReceivablePaymentBinding;
    }
    export interface AccountsReceivablePaymentExtension$Mutations {
        createArPayment: Node$Operation<
            {
                data: {
                    bankAccount?: integer | string;
                    financialSite?: integer | string;
                    payToCustomerId: string;
                    payToCustomerName: string;
                    description?: string;
                    paymentMethod: string;
                    postingDate: string;
                    paymentDate: string;
                    currency?: integer | string;
                    companyFxRate: decimal | string;
                    companyFxRateDivisor: decimal | string;
                    fxRateDate: string;
                    paymentAmount: decimal | string;
                    paymentCompanyAmount: decimal | string;
                    bankFeed?: integer | string;
                    arMatch: {
                        isArMatch?: boolean | string;
                        customerId?: string;
                        matchingReasons?: string;
                        arPaymentType?: string;
                    };
                    jsonArInvoices: {
                        matchedArInvoices?: {
                            recordNo?: integer | string;
                            arInvoiceAmountMatched?: decimal | string;
                        }[];
                    };
                    lines?: {
                        type: FinanceDocumentType;
                        arInvoiceRecordNo?: integer | string;
                        financialSite?: integer | string;
                        currency?: integer | string;
                        paymentAmount: decimal | string;
                        paymentCompanyAmount: decimal | string;
                    }[];
                };
            },
            AccountsReceivablePayment
        >;
    }
    export interface AccountsReceivablePaymentExtension$Operations {
        mutations: AccountsReceivablePaymentExtension$Mutations;
        getDefaults: GetDefaultsOperation<AccountsReceivablePayment>;
    }
    export interface AccountsReceivablePaymentLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsReceivablePayment;
        type: FinanceDocumentType;
        financialSite: Site;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        paymentAmount: string;
        paymentCompanyAmount: string;
        intacctDocumentLine: IntacctAccountsReceivablePaymentLine;
    }
    export interface AccountsReceivablePaymentLineInputExtension {
        type?: FinanceDocumentType;
        financialSite?: integer | string;
        currency?: integer | string;
        paymentAmount?: decimal | string;
        paymentCompanyAmount?: decimal | string;
        intacctDocumentLine?: IntacctAccountsReceivablePaymentLineInput;
    }
    export interface AccountsReceivablePaymentLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsReceivablePayment;
        type: FinanceDocumentType;
        financialSite: Site;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        paymentAmount: string;
        paymentCompanyAmount: string;
        intacctDocumentLine: IntacctAccountsReceivablePaymentLineBinding;
    }
    export interface AttributeExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        composedDescription: string;
        attributeType: AttributeType;
        attributeRestrictedTo: Attribute;
        attributeRestrictedToId: string;
        site: Site;
        item: Item;
        recordNo: integer;
        statusIntacct: string;
        intacctProject: string;
        intacctId: string;
    }
    export interface AttributeInputExtension {
        isActive?: boolean | string;
        id?: string;
        name?: string;
        attributeType?: integer | string;
        attributeRestrictedTo?: integer | string;
        attributeRestrictedToId?: string;
        site?: integer | string;
        item?: integer | string;
        recordNo?: integer | string;
        intacctId?: string;
    }
    export interface AttributeBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        composedDescription: string;
        attributeType: AttributeType;
        attributeRestrictedTo: Attribute;
        attributeRestrictedToId: string;
        site: Site;
        item: Item;
        recordNo: integer;
        statusIntacct: string;
        intacctProject: string;
        intacctId: string;
    }
    export interface AttributeExtension$AsyncOperations {
        updateRecordNoOnEmployee: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface AttributeExtension$Operations {
        asyncOperations: AttributeExtension$AsyncOperations;
    }
    export interface BankAccountExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        name: string;
        id: string;
        financialSite: Site;
        currency: Currency;
        bankAccountType: BankAccountType;
        statusIntacct: string;
        intacctGlAccount: string;
        location: string;
        intacctId: string;
        megaEntityId: string;
    }
    export interface BankAccountInputExtension {
        isActive?: boolean | string;
        name?: string;
        id?: string;
        financialSite?: integer | string;
        currency?: integer | string;
        bankAccountType?: BankAccountType;
        intacctGlAccount?: string;
        location?: string;
        intacctId?: string;
        megaEntityId?: string;
    }
    export interface BankAccountBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        name: string;
        id: string;
        financialSite: Site;
        currency: Currency;
        bankAccountType: BankAccountType;
        statusIntacct: string;
        intacctGlAccount: string;
        location: string;
        intacctId: string;
        megaEntityId: string;
    }
    export interface BusinessEntityContactExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        contact: Contact;
        title: Title;
        firstName: string;
        lastName: string;
        displayName: string;
        preferredName: string;
        role: ContactRole;
        position: string;
        locationPhoneNumber: string;
        email: string;
        image: BinaryStream;
        businessEntity: BusinessEntity;
        address: BusinessEntityAddress;
        isPrimary: boolean;
        intacctPrintAs: string;
        _notes: ClientCollection<SysNoteAssociation>;
    }
    export interface BusinessEntityContactInputExtension {
        isActive?: boolean | string;
        contact?: integer | string;
        title?: Title;
        firstName?: string;
        lastName?: string;
        preferredName?: string;
        role?: ContactRole;
        position?: string;
        locationPhoneNumber?: string;
        email?: string;
        image?: BinaryStream;
        address?: integer | string;
        isPrimary?: boolean | string;
        _notes?: Partial<SysNoteAssociationInput>[];
    }
    export interface BusinessEntityContactBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        contact: Contact;
        title: Title;
        firstName: string;
        lastName: string;
        displayName: string;
        preferredName: string;
        role: ContactRole;
        position: string;
        locationPhoneNumber: string;
        email: string;
        image: BinaryStream;
        businessEntity: BusinessEntity;
        address: BusinessEntityAddress;
        isPrimary: boolean;
        intacctPrintAs: string;
        _notes: ClientCollection<SysNoteAssociation>;
    }
    export interface BusinessEntityAddressExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        address: Address;
        name: string;
        addressLine1: string;
        addressLine2: string;
        city: string;
        region: string;
        postcode: string;
        country: Country;
        locationPhoneNumber: string;
        concatenatedAddress: string;
        concatenatedAddressWithoutName: string;
        businessEntity: BusinessEntity;
        isPrimary: boolean;
        contacts: ClientCollection<BusinessEntityContact>;
        primaryContact: BusinessEntityContact;
        deliveryDetail: DeliveryDetail;
        intacctBusinessEntityAddress: IntacctContact;
    }
    export interface BusinessEntityAddressInputExtension {
        isActive?: boolean | string;
        address?: integer | string;
        name?: string;
        addressLine1?: string;
        addressLine2?: string;
        city?: string;
        region?: string;
        postcode?: string;
        country?: integer | string;
        locationPhoneNumber?: string;
        isPrimary?: boolean | string;
        deliveryDetail?: DeliveryDetailInput;
        intacctBusinessEntityAddress?: IntacctContactInput;
    }
    export interface BusinessEntityAddressBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        address: Address;
        name: string;
        addressLine1: string;
        addressLine2: string;
        city: string;
        region: string;
        postcode: string;
        country: Country;
        locationPhoneNumber: string;
        concatenatedAddress: string;
        concatenatedAddressWithoutName: string;
        businessEntity: BusinessEntity;
        isPrimary: boolean;
        contacts: ClientCollection<BusinessEntityContact>;
        primaryContact: BusinessEntityContact;
        deliveryDetail: DeliveryDetailBinding;
        intacctBusinessEntityAddress: IntacctContactBinding;
    }
    export interface BusinessEntityExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        legalEntity: LegalEntity;
        name: string;
        country: Country;
        currency: Currency;
        taxIdNumber: string;
        siret: string;
        addresses: ClientCollection<BusinessEntityAddress>;
        contacts: ClientCollection<BusinessEntityContact>;
        primaryAddress: BusinessEntityAddress;
        primaryContact: BusinessEntityContact;
        image: BinaryStream;
        website: string;
        isSite: boolean;
        parent: BusinessEntity;
        site: Site;
        customer: Customer;
        supplier: Supplier;
        composedDescription: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        isSupplier: boolean;
        isCustomer: boolean;
    }
    export interface BusinessEntityInputExtension {
        id?: string;
        isActive?: boolean | string;
        legalEntity?: LegalEntity;
        name?: string;
        country?: integer | string;
        currency?: integer | string;
        taxIdNumber?: string;
        siret?: string;
        addresses?: Partial<BusinessEntityAddressInput>[];
        contacts?: Partial<BusinessEntityContactInput>[];
        image?: BinaryStream;
        website?: string;
        parent?: integer | string;
        site?: SiteInput;
        customer?: CustomerInput;
        supplier?: SupplierInput;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface BusinessEntityBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        legalEntity: LegalEntity;
        name: string;
        country: Country;
        currency: Currency;
        taxIdNumber: string;
        siret: string;
        addresses: ClientCollection<BusinessEntityAddressBinding>;
        contacts: ClientCollection<BusinessEntityContactBinding>;
        primaryAddress: BusinessEntityAddress;
        primaryContact: BusinessEntityContact;
        image: BinaryStream;
        website: string;
        isSite: boolean;
        parent: BusinessEntity;
        site: SiteBinding;
        customer: CustomerBinding;
        supplier: SupplierBinding;
        composedDescription: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        isSupplier: boolean;
        isCustomer: boolean;
    }
    export interface CompanyExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        name: string;
        description: string;
        legislation: Legislation;
        chartOfAccount: ChartOfAccount;
        hierarchyChartContent: TextStream;
        siren: string;
        naf: string;
        rcs: string;
        legalForm: LegalForm;
        country: Country;
        currency: Currency;
        priceScale: integer;
        addresses: ClientCollection<CompanyAddress>;
        primaryAddress: CompanyAddress;
        primaryContact: CompanyContact;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        customerOnHoldCheck: CustomerOnHoldType;
        contacts: ClientCollection<CompanyContact>;
        postingClass: PostingClass;
        taxEngine: TaxEngine;
        doStockPosting: boolean;
        doWipPosting: boolean;
        doArPosting: boolean;
        doApPosting: boolean;
        doNonAbsorbedPosting: boolean;
        attributeTypes: ClientCollection<CompanyAttributeType>;
        dimensionTypes: ClientCollection<CompanyDimensionType>;
        defaultAttributes: ClientCollection<CompanyDefaultAttribute>;
        defaultDimensions: ClientCollection<CompanyDefaultDimension>;
        datevConsultantNumber: integer;
        datevCustomerNumber: integer;
        bankAccount: BankAccount;
        doUpdateArAmountPaid: boolean;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface CompanyInputExtension {
        id?: string;
        isActive?: boolean | string;
        name?: string;
        description?: string;
        legislation?: integer | string;
        chartOfAccount?: integer | string;
        siren?: string;
        naf?: string;
        rcs?: string;
        legalForm?: LegalForm;
        country?: integer | string;
        currency?: integer | string;
        addresses?: Partial<CompanyAddressInput>[];
        sequenceNumberId?: string;
        customerOnHoldCheck?: CustomerOnHoldType;
        contacts?: Partial<CompanyContactInput>[];
        postingClass?: integer | string;
        taxEngine?: TaxEngine;
        doStockPosting?: boolean | string;
        doWipPosting?: boolean | string;
        doNonAbsorbedPosting?: boolean | string;
        attributeTypes?: Partial<CompanyAttributeTypeInput>[];
        dimensionTypes?: Partial<CompanyDimensionTypeInput>[];
        defaultAttributes?: Partial<CompanyDefaultAttributeInput>[];
        defaultDimensions?: Partial<CompanyDefaultDimensionInput>[];
        datevConsultantNumber?: integer | string;
        datevCustomerNumber?: integer | string;
        bankAccount?: integer | string;
        doUpdateArAmountPaid?: boolean | string;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface CompanyBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        name: string;
        description: string;
        legislation: Legislation;
        chartOfAccount: ChartOfAccount;
        hierarchyChartContent: TextStream;
        siren: string;
        naf: string;
        rcs: string;
        legalForm: LegalForm;
        country: Country;
        currency: Currency;
        priceScale: integer;
        addresses: ClientCollection<CompanyAddressBinding>;
        primaryAddress: CompanyAddress;
        primaryContact: CompanyContact;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        customerOnHoldCheck: CustomerOnHoldType;
        contacts: ClientCollection<CompanyContactBinding>;
        postingClass: PostingClass;
        taxEngine: TaxEngine;
        doStockPosting: boolean;
        doWipPosting: boolean;
        doArPosting: boolean;
        doApPosting: boolean;
        doNonAbsorbedPosting: boolean;
        attributeTypes: ClientCollection<CompanyAttributeTypeBinding>;
        dimensionTypes: ClientCollection<CompanyDimensionTypeBinding>;
        defaultAttributes: ClientCollection<CompanyDefaultAttributeBinding>;
        defaultDimensions: ClientCollection<CompanyDefaultDimensionBinding>;
        datevConsultantNumber: integer;
        datevCustomerNumber: integer;
        bankAccount: BankAccount;
        doUpdateArAmountPaid: boolean;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface CompanyExtension$AsyncOperations {
        syncCompanyOnHold: AsyncOperation<
            {
                companySysId?: string;
                isAllCompanies?: boolean | string;
            },
            boolean
        >;
    }
    export interface CompanyExtension$Operations {
        asyncOperations: CompanyExtension$AsyncOperations;
    }
    export interface CustomerExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        billToCustomer: Customer;
        billToAddress: BusinessEntityAddress;
        payByCustomer: Customer;
        payByAddress: BusinessEntityAddress;
        deliveryAddresses: ClientCollection<BusinessEntityAddress>;
        primaryShipToAddress: BusinessEntityAddress;
        isOnHold: boolean;
        items: ClientCollection<ItemCustomer>;
        itemPrices: ClientCollection<ItemCustomerPrice>;
        creditLimit: string;
        displayStatus: CustomerDisplayStatus;
        datevId: integer;
        postingClass: PostingClass;
        storedDimensions: string;
        storedAttributes: string;
        intacctCustomer: IntacctCustomer;
        _attachments: ClientCollection<AttachmentAssociation>;
        analyticalData: AnalyticalData;
    }
    export interface CustomerInputExtension {
        isActive?: boolean | string;
        businessEntity?: BusinessEntityInput;
        primaryAddress?: integer | string;
        internalNote?: TextStream;
        paymentTerm?: integer | string;
        minimumOrderAmount?: decimal | string;
        category?: integer | string;
        billToCustomer?: integer | string;
        billToAddress?: integer | string;
        payByCustomer?: integer | string;
        payByAddress?: integer | string;
        isOnHold?: boolean | string;
        items?: Partial<ItemCustomerInput>[];
        creditLimit?: decimal | string;
        datevId?: integer | string;
        postingClass?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        intacctCustomer?: IntacctCustomerInput;
        _attachments?: Partial<AttachmentAssociationInput>[];
        analyticalData?: integer | string;
    }
    export interface CustomerBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        billToCustomer: Customer;
        billToAddress: BusinessEntityAddress;
        payByCustomer: Customer;
        payByAddress: BusinessEntityAddress;
        deliveryAddresses: ClientCollection<BusinessEntityAddress>;
        primaryShipToAddress: BusinessEntityAddress;
        isOnHold: boolean;
        items: ClientCollection<ItemCustomerBinding>;
        itemPrices: ClientCollection<ItemCustomerPrice>;
        creditLimit: string;
        displayStatus: CustomerDisplayStatus;
        datevId: integer;
        postingClass: PostingClass;
        storedDimensions: any;
        storedAttributes: any;
        intacctCustomer: IntacctCustomerBinding;
        _attachments: ClientCollection<AttachmentAssociation>;
        analyticalData: AnalyticalData;
    }
    export interface DimensionExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        composedDescription: string;
        dimensionType: DimensionType;
        intacctId: string;
        intacctObject: string;
        recordNo: integer;
        statusIntacct: string;
    }
    export interface DimensionInputExtension {
        isActive?: boolean | string;
        id?: string;
        name?: string;
        dimensionType?: integer | string;
        intacctId?: string;
        intacctObject?: string;
        recordNo?: integer | string;
    }
    export interface DimensionBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        composedDescription: string;
        dimensionType: DimensionType;
        intacctId: string;
        intacctObject: string;
        recordNo: integer;
        statusIntacct: string;
    }
    export interface DimensionTypeExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        name: string;
        docProperty: DocProperty;
        isUsed: boolean;
        dimensions: ClientCollection<Dimension>;
        analyticalMeasureType: AnalyticalMeasureType;
        intacctObject: string;
    }
    export interface DimensionTypeInputExtension {
        _vendor?: integer | string;
        isActive?: boolean | string;
        name?: string;
        docProperty?: DocProperty;
        intacctObject?: string;
    }
    export interface DimensionTypeBindingExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        name: string;
        docProperty: DocProperty;
        isUsed: boolean;
        dimensions: ClientCollection<Dimension>;
        analyticalMeasureType: AnalyticalMeasureType;
        intacctObject: string;
    }
    export interface IntacctExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        isRequestAsynchronous: boolean;
        isContactListCleaned: boolean;
        isCategoryNameClean: boolean;
        isDisplayContactHidden: boolean;
        senderId: string;
        senderPassword: string;
        companyId: string;
        userId: string;
        userPassword: string;
        isFullFilledAuthentification: boolean;
        endpointUrl: string;
        entityId: string;
        sessionId: string;
        sessionExpiration: string;
        controlIdTableName: string;
        policyId: string;
        asyncTimeout: integer;
        infoXTreeMAuditTrail: string;
        mustXtreemAuditTrail: string;
        isXTreeMAuditTrail: boolean;
        chartOfAccount: ChartOfAccount;
        taxSolution: string;
        messageHistory: ClientCollection<SysMessageHistory>;
        entityList: string;
        transactionIntegrationLevel: TransactionIntegrationLevel;
        lines: ClientCollection<IntacctLine>;
        generateSmartEvent: ClientCollection<IntacctMap>;
        customPackageFile: BinaryStream;
        legislation: Legislation;
        taxCategory: TaxCategory;
    }
    export interface IntacctInputExtension {
        id?: string;
        isActive?: boolean | string;
        isRequestAsynchronous?: boolean | string;
        isContactListCleaned?: boolean | string;
        isCategoryNameClean?: boolean | string;
        isDisplayContactHidden?: boolean | string;
        companyId?: string;
        userId?: string;
        userPassword?: string;
        entityId?: string;
        sessionId?: string;
        sessionExpiration?: string;
        chartOfAccount?: integer | string;
        taxSolution?: string;
        entityList?: string;
        transactionIntegrationLevel?: TransactionIntegrationLevel;
        lines?: Partial<IntacctLineInput>[];
        legislation?: integer | string;
        taxCategory?: integer | string;
    }
    export interface IntacctBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        isRequestAsynchronous: boolean;
        isContactListCleaned: boolean;
        isCategoryNameClean: boolean;
        isDisplayContactHidden: boolean;
        senderId: string;
        senderPassword: string;
        companyId: string;
        userId: string;
        userPassword: string;
        isFullFilledAuthentification: boolean;
        endpointUrl: string;
        entityId: string;
        sessionId: string;
        sessionExpiration: string;
        controlIdTableName: string;
        policyId: string;
        asyncTimeout: integer;
        infoXTreeMAuditTrail: any;
        mustXtreemAuditTrail: any;
        isXTreeMAuditTrail: boolean;
        chartOfAccount: ChartOfAccount;
        taxSolution: string;
        messageHistory: ClientCollection<SysMessageHistory>;
        entityList: any;
        transactionIntegrationLevel: TransactionIntegrationLevel;
        lines: ClientCollection<IntacctLineBinding>;
        generateSmartEvent: ClientCollection<IntacctMap>;
        customPackageFile: BinaryStream;
        legislation: Legislation;
        taxCategory: TaxCategory;
    }
    export interface IntacctExtension$Mutations {
        generateSmartEventFile: Node$Operation<{}, string>;
    }
    export interface IntacctExtension$Operations {
        mutations: IntacctExtension$Mutations;
        getDefaults: GetDefaultsOperation<Intacct>;
    }
    export interface ItemExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergen>;
        classifications: ClientCollection<ItemClassifications>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPrice>;
        itemSites: ClientCollection<ItemSite>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPrice>;
        capacity: integer;
        landedCostItem: LandedCostItem;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        inStockQuantity: string;
        inTransitStockQuantity: string;
        itemTaxGroup: ItemTaxGroup;
        postingClass: PostingClass;
        storedDimensions: string;
        storedAttributes: string;
        composedDescription: string;
        intacctItem: IntacctItem;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
        analyticalData: AnalyticalData;
    }
    export interface ItemInputExtension {
        name?: string;
        description?: string;
        status?: ItemStatus;
        type?: ItemType;
        isBought?: boolean | string;
        isManufactured?: boolean | string;
        isSold?: boolean | string;
        eanNumber?: string;
        commodityCode?: string;
        stockUnit?: integer | string;
        volumeUnit?: integer | string;
        volume?: decimal | string;
        weightUnit?: integer | string;
        weight?: decimal | string;
        density?: decimal | string;
        image?: BinaryStream;
        allergens?: Partial<ItemAllergenInput>[];
        classifications?: Partial<ItemClassificationsInput>[];
        category?: integer | string;
        isStockManaged?: boolean | string;
        isPhantom?: boolean | string;
        isBomRevisionManaged?: boolean | string;
        bomRevisionSequenceNumber?: integer | string;
        isPotencyManagement?: boolean | string;
        isTraceabilityManagement?: boolean | string;
        supplierPrices?: Partial<ItemSupplierPriceInput>[];
        itemSites?: Partial<ItemSiteInput>[];
        suppliers?: Partial<ItemSupplierInput>[];
        customers?: Partial<ItemCustomerInput>[];
        purchaseUnit?: integer | string;
        purchaseUnitToStockUnitConversion?: decimal | string;
        serialNumberManagement?: SerialNumberManagement;
        serialNumberSequenceNumber?: integer | string;
        serialNumberUsage?: SerialNumberUsage;
        salesUnit?: integer | string;
        salesUnitToStockUnitConversion?: decimal | string;
        minimumSalesQuantity?: decimal | string;
        maximumSalesQuantity?: decimal | string;
        currency?: integer | string;
        basePrice?: decimal | string;
        minimumPrice?: decimal | string;
        customerPrices?: Partial<ItemCustomerPriceInput>[];
        capacity?: integer | string;
        landedCostItem?: LandedCostItemInput;
        itemTaxGroup?: integer | string;
        postingClass?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        intacctItem?: IntacctItemInput;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        id?: string;
        lotManagement?: LotManagement;
        lotSequenceNumber?: integer | string;
        isExpiryManaged?: boolean | string;
        analyticalData?: integer | string;
    }
    export interface ItemBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergenBinding>;
        classifications: ClientCollection<ItemClassificationsBinding>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPriceBinding>;
        itemSites: ClientCollection<ItemSiteBinding>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversion: string;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversion: string;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPriceBinding>;
        capacity: integer;
        landedCostItem: LandedCostItemBinding;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        inStockQuantity: string;
        inTransitStockQuantity: string;
        itemTaxGroup: ItemTaxGroup;
        postingClass: PostingClass;
        storedDimensions: any;
        storedAttributes: any;
        composedDescription: string;
        intacctItem: IntacctItemBinding;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
        analyticalData: AnalyticalData;
    }
    export interface JournalEntryExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        financialSite: Site;
        arInvoice: AccountsReceivableInvoice;
        apInvoice: AccountsPayableInvoice;
        postingStatus: JournalStatus;
        journal: Journal;
        postingDate: string;
        description: string;
        reference: string;
        origin: JournalOrigin;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<JournalEntryLine>;
        intacctDocument: IntacctJournalEntry;
        documentType: string;
    }
    export interface JournalEntryInputExtension {
        number?: string;
        financialSite?: integer | string;
        arInvoice?: integer | string;
        apInvoice?: integer | string;
        postingStatus?: JournalStatus;
        journal?: integer | string;
        postingDate?: string;
        description?: string;
        reference?: string;
        origin?: JournalOrigin;
        lines?: Partial<JournalEntryLineInput>[];
        intacctDocument?: IntacctJournalEntryInput;
        documentType?: string;
    }
    export interface JournalEntryBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        financialSite: Site;
        arInvoice: AccountsReceivableInvoice;
        apInvoice: AccountsPayableInvoice;
        postingStatus: JournalStatus;
        journal: Journal;
        postingDate: string;
        description: string;
        reference: string;
        origin: JournalOrigin;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<JournalEntryLineBinding>;
        intacctDocument: IntacctJournalEntryBinding;
        documentType: string;
    }
    export interface JournalEntryLineDimensionExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        journalEntryLine: JournalEntryLine;
        storedAttributes: string;
        storedDimensions: string;
        analyticalData: AnalyticalData;
        hasAttributesOrDimenionsChanged: boolean;
        transactionCurrency: Currency;
        companyCurrency: Currency;
        financialSiteCurrency: Currency;
        transactionAmount: string;
        companyAmount: string;
        financialSiteAmount: string;
        storedComputedAttributes: string;
        financialSite: Site;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: Customer;
        supplier: Supplier;
        project: Attribute;
        employee: Attribute;
        task: Attribute;
        item: Item;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        intacctDimension: string;
    }
    export interface JournalEntryLineDimensionInputExtension {
        storedAttributes?: string;
        storedDimensions?: string;
        analyticalData?: integer | string;
        hasAttributesOrDimenionsChanged?: boolean | string;
        transactionAmount?: decimal | string;
        companyAmount?: decimal | string;
        financialSiteAmount?: decimal | string;
        storedComputedAttributes?: string;
    }
    export interface JournalEntryLineDimensionBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        journalEntryLine: JournalEntryLine;
        storedAttributes: any;
        storedDimensions: any;
        analyticalData: AnalyticalData;
        hasAttributesOrDimenionsChanged: boolean;
        transactionCurrency: Currency;
        companyCurrency: Currency;
        financialSiteCurrency: Currency;
        transactionAmount: string;
        companyAmount: string;
        financialSiteAmount: string;
        storedComputedAttributes: any;
        financialSite: Site;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: Customer;
        supplier: Supplier;
        project: Attribute;
        employee: Attribute;
        task: Attribute;
        item: Item;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        intacctDimension: any;
    }
    export interface JournalEntryLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        journalEntry: JournalEntry;
        financialSite: Site;
        chartOfAccount: ChartOfAccount;
        tax: Tax;
        taxDate: string;
        taxRate: string;
        deductibleTaxRate: string;
        taxExternalReference: string;
        transactionCurrency: Currency;
        inquiryTransactionCurrency: Currency;
        companyCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        financialSiteCurrency: Currency;
        financialSiteFxRate: string;
        financialSiteFxRateDivisor: string;
        fxRateDate: string;
        rateDescription: string;
        account: Account;
        businessEntity: BusinessEntity;
        sign: Sign;
        numericSign: integer;
        blank: string;
        transactionAmount: string;
        transactionCredit: string;
        transactionDebit: string;
        signedTransactionAmount: string;
        companyAmount: string;
        companyCredit: string;
        companyDebit: string;
        financialSiteAmount: string;
        financialSiteCredit: string;
        financialSiteDebit: string;
        description: string;
        inquiryDescription: string;
        commonReference: string;
        attributesAndDimensions: ClientCollection<JournalEntryLineDimension>;
        dueDate: string;
        validationDate: string;
        accountingStagingLines: ClientCollection<JournalEntryLineStaging>;
        financialSiteAttribute: Site;
        businessSiteAttribute: Site;
        stockSiteAttribute: Site;
        manufacturingSiteAttribute: Site;
        customerAttribute: Customer;
        supplierAttribute: Supplier;
        projectAttribute: Attribute;
        employeeAttribute: Attribute;
        taskAttribute: Attribute;
        itemAttribute: Item;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        journalEntryTypeLine: JournalEntryTypeLine;
        contraJournalEntryLine: JournalEntryLine;
        contraAccount: string;
        baseTax: BaseTax;
        datevContraAccountId: integer;
        datevPostingKey: integer;
        datevTransactionAmount: string;
        datevCompanyAmount: string;
        supplierDocumentNumber: string;
        datevBusinessEntityTaxIdNumber: string;
        isBalanceLine: boolean;
        intacctDocumentLine: IntacctJournalEntryLine;
    }
    export interface JournalEntryLineInputExtension {
        financialSite?: integer | string;
        chartOfAccount?: integer | string;
        tax?: integer | string;
        taxDate?: string;
        taxRate?: decimal | string;
        deductibleTaxRate?: decimal | string;
        transactionCurrency?: integer | string;
        companyCurrency?: integer | string;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        financialSiteCurrency?: integer | string;
        financialSiteFxRate?: decimal | string;
        financialSiteFxRateDivisor?: decimal | string;
        fxRateDate?: string;
        account?: integer | string;
        businessEntity?: integer | string;
        sign?: Sign;
        transactionAmount?: decimal | string;
        companyAmount?: decimal | string;
        financialSiteAmount?: decimal | string;
        description?: string;
        commonReference?: string;
        attributesAndDimensions?: Partial<JournalEntryLineDimensionInput>[];
        accountingStagingLines?: Partial<JournalEntryLineStagingInput>[];
        journalEntryTypeLine?: integer | string;
        contraJournalEntryLine?: integer | string;
        baseTax?: integer | string;
        isBalanceLine?: boolean | string;
        intacctDocumentLine?: IntacctJournalEntryLineInput;
    }
    export interface JournalEntryLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        journalEntry: JournalEntry;
        financialSite: Site;
        chartOfAccount: ChartOfAccount;
        tax: Tax;
        taxDate: string;
        taxRate: string;
        deductibleTaxRate: string;
        taxExternalReference: string;
        transactionCurrency: Currency;
        inquiryTransactionCurrency: Currency;
        companyCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        financialSiteCurrency: Currency;
        financialSiteFxRate: string;
        financialSiteFxRateDivisor: string;
        fxRateDate: string;
        rateDescription: string;
        account: Account;
        businessEntity: BusinessEntity;
        sign: Sign;
        numericSign: integer;
        blank: string;
        transactionAmount: string;
        transactionCredit: string;
        transactionDebit: string;
        signedTransactionAmount: string;
        companyAmount: string;
        companyCredit: string;
        companyDebit: string;
        financialSiteAmount: string;
        financialSiteCredit: string;
        financialSiteDebit: string;
        description: string;
        inquiryDescription: string;
        commonReference: string;
        attributesAndDimensions: ClientCollection<JournalEntryLineDimensionBinding>;
        dueDate: string;
        validationDate: string;
        accountingStagingLines: ClientCollection<JournalEntryLineStagingBinding>;
        financialSiteAttribute: Site;
        businessSiteAttribute: Site;
        stockSiteAttribute: Site;
        manufacturingSiteAttribute: Site;
        customerAttribute: Customer;
        supplierAttribute: Supplier;
        projectAttribute: Attribute;
        employeeAttribute: Attribute;
        taskAttribute: Attribute;
        itemAttribute: Item;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        journalEntryTypeLine: JournalEntryTypeLine;
        contraJournalEntryLine: JournalEntryLine;
        contraAccount: string;
        baseTax: BaseTax;
        datevContraAccountId: integer;
        datevPostingKey: integer;
        datevTransactionAmount: string;
        datevCompanyAmount: string;
        supplierDocumentNumber: string;
        datevBusinessEntityTaxIdNumber: string;
        isBalanceLine: boolean;
        intacctDocumentLine: IntacctJournalEntryLineBinding;
    }
    export interface JournalExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        legislation: Legislation;
        name: string;
        primaryDocumentType: string;
        secondaryDocumentType: string;
        isActive: boolean;
        sequence: SequenceNumber;
        isSubjectToGlTaxExcludedAmount: boolean;
        taxImpact: boolean;
        intacctId: string;
        recordNo: integer;
        statusIntacct: string;
    }
    export interface JournalInputExtension {
        _vendor?: integer | string;
        id?: string;
        legislation?: integer | string;
        name?: string;
        primaryDocumentType?: string;
        secondaryDocumentType?: string;
        isActive?: boolean | string;
        sequence?: integer | string;
        taxImpact?: boolean | string;
        intacctId?: string;
        recordNo?: integer | string;
    }
    export interface JournalBindingExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        legislation: Legislation;
        name: string;
        primaryDocumentType: string;
        secondaryDocumentType: string;
        isActive: boolean;
        sequence: SequenceNumber;
        isSubjectToGlTaxExcludedAmount: boolean;
        taxImpact: boolean;
        intacctId: string;
        recordNo: integer;
        statusIntacct: string;
    }
    export interface PaymentTermExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        name: string;
        businessEntityType: BusinessEntityType;
        description: string;
        dueDateType: DueDateType;
        days: integer;
        discountFrom: DueDateType;
        discountDate: integer;
        discountType: PaymentTermDiscountOrPenaltyType;
        discountAmount: string;
        penaltyType: PaymentTermDiscountOrPenaltyType;
        penaltyAmount: string;
        intacctId: string;
        recordNo: integer;
        statusIntacct: string;
        dueFromIntacct: string;
        discountFromIntacct: string;
        discountTypeIntacct: string;
        penaltyTypeIntacct: string;
        isIntacct: boolean;
    }
    export interface PaymentTermInputExtension {
        _vendor?: integer | string;
        id?: string;
        isActive?: boolean | string;
        name?: string;
        businessEntityType?: BusinessEntityType;
        description?: string;
        dueDateType?: DueDateType;
        days?: integer | string;
        discountFrom?: DueDateType;
        discountDate?: integer | string;
        discountType?: PaymentTermDiscountOrPenaltyType;
        discountAmount?: decimal | string;
        penaltyType?: PaymentTermDiscountOrPenaltyType;
        penaltyAmount?: decimal | string;
        intacctId?: string;
        recordNo?: integer | string;
        isIntacct?: boolean | string;
    }
    export interface PaymentTermBindingExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        name: string;
        businessEntityType: BusinessEntityType;
        description: string;
        dueDateType: DueDateType;
        days: integer;
        discountFrom: DueDateType;
        discountDate: integer;
        discountType: PaymentTermDiscountOrPenaltyType;
        discountAmount: string;
        penaltyType: PaymentTermDiscountOrPenaltyType;
        penaltyAmount: string;
        intacctId: string;
        recordNo: integer;
        statusIntacct: string;
        dueFromIntacct: string;
        discountFromIntacct: string;
        discountTypeIntacct: string;
        penaltyTypeIntacct: string;
        isIntacct: boolean;
    }
    export interface SupplierExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        supplierType: SupplierType;
        standardIndustrialClassification: StandardIndustrialClassification;
        paymentMethod: string;
        parent: Supplier;
        incoterm: Incoterm;
        certificates: ClientCollection<SupplierCertificate>;
        items: ClientCollection<ItemSupplier>;
        itemPrices: ClientCollection<ItemSupplierPrice>;
        billBySupplier: Supplier;
        billByAddress: BusinessEntityAddress;
        payToSupplier: Supplier;
        payToAddress: BusinessEntityAddress;
        returnToSupplier: Supplier;
        returnToAddress: BusinessEntityAddress;
        deliveryMode: DeliveryMode;
        datevId: integer;
        postingClass: PostingClass;
        storedDimensions: string;
        storedAttributes: string;
        intacctSupplier: IntacctSupplier;
        paymentMethodSelect: PaymentMethod;
        _attachments: ClientCollection<AttachmentAssociation>;
        analyticalData: AnalyticalData;
    }
    export interface SupplierInputExtension {
        isActive?: boolean | string;
        businessEntity?: BusinessEntityInput;
        primaryAddress?: integer | string;
        internalNote?: TextStream;
        paymentTerm?: integer | string;
        minimumOrderAmount?: decimal | string;
        category?: integer | string;
        supplierType?: SupplierType;
        standardIndustrialClassification?: integer | string;
        paymentMethod?: string;
        parent?: integer | string;
        incoterm?: integer | string;
        certificates?: Partial<SupplierCertificateInput>[];
        items?: Partial<ItemSupplierInput>[];
        billBySupplier?: integer | string;
        billByAddress?: integer | string;
        payToSupplier?: integer | string;
        payToAddress?: integer | string;
        returnToSupplier?: integer | string;
        returnToAddress?: integer | string;
        deliveryMode?: integer | string;
        datevId?: integer | string;
        postingClass?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        intacctSupplier?: IntacctSupplierInput;
        paymentMethodSelect?: PaymentMethod;
        _attachments?: Partial<AttachmentAssociationInput>[];
        analyticalData?: integer | string;
    }
    export interface SupplierBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        businessEntity: BusinessEntity;
        primaryAddress: BusinessEntityAddress;
        internalNote: TextStream;
        paymentTerm: PaymentTerm;
        minimumOrderAmount: string;
        category: CustomerSupplierCategory;
        id: string;
        name: string;
        country: Country;
        currency: Currency;
        legalEntity: LegalEntity;
        taxIdNumber: string;
        siret: string;
        image: BinaryStream;
        supplierType: SupplierType;
        standardIndustrialClassification: StandardIndustrialClassification;
        paymentMethod: string;
        parent: Supplier;
        incoterm: Incoterm;
        certificates: ClientCollection<SupplierCertificateBinding>;
        items: ClientCollection<ItemSupplierBinding>;
        itemPrices: ClientCollection<ItemSupplierPrice>;
        billBySupplier: Supplier;
        billByAddress: BusinessEntityAddress;
        payToSupplier: Supplier;
        payToAddress: BusinessEntityAddress;
        returnToSupplier: Supplier;
        returnToAddress: BusinessEntityAddress;
        deliveryMode: DeliveryMode;
        datevId: integer;
        postingClass: PostingClass;
        storedDimensions: any;
        storedAttributes: any;
        intacctSupplier: IntacctSupplierBinding;
        paymentMethodSelect: PaymentMethod;
        _attachments: ClientCollection<AttachmentAssociation>;
        analyticalData: AnalyticalData;
    }
    export interface TaxExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        name: string;
        primaryExternalReference: string;
        secondaryExternalReference: string;
        taxCategory: TaxCategory;
        country: Country;
        isReverseCharge: boolean;
        jurisdictionName: string;
        legalMention: string;
        taxValues: ClientCollection<TaxValue>;
        isUsed: boolean;
        type: TaxType;
        postingClass: PostingClass;
        postingKey: integer;
        isIntacct: boolean;
        intacctId: string;
        recordNo: integer;
        statusIntacct: string;
        intacctTaxType: string;
        intacctAccount: string;
        isIntacctReverseCharge: boolean;
        rate: string;
        intacctSecondaryExternalReference: Tax;
        intacctSolutionId: string;
        legislation: Legislation;
    }
    export interface TaxInputExtension {
        _vendor?: integer | string;
        id?: string;
        isActive?: boolean | string;
        name?: string;
        primaryExternalReference?: string;
        secondaryExternalReference?: string;
        taxCategory?: integer | string;
        country?: integer | string;
        isReverseCharge?: boolean | string;
        jurisdictionName?: string;
        legalMention?: string;
        taxValues?: Partial<TaxValueInput>[];
        type?: TaxType;
        postingClass?: integer | string;
        postingKey?: integer | string;
        isIntacct?: boolean | string;
        intacctId?: string;
        recordNo?: integer | string;
        intacctTaxType?: string;
        intacctAccount?: string;
        isIntacctReverseCharge?: boolean | string;
        rate?: decimal | string;
        isUpdateFromIntacct?: boolean | string;
        intacctSecondaryExternalReference?: integer | string;
        intacctSolutionId?: string;
        legislation?: integer | string;
    }
    export interface TaxBindingExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        name: string;
        primaryExternalReference: string;
        secondaryExternalReference: string;
        taxCategory: TaxCategory;
        country: Country;
        isReverseCharge: boolean;
        jurisdictionName: string;
        legalMention: string;
        taxValues: ClientCollection<TaxValueBinding>;
        isUsed: boolean;
        type: TaxType;
        postingClass: PostingClass;
        postingKey: integer;
        isIntacct: boolean;
        intacctId: string;
        recordNo: integer;
        statusIntacct: string;
        intacctTaxType: string;
        intacctAccount: string;
        isIntacctReverseCharge: boolean;
        rate: string;
        isUpdateFromIntacct: boolean;
        intacctSecondaryExternalReference: Tax;
        intacctSolutionId: string;
        legislation: Legislation;
    }
    export interface TaxExtension$Lookups {
        intacctSecondaryExternalReference: QueryOperation<Tax>;
    }
    export interface TaxExtension$Operations {
        lookups(dataOrId: string | { data: TaxInput }): TaxExtension$Lookups;
    }
    export interface Package {
        '@sage/xtrem-intacct-finance/FinanceListener': FinanceListener$Operations;
        '@sage/xtrem-intacct-finance/IntacctAccountsPayableInvoiceLine': IntacctAccountsPayableInvoiceLine$Operations;
        '@sage/xtrem-intacct-finance/IntacctAccountsReceivableAdvanceLine': IntacctAccountsReceivableAdvanceLine$Operations;
        '@sage/xtrem-intacct-finance/IntacctAccountsReceivableInvoiceLine': IntacctAccountsReceivableInvoiceLine$Operations;
        '@sage/xtrem-intacct-finance/IntacctAccountsReceivablePaymentLine': IntacctAccountsReceivablePaymentLine$Operations;
        '@sage/xtrem-intacct-finance/IntacctBankAccountMatching': IntacctBankAccountMatching$Operations;
        '@sage/xtrem-intacct-finance/IntacctImportSession': IntacctImportSession$Operations;
        '@sage/xtrem-intacct-finance/IntacctJournalEntryLine': IntacctJournalEntryLine$Operations;
        '@sage/xtrem-intacct-finance/IntacctListener': IntacctListener$Operations;
        '@sage/xtrem-intacct-finance/MapLine': MapLine$Operations;
        '@sage/xtrem-intacct-finance/MapProperty': MapProperty$Operations;
        '@sage/xtrem-intacct-finance/IntacctCashBookManagement': IntacctCashBookManagement$Operations;
        '@sage/xtrem-intacct-finance/IntacctBankAccountTransactionFeed': IntacctBankAccountTransactionFeed$Operations;
        '@sage/xtrem-intacct-finance/IntacctBankAccountTransactionFeedLine': IntacctBankAccountTransactionFeedLine$Operations;
        '@sage/xtrem-intacct-finance/IntacctBankAccountTransactionFeedLineDimension': IntacctBankAccountTransactionFeedLineDimension$Operations;
        '@sage/xtrem-intacct-finance/IntacctMap': IntacctMap$Operations;
        '@sage/xtrem-intacct-finance/IntacctSynchronizationState': IntacctSynchronizationState$Operations;
        '@sage/xtrem-intacct-finance/IntacctContact': IntacctContact$Operations;
        '@sage/xtrem-intacct-finance/IntacctItem': IntacctItem$Operations;
        '@sage/xtrem-intacct-finance/IntacctAccountsPayableInvoice': IntacctAccountsPayableInvoice$Operations;
        '@sage/xtrem-intacct-finance/IntacctAccountsReceivableAdvance': IntacctAccountsReceivableAdvance$Operations;
        '@sage/xtrem-intacct-finance/IntacctAccountsReceivableInvoice': IntacctAccountsReceivableInvoice$Operations;
        '@sage/xtrem-intacct-finance/IntacctAccountsReceivablePayment': IntacctAccountsReceivablePayment$Operations;
        '@sage/xtrem-intacct-finance/IntacctCustomer': IntacctCustomer$Operations;
        '@sage/xtrem-intacct-finance/IntacctJournalEntry': IntacctJournalEntry$Operations;
        '@sage/xtrem-intacct-finance/IntacctSupplier': IntacctSupplier$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremFinance$Package,
            SageXtremFinanceData$Package,
            SageXtremImportExport$Package,
            SageXtremIntacct$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-intacct-finance-api' {
    export type * from '@sage/xtrem-intacct-finance-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-intacct-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type {
        AccountBindingExtension,
        AccountExtension,
        AccountInputExtension,
        AttributeBindingExtension,
        AttributeExtension,
        AttributeExtension$AsyncOperations,
        AttributeExtension$Operations,
        AttributeInputExtension,
        BankAccountBindingExtension,
        BankAccountExtension,
        BankAccountInputExtension,
        DimensionBindingExtension,
        DimensionExtension,
        DimensionInputExtension,
        DimensionTypeBindingExtension,
        DimensionTypeExtension,
        DimensionTypeInputExtension,
        JournalBindingExtension,
        JournalExtension,
        JournalInputExtension,
    } from '@sage/xtrem-intacct-finance-api';
    export interface Account extends AccountExtension {}
    export interface AccountBinding extends AccountBindingExtension {}
    export interface AccountInput extends AccountInputExtension {}
    export interface Attribute extends AttributeExtension {}
    export interface AttributeBinding extends AttributeBindingExtension {}
    export interface AttributeInput extends AttributeInputExtension {}
    export interface Attribute$AsyncOperations extends AttributeExtension$AsyncOperations {}
    export interface Attribute$Operations extends AttributeExtension$Operations {}
    export interface BankAccount extends BankAccountExtension {}
    export interface BankAccountBinding extends BankAccountBindingExtension {}
    export interface BankAccountInput extends BankAccountInputExtension {}
    export interface Dimension extends DimensionExtension {}
    export interface DimensionBinding extends DimensionBindingExtension {}
    export interface DimensionInput extends DimensionInputExtension {}
    export interface DimensionType extends DimensionTypeExtension {}
    export interface DimensionTypeBinding extends DimensionTypeBindingExtension {}
    export interface DimensionTypeInput extends DimensionTypeInputExtension {}
    export interface Journal extends JournalExtension {}
    export interface JournalBinding extends JournalBindingExtension {}
    export interface JournalInput extends JournalInputExtension {}
}
declare module '@sage/xtrem-finance-api-partial' {
    import type {
        AccountsPayableInvoiceBindingExtension,
        AccountsPayableInvoiceExtension,
        AccountsPayableInvoiceInputExtension,
        AccountsPayableInvoiceLineBindingExtension,
        AccountsPayableInvoiceLineExtension,
        AccountsPayableInvoiceLineInputExtension,
        AccountsReceivableAdvanceBindingExtension,
        AccountsReceivableAdvanceExtension,
        AccountsReceivableAdvanceExtension$Mutations,
        AccountsReceivableAdvanceExtension$Operations,
        AccountsReceivableAdvanceInputExtension,
        AccountsReceivableAdvanceLineBindingExtension,
        AccountsReceivableAdvanceLineExtension,
        AccountsReceivableAdvanceLineInputExtension,
        AccountsReceivableInvoiceBindingExtension,
        AccountsReceivableInvoiceExtension,
        AccountsReceivableInvoiceExtension$Mutations,
        AccountsReceivableInvoiceExtension$Operations,
        AccountsReceivableInvoiceInputExtension,
        AccountsReceivableInvoiceLineBindingExtension,
        AccountsReceivableInvoiceLineExtension,
        AccountsReceivableInvoiceLineInputExtension,
        AccountsReceivablePaymentBindingExtension,
        AccountsReceivablePaymentExtension,
        AccountsReceivablePaymentExtension$Mutations,
        AccountsReceivablePaymentExtension$Operations,
        AccountsReceivablePaymentInputExtension,
        AccountsReceivablePaymentLineBindingExtension,
        AccountsReceivablePaymentLineExtension,
        AccountsReceivablePaymentLineInputExtension,
        JournalEntryBindingExtension,
        JournalEntryExtension,
        JournalEntryInputExtension,
        JournalEntryLineBindingExtension,
        JournalEntryLineDimensionBindingExtension,
        JournalEntryLineDimensionExtension,
        JournalEntryLineDimensionInputExtension,
        JournalEntryLineExtension,
        JournalEntryLineInputExtension,
    } from '@sage/xtrem-intacct-finance-api';
    export interface AccountsPayableInvoice extends AccountsPayableInvoiceExtension {}
    export interface AccountsPayableInvoiceBinding extends AccountsPayableInvoiceBindingExtension {}
    export interface AccountsPayableInvoiceInput extends AccountsPayableInvoiceInputExtension {}
    export interface AccountsPayableInvoiceLine extends AccountsPayableInvoiceLineExtension {}
    export interface AccountsPayableInvoiceLineBinding extends AccountsPayableInvoiceLineBindingExtension {}
    export interface AccountsPayableInvoiceLineInput extends AccountsPayableInvoiceLineInputExtension {}
    export interface AccountsReceivableAdvance extends AccountsReceivableAdvanceExtension {}
    export interface AccountsReceivableAdvanceBinding extends AccountsReceivableAdvanceBindingExtension {}
    export interface AccountsReceivableAdvanceInput extends AccountsReceivableAdvanceInputExtension {}
    export interface AccountsReceivableAdvance$Mutations extends AccountsReceivableAdvanceExtension$Mutations {}
    export interface AccountsReceivableAdvance$Operations extends AccountsReceivableAdvanceExtension$Operations {}
    export interface AccountsReceivableAdvanceLine extends AccountsReceivableAdvanceLineExtension {}
    export interface AccountsReceivableAdvanceLineBinding extends AccountsReceivableAdvanceLineBindingExtension {}
    export interface AccountsReceivableAdvanceLineInput extends AccountsReceivableAdvanceLineInputExtension {}
    export interface AccountsReceivableInvoice extends AccountsReceivableInvoiceExtension {}
    export interface AccountsReceivableInvoiceBinding extends AccountsReceivableInvoiceBindingExtension {}
    export interface AccountsReceivableInvoiceInput extends AccountsReceivableInvoiceInputExtension {}
    export interface AccountsReceivableInvoice$Mutations extends AccountsReceivableInvoiceExtension$Mutations {}
    export interface AccountsReceivableInvoice$Operations extends AccountsReceivableInvoiceExtension$Operations {}
    export interface AccountsReceivableInvoiceLine extends AccountsReceivableInvoiceLineExtension {}
    export interface AccountsReceivableInvoiceLineBinding extends AccountsReceivableInvoiceLineBindingExtension {}
    export interface AccountsReceivableInvoiceLineInput extends AccountsReceivableInvoiceLineInputExtension {}
    export interface AccountsReceivablePayment extends AccountsReceivablePaymentExtension {}
    export interface AccountsReceivablePaymentBinding extends AccountsReceivablePaymentBindingExtension {}
    export interface AccountsReceivablePaymentInput extends AccountsReceivablePaymentInputExtension {}
    export interface AccountsReceivablePayment$Mutations extends AccountsReceivablePaymentExtension$Mutations {}
    export interface AccountsReceivablePayment$Operations extends AccountsReceivablePaymentExtension$Operations {}
    export interface AccountsReceivablePaymentLine extends AccountsReceivablePaymentLineExtension {}
    export interface AccountsReceivablePaymentLineBinding extends AccountsReceivablePaymentLineBindingExtension {}
    export interface AccountsReceivablePaymentLineInput extends AccountsReceivablePaymentLineInputExtension {}
    export interface JournalEntry extends JournalEntryExtension {}
    export interface JournalEntryBinding extends JournalEntryBindingExtension {}
    export interface JournalEntryInput extends JournalEntryInputExtension {}
    export interface JournalEntryLineDimension extends JournalEntryLineDimensionExtension {}
    export interface JournalEntryLineDimensionBinding extends JournalEntryLineDimensionBindingExtension {}
    export interface JournalEntryLineDimensionInput extends JournalEntryLineDimensionInputExtension {}
    export interface JournalEntryLine extends JournalEntryLineExtension {}
    export interface JournalEntryLineBinding extends JournalEntryLineBindingExtension {}
    export interface JournalEntryLineInput extends JournalEntryLineInputExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type {
        BusinessEntityAddressBindingExtension,
        BusinessEntityAddressExtension,
        BusinessEntityAddressInputExtension,
        BusinessEntityBindingExtension,
        BusinessEntityContactBindingExtension,
        BusinessEntityContactExtension,
        BusinessEntityContactInputExtension,
        BusinessEntityExtension,
        BusinessEntityInputExtension,
        CustomerBindingExtension,
        CustomerExtension,
        CustomerInputExtension,
        ItemBindingExtension,
        ItemExtension,
        ItemInputExtension,
        PaymentTermBindingExtension,
        PaymentTermExtension,
        PaymentTermInputExtension,
        SupplierBindingExtension,
        SupplierExtension,
        SupplierInputExtension,
    } from '@sage/xtrem-intacct-finance-api';
    export interface BusinessEntityContact extends BusinessEntityContactExtension {}
    export interface BusinessEntityContactBinding extends BusinessEntityContactBindingExtension {}
    export interface BusinessEntityContactInput extends BusinessEntityContactInputExtension {}
    export interface BusinessEntityAddress extends BusinessEntityAddressExtension {}
    export interface BusinessEntityAddressBinding extends BusinessEntityAddressBindingExtension {}
    export interface BusinessEntityAddressInput extends BusinessEntityAddressInputExtension {}
    export interface BusinessEntity extends BusinessEntityExtension {}
    export interface BusinessEntityBinding extends BusinessEntityBindingExtension {}
    export interface BusinessEntityInput extends BusinessEntityInputExtension {}
    export interface Customer extends CustomerExtension {}
    export interface CustomerBinding extends CustomerBindingExtension {}
    export interface CustomerInput extends CustomerInputExtension {}
    export interface Item extends ItemExtension {}
    export interface ItemBinding extends ItemBindingExtension {}
    export interface ItemInput extends ItemInputExtension {}
    export interface PaymentTerm extends PaymentTermExtension {}
    export interface PaymentTermBinding extends PaymentTermBindingExtension {}
    export interface PaymentTermInput extends PaymentTermInputExtension {}
    export interface Supplier extends SupplierExtension {}
    export interface SupplierBinding extends SupplierBindingExtension {}
    export interface SupplierInput extends SupplierInputExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type {
        CompanyBindingExtension,
        CompanyExtension,
        CompanyExtension$AsyncOperations,
        CompanyExtension$Operations,
        CompanyInputExtension,
    } from '@sage/xtrem-intacct-finance-api';
    export interface Company extends CompanyExtension {}
    export interface CompanyBinding extends CompanyBindingExtension {}
    export interface CompanyInput extends CompanyInputExtension {}
    export interface Company$AsyncOperations extends CompanyExtension$AsyncOperations {}
    export interface Company$Operations extends CompanyExtension$Operations {}
}
declare module '@sage/xtrem-intacct-api-partial' {
    import type {
        IntacctBindingExtension,
        IntacctExtension,
        IntacctExtension$Mutations,
        IntacctExtension$Operations,
        IntacctInputExtension,
    } from '@sage/xtrem-intacct-finance-api';
    export interface Intacct extends IntacctExtension {}
    export interface IntacctBinding extends IntacctBindingExtension {}
    export interface IntacctInput extends IntacctInputExtension {}
    export interface Intacct$Mutations extends IntacctExtension$Mutations {}
    export interface Intacct$Operations extends IntacctExtension$Operations {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type {
        TaxBindingExtension,
        TaxExtension,
        TaxExtension$Lookups,
        TaxExtension$Operations,
        TaxInputExtension,
    } from '@sage/xtrem-intacct-finance-api';
    export interface Tax extends TaxExtension {}
    export interface TaxBinding extends TaxBindingExtension {}
    export interface TaxInput extends TaxInputExtension {}
    export interface Tax$Lookups extends TaxExtension$Lookups {}
    export interface Tax$Operations extends TaxExtension$Operations {}
}
