import type { date, dateRange, decimal, integer } from '@sage/xtrem-core';
import type * as xtremIntacct from '@sage/xtrem-intacct';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremIntacctFinance from '../index';

export interface SearchIntacctDataParameters {
    filters: xtremIntacctFinance.interfaces.BankAccountMatching.AccountMatchingQueryFilters;
    intacctImportSession: xtremIntacctFinance.nodes.IntacctImportSession;
    isNewLines: boolean;
}

export interface ImportSessionCounter {
    toCreate: integer;
    alreadyCreated: integer;
    matched: integer;
    unMatch: integer;
    totalAmount: decimal;
    totalDeposit: decimal;
    totalWithdrawal: decimal;
}

export interface AccountMatchingQueryFilters {
    bankAccount: string;
    currencyId: string;
    transactionType?: xtremIntacctFinance.enums.IntacctRecordTransactionType;
    startFromTo?: dateRange; // dateRange; TODO : use this instead of dateFrom / dateTo
    dateFrom: date;
    dateTo: date;
    status: string;
}

/** to query  */
export type IntacctDocumentType = 'ApInvoice' | 'ArInvoice' | 'JournalEntry';

export interface IntacctArInvoice extends IntacctDocument {
    recordNo: number;
    invoiceNo: string;
    customerName: string;
    customerId: string;
    referenceNumber: string;
    dueDate: date;
    termName: string;
    term?: xtremMasterData.nodes.PaymentTerm;
    currencyId: string;
    currency?: xtremMasterData.nodes.Currency;
    /** TRX_TOTALPAID  */ totalPaid: number;
    /** TRX_TOTALDUE */ totalDue: number;
    /** TRX_TOTALENTERED */ totalEntered: number;
    /** TOTALENTERED */ companyTotalEntered: number;
    /** TOTALDUE */ companyTotalDue: number;
    /** TOTALPAID */ companyTotalPaid: number;
}

/** recordNo, description , documentType location journal */
export interface IntacctJournalEntry extends IntacctDocument {
    journal: string;
    account: string;
}

export interface IntacctDocument {
    /** RECORDNO */ recordNo: number;
    /** BATCHNO */ batchNo: number;
    /** DESCRIPTION */ description: string;
    documentType?: IntacctDocumentType;
    location: string;
    date?: date;
    amount: decimal;
    url: string;
    entityId: string;
}

/** date  description accounts amount  */
export interface IntacctDocumentParameters {
    date?: date;
    description?: string;
    account?: string;
    accounts?: string[];
    amount?: decimal;
    payee?: string;
    customerId?: string;
    currencyId?: string;
    orderField?: xtremIntacct.interfaces.OrderFields;
    /** entity level from bankAccount */
    megaEntityId?: string;
    /** from bankAccount */
    location?: string;
    documentNos?: string[];
}
