import type { NodeQueryFilter } from '@sage/xtrem-core';
import type * as xtremIntacct from '@sage/xtrem-intacct';

export interface FieldName {
    idFieldName: string;
    nameFieldName: string;
    descriptionFieldName: string;
}

export interface FieldXtreemName {
    idFieldXtremName: string;
    nameFieldXtremName: string | undefined;
    descriptionFieldXtremName: string | undefined;
}

export interface XtreemDataMap {
    xtremSysId: string;
    xtremID: string;
    xtremName: string;
    xtremDescription: string;
    integrationStatus: string;
}

export interface IntacctToXtreemRequest {
    intacctName: string;
    xtremSysId?: string;
    isThrowingDiagnose?: boolean;
    intacctIdValue?: string;
    filter?: NodeQueryFilter<xtremIntacct.nodes.IntacctNode>;
}
