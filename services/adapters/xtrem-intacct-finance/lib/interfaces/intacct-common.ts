import type { datetime } from '@sage/xtrem-core';
import type * as xtremIntacct from '@sage/xtrem-intacct';

export interface IntacctData {
    id: string;
    name: string;
    description: string;
    whenModified?: datetime;
    whenCreated?: datetime;
    url?: string;
    megaEntityId?: string;
}

export interface IntacctXtremData extends IntacctData {
    _id: string;
    isLinked: boolean;
    xtremSysId: string;
    xtremID: string;
    xtremName: string;
    xtremDescription: string;
    integrationStatus: string;
}

/**
 * Todo : delete
 * Not used ?
 */
export interface SmartEvent {
    recordNo: string;
    smartLinkId: string;
    topic: string;
    ownerObject: string;
    timestamp: datetime;
    userid: string;
    objectKey: string;
}

export interface ListenerReturn {
    status: xtremIntacct.enums.ListenerStatusEnum;
    message: string[];
    receivedRequest: string | object;
}
