import type * as xtremIntacct from '@sage/xtrem-intacct';

export interface FinanceDocumentQuery extends xtremIntacct.interfaces.IntacctKey {
    RECORD_URL: string;
    NUMBER: string;
}
export interface ApArOpenItemsQuery {
    TRX_TOTALPAID: number;
    TOTALPAID: number;
}

export type ObjectName = 'ARINVOICE' | 'APBILL';

export interface IntacctFinanceDocumentAmounts {
    transactionAmountPaid: number;
    companyAmountPaid: number;
    financialSiteAmountPaid: number;
}
