import type { datetime } from '@sage/xtrem-core';

export interface IntegrationState {
    intacctMapId: string;
    node: string;
    sysId: number;
    description: string;
    integrationState: string;
    url: string;
    lastIntegrationDate: datetime;
    errorMessage?: string;
}

export interface IntegrationStateFilter {
    nodes?: string[];
    status?: string[];
    filter?: string;
    first?: number;
}

export interface IntegrationDifference {
    name: string;
    xtreemValue: string;
    intacctValue: string;
}

export interface CompanyOnHoldStatus {
    application: string;
    preference: string;
    prefvalue: string;
}
