import type * as xtremCommunication from '@sage/xtrem-communication';
import type { Diagnose } from '@sage/xtrem-core';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';

export interface SyncState {
    node: string;
    _id: number | string;
    sysId: string;
    state: xtremCommunication.enums.IntegrationState;
    intacctId: string;
    continueOnError?: boolean;
    diagnoses?: Diagnose[];
}

export interface ContactList {
    CONTACT_LIST_INFO: {
        CATEGORYNAME: string;
        contact: { name: SyncState };
    };
}

export interface IntacctSynchronizationLine extends xtremSynchronization.interfaces.ThirdPartySynchronizationNodeLine {}
