import type {
    IntacctBankAccountTransactionFeed,
    IntacctBankAccountTransactionFeedArPayment,
    IntacctBankAccountTransactionFeedLine,
    MatchedArInvoices,
} from './interfaces';

enum FeedRecordStatusEnum {
    /** intacct status is matched */ matched,
    unmatched,
    /** Looking on intacct */ lookingForExistingJournalEntries,
    /** we Found one journal entry on intacct */ journalEntryFound,
    /** we Found multiple journal entry on intacct */ journalEntriesFound,
    /** matching rule as been apply */ draftMatch,
    /** the JE is ready to create */ readyForPosting,
    /** deprecated */ journalEntryPosted,
    /** the JE is being created in SDMO */ journalEntryGenerationInProgress,
    /** The JE creation ended in error in SDMO */ journalEntryGenerationError,
    /** The JE has been created in SDMO */ journalEntryGenerated,
    /** The JE is being sent to Intacct Fin. */ journalEntryPostingInProgress,
    /** the JE has not been sent to Intacct Fin. due to an error. */ journalEntryPostingError,
    /** we found at least on ar invoice that we were able to match with the bank feed */ draftArMatch,
    /** user selected ar invoices to match for the total amount of the bank feed */ readyForArPaymentGeneration,
    arPaymentGenerationInProgress,
    arPaymentPostingInProgress,
    arPaymentPosted,
    readyForArAdvanceGeneration,
    arAdvancePosted,
    /** user selected ar invoices to match, but not for the total amount of the bank feed */ partialArMatch,
    draftArAdvance,
    arPaymentGenerated,
    arAdvanceGenerated,
    arPaymentPostingError,
    arAdvancePostingInProgress,
    arAdvancePostingError,
}
type FeedRecordStatus = keyof typeof FeedRecordStatusEnum;

function getCreatedPaymentStatus(
    intacctBankAccountTransactionFeed: IntacctBankAccountTransactionFeedArPayment,
): FeedRecordStatus {
    switch (intacctBankAccountTransactionFeed.arMatch.arPaymentType) {
        case 'payment':
            if (intacctBankAccountTransactionFeed.financeIntegrationStatus === 'generated') {
                return 'arPaymentGenerated';
            }
            if (intacctBankAccountTransactionFeed.financeIntegrationStatus === 'postingInProgress') {
                return 'arPaymentPostingInProgress';
            }
            if (intacctBankAccountTransactionFeed.financeIntegrationStatus === 'postingError') {
                return 'arPaymentPostingError';
            }
            if (intacctBankAccountTransactionFeed.financeIntegrationStatus === 'posted') {
                return 'arPaymentPosted';
            }
            break;
        case 'advance':
            if (intacctBankAccountTransactionFeed.financeIntegrationStatus === 'generated') {
                return 'arAdvanceGenerated';
            }
            if (intacctBankAccountTransactionFeed.financeIntegrationStatus === 'postingInProgress') {
                return 'arAdvancePostingInProgress';
            }
            if (intacctBankAccountTransactionFeed.financeIntegrationStatus === 'postingError') {
                return 'arAdvancePostingError';
            }
            if (intacctBankAccountTransactionFeed.financeIntegrationStatus === 'posted') {
                return 'arAdvancePosted';
            }
            break;
        default:
            break;
    }
    return 'unmatched';
}

export function getFeedRecordStatusForArPayments(
    intacctBankAccountTransactionFeed: IntacctBankAccountTransactionFeedArPayment,
): FeedRecordStatus {
    // AR Payment status - begin
    // we check if we have matched ar invoices and the matched amount equals the bank amount to match

    const feedRecordStatus: FeedRecordStatus = getCreatedPaymentStatus(intacctBankAccountTransactionFeed);

    if (feedRecordStatus !== 'unmatched') {
        return feedRecordStatus;
    }

    if (
        !intacctBankAccountTransactionFeed.arMatch.arPaymentType ||
        intacctBankAccountTransactionFeed.arMatch.arPaymentType === 'payment'
    ) {
        const jsonArInvoicesValue = intacctBankAccountTransactionFeed.jsonArInvoices;
        if (jsonArInvoicesValue && Object.keys(jsonArInvoicesValue).length) {
            const recordMatchedArInvoices = (jsonArInvoicesValue as unknown as MatchedArInvoices).matchedArInvoices;
            const totalMatched = recordMatchedArInvoices.reduce((prev, cur) => prev + cur.arInvoiceAmountMatched, 0);
            if (
                totalMatched === intacctBankAccountTransactionFeed.amountToMatch &&
                intacctBankAccountTransactionFeed.payToCustomerId &&
                intacctBankAccountTransactionFeed.payToCustomerName &&
                intacctBankAccountTransactionFeed.paymentMethod &&
                intacctBankAccountTransactionFeed.paymentDate &&
                intacctBankAccountTransactionFeed.receiptDate
            ) {
                return 'readyForArPaymentGeneration';
            }
            if (recordMatchedArInvoices.length) {
                return 'partialArMatch';
            }
        }

        // we have a matched ar invoice
        if (intacctBankAccountTransactionFeed.arMatch.isArMatch) {
            return 'draftArMatch';
        }
    }
    // AR Payment status - End

    // AR Advance status - begin
    if (intacctBankAccountTransactionFeed.arMatch.arPaymentType === 'advance') {
        if (
            intacctBankAccountTransactionFeed.payToCustomerId &&
            intacctBankAccountTransactionFeed.payToCustomerName &&
            intacctBankAccountTransactionFeed.paymentMethod &&
            intacctBankAccountTransactionFeed.paymentDate &&
            intacctBankAccountTransactionFeed.receiptDate
        ) {
            return 'readyForArAdvanceGeneration';
        }
        if (intacctBankAccountTransactionFeed.paymentMethod || intacctBankAccountTransactionFeed.payToCustomerId) {
            return 'draftArAdvance';
        }
    }
    // AR Advance status - End

    return 'unmatched';
}

export function getFeedRecordStatus(
    intacctBankAccountTransactionFeed: IntacctBankAccountTransactionFeed,
    intacctBankAccountTransactionFeedLines: IntacctBankAccountTransactionFeedLine[],
): FeedRecordStatus {
    const feedRecordStatusForArPayments = getFeedRecordStatusForArPayments(intacctBankAccountTransactionFeed);

    if (feedRecordStatusForArPayments !== 'unmatched') {
        return feedRecordStatusForArPayments;
    }

    // not created line
    if (intacctBankAccountTransactionFeed._id < 0) {
        // matching rule find
        if (intacctBankAccountTransactionFeed.cleared === 'matched') return 'matched';
        // matching rule find
        if (intacctBankAccountTransactionFeedLines.some(line => line.status === 'draftMatch')) return 'draftMatch';
        // not created line
        return 'unmatched';
    }

    // ## Saved record
    // record currently looking for intacct matches
    if (intacctBankAccountTransactionFeedLines.length) {
        if (intacctBankAccountTransactionFeedLines.some(line => line.status === 'lookingForMatches'))
            return 'lookingForExistingJournalEntries';
        // record found multiple journalEntry on a line
        if (intacctBankAccountTransactionFeedLines.some(line => line.status === 'multipleMatches'))
            return 'journalEntriesFound';
        // record found one journalEntry
        if (
            intacctBankAccountTransactionFeedLines.length &&
            intacctBankAccountTransactionFeedLines.every(line => line.status === 'matchFound')
        )
            return 'journalEntryFound';
    }
    switch (intacctBankAccountTransactionFeed.financeIntegrationStatus) {
        case 'generated':
            return 'journalEntryGenerated';
        case 'generationError':
            return 'journalEntryGenerationError';
        case 'generationInProgress':
            return 'journalEntryGenerationInProgress';
        case 'postingInProgress':
            return 'journalEntryPostingInProgress';
        case 'posted':
            return 'journalEntryPosted';
        case 'postingError':
            return 'journalEntryPostingError';
        case 'notPosted':
        default:
            break;
    }

    if (
        intacctBankAccountTransactionFeedLines.length &&
        intacctBankAccountTransactionFeedLines.every(line => line.status === 'matched')
    )
        return 'matched';

    // record is ready to post
    if (
        intacctBankAccountTransactionFeedLines.length &&
        intacctBankAccountTransactionFeedLines.every(line => !!line.account && !!line.amount)
    ) {
        return 'readyForPosting';
    }

    return 'unmatched';
}
