import type { ExtractArray, OperationResultType } from '@sage/xtrem-client';
import type { IntacctMap$Queries } from '@sage/xtrem-intacct-finance-api';

/**
 *  Interface for returning the intacct lookup data
 */
export interface LookupObject {
    name: string;
    documentType: string;
    fields: LookupProperties[];
    relationships: LookupRelationships[];
    relationshipFields?: LookupProperties[]; // used for relationshipFields datable in ui Page
    additionnalLink?: AdditionnalLink[];
    xtremObject?: string;
}

export interface AdditionnalLink {
    /** Xtreem Property of the node linked */
    xtremProperty: string;
    type: string; // enum string node
    /** Array of value that must be equal to the xtremProperty */
    xtremValues?: string[];
    /** To be able to get a value from the current intacct configuration */
    intacctConfigProperty?: string;
}

export interface InspectObject {
    name: string;
    object: string;
}

/**
 *  Fields for LookupObject
 */
export interface LookupProperties {
    ID: string;
    LABEL: string;
    DESCRIPTION: string;
    REQUIRED: boolean;
    READONLY: boolean;
    WRITEONLY?: boolean; // No Query request on this fields
    CREATEONLY?: boolean; // Custom field properties
    DATATYPE: string;
    ISCUSTOM: boolean;
    VALIDVALUES?: {
        VALIDVALUE: string[];
    } | null;
    isEditable?: boolean;
    xtremProperty?: string;
    xtremDefaultProperty?: string;
    xtremPropertyOption?: string;
}
/**
 * RelationShips Field for LookupObject
 */
export interface LookupRelationships {
    OBJECTPATH: string;
    OBJECTNAME: string;
    LABEL: string;
    RELATIONSHIPTYPE: string;
    RELATEDBY: string;
    xtremProperty?: string;
    xtremPropertyOption?: string;
}

export interface LinkedData extends ExtractArray<OperationResultType<IntacctMap$Queries['getDataIntacct']>> {}
