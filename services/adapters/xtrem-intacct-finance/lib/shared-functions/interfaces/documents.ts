import type { FinanceDocumentType, PostingStatus, TargetDocumentType } from '@sage/xtrem-finance-data-api';
import type { StoredAttributes } from '@sage/xtrem-finance-data/lib/client-functions/interfaces/dimension';
import type { Dict } from '@sage/xtrem-shared';

enum FeedLineMatchingStatusEnum {
    /** defaultStatus before looking for matches */
    lookingForMatches,
    /** JE generated */
    matched,
    /** matching rules applieds */
    draftMatch,
    /** nothing has done to the line  */
    unmatched,
    /** multiple document, user need to select one */
    multipleMatches,
    /** there is same record in intacct found */
    matchFound,
}
type FeedLineMatchingStatus = keyof typeof FeedLineMatchingStatusEnum;

enum FeedRecordStatusEnum {
    /** intacct status is matched */ matched,
    unmatched,
    /** Looking on intacct */ lookingForExistingJournalEntries,
    /** we Found one journal entry on intacct */ journalEntryFound,
    /** we Found multiple journal entry on intacct */ journalEntriesFound,
    /** matching rule as been apply */ draftMatch,
    /** the JE is ready to create */ readyForPosting,
    /** deprecated */ journalEntryPosted,
    /** the JE is being created in SDMO */ journalEntryGenerationInProgress,
    /** The JE creation ended in error in SDMO */ journalEntryGenerationError,
    /** The JE has been created in SDMO */ journalEntryGenerated,
    /** The JE is being sent to Intacct Fin. */ journalEntryPostingInProgress,
    /** the JE has not been sent to Intacct Fin. due to an error. */ journalEntryPostingError,
    /** we found at least on ar invoice that we were able to match with the bank feed */ draftArMatch,
    /** user selected ar invoices to match for the total amount of the bank feed */ readyForArPaymentGeneration,
    arPaymentGenerationInProgress,
    arPaymentPostingInProgress,
    arPaymentPosted,
    readyForArAdvanceGeneration,
    arAdvancePosted,
    /** user selected ar invoices to match, but not for the total amount of the bank feed */ partialArMatch,
    draftArAdvance,
    arPaymentGenerated,
    arAdvanceGenerated,
    arPaymentPostingError,
    arAdvancePostingInProgress,
    arAdvancePostingError,
}
type FeedRecordStatus = keyof typeof FeedRecordStatusEnum;

enum IntacctMatchingStatusEnum {
    unmatched,
    cleared,
    matched,
    partiallyMatched,
    selectToMatch,
    selectToUnmatch,
    ignored,
    draftMatched,
}
type IntacctMatchingStatus = keyof typeof IntacctMatchingStatusEnum;

export type ApArInvoicePaymentType = 'payment' | 'advance' | '';

export interface MatchedArInvoices {
    matchedArInvoices: MatchedArInvoice[];
}

export interface MatchedArInvoice {
    recordNo: number;
    arInvoiceAmountMatched: number;
}

export interface IntacctCustomer {
    customerId: string;
    customerName: string;
}

export interface ArMatch {
    isArMatch: boolean;
    customerId?: string;
    matchingReasons?: string;
    arPaymentType: ApArInvoicePaymentType;
}

export interface IntacctBankAccountTransactionFeedLine {
    status?: FeedLineMatchingStatus;
    amount?: number | string;
    account?: number | string;
}
export interface IntacctBankAccountTransactionFeed extends IntacctBankAccountTransactionFeedArPayment {
    _id: number;
    cleared: IntacctMatchingStatus;
}

export interface IntacctBankAccountTransactionFeedArPayment extends ApArPaymentMatchingData {
    amountToMatch: number;
    arMatch: ArMatch;
    jsonArInvoices: MatchedArInvoices;
    financeIntegrationStatus: PostingStatus;
    targetDocumentType?: TargetDocumentType;
}

export interface ApArPaymentMatchingData {
    payToCustomerId: string;
    payToCustomerName: string;
    paymentMethod: string;
    paymentDate: string;
    receiptDate: string;
    account?: string;
    storedAttributes?: string;
    storedDimensions?: string;
    targetDocumentType?: TargetDocumentType;
}

export interface ApArPaymentCreationData {
    siteId: number;
    currencyId: number;
    bankFeedId: number;
}

export interface ArInvoiceMatchingPageResult {
    arInvoices: MatchedArInvoice[];
    arMatch: ArMatch;
    arPaymentAdvanceData: ApArPaymentMatchingData;
    newFinanceIntegrationStatus: PostingStatus;
    newStatus?: FeedRecordStatus;
}

export interface ArPaymentCreateData {
    bankAccount: number;
    financialSite: number;
    payToCustomerId: string;
    payToCustomerName: string;
    description: string;
    paymentMethod: string;
    postingDate: string;
    paymentDate: string;
    currency: number;
    companyFxRate: number;
    companyFxRateDivisor: number;
    fxRateDate: string;
    paymentAmount: number;
    paymentCompanyAmount: number;
    bankFeed: number;
    arMatch: ArMatch;
    jsonArInvoices: MatchedArInvoices;
    lines: ArPaymentLineCreateData[];
}

export interface ArAdvanceCreateData {
    bankAccount: number;
    financialSite: number;
    payToCustomerId: string;
    payToCustomerName: string;
    description: string;
    paymentMethod: string;
    postingDate: string;
    paymentDate: string;
    currency: number;
    companyFxRate: number;
    companyFxRateDivisor: number;
    fxRateDate: string;
    advanceAmount: number;
    advanceCompanyAmount: number;
    bankFeed: number;
    arMatch: ArMatch;
    lines: ArAdvanceLineCreateData[];
}

export interface ArPaymentLineCreateData {
    type: FinanceDocumentType;
    arInvoiceRecordNo: number;
    financialSite: number;
    currency: number;
    paymentAmount: number;
    paymentCompanyAmount: number;
}
export interface ArAdvanceLineCreateData {
    financialSite: number;
    currency: number;
    advanceAmount: number;
    advanceCompanyAmount: number;
    storedAttributes?: StoredAttributes;
    storedDimensions?: Dict<string>;
    account?: string;
}
