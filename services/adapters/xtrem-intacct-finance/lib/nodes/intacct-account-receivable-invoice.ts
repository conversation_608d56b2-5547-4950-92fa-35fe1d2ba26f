import type { NodeQueryFilter, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremIntacctFinance from '..';
import { getTaxSolutionId } from '../functions';
import { customerPayloadContinueOnError } from '../functions/synchronization';
import { IntacctSynchronizationFinanceState } from './intacct-synchronization-finance-state';

@decorators.subNode<IntacctAccountsReceivableInvoice>({
    extends: () => IntacctSynchronizationFinanceState,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isVitalReferenceChild: true,
})
export class IntacctAccountsReceivableInvoice extends IntacctSynchronizationFinanceState {
    override getSyncStateReference() {
        return this.document;
    }

    override getMapping() {
        return this.$.context.read(xtremIntacctFinance.nodes.IntacctMap, {
            application: '#intacct',
            thirdPartyObjectName: 'ARINVOICE',
            nodeFactory: `#${xtremFinance.nodes.AccountsReceivableInvoice.name}`,
        });
    }

    @decorators.referenceProperty<IntacctAccountsReceivableInvoice, 'document'>({
        node: () => xtremFinance.nodes.AccountsReceivableInvoice,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
    })
    override readonly document: Reference<xtremFinance.nodes.AccountsReceivableInvoice>;

    /**
     * megaEntityId
     * when creating the ar invoice: depending on transactionIntegrationLevel on Intacct settings:
     * if 'entityLevel': will send the ar invoice to the entityId (financialSite.id) on Intacct
     * if 'topLevel': will send the ar invoice to the topLevel on Intacct
     */
    @decorators.stringPropertyOverride<IntacctAccountsReceivableInvoice, 'entityId'>({
        computeValue() {
            return this.getEntityIdDependingOnTransactionIntegrationLevel();
        },
    })
    override readonly entityId: Promise<string>;

    /** Linked to CUSTOMERID  */
    @decorators.jsonProperty<IntacctAccountsReceivableInvoice, 'billToCustomer'>({
        isPublished: true,
        async computeValue() {
            return customerPayloadContinueOnError(await (await this.document).billToCustomer);
        },
    })
    readonly billToCustomer: Promise<xtremIntacctFinance.interfaces.synchronization.SyncState | null>;

    async getLegislationId() {
        return (await (await (await (await this.document).financialSite).legalCompany).legislation).id;
    }

    /** Corresponding to intacct TAXSOLUTIONID  */
    @decorators.stringProperty<IntacctAccountsReceivableInvoice, 'taxSolutionId'>({
        isPublished: true,
        computeValue() {
            return getTaxSolutionId(this);
        },
    })
    readonly taxSolutionId: Promise<string>;

    static override xtremFilter(filterValue: string): NodeQueryFilter<xtremFinance.nodes.AccountsReceivableInvoice> {
        return { number: { _regex: filterValue } };
    }
}
