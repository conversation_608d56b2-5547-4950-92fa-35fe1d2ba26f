import type { Context, Dict, Reference, integer } from '@sage/xtrem-core';
import { Node, asyncArray, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { dataTypes, nodes } from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremIntacctFinance from '..';
import type { IntacctCustomer } from '../shared-functions/interfaces';

const packageName = 'xtrem-intacct-finance';

@decorators.node<IntacctBankAccountMatching>({
    package: packageName,
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isClearedByReset: true,
    indexes: [
        {
            orderBy: { bankAccount: 1, type: 1, priority: 1, keyword: 1 },
            isUnique: true,
        },
    ],
    serviceOptions: () => [xtremIntacctFinance.serviceOptions.intacctCashbookManagement],
    async saveBegin() {
        await xtremIntacctFinance.functions.save.IntacctBankAccountMatching.saveBegin(this);
    },
})
export class IntacctBankAccountMatching extends Node {
    /**  Bank Account */
    @decorators.referenceProperty<IntacctBankAccountMatching, 'bankAccount'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.BankAccount,
        allowedInUniqueIndex: true,
    })
    readonly bankAccount: Reference<xtremFinanceData.nodes.BankAccount | null>;

    @decorators.integerProperty<IntacctBankAccountMatching, 'priority'>({
        isStored: true,
        isPublished: true,
    })
    readonly priority: Promise<integer>;

    @decorators.stringProperty<IntacctBankAccountMatching, 'keyword'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.description,
    })
    readonly keyword: Promise<string>;

    @decorators.referenceProperty<IntacctBankAccountMatching, 'account'>({
        isStored: true,
        isPublished: true,
        node: () => xtremFinanceData.nodes.Account,
        isNullable: true,
        async control(cx, val) {
            await xtremIntacctFinance.functions.controls.IntacctBankAccountMatching.account(
                val,
                await this.tax,
                await this.location,
                await this.updateAccountTaxManagement,
                cx,
                this.$.context,
            );
        },
    })
    readonly account: Reference<xtremFinanceData.nodes.Account | null>;

    /** TRANSACTIONTYPE
     *  - deposit ==> credit
     *  - withdrawal ==> debit (amount * -1)
     */
    @decorators.enumProperty<IntacctBankAccountMatching, 'transactionType'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremIntacctFinance.enums.IntacctRecordTransactionTypeDataType,
    })
    readonly transactionType: Promise<xtremIntacctFinance.enums.IntacctRecordTransactionType | null>;

    /** tax detail */
    @decorators.referenceProperty<IntacctBankAccountMatching, 'tax'>({
        isStored: true,
        isPublished: true,
        node: () => xtremTax.nodes.Tax,
        isNullable: true,
        async control(cx, val) {
            await xtremIntacctFinance.functions.controls.IntacctBankAccountMatching.tax(
                await this.account,
                val,
                await this.updateAccountTaxManagement,
                cx,
                this.$.context,
            );
        },
    })
    readonly tax: Reference<xtremTax.nodes.Tax | null>;

    @decorators.referenceProperty<IntacctBankAccountMatching, 'location'>({
        isStored: true,
        isPublished: true,
        node: () => nodes.Site,
        isNullable: true,
    })
    readonly location: Reference<nodes.Site | null>;

    @decorators.jsonProperty<IntacctBankAccountMatching, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<IntacctBankAccountMatching, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
    })
    readonly storedDimensions: Promise<Dict<string> | null>;

    @decorators.referenceProperty<IntacctBankAccountMatching, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.enumProperty<IntacctBankAccountMatching, 'type'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacctFinance.enums.IntacctmatchingTypeDataType,
        defaultValue() {
            return 'equals';
        },
    })
    readonly type: Promise<xtremIntacctFinance.enums.IntacctMatchingType>;

    // This transient input property is used to know that the user choosed to update the
    // taxManagement property of the account used in this record.
    // That update is done on the onSave event of the IntacctBankAccountMatching.
    @decorators.booleanProperty<IntacctBankAccountMatching, 'updateAccountTaxManagement'>({
        isPublished: true,
        isTransientInput: true,
    })
    readonly updateAccountTaxManagement: Promise<boolean>;

    @decorators.query<typeof IntacctBankAccountMatching, 'queryIntacctDocument'>({
        isPublished: true,
        parameters: [
            {
                name: 'parameters',
                type: 'object',
                properties: {
                    date: { type: 'date', isMandatory: false, isNullable: true },
                    description: { type: 'string', isMandatory: false, isNullable: true },
                    account: { type: 'string', isMandatory: false, isNullable: true }, // Never use
                    amount: { type: 'decimal', isMandatory: false, isNullable: true },
                },
            },
            {
                name: 'documentType',
                type: 'string',
                isMandatory: true,
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    recordNo: { type: 'date', isMandatory: false, isNullable: true },
                    description: { type: 'string', isMandatory: false, isNullable: true },
                    documentType: { type: 'string', isMandatory: false, isNullable: true },
                    location: { type: 'string', isMandatory: false, isNullable: true }, // Never use
                    journal: { type: 'string', isMandatory: false, isNullable: true },
                    date: { type: 'date', isMandatory: false, isNullable: true },
                    amount: { type: 'decimal', isMandatory: false, isNullable: true },
                    account: { type: 'string', isMandatory: false, isNullable: true },
                    url: { type: 'string', isMandatory: false, isNullable: true },
                    batchNo: { type: 'string', isMandatory: false, isNullable: true },
                    entityId: { type: 'string', isMandatory: false, isNullable: true },
                },
            },
        },
    })
    static queryIntacctDocument(
        context: Context,
        parameters: xtremIntacctFinance.interfaces.BankAccountMatching.IntacctDocumentParameters,
        documentType: xtremIntacctFinance.interfaces.BankAccountMatching.IntacctDocumentType,
    ): Promise<xtremIntacctFinance.interfaces.BankAccountMatching.IntacctDocument[]> {
        return xtremIntacctFinance.functions.intacctQueries.document(context, parameters, documentType);
    }

    @decorators.query<typeof IntacctBankAccountMatching, 'queryIntacctArInvoice'>({
        isPublished: true,
        parameters: [
            {
                name: 'parameters',
                type: 'object',
                properties: {
                    customerId: { type: 'string', isNullable: true },
                    currencyId: { type: 'string', isNullable: true },
                    orderField: {
                        name: 'orderField',
                        type: 'object',
                        properties: {
                            name: { type: 'string', isNullable: true },
                            isAscending: { type: 'boolean', isNullable: true },
                        },
                    },
                    documentNos: {
                        type: 'array',
                        item: { type: 'string', name: 'recordNo' },
                    },
                    megaEntityId: { type: 'string', isNullable: true },
                },
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    recordNo: { type: 'integer', isNullable: true },
                    invoiceNo: { type: 'string', isNullable: true },
                    customerName: { type: 'string', isNullable: true },
                    customerId: { type: 'string', isNullable: true },
                    referenceNumber: { type: 'string', isNullable: true },
                    date: { type: 'date', isNullable: true },
                    dueDate: { type: 'date', isNullable: true },
                    totalAmount: { type: 'decimal', isNullable: true },
                    totalDue: { type: 'decimal', isNullable: true },
                    totalPaid: { type: 'decimal', isNullable: true },
                    term: {
                        type: 'reference',
                        node: () => xtremMasterData.nodes.PaymentTerm,
                        isNullable: true,
                    },
                    entityId: { type: 'string', isNullable: true },
                },
            },
        },
    })
    static queryIntacctArInvoice(
        context: Context,
        parameters: xtremIntacctFinance.interfaces.BankAccountMatching.IntacctDocumentParameters,
    ): Promise<xtremIntacctFinance.interfaces.BankAccountMatching.IntacctArInvoice[]> {
        return xtremIntacctFinance.functions.intacctQueries.arInvoice(context, parameters);
    }

    @decorators.query<typeof IntacctBankAccountMatching, 'queryIntacctCustomers'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    customerName: { type: 'string', isNullable: true },
                    customerId: { type: 'string', isNullable: true },
                },
            },
        },
    })
    static queryIntacctCustomers(context: Context): Promise<IntacctCustomer[]> {
        return xtremIntacctFinance.functions.intacctQueries.customerList(context);
    }

    /**
     * Saves a collection of intacct bank matching rules
     * @param context Context
     * @param intacctBankAccountMatchings Collection of matching rules
     * @return If was successful
     */
    @decorators.mutation<typeof IntacctBankAccountMatching, 'bulkSave'>({
        isPublished: true,
        parameters: [
            {
                name: 'intacctBankAccountMatchings',
                type: 'array',
                item: {
                    type: 'instance',
                    node: () => IntacctBankAccountMatching,
                    isTransientInput: true,
                },
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static async bulkSave(
        context: Context,
        intacctBankAccountMatchings: IntacctBankAccountMatching[],
    ): Promise<boolean> {
        if (intacctBankAccountMatchings.length) {
            await asyncArray(intacctBankAccountMatchings).forEach(async intacctBankAccountMatching => {
                if (Number(intacctBankAccountMatching?._id) > 0) {
                    const intacctBankAccountMatchingLine = await context.read(
                        xtremIntacctFinance.nodes.IntacctBankAccountMatching,
                        { _id: intacctBankAccountMatching._id },
                        { forUpdate: true },
                    );
                    await intacctBankAccountMatchingLine.$.set(
                        await intacctBankAccountMatching.$.payload({ withIds: true }),
                    );
                    await intacctBankAccountMatchingLine.$.save();
                } else {
                    const intacctBankAccountMatchingLine = await context.create(
                        IntacctBankAccountMatching,
                        await intacctBankAccountMatching.$.payload({ withIds: true }),
                    );
                    await intacctBankAccountMatchingLine.$.save();
                }
            });
        }
        return true;
    }
}
