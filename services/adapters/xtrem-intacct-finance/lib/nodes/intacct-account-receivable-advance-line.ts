import type { Reference } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';

/** Intacct object : ARDAVANCEITEM */
@decorators.node<IntacctAccountsReceivableAdvanceLine>({
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isVitalReferenceChild: true,
    storage: 'sql',
    indexes: [
        {
            orderBy: { documentLine: 1 },
            isUnique: true,
        },
    ],
})
export class IntacctAccountsReceivableAdvanceLine
    extends Node
    implements xtremSynchronization.interfaces.ThirdPartySynchronizationNodeLine
{
    getSyncStateLine() {
        return this.documentLine;
    }

    async getStoredAttributes(): Promise<xtremMasterData.interfaces.StoredAttributes> {
        return (
            (await (await this.documentLine).storedAttributes) || {
                project: '',
                task: '',
                employee: '',
                customer: '',
                supplier: '',
                item: '',
            }
        );
    }

    async getStoredDimensions(): Promise<any> {
        return (
            (await (await this.documentLine).storedDimensions) || {
                dimensionType01: '',
                dimensionType02: '',
            }
        );
    }

    @decorators.referenceProperty<IntacctAccountsReceivableAdvanceLine, 'documentLine'>({
        node: () => xtremFinance.nodes.AccountsReceivableAdvanceLine,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
    })
    readonly documentLine: Reference<xtremFinance.nodes.AccountsReceivableAdvanceLine>;

    /**     *  Linked to PROJECTID     */
    @decorators.stringProperty<IntacctAccountsReceivableAdvanceLine, 'projectId'>({
        isPublished: true,
        async computeValue() {
            return (await this.getStoredAttributes()).project;
        },
    })
    readonly projectId: Promise<string>;

    /**     *  Linked to CUSTOMERID     */
    @decorators.stringProperty<IntacctAccountsReceivableAdvanceLine, 'customerId'>({
        isPublished: true,
        async computeValue() {
            return (await this.getStoredAttributes())?.customer;
        },
    })
    readonly customerId: Promise<string>;

    /**     *  Linked to VENDORID     */
    @decorators.stringProperty<IntacctAccountsReceivableAdvanceLine, 'vendorId'>({
        isPublished: true,
        async computeValue() {
            return (await this.getStoredAttributes()).supplier;
        },
    })
    readonly vendorId: Promise<string>;

    /**     *  Linked to ITEMID     */
    @decorators.stringProperty<IntacctAccountsReceivableAdvanceLine, 'itemId'>({
        isPublished: true,
        async computeValue() {
            return (await this.getStoredAttributes()).item;
        },
    })
    readonly itemId: Promise<string>;

    /**     *  Linked to DEPARTMENTID     */
    @decorators.stringProperty<IntacctAccountsReceivableAdvanceLine, 'departmentId'>({
        isPublished: true,
        async computeValue() {
            return (await this.getStoredDimensions()).dimensionType01;
        },
    })
    readonly departmentId: Promise<string>;

    /**     *  Linked to CLASSID     */
    @decorators.stringProperty<IntacctAccountsReceivableAdvanceLine, 'classId'>({
        isPublished: true,
        async computeValue() {
            return (await this.getStoredDimensions()).dimensionType02;
        },
    })
    readonly classId: Promise<string>;
}
