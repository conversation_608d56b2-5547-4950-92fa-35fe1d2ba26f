import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremIntacctFinance from '..';
import { CleanContactListInfo } from '../classes/intacct/clean-contact-list-info';
import { contactPayload } from '../functions/synchronization';
import { IntacctSynchronizationState } from './intacct-synchronization-state';

@decorators.subNode<IntacctCustomerSupplier>({
    extends: () => IntacctSynchronizationState,
    canCreate: true,
    isAbstract: true,
})
export class IntacctCustomerSupplier extends IntacctSynchronizationState {
    readonly parent: Reference<xtremMasterData.nodes.Customer | xtremMasterData.nodes.Supplier>;

    async getBusinessEntity() {
        return (await this.parent).businessEntity;
    }

    override async getIntacctId() {
        return (await this.getBusinessEntity()).id;
    }

    async getPrimaryAddress() {
        return (await this.parent).primaryAddress;
    }

    @decorators.stringPropertyOverride<IntacctCustomerSupplier, 'sysIdLink'>({
        async computeValue() {
            if (await (await this.getBusinessEntity()).isCustomer) {
                return `${((await (await this.getBusinessEntity()).customer) ?? '').toString()}`;
            }

            if (await (await this.getBusinessEntity()).isSupplier) {
                return `${((await (await this.getBusinessEntity()).supplier) ?? '').toString()}`;
            }
            // Fallback to the business entity ID if neither customer nor supplier
            return `${(await this.getBusinessEntity())._id.toString()}`;
        },
    })
    override readonly sysIdLink: Promise<string>;

    @decorators.booleanProperty<IntacctCustomerSupplier, 'hideDisplayContact'>({
        isPublished: true,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await (await this.intacctConfiguration)?.isDisplayContactHidden) ?? true;
        },
    })
    readonly hideDisplayContact: Promise<boolean>;

    /**
     * Corresponding to intacct STATUS
     */
    @decorators.stringProperty<IntacctCustomerSupplier, 'status'>({
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await (await this.parent).isActive) ? 'active' : 'inactive';
        },
    })
    readonly status: Promise<string>;

    /**      * Linked to PRIMARYCONTACT CONTACTNAME     * If not exist     */
    @decorators.jsonProperty<IntacctCustomerSupplier, 'primaryContact'>({
        isPublished: true,
        isNullable: true,
        async computeValue() {
            const parent = await this.parent;
            const primaryAddress = await parent.primaryAddress;
            if (!primaryAddress) {
                await this.$.context.logger.warnAsync(
                    async () => `No primary address for ${await (await parent.businessEntity).name}`,
                );
                return null;
            }
            if (!(await this.hideDisplayContact) && primaryAddress._id === (await parent.primaryAddress)._id) {
                return null;
            }
            return contactPayload(primaryAddress);
        },
    })
    readonly primaryContact: Promise<xtremIntacctFinance.interfaces.synchronization.SyncState | null>;

    async getAddressCategory(businessEntityAddress: xtremMasterData.nodes.BusinessEntityAddress) {
        const primaryAddress = await (await this.parent).primaryAddress;
        if (primaryAddress._id === businessEntityAddress._id) {
            return this.$.context.localize('@sage/xtrem-intacct-finance/intacct-address-primary', 'Primary address');
        }
        return null;
    }

    async getAddressBusinessEntity(businessEntityAddress: xtremMasterData.nodes.BusinessEntityAddress) {
        const primaryBeAddress = await (await this.getBusinessEntity()).primaryAddress;
        if ((await businessEntityAddress.deliveryDetail) && (await (await this.node).name) === 'Customer') {
            return this.$.context.localize('@sage/xtrem-intacct-finance/intacct-address-ship-to', 'Ship-to address');
        }
        if (primaryBeAddress && primaryBeAddress._id === businessEntityAddress._id) {
            return this.$.context.localize(
                '@sage/xtrem-intacct-finance/intacct-address-primary-be',
                'Primary business entity address',
            );
        }

        return null;
    }

    /**      *  For CONTACT_LIST_INFO multiple address/contact     */
    @decorators.jsonProperty<IntacctCustomerSupplier, 'contactList'>({
        isPublished: true,
        async computeValue() {
            return (await (await this.parent).businessEntity).addresses
                .map(async address => {
                    const CATEGORYNAME = (await (
                        await this.intacctConfiguration
                    )?.isCategoryNameClean)
                        ? await this.getCleanCategoryName(address)
                        : (await address.name).substring(0, 35);

                    return { CONTACT_LIST_INFO: { CATEGORYNAME, contact: { name: await contactPayload(address) } } };
                })
                .toArray();
        },
    })
    readonly contactList: Promise<xtremIntacctFinance.interfaces.synchronization.ContactList[]>;

    async getCleanCategoryName(address: xtremMasterData.nodes.BusinessEntityAddress): Promise<string> {
        const otherAddressesCategory = this.$.context.localize(
            '@sage/xtrem-intacct-finance/intacct-addresse-others',
            'Other address',
        );
        return (
            (await this.getAddressCategory(address)) ??
            (await this.getAddressBusinessEntity(address)) ??
            otherAddressesCategory
        );
    }

    /** Standard before from synchronization state : will be launch before the customer/supplier synchronization
     * The goal there is to clean the contact list if the option is activated into the intacct configuration
     */
    override async before() {
        if (await (await this.intacctConfiguration)?.isContactListCleaned) {
            const recordNo = await this.recordNo;
            /** if the customer/supplier already exist into intacct Clean the contact list  */
            if ((await this.state) !== 'not' && !!recordNo) {
                await this.$.context.batch.logMessage(
                    'info',
                    `Clean contact list for ${await (await this.parent).name}`,
                );
                const objectName = (await (await this.mapping).thirdPartyObjectName) as 'CUSTOMER' | 'VENDOR';
                const clean = new CleanContactListInfo(this.$.context, await this.intacctId, objectName);
                const result = await clean.execute();
                await this.$.context.batch.logMessage('info', `Clean result : ${JSON.stringify(result)} `);
            }
        }
        return true;
    }
}
