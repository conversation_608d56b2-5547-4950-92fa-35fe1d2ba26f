import type { Reference, integer } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';

/** this is exactly the same as intacct account paybale invoice line
 *  but we are force to do two identical files because we don't have polymorphism
 */
@decorators.node<IntacctAccountsReceivablePaymentLine>({
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isVitalReferenceChild: true,
    storage: 'sql',
    indexes: [
        {
            orderBy: { documentLine: 1 },
            isUnique: true,
        },
    ],
})
export class IntacctAccountsReceivablePaymentLine
    extends Node
    implements xtremSynchronization.interfaces.ThirdPartySynchronizationNodeLine
{
    getSyncStateLine() {
        return this.documentLine;
    }

    @decorators.referenceProperty<IntacctAccountsReceivablePaymentLine, 'documentLine'>({
        node: () => xtremFinance.nodes.AccountsReceivablePaymentLine,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
    })
    readonly documentLine: Reference<xtremFinance.nodes.AccountsReceivablePaymentLine>;

    @decorators.integerProperty<IntacctAccountsReceivablePaymentLine, 'arInvoiceRecordNo'>({
        isStored: true,
        isPublished: true,
    })
    readonly arInvoiceRecordNo: Promise<integer | null>;
}
