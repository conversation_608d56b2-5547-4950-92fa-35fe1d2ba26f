import type * as IA from '@intacct/intacct-sdk';
import * as xtremCommunication from '@sage/xtrem-communication';
import type { Collection, Context, Diagnose, NodeKey, NodeQueryFilter } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, datetime, decorators, Logger, Node, SystemError } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremSystem from '@sage/xtrem-system';
import { dataTypes, serviceOptions } from '@sage/xtrem-system';
import { get, isEmpty, set } from 'lodash';
import * as xtremIntacctFinance from '..';
import { getMapInstance, outboundAndBoth } from '../functions';
import {
    createUpdateXtreem,
    getFieldsName,
    getFieldsToRequest,
    getIntacctDataForCreateUpdate,
    getXtreemData,
    getXtreemDataFormated,
    getXtreemFieldsName,
    manageFiltersForXtreem,
} from '../functions/map';
import { mergeCustomMapping } from '../functions/mapping/mapping-merge';
import { mappingRules } from '../functions/mapping/mapping-rules';

const logger = Logger.getLogger(__filename, 'mapping');
/** Needed to pass sonardCloud codeSmell  */

@decorators.subNode<IntacctMap>({
    extends: () => xtremSynchronization.nodes.BaseMapping,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    async createEnd() {
        await this.$.set({ application: '#intacct', thirdPartyObjectName: await this.id });
    },
})
export class IntacctMap extends xtremSynchronization.nodes.BaseMapping {
    /** Intacct Object name  - deprecated must be move to thirdPartyObjectName base node  */
    @decorators.stringProperty<IntacctMap, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    /** Intacct object description (transaction) */
    @decorators.stringProperty<IntacctMap, 'intacctDescription'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
        lookupAccess: true,
    })
    readonly intacctDescription: Promise<string>;

    /**
     * TODO : Check for  "outBound" | "both" check if the linked node as the properties :
     *  intacctIntegrationState intacctLastIntegrationDate
     *  to automaticly update the status of the node extension
     */

    /**
     *   set to false : SmartEvent will not be generated
     */
    @decorators.booleanProperty<IntacctMap, 'isSmartEvent'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isSmartEvent: Promise<boolean>;

    /**
     *   assignShowPrivate option
     */
    @decorators.booleanProperty<IntacctMap, 'isPrivateShow'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        async control(cx, val) {
            if (val) {
                await logger.debugAsync(
                    async () =>
                        `MEGAENTITYID linked to : ${await this.getXtremPropertyFromIntacctProperty('MEGAENTITYID')}`,
                );
                await cx.error
                    .withMessage(
                        '@sage/xtrem-intacct-finance/mapping-private-show',
                        'You can only activate Include private if the MEGAENTITYID field is present and linked to Sage DMO.',
                    )
                    .if(await this.getXtremPropertyFromIntacctProperty('MEGAENTITYID'))
                    .is.equal.to('');
            }
        },
    })
    readonly isPrivateShow: Promise<boolean>;

    /** mapping file saving - private property     */
    @decorators.jsonProperty<IntacctMap, 'uRelationMapping'>({
        isStored: true,
        isNullable: true,
        isOwnedByCustomer: true,
    })
    readonly uRelationMapping: Promise<xtremIntacctFinance.sharedFunctions.interfaces.LookupObject | null>;

    /** Only custom fields */
    @decorators.jsonProperty<IntacctMap, 'customRelationMapping'>({
        isStored: true,
        isNullable: true,
        isOwnedByCustomer: true,
    })
    readonly customRelationMapping: Promise<xtremIntacctFinance.sharedFunctions.interfaces.LookupObject | null>;

    /** mapping file saving - public property     */
    @decorators.jsonProperty<IntacctMap, 'relationMapping'>({
        isPublished: true,
        isOwnedByCustomer: true,
        dependsOn: ['editableFields'],
        async computeValue() {
            const uRelationMapping = await this.uRelationMapping;
            if (uRelationMapping) {
                logger.error('U relation mapping is deprecated');
                throw new BusinessRuleError('U relation mapping is deprecated');
            }
            const customRelationMapping = (await this.customRelationMapping) || undefined;

            if (!(await this.getFile) && customRelationMapping) {
                return customRelationMapping;
            }

            return mergeCustomMapping({
                default: await this.getFile,
                custom: customRelationMapping,
                editableFields: await this.editableFields,
            });
        },
        async setValue(value) {
            await this.$.set({
                uRelationMapping: null,
                customRelationMapping: isEmpty(value) ? null : await mappingRules({ map: this, fullCustom: value }),
            });
        },
    })
    readonly relationMapping: Promise<xtremIntacctFinance.sharedFunctions.interfaces.LookupObject>;

    @decorators.stringArrayProperty<IntacctMap, 'editableFields'>({
        isPublished: true,
        isStored: true,
    })
    readonly editableFields: Promise<Array<string>>;

    @decorators.bulkMutation<typeof IntacctMap, 'updateCustomMapping'>({ isPublished: true })
    static async updateCustomMapping(context: Context, mapping: IntacctMap) {
        if (
            !(await context.activeServiceOptions).some(
                option => option.name === xtremSystem.serviceOptions.DevTools.name,
            ) &&
            (await context.user)?.email !== '<EMAIL>'
        ) {
            throw new BusinessRuleError('Only administrator can execute the update of the custom mapping.');
        }

        if ((await mapping.uRelationMapping) !== null) {
            const customRelationMapping = await mappingRules({
                map: mapping,
                fullCustom: (await mapping.uRelationMapping) || undefined,
            });
            await context.batch.logMessage('result', JSON.stringify(customRelationMapping), {
                data: customRelationMapping,
            });
            await mapping.$.set({
                customRelationMapping,
                uRelationMapping: null,
            });
            if (!(await mapping.$.trySave())) {
                await context.batch.logMessage('result', context.diagnoses.map(diag => diag.message).join('\n'), {
                    data: context.diagnoses,
                });
            }
        }
    }

    /** additionnal link for Sage DMO */
    @decorators.jsonProperty<IntacctMap, 'additionnalLink'>({
        isPublished: true,
        async computeValue() {
            return (await this.relationMapping).additionnalLink || [];
        },
    })
    readonly additionnalLink: Promise<xtremIntacctFinance.sharedFunctions.interfaces.AdditionnalLink[]>;

    /**
     * xtrem Object linked to Intacct object
     */
    @decorators.stringProperty<IntacctMap, 'intacctFilter'>({
        isPublished: true,
        dataType: () => dataTypes.description,
        async computeValue() {
            try {
                return (await (await this.xtremNode).intacctFilter(this.$.context))
                    .map(filter =>
                        filter.whereValueArray
                            ? `${filter.where} in (${filter.whereValueArray})`
                            : `${filter.where} = ${filter.whereValue}`,
                    )
                    .join('&');
            } catch {
                await this.$.context.logger.warnAsync(async () => `No intacctFilter for ${await this.id}`);
            }
            return '';
        },
    })
    readonly intacctFilter: Promise<string>;

    override async getMappedPropertyNames(): Promise<string[]> {
        const mappedPropertiesNames = (await this.relationMapping).fields
            .filter(field => field.xtremProperty)
            .map(fields => fields.xtremProperty?.split(',') || [])
            .flat(1);

        const { relationshipFields } = await this.relationMapping;
        if (relationshipFields) {
            mappedPropertiesNames.push(
                ...relationshipFields
                    .filter(field => field.xtremProperty)
                    .map(fields => fields.xtremProperty?.split(',') || [])
                    .flat(1),
            );
        }
        return mappedPropertiesNames;
    }

    /** private store of mapping file  */
    private _ufile: xtremIntacctFinance.sharedFunctions.interfaces.LookupObject;

    get getFile(): Promise<xtremIntacctFinance.sharedFunctions.interfaces.LookupObject> {
        return (async () => {
            if (this._ufile) {
                return this._ufile;
            }
            this._ufile = xtremIntacctFinance.functions.mapping.readMappingFile(this.$.context, {
                name: await this.id,
            }) as xtremIntacctFinance.sharedFunctions.interfaces.LookupObject;
            return this._ufile;
        })();
    }

    async getIntacctConfig(): Promise<xtremIntacct.nodes.Intacct> {
        const defaultInstance = await xtremIntacct.nodes.Intacct.defaultInstance(this.$.context);
        if (!defaultInstance) {
            throw new BusinessRuleError('No default instance');
        }
        return defaultInstance;
    }

    private _uAdditionnalFilter: NodeQueryFilter<any>;

    async getAdditionalFilter<T extends Node>(): Promise<NodeQueryFilter<T>> {
        if (this._uAdditionnalFilter) {
            return this._uAdditionnalFilter as NodeQueryFilter<T>;
        }
        const { additionnalLink } = await this.relationMapping;
        this._uAdditionnalFilter = additionnalLink
            ? await asyncArray(additionnalLink).reduce(async (filter, currFilter) => {
                  const customFilter = {};
                  if (currFilter.xtremValues) {
                      set(customFilter, currFilter.xtremProperty, { _in: currFilter.xtremValues });
                  }
                  /** filter from the intacct config property : ( used for GLACCOUNT ) */
                  if (currFilter.intacctConfigProperty) {
                      const xtremPropertyFilter = await (
                          await this.getIntacctConfig()
                      ).$.getValue(currFilter.intacctConfigProperty);

                      if (xtremPropertyFilter) {
                          set(customFilter, currFilter.xtremProperty, {
                              _eq: xtremPropertyFilter instanceof Node ? xtremPropertyFilter._id : xtremPropertyFilter,
                          });
                      }
                  }
                  return {
                      ...filter,
                      ...customFilter,
                  };
              }, {})
            : {};
        logger.debug(() => ` uAdditionnalFilter : ${JSON.stringify(this._uAdditionnalFilter)}`);
        return this._uAdditionnalFilter as NodeQueryFilter<T>;
    }

    get intacctIDField(): Promise<xtremIntacctFinance.nodes.MapProperty | null> {
        return (async () => {
            return (await this.specificFields.find(async item => (await item.type) === 'intacctId')) || null;
        })();
    }

    private _uXtremNode: typeof xtremIntacct.nodes.IntacctNode;

    /** Node Xtreem linked to the intacct map instance // xtremObject property */
    get xtremNode(): Promise<typeof xtremIntacct.nodes.IntacctNode> {
        return (async () => {
            if (this._uXtremNode) {
                return this._uXtremNode;
            }
            const xtremNodeType = (await (await this.nodeFactory)?.getNode()) as typeof xtremIntacct.nodes.IntacctNode;
            if (!xtremNodeType) {
                throw new SystemError('Not a node');
            }
            this._uXtremNode = xtremNodeType;
            return this._uXtremNode;
        })();
    }

    /**
     *  Get the array of string needed to **update** the intacct object
     */
    get intacctFields(): Promise<string[]> {
        return this.getIntacctFields(false);
    }

    /**
     *  Get the array of string needed to **read** the intacct object
     */
    get intacctReadFields(): Promise<string[]> {
        return this.getIntacctFields();
    }

    /** TODO : Merge this two getter in only one function  */

    async getIntacctWrileOnlyFields(): Promise<string[]> {
        const intacctFields = (await this.relationMapping).fields
            .filter(line => line.xtremProperty && line.WRITEONLY)
            .map(field => field.ID);
        const { relationshipFields } = await this.relationMapping;
        const intacctRelationshipFields = relationshipFields
            ? relationshipFields
                  .filter(
                      (line: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties) =>
                          line.xtremProperty && line.WRITEONLY,
                  )
                  .map((field: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties) => field.ID)
            : [];
        return intacctFields.concat(intacctRelationshipFields);
    }

    async getXtremPropertyFromIntacctProperty(intacctProperty: string): Promise<string | undefined> {
        const intacctField = (await this.relationMapping).fields.find(line => line.ID === intacctProperty);
        if (intacctField) {
            return intacctField.xtremProperty;
        }
        return (
            (await this.relationMapping).relationshipFields?.find(line => line.ID === intacctProperty)?.xtremProperty ||
            ''
        );
    }

    async getIntacctFields(isRead = true): Promise<string[]> {
        const intacctFields = (await this.relationMapping).fields
            .filter(line => line.xtremProperty && (!line.WRITEONLY || isRead))
            .map(field => field.ID);
        const { relationshipFields } = await this.relationMapping;
        const intacctRelationshipFields = relationshipFields
            ? relationshipFields
                  .filter(
                      (line: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties) =>
                          line.xtremProperty && (!line.WRITEONLY || isRead),
                  )
                  .map((field: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties) => field.ID)
            : [];

        logger.debug(
            () => `Intacct Fields to request : ${JSON.stringify(intacctFields.concat(intacctRelationshipFields))}`,
        );
        return intacctFields.concat(intacctRelationshipFields);
    }

    /**
     * Specifics Intaccts Fields at least "name" & "description"  & "intacctId"
     */
    @decorators.collectionProperty<IntacctMap, 'specificFields'>({
        isPublished: true,
        node: () => xtremIntacctFinance.nodes.MapProperty,
        reverseReference: 'map',
        isVital: true,
    })
    readonly specificFields: Collection<xtremIntacctFinance.nodes.MapProperty>;

    /**
     * Specifics Intaccts Fields
     */
    @decorators.collectionProperty<IntacctMap, 'lines'>({
        isPublished: true,
        node: () => xtremIntacctFinance.nodes.MapLine,
        reverseReference: 'mapHeader',
        isVital: true,
    })
    readonly lines: Collection<xtremIntacctFinance.nodes.MapLine>;

    /**
     *  Get XTreeM node available for update from intacct
     * return list of string containing name of XTreeM nodes
     * @param context
     */
    @decorators.query<typeof IntacctMap, 'getAvailableXtremObjectList'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'array',
            item: {
                type: 'string',
            },
            isMandatory: true,
        },
    })
    static async getAvailableXtremObjectList(context: Context): Promise<string[]> {
        return (
            await context.select(
                IntacctMap,
                { nodeFactory: { name: true } },
                {
                    filter: {
                        synchronizationDirection: {
                            _in: outboundAndBoth,
                        },
                    },
                },
            )
        ).map(intacctMap => intacctMap.nodeFactory.name);
    }

    /**
     *  Get the mapping file, if not exist get the default mapping from intacct
     *  if isUpdate is set to true, get the last fields ( isCustom & merge to the file )
     * return xtremIntacctFinance.interfaces.LookupObject
     * @param context
     */
    @decorators.query<typeof IntacctMap, 'getObject'>({
        isPublished: true,
        parameters: [
            { name: 'object', type: 'string', isMandatory: false },
            { name: 'docparid', type: 'string', isMandatory: false },
            { name: 'isUpdate', type: 'boolean' },
        ],
        return: {
            type: 'object',
            properties: {
                name: 'string',
                documentType: 'string',
                xtremObject: 'string',
                fields: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: {
                            DATATYPE: 'string',
                            DESCRIPTION: 'string',
                            ID: 'string',
                            ISCUSTOM: 'boolean',
                            CREATEONLY: 'boolean',
                            VALIDVALUES: {
                                isNullable: true,
                                type: 'object',
                                properties: {
                                    VALIDVALUE: {
                                        type: 'array',
                                        item: 'string',
                                    },
                                },
                            },
                            LABEL: 'string',
                            READONLY: 'boolean',
                            REQUIRED: 'boolean',
                            xtremProperty: 'string',
                            isEditable: 'boolean',
                            xtremDefaultProperty: 'string',
                        },
                    },
                },
                relationshipFields: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: {
                            DATATYPE: 'string',
                            DESCRIPTION: 'string',
                            ID: 'string',
                            ISCUSTOM: 'boolean',
                            CREATEONLY: 'boolean',
                            VALIDVALUES: {
                                isNullable: true,
                                type: 'object',
                                properties: {
                                    VALIDVALUE: {
                                        type: 'array',
                                        item: 'string',
                                    },
                                },
                            },
                            LABEL: 'string',
                            READONLY: 'boolean',
                            REQUIRED: 'boolean',
                            xtremProperty: 'string',
                            isEditable: 'boolean',
                            xtremDefaultProperty: 'string',
                        },
                    },
                },
                relationships: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: {
                            LABEL: 'string',
                            OBJECTNAME: 'string',
                            OBJECTPATH: 'string',
                            RELATEDBY: 'string',
                            RELATIONSHIPTYPE: 'string',
                            xtremProperty: 'string',
                        },
                    },
                },
            },
        },
    })
    static async getObject(
        context: Context,
        object: string,
        docparid: string,
        isUpdate = false,
    ): Promise<xtremIntacctFinance.sharedFunctions.interfaces.LookupObject> {
        const mapInstance = await context
            .query(xtremIntacctFinance.nodes.IntacctMap, {
                filter: {
                    application: '#intacct',
                    id: object,
                    intacctDescription: docparid,
                },
            })
            .at(0);
        if (!mapInstance) {
            return xtremIntacctFinance.functions.mapping.getFullObject(context, object);
        }
        const file = await mapInstance.relationMapping;

        if (isUpdate) {
            logger.debug(
                () =>
                    `Updating object relationMapping - before ${file.fields.length} fields -   ${
                        file.relationshipFields?.length || 0
                    } relationShip Fields`,
            );
            const updatedFile = await xtremIntacctFinance.functions.mapping.getFullObject(context, object);
            if (updatedFile) {
                file.fields.push(
                    ...(await asyncArray(updatedFile.fields)
                        .filter(
                            async old =>
                                !(await mapInstance.relationMapping)?.fields
                                    .map(fieldNew => fieldNew.ID)
                                    .includes(old.ID),
                        )
                        .toArray()),
                );
                if (!file.relationshipFields) {
                    file.relationshipFields = [];
                }

                if (updatedFile.relationshipFields) {
                    const relationshipFieldsToPush = await asyncArray(updatedFile.relationshipFields)
                        .filter(async old =>
                            (await mapInstance.relationMapping).relationshipFields
                                ? !(await mapInstance.relationMapping).relationshipFields
                                      ?.map(
                                          (fieldNew: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties) =>
                                              fieldNew.ID,
                                      )
                                      .includes(old.ID)
                                : false,
                        )
                        .toArray();
                    if (relationshipFieldsToPush) {
                        file.relationshipFields.push(...relationshipFieldsToPush);
                    }
                }
                logger.debug(
                    () =>
                        `After : ${file.fields.length} fields -   ${
                            file.relationshipFields?.length || 0
                        } relationShip Fields`,
                );
            }
        }
        return file;
    }

    /**
     *  Get the the list of transaction from Intacct
     * @param context
     */
    @decorators.query<typeof IntacctMap, 'getIntacctTransactionsList'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    name: 'string',
                    object: 'string',
                },
            },
            isMandatory: true,
        },
    })
    static getIntacctTransactionsList(context: Context): Promise<xtremIntacct.classes.sdk.Interfaces.InspectObject[]> {
        return new xtremIntacct.classes.sdk.Functions.Inspect(context, '*').allObject();
    }

    /**
     *  Write file content is the content to write
     * By default it will write in data/mapping folder
     * @param context
     * @param content content to write in the file name of the file will be content.name
     */
    @decorators.mutation<typeof IntacctMap, 'writeStructure'>({
        isPublished: true,
        parameters: [{ name: 'object', type: 'string', isMandatory: true }],
        return: {
            type: 'boolean',
            isMandatory: true,
        },
        serviceOptions: () => [serviceOptions.DevTools],
    })
    static async writeStructure(context: Context, object: string): Promise<boolean> {
        const mapInstance = await context
            .query(xtremIntacctFinance.nodes.IntacctMap, {
                filter: {
                    thirdPartyObjectName: object,
                    application: '#intacct',
                },
            })
            .at(0);
        if (mapInstance) {
            if (await mapInstance.getFile) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-intacct-finance/nodes__intacct-map/file-already-exist',
                        'The file {{object}} already exist.',
                        { object },
                    ),
                );
            }
            const customRelationMapping = await mapInstance.customRelationMapping;
            if (!customRelationMapping) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-intacct-finance/nodes__intacct-map/nothing-to-write',
                        "The file {{object}}  can't be written.",
                        { object },
                    ),
                );
            }
            logger.debug(() => 'Write content');
            await xtremIntacctFinance.functions.mapping.writeMappingFile(context, customRelationMapping);
            return true;
        }

        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-intacct-finance/nodes__intacct-map/no-map-instance',
                'The file cannot be created because the {{object}} does not exist in Sage Intacct.',
                { object },
            ),
        );
    }

    /**
     * Get the file corresponding to the mapping of the object/node
     * @param context
     * @param content
     */
    @decorators.mutation<typeof IntacctMap, 'getStructure'>({
        isPublished: true,
        parameters: [{ name: 'content', type: 'string', isMandatory: false }],
        return: {
            type: 'string',
            isMandatory: true,
        },
    })
    static getStructure(context: Context, content: string): string {
        return JSON.stringify(xtremIntacctFinance.functions.mapping.readMappingFile(context, JSON.parse(content)));
    }

    /**
     *   GetDataINtacct Mix with xtrem data for generic ui array
     * @param context
     * @param name Name of the intacct Object
     * @param transaction Name of the intacct transaction
     * @param filters for now only where like whereValue
     */
    @decorators.query<typeof IntacctMap, 'getDataIntacct'>({
        isPublished: true,
        parameters: [
            { name: 'name', type: 'string', isMandatory: false },
            { name: 'transaction', type: 'string', isMandatory: false },
            { name: 'maxData', type: 'integer', isMandatory: false },
            {
                name: 'filters',
                type: 'array',
                isMandatory: false,
                item: { type: 'object', properties: { where: 'string', whereValue: 'string' } },
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    _id: 'string',
                    id: 'string',
                    name: 'string',
                    description: 'string',
                    whenModified: 'datetime',
                    whenCreated: 'datetime',
                    megaEntityId: 'string',
                    isLinked: 'boolean',
                    xtremSysId: 'string',
                    url: 'string',
                    xtremID: 'string',
                    xtremName: 'string',
                    xtremDescription: 'string',
                    integrationStatus: 'string',
                },
            },
        },
    })
    static async getDataIntacct(
        context: Context,
        name: string,
        transaction: string,
        maxData = 500,
        filters?: xtremIntacct.interfaces.WhereFields[],
    ): Promise<xtremIntacctFinance.interfaces.IntacctXtremData[]> {
        const mapInstance = await context
            .query(xtremIntacctFinance.nodes.IntacctMap, {
                filter: {
                    application: '#intacct',
                    thirdPartyObjectName: name,
                    intacctDescription: transaction,
                },
            })
            .at(0);
        if (mapInstance) {
            const file = await mapInstance.relationMapping;

            const fields = await getFieldsToRequest(mapInstance);
            logger.debug(() => JSON.stringify(fields, null, 4));

            const { idFieldName, nameFieldName, descriptionFieldName } = await getFieldsName(
                mapInstance.specificFields,
            );

            /**
             * because we don't know if we have HH:mm:ss or not
             * @param intacctDate
             */
            const parseDateFormatIntacct = (intacctDate: string) => {
                const dateFormatIntacct = 'MM/DD/YYYY HH:mm:ss';
                return intacctDate
                    ? datetime.parse(
                          intacctDate,
                          context.currentLocale as any,
                          dateFormatIntacct.slice(0, intacctDate.length),
                      )
                    : undefined;
            };

            /** For intacct data request return result  */
            const resultFunction = (result: IA.Xml.Response.Result): xtremIntacctFinance.interfaces.IntacctData[] => {
                return result.data
                    .map((gl: any): xtremIntacctFinance.interfaces.IntacctData => {
                        return {
                            id: idFieldName ? get(gl, idFieldName) : '', // IntacctId
                            name: nameFieldName ? get(gl, nameFieldName) : '', // Intacct name
                            description: descriptionFieldName ? get(gl, descriptionFieldName) : '', // Intacct description
                            whenModified: parseDateFormatIntacct(gl.WHENMODIFIED),
                            whenCreated: parseDateFormatIntacct(gl.WHENCREATED),
                            url: gl.RECORD_URL,
                            megaEntityId: gl.MEGAENTITYID,
                        };
                    })
                    .sort();
            };

            const { idFieldXtremName, nameFieldXtremName, descriptionFieldXtremName } = getXtreemFieldsName(
                file.fields,
                { idFieldName, nameFieldName, descriptionFieldName },
            );

            logger.debug(
                () =>
                    `xtrem fields : id ${idFieldXtremName} - name ${nameFieldXtremName} - description ${descriptionFieldXtremName}`,
            );

            /** Because  intacctFilter isn't declare everywhere */
            const intacctFilter =
                typeof (await mapInstance.xtremNode).intacctFilter === 'function'
                    ? await (await mapInstance.xtremNode).intacctFilter(context)
                    : [];

            const filtersFromPage: xtremIntacct.interfaces.WhereFields[] = filters
                ? filters?.map(filter => {
                      return { ...filter, type: 'like' };
                  })
                : [];

            const filterIntacctQuery = xtremIntacct.functions.manageFiltersForIntacct([
                ...intacctFilter,
                ...filtersFromPage,
            ]);

            const countIntacctData =
                (await new xtremIntacct.classes.sdk.Functions.Query<number>(context, {
                    objectName: name,
                    fields: [{ name: idFieldName, type: 'count' }],
                    filter: filterIntacctQuery,
                    resultFunction: result => get(result.data[0], `COUNT.${idFieldName}`, 0),
                }).execute()) || 0;

            if (countIntacctData > (maxData || 500)) {
                logger.error(
                    () =>
                        `${countIntacctData} lines with filter : ${JSON.stringify(filters)} limited to ${
                            maxData || 500
                        } `,
                );
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-instance-query-to-many-lines',
                        'Too many lines returned from Sage Intacct ({{countIntacctData}}). \n Add filters and try again.',
                        { countIntacctData },
                    ),
                );
            }

            /** Query for intacct to map to xtrem data  */
            const queryIntacctData = new xtremIntacct.classes.sdk.Functions.Query<
                xtremIntacctFinance.interfaces.IntacctData[]
            >(context, {
                objectName: name,
                fields,
                resultFunction,
                filter: filterIntacctQuery,
            });

            queryIntacctData.assignShowPrivate(await mapInstance.isPrivateShow);

            /** Data from intacct */
            const intacctData = await queryIntacctData.execute();

            await logger.debugAsync(
                async () => `Additionnal filter : ${JSON.stringify(await mapInstance.getAdditionalFilter())}`,
            );
            /**
             * Issue : Could not invoke operation Map.getDataIntacct : BusinessEntityAddress.intacctId:
             * NYI: resolving computed property to SQL : Error: BusinessEntityAddress.intacctId:
             * XT-*****
             */
            /** SDMO Data  */
            const xtremDataQuery = await getXtreemData(context, mapInstance, {
                filter: await manageFiltersForXtreem(mapInstance, filters),
            });

            let _id = 1;

            /** Xtreem & Intacct data linked together  */
            const intacctDataLinked = await asyncArray(intacctData)
                .map(async data => {
                    _id += 1;
                    let isLinked = false;
                    const xtremValue = xtremDataQuery
                        ? await asyncArray(xtremDataQuery).find(
                              async (xtremVal: xtremIntacct.nodes.IntacctNode) =>
                                  (await xtremVal.intacctId) === data.id,
                          )
                        : null;
                    if (xtremValue && xtremDataQuery) {
                        isLinked = true; // if we find an intacct value
                        // We delete the value from xtremDataQuery to not have them in the not linked data
                        xtremDataQuery.splice(
                            xtremDataQuery.findIndex(xtremData => xtremData === xtremValue),
                            1,
                        );
                    }
                    return {
                        _id: _id.toString(),
                        ...data,
                        isLinked,
                        ...(await getXtreemDataFormated(xtremValue, {
                            idFieldXtremName,
                            nameFieldXtremName,
                            descriptionFieldXtremName,
                        })),
                    };
                })
                .toArray();

            const queryData =
                (await asyncArray(xtremDataQuery || [])
                    .map(async line => {
                        _id += 1;
                        return {
                            _id: _id.toString(),
                            id: '',
                            name: '',
                            description: '',
                            whenModified: datetime.now(),
                            whenCreated: datetime.now(),
                            megaEntityId: '',
                            isLinked: false,
                            ...(await getXtreemDataFormated(line, {
                                idFieldXtremName,
                                nameFieldXtremName,
                                descriptionFieldXtremName,
                            })),
                        };
                    })
                    .toArray()) || [];

            return [...intacctDataLinked, ...queryData];
        }
        return [
            {
                _id: '',
                id: '',
                name: 'KO',
                description: 'No mapping for now ',
                whenModified: datetime.now(),
                whenCreated: datetime.now(),
                megaEntityId: '',
                isLinked: false,
                xtremSysId: '0',
                xtremID: '',
                xtremName: '',
                xtremDescription: '',
                integrationStatus: xtremCommunication.enums.IntegrationStateEnum.error.toLocaleString(),
            },
        ];
    }

    /**
     * Update the Xtrem instance from intacct payload
     * Create if the linked instance don't exist
     * @param context
     * @param intacctIdValue
     * @param intacctName
     * @returns
     */
    @decorators.mutation<typeof IntacctMap, 'deleteXtrem'>({
        isPublished: true,
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    message: 'string',
                    path: 'string',
                    severity: 'string',
                },
            },
        },
        parameters: [
            { name: 'intacctName', type: 'string', isMandatory: true },
            { name: 'intacctIdValue', type: 'string', isMandatory: false },
            { name: 'xtremSysId', type: 'string', isMandatory: false },
        ],
    })
    static async deleteXtrem(
        context: Context,
        intacctName: string,
        intacctIdValue?: string,
        xtremSysId?: string,
    ): Promise<Diagnose[]> {
        const mapInstance = await getMapInstance(context, intacctName);

        // TODO : Verify on intacct if the instance is really deleted !

        let filter: NodeKey<any> = {};
        if (xtremSysId) {
            filter = { _id: +xtremSysId };
        } else {
            /**
             * NYI: resolving computed property to SQL
             *  filter = intacctIdValue ? { intacctId: intacctIdValue, ...additionnalFilter } : additionnalFilter;
             *  */
            const additionnalFilter = await mapInstance.getAdditionalFilter<xtremIntacct.nodes.IntacctNode>();
            const query = await context
                .query<xtremIntacct.nodes.IntacctNode>(await mapInstance.xtremNode, {
                    filter: { _and: [additionnalFilter, { intacctId: intacctIdValue }] },
                })
                .toArray();
            if (query.length !== 1) {
                logger.warn(() => `delete query return ${query.length} to delete`);
            }
            filter = { _id: query.length === 1 ? query[0]._id : '' };
        }

        logger.debug(() => `Delete : ${JSON.stringify(filter)}`);

        const xtremNode = await context.read(await mapInstance.xtremNode, filter, { forUpdate: true });

        if (await mapInstance.canDelete) {
            await xtremNode.$.tryDelete();
        } else {
            (await mapInstance.xtremNode).deactivateRecord(context, xtremNode._id);
        }

        return context.diagnoses;
    }

    static async setDesynchronizedXtrem(
        context: Context,
        intacctName: string,
        intacctIdValue: string,
    ): Promise<xtremSynchronization.sharedFunctions.IntegrationDifference[]> {
        return (
            await xtremIntacctFinance.classes.IntacctSynchronizationManager.createFromIntacct(context, {
                id: intacctIdValue,
                name: intacctName,
            })
        )?.eventDesynchronized();
    }

    @decorators.asyncMutation<typeof IntacctMap, 'xtreemMassCreationJob'>({
        isPublished: true,
        isSchedulable: false,
        startsReadOnly: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    intacctName: 'string',
                    intacctIdValue: 'string',
                    xtremSysId: 'string',
                    isThrowingDiagnose: 'boolean',
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                created: { type: 'integer', isNullable: true },
                updated: { type: 'integer', isNullable: true },
                error: { type: 'integer', isNullable: true },
                message: { type: 'string', isNullable: true },
            },
        },
    })
    static async xtreemMassCreationJob(
        context: Context,
        data: xtremIntacctFinance.interfaces.IMap.IntacctToXtreemRequest,
    ): Promise<{ message: string; created: number; updated: number; error: number }> {
        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-start',
                'Mass creation of {{intacctName}} as started at {{dateTime}}.',
                { intacctName: data.intacctName, dateTime: datetime.now().toString() },
            ),
        );
        const mapInstance = await getMapInstance(context, data.intacctName);
        const payload = await getIntacctDataForCreateUpdate(context, mapInstance, data);
        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-number-of-lines',
                '{{numberOfLines}} to create/update.',
                { numberOfLines: payload.length },
            ),
        );

        const createdUpdated = await createUpdateXtreem(context, mapInstance, { isThrowingDiagnose: true }, payload);

        let message = '';
        if (data.intacctName === 'TAXDETAIL' && createdUpdated.created > 0) {
            message = context.localize(
                '@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-tax-category-update',
                '\n After integrating, you need to update the tax category.',
            );
        }

        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-end',
                'Mass creation of {{intacctName}} as finish at {{dateTime}}.',
                { intacctName: data.intacctName, dateTime: datetime.now().toString() },
            ),
        );

        return { ...createdUpdated, message };
    }

    /**
     *  Launch the notify that Create / Update all Intacct data From Xtrem
     */
    @decorators.mutation<typeof IntacctMap, 'createUpdateAllIntacct'>({
        isPublished: true,
        startsReadOnly: true,
        return: {
            type: 'string',
        },
        parameters: [{ name: 'intacctName', type: 'string', isMandatory: true }],
    })
    static async createUpdateAllIntacct(context: Context, intacctName: string): Promise<string> {
        const mapInstance = await getMapInstance(context, intacctName);

        const currentMassImport = await context.tryRead(xtremCommunication.nodes.SysNotificationState, {
            notificationId: await mapInstance.notificationId,
        });
        const runningMutation: xtremCommunication.enums.NotificationStatus[] = ['pending', 'running'];

        if (currentMassImport && runningMutation.includes((await currentMassImport?.status) || 'success')) {
            return context.localize('@sage/xtrem-intacct-finance/already-queued', 'The action is already running.');
        }

        await context.runInWritableContext(async writableContext => {
            const notificationId = await writableContext.notify(
                'IntacctMap/intacctMassCreationJob/start',
                {
                    intacctName,
                },
                { replyTopic: 'SysNotificationState/updateStatus' },
            );

            await writableContext.bulkUpdate(xtremSynchronization.nodes.BaseMapping, {
                set: { notificationId },
                where: { _id: mapInstance._id },
            });
        });

        return context.localize('@sage/xtrem-intacct-finance/action-queued', 'The action has been queued.');
    }

    /** Xtreem to Intacct  */
    @decorators.asyncMutation<typeof IntacctMap, 'intacctMassCreationJob'>({
        isPublished: true,
        isSchedulable: false,
        startsReadOnly: true,
        parameters: [
            { name: 'intacctName', type: 'string' },
            { name: 'type', type: 'string' },
        ],
        return: 'boolean',
    })
    static async intacctMassCreationJob(context: Context, intacctName: string, type: string): Promise<boolean> {
        const massIntegration = await xtremIntacctFinance.classes.MassIntegration.create(context, intacctName);
        if (type === 'afterImport') {
            massIntegration.setFilter({ state: 'not' });
            await massIntegration.start();
            return false;
        }
        await massIntegration.startAllXtreem();
        return true;
    }

    /**
     *  Gets intacct url of object integrated
     * @param _id record identify
     * @param nodeName item/customer/supplier
     * @returns intacct url
     */
    @decorators.mutation<typeof IntacctMap, 'getIntacctUrl'>({
        isPublished: true,
        parameters: [
            {
                name: '_id',
                type: 'integer',
                isMandatory: true,
            },
            {
                name: 'nodeName',
                type: 'string',
                isMandatory: true,
            },
        ],
        return: {
            type: 'string',
            isMandatory: true,
        },
    })
    static async getIntacctUrl(
        context: Context,
        _id: number,
        nodeName: 'item' | 'supplier' | 'customer',
    ): Promise<string> {
        const typeNode = xtremIntacct.functions.getIntacctNode(context, nodeName);
        const myNode = (await context.tryRead(typeNode, { _id } as NodeKey<Node>, {
            forUpdate: true,
        })) as xtremIntacct.nodes.IntacctNode;

        const intacctUrl = await xtremIntacctFinance.functions.getIntacctObjectUrl(
            context,
            await myNode.recordNo,
            nodeName,
        );

        await myNode.$.set({ intacctUrl });
        await myNode.$.save();

        return intacctUrl;
    }
}
