import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacctFinance from '..';

@decorators.subNode<IntacctBankAccountTransactionFeedLineDimension>({
    extends: () => xtremFinanceData.nodes.BaseFinanceLineDimension,
    isPublished: true,
    serviceOptions: () => [xtremIntacctFinance.serviceOptions.intacctCashbookManagement],
})
export class IntacctBankAccountTransactionFeedLineDimension extends xtremFinanceData.nodes.BaseFinanceLineDimension {
    /** Override of the  originLine to be able to get the right type for the property */
    @decorators.referencePropertyOverride<IntacctBankAccountTransactionFeedLineDimension, 'originLine'>({
        node: () => xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeedLine,
    })
    override readonly originLine: Reference<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeedLine>;
}
