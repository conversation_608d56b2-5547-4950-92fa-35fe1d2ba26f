import type { Context } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremTax from '@sage/xtrem-tax';
import { intacctCashbookManagement } from '../service-options/intacct-cashbook-management';

@decorators.subNode<IntacctCashBookManagement>({
    extends: () => xtremStructure.nodes.BaseOptionManagement,
    isPublished: true,
})
export class IntacctCashBookManagement extends xtremStructure.nodes.BaseOptionManagement {
    @decorators.query<typeof IntacctCashBookManagement, 'isServiceOptionActiveFunction'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'boolean',
        },
    })
    static isServiceOptionActiveFunction(context: Context) {
        return IntacctCashBookManagement.baseIsServiceOptionActiveFunction(context, intacctCashbookManagement);
    }

    @decorators.mutation<typeof IntacctCashBookManagement, 'serviceOptionChange'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'boolean',
        },
    })
    static async serviceOptionChange(context: Context): Promise<boolean> {
        const serviceOptionState = await IntacctCashBookManagement.baseServiceOptionChange(
            context,
            intacctCashbookManagement,
        );

        if (serviceOptionState) {
            const taxSolutionIds = await xtremFinanceData.functions.Common.getTaxSolutionIdsForLegislation({
                context,
                legislationId: 'ZA',
            });
            await context.bulkUpdate(xtremTax.nodes.TaxSolutionLine, {
                set: { isSubjectToGlTaxExcludedAmount: true },
                where: {
                    taxSolution: { _id: { _in: taxSolutionIds } },
                    taxCategory: { id: 'VAT' },
                },
            });
        }

        return serviceOptionState;
    }
}
