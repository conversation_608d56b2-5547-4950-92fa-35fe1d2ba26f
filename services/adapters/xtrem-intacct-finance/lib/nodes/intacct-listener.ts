import * as xtremCommunication from '@sage/xtrem-communication';
import type { Diagnose, NodeCreateData } from '@sage/xtrem-core';
import {
    Context,
    datetime,
    decorators,
    Logger,
    Node,
    SystemError,
    TextStream,
    ValidationSeverity,
} from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremIntacctFinance from '../../index';
import { updateXtrem } from '../functions/map';
import { desynchronizedEventFromIntacct } from '../functions/synchronization';
import type { SyncState } from '../interfaces/synchronization';

const logger = Logger.getLogger(__filename, 'listener');
const xtremIntacctFinancePackageName = 'xtrem-intacct-finance';
@decorators.node<IntacctListener>({
    package: xtremIntacctFinancePackageName,
    isPublished: true,
})
export class IntacctListener extends Node {
    @decorators.messageListener<typeof IntacctListener, xtremIntacct.interfaces.IntacctEventPayload>({
        queue: () => xtremIntacct.queues.intacctReceive,
        integrationSolution: 'intacct',
        format: 'json',
        startsReadOnly: true,
        getTenantId(envelope) {
            return (
                envelope.attributes.tenantId ||
                envelope.attributes.TenantId ||
                envelope.attributes.ClaimedTenantId ||
                ''
            );
        },
        async getContextAttributes(context, envelope) {
            return {
                user: await Context.accessRightsManager.getCurrentUser(context),
                locale: envelope.attributes.locale ? envelope.attributes.locale : context.currentLocale,
            };
        },
        getId(_context, envelope) {
            return IntacctListener.getId(envelope);
        },
        async onError(context, envelope, error) {
            await context.runInWritableContext(async writableContext => {
                await IntacctListener.updateMessageHistory(writableContext, envelope, {
                    message: [error.message],
                    status: xtremIntacct.enums.ListenerStatusEnum.error,
                    receivedRequest: JSON.stringify(envelope.payload),
                });
            });
        },
    })
    static async onMessageFromIntacctQueue(
        context: Context,
        data: xtremCommunication.MessageEnvelope<xtremIntacct.interfaces.IntacctEventPayload>,
    ): Promise<void> {
        logger.debug(() => `Intacct response: ${JSON.stringify(data, null, 4)}`);

        if (context.configuration.deploymentMode !== 'development' && context.tenantId === null) {
            throw new SystemError(
                `deploymentMode : ${context.configuration.deploymentMode} TenantId : ${context.tenantId}`,
            );
        }

        await context.runInWritableContext(async writableContext => {
            await IntacctListener.checkMessageHistory(writableContext, data);
        });

        const returnMessages = await IntacctListener.intacctEvent(context, data);

        await context.runInWritableContext(async writableContext => {
            await IntacctListener.updateMessageHistory(writableContext, data, returnMessages);
        });
    }

    /**
     *  Comming from smartEvent in intacct
     * @param context
     * @param data
     * @returns
     */
    static async intacctEvent(
        context: Context,
        data: xtremCommunication.MessageEnvelope<xtremIntacct.interfaces.IntacctEventPayload>,
    ): Promise<xtremIntacctFinance.interfaces.ListenerReturn> {
        let diagnose: Diagnose[] = [];
        // Attributes to handle :  S3Payload TimedOut

        const { payload }: { payload: xtremIntacct.interfaces.IntacctEventPayload } = data;
        logger.debug(() => `${payload.change} ${payload.object}: ${payload.recordId}`);

        /** Asynchronous test - do we still need it  ?  */
        if (payload.object?.toUpperCase() === 'LOCATIONENTITY') {
            return {
                status: xtremIntacct.enums.ListenerStatusEnum.done,
                message: ['Asynchronous test received'],
                receivedRequest: JSON.stringify(payload),
            };
        }

        /** We don't manage intacctCallback anymore  */
        if (data.attributes.MessageKind !== 'IntacctEvent') {
            return {
                status: xtremIntacct.enums.ListenerStatusEnum.received,
                message: [`${data.attributes.MessageKind} not implemented`],
                receivedRequest: JSON.stringify(payload),
            };
        }

        const isIntacctObjectManaged = await context
            .query(xtremIntacctFinance.nodes.IntacctMap, { filter: { isSmartEvent: true } })
            .some(async object => {
                return (await object.id) === payload.object.toUpperCase();
            });

        /**
         * Check if the payload Object is in available Intacct object List
         */
        if (!isIntacctObjectManaged) {
            const error = context.localize(
                '@sage/xtrem-intacct-finance/nodes__intacct-listener/not-implemented',
                `Not implemented: {{change}}:{{object}}.`,
                {
                    object: data.payload.object,
                    change: data.payload.change,
                },
            );
            diagnose.push({
                severity: ValidationSeverity.error,
                path: [xtremIntacctFinancePackageName, 'intacct-listener', 'intacctEvent'],
                message: error,
            });
            logger.error(() => error);
            return {
                status: xtremIntacct.enums.ListenerStatusEnum.error,
                message: diagnose.map(diag => diag.message),
                receivedRequest: JSON.stringify(payload),
            };
        }

        switch (payload.change) {
            case 'update':
            case 'create':
                diagnose = (
                    await updateXtrem(context, {
                        intacctName: payload.object.toUpperCase(),
                        intacctIdValue: payload.recordId,
                        isThrowingDiagnose: false,
                    })
                ).diagnose;
                break;
            case 'delete':
                diagnose = await context.runInWritableContext(writableContext => {
                    return xtremIntacctFinance.nodes.IntacctMap.deleteXtrem(
                        writableContext,
                        payload.object.toUpperCase(),
                        payload.recordId,
                    );
                });
                break;
            case 'desynchronized':
                diagnose = await desynchronizedEventFromIntacct(context, {
                    name: payload.object.toUpperCase(),
                    id: payload.recordId,
                });

                break;
            default:
                diagnose.push({
                    severity: ValidationSeverity.warn,
                    path: [xtremIntacctFinancePackageName, 'intacct-listener', 'intacctEvent'],
                    message: `unknow payload change ${payload.change}`,
                });
                break;
        }

        logger.debug(() => JSON.stringify(diagnose));
        const status = diagnose.some(diag =>
            [ValidationSeverity.error, ValidationSeverity.warn].includes(diag.severity),
        )
            ? xtremIntacct.enums.ListenerStatusEnum.error
            : xtremIntacct.enums.ListenerStatusEnum.done;
        return {
            status,
            message: diagnose.map(diag => `${diag.severity.toLocaleString()} : ${diag.message}`),
            receivedRequest: JSON.stringify(payload),
        };
    }

    /**
     * Get the id of the sysCommunicationHistory
     * lineId in case of intacctEvent, messageId if there isn't
     * @param data : MessageEnvelope
     * @returns  Id:string
     */
    static getId(data: xtremCommunication.MessageEnvelope<xtremIntacct.interfaces.IntacctEventPayload>): string {
        return String(data.payload.lineId || data.attributes.messageId);
    }

    /**
     *  Check if the communication history already exist - if not create it
     * @param context
     * @param data
     * @returns
     */
    static async checkMessageHistory(
        context: Context,
        data: xtremCommunication.MessageEnvelope<xtremIntacct.interfaces.IntacctEventPayload>,
    ): Promise<xtremCommunication.nodes.SysMessageHistory | null> {
        const id = IntacctListener.getId(data);

        if (!id) {
            logger.error(() => `ControlId & lineId & messageId not found `);
        }

        const history = id
            ? await context.tryRead(
                  xtremCommunication.nodes.SysMessageHistory,
                  {
                      integrationSolution: 'intacct',
                      id,
                  },
                  { forUpdate: true },
              )
            : null;
        if (!history) {
            await IntacctListener.createMessageHistory(context, data);
        }
        logger.debug(() => `check - history id :  ${history?._id || 'no history'}`);
        return history;
    }

    /**
     *  Only creation of SysMessageHistory node ( integrationSolution / id / receivedRequest / attributes )
     *  id will be the lineId of the payload
     * @param context
     * @param data
     */
    static createMessageHistory(
        context: Context,
        data: xtremCommunication.MessageEnvelope<xtremIntacct.interfaces.IntacctEventPayload>,
    ): Promise<xtremCommunication.nodes.SysMessageHistory> {
        const receivedRequest =
            typeof data.payload === 'object'
                ? TextStream.fromJsonObject(data.payload)
                : TextStream.fromString(data.payload, 'text/plain');

        const historyData = {
            integrationSolution: 'intacct',
            id: IntacctListener.getId(data),
            sentRequest: TextStream.empty,
            receivedRequest,
            status: 'received',
            attributes: data.attributes,
        } as NodeCreateData<xtremCommunication.nodes.SysMessageHistory>;

        if (data.payload?.object) {
            const eventPayload = data.payload;

            historyData.context = {
                function: eventPayload.change,
                nodeName: eventPayload.object,
                parameters: eventPayload.recordId,
                intacctIdName: eventPayload.intacctIdName,
                lineId: eventPayload.lineId,
            } as xtremIntacct.sharedFunctions.interfaces.ContextCallback;
        }

        return xtremCommunication.nodes.SysMessageHistory.createOrUpdateMessageHistory(context, historyData);
    }

    /**
     *  First get the communication history - if not exist return false
     *  updating then return true
     * @param context
     * @param data
     * @param dataHistory
     */

    static async updateMessageHistory(
        context: Context,
        data: xtremCommunication.MessageEnvelope<xtremIntacct.interfaces.IntacctEventPayload>,
        dataHistory: xtremIntacctFinance.interfaces.ListenerReturn,
    ): Promise<boolean> {
        const diagnose = dataHistory.message;
        const history = await IntacctListener.checkMessageHistory(context, data);
        if (!history) {
            logger.error(() => 'No Communication history !');
            return false;
        }

        const receivedRequest = dataHistory.receivedRequest || data.payload;

        await history.$.set({
            receivedRequest:
                typeof receivedRequest === 'object'
                    ? TextStream.fromJsonObject(receivedRequest)
                    : TextStream.fromString(receivedRequest, 'text/plain'),
        });

        switch (dataHistory.status) {
            case xtremIntacct.enums.ListenerStatusEnum.error:
                await history.$.set({ status: 'error' });
                break;
            case xtremIntacct.enums.ListenerStatusEnum.received:
                await history.$.set({ status: 'received' });
                break;
            case xtremIntacct.enums.ListenerStatusEnum.notImplentedYet:
                await history.$.set({ status: 'received' });
                diagnose.push('not implented yet');
                break;
            case xtremIntacct.enums.ListenerStatusEnum.done:
                await history.$.set({
                    receivedStamp: (await history.receivedStamp) ? await history.receivedStamp : datetime.now(),
                    status: 'success',
                });
                break;
            default:
                await history.$.set({ status: 'received' });
        }
        await history.$.set({
            communicationDiagnoses: { messages: diagnose, ...(await history.communicationDiagnoses) },
        });
        logger.debug(() => `updating history id : ${history._id}`);

        if (!(await history.$.trySave())) {
            logger.warn(() => `Saving history issue :\n ${history.$.context.diagnoses.join('\n')}`);
        }
        return true;
    }

    @decorators.notificationListener<typeof IntacctListener>({
        topic: 'xtremImportExport/completed',
    })
    static async importCompleted(context: Context, payload: { nodeName: string }) {
        const mapping = await context
            .query(xtremIntacctFinance.nodes.IntacctMap, {
                filter: { application: '#intacct', nodeFactory: { name: payload.nodeName } },
                first: 1,
            })
            .at(0);
        /** Only for item customer supplier & contact for now  */
        if (mapping && ['Item', 'Customer', 'Supplier', 'Contact'].includes(await mapping.intacctDescription)) {
            await context.notify(
                'IntacctMap/intacctMassCreationJob/start',
                {
                    intacctName: await mapping.id,
                    type: 'afterImport',
                },
                { replyTopic: 'SysNotificationState/updateStatus' },
            );
        }
    }

    @decorators.asyncMutation<typeof IntacctListener, 'deleteIntacct'>({
        parameters: [
            {
                name: 'intacctNode',
                type: 'string',
            },
            {
                name: 'recordNo',
                type: 'string',
            },
        ],
        return: { type: 'string' },
        startsReadOnly: true,
        isPublished: true,
    })
    static async deleteIntacct(
        context: Context,
        intacctNode: string,
        recordNo: string,
    ): Promise<xtremCommunication.enums.IntegrationState> {
        const deleteObject = new xtremIntacct.classes.sdk.Functions.Delete(context, {
            objectName: intacctNode,
            recordNo,
        });
        await deleteObject.execute();

        if (deleteObject.diagnoses.length) {
            await context.batch.logMessage('error', deleteObject.diagnosesMessages());
            return 'error';
        }
        await context.batch.logMessage(
            'info',
            context.localize('@sage/xtrem-intacct-finance/nodes__intacct-listener/delete-success', `Deleted.`),
        );
        await context.batch.updateProgress({ detail: 'deleted', successCount: 1, totalCount: 1 });
        return 'success';
    }

    @decorators.asyncMutation<typeof IntacctListener, 'synchronizeNode'>({
        parameters: [
            {
                name: 'intacctNode',
                type: 'string',
            },
        ],
        return: {
            type: 'object',
            properties: {
                _id: 'string',
                sysId: 'string',
                state: 'string',
                node: { type: 'object', properties: { title: 'string' } },
            },
        },
        startsReadOnly: true,
        isPublished: true,
    })
    static async synchronizeNode(context: Context, intacctNode: string): Promise<Partial<SyncState>> {
        if (!(await xtremIntacct.functions.isIntacctActive(context))) {
            await context.batch.logMessage('warning', 'Intacct is disable');
            return { state: 'not' };
        }

        const nodeIntacct = await context.read(xtremIntacctFinance.nodes.IntacctSynchronizationState, {
            _id: intacctNode,
        });
        const nodeXtreem = await nodeIntacct.node;

        let detail = `${await (await nodeIntacct.node).title} - ${await nodeIntacct.intacctId}`;

        await context.batch.updateProgress({
            detail,
            phase: context.localize(
                '@sage/xtrem-intacct-finance/nodes__intacct-listener/synchronize-node-start',
                `Synchronization started.`,
            ),
            successCount: 0,
            totalCount: 1,
        });

        const state = await nodeIntacct.synchronizeBulkMutation({ isForce: false });

        const phase = context.localize(
            '@sage/xtrem-intacct-finance/nodes__intacct-listener/synchronize-node-finish',
            `Synchronization {{state}}.`,
            { state },
        );
        detail = `${await (await nodeIntacct.node).title} - ${await nodeIntacct.intacctId}`;

        await context.batch.updateProgress({
            detail,
            successCount: state === 'success' ? 1 : 0,
            errorCount: state !== 'success' ? 1 : 0,
            phase,
        });

        if (state === 'error') {
            const base64Key = Buffer.from(JSON.stringify({ _id: await nodeIntacct.sysIdLink })).toString('base64');
            await context.notifyUser({
                title: detail,
                description: phase,
                icon: 'sync',
                level: 'error',
                shouldDisplayToast: true,
                actions: [
                    {
                        link: `${await nodeIntacct.pageLink}/${base64Key}`,
                        style: 'link',
                        title: await (await nodeIntacct.node).title,
                    },
                ],
            });
        }

        return {
            state,
            _id: nodeIntacct._id,
            node: await nodeXtreem.name,
            sysId: (await nodeIntacct.sysId).toString(),
        };
    }
}
