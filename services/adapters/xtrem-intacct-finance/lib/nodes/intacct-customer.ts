import type { Context, NodeCreateData, NodeQueryFilter, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremIntacctFinance from '..';
import { contactPayload } from '../functions/synchronization';
import type { SyncState } from '../interfaces/synchronization';
import { IntacctCustomerSupplier } from './intacct-customer-supplier';

@decorators.subNode<IntacctCustomer>({
    extends: () => IntacctCustomerSupplier,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isVitalReferenceChild: true,
})
export class IntacctCustomer extends IntacctCustomerSupplier {
    override getSyncStateReference() {
        return this.parent;
    }

    override getMapping() {
        return this.$.context.read(xtremIntacctFinance.nodes.IntacctMap, {
            application: '#intacct',
            thirdPartyObjectName: 'CUSTOMER',
            nodeFactory: `#${xtremMasterData.nodes.Customer.name}`,
        });
    }

    @decorators.referenceProperty<IntacctCustomer, 'parent'>({
        node: () => xtremMasterData.nodes.Customer,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
    })
    override readonly parent: Reference<xtremMasterData.nodes.Customer>;

    @decorators.stringPropertyOverride<IntacctCustomer, 'status'>({
        async getValue() {
            return (await (await this.parent).isActive) ? 'active' : 'inactive';
        },
    })
    override readonly status: Promise<string>;

    override async getPageUrl() {
        return `${await (await (await this.node).package).name}/Customer`;
    }

    /**
     * Linked to BILLTOKEY / BILLTO.CONTACTNAME
     */
    @decorators.jsonProperty<IntacctCustomer, 'billToAddress'>({
        isPublished: true,
        isNullable: true,
        async computeValue() {
            const parent = await this.parent;
            const billToAddress = await parent.billToAddress;
            if (!billToAddress) {
                await this.$.context.logger.warnAsync(
                    async () => `No billTo address for ${await (await parent.businessEntity).name}`,
                );
                return null;
            }
            if (!(await this.hideDisplayContact) && billToAddress._id === (await parent.primaryAddress)._id) {
                /** Same as displayAddress */
                return null;
            }
            return contactPayload(billToAddress);
        },
    })
    readonly billToAddress: Promise<SyncState | null>;

    /**
     * Linked to SHIPTOKEY / SHIPTO.CONTACTNAME
     */
    @decorators.jsonProperty<IntacctCustomer, 'shipToAddress'>({
        isPublished: true,
        isNullable: true,
        async computeValue() {
            const parent = await this.parent;
            const shipToAddress = await parent.primaryShipToAddress;
            if (!shipToAddress) return null;
            if (!(await this.hideDisplayContact) && shipToAddress._id === (await parent.primaryAddress)._id) {
                /** Same as displayAddress */
                return null;
            }
            return contactPayload(shipToAddress);
        },
    })
    readonly shipToAddress: Promise<SyncState | null>;

    async getOtherAddress(businessEntityAddress: xtremMasterData.nodes.BusinessEntityAddress) {
        const shipToAddress = await (await this.parent).primaryShipToAddress;
        const billToAddress = await (await this.parent).billToAddress;

        if (shipToAddress?._id === businessEntityAddress._id) {
            return this.$.context.localize(
                '@sage/xtrem-intacct-finance/intacct-address-ship-to-primary',
                'Primary ship-to address',
            );
        }
        if (billToAddress._id === businessEntityAddress._id) {
            return this.$.context.localize('@sage/xtrem-intacct-finance/intacct-address-bill-to', 'Bill-to address');
        }
        return null;
    }

    override async getAddressCategory(businessEntityAddress: xtremMasterData.nodes.BusinessEntityAddress) {
        return (await super.getAddressCategory(businessEntityAddress)) ?? this.getOtherAddress(businessEntityAddress);
    }

    override fromIntacctFields = ['creditLimit', 'onHold'];

    static override initPayload(context: Context, payload: any): NodeCreateData<xtremMasterData.nodes.Customer> {
        context.logger.debug(() => `initPayload : ${JSON.stringify(payload)}`);
        return payload;
    }

    static override xtremFilter(filterValue: string): NodeQueryFilter<xtremMasterData.nodes.Customer> {
        return { businessEntity: { id: { _regex: filterValue } } };
    }

    override async onDesynchronized(intacctData: { creditLimit: number | string; onHold: boolean | string }[]) {
        // If the value sent by intacct is not correct (undefined or bad string), we set it to 0
        const creditLimit = Number(intacctData[0].creditLimit);
        await this.$.context.bulkUpdate(xtremMasterData.nodes.Customer, {
            set: {
                creditLimit: Number.isNaN(creditLimit) ? 0 : creditLimit,
                isOnHold: intacctData[0].onHold === 'true' || intacctData[0].onHold === true,
            },
            where: { _id: (await this.parent)._id },
        });
    }
}
