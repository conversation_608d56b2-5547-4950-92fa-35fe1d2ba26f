import type { AnyRecord, AsyncResponse, Context, NodeQueryFilter, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremIntacctFinance from '..';
import { IntacctSynchronizationState } from './intacct-synchronization-state';

@decorators.subNode<IntacctItem>({
    extends: () => IntacctSynchronizationState,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isVitalReferenceChild: true,
})
export class IntacctItem extends IntacctSynchronizationState {
    override getSyncStateReference() {
        return this.item;
    }

    override getMapping() {
        return this.$.context.read(xtremIntacctFinance.nodes.IntacctMap, {
            application: '#intacct',
            thirdPartyObjectName: 'ITEM',
            nodeFactory: `#${xtremMasterData.nodes.Item.name}`,
        });
    }

    override async getIntacctId() {
        return (await this.item).id;
    }

    @decorators.referenceProperty<IntacctItem, 'item'>({
        node: () => xtremMasterData.nodes.Item,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    /** Linked to intacct STATUS */
    @decorators.stringProperty<IntacctItem, 'status'>({
        isPublished: true,
        async getValue() {
            return (await (await this.item).status) === 'active' ? 'active' : 'inactive';
        },
        async setValue(val: string) {
            await (await this.item).$.set({ status: val === 'Active' ? 'active' : 'inDevelopment' });
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly status: Promise<string>;

    /** intacct link to ITEMTYPE */
    @decorators.stringProperty<IntacctItem, 'type'>({
        isPublished: true,
        getValue() {
            return 'Non-Inventory';
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly type: Promise<string>;

    static override initPayload(context: Context, payload: any): AsyncResponse<AnyRecord> {
        payload.salesUnit = payload.intactItem.salesUnit ? payload.intactItem.salesUnit : payload.intactItem.stockUnit;
        payload.weight = payload.intactItem.shipWeight;
        context.logger.debug(() => `initPayload : ${JSON.stringify(payload)}`);
        return payload;
    }

    static override xtremFilter(filterValue: string): NodeQueryFilter<xtremMasterData.nodes.Item> {
        return { id: { _regex: filterValue } };
    }
}
