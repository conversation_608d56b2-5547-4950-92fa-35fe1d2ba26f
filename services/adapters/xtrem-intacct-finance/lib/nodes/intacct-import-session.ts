import type { Collection, Context, integer, Reference } from '@sage/xtrem-core';
import { date, decorators, Node } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremImportExport from '@sage/xtrem-import-export';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremIntacctFinance from '..';

const packageName = 'xtrem-intacct-finance';

@decorators.node<IntacctImportSession>({
    package: packageName,
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isClearedByReset: true,
    indexes: [],
    serviceOptions: () => [xtremIntacctFinance.serviceOptions.intacctCashbookManagement],
})
export class IntacctImportSession extends Node {
    @decorators.stringProperty<IntacctImportSession, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<IntacctImportSession, 'mapObject'>({
        isStored: true,
        isPublished: true,
        node: () => xtremIntacctFinance.nodes.IntacctMap,
    })
    readonly mapObject: Reference<xtremIntacctFinance.nodes.IntacctMap>;

    // Date of the import
    @decorators.dateProperty<IntacctImportSession, 'dateCreation'>({
        isStored: true,
        isPublished: true,
        defaultValue: () => date.today(),
    })
    readonly dateCreation: Promise<date>;

    @decorators.jsonProperty<IntacctImportSession, 'queryParameters'>({
        isStored: true,
        isPublished: true,
    })
    readonly queryParameters: Promise<any>;

    @decorators.integerProperty<IntacctImportSession, 'numberOfLinesToImport'>({
        isStored: true,
        isPublished: true,
        defaultValue: 0,
    })
    readonly numberOfLinesToImport: Promise<integer>;

    @decorators.integerProperty<IntacctImportSession, 'linesImported'>({
        isPublished: true,
        getValue() {
            return this.transactionFeed.length;
        },
    })
    readonly linesImported: Promise<integer>;

    @decorators.enumProperty<IntacctImportSession, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremImportExport.enums.importStatusEnumDataType,
        defaultValue: 'pending',
    })
    readonly status: Promise<xtremImportExport.enums.ImportStatus>;

    @decorators.referenceProperty<IntacctImportSession, 'bankAccount'>({
        isStored: true,
        isPublished: true,
        node: () => xtremFinanceData.nodes.BankAccount,
        isNullable: true, // XT-80680 changed during bank account refactor since cash book management will be removed
        async control(cx) {
            if (
                (await (await this.bankAccount)?.currency)?._id !==
                (await (await (await (await this.bankAccount)?.financialSite)?.legalCompany)?.currency)?._id
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-intacct-finance/nodes__intacct_import_session__currencies_do_not_match',
                    'The bank account currency must be the same as the bank account financial site currency.',
                );
            }
        },
    })
    readonly bankAccount: Reference<xtremFinanceData.nodes.BankAccount | null>;

    @decorators.collectionProperty<IntacctImportSession, 'transactionFeed'>({
        isPublished: true,
        isVital: true,
        node: () => xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed,
        reverseReference: 'importSession',
    })
    readonly transactionFeed: Collection<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed>;

    static getPostMessage(context: Context, description: string): string {
        return context.localize(
            '@sage/xtrem-intacct-finance/intacct_bank_account_transaction_feed_has_no_lines_to_post',
            'The Sage Intacct bank account transaction feed {{intacctbankAccountTransactionFeed}} has no lines to post.',
            {
                intacctbankAccountTransactionFeed: description,
            },
        );
    }
}
