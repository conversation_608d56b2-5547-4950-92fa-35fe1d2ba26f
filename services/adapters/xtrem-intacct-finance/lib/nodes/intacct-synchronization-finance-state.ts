import type * as xtremCommunication from '@sage/xtrem-communication';
import type { Context, NodeCreateData, Reference } from '@sage/xtrem-core';
import { BusinessRuleError, datetime, decorators } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacct from '@sage/xtrem-intacct';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import { get } from 'lodash';
import * as xtremIntacctFinance from '..';
import { afterPostDocument, replyToOriginalDocuments } from '../functions/finance';
import type { FinanceDocumentQuery } from '../interfaces/finance';
import { IntacctSynchronizationState } from './intacct-synchronization-state';

@decorators.subNode<IntacctSynchronizationFinanceState>({
    extends: () => IntacctSynchronizationState,
    canCreate: true,
    isAbstract: true,
    async createEnd() {
        await this.$.set(await this.getFinanceTransaction());
    },
    async saveBegin() {
        await this.$.set(await this.getFinanceTransaction());
    },
})
export class IntacctSynchronizationFinanceState extends IntacctSynchronizationState {
    readonly document: Reference<xtremFinance.functions.FinanceTypeNode>;

    @decorators.jsonProperty<IntacctSynchronizationFinanceState, 'externalIntegration'>({
        isPublished: true,
        async getValue() {
            return {
                app: 'intacct',
                recordId: await this.intacctId,
                url: await this.url,
            };
        },
    })
    readonly externalIntegration: Promise<xtremFinanceData.interfaces.FinanceExternalIntegration>;

    async getPostingDateField() {
        const mapping = await (await this.getMapping()).relationMapping;
        return mapping.fields.find(field => field.xtremProperty === 'postingDate')?.ID || '';
    }

    async getWritableDocument(context: Context): Promise<xtremFinance.functions.FinanceTypeNode> {
        const financeDocument = (await context.read(
            (await this.document).$.factory.nodeConstructor,
            { _id: (await this.document)._id },
            { forUpdate: true },
        )) as xtremFinance.functions.FinanceTypeNode;
        // To allow update of postingStatus
        financeDocument.skipCallApi = true;
        financeDocument.pCanUpdateFromExternalIntegration = true;

        return financeDocument;
    }

    /** For finance documents the intacctId is comming from intacct we don't set it there  */
    override async getIntacctId() {
        return (await this.intacctId) ?? '';
    }

    override intacctIdField = 'RECORDNO';

    documentNumberField = 'DOCNUMBER';

    async getDocumentType() {
        await this.$.context.logger.debugAsync(
            async () => `${await (await this.node).title}(${await this.$.getNaturalKeyValue()}) : No document type`,
        );
        return '';
    }

    /** used by the checkExist  */
    async getIntacctDocumentFilter(): Promise<xtremIntacct.classes.sdk.Interfaces.Ifilter> {
        const postingDateField = await this.getPostingDateField();

        const documentNumber = await (await this.document).number;
        // To be deleted
        const postingDate = await (await this.document).postingDate;

        const documentNumberFilter = new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(
            this.documentNumberField,
        ).equalTo(documentNumber);

        const postingDateFilter = postingDateField
            ? new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(postingDateField).equalTo(
                  postingDate.format('MM/DD/YYYY'),
              )
            : null;

        return postingDateFilter
            ? new xtremIntacct.classes.sdk.Functions.QueryOperator.AndOperator([
                  documentNumberFilter,
                  postingDateFilter,
              ])
            : documentNumberFilter;
    }

    override checkExist(): Promise<undefined> {
        this.$.context.logger.debug(() => `We don't check if it exist on IntacctSynchronizationManager `);
        return Promise.resolve(undefined);
    }

    async checkExistForRetry(): Promise<FinanceDocumentQuery | undefined> {
        const { intacctIdField } = this;

        // intacct object and property to filter
        const fields: (string | xtremIntacct.interfaces.QueryFields)[] = [
            { name: 'RECORD_URL', type: 'select' },
            { name: 'RECORDNO', type: 'select' },
        ];

        if (intacctIdField) {
            fields.push({ name: intacctIdField, type: 'select' });
        }

        const checkAtEntityLevel = ['bankReconciliationWithdrawal', 'bankReconciliationDeposit'].includes(
            await this.getDocumentType(),
        );

        /** Don't we need to add more filters if we have documents that have documentNumber */
        const intacctQuery = new xtremIntacct.classes.sdk.Functions.Query<FinanceDocumentQuery[]>(this.$.context, {
            objectName: await (await this.mapping).thirdPartyObjectName,
            fields,
            entityId: await this.entityId,
            filter: await this.getIntacctDocumentFilter(),
            resultFunction: result => {
                if (!result.data) {
                    return [];
                }
                return result.data.map(line => {
                    return {
                        RECORDNO: line.RECORDNO,
                        RECORD_URL: line.RECORD_URL,
                        NUMBER: get(line, intacctIdField),
                    };
                });
            },
        });
        intacctQuery.showPrivate = checkAtEntityLevel;

        const financeDocumentExist = await intacctQuery.execute();

        if (financeDocumentExist.length > 1) {
            throw new BusinessRuleError(
                this.$.context.localize(
                    '@sage/xtrem-intacct-finance/too_many_documents_found_on_intacct',
                    '{{targetDocumentType}} {{documentNumber}}: Number of documents found on Sage Intacct is {{numberOfDocumentsFoundOnIntacct}}. The document cannot be updated.',
                    {
                        targetDocumentType: await (await (await this.getMapping()).nodeFactory).title,
                        documentNumber: await (await this.document).number,
                        numberOfDocumentsFoundOnIntacct: financeDocumentExist.length,
                    },
                ),
            );
        }
        return financeDocumentExist.at(0);
    }

    async getTargetDocumentType(): Promise<xtremFinanceData.enums.TargetDocumentType> {
        const nodeName = await (await this.node).name;
        return `${nodeName.charAt(0).toLowerCase()}${nodeName.slice(1)}` as xtremFinanceData.enums.TargetDocumentType;
    }

    async getFinanceTransaction(): Promise<{ secondaryPageLink?: xtremSynchronization.sharedFunctions.LinkToPage }> {
        if ((await this.secondaryPageLink).text) {
            return {};
        }

        const secondaryPageLink = await (
            await this.$.context
                .query(xtremFinanceData.nodes.FinanceTransaction, {
                    filter: {
                        targetDocumentNumber: await (await this.document).number,
                        targetDocumentType: await this.getTargetDocumentType(),
                    },
                    orderBy: { status: -1 },
                })
                .at(0)
        )?.documentNumberLink;

        return secondaryPageLink ? { secondaryPageLink } : {};
    }

    async updateIntegrationState(intacctDocument: FinanceDocumentQuery, errorMessage?: string) {
        // Without the as any i get an issue on the typing NodeCreateData<this>*
        const payload: NodeCreateData<IntacctSynchronizationFinanceState> = {
            recordNo: Number(intacctDocument.RECORDNO),
            state: errorMessage ? 'error' : 'success',
            lastMessage: errorMessage || '',
            creationStamp: datetime.now(),
            url: intacctDocument.RECORD_URL,
            intacctId: get(intacctDocument, 'NUMBER'), // because we insert NUMBER: get(line, intacctIdField),
            ...(await this.getFinanceTransaction()),
        };
        await this.$.set(payload as any);
        await this.$.save();
    }

    async errorPendingBeforeManagement(): Promise<boolean> {
        const intacctDocument = await this.checkExistForRetry();

        if (!intacctDocument) {
            return true;
        }

        await this.updateIntegrationState(intacctDocument);
        await this.$.context.batch.logMessage(
            'warning',
            this.$.context.localize(
                '@sage/xtrem-intacct-finance/nodes__finance-listener__document_found_on_intacct_updated',
                '{{NUMBER}}: The document was found in Sage Intacct. Status updated.',
                intacctDocument,
            ),
        );
        await this.after({ integrationState: 'success', error: null });
        // Will not execute synchronize Method
        return false;
    }

    override async before() {
        switch (await this.state) {
            case 'not':
                return true;
            case 'error':
            case 'pending':
                return this.errorPendingBeforeManagement();
            case 'success':
                await this.$.context.batch.logMessage(
                    'warning',
                    this.$.context.localize(
                        '@sage/xtrem-intacct-finance/nodes__finance-listener__already_success',
                        '{{number}}: The finance document was already synchronized.',
                        { number: await (await this.document).number },
                    ),
                );
                return false;
            default:
                return false;
        }
    }

    override async after(after: {
        integrationState: xtremCommunication.enums.IntegrationState;
        error: BusinessRuleError | null;
    }) {
        const { document, intacctDocument } = await IntacctSynchronizationFinanceState.withWritableContext(
            this.$.context,
            async context => {
                const writableDocument = await this.getWritableDocument(context);
                await this.$.context.batch.logMessage(
                    'test',
                    `After method for ${await writableDocument.$.getNaturalKeyValue()}`,
                );
                writableDocument.pExternalIntegrationMessage = after.error?.message || '';
                // If the intacct integration status has changed and the new status is either "success" or "posted"
                // => we have to update the postingStatus of the journal entry or ap\ar invoice accordingly
                await afterPostDocument(writableDocument);
                await writableDocument.$.save();

                return { document: writableDocument, intacctDocument: await writableDocument.getSyncStateReference() };
            },
        );

        if (
            document instanceof xtremFinance.nodes.AccountsReceivablePayment ||
            document instanceof xtremIntacctFinance.nodeExtensions.AccountsReceivablePaymentExtension ||
            document instanceof xtremFinance.nodes.AccountsReceivableAdvance ||
            document instanceof xtremIntacctFinance.nodeExtensions.AccountsReceivableAdvanceExtension
        ) {
            return;
        }

        if (!intacctDocument) {
            throw new BusinessRuleError('No intacct document');
        }

        await IntacctSynchronizationFinanceState.withWritableContext(this.$.context, async context => {
            await replyToOriginalDocuments(context, {
                intacctDocument,
                document,
                message: after.error ? after.error.message : '',
            });
        });
    }

    /**
     * returns an entityId to be sent to Intacct:
     * when creating an ap or ar invoice or a journal entry: depending on transactionIntegrationLevel on Intacct settings:
     * if 'entityLevel': will send to the entityLevel on Intacct (entityId = financialSite.id)
     * if 'topLevel': will send to the topLevel on Intacct (entityId = '')
     */
    async getEntityIdDependingOnTransactionIntegrationLevel(): Promise<string> {
        const defaultInstance = await xtremIntacct.nodes.Intacct.defaultInstance(this.$.context);
        if ((await defaultInstance?.transactionIntegrationLevel) === 'entityLevel') {
            return (await (await this.document).financialSite)?.id;
        }
        return '';
    }

    async getCountryId(): Promise<string> {
        return (await (await (await (await this.document).financialSite).legalCompany).country).id;
    }
}
