import type { NodeQueryFilter, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremIntacctFinance from '..';
import { getTaxSolutionId } from '../functions';
import { contactPayload, supplierPayloadContinueOnError } from '../functions/synchronization';
import type { SyncState } from '../interfaces/synchronization';
import { IntacctSynchronizationFinanceState } from './intacct-synchronization-finance-state';

/** *  Intacct object : APBILL */
@decorators.subNode<IntacctAccountsPayableInvoice>({
    extends: () => IntacctSynchronizationFinanceState,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isVitalReferenceChild: true,
})
export class IntacctAccountsPayableInvoice extends IntacctSynchronizationFinanceState {
    override getSyncStateReference() {
        return this.document;
    }

    override getMapping() {
        return this.$.context.read(xtremIntacctFinance.nodes.IntacctMap, {
            application: '#intacct',
            thirdPartyObjectName: 'APBILL',
            nodeFactory: `#${xtremFinance.nodes.AccountsPayableInvoice.name}`,
        });
    }

    @decorators.referenceProperty<IntacctAccountsPayableInvoice, 'document'>({
        node: () => xtremFinance.nodes.AccountsPayableInvoice,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
    })
    override readonly document: Reference<xtremFinance.nodes.AccountsPayableInvoice>;

    /**
     * megaEntityId
     * when creating the ap invoice: depending on transactionIntegrationLevel on Intacct settings:
     * if 'entityLevel': will send the ap invoice to the entityId (financialSite.id) on Intacct
     * if 'topLevel': will send the ap invoice to the topLevel on Intacct
     */
    @decorators.stringPropertyOverride<IntacctAccountsPayableInvoice, 'entityId'>({
        computeValue() {
            return this.getEntityIdDependingOnTransactionIntegrationLevel();
        },
    })
    override readonly entityId: Promise<string>;

    async getLegislationId() {
        return (await (await (await (await this.document).financialSite).legalCompany).legislation).id;
    }

    /** Linked to VENDORID  */
    @decorators.jsonProperty<IntacctAccountsPayableInvoice, 'billBySupplier'>({
        isPublished: true,
        async computeValue() {
            return supplierPayloadContinueOnError(await (await this.document).billBySupplier);
        },
    })
    readonly billBySupplier: Promise<xtremIntacctFinance.interfaces.synchronization.SyncState | null>;

    /** Corresponding to intacct TAXSOLUTIONID */
    @decorators.stringProperty<IntacctAccountsPayableInvoice, 'taxSolutionId'>({
        isPublished: true,
        computeValue() {
            return getTaxSolutionId(this);
        },
    })
    readonly taxSolutionId: Promise<string>;

    /** Corresponding to intacct BILLTOPAYTOCONTACTNAME   */
    @decorators.jsonProperty<IntacctAccountsPayableInvoice, 'billToPayToContactName'>({
        isPublished: true,
        isNullable: true,
        async computeValue() {
            const payToSupplierAddress = await (await this.document).payToSupplierLinkedAddress;
            if (!payToSupplierAddress) {
                await this.$.context.logger.warnAsync(
                    async () => `No billToPayTo address for ${(await this.document).number}`,
                );
                return null;
            }
            return contactPayload(payToSupplierAddress);
        },
    })
    readonly billToPayToContactName: Promise<SyncState | null>;

    /** Corresponding to intacct SHIPTORETURNTOCONTACTNAME */
    @decorators.jsonProperty<IntacctAccountsPayableInvoice, 'shipToReturnToContactName'>({
        isPublished: true,
        isNullable: true,
        async computeValue() {
            const returnAddress = await (await this.document).returnLinkedAddress;
            if (!returnAddress) {
                await this.$.context.logger.warnAsync(
                    async () => `No returnAddress for ${await (await this.document).number}`,
                );
                return null;
            }
            return contactPayload(returnAddress);
        },
    })
    readonly shipToReturnToContactName: Promise<SyncState | null>;

    static override xtremFilter(filterValue: string): NodeQueryFilter<xtremFinance.nodes.AccountsPayableInvoice> {
        return { number: { _regex: filterValue } };
    }
}
