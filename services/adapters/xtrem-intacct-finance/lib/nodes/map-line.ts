import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremIntacctFinance from '../../index';

@decorators.node<MapLine>({
    package: 'xtrem-intacct-finance',
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
})
export class MapLine extends Node {
    /**
     *  Name of the intacct Property to link to the header
     */
    @decorators.stringProperty<MapLine, 'collectionName'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly collectionName: Promise<string>;

    @decorators.referenceProperty<MapLine, 'mapHeader'>({
        isStored: true,
        isPublished: true,
        node: () => xtremIntacctFinance.nodes.IntacctMap,
        isVitalParent: true,
    })
    readonly mapHeader: Reference<xtremIntacctFinance.nodes.IntacctMap>;

    @decorators.referenceProperty<MapLine, 'line'>({
        isStored: true,
        isPublished: true,
        node: () => xtremIntacctFinance.nodes.IntacctMap,
    })
    readonly line: Reference<xtremIntacctFinance.nodes.IntacctMap>;

    /**
     *  Name of the intacct Property to link to the header
     */
    @decorators.stringProperty<MapLine, 'propertyHeader'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly propertyHeader: Promise<string>;

    /**
     * Name of the intacct property of line to link to the propertyHeader field
     */
    @decorators.stringProperty<MapLine, 'propertyLine'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly propertyLine: Promise<string>;
}
