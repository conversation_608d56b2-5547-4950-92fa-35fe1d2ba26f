import type { Context, NodeCreateData, NodeQueryFilter, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremIntacctFinance from '..';
import { contactPayload } from '../functions/synchronization';
import { IntacctCustomerSupplier } from './intacct-customer-supplier';

@decorators.subNode<IntacctSupplier>({
    extends: () => IntacctCustomerSupplier,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isVitalReferenceChild: true,
})
export class IntacctSupplier extends IntacctCustomerSupplier {
    override getSyncStateReference() {
        return this.parent;
    }

    override getMapping() {
        return this.$.context.read(xtremIntacctFinance.nodes.IntacctMap, {
            application: '#intacct',
            thirdPartyObjectName: 'VENDOR',
            nodeFactory: `#${xtremMasterData.nodes.Supplier.name}`,
        });
    }

    override async getPageUrl() {
        return `${await (await (await this.node).package).name}/Supplier`;
    }

    @decorators.referenceProperty<IntacctSupplier, 'parent'>({
        node: () => xtremMasterData.nodes.Supplier,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
    })
    override readonly parent: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.stringPropertyOverride<IntacctSupplier, 'status'>({
        async getValue() {
            return (await (await this.parent).isActive) ? 'active' : 'inactive';
        },
    })
    override readonly status: Promise<string>;

    /**     * Linked to PAYTOKEY     */
    @decorators.jsonProperty<IntacctSupplier, 'payToAddress'>({
        isPublished: true,
        isNullable: true,
        async computeValue() {
            const parent = await this.parent;
            const payToAddress = await parent.payToAddress;
            if (!payToAddress) {
                await this.$.context.logger.warnAsync(
                    async () => `No payTo address for ${await (await parent.businessEntity).name}`,
                );
                return null;
            }
            if (!(await this.hideDisplayContact) && payToAddress._id === (await parent.primaryAddress)._id) {
                /** Same as displayAddress */
                return null;
            }
            return contactPayload(payToAddress);
        },
    })
    readonly payToAddress: Promise<xtremIntacctFinance.interfaces.synchronization.SyncState | null>;

    /**     * RETURNTOKEY     */
    @decorators.jsonProperty<IntacctSupplier, 'returnToAddress'>({
        isPublished: true,
        isNullable: true,
        async computeValue() {
            const parent = await this.parent;
            const returnToAddress = await parent.returnToAddress;
            if (!returnToAddress) {
                await this.$.context.logger.warnAsync(
                    async () => `No returnTo address for ${await (await parent.businessEntity).name}`,
                );
                return null;
            }
            if (!(await this.hideDisplayContact) && returnToAddress._id === (await parent.primaryAddress)._id) {
                /** Same as displayAddress */
                return null;
            }
            return contactPayload(returnToAddress);
        },
    })
    readonly returnToAddress: Promise<xtremIntacctFinance.interfaces.synchronization.SyncState | null>;

    async getOtherAddress(businessEntityAddress: xtremMasterData.nodes.BusinessEntityAddress) {
        const returnToAddress = await (await this.parent).returnToAddress;
        const payToAddress = await (await this.parent).payToAddress;

        if (payToAddress._id === businessEntityAddress._id) {
            return this.$.context.localize('@sage/xtrem-intacct-finance/intacct-address-pay-to', 'Pay-to address');
        }
        if (returnToAddress._id === businessEntityAddress._id) {
            return this.$.context.localize(
                '@sage/xtrem-intacct-finance/intacct-address-return-to',
                'Return-to address',
            );
        }

        return null;
    }

    override async getAddressCategory(businessEntityAddress: xtremMasterData.nodes.BusinessEntityAddress) {
        return (await super.getAddressCategory(businessEntityAddress)) ?? this.getOtherAddress(businessEntityAddress);
    }

    static override initPayload(context: Context, payload: any): NodeCreateData<xtremMasterData.nodes.Supplier> {
        context.logger.debug(() => `initPayload : ${JSON.stringify(payload)}`);
        return payload;
    }

    static override xtremFilter(filterValue: string): NodeQueryFilter<xtremMasterData.nodes.Supplier> {
        return { businessEntity: { id: { _regex: filterValue } } };
    }
}
