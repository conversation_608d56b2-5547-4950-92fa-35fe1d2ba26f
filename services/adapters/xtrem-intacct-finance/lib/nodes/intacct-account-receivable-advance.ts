import type { NodeQueryFilter, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremIntacctFinance from '..';
import { IntacctSynchronizationFinanceState } from './intacct-synchronization-finance-state';

/** ARADVANCE */
@decorators.subNode<IntacctAccountsReceivableAdvance>({
    extends: () => IntacctSynchronizationFinanceState,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isVitalReferenceChild: true,
})
export class IntacctAccountsReceivableAdvance extends IntacctSynchronizationFinanceState {
    override getSyncStateReference() {
        return this.document;
    }

    override getMapping() {
        return this.$.context.read(xtremIntacctFinance.nodes.IntacctMap, {
            application: '#intacct',
            thirdPartyObjectName: 'ARADVANCE',
            nodeFactory: `#${xtremFinance.nodes.AccountsReceivableAdvance}`,
        });
    }

    @decorators.referenceProperty<IntacctAccountsReceivableAdvance, 'document'>({
        node: () => xtremFinance.nodes.AccountsReceivableAdvance,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
    })
    override readonly document: Reference<xtremFinance.nodes.AccountsReceivableAdvance>;

    /**
     * megaEntityId
     * when creating the ar payment, will send the the to the entityId
     */
    @decorators.stringPropertyOverride<IntacctAccountsReceivableAdvance, 'entityId'>({
        async computeValue() {
            const bankAccount = await (await this.bankFeed)?.bankAccount;
            return (await bankAccount?.megaEntityId) || (await (await bankAccount?.financialSite)?.id) || '';
        },
    })
    override readonly entityId: Promise<string>;

    @decorators.stringProperty<IntacctAccountsReceivableAdvance, 'paymentMethod'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.mediumString,
    })
    readonly paymentMethod: Promise<string>;

    @decorators.referenceProperty<IntacctAccountsReceivableAdvance, 'bankFeed'>({
        isStored: true,
        isPublished: true,
        node: () => xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed,
        isNullable: true,
    })
    readonly bankFeed: Reference<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed> | null;

    static override xtremFilter(filterValue: string): NodeQueryFilter<xtremFinance.nodes.AccountsReceivableAdvance> {
        return { number: { _regex: filterValue } };
    }
}
