import type {
    Collection,
    Context,
    decimal,
    Dict,
    integer,
    NodeCreateData,
    NodePayloadData,
    Reference,
} from '@sage/xtrem-core';
import { asyncArray, date, decorators, Logger, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import { camelCase } from 'lodash';
import * as xtremIntacctFinance from '..';
import { getMatchingRules } from '../functions/transaction-feed-lib';
import type { ArMatch, MatchedArInvoices } from '../shared-functions/interfaces';

const intacctDateFormat = 'MM/DD/YYYY';

const logger = Logger.getLogger(__filename, 'cashbook');

@decorators.subNode<IntacctBankAccountTransactionFeed>({
    extends: () => xtremFinanceData.nodes.BaseFinanceDocument,
    isPublished: true,
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isVitalCollectionChild: true,
    async saveEnd() {
        if (
            this.$.status === NodeStatus.added ||
            (await this.lines.some(async line => (await line.status) === 'lookingForMatches'))
        ) {
            await this.$.context.notify('IntacctBankReconciliation/matchingSearch', { documentSysId: this._id });
        }
    },
    serviceOptions: () => [xtremIntacctFinance.serviceOptions.intacctCashbookManagement],
})
export class IntacctBankAccountTransactionFeed extends xtremFinanceData.nodes.BaseFinanceDocument {
    /** Intacct RECORDNO linked to BaseFinanceDocument.number */

    /**  Import session */
    @decorators.referenceProperty<IntacctBankAccountTransactionFeed, 'importSession'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremIntacctFinance.nodes.IntacctImportSession,
    })
    readonly importSession: Reference<xtremIntacctFinance.nodes.IntacctImportSession>;

    /**  Bank Account */
    @decorators.referenceProperty<IntacctBankAccountTransactionFeed, 'bankAccount'>({
        isStored: true,
        isPublished: true,
        isNullable: true, // XT-80680 changed during bank account refactor since cash book management will be removed
        node: () => xtremFinanceData.nodes.BankAccount,
    })
    readonly bankAccount: Reference<xtremFinanceData.nodes.BankAccount | null>;

    /** RECORDNO  // intacctID field  */
    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'intacctId'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.code, // We need an intacctId as a string
    })
    readonly intacctId: Promise<string>;

    /** FINANCIALENTITY */
    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'entity'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly entity: Promise<string>;

    /** FINANCIALENTITYNAME */
    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'entityName'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly entityName: Promise<string>;

    /** FINANCIALACCOUNTTYPE */
    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'accountType'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly accountType: Promise<string>;

    /** FINACCTTXNFEEDKEY */
    @decorators.integerProperty<IntacctBankAccountTransactionFeed, 'accountFeedKey'>({
        isStored: true,
        isPublished: true,
    })
    readonly accountFeedKey: Promise<integer>;

    /** TRANSACTIONID */
    @decorators.integerProperty<IntacctBankAccountTransactionFeed, 'transactionId'>({
        isStored: true,
        isPublished: true,
    })
    readonly transactionId: Promise<integer>;

    /** BANKACCTRECONKEY */
    @decorators.integerProperty<IntacctBankAccountTransactionFeed, 'accountReconKey'>({
        isStored: true,
        isPublished: true,
    })
    readonly accountReconKey: Promise<integer>;

    /** POSTINGDATE */
    @decorators.dateProperty<IntacctBankAccountTransactionFeed, 'postingDate'>({
        isStored: true,
        isPublished: true,
    })
    readonly postingDate: Promise<date>;

    /** RECDATE */
    @decorators.dateProperty<IntacctBankAccountTransactionFeed, 'reconcilitationDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly reconcilitationDate: Promise<date | null>;

    /** TRANSACTIONTYPE
     *  - deposit ==> credit
     *  - withdrawal ==> debit (amount * -1)
     */
    @decorators.enumProperty<IntacctBankAccountTransactionFeed, 'transactionType'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacctFinance.enums.IntacctRecordTransactionTypeDataType,
    })
    readonly transactionType: Promise<xtremIntacctFinance.enums.IntacctRecordTransactionType>;

    /** DOCTYPE */
    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'documentType'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly documentType: Promise<string>;

    /** DOCNO */
    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'documentNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly documentNumber: Promise<string>;

    /** PAYEE */
    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'payee'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly payee: Promise<string>;

    /** AMOUNT */
    @decorators.decimalProperty<IntacctBankAccountTransactionFeed, 'amount'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.decimal,
    })
    readonly amount: Promise<decimal>;

    /** AMOUNTTOMATCH  amount and charges, / we can split the transaction / remaining amount to match */
    @decorators.decimalProperty<IntacctBankAccountTransactionFeed, 'amountToMatch'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.decimal,
    })
    readonly amountToMatch: Promise<decimal>;

    /** DESCRIPTION */
    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.description,
    })
    readonly description: Promise<string>;

    /** CLEARED
     * "Unmatched","Cleared","Matched","Partially matched","Selected to match","Selected to unmatch","Ignored","Draft matched"
     */
    @decorators.enumProperty<IntacctBankAccountTransactionFeed, 'cleared'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacctFinance.enums.IntacctmatchingStatusDataType,
    })
    readonly cleared: Promise<xtremIntacctFinance.enums.IntacctMatchingStatus>;

    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'intacctCleared'>({
        async computeValue() {
            return (await this.cleared)
                .replace(/([A-Z])/g, ' $1') // add one space before each maj
                .toLowerCase() // Lower
                .replace(/^./, str => str.toUpperCase()); // Uper the first
        },
        dataType: () => dataTypes.description,
    })
    readonly intacctCleared: Promise<string>;

    @decorators.enumProperty<IntacctBankAccountTransactionFeed, 'targetDocumentType'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.targetDocumentTypeDataType,
        async computeValue() {
            return (
                (await (
                    await xtremFinanceData.functions.getFinanceTransactionFromOrigin(
                        this.$.context,
                        this._id,
                        await this.financeDocumentType,
                    )
                )?.targetDocumentType) || null
            );
        },
    })
    readonly targetDocumentType: Promise<xtremFinanceData.enums.TargetDocumentType> | null;

    /** CURRENCY */
    @decorators.referenceProperty<IntacctBankAccountTransactionFeed, 'currency'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    /** FEEDTYPE */
    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'feedType'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.code,
    })
    readonly feedType: Promise<string>;

    @decorators.referenceProperty<IntacctBankAccountTransactionFeed, 'account'>({
        isStored: true,
        isPublished: true,
        node: () => xtremFinanceData.nodes.Account,
        isNullable: true,
    })
    readonly account: Reference<xtremFinanceData.nodes.Account | null>;

    @decorators.jsonProperty<IntacctBankAccountTransactionFeed, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<IntacctBankAccountTransactionFeed, 'computedAttributes'>({
        isPublished: true,
        dependsOn: ['bankAccount'],
        async computeValue() {
            // XT-80680 changed during bank account refactor since cash book management will be removed
            return xtremFinanceData.functions.computeGenericAttributes(this.$.context, {
                financialSite: await (await this.bankAccount)?.financialSite,
            });
        },
    })
    readonly computedAttributes: Promise<object>;

    @decorators.jsonProperty<IntacctBankAccountTransactionFeed, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<IntacctBankAccountTransactionFeed, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<IntacctBankAccountTransactionFeed, 'arMatch'>({
        isPublished: true,
        isStored: true,
        defaultValue: { isArMatch: false, arPaymentType: '' },
    })
    readonly arMatch: Promise<ArMatch>;

    @decorators.jsonProperty<IntacctBankAccountTransactionFeed, 'jsonArInvoices'>({
        isPublished: true,
        isStored: true,
        defaultValue: { matchedArInvoices: [] },
    })
    readonly jsonArInvoices: Promise<MatchedArInvoices>;

    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'journalEntryNumber'>({
        isStored: true,
        isPublished: true,
        // isFrozen: true, //TODO: work on forzen conditions
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly journalEntryNumber: Promise<string>;

    @decorators.enumProperty<IntacctBankAccountTransactionFeed, 'financeIntegrationApp'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationAppDataType,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationApp(
                this.$.context,
                await this.journalEntryNumber,
                'journalEntry',
            );
        },
    })
    readonly financeIntegrationApp: Promise<xtremFinanceData.enums.FinanceIntegrationApp | null>;

    @decorators.enumProperty<IntacctBankAccountTransactionFeed, 'internalFinanceIntegrationStatus'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
        async computeValue() {
            return (
                (await (
                    await xtremFinanceData.functions.getFinanceTransactionFromOrigin(
                        this.$.context,
                        this._id,
                        await this.financeDocumentType,
                    )
                )?.status) || 'toBeRecorded'
            );
        },
    })
    readonly internalFinanceIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'financeIntegrationAppUrl'>({
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.url,
        async computeValue() {
            // implement by case for advance (journal entry should not be needed since it's at line level)
            if (['advance', 'payment'].includes((await this.arMatch).arPaymentType)) {
                return (
                    (await (
                        await xtremFinanceData.functions.getFinanceTransactionFromOrigin(
                            this.$.context,
                            this._id,
                            await this.financeDocumentType,
                        )
                    )?.financeIntegrationAppUrl) || ''
                );
            }
            return '';
        },
    })
    readonly financeIntegrationAppUrl: Promise<string>;

    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'financeIntegrationAppRecordId'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        async computeValue() {
            // implement by case for advance (journal entry should not be needed since it's at line level)
            if (['advance', 'payment'].includes((await this.arMatch).arPaymentType)) {
                return (
                    (await (
                        await xtremFinanceData.functions.getFinanceTransactionFromOrigin(
                            this.$.context,
                            this._id,
                            await this.financeDocumentType,
                        )
                    )?.financeIntegrationAppRecordId) || ''
                );
            }
            return '';
        },
    })
    readonly financeIntegrationAppRecordId: Promise<string>;

    @decorators.enumProperty<IntacctBankAccountTransactionFeed, 'financeDocumentType'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.financeDocumentTypeDataType,
        async getValue() {
            return (await this.transactionType) === 'withdrawal'
                ? 'bankReconciliationWithdrawal'
                : 'bankReconciliationDeposit';
        },
    })
    readonly financeDocumentType: Promise<xtremFinanceData.enums.FinanceDocumentType>;

    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'financeDocumentCreatedNumber'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        async computeValue() {
            return (
                (await (
                    await xtremFinanceData.functions.getFinanceTransactionFromOrigin(
                        this.$.context,
                        this._id,
                        await this.financeDocumentType,
                    )
                )?.targetDocumentNumber) || ''
            );
        },
    })
    readonly financeDocumentCreatedNumber: Promise<string>;

    @decorators.integerProperty<IntacctBankAccountTransactionFeed, 'financeDocumentCreatedSysId'>({
        isPublished: true,
        async computeValue() {
            return (
                (await (
                    await xtremFinanceData.functions.getFinanceTransactionFromOrigin(
                        this.$.context,
                        this._id,
                        await this.financeDocumentType,
                    )
                )?.targetDocumentSysId) || 0
            );
        },
    })
    readonly financeDocumentCreatedSysId: Promise<integer>;

    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'financeDocumentGenerationErrorMessage'>({
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
        async computeValue() {
            return (
                (await (
                    await xtremFinanceData.functions.getFinanceTransactionFromOrigin(
                        this.$.context,
                        this._id,
                        await this.financeDocumentType,
                    )
                )?.message) || ''
            );
        },
    })
    readonly financeDocumentGenerationErrorMessage: Promise<string>;

    @decorators.dateProperty<IntacctBankAccountTransactionFeed, 'paymentDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly paymentDate: Promise<date | null>;

    @decorators.dateProperty<IntacctBankAccountTransactionFeed, 'receiptDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly receiptDate: Promise<date | null>;

    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'payToCustomerId'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly payToCustomerId: Promise<string>;

    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'payToCustomerName'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly payToCustomerName: Promise<string>;

    @decorators.stringProperty<IntacctBankAccountTransactionFeed, 'paymentMethod'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.mediumString,
    })
    readonly paymentMethod: Promise<string>;

    @decorators.collectionPropertyOverride<IntacctBankAccountTransactionFeed, 'lines'>({
        node: () => xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeedLine,
    })
    override readonly lines: Collection<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeedLine>;

    /**
     *  We can't update the intacct BANKACCTTXNRECORD object
     * @param context
     * @param recordNo
     * @param cleared
     * @returns
     */
    @decorators.mutation<typeof IntacctBankAccountTransactionFeed, 'setCleared'>({
        isPublished: true,
        parameters: [
            {
                name: 'recordNo',
                type: 'integer',
                isMandatory: true,
            },
            {
                name: 'cleared',
                type: 'enum',
                dataType: () => xtremIntacctFinance.enums.IntacctmatchingStatusDataType,
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static async setCleared(
        context: Context,
        recordNo: number,
        cleared: xtremIntacctFinance.enums.IntacctMatchingStatus,
    ): Promise<boolean> {
        const intacctCleared = cleared
            .replace(/([A-Z])/g, ' $1') // add one space before each maj
            .toLowerCase() // Lower
            .replace(/^./, str => str.toUpperCase());

        const request = `<update><BANKACCTTXNRECORD><RECORDNO>${recordNo}</RECORDNO>
        <CLEARED>${intacctCleared}</CLEARED>  </BANKACCTTXNRECORD></update>`;

        await new xtremIntacct.classes.sdk.Functions.XmlQuery(context, request).execute();

        return true;
    }

    @decorators.query<typeof IntacctBankAccountTransactionFeed, 'getMatchLine'>({
        isPublished: true,
        parameters: [
            {
                name: 'transactionFeed',
                type: 'object',
                properties: {
                    description: { type: 'string', isMandatory: false, isNullable: true },
                },
            },
            {
                name: 'matchingRules',
                type: 'array',
                item: { type: 'instance', node: () => xtremIntacctFinance.nodes.IntacctBankAccountMatching },
                isMandatory: false,
            },
        ],
        return: {
            type: 'object',
            properties: {
                isMatched: { type: 'enum', dataType: () => xtremIntacctFinance.enums.FeedLineMatchingStatusDataType },
                account: { type: 'reference', node: () => xtremFinanceData.nodes.Account },
                tax: { type: 'reference', node: () => xtremTax.nodes.Tax },
                location: { type: 'reference', node: () => xtremSystem.nodes.Site },
                // TODO :  getMatchLine function not used in front side for now : fix this before
                // storedDimensions: { type: 'object' }, // Don't know how to return this
                // storedAttributes: { type: 'object' }, // xtremMasterData.interfaces.StoredAttributes;
            },
        },
    })
    static async getMatchLine(
        context: Context,
        transactionFeed: NodeCreateData<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed>,
        matchingRules?: xtremIntacctFinance.nodes.IntacctBankAccountMatching[],
    ): Promise<{
        matchedStatus: xtremIntacctFinance.enums.FeedLineMatchingStatus;
        account?: xtremFinanceData.nodes.Account;
        tax?: xtremTax.nodes.Tax;
        location?: xtremSystem.nodes.Site;
        storedDimensions?: Dict<string>;
        storedAttributes?: xtremMasterData.interfaces.StoredAttributes;
    }> {
        if (transactionFeed.cleared === 'matched') {
            // if the cleard status of the record is match the line must be match also
            return { matchedStatus: 'matched' };
        }

        if (!matchingRules) {
            logger.warn(() => 'No matching rules define in the cache ! ');
        }
        // if we don't provide a matchingRules array ( case api ) we have to get one
        const matchingRulesArray =
            matchingRules ||
            (await getMatchingRules(
                context,
                transactionFeed.bankAccount as xtremFinanceData.nodes.BankAccount,
                {},
                transactionFeed.transactionType,
            ));

        const matchFound = await asyncArray(matchingRulesArray)
            .filter(async ruleTransaction =>
                (await ruleTransaction.transactionType)
                    ? (await ruleTransaction.transactionType) === transactionFeed.transactionType
                    : true,
            )
            .find(async rule => {
                return xtremIntacctFinance.functions.matchingStatement.findMatchingDescription(
                    await rule.type,
                    await rule.keyword,
                    transactionFeed.description || '',
                );
            });

        return matchFound
            ? {
                  matchedStatus: 'draftMatch',
                  account: (await matchFound.account) || undefined,
                  tax: (await matchFound.tax) || undefined,
                  location: (await matchFound.location) || undefined,
                  storedDimensions: (await matchFound.storedDimensions) || undefined,
                  storedAttributes: (await matchFound.storedAttributes) || undefined,
              }
            : { matchedStatus: 'unmatched' };
    }

    /**
     *  Initialisator for mapping Payload
     * @param payload
     * @returns
     */
    static async initPayload(
        context: Context,
        payload: any,
    ): Promise<NodePayloadData<IntacctBankAccountTransactionFeed>> {
        delete payload.intacctName;

        const newPayload: NodePayloadData<IntacctBankAccountTransactionFeed> = { ...payload };

        const bankAccount = await context.read(xtremFinanceData.nodes.BankAccount, { id: payload.entity });
        /** FinancialSite */
        const location = await xtremFinanceData.functions.getFinancialSite(
            await context.read(xtremSystem.nodes.Site, { id: await bankAccount.location }),
        );

        newPayload.bankAccount = { _id: bankAccount._id };

        newPayload.number = `${payload.number}`;
        newPayload.intacctId = `${payload.intacctId}`;

        newPayload.postingDate = payload.postingDate
            ? date.parse(payload.postingDate.substring(0, 10), undefined, intacctDateFormat)
            : undefined;

        newPayload.reconcilitationDate = payload.reconcilitationDate
            ? date.parse(payload.reconcilitationDate.substring(0, 10), undefined, intacctDateFormat)
            : null;

        newPayload.cleared = camelCase(payload.intacctCleared) as xtremIntacctFinance.enums.IntacctMatchingStatus;
        newPayload.transactionType = camelCase(
            payload.transactionType,
        ) as xtremIntacctFinance.enums.IntacctRecordTransactionType;
        delete payload.transactionType;

        /** If no currency on the transaction we select the currency of the bankAccount */
        if (!newPayload.currency) {
            newPayload.currency = { _id: (await bankAccount.currency)._id };
        }

        if (!payload.arMatch?.isArMatch) {
            // we check this because if there's an ar match we don't want to add a line
            newPayload.lines = [
                // IntacctBankAccountTransactionFeedLine
                {
                    amount: payload.amount,
                    location: { _id: location._id, financialSite: { _id: (await location.financialSite)?._id } },
                },
            ];
        }

        context.logger.debug(() => ` Payload to save ${JSON.stringify(newPayload)}`);
        return newPayload;
    }
}
