import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremIntacctFinance from '../../index';

@decorators.node<MapProperty>({
    package: 'xtrem-intacct-finance',
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
})
export class MapProperty extends Node {
    @decorators.referenceProperty<MapProperty, 'map'>({
        isStored: true,
        isPublished: true,
        node: () => xtremIntacctFinance.nodes.IntacctMap,
        isVitalParent: true,
    })
    readonly map: Reference<xtremIntacctFinance.nodes.IntacctMap>;

    @decorators.stringProperty<MapProperty, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly name: Promise<string>;

    @decorators.enumProperty<MapProperty, 'type'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremIntacct.enums.propertyTypeDataType,
        defaultValue: () => 'none',
    })
    readonly type: Promise<xtremIntacct.enums.PropertyType>;
}
