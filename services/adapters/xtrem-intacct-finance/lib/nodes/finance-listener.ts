import type { Context, integer } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, Logger, Node } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type { IntacctSynchronizationFinanceState } from './intacct-synchronization-finance-state';

const logger = Logger.getLogger(__filename, 'financeListener');

@decorators.node<FinanceListener>({
    package: 'xtrem-intacct-finance',
    isPublished: true,
})
export class FinanceListener extends Node {
    @decorators.mutation<typeof FinanceListener, 'retryFinanceDocument'>({
        isPublished: true,
        parameters: [
            {
                name: 'financeTransaction',
                type: 'reference',
                isNullable: false,
                isWritable: true,
                node: () => xtremFinanceData.nodes.FinanceTransaction,
            },
            {
                name: 'financeDocumentSysId',
                type: 'integer',
                isMandatory: true,
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async retryFinanceDocument(
        context: Context,
        financeTransaction: xtremFinanceData.nodes.FinanceTransaction,
        financeDocumentSysId: integer,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        const allowedTargetDocumentType: xtremFinanceData.enums.TargetDocumentType[] = [
            'journalEntry',
            'accountsPayableInvoice',
            'accountsReceivableInvoice',
        ];
        const batchId = await financeTransaction.batchId;
        const targetDocumentType = await financeTransaction.targetDocumentType;
        const financeDocumentKey = { type: targetDocumentType, sysId: financeDocumentSysId };

        if (!allowedTargetDocumentType.includes(targetDocumentType)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-intacct-finance/target_document_type_not_supported',
                    '{{targetDocumentType}}: Target document type not supported.',
                    { targetDocumentType },
                ),
            );
        }
        const financeDocument = await xtremFinance.functions.financeRetry.getTargetFinanceDocument(
            context,
            financeDocumentKey,
        );

        const intacctDocument = (await financeDocument.getSyncStateReference()) as IntacctSynchronizationFinanceState;
        if (!intacctDocument) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-intacct-finance/intacct_document_not_found',
                    '{{sysId}} {{type}}: intacct document not found.',
                    financeDocumentKey,
                ),
            );
        }

        // if we have already a finance integration app record if, we just update the finance transaction table
        if (await intacctDocument.recordNo) {
            logger.info(
                `Finance document retry - Intacct reference found for ${targetDocumentType} ${await financeTransaction.targetDocumentNumber}`,
            );

            await financeTransaction.$.set({
                targetDocumentNumber: await financeDocument.number,
                targetDocumentSysId: financeDocument._id,
                status: 'posted',
                message: '',
            });
            await financeTransaction.$.save();
            await logger.debugAsync(async () => `Document number ${await financeDocument.number} updated.`);

            const financeTransactionData: xtremFinanceData.interfaces.FinanceTransactionData = {
                batchId,
                documentNumber: await financeTransaction.documentNumber,
                documentType: await financeTransaction.documentType,
                targetDocumentType,
                targetDocumentNumber: await financeDocument.number,
                targetDocumentSysId: financeDocument._id,
                validationMessages: [],
                status: 'posted',
                financeExternalIntegration: {
                    app: 'intacct',
                    recordId: await intacctDocument.intacctId,
                    url: await intacctDocument.url,
                },
                isJustForStatus: true, // this will tell the listener static function that update of finance transaction node was already done
            };

            const replyData = await context
                .query(xtremFinanceData.nodes.AccountingStaging, {
                    filter: {
                        batchId,
                    },
                    first: 1,
                })
                .at(0);

            if (financeTransactionData && replyData) {
                logger.info(`Finance document retry - Notification will be sent for status update`);
                await context.reply(await replyData.replyTopic, financeTransactionData, {
                    replyId: await replyData.originNotificationId,
                });
            }

            return {
                wasSuccessful: true,
                message: context.localize('@sage/xtrem-intacct-finance/status_updated', 'Status updated.'),
            };
        }

        // if we don't, we can ask intacct if the document is there
        // TODO: for current version, add a time stamp to not allow this until x time?
        if (['intacct', null].includes(await financeTransaction.financeIntegrationApp)) {
            await financeDocument.$.context.notify('SynchronizationState/synchronize/start', {
                filter: `{"_id":{"_in":["${intacctDocument._id}"]}}`,
            });
            return {
                message: context.localize('@sage/xtrem-intacct-finance/nodes__finance-listener__retry', 'Processing.'),
                wasSuccessful: true,
            };
        }
        throw new BusinessRuleError(`Error : ${await financeTransaction.financeIntegrationApp}`);
    }
}
