import type { Context, NodeCreateData, NodeQueryFilter, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremIntacctFinance from '..';
import { IntacctSynchronizationState } from './intacct-synchronization-state';

@decorators.subNode<IntacctContact>({
    extends: () => IntacctSynchronizationState,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isVitalReferenceChild: true,
})
export class IntacctContact extends IntacctSynchronizationState {
    override getSyncStateReference() {
        return this.contact;
    }

    override intacctIdField = 'CONTACTNAME';

    override getMapping() {
        return this.$.context.read(xtremIntacctFinance.nodes.IntacctMap, {
            application: '#intacct',
            thirdPartyObjectName: 'CONTACT',
            nodeFactory: `#${xtremMasterData.nodes.BusinessEntityAddress.name}`,
        });
    }

    override async getIntacctId() {
        const contact = await this.contact;
        const businessEntity = await contact.businessEntity;

        return (await contact.city).trim().length > 0
            ? `${await businessEntity.name} ${await contact.city} (${contact._id})`
            : `${await businessEntity.name} (${contact._id})`;
    }

    override async getPageUrl() {
        return `${await (await (await this.node).package).name}/BusinessEntity`;
    }

    @decorators.stringPropertyOverride<IntacctContact, 'sysIdLink'>({
        async computeValue() {
            return `${(await (await this.contact).businessEntity)._id.toString()}`;
        },
    })
    override readonly sysIdLink: Promise<string>;

    @decorators.referenceProperty<IntacctContact, 'contact'>({
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
    })
    readonly contact: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.stringProperty<IntacctContact, 'name'>({
        isPublished: true,
        dependsOn: [{ contact: ['businessEntity', 'city'] }],
        computeValue() {
            return this.getIntacctId();
        },
    })
    readonly name: Promise<string>;

    /**
     *  Linked to PRINTAS
     */
    @decorators.stringProperty<IntacctContact, 'printAs'>({
        isPublished: true,

        async computeValue() {
            const primaryContact = await (await this.contact).primaryContact;
            const name = await (await (await this.contact).businessEntity).name;
            const primaryContactName = (await primaryContact?.intacctPrintAs) ?? '';

            return `${primaryContactName}${primaryContactName ? ' ' : ''}${name}`;
        },
    })
    readonly printAs: Promise<string>;

    /**
     *  Linked to PREFIX
     */
    @decorators.stringProperty<IntacctContact, 'prefix'>({
        isPublished: true,
        async computeValue() {
            const primaryContact = await (await this.contact).primaryContact;
            return primaryContact
                ? this.$.context.localizeEnumMember('@sage/xtrem-master-data/Title', await primaryContact.title)
                : '';
        },
    })
    readonly prefix: Promise<string>;

    @decorators.stringProperty<IntacctContact, 'status'>({
        isPublished: true,
        async getValue() {
            return (await (await this.contact).isActive) ? 'active' : 'inactive';
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly status: Promise<string>;

    static override initPayload(
        context: Context,
        payload: any,
    ): NodeCreateData<xtremMasterData.nodes.BusinessEntityContact> {
        context.logger.debug(() => `initPayload : ${JSON.stringify(payload)}`);
        return payload;
    }

    static override xtremFilter(filterValue: string): NodeQueryFilter<xtremMasterData.nodes.BusinessEntityContact> {
        return { email: { _regex: filterValue } };
    }
}
