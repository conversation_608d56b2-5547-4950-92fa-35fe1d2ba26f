import type { NodeQueryFilter, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremIntacctFinance from '..';
import { GenericIfunction } from '../classes/intacct';
import { getTaxSolution } from '../functions';
import { IntacctSynchronizationFinanceState } from './intacct-synchronization-finance-state';

@decorators.subNode<IntacctJournalEntry>({
    extends: () => IntacctSynchronizationFinanceState,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isVitalReferenceChild: true,
})
export class IntacctJournalEntry extends IntacctSynchronizationFinanceState {
    override intacctIdField = 'BATCHNO';

    override documentNumberField = 'REFERENCENO';

    override getSyncStateReference() {
        return this.document;
    }

    override getMapping() {
        return this.$.context.read(xtremIntacctFinance.nodes.IntacctMap, {
            application: '#intacct',
            thirdPartyObjectName: 'GLBATCH',
            nodeFactory: `#${xtremFinance.nodes.JournalEntry.name}`,
        });
    }

    @decorators.referenceProperty<IntacctJournalEntry, 'document'>({
        node: () => xtremFinance.nodes.JournalEntry,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
    })
    override readonly document: Reference<xtremFinance.nodes.JournalEntry>;

    /**
     * megaEntityId
     * when creating the journal entry: 2 cases
     * 1) temporary solution for bank manager (will be aligned later):
     * hard coded sending of the journal entry to the entityId (bankAccount.megaEntityId or bankAccount.financialSite.id)
     * 2) all other journals:
     * depending on transactionIntegrationLevel on Intacct settings:
     * if 'entityLevel': will send the journal entry to the entityId (financialSite.id) on Intacct
     * if 'topLevel': will send the journal entry to the topLevel on Intacct
     */
    @decorators.stringPropertyOverride<IntacctJournalEntry, 'entityId'>({
        async computeValue() {
            // first check if it is the bank manager case
            const accountingStaging: xtremFinanceData.nodes.AccountingStaging | undefined = await (
                await (await (await this.document).lines.at(0))?.accountingStagingLines.at(0)
            )?.accountingStaging;

            if (
                ['bankReconciliationDeposit', 'bankReconciliationWithdrawal'].includes(
                    (await accountingStaging?.documentType) || '',
                )
            ) {
                const bankAccountTransactionFeedLineSysId = (await accountingStaging?.baseDocumentLine)?._id;

                if (bankAccountTransactionFeedLineSysId) {
                    const bankAccountTransactionFeedLine = await this.$.context.read(
                        xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeedLine,
                        { _id: bankAccountTransactionFeedLineSysId },
                    );
                    const bankAccount = await (await bankAccountTransactionFeedLine.document).bankAccount;
                    // XT-80680 changed during bank account refactor since cash book management will be removed
                    if (await bankAccount?.megaEntityId) {
                        return bankAccount?.megaEntityId || '';
                    }
                    return (await bankAccount?.financialSite)?.id || '';
                }
                return ''; // enforce topLevel
            }

            // if no bank manager case, check the transactionIntegrationLevel setting
            return this.getEntityIdDependingOnTransactionIntegrationLevel();
        },
    })
    override readonly entityId: Promise<string>;

    @decorators.stringProperty<IntacctJournalEntry, 'taxImplications'>({
        isPublished: true,
        async computeValue() {
            if (await (await (await this.document).journal).taxImpact) {
                const lineAccountingStagingLines = await (
                    await (
                        await this.document
                    ).lines.find(async line => {
                        return !!(await (
                            await line.intacctDocumentLine
                        )?.excludeRecord);
                    })
                )?.accountingStagingLines.toArray();
                if (
                    lineAccountingStagingLines?.length &&
                    (await (await lineAccountingStagingLines[0]?.accountingStaging)?.documentType) ===
                        'bankReconciliationDeposit'
                ) {
                    return 'Outbound';
                }
                if (
                    lineAccountingStagingLines?.length &&
                    (await (await lineAccountingStagingLines[0]?.accountingStaging)?.documentType) ===
                        'bankReconciliationWithdrawal'
                ) {
                    return 'Inbound';
                }
            }
            return GenericIfunction.hideThisField;
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly taxImplications: Promise<string>;

    /**
     * Corresponding to intacct TAXSOLUTIONID
     */
    @decorators.stringProperty<IntacctJournalEntry, 'taxSolutionId'>({
        isPublished: true,
        async computeValue() {
            if (
                (await (await (await this.document).journal).taxImpact) &&
                (await (
                    await this.document
                ).lines.some(async line => {
                    return !!(await line.intacctDocumentLine)?.excludeRecord;
                }))
            ) {
                const intacctConfiguration = await xtremIntacct.nodes.Intacct.defaultInstance(this.$.context);
                if (intacctConfiguration) {
                    return (
                        await getTaxSolution(
                            intacctConfiguration,
                            await (
                                await (
                                    await (
                                        await (
                                            await this.document
                                        ).financialSite
                                    ).legalCompany
                                ).country
                            ).id,
                        )
                    )[0];
                }
            }
            return GenericIfunction.hideThisField;
        },
    })
    readonly taxSolutionId: Promise<string>;

    static override xtremFilter(filterValue: string): NodeQueryFilter<xtremFinance.nodes.JournalEntry> {
        return { number: { _regex: filterValue } };
    }
}
