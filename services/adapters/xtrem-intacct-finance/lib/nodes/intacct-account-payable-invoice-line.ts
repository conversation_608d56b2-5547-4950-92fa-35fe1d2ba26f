import type { Reference, decimal } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import type * as xtremIntacctFinance from '..';
import { manageTaxEntries } from '../functions';
import {
    customerPayloadContinueOnError,
    itemPayloadContinueOnError,
    supplierPayloadContinueOnError,
} from '../functions/synchronization';

/** *  Intacct object : APBILLITEM */
@decorators.node<IntacctAccountsPayableInvoiceLine>({
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isVitalReferenceChild: true,
    storage: 'sql',
    indexes: [{ orderBy: { documentLine: 1 }, isUnique: true }],
})
export class IntacctAccountsPayableInvoiceLine
    extends Node
    implements xtremSynchronization.interfaces.ThirdPartySynchronizationNodeLine
{
    getSyncStateLine() {
        return this.documentLine;
    }

    async getAttributeAndDimensionLine0() {
        if ((await (await this.documentLine).attributesAndDimensions.length) === 1) {
            return (await this.documentLine).attributesAndDimensions.elementAt(0);
        }
        return null;
    }

    @decorators.referenceProperty<IntacctAccountsPayableInvoiceLine, 'documentLine'>({
        node: () => xtremFinance.nodes.AccountsPayableInvoiceLine,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
    })
    readonly documentLine: Reference<xtremFinance.nodes.AccountsPayableInvoiceLine>;

    /** Linked to INCLUSIVETAX   */
    @decorators.booleanProperty<IntacctAccountsPayableInvoiceLine, 'inclusiveTax'>({
        isPublished: true,
        getValue() {
            return false;
        },
    })
    readonly inclusiveTax: Promise<boolean>;

    /** Linked to DEPARTMENTID   */
    @decorators.stringProperty<IntacctAccountsPayableInvoiceLine, 'departmentId'>({
        isPublished: true,
        async computeValue() {
            return (await (await (await this.getAttributeAndDimensionLine0())?.dimension01)?.intacctId) || '';
        },
    })
    readonly departmentId: Promise<string>;

    /** Linked to PROJECTID      */
    @decorators.stringProperty<IntacctAccountsPayableInvoiceLine, 'projectId'>({
        isPublished: true,
        async computeValue() {
            return (await (await (await this.getAttributeAndDimensionLine0())?.project)?.id) || '';
        },
    })
    readonly projectId: Promise<string>;

    /** Linked to EMPLOYEEID      */
    @decorators.stringProperty<IntacctAccountsPayableInvoiceLine, 'employeeId'>({
        isPublished: true,
        async computeValue() {
            return (await (await (await this.getAttributeAndDimensionLine0())?.employee)?.id) || '';
        },
    })
    readonly employeeId: Promise<string>;

    /** Linked to TASKID      */
    @decorators.stringProperty<IntacctAccountsPayableInvoiceLine, 'taskId'>({
        isPublished: true,
        async computeValue() {
            return (await (await (await this.getAttributeAndDimensionLine0())?.task)?.id) || '';
        },
    })
    readonly taskId: Promise<string>;

    /** Linked to CUSTOMERID  */
    @decorators.jsonProperty<IntacctAccountsPayableInvoiceLine, 'customerId'>({
        isPublished: true,
        isNullable: true,
        async computeValue() {
            const customer = await (await this.getAttributeAndDimensionLine0())?.customer;
            return customer ? customerPayloadContinueOnError(customer) : null;
        },
    })
    readonly customerId: Promise<xtremIntacctFinance.interfaces.synchronization.SyncState | null>;

    /** Linked to VENDORID */
    @decorators.jsonProperty<IntacctAccountsPayableInvoiceLine, 'vendorId'>({
        isPublished: true,
        isNullable: true,
        async computeValue() {
            const supplier = await (await this.getAttributeAndDimensionLine0())?.supplier;
            return supplier ? supplierPayloadContinueOnError(supplier) : null;
        },
    })
    readonly vendorId: Promise<xtremIntacctFinance.interfaces.synchronization.SyncState | null>;

    /**       *  Linked to ITEMID     */
    @decorators.jsonProperty<IntacctAccountsPayableInvoiceLine, 'itemId'>({
        isPublished: true,
        isNullable: true,
        async computeValue() {
            const item = await (await this.getAttributeAndDimensionLine0())?.item;
            return item ? itemPayloadContinueOnError(item) : null;
        },
    })
    readonly itemId: Promise<xtremIntacctFinance.interfaces.synchronization.SyncState | null>;

    /** Linked to CLASSID   */
    @decorators.stringProperty<IntacctAccountsPayableInvoiceLine, 'classId'>({
        isPublished: true,
        async computeValue() {
            return (await (await (await this.getAttributeAndDimensionLine0())?.dimension02)?.intacctId) || '';
        },
    })
    readonly classId: Promise<string>;

    /**     *  TRX_AMOUNT     */
    @decorators.decimalProperty<IntacctAccountsPayableInvoiceLine, 'signedAmountExcludingTax'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            const documentLine = await this.documentLine;
            const origin = await (await documentLine.document).origin;
            if ((await documentLine.documentLineType) === 'taxLine') {
                return origin === 'creditMemo' ? -(await documentLine.taxLineTaxAmount) : documentLine.taxLineTaxAmount;
            }
            return origin === 'creditMemo' ? -(await documentLine.amountExcludingTax) : documentLine.amountExcludingTax;
        },
    })
    readonly signedAmountExcludingTax: Promise<decimal>;

    /**     *  MEMO     */
    @decorators.stringProperty<IntacctAccountsPayableInvoiceLine, 'memo'>({
        isPublished: true,
        async getValue() {
            const documentLine = await this.documentLine;
            return (await documentLine.documentLineType) === 'taxLine'
                ? documentLine.taxDetail
                : documentLine.description;
        },
    })
    readonly memo: Promise<string>;

    /**
     *  TAXENTRIES
     */
    @decorators.jsonProperty<IntacctAccountsPayableInvoiceLine, 'taxEntries'>({
        isPublished: true,
        async computeValue() {
            const documentLine = await this.documentLine;
            return manageTaxEntries({
                financialSite: await documentLine.financialSite,
                originDocument: await (await documentLine.document).origin,
                taxes: await documentLine.taxes.toArray(),
            });
        },
    })
    readonly taxEntries: Promise<xtremIntacctFinance.interfaces.Tax.IntacctTaxEntries[]>;
}
