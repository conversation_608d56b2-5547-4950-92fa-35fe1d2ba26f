import type { Reference, decimal, integer } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import type * as xtremIntacctFinance from '..';
import { GenericIfunction } from '../classes/intacct';
import { manageJournalEntryLineTaxEntries } from '../functions';

/** this is exactly the same as intacct account paybale invoice line
 *  but we are force to do two identical files because we don't have polymorphism
 */
@decorators.node<IntacctJournalEntryLine>({
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isVitalReferenceChild: true,
    storage: 'sql',
    indexes: [
        {
            orderBy: { documentLine: 1 },
            isUnique: true,
        },
    ],
})
export class IntacctJournalEntryLine
    extends Node
    implements xtremSynchronization.interfaces.ThirdPartySynchronizationNodeLine
{
    getSyncStateLine() {
        return this.documentLine;
    }

    @decorators.referenceProperty<IntacctJournalEntryLine, 'documentLine'>({
        node: () => xtremFinance.nodes.JournalEntryLine,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
    })
    readonly documentLine: Reference<xtremFinance.nodes.JournalEntryLine>;

    /** Corresponding to intacct TRX_AMOUNT  */
    @decorators.decimalProperty<IntacctJournalEntryLine, 'absoluteTransactionAmount'>({
        isPublished: true,

        async computeValue() {
            return Math.abs((await (await this.documentLine)?.transactionAmount) || 0);
        },
        dataType: () => xtremMasterData.dataTypes.costDataType,
    })
    readonly absoluteTransactionAmount: Promise<decimal>;

    /** Corresponding to intacct TR_TYPE   */
    @decorators.integerProperty<IntacctJournalEntryLine, 'sign'>({
        isPublished: true,
        async getValue() {
            const documentLine = await this.documentLine;
            const inverse = (await documentLine.transactionAmount) < 0;
            if ((await documentLine.sign) === 'D') {
                return inverse ? -1 : 1;
            }
            if ((await documentLine.sign) === 'C') {
                return inverse ? 1 : -1;
            }
            return 0;
        },
    })
    readonly sign: Promise<integer>;

    /**  Linked to LOCATION     */
    @decorators.stringProperty<IntacctJournalEntryLine, 'locationWhenNoSplit'>({
        isPublished: true,
        async computeValue() {
            const documentLine = await this.documentLine;
            if ((await documentLine.attributesAndDimensions.length) === 0) {
                return (await documentLine.financialSite).id;
            }
            return GenericIfunction.hideThisField;
        },
    })
    readonly locationWhenNoSplit: Promise<string>;

    /** Linked to ALLOCATION  */
    @decorators.stringProperty<IntacctJournalEntryLine, 'allocationSplit'>({
        isPublished: true,
        async computeValue() {
            if ((await (await this.documentLine).attributesAndDimensions.length) > 1) {
                return 'Custom';
            }
            return GenericIfunction.hideThisField;
        },
    })
    readonly allocationSplit: Promise<string>;

    /**
     *  For DimensionLines SPLIT :
     *  return without split & no amount if there is only one dimensionLine
     *  Without split DEPARTMENT & LOCATION WITH split LOCATIONID & DEPARTMENTID !
     */
    @decorators.jsonProperty<IntacctJournalEntryLine, 'dimensionSplit'>({
        isPublished: true,
        async computeValue() {
            const documentLine = await this.documentLine;
            if ((await documentLine.attributesAndDimensions.length) === 1) {
                const intacctDimension = await (
                    await documentLine.attributesAndDimensions.elementAt(0)
                ).intacctDimension;
                const department = intacctDimension.DEPARTMENTID;
                delete intacctDimension.DEPARTMENTID;
                return {
                    LOCATION: (await (await documentLine.financialSite)?.id)?.toString(),
                    ...intacctDimension,
                    DEPARTMENT: department,
                };
            }
            if ((await documentLine.attributesAndDimensions.length) > 1) {
                return documentLine.attributesAndDimensions
                    .map(async dimensionLine => {
                        return {
                            SPLIT: {
                                AMOUNT: (await dimensionLine.transactionAmount).toString(),
                                LOCATIONID: (await (await documentLine.financialSite)?.id)?.toString(),
                                ...(await dimensionLine.intacctDimension),
                            },
                        };
                    })
                    .toArray();
            }
            this.$.context.logger.debug(() => `No dimension define for ${this._id}`);
            return {};
        },
    })
    readonly dimensionSplit: Promise<any>;

    @decorators.booleanProperty<IntacctJournalEntryLine, 'excludeRecord'>({
        isPublished: true,
        async getValue() {
            const documentLine = await this.documentLine;
            return (
                (await (await (await documentLine.journalEntry).journal).taxImpact) &&
                ['tax', 'reverseCharge'].includes(await (await documentLine.account).taxManagement)
            );
        },
    })
    readonly excludeRecord: Promise<boolean>;

    /**
     *  TAXENTRIES
     */
    @decorators.jsonProperty<IntacctJournalEntryLine, 'taxEntries'>({
        isPublished: true,
        async computeValue() {
            const documentLine = await this.documentLine;
            if (
                (await (await (await documentLine.journalEntry).journal).taxImpact) &&
                (await (await documentLine.account).taxManagement) === 'excludingTax'
            ) {
                return manageJournalEntryLineTaxEntries(await documentLine.accountingStagingLines.toArray());
            }
            return [];
        },
    })
    readonly taxEntries: Promise<xtremIntacctFinance.interfaces.Tax.IntacctTaxEntries[]>;
}
