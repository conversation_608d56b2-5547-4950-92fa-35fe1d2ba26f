import type { decimal, Dict, Reference } from '@sage/xtrem-core';
import { decorators, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import { dataTypes, nodes } from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremIntacctFinance from '..';

@decorators.subNode<IntacctBankAccountTransactionFeedLine>({
    extends: () => xtremFinanceData.nodes.BaseFinanceLine,
    isPublished: true,
    async saveBegin() {
        /** if we change tax account & amount we want to search for journal entries matching */
        if (
            this.$.status === NodeStatus.modified &&
            ((await (await this.$.old).tax) !== (await this.tax) ||
                (await (await this.$.old).account) !== (await this.account) ||
                (await (await this.$.old).amount) !== (await this.amount))
        ) {
            await this.$.set({ status: 'lookingForMatches' });
        }
    },
    serviceOptions: () => [xtremIntacctFinance.serviceOptions.intacctCashbookManagement],
})
export class IntacctBankAccountTransactionFeedLine extends xtremFinanceData.nodes.BaseFinanceLine {
    @decorators.referencePropertyOverride<IntacctBankAccountTransactionFeedLine, 'document'>({
        node: () => xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed,
    })
    override readonly document: Reference<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed>;

    /**
     * Matched : JE generated
     * Draft Match : Matching rules are applied to the bank line but JE not yet generated /
     * UnMatched  : Nothing has been done to the line so far
     * MatchFound  : a journalEntry as been found into intacct
     * Multiple match : multiple journal entry as been found into intacct
     */
    @decorators.enumProperty<IntacctBankAccountTransactionFeedLine, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacctFinance.enums.FeedLineMatchingStatusDataType,
        defaultValue: 'lookingForMatches',
    })
    readonly status: Promise<xtremIntacctFinance.enums.FeedLineMatchingStatus>;

    /** Intacct journal entry recordNo  */
    @decorators.stringProperty<IntacctBankAccountTransactionFeedLine, 'intacctJournalEntryRecordNo'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.code,
    })
    readonly intacctJournalEntryRecordNo: Promise<string>;

    /** Intacct journal entry recordNo  */
    @decorators.stringProperty<IntacctBankAccountTransactionFeedLine, 'intacctJournalEntryUrl'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.url,
    })
    readonly intacctJournalEntryUrl: Promise<string>;

    /** AMOUNT */
    @decorators.decimalProperty<IntacctBankAccountTransactionFeedLine, 'amount'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.decimal,
    })
    readonly amount: Promise<decimal>;

    /** Contra GL Account  */
    @decorators.referenceProperty<IntacctBankAccountTransactionFeedLine, 'account'>({
        isStored: true,
        isPublished: true,
        node: () => xtremFinanceData.nodes.Account,
        isNullable: true,
        filters: {
            control: {
                taxManagement: { _in: ['excludingTax', 'other'] },
            },
        },
        async control(cx, val) {
            if (await (await val?.chartOfAccount)?.legislation) {
                if (
                    (await this.tax) &&
                    (await (await val?.chartOfAccount)?.legislation)?._id !== (await (await this.tax)?.legislation)?._id
                ) {
                    cx.error.addLocalized(
                        '@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction_feed__tax_legislation_and_coa_legislation_dont_match',
                        'The tax legislation must be the same as the chart of accounts legislation.',
                    );
                }
                if (
                    (await this.location) &&
                    (await (await val?.chartOfAccount)?.legislation)?._id !==
                        (await (await (await this.location)?.legalCompany)?.legislation)?._id
                ) {
                    cx.error.addLocalized(
                        '@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction_feed__site_legislation_and_coa_legislation_dont_match',
                        'The site legislation must be the same as the chart of accounts legislation.',
                    );
                }
            }
        },
    })
    readonly account: Reference<xtremFinanceData.nodes.Account | null>;

    /** tax detail */
    @decorators.referenceProperty<IntacctBankAccountTransactionFeedLine, 'tax'>({
        isStored: true,
        isPublished: true,
        node: () => xtremTax.nodes.Tax,
        isNullable: true,
        async control(cx, val) {
            if ((await (await this.account)?.taxManagement) === 'excludingTax') {
                await cx.error
                    .withMessage(
                        '@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed__tax_mandatory',
                        'The tax is mandatory.',
                    )
                    .if(val)
                    .is.equal.to(null);
            } else {
                await cx.error
                    .withMessage(
                        '@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed__tax_not_allowed',
                        'The tax must be empty.',
                    )
                    .if(val)
                    .is.not.equal.to(null);
            }
        },
    })
    readonly tax: Reference<xtremTax.nodes.Tax | null>;

    /** tax amount // to be calculated */
    @decorators.decimalProperty<IntacctBankAccountTransactionFeedLine, 'taxAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.decimal,
        isNullable: true,
    })
    readonly taxAmount: Promise<decimal | null>;

    /** tax rate // to be calculated */
    @decorators.decimalProperty<IntacctBankAccountTransactionFeedLine, 'taxRate'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.decimal,
        isNullable: true,
    })
    readonly taxRate: Promise<decimal | null>;

    @decorators.referenceProperty<IntacctBankAccountTransactionFeedLine, 'location'>({
        isStored: true,
        isPublished: true,
        node: () => nodes.Site,
        isNullable: true,
        dependsOn: ['document'],
        async defaultValue() {
            // XT-80680 changed during bank account refactor since cash book management will be removed
            return (await (await (await this.document)?.bankAccount)?.financialSite) || null;
        },
    })
    readonly location: Reference<nodes.Site | null>;

    @decorators.jsonProperty<IntacctBankAccountTransactionFeedLine, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<IntacctBankAccountTransactionFeedLine, 'computedAttributes'>({
        isPublished: true,
        dependsOn: ['location'],
        async computeValue() {
            const site = await this.location;
            return xtremFinanceData.functions.computeGenericAttributes(this.$.context, {
                financialSite: site ? await xtremFinanceData.functions.getFinancialSite(site) : null,
            });
        },
    })
    readonly computedAttributes: Promise<object>;

    @decorators.jsonProperty<IntacctBankAccountTransactionFeedLine, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
    })
    readonly storedDimensions: Promise<Dict<string> | null>;

    @decorators.referenceProperty<IntacctBankAccountTransactionFeedLine, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    /** Intacct journal entry batchNo  */
    @decorators.stringProperty<IntacctBankAccountTransactionFeedLine, 'intacctJournalEntryBatchNo'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.code,
    })
    readonly intacctJournalEntryBatchNo: Promise<string>;
}
