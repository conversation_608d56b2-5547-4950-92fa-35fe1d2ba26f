import type * as xtremCommunication from '@sage/xtrem-communication';
import type { Diagnose, Reference } from '@sage/xtrem-core';
import { SystemError, ValidationSeverity, decorators } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import { get } from 'lodash';
import * as xtremIntacctFinance from '..';

@decorators.subNode<IntacctSynchronizationState>({
    extends: () => xtremIntacct.nodes.IntacctNodeState,
    isPublished: true,
    canCreate: true,
    isAbstract: true,
    async createEnd() {
        await this.$.set({ integration: '#intacct' });
    },
})
export class IntacctSynchronizationState extends xtremIntacct.nodes.IntacctNodeState {
    @decorators.stringProperty<IntacctSynchronizationState, 'entityId'>({
        isPublished: true,
        getValue: () => '',
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly entityId: Promise<string>;

    fromIntacctFields: string[] = [];

    override async getMapping(): Reference<xtremIntacctFinance.nodes.IntacctMap> {
        throw new SystemError(` ${await (await this.node).name} : getMapping not implemented`);
    }

    intacctIdField = 'NAME';

    async checkExist(): Promise<xtremIntacct.interfaces.IntacctKey | undefined> {
        const objectName = await (await this.getMapping()).id;
        const intacctId = (await this.intacctId) || (await this.getIntacctId());

        if (!intacctId) {
            this.$.context.logger.warn(() => `${objectName} No intacct id`);
            return undefined;
        }

        const fields = ['RECORDNO', this.intacctIdField];
        const readByName = await new xtremIntacct.classes.sdk.Functions.ReadByName<
            xtremIntacct.interfaces.IntacctKey[]
        >(this.$.context, {
            fields,
            objectName,
            filter: [intacctId],
            resultFunction: result => {
                if (!result.data) {
                    return [];
                }
                return result.data.map(line => {
                    return {
                        RECORDNO: line.RECORDNO,
                        NAME: get(line, this.intacctIdField),
                    };
                });
            },
        }).execute();

        this.$.context.logger.debug(() => `${JSON.stringify(readByName)}`);

        if (readByName.length > 1) {
            this.$.context.logger.error(() => `More than one ${objectName} for ${intacctId}`);
        }

        return readByName.at(0);
    }

    override async synchronizeMethod(parameters: {
        isForce: boolean;
    }): Promise<{ state: xtremCommunication.enums.IntegrationState; diagnoses?: Diagnose[] }> {
        const { context } = this.$;
        if (!parameters.isForce && (await this.state) === 'pending') {
            const message = this.$.context.localize(
                '@sage/xtrem-intacct-finance/synchronization_already_in_progress',
                'Synchronization is already in progress',
            );
            await context.batch.logMessage('warning', message);
            await context.batch.updateProgress({ successCount: 1, totalCount: 1 });
            return {
                state: 'pending',
                diagnoses: [{ severity: ValidationSeverity.warn, path: ['synchronizeMethod'], message }],
            };
        }

        const node = await this.node;
        const mapping = await this.getMapping();

        await context.batch.logMessage('info', `Start ${await node.title} synchronisation with ${await mapping.id}`);

        if (await context.batch.isStopRequested()) {
            const message = `Stop requested at ${Date.now().toString()}`;
            await context.batch.logMessage('warning', `Stop requested at ${Date.now().toString()}`);
            await context.batch.confirmStop();
            return {
                state: 'error',
                diagnoses: [
                    { severity: ValidationSeverity.error, path: ['synchronizeMethod', 'StopRequested'], message },
                ],
            };
        }

        const { state, diagnoses } = await (
            await xtremIntacctFinance.classes.IntacctSynchronizationManager.create(context, this)
        ).synchronize();

        await context.batch.updateProgress({
            errorCount: 0,
            successCount: 1,
            totalCount: 1,
        });

        return { state: state || 'not', diagnoses };
    }

    onDesynchronized(intacctData: any): Promise<void> {
        this.$.context.logger.debug(() => `Not implemented ${JSON.stringify(intacctData)}`);
        return Promise.resolve();
    }
}
