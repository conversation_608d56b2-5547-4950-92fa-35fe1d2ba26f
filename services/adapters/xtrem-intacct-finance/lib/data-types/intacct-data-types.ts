import { DecimalDataType, RoundingMode, StringDataType } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';

export const intacctIdPropertyDataType = new StringDataType({ maxLength: 200 });

export const intacctFactorDataType = new DecimalDataType({
    precision: 20,
    scale: 10,
    roundingMode: RoundingMode.roundHalfUp,
});

export const intacctUrl = new StringDataType({ maxLength: 200 });

// TODO: build issue if not import datatype from xtrem-intacct-finance. Xtrem-intacct-gateway failed in node extension. Need at least one import form xtrem-intacct-finance.
export const financeIntacctPropertyDataType = xtremIntacct.dataTypes.intacctPropertyDataType;
