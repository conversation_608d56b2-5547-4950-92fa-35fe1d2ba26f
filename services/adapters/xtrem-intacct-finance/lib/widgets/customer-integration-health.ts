import * as ui from '@sage/xtrem-ui';

interface CustomerQueryResponse {
    cursor: string;
    node: {
        _id: string;
        id: string;
        name: string;
        intacctCustomer: {
            intacctId: string;
            state: string;
        };
    };
}

interface CustomerIntegrationStatus {
    _id: string;
    title: string;
    titleRight: string;
    line2: string;
    line2Right: string;
    cursor: string;
}

@ui.widgets.table<CustomerIntegrationHealth>({
    title: 'Customer integration status',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    category: 'Intacct integration',
    content() {
        const numberOfTransactions = this.$.data?.xtremMasterData?.customer?.query?.edges?.length ?? 0;
        if (numberOfTransactions > 0) {
            return this.$.data.xtremMasterData.customer.query.edges.map(
                ({ node, cursor }: CustomerQueryResponse): CustomerIntegrationStatus => ({
                    _id: node._id,
                    title: node.id,
                    titleRight: node.name,
                    line2: ui.localizeEnumMember(
                        '@sage/xtrem-communication/IntegrationState',
                        node.intacctCustomer.state,
                    ),
                    line2Right: node.intacctCustomer.intacctId,
                    cursor,
                }),
            );
        }
        return [];
    },
    callToActions: {
        seeAll: {
            title: 'See all',
            onClick() {
                this.$.router.goTo('@sage/xtrem-master-data/Customer');
            },
        },
    },
    canSwitchViewMode: true,
    displayMode: 'table',
    dataDropdownMenu: {
        orderBy: {
            number: { title: 'Sort by ID' },
            status: { title: 'Sort by name' },
        },
    },
    rowDefinition: {
        title: {
            title: 'ID',
            renderedAs: 'link',
            onClick(_id: string) {
                this.$.router.goTo('@sage/xtrem-master-data/Customer', { _id });
            },
        },
        titleRight: { title: 'Name' },
        line2: {
            title: 'Status',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const row = this.$.data.xtremMasterData.customer.query.edges.find(
                        (item: CustomerQueryResponse) => _id === item.node._id,
                    );
                    switch (row.node.intacctCustomer.state) {
                        case 'not':
                        case 'pending':
                        case 'desynchronized':
                            return 'warning';
                        case 'error':
                            return 'negative';
                        default:
                            return 'neutral';
                    }
                },
            },
        },
        line2Right: {
            title: 'Sage Intacct ID',
        },
    },
    getQuery(args) {
        const filter = {
            intacctCustomer: { state: { _in: ['pending', 'not', 'error', 'desynchronized'] } },
        };
        const orderBy: { id?: number; name?: number } = {};
        if (!this.$.options.dataOptions?.orderBy || this.$.options.dataOptions?.orderBy === 'id') {
            orderBy.id = 1;
        }

        if (this.$.options.dataOptions?.orderBy === 'name') {
            orderBy.name = 1;
        }

        return {
            xtremMasterData: {
                customer: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify(orderBy),
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            cursor: true,
                            node: {
                                _id: true,
                                id: true,
                                name: true,
                                intacctCustomer: {
                                    intacctId: true,
                                    state: true,
                                },
                            },
                        },
                    },
                },
            },
        };
    },
})
export class CustomerIntegrationHealth extends ui.widgets.TableWidget {}
