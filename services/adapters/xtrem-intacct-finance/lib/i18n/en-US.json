{"@sage/xtrem-intacct-finance/action-queued": "The action has been queued.", "@sage/xtrem-intacct-finance/activity__finance_listener__name": "Finance listener", "@sage/xtrem-intacct-finance/activity__intacct_bank_account_transaction_feed__name": "Sage Intacct bank account transaction feed", "@sage/xtrem-intacct-finance/activity__intacct_map__name": "Sage Intacct map", "@sage/xtrem-intacct-finance/already-queued": "The action is already running.", "@sage/xtrem-intacct-finance/bank-account-account-dont-exist": "{{bankName}}: the account {{accountName}} doesn't exist in Sage DMO.", "@sage/xtrem-intacct-finance/bank-account-financial-site-dont-exist": "{{bankName}}: the financial site {{financialSite}} doesn't exist in Sage DMO.", "@sage/xtrem-intacct-finance/cancel": "Cancel", "@sage/xtrem-intacct-finance/cannot_set_tax_with_account_not_subjected_to_taxes": "You cannot set a tax detail with an account that is not subjected to taxes.", "@sage/xtrem-intacct-finance/classes__structure-hooks__intacct_is_active": "Sage Intacct integration is active.", "@sage/xtrem-intacct-finance/classes__structure-hooks__intacct_is_not_active": "Sage Intacct integration is not active.", "@sage/xtrem-intacct-finance/classes__structure-hooks__payment_tracking_is_active": "Intacct activation is not possible if payment tracking is active.", "@sage/xtrem-intacct-finance/company_on_hold_complete": "Company on hold synchronization complete", "@sage/xtrem-intacct-finance/company_on_hold_start": "Company on hold synchronization start", "@sage/xtrem-intacct-finance/company_on_hold_stop": "Stop requested at {{stopDate}} ", "@sage/xtrem-intacct-finance/company_on_hold_success": "Company {{companyForUpdateId}} successfully updated", "@sage/xtrem-intacct-finance/confirm-creation": "Confirm creation", "@sage/xtrem-intacct-finance/confirm-reset": "You are about to reset the record mapping to its initial status.", "@sage/xtrem-intacct-finance/confirm-reset-dialog-title": "Confirm reset", "@sage/xtrem-intacct-finance/confirm-update": "Confirm update", "@sage/xtrem-intacct-finance/create-ar-advance-context": "You are about to create a {{currencySymbol}}{{amount}} payment to {{customer}}.", "@sage/xtrem-intacct-finance/create-ar-advance-title": "Create AR advance", "@sage/xtrem-intacct-finance/create-ar-payment-context": "You are about to create a {{currencySymbol}}{{amount}} payment to {{customer}}.", "@sage/xtrem-intacct-finance/create-ar-payment-title": "Create AR payment", "@sage/xtrem-intacct-finance/data_types__feed_line_matching_status_enum__name": "Feed line matching status enum", "@sage/xtrem-intacct-finance/data_types__feed_record_status_enum__name": "Feed record status enum", "@sage/xtrem-intacct-finance/data_types__intacct_factor_data_type__name": "Intacct factor data type", "@sage/xtrem-intacct-finance/data_types__intacct_id_property_data_type__name": "Intacct ID property data type", "@sage/xtrem-intacct-finance/data_types__intacct_matching_status_enum__name": "Intacct matching status enum", "@sage/xtrem-intacct-finance/data_types__intacct_matching_type_enum__name": "Intacct matching type enum", "@sage/xtrem-intacct-finance/data_types__intacct_record_transaction_type_enum__name": "Intacct record transaction type enum", "@sage/xtrem-intacct-finance/data_types__intacct_url__name": "Intacct URL", "@sage/xtrem-intacct-finance/description": "Description", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__draftMatch": "Draft match", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__lookingForMatches": "Looking for matches", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__matched": "Matched", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__matchFound": "Match found", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__multipleMatches": "Multiple matches", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__unmatched": "Unmatched", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvanceGenerated": "AR advance generated", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvancePosted": "AR advance posted", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvancePostingError": "AR advance posting error", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvancePostingInProgress": "AR advance posting in progress", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentGenerated": "AR payment generated", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentGenerationInProgress": "AR payment generation in progress", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentPosted": "AR payment posted", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentPostingError": "AR payment posting error", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentPostingInProgress": "AR payment posting in progress", "@sage/xtrem-intacct-finance/enums__feed_record_status__draftArAdvance": "Draft AR advance", "@sage/xtrem-intacct-finance/enums__feed_record_status__draftArMatch": "Draft AR match", "@sage/xtrem-intacct-finance/enums__feed_record_status__draftMatch": "Draft match", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntriesFound": "Journal entries found", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryFound": "Journal entry found", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryGenerated": "Journal entry generated", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryGenerationError": "Journal entry generation error", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryGenerationInProgress": "Journal entry generation in progress", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryPosted": "Journal entry posted", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryPostingError": "Journal entry posting error", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryPostingInProgress": "Journal entry posting in progress", "@sage/xtrem-intacct-finance/enums__feed_record_status__lookingForExistingJournalEntries": "Looking for existing journal entries", "@sage/xtrem-intacct-finance/enums__feed_record_status__matched": "Matched", "@sage/xtrem-intacct-finance/enums__feed_record_status__partialArMatch": "Partial AR match", "@sage/xtrem-intacct-finance/enums__feed_record_status__readyForArAdvanceGeneration": "Ready for AR advance generation", "@sage/xtrem-intacct-finance/enums__feed_record_status__readyForArPaymentGeneration": "Ready for AR payment generation", "@sage/xtrem-intacct-finance/enums__feed_record_status__readyForPosting": "Ready for posting", "@sage/xtrem-intacct-finance/enums__feed_record_status__unmatched": "Unmatched", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__cleared": "Cleared", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__draftMatched": "Draft matched", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__ignored": "Ignored", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__matched": "Matched", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__partiallyMatched": "Partially matched", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__selectToMatch": "Select to match", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__selectToUnmatch": "Select to unmatch", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__unmatched": "Unmatched", "@sage/xtrem-intacct-finance/enums__intacct_matching_type__contains": "Contains", "@sage/xtrem-intacct-finance/enums__intacct_matching_type__equals": "Equals", "@sage/xtrem-intacct-finance/enums__intacct_matching_type__regularExpression": "Regular expression", "@sage/xtrem-intacct-finance/enums__intacct_record_transaction_type__deposit": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_record_transaction_type__withdrawal": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/error": "Error", "@sage/xtrem-intacct-finance/functions__map/xtreem-mass-creation-tax-category-update-with-error": "After integrating, you need to update the tax category.", "@sage/xtrem-intacct-finance/id": "ID", "@sage/xtrem-intacct-finance/intacct_bank_account_transaction_feed_has_no_lines_to_post": "The Sage Intacct bank account transaction feed {{intacctbankAccountTransactionFeed}} has no lines to post.", "@sage/xtrem-intacct-finance/intacct_document_not_found": "{{sysId}} {{type}}: Sage Intacct document not found.", "@sage/xtrem-intacct-finance/intacct-address-bill-to": "Bill-to address", "@sage/xtrem-intacct-finance/intacct-address-pay-to": "Pay-to address", "@sage/xtrem-intacct-finance/intacct-address-primary": "Primary address", "@sage/xtrem-intacct-finance/intacct-address-primary-be": "Primary business entity address", "@sage/xtrem-intacct-finance/intacct-address-return-to": "Return-to address", "@sage/xtrem-intacct-finance/intacct-address-ship-to": "Ship-to address", "@sage/xtrem-intacct-finance/intacct-address-ship-to-primary": "Primary ship-to address", "@sage/xtrem-intacct-finance/intacct-addresse-others": "Other address", "@sage/xtrem-intacct-finance/item_extension_item_id_frozen": "You cannot change the item ID if it was integrated with Sage Intacct.", "@sage/xtrem-intacct-finance/mapping-private-show": "You can only activate Include private if the MEGAENTITYID field is present and linked to Sage DMO.", "@sage/xtrem-intacct-finance/menu_item__cashbook-manager": "Bank Manager", "@sage/xtrem-intacct-finance/name": "Name", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredClass": "Required class", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredCustomer": "Required customer", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredDepartement": "Required departement", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredEmploye": "Required employee", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredItem": "Required item", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredLocation": "Required location", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredProject": "Required project", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredSupplier": "Required supplier", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredTask": "Is required task", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__statusIntacct": "Status Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__uIntacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__billToPayToContactName": "Bill-to/Pay-to contact name", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctDocument": "Sage Intacct document", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctIntegrationState": "Sage Intacct integration status", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctLastIntegrationDate": "Sage Intacct last integration date", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctUrl": "Sage Intacct URL", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__shipToReturnToContactName": "Ship-to return-to contact name", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__taxSolutionId": "Tax solution ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__classId": "Class ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__customerId": "Customer ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__departmentId": "Department ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__inclusiveTax": "Including tax", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__intacctDocumentLine": "Sage Intacct document line", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__itemId": "Item ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__lineAmountExcludingTaxSigned": "Line amount excluding tax signed", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__memo": "Memo", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__projectId": "Project ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__taxEntries": "Tax entries", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__vendorId": "Vendor ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__mutation__createArAdvance": "Create AR advance", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__mutation__createArAdvance__failed": "Create ar advance failed.", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__mutation__createArAdvance__parameter__data": "Data", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__bankFeed": "Bank feed", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__entityId": "Entity ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctDocument": "Sage Intacct document", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctIntegrationState": "Sage Intacct integration status", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctLastIntegrationDate": "Sage Intacct last integration date", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctUrl": "Sage Intacct URL", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__paymentMethod": "Payment method", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__classId": "Class ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__customerId": "Customer ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__departmentId": "Department ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__intacctDocumentLine": "Sage Intacct document line", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__itemId": "Item ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__projectId": "Project ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__vendorId": "Vendor ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__mutation__updateOpenItemFromIntacct": "Update open item from Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__mutation__updateOpenItemFromIntacct__failed": "Update open item from intacct failed.", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__mutation__updateOpenItemFromIntacct__parameter__arInvoiceSysId": "AR invoice system ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctDocument": "Sage Intacct document", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctIntegrationState": "Sage Intacct integration status", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctLastIntegrationDate": "Sage Intacct last integration date", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctUrl": "Sage Intacct URL", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__taxSolutionId": "Tax solution ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__classId": "Class ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__customerId": "Customer ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__departmentId": "Department ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__intacctDocumentLine": "Sage Intacct document line", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__itemId": "Item ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__lineAmountExcludingTaxSigned": "Line amount excluding tax signed", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__memo": "Memo", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__projectId": "Project ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__taxEntries": "Tax entries", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__vendorId": "Vendor ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__mutation__createArPayment": "Create AR payment", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__mutation__createArPayment__failed": "Create ar payment failed.", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__mutation__createArPayment__parameter__data": "Data", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__bankFeed": "Bank feed", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__entityId": "Entity ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctDocument": "Sage Intacct document", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctIntegrationState": "Sage Intacct integration status", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctLastIntegrationDate": "Sage Intacct last integration date", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctUrl": "Sage Intacct URL", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__paymentMethod": "Payment method", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__arInvoiceRecordNo": "AR invoice record number", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__intacctDocumentLine": "Sage Intacct document line", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__accounts-payable-invoice-extension__accounts_payable_invoice_already_sent": "The accounts payable invoice was already sent to Sage Intacct. Status: {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-advance-extension__accounts_receivable_advance_already_sent": "The accounts receivable advance was sent to Sage Intacct. Status : {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-invoice-extension__accounts_receivable_invoice_already_sent": "The accounts receivable invoice has already been sent to Sage Intacct. Status: {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-payment-extension__accounts_receivable_payment_already_sent": "The accounts receivable payment was sent to Sage Intacct. Status : {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__bulkMutation__updateRecordNoOnEmployee": "Update record number on employee", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__bulkMutation__updateRecordNoOnEmployee__failed": "Update record number on employee failed.", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__intacctProject": "Sage Intacct project", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__statusIntacct": "Status Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__uIntacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__intacctGlAccount": "Sage Intacct GL account", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__location": "Location", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__megaEntityId": "MEGA entity ID", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__statusIntacct": "Status Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__business_entity_address_extension__property__intacctBusinessEntityAddress": "Sage Intacct business entity address", "@sage/xtrem-intacct-finance/node-extensions__business_entity_contact_extension__property__intacctPrintAs": "Sage Intacct print as", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold": "Synchronization company on hold", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold__failed": "Sync company on hold failed.", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold__parameter__companySysId": "Company system ID", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold__parameter__isAllCompanies": "Is all companies", "@sage/xtrem-intacct-finance/node-extensions__company_extension__property__doApPosting": "Do <PERSON> posting", "@sage/xtrem-intacct-finance/node-extensions__company_extension__property__doArPosting": "Do <PERSON> posting", "@sage/xtrem-intacct-finance/node-extensions__company_extension__property__doUpdateArAmountPaid": "Update AR amount paid", "@sage/xtrem-intacct-finance/node-extensions__customer_extension__property__intacctCustomer": "Sage Intacct customer", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__intacctObject": "Sage Intacct object", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__statusIntacct": "Status Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__uIntacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__dimension_type_extension__property__intacctObject": "Sage Intacct object", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__mutation__generateSmartEventFile": "Generate Smart Event file", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__mutation__generateSmartEventFile__failed": "Generate smart event file failed.", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__property__customPackageFile": "Custom package file", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__property__generateSmartEvent": "Generate Smart Event", "@sage/xtrem-intacct-finance/node-extensions__item_extension__property__id": "ID", "@sage/xtrem-intacct-finance/node-extensions__item_extension__property__intacctItem": "Sage Intacct item", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__entityId": "Entity ID", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctDocument": "Sage Intacct document", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctIntegrationState": "Sage Intacct integration status", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctLastIntegrationDate": "Sage Intacct last integration date", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctUrl": "Sage Intacct URL", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__isActive": "Active", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__taxImplications": "Tax implications", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__taxSolutionId": "Tax solution ID", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_dimension_extension__property__intacctDimension": "Sage Intacct dimension", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__absoluteTransactionAmount": "Absolute transaction amount", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__allocationSplit": "Allocation split", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__excludeRecord": "Exclude record", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctDimensionSplit": "Sage Intacct dimension split", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctDocumentLine": "Sage Intacct document line", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctSign": "Sage Intacct sign", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__isActive": "Active", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__locationWhenNoSplit": "Location when no split", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__taxEntries": "Tax entries", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__statusIntacct": "Status Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__uIntacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__journal-entry-extension__journal_entry_already_sent": "The journal entry has already been sent to Sage Intacct. Status: {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountFromIntacct": "Discount from Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountTypeIntacct": "Discount type Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__dueFromIntacct": "Due from Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__isIntacct": "Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__penaltyTypeIntacct": "Penalty type Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__statusIntacct": "Status Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__uIntacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__supplier_extension__property__intacctSupplier": "Sage Intacct supplier", "@sage/xtrem-intacct-finance/node-extensions__supplier_extension__property__paymentMethodSelect": "Payment method select", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctAccount": "Sage Intacct account", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctSecondaryExternalReference": "Sage Intacct secondary external reference", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctSolutionId": "Sage Intacct solution ID", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctTaxType": "Sage Intacct tax type", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__isIntacct": "Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__isIntacctReverseCharge": "Sage Intacct reverse charge", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__isUpdateFromIntacct": "Update from Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__rate": "Rate", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__recordNo": "Record number", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__statusIntacct": "Status Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__uIntacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/nodes__company_customer_on_hold_control": "You cannot update customer on hold if Sage Intacct is active.", "@sage/xtrem-intacct-finance/nodes__company_get_company_ref_not_mapped": "The return value from Sage Intacct {{intacctCompanyRef}} cannot be mapped.", "@sage/xtrem-intacct-finance/nodes__customer_credit_limit_control": "You cannot update credit limit if Sage Intacct is active.", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument": "Retry finance document", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument__failed": "Retry finance document failed.", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument__parameter__financeDocumentSysId": "Finance document system ID", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument__parameter__financeTransaction": "Finance Transaction", "@sage/xtrem-intacct-finance/nodes__finance_listener__node_name": "Finance listener", "@sage/xtrem-intacct-finance/nodes__finance-listener__already_success": "{{number}}: The finance document was already synchronized.", "@sage/xtrem-intacct-finance/nodes__finance-listener__document_found_on_intacct_updated": "{{NUMBER}}: The document was found in Sage Intacct. Status updated.", "@sage/xtrem-intacct-finance/nodes__finance-listener__document_not_found_on_intacct_posted_waiting": "The document was not found in Sage Intacct. Document posted and waiting for reply from Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__finance-listener__retry": "Processing.", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__node_name": "Sage Intacct accounts payable invoice", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__billBySupplier": "Bill-by supplier", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__billToPayToContactName": "Bill-to / pay-to contact name", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__document": "Document", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__entityId": "Entity ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__shipToReturnToContactName": "Ship-to / return-to contact name", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__taxSolutionId": "Tax solution ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__node_name": "Sage Intacct accounts payable invoice line", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__classId": "Class ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__customerId": "Customer ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__departmentId": "Department ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__documentLine": "Document line", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__employeeId": "Employee ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__inclusiveTax": "Inclusive tax", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__itemId": "Item ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__lineAmountExcludingTaxSigned": "Line amount excluding tax signed", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__memo": "Memo", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__projectId": "Project ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__signedAmountExcludingTax": "Signed amount excluding tax", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__taskId": "Task ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__taxEntries": "Tax entries", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__vendorId": "Vendor ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__node_name": "Sage Intacct accounts receivable advance", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__bankFeed": "Bank feed", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__document": "Document", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__entityId": "Entity ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__paymentMethod": "Payment method", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__node_name": "Sage Intacct accounts receivable advance line", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__classId": "Class ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__customerId": "Customer ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__departmentId": "Department ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__documentLine": "Document line", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__itemId": "Item ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__projectId": "Project ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__vendorId": "Vendor ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__node_name": "Sage Intacct accounts receivable invoice", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__billToCustomer": "Bill-to customer", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__document": "Document", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__entityId": "Entity ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__taxSolutionId": "Tax solution ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__node_name": "Sage Intacct accounts receivable invoice line", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__classId": "Class ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__customerId": "Customer ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__departmentId": "Department ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__documentLine": "Document line", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__employeeId": "Employee ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__inclusiveTax": "Inclusive tax", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__itemId": "Item ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__lineAmountExcludingTaxSigned": "Line amount excluding tax signed", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__memo": "Memo", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__projectId": "Project ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__signedAmountExcludingTax": "Signed amount excluding tax", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__taskId": "Task ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__taxEntries": "Tax entries", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__vendorId": "Vendor ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__node_name": "Sage Intacct accounts receivable payment", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__bankFeed": "Bank feed", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__document": "Document", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__entityId": "Entity ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__paymentMethod": "Payment method", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__node_name": "Sage Intacct accounts receivable payment line", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__property__arInvoiceRecordNo": "AR invoice record number", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__property__documentLine": "Document line", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__mutation__bulkSave": "Bulk save", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__mutation__bulkSave__failed": "Bulk save failed.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__mutation__bulkSave__parameter__intacctBankAccountMatchings": "Sage Intacct bank account matchings", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__node_name": "Sage Intacct bank account matching", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__account": "Account", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__analyticalData": "Analytical data", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__bankAccount": "Bank account", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__keyword": "Keyword", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__location": "Location", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__priority": "Priority", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__storedAttributes": "Stored attributes", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__storedDimensions": "Stored dimensions", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__tax": "Tax", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__transactionType": "Transaction type", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__type": "Type", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__updateAccountTaxManagement": "Update account tax management", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctArInvoice": "Query Sage Intacct AR invoice", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctArInvoice__failed": "Query intacct ar invoice failed.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctArInvoice__parameter__parameters": "Parameters", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctCustomers": "Query Sage Intacct customers", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctCustomers__failed": "Query Intacct customers failed.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument": "Query Sage Intacct document", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument__failed": "Query intacct document failed.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument__parameter__documentType": "Document type", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument__parameter__parameters": "Parameters", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__wrong_account_tax_management": "You can only select an account linked to a tax management that is either 'Other' or 'Excluding tax'.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared": "Set cleared", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared__failed": "Set cleared failed.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared__parameter__cleared": "Cleared", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared__parameter__recordNo": "Record number", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__node_name": "Sage Intacct bank account transaction feed", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__account": "Account", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__accountFeedKey": "Account feed key", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__accountReconKey": "Bank account reconciliation key", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__accountType": "Account type", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__amount": "Amount", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__amountToMatch": "Amount to match", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__analyticalData": "Analytical data", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__arMatch": "AR match", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__bankAccount": "Bank account", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__cleared": "Cleared", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__computedAttributes": "Computed attributes", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__description": "Description", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__documentNumber": "Document number", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__documentType": "Document type", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__entity": "Entity", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__entityName": "Entity name", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__feedType": "Feed type", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentCreatedNumber": "Finance document created number", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentCreatedSysId": "Finance document created system ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentGenerationErrorMessage": "Finance document generation error message", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentType": "Finance document type", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeIntegrationApp": "Finance integration application", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeIntegrationAppRecordId": "Finance integration application record ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeIntegrationAppUrl": "Finance integration application URL", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__importSession": "Import session", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__intacctCleared": "Sage Intacct cleared", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__internalFinanceIntegrationStatus": "Internal finance integration status", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__journalEntryNumber": "Journal entry number", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__jsonArInvoices": "JSON AR invoices", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__lines": "Lines", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__payee": "Payee", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__paymentDate": "Payment date", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__paymentMethod": "Payment method", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__payToCustomerId": "Pay-to customer ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__payToCustomerName": "Pay-to customer name", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__postingDate": "Posting date", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__receiptDate": "Receipt date", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__reconcilitationDate": "Reconciliation date", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__storedAttributes": "Stored attributes", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__storedDimensions": "Stored dimensions", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__targetDocumentType": "Target document type", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__transactionId": "Transaction ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__transactionType": "Transaction type", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine": "Get match line", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine__failed": "Get match line failed.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine__parameter__matchingRules": "Matching rules", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine__parameter__transactionFeed": "Transaction feed", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__node_name": "Sage Intacct bank account transaction feed line", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__account": "Account", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__amount": "Amount", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__analyticalData": "Analytical data", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__document": "Document", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__intacctJournalEntryBatchNo": "Sage Intacct journal entry batch number", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__intacctJournalEntryRecordNo": "Sage Intacct journal entry record no.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__intacctJournalEntryUrl": "Sage Intacct journal entry URL", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__location": "Location", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__status": "Status", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__tax": "Tax", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__taxAmount": "Tax amount", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__taxRate": "Tax rate", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__node_name": "Sage Intacct bank account transaction feed line dimension", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__property__originLine": "Origin line", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__mutation__serviceOptionChange": "Service option change", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__mutation__serviceOptionChange__failed": "Service option change failed.", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__node_name": "Sage Intacct Cash Book Management", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__query__isServiceOptionActiveFunction": "Service option active function", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__query__isServiceOptionActiveFunction__failed": "Is service option active function failed.", "@sage/xtrem-intacct-finance/nodes__intacct_contact__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_contact__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_contact__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_contact__node_name": "Sage Intacct contact", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__contact": "Contact", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__name": "Name", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__prefix": "Prefix", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__printAs": "Print as", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__status": "Status", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__sysIdLink": "System ID link", "@sage/xtrem-intacct-finance/nodes__intacct_customer__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_customer__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_customer__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_customer__node_name": "Sage Intacct customer", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__billToAddress": "Bill-to address", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__parent": "Parent", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__shipToAddress": "Ship-to address", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__status": "Status", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__node_name": "Sage Intacct customer supplier", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__contactList": "Contact list", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__hideDisplayContact": "Hide display contact", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__primaryContact": "Primary contact", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__status": "Status", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__sysIdLink": "System ID link", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__currencies_do_not_match": "The bank account currency must be the same as the bank account financial site currency.", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__node_name": "Sage Intacct import session", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__bankAccount": "Bank account", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__dateCreation": "Creation date", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__description": "Description", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__linesImported": "Lines imported", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__mapObject": "Map object", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__numberOfLinesToImport": "Number of lines to import", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__queryParameters": "Query parameters", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__status": "Status", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__transactionFeed": "Transaction feed", "@sage/xtrem-intacct-finance/nodes__intacct_item__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_item__node_name": "Sage Intacct item", "@sage/xtrem-intacct-finance/nodes__intacct_item__property__item": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_item__property__status": "Status", "@sage/xtrem-intacct-finance/nodes__intacct_item__property__type": "Type", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__node_name": "Sage Intacct journal entry", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__document": "Document", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__entityId": "Entity ID", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__taxImplications": "Tax implications", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__taxSolutionId": "Tax solution ID", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__node_name": "Sage Intacct journal entry line", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__absoluteTransactionAmount": "Absolute transaction amount", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__allocationSplit": "Allocation split", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__dimensionSplit": "Dimension split", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__documentLine": "Document line", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__excludeRecord": "Exclude record", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__locationWhenNoSplit": "Location when no split", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__sign": "Sign", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__taxEntries": "Tax entries", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct": "Delete Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct__failed": "Delete intacct failed.", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct__parameter__intacctNode": "Sage Intacct node", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct__parameter__recordNo": "Record number", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__synchronizeNode": "Synchronize node", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__synchronizeNode__failed": "Synchronize node failed.", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__synchronizeNode__parameter__intacctNode": "Sage Intacct node", "@sage/xtrem-intacct-finance/nodes__intacct_listener__node_name": "Sage Intacct listener", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob": "Sage Intacct mass creation job", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob__failed": "Intacct mass creation job failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob__parameter__intacctName": "Sage Intacct name", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob__parameter__type": "Type", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__xtreemMassCreationJob": "Xtreem mass creation job", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__xtreemMassCreationJob__failed": "Xtreem mass creation job failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__xtreemMassCreationJob__parameter__data": "Data", "@sage/xtrem-intacct-finance/nodes__intacct_map__bulkMutation__updateCustomMapping": "Update custom mapping", "@sage/xtrem-intacct-finance/nodes__intacct_map__bulkMutation__updateCustomMapping__failed": "Update custom mapping failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__createUpdateAllIntacct": "Create update all Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__createUpdateAllIntacct__failed": "Create update all intacct failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__createUpdateAllIntacct__parameter__intacctName": "Sage Intacct name", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem": "Delete Xtrem", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__failed": "Delete xtrem failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__parameter__intacctIdValue": "Sage Intacct ID value", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__parameter__intacctName": "Sage Intacct name", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__parameter__xtremSysId": "Xtrem system ID", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl": "Get Sage Intacct URL", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl__failed": "Get Intacct URL failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl__parameter___id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl__parameter__nodeName": "Node name", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getStructure": "Get structure", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getStructure__failed": "Get structure failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getStructure__parameter__content": "Content", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__updateXtrem": "Update Xtrem", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__updateXtrem__parameter__data": "Data", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__writeStructure": "Write structure", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__writeStructure__failed": "Write structure failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__writeStructure__parameter__object": "Object", "@sage/xtrem-intacct-finance/nodes__intacct_map__node_name": "Sage Intacct mapping", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__additionnalLink": "Additional link", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__customRelationMapping": "Custom relation mapping", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__editableFields": "Editable fields", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__intacctDescription": "Sage Intacct description", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__intacctFilter": "Sage Intacct filter", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__isPrivateShow": "Private show", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__isSmartEvent": "Smart Event", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__lines": "Lines", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__relationMapping": "Relation mapping", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__setupId": "Setup ID", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__specificFields": "Specific fields", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__uRelationMapping": "Relation mapping", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getAvailableIntacctObjectList": "Get available Sage Intacct object list", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getAvailableXtremObjectList": "Get available Xtrem object list", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getAvailableXtremObjectList__failed": "Get available xtrem object list failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct": "Get data Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__failed": "Get data Intacct failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__filters": "Filters", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__maxData": "Maximum data", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__name": "Name", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__transaction": "Transaction", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getIntacctTransactionsList": "Get Sage Intacct transactions list", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getIntacctTransactionsList__failed": "Get intacct transactions list failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject": "Get object", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__failed": "Get object failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__parameter__docparid": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__parameter__isUpdate": "Update", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__parameter__object": "Object", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getPropertiesList": "Get properties list", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getPropertiesList__parameter__tableName": "Table name", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__node_name": "Sage Intacct supplier", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__parent": "Parent", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__payToAddress": "Pay-to address", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__returnToAddress": "Return-to address", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__status": "Status", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_finance_state__node_name": "Sage Intacct synchronization finance status", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_finance_state__property__externalIntegration": "External integration", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_state__node_name": "Sage Intacct synchronization status", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_state__property__entityId": "Entity ID", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__site_legislations_and_coa_legislation_dont_match": "The site legislation must be the same as the chart of accounts legislation.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_legislation_and_coa_legislation_dont_match": "The tax legislation must be the same as the chart of accounts legislation.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_management_should_be_excluding_taxes": "You cannot set a tax detail with an account that is not subjected to taxes.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_mandatory": "The tax is mandatory.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_not_allowed": "The tax must be empty.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction_feed__site_legislation_and_coa_legislation_dont_match": "The site legislation must be the same as the chart of accounts legislation.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction_feed__tax_legislation_and_coa_legislation_dont_match": "The tax legislation must be the same as the chart of accounts legislation.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed__tax_mandatory": "The tax is mandatory.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed__tax_not_allowed": "The tax must be empty.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed/bank-manager-import-fail": "Sage DMO Bank Manager : The import failed ({{filters}}). \n {{error}}.", "@sage/xtrem-intacct-finance/nodes__intacct-listener/delete-success": "Deleted.", "@sage/xtrem-intacct-finance/nodes__intacct-listener/intacct-time-out": "Time out: <PERSON> did not answer.", "@sage/xtrem-intacct-finance/nodes__intacct-listener/not-implemented": "Not implemented: {{change}}:{{object}}  ", "@sage/xtrem-intacct-finance/nodes__intacct-listener/synchronize-node-finish": "Synchronization {{state}}.", "@sage/xtrem-intacct-finance/nodes__intacct-listener/synchronize-node-start": "Synchronization started.", "@sage/xtrem-intacct-finance/nodes__intacct-map/create-update-xtrem": "Too many lines returned from Sage Intacct ({{lenght}}) - intacctId : {{intacctIdValue}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/desynchronized_not_exist": "Desynchronized {{node}}: {{name}} Sage Intacct ID: {{id}} could not be found.", "@sage/xtrem-intacct-finance/nodes__intacct-map/file-already-exist": "The file {{object}} already exists.", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-instance-query-to-many-lines": "Too many lines returned from Sage Intacct ({{countIntacctData}}). \n Add filters and try again.", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end": "Mass creation of {{intacctName}} as finish at {{dateTime}}. \n {{errorCount}}errors, {{successCount}} success /{{totalCount}} total ", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end-with-errors-phase": "Finished with errors.", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end-without-errors-phase": "Finished.", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-fail": "Sage Intacct: The mass creation failed ({{intacctName}}).", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-number-of-lines": "{{numberOfLines}} to create/update.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-desynchronization": "The object is synchronized.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-intacct-data": "No Sage Intacct data for the {{intacctName}} object where {{intacctIDField}} equals {{intacctIdValue}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-intacct-instance": "No Sage Intacct instance activated.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-map-instance": "The file cannot be created because the {{object}} does not exist in Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__intacct-map/nothing-to-write": "The file {{object}} cannot be written.", "@sage/xtrem-intacct-finance/nodes__intacct-map/to-many-lines": "The query returned many lines: {{numberOflines}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-end": "Mass creation of {{intacctName}} as finish at {{dateTime}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-number-of-lines": "{{numberOfLines}} to create/update.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-start": "Mass creation of {{intacctName}} as started at {{dateTime}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-tax-category-update": "\n After integrating, you need to update the tax category.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-created-updated": "Sage DMO {{created}}: {{id}} ", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-created-updated-total": "Records created: {{created}}. /n Records updated: {{updated}}. /n Records with errors: {{error}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-query-to-many-lines": "Too many lines returned from Sage DMO ({{numberOfNodes}}).\n Add filters and try again.", "@sage/xtrem-intacct-finance/nodes__map_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__map_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__map_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__map_line__node_name": "Map line", "@sage/xtrem-intacct-finance/nodes__map_line__property__collectionName": "Collection name", "@sage/xtrem-intacct-finance/nodes__map_line__property__line": "Line", "@sage/xtrem-intacct-finance/nodes__map_line__property__mapHeader": "Map header", "@sage/xtrem-intacct-finance/nodes__map_line__property__propertyHeader": "Property header", "@sage/xtrem-intacct-finance/nodes__map_line__property__propertyLine": "Property line", "@sage/xtrem-intacct-finance/nodes__map_property__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__map_property__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__map_property__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__map_property__node_name": "Map property", "@sage/xtrem-intacct-finance/nodes__map_property__property__map": "Map", "@sage/xtrem-intacct-finance/nodes__map_property__property__name": "Name", "@sage/xtrem-intacct-finance/nodes__map_property__property__type": "Type", "@sage/xtrem-intacct-finance/nodes__tax_extension__deletion_forbidden": "You cannot delete this tax record. It is linked to Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_not_allowed": "You cannot create, edit or delete this tax record. It is linked to Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_active_is_forbidden": "You cannot enable this tax record. It is linked to Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_country_is_forbidden": "You cannot edit the country.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_primary_external_reference_is_forbidden": "You cannot edit the primary external reference.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_reverse_charge_is_forbidden": "You cannot edit the reverse charge.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_tax_type_is_forbidden": "You cannot edit the tax type. It is linked to Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__tax_extension__negative_rate_not_allowed": "You cannot enter a negative rate.", "@sage/xtrem-intacct-finance/package__name": "Sage Intacct finance", "@sage/xtrem-intacct-finance/page__intacct_mapping_create_all_in_intacct": "Create all in Sage Intacct.", "@sage/xtrem-intacct-finance/page__intacct_mapping_create_all_in_intacct_manufacturing": "Create all in Sage DMO", "@sage/xtrem-intacct-finance/page__intacct-map/xtrem-instance-error": "Sage Intacct ID __{{intacctId}}__ not saved.", "@sage/xtrem-intacct-finance/page_extensions__common__warning": "Warning", "@sage/xtrem-intacct-finance/page_extensions__there_are_ap_invoices_not_posted": "There are AP invoices that are not yet posted to the GL.", "@sage/xtrem-intacct-finance/page_extensions__there_are_ar_invoices_not_posted": "There are AR invoices that are not yet posted to the GL.", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__addresses____columns__helperText__intacctBusinessEntityAddress___id": "Synchronizing", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__addresses____columns__title__intacctBusinessEntityAddress___id": "Refresh", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__addresses____columns__title__intacctBusinessEntityAddress__state": "Integration status", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__displayAddresses____columns__helperText__refresh": "Synchronizing", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__displayAddresses____columns__title__intacctBusinessEntityAddress__state": "Integration status", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__displayAddresses____columns__title__refresh": "Refresh", "@sage/xtrem-intacct-finance/page-extensions__company_extension__creditLimitBlock____title": "Credit limit", "@sage/xtrem-intacct-finance/page-extensions__company_extension__doUpdateArAmountPaid____title": "Update amount paid on sales document reports", "@sage/xtrem-intacct-finance/page-extensions__company_extension__managementSection____title": "Management", "@sage/xtrem-intacct-finance/page-extensions__company_extension__synchronize____helperText": "Synchronizing", "@sage/xtrem-intacct-finance/page-extensions__company_extension__synchronize____title": "Refresh", "@sage/xtrem-intacct-finance/page-extensions__customer_extension____navigationPanel__listItem__line16__title": "Sage Intacct ID", "@sage/xtrem-intacct-finance/page-extensions__customer_extension____navigationPanel__listItem__line17__title": "Sage Intacct integration status", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__addresses____columns__helperText___id": "Synchronizing", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__addresses____columns__title___id": "Refresh", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__addresses____columns__title__intacctBusinessEntityAddress__state": "Integration state", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__addresses____levels__columns__title__intacctBusinessEntityAddress__state": "Integration status", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__displayAddresses____columns__helperText": "Synchronizing", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__displayAddresses____columns__title": "Refresh", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__displayAddresses____columns__title__intacctBusinessEntityAddress__state": "Integration status", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctId____title": "Sage Intacct ID", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctIntegrationState____title": "Integration status", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctUrl____title": "Sage Intacct URL", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctUrlLink____title": "Sage Intacct ID", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__synchronize____helperText": "Synchronizing", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__synchronize____title": "Refresh", "@sage/xtrem-intacct-finance/page-extensions__dimension_type_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__dimension_type_extension__intacctObject____title": "Sage Intacct object", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__customPackageFile____title": "Custom package file", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__customPackageSection____title": "Custom package", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__intacctDescription": "Sage Intacct name", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__nodeFactory__name": "Sage DMO object", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__synchronizationDirection": "Synchronization", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____title": "Smart Events to generate", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__objectSelect____title": "Sage Intacct object", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__save____title": "Save", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__smartEventBlock____title": "Smart Events ", "@sage/xtrem-intacct-finance/page-extensions__item_extension____navigationPanel__listItem__intacctId__title": "Sage Intacct ID", "@sage/xtrem-intacct-finance/page-extensions__item_extension____navigationPanel__listItem__intacctState__title": "Sage Intacct integration status", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctId____title": "Sage Intacct ID", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctIntegrationState____title": "Integration status", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctUrl____title": "Sage Intacct URL", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctUrlLink____title": "Sage Intacct ID", "@sage/xtrem-intacct-finance/page-extensions__item_extension__synchronize____helperText": "Synchronizing", "@sage/xtrem-intacct-finance/page-extensions__item_extension__synchronize____title": "Synchronize", "@sage/xtrem-intacct-finance/page-extensions__journal_extension__recordNo____title": "Record number", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension____navigationPanel__listItem__line12__title": "Sage Intacct ID", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension____navigationPanel__listItem__line13__title": "Sage Intacct integration state", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension____navigationPanel__listItem__line8__title": "Sage Intacct ID", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension____navigationPanel__listItem__line9__title": "Sage Intacct integration status", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__addresses____columns__helperText___id": "Synchronizing", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__addresses____columns__title___id": "Refresh", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__addresses____columns__title__intacctBusinessEntityAddress__state": "Integration state", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__addresses____levels__columns__title__intacctBusinessEntityAddress__state": "Integration status", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__displayAddresses____columns__helperText": "Synchronizing", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__displayAddresses____columns__title": "Refresh", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__displayAddresses____columns__title__intacctBusinessEntityAddress__state": "Integration status", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctId____title": "Sage Intacct ID", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctIntegrationState____title": "Integration status", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctUrl____title": "Sage Intacct URL", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctUrlLink____title": "Sage Intacct ID", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__paymentMethodSelect____title": "Payment method", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__synchronize____helperText": "Synchronizing", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__synchronize____title": "Refresh", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__country____title": "Country", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__country__name": "Country", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__isIntacctReverseCharge": "Sage Intacct reverse charge", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__primaryExternalReference": "Primary external reference", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__taxCategory__name": "Tax category", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____title": "Secondary external reference", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isActive____title": "Active", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isIntacct____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isIntacctReverseCharge____title": "Sage Intacct reverse charge", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isReverseCharge____title": "Reverse charge", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__primaryExternalReference____title": "Primary external reference", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__secondaryExternalReference____title": "Secondary external reference", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__type____title": "Tax type", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching____title": "Accounts receivable invoice matching", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__addAdvanceDimensionAttribute____title": "Dimensions", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__columns__account__name__title": "Tax management", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__account__name": "Account", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__customerId": "Customer ID", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__customerName": "Customer name", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__errorMessage": "Message", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__intacctArDocumentID": "Sage Intacct AR advance", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__paymentDate": "Payment date", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__paymentMethod": "Payment method", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__receiptDate": "Receipt date", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__totalAmount": "Total amount", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____dropdownActions__title": "Delete line", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____title": "AR advance generation", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__amountToMatch____title": "Amount to match", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__bankAccount____title": "Bank account", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__cancel____title": "Cancel", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__createArAdvance____title": "Create AR advance", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__createArPayment____title": "Create AR payment", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__customer_id_was_found": "Customer ID {{customerId}} found.", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__customer_name_was_found": "Customer name {{customerName}} found.", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoice_number_was_found": "Invoice number {{invoiceNumber}} found.", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__columns__payentTerm__name__title": "Name", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__columns__payentTerm__name__title__2": "Description", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__columns__payentTerm__name__title__3": "ID", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__amountDue": "Amount due", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__amountMatch": "Amount match", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__amountToMatch": "Amount to match", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__customerId": "Customer ID", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__customerName": "Customer name", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__date": "Invoice date", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__dueDate": "Due date", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__entityId": "Entity ID", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__invoiceDate": "Invoice date", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__invoiceNo": "Invoice number", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__invoiceNumber": "Invoice number", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__payentTerm__name": "Term", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__referenceNumber": "Reference number", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__term__name": "Term", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__totalAmount": "Total amount", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__totalDue": "Amount due", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__totalPaid": "Total paid", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____title": "Invoices", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__mainBlock____title": "General", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__mainSection____title": "General", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__matchingReason____title": "Matching reason", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__ok____title": "OK", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__customerId": "Customer ID", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__customerName": "Customer name", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__errorMessage": "Message", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__intacctArDocumentID": "Sage Intacct AR payment", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__paymentDate": "Payment date", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__paymentMethod": "Payment method", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__receiptDate": "Receipt date", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__totalAmount": "Total amount", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____title": "Payment generation", "@sage/xtrem-intacct-finance/pages__bank_account____navigationPanel__listItem__line3__title": "Entity ID", "@sage/xtrem-intacct-finance/pages__bank_account____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-intacct-finance/pages__bank_account____objectTypePlural": "Bank accounts", "@sage/xtrem-intacct-finance/pages__bank_account____objectTypeSingular": "Bank account", "@sage/xtrem-intacct-finance/pages__bank_account____title": "Bank account", "@sage/xtrem-intacct-finance/pages__bank_account___id____title": "ID", "@sage/xtrem-intacct-finance/pages__bank_account__account____title": "Account", "@sage/xtrem-intacct-finance/pages__bank_account__currency____columns__title__id": "ISO 4217", "@sage/xtrem-intacct-finance/pages__bank_account__currency____columns__title__name": "Name", "@sage/xtrem-intacct-finance/pages__bank_account__currency____columns__title__symbol": "Symbol", "@sage/xtrem-intacct-finance/pages__bank_account__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-intacct-finance/pages__bank_account__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__bank_account__id____title": "ID", "@sage/xtrem-intacct-finance/pages__bank_account__isActive____title": "Active", "@sage/xtrem-intacct-finance/pages__bank_account__mainBlock____title": "Bank account information", "@sage/xtrem-intacct-finance/pages__bank_account__mainSection____title": "General", "@sage/xtrem-intacct-finance/pages__bank_account__megaEntityId____title": "Entity ID", "@sage/xtrem-intacct-finance/pages__bank_account__name____title": "Name", "@sage/xtrem-intacct-finance/pages__delete_page_dialog_content": "You are about to delete this record.", "@sage/xtrem-intacct-finance/pages__delete_page_dialog_title": "Confirm deletion", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog____objectTypePlural": "Sage Intacct bank account matching rules", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog____objectTypeSingular": "Sage Intacct bank account matching rule", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog____title": "Matching rules", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__addRule____title": "Add rule", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__apply____title": "Apply matching rules", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__cancel____title": "Cancel", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__mainSection____title": "General", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__account__name__title": "Tax management", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__account__name__title__5": "Legislation", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__location__name__title__2": "Name", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__tax__name__title__2": "Name", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title___id": "ID", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__account__name": "Account", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__bankAccount__id": "Bank account", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__isModified": "Modified", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__keyword": "Keyword", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__priority": "Priority", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__tax__name": "Tax", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__transactionType": "Transaction type", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__type": "Type", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____dropdownActions__title": "Delete", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____dropdownActions__title__2": "Save", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____dropdownActions__title__3": "Dimensions", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____title": "Matching rules", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__save____title": "Save", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____navigationPanel__listItem__line2__title": "ID", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____navigationPanel__listItem__title__title": "Description", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____objectTypePlural": "Bank account transaction feeds", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____objectTypeSingular": "Bank account transaction feed", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____title": "Transaction feed", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__accountFeedKey____title": "Financial account feed key", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__accountReconKey____title": "Bank account reconciliation key", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__accountType____title": "Financial account type", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__amount____title": "Amount", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__amountToMatch____title": "Amount to match", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__cleared____title": "Cleared", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__description____title": "Description", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__documentNumber____title": "Document number", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__documentType____title": "Document type", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__entity____title": "Financial entity", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__entityName____title": "Financial entity name", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__feedType____title": "Feed type", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__intacctId____title": "ID", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__mainBlock____title": "Transaction", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__mainSection____title": "General", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__number____title": "Number", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__payee____title": "Payee", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__postingDate____title": "Posting date", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__reconcilitationDate____title": "Reconciliation date", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__transactionId____title": "Transaction ID", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__transactionType____title": "Transaction type", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog____objectTypeSingular": "Sage Intacct document search", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog____title": "Document search", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__account____title": "Account", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__amount____title": "Amount", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__criteriaBlock____title": "Criteria", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__date____title": "Date", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__description____title": "Description", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__documentType____placeholder": "Select document type", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__documentType____title": "Document type", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__account": "Account", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__amount": "Amount", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__date": "Date", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__description": "Description", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__documentType": "Document type", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__entityId": "Entity", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__journal": "Journal", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__location": "Location", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__url": "Accounting integration reference", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____title": "Results", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__select____title": "Save", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line_4__title": "Smart Event", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line_5__title": "Synchronization way", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line2__title": "Sage Intacct transactions", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line6__title": "Include private", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__title__title": "Sage Intacct object", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__titleRight__title": "Sage DMO node", "@sage/xtrem-intacct-finance/pages__intacct_map____objectTypePlural": "Sage Intacct mappings", "@sage/xtrem-intacct-finance/pages__intacct_map____objectTypeSingular": "Sage Intacct mapping", "@sage/xtrem-intacct-finance/pages__intacct_map____title": "Mapping", "@sage/xtrem-intacct-finance/pages__intacct_map__additionnalLink____title": "Aditionnal links", "@sage/xtrem-intacct-finance/pages__intacct_map__additionnalLinkFormated____title": "Aditionnal links", "@sage/xtrem-intacct-finance/pages__intacct_map__addLineSpecificFields____title": "Add component", "@sage/xtrem-intacct-finance/pages__intacct_map__createAll____title": "Create all in Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__createAllIntacct____title": "Create all in Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__dataSection____title": "Data", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__DATATYPE": "Data type", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__DESCRIPTION": "Description", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__ID": "ID", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__ISCUSTOM": "Custom", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__isEditable": "Editable", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__LABEL": "Label", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__READONLY": "Read only", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__REQUIRED": "Required", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__xtremDefaultProperty": "Default property", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__xtremProperty": "Sage DMO property", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__xtremPropertyOption": "Property option", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____title": "Fields", "@sage/xtrem-intacct-finance/pages__intacct_map__id____title": "Sage Intacct object", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctDataBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctDescription____title": "Sage Intacct transactions", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctFilter____title": "Sage Intacct filters", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctNameSelect____title": "Sage Intacct transactions", "@sage/xtrem-intacct-finance/pages__intacct_map__isActive____title": "Active", "@sage/xtrem-intacct-finance/pages__intacct_map__isPrivateShow____title": "Include private", "@sage/xtrem-intacct-finance/pages__intacct_map__isSmartEvent____title": "Smart Event", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__collectionName": "Collection name", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__line__id": "Object", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__line__intacctDescription": "Name", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__propertyHeader": "Header property", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__propertyLine": "Line property", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____title": "Mappings", "@sage/xtrem-intacct-finance/pages__intacct_map__lineSection____title": "Collections/Lines", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__integrationStatus": "Integration status", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__isLinked": "Linked", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__megaEntityId": "Entity ID", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__whenCreated": "Create", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__whenModified": "Update", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title": "Create in Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title__2": "Update in Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title__3": "Delete in Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____title": "Results", "@sage/xtrem-intacct-finance/pages__intacct_map__mainBlock____title": "Configuration", "@sage/xtrem-intacct-finance/pages__intacct_map__mainSection____title": "Mapping", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeFactory____columns__title__name": "Node", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeFactory____columns__title__title": "Node", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeFactory____title": "Node factory", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeProperties____title": "Fields", "@sage/xtrem-intacct-finance/pages__intacct_map__notificationStatus____title": "Mass import status", "@sage/xtrem-intacct-finance/pages__intacct_map__refreshData____title": "Refresh", "@sage/xtrem-intacct-finance/pages__intacct_map__refreshRelationMapping____title": "Refresh", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__DATATYPE": "Data type", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__DESCRIPTION": "Description", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__ID": "ID", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__ISCUSTOM": "Custom", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__isEditable": "Editable", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__LABEL": "Label", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__READONLY": "Read only", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__REQUIRED": "Required", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__xtremDefaultProperty": "Default property", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__xtremProperty": "Sage DMO property", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__xtremPropertyOption": "Property option", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____title": "Relationships", "@sage/xtrem-intacct-finance/pages__intacct_map__resetRelationMapping____title": "Reset", "@sage/xtrem-intacct-finance/pages__intacct_map__save____title": "Save", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____columns__title__name": "Sage Intacct property name", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____columns__title__type": "Type", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____columns__title__whereValue": "Filter", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____dropdownActions__title": "Remove", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____title": "Technical properties", "@sage/xtrem-intacct-finance/pages__intacct_map__synchronizationDirection____title": "Synchronization way", "@sage/xtrem-intacct-finance/pages__intacct_map__writeMappingFile____title": "Write mapping file", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____navigationPanel__listItem__title__title": "Bank", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____objectTypePlural": "Transaction feeds", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____objectTypeSingular": "", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____title": "Transaction workbench", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query___etag____title": "ETag", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query___id____title": "ID", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____columns__columns__account__name__title__3": "Name", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____columns__columns__currency___id__title__4": "Decimal digits", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____columns__columns__financialSite___id__title__2": "ID", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____title": "Bank account", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__criteriaBlock____title": "Criteria", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__dateCreation____title": "Creation date", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__dateFrom____title": "From date", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__dateTo____title": "To date", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__description____title": "Description", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__mainSection____title": "Transaction workbench", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__mapObject____columns__title__intacctDescription": "Name", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__mapObject____title": "Mapping", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matched____title": "Matched", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matchingRules____title": "Open matching rules", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matchStatus____placeholder": "Select status", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matchStatus____title": "Status", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__number_of_journal_entries_created": "Journal entries created: {{documentsPosted}}.", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__post____title": "Post", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__refreshTransactionFeed____title": "Refresh", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__save____title": "Save", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__status____placeholder": "Select status", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__status____title": "Status", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__totalAmount____title": "Total amount", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__totalDeposit____title": "Total deposit", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__totalWithdrawal____title": "Total withdrawal", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__account__name__title": "Tax management", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__account__name__title__6": "ID", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__account__name__title__7": "Tax management", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__bankAccount__name__columns__title___id": "ID", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__bankAccount__name__columns__title___id__2": "ID", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__bankAccount__name__title__5": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__currency__name__title": "Name", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__currency__name__title__3": "Name", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__document___id__title__5": "Amount", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__location__name__title__2": "Name", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__tax__name__title__2": "Name", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title___id": "ID", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__account__name": "Account", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__amount": "Amount", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__amount__2": "Amount", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__amountToMatch": "Amount to match", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__currency__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__description": "Description", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__intacctJournalEntryBatchNo": "Accounting integration reference", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__location__name": "Location", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__payee": "Payee", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__status": "Line status", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__tax__name": "Tax", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__taxAmount": "Tax amount", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__transactionType": "Transaction type", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title": "AR payment", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__2": "Delete line", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__3": "Split line", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__4": "Dimensions", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__5": "Search", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__6": "Create rule", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__8": "Search", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__9": "Create rule", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____title": "Records", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionType____placeholder": "Transaction type", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionType____title": "Transaction type", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__unMatched____title": "Unmatched", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__withPostableFeeds____title": "With postable feeds", "@sage/xtrem-intacct-finance/pages__intact_transaction_feed_query__map_object_mandatory": "The mapping object Bank account transaction feed records is not defined", "@sage/xtrem-intacct-finance/pages__matching_rules_bulk_save": "Matching rules saved.", "@sage/xtrem-intacct-finance/pages__tax_extension__was_deactivated_secondary_external_reference": "The tax is turned off because the secondary external reference is empty and the reverse charge is selected.", "@sage/xtrem-intacct-finance/pages_accounts_receivable_invoice_matching_advance_created": "Advance created:", "@sage/xtrem-intacct-finance/pages_accounts_receivable_invoice_matching_payment_created": "Payment created:", "@sage/xtrem-intacct-finance/pages_document-no-document-selected": "No document selected.", "@sage/xtrem-intacct-finance/pages_document-search-more-than-one": "You can't select more than one document.", "@sage/xtrem-intacct-finance/pages_intacct_transaction_feed_query_create_rule": "Matching rule saved.", "@sage/xtrem-intacct-finance/pages_integration-error": "Integration error", "@sage/xtrem-intacct-finance/pages-confirm-cancel": "Cancel", "@sage/xtrem-intacct-finance/pages-confirm-delete": "Delete", "@sage/xtrem-intacct-finance/permission__get_available_intacct_object_list__name": "Get available Sage Intacct object list", "@sage/xtrem-intacct-finance/permission__get_properties_list__name": "Get properties list", "@sage/xtrem-intacct-finance/permission__manage__name": "Manage", "@sage/xtrem-intacct-finance/permission__read__name": "Read", "@sage/xtrem-intacct-finance/permission__retry_finance_document__name": "Retry finance document", "@sage/xtrem-intacct-finance/permission__synchronization_with_sage_intacct__name": "Synchronization with Sage Intacct", "@sage/xtrem-intacct-finance/permission__update_xtrem__name": "Update xtrem", "@sage/xtrem-intacct-finance/reset": "Reset", "@sage/xtrem-intacct-finance/search": "Search", "@sage/xtrem-intacct-finance/service_options__intacct_cashbook_management__name": "Sage Intacct Cashbook Management", "@sage/xtrem-intacct-finance/status_updated": "Status updated.", "@sage/xtrem-intacct-finance/synchronization_already_in_progress": "Synchronization is already in progress", "@sage/xtrem-intacct-finance/target_document_not_found": "{{targetDocumentSysId}} {{targetDocumentType}}: Target document type not found.", "@sage/xtrem-intacct-finance/target_document_type_not_supported": "{{targetDocumentType}}: Target document type not supported.", "@sage/xtrem-intacct-finance/too_many_documents_found_on_intacct": "{{targetDocumentType}} {{documentNumber}}: Number of documents found on Sage Intacct is {{numberOfDocumentsFoundOnIntacct}}. The document cannot be updated.", "@sage/xtrem-intacct-finance/transaction-feed-query-provide-bank-account": "Enter the bank account number.", "@sage/xtrem-intacct-finance/transaction-feed-query-provide-dates": "Enter values for the from date and the to date.", "@sage/xtrem-intacct-finance/update-account-tax-management-context": "You are about to set the account management to 'Excluding tax'.", "@sage/xtrem-intacct-finance/update-account-tax-management-title": "Confirm account tax management update.", "@sage/xtrem-intacct-finance/update-xtreem-return": "Records created: {{created}}. \n Records updated: {{updated}}. \n {{message}}", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____callToActions__seeAll__title": "See all", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____dataDropdownMenu__orderBy__number__title": "Sort by ID", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____dataDropdownMenu__orderBy__status__title": "Sort by name", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__line2__title": "Status", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__line2Right__title": "Sage Intacct ID", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__title__title": "ID", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__titleRight__title": "Name", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____title": "Customer integration status", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____callToActions__seeAll__title": "See all", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____dataDropdownMenu__orderBy__number__title": "Sort by ID", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____dataDropdownMenu__orderBy__status__title": "Sort by name", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__line2__title": "Status", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__line2Right__title": "Sage Intacct ID", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__title__title": "ID", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__titleRight__title": "Name", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____title": "Supplier integration status"}