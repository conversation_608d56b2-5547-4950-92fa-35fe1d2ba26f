{"@sage/xtrem-intacct-finance/action-queued": "Die Aktion wurde in die Warteschlange eingestellt.", "@sage/xtrem-intacct-finance/activity__finance_listener__name": "Finance Listener", "@sage/xtrem-intacct-finance/activity__intacct_bank_account_transaction_feed__name": "Transaktionsfeed Bankkonto Sage Intacct", "@sage/xtrem-intacct-finance/activity__intacct_cash_book_management__name": "Cashbook Management Sage Intacct", "@sage/xtrem-intacct-finance/activity__intacct_map__name": "Zuordnung Sage Intacct", "@sage/xtrem-intacct-finance/already-queued": "Die Aktion läuft bereits.", "@sage/xtrem-intacct-finance/bank-account-account-dont-exist": "{{bankName}}: <PERSON> {{accountName}} ist in Sage DMO nicht vorhanden.", "@sage/xtrem-intacct-finance/bank-account-financial-site-dont-exist": "{{bankName}}: Der Buchhaltungsstandort {{financialSite}} ist in Sage DMO nicht vorhanden.", "@sage/xtrem-intacct-finance/cancel": "Abbrechen", "@sage/xtrem-intacct-finance/cannot_set_tax_with_account_not_subjected_to_taxes": "Sie können ein Steuerdetail nicht mit einem Konto setzen, das nicht Steuern unterliegt.", "@sage/xtrem-intacct-finance/classes__structure-hooks__intacct_is_active": "Die Sage Intacct-Integration ist aktiv.", "@sage/xtrem-intacct-finance/classes__structure-hooks__intacct_is_not_active": "Die Sage Intacct-Integration ist nicht aktiv.", "@sage/xtrem-intacct-finance/classes__structure-hooks__payment_tracking_is_active": "Die Aktivierung von Intacct ist nicht möglich, wenn die Zahlungsverfolgung aktiv ist.", "@sage/xtrem-intacct-finance/company_on_hold_complete": "Synchronisierung Unternehmen gesperrt durchgeführt", "@sage/xtrem-intacct-finance/company_on_hold_start": "Start Synchronisierung Unternehmen gesperrt", "@sage/xtrem-intacct-finance/company_on_hold_stop": "<PERSON>halt<PERSON> angefordert am {{stopDate}} ", "@sage/xtrem-intacct-finance/company_on_hold_success": "Unternehmen {{companyForUpdateId}} erfolgreich aktualisiert", "@sage/xtrem-intacct-finance/confirm-creation": "Erstellung bestätigen", "@sage/xtrem-intacct-finance/confirm-reset": "<PERSON><PERSON> sind dabei, die Datensatzzuordnung auf den ursprünglichen Status zurückzusetzen.", "@sage/xtrem-intacct-finance/confirm-reset-dialog-title": "Zurücksetzen bestätigen", "@sage/xtrem-intacct-finance/confirm-update": "Aktualisierung bestätigen", "@sage/xtrem-intacct-finance/create-ar-advance-context": "<PERSON><PERSON> sind da<PERSON>, eine Zahlung über {{amount}}{{currencySymbol}} an {{customer}} zu erstellen.", "@sage/xtrem-intacct-finance/create-ar-advance-title": "Kundenanzahlung erstellen", "@sage/xtrem-intacct-finance/create-ar-payment-context": "<PERSON><PERSON> sind da<PERSON>, eine Zahlung über {{amount}}{{currencySymbol}} an {{customer}} zu erstellen.", "@sage/xtrem-intacct-finance/create-ar-payment-title": "Zahlung Forderungen erstellen", "@sage/xtrem-intacct-finance/data_types__feed_line_matching_status_enum__name": "Enum Status Zuordnung Zeile Feed", "@sage/xtrem-intacct-finance/data_types__feed_record_status_enum__name": "Enum Status Datensatz Feed", "@sage/xtrem-intacct-finance/data_types__intacct_factor_data_type__name": "Datentyp Factor Intacct", "@sage/xtrem-intacct-finance/data_types__intacct_id_property_data_type__name": "Datentyp Eigenschaft ID Intacct", "@sage/xtrem-intacct-finance/data_types__intacct_matching_status_enum__name": "Enum Status Zuordnung Intacct", "@sage/xtrem-intacct-finance/data_types__intacct_matching_type_enum__name": "Enum Typ Zuordnung Intacct", "@sage/xtrem-intacct-finance/data_types__intacct_record_transaction_type_enum__name": "Enum Transaktionstyp Datensatz Intacct", "@sage/xtrem-intacct-finance/data_types__intacct_url__name": "URL Intacct", "@sage/xtrem-intacct-finance/data_types__payment_term_discount_or_penalty_type_enum__name": "Payment term discount or penalty type enum", "@sage/xtrem-intacct-finance/description": "Bezeichnung", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__draftMatch": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__lookingForMatches": "Suche nach Zuordnungen", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__matched": "Zugeordnet", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__matchFound": "Zuordnung gefunden", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__multipleMatches": "Mehrfache Zuordnungen", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__unmatched": "<PERSON>cht zu<PERSON>", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvanceGenerated": "Kundenanzahlung generiert", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvancePosted": "Kundenanzahlung gebucht", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvancePostingError": "Buchungsfehler Kundenanzahlung", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvancePostingInProgress": "Buchung Kundenanzahlung in Bearbeitung", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentGenerated": "Zahlung Ausgangsrechnung generiert", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentGenerationInProgress": "Generierung Zahlung Ausgangsrechnung in Bearbeitung", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentPosted": "Zahlung Ausgangsrechnung gebucht", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentPostingError": "Buchungsfehler Zahlung Ausgangsrechnung", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentPostingInProgress": "Buchung Zahlung Ausgangsrechnung in Bearbeitung", "@sage/xtrem-intacct-finance/enums__feed_record_status__draftArAdvance": "Entwurf Kundenanzahlung", "@sage/xtrem-intacct-finance/enums__feed_record_status__draftArMatch": "Entwurf Z<PERSON>rdnung Forderungen", "@sage/xtrem-intacct-finance/enums__feed_record_status__draftMatch": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntriesFound": "Buchungen gefunden", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryFound": "Buchung gefunden", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryGenerated": "Buchung generiert", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryGenerationError": "Generierungsfehler Buchung", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryGenerationInProgress": "Generierung Buchung in Bearbeitung", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryPosted": "Buchung gebucht", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryPostingError": "Buchungsfehler Buchung", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryPostingInProgress": "Buchung in Bearbeitung", "@sage/xtrem-intacct-finance/enums__feed_record_status__lookingForExistingJournalEntries": "Suche nach bestehenden Buchungen", "@sage/xtrem-intacct-finance/enums__feed_record_status__matched": "Zugeordnet", "@sage/xtrem-intacct-finance/enums__feed_record_status__partialArMatch": "Teilweise Zuordnung Forderungen", "@sage/xtrem-intacct-finance/enums__feed_record_status__readyForArAdvanceGeneration": "Bereit für die Generierung der Kundenanzahlung", "@sage/xtrem-intacct-finance/enums__feed_record_status__readyForArPaymentGeneration": "Bereit für die Generierung der Vorauszahlung Forderungen", "@sage/xtrem-intacct-finance/enums__feed_record_status__readyForPosting": "Bereit zur Buchung", "@sage/xtrem-intacct-finance/enums__feed_record_status__unmatched": "<PERSON>cht zu<PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__cleared": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__draftMatched": "Entwurf zugeordnet", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__ignored": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__matched": "Zugeordnet", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__partiallyMatched": "Teilweise zugeordnet", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__selectToMatch": "Auswahl <PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__selectToUnmatch": "Auswahl keine <PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__unmatched": "<PERSON>cht zu<PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_type__contains": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_type__equals": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_type__regularExpression": "Regulärer Ausdruck", "@sage/xtrem-intacct-finance/enums__intacct_record_transaction_type__deposit": "Einzahlung", "@sage/xtrem-intacct-finance/enums__intacct_record_transaction_type__withdrawal": "Auszahlung", "@sage/xtrem-intacct-finance/enums__payment_term_discount_or_penalty_type__amount": "Betrag", "@sage/xtrem-intacct-finance/enums__payment_term_discount_or_penalty_type__percentage": "Prozentsatz", "@sage/xtrem-intacct-finance/error": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/functions__map/xtreem-mass-creation-tax-category-update-with-error": "Nach der Integration müssen Sie die Steuerkategorie aktualisieren.", "@sage/xtrem-intacct-finance/id": "ID", "@sage/xtrem-intacct-finance/intacct_bank_account_transaction_feed_has_no_lines_to_post": "Der Sage Intacct-Bankkontotransaktionsfeed {{intacctbankAccountTransactionFeed}} enthält keine zu buchenden Zeilen.", "@sage/xtrem-intacct-finance/intacct_document_not_found": "{{sysId}} {{type}}: Dokument Sage Intacct nicht gefunden.", "@sage/xtrem-intacct-finance/intacct-address-bill-to": "Re<PERSON>nungsadress<PERSON>", "@sage/xtrem-intacct-finance/intacct-address-pay-to": "<PERSON><PERSON><PERSON> Zahlungsempfänger", "@sage/xtrem-intacct-finance/intacct-address-primary": "Hauptadresse", "@sage/xtrem-intacct-finance/intacct-address-primary-be": "Hauptgeschäftsentitätenadresse", "@sage/xtrem-intacct-finance/intacct-address-return-to": "Retouradresse", "@sage/xtrem-intacct-finance/intacct-address-ship-to": "Lieferadresse", "@sage/xtrem-intacct-finance/intacct-address-ship-to-primary": "Hauptlieferadresse", "@sage/xtrem-intacct-finance/intacct-addresse-others": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/item_extension_item_id_frozen": "Sie können die Artikel-ID nicht ändern, wenn sie mit Sage Intacct integriert war.", "@sage/xtrem-intacct-finance/mapping-private-show": "Sie können 'Private Daten e<PERSON>ch<PERSON>n' nur aktivieren, wenn das Feld 'ID Entität MEGA' vorhanden und mit Sage DMO verknüpft ist.", "@sage/xtrem-intacct-finance/menu_item__cashbook-manager": "Bank Manager", "@sage/xtrem-intacct-finance/name": "Name", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredClass": "Erforderliche Klasse", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredCustomer": "Erforderlicher Kunde", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredDepartement": "Erforderliche Abteilung", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredEmploye": "Erforderlicher Mitarbeiter", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredItem": "Erforderlicher Artikel", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredLocation": "Erforderlicher Lagerplatz", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredProject": "Erforderliches Projekt", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredSupplier": "Erforderlicher Lieferant", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredTask": "Ist erforderliche Aufgabe", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__statusIntacct": "Status Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__uIntacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__billToPayToContactName": "Kontaktname Rechnungsempfänger/Zahlungsempfänger", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctDocument": "Dokument Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctIntegrationState": "Status Integration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctLastIntegrationDate": "Letztes Integrationsdatum Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctUrl": "URL Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__shipToReturnToContactName": "Kontaktname Leistungsempfänger Retourempfänger", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__taxSolutionId": "Steuerlösungs-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__classId": "Klassen-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__customerId": "Kunden-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__departmentId": "Abteilungs-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__inclusiveTax": "Inkl. Steuern", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__intacctDocumentLine": "Dokumentzeile Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__itemId": "Artikel-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__lineAmountExcludingTaxSigned": "Zeilenbetrag exkl. Steuern unterzeichnet", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__memo": "Memo", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__projectId": "Projekt-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__taxEntries": "Steuereinträge", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__vendorId": "Verkäufer-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__mutation__createArAdvance": "Kundenanzahlung erstellen", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__mutation__createArAdvance__failed": "Anzahlung Ausgangsrechnung erstellen fehlgeschlagen.", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__mutation__createArAdvance__parameter__data": "Daten", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__bankFeed": "Bankfeed", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__entityId": "Entitäts-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctDocument": "Abfrage Dokument Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctIntegrationState": "Status Integration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctLastIntegrationDate": "Letztes Integrationsdatum Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctUrl": "URL Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__paymentMethod": "Zahlungsart", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__classId": "Klassen-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__customerId": "Kunden-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__departmentId": "Abteilungs-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__intacctDocumentLine": "Dokumentzeile Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__itemId": "Artikel-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__projectId": "Projekt-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__vendorId": "Verkäufer-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__mutation__updateOpenItemFromIntacct": "Offenen Posten aus Sage Intacct aktualisieren", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__mutation__updateOpenItemFromIntacct__failed": "Offenen Posten aus Sage Intacct aktualisieren fehlgeschlagen.", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__mutation__updateOpenItemFromIntacct__parameter__arInvoiceSysId": "System-ID Ausgangsrechnung", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctDocument": "Abfrage Dokument Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctIntegrationState": "Status Integration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctLastIntegrationDate": "Letztes Integrationsdatum Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctUrl": "URL Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__taxSolutionId": "Steuerlösungs-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__classId": "Klassen-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__customerId": "Kunden-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__departmentId": "Abteilungs-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__intacctDocumentLine": "Abfrage Dokumentzeile Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__itemId": "Artikel-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__lineAmountExcludingTaxSigned": "Zeilenbetrag exkl. Steuern unterzeichnet", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__memo": "Memo", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__projectId": "Projekt-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__taxEntries": "Steuereinträge", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__vendorId": "Verkäufer-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__mutation__createArPayment": "Zahlung Forderungen erstellen", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__mutation__createArPayment__failed": "Zahlung Ausgangsrechnung erstellen fehlgeschlagen.", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__mutation__createArPayment__parameter__data": "Daten", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__bankFeed": "Bankfeed", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__entityId": "Entitäts-ID", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctDocument": "Abfrage Dokument Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctIntegrationState": "Status Integration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctLastIntegrationDate": "Letztes Integrationsdatum Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctUrl": "URL Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__paymentMethod": "Zahlungsart", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__arInvoiceRecordNo": "Datensatznummer Ausgangsrechnung", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__intacctDocumentLine": "Abfrage Dokumentzeile Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__accounts-payable-invoice-extension__accounts_payable_invoice_already_sent": "Die Eingangsrechnung wurde bereits an Sage Intacct gesendet. Status: {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-advance-extension__accounts_receivable_advance_already_sent": "Die Kundenanzahlung wurde an Sage Intacct gesendet. Status: {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-invoice-extension__accounts_receivable_invoice_already_sent": "Die Ausgangsrechnung wurde bereits an Sage Intacct gesendet. Status: {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-payment-extension__accounts_receivable_payment_already_sent": "Die Zahlung zur Ausgangsrechnung wurde an Sage Intacct gesendet. Status: {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__bulkMutation__updateRecordNoOnEmployee": "Datensatznummer Mitarbeiter aktualisieren", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__bulkMutation__updateRecordNoOnEmployee__failed": "Datensatznummer Mitarbeiter aktualisieren fehlgeschlagen.", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__intacctProject": "Projekt Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__statusIntacct": "Status Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__uIntacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__intacctGlAccount": "Hauptbuchkonto Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__location": "Lagerplatz", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__megaEntityId": "ID Entität MEGA", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__statusIntacct": "Status Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__business_entity_address_extension__property__intacctBusinessEntityAddress": "Geschäftsentitätenadresse Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__business_entity_address_extension__property__name": "Name", "@sage/xtrem-intacct-finance/node-extensions__business_entity_contact_extension__property__intacctPrintAs": "Sage Intacct drucken als", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold": "Synchronisierung Unternehmen gesperrt", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold__failed": "Synchronisierung Unternehmen gesperrt fehlgeschlagen.", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold__parameter__companySysId": "System-ID Unternehmen", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold__parameter__isAllCompanies": "Ist alle Unternehmen", "@sage/xtrem-intacct-finance/node-extensions__company_extension__property__doApPosting": "Buchung Eingangsrechnung", "@sage/xtrem-intacct-finance/node-extensions__company_extension__property__doArPosting": "Buchung Ausgangsrechnung", "@sage/xtrem-intacct-finance/node-extensions__company_extension__property__doUpdateArAmountPaid": "Bezahlten Betrag Forderungen aktualisieren", "@sage/xtrem-intacct-finance/node-extensions__customer_extension__property__intacctCustomer": "Kunde Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__customer_extension__property__primaryContactIntacctId": "Hauptkontakt Sage Intacct-ID", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__intacctObject": "Objekt Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__statusIntacct": "Status Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__uIntacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_type_extension__property__intacctObject": "Objekt Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__mutation__generateSmartEventFile": "Smart Event-<PERSON><PERSON>", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__mutation__generateSmartEventFile__failed": "Smart Event-<PERSON><PERSON> generieren fehlgeschlagen.", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__property__customPackageFile": "Benutzerdefinierte Paketdatei", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__property__generateSmartEvent": "Generierung Smart Event", "@sage/xtrem-intacct-finance/node-extensions__item_extension__property__id": "ID", "@sage/xtrem-intacct-finance/node-extensions__item_extension__property__intacctItem": "Artikel Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__entityId": "Entitäts-ID", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctDocument": "Abfrage Dokument Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctIntegrationState": "Status Integration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctLastIntegrationDate": "Letztes Integrationsdatum Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctUrl": "URL Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__isActive": "Aktiv", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__taxImplications": "Auswirkungen Steuer", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__taxSolutionId": "Steuerlösung", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_dimension_extension__property__intacctDimension": "Sektor Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__absoluteTransactionAmount": "Absoluter Transaktionsbetrag", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__allocationSplit": "Aufteilung Reservierung", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__excludeRecord": "Datensatz ausschließen", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctDimensionSplit": "Aufteilung Sektor Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctDocumentLine": "Abfrage Dokumentzeile Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctSign": "Vorzeichen Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__isActive": "Aktiv", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__locationWhenNoSplit": "Lagerplatz, wenn keine Aufteilung", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__taxEntries": "Steuereinträge", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__statusIntacct": "Status Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__uIntacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal-entry-extension__journal_entry_already_sent": "Die Buchung wurde bereits an Sage Intacct gesendet. Status: {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountDate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountFrom": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountFromIntacct": "<PERSON><PERSON><PERSON> von Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountType": "Rabattart", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountTypeIntacct": "Skontoart Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__dueFromIntacct": "<PERSON><PERSON><PERSON><PERSON> von Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__isIntacct": "Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__penaltyAmount": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__penaltyType": "Ma<PERSON>typ", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__penaltyTypeIntacct": "Mahntyp Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__statusIntacct": "Status Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__uIntacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__supplier_extension__property__intacctSupplier": "Lieferant Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__supplier_extension__property__paymentMethodSelect": "Auswahl Zahlungsmethode", "@sage/xtrem-intacct-finance/node-extensions__supplier_extension__property__payToAddressIntacctId": "Sage Intacct-ID Adresse Zahlungsempfänger", "@sage/xtrem-intacct-finance/node-extensions__supplier_extension__property__payToAddressRecordNo": "Datensatznummer Adresse Zahlungsempfänger", "@sage/xtrem-intacct-finance/node-extensions__supplier_extension__property__primaryContactIntacctId": "Hauptkontakt Sage Intacct-ID", "@sage/xtrem-intacct-finance/node-extensions__supplier_extension__property__primaryContactRecordNo": "Datensatznummer Hauptkontakt", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctAccount": "Konto Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctSecondaryExternalReference": "Sekundäre externe Referenz Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctSolutionId": "ID Sage Intacct-Lösung", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctTaxType": "Steuertyp Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__isIntacct": "Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__isIntacctReverseCharge": "Reverse Charge Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__isUpdateFromIntacct": "Aktualisierung von Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__rate": "Satz", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__statusIntacct": "Status Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__uIntacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/nodes__company_customer_on_hold_control": "<PERSON>e können einen gesperrten Kunden nicht aktualisieren, wenn Sage Intacct aktiv ist.", "@sage/xtrem-intacct-finance/nodes__company_get_company_ref_not_mapped": "Der Rückgabewert aus Sage Intacct {{intacctCompanyRef}} kann nicht zugeordnet werden.", "@sage/xtrem-intacct-finance/nodes__customer_credit_limit_control": "<PERSON><PERSON> können das Kreditlimit nicht aktualisieren, wenn Sage Intacct aktiv ist.", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument": "Finanzdokument wiederholen", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument__failed": "Finanzdokument wiederholen fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument__parameter__financeDocumentSysId": "System-ID Finanzdokument", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument__parameter__financeTransaction": "Finanztransaktion", "@sage/xtrem-intacct-finance/nodes__finance_listener__node_name": "Finance Listener", "@sage/xtrem-intacct-finance/nodes__finance-listener__already_success": "{{number}}: Das Finanzdokument wurde bereits synchronisiert.", "@sage/xtrem-intacct-finance/nodes__finance-listener__document_found_on_intacct_updated": "{{NUMBER}}: <PERSON> Dokument wurde in Sage Intacct gefunden. Status aktualisiert.", "@sage/xtrem-intacct-finance/nodes__finance-listener__document_not_found_on_intacct_posted_waiting": "Das Dokument wurde nicht in Sage Intacct gefunden. Das Dokument wurde gebucht und wartet auf Antwort von Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__finance-listener__retry": "Wird verarbei<PERSON>t.", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__node_name": "Eingangsrechnung Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__billBySupplier": "Rechnungssteller", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__billToPayToContactName": "Kontaktname Rechnungsempfänger / Zahlungsempfänger", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__document": "Dokument", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__entityId": "Entitäts-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__shipToReturnToContactName": "Kontaktname Leistungsempfänger / Retourempfänger", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__taxSolutionId": "Steuerlösungs-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__node_name": "Eingangsrechnungszeile Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__classId": "Klassen-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__customerId": "Kunden-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__departmentId": "Abteilungs-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__documentLine": "Dokumentzeile", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__employeeId": "Mitarbeiter-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__inclusiveTax": "Inklusive Steuern", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__itemId": "Artikel-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__memo": "Memo", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__projectId": "Projekt-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__signedAmountExcludingTax": "Vorzeichen Betrag exkl. Steuern", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__taskId": "Aufgaben-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__taxEntries": "Steuereinträge", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__vendorId": "Verkäufer-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__node_name": "Anzahlung zur Ausgangsrechnung Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__bankFeed": "Bankfeed", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__document": "Dokument", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__entityId": "Entitäts-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__paymentMethod": "Zahlungsart", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__node_name": "Anzahlung zur Ausgangsrechnungszeile Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__classId": "Klassen-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__customerId": "Kunden-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__departmentId": "Abteilungs-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__documentLine": "Dokumentzeile", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__itemId": "Artikel-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__projectId": "Projekt-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__vendorId": "Verkäufer-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__node_name": "Ausgangsrechnung Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__billToCustomer": "Rechnungsempfänger", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__document": "Dokument", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__entityId": "Entitäts-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__taxSolutionId": "Steuerlösungs-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__node_name": "Ausgangsrechnungszeile Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__classId": "Klassen-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__customerId": "Kunden-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__departmentId": "Abteilungs-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__documentLine": "Dokumentzeile", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__employeeId": "Mitarbeiter-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__inclusiveTax": "Inklusive Steuern", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__itemId": "Artikel-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__memo": "Memo", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__projectId": "Projekt-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__signedAmountExcludingTax": "Vorzeichen Betrag exkl. Steuern", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__taskId": "Aufgaben-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__taxEntries": "Steuereinträge", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__vendorId": "Verkäufer-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__node_name": "Zahlung zur Ausgangsrechnung Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__bankFeed": "Bankfeed", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__document": "Dokument", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__entityId": "Entitäts-ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__paymentMethod": "Zahlungsart", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__node_name": "Zahlung zur Ausgangsrechnungszeile Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__property__arInvoiceRecordNo": "Datensatznummer Ausgangsrechnung", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__property__documentLine": "Dokumentzeile", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__mutation__bulkSave": "Massenspeichern", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__mutation__bulkSave__failed": "Massenspeichern fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__mutation__bulkSave__parameter__intacctBankAccountMatchings": "Zuordnungen Sage Intacct-Bankkonto", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__node_name": "Zuordnung Sage Intacct-Bankkonto", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__account": "Ko<PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__analyticalData": "Analytische Daten", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__bankAccount": "Bankkonto", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__keyword": "Schlüsselwort", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__location": "Lagerplatz", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__priority": "Priorität", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__tax": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__transactionType": "Transaktionsart", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__type": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__updateAccountTaxManagement": "Aktualisierung Kontosteuerverwaltung", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctArInvoice": "Abfrage Ausgangsrechnung Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctArInvoice__failed": "Abfrage Ausgangsrechnung Sage Intacct fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctArInvoice__parameter__parameters": "Parameter", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctCustomers": "Abfrage Kunden Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctCustomers__failed": "Abfrage Kunden Sage Intacct fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument": "Abfrage Dokument Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument__failed": "Abfrage Dokument Sage Intacct fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument__parameter__documentType": "Dokumenttyp", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument__parameter__parameters": "Parameter", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__wrong_account_tax_management": "Sie können nur ein mit einer Steuerverwaltung verknüpftes Konto auswählen, das den Wert 'Sonstiges' oder 'Exkl. Steuern' hat.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared__failed": "<PERSON><PERSON><PERSON>zen fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared__parameter__cleared": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared__parameter__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__node_name": "Transaktionsfeed Bankkonto Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__account": "Ko<PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__accountFeedKey": "Konto-Feed-Schlüssel", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__accountReconKey": "Abstimmungsschlüssel Bankkonto", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__accountType": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__amount": "Betrag", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__amountToMatch": "Zuzuordnender Betrag", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__analyticalData": "Analytische Daten", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__arMatch": "Zuordnung Ausgangsrechnungen", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__bankAccount": "Bankkonto", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__cleared": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__computedAttributes": "Berechnete Attribute", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__currency": "Währung", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__description": "Bezeichnung", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__documentNumber": "Dokumentnummer", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__documentType": "Dokumenttyp", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__entity": "Entität", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__entityName": "Entitätsname", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__feedType": "Feedtyp", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentCreatedNumber": "Nummer Finanzdokument erstellt", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentCreatedSysId": "System-ID Finanzdokument erstellt", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentGenerationErrorMessage": "Fehlermeldung Generierung Finanzdokument", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentType": "Typ Finanzdokument", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeIntegrationApp": "Anwendung Integration Finanzen", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeIntegrationAppRecordId": "Datensatz-ID Anwendung Integration Finanzen", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeIntegrationAppUrl": "URL Anwendung Integration Finanzen", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__importSession": "Sitzung importieren", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__intacctCleared": "Sage Intacct geleert", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__internalFinanceIntegrationStatus": "Interner Finanzintegrationsstatus", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__journalEntryNumber": "Buchungsnummer", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__jsonArInvoices": "Ausgangsrechnungen JSON", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__payee": "Zahlungsempfänger", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__paymentDate": "Zahlungsdatum", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__paymentMethod": "Zahlungsart", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__payToCustomerId": "ID Zahlungsempfänger", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__payToCustomerName": "Name Zahlungsempfänger", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__postingDate": "Buchungsdatum", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__receiptDate": "Eingangsdatum", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__reconcilitationDate": "Abstimmungsdatum", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__targetDocumentType": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__transactionId": "Transaktions-ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__transactionType": "Transaktionsart", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine": "Zuordnungszeile abrufen", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine__failed": "Zuordnungszeile abrufen fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine__parameter__matchingRules": "Zuordnungsregeln", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine__parameter__transactionFeed": "Transaktionsfeed", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__node_name": "Zeile Transaktionsfeed Bankkonto Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__account": "Ko<PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__amount": "Betrag", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__analyticalData": "Analytische Daten", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__computedAttributes": "Berechnete Attribute", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__document": "Dokument", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__intacctJournalEntryBatchNo": "Buchungsstapelnummer Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__intacctJournalEntryRecordNo": "Datensatznummer Buchung Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__intacctJournalEntryUrl": "URL Buchung Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__location": "Lagerplatz", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__status": "Status", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__tax": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__taxAmount": "Steuerbetrag", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__taxRate": "Steuersatz", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__node_name": "Sektor Zeile Transaktionsfeed Bankkonto Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__property__originLine": "Ursprüngliche Zeile", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__mutation__serviceOptionChange": "Änderung Dienstoption", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__mutation__serviceOptionChange__failed": "Änderung Dienstoption fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__node_name": "Cashbook Management Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__query__isServiceOptionActiveFunction": "Funktion Dienstoption aktiv", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__query__isServiceOptionActiveFunction__failed": "Ist Funktion Dienstoption aktiv fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_contact__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_contact__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_contact__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_contact__node_name": "Kontakt Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__contact": "Kontakt", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__name": "Name", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__prefix": "Präfix", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__printAs": "<PERSON><PERSON><PERSON> als", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__status": "Status", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__sysIdLink": "Verknüpfung System-ID", "@sage/xtrem-intacct-finance/nodes__intacct_customer__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_customer__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_customer__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_customer__node_name": "Kunde Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__billToAddress": "Re<PERSON>nungsadress<PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__parent": "Übergeordnet", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__shipToAddress": "Lieferadresse", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__status": "Status", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__node_name": "Kunde Lieferant Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__contactList": "Kontaktliste", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__hideDisplayContact": "Kontakt einblenden/ausblenden", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__primaryContact": "Hauptkontakt", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__status": "Status", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__sysIdLink": "Verknüpfung System-ID", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__currencies_do_not_match": "Die Bankwährung muss die gleiche wie die Währung des Buchhaltungsstandorts zum Bankkonto sein.", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__node_name": "Importsitzung Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__bankAccount": "Bankkonto", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__dateCreation": "Erstellungsdatum", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__description": "Bezeichnung", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__linesImported": "Importierte Zeilen", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__mapObject": "Zuordnung Objekt", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__numberOfLinesToImport": "<PERSON><PERSON><PERSON> zu importierende Zeilen", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__queryParameters": "Abfrageparameter", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__status": "Status", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__transactionFeed": "Transaktionsfeed", "@sage/xtrem-intacct-finance/nodes__intacct_item__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_item__node_name": "Artikel Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_item__property__item": "Artikel", "@sage/xtrem-intacct-finance/nodes__intacct_item__property__status": "Status", "@sage/xtrem-intacct-finance/nodes__intacct_item__property__type": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__node_name": "Buchung Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__document": "Dokument", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__entityId": "Entitäts-ID", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__taxImplications": "Auswirkungen Steuer", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__taxSolutionId": "Steuerlösungs-ID", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__node_name": "Buchungszeile Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__absoluteTransactionAmount": "Absoluter Transaktionsbetrag", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__allocationSplit": "Aufteilung Reservierung", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__dimensionSplit": "Aufteilung Sektor", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__documentLine": "Dokumentzeile", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__excludeRecord": "Datensatz ausschließen", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__locationWhenNoSplit": "Lagerplatz, wenn keine Aufteilung", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__sign": "Vorzeichen", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__taxEntries": "Steuereinträge", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct": "Sage Intacct löschen", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct__failed": "Sage Intacct löschen fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct__parameter__intacctNode": "Node Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct__parameter__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__synchronizeNode": "Node synchronisieren", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__synchronizeNode__failed": "Node synchronisieren fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__synchronizeNode__parameter__intacctNode": "Node Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_listener__node_name": "Listener <PERSON>ta<PERSON>t", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob": "Massenerstellungsjob Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob__failed": "Massenerstellungsjob Sage Intacct fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob__parameter__intacctName": "Name Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob__parameter__type": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__xtreemMassCreationJob": "Massenerstellungsjob Xtreem", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__xtreemMassCreationJob__failed": "Massenerstellungsjob Xtreem fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__xtreemMassCreationJob__parameter__data": "Daten", "@sage/xtrem-intacct-finance/nodes__intacct_map__bulkMutation__updateCustomMapping": "Benutzerdefinierte Zuordnung aktualisieren", "@sage/xtrem-intacct-finance/nodes__intacct_map__bulkMutation__updateCustomMapping__failed": "Benutzerdefinierte Zuordnung aktualisieren fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__createUpdateAllIntacct": "Alle Sage Intacct erstellen/aktualisieren", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__createUpdateAllIntacct__failed": "Alle Sage Intacct erstellen/aktualisieren fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__createUpdateAllIntacct__parameter__intacctName": "Name Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem": "Xtrem löschen", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__failed": "Xtrem löschen fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__parameter__intacctIdValue": "Wert Sage Intacct-ID", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__parameter__intacctName": "Name Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__parameter__xtremSysId": "System-ID Xtrem", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl": "URL Sage Intacct abrufen", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl__failed": "URL Sage Intacct abrufen fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl__parameter___id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl__parameter__nodeName": "Node-Name", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getStructure": "Struktur abrufen", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getStructure__failed": "Struktur abrufen fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getStructure__parameter__content": "Inhalt", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__writeStructure": "Struk<PERSON> schreiben", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__writeStructure__failed": "Struktur schreiben fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__writeStructure__parameter__object": "Objekt", "@sage/xtrem-intacct-finance/nodes__intacct_map__node_name": "Zuordnung Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__additionnalLink": "Zusätzlicher Link", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__customRelationMapping": "Benutzerdefinierte Zuordnung Beziehung", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__editableFields": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__intacctDescription": "Bezeichnung Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__intacctFilter": "Fi<PERSON> Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__isPrivateShow": "Private Daten anzeigen", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__isSmartEvent": "Smart Event", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__relationMapping": "<PERSON><PERSON>rd<PERSON>ng Beziehung", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__setupId": "ID Einstellungen", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__specificFields": "Spezifische Felder", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__uRelationMapping": "<PERSON><PERSON>rd<PERSON>ng Beziehung", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__xtremObject": "Objekt Xtrem", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__compareObject": "Objekt vergleichen", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__compareObject__parameter__node": "Node", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__compareObject__parameter__sysId": "System-ID", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getAvailableXtremObjectList": "Verfügbare Xtrem-Objektliste abrufen", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getAvailableXtremObjectList__failed": "Verfügbare Xtrem-Objektliste abrufen fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct": "Sage Intacct-<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__failed": "Sage Intacct-Daten abrufen fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__filters": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__maxData": "Daten Maximum", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__name": "Name", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__transaction": "Transaktion", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getIntacctTransactionsList": "Sage Intacct-Transaktionsliste abrufen", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getIntacctTransactionsList__failed": "Sage Intacct-Transaktionsliste abrufen fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject": "Objekt abrufen", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__failed": "Objekt abrufen fehlgeschlagen.", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__parameter__docparid": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__parameter__isUpdate": "Aktualisieren", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__parameter__object": "Objekt", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getPropertiesList": "Eigenschaftenliste abrufen", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getPropertiesList__parameter__tableName": "Tabellenname", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__node_name": "Lieferant Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__parent": "Übergeordnet", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__payToAddress": "<PERSON><PERSON><PERSON> Zahlungsempfänger", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__returnToAddress": "Retouradresse", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__status": "Status", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_finance_state__node_name": "Status Synchronisierung Finanzen Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_finance_state__property__externalIntegration": "Externe Integration", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_state__node_name": "Status Synchronisierung Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_state__property__entityId": "Entitäts-ID", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__site_legislations_and_coa_legislation_dont_match": "Die Rechtsordnung des Standorts muss die gleiche wie die Rechtsordnung des Kontenrahmens sein.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_legislation_and_coa_legislation_dont_match": "Die Rechtsordnung der Steuer muss die gleiche wie die Rechtsordnung des Kontenrahmens sein.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_management_should_be_excluding_taxes": "Sie können ein Steuerdetail nicht mit einem Konto setzen, das nicht Steuern unterliegt.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_mandatory": "Die Steuer ist erforderlich.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_not_allowed": "<PERSON> Steuer muss leer sein.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction_feed__site_legislation_and_coa_legislation_dont_match": "Die Rechtsordnung des Standorts muss die gleiche wie die Rechtsordnung des Kontenrahmens sein.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction_feed__tax_legislation_and_coa_legislation_dont_match": "Die Rechtsordnung der Steuer muss die gleiche wie die Rechtsordnung des Kontenrahmens sein.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed__tax_mandatory": "Die Steuer ist erforderlich.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed__tax_not_allowed": "<PERSON> Steuer muss leer sein.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed/bank-manager-import-fail": "Sage DMO Bank Manager : Der Import ist fehlgeschlagen ({{filters}}). \n {{error}}.", "@sage/xtrem-intacct-finance/nodes__intacct-listener/delete-success": "Gelöscht.", "@sage/xtrem-intacct-finance/nodes__intacct-listener/intacct-time-out": "Zeitüberschreitung: Sage Intacct antwortet nicht.", "@sage/xtrem-intacct-finance/nodes__intacct-listener/not-implemented": "<PERSON>cht implementiert: {{change}}:{{object}}  ", "@sage/xtrem-intacct-finance/nodes__intacct-listener/synchronize-node-finish": "Synchronisierung {{state}}.", "@sage/xtrem-intacct-finance/nodes__intacct-listener/synchronize-node-start": "Synchronisierung gestartet.", "@sage/xtrem-intacct-finance/nodes__intacct-map/create-update-xtrem": "<PERSON><PERSON> viel<PERSON> Zeilen von Sage Intacct ausgegeben ({{lenght}}) - intacctId : {{intacctIdValue}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/desynchronized_not_exist": "Nicht synchronisiert : {{node}}: {{name}} Sage Intacct-ID: {{id}} nicht gefunden.", "@sage/xtrem-intacct-finance/nodes__intacct-map/file-already-exist": "Die Datei {{object}} ist bereits vorhanden.", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-instance-query-to-many-lines": "<PERSON><PERSON> viele Zeilen von Sage Intacct ausgegeben ({{countIntacctData}}). \n Fügen Sie Filter hinzu und versuchen Sie es erneut.", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end": "Sammelerstellung von {{intacctName}} bei Ende um {{dateTime}}. \n {{errorCount}} <PERSON><PERSON>, {{successCount}} erfolgreich /{{totalCount}} gesamt ", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end-with-errors-phase": "<PERSON><PERSON> mit Fehlern.", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end-without-errors-phase": "<PERSON><PERSON>.", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-fail": "Sage Intacct: Die Massenerstellung ist fehlgeschlagen ({{intacctName}}).", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-number-of-lines": "{{numberOfLines}} zu erstellen/aktualisieren.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-desynchronization": "Das Objekt ist synchronisiert.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-intacct-data": "<PERSON><PERSON> Intacct-Daten für das Objekt {{intacctName}} mit {{intacctIDField}} gleich {{intacctIdValue}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-intacct-instance": "<PERSON><PERSON> Sage Intacct-Instanz aktiviert.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-map-instance": "Die Datei kann nicht erstellt werden, da {{object}} nicht in Sage Intacct vorhanden ist.", "@sage/xtrem-intacct-finance/nodes__intacct-map/nothing-to-write": "Die Datei {{object}} kann nicht geschrieben werden.", "@sage/xtrem-intacct-finance/nodes__intacct-map/to-many-lines": "Die Abfrage hat viele Zeilen zurückgegeben: {{numberOflines}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-end": "Sammelerstellung von {{intacctName}} bei Ende um {{dateTime}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-number-of-lines": "{{numberOfLines}} zu erstellen/aktualisieren.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-start": "Sammelerstellung von {{intacctName}} bei Beginn um {{dateTime}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-tax-category-update": "\n Nach der Integration müssen Sie die Steuerkategorie aktualisieren.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-created-updated": "Sage DMO {{created}}: {{id}} ", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-created-updated-total": "Datensätze erstellt: {{created}}. /n Datensätze aktualisiert: {{updated}}. /n Datensätze mit Fehlern: {{error}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-query-to-many-lines": "<PERSON><PERSON> viele Zeilen von Sage DMO zurückgegeben ({{numberOfNodes}}).\n Fügen Sie Filter hinzu und versuchen Sie es erneut.", "@sage/xtrem-intacct-finance/nodes__map_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__map_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__map_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__map_line__node_name": "Zuordnungszeile", "@sage/xtrem-intacct-finance/nodes__map_line__property__collectionName": "Name Collection", "@sage/xtrem-intacct-finance/nodes__map_line__property__line": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__map_line__property__mapHeader": "Zuordnung Kopfzeile", "@sage/xtrem-intacct-finance/nodes__map_line__property__propertyHeader": "Kopfzeile Eigenschaft", "@sage/xtrem-intacct-finance/nodes__map_line__property__propertyLine": "Zeile Eigenschaft", "@sage/xtrem-intacct-finance/nodes__map_property__asyncMutation__asyncExport": "Export", "@sage/xtrem-intacct-finance/nodes__map_property__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-intacct-finance/nodes__map_property__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-intacct-finance/nodes__map_property__node_name": "Zuordnung Eigenschaft", "@sage/xtrem-intacct-finance/nodes__map_property__property__map": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__map_property__property__name": "Name", "@sage/xtrem-intacct-finance/nodes__map_property__property__type": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__payment_term_discount_amount_percentage_error": "Der Rabattbetrag muss kleiner als 100% sein.", "@sage/xtrem-intacct-finance/nodes__payment_term_discount_mandatory": "Ra<PERSON><PERSON><PERSON> und Rabattbetrag sind erford<PERSON>lich, wenn ein Rabattdatum erfasst wird.", "@sage/xtrem-intacct-finance/nodes__payment_term_penalty_amount_percentage_error": "Die Mahngebühr sollte kleiner als 100% sein", "@sage/xtrem-intacct-finance/nodes__tax_extension__deletion_forbidden": "<PERSON>e können diesen Steuerdatensatz nicht löschen. Er ist mit Sage Intacct verknüpft.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_not_allowed": "<PERSON>e können diesen Steuerdatensatz nicht erstellen, bearbeiten oder löschen. Er ist mit Sage Intacct verknüpft.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_active_is_forbidden": "<PERSON>e können diesen Steuerdatensatz nicht aktivieren. Er ist mit Sage Intacct verknüpft.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_country_is_forbidden": "<PERSON>e können das Land nicht bearbeiten.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_primary_external_reference_is_forbidden": "Sie können die primäre externe Referenz nicht bearbeiten.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_reverse_charge_is_forbidden": "Sie können die Umkehrung der Steuerschuldnerschaft (Reverse Charge) nicht bearbeiten.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_tax_type_is_forbidden": "<PERSON>e können den Steuertyp nicht bearbeiten. Er ist mit Sage Intacct verknüpft.", "@sage/xtrem-intacct-finance/nodes__tax_extension__negative_rate_not_allowed": "<PERSON>e können keinen negativen Satz erfassen.", "@sage/xtrem-intacct-finance/package__name": "Sage Intacct Finanzen", "@sage/xtrem-intacct-finance/page__intacct_mapping_create_all_in_intacct": "Alle in Sage Intacct erstellen.", "@sage/xtrem-intacct-finance/page__intacct_mapping_create_all_in_intacct_manufacturing": "Alle in Sage DMO erstellen", "@sage/xtrem-intacct-finance/page__intacct-map/xtrem-instance-error": "Sage Intacct-ID __{{intacctId}}__ nicht ges<PERSON>.", "@sage/xtrem-intacct-finance/page_extensions__common__warning": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/page_extensions__there_are_ap_invoices_not_posted": "Es sind Eingangsrechnungen vorhanden, die noch nicht ins Hauptbuch gebucht wurden.", "@sage/xtrem-intacct-finance/page_extensions__there_are_ar_invoices_not_posted": "Es sind Ausgangsrechnungen vorhanden, die noch nicht ins Hauptbuch gebucht wurden.", "@sage/xtrem-intacct-finance/page-extensions__business_entity_customer_extension__intacctUrl____title": "URL Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__addresses____columns__helperText__intacctBusinessEntityAddress___id": "Wird synchronisiert", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__addresses____columns__title__intacctBusinessEntityAddress___id": "Aktualisieren", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__addresses____columns__title__intacctBusinessEntityAddress__state": "Status Integration", "@sage/xtrem-intacct-finance/page-extensions__business_entity_supplier_extension__intacctUrl____title": "URL Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__company_extension__creditLimitBlock____title": "Kreditlimit", "@sage/xtrem-intacct-finance/page-extensions__company_extension__doUpdateArAmountPaid____title": "Bezahlten Betrag in Verkaufsdokumentreports aktualisieren", "@sage/xtrem-intacct-finance/page-extensions__company_extension__managementSection____title": "Verwaltung", "@sage/xtrem-intacct-finance/page-extensions__company_extension__synchronize____helperText": "Wird synchronisiert", "@sage/xtrem-intacct-finance/page-extensions__company_extension__synchronize____title": "Aktualisieren", "@sage/xtrem-intacct-finance/page-extensions__customer_extension____navigationPanel__listItem__line16__title": "ID Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension____navigationPanel__listItem__line17__title": "Status Integration Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__addresses____columns__helperText___id": "Wird synchronisiert", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__addresses____columns__title___id": "Aktualisieren", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__addresses____columns__title__intacctBusinessEntityAddress__state": "Status Integration", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctIntegrationState____title": "Status Integration", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctUrlLink____title": "ID Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__synchronize____helperText": "Wird synchronisiert", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__synchronize____title": "Aktualisieren", "@sage/xtrem-intacct-finance/page-extensions__dimension_type_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__dimension_type_extension__intacctObject____title": "Objekt Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__customPackageFile____title": "Benutzerdefinierte Paketdatei", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__customPackageSection____title": "Benutzerdefiniertes Paket", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__canCreate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__canDelete": "Löschen", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__canUpdate": "Aktualisieren", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__intacctDescription": "Name Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__intacctName": "Name Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__nodeFactory__name": "Objekt Sage DMO", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__synchronisationWay": "Richtung Synchronisierung", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__synchronizationDirection": "Synchronisierung", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__xtremObject": "Objekt Sage DMO", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____title": "<PERSON><PERSON> gene<PERSON><PERSON>e Smart Events", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__objectSelect____title": "Objekt Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__save____title": "Speichern", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__smartEventBlock____title": "Smart Events ", "@sage/xtrem-intacct-finance/page-extensions__item_extension____navigationPanel__listItem__intacctId__title": "ID Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension____navigationPanel__listItem__intacctState__title": "Status Integration Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctId____title": "ID Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctIntegrationState____title": "Status Integration", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctUrl____title": "URL Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctUrlLink____title": "ID Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__synchronize____helperText": "Wird synchronisiert", "@sage/xtrem-intacct-finance/page-extensions__item_extension__synchronize____title": "Synchroni<PERSON><PERSON>", "@sage/xtrem-intacct-finance/page-extensions__journal_extension__recordNo____title": "Datensatznummer", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__blockDiscount____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__blockPenalty____title": "Mahngebühr", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__discountDate____title": "Tag", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__discountFrom____title": "Ab", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__discountType____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__penaltyType____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension____navigationPanel__listItem__line12__title": "ID Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension____navigationPanel__listItem__line13__title": "Status Integration Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__addresses____columns__helperText___id": "Wird synchronisiert", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__addresses____columns__title___id": "Aktualisieren", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__addresses____columns__title__intacctBusinessEntityAddress__state": "Status Integration", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctIntegrationState____title": "Status Integration", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctUrlLink____title": "ID Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__paymentMethodSelect____title": "Zahlungsart", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__synchronize____helperText": "Wird synchronisiert", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__synchronize____title": "Aktualisieren", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__country____title": "Land", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__country__name": "Land", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__isIntacctReverseCharge": "Reverse Charge Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__primaryExternalReference": "Primäre externe Referenz", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__taxCategory__name": "Steuerkategorie", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____title": "Sekundäre externe Referenz", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isActive____title": "Aktiv", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isIntacct____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isIntacctReverseCharge____title": "Reverse Charge Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isReverseCharge____title": "Reverse Charge", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__primaryExternalReference____title": "Primäre externe Referenz", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__secondaryExternalReference____title": "Sekundäre externe Referenz", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__type____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching____title": "Zuordnung Ausgangsrechnung", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__addAdvanceDimensionAttribute____title": "Sektoren", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__columns__account__name__title": "Steuerverwaltung", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__account__name": "Ko<PERSON>", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__customerId": "Kunden-ID", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__customerName": "Kundenname", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__errorMessage": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__intacctArDocumentID": "Kundenanzahlung Sage Intacct", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__paymentDate": "Zahlungsdatum", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__paymentMethod": "Zahlungsart", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__receiptDate": "Eingangsdatum", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__totalAmount": "Gesamtbetrag", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____dropdownActions__title": "Zeile löschen", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____title": "Generierung Kundenanzahlung", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__amountToMatch____title": "Zuzuordnender Betrag", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__bankAccount____title": "Bankkonto", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__cancel____title": "Abbrechen", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__createArAdvance____title": "Kundenanzahlung erstellen", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__createArPayment____title": "Zahlung Forderungen erstellen", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__customer_id_was_found": "Kunden-ID {{customerId}} gefunden.", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__customer_name_was_found": "Kundenname {{customerName}} gefunden.", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoice_number_was_found": "Rechnungsnummer {{invoiceNumber}} gefunden.", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__amountToMatch": "Zuzuordnender Betrag", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__customerId": "Kunden-ID", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__customerName": "Kundenname", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__date": "Rechnungsdatum", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__dueDate": "Fälligkeitsdatum", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__entityId": "Entitäts-ID", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__invoiceNo": "Rechnungsnummer", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__referenceNumber": "Referenznummer", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__term__name": "Begriff", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__totalAmount": "Gesamtbetrag", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__totalDue": "<PERSON><PERSON><PERSON><PERSON> Betrag", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__totalPaid": "Gesamt bezahlt", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____title": "Re<PERSON><PERSON>ngen", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__mainBlock____title": "Allgemein", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__mainSection____title": "Allgemein", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__matchingReason____title": "Zuordnungsgrund", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__ok____title": "OK", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__arPayment": "Zahlung Ausgangsrechnung", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__customerId": "Kunden-ID", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__customerName": "Kundenname", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__intacctArPayment": "Zahlung Ausgangsrechnung Sage Intacct", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__paymentDate": "Zahlungsdatum", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__paymentMethod": "Zahlungsart", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__receiptDate": "Eingangsdatum", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__totalAmount": "Gesamtbetrag", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____title": "Zahlungsgenerierung", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__customerId": "Kunden-ID", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__customerName": "Kundenname", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__errorMessage": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__intacctArDocumentID": "Zahlung Forderungen Sage Intacct", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__paymentDate": "Zahlungsdatum", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__paymentMethod": "Zahlungsart", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__receiptDate": "Eingangsdatum", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__totalAmount": "Gesamtbetrag", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____title": "Zahlungsgenerierung", "@sage/xtrem-intacct-finance/pages__bank_account____navigationPanel__listItem__line3__title": "Entitäts-ID", "@sage/xtrem-intacct-finance/pages__bank_account____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-intacct-finance/pages__bank_account____objectTypePlural": "Bankkonten", "@sage/xtrem-intacct-finance/pages__bank_account____objectTypeSingular": "Bankkonto", "@sage/xtrem-intacct-finance/pages__bank_account____title": "Bankkonto", "@sage/xtrem-intacct-finance/pages__bank_account___id____title": "ID", "@sage/xtrem-intacct-finance/pages__bank_account__account____title": "Ko<PERSON>", "@sage/xtrem-intacct-finance/pages__bank_account__currency____columns__title__id": "ISO 4217", "@sage/xtrem-intacct-finance/pages__bank_account__currency____columns__title__name": "Name", "@sage/xtrem-intacct-finance/pages__bank_account__currency____columns__title__symbol": "Symbol", "@sage/xtrem-intacct-finance/pages__bank_account__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-intacct-finance/pages__bank_account__currency____title": "Währung", "@sage/xtrem-intacct-finance/pages__bank_account__id____title": "ID", "@sage/xtrem-intacct-finance/pages__bank_account__isActive____title": "Aktiv", "@sage/xtrem-intacct-finance/pages__bank_account__mainBlock____title": "Bankkontoinformationen", "@sage/xtrem-intacct-finance/pages__bank_account__mainSection____title": "Allgemein", "@sage/xtrem-intacct-finance/pages__bank_account__megaEntityId____title": "Entitäts-ID", "@sage/xtrem-intacct-finance/pages__bank_account__name____title": "Name", "@sage/xtrem-intacct-finance/pages__delete_page_dialog_content": "<PERSON><PERSON> sind dabei, diesen Datensatz zu löschen.", "@sage/xtrem-intacct-finance/pages__delete_page_dialog_title": "Löschen bestätigen", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog____objectTypePlural": "Zuordnungsregeln Sage Intacct-Bankkonto", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog____objectTypeSingular": "Zuordnungsregel Sage Intacct-Bankkonto", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog____title": "Zuordnungsregeln", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__addRule____title": "Regel hinzufügen", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__apply____title": "Zuordnungsregeln anwenden", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__cancel____title": "Abbrechen", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__mainSection____title": "Allgemein", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__account__name__title": "Steuerverwaltung", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__account__name__title__5": "Rechtsordnung", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__location__name__title__2": "Name", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__tax__name__title__2": "Name", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title___id": "ID", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__account__name": "Ko<PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__bankAccount__id": "Bankkonto", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__isModified": "G<PERSON><PERSON>ndert", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__keyword": "Schlüsselwort", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__priority": "Priorität", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__tax__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__transactionType": "Transaktionsart", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____dropdownActions__title": "Löschen", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____dropdownActions__title__2": "Speichern", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____dropdownActions__title__3": "Sektoren", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____title": "Zuordnungsregeln", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__save____title": "Speichern", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____navigationPanel__listItem__line2__title": "ID", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____navigationPanel__listItem__title__title": "Bezeichnung", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____objectTypePlural": "Transaktionsfeeds Bankkonto", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____objectTypeSingular": "Transaktionsfeed Bankkonto", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____title": "Transaktionsfeed", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__accountFeedKey____title": "Feedschlüssel Finanzkonto", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__accountReconKey____title": "Abstimmungsschlüssel Bankkonto", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__accountType____title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__amount____title": "Betrag", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__amountToMatch____title": "Zuzuordnender Betrag", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__cleared____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__currency____title": "Währung", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__description____title": "Bezeichnung", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__documentNumber____title": "Dokumentnummer", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__documentType____title": "Dokumenttyp", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__entity____title": "Finanzentität", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__entityName____title": "Name Finanzentität", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__feedType____title": "Feedtyp", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__intacctId____title": "ID", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__mainBlock____title": "Transaktion", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__mainSection____title": "Allgemein", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__payee____title": "Zahlungsempfänger", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__postingDate____title": "Buchungsdatum", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__reconcilitationDate____title": "Abstimmungsdatum", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__transactionId____title": "Transaktions-ID", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__transactionType____title": "Transaktionsart", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog____objectTypeSingular": "Dokumentsuche Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog____title": "Dokumentsuche", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__account____title": "Ko<PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__amount____title": "Betrag", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__criteriaBlock____title": "Kriterien", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__date____title": "Datum", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__description____title": "Bezeichnung", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__documentType____placeholder": "Dokumenttyp auswählen", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__documentType____title": "Dokumenttyp", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title___id": "ID", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__account": "Ko<PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__amount": "Betrag", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__batchNo": "Batchnummer", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__date": "Datum", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__description": "Bezeichnung", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__documentType": "Dokumenttyp", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__entityId": "Entität", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__journal": "Journal", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__location": "Lagerplatz", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__recordNo": "Datensatznummer", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__url": "Referenz Integration Buchhaltung", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____title": "Ergebnisse", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__select____title": "Speichern", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line_4__title": "Smart Event", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line_5__title": "Richtung Synchronisierung", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line2__title": "Transaktionen Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line4__title": "Smart Event", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line5__title": "Richtung Synchronisierung", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line6__title": "Private Date<PERSON> e<PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__title__title": "Objekt Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__titleRight__title": "Node Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map____objectTypePlural": "Zuordnungen Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map____objectTypeSingular": "Zuordnung Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__additionnalLink____title": "Zusätzliche Links", "@sage/xtrem-intacct-finance/pages__intacct_map__additionnalLinkFormated____title": "Zusätzliche Links", "@sage/xtrem-intacct-finance/pages__intacct_map__addLineSpecificFields____title": "Komponente hinzufügen", "@sage/xtrem-intacct-finance/pages__intacct_map__createAll____title": "Alle in Sage DMO erstellen", "@sage/xtrem-intacct-finance/pages__intacct_map__createAllIntacct____title": "Alle in Sage Intacct erstellen", "@sage/xtrem-intacct-finance/pages__intacct_map__dataSection____title": "Daten", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__DATATYPE": "Datentyp", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__DESCRIPTION": "Bezeichnung", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__ID": "ID", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__ISCUSTOM": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__isEditable": "<PERSON>ier<PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__LABEL": "Titel", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__READONLY": "Schreibgeschützt", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__REQUIRED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__xtremDefaultProperty": "Standardeigenschaft", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__xtremProperty": "Eigenschaft Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__xtremPropertyOption": "Option Eigenschaft", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__id____title": "Objekt Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctDataBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctDescription____title": "Transaktionen Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctFilter____title": "Fi<PERSON> Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctNameSelect____title": "Transaktionen Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__isActive____title": "Aktiv", "@sage/xtrem-intacct-finance/pages__intacct_map__isPrivateShow____title": "Private Date<PERSON> e<PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__isSmartEvent____title": "Smart Event", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__collectionName": "Name Collection", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__line__id": "Objekt", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__line__intacctDescription": "Name", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__propertyHeader": "Eigenschaft Kopf", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__propertyLine": "Eigenschaft Zeile", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____title": "Z<PERSON><PERSON>nungen", "@sage/xtrem-intacct-finance/pages__intacct_map__lineSection____title": "Collections/Zeilen", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__integrationStatus": "Status Integration", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__isLinked": "Verknüpft", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__megaEntityId": "Entitäts-ID", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__whenCreated": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__whenModified": "Aktualisieren", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title": "In Sage DMO erstellen", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title__2": "In Sage DMO aktualisieren", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title__3": "In Sage DMO löschen", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title__4": "In Sage DMO löschen", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____title": "Ergebnisse", "@sage/xtrem-intacct-finance/pages__intacct_map__mainBlock____title": "Konfiguration", "@sage/xtrem-intacct-finance/pages__intacct_map__mainSection____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeFactory____columns__title__name": "Node", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeFactory____columns__title__title": "Node", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeFactory____title": "Node-Standard", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeProperties____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__notificationStatus____title": "Status Massenimport", "@sage/xtrem-intacct-finance/pages__intacct_map__refreshData____title": "Aktualisieren", "@sage/xtrem-intacct-finance/pages__intacct_map__refreshRelationMapping____title": "Aktualisieren", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__DATATYPE": "Datentyp", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__DESCRIPTION": "Bezeichnung", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__ID": "ID", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__ISCUSTOM": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__isEditable": "<PERSON>ier<PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__LABEL": "Titel", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__READONLY": "Schreibgeschützt", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__REQUIRED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__xtremDefaultProperty": "Standardeigenschaft", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__xtremProperty": "Eigenschaft Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__xtremPropertyOption": "Option Eigenschaft", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____title": "Beziehungen", "@sage/xtrem-intacct-finance/pages__intacct_map__resetRelationMapping____title": "Z<PERSON>ücksetzen", "@sage/xtrem-intacct-finance/pages__intacct_map__save____title": "Speichern", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____columns__title__name": "Eigenschaftsname Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____columns__title__whereValue": "Filter", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____dropdownActions__title": "Entfernen", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____title": "Technische Eigenschaften", "@sage/xtrem-intacct-finance/pages__intacct_map__synchronizationDirection____title": "Richtung Synchronisierung", "@sage/xtrem-intacct-finance/pages__intacct_map__writeMappingFile____title": "Zuordnungsdatei schreiben", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____navigationPanel__listItem__title__title": "Bank", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____objectTypePlural": "Transaktionsfeeds", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____objectTypeSingular": "", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____title": "Workbench Transaktionen", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query___etag____title": "ETag", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query___id____title": "ID", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____columns__columns__account__name__title__3": "Name", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____columns__columns__currency___id__title__4": "Dezimalstellen", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____columns__columns__financialSite___id__title__2": "ID", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____title": "Bankkonto", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__criteriaBlock____title": "Kriterien", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__dateCreation____title": "Erstellungsdatum", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__dateFrom____title": "<PERSON><PERSON> von", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__dateTo____title": "Datum bis", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__description____title": "Bezeichnung", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__mainSection____title": "Workbench Transaktionen", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__mapObject____columns__title__intacctDescription": "Name", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__mapObject____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matched____title": "Zugeordnet", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matchingRules____title": "Zuordnungsregeln öffnen", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matchStatus____placeholder": "Status auswählen", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matchStatus____title": "Status", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__number_of_journal_entries_created": "Journal entries created: {{documentsPosted}}.", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__post____title": "Buchen", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__refreshTransactionFeed____title": "Aktualisieren", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__totalAmount____title": "Gesamtbetrag", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__totalDeposit____title": "Einzahlung gesamt", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__totalWithdrawal____title": "Auszahlung gesamt", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__account__name__title": "Steuerverwaltung", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__account__name__title__6": "ID", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__account__name__title__7": "Steuerverwaltung", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__bankAccount__name__columns__title___id": "ID", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__bankAccount__name__columns__title___id__2": "ID", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__bankAccount__name__title__5": "Währung", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__currency__name__title": "Name", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__currency__name__title__3": "Name", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__document___id__title__5": "Betrag", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__location__name__title__2": "Name", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__tax__name__title__2": "Name", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title___id": "ID", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__account__name": "Ko<PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__amount": "Betrag", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__amount__2": "Betrag", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__amountToMatch": "Zuzuordnender Betrag", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__currency__name": "Währung", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__description": "Bezeichnung", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__intacctJournalEntryBatchNo": "Referenz Integration Buchhaltung", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__location__name": "Lagerplatz", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__payee": "Zahlungsempfänger", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__status": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__tax__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__taxAmount": "Steuerbetrag", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__transactionType": "Transaktionsart", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title": "Zahlung Ausgangsrechnung", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__2": "Zeile löschen", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__3": "<PERSON>eile aufteilen", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__4": "Sektoren", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__5": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__6": "Regel erstellen", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____title": "Datensät<PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionType____placeholder": "Transaktionsart", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionType____title": "Transaktionsart", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__unMatched____title": "<PERSON>cht zu<PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__withPostableFeeds____title": "<PERSON><PERSON> buch<PERSON>en <PERSON>eds", "@sage/xtrem-intacct-finance/pages__intact_transaction_feed_query__map_object_mandatory": "Das Zuordnungsobjekt Datensätze Transaktionsfeed Bankkonto ist nicht definiert", "@sage/xtrem-intacct-finance/pages__matching_rules_bulk_save": "Zuordnungsregeln gespeichert.", "@sage/xtrem-intacct-finance/pages__tax_extension__was_deactivated_secondary_external_reference": "Die Steuer wurde deaktiviert, da die sekundäre externe Referenz leer ist und Reverse Charge ausgewählt ist.", "@sage/xtrem-intacct-finance/pages_accounts_receivable_invoice_matching_advance_created": "Anzahlung erstellt:", "@sage/xtrem-intacct-finance/pages_accounts_receivable_invoice_matching_payment_created": "Zahlung erstellt:", "@sage/xtrem-intacct-finance/pages_document-no-document-selected": "Kein Dokument ausgewählt.", "@sage/xtrem-intacct-finance/pages_document-search-more-than-one": "<PERSON>e können nicht mehr als ein Dokument auswählen.", "@sage/xtrem-intacct-finance/pages_intacct_transaction_feed_query_create_rule": "Zuordnungsregel gespeichert.", "@sage/xtrem-intacct-finance/pages_integration-error": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages-confirm-cancel": "Abbrechen", "@sage/xtrem-intacct-finance/pages-confirm-delete": "Löschen", "@sage/xtrem-intacct-finance/permission__create__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/permission__create_update_all_intacct__name": "Alle Sage Intacct erstellen/aktualisieren", "@sage/xtrem-intacct-finance/permission__delete__name": "Löschen", "@sage/xtrem-intacct-finance/permission__delete_xtrem__name": "Xtrem löschen", "@sage/xtrem-intacct-finance/permission__get_available_xtrem_object_list__name": "Verfügbare Xtrem-Objektliste abrufen", "@sage/xtrem-intacct-finance/permission__get_data_intacct__name": "Sage Intacct-<PERSON><PERSON>", "@sage/xtrem-intacct-finance/permission__get_intacct_transactions_list__name": "Sage Intacct-Transaktionsliste abrufen", "@sage/xtrem-intacct-finance/permission__get_match_line__name": "Zuordnungszeile abrufen", "@sage/xtrem-intacct-finance/permission__get_object__name": "Objekt abrufen", "@sage/xtrem-intacct-finance/permission__get_structure__name": "Struktur abrufen", "@sage/xtrem-intacct-finance/permission__is_service_option_active_function__name": "Ist Funktion Dienstoption aktiv", "@sage/xtrem-intacct-finance/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/permission__post__name": "Buchen", "@sage/xtrem-intacct-finance/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/permission__retry_finance_document__name": "Finanzdokument wiederholen", "@sage/xtrem-intacct-finance/permission__search_intacct_data__name": "Sage Intacct-<PERSON><PERSON> suchen", "@sage/xtrem-intacct-finance/permission__service_option_change__name": "Änderung Dienstoption", "@sage/xtrem-intacct-finance/permission__set_cleared__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/permission__synchronization_with_sage_intacct__name": "Synchronisierung mit Sage Intacct", "@sage/xtrem-intacct-finance/permission__update__name": "Aktualisieren", "@sage/xtrem-intacct-finance/permission__write_structure__name": "Struk<PERSON> schreiben", "@sage/xtrem-intacct-finance/reset": "Z<PERSON>ücksetzen", "@sage/xtrem-intacct-finance/search": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/service_options__intacct_cashbook_management__name": "Sage Intacct Cashbook Management", "@sage/xtrem-intacct-finance/status_updated": "Status aktualisiert", "@sage/xtrem-intacct-finance/synchronization_already_in_progress": "Die Synchronisierung ist bereits in Bearbeitung", "@sage/xtrem-intacct-finance/target_document_not_found": "{{targetDocumentSysId}} {{targetDocumentType}}: Zieldokumenttyp nicht gefunden.", "@sage/xtrem-intacct-finance/target_document_type_not_supported": "{{targetDocumentType}}: <PERSON> Zieldokumenttyp wird nicht unterstützt.", "@sage/xtrem-intacct-finance/too_many_documents_found_on_intacct": "{{targetDocumentType}} {{documentNumber}}: <PERSON><PERSON> wurden {{numberOfDocumentsFoundOnIntacct}} Dokumente in Sage Intacct gefunden. Das Dokument kann nicht aktualisiert werden.", "@sage/xtrem-intacct-finance/transaction-feed-query-provide-bank-account": "Erfassen Sie die Bankkontonummer.", "@sage/xtrem-intacct-finance/transaction-feed-query-provide-dates": "Erfassen Sie Werte für das Datum von und das Datum bis.", "@sage/xtrem-intacct-finance/update-account-tax-management-context": "<PERSON><PERSON> sind dabei, die Kontoverwaltung auf 'Exkl. Steuern' zu setzen.", "@sage/xtrem-intacct-finance/update-account-tax-management-title": "Bestätigen Sie die Aktualisierung der Kontosteuerverwaltung.", "@sage/xtrem-intacct-finance/update-xtreem-return": "Datensätze erstellt: {{created}}. \n Datensätze aktualisiert: {{updated}}. \n {{message}}", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____callToActions__seeAll__title": "Alles anzeigen", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____dataDropdownMenu__orderBy__number__title": "Nach ID sortieren", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____dataDropdownMenu__orderBy__status__title": "Nach Name sortieren", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__line2__title": "Status", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__line2Right__title": "ID Sage Intacct", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__title__title": "ID", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__titleRight__title": "Name", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____title": "Status Integration Kunde", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____callToActions__seeAll__title": "Alles anzeigen", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____dataDropdownMenu__orderBy__number__title": "Nach ID sortieren", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____dataDropdownMenu__orderBy__status__title": "Nach Name sortieren", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__line2__title": "Status", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__line2Right__title": "ID Sage Intacct", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__title__title": "ID", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__titleRight__title": "Name", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____title": "Status Integration Lieferant"}