{"@sage/xtrem-intacct-finance/action-queued": "L'action a été mise en attente.", "@sage/xtrem-intacct-finance/activity__finance_listener__name": "Listener finance", "@sage/xtrem-intacct-finance/activity__intacct_bank_account_transaction_feed__name": "Flux de transaction de compte bancaire Sage Intacct", "@sage/xtrem-intacct-finance/activity__intacct_cash_book_management__name": "Gestion Bank Manager Sage Intacct", "@sage/xtrem-intacct-finance/activity__intacct_map__name": "Mapping Sage Intacct", "@sage/xtrem-intacct-finance/already-queued": "L'action est déjà en cours.", "@sage/xtrem-intacct-finance/bank-account-account-dont-exist": "{{bankName}} : le compte  {{accountName}} n'existe pas dans Sage DMO.", "@sage/xtrem-intacct-finance/bank-account-financial-site-dont-exist": "{{bankName}} : le site financier {{financialSite}} n'existe pas dans Sage DMO.", "@sage/xtrem-intacct-finance/cancel": "Annuler", "@sage/xtrem-intacct-finance/cannot_set_tax_with_account_not_subjected_to_taxes": "Vous ne pouvez pas définir un détail de taxe pour un compte qui n'est pas soumis aux taxes.", "@sage/xtrem-intacct-finance/classes__structure-hooks__intacct_is_active": "Intégration Sage Intacct active", "@sage/xtrem-intacct-finance/classes__structure-hooks__intacct_is_not_active": "Intégration Sage Intacct inactive", "@sage/xtrem-intacct-finance/classes__structure-hooks__payment_tracking_is_active": "L'activation Intacct est impossible si le suivi des règlements est actif.", "@sage/xtrem-intacct-finance/company_on_hold_complete": "Synchronisation réalisée pour la société en attente", "@sage/xtrem-intacct-finance/company_on_hold_start": "Synchronisation démarrée pour la société en attente", "@sage/xtrem-intacct-finance/company_on_hold_stop": "Interruption demandée le {{stopDate}} ", "@sage/xtrem-intacct-finance/company_on_hold_success": "Société {{companyForUpdateId}} mise à jour", "@sage/xtrem-intacct-finance/confirm-creation": "Confirmer la création", "@sage/xtrem-intacct-finance/confirm-reset": "Vous êtes sur le point de rétablir le mapping d'enregistrement à son état initial.", "@sage/xtrem-intacct-finance/confirm-reset-dialog-title": "Confirmer la réinitialisation", "@sage/xtrem-intacct-finance/confirm-update": "Confirmer la mise à jour", "@sage/xtrem-intacct-finance/create-ar-advance-context": "Vous êtes sur le point de créer un règlement {{currencySymbol}}{{amount}} à {{customer}}.", "@sage/xtrem-intacct-finance/create-ar-advance-title": "Créer l'avance comptable client", "@sage/xtrem-intacct-finance/create-ar-payment-context": "Vous êtes sur le point de créer un règlement {{currencySymbol}}{{amount}} à {{customer}}.", "@sage/xtrem-intacct-finance/create-ar-payment-title": "<PERSON><PERSON><PERSON> le règlement comptable client", "@sage/xtrem-intacct-finance/data_types__feed_line_matching_status_enum__name": "Enum statut de rapprochement de ligne de flux", "@sage/xtrem-intacct-finance/data_types__feed_record_status_enum__name": "Enum statut enregistrement de flux", "@sage/xtrem-intacct-finance/data_types__intacct_factor_data_type__name": "Type de données factor Intacct", "@sage/xtrem-intacct-finance/data_types__intacct_id_property_data_type__name": "Type de données propriété code Intacct", "@sage/xtrem-intacct-finance/data_types__intacct_matching_status_enum__name": "Enum statut rapprochement Intacct", "@sage/xtrem-intacct-finance/data_types__intacct_matching_type_enum__name": "Enum type rapprochement Intacct", "@sage/xtrem-intacct-finance/data_types__intacct_record_transaction_type_enum__name": "Enum type transaction enregistrement Intacct", "@sage/xtrem-intacct-finance/data_types__intacct_url__name": "URL Intacct", "@sage/xtrem-intacct-finance/data_types__payment_term_discount_or_penalty_type_enum__name": "Enum type remise ou pénalité condition de paiement", "@sage/xtrem-intacct-finance/description": "Description", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__draftMatch": "Brouillon de rapprochement", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__lookingForMatches": "Recherche de rapprochements", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__matched": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__matchFound": "Rapprochement trouvé", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__multipleMatches": "Plusieurs rapprochements", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__unmatched": "Délett<PERSON>", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvanceGenerated": "Avance comptable client g<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvancePosted": "Avance comptable client comptabilisé", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvancePostingError": "Erreur de comptabilisation avance comptable client", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvancePostingInProgress": "Comptabilisation avance comptable client en cours", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentGenerated": "Règlement comptable client g<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentGenerationInProgress": "Génération règlement comptable client en cours", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentPosted": "Règlement comptable client comptabilisé", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentPostingError": "Erreur de comptabilisation avance comptable client", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentPostingInProgress": "Comptabilisation règlement comptable client en cours", "@sage/xtrem-intacct-finance/enums__feed_record_status__draftArAdvance": "Brouillon d'avance comptable client", "@sage/xtrem-intacct-finance/enums__feed_record_status__draftArMatch": "Brouillon de rapprochement client", "@sage/xtrem-intacct-finance/enums__feed_record_status__draftMatch": "Brouillon de rapprochement", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntriesFound": "Écritures trouvées", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryFound": "Écriture trouvée", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryGenerated": "Écriture générée", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryGenerationError": "Erreur de génération d'écriture", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryGenerationInProgress": "Génération d'écriture en cours", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryPosted": "Écriture comptabilisée", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryPostingError": "Erreur de comptabilisation d'écriture", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryPostingInProgress": "Comptabilisation d'écriture en cours", "@sage/xtrem-intacct-finance/enums__feed_record_status__lookingForExistingJournalEntries": "Recherche d'écritures existantes", "@sage/xtrem-intacct-finance/enums__feed_record_status__matched": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__feed_record_status__partialArMatch": "Rapprochement client partiel", "@sage/xtrem-intacct-finance/enums__feed_record_status__readyForArAdvanceGeneration": "Prêt pour génération d'avance comptable client", "@sage/xtrem-intacct-finance/enums__feed_record_status__readyForArPaymentGeneration": "Prêt pour génération de règlement comptable client", "@sage/xtrem-intacct-finance/enums__feed_record_status__readyForPosting": "Prêt pour comptabilisation", "@sage/xtrem-intacct-finance/enums__feed_record_status__unmatched": "Délett<PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__cleared": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__draftMatched": "Brou<PERSON>n rapproché", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__ignored": "Ignoré", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__matched": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__partiallyMatched": "Rappro<PERSON><PERSON> partiel<PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__selectToMatch": "Sélectionner pour rapprochement", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__selectToUnmatch": "Sélectionner pour délettrer", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__unmatched": "Délett<PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_type__contains": "Contient", "@sage/xtrem-intacct-finance/enums__intacct_matching_type__equals": "Égal à", "@sage/xtrem-intacct-finance/enums__intacct_matching_type__regularExpression": "Expression régulière", "@sage/xtrem-intacct-finance/enums__intacct_record_transaction_type__deposit": "Encaissement", "@sage/xtrem-intacct-finance/enums__intacct_record_transaction_type__withdrawal": "Décaissement", "@sage/xtrem-intacct-finance/enums__payment_term_discount_or_penalty_type__amount": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__payment_term_discount_or_penalty_type__percentage": "Pourcentage", "@sage/xtrem-intacct-finance/error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/functions__map/xtreem-mass-creation-tax-category-update-with-error": "Après intégration, vous devez actualiser la catégorie de taxe.", "@sage/xtrem-intacct-finance/id": "Code", "@sage/xtrem-intacct-finance/intacct_bank_account_transaction_feed_has_no_lines_to_post": "Le flux de transaction de comptes bancaires Sage Intacct {{intacctbankAccountTransactionFeed}} ne comporte aucune ligne à comptabiliser.", "@sage/xtrem-intacct-finance/intacct_document_not_found": "{{sysId}} {{type}} : document Sage Intacct introuvable.", "@sage/xtrem-intacct-finance/intacct-address-bill-to": "Adresse de facturation", "@sage/xtrem-intacct-finance/intacct-address-pay-to": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-intacct-finance/intacct-address-primary": "Adresse principale", "@sage/xtrem-intacct-finance/intacct-address-primary-be": "Adresse entité commerciale principale", "@sage/xtrem-intacct-finance/intacct-address-return-to": "<PERSON><PERSON><PERSON> <PERSON> retour", "@sage/xtrem-intacct-finance/intacct-address-ship-to": "Adresse d'expédition", "@sage/xtrem-intacct-finance/intacct-address-ship-to-primary": "<PERSON><PERSON><PERSON> de livraison principale", "@sage/xtrem-intacct-finance/intacct-addresse-others": "<PERSON>tre adresse", "@sage/xtrem-intacct-finance/item_extension_item_id_frozen": "Vous ne pouvez pas changer le code article s'il est déjà intégré avec Sage Intacct.", "@sage/xtrem-intacct-finance/mapping-private-show": "Vous pouvez uniquement sélectionner Inclure les données privées si le champ MEGAENTITYID existe et est lié à Sage DMO.", "@sage/xtrem-intacct-finance/menu_item__cashbook-manager": "Bank Manager", "@sage/xtrem-intacct-finance/name": "Nom", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredClass": "Classe demandée", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredCustomer": "Client demandé", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredDepartement": "Service demandé", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredEmploye": "Collaborateur demandé", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredItem": "Article demandé", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredLocation": "Emplacement demandé", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredProject": "Affaire demandée", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredSupplier": "Fournisseur demandé", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredTask": "Tâche requise", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__statusIntacct": "Statut Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__uIntacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__billToPayToContactName": "Nom du client facturé / fournisseur payé", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctDocument": "Document Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctIntegrationState": "Statut intégration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctLastIntegrationDate": "Dernière date d'intégration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctUrl": "URL Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__shipToReturnToContactName": "Nom du contact de l'adresse d'expédition / adresse de retour", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__taxSolutionId": "Code de solution de taxe", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__classId": "Code classe", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__customerId": "Code client", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__departmentId": "Code département", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__inclusiveTax": "TTC", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__intacctDocumentLine": "Ligne de document Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__itemId": "Code article", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__lineAmountExcludingTaxSigned": "Montant ligne HT signé", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__memo": "Avoir", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__projectId": "ID affaire", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__taxEntries": "Écritures de taxe", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__vendorId": "Code fournisseur", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__mutation__createArAdvance": "Créer l'avance comptable client", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__mutation__createArAdvance__failed": "Échec de création d'avance comptable client.", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__mutation__createArAdvance__parameter__data": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__bankFeed": "Flux bancaire", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__entityId": "Code entité", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctDocument": "Document Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctIntegrationState": "Statut intégration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctLastIntegrationDate": "Dernière date d'intégration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctUrl": "URL Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__paymentMethod": "Mode de paiement", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__classId": "Code classe", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__customerId": "Code client", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__departmentId": "Code département", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__intacctDocumentLine": "Ligne de document Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__itemId": "Code article", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__projectId": "Code affaire", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__vendorId": "Code fournisseur", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__mutation__updateOpenItemFromIntacct": "Mettre l'échéance à jour à partir de Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__mutation__updateOpenItemFromIntacct__failed": "Échec de mise à jour de l'échéance à partir d'Intacct.", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__mutation__updateOpenItemFromIntacct__parameter__arInvoiceSysId": "Code système facture client", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctDocument": "Document Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctIntegrationState": "Statut intégration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctLastIntegrationDate": "Dernière date d'intégration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctUrl": "URL Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__taxSolutionId": "Code de solution de taxe", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__classId": "Code classe", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__customerId": "Code client", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__departmentId": "Code département", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__intacctDocumentLine": "Ligne de document Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__itemId": "Code article", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__lineAmountExcludingTaxSigned": "Montant ligne HT signé", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__memo": "Mémo", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__projectId": "Code affaire", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__taxEntries": "Écritures de taxe", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__vendorId": "Code fournisseur", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__mutation__createArPayment": "<PERSON><PERSON><PERSON> le règlement comptable client", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__mutation__createArPayment__failed": "Échec de création de règlement comptable client.", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__mutation__createArPayment__parameter__data": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__bankFeed": "Flux bancaire", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__entityId": "Code entité", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctDocument": "Document Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctIntegrationState": "Statut intégration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctLastIntegrationDate": "Dernière date d'intégration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctUrl": "URL Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__paymentMethod": "Mode de paiement", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__arInvoiceRecordNo": "Numéro d'enregistrement de la facture comptable client", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__intacctDocumentLine": "Ligne de document Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__accounts-payable-invoice-extension__accounts_payable_invoice_already_sent": "La facture comptable fournisseur a déjà été transmise à Sage Intacct. Statut : {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-advance-extension__accounts_receivable_advance_already_sent": "L'avance client a été transmise à Sage Intacct. Statut : {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-invoice-extension__accounts_receivable_invoice_already_sent": "La facture comptable fournisseur a déjà été transmise à Sage Intacct. Statut : {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-payment-extension__accounts_receivable_payment_already_sent": "Le règlement client a été transmis à Sage Intacct. Statut : {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__bulkMutation__updateRecordNoOnEmployee": "Mettre à jour le numéro d'enregistrement pour le collaborateur", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__bulkMutation__updateRecordNoOnEmployee__failed": "Échec de mise à jour du numéro d'enregistrement pour le collaborateur.", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__intacctProject": "Projet Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__statusIntacct": "Statut Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__uIntacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__intacctGlAccount": "Compte général Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__location": "Emplacement", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__megaEntityId": "ID entité MEGA", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__statusIntacct": "Statut Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__business_entity_address_extension__property__intacctBusinessEntityAddress": "Adresse de l'entité commerciale Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__business_entity_address_extension__property__name": "Nom", "@sage/xtrem-intacct-finance/node-extensions__business_entity_contact_extension__property__intacctPrintAs": "Sage Intacct Imprimer en tant que", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold": "Synchronisation de la société en attente", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold__failed": "Échec de synchronisation de société en suspens.", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold__parameter__companySysId": "Code système société", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold__parameter__isAllCompanies": "Toutes les sociétés", "@sage/xtrem-intacct-finance/node-extensions__company_extension__property__doApPosting": "Comptabilisation factures fournisseurs", "@sage/xtrem-intacct-finance/node-extensions__company_extension__property__doArPosting": "Comptabilisation factures clients", "@sage/xtrem-intacct-finance/node-extensions__company_extension__property__doUpdateArAmountPaid": "Mise à jour du montant client réglé", "@sage/xtrem-intacct-finance/node-extensions__customer_extension__property__intacctCustomer": "Client Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__intacctObject": "Objet Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__statusIntacct": "Statut Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__uIntacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_type_extension__property__intacctObject": "Objet Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__mutation__generateSmartEventFile": "<PERSON><PERSON><PERSON><PERSON> le fichier Smart Event", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__mutation__generateSmartEventFile__failed": "Échec de génération du fichier Smart Event.", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__property__customPackageFile": "Fichier de package personnalisé", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__property__generateSmartEvent": "<PERSON><PERSON><PERSON><PERSON> Smart Event", "@sage/xtrem-intacct-finance/node-extensions__item_extension__property__id": "Code", "@sage/xtrem-intacct-finance/node-extensions__item_extension__property__intacctItem": "Article Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__entityId": "Code entité", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctDocument": "Document Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctIntegrationState": "Statut intégration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctLastIntegrationDate": "Dernière date d'intégration Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctUrl": "URL Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__isActive": "Active", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__taxImplications": "Implications de taxe", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__taxSolutionId": "Code de solution de taxe", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_dimension_extension__property__intacctDimension": "Section Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__absoluteTransactionAmount": "Montant de transaction absolu", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__allocationSplit": "Division d'allocation", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__excludeRecord": "Exclure enregistrement", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctDimensionSplit": "Division de section Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctDocumentLine": "Ligne de document Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctSign": "Sens Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__isActive": "Active", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__locationWhenNoSplit": "Emplacement en l'absence de division", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__taxEntries": "Écritures de taxe", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__statusIntacct": "Statut Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__uIntacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal-entry-extension__journal_entry_already_sent": "L'écriture a déjà été transmise à Sage Intacct. Statut : {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountAmount": "<PERSON><PERSON> de remise", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountDate": "Date de remise", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountFrom": "Remise début", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountFromIntacct": "Calcul date escompte Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountType": "Type de remise", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountTypeIntacct": "Type d'escompte Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__dueFromIntacct": "Échéance en provenance de Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__isIntacct": "Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__penaltyAmount": "Montant de pénalité", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__penaltyType": "Type de pénalité", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__penaltyTypeIntacct": "Type de pénalité Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__statusIntacct": "Statut Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__uIntacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__supplier_extension__property__intacctSupplier": "Fournisseur Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__supplier_extension__property__paymentMethodSelect": "Sélection mode de règlement", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctAccount": "Compte Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctSecondaryExternalReference": "Référence externe secondaire Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctSolutionId": "Code solution Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctTaxType": "Type de taxe Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__isIntacct": "Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__isIntacctReverseCharge": "Autoliquidation Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__isUpdateFromIntacct": "Mise à jour à partir de Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__rate": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__statusIntacct": "Statut Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__uIntacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/nodes__company_customer_on_hold_control": "Vous ne pouvez pas mettre à jour le client bloqué si Sage Intacct est actif.", "@sage/xtrem-intacct-finance/nodes__company_get_company_ref_not_mapped": "La valeur retournée par Sage Intacct {{intacctCompanyRef}} ne peut pas être mappée.", "@sage/xtrem-intacct-finance/nodes__customer_credit_limit_control": "Vous ne pouvez pas mettre à jour la limite de crédit si Sage Intacct est actif.", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument": "Renvoyer le document finance", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument__failed": "Échec de renvoi du document financier.", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument__parameter__financeDocumentSysId": "Code système du document finance", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument__parameter__financeTransaction": "Transaction Finance", "@sage/xtrem-intacct-finance/nodes__finance_listener__node_name": "Listener finance", "@sage/xtrem-intacct-finance/nodes__finance-listener__already_success": "{{number}} : le document finance a déjà été synchronisé.", "@sage/xtrem-intacct-finance/nodes__finance-listener__document_found_on_intacct_updated": "{{NUMBER}} : le document a été trouvé dans Sage Intacct. Le statut a été mis à jour.", "@sage/xtrem-intacct-finance/nodes__finance-listener__document_not_found_on_intacct_posted_waiting": "Le document n'a pas été trouvé dans Sage Intacct. Le document a été comptabilisé et est en attente de réponse de la part de Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__finance-listener__retry": "En traitement", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__node_name": "Facture fournisseur Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__billBySupplier": "Fournisseur facturant", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__billToPayToContactName": "Nom du contact du client facturé / fournisseur payé", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__document": "Document", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__entityId": "Code entité", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__shipToReturnToContactName": "Nom du contact de l'adresse d'expédition / adresse de retour", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__taxSolutionId": "Code de solution de taxe", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__node_name": "Ligne de facture fournisseur Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__classId": "Code classe", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__customerId": "Code client", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__departmentId": "Code département", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__documentLine": "Ligne de document", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__employeeId": "Code collaborateur", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__inclusiveTax": "TTC", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__itemId": "Code article", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__lineAmountExcludingTaxSigned": "Montant ligne HT signé", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__memo": "Avoir", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__projectId": "ID affaire", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__signedAmountExcludingTax": "Montant ligne HT", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__taskId": "Code tâche", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__taxEntries": "Écritures de taxe", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__vendorId": "Code fournisseur", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__node_name": "Avance client Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__bankFeed": "Flux bancaire", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__document": "Document", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__entityId": "Code entité", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__paymentMethod": "Mode de paiement", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__node_name": "Ligne d'avance client Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__classId": "Code classe", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__customerId": "Code client", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__departmentId": "Code département", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__documentLine": "Ligne de document", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__itemId": "Code article", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__projectId": "Code affaire", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__vendorId": "Code fournisseur", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__node_name": "Facture client Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__billToCustomer": "Client facturé", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__document": "Document", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__entityId": "Code entité", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__taxSolutionId": "Code de solution de taxe", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__node_name": "Ligne facture client Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__classId": "Code classe", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__customerId": "Code client", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__departmentId": "Code département", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__documentLine": "Ligne de document", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__employeeId": "Code collaborateur", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__inclusiveTax": "TTC", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__itemId": "Code article", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__lineAmountExcludingTaxSigned": "Montant ligne HT signé", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__memo": "Avoir", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__projectId": "Code affaire", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__signedAmountExcludingTax": "Montant ligne HT", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__taskId": "Code tâche", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__taxEntries": "Écritures de taxe", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__vendorId": "Code fournisseur", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__node_name": "Règlement client Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__bankFeed": "Flux bancaire", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__document": "Document", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__entityId": "Code entité", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__paymentMethod": "Mode de paiement", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__node_name": "Ligne de règlement client Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__property__arInvoiceRecordNo": "Numéro d'enregistrement de la facture comptable client", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__property__documentLine": "Ligne de document", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__mutation__bulkSave": "Enregistrer en masse", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__mutation__bulkSave__failed": "Échec de l'enregistrement en masse.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__mutation__bulkSave__parameter__intacctBankAccountMatchings": "Rapprochements de comptes bancaires Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__node_name": "Rapprochement de comptes bancaires Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__analyticalData": "Données analytiques", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__bankAccount": "Compte bancaire", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__keyword": "Mot de passe", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__location": "Emplacement", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__priority": "Priorité", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__storedAttributes": "Attributs stockés", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__storedDimensions": "Sections stockées", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__tax": "Taxe", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__transactionType": "Type de transaction", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__type": "Type", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__updateAccountTaxManagement": "MAJ gestion taxe compte", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctArInvoice": "Requête facture client Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctArInvoice__failed": "Échec de consultation de facture client Intacct.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctArInvoice__parameter__parameters": "Paramètres", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctCustomers": "Requête clients Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctCustomers__failed": "Échec de consultation de clients Intacct.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument": "Requête document Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument__failed": "Échec de renvoi du document financier.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument__parameter__documentType": "Type de document", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument__parameter__parameters": "Paramètres", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__wrong_account_tax_management": "Vous pouvez uniquement sélectionner un compte associé à une gestion de taxe de type 'Autre' ou 'HT'.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared": "Définir sur Effacé", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared__failed": "Échec de paramétrage comme effacé.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared__parameter__cleared": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared__parameter__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__node_name": "Flux de transaction de compte bancaire Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__accountFeedKey": "Clé de flux de compte", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__accountReconKey": "Clé de rapprochement de compte bancaire", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__accountType": "Type de compte", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__amount": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__amountToMatch": "Montant à rapprocher", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__analyticalData": "Données analytiques", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__arMatch": "Rapprochement client", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__bankAccount": "Compte bancaire", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__cleared": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__computedAttributes": "Attributs calculés", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__currency": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__description": "Description", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__documentNumber": "Numéro de document", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__documentType": "Type de document", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__entity": "Entité", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__entityName": "Nom de l'entité", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__feedType": "Type de flux", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentCreatedNumber": "Numéro du document finance créé", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentCreatedSysId": "Code système du document finance créé", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentGenerationErrorMessage": "Message d'erreur de génération du document finance", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentType": "Type document finance", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeIntegrationApp": "Application d'intégration financière", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeIntegrationAppRecordId": "Code d'enregistrement de l'application d'intégration financière", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeIntegrationAppUrl": "URL d'application d'intégration finance", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__importSession": "Session d'import", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__intacctCleared": "Sage Intacct effacé", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__internalFinanceIntegrationStatus": "Statut intégration financière interne", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__journalEntryNumber": "Numéro d'écriture", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__jsonArInvoices": "Factures comptables clients JSON", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__payee": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__paymentDate": "Date de règlement", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__paymentMethod": "Mode de paiement", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__payToCustomerId": "Code du fournisseur payé", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__payToCustomerName": "Nom du fournisseur payé", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__postingDate": "Date de comptabilisation", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__receiptDate": "Date de réception", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__reconcilitationDate": "Date de rapprochement", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__storedAttributes": "Attributs stockés", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__storedDimensions": "Sections stockées", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__targetDocumentType": "Type document cible", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__transactionId": "Code de transaction", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__transactionType": "Type de transaction", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine": "Obtenir ligne de rapprochement", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine__failed": "Échec d'obtention de la ligne de rapprochement.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine__parameter__matchingRules": "R<PERSON>gles de correspondance", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine__parameter__transactionFeed": "Flux de transaction", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__node_name": "Ligne de flux de transaction de compte Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__amount": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__analyticalData": "Données analytiques", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__computedAttributes": "Attributs calculés", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__document": "Document", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__intacctJournalEntryBatchNo": "Numéro de batch d'écritures Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__intacctJournalEntryRecordNo": "N° d'enregistrement d'écriture Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__intacctJournalEntryUrl": "URL d'écriture Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__location": "Emplacement", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__status": "Statut", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__storedAttributes": "Attributs stockés", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__storedDimensions": "Sections stockées", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__tax": "Taxe", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__taxAmount": "Montant de taxe", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__taxRate": "Taux de taxe", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__node_name": "Section de ligne de flux de transaction de compte Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__property__originLine": "Ligne d'origine", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__mutation__serviceOptionChange": "Modification option de service", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__mutation__serviceOptionChange__failed": "Échec de modification des options de service.", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__node_name": "Gestion Bank Manager Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__query__isServiceOptionActiveFunction": "Fonction options de service active", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__query__isServiceOptionActiveFunction__failed": "Échec de la fonction des options de service active.", "@sage/xtrem-intacct-finance/nodes__intacct_contact__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_contact__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_contact__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_contact__node_name": "Contact Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__contact": "Contact", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__name": "Nom", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__prefix": "Préfixe", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__printAs": "Imprimer en tant que", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__status": "Statut", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__sysIdLink": "Lien code système", "@sage/xtrem-intacct-finance/nodes__intacct_customer__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_customer__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_customer__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_customer__node_name": "Client Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__billToAddress": "Adresse de facturation", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__parent": "Parent", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__shipToAddress": "Adresse d'expédition", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__status": "Statut", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__node_name": "Client fournisseur Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__contactList": "Liste de contacts", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__hideDisplayContact": "Masquer / Afficher le contact", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__primaryContact": "Contact principal", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__status": "Statut", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__sysIdLink": "Lien code système", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__currencies_do_not_match": "La devise du compte bancaire doit être la même que la devise du site financier associé au compte bancaire.", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__node_name": "Session d'import Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__bankAccount": "Compte bancaire", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__dateCreation": "Date de création", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__description": "Description", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__linesImported": "Lignes importées", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__mapObject": "Map object", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__numberOfLinesToImport": "Nombre de lignes à compter", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__queryParameters": "Lecture paramètres", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__status": "Statut", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__transactionFeed": "Flux de transaction", "@sage/xtrem-intacct-finance/nodes__intacct_item__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_item__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_item__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_item__node_name": "Article Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_item__property__item": "Article", "@sage/xtrem-intacct-finance/nodes__intacct_item__property__status": "Statut", "@sage/xtrem-intacct-finance/nodes__intacct_item__property__type": "Type", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__node_name": "Écriture Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__document": "Document", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__entityId": "Code entité", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__taxImplications": "Implications de taxe", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__taxSolutionId": "Code de solution de taxe", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__node_name": "Ligne d'écriture Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__absoluteTransactionAmount": "Montant de transaction absolu", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__allocationSplit": "Division d'allocation", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__dimensionSplit": "Division sections", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__documentLine": "Ligne de document", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__excludeRecord": "Exclure enregistrement", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__locationWhenNoSplit": "Emplacement en l'absence de division", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__sign": "<PERSON>s", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__taxEntries": "Écritures de taxe", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct": "Supprimer Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct__failed": "Échec de suppression de Intacct.", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct__parameter__intacctNode": "Node Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct__parameter__recordNo": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__synchronizeNode": "Synchroniser le node", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__synchronizeNode__failed": "Échec de synchronisation CBN nomenclature.", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__synchronizeNode__parameter__intacctNode": "Node Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_listener__node_name": "Listener <PERSON>ta<PERSON>t", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob": "Tâche de création en masse Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob__failed": "Échec de la tâche de création en masse Xtreem.", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob__parameter__intacctName": "Nom de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob__parameter__type": "Type", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__xtreemMassCreationJob": "Tâche de création en masse", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__xtreemMassCreationJob__failed": "Échec de la tâche de création en masse Xtreem.", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__xtreemMassCreationJob__parameter__data": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__bulkMutation__updateCustomMapping": "Mettre à jour le mapping personnalisé", "@sage/xtrem-intacct-finance/nodes__intacct_map__bulkMutation__updateCustomMapping__failed": "Échec de mise à jour des mappings actifs.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__createUpdateAllIntacct": "<PERSON><PERSON> c<PERSON> / Mettre à jour dans Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__createUpdateAllIntacct__failed": "Échec de création / Mise à jour de tout dans Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__createUpdateAllIntacct__parameter__intacctName": "Nom de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem": "Supprimer Xtrem", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__failed": "Échec de suppression xtrem.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__parameter__intacctIdValue": "Valeur de code de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__parameter__intacctName": "Nom de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__parameter__xtremSysId": "Code système Xtrem", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl": "Obtenir l'URL de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl__failed": "Échec d'obtention de l'URL Intacct.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl__parameter___id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl__parameter__nodeName": "Nom du node", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getStructure": "Obtenir la structure", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getStructure__failed": "Échec d'obtention de structure.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getStructure__parameter__content": "Contenu", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__writeStructure": "Écrire la structure", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__writeStructure__failed": "Échec d'écriture de la structure.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__writeStructure__parameter__object": "Objet", "@sage/xtrem-intacct-finance/nodes__intacct_map__node_name": "Mapping Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__additionnalLink": "Lien supplémentaire", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__customRelationMapping": "Mapping relation personnalis<PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__editableFields": "Champs modifiables", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__intacctDescription": "Description Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__intacctFilter": "Filtre Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__isPrivateShow": "Vue enregistrements privés", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__isSmartEvent": "Smart Events", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__relationMapping": "Mapping de relation", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__setupId": "Code paramétrage", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__specificFields": "Champs spécifiques", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__uRelationMapping": "Mapping de relation", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__xtremObject": "Objet Xtrem", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__compareObject": "Comparer objet", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__compareObject__parameter__node": "Node", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__compareObject__parameter__sysId": "Code système", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getAvailableXtremObjectList": "Obtenir la liste des objets Xtrem disponibles", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getAvailableXtremObjectList__failed": "Échec d'obtention de la liste des objets xtrem disponibles.", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct": "Donnée en provenance de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__failed": "Échec d'obtention données Intacct.", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__filters": "Filtres", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__maxData": "Données maximum", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__name": "Nom", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__transaction": "Transaction", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getIntacctTransactionsList": "Obtenir la liste des transactions Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getIntacctTransactionsList__failed": "Échec d'obtention de la liste des transactions Intacct.", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject": "Obt<PERSON>r objet", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__failed": "Échec d'obtention d'objet.", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__parameter__docparid": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__parameter__isUpdate": "Mettre à jour", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__parameter__object": "Objet", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__node_name": "Fournisseur Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__parent": "Parent", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__payToAddress": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__returnToAddress": "<PERSON><PERSON><PERSON> <PERSON> retour", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__status": "Statut", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_finance_state__node_name": "Statut finance synchronisation Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_finance_state__property__externalIntegration": "Integration externe", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_state__node_name": "Etat de synchronisation Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_state__property__entityId": "Code entité", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__site_legislations_and_coa_legislation_dont_match": "La législation du site doit être la même que celle du plan comptable.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_legislation_and_coa_legislation_dont_match": "La législation de taxe doit être la même que celle du plan comptable.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_management_should_be_excluding_taxes": "Vous ne pouvez pas définir un détail de taxe pour un compte qui n'est pas soumis aux taxes.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_mandatory": "La taxe est obligatoire.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_not_allowed": "La taxe doit être vide.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction_feed__site_legislation_and_coa_legislation_dont_match": "La législation du site doit être la même que celle du plan comptable.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction_feed__tax_legislation_and_coa_legislation_dont_match": "La législation de taxe doit être la même que celle du plan comptable.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed__tax_mandatory": "La taxe doit être renseignée.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed__tax_not_allowed": "La taxe doit être vide.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed/bank-manager-import-fail": "Sage DMO Bank Manager : l'import a échoué ({{filters}}). \n {{error}}.", "@sage/xtrem-intacct-finance/nodes__intacct-listener/delete-success": "Supprimé", "@sage/xtrem-intacct-finance/nodes__intacct-listener/intacct-time-out": "<PERSON><PERSON><PERSON> d'attente dépassé : Sage Intacct n'a pas répondu.", "@sage/xtrem-intacct-finance/nodes__intacct-listener/not-implemented": "Non mis en oeuvre : {{change}}:{{object}}  ", "@sage/xtrem-intacct-finance/nodes__intacct-listener/synchronize-node-finish": "Synchronisation : {{state}}", "@sage/xtrem-intacct-finance/nodes__intacct-listener/synchronize-node-start": "Synchronisation démarrée", "@sage/xtrem-intacct-finance/nodes__intacct-map/create-update-xtrem": "Trop de lignes envoyées par Sage Intacct ({{lenght}}) - intacctId : {{intacctIdValue}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/desynchronized_not_exist": "Désynchronisation {{node}} : code Sage Intacct  {{name}} : {{id}} introuvable.", "@sage/xtrem-intacct-finance/nodes__intacct-map/file-already-exist": "Le fichier {{object}} existe déjà.", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-instance-query-to-many-lines": "Trop de lignes retournées par Sage Intacct ({{countIntacctData}}). \n Ajoutez des filtres et essayez de nouveau.", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end": "Création en masse de {{intacctName}} terminée à {{dateTime}}. \n {{errorCount}}erreurs, {{successCount}} ré<PERSON><PERSON> /{{totalCount}} au total ", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end-with-errors-phase": "Terminée avec erreurs", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end-without-errors-phase": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-fail": "Sage Intacct : <PERSON><PERSON><PERSON> de la création de masse ({{intacctName}}).", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-number-of-lines": "{{numberOfLines}} à créer / mettre à jour.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-desynchronization": "L'objet est synchronisé.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-intacct-data": "Aucune donnée Sage Intacct pour l'objet {{intacctName}} avec {{intacctIDField}} étant égal à {{intacctIdValue}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-intacct-instance": "Aucune instance Sage Intacct activée.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-map-instance": "Le fichier ne peut pas être créé parce que {{object}} n'existe pas dans Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__intacct-map/nothing-to-write": "Impossible d'écrire le fichier {{object}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/to-many-lines": "La requête a renvoyé de nombreuses lignes :{{numberOflines}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-end": "Création en masse de {{intacctName}} terminée à {{dateTime}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-number-of-lines": "{{numberOfLines}} à créer / mettre à jour", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-start": "Création en masse de {{intacctName}} démarrée à {{dateTime}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-tax-category-update": "\n Après intégration, vous devez actualiser la catégorie de taxe.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-created-updated": "Sage DMO {{created}} : {{id}} ", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-created-updated-total": "Enregistrements créés : {{created}}. /n Enregistrements mis à jour : {{updated}}. /n Enregistrements avec erreurs : {{error}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-query-to-many-lines": "Trop de lignes retournées par Sage DMO ({{numberOfNodes}}).\\n Ajoutez des filtres et essayez de nouveau.", "@sage/xtrem-intacct-finance/nodes__map_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__map_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__map_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__map_line__node_name": "Mapper la ligne", "@sage/xtrem-intacct-finance/nodes__map_line__property__collectionName": "Nom de collection", "@sage/xtrem-intacct-finance/nodes__map_line__property__line": "Ligne", "@sage/xtrem-intacct-finance/nodes__map_line__property__mapHeader": "En-tête de mapping", "@sage/xtrem-intacct-finance/nodes__map_line__property__propertyHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON> de proprié<PERSON>", "@sage/xtrem-intacct-finance/nodes__map_line__property__propertyLine": "Ligne de propriété", "@sage/xtrem-intacct-finance/nodes__map_property__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-intacct-finance/nodes__map_property__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-intacct-finance/nodes__map_property__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-intacct-finance/nodes__map_property__node_name": "Propriété de mapping", "@sage/xtrem-intacct-finance/nodes__map_property__property__map": "Mapping", "@sage/xtrem-intacct-finance/nodes__map_property__property__name": "Nom", "@sage/xtrem-intacct-finance/nodes__map_property__property__type": "Type", "@sage/xtrem-intacct-finance/nodes__payment_term_discount_amount_percentage_error": "Le montant de la remise doit être inférieur à 100%.", "@sage/xtrem-intacct-finance/nodes__payment_term_discount_mandatory": "Le type et le montant de remise sont obligatoires quand une date de remise est renseignée.", "@sage/xtrem-intacct-finance/nodes__payment_term_penalty_amount_percentage_error": "Le montant de la pénalité doit être inférieur à 100%", "@sage/xtrem-intacct-finance/nodes__tax_extension__deletion_forbidden": "Vous ne pouvez pas supprimer cette taxe. Elle est liée à Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_not_allowed": "Vous ne pouvez pas créer, modifier ou supprimer cette taxe. Elle est liée à Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_active_is_forbidden": "Vous ne pouvez pas activer cette taxe. Elle est liée à Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_country_is_forbidden": "Vous ne pouvez pas modifier le pays.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_primary_external_reference_is_forbidden": "Vous ne pouvez pas modifier la référence externe principale.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_reverse_charge_is_forbidden": "Vous ne pouvez pas modifier l'autoliquidation.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_tax_type_is_forbidden": "Vous ne pouvez pas modifier le type de taxe. Il est lié à Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__tax_extension__negative_rate_not_allowed": "Vous ne pouvez pas indiquer un taux négatif.", "@sage/xtrem-intacct-finance/package__name": "Sage Intacct finance", "@sage/xtrem-intacct-finance/page__intacct_mapping_create_all_in_intacct": "<PERSON>ut c<PERSON>er dans Sage Intacct.", "@sage/xtrem-intacct-finance/page__intacct_mapping_create_all_in_intacct_manufacturing": "<PERSON>ut c<PERSON>er dans Sage DMO", "@sage/xtrem-intacct-finance/page__intacct-map/xtrem-instance-error": "Code Sage Intacct __{{intacctId}}__ non sauvegardé", "@sage/xtrem-intacct-finance/page_extensions__common__warning": "Avertissement", "@sage/xtrem-intacct-finance/page_extensions__there_are_ap_invoices_not_posted": "Certaines factures fournisseurs ne sont pas encore comptabilisées au grand-livre.", "@sage/xtrem-intacct-finance/page_extensions__there_are_ar_invoices_not_posted": "Certaines factures clients ne sont pas encore comptabilisées au grand-livre.", "@sage/xtrem-intacct-finance/page-extensions__business_entity_customer_extension__intacctUrl____title": "URL Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__addresses____columns__helperText__intacctBusinessEntityAddress___id": "Synchronisation", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__addresses____columns__title__intacctBusinessEntityAddress___id": "Actualiser", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__addresses____columns__title__intacctBusinessEntityAddress__state": "Statut intégration", "@sage/xtrem-intacct-finance/page-extensions__business_entity_supplier_extension__intacctUrl____title": "URL Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__company_extension__creditLimitBlock____title": "<PERSON>ite <PERSON>", "@sage/xtrem-intacct-finance/page-extensions__company_extension__doUpdateArAmountPaid____title": "Mise à jour du montant réglé sur les éditions des documents de vente", "@sage/xtrem-intacct-finance/page-extensions__company_extension__managementSection____title": "Gestion", "@sage/xtrem-intacct-finance/page-extensions__company_extension__synchronize____helperText": "Synchronisation", "@sage/xtrem-intacct-finance/page-extensions__company_extension__synchronize____title": "Actualiser", "@sage/xtrem-intacct-finance/page-extensions__customer_extension____navigationPanel__listItem__line16__title": "Code Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension____navigationPanel__listItem__line17__title": "Statut intégration Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__addresses____columns__helperText___id": "Synchronisation", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__addresses____columns__title___id": "Actualiser", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__addresses____columns__title__intacctBusinessEntityAddress__state": "Statut intégration", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__addresses____levels__columns__title__intacctBusinessEntityAddress__state": "Statut intégration", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__displayAddresses____columns__helperText": "Synchronisation", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__displayAddresses____columns__title": "Actualiser", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__displayAddresses____columns__title__intacctBusinessEntityAddress__state": "Statut intégration", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctId____title": "Code Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctIntegrationState____title": "Statut d'intégration", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctUrl____title": "URL Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctUrlLink____title": "Code Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__synchronize____helperText": "Synchronisation", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__synchronize____title": "Actualiser", "@sage/xtrem-intacct-finance/page-extensions__dimension_type_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__dimension_type_extension__intacctObject____title": "Objet Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__customPackageFile____title": "Fichier de package personnalisé", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__customPackageSection____title": "Package personnalisé", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__canCreate": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__canDelete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__canUpdate": "Mettre à jour", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__intacctDescription": "Nom de Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__intacctName": "Nom de Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__nodeFactory__name": "Objet Sage DMO", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__synchronisationWay": "Sens de synchronisation", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__synchronizationDirection": "Synchronisation", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__xtremObject": "Objet Sage DMO", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____title": "Smart events à générer", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__objectSelect____title": "Objet Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__save____title": "Enregistrer", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__smartEventBlock____title": "Smart Events ", "@sage/xtrem-intacct-finance/page-extensions__item_extension____navigationPanel__listItem__intacctId__title": "Code Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension____navigationPanel__listItem__intacctState__title": "Statut intégration Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctId____title": "Code Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctIntegrationState____title": "Statut intégration", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctUrl____title": "URL Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctUrlLink____title": "Code Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__synchronize____helperText": "Synchronisation", "@sage/xtrem-intacct-finance/page-extensions__item_extension__synchronize____title": "Synchroniser", "@sage/xtrem-intacct-finance/page-extensions__journal_extension__recordNo____title": "Numéro d'enregistrement", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__blockDiscount____title": "Remise", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__blockPenalty____title": "Pénalité", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__discountDate____title": "Jour", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__discountFrom____title": "De", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__discountType____title": "Type", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__penaltyType____title": "Type", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension____navigationPanel__listItem__line12__title": "Code Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension____navigationPanel__listItem__line13__title": "Statut d'intégration Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension____navigationPanel__listItem__line8__title": "Code Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension____navigationPanel__listItem__line9__title": "Statut intégration Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__addresses____columns__helperText___id": "Synchronisation", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__addresses____columns__title___id": "Actualiser", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__addresses____columns__title__intacctBusinessEntityAddress__state": "Statut intégration", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__addresses____levels__columns__title__intacctBusinessEntityAddress__state": "Statut intégration", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__displayAddresses____columns__helperText": "Synchronisation", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__displayAddresses____columns__title": "Actualiser", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__displayAddresses____columns__title__intacctBusinessEntityAddress__state": "Statut intégration", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctId____title": "Code Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctIntegrationState____title": "Statut d'intégration", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctUrl____title": "URL Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctUrlLink____title": "Code Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__paymentMethodSelect____title": "Mode de paiement", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__synchronize____helperText": "Synchronisation", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__synchronize____title": "Actualiser", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__country____title": "Pays", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__country__name": "Pays", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__isIntacctReverseCharge": "Autoliquidation Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__primaryExternalReference": "Référence externe principale", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__taxCategory__name": "Catégorie de taxe", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____title": "Référence externe secondaire", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isActive____title": "Active", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isIntacct____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isIntacctReverseCharge____title": "Autoliquidation Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isReverseCharge____title": "Autoliquidation", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__primaryExternalReference____title": "Référence externe principale", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__secondaryExternalReference____title": "Référence externe secondaire", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__type____title": "Type de taxe", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching____title": "Rapprochement des factures clients", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__addAdvanceDimensionAttribute____title": "Sections", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__columns__account__name__title": "Gestion taxes", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__account__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__customerId": "Code client", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__customerName": "Nom client", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__errorMessage": "Message", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__intacctArDocumentID": "Avance comptable client Sage Intacct", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__paymentDate": "Date de règlement", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__paymentMethod": "Mode paiement", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__receiptDate": "Date récept", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__totalAmount": "Montant total", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____dropdownActions__title": "Supp<PERSON>er ligne", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____title": "Génération avance comptable client", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__amountToMatch____title": "Montant à rapprocher", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__bankAccount____title": "Compte bancaire", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__cancel____title": "Annuler", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__createArAdvance____title": "Créer l'avance comptable client", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__createArPayment____title": "<PERSON><PERSON><PERSON> le règlement comptable client", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__customer_id_was_found": "Code client {{customer}} trouvé.", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__customer_name_was_found": "Nom du client {{customerName}} trouvé.", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoice_number_was_found": "Numéro de facture {{invoiceNumber}} trouvé.", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__amountToMatch": "Montant à rapprocher", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__customerId": "Code client", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__customerName": "Nom client", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__date": "Date facture", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__dueDate": "Date d'échéance", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__entityId": "Code entité", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__invoiceNo": "N° facture", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__referenceNumber": "N° référence", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__term__name": "Condition", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__totalAmount": "Montant total", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__totalDue": "<PERSON><PERSON> dû", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__totalPaid": "Total réglé", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____title": "Factures", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__mainBlock____title": "Général", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__mainSection____title": "Général", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__matchingReason____title": "<PERSON><PERSON><PERSON> de rapprochement", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__ok____title": "OK", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__arPayment": "Paiement factures comptables clients", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__customerId": "Code client", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__customerName": "Nom client", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__intacctArPayment": "Paiement des factures comptables clients Sage Intacct", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__paymentDate": "Date paiement", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__paymentMethod": "Mode paiement", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__receiptDate": "Date récept", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__totalAmount": "Montant total", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____title": "Génération de paiement", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__customerId": "Code client", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__customerName": "Nom client", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__errorMessage": "Message", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__intacctArDocumentID": "Paiement des factures comptables clients Sage Intacct", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__paymentDate": "Date de règlement", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__paymentMethod": "Mode paiement", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__receiptDate": "Date récept", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__totalAmount": "Montant total", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____title": "Génération de paiement", "@sage/xtrem-intacct-finance/pages__bank_account____navigationPanel__listItem__line3__title": "Code entité", "@sage/xtrem-intacct-finance/pages__bank_account____navigationPanel__optionsMenu__title": "Tous", "@sage/xtrem-intacct-finance/pages__bank_account____objectTypePlural": "Comptes bancaires", "@sage/xtrem-intacct-finance/pages__bank_account____objectTypeSingular": "Compte bancaire", "@sage/xtrem-intacct-finance/pages__bank_account____title": "Compte bancaire", "@sage/xtrem-intacct-finance/pages__bank_account___id____title": "Code", "@sage/xtrem-intacct-finance/pages__bank_account__account____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__bank_account__currency____columns__title__id": "ISO 4217", "@sage/xtrem-intacct-finance/pages__bank_account__currency____columns__title__name": "Nom", "@sage/xtrem-intacct-finance/pages__bank_account__currency____columns__title__symbol": "Symbole", "@sage/xtrem-intacct-finance/pages__bank_account__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-intacct-finance/pages__bank_account__currency____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__bank_account__id____title": "Code", "@sage/xtrem-intacct-finance/pages__bank_account__isActive____title": "Actif", "@sage/xtrem-intacct-finance/pages__bank_account__mainBlock____title": "Information du compte bancaire", "@sage/xtrem-intacct-finance/pages__bank_account__mainSection____title": "Général", "@sage/xtrem-intacct-finance/pages__bank_account__megaEntityId____title": "Code entité", "@sage/xtrem-intacct-finance/pages__bank_account__name____title": "Nom", "@sage/xtrem-intacct-finance/pages__delete_page_dialog_content": "Vous êtes sur le point de supprimer cet enregistrement.", "@sage/xtrem-intacct-finance/pages__delete_page_dialog_title": "Confirmer la <PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog____objectTypePlural": "Règles de correspondance de comptes bancaires Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog____objectTypeSingular": "Règle de correspondance de compte bancaire Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog____title": "R<PERSON>gles de correspondance", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__addRule____title": "<PERSON><PERSON><PERSON> règle", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__apply____title": "Appliquer les règles de correspondance", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__cancel____title": "Annuler", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__mainSection____title": "Général", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__account__name__title": "Gestion taxes", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__account__name__title__5": "Législation", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__location__name__title__2": "Nom", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__tax__name__title__2": "Nom", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title___id": "Code", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__account__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__bankAccount__id": "Compte bancaire", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__isModified": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__keyword": "Mot de passe", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__priority": "Priorité", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__tax__name": "Taxe", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__transactionType": "Type transaction", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__type": "Type", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____dropdownActions__title__2": "Enregistrer", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____dropdownActions__title__3": "Sections", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____title": "R<PERSON>gles de correspondance", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__save____title": "Enregistrer", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____navigationPanel__listItem__line2__title": "Code", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____navigationPanel__listItem__title__title": "Description", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____navigationPanel__optionsMenu__title": "Tous", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____objectTypePlural": "Flux de transaction de comptes bancaires", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____objectTypeSingular": "Flux de transaction de compte bancaire", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____title": "Flux de transaction", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__accountFeedKey____title": "Clé de flux de compte financier", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__accountReconKey____title": "Clé de rapprochement de compte bancaire", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__accountType____title": "Type de compte financier", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__amount____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__amountToMatch____title": "Montant à rapprocher", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__cleared____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__currency____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__description____title": "Description", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__documentNumber____title": "Numéro de document", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__documentType____title": "Type de document", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__entity____title": "Entité financier", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__entityName____title": "Nom de l'entité financière", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__feedType____title": "Type de flux", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__intacctId____title": "Code", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__mainBlock____title": "Transaction", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__mainSection____title": "Général", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__payee____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__postingDate____title": "Date de comptabilisation", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__reconcilitationDate____title": "Date de rapprochement", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__transactionId____title": "Code de transaction", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__transactionType____title": "Type de transaction", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog____objectTypeSingular": "Recherche de documents Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog____title": "Recherche de document", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__account____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__amount____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__criteriaBlock____title": "Critères", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__date____title": "Date", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__description____title": "Description", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__documentType____placeholder": "Sélectionner le type de document", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__documentType____title": "Type de document", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title___id": "Code", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__amount": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__batchNo": "Numéro de batch", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__date": "Date", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__description": "Description", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__documentType": "Type document", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__entityId": "Entité", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__journal": "Journal", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__location": "Emplacement", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__recordNo": "N° enregistrement", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__url": "Réf. intégration comptable", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____title": "Résultats", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__select____title": "Enregistrer", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line_4__title": "Smart Events", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line_5__title": "Sens de synchronisation", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line2__title": "Transactions Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line4__title": "Smart Events", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line5__title": "Sens de synchronisation", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line6__title": "Inclure les données privées", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__title__title": "Objet Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__titleRight__title": "Node Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map____objectTypePlural": "Mappings Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map____objectTypeSingular": "Mapping Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map____title": "Mapping", "@sage/xtrem-intacct-finance/pages__intacct_map__additionnalLink____title": "Liens supplémentaires", "@sage/xtrem-intacct-finance/pages__intacct_map__additionnalLinkFormated____title": "Liens supplémentaires", "@sage/xtrem-intacct-finance/pages__intacct_map__addLineSpecificFields____title": "A<PERSON>ter composant", "@sage/xtrem-intacct-finance/pages__intacct_map__createAll____title": "<PERSON>ut c<PERSON>er dans Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__createAllIntacct____title": "<PERSON>ut c<PERSON>er dans Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__dataSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__DATATYPE": "Type données", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__DESCRIPTION": "Description", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__ID": "Code", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__ISCUSTOM": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__isEditable": "Modifiable", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__LABEL": "Intitulé", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__READONLY": "Lecture seule", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__REQUIRED": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__xtremDefaultProperty": "Propriété par défaut", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__xtremProperty": "Propriété Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__xtremPropertyOption": "Option propriété", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__id____title": "Objet Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctDataBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctDescription____title": "Transactions Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctFilter____title": "Filtres Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctNameSelect____title": "Transactions Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__isActive____title": "Actif", "@sage/xtrem-intacct-finance/pages__intacct_map__isPrivateShow____title": "Inclure les données privées", "@sage/xtrem-intacct-finance/pages__intacct_map__isSmartEvent____title": "Smart Events", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__collectionName": "Nom collection", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__line__id": "Objet", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__line__intacctDescription": "Nom", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__propertyHeader": "Propri<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__propertyLine": "Propriété ligne", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____title": "Mappings", "@sage/xtrem-intacct-finance/pages__intacct_map__lineSection____title": "Collections / Lignes", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__integrationStatus": "Statut d'intégration", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__isLinked": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__megaEntityId": "Code entité", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__whenCreated": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__whenModified": "Mise à jour", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title": "Créer dans Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title__2": "Mettre à jour dans Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title__3": "Supprimer dans Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title__4": "Supprimer dans Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____title": "Résultats", "@sage/xtrem-intacct-finance/pages__intacct_map__mainBlock____title": "Configuration", "@sage/xtrem-intacct-finance/pages__intacct_map__mainSection____title": "Mapping", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeFactory____columns__title__name": "Node", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeFactory____columns__title__title": "Node", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeFactory____title": "Node livré", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeProperties____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__notificationStatus____title": "Statut de l'import en masse", "@sage/xtrem-intacct-finance/pages__intacct_map__refreshData____title": "Actualiser", "@sage/xtrem-intacct-finance/pages__intacct_map__refreshRelationMapping____title": "Actualiser", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__DATATYPE": "Type données", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__DESCRIPTION": "Description", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__ID": "Code", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__ISCUSTOM": "Personnalisée", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__isEditable": "Modifiable", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__LABEL": "Intitulé", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__READONLY": "Lecture seule", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__REQUIRED": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__xtremDefaultProperty": "Propriété par défaut", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__xtremProperty": "Propriété Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__xtremPropertyOption": "Option propriété", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____title": "Relations", "@sage/xtrem-intacct-finance/pages__intacct_map__resetRelationMapping____title": "Réinitialiser", "@sage/xtrem-intacct-finance/pages__intacct_map__save____title": "Enregistrer", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____columns__title__name": "Nom propriété Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____columns__title__type": "Type", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____columns__title__whereValue": "Filtre", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____title": "Propriétés techniques", "@sage/xtrem-intacct-finance/pages__intacct_map__synchronizationDirection____title": "Sens de synchronisation", "@sage/xtrem-intacct-finance/pages__intacct_map__writeMappingFile____title": "<PERSON><PERSON><PERSON><PERSON> le fichier de mapping", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____navigationPanel__listItem__title__title": "Banque", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____navigationPanel__optionsMenu__title": "Toutes", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____objectTypePlural": "Flux de transactions", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____objectTypeSingular": "Plan de travail de transactions", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____title": "Plan de travail de transactions", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query___etag____title": "ETag", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query___id____title": "Code", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____columns__columns__account__name__title__3": "Nom", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____columns__columns__currency___id__title__4": "Décimales", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____columns__columns__financialSite___id__title__2": "Code", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____title": "Compte bancaire", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__criteriaBlock____title": "Critères", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__dateCreation____title": "Date de création", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__dateFrom____title": "Date début", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__dateTo____title": "Taxe début", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__description____title": "Description", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__mainSection____title": "Plan de travail de transactions", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__mapObject____columns__title__intacctDescription": "Nom", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__mapObject____title": "Mapping", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matched____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matchingRules____title": "<PERSON><PERSON><PERSON><PERSON>r les règles de correspondance", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matchStatus____placeholder": "Sélectionner le statut", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matchStatus____title": "Statut", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__number_of_journal_entries_created": "Journal entries created: {{documentsPosted}}.", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__post____title": "Comptabi<PERSON>er", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__refreshTransactionFeed____title": "Actualiser", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__totalAmount____title": "Montant total", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__totalDeposit____title": "Encaissement total", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__totalWithdrawal____title": "Décaissement total", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__account__name__title": "Gestion taxes", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__account__name__title__6": "Code", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__account__name__title__7": "Gestion taxes", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__bankAccount__name__columns__title___id": "Code", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__bankAccount__name__columns__title___id__2": "Code", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__bankAccount__name__title__5": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__currency__name__title": "Nom", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__currency__name__title__3": "Nom", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__document___id__title__5": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__location__name__title__2": "Nom", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__tax__name__title__2": "Nom", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title___id": "Code", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__account__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__amount": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__amount__2": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__amountToMatch": "Montant à rapprocher", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__currency__name": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__description": "Description", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__intacctJournalEntryBatchNo": "Réf. intégration comptable", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__location__name": "Emplacement", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__payee": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__status": "Statut ligne", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__tax__name": "Taxe", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__taxAmount": "Montant taxe", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__transactionType": "Type transaction", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title": "Règlement comptable client", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__2": "Supp<PERSON>er ligne", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__3": "Diviser ligne", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__4": "Sections", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__5": "Recherche", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__6": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____title": "Enregistrements", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionType____placeholder": "Type de transaction", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionType____title": "Type de transaction", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__unMatched____title": "Délett<PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__withPostableFeeds____title": "Avec flux comptabilisables", "@sage/xtrem-intacct-finance/pages__intact_transaction_feed_query__map_object_mandatory": "L'objet de mapping Enregistrements des flux de transaction de comptes bancaires n'est pas défini.", "@sage/xtrem-intacct-finance/pages__matching_rules_bulk_save": "Règles de correspondance enregistrées", "@sage/xtrem-intacct-finance/pages__tax_extension__was_deactivated_secondary_external_reference": "La taxe est désactivée parce que la référence externe secondaire est vide et que l'autoliquidation est sélectionnée.", "@sage/xtrem-intacct-finance/pages_accounts_receivable_invoice_matching_advance_created": "Avance créée :", "@sage/xtrem-intacct-finance/pages_accounts_receivable_invoice_matching_payment_created": "Règlement créé :", "@sage/xtrem-intacct-finance/pages_document-no-document-selected": "Aucun document sélectionné.", "@sage/xtrem-intacct-finance/pages_document-search-more-than-one": "Vous ne pouvez pas sélectionner plus d'un document.", "@sage/xtrem-intacct-finance/pages_intacct_transaction_feed_query_create_rule": "Règle de correspondance enregistrée", "@sage/xtrem-intacct-finance/pages_integration-error": "Erreur de génération", "@sage/xtrem-intacct-finance/pages-confirm-cancel": "Annuler", "@sage/xtrem-intacct-finance/pages-confirm-delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/permission__create_update_all_intacct__name": "<PERSON><PERSON> c<PERSON> / Mettre à jour dans Sage Intacct", "@sage/xtrem-intacct-finance/permission__delete__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/permission__delete_xtrem__name": "Supprimer xtrem", "@sage/xtrem-intacct-finance/permission__get_available_xtrem_object_list__name": "Obtenir la liste des objets xtrem disponibles", "@sage/xtrem-intacct-finance/permission__get_data_intacct__name": "Donnée en provenance de Sage Intacct", "@sage/xtrem-intacct-finance/permission__get_intacct_transactions_list__name": "Obtenir la liste des transactions Sage Intacct", "@sage/xtrem-intacct-finance/permission__get_match_line__name": "Obtenir ligne de rapprochement", "@sage/xtrem-intacct-finance/permission__get_object__name": "Obt<PERSON>r objet", "@sage/xtrem-intacct-finance/permission__get_structure__name": "Obtenir structure", "@sage/xtrem-intacct-finance/permission__is_service_option_active_function__name": "Fonction option de service active", "@sage/xtrem-intacct-finance/permission__manage__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/permission__post__name": "Comptabi<PERSON>er", "@sage/xtrem-intacct-finance/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/permission__retry_finance_document__name": "Renvoyer le document finance", "@sage/xtrem-intacct-finance/permission__search_intacct_data__name": "Requête données Sage Intacct", "@sage/xtrem-intacct-finance/permission__service_option_change__name": "Modification option de service", "@sage/xtrem-intacct-finance/permission__set_cleared__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/permission__synchronization_with_sage_intacct__name": "Synchronisation avec Sage Intacct", "@sage/xtrem-intacct-finance/permission__update__name": "Mettre à jour", "@sage/xtrem-intacct-finance/permission__write_structure__name": "Écrire structure", "@sage/xtrem-intacct-finance/reset": "Réinitialiser", "@sage/xtrem-intacct-finance/search": "Recherche", "@sage/xtrem-intacct-finance/service_options__intacct_cashbook_management__name": "Gestion Bank Manager Sage Intacct", "@sage/xtrem-intacct-finance/status_updated": "Statut mis à jour", "@sage/xtrem-intacct-finance/synchronization_already_in_progress": "La synchronisation est en cours.", "@sage/xtrem-intacct-finance/target_document_not_found": "{{targetDocumentSysId}} {{targetDocumentType}} : ce type de document cible est introuvable.", "@sage/xtrem-intacct-finance/target_document_type_not_supported": "{{targetDocumentType}} : ce type de document cible n'est pas pris en charge.", "@sage/xtrem-intacct-finance/too_many_documents_found_on_intacct": "{{targetDocumentType}} {{documentNumber}} : le nombre de documents trouvés dans Sage Intacct est {{numberOfDocumentsFoundOnIntacct}}. Le document ne peut pas être mis à jour.", "@sage/xtrem-intacct-finance/transaction-feed-query-provide-bank-account": "Ren<PERSON><PERSON><PERSON> le numéro du compte bancaire.", "@sage/xtrem-intacct-finance/transaction-feed-query-provide-dates": "Renseignez des valeurs pour la date début et la date fin.", "@sage/xtrem-intacct-finance/update-account-tax-management-context": "Vous êtes sur le point de définir une gestion de compte de type 'Hors Taxe'.", "@sage/xtrem-intacct-finance/update-account-tax-management-title": "Confirmer la mise à jour de la gestion des taxes de comptes", "@sage/xtrem-intacct-finance/update-xtreem-return": "Enregistrements créés : {{created}}. \n Enregistrements mis à jour : {{updated}}. \n {{message}}", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____callToActions__seeAll__title": "Tout voir", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____dataDropdownMenu__orderBy__number__title": "Trier par code", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____dataDropdownMenu__orderBy__status__title": "Trier par nom", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__line2__title": "Statut", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__line2Right__title": "Code Sage Intacct", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__title__title": "Code", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__titleRight__title": "Nom", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____title": "Statut d'intégration client", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____callToActions__seeAll__title": "Tout voir", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____dataDropdownMenu__orderBy__number__title": "Trier par code", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____dataDropdownMenu__orderBy__status__title": "Trier par nom", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__line2__title": "Statut", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__line2Right__title": "Code Sage Intacct", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__title__title": "Code", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__titleRight__title": "Nom", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____title": "Statut d'intégration fournisseur"}