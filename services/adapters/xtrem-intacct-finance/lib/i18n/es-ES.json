{"@sage/xtrem-intacct-finance/action-queued": "La acción se ha añadido a la cola.", "@sage/xtrem-intacct-finance/activity__finance_listener__name": "Proceso de escucha de contabilidad", "@sage/xtrem-intacct-finance/activity__intacct_bank_account_transaction_feed__name": "Lista de transacciones de cuentas bancarias de Sage Intacct", "@sage/xtrem-intacct-finance/activity__intacct_cash_book_management__name": "Sage Intacct Cash Book Management", "@sage/xtrem-intacct-finance/activity__intacct_map__name": "Mapeo en Sage Intacct", "@sage/xtrem-intacct-finance/already-queued": "La acción ya está en curso.", "@sage/xtrem-intacct-finance/bank-account-account-dont-exist": "{{bankName}}: la cuenta {{accountName}} no existe en Sage DMO.", "@sage/xtrem-intacct-finance/bank-account-financial-site-dont-exist": "{{bankName}}: la planta financiera {{financialSite}} no existe en Sage DMO.", "@sage/xtrem-intacct-finance/cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/cannot_set_tax_with_account_not_subjected_to_taxes": "No puedes definir impuestos en una cuenta no sujeta a impuestos.", "@sage/xtrem-intacct-finance/classes__structure-hooks__intacct_is_active": "La integración con Sage Intacct está activa.", "@sage/xtrem-intacct-finance/classes__structure-hooks__intacct_is_not_active": "La integración con Sage Intacct no está activa.", "@sage/xtrem-intacct-finance/classes__structure-hooks__payment_tracking_is_active": "Si el seguimiento de pagos está activo, la integración con Sage Intacct no se puede activar.", "@sage/xtrem-intacct-finance/company_on_hold_complete": "La comprobación de la sociedad se ha sincronizado.", "@sage/xtrem-intacct-finance/company_on_hold_start": "La sincronización de la comprobación de la sociedad se ha iniciado.", "@sage/xtrem-intacct-finance/company_on_hold_stop": "La cancelación se ha solicitado el {{stopDate}}. ", "@sage/xtrem-intacct-finance/company_on_hold_success": "La sociedad {{companyForUpdateId}} se ha actualizado.", "@sage/xtrem-intacct-finance/confirm-creation": "Confirmar creación", "@sage/xtrem-intacct-finance/confirm-reset": "¿Quieres restablecer el mapeo del registro a su estado inicial?", "@sage/xtrem-intacct-finance/confirm-reset-dialog-title": "Confirmar rest<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/confirm-update": "Confirmar actualiza<PERSON>", "@sage/xtrem-intacct-finance/create-ar-advance-context": "¿Quieres crear un pago de {{amount}} {{currencySymbol}} para {{customer}}?", "@sage/xtrem-intacct-finance/create-ar-advance-title": "<PERSON><PERSON><PERSON> anticipo de cliente", "@sage/xtrem-intacct-finance/create-ar-payment-context": "¿Quieres crear un pago de {{amount}} {{currencySymbol}} para {{customer}}?", "@sage/xtrem-intacct-finance/create-ar-payment-title": "Crear pago de factura contable de cliente", "@sage/xtrem-intacct-finance/data_types__feed_line_matching_status_enum__name": "Feed line matching status enum", "@sage/xtrem-intacct-finance/data_types__feed_record_status_enum__name": "Feed record status enum", "@sage/xtrem-intacct-finance/data_types__intacct_factor_data_type__name": "Intacct factor data type", "@sage/xtrem-intacct-finance/data_types__intacct_id_property_data_type__name": "Intacct ID property data type", "@sage/xtrem-intacct-finance/data_types__intacct_matching_status_enum__name": "Intacct matching status enum", "@sage/xtrem-intacct-finance/data_types__intacct_matching_type_enum__name": "Intacct matching type enum", "@sage/xtrem-intacct-finance/data_types__intacct_record_transaction_type_enum__name": "Intacct record transaction type enum", "@sage/xtrem-intacct-finance/data_types__intacct_url__name": "URL en Sage Intacct", "@sage/xtrem-intacct-finance/data_types__payment_term_discount_or_penalty_type_enum__name": "Payment term discount or penalty type enum", "@sage/xtrem-intacct-finance/description": "Descripción", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__draftMatch": "Correspondencia en borrador", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__lookingForMatches": "Búsqueda de correspondencias", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__matched": "Con correspondencia", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__matchFound": "Correspondencia encontrada", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__multipleMatches": "Varias correspondencias", "@sage/xtrem-intacct-finance/enums__feed_line_matching_status__unmatched": "Sin marcar", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvanceGenerated": "Anticipo de cliente generado", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvancePosted": "Anticipo de cliente contabilizado", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvancePostingError": "Error de contabilización de anticipo de cliente", "@sage/xtrem-intacct-finance/enums__feed_record_status__arAdvancePostingInProgress": "Contabilización de anticipo de cliente en curso", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentGenerated": "Pago de cliente generado", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentGenerationInProgress": "Generación de pago de cliente en curso", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentPosted": "Pago de cliente contabilizado", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentPostingError": "Error de contabilización de pago de cliente", "@sage/xtrem-intacct-finance/enums__feed_record_status__arPaymentPostingInProgress": "Contabilización de pago de cliente en curso", "@sage/xtrem-intacct-finance/enums__feed_record_status__draftArAdvance": "<PERSON><PERSON><PERSON> de anticipo de cliente", "@sage/xtrem-intacct-finance/enums__feed_record_status__draftArMatch": "Borrador de conciliación de documento de cliente", "@sage/xtrem-intacct-finance/enums__feed_record_status__draftMatch": "Correspondencia en borrador", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntriesFound": "Asientos encontrados", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryFound": "Asiento encontrado", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryGenerated": "Asiento generado", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryGenerationError": "Error de generación de asiento", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryGenerationInProgress": "Generación de asiento en curso", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryPosted": "Asiento contabilizado", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryPostingError": "Error de contabilización de asiento", "@sage/xtrem-intacct-finance/enums__feed_record_status__journalEntryPostingInProgress": "Contabilización de asiento en curso", "@sage/xtrem-intacct-finance/enums__feed_record_status__lookingForExistingJournalEntries": "Búsqueda de asientos existentes", "@sage/xtrem-intacct-finance/enums__feed_record_status__matched": "Con correspondencia", "@sage/xtrem-intacct-finance/enums__feed_record_status__partialArMatch": "Conciliación parcial de documento de cliente", "@sage/xtrem-intacct-finance/enums__feed_record_status__readyForArAdvanceGeneration": "Listo para generar anticipo de cliente", "@sage/xtrem-intacct-finance/enums__feed_record_status__readyForArPaymentGeneration": "Listo para generar pago de cliente", "@sage/xtrem-intacct-finance/enums__feed_record_status__readyForPosting": "Listo para contabilizar", "@sage/xtrem-intacct-finance/enums__feed_record_status__unmatched": "Sin marcar", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__cleared": "Conciliada", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__draftMatched": "<PERSON><PERSON><PERSON> con correspondencia", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__ignored": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__matched": "Con correspondencia", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__partiallyMatched": "Parcialmente marcada", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__selectToMatch": "Seleccionar para marcar", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__selectToUnmatch": "Seleccionar para desmarcar", "@sage/xtrem-intacct-finance/enums__intacct_matching_status__unmatched": "Sin marcar", "@sage/xtrem-intacct-finance/enums__intacct_matching_type__contains": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_matching_type__equals": "Igual a", "@sage/xtrem-intacct-finance/enums__intacct_matching_type__regularExpression": "Expresión regular", "@sage/xtrem-intacct-finance/enums__intacct_record_transaction_type__deposit": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__intacct_record_transaction_type__withdrawal": "Re<PERSON><PERSON>", "@sage/xtrem-intacct-finance/enums__payment_term_discount_or_penalty_type__amount": "Importe", "@sage/xtrem-intacct-finance/enums__payment_term_discount_or_penalty_type__percentage": "Po<PERSON>entaj<PERSON>", "@sage/xtrem-intacct-finance/error": "Error", "@sage/xtrem-intacct-finance/functions__map/xtreem-mass-creation-tax-category-update-with-error": "Una vez realizada la integración, tienes que actualizar la categoría de impuesto.", "@sage/xtrem-intacct-finance/id": "Id.", "@sage/xtrem-intacct-finance/intacct_bank_account_transaction_feed_has_no_lines_to_post": "La lista de transacciones de cuenta bancaria de Sage Intacct {{intacctbankAccountTransactionFeed}} no contiene ninguna línea por contabilizar.", "@sage/xtrem-intacct-finance/intacct_document_not_found": "No se ha encontrado el documento de Sage Intacct de tipo {{type}} con id. {{sysId}}.", "@sage/xtrem-intacct-finance/intacct-address-bill-to": "Dirección de facturación", "@sage/xtrem-intacct-finance/intacct-address-pay-to": "Dirección de pago", "@sage/xtrem-intacct-finance/intacct-address-primary": "Dirección principal", "@sage/xtrem-intacct-finance/intacct-address-primary-be": "Dirección de entidad empresarial principal", "@sage/xtrem-intacct-finance/intacct-address-return-to": "Dirección de devolución", "@sage/xtrem-intacct-finance/intacct-address-ship-to": "Dirección de expedición", "@sage/xtrem-intacct-finance/intacct-address-ship-to-primary": "Dirección de expedición principal", "@sage/xtrem-intacct-finance/intacct-addresse-others": "Otra dirección", "@sage/xtrem-intacct-finance/item_extension_item_id_frozen": "No puedes cambiar el id. de artículo si está integrado con Sage Intacct.", "@sage/xtrem-intacct-finance/mapping-private-show": "Solo puedes marcar \"Combinar registros\" si el campo \"Id. de entidad MEGA\" existe y está vinculado a Sage DMO.", "@sage/xtrem-intacct-finance/menu_item__cashbook-manager": "Bank Manager", "@sage/xtrem-intacct-finance/name": "Nombre", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredClass": "Clase obligatoria", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredCustomer": "Cliente obligatorio", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredDepartement": "Departamento obligatorio", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredEmploye": "Trabajador obligatorio", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredItem": "Artí<PERSON>lo obligatorio", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredLocation": "Ubicación obligatoria", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredProject": "Proyecto obligatorio", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredSupplier": "Proveedor obligatorio", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__isRequiredTask": "Tarea obligatoria", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__statusIntacct": "Estado en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__account_extension__property__uIntacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__billToPayToContactName": "Nombre de contacto de pago o de facturación", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctDocument": "Documento en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctIntegrationState": "Estado de integración con Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctLastIntegrationDate": "Fecha de última integración en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__intacctUrl": "URL en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__shipToReturnToContactName": "Ship-to return-to contact name", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_extension__property__taxSolutionId": "Id. de solución de impuestos", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__classId": "Id. de clase", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__customerId": "Id. de cliente", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__departmentId": "Id. de departamento", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__inclusiveTax": "Con impuestos", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__intacctDocumentLine": "Línea de documento en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__itemId": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__lineAmountExcludingTaxSigned": "Importe sin impuestos de línea firmada", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__memo": "Factura rectificativa", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__projectId": "Id. de proyecto", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__taxEntries": "Entradas de impuestos", "@sage/xtrem-intacct-finance/node-extensions__accounts_payable_invoice_line_extension__property__vendorId": "<PERSON><PERSON>. de <PERSON>edor", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__mutation__createArAdvance": "<PERSON><PERSON><PERSON> anticipo de cliente", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__mutation__createArAdvance__failed": "Error al crear el anticipo de cliente", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__mutation__createArAdvance__parameter__data": "Datos", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__bankFeed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__entityId": "Id. de entidad", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctDocument": "Documento en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctIntegrationState": "Estado de integración con Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctLastIntegrationDate": "Fecha de última integración en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__intacctUrl": "URL en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__paymentMethod": "Forma de pago", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__classId": "Id. de clase", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__customerId": "Id. de cliente", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__departmentId": "Id. de departamento", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__intacctDocumentLine": "Línea de documento en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__itemId": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__projectId": "Id. de proyecto", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_advance_line_extension__property__vendorId": "<PERSON><PERSON>. de <PERSON>edor", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__mutation__updateOpenItemFromIntacct": "Actualizar vencimiento desde Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__mutation__updateOpenItemFromIntacct__failed": "Error al actualizar el vencimiento desde Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__mutation__updateOpenItemFromIntacct__parameter__arInvoiceSysId": "Id. de sistema de factura contable de cliente", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctDocument": "Documento en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctIntegrationState": "Estado de integración con Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctLastIntegrationDate": "Fecha de última integración en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__intacctUrl": "URL en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_extension__property__taxSolutionId": "Id. de solución de impuestos", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__classId": "Id. de clase", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__customerId": "Id. de cliente", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__departmentId": "Id. de departamento", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__intacctDocumentLine": "Línea de documento en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__itemId": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__lineAmountExcludingTaxSigned": "Importe sin impuestos de línea firmada", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__memo": "Factura rectificativa", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__projectId": "Id. de proyecto", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__taxEntries": "Entradas de impuestos", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_invoice_line_extension__property__vendorId": "<PERSON><PERSON>. de <PERSON>edor", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__mutation__createArPayment": "Crear pago de factura contable de cliente", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__mutation__createArPayment__failed": "Error al crear el pago de la factura contable de cliente", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__mutation__createArPayment__parameter__data": "Datos", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__bankFeed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__entityId": "Id. de entidad", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctDocument": "Documento en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctIntegrationState": "Estado de integración con Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctLastIntegrationDate": "Fecha de última integración en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__intacctUrl": "URL en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__paymentMethod": "Forma de pago", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__arInvoiceRecordNo": "Número de registro de factura contable de cliente", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__intacctDocumentLine": "Línea de documento en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__accounts_receivable_payment_line_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__accounts-payable-invoice-extension__accounts_payable_invoice_already_sent": "La factura contable de proveedor ya se ha enviado a Sage Intacct. Estado: {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-advance-extension__accounts_receivable_advance_already_sent": "El anticipo de cliente ya se ha enviado a Sage Intacct. Estado: {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-invoice-extension__accounts_receivable_invoice_already_sent": "La factura contable de cliente ya se ha enviado a Sage Intacct. Estado: {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-payment-extension__accounts_receivable_payment_already_sent": "El pago de cliente ya se ha enviado a Sage Intacct. Estado: {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__bulkMutation__updateRecordNoOnEmployee": "Actualizar número de registro en trabajador", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__bulkMutation__updateRecordNoOnEmployee__failed": "Error al actualizar el número de registro en el trabajador", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__intacctProject": "Proyecto en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__statusIntacct": "Estado en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__attribute_extension__property__uIntacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__intacctGlAccount": "Cuenta general en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__location": "Ubicación", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__megaEntityId": "Id. de entidad MEGA", "@sage/xtrem-intacct-finance/node-extensions__bank_account_extension__property__statusIntacct": "Estado en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__business_entity_address_extension__property__intacctBusinessEntityAddress": "Dirección de entidad empresarial en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__business_entity_address_extension__property__name": "Nombre", "@sage/xtrem-intacct-finance/node-extensions__business_entity_contact_extension__property__intacctPrintAs": "Imprimir como en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold": "Sincronizar comprobación de la sociedad", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold__failed": "Error al sincronizar la comprobación de la sociedad", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold__parameter__companySysId": "Id. de sistema de sociedad", "@sage/xtrem-intacct-finance/node-extensions__company_extension__asyncMutation__syncCompanyOnHold__parameter__isAllCompanies": "Todas las sociedades", "@sage/xtrem-intacct-finance/node-extensions__company_extension__property__doApPosting": "Contabilización de documento de proveedor", "@sage/xtrem-intacct-finance/node-extensions__company_extension__property__doArPosting": "Contabilización de documento de cliente", "@sage/xtrem-intacct-finance/node-extensions__company_extension__property__doUpdateArAmountPaid": "Actualizar importe de cliente pagado", "@sage/xtrem-intacct-finance/node-extensions__customer_extension__property__intacctCustomer": "Cliente en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__intacctObject": "Objeto en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__statusIntacct": "Estado en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_extension__property__uIntacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__dimension_type_extension__property__intacctObject": "Objeto en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__mutation__generateSmartEventFile": "Generar archivo de Smart Event", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__mutation__generateSmartEventFile__failed": "Error al generar el archivo de Smart Event", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__property__customPackageFile": "Archivo de paquete personalizado", "@sage/xtrem-intacct-finance/node-extensions__intacct_extension__property__generateSmartEvent": "Generar Smart Event", "@sage/xtrem-intacct-finance/node-extensions__item_extension__property__id": "Id.", "@sage/xtrem-intacct-finance/node-extensions__item_extension__property__intacctItem": "Artículo en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__entityId": "Id. de entidad", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctDocument": "Documento en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctIntegrationState": "Estado de integración con Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctLastIntegrationDate": "Fecha de última integración en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__intacctUrl": "URL en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__isActive": "Activo", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__taxImplications": "Implicaciones fiscales", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_extension__property__taxSolutionId": "Id. de solución de impuestos", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_dimension_extension__property__intacctDimension": "Sección en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__absoluteTransactionAmount": "Importe de transacción absoluto", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__allocationSplit": "División de asignación", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__excludeRecord": "Excluir registro", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctDimensionSplit": "División de sección en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctDocumentLine": "Línea de documento en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__intacctSign": "Signo en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__isActive": "Activo", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__locationWhenNoSplit": "Ubicación sin división", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__journal_entry_line_extension__property__taxEntries": "Entradas de impuestos", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__statusIntacct": "Estado en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal_extension__property__uIntacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__journal-entry-extension__journal_entry_already_sent": "El asiento ya se ha enviado a Sage Intacct. Estado: {{intacctIntegrationState}}", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountAmount": "Importe de descuento", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountDate": "Fecha de descuento", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountFrom": "Descuento desde", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountFromIntacct": "Descuento desde Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountType": "Tipo de descuento", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__discountTypeIntacct": "Tipo de descuento en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__dueFromIntacct": "Inicio de vencimiento en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__isIntacct": "Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__penaltyAmount": "Importe de recargo", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__penaltyType": "Tipo de recargo", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__penaltyTypeIntacct": "Tipo de recargo en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__statusIntacct": "Estado en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__payment_term_extension__property__uIntacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__supplier_extension__property__intacctSupplier": "Proveed<PERSON> en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__supplier_extension__property__paymentMethodSelect": "Selección de forma de pago", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctAccount": "Cuenta de Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctSecondaryExternalReference": "Referencia externa secundaria en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctSolutionId": "Id. de solución en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__intacctTaxType": "Tipo de impuesto en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__isIntacct": "Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__isIntacctReverseCharge": "Inversión de sujeto pasivo en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__isUpdateFromIntacct": "Actualizar desde Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__rate": "Tipo impositivo", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__statusIntacct": "Estado en Sage Intacct", "@sage/xtrem-intacct-finance/node-extensions__tax_extension__property__uIntacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__company_customer_on_hold_control": "No puedes actualizar la comprobación del cliente si Sage Intacct está activo.", "@sage/xtrem-intacct-finance/nodes__company_get_company_ref_not_mapped": "La sociedad {{intacctCompanyRef}} de Sage Intacct no se puede mapear.", "@sage/xtrem-intacct-finance/nodes__customer_credit_limit_control": "No puedes actualizar el límite de crédito si Sage Intacct está activo.", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument": "Reintentar documento contable", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument__failed": "Error al reintentar el documento contable", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument__parameter__financeDocumentSysId": "Id. de sistema de documento contable", "@sage/xtrem-intacct-finance/nodes__finance_listener__mutation__retryFinanceDocument__parameter__financeTransaction": "Transacción contable", "@sage/xtrem-intacct-finance/nodes__finance_listener__node_name": "Proceso de escucha de contabilidad", "@sage/xtrem-intacct-finance/nodes__finance-listener__already_success": "El documento contable {{number}} ya está sincronizado.", "@sage/xtrem-intacct-finance/nodes__finance-listener__document_found_on_intacct_updated": "El documento {{NUMBER}}se ha encontrado en Sage Intacct. Estado actualizado.", "@sage/xtrem-intacct-finance/nodes__finance-listener__document_not_found_on_intacct_posted_waiting": "El documento no se ha encontrado en Sage Intacct. Documento contabilizado y esperando respuesta de Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__finance-listener__retry": "Procesando", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__node_name": "Factura contable de proveedor en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__billBySupplier": "Proveedor <PERSON>dor", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__billToPayToContactName": "Nombre de contacto de pago o de facturación", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__document": "Documento", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__entityId": "Id. de entidad", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__shipToReturnToContactName": "Nombre de contacto destinatario o receptor de devolución", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice__property__taxSolutionId": "Id. de solución de impuestos", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__node_name": "Línea de factura contable de proveedor en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__classId": "Id. de clase", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__customerId": "Id. de cliente", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__departmentId": "Id. de departamento", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__documentLine": "Línea de documento", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__employeeId": "<PERSON><PERSON><PERSON> <PERSON> trabajador", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__inclusiveTax": "Con impuestos", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__itemId": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__lineAmountExcludingTaxSigned": "Importe sin impuestos de línea firmada", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__memo": "Factura rectificativa", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__projectId": "Id. de proyecto", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__signedAmountExcludingTax": "Importe sin impuestos con signo", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__taskId": "Id. de tarea", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__taxEntries": "Entradas de impuestos", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_payable_invoice_line__property__vendorId": "<PERSON><PERSON>. de <PERSON>edor", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__node_name": "Anticipo de cliente en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__bankFeed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__document": "Documento", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__entityId": "Id. de entidad", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance__property__paymentMethod": "Forma de pago", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__node_name": "Línea de anticipo de cliente en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__classId": "Id. de clase", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__customerId": "Id. de cliente", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__departmentId": "Id. de departamento", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__documentLine": "Línea de documento", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__itemId": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__projectId": "Id. de proyecto", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_advance_line__property__vendorId": "<PERSON><PERSON>. de <PERSON>edor", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__node_name": "Factura contable de cliente en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__billToCustomer": "Cliente facturado", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__document": "Documento", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__entityId": "Id. de entidad", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice__property__taxSolutionId": "Id. de solución de impuestos", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__node_name": "Línea de factura contable de cliente en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__classId": "Id. de clase", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__customerId": "Id. de cliente", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__departmentId": "Id. de departamento", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__documentLine": "Línea de documento", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__employeeId": "<PERSON><PERSON><PERSON> <PERSON> trabajador", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__inclusiveTax": "Con impuestos", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__itemId": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__lineAmountExcludingTaxSigned": "Importe sin impuestos de línea firmada", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__memo": "Factura rectificativa", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__projectId": "Id. de proyecto", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__signedAmountExcludingTax": "Importe sin impuestos con signo", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__taskId": "Id. de tarea", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__taxEntries": "Entradas de impuestos", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_invoice_line__property__vendorId": "<PERSON><PERSON>. de <PERSON>edor", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__node_name": "Pago de cliente en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__bankFeed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__document": "Documento", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__entityId": "Id. de entidad", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment__property__paymentMethod": "Forma de pago", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__node_name": "Línea de pago de cliente en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__property__arInvoiceRecordNo": "Número de registro de factura contable de cliente", "@sage/xtrem-intacct-finance/nodes__intacct_accounts_receivable_payment_line__property__documentLine": "Línea de documento", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__mutation__bulkSave": "Guardar en masa", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__mutation__bulkSave__failed": "Error al guardar en masa", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__mutation__bulkSave__parameter__intacctBankAccountMatchings": "Sage Intacct bank account matchings", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__node_name": "Correspondencia de cuentas bancarias de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__account": "C<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__bankAccount": "Cuenta bancaria", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__keyword": "Palabra clave", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__location": "Ubicación", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__priority": "Prioridad", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__tax": "Impuesto", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__transactionType": "Tipo de transacción", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__type": "Tipo", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__property__updateAccountTaxManagement": "Actualizar gestión de impuestos de cuenta", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctArInvoice": "Consultar factura contable de cliente en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctArInvoice__failed": "Error al consultar la factura contable de cliente en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctArInvoice__parameter__parameters": "Parámetros", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctCustomers": "Consultar clientes en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctCustomers__failed": "Error al consultar los clientes en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument": "Consultar documento en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument__failed": "Error al consultar el documento en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument__parameter__documentType": "Tipo de documento", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__query__queryIntacctDocument__parameter__parameters": "Parámetros", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__wrong_account_tax_management": "Solo puedes seleccionar una cuenta vinculada a una gestión de impuestos de tipo \"Otra\" o \"Sin impuestos\".", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared": "Set cleared", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared__failed": "Set cleared failed.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared__parameter__cleared": "Cleared", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__mutation__setCleared__parameter__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__node_name": "Lista de transacciones de cuentas bancarias de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__account": "C<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__accountFeedKey": "Clave de importación de cuenta", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__accountReconKey": "Clave de conciliación bancaria", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__accountType": "Tipo de cuenta", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__amount": "Importe", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__amountToMatch": "Importe por marcar", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__arMatch": "Conciliación de documento de cliente", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__bankAccount": "Cuenta bancaria", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__cleared": "Conciliada", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__computedAttributes": "Atributos calculados", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__currency": "Divisa", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__description": "Descripción", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__documentNumber": "Número de documento", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__documentType": "Tipo de documento", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__entity": "Entidad", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__entityName": "Nombre de entidad", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__feedType": "Tipo de importación", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentCreatedNumber": "Finance document created number", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentCreatedSysId": "Finance document created system ID", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentGenerationErrorMessage": "Mensaje de error de generación de documento contable", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeDocumentType": "Tipo de documento contable", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeIntegrationApp": "Aplicación de integración contable", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeIntegrationAppRecordId": "Id. de registro de aplicación de integración contable", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__financeIntegrationAppUrl": "URL de aplicación de integración contable", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__importSession": "Importar sesión", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__intacctCleared": "Saldada en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__internalFinanceIntegrationStatus": "Estado de integración de contabilidad interna", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__journalEntryNumber": "Número de asiento", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__jsonArInvoices": "Facturas contables de cliente en formato JSON", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__lines": "Líneas", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__payee": "Beneficiario", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__paymentDate": "Fecha de pago", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__paymentMethod": "Forma de pago", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__payToCustomerId": "Id. de cliente", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__payToCustomerName": "Nombre de cliente", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__postingDate": "Fecha de contabilización", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__receiptDate": "Fecha de recepción", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__reconcilitationDate": "Fecha de conciliación", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__targetDocumentType": "Tipo de documento de destino", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__transactionId": "Id. de transacción", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__property__transactionType": "Tipo de transacción", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine": "Get match line", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine__failed": "Get match line failed.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine__parameter__matchingRules": "Reglas de correspondencia", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed__query__getMatchLine__parameter__transactionFeed": "Lista de transacciones", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__node_name": "Línea de lista de transacciones de cuentas bancarias de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__account": "C<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__amount": "Importe", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__computedAttributes": "Atributos calculados", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__document": "Documento", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__intacctJournalEntryBatchNo": "Número de tanda de asiento en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__intacctJournalEntryRecordNo": "N.º de registro de asiento en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__intacctJournalEntryUrl": "URL de asiento en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__location": "Ubicación", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__status": "Estado", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__tax": "Impuesto", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__taxAmount": "Importe de impuesto", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line__property__taxRate": "Tipo impositivo", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__node_name": "Sección de línea de lista de transacciones de cuentas bancarias de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_bank_account_transaction_feed_line_dimension__property__originLine": "<PERSON><PERSON>ea de origen", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__mutation__serviceOptionChange": "Cambiar opción de servicio", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__mutation__serviceOptionChange__failed": "Error al cambiar la opción de servicio", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__node_name": "Sage Intacct Cash Book Management", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__query__isServiceOptionActiveFunction": "Service option active function", "@sage/xtrem-intacct-finance/nodes__intacct_cash_book_management__query__isServiceOptionActiveFunction__failed": "Is service option active function failed.", "@sage/xtrem-intacct-finance/nodes__intacct_contact__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_contact__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_contact__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_contact__node_name": "Contacto en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__contact": "Contacto", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__name": "Nombre", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__prefix": "Prefijo", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__printAs": "Imprimir como", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__status": "Estado", "@sage/xtrem-intacct-finance/nodes__intacct_contact__property__sysIdLink": "Vínculo a id. de sistema", "@sage/xtrem-intacct-finance/nodes__intacct_customer__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_customer__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_customer__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_customer__node_name": "Cliente en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__billToAddress": "Dirección de facturación", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__parent": "Principal", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__shipToAddress": "Dirección de expedición", "@sage/xtrem-intacct-finance/nodes__intacct_customer__property__status": "Estado", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__node_name": "<PERSON><PERSON><PERSON> o proveedor en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__contactList": "Lista de contactos", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__hideDisplayContact": "Ocultar/Mostrar contacto", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__primaryContact": "Contacto principal", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__status": "Estado", "@sage/xtrem-intacct-finance/nodes__intacct_customer_supplier__property__sysIdLink": "Vínculo a id. de sistema", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__currencies_do_not_match": "La divisa de la cuenta bancaria debe ser la misma que la de la planta financiera de la cuenta.", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__node_name": "Sesión de importación en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__bankAccount": "Cuenta bancaria", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__dateCreation": "Fecha de creación", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__description": "Descripción", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__linesImported": "Líneas importadas", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__mapObject": "Mapeo de objeto", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__numberOfLinesToImport": "Número de líneas que importar", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__queryParameters": "Configuración de consulta", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__status": "Estado", "@sage/xtrem-intacct-finance/nodes__intacct_import_session__property__transactionFeed": "Lista de transacciones", "@sage/xtrem-intacct-finance/nodes__intacct_item__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_item__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_item__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_item__node_name": "Artículo en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_item__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_item__property__status": "Estado", "@sage/xtrem-intacct-finance/nodes__intacct_item__property__type": "Tipo", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__node_name": "Asiento en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__document": "Documento", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__entityId": "Id. de entidad", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__taxImplications": "Implicaciones fiscales", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry__property__taxSolutionId": "Id. de solución de impuestos", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__node_name": "Apunte en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__absoluteTransactionAmount": "Importe de transacción absoluto", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__allocationSplit": "División de asignación", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__dimensionSplit": "División de sección", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__documentLine": "Línea de documento", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__excludeRecord": "Excluir registro", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__locationWhenNoSplit": "Ubicación sin división", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__sign": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_journal_entry_line__property__taxEntries": "Entradas de impuestos", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct": "Eliminar en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct__failed": "Error al eliminar en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct__parameter__intacctNode": "Nodo en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__deleteIntacct__parameter__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__synchronizeNode": "<PERSON><PERSON><PERSON><PERSON><PERSON> nodo", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__synchronizeNode__failed": "Error al sincronizar el nodo", "@sage/xtrem-intacct-finance/nodes__intacct_listener__asyncMutation__synchronizeNode__parameter__intacctNode": "Nodo en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_listener__node_name": "Proceso de escucha en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob": "<PERSON><PERSON><PERSON> tareas en masa en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob__failed": "Error al crear las tareas en masa en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob__parameter__intacctName": "Nombre en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__intacctMassCreationJob__parameter__type": "Tipo", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__xtreemMassCreationJob": "Xtreem mass creation job", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__xtreemMassCreationJob__failed": "Xtreem mass creation job failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__asyncMutation__xtreemMassCreationJob__parameter__data": "Datos", "@sage/xtrem-intacct-finance/nodes__intacct_map__bulkMutation__updateCustomMapping": "Actualizar mapeo personalizado", "@sage/xtrem-intacct-finance/nodes__intacct_map__bulkMutation__updateCustomMapping__failed": "Error al actualizar el mapeo personalizado", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__createUpdateAllIntacct": "<PERSON><PERSON><PERSON> o actualizar en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__createUpdateAllIntacct__failed": "Error al crear o actualizar en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__createUpdateAllIntacct__parameter__intacctName": "Nombre en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem": "Delete Xtrem", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__failed": "Delete xtrem failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__parameter__intacctIdValue": "Valor de id. en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__parameter__intacctName": "Nombre en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__deleteXtrem__parameter__xtremSysId": "Xtrem system ID", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl": "Obtener URL de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl__failed": "Error al obtener la URL de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl__parameter___id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getIntacctUrl__parameter__nodeName": "Nombre de nodo", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getStructure": "Obtener estructura", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getStructure__failed": "Error al obtener la estructura", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__getStructure__parameter__content": "Contenido", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__writeStructure": "Escribir estructura", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__writeStructure__failed": "Error al escribir la estructura", "@sage/xtrem-intacct-finance/nodes__intacct_map__mutation__writeStructure__parameter__object": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__node_name": "Mapeo en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__additionnalLink": "Vínculo adicional", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__customRelationMapping": "Mapeo de relaciones personalizado", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__editableFields": "Campos editables", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__intacctDescription": "Descripción en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__intacctFilter": "Filtro de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__isPrivateShow": "Combinar registros", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__isSmartEvent": "Smart Event", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__lines": "Líneas", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__relationMapping": "Mapeo de relaciones", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__setupId": "Id. de parametrización", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__specificFields": "Campos específicos", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__uRelationMapping": "Mapeo de relaciones", "@sage/xtrem-intacct-finance/nodes__intacct_map__property__xtremObject": "Xtrem object", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__compareObject": "Comparar objeto", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__compareObject__parameter__node": "Nodo", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__compareObject__parameter__sysId": "Id. de siste<PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getAvailableXtremObjectList": "Get available Xtrem object list", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getAvailableXtremObjectList__failed": "Get available xtrem object list failed.", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct": "Obtener datos de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__failed": "Error al obtener los datos de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__filters": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__maxData": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__name": "Nombre", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getDataIntacct__parameter__transaction": "Transacción", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getIntacctTransactionsList": "Obtener lista de transacciones de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getIntacctTransactionsList__failed": "Error al obtener la lista de transacciones de Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject": "Obtener objeto", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__failed": "Error al obtener el objeto", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__parameter__docparid": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__parameter__isUpdate": "Actualización", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getObject__parameter__object": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getPropertiesList": "Obtener lista de propiedades", "@sage/xtrem-intacct-finance/nodes__intacct_map__query__getPropertiesList__parameter__tableName": "Nombre de tabla", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__node_name": "Proveed<PERSON> en Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__parent": "Principal", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__payToAddress": "Dirección de pago", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__returnToAddress": "Dirección de devolución", "@sage/xtrem-intacct-finance/nodes__intacct_supplier__property__status": "Estado", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_finance_state__node_name": "Estado contable de sincronización con Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_finance_state__property__externalIntegration": "Integración externa", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_state__node_name": "Estado de sincronización con Sage Intacct", "@sage/xtrem-intacct-finance/nodes__intacct_synchronization_state__property__entityId": "Id. de entidad", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__site_legislations_and_coa_legislation_dont_match": "La legislación de la planta debe ser la misma que la legislación del plan de cuentas.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_legislation_and_coa_legislation_dont_match": "La legislación del impuesto debe ser la misma que la legislación del plan de cuentas.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_management_should_be_excluding_taxes": "No puedes definir impuestos en una cuenta no sujeta a impuestos.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_mandatory": "Introduce un impuesto.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_not_allowed": "El impuesto debe estar en blanco.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction_feed__site_legislation_and_coa_legislation_dont_match": "La legislación de la planta debe ser la misma que la legislación del plan de cuentas.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction_feed__tax_legislation_and_coa_legislation_dont_match": "La legislación del impuesto debe ser la misma que la legislación del plan de cuentas.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed__tax_mandatory": "Introduce un impuesto.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed__tax_not_allowed": "El impuesto debe estar en blanco.", "@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed/bank-manager-import-fail": "Sage DMO Bank Manager: ha habido un error en la importación ({{filters}}). \n {{error}}.", "@sage/xtrem-intacct-finance/nodes__intacct-listener/delete-success": "Eliminado", "@sage/xtrem-intacct-finance/nodes__intacct-listener/intacct-time-out": "El tiempo de espera ha finalizado sin respuesta de Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__intacct-listener/not-implemented": "Sin implementar: {{change}}:{{object}}  ", "@sage/xtrem-intacct-finance/nodes__intacct-listener/synchronize-node-finish": "Estado de sincronización:{{state}}", "@sage/xtrem-intacct-finance/nodes__intacct-listener/synchronize-node-start": "La sincronización se ha iniciado.", "@sage/xtrem-intacct-finance/nodes__intacct-map/create-update-xtrem": "Demasiadas líneas procedentes de Sage Intacct: {{lenght}}. Id. en Sage Intacct: {{intacctIdValue}}", "@sage/xtrem-intacct-finance/nodes__intacct-map/desynchronized_not_exist": "Desincronización de {{node}}: no se ha encontrado \"{{id}}\" para el identificador \"{{intacctName}}\" en Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__intacct-map/file-already-exist": "El archivo \"{{object}}\" ya existe.", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-instance-query-to-many-lines": "Demasiadas líneas procedentes de Sage Intacct: {{countIntacctData}}. \n Filtra la consulta e inténtalo de nuevo.", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end": "La creación masiva de \"{{intacctName}}\" ha finalizado a las {{dateTime}}. \n Total: {{totalCount}}: errores: {{errorCount}}; confirmaciones: {{successCount}} ", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end-with-errors-phase": "La creación ha finalizado con errores.", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end-without-errors-phase": "La creación ha finalizado.", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-fail": "Ha habido un error en la creación masiva en Sage Intacct ({{intacctName}}).", "@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-number-of-lines": "Número de líneas por crear o actualizar: {{numberOfLines}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-desynchronization": "El objeto está sincronizado.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-intacct-data": "No hay datos de Sage Intacct para el objeto {{intacctName}}, donde {{intacctIDField}} es {{intacctIdValue}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-intacct-instance": "No hay ninguna instancia de Sage Intacct activada.", "@sage/xtrem-intacct-finance/nodes__intacct-map/no-map-instance": "El archivo no se puede crear porque el objeto {{object}} no existe en Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__intacct-map/nothing-to-write": "El archivo \"{{object}}\" no se puede escribir.", "@sage/xtrem-intacct-finance/nodes__intacct-map/to-many-lines": "Líneas generadas: {{numberOflines}}", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-end": "Mass creation of {{intacctName}} as finish at {{dateTime}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-number-of-lines": "Número de líneas por crear o actualizar: {{numberOfLines}}", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-start": "Mass creation of {{intacctName}} as started at {{dateTime}}.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtreem-mass-creation-tax-category-update": "\n Una vez realizada la integración, tienes que actualizar la categoría de impuesto.", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-created-updated": "Sage DMO {{created}}: {{id}} ", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-created-updated-total": "Registros creados: {{created}}/n Registros actualizados: {{updated}}/n Registros con errores: {{error}}", "@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-query-to-many-lines": "Demasiadas líneas procedentes de Sage DMO: {{numberOfNodes}}.\n Filtra la consulta e inténtalo de nuevo.", "@sage/xtrem-intacct-finance/nodes__map_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__map_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__map_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__map_line__node_name": "Mapeo de línea", "@sage/xtrem-intacct-finance/nodes__map_line__property__collectionName": "Nombre de colección", "@sage/xtrem-intacct-finance/nodes__map_line__property__line": "Lín<PERSON>", "@sage/xtrem-intacct-finance/nodes__map_line__property__mapHeader": "Mapeo de cabecera", "@sage/xtrem-intacct-finance/nodes__map_line__property__propertyHeader": "<PERSON><PERSON><PERSON><PERSON> de propiedad", "@sage/xtrem-intacct-finance/nodes__map_line__property__propertyLine": "<PERSON><PERSON><PERSON> propied<PERSON>", "@sage/xtrem-intacct-finance/nodes__map_property__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-intacct-finance/nodes__map_property__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-intacct-finance/nodes__map_property__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-intacct-finance/nodes__map_property__node_name": "Mapeo de propiedad", "@sage/xtrem-intacct-finance/nodes__map_property__property__map": "Mapear", "@sage/xtrem-intacct-finance/nodes__map_property__property__name": "Nombre", "@sage/xtrem-intacct-finance/nodes__map_property__property__type": "Tipo", "@sage/xtrem-intacct-finance/nodes__payment_term_discount_amount_percentage_error": "El importe de descuento debe ser inferior al 100 %.", "@sage/xtrem-intacct-finance/nodes__payment_term_discount_mandatory": "El tipo y el importe de descuento son obligatorios cuando se introduce una fecha de descuento.", "@sage/xtrem-intacct-finance/nodes__payment_term_penalty_amount_percentage_error": "El importe de recargo debe ser inferior al 100 %.", "@sage/xtrem-intacct-finance/nodes__tax_extension__deletion_forbidden": "No puedes eliminar este registro. Está vinculado a Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_not_allowed": "No puedes crear, editar o eliminar este registro. Está vinculado a Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_active_is_forbidden": "No puedes habilitar este registro. Está vinculado a Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_country_is_forbidden": "No puedes editar el país.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_primary_external_reference_is_forbidden": "No puedes editar la referencia externa principal.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_reverse_charge_is_forbidden": "No puedes editar la inversión de sujeto pasivo.", "@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_tax_type_is_forbidden": "No puedes editar el tipo de impuesto. Está vinculado a Sage Intacct.", "@sage/xtrem-intacct-finance/nodes__tax_extension__negative_rate_not_allowed": "No puedes introducir un tipo negativo.", "@sage/xtrem-intacct-finance/package__name": "Integración contable con Sage Intacct", "@sage/xtrem-intacct-finance/page__intacct_mapping_create_all_in_intacct": "<PERSON><PERSON><PERSON> todo en Sage Intacct", "@sage/xtrem-intacct-finance/page__intacct_mapping_create_all_in_intacct_manufacturing": "<PERSON><PERSON><PERSON> todo en Sage DMO", "@sage/xtrem-intacct-finance/page__intacct-map/xtrem-instance-error": "No se ha guardado el id. de Sage Intacct {{intacctId}}.", "@sage/xtrem-intacct-finance/page_extensions__common__warning": "Aviso", "@sage/xtrem-intacct-finance/page_extensions__there_are_ap_invoices_not_posted": "Hay facturas contables de proveedor que todavía no se han contabilizado en el libro mayor.", "@sage/xtrem-intacct-finance/page_extensions__there_are_ar_invoices_not_posted": "Hay facturas contables de cliente que todavía no se han contabilizado en el libro mayor.", "@sage/xtrem-intacct-finance/page-extensions__business_entity_customer_extension__intacctUrl____title": "URL en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__addresses____columns__helperText__intacctBusinessEntityAddress___id": "Sincronizando", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__addresses____columns__title__intacctBusinessEntityAddress___id": "Actualizar", "@sage/xtrem-intacct-finance/page-extensions__business_entity_extension__addresses____columns__title__intacctBusinessEntityAddress__state": "Estado de integración", "@sage/xtrem-intacct-finance/page-extensions__business_entity_supplier_extension__intacctUrl____title": "URL en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__company_extension__creditLimitBlock____title": "Límite de crédito", "@sage/xtrem-intacct-finance/page-extensions__company_extension__doUpdateArAmountPaid____title": "Actualizar importe pagado en informes de venta", "@sage/xtrem-intacct-finance/page-extensions__company_extension__managementSection____title": "Gestión", "@sage/xtrem-intacct-finance/page-extensions__company_extension__synchronize____helperText": "Sincronizando...", "@sage/xtrem-intacct-finance/page-extensions__company_extension__synchronize____title": "Actualizar", "@sage/xtrem-intacct-finance/page-extensions__customer_extension____navigationPanel__listItem__line16__title": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension____navigationPanel__listItem__line17__title": "Estado de integración con Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__addresses____columns__helperText___id": "Sincronizando", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__addresses____columns__title___id": "Actualizar", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__addresses____columns__title__intacctBusinessEntityAddress__state": "Estado de integración", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctIntegrationState____title": "Estado de integración", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__intacctUrlLink____title": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__synchronize____helperText": "Sincronizando", "@sage/xtrem-intacct-finance/page-extensions__customer_extension__synchronize____title": "Actualizar", "@sage/xtrem-intacct-finance/page-extensions__dimension_type_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__dimension_type_extension__intacctObject____title": "Objeto en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__customPackageFile____title": "Archivo de paquete personalizado", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__customPackageSection____title": "Paquete personalizado", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__canCreate": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__canDelete": "Eliminar", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__canUpdate": "Actualizar", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__intacctDescription": "Nombre en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__intacctName": "Nombre en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__nodeFactory__name": "Objeto en Sage DMO", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__synchronisationWay": "Sentido de sincronización", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__synchronizationDirection": "Sincronización", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____columns__title__xtremObject": "Objeto en Sage DMO", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__generateSmartEvent____title": "Smart Events por generar", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__objectSelect____title": "Objeto en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__save____title": "Guardar", "@sage/xtrem-intacct-finance/page-extensions__intacct_extension__smartEventBlock____title": "Smart Events ", "@sage/xtrem-intacct-finance/page-extensions__item_extension____navigationPanel__listItem__intacctId__title": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension____navigationPanel__listItem__intacctState__title": "Estado de integración con Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctId____title": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctIntegrationState____title": "Estado de integración", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctUrl____title": "URL en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__intacctUrlLink____title": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__item_extension__synchronize____helperText": "Sincronizando...", "@sage/xtrem-intacct-finance/page-extensions__item_extension__synchronize____title": "Sincronizar", "@sage/xtrem-intacct-finance/page-extensions__journal_extension__recordNo____title": "Número de registro", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__blockDiscount____title": "Descuento", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__blockPenalty____title": "Recargo", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__discountDate____title": "Día", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__discountFrom____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__discountType____title": "Tipo", "@sage/xtrem-intacct-finance/page-extensions__payment_term_extension__penaltyType____title": "Tipo", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension____navigationPanel__listItem__line12__title": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension____navigationPanel__listItem__line13__title": "Estado de integración con Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__addresses____columns__helperText___id": "Sincronizando", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__addresses____columns__title___id": "Actualizar", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__addresses____columns__title__intacctBusinessEntityAddress__state": "Estado de integración", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctIntegrationState____title": "Estado de integración", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__intacctUrlLink____title": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__paymentMethodSelect____title": "Forma de pago", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__synchronize____helperText": "Sincronizando", "@sage/xtrem-intacct-finance/page-extensions__supplier_extension__synchronize____title": "Actualizar", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__country____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__country__name": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__isIntacctReverseCharge": "Inversión de sujeto pasivo en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__primaryExternalReference": "Referencia externa principal", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____columns__title__taxCategory__name": "Categoría de impuesto", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__intacctSecondaryExternalReference____title": "Referencia externa secundaria", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isActive____title": "Activo", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isIntacct____title": "Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isIntacctReverseCharge____title": "Inversión de sujeto pasivo en Sage Intacct", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__isReverseCharge____title": "Inversión de sujeto pasivo", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__primaryExternalReference____title": "Referencia externa principal", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__secondaryExternalReference____title": "Referencia externa secundaria", "@sage/xtrem-intacct-finance/page-extensions__tax_extension__type____title": "Tipo de impuesto", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching____title": "Correspondencia de factura contable de cliente", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__addAdvanceDimensionAttribute____title": "Secciones", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__columns__account__name__title": "Gestión de impuestos", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__account__name": "C<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__customerId": "Id. de cliente", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__customerName": "Nombre de cliente", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__errorMessage": "Men<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__intacctArDocumentID": "Anticipo de cliente en Sage Intacct", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__paymentDate": "Fecha de pago", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__paymentMethod": "Forma de pago", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__receiptDate": "Fecha de recepción", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____columns__title__totalAmount": "Importe total", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____dropdownActions__title": "Eliminar línea", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__advanceToCreate____title": "Generación de anticipo de cliente", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__amountToMatch____title": "Importe por marcar", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__bankAccount____title": "Cuenta bancaria", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__createArAdvance____title": "<PERSON><PERSON><PERSON> anticipo de cliente", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__createArPayment____title": "Crear pago de factura contable de cliente", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__customer_id_was_found": "Se ha encontrado el id. del cliente {{customerId}}.", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__customer_name_was_found": "Se ha encontrado el nombre del cliente {{customerName}}.", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoice_number_was_found": "Número de factura {{invoiceNumber}} encontrado", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__amountToMatch": "Importe por marcar", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__customerId": "Id. de cliente", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__customerName": "Cliente", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__date": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__entityId": "Id. de entidad", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__invoiceNo": "Número de factura", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__referenceNumber": "Número de referencia", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__term__name": "Condiciones de pago", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__totalAmount": "Importe total", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__totalDue": "Importe pendiente", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____columns__title__totalPaid": "Total pagado", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoices____title": "Facturas", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__mainBlock____title": "General", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__mainSection____title": "General", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__matchingReason____title": "Motivo de correspondencia", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__ok____title": "Aceptar", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__arPayment": "Pago de factura contable de cliente", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__customerId": "Id. de cliente", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__customerName": "Cliente", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__intacctArPayment": "Pago de factura contable de cliente en Sage Intacct", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__paymentDate": "Fecha de pago", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__paymentMethod": "Forma de pago", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__receiptDate": "Fecha de recepción", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____columns__title__totalAmount": "Importe total", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentGeneration____title": "Generación de pago", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__customerId": "Id. de cliente", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__customerName": "Nombre de cliente", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__errorMessage": "Men<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__intacctArDocumentID": "Pago de factura contable de cliente en Sage Intacct", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__paymentDate": "Fecha de pago", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__paymentMethod": "Forma de pago", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__receiptDate": "Fecha de recepción", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____columns__title__totalAmount": "Importe total", "@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__paymentToCreate____title": "Generación de pago", "@sage/xtrem-intacct-finance/pages__bank_account____navigationPanel__listItem__line3__title": "Id. de entidad", "@sage/xtrem-intacct-finance/pages__bank_account____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__bank_account____objectTypePlural": "Cuentas bancarias", "@sage/xtrem-intacct-finance/pages__bank_account____objectTypeSingular": "Cuenta bancaria", "@sage/xtrem-intacct-finance/pages__bank_account____title": "Cuenta bancaria", "@sage/xtrem-intacct-finance/pages__bank_account___id____title": "Id.", "@sage/xtrem-intacct-finance/pages__bank_account__account____title": "C<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__bank_account__currency____columns__title__id": "Código ISO 4217", "@sage/xtrem-intacct-finance/pages__bank_account__currency____columns__title__name": "Nombre", "@sage/xtrem-intacct-finance/pages__bank_account__currency____columns__title__symbol": "Símbolo", "@sage/xtrem-intacct-finance/pages__bank_account__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-intacct-finance/pages__bank_account__currency____title": "Divisa", "@sage/xtrem-intacct-finance/pages__bank_account__id____title": "Id.", "@sage/xtrem-intacct-finance/pages__bank_account__isActive____title": "Activa", "@sage/xtrem-intacct-finance/pages__bank_account__mainBlock____title": "Información de cuenta bancaria", "@sage/xtrem-intacct-finance/pages__bank_account__mainSection____title": "General", "@sage/xtrem-intacct-finance/pages__bank_account__megaEntityId____title": "Id. de entidad", "@sage/xtrem-intacct-finance/pages__bank_account__name____title": "Nombre", "@sage/xtrem-intacct-finance/pages__delete_page_dialog_content": "¿Quieres eliminar este registro?", "@sage/xtrem-intacct-finance/pages__delete_page_dialog_title": "Confirmar eliminación", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog____objectTypePlural": "Reglas de correspondencias de cuentas bancarias de Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog____objectTypeSingular": "Regla de correspondencias de cuentas bancarias de Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog____title": "Reglas de correspondencia", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__addRule____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__apply____title": "Aplicar reglas de correspondencia", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__mainSection____title": "General", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__account__name__title": "Gestión de impuestos", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__account__name__title__5": "Legislación", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__location__name__title__2": "Nombre", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__columns__tax__name__title__2": "Nombre", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title___id": "Id.", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__account__name": "C<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__bankAccount__id": "Cuenta bancaria", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__isModified": "Modificada", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__keyword": "Palabra clave", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__priority": "Prioridad", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__tax__name": "Impuesto", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__transactionType": "Tipo de transacción", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____columns__title__type": "Tipo", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____dropdownActions__title": "Eliminar", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____dropdownActions__title__2": "Guardar", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____dropdownActions__title__3": "Secciones", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__matchingRules____title": "Reglas de correspondencia", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_matching_dialog__save____title": "Guardar", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____navigationPanel__listItem__line2__title": "Id.", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____navigationPanel__listItem__title__title": "Descripción", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____objectTypePlural": "Listas de transacciones de cuenta bancaria", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____objectTypeSingular": "Lista de transacciones de cuenta bancaria", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed____title": "Lista de transacciones", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__accountFeedKey____title": "Clave de importación de cuenta financiera", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__accountReconKey____title": "Clave de conciliación bancaria", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__accountType____title": "Tipo de cuenta financiera", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__amount____title": "Importe", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__amountToMatch____title": "Importe por marcar", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__cleared____title": "Conciliación", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__currency____title": "Divisa", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__description____title": "Descripción", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__documentNumber____title": "Número de documento", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__documentType____title": "Tipo de documento", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__entity____title": "Entidad financiera", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__entityName____title": "Nombre de entidad financiera", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__feedType____title": "Tipo de importación", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__intacctId____title": "Id.", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__mainBlock____title": "Transacción", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__mainSection____title": "General", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__number____title": "Número", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__payee____title": "Beneficiario", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__postingDate____title": "Fecha de contabilización", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__reconcilitationDate____title": "Fecha de conciliación", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__transactionId____title": "Id. de transacción", "@sage/xtrem-intacct-finance/pages__intacct_bank_account_transaction_feed__transactionType____title": "Tipo de transacción", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog____objectTypeSingular": "Búsqueda de documentos en Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog____title": "Búsqueda de documentos", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__account____title": "C<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__amount____title": "Importe", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__criteriaBlock____title": "Criterios", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__date____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__description____title": "Descripción", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__documentType____placeholder": "Seleccionar tipo de documento", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__documentType____title": "Tipo de documento", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title___id": "Id.", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__account": "C<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__amount": "Importe", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__batchNo": "Número de <PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__date": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__description": "Descripción", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__documentType": "Tipo de documento", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__entityId": "Entidad", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__journal": "Diario", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__location": "Ubicación", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__recordNo": "Número de registro", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____columns__title__url": "Referencia de integración contable", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__financialDocuments____title": "Resul<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_financial_document_search_dialog__select____title": "Guardar", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line_4__title": "Smart Event", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line_5__title": "Dirección de sincronización", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line2__title": "Transacciones en Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line4__title": "Smart Event", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line5__title": "Sentido de sincronización", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__line6__title": "Combinar registros", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__title__title": "Objeto en Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map____navigationPanel__listItem__titleRight__title": "Nodo en Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map____objectTypePlural": "Mapeos en Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map____objectTypeSingular": "Mapeo en Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map____title": "Mapeo", "@sage/xtrem-intacct-finance/pages__intacct_map__additionnalLink____title": "Vín<PERSON>los adicionales", "@sage/xtrem-intacct-finance/pages__intacct_map__additionnalLinkFormated____title": "Vín<PERSON>los adicionales", "@sage/xtrem-intacct-finance/pages__intacct_map__addLineSpecificFields____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__createAll____title": "<PERSON><PERSON><PERSON> todo en Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__createAllIntacct____title": "<PERSON><PERSON><PERSON> todo en Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__dataSection____title": "Datos", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__DATATYPE": "Tipo de <PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__DESCRIPTION": "Descripción", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__ID": "Id.", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__ISCUSTOM": "Personalizado", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__isEditable": "Editable", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__LABEL": "Etiqueta", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__READONLY": "Solo lectura", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__REQUIRED": "Obligatorio", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__xtremDefaultProperty": "Propiedad por defecto", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__xtremProperty": "Propiedad en Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____columns__title__xtremPropertyOption": "Opción de propiedad", "@sage/xtrem-intacct-finance/pages__intacct_map__fields____title": "Campos", "@sage/xtrem-intacct-finance/pages__intacct_map__id____title": "Objeto en Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctDataBlock____title": "Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctDescription____title": "Transacciones en Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctFilter____title": "Filtros de Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__intacctNameSelect____title": "Transacciones en Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__isActive____title": "Activo", "@sage/xtrem-intacct-finance/pages__intacct_map__isPrivateShow____title": "Combinar registros", "@sage/xtrem-intacct-finance/pages__intacct_map__isSmartEvent____title": "Smart Event", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__collectionName": "Nombre de colección", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__line__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__line__intacctDescription": "Nombre", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__propertyHeader": "Propiedad de cabecera", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____columns__title__propertyLine": "Propiedad de línea", "@sage/xtrem-intacct-finance/pages__intacct_map__lines____title": "Mapeos", "@sage/xtrem-intacct-finance/pages__intacct_map__lineSection____title": "Colecciones/Líneas", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__integrationStatus": "Estado de integración", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__isLinked": "Vinculado", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__megaEntityId": "Id. de entidad", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__whenCreated": "Creación", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____columns__title__whenModified": "Actualización", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title": "<PERSON><PERSON><PERSON> en Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title__2": "Actualizar en Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title__3": "Eliminar en Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____dropdownActions__title__4": "Eliminar en Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__linkedData____title": "Resul<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__mainBlock____title": "Configuración", "@sage/xtrem-intacct-finance/pages__intacct_map__mainSection____title": "Mapeo", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeFactory____columns__title__name": "Nodo", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeFactory____columns__title__title": "Nodo", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeFactory____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__nodeProperties____title": "Campos", "@sage/xtrem-intacct-finance/pages__intacct_map__notificationStatus____title": "Estado de importación masiva", "@sage/xtrem-intacct-finance/pages__intacct_map__refreshData____title": "Actualizar", "@sage/xtrem-intacct-finance/pages__intacct_map__refreshRelationMapping____title": "Actualizar", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__DATATYPE": "Tipo de <PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__DESCRIPTION": "Descripción", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__ID": "Id.", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__ISCUSTOM": "Personalizado", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__isEditable": "Editable", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__LABEL": "Etiqueta", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__READONLY": "Solo lectura", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__REQUIRED": "Obligatorio", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__xtremDefaultProperty": "Propiedad por defecto", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__xtremProperty": "Propiedad en Sage DMO", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____columns__title__xtremPropertyOption": "Opción de propiedad", "@sage/xtrem-intacct-finance/pages__intacct_map__relationshipFields____title": "Relaciones", "@sage/xtrem-intacct-finance/pages__intacct_map__resetRelationMapping____title": "Restablecer", "@sage/xtrem-intacct-finance/pages__intacct_map__save____title": "Guardar", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____columns__title__name": "Nombre de propiedad en Sage Intacct", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____columns__title__type": "Tipo", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____columns__title__whereValue": "Filtro", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_map__specificFields____title": "Propiedades técnicas", "@sage/xtrem-intacct-finance/pages__intacct_map__synchronizationDirection____title": "Dirección de sincronización", "@sage/xtrem-intacct-finance/pages__intacct_map__writeMappingFile____title": "Escribir archivo de mapeo", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____navigationPanel__listItem__title__title": "Banco", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____objectTypePlural": "Listas de transacciones", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____objectTypeSingular": "", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query____title": "Área de trabajo de transacciones", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query___etag____title": "ETag", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query___id____title": "Id.", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____columns__columns__account__name__title__3": "Nombre", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____columns__columns__currency___id__title__4": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____columns__columns__financialSite___id__title__2": "Id.", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__bankAccount____title": "Cuenta bancaria", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__criteriaBlock____title": "Criterios", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__dateCreation____title": "Fecha de creación", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__dateFrom____title": "Fecha de inicio", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__dateTo____title": "<PERSON><PERSON> de fin", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__description____title": "Descripción", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__mainSection____title": "Área de trabajo de transacciones", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__mapObject____columns__title__intacctDescription": "Nombre", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__mapObject____title": "Mapeo", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matched____title": "Con correspondencias", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matchingRules____title": "<PERSON><PERSON><PERSON> reglas de correspondencia", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matchStatus____placeholder": "Seleccionar estado", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__matchStatus____title": "Estado", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__number_of_journal_entries_created": "Journal entries created: {{documentsPosted}}.", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__post____title": "Contabilizar", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__refreshTransactionFeed____title": "Actualizar", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__totalAmount____title": "Importe total", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__totalDeposit____title": "Total depósitos", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__totalWithdrawal____title": "Total retiradas", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__account__name__title": "Gestión de impuestos", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__account__name__title__6": "Id.", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__account__name__title__7": "Gestión de impuestos", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__bankAccount__name__columns__title___id": "Id.", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__bankAccount__name__columns__title___id__2": "Id.", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__bankAccount__name__title__5": "Divisa", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__currency__name__title": "Nombre", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__currency__name__title__3": "Nombre", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__document___id__title__5": "Importe", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__location__name__title__2": "Nombre", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__columns__tax__name__title__2": "Nombre", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title___id": "Id.", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__account__name": "C<PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__amount": "Importe", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__amount__2": "Importe", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__amountToMatch": "Importe por marcar", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__currency__name": "Divisa", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__description": "Descripción", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__intacctJournalEntryBatchNo": "Referencia de integración contable", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__location__name": "Ubicación", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__payee": "Beneficiario", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__status": "Estado de línea", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__tax__name": "Impuesto", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__taxAmount": "Importe de impuesto", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__columns__title__transactionType": "Tipo de transacción", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title": "Pago de factura contable de cliente", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__2": "Eliminar línea", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__3": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__4": "Secciones", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__5": "Buscar", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____levels__dropdownActions__title__6": "<PERSON><PERSON><PERSON> regla", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionFeed____title": "Registros", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionType____placeholder": "Tipo de transacción", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__transactionType____title": "Tipo de transacción", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__unMatched____title": "Sin correspondencias", "@sage/xtrem-intacct-finance/pages__intacct_transaction_feed_query__withPostableFeeds____title": "Con listas contabilizables", "@sage/xtrem-intacct-finance/pages__intact_transaction_feed_query__map_object_mandatory": "Los registros del objeto de mapeo \"Lista de transacciones de cuenta bancaria\" no están definidos.", "@sage/xtrem-intacct-finance/pages__matching_rules_bulk_save": "Lista de reglas de correspondencia guardada", "@sage/xtrem-intacct-finance/pages__tax_extension__was_deactivated_secondary_external_reference": "El impuesto está desactivado porque la referencia externa secundaria está en blanco y la inversión de sujeto pasivo está marcada.", "@sage/xtrem-intacct-finance/pages_accounts_receivable_invoice_matching_advance_created": "<PERSON><PERSON><PERSON> c<PERSON>:", "@sage/xtrem-intacct-finance/pages_accounts_receivable_invoice_matching_payment_created": "<PERSON><PERSON> creado:", "@sage/xtrem-intacct-finance/pages_document-no-document-selected": "Selecciona un documento.", "@sage/xtrem-intacct-finance/pages_document-search-more-than-one": "Solo puedes seleccionar un documento.", "@sage/xtrem-intacct-finance/pages_intacct_transaction_feed_query_create_rule": "Regla de correspondencia guardada", "@sage/xtrem-intacct-finance/pages_integration-error": "Error de integración", "@sage/xtrem-intacct-finance/pages-confirm-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/pages-confirm-delete": "Eliminar", "@sage/xtrem-intacct-finance/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-finance/permission__create_update_all_intacct__name": "<PERSON><PERSON><PERSON> o actualizar en Sage Intacct", "@sage/xtrem-intacct-finance/permission__delete__name": "Eliminar", "@sage/xtrem-intacct-finance/permission__delete_xtrem__name": "Delete Xtrem", "@sage/xtrem-intacct-finance/permission__get_available_xtrem_object_list__name": "Get available Xtrem object list", "@sage/xtrem-intacct-finance/permission__get_data_intacct__name": "Obtener datos de Sage Intacct", "@sage/xtrem-intacct-finance/permission__get_intacct_transactions_list__name": "Obtener lista de transacciones de Sage Intacct", "@sage/xtrem-intacct-finance/permission__get_match_line__name": "Obtener línea de punteo", "@sage/xtrem-intacct-finance/permission__get_object__name": "Obtener objeto", "@sage/xtrem-intacct-finance/permission__get_structure__name": "Obtener estructura", "@sage/xtrem-intacct-finance/permission__is_service_option_active_function__name": "Service option active function", "@sage/xtrem-intacct-finance/permission__manage__name": "Gestionar", "@sage/xtrem-intacct-finance/permission__post__name": "Contabilizar", "@sage/xtrem-intacct-finance/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-intacct-finance/permission__retry_finance_document__name": "Reintentar documento contable", "@sage/xtrem-intacct-finance/permission__search_intacct_data__name": "Buscar datos de Sage Intacct", "@sage/xtrem-intacct-finance/permission__service_option_change__name": "Cambiar opción de servicio", "@sage/xtrem-intacct-finance/permission__set_cleared__name": "Conciliar", "@sage/xtrem-intacct-finance/permission__synchronization_with_sage_intacct__name": "<PERSON><PERSON><PERSON><PERSON><PERSON> con <PERSON>t", "@sage/xtrem-intacct-finance/permission__update__name": "Actualizar", "@sage/xtrem-intacct-finance/permission__write_structure__name": "Escribir estructura", "@sage/xtrem-intacct-finance/reset": "Restablecer", "@sage/xtrem-intacct-finance/search": "Buscar", "@sage/xtrem-intacct-finance/service_options__intacct_cashbook_management__name": "Sage Intacct Cashbook Management", "@sage/xtrem-intacct-finance/status_not_updated": "El documento no se ha enviado. El estado no se puede actualizar.", "@sage/xtrem-intacct-finance/status_updated": "El estado se ha actualizado.", "@sage/xtrem-intacct-finance/synchronization_already_in_progress": "La sincronización ya está en curso", "@sage/xtrem-intacct-finance/target_document_not_found": "No se ha encontrado el tipo de documento {{targetDocumentType}} con id. {{targetDocumentSysId}}.", "@sage/xtrem-intacct-finance/target_document_type_not_supported": "El tipo de documento de destino \"{{targetDocumentType}}\" no es compatible.", "@sage/xtrem-intacct-finance/too_many_documents_found_on_intacct": "Número de documentos encontrados en Sage Intacct para {{targetDocumentType}} {{documentNumber}}: {{numberOfDocumentsFoundOnIntacct}}. El documento no se puede actualizar.", "@sage/xtrem-intacct-finance/transaction-feed-query-provide-bank-account": "Introduce el número de cuenta bancaria.", "@sage/xtrem-intacct-finance/transaction-feed-query-provide-dates": "Introduce una fecha de inicio y una de fin.", "@sage/xtrem-intacct-finance/update-account-tax-management-context": "¿Quieres definir la gestión de impuestos de la cuenta en \"Sin impuestos\"?", "@sage/xtrem-intacct-finance/update-account-tax-management-title": "Confirmar actualiza<PERSON>", "@sage/xtrem-intacct-finance/update-xtreem-return": "Registros creados: {{created}}. Registros actualizados: {{updated}}\n {{message}}", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____callToActions__seeAll__title": "Ver todo", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____dataDropdownMenu__orderBy__number__title": "Ordenar por id.", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____dataDropdownMenu__orderBy__status__title": "Ordenar por nombre", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__line2__title": "Estado", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__line2Right__title": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__title__title": "Id.", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____rowDefinition__titleRight__title": "Nombre", "@sage/xtrem-intacct-finance/widgets__customer_integration_health____title": "Estado de integración de clientes", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____callToActions__seeAll__title": "Ver todo", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____dataDropdownMenu__orderBy__number__title": "Ordenar por id.", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____dataDropdownMenu__orderBy__status__title": "Ordenar por nombre", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__line2__title": "Estado", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__line2Right__title": "Id. en Sage Intacct", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__title__title": "Id.", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____rowDefinition__titleRight__title": "Nombre", "@sage/xtrem-intacct-finance/widgets__supplier_integration_health____title": "Estado de integración de proveedores"}