import type { Graph<PERSON><PERSON> } from '@sage/xtrem-intacct-finance-api';
import type { BusinessEntityAddress } from '@sage/xtrem-master-data-api';
import type { BusinessEntity } from '@sage/xtrem-master-data/build/lib/pages/business-entity';
import * as ui from '@sage/xtrem-ui';
import { noop } from 'lodash';
import {
    integrationAddressOnClick,
    integrationAddressOnError,
} from '../client-functions/business-entity-address-intacct';
import * as PillColorIntacct from '../client-functions/pill-color';

@ui.decorators.pageExtension<BusinessEntityExtension>({
    extends: '@sage/xtrem-master-data/BusinessEntity',
    extensionAccessBinding: { node: '@sage/xtrem-intacct/Intacct', bind: 'defaultInstance' },
})
export class BusinessEntityExtension extends ui.PageExtension<BusinessEntity, GraphApi> {
    @ui.decorators.podCollectionFieldOverride<BusinessEntityExtension, BusinessEntityAddress>({
        columns: [
            ui.nestedFieldExtensions.label({
                bind: { intacctBusinessEntityAddress: { state: true } },
                title: 'Integration state',
                optionType: '@sage/xtrem-communication/IntegrationState',
                style: (_id, rowValue) =>
                    PillColorIntacct.getLabelColorByStatus(
                        'IntegrationState',
                        rowValue?.intacctBusinessEntityAddress?.state,
                    ),
            }),
            ui.nestedFieldExtensions.text({ bind: { intacctBusinessEntityAddress: { _id: true } }, isHidden: true }),
            ui.nestedFieldExtensions.text({
                bind: { intacctBusinessEntityAddress: { lastMessage: true } },
                isHidden: true,
            }),
            ui.nestedFieldExtensions.icon({
                title: 'Refresh',
                helperText: 'Synchronizing',
                isHelperTextHidden: true,
                insertBefore: 'isShippingAddress',
                map: () => 'refresh',
                bind: { intacctBusinessEntityAddress: { _id: true } },
                // XT-69128 rowData typing is incorrect  this must be BusinessEntityAddress
                async onError(_id: string, rowData: any) {
                    await integrationAddressOnError(this, rowData);
                },
                // XT-69128 rowData typing is incorrect this must be BusinessEntityAddress
                async onClick(_id: string, rowData: any) {
                    await integrationAddressOnClick(this, rowData);
                },
            }),
        ],
    })
    addresses: ui.fields.PodCollection<BusinessEntityAddress>;

    /** enhancement request : https://jira.sage.com/browse/XT-62899 */
    intacctAddress(address: ui.PartialNodeWithId<BusinessEntityAddress>) {
        noop(this); //
        if (address) {
            delete address.intacctBusinessEntityAddress;
        }
        return address;
    }
}

declare module '@sage/xtrem-master-data/build/lib/pages/business-entity' {
    export interface BusinessEntity extends BusinessEntityExtension {}
}
