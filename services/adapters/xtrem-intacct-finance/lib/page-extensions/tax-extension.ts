import type { GraphApi } from '@sage/xtrem-intacct-finance-api';
import type { Country } from '@sage/xtrem-structure-api';
import type { Tax as TaxBinding } from '@sage/xtrem-tax-api';
import type { Tax } from '@sage/xtrem-tax/build/lib/pages/tax';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<TaxExtension>({
    extensionAccessBinding: {
        node: '@sage/xtrem-intacct/Intacct',
        bind: 'defaultInstance',
    },
    extends: '@sage/xtrem-tax/Tax',
    onLoad() {
        this.enableDisableHeaderInformation();
        this.hideShowHeaderInformation();
    },
})
export class TaxExtension extends ui.PageExtension<Tax, GraphApi> {
    @ui.decorators.checkboxField<TaxExtension>({
        parent() {
            return this.generalBlock;
        },
        title: 'Sage Intacct',
        insertBefore() {
            return this.primaryExternalReference;
        },
        isDisabled: true,
    })
    isIntacct: ui.fields.Checkbox;

    @ui.decorators.switchFieldOverride<TaxExtension>({
        title: 'Active',
        onChangeAfter() {
            this.disallowActivationChange();
        },
    })
    isActive: ui.fields.Switch;

    @ui.decorators.checkboxField<TaxExtension>({
        parent() {
            return this.generalBlock;
        },
        size: 'small',
        title: 'Sage Intacct reverse charge',
        insertBefore() {
            return this.primaryExternalReference;
        },
    })
    isIntacctReverseCharge: ui.fields.Checkbox;

    @ui.decorators.textFieldOverride<TaxExtension>({
        title: 'Primary external reference',
    })
    primaryExternalReference: ui.fields.Text;

    @ui.decorators.textFieldOverride<TaxExtension>({
        title: 'Secondary external reference',
    })
    secondaryExternalReference: ui.fields.Text;

    @ui.decorators.referenceFieldOverride<TaxExtension>({
        title: 'Country',
    })
    country: ui.fields.Reference<Country>;

    @ui.decorators.referenceField<TaxExtension, TaxBinding>({
        parent() {
            return this.generalBlock;
        },
        title: 'Secondary external reference',
        width: 'large',
        insertBefore() {
            return this.secondaryExternalReference;
        },
        valueField: 'primaryExternalReference',
        node: '@sage/xtrem-tax/Tax',
        minLookupCharacters: 1,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({
                bind: 'primaryExternalReference',
                title: 'Primary external reference',
            }),
            ui.nestedFields.reference({
                bind: 'country',
                title: 'Country',
                node: '@sage/xtrem-structure/Country',
                valueField: 'name',
                columns: [ui.nestedFields.text({ bind: 'name' })],
            }),
            ui.nestedFields.reference({
                bind: 'taxCategory',
                title: 'Tax category',
                node: '@sage/xtrem-tax/TaxCategory',
                valueField: 'name',
                columns: [ui.nestedFields.text({ bind: 'name' })],
            }),
            ui.nestedFields.checkbox({ bind: 'isIntacctReverseCharge', title: 'Sage Intacct reverse charge' }),
        ],
        onChange() {
            this.secondaryExternalReference.value = this.intacctSecondaryExternalReference.value
                ? (this.intacctSecondaryExternalReference.value.primaryExternalReference as any)
                : null;
            this.disallowActivationChange();
        },
    })
    intacctSecondaryExternalReference: ui.fields.Reference<TaxBinding>;

    @ui.decorators.checkboxFieldOverride<TaxExtension>({
        title: 'Reverse charge',
        onChangeAfter() {
            this.disallowActivationChange();
        },
    })
    isReverseCharge: ui.fields.Checkbox;

    @ui.decorators.dropdownListFieldOverride<TaxExtension>({
        title: 'Tax type',
    })
    type: ui.fields.DropdownList;

    hideShowHeaderInformation() {
        this.isIntacct.isHidden = !this.isIntacct.value;
        this.isIntacctReverseCharge.isHidden = !this.isIntacct.value;
        this.primaryExternalReference.isReadOnly = !!this.isIntacct.value;
        this.country.isReadOnly = !!this.isIntacct.value;
        this.secondaryExternalReference.isHidden = !!this.isIntacct.value;
        this.intacctSecondaryExternalReference.isHidden = !this.isIntacct.value || !!this.isIntacctReverseCharge.value;
        this.isReverseCharge.isHidden = !!this.isIntacct.value && !!this.isIntacctReverseCharge.value;
    }

    enableDisableHeaderInformation() {
        this.isActive.isDisabled = !!this.isIntacctReverseCharge.value;
        this.isIntacctReverseCharge.isDisabled = !!this.isIntacct.value;
        this.type.isDisabled = this.type.isDisabled || !!this.isIntacct.value;
    }

    disallowActivationChange() {
        if (this.isActive.value && this.isIntacct.value) {
            if (!this.intacctSecondaryExternalReference.value && this.isReverseCharge.value) {
                this.isActive.value = false;
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-intacct-finance/pages__tax_extension__was_deactivated_secondary_external_reference',
                        'The tax is turned off because the secondary external reference is empty and the reverse charge is selected.',
                    ),
                    {
                        type: 'warning',
                    },
                );
            }
        }
    }
}

declare module '@sage/xtrem-tax/build/lib/pages/tax' {
    export interface Tax extends TaxExtension {}
}
