import type { DimensionType } from '@sage/xtrem-finance-data/build/lib/pages/dimension-type';
import type { GraphApi } from '@sage/xtrem-intacct-finance-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<DimensionTypeExtension>({
    extends: '@sage/xtrem-master-data/DimensionType',
    extensionAccessBinding: {
        node: '@sage/xtrem-intacct/Intacct',
        bind: 'defaultInstance',
    },
})
export class DimensionTypeExtension extends ui.PageExtension<DimensionType, GraphApi> {
    @ui.decorators.block<DimensionTypeExtension>({
        parent() {
            return this.mainSection;
        },
        title: 'Sage Intacct',
        width: 'extra-large',
    })
    intacctBlock: ui.containers.Block;

    @ui.decorators.textField<DimensionTypeExtension>({
        title: 'Sage Intacct object',
        parent() {
            return this.intacctBlock;
        },
        isDisabled: true,
    })
    intacctObject: ui.fields.Text;
}

declare module '@sage/xtrem-finance-data/build/lib/pages/dimension-type' {
    export interface DimensionType extends DimensionTypeExtension {}
}
