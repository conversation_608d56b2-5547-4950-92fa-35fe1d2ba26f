import type { IntegrationState } from '@sage/xtrem-communication-api';
import type { GraphApi, IntacctSupplier } from '@sage/xtrem-intacct-finance-api';
import type { BusinessEntityAddress, Supplier as SupplierNode } from '@sage/xtrem-master-data-api';
import type { Supplier as SupplierPage } from '@sage/xtrem-master-data/build/lib/pages/supplier';
import * as ui from '@sage/xtrem-ui';
import { noop } from 'lodash';
import {
    integrationAddressOnClick,
    integrationAddressOnError,
} from '../client-functions/business-entity-address-intacct';
import { synchronizeOnClick, synchronizeOnError } from '../client-functions/business-entity-intacct';
import { getIntacctUrl, isActive } from '../client-functions/common';
import * as PillColorIntacct from '../client-functions/pill-color';

@ui.decorators.pageExtension<SupplierExtension, SupplierNode>({
    extends: '@sage/xtrem-master-data/Supplier',
    extensionAccessBinding: { node: '@sage/xtrem-intacct/Intacct', bind: 'defaultInstance' },
    async onLoad() {
        this.paymentMethod.isHidden = true;

        const isIntacctActive = await isActive(this);
        this.intacctUrlLink.isDisabled = !(isIntacctActive && this.intacctIntegrationState.value === 'success');
    },
    navigationPanel: {
        listItem: {
            line12: ui.nestedFieldExtensions.text({
                title: 'Sage Intacct ID',
                bind: { intacctSupplier: { intacctId: true } },
                insertBefore: 'minimumOrderAmount',
                isHiddenOnMainField: true,
            }),
            line13: ui.nestedFieldExtensions.label({
                title: 'Sage Intacct integration state',
                bind: { intacctSupplier: { state: true } },
                optionType: '@sage/xtrem-communication/IntegrationState',
                insertBefore: 'minimumOrderAmount',
                isHiddenOnMainField: true,
                style: (_id, rowValue) =>
                    PillColorIntacct.getLabelColorByStatus('IntegrationState', rowValue?.intacctSupplier?.state),
            }),
        },
    },
})
export class SupplierExtension extends ui.PageExtension<SupplierPage, GraphApi> {
    @ui.decorators.referenceField<SupplierExtension, IntacctSupplier>({
        node: '@sage/xtrem-intacct-finance/IntacctSupplier',
        bind: { intacctSupplier: true },
        columns: [
            ui.nestedFields.technical({ bind: 'intacctId' }),
            ui.nestedFields.technical({ bind: 'lastMessage' }),
            ui.nestedFields.technical({ bind: 'url' }),
            ui.nestedFields.technical({ bind: 'state' }),
        ],
    })
    intacctReference: ui.fields.Reference<IntacctSupplier>;

    @ui.decorators.block<SupplierExtension>({
        parent() {
            return this.mainSection;
        },
        title: 'Sage Intacct',
        width: 'extra-large',
    })
    intacctBlock: ui.containers.Block;

    @ui.decorators.linkField<SupplierExtension>({
        parent() {
            return this.intacctBlock;
        },
        title: 'Sage Intacct ID',
        bind: { intacctSupplier: { intacctId: true } },
        async onClick() {
            const url = this.intacctReference.value?.url;
            const intacctUrl = !url && this._id.value ? await getIntacctUrl(this, this._id.value, 'supplier') : url;
            this.$.router.goToExternal(intacctUrl || '');
        },
    })
    intacctUrlLink: ui.fields.Link;

    @ui.decorators.labelField<SupplierExtension>({
        parent() {
            return this.intacctBlock;
        },
        title: 'Integration status',
        optionType: '@sage/xtrem-communication/IntegrationState',
        width: 'large',
        bind: { intacctSupplier: { state: true } },
        style() {
            return PillColorIntacct.getLabelColorByStatus('IntegrationState', this.intacctIntegrationState.value);
        },
    })
    intacctIntegrationState: ui.fields.Label<IntegrationState>;

    @ui.decorators.podCollectionFieldOverride<SupplierExtension, BusinessEntityAddress>({
        columns: [
            ui.nestedFieldExtensions.label({
                bind: { intacctBusinessEntityAddress: { state: true } },
                title: 'Integration state',
                optionType: '@sage/xtrem-communication/IntegrationState',
                style: (_id, rowValue) =>
                    PillColorIntacct.getLabelColorByStatus(
                        'IntegrationState',
                        rowValue?.intacctBusinessEntityAddress?.state,
                    ),
            }),
            ui.nestedFieldExtensions.text({ bind: { intacctBusinessEntityAddress: { _id: true } }, isHidden: true }),
            ui.nestedFieldExtensions.text({
                bind: { intacctBusinessEntityAddress: { lastMessage: true } },
                isHidden: true,
            }),
            ui.nestedFieldExtensions.icon({
                title: 'Refresh',
                helperText: 'Synchronizing',
                isHelperTextHidden: true,
                bind: '_id',
                insertBefore: 'isShippingAddress',
                map: () => 'refresh',
                // XT-69128 rowData typing is incorrect  this must be BusinessEntityAddress
                async onError(_id: string, rowData: any) {
                    await integrationAddressOnError(this, rowData);
                },
                // XT-69128 rowData typing is incorrect this must be BusinessEntityAddress
                async onClick(_id: string, rowData: any) {
                    await integrationAddressOnClick(this, rowData);
                },
            }),
        ],
    })
    addresses: ui.fields.PodCollection<BusinessEntityAddress>;

    @ui.decorators.dropdownListField<SupplierExtension>({
        parent() {
            return this.financialBlock;
        },
        title: 'Payment method',
        optionType: '@sage/xtrem-master-data/PaymentMethod',
    })
    paymentMethodSelect: ui.fields.DropdownList;

    @ui.decorators.iconField<SupplierExtension>({
        parent() {
            return this.intacctBlock;
        },
        title: 'Refresh',
        isTransient: true,
        helperText: 'Synchronizing',
        isHelperTextHidden: true,
        map: () => 'refresh',
        async onError() {
            await synchronizeOnError(this);
        },
        async onClick() {
            await synchronizeOnClick(this);
        },
    })
    synchronize: ui.fields.Icon;

    /** enhancement request : https://jira.sage.com/browse/XT-62899 */
    intacctAddress(address: ui.PartialNodeWithId<BusinessEntityAddress>) {
        noop(this); //
        if (address) {
            delete address.intacctBusinessEntityAddress;
        }
        return address;
    }
}

declare module '@sage/xtrem-master-data/build/lib/pages/supplier' {
    interface Supplier extends SupplierExtension {}
}
