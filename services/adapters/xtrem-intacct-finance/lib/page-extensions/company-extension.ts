import type { GraphApi } from '@sage/xtrem-finance-data-api';
import type { Company as CompanyPage } from '@sage/xtrem-master-data/build/lib/pages/company';
import * as ui from '@sage/xtrem-ui';
import { isActive } from '../client-functions/common';

@ui.decorators.pageExtension<CompanyExtension>({
    extends: '@sage/xtrem-master-data/Company',
    extensionAccessBinding: {
        node: '@sage/xtrem-intacct/Intacct',
        bind: 'defaultInstance',
    },
    async onLoad() {
        const isIntacctActive = await isActive(this);
        this.customerOnHoldCheck.isDisabled = isIntacctActive;
        this.synchronize.isHidden = !isIntacctActive;
    },
})
export class CompanyExtension extends ui.PageExtension<CompanyPage, GraphApi> {
    /**
     * Management Tab
     */
    @ui.decorators.section<CompanyExtension>({
        title: 'Management',
    })
    managementSection: ui.containers.Section;

    @ui.decorators.block<CompanyExtension>({
        parent() {
            return this.managementSection;
        },
        title: 'Credit limit',
        width: 'extra-large',
    })
    creditLimitBlock: ui.containers.Block;

    @ui.decorators.separatorField<CompanyExtension>({
        parent() {
            return this.creditLimitBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorThirdLine: ui.fields.Separator;

    @ui.decorators.iconField<CompanyExtension>({
        parent() {
            return this.creditLimitBlock;
        },
        title: 'Refresh',
        isTransient: true,
        helperText: 'Synchronizing',
        isHelperTextHidden: true,
        insertAfter() {
            return this.customerOnHoldCheck;
        },
        map: () => 'refresh',
        onError() {
            this.synchronize.isDisabled = false;
            this.synchronize.isHelperTextHidden = true;
        },
        async onClick() {
            this.synchronize.isDisabled = true;
            this.synchronize.isHelperTextHidden = false;

            await this.$.graph
                .node('@sage/xtrem-system/Company')
                .asyncOperations.syncCompanyOnHold.runToCompletion(true, {
                    companySysId: this._id.value || '',
                    isAllCompanies: false,
                })
                .execute();

            await this.customerOnHoldCheck.refresh();
            this.synchronize.isHelperTextHidden = true;
            this.synchronize.isDisabled = false;
        },
    })
    synchronize: ui.fields.Icon;

    @ui.decorators.switchField<CompanyExtension>({
        parent() {
            return this.paymentTrackingBlock;
        },
        title: 'Update amount paid on sales document reports',
        width: 'large',
    })
    doUpdateArAmountPaid: ui.fields.Switch;
}

declare module '@sage/xtrem-master-data/build/lib/pages/company' {
    interface Company extends CompanyExtension {}
}
