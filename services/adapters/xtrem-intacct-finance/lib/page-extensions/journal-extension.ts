import type { Journal } from '@sage/xtrem-finance-data/build/lib/pages/journal';
import type { GraphApi } from '@sage/xtrem-intacct-finance-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<JournalExtension>({
    extends: '@sage/xtrem-finance-data/Journal',
    extensionAccessBinding: {
        node: '@sage/xtrem-intacct/Intacct',
        bind: 'defaultInstance',
    },
    onLoad() {
        if (this.recordNo.value) {
            this.$standardDuplicateAction.isHidden = true;
        }
    },
})
export class JournalExtension extends ui.PageExtension<Journal, GraphApi> {
    @ui.decorators.numericField<JournalExtension>({
        title: 'Record number',
    })
    recordNo: ui.fields.Numeric;
}

declare module '@sage/xtrem-finance-data/build/lib/pages/journal' {
    export interface Journal extends JournalExtension {}
}
