import type { GraphApi, IntacctMap } from '@sage/xtrem-intacct-finance-api';
import type { Intacct } from '@sage/xtrem-intacct/build/lib/pages/intacct';
import * as ui from '@sage/xtrem-ui';
import { checkOpenApArInvoices } from '../client-functions/page-functions';

const xtremObject: { name: string; intacctId: string }[] = [
    { name: 'item', intacctId: 'ITEMID' },
    { name: 'supplier', intacctId: 'VENDORID' },
    { name: 'customer', intacctId: 'CUSTOMERID' },
    { name: 'business-entity-address', intacctId: 'CONTACTNAME' },
];

@ui.decorators.pageExtension<IntacctExtension>({
    extends: '@sage/xtrem-intacct/Intacct',
    onLoad() {
        this.objectSelect.options = [
            ...this.generateSmartEvent.value.map(object => object.id),
            ...xtremObject.map(object => object.name),
        ];

        if (this.objectEvent.value) {
            this.objectSelect.value = this.objectEvent.value;
        }
        this.objectEvent.isHidden = true;
        this.isConfigurationActiveOnExtension = this.isActive.value || false;
    },
})
export class IntacctExtension extends ui.PageExtension<Intacct, GraphApi> {
    isConfigurationActiveOnExtension: boolean;

    @ui.decorators.dropdownListField<IntacctExtension>({
        parent() {
            return this.eventBlock;
        },
        insertBefore() {
            return this.change;
        },
        isTransient: true,
        title: 'Sage Intacct object',

        onChange() {
            if (this.objectSelect.value) {
                this.objectEvent.value = this.objectSelect.value;
                this.intacctIdName.value =
                    xtremObject.find(object => object.name === this.objectSelect.value)?.intacctId || '';
            }
        },
    })
    objectSelect: ui.fields.DropdownList;

    @ui.decorators.section<IntacctExtension>({ title: 'Custom Package' })
    customPackageSection: ui.containers.Section;

    @ui.decorators.block<IntacctExtension>({
        parent() {
            return this.customPackageSection;
        },
        isHidden: true,
        title: 'Smart Events ',
        width: 'extra-large',
    })
    smartEventBlock: ui.containers.Block;

    @ui.decorators.tableField<IntacctExtension, IntacctMap>({
        parent() {
            return this.customPackageSection;
        },
        node: '@sage/xtrem-intacct-finance/IntacctMap',
        canSelect: false,
        title: 'Smart Events to generate',
        canUserHideColumns: false,
        columns: [
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'intacctDescription', title: 'Sage Intacct name' }),
            ui.nestedFields.text({ bind: { nodeFactory: { name: true } }, title: 'Sage DMO object' }),
            ui.nestedFields.dropdownList({
                bind: 'synchronizationDirection',
                title: 'Synchronization',
                optionType: '@sage/xtrem-synchronization/SynchronizationDirection',
            }),
            ui.nestedFields.technical({ bind: 'canCreate' }),
            ui.nestedFields.technical({ bind: 'canUpdate' }),
            ui.nestedFields.technical({ bind: 'canDelete' }),
        ],
    })
    generateSmartEvent: ui.fields.Table<IntacctMap>;

    @ui.decorators.block<IntacctExtension>({
        parent() {
            return this.customPackageSection;
        },
        width: 'extra-large',
    })
    generateBlock: ui.containers.Block;

    @ui.decorators.fileField<IntacctExtension>({
        parent() {
            return this.generateBlock;
        },
        fileTypes: 'application/xml',
        text: 'package-file.xml',
        title: 'Custom Package file',
    })
    customPackageFile: ui.fields.File;

    @ui.decorators.pageActionOverride<IntacctExtension>({
        title: 'Save',
        onClickAfter() {
            (async () => {
                if (!this.isConfigurationActiveOnExtension && !!this.isActive.value) {
                    await checkOpenApArInvoices(this);
                }
                this.isConfigurationActiveOnExtension = this.isActive.value || false;
            })().catch(e => ui.console.error(e));
        },
    })
    save: ui.PageAction;
}
declare module '@sage/xtrem-intacct/build/lib/pages/intacct' {
    export interface Intacct extends IntacctExtension {}
}
