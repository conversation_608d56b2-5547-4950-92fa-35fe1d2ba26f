import type { IntegrationState } from '@sage/xtrem-communication-api';
import type { <PERSON><PERSON>h<PERSON><PERSON>, IntacctCustomer } from '@sage/xtrem-intacct-finance-api';
import type { BusinessEntityAddress, Customer as CustomerNode } from '@sage/xtrem-master-data-api';
import type { Customer as CustomerPage } from '@sage/xtrem-master-data/build/lib/pages/customer';
import * as ui from '@sage/xtrem-ui';
import { noop } from 'lodash';
import {
    integrationAddressOnClick,
    integrationAddressOnError,
} from '../client-functions/business-entity-address-intacct';
import { synchronizeOnClick, synchronizeOnError } from '../client-functions/business-entity-intacct';
import { getIntacctUrl, isActive } from '../client-functions/common';
import * as PillColorIntacct from '../client-functions/pill-color';

@ui.decorators.pageExtension<CustomerExtension, CustomerNode>({
    extends: '@sage/xtrem-master-data/Customer',
    extensionAccessBinding: { node: '@sage/xtrem-intacct/Intacct', bind: 'defaultInstance' },
    async onLoad() {
        const isIntacctActive = await isActive(this);
        this.intacctUrlLink.isDisabled = !(isIntacctActive && this.intacctIntegrationState.value === 'success');
        this.creditLimit.isDisabled = isIntacctActive;
        this.isIntacctActive.value = isIntacctActive;
        this.toggleOnHoldState(this.$.isDirty);
    },
    navigationPanel: {
        listItem: {
            line16: ui.nestedFieldExtensions.text({
                title: 'Sage Intacct ID',
                bind: { intacctCustomer: { intacctId: true } },
                insertBefore: 'minimumOrderAmount',
                isHiddenOnMainField: true,
            }),
            line17: ui.nestedFieldExtensions.label({
                title: 'Sage Intacct integration status',
                bind: { intacctCustomer: { state: true } },
                optionType: '@sage/xtrem-communication/IntegrationState',
                insertBefore: 'minimumOrderAmount',
                isHiddenOnMainField: true,
                style: (_id, rowValue) =>
                    PillColorIntacct.getLabelColorByStatus('IntegrationState', rowValue?.intacctCustomer?.state),
            }),
        },
    },
})
export class CustomerExtension extends ui.PageExtension<CustomerPage, GraphApi> {
    @ui.decorators.referenceField<CustomerExtension, IntacctCustomer>({
        node: '@sage/xtrem-intacct-finance/IntacctCustomer',
        bind: { intacctCustomer: true },
        columns: [
            ui.nestedFields.technical({ bind: 'intacctId' }),
            ui.nestedFields.technical({ bind: 'lastMessage' }),
            ui.nestedFields.technical({ bind: 'url' }),
            ui.nestedFields.technical({ bind: 'state' }),
        ],
    })
    intacctReference: ui.fields.Reference<IntacctCustomer>;

    @ui.decorators.block<CustomerExtension>({
        parent() {
            return this.mainSection;
        },
        title: 'Sage Intacct',
        width: 'extra-large',
    })
    intacctBlock: ui.containers.Block;

    @ui.decorators.linkField<CustomerExtension>({
        parent() {
            return this.intacctBlock;
        },
        title: 'Sage Intacct ID',
        bind: { intacctCustomer: { intacctId: true } },
        async onClick() {
            const url = this.intacctReference.value?.url;
            const intacctUrl = !url && this._id.value ? await getIntacctUrl(this, this._id.value, 'customer') : url;
            this.$.router.goToExternal(intacctUrl || '');
        },
    })
    intacctUrlLink: ui.fields.Link;

    @ui.decorators.labelField<CustomerExtension>({
        parent() {
            return this.intacctBlock;
        },
        title: 'Integration status',
        optionType: '@sage/xtrem-communication/IntegrationState',
        width: 'large',
        bind: { intacctCustomer: { state: true } },
        style() {
            return PillColorIntacct.getLabelColorByStatus('IntegrationState', this.intacctIntegrationState.value);
        },
    })
    intacctIntegrationState: ui.fields.Label<IntegrationState>;

    @ui.decorators.podCollectionFieldOverride<CustomerExtension, BusinessEntityAddress>({
        columns: [
            ui.nestedFieldExtensions.label({
                bind: { intacctBusinessEntityAddress: { state: true } },
                title: 'Integration state',
                optionType: '@sage/xtrem-communication/IntegrationState',
                style: (_id, rowValue) =>
                    PillColorIntacct.getLabelColorByStatus(
                        'IntegrationState',
                        rowValue?.intacctBusinessEntityAddress?.state,
                    ),
            }),
            ui.nestedFieldExtensions.text({ bind: { intacctBusinessEntityAddress: { _id: true } }, isHidden: true }),
            ui.nestedFieldExtensions.text({
                bind: { intacctBusinessEntityAddress: { lastMessage: true } },
                isHidden: true,
            }),
            ui.nestedFieldExtensions.icon({
                title: 'Refresh',
                helperText: 'Synchronizing',
                isHelperTextHidden: true,
                bind: '_id',
                insertBefore: 'isShippingAddress',
                map: () => 'refresh',
                // XT-69128 rowData typing is incorrect
                async onError(_id, rowData: any) {
                    await integrationAddressOnError(this, rowData);
                },
                // XT-69128 rowData typing is incorrect
                async onClick(_id, rowData: any) {
                    await integrationAddressOnClick(this, rowData);
                },
            }),
        ],
    })
    addresses: ui.fields.PodCollection<BusinessEntityAddress>;

    @ui.decorators.iconField<CustomerExtension>({
        parent() {
            return this.intacctBlock;
        },
        title: 'Refresh',
        isTransient: true,
        helperText: 'Synchronizing',
        isHelperTextHidden: true,
        map: () => 'refresh',
        async onError() {
            await synchronizeOnError(this);
        },
        async onClick() {
            await synchronizeOnClick(this);
        },
    })
    synchronize: ui.fields.Icon;

    /** enhancement request : https://jira.sage.com/browse/XT-62899 */
    intacctAddress(address: ui.PartialNodeWithId<BusinessEntityAddress>) {
        noop(this); //
        if (address) {
            delete address.intacctBusinessEntityAddress;
        }
        return address;
    }
}

declare module '@sage/xtrem-master-data/build/lib/pages/customer' {
    interface Customer extends CustomerExtension {}
}
