import type { <PERSON>raph<PERSON><PERSON> } from '@sage/xtrem-intacct-finance-api';
import type { Item as ItemNode } from '@sage/xtrem-master-data-api';
import type { Item as ItemPage } from '@sage/xtrem-master-data/build/lib/pages/item';
import * as ui from '@sage/xtrem-ui';
import { getIntacctUrl, isActive } from '../client-functions/common';
import * as PillColorIntacct from '../client-functions/pill-color';

@ui.decorators.pageExtension<ItemExtension, ItemNode>({
    extends: '@sage/xtrem-master-data/Item',
    async onLoad() {
        this.intacctUrlLink.isDisabled = !((await isActive(this)) && this.intacctIntegrationState.value === 'success');
    },
    extensionAccessBinding: { node: '@sage/xtrem-intacct/Intacct', bind: 'defaultInstance' },
    navigationPanel: {
        listItem: {
            intacctState: ui.nestedFieldExtensions.label({
                title: 'Sage Intacct integration Status',
                optionType: '@sage/xtrem-communication/IntegrationState',
                bind: { intacctItem: { state: true } },
                insertAfter: 'status',
                isHiddenOnMainField: true,
                style: (_id, rowValue) =>
                    PillColorIntacct.getLabelColorByStatus('IntegrationState', rowValue.intacctItem?.state),
            }),
            intacctId: ui.nestedFieldExtensions.text({
                title: 'Sage Intacct ID',
                bind: { intacctItem: { intacctId: true } },
                insertAfter: 'status',
                isHiddenOnMainField: true,
            }),
        },
    },
})
export class ItemExtension extends ui.PageExtension<ItemPage, GraphApi> {
    @ui.decorators.block<ItemExtension>({
        parent() {
            return this.mainSection;
        },
        title: 'Sage Intacct',
        width: 'extra-large',
    })
    intacctBlock: ui.containers.Block;

    @ui.decorators.linkField<ItemExtension>({
        parent() {
            return this.intacctBlock;
        },
        title: 'Sage Intacct ID',
        isTransient: true,
        map() {
            return this.intacctId.value || '';
        },
        async onClick() {
            if (!this.intacctUrl.value && this._id.value) {
                this.intacctUrl.value = await getIntacctUrl(this, this._id.value, 'item');
            }
            this.$.router.goToExternal(this.intacctUrl.value || '');
        },
    })
    intacctUrlLink: ui.fields.Link;

    @ui.decorators.labelField<ItemExtension>({
        parent() {
            return this.intacctBlock;
        },
        title: 'Integration status',
        optionType: '@sage/xtrem-communication/IntegrationState',
        bind: { intacctItem: { state: true } },
        width: 'large',
        style: (_id, rowValue) =>
            PillColorIntacct.getLabelColorByStatus('IntegrationState', rowValue.intacctItem?.state),
    })
    intacctIntegrationState: ui.fields.Label;

    @ui.decorators.textField<ItemExtension>({
        parent() {
            return this.intacctBlock;
        },
        title: 'Sage Intacct ID',
        isHidden: true,
        bind: { intacctItem: { intacctId: true } },
    })
    intacctId: ui.fields.Text;

    @ui.decorators.textField<ItemExtension>({
        parent() {
            return this.intacctBlock;
        },
        title: 'Sage Intacct URL',
        isHidden: true,
        bind: { intacctItem: { url: true } },
    })
    intacctUrl: ui.fields.Text;

    @ui.decorators.textField<ItemExtension>({ bind: { intacctItem: { _id: true } } })
    intacctItemSysId: ui.fields.Text;

    @ui.decorators.textField<ItemExtension>({ bind: { intacctItem: { lastMessage: true } } })
    lastMessage: ui.fields.Text;

    @ui.decorators.iconField<ItemExtension>({
        parent() {
            return this.intacctBlock;
        },
        title: 'Synchronize',
        isTransient: true,
        helperText: 'Synchronizing',
        isHelperTextHidden: true,
        map() {
            return 'refresh';
        },
        async onError() {
            this.synchronize.isDisabled = false;
            this.intacctBlock.isDisabled = false;
            this.synchronize.isHelperTextHidden = true;
            await this.refreshIntacctBlock();
            if (this.intacctIntegrationState.value === 'error') {
                this.$.showToast(this.lastMessage.value || '', { type: 'error' });
            }
        },
        async onClick() {
            this.synchronize.isDisabled = true;
            this.synchronize.isHelperTextHidden = false;
            this.intacctBlock.isDisabled = true;

            if (!this.intacctItemSysId.value) {
                await this.$.graph.update({ values: { intacctItem: {} } });
                await this.intacctItemSysId.refresh();
            }
            if (this.intacctItemSysId.value) {
                const { state } = await this.$.graph
                    .node('@sage/xtrem-intacct-finance/IntacctListener')
                    .asyncOperations.synchronizeNode.runToCompletion(
                        { state: true },
                        {
                            intacctNode: this.intacctItemSysId.value,
                        },
                    )
                    .execute();
                if (state) {
                    await this.refreshIntacctBlock();
                }
                if (state === 'error') {
                    this.$.showToast(this.lastMessage.value || '', { type: 'error' });
                }
            }
            this.synchronize.isHelperTextHidden = true;
            this.intacctBlock.isDisabled = false;
            this.synchronize.isDisabled = false;
        },
    })
    synchronize: ui.fields.Icon;

    async refreshIntacctBlock() {
        await this.intacctId.refresh();
        await this.intacctIntegrationState.refresh();
        await this.intacctUrl.refresh();
        await this.intacctItemSysId.refresh();
        await this.lastMessage.refresh();
        this.intacctUrlLink.value = this.intacctId.value;
        this.intacctUrlLink.isDisabled = this.intacctIntegrationState.value !== 'success';
    }
}

declare module '@sage/xtrem-master-data/build/lib/pages/item' {
    export interface Item extends ItemExtension {}
}
