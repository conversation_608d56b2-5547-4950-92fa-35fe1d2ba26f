import { CustomSqlAction } from '@sage/xtrem-system';

export const setTaxType = new CustomSqlAction({
    description: 'Set the new property type for all existing taxes',
    body: async helper => {
        await helper.executeSql(`UPDATE ${helper.schemaName}.tax
	SET type = CASE
		WHEN intacct_tax_type = 'Purchase' THEN 'purchasing' ::${helper.schemaName}.tax_type_enum
		WHEN intacct_tax_type = 'Sale' THEN 'sales' ::${helper.schemaName}.tax_type_enum
		WHEN intacct_tax_type = '' THEN 'purchasingAndSales' ::${helper.schemaName}.tax_type_enum
		END;`);
    },
});
