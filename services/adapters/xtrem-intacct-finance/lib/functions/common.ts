import type { AnyR<PERSON>ord, Context, Logger } from '@sage/xtrem-core';
import { BusinessRuleError, SystemError } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as _ from 'lodash';
import * as xtremIntacctFinance from '..';
import { getTaxSolution } from './tax';

export const outboundAndBoth: xtremSynchronization.enums.SynchronizationDirection[] = ['outbound', 'both'];

const intacctSysProperty = [
    'intacctId',
    'recordNo',
    'uIntacctId',
    'intacctIntegrationState',
    'intacctLastIntegrationDate',
];

export function deleteProperty(object: any, property: string): void {
    function deleteProp(wObject: any, propertyArray: string[]) {
        if (!wObject) {
            return;
        }
        if (propertyArray.length === 1 && wObject[propertyArray[0]]) {
            delete wObject[propertyArray[0]];
            return;
        }
        deleteProp(wObject[propertyArray.shift() || ''], propertyArray);
    }

    const propertyArraySplited = property.split('.');

    deleteProp(object, propertyArraySplited);
}

/**
 * Deep diff between two object, using lodash
 * @param  {Object} object Object compared
 * @param  {Object} base   Object to compare with
 * @return {Object}        Return a new object who represent the diff
 */
export function deepDifference(object: any, base: any, intacctSysPropertyOveride?: string[]): any {
    function changes(upperObject: any, upperBase: any) {
        return _.transform(upperObject, (result: any, value, key) => {
            if (!_.isEqual(value, upperBase[key])) {
                result[key] = _.isObject(value) && _.isObject(upperBase[key]) ? changes(value, upperBase[key]) : value;
            }
        });
    }

    /** To not modify object pass in parameters  */
    const wObject = { ...object };
    const wBase = { ...base };
    const propertiesToExclude = intacctSysPropertyOveride || intacctSysProperty;
    propertiesToExclude.forEach(property => {
        deleteProperty(wObject, property);
        deleteProperty(wBase, property);
    });

    return changes(wObject, wBase);
}
/**
 *  Get all the path of the object
 * @param object object to get the path
 * @param currentPath used by recursivity
 * @returns
 */
export function allObjectPaths(object: any, currentPath?: string): string[] {
    return Object.getOwnPropertyNames(object).flatMap(property => {
        const pathToWrite = currentPath ? `${currentPath}.` : '';
        if (object[property] && typeof object[property] === 'object') {
            return allObjectPaths(object[property], `${pathToWrite}${property}`);
        }
        return `${pathToWrite}${property}`;
    });
}

/**
 *
 * @param obj1 current payload
 * @param obj2 old payload
 * @param logger to see modified fields in debugging
 * @returns  array of string properties
 */
export function getObjectDiff(diffParam: {
    obj1: any;
    obj2: any;
    logger?: Logger;
    excludePropertiesOveride?: string[];
    excludeProperties?: string[];
}): string[] {
    const { obj1, obj2, logger, excludePropertiesOveride, excludeProperties } = diffParam;

    const propertiesToExclude = excludePropertiesOveride || [...intacctSysProperty, ...(excludeProperties || [])];

    if (typeof obj1 !== typeof obj2) {
        throw new SystemError(`${typeof obj1} is different than ${typeof obj2}`);
    }

    const diff = Object.keys(obj1)
        .filter(intacctProperty => !propertiesToExclude.includes(intacctProperty))
        .reduce(
            (diffArray, key) => {
                if (!_.has(obj2, key)) {
                    diffArray.push(key);
                } else if (_.isEqual(obj1[key], obj2[key])) {
                    const resultKeyIndex = diffArray.indexOf(key);
                    diffArray.splice(resultKeyIndex, 1);
                }
                return diffArray;
            },
            Object.keys(obj2).filter(intacctProperty => !propertiesToExclude.includes(intacctProperty)),
        );
    if (logger) logger.debug(() => `Modified fields : ${diff.join(' , ')}`);
    if (logger) logger.debug(() => `DeepDifference : ${JSON.stringify(deepDifference(obj1, obj2))}`);

    return diff;
}

export function removeEmpty(obj: any): any {
    Object.entries(obj).forEach(
        ([key, val]) =>
            (val && typeof val === 'object' && removeEmpty(val)) || ((val === null || val === '') && delete obj[key]),
    );
    return obj;
}

/**
 *
 * @param context
 * @param XtremNode Xtrem node name
 * @returns The id of Intacct object name
 */
async function getIntacctObject(context: Context, xtremNodeName: string): Promise<string> {
    return (
        (
            await context.select(
                xtremIntacctFinance.nodes.IntacctMap,
                { id: true },
                { filter: { nodeFactory: { name: xtremNodeName } } },
            )
        ).at(0)?.id || ''
    );
}

/**
 *
 * @param context
 * @param recordNo Unique intacct recordNo
 * @param XtremNode Xtrem node name
 * @returns Intacct URL
 */
export async function getIntacctObjectUrl(context: Context, recordNo: number, XtremNode: string): Promise<string> {
    if (!recordNo) {
        return '';
    }
    const intacctQuery = new xtremIntacct.classes.sdk.Functions.Query<{ RECORD_URL: string }[]>(context, {
        objectName: await getIntacctObject(context, XtremNode),
        fields: [{ name: 'RECORD_URL', type: 'select' }],
        filter: new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('RECORDNO').equalTo(recordNo.toString()),
    });
    intacctQuery.showPrivate = true;

    return (await intacctQuery.execute()).at(0)?.RECORD_URL || '';
}

/**
 *  Get the map instance , control if we have all what we need to retrieve the data
 * @param context
 * @param intacctName
 */
export async function getMapInstance(
    context: Context,
    intacctNameId: string,
): Promise<xtremIntacctFinance.nodes.IntacctMap> {
    const mapInstance = await context
        .query(xtremIntacctFinance.nodes.IntacctMap, {
            filter: { application: '#intacct', id: intacctNameId },
        })
        .at(0);
    if (!mapInstance) {
        throw new BusinessRuleError(`No mapp instance ${intacctNameId} `);
    }
    if (!(await mapInstance.intacctIDField)) {
        throw new BusinessRuleError(`No intacct ID Field for ${await mapInstance.id} `);
    }
    return mapInstance;
}

/**
 *  Retrieve the payload from intacct instance
 * @param context
 * @param mapInstanceOrName
 * @param whereField
 * @param whereValue
 * @returns
 */
export async function getPayLoad(
    context: Context,
    mapInstanceOrName: xtremIntacctFinance.nodes.IntacctMap | string,
    filters?: xtremIntacct.interfaces.WhereFields[],
): Promise<AnyRecord[]> {
    const mapInstance =
        typeof mapInstanceOrName === 'string' ? await getMapInstance(context, mapInstanceOrName) : mapInstanceOrName;

    const data = await xtremIntacct.functions.getIntacctData(context, {
        fields: await mapInstance.intacctFields,
        objectName: await mapInstance.id,
        filters,
        isPrivateShow: await mapInstance.isPrivateShow,
    });

    return xtremIntacctFinance.functions.getPayLoadWithMapData(mapInstance, data);
}

/**
 * Retrieve the RECORDNO for a given EMPLOYEEID from intacct instance. This function is needed for the upgrade from V41/V42 to V43 of SDMO
 * because the RECORDNO on an "employee" attribute was originally set to the RECORDNO of the contact of the employee instead of the RECORDNO
 * of the employee itself. From V43 we no longer use the EMPLOYEEID for synchronization, but the RECORDNO and therefore we need the correct one
 * @param context
 * @param employeeId
 * @returns Intacct recordNo
 */
export async function getIntacctEmployeeRecordNo(context: Context, employeeId: string): Promise<number> {
    if (!employeeId) {
        return 0;
    }
    const intacctQuery = new xtremIntacct.classes.sdk.Functions.Query<{ RECORDNO: string }[]>(context, {
        objectName: 'EMPLOYEE',
        fields: [{ name: 'RECORDNO', type: 'select' }],
        filter: new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('EMPLOYEEID').equalTo(employeeId),
    });
    intacctQuery.showPrivate = true;

    return Number((await intacctQuery.execute()).at(0)?.RECORDNO) || 0;
}

type IntacctAccountInvoice =
    | xtremIntacctFinance.nodes.IntacctAccountsPayableInvoice
    | xtremIntacctFinance.nodes.IntacctAccountsReceivableInvoice;

/**
 * Computes the data to send in TAXSOLUTIONID
 * @param accountInvoice Intacct accounts payable or receivable invoice
 * @returns Intacct recordNo
 */
export async function getTaxSolutionId(accountInvoice: IntacctAccountInvoice): Promise<string> {
    const intacctConfiguration = await xtremIntacct.nodes.Intacct.defaultInstance(accountInvoice.$.context);
    if (['GB', 'ZA', 'FR', 'DE'].includes(await accountInvoice.getLegislationId()) && intacctConfiguration) {
        return (await getTaxSolution(intacctConfiguration, await accountInvoice.getCountryId())).at(0) || '';
    }
    return '';
}
