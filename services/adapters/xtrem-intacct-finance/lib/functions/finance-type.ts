import type { ExtensionMembers } from '@sage/xtrem-core';
import type * as xtremFinance from '@sage/xtrem-finance';
import type * as xtremIntacctFinance from '..';

export type FinanceDocumentExtensionMembersType =
    | ExtensionMembers<xtremIntacctFinance.nodeExtensions.JournalEntryExtension & xtremFinance.nodes.JournalEntry>
    | ExtensionMembers<
          xtremIntacctFinance.nodeExtensions.AccountsPayableInvoiceExtension & xtremFinance.nodes.AccountsPayableInvoice
      >
    | ExtensionMembers<
          xtremIntacctFinance.nodeExtensions.AccountsReceivableInvoiceExtension &
              xtremFinance.nodes.AccountsReceivableInvoice
      >
    | ExtensionMembers<
          xtremIntacctFinance.nodeExtensions.AccountsReceivablePaymentExtension &
              xtremFinance.nodes.AccountsReceivablePayment
      >
    | ExtensionMembers<
          xtremIntacctFinance.nodeExtensions.AccountsReceivableAdvanceExtension &
              xtremFinance.nodes.AccountsReceivableAdvance
      >;

export type FinanceDocumentType =
    | xtremIntacctFinance.nodeExtensions.JournalEntryExtension
    | xtremIntacctFinance.nodeExtensions.AccountsPayableInvoiceExtension
    | xtremIntacctFinance.nodeExtensions.AccountsReceivableInvoiceExtension
    | xtremIntacctFinance.nodeExtensions.AccountsReceivablePaymentExtension
    | xtremIntacctFinance.nodeExtensions.AccountsReceivableAdvanceExtension
    | FinanceDocumentExtensionMembersType;
