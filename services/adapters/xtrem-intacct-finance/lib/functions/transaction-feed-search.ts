import * as xtremCommunication from '@sage/xtrem-communication';
import type { Context, Diagnose, NodePayloadData } from '@sage/xtrem-core';
import type * as xtremImportExport from '@sage/xtrem-import-export';
import * as xtremIntacctFinance from '..';
import type { SearchIntacctDataParameters } from '../interfaces/bank-account-matching';
import { getPayLoad } from './common';

export function getIntacctTransactionFeed(
    context: Context,
    filters: xtremIntacctFinance.interfaces.BankAccountMatching.AccountMatchingQueryFilters,
): Promise<NodePayloadData<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed>[]> {
    /** get the BANKACCTTXNRECORD feed records  */
    return getPayLoad(
        context,
        'BANKACCTTXNRECORD',
        xtremIntacctFinance.functions.intacctQueries.filters.bankAccountTransactionFeed(filters),
    );
}

export async function isImportInProgress(context: Context, intacctImportSessionSysid: number): Promise<boolean> {
    const { status } = await context.read(xtremIntacctFinance.nodes.IntacctImportSession, {
        _id: intacctImportSessionSysid,
    });
    return (await status) === 'inProgress';
}

export async function errorImport(
    context: Context,
    envelope: xtremCommunication.NotificationEnvelope<SearchIntacctDataParameters>,
    error: Error,
) {
    await context.runInWritableContext(async (writableContext: Context) => {
        // error is a ErrorDetail but the interface is on clientSide so it's set to any to be able to get the extensions.diagnoses
        const errorMessage = (error as any).extensions?.diagnoses
            ?.map((diagnose: Diagnose) => `${diagnose.path} ${diagnose?.message || ''}`)
            .join('\n');
        const notification = { name: 'xtreemMassCreation', stack: error.stack };

        await xtremCommunication.nodes.SysNotificationHistory.createNotificationHistory(
            writableContext,
            envelope,
            'error',
            {
                ...notification,
                message: context.localize(
                    '@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction-feed/bank-manager-import-fail',
                    'Sage DMO Bank Manager : The import failed ({{filters}}). \n {{error}}.',
                    {
                        filters: `${
                            envelope.payload.filters.bankAccount
                        } - ${envelope.payload.filters.dateFrom.toString()} - ${envelope.payload.filters.dateTo.toString()}`,
                        error: errorMessage || error.message,
                    },
                ),
            },
        );
    });
}

export function updateImportSession(
    context: Context,
    intacctImportSessionSysid: number,
    length: number,
    status: xtremImportExport.enums.ImportStatus,
) {
    return context.runInWritableContext(async (writeContext: Context) => {
        const intacctImportSessionUpdate = await writeContext.read(
            xtremIntacctFinance.nodes.IntacctImportSession,
            {
                _id: intacctImportSessionSysid,
            },
            { forUpdate: true },
        );
        await intacctImportSessionUpdate.$.set({
            status,
            numberOfLinesToImport: length,
        });
        await intacctImportSessionUpdate.$.save();
        return intacctImportSessionUpdate;
    });
}
