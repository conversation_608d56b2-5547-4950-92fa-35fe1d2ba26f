import type { Context } from '@sage/xtrem-core';
import { asyncArray, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremTax from '@sage/xtrem-tax';

// Get legislation _id and id that have doApPosting or doArPosting(passed on filter)
async function getLegislationIdsDoApPostingOrDoArPosting(context: Context): Promise<string[]> {
    return (
        await context.select(
            xtremStructure.nodes.Legislation,
            { id: true },
            { filter: { _or: [{ doApPosting: true }, { doArPosting: true }] } },
        )
    ).map(legislation => legislation.id);
}

async function checkApInvoicesNotClosed(context: Context): Promise<void> {
    const numberOfApInvoicesNotPosted = await context.queryCount(xtremFinance.nodes.AccountsPayableInvoice, {
        filter: {
            postingStatus: { _ne: 'posted' },
            financialSite: { legalCompany: { legislation: { doArPosting: true } } },
        },
    });
    if (numberOfApInvoicesNotPosted > 0) {
        context.diagnoses.push({
            severity: ValidationSeverity.warn,
            path: [],
            message: context.localize(
                '@sage/xtrem-intacct-finance/page_extensions__there_are_ap_invoices_not_posted',
                'There are AP invoices that are not yet posted to the GL.',
            ),
        });
    }
}

async function checkArInvoicesNotClosed(context: Context): Promise<void> {
    const numberOfArInvoicesNotPosted = await context.queryCount(xtremFinance.nodes.AccountsReceivableInvoice, {
        filter: {
            postingStatus: { _ne: 'posted' },
            financialSite: { legalCompany: { legislation: { doArPosting: true } } },
        },
    });

    if (numberOfArInvoicesNotPosted > 0) {
        context.diagnoses.push({
            severity: ValidationSeverity.warn,
            path: [],
            message: context.localize(
                '@sage/xtrem-intacct-finance/page_extensions__there_are_ar_invoices_not_posted',
                'There are AR invoices that are not yet posted to the GL.',
            ),
        });
    }
}

// Check if there are any AP/AR invoices not closed for the legislations found with doApPosting or doArPosting
async function checkInvoicesNotClosed(context: Context): Promise<void> {
    await checkApInvoicesNotClosed(context);
    await checkArInvoicesNotClosed(context);
}

// Updates Tax Solution lines for each legislation found with doApPosting or doArPosting
async function updateTaxSolutionLines(
    parameters: xtremFinance.interfaces.UpdateTaxSolutionLinesParameters,
): Promise<number> {
    const taxSolutionIds = await xtremFinanceData.functions.Common.getTaxSolutionIdsForLegislation({
        context: parameters.context,
        legislationId: parameters.legislationId,
    });
    return parameters.context.bulkUpdate(xtremTax.nodes.TaxSolutionLine, {
        set: { isSubjectToGlTaxExcludedAmount: parameters.isSubjectToGlTaxExcludedAmount },
        where: {
            taxSolution: { _id: { _in: taxSolutionIds } },
            isSubjectToGlTaxExcludedAmount: !parameters.isSubjectToGlTaxExcludedAmount,
        },
    });
}

// Updates Accounts taxManagement to 'other' for each legislation found with doApPosting or doArPosting
function updateAccountsTaxManagement(
    parameters: xtremFinance.interfaces.UpdateAccountsTaxManagementParameters,
): Promise<number> {
    return parameters.context.bulkUpdate(xtremFinanceData.nodes.Account, {
        set: { taxManagement: 'other' },
        where: { chartOfAccount: { legislation: { id: { _in: parameters.legislationIds } } } },
    });
}

// Updates Tax Solution Lines and Accounts for each legislation found with doApPosting or doArPosting
async function updateTaxSolutionLinesAndAccountsByLegislation(
    parameters: xtremFinance.interfaces.UpdateTaxSolutionLinesAndAccountsByLegislationParameters,
): Promise<void> {
    await asyncArray(parameters.legislationIds).forEach(async legislationId => {
        await updateTaxSolutionLines({ ...parameters, legislationId });
    });
    if (parameters.updateAccounts) {
        await updateAccountsTaxManagement(parameters);
    }
}

/**
 * Updates Tax Solution Lines and Accounts according to the intacctOption service option value
 * @param context: Context
 * @param intactOption: Intacct service option value
 */
export async function intacctOptionChangeUpdates(context: Context, intactOption: boolean): Promise<void> {
    if (intactOption) {
        // Check if there are any AP/AR invoices not closed
        await checkInvoicesNotClosed(context);
    }

    // Update tax solution lines and accounts by legislation
    await updateTaxSolutionLinesAndAccountsByLegislation({
        context,
        legislationIds: await getLegislationIdsDoApPostingOrDoArPosting(context),
        isSubjectToGlTaxExcludedAmount: !intactOption,
        updateAccounts: intactOption,
    });
}
