import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacct from '@sage/xtrem-intacct';

// In order to be able to set intacct activation option on:
// 1 - an intacct connection must be active
// 2 - the payment tracking service option must be off
export async function intacctActivationOptionEnableControl(context: Context) {
    if ((await context.query(xtremIntacct.nodes.Intacct, { filter: { isActive: true } }).length) !== 1) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-intacct-finance/classes__structure-hooks__intacct_is_not_active',
                'Sage Intacct integration is not active.',
            ),
        );
    }

    if (await context.isServiceOptionEnabled(xtremFinanceData.serviceOptions.paymentTrackingOption)) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-intacct-finance/classes__structure-hooks__payment_tracking_is_active',
                'Intacct activation is not possible if payment tracking is active.',
            ),
        );
    }
}

// In order to be able to set intacct activation option off:
// 1 - there must be no intacct connection active
export async function intacctActivationOptionDisableControl(context: Context) {
    if ((await context.query(xtremIntacct.nodes.Intacct, { filter: { isActive: true } }).length) !== 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-intacct-finance/classes__structure-hooks__intacct_is_active',
                'Sage Intacct integration is active.',
            ),
        );
    }
}
