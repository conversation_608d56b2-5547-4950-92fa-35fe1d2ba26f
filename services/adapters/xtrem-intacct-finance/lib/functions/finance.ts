import type * as xtremCommunication from '@sage/xtrem-communication';
import type { Context, Logger, Node, ValidationContext } from '@sage/xtrem-core';
import { ValidationSeverity, asyncArray } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremIntacctFinance from '../index';
import type { ApArOpenItemsQuery, IntacctFinanceDocumentAmounts, ObjectName } from '../interfaces/finance';
import type { IntacctSynchronizationFinanceState } from '../nodes';
import { alreadySentToIntacct } from './finance-messages';
import type { FinanceDocumentExtensionMembersType, FinanceDocumentType } from './finance-type';

/**
 * Processes the tasks that need to be done on ar invoice posting.
 * @param arInvoice The AR invoice being processed
 */
async function postArInvoice(arInvoice: xtremFinance.nodes.AccountsReceivableInvoice): Promise<void> {
    if (!(await arInvoice.openItems.length)) {
        const openItemData = await xtremFinance.nodes.AccountsReceivableInvoice.newOpenItemPayload(arInvoice);
        const openItem = await arInvoice.$.context.create(xtremFinance.nodes.AccountsReceivableOpenItem, openItemData);
        await openItem.$.save();
    }
    if (await (await (await arInvoice.financialSite).legalCompany).doArPosting) {
        // send notification in order to create a staging table entry for the accounting engine
        await xtremFinanceData.functions.accountsReceivableInvoiceNotification(
            arInvoice.$.context,
            arInvoice,
            (await arInvoice.lines.toArray()) as xtremFinanceData.interfaces.ApArInvoiceLine[],
        );
    }
}

/**
 * Processes the tasks that need to be done on ap invoice posting.
 * @param apInvoice The AP invoice being processed
 */
async function postApInvoice(apInvoice: xtremFinance.nodes.AccountsPayableInvoice): Promise<void> {
    if (!(await apInvoice.openItems.length)) {
        const openItemData = await xtremFinance.nodes.AccountsPayableInvoice.newOpenItemPayload(apInvoice);
        const openItem = await apInvoice.$.context.create(xtremFinance.nodes.AccountsPayableOpenItem, openItemData);
        await openItem.$.save();
    }
    if (await (await (await apInvoice.financialSite).legalCompany).doArPosting) {
        // send notification in order to create a staging table entry for the accounting engine
        await xtremFinanceData.functions.accountsPayableInvoiceNotification(
            apInvoice.$.context,
            apInvoice,
            (await apInvoice.lines.toArray()) as xtremFinanceData.interfaces.ApArInvoiceLine[],
        );
    }
}

/**
 * When sending an ar payment or advance to intacct, update the finance transaction node without using notifications
 * @param context A context
 * @param targetDocumentNumber The target document number, i. e., the number of the journal entry or ap\ar invoice
 * @param intacctId The Intacct record id (not record number)
 * @param intacctUrl The Intacct object url
 * @param intacctIntegrationState The intacctIntegrationState
 * @param intacctIntegrationState The intacct integration error message, eventually
 */
export async function updateFinanceTransaction(
    context: Context,
    update: {
        targetDocument: { number: string; type: xtremFinanceData.enums.TargetDocumentType };
        intacctIntegration: {
            intacctId: string;
            url: string;
            message?: string;
            state: xtremCommunication.enums.IntegrationState;
        };
    },
): Promise<void> {
    const { targetDocument, intacctIntegration } = update;

    const financeIntegrationStatus: xtremFinanceData.enums.FinanceIntegrationStatus =
        xtremFinanceData.functions.getFinanceIntegrationStatusFromFinanceIntegrationAppStatus(intacctIntegration.state);

    const financeTransaction = await xtremFinanceData.functions.getFinanceTransaction(
        context,
        targetDocument.number,
        targetDocument.type,
    );

    if (financeTransaction) {
        const payload: xtremFinanceData.interfaces.FinanceTransactionData = {
            batchId: await financeTransaction.batchId,
            documentNumber: await financeTransaction.documentNumber,
            documentType: await financeTransaction.documentType,
            targetDocumentNumber: await financeTransaction.targetDocumentNumber,
            targetDocumentType: await financeTransaction.targetDocumentType,
            targetDocumentSysId: await financeTransaction.targetDocumentSysId,
            validationMessages: intacctIntegration.message
                ? [{ type: ValidationSeverity.error, message: intacctIntegration.message }]
                : [],
            status: financeIntegrationStatus,
            financeExternalIntegration: {
                app: 'intacct',
                recordId: intacctIntegration.intacctId,
                url: intacctIntegration.url,
            },
        };
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }
    // What about if we don't get a financeTransaction ?
}

/**
 * Updates the postingStatus of the document and processes the tasks that need to be done on posting.
 * @param financeDocument The journal entry or an AP\AR invoice being processed
 */
export async function afterPostDocument(financeDocument: FinanceDocumentExtensionMembersType): Promise<void> {
    const intacctDocument = await financeDocument.getSyncStateReference();
    if (!intacctDocument) {
        await financeDocument.$.context.logger.errorAsync(
            async () => `No intacct document for ${await financeDocument.$.getNaturalKeyValue()}`,
        );
        return;
    }
    const postingStatus = (await intacctDocument.state) === 'success' ? 'posted' : 'error';

    await financeDocument.$.set({ postingStatus });

    await financeDocument.$.context.batch.logMessage('test', `postingStatus : ${postingStatus}`);

    const targetDocumentType = await intacctDocument.getTargetDocumentType();

    if (postingStatus === 'posted' && postingStatus !== (await (await financeDocument.$.old).postingStatus)) {
        if (targetDocumentType === 'accountsReceivableInvoice') {
            await postArInvoice(financeDocument as xtremFinance.nodes.AccountsReceivableInvoice);
        }
        if (targetDocumentType === 'accountsPayableInvoice') {
            await postApInvoice(financeDocument as xtremFinance.nodes.AccountsPayableInvoice);
        }
    }

    // before was only done for AccountsReceivablePayment& AccountsReceivableAdvance why ?

    await financeDocument.$.context.batch.logMessage('test', `updateFinanceTransaction`);
    await updateFinanceTransaction(financeDocument.$.context, {
        targetDocument: {
            number: await financeDocument.number,
            type: targetDocumentType,
        },
        intacctIntegration: {
            intacctId: await intacctDocument.intacctId,
            url: await intacctDocument.url,
            state: await intacctDocument.state,
            message: financeDocument.pExternalIntegrationMessage,
        },
    });
}
/**
 * When sending a journal entry or an ap\ar invoice to intacct, reply back when the integration state changes.
 * @param context A context
 * @param targetDocumentNumber The target document number, i. e., the number of the journal entry or ap\ar invoice
 * @param targetDocumentSysId The target document system id, i. e., the _id of the journal entry or ap\ar invoice
 * @param intacctId The Intacct record id (not record number)
 * @param intacctUrl The Intacct object url
 * @param lines An array or journal entry lines or ap\ar invoice lines
 * @param intacctIntegrationState The intacctIntegrationState
 */
export async function replyToOriginalDocuments(
    context: Context,
    reply: {
        intacctDocument: IntacctSynchronizationFinanceState;
        document:
            | xtremFinance.nodes.JournalEntry
            | xtremFinance.nodes.AccountsPayableInvoice
            | xtremFinance.nodes.AccountsReceivableInvoice;
        message?: string;
    },
): Promise<void> {
    const financeTransactionRecords: xtremFinanceData.interfaces.FinanceTransactionRecord[] = [];
    const { intacctDocument, document, message } = reply;
    const financeIntegrationStatus: xtremFinanceData.enums.FinanceIntegrationStatus =
        xtremFinanceData.functions.getFinanceIntegrationStatusFromFinanceIntegrationAppStatus(
            await reply.intacctDocument.state,
        );

    await document.lines.forEach(line =>
        line.accountingStagingLines.forEach(
            async (
                intermediate:
                    | xtremFinance.nodes.JournalEntryLineStaging
                    | xtremFinanceData.nodes.AccountsPayableInvoiceLineStaging
                    | xtremFinanceData.nodes.AccountsReceivableInvoiceLineStaging,
            ) => {
                const accountingStaging = await intermediate.accountingStaging;
                if (
                    !(await asyncArray(financeTransactionRecords).some(
                        async element =>
                            element.batchId === (await accountingStaging.batchId) &&
                            element.documentNumber === (await accountingStaging.documentNumber) &&
                            element.documentType === (await accountingStaging.documentType) &&
                            element.targetDocumentType === (await accountingStaging.targetDocumentType),
                    ))
                ) {
                    const financeTransactionRecord: xtremFinanceData.interfaces.FinanceTransactionRecord = {
                        batchId: await accountingStaging.batchId,
                        documentNumber: await accountingStaging.documentNumber,
                        documentType: await accountingStaging.documentType,
                        targetDocumentType: await accountingStaging.targetDocumentType,
                    };
                    financeTransactionRecords.push(financeTransactionRecord);

                    const financeTransactionData: xtremFinanceData.interfaces.FinanceTransactionData = {
                        ...financeTransactionRecord,
                        targetDocumentNumber: await document.number,
                        targetDocumentSysId: document._id,
                        validationMessages: message ? [{ type: ValidationSeverity.error, message }] : [],
                        status: financeIntegrationStatus,
                        financeExternalIntegration: await intacctDocument.externalIntegration,
                        isJustForStatus: true, // this will tell the listener static function that update of finance transaction node was already done
                    };
                    // Finance reply:
                    // 1 - we update the finance transaction node
                    // 2 - if the originated we send a reply to the accounting staging node
                    await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, financeTransactionData);
                    if (
                        !(
                            reply.document instanceof xtremFinance.nodes.JournalEntry &&
                            !['purchaseInvoice', 'purchaseCreditMemo', 'salesInvoice', 'salesCreditMemo'].includes(
                                financeTransactionData.documentType,
                            )
                        )
                    ) {
                        context.logger.info(
                            `Finance reply from intacct integration of document ${await document.number}`,
                        );
                        await context.reply(await accountingStaging.replyTopic, financeTransactionData, {
                            replyId: await accountingStaging.originNotificationId,
                        });
                    }
                }
            },
        ),
    );
}

/**
 *  Control if the journal entry or ap/ar invoice is sent to intacct ( intacctIntegrationState set to pending or success or error )
 *  Allow changes of  intacctDocument to keep the logic of intacct integration
 *  Added isPrinted
 * @param cx ValidationContext
 * @param financeDocument a journal entry or an AP\AR invoice for which the control should be done
 */
export async function controlBeginAndDelete(
    cx: ValidationContext,
    financeDocument: FinanceDocumentType,
    logger: Logger,
): Promise<void> {
    if (financeDocument.skipCallApi) return;

    if (!(await xtremIntacct.functions.isIntacctActive(financeDocument.$.context))) {
        financeDocument.skipCallApi = true;
        return;
    }

    const objectDiff = xtremIntacctFinance.functions.getObjectDiff({
        obj1: await financeDocument.$.payload({ withIds: true }),
        obj2: await (await financeDocument.$.old).$.payload({ withIds: true }),
        logger,
        excludePropertiesOveride: ['isPrinted', 'postingStatus'],
    });
    if (objectDiff.length === 0) {
        return;
    }

    const integrationState = (await (await financeDocument.getSyncStateReference())?.state) || 'not';

    const sendToIntacct: xtremCommunication.enums.IntegrationState[] = ['pending', 'success'];
    if (sendToIntacct.includes(integrationState)) {
        alreadySentToIntacct({ cx, financeDocument, integrationState });
    }
}

/**
 *  Does the common saveBegin for a journal entry or an AP\AR invoice-extensions
 * @param financeDocument a journal entry or an AP\AR invoice for which the saveBegin should be done
 */
export async function saveBegin(financeDocument: FinanceDocumentExtensionMembersType): Promise<void> {
    if (financeDocument.skipCallApi) {
        return;
    }
    // still needed for number !! to be deleted
    await financeDocument.$.context.flushDeferredActions();

    // Create the intacct document if not existing
    if (!(await financeDocument.intacctDocument)) {
        await financeDocument.$.set({ intacctDocument: {} });
    }

    // Assure that document postingStatus is inProgress if we are posting the document (still necessary for journal entries)
    if (financeDocument.pIsPosting) {
        await financeDocument.$.set({ postingStatus: 'inProgress' });
    }
}

/**
 *  Does the common saveEnd for a journal entry or AP\AR invoice-extensions
 *  launch the AutomaticIFunction to send the document to intacct
 * @param financeDocument a journal entry or an AP\AR invoice for which the saveEnd should be done
 */
export async function saveEnd(financeDocument: FinanceDocumentExtensionMembersType & Node): Promise<void> {
    if (financeDocument.skipCallApi) {
        return;
    }
    const intacctNode = await financeDocument.intacctDocument;
    if (!intacctNode) {
        financeDocument.$.context.logger.warn('No intacct Node - synchronize aborted');
        return;
    }

    if (financeDocument.pIsPosting && ['not', 'pending', 'error'].includes(await intacctNode.state)) {
        await financeDocument.$.context.notify(
            'IntacctListener/synchronizeNode/start',
            { intacctNode: intacctNode._id },
            { replyTopic: 'SysNotificationState/updateStatus' },
        );
    }
}

type UpdateOpenItemParameters = {
    context: Context;
    objectName: ObjectName;
    amounts: IntacctFinanceDocumentAmounts;
    status: xtremFinanceData.enums.OpenItemStatus;
    openItemSysId: number;
};

async function updateOpenItem(parameters: UpdateOpenItemParameters) {
    const openItemToUpdate = await parameters.context.read(
        xtremFinanceData.nodes.BaseOpenItem,
        { _id: parameters.openItemSysId },
        { forUpdate: true },
    );
    await openItemToUpdate.$.set({ ...parameters.amounts, status: parameters.status });
    await openItemToUpdate.$.save();
}

function getUpdatedStatus(
    amountDues: {
        transaction: number;
        company: number;
        financialSite: number;
    },
    payments: {
        total: number;
        trxTotal: number;
    },
): xtremFinanceData.enums.OpenItemStatus {
    if (
        amountDues.transaction === payments.trxTotal &&
        amountDues.company === payments.total &&
        amountDues.financialSite === payments.total
    ) {
        return 'paid';
    }
    return payments.trxTotal !== Number(0) ? 'partiallyPaid' : 'notPaid';
}

export async function updateOpenItemFromIntacct(
    context: Context,
    objectName: ObjectName,
    financeDocument: xtremFinance.nodes.AccountsPayableInvoice | xtremFinance.nodes.AccountsReceivableInvoice,
    logger?: Logger,
) {
    const intacctDocument = await financeDocument.getSyncStateReference();
    logger?.info(
        `${objectName}: Getting payment data for document with integration app ${await financeDocument.financeIntegrationApp},
        recordno ${await intacctDocument?.recordNo} and ${await financeDocument.openItems.length} open items.`,
    );
    const financialSite = await financeDocument.financialSite;

    if (
        (await financeDocument.financeIntegrationApp) === 'intacct' &&
        Number((await financeDocument.financeIntegrationAppRecordId) || 0) > 0 &&
        (await financeDocument.openItems.length) === 1 &&
        (await (
            await financialSite.legalCompany
        ).doUpdateArAmountPaid)
    ) {
        const fields: (string | xtremIntacct.interfaces.QueryFields)[] = [
            { name: 'TRX_TOTALPAID', type: 'select' },
            { name: 'TOTALPAID', type: 'select' },
        ];

        // depending on transactionIntegrationLevel on Intacct settings:
        // if 'entityLevel': we read the ar invoice at entityLevel on Intacct (with the entityId = financialSite.id)
        // if 'topLevel': we read the ar invoice at the topLevel on Intacct (empty entityId)
        const defaultInstance = await xtremIntacct.nodes.Intacct.defaultInstance(context);
        const entityId =
            (await defaultInstance?.transactionIntegrationLevel) === 'entityLevel' ? await financialSite?.id : '';

        const intacctFinanceDocument = await new xtremIntacct.classes.sdk.Functions.Query<ApArOpenItemsQuery[]>(
            context,
            {
                objectName,
                fields,
                entityId,
                filter: new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('RECORDNO').equalTo(
                    (await financeDocument.financeIntegrationAppRecordId)?.toString() || '',
                ),
            },
        ).execute();

        logger?.info(() => `Documents found on intacct: ${intacctFinanceDocument.length}`);
        logger?.debug(() => `Intacct document found ${JSON.stringify(intacctFinanceDocument)}`);

        if (intacctFinanceDocument.length === 1) {
            const openItem = await financeDocument.openItems.elementAt(0);
            const amounts: IntacctFinanceDocumentAmounts = {
                transactionAmountPaid: intacctFinanceDocument.at(0)?.TRX_TOTALPAID ?? 0,
                companyAmountPaid: intacctFinanceDocument.at(0)?.TOTALPAID ?? 0,
                financialSiteAmountPaid: intacctFinanceDocument.at(0)?.TOTALPAID ?? 0,
            };

            await updateOpenItem({
                context,
                amounts,
                objectName,
                status: getUpdatedStatus(
                    {
                        transaction: Number(await openItem.transactionAmountDue),
                        company: Number(await openItem.companyAmountDue),
                        financialSite: Number(await openItem.financialSiteAmountDue),
                    },
                    {
                        total: Number(Math.abs(amounts.companyAmountPaid)),
                        trxTotal: Number(Math.abs(amounts.transactionAmountPaid)),
                    },
                ),
                openItemSysId: openItem._id,
            });

            financeDocument.pCanUpdateFromExternalIntegration = true;
            financeDocument.skipCallApi = true;

            await financeDocument.$.save();
        }
    }
}
