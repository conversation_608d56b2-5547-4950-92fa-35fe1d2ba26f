import { Logger } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { synchronize } from './synchronization';

const logger = Logger.getLogger(__filename, 'sync');

export async function synchronizeCustomerSupplier(businessEntity: xtremMasterData.nodes.BusinessEntity): Promise<{
    customer: boolean;
    supplier: boolean;
}> {
    /** isCustomer & isSupplier must be computed properties !  */
    logger.debug(() => 'synchronizeCustomerSupplier');
    const isSynchronized = { customer: false, supplier: false };

    const customer = await businessEntity.$.context.tryRead(xtremMasterData.nodes.Customer, { businessEntity });
    if (customer) {
        isSynchronized.customer = await synchronize(customer);
    }

    const supplier = await businessEntity.$.context.tryRead(xtremMasterData.nodes.Supplier, { businessEntity });
    if (supplier) {
        isSynchronized.supplier = await synchronize(supplier);
    }

    return isSynchronized;
}
