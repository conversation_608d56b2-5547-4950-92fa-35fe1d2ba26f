import type * as xtremMasterData from '@sage/xtrem-master-data';
import type { SyncState } from '../../interfaces/synchronization';

export async function contactPayload(beAddress: xtremMasterData.nodes.BusinessEntityAddress): Promise<SyncState> {
    return {
        _id: (await beAddress.intacctBusinessEntityAddress)?._id || 0,
        node: 'BusinessEntityAddress',
        state: (await (await beAddress.intacctBusinessEntityAddress)?.state) || 'not',
        sysId: beAddress._id.toString(),
        intacctId: (await (await beAddress.intacctBusinessEntityAddress)?.intacctId) || '',
    };
}

export async function contactPayloadContinueOnError(
    beAddress: xtremMasterData.nodes.BusinessEntityAddress,
): Promise<SyncState> {
    return { ...(await contactPayload(beAddress)), continueOnError: true };
}

export async function customerPayload(customer: xtremMasterData.nodes.Customer): Promise<SyncState> {
    return {
        _id: (await customer.intacctCustomer)?._id || 0,
        node: 'Customer',
        state: (await (await customer.intacctCustomer)?.state) || 'not',
        sysId: customer._id.toString(),
        intacctId: (await (await customer.intacctCustomer)?.intacctId) || '',
    };
}

export async function customerPayloadContinueOnError(customer: xtremMasterData.nodes.Customer): Promise<SyncState> {
    return { ...(await customerPayload(customer)), continueOnError: true };
}

export async function supplierPayload(supplier: xtremMasterData.nodes.Supplier): Promise<SyncState> {
    return {
        _id: (await supplier.intacctSupplier)?._id || 0,
        node: 'Supplier',
        state: (await (await supplier.intacctSupplier)?.state) || 'not',
        sysId: supplier._id.toString(),
        intacctId: (await (await supplier.intacctSupplier)?.intacctId) || '',
    };
}

export async function supplierPayloadContinueOnError(supplier: xtremMasterData.nodes.Supplier): Promise<SyncState> {
    return { ...(await supplierPayload(supplier)), continueOnError: true };
}

export async function itemPayload(item: xtremMasterData.nodes.Item): Promise<SyncState> {
    return {
        _id: (await item.intacctItem)?._id || 0,
        node: 'Item',
        state: (await (await item.intacctItem)?.state) || 'not',
        sysId: item._id.toString(),
        intacctId: (await (await item.intacctItem)?.intacctId) || '',
    };
}

export async function itemPayloadContinueOnError(item: xtremMasterData.nodes.Item): Promise<SyncState> {
    return { ...(await itemPayload(item)), continueOnError: true };
}
