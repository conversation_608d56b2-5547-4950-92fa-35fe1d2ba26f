import type { Context } from '@sage/xtrem-core';
import { Logger, NodeStatus, ValidationSeverity } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSynchronization from '@sage/xtrem-synchronization';
import { isEmpty, set } from 'lodash';
import * as xtremIntacctFinance from '../..';
import type { SyncState } from '../../interfaces/synchronization';

const logger = Logger.getLogger(__filename, 'sync');

export function isSyncState(object: any): object is SyncState {
    return object && 'node' in object && '_id' in object && 'sysId' in object && 'state' in object;
}

export async function doWeLaunchIt(
    nodeThis: xtremSynchronization.interfaces.SynchronizationNode,
    mappedPropertiesNames: string[],
): Promise<{
    isPropertiesUpdate: boolean;
    isMappedPropertyUpdate: boolean;
}> {
    if (nodeThis?.$.status === NodeStatus.modified) {
        const updatedXtreemProperties = xtremIntacctFinance.functions.getObjectDiff({
            obj1: await nodeThis.$.payload({ withIds: true }),
            obj2: await (await nodeThis.$.old).$.payload({ withIds: true }),
            logger,
        });
        const rootProperty = mappedPropertiesNames.map(property => property.split('.').shift());
        return {
            isPropertiesUpdate: updatedXtreemProperties.length > 0,
            isMappedPropertyUpdate: updatedXtreemProperties.some(fields => rootProperty.includes(fields)),
        };
    }
    return {
        isPropertiesUpdate: true,
        isMappedPropertyUpdate: true,
    };
}

export async function deleteIntacct(context: Context, param: { intacctNode: string; recordNo?: number }) {
    if (param.recordNo) {
        await context.notify(
            'IntacctListener/deleteIntacct/start',
            {
                intacctNode: param.intacctNode,
                recordNo: param.recordNo.toString(),
            },
            { replyTopic: 'SysNotificationState/updateStatus' },
        );
    }
}

export async function synchronize(
    nodeThis: xtremSynchronization.interfaces.SynchronizationNode,
    intacctNode?: xtremSynchronization.nodes.SynchronizationState,
) {
    const intacctNodeSynchronized = intacctNode ?? (await nodeThis?.getSyncStateReference());
    if (!intacctNodeSynchronized) {
        logger.warn('No intacct Node - synchronize aborted');
        return false;
    }

    if (intacctNodeSynchronized._id < 0) {
        logger.warn(`Intacct node is not created ${nodeThis.$.factory.name} ${nodeThis.$.getNaturalKeyValue()} `);
        return false;
    }

    /** Before launching notify we increment the version */
    await nodeThis.$.context.bulkUpdate(xtremSynchronization.nodes.SynchronizationState, {
        set: {
            async version() {
                return (await this.version) + 1;
            },
        },
        where: {
            _id: intacctNodeSynchronized._id,
        },
    });

    if ((await intacctNodeSynchronized.state) === 'pending') {
        logger.warn(
            `${await (
                await intacctNodeSynchronized.node
            ).title} ${await intacctNodeSynchronized.sysId}:  Synchronization already in progress`,
        );
        return false;
    }
    await logger.debugAsync(
        async () => `Launch notify from :  Node: ${nodeThis.$.factory.name}
                            Key: ${await nodeThis.$.getNaturalKeyValue()}
                            V${await intacctNodeSynchronized.version} `,
    );

    await nodeThis.$.context.notify(
        'IntacctListener/synchronizeNode/start',
        {
            intacctNode: intacctNodeSynchronized._id,
        },
        { replyTopic: 'SysNotificationState/updateStatus' },
    );

    return true;
}

export async function doWeSynchronize(
    nodeThis: xtremSynchronization.interfaces.SynchronizationNode,
): Promise<{ isSynchronize: boolean; intacctNode?: xtremSynchronization.nodes.SynchronizationState }> {
    const intacctNode = await nodeThis.getSyncStateReference();
    if (!intacctNode) {
        nodeThis.$.context.logger.warn('No intacct Node - synchronize aborted');
        return { isSynchronize: false };
    }

    const isImport = nodeThis.$.context.source === 'import';
    if (nodeThis.skipCallApi || isImport) {
        logger.debug(() => ` skipCallIntacctApi: ${nodeThis.skipCallApi} - isImport:${isImport}`);
        return { isSynchronize: false };
    }

    if ((await intacctNode?.version) === 0) {
        await logger.debugAsync(async () => ` First creation of the intacctNode Version${await intacctNode?.version}`);
        return { isSynchronize: true, intacctNode };
    }

    logger.debug(() => ` Node status is ${nodeThis.$.status}`);
    if (nodeThis.$.status === NodeStatus.unchanged) {
        return { isSynchronize: false };
    }

    const { isMappedPropertyUpdate } = await doWeLaunchIt(
        nodeThis,
        await (
            await intacctNode.getMapping()
        ).mappedPropertyNames,
    );
    if (!isMappedPropertyUpdate) {
        logger.debug(() => 'No changes on mappedProperties');
        /** No changes on mappedProperties */
        return { isSynchronize: false };
    }
    return { isSynchronize: true, intacctNode };
}

/**
 *  Launch the intacct integration for an IntacctNode
 * @param nodeThis
 * @returns
 */
export async function launchSynchronization(
    nodeThis: xtremSynchronization.interfaces.SynchronizationNode,
): Promise<boolean> {
    const { isSynchronize, intacctNode } = await doWeSynchronize(nodeThis);

    return isSynchronize && intacctNode ? synchronize(nodeThis, intacctNode) : false;
}

export async function doWebusinessEntitySynchronize(nodeThis: xtremMasterData.nodes.BusinessEntity) {
    if (nodeThis.$.status !== NodeStatus.modified) {
        return false;
    }
    const propertyList = (
        await nodeThis.$.context.select(
            xtremMetadata.nodes.MetaNodeProperty,
            { name: true },
            {
                filter: {
                    factory: '#BusinessEntity',
                    isStored: true,
                },
            },
        )
    ).map(select => select.name);

    const rootProperty = [
        ...(await (
            await nodeThis.$.context.read(xtremIntacctFinance.nodes.IntacctMap, {
                application: '#intacct',
                thirdPartyObjectName: 'VENDOR',
                nodeFactory: '#Supplier',
            })
        ).mappedPropertyNames),
        ...(await (
            await nodeThis.$.context.read(xtremIntacctFinance.nodes.IntacctMap, {
                application: '#intacct',
                thirdPartyObjectName: 'CUSTOMER',
                nodeFactory: '#Customer',
            })
        ).mappedPropertyNames),
    ].map(prop => prop.split('.'));

    const propertyNames = {};

    rootProperty
        .filter(mappedPropertie => propertyList.includes(mappedPropertie[0]))
        .forEach(prop => {
            set(propertyNames, prop[0], { _id: true });
        });

    if (isEmpty(propertyNames)) {
        return false;
    }

    const updatedXtreemProperties = xtremIntacctFinance.functions.getObjectDiff({
        obj1: await nodeThis.$.payload({ withIds: true, propertyNames }),
        obj2: await (await nodeThis.$.old).$.payload({ withIds: true, propertyNames }),
        logger,
    });

    return !!updatedXtreemProperties.length;
}

export async function setDesynchronizedXtrem(
    context: Context,
    intacct: { name: string; id: string },
): Promise<xtremSynchronization.sharedFunctions.IntegrationDifference[]> {
    return (
        await xtremIntacctFinance.classes.IntacctSynchronizationManager.createFromIntacct(context, intacct)
    )?.eventDesynchronized();
}

export async function desynchronizedEventFromIntacct(context: Context, intacct: { name: string; id: string }) {
    const desynchronized = await setDesynchronizedXtrem(context, intacct);

    return desynchronized.length
        ? desynchronized.map(diff => {
              return {
                  severity: ValidationSeverity.error,
                  path: ['xtremIntacctFinance', 'desynchronized', 'setDesynchronizedXtrem'],
                  message: `${diff.name}: ${diff.thirdPartyValue} / ${diff.xtreemValue}`,
              };
          })
        : [
              {
                  severity: ValidationSeverity.info,
                  path: ['xtremIntacctFinance', 'desynchronized', 'setDesynchronizedXtrem'],
                  message: `${intacct.id} Not desynchronized`,
              },
          ];
}
