import type * as xtremIntacctFinance from '../..';

// Check when receiving a field value is '' when the only options are 'true' or 'false'
// If '' = 'false'
export function formatIntacctData(
    intacctData: any,
    fields: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties[],
) {
    Object.entries(intacctData)
        .filter(([, value]) => value === '')
        .forEach(([key]) => {
            if (
                fields.filter(
                    intacctProperty =>
                        intacctProperty.ID === key &&
                        intacctProperty.DATATYPE === 'TEXT' &&
                        intacctProperty.VALIDVALUES &&
                        intacctProperty.VALIDVALUES.VALIDVALUE.toString() === 'true,false',
                ).length
            ) {
                intacctData[key] = 'false';
            }
        });

    return intacctData;
}
