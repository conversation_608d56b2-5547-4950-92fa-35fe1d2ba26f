import type { AnyRecord, Context, NodePayloadData } from '@sage/xtrem-core';
import { asyncArray, Diagnose, LocalizedError, Logger, ValidationSeverity } from '@sage/xtrem-core';
import type * as xtremIntacct from '@sage/xtrem-intacct';
import * as _ from 'lodash';
import * as xtremIntacctFinance from '../../index';
import { getMapInstance, getPayLoad } from './common';

const logger = Logger.getLogger(__filename, 'map-lib');

export async function getPayLoadWithMapData(
    mapInstance: xtremIntacctFinance.nodes.IntacctMap,
    data: AnyRecord[],
): Promise<AnyRecord[]> {
    const payloadArray: any[] = [];
    const { context } = mapInstance.$;

    await asyncArray(data).forEach(async dataLine => {
        const payload = {};

        const relationMapping = await mapInstance.relationMapping;
        const { relationshipFields } = relationMapping;
        /**
         * Simple fields
         */
        await asyncArray(relationMapping.fields)
            .filter(line => !!line.xtremProperty)
            .forEach(async field => {
                if (!field.xtremProperty) return;
                await asyncArray(field.xtremProperty.split(',')).forEach(async property => {
                    const value = await xtremIntacctFinance.functions.mapping.transformToPayload(
                        context,
                        dataLine,
                        field,
                    );
                    /**
                     *   the == null will get undefined & null we want to keep false values
                     */
                    if (value != null) {
                        _.set(payload, property.trim(), value);
                    }
                });
            });

        /**
         * Fields with intacct references
         */
        if (relationshipFields) {
            await asyncArray(relationshipFields)
                .filter(line => !!line.xtremProperty)
                .forEach(async field => {
                    const value = await xtremIntacctFinance.functions.mapping.transformToPayload(
                        context,
                        dataLine,
                        field,
                    );
                    /**
                     *   the == null will get undefined & null we want to keep false values
                     */
                    if (value != null) {
                        _.set(payload, field.xtremProperty?.trim() || '', value);
                    }
                });
        }
        /**
         * Collections Fields
         */
        await mapInstance.lines.forEach(async line => {
            await logger.debugAsync(
                async () => `get the lines on ${await (await line.line).intacctDescription} intacct object`,
            );

            const mapInstanceLine = await getMapInstance(context, await (await line.line).id);

            const payloadLine = await getPayLoad(context, mapInstanceLine, [
                {
                    where: await line.propertyHeader,
                    whereValue: _.get(dataLine, await line.propertyHeader) as string,
                },
            ]);

            _.set(payload, await line.collectionName, payloadLine);
        });
        payloadArray.push(payload);
    });

    return payloadArray;
}

async function getInstance(
    writableContext: Context,
    createData: {
        instanceSysId: number;
        xtremNode: typeof xtremIntacct.nodes.IntacctNode;
        xtremPayload: NodePayloadData<any>;
    },
) {
    if (createData.instanceSysId > 0) {
        // Updating  - we have an Sage DMO instance to update
        const updatableInstance = await writableContext.read(
            createData.xtremNode,
            {
                _id: createData.instanceSysId,
            },
            { forUpdate: true },
        );
        logger.debug(() => `Update : ${JSON.stringify(createData.xtremPayload)}`);
        await updatableInstance.$.set(createData.xtremPayload);
        return { updatableInstance, isCreated: false };
    }

    return {
        updatableInstance: await writableContext.create(createData.xtremNode, createData.xtremPayload),
        isCreated: true,
    };
}

async function createOrUpdateFromIntacct(
    writableContext: Context,
    createData: {
        instanceSysId: number;
        xtremNode: typeof xtremIntacct.nodes.IntacctNode;
        xtremPayload: NodePayloadData<any>;
    },
): Promise<{ isCreated: boolean; isUpdated: boolean; isError: boolean; diagnoses: Diagnose[] }> {
    const { updatableInstance, isCreated } = await getInstance(writableContext, createData);
    await updatableInstance.$.save();
    // Save is success
    const successSave = writableContext.localize(
        '@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-created-updated',
        'Sage DMO {{created}}: {{id}} ',
        {
            id: (await updatableInstance?.intacctId) || '',
            created: isCreated ? 'created' : 'updated',
        },
    );
    logger.debug(() => successSave);
    return {
        isCreated,
        isUpdated: !isCreated,
        isError: false,
        diagnoses: [new Diagnose(ValidationSeverity.info, ['createOrUpdateFromIntacct', 'updateXtrem'], successSave)],
    };
}

/**
 *
 * @param context must be readOnly
 * @param instanceSysId  sysId to update ( if -1 will be created )
 * @param xtremNode Node to create
 * @param xtremPayload Payload for create/update
 */
export function createOrUpdateFromIntacctCall(
    context: Context,
    createData: {
        instanceSysId: number;
        xtremNode: typeof xtremIntacct.nodes.IntacctNode;
        xtremPayload: NodePayloadData<any>;
    },
): Promise<{ isCreated: boolean; isUpdated: boolean; isError: boolean; diagnoses: Diagnose[] }> {
    return context.runInWritableContext(async writableContext => {
        try {
            return await createOrUpdateFromIntacct(writableContext, createData);
        } catch (error) {
            logger.debug(error.message);
            if (!(error instanceof LocalizedError)) throw error;

            const message = error.getMessageAndDiagnosesText(writableContext.diagnoses);
            return {
                isCreated: false,
                isUpdated: false,
                isError: true,
                diagnoses: [
                    {
                        message,
                        severity: ValidationSeverity.error,
                        path: [],
                    },
                    new Diagnose(
                        ValidationSeverity.error,
                        ['createOrUpdateFromIntacct', 'updateXtrem'],
                        context.localize(
                            '@sage/xtrem-intacct-finance/page__intacct-map/xtrem-instance-error',
                            'Sage Intacct ID __{{intacctId}}__ not saved.',
                            { intacctId: (await createData.xtremPayload?.intacctId) || '??' },
                        ),
                    ),
                ],
            };
        }
    });
    // To be able to continue without the saved node,
}
