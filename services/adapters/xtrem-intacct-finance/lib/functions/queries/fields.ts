/** Fields & constants for intacct queries  */

export const intacctDateFormat = 'MM/DD/YYYY';

export namespace CommonFields {
    /** Common intacct fields - MEGAENTITYID */
    export const MEGAENTITYID = 'MEGAENTITYID';
    /** Common intacct fields - RECORD_URL */
    export const RECORD_URL = 'RECORD_URL';
    /** Common intacct fields - RECORDNO */
    export const RECORDNO = 'RECORDNO';
    /** Common - DESCRIPTION  */
    export const DESCRIPTION = 'DESCRIPTION';
    /** Common - RECORDID  */
    export const RECORDID = 'RECORDID';
    /** Commin Date creation */
    export const WHENCREATED = 'WHENCREATED';
}

export namespace ArInvoiceFields {
    // Ar Invoice FIELDS
    export const CUSTOMERNAME = 'CUSTOMERNAME';
    export const CUSTOMERID = 'CUSTOMERID';
    export const DOCNUMBER = 'DOCNUMBER';
    export const WHENDUE = 'WHENDUE';
    export const TERMNAME = 'TERMNAME';
    export const CURRENCY = 'CURRENCY';
    export const AMOUNT = 'AMOUNT';
    export const LOCATIONID = 'LOCATIONID';
    export const BASECURRENCY = 'BASECURR';
    export const TOTALDUE = 'TOTALDUE';
    export const TOTALPAID = 'TOTALPAID';
    export const TOTALENTERED = 'TOTALENTERED';
    export const TRX_TOTALENTERED = 'TRX_TOTALENTERED';
    export const TRX_TOTALDUE = 'TRX_TOTALDUE';
    export const TRX_TOTALPAID = 'TRX_TOTALPAID';
}

export namespace JournalEntryFields {
    // GLBATCH & GLENTRY Fields
    export const GLBATCH__RECORDNO = 'GLBATCH.RECORDNO';
    export const GLBATCH__BATCHNO = 'GLBATCH.BATCHNO';
    export const GLBATCH__BATCH_TITLE = 'GLBATCH.BATCH_TITLE';
    export const GLBATCH__BASELOCATION = 'GLBATCH.BASELOCATION';
    export const GLBATCH__BATCH_DATE = 'GLBATCH.BATCH_DATE';
    export const GLBATCH__JOURNAL = 'GLBATCH.JOURNAL';
    export const GLBATCH__MEGAENTITYID = 'GLBATCH.MEGAENTITYID';
    export const ACCOUNTNO = 'ACCOUNTNO';
    export const LOCATION = 'LOCATION';
    export const AMOUNT = 'AMOUNT';
}

/** all arInvoice fields  */
export const arInvoiceFieldsArray = [
    CommonFields.DESCRIPTION,
    CommonFields.RECORDNO,
    CommonFields.RECORDID,
    ArInvoiceFields.CUSTOMERNAME,
    ArInvoiceFields.CUSTOMERID,
    ArInvoiceFields.DOCNUMBER,
    CommonFields.WHENCREATED,
    ArInvoiceFields.WHENDUE,
    ArInvoiceFields.TERMNAME,
    ArInvoiceFields.CURRENCY,
    ArInvoiceFields.TOTALDUE,
    ArInvoiceFields.TOTALPAID,
    ArInvoiceFields.TOTALENTERED,
    ArInvoiceFields.TRX_TOTALENTERED,
    ArInvoiceFields.TRX_TOTALDUE,
    ArInvoiceFields.TRX_TOTALPAID,
    CommonFields.RECORD_URL,
    CommonFields.MEGAENTITYID,
];

export const journalEntryFieldsArray = [
    JournalEntryFields.GLBATCH__RECORDNO,
    JournalEntryFields.GLBATCH__BATCHNO,
    JournalEntryFields.GLBATCH__BATCH_TITLE,
    JournalEntryFields.GLBATCH__BASELOCATION,
    JournalEntryFields.GLBATCH__BATCH_DATE,
    JournalEntryFields.GLBATCH__JOURNAL,
    JournalEntryFields.GLBATCH__MEGAENTITYID,
    JournalEntryFields.LOCATION,
    JournalEntryFields.AMOUNT,
    JournalEntryFields.ACCOUNTNO,
    CommonFields.RECORD_URL,
];
