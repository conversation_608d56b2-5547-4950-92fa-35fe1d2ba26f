import type { Context } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremIntacctFinance from '../..';
import type {
    IntacctArInvoice,
    IntacctDocumentParameters,
    IntacctDocumentType,
    IntacctJournalEntry,
} from '../../interfaces/bank-account-matching';
import { arInvoiceFieldsArray, journalEntryFieldsArray } from './fields';
import * as filters from './filters';
import * as transformers from './transformers';

/**
 * Query to fetch customers (id and name) from intacct
 * @returns a list of intacct customers
 */
export function customerList(context: Context) {
    const intacctQuery = new xtremIntacct.classes.sdk.Functions.Query(context, {
        objectName: 'CUSTOMER',
        fields: ['CUSTOMERID', 'NAME'],
        resultFunction: transformers.customerList,
    });
    return intacctQuery.execute();
}

/** request to GLENTRY  */
export async function journalEntry(
    context: Context,
    parameters: IntacctDocumentParameters,
): Promise<IntacctJournalEntry[]> {
    const parametersQuery: xtremIntacct.interfaces.Query<
        xtremIntacctFinance.interfaces.BankAccountMatching.IntacctJournalEntry[]
    > = { objectName: 'GLENTRY', fields: journalEntryFieldsArray, resultFunction: transformers.journalEntry };

    const filterArray = filters.journaLEntry(parameters);

    if (filterArray.length === 0) {
        return [];
    }

    parametersQuery.entityId = parameters.megaEntityId;

    const intacctQuery = new xtremIntacct.classes.sdk.Functions.Query(context, {
        ...parametersQuery,
        filter: filters.manageFiltersAnd(filterArray),
    });

    intacctQuery.showPrivate = !parametersQuery.entityId;

    const resultValue = await intacctQuery.execute();

    // TODO : add a isThrowing option to the execute ?
    if (intacctQuery.diagnoses.length) {
        throw new BusinessRuleError(intacctQuery.diagnoses.join('\n'));
    }

    context.logger.debug(() => `${resultValue.length} lines returned`);

    return resultValue;
}
/** Request to  intacct ARINVOICE  */
export async function arInvoice(context: Context, parameters: IntacctDocumentParameters): Promise<IntacctArInvoice[]> {
    const orderBy: xtremIntacct.interfaces.OrderFields[] | undefined = parameters.orderField
        ? [{ name: parameters.orderField.name, isAscending: parameters.orderField.isAscending || true }]
        : undefined;

    const intacctQuery = new xtremIntacct.classes.sdk.Functions.Query(context, {
        objectName: 'ARINVOICE',
        fields: arInvoiceFieldsArray,
        resultFunction: transformers.arInvoice,
        entityId: parameters.megaEntityId,
        orderBy,
        filter: filters.arInvoice(parameters),
    });

    intacctQuery.showPrivate = !parameters.megaEntityId;

    const resultValue = await intacctQuery.execute();

    // TODO : add a isThrowing option to the execute ?
    if (intacctQuery.diagnoses.length) {
        throw new BusinessRuleError(intacctQuery.diagnoses.join('\n'));
    }

    context.logger.debug(() => `${resultValue.length} lines returned`);

    return asyncArray(resultValue)
        .map(line =>
            xtremIntacctFinance.nodeExtensions.AccountsReceivableInvoiceExtension.queryIntacctArInvoice(context, line),
        )
        .toArray();
}

/**
 * Query to provide on AP, AR invoices and journals with parameters to check
 * @param parameters based on date, description, accounts, and amount
 * @param documentType
 */
export function document(
    context: Context,
    parameters: IntacctDocumentParameters,
    documentType: IntacctDocumentType,
): Promise<IntacctJournalEntry[] | IntacctArInvoice[]> {
    switch (documentType) {
        case 'JournalEntry':
            return journalEntry(context, parameters);
        case 'ArInvoice':
            return arInvoice(context, parameters);
        case 'ApInvoice': // Not yet implemented
            return Promise.resolve([
                {
                    account: '',
                    amount: 0,
                    description: 'Not yet implemented',
                    journal: '',
                    location: '',
                    recordNo: 0,
                    url: '',
                    documentType: 'ArInvoice',
                    entityId: '',
                    batchNo: 0,
                },
            ]);
        default:
            return Promise.resolve([]);
    }
}
