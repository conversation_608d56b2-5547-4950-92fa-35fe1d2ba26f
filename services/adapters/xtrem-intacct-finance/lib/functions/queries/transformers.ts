import type * as IA from '@intacct/intacct-sdk';
import { date } from '@sage/xtrem-core';
import type { IntacctArInvoice, IntacctJournalEntry } from '../../interfaces/bank-account-matching';
import type { IntacctCustomer } from '../../shared-functions/interfaces/documents';
import { ArInvoiceFields, CommonFields, intacctDateFormat, JournalEntryFields } from './fields';

/** Transformer for customer list  */

export function customerList(result: IA.Xml.Response.Result): IntacctCustomer[] {
    return result.data?.map(intacctCustomer => {
        return {
            customerId: intacctCustomer.CUSTOMERID,
            customerName: intacctCustomer.NAME,
        };
    });
}

/** Transformer for JournalEntry Request  */
export function journalEntry(result: IA.Xml.Response.Result): IntacctJournalEntry[] {
    return result.data?.map(gl => {
        return {
            recordNo: gl[JournalEntryFields.GLBATCH__RECORDNO],
            batchNo: gl[JournalEntryFields.GLBATCH__BATCHNO],
            description: gl[JournalEntryFields.GLBATCH__BATCH_TITLE],
            location: gl[JournalEntryFields.LOCATION],
            date: date.parse(gl[JournalEntryFields.GLBATCH__BATCH_DATE], undefined, intacctDateFormat),
            journal: gl[JournalEntryFields.GLBATCH__JOURNAL],
            amount: gl[JournalEntryFields.AMOUNT],
            account: gl[JournalEntryFields.ACCOUNTNO],
            url: gl[CommonFields.RECORD_URL],
            entityId: gl[JournalEntryFields.GLBATCH__MEGAENTITYID],
        };
    });
}

/** Transformer for AR Invoice request  */
export function arInvoice(result: IA.Xml.Response.Result): IntacctArInvoice[] {
    return result.data?.map(ari => {
        return {
            recordNo: ari[CommonFields.RECORDNO],
            invoiceNo: ari[CommonFields.RECORDID],
            description: ari[CommonFields.DESCRIPTION],
            customerName: ari[ArInvoiceFields.CUSTOMERNAME],
            customerId: ari[ArInvoiceFields.CUSTOMERID],
            referenceNumber: ari[ArInvoiceFields.DOCNUMBER],
            date: date.parse(ari[CommonFields.WHENCREATED], undefined, intacctDateFormat),
            dueDate: date.parse(ari[ArInvoiceFields.WHENDUE], undefined, intacctDateFormat),
            termName: ari[ArInvoiceFields.TERMNAME],
            currencyId: ari[ArInvoiceFields.CURRENCY],
            amount: ari[ArInvoiceFields.AMOUNT],
            companyTotalPaid: ari[ArInvoiceFields.TOTALPAID],
            companyTotalDue: ari[ArInvoiceFields.TOTALDUE],
            companyTotalEntered: ari[ArInvoiceFields.TOTALENTERED],
            totalEntered: ari[ArInvoiceFields.TRX_TOTALENTERED],
            totalDue: ari[ArInvoiceFields.TRX_TOTALDUE],
            totalPaid: ari[ArInvoiceFields.TRX_TOTALPAID],
            location: ari[ArInvoiceFields.LOCATIONID],
            url: ari[CommonFields.RECORD_URL],
            entityId: ari[CommonFields.MEGAENTITYID],
        } as IntacctArInvoice;
    });
}
