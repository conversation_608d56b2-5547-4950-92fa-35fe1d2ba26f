import * as xtremIntacct from '@sage/xtrem-intacct';
import type { AccountMatchingQueryFilters, IntacctDocumentParameters } from '../../interfaces/bank-account-matching';
import { ArInvoiceFields, CommonFields, intacctDateFormat, JournalEntryFields } from './fields';
/**
 * Manage the query filters for the intacct bank account transaction feed request
 * @param filters
 * @returns array of WhereFields
 */
export function bankAccountTransactionFeed(
    filters: AccountMatchingQueryFilters,
): xtremIntacct.interfaces.WhereFields[] {
    const filter: xtremIntacct.interfaces.WhereFields[] = [];

    if (filters.bankAccount) {
        filter.push({ where: 'FINANCIALENTITY', type: 'like', whereValue: filters.bankAccount });
    }
    if (filters.transactionType) {
        filter.push({
            where: 'TRANSACTIONTYPE',
            whereValue: filters.transactionType,
        });
    }
    if (filters.status) {
        filter.push({ where: 'CLEARED', whereValue: filters.status });
    } else {
        filter.push({ where: 'CLEARED', whereValue: 'Ignored', type: 'notEqualTo' });
    }
    /** Not used  */
    if (filters.startFromTo) {
        filter.push({
            where: 'POSTINGDATE',
            whereValue: filters.startFromTo.start?.toString() || '',
            secondWhereValue: filters.startFromTo.end?.toString() || '',
            type: 'beetwen',
        });
    }
    /** We add one day to have the last day included in the query  */
    if (filters.dateTo || filters.dateFrom) {
        filter.push({
            where: 'POSTINGDATE',
            whereValue: filters.dateFrom?.format(intacctDateFormat) || '',
            secondWhereValue: filters.dateTo?.format(intacctDateFormat) || '',
            type: 'beetwen',
        });
    }
    return filter;
}

// return IFilter with andOperator (if only one return the ifilter )
export function manageFiltersAnd(
    filterArray: xtremIntacct.classes.sdk.Interfaces.Ifilter[],
): xtremIntacct.classes.sdk.Interfaces.Ifilter {
    return filterArray.length > 1
        ? new xtremIntacct.classes.sdk.Functions.QueryOperator.AndOperator(filterArray)
        : filterArray[0];
}

// return IFilter with orOperator (if only one return the ifilter )
export function manageFiltersOr(
    filterArray: xtremIntacct.classes.sdk.Interfaces.Ifilter[],
): xtremIntacct.classes.sdk.Interfaces.Ifilter {
    return filterArray.length > 1
        ? new xtremIntacct.classes.sdk.Functions.QueryOperator.OrOperator(filterArray)
        : filterArray[0];
}

export function arInvoice(parameters: IntacctDocumentParameters): xtremIntacct.classes.sdk.Interfaces.Ifilter {
    const filterArray: xtremIntacct.classes.sdk.Interfaces.Ifilter[] = [];
    const filterOrArray: xtremIntacct.classes.sdk.Interfaces.Ifilter[] = [];

    if (parameters.customerId) {
        filterOrArray.push(
            new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(ArInvoiceFields.CUSTOMERID).equalTo(
                `${parameters.customerId}`,
            ),
        );
    }

    if (parameters.megaEntityId) {
        filterArray.push(
            new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(CommonFields.MEGAENTITYID).isNotNull(),
        );
    }

    if (parameters.currencyId) {
        filterArray.push(
            new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(ArInvoiceFields.CURRENCY).equalTo(
                `${parameters.currencyId}`,
            ),
            new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(ArInvoiceFields.BASECURRENCY).equalTo(
                `${parameters.currencyId}`,
            ),
        );
    }

    if (parameters.documentNos && parameters.documentNos.length) {
        filterArray.push(
            new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(CommonFields.RECORDNO).in(
                parameters.documentNos,
            ),
        );
    } else {
        filterArray.push(
            new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(ArInvoiceFields.TOTALDUE).greaterThan('0'),
        );
    }

    const filterOr = manageFiltersOr(filterOrArray);
    const filterAnd = manageFiltersAnd(filterArray);

    return filterOr
        ? new xtremIntacct.classes.sdk.Functions.QueryOperator.AndOperator([filterOr, filterAnd])
        : filterAnd;
}

/** Manage Filters for the the JournalEntry  */
export function journaLEntry(parameters: IntacctDocumentParameters): xtremIntacct.classes.sdk.Interfaces.Ifilter[] {
    const filterArray: xtremIntacct.classes.sdk.Interfaces.Ifilter[] = [];

    /**  MegaEntity part */
    if (!parameters.megaEntityId && parameters.location) {
        filterArray.push(
            new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(JournalEntryFields.LOCATION).equalTo(
                parameters.location,
            ),
        );
    }

    if (parameters.description) {
        filterArray.push(
            new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(JournalEntryFields.GLBATCH__BATCH_TITLE).like(
                parameters.description,
            ),
        );
    }
    if (parameters.date) {
        filterArray.push(
            new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(JournalEntryFields.GLBATCH__BATCH_DATE).equalTo(
                parameters.date.format(intacctDateFormat),
            ),
        );
    }
    if (parameters.accounts && parameters.accounts.length !== 0) {
        if (parameters.accounts.length === 1) {
            filterArray.push(
                new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(JournalEntryFields.ACCOUNTNO).equalTo(
                    parameters.accounts[0],
                ),
            );
        } else {
            filterArray.push(
                new xtremIntacct.classes.sdk.Functions.QueryOperator.OrOperator(
                    parameters.accounts.map(account => {
                        return new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(
                            JournalEntryFields.ACCOUNTNO,
                        ).equalTo(account);
                    }),
                ),
            );
        }
    }
    if (parameters.amount) {
        filterArray.push(
            new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(JournalEntryFields.AMOUNT).equalTo(
                parameters.amount.toString(),
            ),
        );
    }

    if (parameters.account) {
        filterArray.push(
            new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter(JournalEntryFields.ACCOUNTNO).equalTo(
                parameters.account,
            ),
        );
    }

    return filterArray;
}
