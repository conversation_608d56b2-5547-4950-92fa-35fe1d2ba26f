import type { ValidationContext } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import type * as xtremMasterData from '@sage/xtrem-master-data';

export async function isIntacctCreditLimit(
    customer: xtremMasterData.nodes.Customer,
    cx: ValidationContext,
): Promise<void> {
    if (
        (await xtremIntacct.functions.isIntacctActive(customer.$.context)) &&
        !customer.canUpdateFromExternalIntegration &&
        (await (await customer.$.old).creditLimit) !== (await customer.creditLimit)
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-intacct-finance/nodes__customer_credit_limit_control',
            'You cannot update credit limit if Sage Intacct is active.',
        );
    }
}
