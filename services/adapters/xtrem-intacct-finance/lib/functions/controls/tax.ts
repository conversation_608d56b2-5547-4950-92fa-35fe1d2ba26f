import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremTax from '@sage/xtrem-tax';

/*
 * @param cx ValidationContext
 * @param tax
 */
export async function controlBeginIsIntacctReverseCharge(
    cx: ValidationContext,
    tax: xtremTax.nodes.Tax,
): Promise<void> {
    if ((await tax.isIntacct) && tax.$.status !== NodeStatus.added && (await tax.isIntacctReverseCharge)) {
        if ((await (await tax.$.old).isReverseCharge) !== (await tax.isReverseCharge)) {
            cx.error.addLocalized(
                '@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_reverse_charge_is_forbidden',
                'You cannot edit the reverse charge.',
            );
        }

        if ((await (await tax.$.old).isActive) !== (await tax.isActive)) {
            cx.error.addLocalized(
                '@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_active_is_forbidden',
                'You cannot enable this tax record. It is linked to Sage Intacct.',
            );
        }
    }
}

/*
 * @param cx ValidationContext
 * @param tax
 */
export async function controlBeginIsIntacct(cx: ValidationContext, tax: xtremTax.nodes.Tax): Promise<void> {
    if (await tax.isIntacct) {
        if (
            !(await tax.isUpdateFromIntacct) &&
            tax._id > 0 &&
            (await tax.taxValues.some(line =>
                [NodeStatus.modified, NodeStatus.deleted, NodeStatus.added].includes(line.$.status),
            ))
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-intacct-finance/nodes__tax_extension__modification_not_allowed',
                'You cannot create, edit or delete this tax record. It is linked to Sage Intacct.',
            );
        }

        if (tax.$.status !== NodeStatus.added) {
            if ((await (await tax.$.old).country)._id !== (await tax.country)._id) {
                cx.error.addLocalized(
                    '@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_country_is_forbidden',
                    'You cannot edit the country.',
                );
            }

            if ((await (await tax.$.old).primaryExternalReference) !== (await tax.primaryExternalReference)) {
                cx.error.addLocalized(
                    '@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_primary_external_reference_is_forbidden',
                    'You cannot edit the primary external reference.',
                );
            }
        }
    }
}

/*
 * @param cx ValidationContext
 * @param tax
 */
export async function controlBeginValidateTaxRate(cx: ValidationContext, tax: xtremTax.nodes.Tax): Promise<void> {
    if (
        (!(await tax.isIntacct) || ((await tax.isIntacct) && !(await tax.isIntacctReverseCharge))) &&
        (await tax.taxValues.some(
            async line => [NodeStatus.added, NodeStatus.modified].includes(line.$.status) && (await line.rate) < 0,
        ))
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-intacct-finance/nodes__tax_extension__negative_rate_not_allowed',
            'You cannot enter a negative rate.',
        );
    }
}

/*
 * @param cx ValidationContext
 * @param tax
 */
export async function controlBeginType(cx: ValidationContext, tax: xtremTax.nodes.Tax): Promise<void> {
    if (
        (await tax.isIntacct) &&
        !(await tax.isUpdateFromIntacct) &&
        tax.$.status !== NodeStatus.added &&
        (await (await tax.$.old).type) !== (await tax.type)
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-intacct-finance/nodes__tax_extension__modification_of_the_tax_type_is_forbidden',
            'You cannot edit the tax type. It is linked to Sage Intacct.',
        );
    }
}
