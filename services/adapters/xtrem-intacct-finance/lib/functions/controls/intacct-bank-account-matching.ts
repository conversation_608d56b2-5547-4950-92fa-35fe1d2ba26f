import type { Context, ValidationContext } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremTax from '@sage/xtrem-tax';

export async function account(
    matchingAccount: xtremFinanceData.nodes.Account | null,
    matchingTax: xtremTax.nodes.Tax | null,
    matchingLocation: xtremSystem.nodes.Site | null,
    updateAccountTaxManagement: boolean,
    validationContext: ValidationContext,
    context: Context,
) {
    if (matchingTax && (await matchingAccount?.taxManagement) !== 'excludingTax' && !updateAccountTaxManagement) {
        validationContext.error.addLocalized(
            '@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_management_should_be_excluding_taxes',
            'You cannot set a tax detail with an account that is not subjected to taxes.',
        );
    }

    if (
        matchingAccount &&
        (await xtremFinanceData.functions.Common.isSubjectToGlTaxExcludedAmount({
            context,
            legislationId: (await (await (await matchingAccount?.chartOfAccount)?.legislation)?.id) || '',
        }))
    ) {
        if (!['excludingTax', 'other'].includes((await matchingAccount?.taxManagement) || '')) {
            validationContext.error.addLocalized(
                '@sage/xtrem-intacct-finance/nodes__intacct_bank_account_matching__wrong_account_tax_management',
                "You can only select an account linked to a tax management that is either 'Other' or 'Excluding tax'.",
            );
        }

        if (await (await matchingAccount?.chartOfAccount)?.legislation) {
            if (
                matchingTax &&
                (await (await matchingAccount?.chartOfAccount)?.legislation)?._id !==
                    (await matchingTax?.legislation)?._id
            ) {
                validationContext.error.addLocalized(
                    '@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_legislation_and_coa_legislation_dont_match',
                    'The tax legislation must be the same as the chart of accounts legislation.',
                );
            }
            if (
                matchingLocation &&
                (await (await matchingAccount?.chartOfAccount)?.legislation)?._id !==
                    (await (await matchingLocation?.legalCompany)?.legislation)?._id
            ) {
                validationContext.error.addLocalized(
                    '@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__site_legislations_and_coa_legislation_dont_match',
                    'The site legislation must be the same as the chart of accounts legislation.',
                );
            }
        }
    }
}

export async function tax(
    matchingAccount: xtremFinanceData.nodes.Account | null,
    matchingTax: xtremTax.nodes.Tax | null,
    updateAccountTaxManagement: boolean,
    validationContext: ValidationContext,
    context: Context,
) {
    if (
        await xtremFinanceData.functions.Common.isSubjectToGlTaxExcludedAmount({
            context,
            legislationId: (await (await (await matchingAccount?.chartOfAccount)?.legislation)?.id) || '',
        })
    ) {
        if ((await matchingAccount?.taxManagement) === 'excludingTax' && !matchingTax) {
            validationContext.error.addLocalized(
                '@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_mandatory',
                'The tax is mandatory.',
            );
        }
        if ((await matchingAccount?.taxManagement) !== 'excludingTax' && matchingTax && !updateAccountTaxManagement) {
            validationContext.error.addLocalized(
                '@sage/xtrem-intacct-finance/nodes__intacct-bank-account-matching__tax_not_allowed',
                'The tax must be empty.',
            );
        }
    }
}
