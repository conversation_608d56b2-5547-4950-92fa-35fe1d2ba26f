import type { ValidationContext } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import type * as xtremSystem from '@sage/xtrem-system';

export async function isIntacctOnHoldCheck(company: xtremSystem.nodes.Company, cx: ValidationContext): Promise<void> {
    if (
        (await xtremIntacct.functions.isIntacctActive(company.$.context)) &&
        !company.canUpdateFromExternalIntegration &&
        (await (await company.$.old).customerOnHoldCheck) !== (await company.customerOnHoldCheck)
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-intacct-finance/nodes__company_customer_on_hold_control',
            'You cannot update customer on hold if Sage Intacct is active.',
        );
    }
}
