import type * as IA from '@intacct/intacct-sdk';
import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, Logger } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import { isArray } from 'lodash';
import type * as xtremIntacctFinance from '../index';

const logger = Logger.getLogger(__filename, 'intacct-api-call');

/**
 *
 * @param validValues VALIDVALUES from intacct can be an object of VALIDVALUE:string[] or string[] or null
 * @returns the valid values in the
 */
function manageValideValueField(validValues: { VALIDVALUE: string[] } | string[] | null) {
    if (!validValues) {
        return null;
    }

    if (isArray(validValues) && validValues.every(value => typeof value === 'string')) {
        return { VALIDVALUE: validValues };
    }
    if (!isArray(validValues)) {
        // Handle when VALIDVALUE isn't an array
        if (typeof validValues.VALIDVALUE === 'string') {
            // if only one field there we get the issue : Expected Iterable, but did not find one for field
            return { VALIDVALUE: [validValues.VALIDVALUE, ''] };
        }

        if (isArray(validValues.VALIDVALUE)) {
            return validValues;
        }
    }
    throw new BusinessRuleError(`not managed validValues : ${JSON.stringify(validValues)}`);
}

/**
 *  Mapping for Lookup
 * @param result
 * @returns
 */
export function mappingLookup(
    result: IA.Xml.Response.Result,
): xtremIntacctFinance.sharedFunctions.interfaces.LookupObject[] {
    logger.debug(() => `${JSON.stringify(result.data)}`);
    return result.data
        .map(gl => {
            return {
                name: gl.$.Name,
                documentType: gl.$.DocumentType,
                fields: gl.Fields.Field.map((field: any) => {
                    const fieldXT = {
                        ...field,
                        REQUIRED: field.REQUIRED === 'true',
                        READONLY: field.READONLY === 'true',
                        ISCUSTOM: field.ISCUSTOM === 'true',
                        VALIDVALUES: manageValideValueField(field.VALIDVALUES),
                        xtremProperty: '',
                    } as xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties;
                    if (!fieldXT.VALIDVALUES) {
                        delete fieldXT.VALIDVALUES;
                    }
                    return fieldXT;
                }),
                relationships:
                    gl.Relationships && Array.isArray(gl.Relationships.Relationship)
                        ? gl.Relationships.Relationship.map((field: any) => {
                              return {
                                  ...field,
                                  xtremProperty: '',
                              } as xtremIntacctFinance.sharedFunctions.interfaces.LookupRelationships;
                          })
                        : [gl.Relationships?.Relationship],
            } as xtremIntacctFinance.sharedFunctions.interfaces.LookupObject;
        })
        .sort();
}

/**
 *  Call the lookup request on intacct finance :
 * <inspect><object>*</object></inspect> : Retrieve all intacct objects , or the list of the field of a special object
 *  TODO : to be move on intacct package
 * @param context
 * @param object
 */
export function lookupApi(
    context: Context,
    lookup: { object: string },
): Promise<xtremIntacctFinance.sharedFunctions.interfaces.LookupObject[]> {
    // TODO : use SDK folder !
    return new xtremIntacct.classes.sdk.Functions.Lookup<xtremIntacctFinance.sharedFunctions.interfaces.LookupObject[]>(
        context,
        lookup.object,
        {
            resultFunction: mappingLookup,
        },
    ).execute();
}
