import type { Collection } from '@sage/xtrem-core';
import { asyncArray } from '@sage/xtrem-core';
import * as Re2 from 're2';
import type * as xtremIntacctFinance from '..';
import type { IntacctBankAccountTransactionFeedLine as IIntacctBankAccountTransactionFeedLine } from '../shared-functions/interfaces';

/**
 *  Look for keyWord that match the description
 * @param type
 * @param keyword
 * @param description
 * @returns boolean
 */
export function findMatchingDescription(
    type: xtremIntacctFinance.enums.IntacctMatchingType,
    keyword: string,
    description: string,
): boolean {
    switch (type) {
        case 'equals': {
            return keyword === description;
        }
        case 'contains': {
            return description.search(keyword) !== -1;
        }
        case 'regularExpression': {
            //  insensitive. Case insensitive match
            const regexp = new Re2(keyword, 'i');
            return regexp.test(description || '');
        }
        default:
            return keyword === description;
    }
}

export async function getBankFeedLinesDataForStatusUpdate(
    lines: Collection<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeedLine>,
): Promise<IIntacctBankAccountTransactionFeedLine[]> {
    return asyncArray(await lines.toArray())
        .map(async line => {
            return {
                status: await line.status,
                amount: await line.amount,
                account: (await line.account)?._id,
            };
        })
        .toArray();
}
