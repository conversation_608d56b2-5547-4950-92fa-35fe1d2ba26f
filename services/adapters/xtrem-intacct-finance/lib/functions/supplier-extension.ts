import type * as xtremMasterData from '@sage/xtrem-master-data';

export function paymentMethodIntacct(value: xtremMasterData.enums.PaymentMethod | null): string | undefined {
    switch (value) {
        case 'printedCheck':
            return 'Printed Check';
        case 'creditCard':
            return 'Charge Card';
        case 'cash':
            return 'Cash';
        case 'ACH':
        case 'EFT':
            return value;
        default:
            return undefined;
    }
}
