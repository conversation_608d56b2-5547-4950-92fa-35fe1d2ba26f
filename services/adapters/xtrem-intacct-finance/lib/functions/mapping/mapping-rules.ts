import type * as xtremIntacctFinance from '../..';
import type { MappingRules } from '../../interfaces/mapping';

export const DELETEDProperty = 'DELETEDProperty';

export function extractCustomFields(fields: {
    custom?: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties[];
    default?: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties[];
    editableFields: Array<string>;
}) {
    if (!fields.custom && !fields.default) {
        return [];
    }

    const defaultFields = fields.default?.filter(field => field.xtremProperty) || [];
    const customFields = fields.custom?.filter(field => !!field.xtremProperty) || [];

    defaultFields.forEach(defaultField => {
        const customField = customFields.find(custField => custField.ID === defaultField.ID);
        if (customField) {
            if (customField?.xtremProperty === defaultField.xtremProperty) {
                customFields.splice(customFields.indexOf(customField), 1);
            } else {
                customField.xtremDefaultProperty = defaultField.xtremProperty;
            }
        } else {
            customFields.push({ ...defaultField, xtremProperty: DELETEDProperty });
        }
    });

    return customFields.filter(field => !(field.xtremDefaultProperty && !fields.editableFields.includes(field.ID)));
}

function extractCustomMapping(mapping: MappingRules) {
    const newMapping: xtremIntacctFinance.sharedFunctions.interfaces.LookupObject = {
        ...{
            ...mapping.default,
            fields: extractCustomFields({
                custom: mapping.custom.fields,
                default: mapping.default.fields,
                editableFields: mapping.editableFields,
            }),
            relationships: [],
            relationshipFields: extractCustomFields({
                custom: mapping.custom.relationshipFields,
                default: mapping.default.relationshipFields,
                editableFields: mapping.editableFields,
            }),
        },
    };

    return newMapping;
}

export async function mappingRules(define: {
    map: xtremIntacctFinance.nodes.IntacctMap;
    fullCustom?: xtremIntacctFinance.sharedFunctions.interfaces.LookupObject;
}) {
    /** This is the value comming from UI */
    const custom = define.fullCustom ?? (await define.map.uRelationMapping);
    /** This the default mapping comming from the file  */
    const defaultMapping = await define.map.getFile;

    if (!custom) {
        return defaultMapping;
    }

    if (!defaultMapping) {
        return custom;
    }

    return extractCustomMapping({ custom, default: defaultMapping, editableFields: await define.map.editableFields });
}
