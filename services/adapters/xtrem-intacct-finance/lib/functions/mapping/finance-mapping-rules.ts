/** Properties that changes and need to be changed into uRelationMapping  */

/** ** APBILL - changes
 * BILLTOPAYTOCONTACTNAME intacctDocument.billToPayToContactName
 * SHIPTORETURNTOCONTACTNAME intacctDocument.shipToReturnToContactName
 * TAXSOLUTIONID intacctDocument.taxSolutionId
 *
 * ** APBILLITEM
 * TRX_AMOUNT intacctDocumentLine.signedAmountExcludingTax
 * DEPARTMENTID intacctDocumentLine.departmentId
 * ENTRYDESCRIPTION intacctDocumentLine.memo
 * CLASSID intacctDocumentLine.classId
 * CUSTOMERID intacctDocumentLine.customerId
 * VENDORID intacctDocumentLine.vendorId
 * ITEMID intacctDocumentLine.itemId
 * PROJECTID intacctDocumentLine.projectId
 * TAXENTRIES intacctDocumentLine.taxEntries
 *
 * ** ARADVANCE
 * DEPARTMENTID intacctDocument.paymentMethod
 *
 * ** ARADVANCEITEM
 * DEPARTMENTID intacctDocumentLine.departmentId
 * CLASSID intacctDocumentLine.classId
 * CUSTOMERID intacctDocumentLine.customerId
 * VENDORID intacctDocumentLine.vendorId
 * ITEMID intacctDocumentLine.itemId
 * PROJECTID intacctDocumentLine.projectId
 *
 * ** ARINVOICE
 *  RECORDNO intacctDocument.recordNo,intacctDocument.intacctId
 *  TAXSOLUTIONID intacctDocument.taxSolutionId
 *
 * ** ARINVOICEITEM
 * TRX_AMOUNT intacctDocumentLine.signedAmountExcludingTax
 * DEPARTMENTID intacctDocumentLine.departmentId
 * ENTRYDESCRIPTION intacctDocumentLine.memo
 * CUSTOMERID intacctDocumentLine.customerId
 * VENDORID intacctDocumentLine.vendorId
 * ITEMID intacctDocumentLine.itemId
 * PROJECTID intacctDocumentLine.projectId
 * TAXENTRIES intacctDocumentLine.taxEntries
 *
 * ** ARPYMT
 * PAYMENTMETHOD intacctDocument.paymentMethod
 *
 * ** ARPYMTDETAIL
 * RECORDKEY intacctDocumentLine.arInvoiceRecordNo
 * RECORDNO ==> Deleted recordNo & intacctId
 *
 * **  GLBATCH
 * RECORDNO intacctDocument.recordNo
 * BATCHNO intacctDocument.intacctId
 * TAXIMPLICATIONS intacctDocument.taxImplications
 * TAXSOLUTIONID intacctDocument.taxSolutionId
 *
 * */
