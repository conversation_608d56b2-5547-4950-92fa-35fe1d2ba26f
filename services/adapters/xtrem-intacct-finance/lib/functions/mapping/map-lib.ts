import type { AnyValue, Context } from '@sage/xtrem-core';
import { BusinessRuleError, Logger } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as _ from 'lodash';
import * as xtremIntacctFinance from '../../../index';

const logger = Logger.getLogger(__filename, 'map-lib');
/**
 *  format the lookupProperties from an inspectField string
 * @param fieldIdInspect
 * @param relationShipField  ( if existing )
 * @returns
 */
export function formatRelationShipField(
    fieldIdInspect: string,
    relationShipField?: xtremIntacctFinance.sharedFunctions.interfaces.LookupRelationships,
): xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties {
    return {
        DATATYPE: fieldIdInspect.split('.')[0],
        ID: fieldIdInspect,
        DESCRIPTION: fieldIdInspect.split('.').slice(1).join('.'), // param without the first ( PATH )
        READONLY: true,
        CREATEONLY: false,
        ISCUSTOM: false,
        REQUIRED: false,
        LABEL:
            relationShipField?.LABEL ||
            fieldIdInspect
                .split('.')
                .slice(fieldIdInspect.split('.').length - 1)
                .join(''), // Last param,
        xtremProperty: '',
    };
}

/**
 * get the lookup & the inspect merge them into one file to be able to have a mapping object
 * @param lookup  comming from the api lookup object
 * @param inspect comming from the api inspect object
 * @returns
 */
export function mergeLookupAndInspect(
    lookup: xtremIntacctFinance.sharedFunctions.interfaces.LookupObject,
    inspect: xtremIntacct.classes.sdk.Interfaces.InspectResult,
): xtremIntacctFinance.sharedFunctions.interfaces.LookupObject {
    lookup.relationshipFields = lookup.relationshipFields ? lookup.relationshipFields : [];

    inspect.Fields.Field.forEach(fieldIdInspect => {
        const relationShip = lookup.relationships?.find(
            relationShipFind =>
                relationShipFind && relationShipFind.RELATEDBY.split('.')[0] === fieldIdInspect.split('.')[0],
        );

        const fieldIdNumber = lookup.fields.findIndex(field => field.ID === fieldIdInspect);

        if (fieldIdNumber !== -1 && fieldIdInspect.split('.').length > 1 && lookup.relationshipFields) {
            lookup.fields.splice(fieldIdNumber, 1);
            lookup.relationshipFields.push(formatRelationShipField(fieldIdInspect, relationShip));
            logger.debug(
                () => ` ${fieldIdInspect} pushed into relationshipFields 
                 deleted from lookup.fields ${fieldIdNumber}`,
            );
        }
    });

    return lookup;
}

/**
 *  Use the lookup & inspect api , add what is return by the inspectApi to get data
 * @param context
 * @param object
 */
export async function getFullObject(
    context: Context,
    intacctObject: string,
): Promise<xtremIntacctFinance.sharedFunctions.interfaces.LookupObject> {
    const lookup = (await xtremIntacctFinance.functions.lookupApi(context, { object: intacctObject })).at(0);
    const inspect = await new xtremIntacct.classes.sdk.Functions.Inspect(context, intacctObject).allFields();

    if (!lookup) {
        throw new BusinessRuleError(`Lookup ${intacctObject} error `);
    }
    if (!inspect) {
        throw new BusinessRuleError(`inspect ${intacctObject} error `);
    }

    return mergeLookupAndInspect(lookup, inspect);
}

/**
 *  intacct is returned true or false as string & sometimes empty string
 * @param value
 * @returns
 */
export function manageEnumAsString(value: string | undefined) {
    switch (value) {
        case 'true':
            return true;
        case 'false':
            return false;
        case '':
            return null;
        default:
            return value;
    }
}

/**
 *
 * @param context
 * @param intacctData
 * @param field
 */
export async function transformToPayload(
    context: Context,
    intacctDataLine: any,
    field: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties,
): Promise<AnyValue> {
    logger.debug(
        () =>
            `Replace ${field.ID} property with ${field.xtremProperty} ( value : ${_.get(intacctDataLine, field.ID)}) `,
    );

    if (field.xtremPropertyOption) {
        const dataToFind = _.get(intacctDataLine, field.ID);

        const linkData = field.xtremPropertyOption.split('.');
        if (linkData.length !== 2) {
            throw new BusinessRuleError('option not allowed ');
        }
        const linkedNode = xtremIntacct.functions.getIntacctNode(context, linkData[0]);

        const filter = {};
        _.set(filter, linkData[1], dataToFind);

        const _id = (await context.query(linkedNode, { filter }).at(0))?._id;
        if (!_id) {
            throw new BusinessRuleError(`Not data  for : ${linkData[0]} ${JSON.stringify(filter)}`);
        }
        return _id;
    }
    /**
     * intacctIfeldId
     * # case : RECORD#
     */
    const value = _.get(intacctDataLine, field.ID.replace('#', ''));

    switch (field.DATATYPE) {
        case 'TEXT':
        case 'BOOLEAN':
        case 'ENUM':
            return manageEnumAsString(value);
        case 'DECIMAL':
        case 'INTEGER':
            return Number(value);
        default:
            return value;
    }
}
