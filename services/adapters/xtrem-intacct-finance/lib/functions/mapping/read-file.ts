import type { Context } from '@sage/xtrem-core';
import * as fs from 'fs';
import * as path from 'path';
import * as xtremIntacctFinance from '../../../index';

export function readMappingFile(
    context: Context,
    structure: { name: string },
): xtremIntacctFinance.sharedFunctions.interfaces.LookupObject | null {
    if (!structure.name) {
        return null;
    }

    /**
     * current directory
     */
    const currentDirectory = context.introspection.getNodeDescriptor(xtremIntacctFinance.nodes.IntacctMap).package.dir;

    const filePath = path.resolve(currentDirectory, 'data/mapping', `${structure.name}.json`);
    if (!fs.existsSync(filePath)) {
        context.logger.error(
            `Mapping file doesn't exists. The file must be in /data/mapping of your module with the following name ${structure.name}.json ${filePath}`,
        );
        return null;
    }

    context.logger.debug(() => `reading ${filePath}`);
    const mappingData = fs.readFileSync(filePath, 'utf8');
    context.logger.debug(() => `${mappingData}`);
    return mappingData ? JSON.parse(mappingData) : null;
}
