import type * as xtremIntacctFinance from '../..';
import { DELETEDProperty } from './mapping-rules';

export function mergeCustomFields(fields: {
    default?: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties[];
    custom: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties[];
    editableFields: Array<string>;
}) {
    const customFields = fields.custom.map(property => {
        const defaultValue = fields.default?.find(defaultField => defaultField.ID === property.ID);

        const newMergedProperty: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties = {
            ...property,
            ...(property.xtremProperty === DELETEDProperty ? { xtremProperty: '' } : {}),
            xtremDefaultProperty: defaultValue?.xtremProperty || '',
            isEditable: defaultValue?.isEditable || true,
        };
        if (defaultValue) {
            fields.default?.splice(
                fields.default.findIndex(field => field.ID === property.ID),
                1,
            );
        }
        return newMergedProperty;
    });

    return [...customFields, ...(fields.default || [])];
}

export function mergeCustomMapping(mapping: {
    default: xtremIntacctFinance.sharedFunctions.interfaces.LookupObject;
    custom?: xtremIntacctFinance.sharedFunctions.interfaces.LookupObject;
    editableFields: Array<string>;
}) {
    mapping.default.fields.forEach(editableField => {
        editableField.isEditable = editableField.xtremProperty
            ? mapping.editableFields.includes(editableField.ID)
            : true;
    });
    mapping.default.relationshipFields?.forEach(editableField => {
        editableField.isEditable = editableField.xtremProperty
            ? mapping.editableFields.includes(editableField.ID)
            : true;
    });

    if (!mapping.custom) {
        return mapping.default;
    }

    const customFields = mapping.custom?.fields;
    const customRelationShipFields = mapping.custom?.relationshipFields;

    const mergedMapping: xtremIntacctFinance.sharedFunctions.interfaces.LookupObject = {
        ...mapping.default,
        fields: customFields
            ? mergeCustomFields({
                  default: mapping.default.fields,
                  custom: customFields,
                  editableFields: mapping.editableFields,
              })
            : mapping.default.fields,
        relationshipFields: customRelationShipFields
            ? mergeCustomFields({
                  default: mapping.default.relationshipFields,
                  custom: customRelationShipFields,
                  editableFields: mapping.editableFields,
              })
            : mapping.default.relationshipFields,
    };

    return mergedMapping;
}
