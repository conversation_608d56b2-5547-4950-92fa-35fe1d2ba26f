import type { Context } from '@sage/xtrem-core';
import * as fs from 'fs';
import * as fps from 'path';
import * as xtremIntacctFinance from '../../../index';

/**
 * prettify content and add undefined for unmapped properties
 * @param content
 */
function stringifyContent(content: any): string {
    return JSON.stringify(
        content,
        (_key, value) => {
            return value === undefined ? '' : value;
        },
        4,
    );
}

/**
 * write the mapping in file
 * @param fileName
 * @param content
 * @param existingContent
 */
export async function writeMappingFile(
    context: Context,
    mapping: xtremIntacctFinance.sharedFunctions.interfaces.LookupObject,
): Promise<void> {
    const currentDirectory = context.introspection.getNodeDescriptor(xtremIntacctFinance.nodes.IntacctMap).package.dir;
    const mappingDirectory = fps.resolve(currentDirectory, 'data/mapping');

    context.logger.debug(() => `Create file : ${currentDirectory} / ${mappingDirectory} name : ${mapping.name}`);
    /**
     * check if mapping directory exists
     */
    await fs.promises.mkdir(mappingDirectory, { recursive: true });

    const filePath = fps.resolve(currentDirectory, 'data/mapping', `${mapping.name}.json`);

    await fs.promises.writeFile(filePath, stringifyContent(mapping));
}
