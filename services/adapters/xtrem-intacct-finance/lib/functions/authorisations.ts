import type { OperationGrant } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremIntacctFinance from '..';

export const cashManagerActivities: OperationGrant[] = [
    {
        operations: ['getTaxValues'],
        on: [() => xtremTax.nodes.Tax],
    },
    {
        operations: ['read', 'create', 'update', 'delete', 'queryIntacctDocument', 'bulkSave', 'queryIntacctArInvoice'],
        on: [() => xtremIntacctFinance.nodes.IntacctBankAccountMatching],
    },
    {
        operations: ['read', 'defaultInstance'],
        on: [() => xtremIntacct.nodes.Intacct],
    },
    {
        operations: [
            'read',
            'create',
            'update',
            'delete',
            'getAvailableXtremObjectList',
            'getObject',
            'getIntacctTransactionsList',
            'writeStructure',
            'getStructure',
            'getDataIntacct',
            'deleteXtrem',
        ],
        on: [() => xtremIntacctFinance.nodes.IntacctMap],
    },
    {
        operations: ['read', 'create', 'update', 'delete'],
        on: [() => xtremIntacctFinance.nodes.IntacctImportSession],
    },
    {
        operations: ['read'],
        on: [() => xtremStructure.nodes.Legislation],
    },
    {
        operations: ['read'],
        on: [() => xtremStructure.nodes.Country],
    },
    {
        operations: ['read'],
        on: [() => xtremStructure.nodes.ChartOfAccount],
    },
];
