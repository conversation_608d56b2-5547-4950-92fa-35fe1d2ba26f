import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremIntacctFinance from '../..';

export async function saveBegin(intacctBankAccountMatching: xtremIntacctFinance.nodes.IntacctBankAccountMatching) {
    if (await intacctBankAccountMatching.account) {
        if (await intacctBankAccountMatching.updateAccountTaxManagement) {
            const account = await intacctBankAccountMatching.$.context.read(
                xtremFinanceData.nodes.Account,
                { _id: (await intacctBankAccountMatching.account)?._id },
                { forUpdate: true },
            );
            await account.$.set({ taxManagement: 'excludingTax' });
            await account.$.save();
        }
    }
}
