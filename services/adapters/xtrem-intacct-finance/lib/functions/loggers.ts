import type { AnyR<PERSON><PERSON>, Logger } from '@sage/xtrem-core';
import type * as xtremIntacct from '@sage/xtrem-intacct';

export function xtreemPayloadToCreateUpdate(
    payloadToCreateUpdate: {
        instance: xtremIntacct.nodes.IntacctNode;
        xtremPayload: AnyRecord;
    },
    length: number,
    logger: Logger,
) {
    if (length < 5) {
        // debug : we don't want the payload if we have more than 5 instances to create/update ( lisibility of logs )
        logger.debug(
            () => ` ** Payload to update/create ** \n:  ${JSON.stringify(payloadToCreateUpdate, undefined, 3)} `,
        );
    }
}
