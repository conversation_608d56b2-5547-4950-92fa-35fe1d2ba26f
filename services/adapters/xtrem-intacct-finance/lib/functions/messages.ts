import type { Context } from '@sage/xtrem-core';

export function toManyLines(context: Context, numberOflines: string): string {
    return context.localize(
        '@sage/xtrem-intacct-finance/nodes__intacct-map/to-many-lines',
        'The query returned many lines:{{numberOflines}}.',
        {
            numberOflines,
        },
    );
}

export function noDesynchronization(context: Context): string {
    return context.localize(
        '@sage/xtrem-intacct-finance/nodes__intacct-map/no-desynchronization',
        'The object is synchronized.',
    );
}

export function noIntacctInstanceActive(context: Context): string {
    return context.localize(
        '@sage/xtrem-intacct-finance/nodes__intacct-map/no-intacct-instance',
        'No Sage Intacct instance activated.',
    );
}

export function noIntactData(
    context: Context,
    data: { intacctName: string; intacctIDField?: string; intacctIdValue?: string },
): string {
    return context.localize(
        '@sage/xtrem-intacct-finance/nodes__intacct-map/no-intacct-data',
        'No Sage Intacct data for the {{intacctName}} object where {{intacctIDField}} equals {{intacctIdValue}}.',
        data,
    );
}
