import * as xtremCommunication from '@sage/xtrem-communication';
import type { AnyRecord, Collection, Context, NodeQueryFilter } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, Diagnose, Logger, ValidationSeverity } from '@sage/xtrem-core';
import type * as xtremIntacct from '@sage/xtrem-intacct';
import * as _ from 'lodash';
import * as xtremIntacctFinance from '..';
import type * as IMap from '../interfaces/i-map';
import { getMapInstance, getPayLoad } from './common';

const emptyField = { name: '' };
const logger = Logger.getLogger(__filename, 'map-lib');

export async function manageFiltersForXtreem(
    mapInstance: xtremIntacctFinance.nodes.IntacctMap,
    filters?: xtremIntacct.interfaces.WhereFields[],
): Promise<NodeQueryFilter<any>> {
    if (!filters || filters.length === 0) {
        return {};
    }
    const filtersToReturn = await asyncArray(filters)
        .map(async filter => {
            const xtreemFilter = {};
            const intacctFieldName = filter.where;
            const xtremFieldName = (await mapInstance.relationMapping).fields.find(
                field => field.ID === intacctFieldName,
            )?.xtremProperty;
            if (xtremFieldName) {
                xtremFieldName
                    ?.split(',')
                    .filter(nameFilter => nameFilter !== 'intacctId')
                    .forEach(name =>
                        _.set(xtreemFilter, name.trim(), { _regex: filter.whereValue.replaceAll('%', '') }),
                    );
            }
            return xtreemFilter;
        })
        .toArray();

    return filtersToReturn.length > 1 ? { _and: filtersToReturn } : filtersToReturn[0];
}

export async function getFieldsName(
    specificFields: Collection<xtremIntacctFinance.nodes.MapProperty>,
): Promise<IMap.FieldName> {
    /** For id Column */

    const idFieldName = await (
        (await specificFields.find(async field => (await field.type) === 'intacctId')) || emptyField
    ).name;
    /** For name Column */
    const nameFieldName = await (
        (await specificFields.find(async field => (await field.type) === 'name')) || emptyField
    ).name;
    /** For description Column */
    const descriptionFieldName = await (
        (await specificFields.find(async field => (await field.type) === 'description')) || emptyField
    ).name;

    return {
        idFieldName,
        nameFieldName,
        descriptionFieldName,
    };
}

export function getXtreemFieldsName(
    fields: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties[],
    fieldIntacct: IMap.FieldName,
): IMap.FieldXtreemName {
    const idFieldXtremName = (
        fields
            .find(field => field.ID === fieldIntacct.idFieldName)
            ?.xtremProperty?.split(',')
            .find(xProp => xProp !== 'intacctId') || ''
    ).trim();

    const nameFieldXtremName = fields.find(field => field.ID === fieldIntacct.nameFieldName)?.xtremProperty;
    const descriptionFieldXtremName = fields.find(
        field => field.ID === fieldIntacct.descriptionFieldName,
    )?.xtremProperty;

    return {
        idFieldXtremName,
        nameFieldXtremName,
        descriptionFieldXtremName,
    };
}

/** Get if exist in the mapping WHENMODIFIED &&  WHENCREATED */
export function getWhenFields(fields: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties[]): string[] {
    const whenFields: string[] = [];

    if (fields.find(field => field.ID === 'WHENMODIFIED')) {
        whenFields.push('WHENMODIFIED');
    }
    if (fields.find(field => field.ID === 'WHENCREATED')) {
        whenFields.push('WHENCREATED');
    }

    return whenFields;
}

/**
 * Get all the fields needed to request
 * @param mapInstance
 * @returns
 */
export async function getFieldsToRequest(mapInstance: xtremIntacctFinance.nodes.IntacctMap): Promise<string[]> {
    const file = await mapInstance.relationMapping;

    const whenFields = getWhenFields(file.fields); //  WHENMODIFIED && WHENCREATED

    // fields that are linked to "name" & "description" & "intacctId"
    const fieldsToRequest = [...(await mapInstance.specificFields.map(field => field.name).toArray()), ...whenFields];

    // Get INTACCT RECORD_URL & RECORDNO fields
    if ((await mapInstance.relationMapping).fields.find(field => field.ID === 'RECORD_URL')) {
        fieldsToRequest.push('RECORD_URL');
    }
    if (file.fields.find(field => field.ID === 'RECORDNO')) {
        fieldsToRequest.push('RECORDNO');
    }

    if (file.fields.find(field => field.ID === 'MEGAENTITYID')) {
        fieldsToRequest.push('MEGAENTITYID');
    }

    return fieldsToRequest;
}

/**
 *  get all the data from a node with additionnalFilter managed
 * @param context
 * @param mapInstance IntacctMap
 * @returns xtreem data
 */
export async function getXtreemData(
    context: Context,
    mapInstance: xtremIntacctFinance.nodes.IntacctMap,
    filterData: Omit<xtremIntacctFinance.interfaces.IMap.IntacctToXtreemRequest, 'intacctName'>,
    maxData?: number | 'MAX',
): Promise<xtremIntacct.nodes.IntacctNode[]> {
    if ((await mapInstance.nodeFactory) && (await mapInstance.xtremNode)) {
        const filterIntacctId: NodeQueryFilter<xtremIntacct.nodes.IntacctNode> = filterData.intacctIdValue
            ? { intacctId: filterData.intacctIdValue }
            : {};

        const filterSysId: NodeQueryFilter<xtremIntacct.nodes.IntacctNode> = filterData.xtremSysId
            ? { _id: Number(filterData.xtremSysId) }
            : await mapInstance.getAdditionalFilter();

        const filter = { _and: [filterIntacctId, filterSysId, filterData.filter || {}] };
        logger.debug(() => `filters : ${JSON.stringify(filter)}`);

        const numberOfNodes = await context.queryCount<xtremIntacct.nodes.IntacctNode>(await mapInstance.xtremNode, {
            filter,
        });

        if (maxData !== 'MAX' && numberOfNodes > (maxData || 500)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-query-to-many-lines',
                    'Too many lines returned from Sage DMO ({{numberOfNodes}}).\n Add filters and try again.',
                    { numberOfNodes },
                ),
            );
        }

        return context.query(await mapInstance.xtremNode, { filter }).toArray();
    }
    return [];
}

export async function getXtreemDataFormated(
    xtremValue: xtremIntacct.nodes.IntacctNode | null | undefined,
    xtremFieldNames: IMap.FieldXtreemName,
): Promise<IMap.XtreemDataMap> {
    if (xtremValue) {
        return {
            xtremSysId: String(xtremValue._id),
            xtremID: xtremFieldNames.idFieldXtremName
                ? (await xtremValue.$.getValue<string>(xtremFieldNames.idFieldXtremName)) || ''
                : 'No Link',
            xtremName: xtremFieldNames.nameFieldXtremName
                ? (await xtremValue.$.getValue<string>(xtremFieldNames.nameFieldXtremName)) || ''
                : '',
            xtremDescription: xtremFieldNames.descriptionFieldXtremName
                ? (await xtremValue.$.getValue<string>(xtremFieldNames.descriptionFieldXtremName)) || ''
                : '',
            integrationStatus: (await xtremValue.$.getValue('intacctIntegrationState')) || '',
        };
    }
    return {
        xtremSysId: '0',
        xtremID: '',
        xtremName: '',
        xtremDescription: '',
        integrationStatus: xtremCommunication.enums.IntegrationStateEnum.error.toLocaleString(),
    };
}

/** Get INTACCT Data  */
export async function getIntacctDataForCreateUpdate(
    context: Context,
    mapInstance: xtremIntacctFinance.nodes.IntacctMap,
    data: Omit<xtremIntacctFinance.interfaces.IMap.IntacctToXtreemRequest, 'intacctName'>,
): Promise<AnyRecord[]> {
    const intacctIdFieldName = await (await mapInstance.intacctIDField)?.name;
    const filters =
        intacctIdFieldName && data.intacctIdValue
            ? ([
                  { where: await (await mapInstance.intacctIDField)?.name, whereValue: data.intacctIdValue },
              ] as xtremIntacct.interfaces.WhereFields[])
            : [];

    /** to be able to filter intacct  */
    try {
        filters.push(...(await (await mapInstance.xtremNode).intacctFilter(context)));
    } catch {
        await context.logger.infoAsync(async () => `No intacctFilter for ${await mapInstance.id}`);
    }

    const payload = await getPayLoad(context, mapInstance, filters);

    if (payload.length > 1) {
        logger.warn(xtremIntacctFinance.functions.messages.toManyLines(context, payload.length.toString()));
    }
    if (payload.length === 1) {
        logger.debug(() => `intacctId:${intacctIdFieldName ? payload[0][intacctIdFieldName] : ''}`);
    }

    if (payload.length === 0) {
        throw new BusinessRuleError(
            xtremIntacctFinance.functions.messages.noIntactData(context, {
                intacctName: await mapInstance.intacctDescription,
                intacctIDField: await (await mapInstance.intacctIDField)?.name,
                intacctIdValue: data.intacctIdValue,
            }),
        );
    }
    return payload;
}

/**
 * ## INTACCT ==> XTREEM
 *  Create Xtreem data from intacct
 * @param context
 * @param mapInstance
 * @param data
 * @param intacctPayload
 * @param logger
 * @returns
 */
export async function createUpdateXtreem(
    context: Context,
    mapInstance: xtremIntacctFinance.nodes.IntacctMap,
    data: Omit<xtremIntacctFinance.interfaces.IMap.IntacctToXtreemRequest, 'intacctName'>,
    intacctPayload: AnyRecord[],
): Promise<{ diagnose: Diagnose[]; created: number; updated: number; error: number }> {
    /** To count o many creation & update done  */
    const returnResult = { created: 0, updated: 0, error: 0 };
    /** contains diagnose of all transaction created & deleted */
    const diagnosesToThrow: Diagnose[] = [];
    /** Filter for the IntacctNode any because we are on a generic Node (mapInstance.xtremNode can't be set) */

    // TODO : add batch logic there
    // context.batch.updateProgress({});
    // context.batch.logMessage()

    const intacctName = await mapInstance.id;
    const xtremInstance = await getXtreemData(context, mapInstance, data, 'MAX');
    logger.debug(
        () => `${intacctName} : ${xtremInstance.length} xtreemInstances - ${intacctPayload.length} intacctPayload `,
    );

    /** instance is the Sage DMO node , data the payload for creation / update  */
    const validPayloads = asyncArray(intacctPayload).map(async eachPayload => {
        let xtremPayload;
        try {
            xtremPayload = await (
                await mapInstance.xtremNode
            ).initPayload(context, {
                ...eachPayload,
                intacctName,
            });
        } catch (ex) {
            xtremPayload = undefined;
            diagnosesToThrow.push(new Diagnose(ValidationSeverity.error, ['map', 'updateXtrem'], ex.message));
        }

        const payloadIntacctId = xtremPayload ? xtremPayload.intacctId : eachPayload.intacctId;
        return {
            instance: await asyncArray(xtremInstance).find(
                async instance => (await instance.intacctId) === payloadIntacctId,
            ),
            xtremPayload,
        };
    });

    await validPayloads
        .filter(validPayload => !!validPayload.xtremPayload)
        .forEach(
            async (payloadToCreateUpdate: { instance: xtremIntacct.nodes.IntacctNode; xtremPayload: AnyRecord }) => {
                if (payloadToCreateUpdate?.xtremPayload.intacctName) {
                    // IntacctName can be use in the initPayload( for nodes that are linked to multiple intacct objects )
                    delete payloadToCreateUpdate.xtremPayload.intacctName;
                }

                const createUpdateReturn = await xtremIntacctFinance.functions.createOrUpdateFromIntacctCall(context, {
                    instanceSysId: payloadToCreateUpdate.instance?._id || -1,
                    xtremNode: await mapInstance.xtremNode,
                    xtremPayload: payloadToCreateUpdate.xtremPayload,
                });
                returnResult.created += createUpdateReturn.isCreated ? 1 : 0;
                returnResult.updated += createUpdateReturn.isUpdated ? 1 : 0;
                returnResult.error += createUpdateReturn.isError ? 1 : 0;
                diagnosesToThrow.push(...createUpdateReturn.diagnoses);
            },
        );
    if (
        data.isThrowingDiagnose &&
        diagnosesToThrow.some(diagnose =>
            [ValidationSeverity.error, ValidationSeverity.exception].includes(diagnose.severity),
        )
    ) {
        // For UI to show the error
        let message = '';
        if (returnResult.created > 0 && (await mapInstance.id) === 'TAXDETAIL') {
            message = context.localize(
                '@sage/xtrem-intacct-finance/functions__map/xtreem-mass-creation-tax-category-update-with-error',
                'After integrating, you need to update the tax category.',
            );
        }
        throw new BusinessRuleError(
            [
                context.localize(
                    '@sage/xtrem-intacct-finance/nodes__intacct-map/xtrem-instance-created-updated-total',
                    'Records created: {{created}}. /n Records updated: {{updated}}. /n Records with errors: {{error}}.',
                    returnResult,
                ),
                ...diagnosesToThrow.map(diagnose => diagnose.message),
                message,
            ].join('\n'),
        );
    }
    // diagnose are returned for the listener, to have a general handling of the result ( added in history )
    return { diagnose: diagnosesToThrow, ...returnResult };
}

/**
 *
 * @param context readOnly context
 * @param data
 * @returns
 */
export async function updateXtrem(
    context: Context,
    data: xtremIntacctFinance.interfaces.IMap.IntacctToXtreemRequest,
): Promise<{ diagnose: Diagnose[]; created: number; updated: number; isQueued?: boolean }> {
    const mapInstance = await getMapInstance(context, data.intacctName);

    /** Payload  data to create update on xtrem  */
    const payload = await getIntacctDataForCreateUpdate(context, mapInstance, data);
    logger.debug(() => `${payload.length} to create update `);
    if (payload.length === 1) {
        return createUpdateXtreem(context, mapInstance, data, payload);
    }
    throw new BusinessRuleError(
        context.localize(
            '@sage/xtrem-intacct-finance/nodes__intacct-map/create-update-xtrem',
            'Too many lines returned from Sage Intacct ({{lenght}}) - intacctId : {{intacctIdValue}}.',
            { lenght: payload.length, ...data },
        ),
    );
}
