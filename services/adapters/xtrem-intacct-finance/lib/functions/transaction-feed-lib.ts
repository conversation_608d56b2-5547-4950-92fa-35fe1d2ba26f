import type { Context, NodeCreateData, NodePayloadData, NodeQueryFilter, NodeUpdateData } from '@sage/xtrem-core';
import { date, Logger } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremIntacctFinance from '..';
import type { IntacctArInvoice } from '../interfaces/bank-account-matching';

/** - matched
    - readyForPosting
    - journalEntryPosted
    - journalEntryFound
    - draftMatch */
export const matched: xtremIntacctFinance.enums.FeedRecordStatus[] = [
    'matched',
    'readyForPosting',
    'journalEntryPosted',
    'journalEntryFound',
    'draftMatch',
];

/**
 * - unmatched
   - lookingForExistingJournalEntries
   - journalEntriesFound
 */
export const unMatched: xtremIntacctFinance.enums.FeedRecordStatus[] = [
    'unmatched',
    'lookingForExistingJournalEntries',
    'journalEntriesFound',
];

async function getMatchingRulesFilter(
    bankAccount: xtremFinanceData.nodes.BankAccount,
): Promise<NodeQueryFilter<xtremIntacctFinance.nodes.IntacctBankAccountMatching>> {
    return {
        _or: [
            {
                bankAccount: { id: { _eq: await bankAccount.id } },
            },
            { bankAccount: { _eq: null } },
        ],
    };
}

/** Only query to getMatching Rules !  */
export async function getMatchingRules(
    context: Context,
    bankAccount: xtremFinanceData.nodes.BankAccount,
    additionnalFilter: NodeQueryFilter<xtremIntacctFinance.nodes.IntacctBankAccountMatching>,
    transactionType?: xtremIntacctFinance.enums.IntacctRecordTransactionType,
) {
    const typeFilter = transactionType ? { transactionType } : undefined;
    const matchingRulesFilter = {
        ...typeFilter,
        ...(await getMatchingRulesFilter(bankAccount)),
        ...additionnalFilter,
    } as NodeQueryFilter<xtremIntacctFinance.nodes.IntacctBankAccountMatching>;

    return context
        .query(xtremIntacctFinance.nodes.IntacctBankAccountMatching, {
            filter: matchingRulesFilter,
            orderBy: { bankAccount: -1, priority: 1 },
        })
        .toArray();
}

export async function getMatchLineProcess(
    context: Context,
    transactionFeed: NodeCreateData<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed>,
    matchingRules: xtremIntacctFinance.nodes.IntacctBankAccountMatching[],
    logger: Logger,
): Promise<NodeUpdateData<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed>> {
    const { matchedStatus, account, tax, storedDimensions, storedAttributes } =
        await xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed.getMatchLine(
            context,
            transactionFeed,
            matchingRules,
        );
    logger.debug(() => `${transactionFeed.description} : ${matchedStatus} , ${account?.composedDescription}`);

    const { taxAmount, rate } =
        tax && transactionFeed.amount
            ? await xtremTax.nodes.Tax.calculateTax(
                  context,
                  tax,
                  transactionFeed.postingDate || date.today(),
                  transactionFeed.amount,
              )
            : { taxAmount: undefined, rate: undefined };

    const bankAccount = transactionFeed.bankAccount as NodeCreateData<xtremFinanceData.nodes.BankAccount>;
    const financialSiteSysId = // Perf issue : To not read bankAccount everyTime financialSiteSysId as been added on the initPayload
        bankAccount && bankAccount.financialSite
            ? (bankAccount.financialSite as NodeCreateData<xtremSystem.nodes.Site>)._id
            : undefined;

    const location = {
        _id:
            financialSiteSysId ||
            (
                await (
                    await context.read(xtremFinanceData.nodes.BankAccount, { _id: bankAccount._id || -1 })
                ).financialSite
            )._id,
    };

    return {
        storedDimensions,
        storedAttributes,
        lines: [
            {
                status: matchedStatus,
                amount: transactionFeed.amount || 0,
                account: account
                    ? {
                          _id: account?._id,
                          name: await account?.name,
                          composedDescription: await account?.composedDescription,
                      }
                    : undefined,
                tax: tax ? { _id: tax?._id, name: await tax?.name } : undefined,
                taxAmount,
                taxRate: rate,
                storedDimensions,
                storedAttributes,
                location,
            },
        ],
    };
}

/** create a transient transaction feed  */
export async function transientTransactionFeed(
    context: Context,
    parameters: {
        data: NodePayloadData<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed>;
        matchingRules: xtremIntacctFinance.nodes.IntacctBankAccountMatching[];
        isPersisted: boolean;
        intacctImportSession?: xtremIntacctFinance.nodes.IntacctImportSession;
    },

    logger?: Logger,
): Promise<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed> {
    const myLogger = logger || Logger.getLogger(__filename, 'cashbook-lib');
    const transactionFeedData = await xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed.initPayload(
        context,
        parameters.data,
    );
    const transactionFeed = await context.create(
        xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed,
        transactionFeedData,
        {
            isTransient: !parameters.isPersisted,
        },
    );

    if (!parameters.data.arMatch?.isArMatch) {
        // We set the first line of the transactionFeed
        const dataToset = await getMatchLineProcess(context, transactionFeedData, parameters.matchingRules, myLogger);
        myLogger.debug(() => JSON.stringify(dataToset, null, 4));
        await transactionFeed.$.set(dataToset);
    }

    return transactionFeed;
}

export function transientTransactionFeedWritable(
    context: Context,
    parameters: {
        data: NodePayloadData<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed>;
        matchingRules: xtremIntacctFinance.nodes.IntacctBankAccountMatching[];
        intacctImportSession?: xtremIntacctFinance.nodes.IntacctImportSession;
    },
    logger?: Logger,
) {
    return context.runInWritableContext(async writableContext => {
        const transactionFeeed = await transientTransactionFeed(
            writableContext,
            { ...parameters, isPersisted: true },
            logger,
        );
        await transactionFeeed.$.set({ importSession: { _id: parameters.intacctImportSession?._id } });
        await transactionFeeed.$.save();
        return transactionFeeed;
    });
}

/**
 *  get the ar invoices for a given bank feed
 * @param data  given bank feed
 * @param outstandingInvoices list of ar invoices
 * @returns the customerId of the arInvoice
 */
export function matchBankFeedArInvoices(
    data: NodePayloadData<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed>,
    outstandingInvoices: IntacctArInvoice[],
): string {
    // TODO  improve this match / create one regex with RE2 package
    // match is done case by case because there are properties more relevant then others

    // try to match with invoiceNo first - description
    if (data.description) {
        const arInvoices = outstandingInvoices.find(outstandingInvoice =>
            (data.description || '').match(new RegExp(outstandingInvoice.invoiceNo, 'i')),
        );
        if (arInvoices && arInvoices.customerId) {
            return arInvoices.customerId;
        }
    }

    // try to match with invoiceNo first - payee
    if (data.payee) {
        const arInvoices = outstandingInvoices.find(outstandingInvoice =>
            (data.payee || '').match(new RegExp(outstandingInvoice.invoiceNo, 'i')),
        );
        if (arInvoices && arInvoices.customerId) {
            return arInvoices.customerId;
        }
    }

    // then try to match with customerId - description
    if (data.description) {
        const arInvoices = outstandingInvoices.find(outstandingInvoice =>
            (data.description || '').match(new RegExp(outstandingInvoice.customerId, 'i')),
        );
        if (arInvoices && arInvoices.customerId) {
            return arInvoices.customerId;
        }
    }

    // then try to match with customerId - payee
    if (data.payee) {
        const arInvoices = outstandingInvoices.find(outstandingInvoice =>
            (data.payee || '').match(new RegExp(outstandingInvoice.customerId, 'i')),
        );
        if (arInvoices && arInvoices.customerId) {
            return arInvoices.customerId;
        }
    }

    // finally try to match with customerName - description
    if (data.description) {
        const arInvoices = outstandingInvoices.find(outstandingInvoice =>
            (data.description || '').match(new RegExp(outstandingInvoice.customerName, 'i')),
        );
        if (arInvoices && arInvoices.customerId) {
            return arInvoices.customerId;
        }
    }

    // finally try to match with customerName - payee
    if (data.payee) {
        const arInvoices = outstandingInvoices.find(outstandingInvoice =>
            (data.payee || '').match(new RegExp(outstandingInvoice.customerName, 'i')),
        );
        if (arInvoices && arInvoices.customerId) {
            return arInvoices.customerId;
        }
    }

    return '';
}
