import type { ValidationContext } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremIntacctFinance from '..';
import type { FinanceDocumentType } from './finance-type';

export function alreadySentToIntacct(document: {
    financeDocument: FinanceDocumentType;
    cx: ValidationContext;
    integrationState: string;
}) {
    const { financeDocument, cx, integrationState } = document;
    if (
        financeDocument instanceof xtremFinance.nodes.AccountsPayableInvoice ||
        financeDocument instanceof xtremIntacctFinance.nodeExtensions.AccountsPayableInvoiceExtension
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-intacct-finance/node-extensions__accounts-payable-invoice-extension__accounts_payable_invoice_already_sent',
            'The accounts payable invoice was already sent to Sage Intacct. Status: {{intacctIntegrationState}}',
            { integrationState },
        );
    }
    if (
        financeDocument instanceof xtremFinance.nodes.AccountsReceivableInvoice ||
        financeDocument instanceof xtremIntacctFinance.nodeExtensions.AccountsReceivableInvoiceExtension
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-invoice-extension__accounts_receivable_invoice_already_sent',
            'The accounts receivable invoice has already been sent to Sage Intacct. Status : {{intacctIntegrationState}}',
            { integrationState },
        );
    }
    if (
        financeDocument instanceof xtremFinance.nodes.AccountsReceivableAdvance ||
        financeDocument instanceof xtremIntacctFinance.nodeExtensions.AccountsReceivableAdvanceExtension
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-advance-extension__accounts_receivable_advance_already_sent',
            'The accounts receivable advance was sent to Sage Intacct. Status : {{intacctIntegrationState}}',
            { integrationState },
        );
    }
    if (
        financeDocument instanceof xtremFinance.nodes.AccountsReceivablePayment ||
        financeDocument instanceof xtremIntacctFinance.nodeExtensions.AccountsReceivablePaymentExtension
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-intacct-finance/node-extensions__accounts-receivable-payment-extension__accounts_receivable_payment_already_sent',
            'The accounts receivable payment was sent to Sage Intacct. Status : {{intacctIntegrationState}}',
            { integrationState },
        );
    }
    if (
        financeDocument instanceof xtremFinance.nodes.JournalEntry ||
        financeDocument instanceof xtremIntacctFinance.nodeExtensions.JournalEntryExtension
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-intacct-finance/node-extensions__journal-entry-extension__journal_entry_already_sent',
            'The journal entry has already been sent to Sage Intacct. Status: {{intacctIntegrationState}}',
            { integrationState },
        );
    }
}
