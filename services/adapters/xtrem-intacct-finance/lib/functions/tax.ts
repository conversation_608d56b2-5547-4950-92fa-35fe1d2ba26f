import { asyncArray } from '@sage/xtrem-core';
import type * as xtremFinance from '@sage/xtrem-finance';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremIntacct from '@sage/xtrem-intacct';
import type * as xtremStructure from '@sage/xtrem-structure';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremTax from '@sage/xtrem-tax';
import type { IntacctTaxEntries } from '../interfaces/tax';

async function getDetailedId(taxReference: xtremTax.nodes.Tax | null, isReverseCharge = false): Promise<string> {
    return isReverseCharge
        ? (await taxReference?.secondaryExternalReference) || ''
        : (await taxReference?.primaryExternalReference) || '';
}

function getTrxTax(data: {
    originDocument: xtremFinanceData.enums.AccountsPayableReceivableInvoiceOrigin;
    isReverseCharge?: boolean;
    taxAmountAdjusted: number;
}): string {
    if (data.originDocument === 'creditMemo') {
        return (data.isReverseCharge ? data.taxAmountAdjusted : -data.taxAmountAdjusted).toString();
    }
    return (data.isReverseCharge ? -data.taxAmountAdjusted : data.taxAmountAdjusted).toString();
}

/**
 * generate TAXENTRIES json function for intacct creation
 * @param data
 * @returns
 */
export async function manageTaxEntries(data: {
    originDocument: xtremFinanceData.enums.AccountsPayableReceivableInvoiceOrigin;
    financialSite: xtremSystem.nodes.Site;
    taxes: xtremTax.nodes.BaseLineTax[]; // need subNoding for this one
}): Promise<IntacctTaxEntries[]> {
    if (['GB', 'ZA', 'FR', 'DE'].includes(await (await (await data.financialSite.legalCompany).legislation).id)) {
        return asyncArray(data.taxes)
            .map(async (taxLine: xtremFinance.nodes.AccountsReceivableInvoiceLineTax) => {
                const isReverseCharge = await taxLine.isReverseCharge;
                const taxEntries: IntacctTaxEntries = {
                    TAXENTRIES: [
                        {
                            TAXENTRY: {
                                DETAILID: await getDetailedId(await taxLine.taxReference),
                                TRX_TAX: getTrxTax({ ...data, taxAmountAdjusted: await taxLine.taxAmountAdjusted }),
                            },
                        },
                    ],
                };
                if (isReverseCharge) {
                    taxEntries.TAXENTRIES.push({
                        TAXENTRY: {
                            DETAILID: await getDetailedId(await taxLine.taxReference, isReverseCharge),
                            TRX_TAX: getTrxTax({
                                ...data,
                                isReverseCharge,
                                taxAmountAdjusted: await taxLine.taxAmountAdjusted,
                            }),
                        },
                    });
                }
                return taxEntries;
            })
            .toArray();
    }
    return [];
}

/**
 * generate TAXENTRIES json function for intacct creation for journal entries
 * @param accountingStagingLines: array of xtremFinance.nodes.JournalEntryLineStaging
 * @returns IntacctTaxEntries[]
 */
export function manageJournalEntryLineTaxEntries(
    accountingStagingLines: xtremFinance.nodes.JournalEntryLineStaging[],
): Promise<IntacctTaxEntries[]> {
    return asyncArray(accountingStagingLines)
        .map(async accountingStagingLine => {
            const taxEntries: IntacctTaxEntries = { TAXENTRIES: [] };
            taxEntries.TAXENTRIES = await (
                await accountingStagingLine.accountingStaging
            ).amounts
                .filter(async amountLine => (await amountLine.amountType) === 'taxAmount')
                .map(async amountLine => {
                    return {
                        TAXENTRY: {
                            DETAILID: (await (await amountLine.tax)?.primaryExternalReference) || '',
                            TRX_TAX: (await amountLine.amount).toString(),
                        },
                    };
                })
                .toArray();
            return taxEntries;
        })
        .toArray();
}

async function getIntacctLine(
    intacctConfiguration: xtremIntacct.nodes.Intacct,
    taxSolutionId: string,
    country: string,
): Promise<xtremIntacct.nodes.IntacctLine | null> {
    return (
        (await intacctConfiguration.lines
            .filter(async line =>
                taxSolutionId
                    ? (await line.taxSolution) === taxSolutionId
                    : (await (await line.country).id) === country,
            )
            .at(0)) || null
    );
}

/**
 * Returns the taxSolution linked to the given country in the lines of the given intacctConfiguration.
 * If no country is passed in, we return a comma separated string with all the taxSolutions of all lines in the configuration
 * @param intacctConfiguration: Intacct node in which we search for the taxSolution in the lines
 * @param country: Id of the country for which we need the taxSolution or empty string
 * @returns string[]
 */
export async function getTaxSolution(
    intacctConfiguration: xtremIntacct.nodes.Intacct,
    country = '',
): Promise<string[]> {
    if (country) {
        return [(await (await getIntacctLine(intacctConfiguration, '', country))?.taxSolution) || ''];
    }
    if (!intacctConfiguration) {
        return [];
    }
    return intacctConfiguration.lines.map(line => line.taxSolution).toArray();
}

/**
 * Returns the country linked to the first line in the given intacctConfiguration which has the given taxSolutionId
 * If the taxSolutionId === '' we return the country of the first line in the intacctConfiguration
 * @param intacctConfiguration: Intacct node in which we search for the country in the lines
 * @param taxSolutionId: name of the tax solution in Intacct
 * @returns Country || null
 */
export async function getTaxCountry(
    intacctConfiguration: xtremIntacct.nodes.Intacct,
    taxSolutionId: string,
): Promise<xtremStructure.nodes.Country | null> {
    if (taxSolutionId) {
        return (await getIntacctLine(intacctConfiguration, taxSolutionId, ''))?.country || null;
    }
    const confLine = await intacctConfiguration?.lines.elementAt(0);
    if (!confLine) {
        return null;
    }
    return confLine.country;
}

/**
 * Returns the taxCategory linked to the first line in the given intacctConfiguration which has the given taxSolutionId.
 * If the taxSolutionId is empty, we return the taxCategory of the first line in the intacctConfiguration
 * @param intacctConfiguration: Intacct node in which we search for the taxCategory in the lines
 * @param taxSolutionId: name of the tax solution in Intacct
 * @returns TaxCategory || null
 */
export async function getTaxCategory(
    intacctConfiguration: xtremIntacct.nodes.Intacct,
    taxSolutionId: string,
): Promise<xtremTax.nodes.TaxCategory | null> {
    if (taxSolutionId) {
        return (await getIntacctLine(intacctConfiguration, taxSolutionId, ''))?.taxCategory || null;
    }
    const confLine = await intacctConfiguration?.lines.elementAt(0);
    if (!confLine) {
        return null;
    }
    return confLine.taxCategory;
}
