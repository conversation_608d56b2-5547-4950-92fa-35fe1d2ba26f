import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, asyncArray } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import type * as xtremIntacctFinance from '../../index';

function mapCompanyOnHold(companyOnHold: string): xtremMasterData.enums.CustomerOnHoldType | null {
    switch (companyOnHold) {
        case 'WARN':
            return 'warning';
        case 'DISALLOW':
            return 'blocking';
        case 'NOCHECK':
            return 'none';
        default:
            return null;
    }
}

async function getCompanyPrefs(
    context: Context,
    entityId: string | undefined,
): Promise<xtremMasterData.enums.CustomerOnHoldType> {
    const intacctCompanyRef =
        (
            await new xtremIntacct.classes.sdk.Functions.GetCompanyPrefs<
                xtremIntacctFinance.interfaces.CompanyOnHoldStatus[]
            >(
                context,
                {
                    synchronous: true,
                    entityId,
                },
                'AR',
            ).execute()
        ).find(companyPref => companyPref.preference === 'ONHOLDCHECK')?.prefvalue || '';

    const mappedValue = mapCompanyOnHold(intacctCompanyRef);

    if (!mappedValue) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-intacct-finance/nodes__company_get_company_ref_not_mapped',
                'The return value from Intacct {{intacctCompanyRef}} cannot be mapped.',
                { intacctCompanyRef },
            ),
        );
    }

    return mappedValue;
}

async function updateCompanyPref(
    context: Context,
    company: {
        _id: number;
        onHoldCheck: xtremMasterData.enums.CustomerOnHoldType;
    },
) {
    const companyForUpdate = await context.read(xtremSystem.nodes.Company, { _id: company._id }, { forUpdate: true });

    if (company.onHoldCheck !== (await companyForUpdate.customerOnHoldCheck)) {
        await companyForUpdate.$.set({
            customerOnHoldCheck: company.onHoldCheck,
        });

        companyForUpdate.canUpdateFromExternalIntegration = true;

        await companyForUpdate.$.save();

        await context.batch.updateProgress({
            detail: `${await companyForUpdate.id} updated`,
            phase: 'update',
        });

        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-intacct-finance/company_on_hold_success',
                'Company {{companyForUpdateId}} successfully updated',
                {
                    companyForUpdateId: await companyForUpdate.id,
                },
            ),
        );
    }
}

export async function syncCompanyOnHold(context: Context, company: { _id: number; isAll: boolean }): Promise<boolean> {
    const filter = company.isAll
        ? {}
        : {
              _id: { _eq: company._id },
          };

    const companies = await context.select(
        xtremSystem.nodes.Company,
        {
            _id: true,
            id: true,
        },
        {
            filter,
        },
    );

    await context.batch.updateProgress({
        detail: 'synchronized',
        errorCount: 0,
        successCount: 0,
        totalCount: companies.length,
        phase: 'start',
    });

    await context.batch.logMessage(
        'info',
        context.localize('@sage/xtrem-intacct-finance/company_on_hold_start', 'Company on hold sync start'),
    );

    const defaultInstance = await xtremIntacct.nodes.Intacct.defaultInstance(context);

    const intacctCompanies = defaultInstance
        ? await xtremIntacct.nodes.Intacct.getEntities(context, await defaultInstance.id)
        : null;

    const rootCompanyOnHold = await getCompanyPrefs(context, undefined);

    await asyncArray(companies).forEach(async (companyToUpdate, i) => {
        if (await context.batch.isStopRequested()) {
            await context.batch.logMessage(
                'info',
                context.localize(
                    '@sage/xtrem-intacct-finance/company_on_hold_stop',
                    'Stop requested at {{stopDate}} ',
                    {
                        stopDate: Date.now().toString(),
                    },
                ),
            );

            await context.batch.confirmStop();
            return;
        }

        await context.batch.updateProgress({
            detail: 'processing',
            errorCount: 0,
            successCount: i,
            totalCount: companies.length,
            phase: 'processing',
        });

        await updateCompanyPref(context, {
            _id: companyToUpdate._id,
            onHoldCheck: intacctCompanies?.some(intacctCompany => intacctCompany.id === companyToUpdate.id)
                ? await getCompanyPrefs(context, companyToUpdate.id)
                : rootCompanyOnHold,
        });
    });

    await context.batch.updateProgress({
        detail: 'complete',
        errorCount: 0,
        successCount: companies.length,
        totalCount: companies.length,
        phase: 'done',
    });

    await context.batch.logMessage(
        'result',
        context.localize('@sage/xtrem-intacct-finance/company_on_hold_complete', 'Company on hold sync complete'),
    );

    return true;
}
