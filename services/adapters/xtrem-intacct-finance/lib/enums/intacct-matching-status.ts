import { EnumDataType } from '@sage/xtrem-core';

export enum IntacctMatchingStatusEnum {
    unmatched,
    cleared,
    matched,
    partiallyMatched,
    selectToMatch,
    selectToUnmatch,
    ignored,
    draftMatched,
}

export type IntacctMatchingStatus = keyof typeof IntacctMatchingStatusEnum;

export const IntacctmatchingStatusDataType = new EnumDataType<IntacctMatchingStatus>({
    enum: IntacctMatchingStatusEnum,
    filename: __filename,
});
