import { EnumDataType } from '@sage/xtrem-core';

export enum FeedRecordStatusEnum {
    /** intacct status is matched */ matched,
    unmatched,
    /** Looking on intacct */ lookingForExistingJournalEntries,
    /** we Found one journal entry on intacct */ journalEntryFound,
    /** we Found multiple journal entry on intacct */ journalEntriesFound,
    /** matching rule as been apply */ draftMatch,
    /** the JE is ready to create */ readyForPosting,
    /** deprecated */ journalEntryPosted,
    /** the JE is being created in SDMO */ journalEntryGenerationInProgress,
    /** The JE creation ended in error in SDMO */ journalEntryGenerationError,
    /** The JE has been created in SDMO */ journalEntryGenerated,
    /** The JE is being sent to Intacct Fin. */ journalEntryPostingInProgress,
    /** the JE has not been sent to Intacct Fin. due to an error. */ journalEntryPostingError,
    /** we found at least on ar invoice that we were able to match with the bank feed */ draftArMatch,
    /** user selected ar invoices to match for the total amount of the bank feed */ readyForArPaymentGeneration,
    arPaymentGenerationInProgress,
    arPaymentPostingInProgress,
    arPaymentPosted,
    readyForArAdvanceGeneration,
    arAdvancePosted,
    /** user selected ar invoices to match, but not for the total amount of the bank feed */ partialArMatch,
    draftArAdvance,
    arPaymentGenerated,
    arAdvanceGenerated,
    arPaymentPostingError,
    arAdvancePostingInProgress,
    arAdvancePostingError,
}

export type FeedRecordStatus = keyof typeof FeedRecordStatusEnum;

export const FeedRecordStatusDataType = new EnumDataType<FeedRecordStatus>({
    enum: FeedRecordStatusEnum,
    filename: __filename,
});
