import { EnumDataType } from '@sage/xtrem-core';

export enum FeedLineMatchingStatusEnum {
    /** defaultStatus before looking for matches */
    lookingForMatches,
    /** JE generated */
    matched,
    /** matching rules applieds */
    draftMatch,
    /** nothing has done to the line  */
    unmatched,
    /** multiple document, user need to select one */
    multipleMatches,
    /** there is same record in intacct found */
    matchFound,
}

export type FeedLineMatchingStatus = keyof typeof FeedLineMatchingStatusEnum;

export const FeedLineMatchingStatusDataType = new EnumDataType<FeedLineMatchingStatus>({
    enum: FeedLineMatchingStatusEnum,
    filename: __filename,
});
