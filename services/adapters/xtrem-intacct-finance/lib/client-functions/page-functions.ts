import { extractEdges } from '@sage/xtrem-client';
import type { GraphApi } from '@sage/xtrem-intacct-finance-api';
import * as ui from '@sage/xtrem-ui';

export function confirmDialogWithAcceptButtonText(
    page: ui.Page<GraphApi>,
    param: {
        title: string;
        message: string;
        acceptButtonText: string;
    },
) {
    const options = {
        acceptButton: {
            text: param.acceptButtonText,
        },
        cancelButton: {
            text: ui.localize('@sage/xtrem-intacct-finance/pages-confirm-cancel', 'Cancel'),
        },
    };
    return page.$.dialog
        .confirmation('warn', param.title, param.message, options)
        .then(() => true)
        .catch(() => false);
}

export async function checkOpenApArInvoices(page: ui.Page<GraphApi>) {
    const nonPostedAccountsPayableInvoices = extractEdges(
        await page.$.graph
            .node('@sage/xtrem-finance/AccountsPayableInvoice')
            .query(
                ui.queryUtils.edgesSelector(
                    { number: true },
                    {
                        filter: {
                            postingStatus: { _ne: 'posted' },
                            financialSite: { legalCompany: { legislation: { doApPosting: true } } },
                        },
                        first: 1,
                    },
                ),
            )
            .execute(),
    );

    if (nonPostedAccountsPayableInvoices.length) {
        await page.$.dialog.message(
            'warn',
            ui.localize('@sage/xtrem-intacct-finance/page_extensions__common__warning', 'Warning'),
            ui.localize(
                '@sage/xtrem-intacct-finance/page_extensions__there_are_ap_invoices_not_posted',
                'There are AP invoices that are not yet posted to the GL.',
            ),
        );
    }

    const nonPostedAccountsReceivableInvoices = extractEdges(
        await page.$.graph
            .node('@sage/xtrem-finance/AccountsReceivableInvoice')
            .query(
                ui.queryUtils.edgesSelector(
                    { number: true },
                    {
                        filter: {
                            postingStatus: { _ne: 'posted' },
                            financialSite: { legalCompany: { legislation: { doArPosting: true } } },
                        },
                        first: 1,
                    },
                ),
            )
            .execute(),
    );

    if (nonPostedAccountsReceivableInvoices.length) {
        await page.$.dialog.message(
            'warn',
            ui.localize('@sage/xtrem-intacct-finance/page_extensions__common__warning', 'Warning'),
            ui.localize(
                '@sage/xtrem-intacct-finance/page_extensions__there_are_ar_invoices_not_posted',
                'There are AR invoices that are not yet posted to the GL.',
            ),
        );
    }
}
