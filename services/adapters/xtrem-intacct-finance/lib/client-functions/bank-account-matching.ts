import type { ClientCollection } from '@sage/xtrem-client';
import type { IntacctBankAccountTransactionFeedLine } from '@sage/xtrem-intacct-finance-api';
import type { IntacctBankAccountTransactionFeedLine as IIntacctBankAccountTransactionFeedLine } from '../shared-functions/interfaces';

export function getBankFeedLinesDataForStatusUpdate(
    lines: ClientCollection<IntacctBankAccountTransactionFeedLine>,
): IIntacctBankAccountTransactionFeedLine[] {
    return lines
        ? lines.query.edges.map(line => ({
              status: line.node.status,
              amount: line.node.amount,
              account: line.node.account._id,
          }))
        : [];
}
