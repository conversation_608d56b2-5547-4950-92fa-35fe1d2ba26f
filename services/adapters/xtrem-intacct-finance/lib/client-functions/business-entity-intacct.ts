import type { BusinessEntity } from '@sage/xtrem-master-data/build/lib/pages/business-entity';
import type { Customer } from '@sage/xtrem-master-data/build/lib/pages/customer';
import type { Supplier } from '@sage/xtrem-master-data/build/lib/pages/supplier';
import type { ExtensionMembers } from '@sage/xtrem-ui/build/lib/service/page-extension';
import { integrationErrorLocalized } from './common';
import type { BusinessEntityExtension } from '../page-extensions/business-entity-extension';
import type { CustomerExtension } from '../page-extensions/customer-extension';
import type { SupplierExtension } from '../page-extensions/supplier-extension';

/** we encounter issues when loading Customer & supplier extension pages for the same client-function  */

export type CustomerPage = ExtensionMembers<Customer & CustomerExtension>;
export type SupplierPage = ExtensionMembers<Supplier & SupplierExtension>;
export type BusinessEntityPage = ExtensionMembers<BusinessEntity & BusinessEntityExtension>;

export type BePage = CustomerPage | SupplierPage | BusinessEntityPage;
export type CustomerSupplierPage = CustomerPage | SupplierPage;

function isCustomerPage(page: any): page is CustomerPage {
    return !!page.customer;
}

function isSupplierPage(page: any): page is SupplierPage {
    return !!page.supplier;
}

export async function refreshIntacctBlock(page: CustomerSupplierPage) {
    await page.intacctReference.refresh();
    page.intacctUrlLink.value = page.intacctReference.value?.intacctId || '';
    page.intacctIntegrationState.value = page.intacctReference.value?.state || 'error';
    page.intacctUrlLink.isDisabled = page.intacctIntegrationState.value !== 'success';
    return page.intacctReference.value;
}

export async function synchronizeOnError(page: CustomerSupplierPage) {
    page.synchronize.isDisabled = false;
    page.intacctBlock.isDisabled = false;
    page.synchronize.isHelperTextHidden = true;
    const intacctReference = await refreshIntacctBlock(page);
    if (intacctReference && intacctReference.state === 'error') {
        page.$.showToast(intacctReference.lastMessage || integrationErrorLocalized, { type: 'error' });
    }
}

async function updateIntacctCustomerSysId(page: CustomerPage) {
    await page.$.graph.update({
        values: { customer: { intacctCustomer: {} } },
        fieldsToReturn: ['intacctCustomer'],
    });
}

async function updateIntacctSupplierSysId(page: SupplierPage) {
    await page.$.graph.update({
        values: { supplier: { intacctSupplier: {} } },
        fieldsToReturn: ['intacctSupplier'],
    });
}

async function intacctChildCreated(page: CustomerSupplierPage) {
    if (!page.intacctReference.value?._id) {
        if (isCustomerPage(page)) {
            await updateIntacctCustomerSysId(page);
        }
        if (isSupplierPage(page)) {
            await updateIntacctSupplierSysId(page);
        }
    }
    return page.intacctReference.value?._id;
}

export async function synchronizeOnClick(page: CustomerSupplierPage) {
    page.synchronize.isDisabled = true;
    page.synchronize.isHelperTextHidden = false;
    page.intacctBlock.isDisabled = true;
    const integrationChildNodeSysId = await intacctChildCreated(page);
    if (integrationChildNodeSysId) {
        const { state } = await page.$.graph
            .node('@sage/xtrem-intacct-finance/IntacctListener')
            .asyncOperations.synchronizeNode.runToCompletion(
                { state: true },
                { intacctNode: integrationChildNodeSysId },
            )
            .execute();
        if (state) {
            await refreshIntacctBlock(page);
        }
        if (state === 'error') {
            await synchronizeOnError(page);
        }
    }
    page.synchronize.isHelperTextHidden = true;
    page.intacctBlock.isDisabled = false;
    page.synchronize.isDisabled = false;
}
