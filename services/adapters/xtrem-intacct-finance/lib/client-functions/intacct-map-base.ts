import type { <PERSON>raph<PERSON><PERSON> } from '@sage/xtrem-intacct-finance-api';
import * as ui from '@sage/xtrem-ui';
import type { InspectObject, LookupObject } from '../shared-functions/interfaces';

export abstract class IntacctMapBase extends ui.Page<GraphApi> {
    lookupObject: LookupObject;

    intacctObjectList: { name: string; object: string }[];

    xtremNodeList: { code: string; name: string }[];

    xtremProperties: { name: string }[];

    xtremPropertiesNameArray: string[];

    /** notification of the massCreation */
    notificationSysId: string;

    async baseOnload() {
        this.intacctObjectList = await this.queryIntacctTransactions();
    }

    queryIntacctTransactions(): Promise<InspectObject[]> {
        return this.$.graph
            .node('@sage/xtrem-intacct-finance/IntacctMap')
            .queries.getIntacctTransactionsList({ name: true, object: true }, {})
            .execute();
    }

    loadIntacctObject(map: { id: string | null; intacctName: string | null }, isUpdate = false): Promise<LookupObject> {
        return map.id && map.intacctName
            ? this.$.graph
                  .node('@sage/xtrem-intacct-finance/IntacctMap')
                  .queries.getObject(
                      {
                          documentType: true,
                          name: true,
                          xtremObject: true,
                          fields: {
                              CREATEONLY: true,
                              DATATYPE: true,
                              DESCRIPTION: true,
                              ID: true,
                              ISCUSTOM: true,
                              VALIDVALUES: {
                                  VALIDVALUE: true,
                              },
                              LABEL: true,
                              READONLY: true,
                              REQUIRED: true,
                              isEditable: true,
                              xtremProperty: true,
                              xtremDefaultProperty: true,
                          },
                          relationshipFields: {
                              CREATEONLY: true,
                              DATATYPE: true,
                              DESCRIPTION: true,
                              ID: true,
                              ISCUSTOM: true,
                              VALIDVALUES: {
                                  VALIDVALUE: true,
                              },
                              LABEL: true,
                              READONLY: true,
                              REQUIRED: true,
                              isEditable: true,
                              xtremProperty: true,
                              xtremDefaultProperty: true,
                          },
                          relationships: {
                              LABEL: true,
                              OBJECTNAME: true,
                              OBJECTPATH: true,
                              RELATEDBY: true,
                              RELATIONSHIPTYPE: true,
                              xtremProperty: true,
                          },
                      },
                      { object: map.id, docparid: map.intacctName, isUpdate },
                  )
                  .execute()
            : ({} as Promise<LookupObject>);
    }

    public columnTitleProperty = {
        xtremId: `xtrem ${ui.localize('@sage/xtrem-intacct-finance/id', 'ID')}`,
        intacctId: `intacct ${ui.localize('@sage/xtrem-intacct-finance/id', 'ID')}`, // 5
        xtremName: `xtrem ${ui.localize('@sage/xtrem-intacct-finance/name', 'Name')}`, // 6
        intacctName: `intacct ${ui.localize('@sage/xtrem-intacct-finance/name', 'Name')}`, // 5
        xtremDescription: `xtrem ${ui.localize('@sage/xtrem-intacct-finance/description', 'Description')}`, // 6
        intacctDescription: `intacct ${ui.localize('@sage/xtrem-intacct-finance/description', 'Description')}`, // 5
    };
}
