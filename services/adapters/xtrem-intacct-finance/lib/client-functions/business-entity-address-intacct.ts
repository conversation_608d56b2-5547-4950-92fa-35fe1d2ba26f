import type { BusinessEntityAddress } from '@sage/xtrem-master-data-api';
import type * as ui from '@sage/xtrem-ui';
import type { BePage } from './business-entity-intacct';
import { integrationErrorLocalized } from './common';

export async function integrationAddressOnError(page: BePage, rowData: ui.PartialNodeWithId<BusinessEntityAddress>) {
    page.addresses.isDisabled = false;
    await page.$.router.refresh();
    if (rowData?.intacctBusinessEntityAddress?.state === 'error') {
        page.$.showToast(rowData.intacctBusinessEntityAddress.lastMessage || integrationErrorLocalized, {
            type: 'error',
        });
    }
}

async function getIntacctBusinessEntityAddressSysId(
    page: BePage,
    rowData: ui.PartialNodeWithId<BusinessEntityAddress>,
) {
    if (rowData.intacctBusinessEntityAddress?._id) {
        return rowData.intacctBusinessEntityAddress._id;
    }
    const intacctBusinessEntityAddressSysId = await page.$.graph
        .node('@sage/xtrem-master-data/BusinessEntityAddress')
        .updateById(
            { intacctBusinessEntityAddress: { _id: true } },
            { _id: rowData._id, data: { intacctBusinessEntityAddress: {} } },
        )
        .execute();
    return intacctBusinessEntityAddressSysId.intacctBusinessEntityAddress._id;
}

export async function integrationAddressOnClick(page: BePage, rowData: ui.PartialNodeWithId<BusinessEntityAddress>) {
    page.addresses.isDisabled = true;
    const intacctNodeSysid = await getIntacctBusinessEntityAddressSysId(page, rowData);
    if (intacctNodeSysid) {
        const { state } = await page.$.graph
            .node('@sage/xtrem-intacct-finance/IntacctListener')
            .asyncOperations.synchronizeNode.runToCompletion({ state: true }, { intacctNode: intacctNodeSysid })
            .execute();
        if (state) {
            await page.$.router.refresh();
        }
        if (state === 'error') {
            page.$.showToast(rowData.intacctBusinessEntityAddress?.lastMessage || integrationErrorLocalized, {
                type: 'error',
            });
        }
    }
    page.addresses.isDisabled = false;
}
