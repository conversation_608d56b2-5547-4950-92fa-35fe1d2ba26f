import type { MapProperty } from '@sage/xtrem-intacct-finance-api';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { SynchronizationDirection } from '@sage/xtrem-synchronization-api';
import type { PartialCollectionValue } from '@sage/xtrem-ui';
import { localize } from '@sage/xtrem-ui';
import type { IntacctMap } from '../pages/intacct-map';
import type { LookupProperties } from '../shared-functions/interfaces';

export function recordValueDefaultforSpecificFields(specificFields: Array<PartialCollectionValue<MapProperty>>) {
    if (!specificFields.some(value => value.type === 'intacctId')) {
        return 'intacctId';
    }
    if (!specificFields.some(value => value.type === 'description')) {
        return 'description';
    }
    if (!specificFields.some(value => value.type === 'name')) {
        return 'name';
    }
    if (!specificFields.some(value => value.type === 'text')) {
        return 'text';
    }
    return 'number';
}

export async function resetRelationMappingFunc(page: IntacctMap) {
    const reset = localize('@sage/xtrem-intacct-finance/reset', 'Reset');
    const cancel = localize('@sage/xtrem-intacct-finance/cancel', 'Cancel');
    const sysId = page._id.value;
    const isConfirmed = await utils.confirmDialogToBoolean(
        page.$.dialog.confirmation(
            'warn',
            localize('@sage/xtrem-intacct-finance/confirm-reset-dialog-title', 'Confirm reset'),
            localize(
                '@sage/xtrem-intacct-finance/confirm-reset',
                'You are about to reset the record mapping to its initial state.',
            ),
            { acceptButton: { text: reset }, cancelButton: { text: cancel } },
        ),
    );

    if (isConfirmed && sysId) {
        // relationMapping will be set to null so we will get the mapping from the file // origin
        await page.$.graph
            .node('@sage/xtrem-intacct-finance/IntacctMap')
            .updateById({ id: true }, { _id: sysId, data: { _id: sysId, relationMapping: '{}' } })
            .execute();

        await page.updateRelationMapping();
    }
}

export function isDisableCreateAll(synchronizationDirection: SynchronizationDirection | null): boolean {
    if (!synchronizationDirection) {
        return true;
    }
    return !['both', 'inbound'].includes(synchronizationDirection);
}

export function isDisableCreateAllIntacct(synchronizationDirection: SynchronizationDirection | null): boolean {
    if (!synchronizationDirection) {
        return true;
    }
    return !['both', 'outbound'].includes(synchronizationDirection);
}

export function isEditable(rowValue: LookupProperties) {
    if (rowValue?.isEditable == null) {
        return true;
    }
    return rowValue?.isEditable;
}
