import type { ExtractArray, OperationResultType } from '@sage/xtrem-client';
import type { PostingStatus } from '@sage/xtrem-finance-data-api';
import type { IntacctBankAccountMatching$Queries } from '@sage/xtrem-intacct-finance-api';
import type { DeepPartial } from '@sage/xtrem-shared';
import type { ApArPaymentMatchingData } from '../../shared-functions/interfaces';

export interface IntacctFinanceDocument {
    _id: string;
    description: string;
    documentType: string;
    journal: string;
    location: string;
    recordNo: string;
    date: string;
    amount: string;
    account: string;
    url: string;
    batchNo: string;
    entityId: string;
}
export interface ParametersForFinanceDocumentSearch {
    batchNoSelected?: string;
    account?: string;
    amount?: number;
    date?: string;
    description?: string;
    documentType?: string;
}

export interface ParametersForArInvoiceMatching extends Partial<ApArPaymentMatchingData> {
    bankAccount?: string;
    amountToMatch?: number;
    description?: string;
    payee?: string;
    postingDate?: string;
    arInvoicesAlreadySelected?: string;
    usedArInvoices?: string;
    arMatch?: string;
    bankFeedId?: number;
    financeIntegrationStatus?: PostingStatus;
    financeDocumentCreatedNumber?: string;
    financeDocumentCreatedSysId?: number;
    financeDocumentGenerationErrorMessage?: string;
    financeIntegrationAppUrl?: string;
    financeIntegrationAppRecordId?: string;
    chartOfAccountSysId?: string;
}

export type IntacctArInvoice = DeepPartial<
    ExtractArray<OperationResultType<IntacctBankAccountMatching$Queries['queryIntacctArInvoice']>>
>;

export type MatchingCriteria = 'invoiceNo' | 'customerId' | 'customerName' | '';
