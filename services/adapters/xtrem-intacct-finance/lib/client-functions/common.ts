import type { GraphApi } from '@sage/xtrem-intacct-finance-api';
import { formatError } from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { SynchronizationDirection } from '@sage/xtrem-synchronization-api';
import * as ui from '@sage/xtrem-ui';

export const integrationErrorLocalized = ui.localize(
    '@sage/xtrem-intacct-finance/pages_integration-error',
    'Integration Error',
);

export const outbound: SynchronizationDirection = 'outbound';
export const inbound: SynchronizationDirection = 'inbound';
export const both: SynchronizationDirection = 'both';

/** Format error for the intacct mapping returns :
 * - First line as a title
 * - carriage return with two \n
 *  mdContent:true
 */
export function formatErrorIntacctMapping(page: ui.Page, error: string | (Error & { errors: Array<any> })): string {
    const formatedError = formatError(page, error).split('\n');
    formatedError[0] = `######${formatedError[0]}`;
    return formatedError.join('\n\n');
}

export async function isActive(page: ui.Page<GraphApi>): Promise<boolean> {
    return (
        (
            await page.$.graph
                .node('@sage/xtrem-intacct/Intacct')
                .queries.defaultInstance({ _id: true, isActive: true }, { isThrowing: false })
                .execute()
        )?.isActive || false
    );
}

type NodeName = 'item' | 'supplier' | 'customer';

export function getIntacctUrl(page: ui.Page<GraphApi>, _id: string, nodeName: NodeName): Promise<string> {
    return page.$.graph
        .node('@sage/xtrem-intacct-finance/IntacctMap')
        .mutations.getIntacctUrl(true, { _id, nodeName })
        .execute();
}
