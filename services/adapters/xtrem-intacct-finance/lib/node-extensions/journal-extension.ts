import type { AnyRecord, Context, integer } from '@sage/xtrem-core';
import { decorators, Logger, NodeExtension } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacct from '@sage/xtrem-intacct';

const logger = Logger.getLogger(__filename, 'intacct-journal-extension');
/**
 *  Intacct object : JOURNAL
 */
@decorators.nodeExtension<JournalExtension>({
    extends: () => xtremFinanceData.nodes.Journal,
})
export class JournalExtension extends NodeExtension<xtremFinanceData.nodes.Journal> {
    /**
     * Corresponding to intacct SYMBOL, xtrem id
     */
    @decorators.stringProperty<JournalExtension, 'uIntacctId'>({
        isStored: true,
        isPublished: false,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly uIntacctId: Promise<string>;

    /**
     * TODO refactor after enhancement request  XT-12385
     */
    @decorators.stringProperty<JournalExtension, 'intacctId'>({
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
        dependsOn: ['uIntacctId'],
        async getValue() {
            return (await this.uIntacctId) !== '' ? this.uIntacctId : this.id;
        },
        async setValue(value) {
            await this.$.set({ uIntacctId: value });
        },
    })
    readonly intacctId: Promise<string>;

    /**
     * Corresponding to intacct RECORDNO
     */
    @decorators.integerProperty<JournalExtension, 'recordNo'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly recordNo: Promise<integer | null>;

    /**
     *  Linked to intacct STATUS, xtrem isActive
     */
    @decorators.stringProperty<JournalExtension, 'statusIntacct'>({
        isPublished: true,
        async getValue() {
            return (await this.isActive) ? 'active' : 'inactive';
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly statusIntacct: Promise<string>;

    /**
     *  Initialisator for mapping Payload
     * @param payload
     * @returns
     */
    static async initPayload(context: Context, payload: any): Promise<AnyRecord> {
        logger.debug(() => ` Payload to save ${JSON.stringify(payload)}`);

        payload.isActive = payload.statusIntacct === 'active';

        const intacctConfiguration = await xtremIntacct.nodes.Intacct.defaultInstance(context);

        payload.legislation = await intacctConfiguration?.legislation;

        delete payload.statusIntacct;

        return payload;
    }
}

declare module '@sage/xtrem-finance-data/lib/nodes/journal' {
    export interface Journal extends JournalExtension {}
}
