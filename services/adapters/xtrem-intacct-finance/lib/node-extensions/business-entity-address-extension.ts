import type { Reference } from '@sage/xtrem-core';
import { decorators, NodeStatus, SubNodeExtension1 } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremIntacctFinance from '..';
import { deleteIntacct, launchSynchronization } from '../functions/synchronization';
import { synchronizeCustomerSupplier } from '../functions/synchronization/business-entity';

@decorators.subNodeExtension1<BusinessEntityAddressExtension>({
    extends: () => xtremMasterData.nodes.BusinessEntityAddress,
    async deleteEnd() {
        if (await xtremIntacct.functions.isIntacctActive(this.$.context)) {
            await deleteIntacct(this.$.context, {
                intacctNode: 'CONTACT',
                recordNo: (await (await this.intacctBusinessEntityAddress)?.recordNo) || undefined,
            });
        }
    },
    async saveBegin() {
        if (!(await this.intacctBusinessEntityAddress)) {
            const businessEntity = await this.businessEntity;
            if (businessEntity.$.isValueDeferred('id')) {
                await businessEntity.$.context.flushDeferredActions();
            }
            await this.$.set({ intacctBusinessEntityAddress: {} });
        }

        if (!(await xtremIntacct.functions.isIntacctActive(this.$.context))) {
            this.skipCallApi = true;
        }
    },

    async saveEnd() {
        const isSynchronized = await launchSynchronization(this as xtremMasterData.nodes.BusinessEntityAddress);
        if (isSynchronized && ((await this.isPrimary) || this.$.status === NodeStatus.added)) {
            await synchronizeCustomerSupplier(await this.businessEntity);
        }
    },
})
export class BusinessEntityAddressExtension
    extends SubNodeExtension1<xtremMasterData.nodes.BusinessEntityAddress>
    implements xtremSynchronization.interfaces.SynchronizationNode
{
    skipCallApi: boolean;

    @decorators.referenceProperty<BusinessEntityAddressExtension, 'intacctBusinessEntityAddress'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'contact',
        node: () => xtremIntacctFinance.nodes.IntacctContact,
        isNullable: true,
        excludedFromPayload: true,
        lookupAccess: true,
    })
    readonly intacctBusinessEntityAddress: Reference<xtremIntacctFinance.nodes.IntacctContact | null>;

    getSyncStateReference() {
        return this.intacctBusinessEntityAddress;
    }
}
declare module '@sage/xtrem-master-data/lib/nodes/business-entity-address' {
    export interface BusinessEntityAddress extends BusinessEntityAddressExtension {}
}
