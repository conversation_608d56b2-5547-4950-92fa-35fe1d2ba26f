import type { Reference } from '@sage/xtrem-core';
import { decorators, SubNodeExtension1 } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremIntacctFinance from '..';

/** *  Intacct object : APBILLITEM */
@decorators.subNodeExtension1<AccountsPayableInvoiceLineExtension>({
    extends: () => xtremFinance.nodes.AccountsPayableInvoiceLine,
    async saveBegin() {
        if (!(await this.intacctDocumentLine)) {
            await this.$.set({ intacctDocumentLine: {} });
        }
    },
})
export class AccountsPayableInvoiceLineExtension
    extends SubNodeExtension1<xtremFinance.nodes.AccountsPayableInvoiceLine>
    implements xtremSynchronization.interfaces.SynchronizationNodeLine
{
    getSyncStateLine() {
        return this.intacctDocumentLine;
    }

    @decorators.referenceProperty<AccountsPayableInvoiceLineExtension, 'intacctDocumentLine'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremIntacctFinance.nodes.IntacctAccountsPayableInvoiceLine,
        isNullable: true,
    })
    readonly intacctDocumentLine: Reference<xtremIntacctFinance.nodes.IntacctAccountsPayableInvoiceLine | null>;
}

declare module '@sage/xtrem-finance/lib/nodes/accounts-payable-invoice-line' {
    export interface AccountsPayableInvoiceLine extends AccountsPayableInvoiceLineExtension {}
}
