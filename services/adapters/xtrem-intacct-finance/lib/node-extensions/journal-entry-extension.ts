import type { Reference } from '@sage/xtrem-core';
import { decorators, Logger, NodeExtension, NodeStatus } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremIntacctFinance from '..';

const logger = Logger.getLogger(__filename, 'journal-entry');

/**  *  Intacct object : JOURNAL */
@decorators.nodeExtension<JournalEntryExtension>({
    extends: () => xtremFinance.nodes.JournalEntry,
    async controlDelete(cx) {
        await xtremIntacctFinance.functions.finance.controlBeginAndDelete(cx, this, logger);
    },
    async controlBegin(cx) {
        if (this.$.status !== NodeStatus.added) {
            await xtremIntacctFinance.functions.finance.controlBeginAndDelete(cx, this, logger);
        }
    },
    async saveBegin() {
        await xtremIntacctFinance.functions.finance.saveBegin(this);
    },
    async saveEnd() {
        await xtremIntacctFinance.functions.finance.saveEnd(this as xtremFinance.nodes.JournalEntry);
    },
})
export class JournalEntryExtension
    extends NodeExtension<xtremFinance.nodes.JournalEntry>
    implements xtremSynchronization.interfaces.SynchronizationNode
{
    skipCallApi: boolean;

    getSyncStateReference() {
        return this.intacctDocument;
    }

    @decorators.referenceProperty<JournalEntryExtension, 'intacctDocument'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'document',
        node: () => xtremIntacctFinance.nodes.IntacctJournalEntry,
        isNullable: true,
        excludedFromPayload: true,
    })
    readonly intacctDocument: Reference<xtremIntacctFinance.nodes.IntacctJournalEntry | null>;

    // To be deleted after pr

    // @decorators.stringProperty<JournalEntryExtension, 'intacctId'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    // })
    // readonly intacctId: Promise<string>;

    // @decorators.integerProperty<JournalEntryExtension, 'recordNo'>({
    //     isStored: true,
    //     isPublished: true,
    //     isNullable: true,
    // })
    // readonly recordNo: Promise<integer | null>;
}

declare module '@sage/xtrem-finance/lib/nodes/journal-entry' {
    export interface JournalEntry extends JournalEntryExtension {}
}
