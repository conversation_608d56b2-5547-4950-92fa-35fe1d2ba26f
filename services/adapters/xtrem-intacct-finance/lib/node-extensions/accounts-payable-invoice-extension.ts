import type { Reference } from '@sage/xtrem-core';
import { decorators, Logger, NodeExtension, NodeStatus } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremIntacctFinance from '..';

const logger = Logger.getLogger(__filename, 'ap-invoice');

/** *  Intacct object : APBILL */
@decorators.nodeExtension<AccountsPayableInvoiceExtension>({
    extends: () => xtremFinance.nodes.AccountsPayableInvoice,
    async controlDelete(cx) {
        await xtremIntacctFinance.functions.finance.controlBeginAndDelete(cx, this, logger);
    },
    async controlBegin(cx) {
        if (this.$.status !== NodeStatus.added) {
            await xtremIntacctFinance.functions.finance.controlBeginAndDelete(cx, this, logger);
        }
    },
    async saveBegin() {
        await xtremIntacctFinance.functions.finance.saveBegin(this);
    },
    async saveEnd() {
        await xtremIntacctFinance.functions.finance.saveEnd(this as xtremFinance.nodes.AccountsPayableInvoice);
    },
})
export class AccountsPayableInvoiceExtension
    extends NodeExtension<xtremFinance.nodes.AccountsPayableInvoice>
    implements xtremSynchronization.interfaces.SynchronizationNode
{
    skipCallApi: boolean;

    getSyncStateReference() {
        return this.intacctDocument;
    }

    @decorators.referenceProperty<AccountsPayableInvoiceExtension, 'intacctDocument'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'document',
        node: () => xtremIntacctFinance.nodes.IntacctAccountsPayableInvoice,
        isNullable: true,
        excludedFromPayload: true,
    })
    readonly intacctDocument: Reference<xtremIntacctFinance.nodes.IntacctAccountsPayableInvoice | null>;

    /** Next properties must be deleted  */

    // @decorators.stringProperty<AccountsPayableInvoiceExtension, 'intacctId'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    // })
    // readonly intacctId: Promise<string>;

    // @decorators.integerProperty<AccountsPayableInvoiceExtension, 'recordNo'>({
    //     isStored: true,
    //     isPublished: true,
    //     isNullable: true,
    // })
    // readonly recordNo: Promise<integer | null>;

    // @decorators.enumProperty<AccountsPayableInvoiceExtension, 'intacctIntegrationState'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremCommunication.enums.IntegrationStateDataType,
    //     defaultValue: () => 'not',
    // })
    // readonly intacctIntegrationState: Promise<xtremCommunication.enums.IntegrationState>;

    // @decorators.datetimeProperty<AccountsPayableInvoiceExtension, 'intacctLastIntegrationDate'>({
    //     isStored: true,
    //     isPublished: true,
    //     isNullable: true,
    //     defaultValue: null,
    // })
    // readonly intacctLastIntegrationDate: Promise<datetime | null>;

    // @decorators.stringProperty<AccountsPayableInvoiceExtension, 'intacctUrl'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremIntacctFinance.dataTypes.intacctUrl,
    // })
    // readonly intacctUrl: Promise<string>;
}

declare module '@sage/xtrem-finance/lib/nodes/accounts-payable-invoice' {
    export interface AccountsPayableInvoice extends AccountsPayableInvoiceExtension {}
}
