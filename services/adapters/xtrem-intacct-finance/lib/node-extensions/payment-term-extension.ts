import type { AnyRecord, Context, integer } from '@sage/xtrem-core';
import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';

/** intacct value from APTERM/ARTERM DUEFROM property */
const FROM_INVOICE_OR_BILL_DATE = 'from invoice/bill date';
const AFTER_END_OF_MONTH_OF_INVOICE_OR_BILL_DATE = 'after end of month of invoice/bill date';
const FROM_INVOICE_OR_BILL_DATE_EXTENDING_TO_EOM = 'from invoice/bill date extending to eom';
// can also be  "of the month of invoice/bill date","of next month from invoice/bill date","of 2nd month from invoice/bill date",
// "of 3rd month from invoice/bill date", "of 4th month from invoice/bill date","of 5th month from invoice/bill date",
// "of 6th month from invoice/bill date","after end of month of invoice/bill date","from invoice/bill date extending to eom"

@decorators.nodeExtension<PaymentTermExtension>({
    extends: () => xtremMasterData.nodes.PaymentTerm,
})
export class PaymentTermExtension extends NodeExtension<xtremMasterData.nodes.PaymentTerm> {
    /**
     * Corresponding to intacct NAME
     */
    @decorators.stringProperty<PaymentTermExtension, 'uIntacctId'>({
        isStored: true,
        isPublished: false,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly uIntacctId: Promise<string>;

    @decorators.stringProperty<PaymentTermExtension, 'intacctId'>({
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
        dependsOn: ['uIntacctId'],
        async getValue() {
            return (await this.uIntacctId) !== '' ? this.uIntacctId : this.name;
        },
        async setValue(value) {
            await this.$.set({ uIntacctId: value });
        },
    })
    readonly intacctId: Promise<string>;

    /**
     * Corresponding to intacct RECORDNO
     */
    @decorators.integerProperty<PaymentTermExtension, 'recordNo'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly recordNo: Promise<integer | null>;

    /**
     *  Linked to intacct STATUS
     */
    @decorators.stringProperty<PaymentTermExtension, 'statusIntacct'>({
        isPublished: true,
        async getValue() {
            return (await this.isActive) ? 'active' : 'inactive';
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly statusIntacct: Promise<string>;

    /**
     *  Linked to intacct DUEFORM
     */
    @decorators.stringProperty<PaymentTermExtension, 'dueFromIntacct'>({
        isPublished: true,
        async computeValue() {
            switch (await this.dueDateType) {
                case 'afterInvoiceDate':
                    return FROM_INVOICE_OR_BILL_DATE;
                case 'afterTheEndOfTheMonthOfInvoiceDate':
                    return AFTER_END_OF_MONTH_OF_INVOICE_OR_BILL_DATE;
                case 'afterInvoiceDateAndExtendedToEndOfMonth':
                    return FROM_INVOICE_OR_BILL_DATE_EXTENDING_TO_EOM;
                default:
                    return FROM_INVOICE_OR_BILL_DATE;
            }
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly dueFromIntacct: Promise<string>;

    /**
     *  Linked to intacct DISCOUNTFROM
     */
    @decorators.stringProperty<PaymentTermExtension, 'discountFromIntacct'>({
        isPublished: true,
        async computeValue() {
            switch (await this.discountFrom) {
                case 'afterInvoiceDate':
                    return FROM_INVOICE_OR_BILL_DATE;
                case 'afterTheEndOfTheMonthOfInvoiceDate':
                    return AFTER_END_OF_MONTH_OF_INVOICE_OR_BILL_DATE;
                case 'afterInvoiceDateAndExtendedToEndOfMonth':
                    return FROM_INVOICE_OR_BILL_DATE_EXTENDING_TO_EOM;
                default:
                    return FROM_INVOICE_OR_BILL_DATE;
            }
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly discountFromIntacct: Promise<string>;

    /**
     *  Linked to intacct DISCOUNTTYPE
     */
    @decorators.stringProperty<PaymentTermExtension, 'discountTypeIntacct'>({
        isPublished: true,
        async computeValue() {
            switch (await this.discountType) {
                case 'amount':
                    return '$';
                case 'percentage':
                    return '%';
                default:
                    return '$';
            }
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly discountTypeIntacct: Promise<string>;

    /**
     *  Linked to intacct penaltyType
     */
    @decorators.stringProperty<PaymentTermExtension, 'penaltyTypeIntacct'>({
        isPublished: true,
        async computeValue() {
            switch (await this.penaltyType) {
                case 'amount':
                    return '$';
                case 'percentage':
                    return '%';
                default:
                    return '$';
            }
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly penaltyTypeIntacct: Promise<string>;

    /**
     * is this Generated from intacct
     */
    @decorators.booleanProperty<PaymentTermExtension, 'isIntacct'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: false,
    })
    readonly isIntacct: Promise<boolean>;

    /**
     *  Initialisator for mapping Payload
     * Returning Clean payload for create/update
     * @param payload
     * @returns
     */
    static initPayload(context: Context, payload: any): AnyRecord {
        payload.isActive = payload.statusIntacct === 'active';
        payload.isIntacct = true;

        payload.businessEntityType = payload.intacctName === 'APTERM' ? 'supplier' : 'customer';

        if (payload.discountFromIntacct) {
            switch (payload.discountFromIntacct) {
                case FROM_INVOICE_OR_BILL_DATE:
                    payload.discountFrom = 'afterInvoiceDate';
                    break;
                case AFTER_END_OF_MONTH_OF_INVOICE_OR_BILL_DATE:
                    payload.discountFrom = 'afterTheEndOfTheMonthOfInvoiceDate';
                    break;
                case FROM_INVOICE_OR_BILL_DATE_EXTENDING_TO_EOM:
                    payload.discountFrom = 'afterInvoiceDateAndExtendedToEndOfMonth';
                    break;
                default:
                    payload.discountFrom = null;
                    break;
            }
        }

        if (payload.discountTypeIntacct) {
            payload.discountType = payload.discountTypeIntacct === '$' ? 'amount' : 'percentage';
        }

        if (payload.dueFromIntacct) {
            switch (payload.dueFromIntacct) {
                case FROM_INVOICE_OR_BILL_DATE:
                    payload.dueDateType = 'afterInvoiceDate';
                    break;
                case AFTER_END_OF_MONTH_OF_INVOICE_OR_BILL_DATE:
                    payload.dueDateType = 'afterTheEndOfTheMonthOfInvoiceDate';
                    break;
                case FROM_INVOICE_OR_BILL_DATE_EXTENDING_TO_EOM:
                    payload.dueDateType = 'afterInvoiceDateAndExtendedToEndOfMonth';
                    break;
                default:
                    payload.dueDateType = 'afterInvoiceDate';
                    break;
            }
        }

        if (payload.penaltyTypeIntacct) {
            payload.penaltyType = payload.penaltyTypeIntacct === '$' ? 'amount' : 'percentage';
        }

        delete payload.dueFromIntacct;
        delete payload.discountFromIntacct;
        delete payload.discountTypeIntacct;
        delete payload.penaltyTypeIntacct;

        delete payload.intacctName;
        delete payload.statusIntacct;

        context.logger.debug(() => ` Payload to save ${JSON.stringify(payload)}`);

        return payload;
    }
}

declare module '@sage/xtrem-master-data/lib/nodes/payment-term' {
    export interface PaymentTerm extends PaymentTermExtension {}
}
