import type { AnyRecord, Context, integer } from '@sage/xtrem-core';
import { decorators, Logger, NodeExtension } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremIntacctFinance from '..';

const logger = Logger.getLogger(__filename, 'intacct-attribute-extension');
@decorators.nodeExtension<AttributeExtension>({
    extends: () => xtremFinanceData.nodes.Attribute,
})
export class AttributeExtension extends NodeExtension<xtremFinanceData.nodes.Attribute> {
    /**
     * Corresponding to intacct PROJECTID OR TASKID OR EMPLOYEEID
     */
    @decorators.stringProperty<AttributeExtension, 'uIntacctId'>({
        isStored: true,
        isPublished: false,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly uIntacctId: Promise<string>;

    /**
     * TODO refactor after enhancement request  XT-12385
     */
    @decorators.stringProperty<AttributeExtension, 'intacctId'>({
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
        dependsOn: ['recordNo'],
        async getValue() {
            return `${await this.recordNo}`;
        },
        async setValue(value) {
            await this.$.set({ recordNo: !Number.isNaN(Number(value)) ? Number(value) : null });
        },
    })
    readonly intacctId: Promise<string>;

    /**
     * Corresponding to intacct RECORDNO
     */
    @decorators.integerProperty<AttributeExtension, 'recordNo'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly recordNo: Promise<integer | null>;

    /**
     *  Linked to intacct STATUS
     */
    @decorators.stringProperty<AttributeExtension, 'statusIntacct'>({
        isPublished: true,
        async getValue() {
            return (await this.isActive) ? 'active' : 'inactive';
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly statusIntacct: Promise<string>;

    @decorators.stringProperty<AttributeExtension, 'intacctProject'>({
        isPublished: true,
        async getValue() {
            return (await this.attributeRestrictedTo)?.id ?? '';
        },
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly intacctProject: Promise<string>;

    /**
     *  Initialisator for mapping Payload
     * @param payload
     * @returns
     */
    static async initPayload(context: Context, payload: any): Promise<AnyRecord> {
        logger.debug(() => ` Payload to save ${JSON.stringify(payload)}`);
        if (payload.recordNo) {
            payload.intacctId = `${payload.recordNo}`;
        }
        payload.isActive = payload.statusIntacct === 'active';

        let attributeTypeKey = '';
        if (payload.employeeRecordNo) {
            attributeTypeKey = '#employee';
        }
        if (payload.projectRecordNo) {
            attributeTypeKey = '#project';
        }
        if (payload.taskRecordNo) {
            attributeTypeKey = '#task';
            payload.isActive = true; // force to 'active' for task
            if (payload.intacctProject) {
                payload.attributeRestrictedTo = `${payload.intacctProject}|project|`;
            }
        }

        if (
            (await context.queryCount(xtremFinanceData.nodes.Attribute, {
                filter: {
                    recordNo: payload.intacctId,
                },
            })) === 0
        ) {
            payload.attributeType = await (
                await context.read(xtremFinanceData.nodes.AttributeType, { _id: attributeTypeKey })
            ).$.payload({
                withIds: true,
            });
        } else {
            delete payload.attributeType;
        }

        delete payload.employeeRecordNo;
        delete payload.projectRecordNo;
        delete payload.taskRecordNo;
        delete payload.statusIntacct;
        delete payload.intacctProject;

        return payload;
    }

    /**
     * This bulkMutation is called from the scheduler for the upgrade from V41/V42 to V43 of SDMO on the first Saturday after the upgrade.
     * The RECORDNO on an "employee" attribute was originally set to the RECORDNO of the contact of the employee instead of the RECORDNO
     * of the employee itself. From V43 we no longer use the EMPLOYEEID for synchronization, but the RECORDNO and therefore we need the correct one.
     * The mutation picks up every "employee" attribute with a non empty uIntacctId and sets the correct RECORDNO from Intacct on it. The uIntacctId
     * is then set to empty, so that the record is not picked up again when the mutation is triggered a second time.
     * @returns true when Intacct integration active, else false
     */
    @decorators.bulkMutation<typeof AttributeExtension, 'updateRecordNoOnEmployee'>({ isPublished: true })
    static async updateRecordNoOnEmployee(context: Context, employee: xtremFinanceData.nodes.Attribute) {
        if (await xtremIntacct.functions.isIntacctActive(context)) {
            const recordNo = await xtremIntacctFinance.functions.getIntacctEmployeeRecordNo(context, await employee.id);

            await employee.$.set({ recordNo, uIntacctId: '' });
            if (!(await employee.$.trySave())) {
                await context.batch.logMessage('error', context.diagnoses.map(diag => diag.message).join('\n'), {
                    data: context.diagnoses,
                });
            } else {
                await context.batch.logMessage(
                    'info',
                    `Update recordNo for employee ${await employee.name} to ${await employee.recordNo}`,
                );
            }
        } else {
            await context.batch.logMessage('warning', 'Intacct is disabled');
        }
    }
}

declare module '@sage/xtrem-finance-data/lib/nodes/attribute' {
    export interface Attribute extends AttributeExtension {}
}
