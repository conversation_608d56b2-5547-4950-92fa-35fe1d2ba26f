import type { Context, Reference } from '@sage/xtrem-core';
import { decorators, Logger, NodeExtension, NodeStatus, Uuid } from '@sage/xtrem-core';
import { DateValue } from '@sage/xtrem-date-time';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremIntacctFinance from '..';

const logger = Logger.getLogger(__filename, 'ar-payment');

/**
 *  Intacct object : ARPYMT
 */

@decorators.nodeExtension<AccountsReceivablePaymentExtension>({
    extends: () => xtremFinance.nodes.AccountsReceivablePayment,
    async controlDelete(cx) {
        await xtremIntacctFinance.functions.finance.controlBeginAndDelete(cx, this, logger);
    },
    async controlBegin(cx) {
        if (this.$.status !== NodeStatus.added) {
            await xtremIntacctFinance.functions.finance.controlBeginAndDelete(cx, this, logger);
        }
    },
    async saveBegin() {
        await xtremIntacctFinance.functions.finance.saveBegin(this);
    },
    async saveEnd() {
        await xtremIntacctFinance.functions.finance.saveEnd(this as xtremFinance.nodes.AccountsReceivablePayment);
    },
})
export class AccountsReceivablePaymentExtension
    extends NodeExtension<xtremFinance.nodes.AccountsReceivablePayment>
    implements xtremSynchronization.interfaces.SynchronizationNode
{
    getSyncStateReference() {
        return this.intacctDocument;
    }

    skipCallApi: boolean;

    @decorators.referenceProperty<AccountsReceivablePaymentExtension, 'intacctDocument'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'document',
        node: () => xtremIntacctFinance.nodes.IntacctAccountsReceivablePayment,
        isNullable: true,
        excludedFromPayload: true,
    })
    readonly intacctDocument: Reference<xtremIntacctFinance.nodes.IntacctAccountsReceivablePayment | null>;

    // @decorators.stringProperty<AccountsReceivablePaymentExtension, 'intacctId'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    // })
    // readonly intacctId: Promise<string>;

    // /**
    //  * Corresponding to intacct RECORDNO
    //  */
    // @decorators.integerProperty<AccountsReceivablePaymentExtension, 'recordNo'>({
    //     isStored: true,
    //     isPublished: true,
    //     isNullable: true,
    // })
    // readonly recordNo: Promise<integer | null>;

    // /**
    //  * the integration status
    //  */
    // @decorators.enumProperty<AccountsReceivablePaymentExtension, 'intacctIntegrationState'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremCommunication.enums.IntegrationStateDataType,
    //     defaultValue: () => 'not',
    // })
    // readonly intacctIntegrationState: Promise<xtremCommunication.enums.IntegrationState>;

    // /**
    //  * intacct last integration date time
    //  */
    // @decorators.datetimeProperty<AccountsReceivablePaymentExtension, 'intacctLastIntegrationDate'>({
    //     isStored: true,
    //     isPublished: true,
    //     isNullable: true,
    //     defaultValue: null,
    // })
    // readonly intacctLastIntegrationDate: Promise<datetime | null>;

    // /**
    //  * Corresponding to intacct RECORD_URL
    //  */
    // @decorators.stringProperty<AccountsReceivablePaymentExtension, 'intacctUrl'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremIntacctFinance.dataTypes.intacctUrl,
    // })
    // readonly intacctUrl: Promise<string>;

    @decorators.mutation<typeof AccountsReceivablePaymentExtension, 'createArPayment'>({
        isPublished: true,
        parameters: [
            {
                isMandatory: true,
                name: 'data',
                type: 'object',
                properties: {
                    bankAccount: {
                        isNullable: false,
                        type: 'reference',
                        node: () => xtremFinanceData.nodes.BankAccount,
                    },
                    financialSite: {
                        isNullable: false,
                        type: 'reference',
                        node: () => xtremSystem.nodes.Site,
                    },
                    payToCustomerId: {
                        type: 'string',
                        isMandatory: true,
                    },
                    payToCustomerName: {
                        type: 'string',
                        isMandatory: true,
                    },
                    description: {
                        type: 'string',
                    },
                    paymentMethod: {
                        type: 'string',
                        isMandatory: true,
                    },
                    postingDate: {
                        type: 'string',
                        isMandatory: true,
                    },
                    paymentDate: {
                        type: 'string',
                        isMandatory: true,
                    },
                    currency: {
                        isNullable: false,
                        type: 'reference',
                        node: () => xtremMasterData.nodes.Currency,
                    },
                    companyFxRate: {
                        type: 'decimal',
                        isMandatory: true,
                    },
                    companyFxRateDivisor: {
                        type: 'decimal',
                        isMandatory: true,
                    },
                    fxRateDate: {
                        type: 'string',
                        isMandatory: true,
                    },
                    paymentAmount: {
                        type: 'decimal',
                        isMandatory: true,
                    },
                    paymentCompanyAmount: {
                        type: 'decimal',
                        isMandatory: true,
                    },
                    bankFeed: {
                        type: 'integer',
                        isMandatory: false,
                    },
                    arMatch: {
                        type: 'object',
                        isMandatory: true,
                        properties: {
                            isArMatch: {
                                type: 'boolean',
                            },
                            customerId: { type: 'string' },
                            matchingReasons: { type: 'string' },
                            arPaymentType: { type: 'string' },
                        },
                    },
                    jsonArInvoices: {
                        type: 'object',
                        isMandatory: true,
                        properties: {
                            matchedArInvoices: {
                                type: 'array',
                                item: {
                                    type: 'object',
                                    properties: {
                                        recordNo: { type: 'integer' },
                                        arInvoiceAmountMatched: { type: 'decimal' },
                                    },
                                },
                            },
                        },
                    },
                    lines: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                type: {
                                    type: 'enum',
                                    dataType: () => xtremFinanceData.enums.financeDocumentTypeDataType,
                                    isMandatory: true,
                                },
                                arInvoiceRecordNo: {
                                    isNullable: false,
                                    type: 'integer',
                                },
                                financialSite: {
                                    type: 'reference',
                                    node: () => xtremSystem.nodes.Site,
                                    isNullable: false,
                                },
                                currency: {
                                    type: 'reference',
                                    node: () => xtremMasterData.nodes.Currency,
                                    isNullable: false,
                                },
                                paymentAmount: {
                                    type: 'decimal',
                                    isMandatory: true,
                                },
                                paymentCompanyAmount: {
                                    type: 'decimal',
                                    isMandatory: true,
                                },
                            },
                        },
                    },
                },
            },
        ],
        return: {
            type: 'reference',
            node: () => xtremFinance.nodes.AccountsReceivablePayment,
        },
    })
    static async createArPayment(
        context: Context,
        data: xtremIntacctFinance.sharedFunctions.interfaces.ArPaymentCreateData,
    ): Promise<xtremFinance.nodes.AccountsReceivablePayment> {
        const arPayment = await context.create(xtremFinance.nodes.AccountsReceivablePayment, {
            bankAccount: data.bankAccount,
            financialSite: data.financialSite,
            payToCustomerId: data.payToCustomerId,
            payToCustomerName: data.payToCustomerName,
            description: data.description,
            intacctDocument: { paymentMethod: data.paymentMethod, bankFeed: data.bankFeed },
            postingDate: DateValue.parse(data.postingDate),
            paymentDate: DateValue.parse(data.paymentDate),
            currency: data.currency,
            companyFxRate: data.companyFxRate,
            companyFxRateDivisor: data.companyFxRateDivisor,
            fxRateDate: DateValue.parse(data.fxRateDate),
            paymentAmount: data.paymentCompanyAmount,
            paymentCompanyAmount: data.paymentCompanyAmount,

            lines: data.lines,
        });
        await arPayment.$.save();

        const bankFeed = await context.read(
            xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed,
            { _id: data.bankFeed },
            { forUpdate: true },
        );

        await bankFeed.$.set({
            payToCustomerId: data.payToCustomerId,
            payToCustomerName: data.payToCustomerName,
            paymentMethod: data.paymentMethod,
            paymentDate: DateValue.parse(data.paymentDate),
            receiptDate: DateValue.parse(data.postingDate),
            arMatch: data.arMatch,
            jsonArInvoices: data.jsonArInvoices,
            lines: [],
        });
        await bankFeed.$.save();

        const intacctArAdvance = await arPayment.intacctDocument;
        await (
            await context.create(xtremFinanceData.nodes.FinanceTransaction, {
                batchId: Uuid.generate().toString(),
                documentSysId: (await intacctArAdvance?.bankFeed)?._id,
                documentNumber: (await intacctArAdvance?.bankFeed)?._id.toString(),
                documentType: 'bankReconciliationDeposit',
                targetDocumentNumber: await arPayment.number,
                targetDocumentSysId: arPayment._id,
                targetDocumentType: 'accountsReceivablePayment',
                status: 'recorded',
                message: '',
                financialSite: data.financialSite,
            })
        ).$.save();

        await xtremFinance.nodes.AccountsReceivablePayment.post(
            context,
            await context.read(
                xtremFinance.nodes.AccountsReceivablePayment,
                { _id: arPayment._id },
                { forUpdate: true },
            ),
        );
        return context.read(xtremFinance.nodes.AccountsReceivablePayment, { _id: arPayment._id });
    }
}

declare module '@sage/xtrem-finance/lib/nodes/accounts-receivable-payment' {
    export interface AccountsReceivablePayment extends AccountsReceivablePaymentExtension {}
}
