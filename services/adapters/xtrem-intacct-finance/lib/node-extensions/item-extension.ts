import type { Reference } from '@sage/xtrem-core';
import { decorators, NodeExtension, NodeStatus } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremIntacctFinance from '..';
import { deleteIntacct, launchSynchronization } from '../functions/synchronization';

@decorators.nodeExtension<ItemExtension>({
    extends: () => xtremMasterData.nodes.Item,
    async deleteEnd() {
        if (await xtremIntacct.functions.isIntacctActive(this.$.context)) {
            await deleteIntacct(this.$.context, {
                intacctNode: 'ITEM',
                recordNo: (await (await this.intacctItem)?.recordNo) || undefined,
            });
        }
    },
    async saveBegin() {
        if (!(await this.intacctItem)) {
            if (this.$.isValueDeferred('id')) {
                await this.$.context.flushDeferredActions();
            }
            await this.$.set({ intacctItem: {} });
        }

        if (!(await xtremIntacct.functions.isIntacctActive(this.$.context))) {
            this.skipCallApi = true;
        }
    },
    async saveEnd() {
        await launchSynchronization(this as xtremMasterData.nodes.Item);
    },
    async controlBegin(cx) {
        if (await xtremIntacct.functions.isIntacctActive(this.$.context)) {
            return;
        }
        if (this.$.status !== NodeStatus.added) {
            const oldItem = await this.$.old;
            if ((await this.type) !== (await oldItem.type)) {
                cx.warn.add('Type is frozen on intacct ');
            }
        }
    },
})
export class ItemExtension
    extends NodeExtension<xtremMasterData.nodes.Item>
    implements xtremSynchronization.interfaces.SynchronizationNode
{
    skipCallApi: boolean;

    @decorators.referenceProperty<ItemExtension, 'intacctItem'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'item',
        node: () => xtremIntacctFinance.nodes.IntacctItem,
        isNullable: true,
        duplicatedValue: null,
    })
    readonly intacctItem: Reference<xtremIntacctFinance.nodes.IntacctItem | null>;

    getSyncStateReference() {
        return this.intacctItem;
    }
}
declare module '@sage/xtrem-master-data/lib/nodes/item' {
    export interface Item extends ItemExtension {}
}
