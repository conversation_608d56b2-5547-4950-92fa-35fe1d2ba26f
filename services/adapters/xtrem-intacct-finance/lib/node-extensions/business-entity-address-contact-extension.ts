import { decorators, NodeStatus, SubNodeExtension1 } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { synchronize } from '../functions/synchronization';

@decorators.subNodeExtension1<BusinessEntityContactExtension>({
    extends: () => xtremMasterData.nodes.BusinessEntityContact,
    async saveEnd() {
        if (this.skipCallIntacctApi) {
            return;
        }
        const address = await this.address;
        const intacctBusinessEntityAddress = await address.intacctBusinessEntityAddress;
        if (this.$.status === NodeStatus.modified && intacctBusinessEntityAddress) {
            await synchronize(address, intacctBusinessEntityAddress);
        }
    },
})
export class BusinessEntityContactExtension extends SubNodeExtension1<xtremMasterData.nodes.BusinessEntityContact> {
    skipCallIntacctApi: boolean;

    /**     *  Linked to PRINTAS     */
    @decorators.stringProperty<BusinessEntityContactExtension, 'intacctPrintAs'>({
        isPublished: true,
        async getValue() {
            return `${await this.firstName} ${await this.lastName}`;
        },
    })
    readonly intacctPrintAs: Promise<string>;
}
declare module '@sage/xtrem-master-data/lib/nodes/business-entity-contact' {
    export interface BusinessEntityContact extends BusinessEntityContactExtension {}
}
