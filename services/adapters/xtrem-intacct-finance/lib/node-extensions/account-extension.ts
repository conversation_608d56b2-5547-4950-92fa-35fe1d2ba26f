import type { AnyRecord, Context, integer } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, Logger, NodeExtension } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacct from '@sage/xtrem-intacct';
import { noIntacctInstanceActive } from '../functions/messages';

const logger = Logger.getLogger(__filename, 'intacct-account-extension');
/**
 *  Intacct object : GLACCOUNT
 */
@decorators.nodeExtension<AccountExtension>({
    extends: () => xtremFinanceData.nodes.Account,
})
export class AccountExtension extends NodeExtension<xtremFinanceData.nodes.Account> {
    /**
     * Corresponding to intacct ACCOUNTNO
     */
    @decorators.stringProperty<AccountExtension, 'uIntacctId'>({
        isStored: true,
        isPublished: false,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly uIntacctId: Promise<string>;

    /**
     * TODO refactor after enhancement request  XT-12385
     */
    @decorators.stringProperty<AccountExtension, 'intacctId'>({
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
        dependsOn: ['uIntacctId'],
        async getValue() {
            return (await this.uIntacctId) !== '' ? this.uIntacctId : this.id;
        },
        async setValue(value) {
            await this.$.set({ uIntacctId: value });
        },
    })
    readonly intacctId: Promise<string>;

    /**
     * Corresponding to intacct RECORDNO
     */
    @decorators.integerProperty<AccountExtension, 'recordNo'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly recordNo: Promise<integer | null>;

    /**
     *  Linked to intacct STATUS
     */
    @decorators.stringProperty<AccountExtension, 'statusIntacct'>({
        isPublished: true,
        async getValue() {
            return (await this.isActive) ? 'active' : 'inactive';
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly statusIntacct: Promise<string>;

    /**
     * REQUIRELOC intacct property
     */
    @decorators.booleanProperty<AccountExtension, 'isRequiredLocation'>({
        isPublished: true,
        getValue() {
            return this.attributeTypes.some(
                async type => (await (await type.attributeType).id) === 'businessSite' && type.isRequired,
            );
        },
    })
    readonly isRequiredLocation: Promise<boolean>;

    /**
     * REQUIREITEM intacct property
     */
    @decorators.booleanProperty<AccountExtension, 'isRequiredItem'>({
        isPublished: true,
        getValue() {
            return this.attributeTypes.some(
                async type => (await (await type.attributeType).id) === 'item' && type.isRequired,
            );
        },
    })
    readonly isRequiredItem: Promise<boolean>;

    /**
     * REQUIRECUSTOMER intacct property
     */
    @decorators.booleanProperty<AccountExtension, 'isRequiredCustomer'>({
        isPublished: true,
        getValue() {
            return this.attributeTypes.some(
                async type => (await (await type.attributeType).id) === 'customer' && type.isRequired,
            );
        },
    })
    readonly isRequiredCustomer: Promise<boolean>;

    /**
     * REQUIREVENDOR intacct property
     */
    @decorators.booleanProperty<AccountExtension, 'isRequiredSupplier'>({
        isPublished: true,
        getValue() {
            return this.attributeTypes.some(
                async type => (await (await type.attributeType).id) === 'supplier' && type.isRequired,
            );
        },
    })
    readonly isRequiredSupplier: Promise<boolean>;

    /**
     * REQUIREPROJECT intacct property
     */
    @decorators.booleanProperty<AccountExtension, 'isRequiredProject'>({
        isPublished: true,
        getValue() {
            return this.attributeTypes.some(
                async type => (await (await type.attributeType).id) === 'project' && type.isRequired,
            );
        },
    })
    readonly isRequiredProject: Promise<boolean>;

    /**
     * REQUIREEMPLOYEE intacct property
     */
    @decorators.booleanProperty<AccountExtension, 'isRequiredEmploye'>({
        isPublished: true,
        getValue() {
            return this.attributeTypes.some(
                async type => (await (await type.attributeType).id) === 'employee' && type.isRequired,
            );
        },
    })
    readonly isRequiredEmploye: Promise<boolean>;

    /**
     * REQUIRETASK intacct property
     */
    @decorators.booleanProperty<AccountExtension, 'isRequiredTask'>({
        isPublished: true,
        getValue() {
            return this.attributeTypes.some(
                async type => (await (await type.attributeType).id) === 'task' && type.isRequired,
            );
        },
    })
    readonly isRequiredTask: Promise<boolean>;

    /**
     * REQUIREDEPT intacct property
     */
    @decorators.booleanProperty<AccountExtension, 'isRequiredDepartement'>({
        isPublished: true,
        getValue() {
            return this.dimensionTypes.some(
                async type => (await (await type.dimensionType).docProperty) === 'dimensionType01' && type.isRequired,
            );
        },
    })
    readonly isRequiredDepartement: Promise<boolean>;

    /**
     * REQUIRECLASS intacct property
     */
    @decorators.booleanProperty<AccountExtension, 'isRequiredClass'>({
        isPublished: true,
        getValue() {
            return this.dimensionTypes.some(
                async type => (await (await type.dimensionType).docProperty) === 'dimensionType02' && type.isRequired,
            );
        },
    })
    readonly isRequiredClass: Promise<boolean>;

    /**
     *  Initialisator for mapping Payload
     * @param payload
     * @returns
     */
    static async initPayload(context: Context, payload: any): Promise<AnyRecord> {
        logger.debug(() => ` Payload to save ${JSON.stringify(payload)}`);

        payload.isActive = payload.statusIntacct === 'active';

        const intacctConfiguration = await xtremIntacct.nodes.Intacct.defaultInstance(context);

        if (!intacctConfiguration) {
            throw new BusinessRuleError(noIntacctInstanceActive(context));
        }

        payload.chartOfAccount = await intacctConfiguration.chartOfAccount;

        delete payload.intacctName;
        delete payload.statusIntacct;

        payload.attributeTypes = [];
        payload.dimensionTypes = [];

        if (payload.isRequiredLocation) {
            payload.attributeTypes.push({
                attributeType: await context.tryRead(xtremFinanceData.nodes.AttributeType, { _id: '#businessSite' }),
                isRequired: true,
            });
        }
        delete payload.isRequiredLocation;

        if (payload.isRequiredItem) {
            payload.attributeTypes.push({
                attributeType: await context.tryRead(xtremFinanceData.nodes.AttributeType, { _id: '#item' }),
                isRequired: true,
            });
        }
        delete payload.isRequiredItem;

        if (payload.isRequiredSupplier) {
            payload.attributeTypes.push({
                attributeType: await context.tryRead(xtremFinanceData.nodes.AttributeType, { _id: '#supplier' }),
                isRequired: true,
            });
        }
        delete payload.isRequiredSupplier;

        if (payload.isRequiredCustomer) {
            payload.attributeTypes.push({
                attributeType: await context.tryRead(xtremFinanceData.nodes.AttributeType, { _id: '#customer' }),
                isRequired: true,
            });
        }
        delete payload.isRequiredCustomer;

        if (payload.isRequiredProject) {
            payload.attributeTypes.push({
                attributeType: await context.tryRead(xtremFinanceData.nodes.AttributeType, { _id: '#project' }),
                isRequired: true,
            });
        }
        delete payload.isRequiredProject;

        if (payload.isRequiredEmploye) {
            payload.attributeTypes.push({
                attributeType: await context.tryRead(xtremFinanceData.nodes.AttributeType, { _id: '#employee' }),
                isRequired: true,
            });
        }
        delete payload.isRequiredEmploye;

        if (payload.isRequiredTask) {
            payload.attributeTypes.push({
                attributeType: await context.tryRead(xtremFinanceData.nodes.AttributeType, { _id: '#task' }),
                isRequired: true,
            });
        }
        delete payload.isRequiredTask;

        if (payload.isRequiredDepartement) {
            payload.dimensionTypes.push({
                dimensionType: await context
                    .query(xtremFinanceData.nodes.DimensionType, {
                        filter: { intacctObject: 'DEPARTMENT' },
                    })
                    .at(0),
                isRequired: true,
            });
        }
        delete payload.isRequiredDepartement;

        if (payload.isRequiredClass) {
            payload.dimensionTypes.push({
                dimensionType: await context
                    .query(xtremFinanceData.nodes.DimensionType, {
                        filter: { intacctObject: 'CLASS' },
                    })
                    .at(0),
                isRequired: true,
            });
        }

        delete payload.isRequiredClass;

        return payload;
    }
}

declare module '@sage/xtrem-finance-data/lib/nodes/account' {
    export interface Account extends AccountExtension {}
}
