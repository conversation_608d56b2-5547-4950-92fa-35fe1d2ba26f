import type { Context, Reference } from '@sage/xtrem-core';
import { asyncArray, decorators, Logger, NodeExtension, NodeStatus, Uuid } from '@sage/xtrem-core';
import { DateValue } from '@sage/xtrem-date-time';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremIntacctFinance from '..';

const logger = Logger.getLogger(__filename, 'ar-advance');

@decorators.nodeExtension<AccountsReceivableAdvanceExtension>({
    extends: () => xtremFinance.nodes.AccountsReceivableAdvance,
    async controlDelete(cx) {
        await xtremIntacctFinance.functions.finance.controlBeginAndDelete(cx, this, logger);
    },
    async controlBegin(cx) {
        if (this.$.status !== NodeStatus.added) {
            await xtremIntacctFinance.functions.finance.controlBeginAndDelete(cx, this, logger);
        }
    },
    async saveBegin() {
        await xtremIntacctFinance.functions.finance.saveBegin(this);
    },
    async saveEnd() {
        await xtremIntacctFinance.functions.finance.saveEnd(this as xtremFinance.nodes.AccountsReceivableAdvance);
    },
})
export class AccountsReceivableAdvanceExtension
    extends NodeExtension<xtremFinance.nodes.AccountsReceivableAdvance>
    implements xtremSynchronization.interfaces.SynchronizationNode
{
    skipCallApi: boolean;

    getSyncStateReference() {
        return this.intacctDocument;
    }

    @decorators.referenceProperty<AccountsReceivableAdvanceExtension, 'intacctDocument'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'document',
        node: () => xtremIntacctFinance.nodes.IntacctAccountsReceivableAdvance,
        isNullable: true,
        excludedFromPayload: true,
    })
    readonly intacctDocument: Reference<xtremIntacctFinance.nodes.IntacctAccountsReceivableAdvance | null>;

    /** new properties to be deleted  */
    // @decorators.stringProperty<AccountsReceivableAdvanceExtension, 'intacctId'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    // })
    // readonly intacctId: Promise<string>;

    // @decorators.integerProperty<AccountsReceivableAdvanceExtension, 'recordNo'>({
    //     isStored: true,
    //     isPublished: true,
    //     isNullable: true,
    // })
    // readonly recordNo: Promise<integer | null>;

    // @decorators.enumProperty<AccountsReceivableAdvanceExtension, 'intacctIntegrationState'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremCommunication.enums.IntegrationStateDataType,
    //     defaultValue: () => 'not',
    // })
    // readonly intacctIntegrationState: Promise<xtremCommunication.enums.IntegrationState>;

    // @decorators.datetimeProperty<AccountsReceivableAdvanceExtension, 'intacctLastIntegrationDate'>({
    //     isStored: true,
    //     isPublished: true,
    //     isNullable: true,
    //     defaultValue: null,
    // })
    // readonly intacctLastIntegrationDate: Promise<datetime | null>;

    // @decorators.stringProperty<AccountsReceivableAdvanceExtension, 'intacctUrl'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremIntacctFinance.dataTypes.intacctUrl,
    // })
    // readonly intacctUrl: Promise<string>;

    @decorators.mutation<typeof AccountsReceivableAdvanceExtension, 'createArAdvance'>({
        isPublished: true,
        parameters: [
            {
                isMandatory: true,
                name: 'data',
                type: 'object',
                properties: {
                    bankAccount: {
                        isNullable: false,
                        type: 'reference',
                        node: () => xtremFinanceData.nodes.BankAccount,
                    },
                    financialSite: {
                        isNullable: false,
                        type: 'reference',
                        node: () => xtremSystem.nodes.Site,
                    },
                    payToCustomerId: {
                        type: 'string',
                        isMandatory: true,
                    },
                    payToCustomerName: {
                        type: 'string',
                        isMandatory: true,
                    },
                    description: {
                        type: 'string',
                    },
                    paymentMethod: {
                        type: 'string',
                        isMandatory: true,
                    },
                    postingDate: {
                        type: 'string',
                        isMandatory: true,
                    },
                    paymentDate: {
                        type: 'string',
                        isMandatory: true,
                    },
                    currency: {
                        isNullable: false,
                        type: 'reference',
                        node: () => xtremMasterData.nodes.Currency,
                    },
                    companyFxRate: {
                        type: 'decimal',
                        isMandatory: true,
                    },
                    companyFxRateDivisor: {
                        type: 'decimal',
                        isMandatory: true,
                    },
                    fxRateDate: {
                        type: 'string',
                        isMandatory: true,
                    },
                    advanceAmount: {
                        type: 'decimal',
                        isMandatory: true,
                    },
                    advanceCompanyAmount: {
                        type: 'decimal',
                        isMandatory: true,
                    },
                    bankFeed: {
                        type: 'integer',
                        isMandatory: false,
                    },
                    arMatch: {
                        type: 'object',
                        isMandatory: true,
                        properties: {
                            isArMatch: {
                                type: 'boolean',
                            },
                            customerId: { type: 'string' },
                            matchingReasons: { type: 'string' },
                            arPaymentType: { type: 'string' },
                        },
                    },
                    lines: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                financialSite: {
                                    type: 'reference',
                                    node: () => xtremSystem.nodes.Site,
                                    isNullable: false,
                                },
                                currency: {
                                    type: 'reference',
                                    node: () => xtremMasterData.nodes.Currency,
                                    isNullable: false,
                                },
                                account: {
                                    type: 'reference',
                                    node: () => xtremFinanceData.nodes.Account,
                                    isNullable: false,
                                },
                                advanceAmount: {
                                    type: 'decimal',
                                    isMandatory: true,
                                },
                                advanceCompanyAmount: {
                                    type: 'decimal',
                                    isMandatory: true,
                                },
                                storedAttributes: {
                                    type: 'string',
                                },
                                storedDimensions: {
                                    type: 'string',
                                },
                            },
                        },
                    },
                },
            },
        ],
        return: {
            type: 'reference',
            node: () => xtremFinance.nodes.AccountsReceivableAdvance,
        },
    })
    static async createArAdvance(
        context: Context,
        data: xtremIntacctFinance.sharedFunctions.interfaces.ArAdvanceCreateData,
    ): Promise<xtremFinance.nodes.AccountsReceivableAdvance> {
        const arAdvance = await context.create(xtremFinance.nodes.AccountsReceivableAdvance, {
            bankAccount: data.bankAccount,
            financialSite: data.financialSite,
            payToCustomerId: data.payToCustomerId,
            payToCustomerName: data.payToCustomerName,
            description: data.description,
            intacctDocument: { paymentMethod: data.paymentMethod, bankFeed: data.bankFeed },
            postingDate: DateValue.parse(data.postingDate),
            paymentDate: DateValue.parse(data.paymentDate),
            currency: data.currency,
            companyFxRate: data.companyFxRate,
            companyFxRateDivisor: data.companyFxRateDivisor,
            fxRateDate: DateValue.parse(data.fxRateDate),
            advanceAmount: data.advanceAmount,
            advanceCompanyAmount: data.advanceCompanyAmount,
            lines: data.lines,
        });
        await arAdvance.$.save();

        const bankFeed = await context.read(
            xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed,
            { _id: data.bankFeed },
            { forUpdate: true },
        );

        let lineCounter = 0;
        const lines = await asyncArray(data.lines)
            .map(async line => {
                let _action: 'create' | 'update' = 'create';
                if ((await bankFeed.lines.length) > lineCounter) {
                    _action = 'update';
                }
                const lineData = {
                    _id:
                        (await bankFeed.lines.length) > lineCounter
                            ? (await bankFeed.lines.elementAt(lineCounter))._id
                            : undefined,
                    _action,
                    account: line.account,
                    amount: line.advanceAmount,
                    storedAttributes: line.storedAttributes,
                    storedDimensions: line.storedDimensions,
                };
                lineCounter += 1;
                return lineData;
            })
            .toArray();

        await bankFeed.$.set({
            payToCustomerId: data.payToCustomerId,
            payToCustomerName: data.payToCustomerName,
            paymentMethod: data.paymentMethod,
            paymentDate: DateValue.parse(data.paymentDate),
            receiptDate: DateValue.parse(data.postingDate),
            arMatch: data.arMatch,
            lines,
        });
        await bankFeed.$.save();

        const intacctArAdvance = await arAdvance.intacctDocument;
        await (
            await context.create(xtremFinanceData.nodes.FinanceTransaction, {
                batchId: Uuid.generate().toString(),
                documentSysId: (await intacctArAdvance?.bankFeed)?._id,
                documentNumber: (await intacctArAdvance?.bankFeed)?._id.toString(),
                documentType: 'bankReconciliationDeposit',
                targetDocumentNumber: await arAdvance.number,
                targetDocumentSysId: arAdvance._id,
                targetDocumentType: 'accountsReceivableAdvance',
                status: 'recorded',
                message: '',
                financialSite: data.financialSite,
            })
        ).$.save();

        await xtremFinance.nodes.AccountsReceivableAdvance.post(
            context,
            await context.read(
                xtremFinance.nodes.AccountsReceivableAdvance,
                { _id: arAdvance._id },
                { forUpdate: true },
            ),
        );
        return context.read(xtremFinance.nodes.AccountsReceivableAdvance, { _id: arAdvance._id });
    }
}

declare module '@sage/xtrem-finance/lib/nodes/accounts-receivable-advance' {
    export interface AccountsReceivableAdvance extends AccountsReceivableAdvanceExtension {}
}
