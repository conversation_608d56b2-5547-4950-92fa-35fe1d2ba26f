import type { AnyRecord, Context, integer } from '@sage/xtrem-core';
import { decorators, Logger, NodeExtension } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacct from '@sage/xtrem-intacct';

const logger = Logger.getLogger(__filename, 'intacct-dimension-extension');

@decorators.nodeExtension<DimensionExtension>({
    extends: () => xtremFinanceData.nodes.Dimension,
})
export class DimensionExtension extends NodeExtension<xtremFinanceData.nodes.Dimension> {
    /**
     * Corresponding to intacct DEPARTMENTID  OR CLASSID
     */
    @decorators.stringProperty<DimensionExtension, 'uIntacctId'>({
        isStored: true,
        isPublished: false,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly uIntacctId: Promise<string>;

    @decorators.stringProperty<DimensionExtension, 'intacctId'>({
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
        dependsOn: ['uIntacctId'],
        async getValue() {
            return (await this.uIntacctId) !== '' ? this.uIntacctId : this.id;
        },
        async setValue(value) {
            await this.$.set({ uIntacctId: value });
        },
    })
    readonly intacctId: Promise<string>;

    @decorators.stringProperty<DimensionExtension, 'intacctObject'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly intacctObject: Promise<string>;

    /**
     * Corresponding to intacct RECORDNO
     */
    @decorators.integerProperty<DimensionExtension, 'recordNo'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly recordNo: Promise<integer | null>;

    /**
     *  Linked to intacct STATUS
     */
    @decorators.stringProperty<DimensionExtension, 'statusIntacct'>({
        isPublished: true,
        async getValue() {
            return (await this.isActive) ? 'active' : 'inactive';
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly statusIntacct: Promise<string>;

    /**
     * Allow modification of intacctId & recordNo when
     * @returns
     */
    async specificsFields(): Promise<boolean> {
        return (
            (await (await this.$.old).intacctId) === (await this.intacctId) &&
            (await (await this.$.old).recordNo) === (await this.recordNo)
        );
    }

    /**
     *  Initialisator for mapping Payload
     * @param payload
     * @returns
     */
    static async initPayload(context: Context, payload: any): Promise<AnyRecord> {
        logger.debug(() => ` Payload to save ${JSON.stringify(payload)}`);
        payload.isActive = payload.statusIntacct === 'active';
        let dimensionTypeKey = '';
        if (payload.departmentRecordNo) {
            dimensionTypeKey = 'dimensionType01';
            payload.intacctObject = 'DEPARTEMENT';
        }
        if (payload.classRecordNo) {
            dimensionTypeKey = 'dimensionType02';
            payload.intacctObject = 'CLASS';
        }
        payload.dimensionType = (
            await context.read(xtremFinanceData.nodes.DimensionType, {
                docProperty: dimensionTypeKey,
            })
        )._id;

        delete payload.classRecordNo;
        delete payload.departmentRecordNo;
        delete payload.intacctName;
        delete payload.statusIntacct;

        return payload;
    }
}

declare module '@sage/xtrem-finance-data/lib/nodes/dimension' {
    export interface Dimension extends DimensionExtension {}
}
