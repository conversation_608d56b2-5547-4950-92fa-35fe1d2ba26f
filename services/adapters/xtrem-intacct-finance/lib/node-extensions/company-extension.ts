import type { Context } from '@sage/xtrem-core';
import { NodeExtension, NodeStatus, decorators } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremIntacctFinance from '..';

@decorators.nodeExtension<CompanyExtension>({
    extends: () => xtremSystem.nodes.Company,
    async controlBegin(cx) {
        if (this.$.status === NodeStatus.modified) {
            await xtremIntacctFinance.functions.controls.isIntacctOnHoldCheck(this as xtremSystem.nodes.Company, cx);
        }
    },
})
export class CompanyExtension extends NodeExtension<xtremSystem.nodes.Company> {
    public canUpdateFromExternalIntegration = false;

    @decorators.booleanProperty<CompanyExtension, 'doUpdateArAmountPaid'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly doUpdateArAmountPaid: Promise<boolean>;

    @decorators.booleanPropertyOverride<CompanyExtension, 'doArPosting'>({
        dependsOn: ['legislation'],
        async computeValue() {
            return (await xtremIntacctFinance.functions.isIntacctActive(this.$.context))
                ? false
                : (await this.legislation).doArPosting;
        },
    })
    readonly doArPosting: Promise<boolean>;

    @decorators.booleanPropertyOverride<CompanyExtension, 'doApPosting'>({
        dependsOn: ['legislation'],
        async computeValue() {
            return (await xtremIntacctFinance.functions.isIntacctActive(this.$.context))
                ? false
                : (await this.legislation).doApPosting;
        },
    })
    readonly doApPosting: Promise<boolean>;

    @decorators.asyncMutation<typeof CompanyExtension, 'syncCompanyOnHold'>({
        isPublished: true,
        startsReadOnly: false,
        isSchedulable: true,
        parameters: [
            { name: 'companySysId', type: 'string' },
            { name: 'isAllCompanies', type: 'boolean' },
        ],
        return: { type: 'boolean' },
    })
    static async syncCompanyOnHold(context: Context, companySysId: string, isAllCompanies = false) {
        if (!(await xtremIntacct.functions.isIntacctActive(context))) {
            await context.batch.logMessage('warning', 'Intacct is disable');
            return false;
        }

        return xtremIntacctFinance.functions.syncCompanyOnHold(context, {
            _id: Number(companySysId),
            isAll: isAllCompanies,
        });
    }
}
declare module '@sage/xtrem-system/lib/nodes/company' {
    export interface Company extends CompanyExtension {}
}
