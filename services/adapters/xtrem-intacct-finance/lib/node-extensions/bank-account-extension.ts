import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremSystem from '@sage/xtrem-system';

/**
 * CHECKINGACCOUNT intacct object
 */
@decorators.nodeExtension<BankAccountExtension>({
    extends: () => xtremFinanceData.nodes.BankAccount,
})
export class BankAccountExtension extends NodeExtension<xtremFinanceData.nodes.BankAccount> {
    /**
     *  Linked to intacct STATUS
     */
    @decorators.stringProperty<BankAccountExtension, 'statusIntacct'>({
        isPublished: true,
        async getValue() {
            return (await this.isActive) ? 'active' : 'inactive';
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly statusIntacct: Promise<string>;

    /** GLACCOUNTNO */
    @decorators.stringProperty<BankAccountExtension, 'intacctGlAccount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly intacctGlAccount: Promise<string>;

    /** locationID  TODO : to be mapped to sites ?  */
    @decorators.stringProperty<BankAccountExtension, 'location'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly location: Promise<string>;

    /** BANKACCOUNTID  // intacctID field  */
    @decorators.stringProperty<BankAccountExtension, 'intacctId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.code,
    })
    readonly intacctId: Promise<string>;

    /** MEGAENTITYID   // entity id field  */
    @decorators.stringProperty<BankAccountExtension, 'megaEntityId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.id,
    })
    readonly megaEntityId: Promise<string>;

    /**
     *  Initialisator for mapping Payload
     * @param payload
     * @returns
     */
    static async initPayload(context: Context, payload: any) {
        payload.isActive = payload.statusIntacct === 'active';
        delete payload.statusIntacct;

        const intacctConfiguration = (await xtremIntacct.nodes.Intacct.defaultInstance(
            context,
            true,
        )) as xtremIntacct.nodes.Intacct;

        const site = await context.tryRead(xtremSystem.nodes.Site, { id: payload.location });
        const account = await context.tryRead(xtremFinanceData.nodes.Account, {
            id: payload.intacctGlAccount,
            chartOfAccount: (await intacctConfiguration.chartOfAccount)._id,
        });

        if (!account) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-intacct-finance/bank-account-account-dont-exist',
                    "{{bankName}}: the account {{accountName}} doesn't exist in Sage DMO.",
                    { bankName: payload.name, accountName: payload.intacctGlAccount },
                ),
            );
        } else {
            payload.account = account;
        }

        if (!site) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-intacct-finance/bank-account-financial-site-dont-exist',
                    "{{bankName}}: the financial site {{financialSite}} doesn't exist in Sage DMO.",
                    { financialSite: payload.location, bankName: payload.name },
                ),
            );
        }

        const financialSite = await xtremFinanceData.functions.getFinancialSite(site);

        payload.financialSite = {
            _id: financialSite._id,
        };

        payload.account = {
            _id: account._id,
        };

        context.logger.debug(() => ` Payload to save ${JSON.stringify(payload)}`);
        return payload;
    }
}

declare module '@sage/xtrem-finance-data/lib/nodes/bank-account' {
    export interface BankAccount extends BankAccountExtension {}
}
