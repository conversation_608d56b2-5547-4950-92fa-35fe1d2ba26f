import type { Collection, Context } from '@sage/xtrem-core';
import { BinaryStream, decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremIntacctFinance from '../index';

@decorators.nodeExtension<IntacctExtension>({
    extends: () => xtremIntacct.nodes.Intacct,
})
export class IntacctExtension extends NodeExtension<xtremIntacct.nodes.Intacct> {
    @decorators.collectionProperty<IntacctExtension, 'generateSmartEvent'>({
        isPublished: true,
        node: () => xtremIntacctFinance.nodes.IntacctMap,
        getFilter() {
            return { isSmartEvent: true };
        },
    })
    readonly generateSmartEvent: Collection<xtremIntacctFinance.nodes.IntacctMap>;

    @decorators.binaryStreamProperty<IntacctExtension, 'customPackageFile'>({
        isPublished: true,
        async computeValue() {
            return BinaryStream.fromBuffer(Buffer.from(await IntacctExtension.generateSmartEventFile(this.$.context)));
        },
    })
    readonly customPackageFile: Promise<BinaryStream>;

    static async getMyPackage(context: Context): Promise<xtremIntacct.classes.CustomPackage> {
        const defaultInstance = await xtremIntacct.nodes.Intacct.defaultInstance(context);
        return new xtremIntacct.classes.CustomPackage({
            name: 'XtreemSmartEvents',
            apiUser: (await defaultInstance?.userId) || '',
            author: (await context.user)?.userName || 'SDMO',
            description: 'All SmartEvents for Xtreem',
        });
    }

    /**
     *  Write file content is the content to write
     * By default it will write in data/mapping folder
     * @param context
     * @param content content to write in the file name of the file will be content.name
     */
    @decorators.mutation<typeof IntacctExtension, 'generateSmartEventFile'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'string',
            isMandatory: true,
        },
    })
    static async generateSmartEventFile(context: Context): Promise<string> {
        const myPackage = await IntacctExtension.getMyPackage(context);
        const mapSmartEvent = context.query(xtremIntacctFinance.nodes.IntacctMap, { filter: { isSmartEvent: true } });

        await mapSmartEvent
            .filter(
                async map => (await map.intacctIDField) !== null && (await map.synchronizationDirection) === 'inbound',
            )
            .forEach(async mapIntacct => {
                myPackage.addSmarlinkCreateUpdateDelete({
                    object: await mapIntacct.id,
                    objectId: (await (await mapIntacct.intacctIDField)?.name) || '',
                });
            });
        await mapSmartEvent
            .filter(
                async map => (await map.intacctIDField) !== null && (await map.synchronizationDirection) === 'outbound',
            )
            .forEach(async mapIntacct => {
                myPackage.addSmartLink({
                    object: await mapIntacct.id,
                    objectId: (await (await mapIntacct.intacctIDField)?.name) || '',
                    changeMade: 'desynchronized',
                    events: ['add', 'delete', 'set'],
                });
            });

        return myPackage.toString(true);
    }
}

declare module '@sage/xtrem-intacct/lib/nodes/intacct' {
    export interface Intacct extends IntacctExtension {}
}
