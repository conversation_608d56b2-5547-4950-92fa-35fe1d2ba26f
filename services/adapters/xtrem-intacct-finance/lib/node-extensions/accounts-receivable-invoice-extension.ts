import type { Context, integer, Reference } from '@sage/xtrem-core';
import { decorators, LocalizedError, Logger, NodeExtension, NodeStatus } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremIntacctFinance from '..';

const logger = Logger.getLogger(__filename, 'ar-invoice');

/**
 *  Intacct object : ARINVOICE
 */
@decorators.nodeExtension<AccountsReceivableInvoiceExtension>({
    extends: () => xtremFinance.nodes.AccountsReceivableInvoice,
    async controlDelete(cx) {
        await xtremIntacctFinance.functions.finance.controlBeginAndDelete(cx, this, logger);
    },
    async controlBegin(cx) {
        if (this.$.status !== NodeStatus.added) {
            await xtremIntacctFinance.functions.finance.controlBeginAndDelete(cx, this, logger);
        }
    },
    async saveBegin() {
        await xtremIntacctFinance.functions.finance.saveBegin(this);
    },
    async saveEnd() {
        await xtremIntacctFinance.functions.finance.saveEnd(this as xtremFinance.nodes.AccountsReceivableInvoice);
    },
})
export class AccountsReceivableInvoiceExtension
    extends NodeExtension<xtremFinance.nodes.AccountsReceivableInvoice>
    implements xtremSynchronization.interfaces.SynchronizationNode
{
    skipCallApi: boolean;

    getSyncStateReference() {
        return this.intacctDocument;
    }

    @decorators.referenceProperty<AccountsReceivableInvoiceExtension, 'intacctDocument'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'document',
        node: () => xtremIntacctFinance.nodes.IntacctAccountsReceivableInvoice,
        isNullable: true,
        excludedFromPayload: true,
    })
    readonly intacctDocument: Reference<xtremIntacctFinance.nodes.IntacctAccountsReceivableInvoice | null>;

    // @decorators.stringProperty<AccountsReceivableInvoiceExtension, 'intacctId'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    // })
    // readonly intacctId: Promise<string>;

    // /**
    //  * Corresponding to intacct RECORDNO
    //  */
    // @decorators.integerProperty<AccountsReceivableInvoiceExtension, 'recordNo'>({
    //     isStored: true,
    //     isPublished: true,
    //     isNullable: true,
    // })
    // readonly recordNo: Promise<integer | null>;

    // /**
    //  * the integration status
    //  */
    // @decorators.enumProperty<AccountsReceivableInvoiceExtension, 'intacctIntegrationState'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremCommunication.enums.IntegrationStateDataType,
    //     defaultValue: () => 'not',
    // })
    // readonly intacctIntegrationState: Promise<xtremCommunication.enums.IntegrationState>;

    // /**
    //  * intacct last integration date time
    //  */
    // @decorators.datetimeProperty<AccountsReceivableInvoiceExtension, 'intacctLastIntegrationDate'>({
    //     isStored: true,
    //     isPublished: true,
    //     isNullable: true,
    //     defaultValue: null,
    // })
    // readonly intacctLastIntegrationDate: Promise<datetime | null>;

    /**
     * Corresponding to intacct RECORD_URL
     */
    // @decorators.stringProperty<AccountsReceivableInvoiceExtension, 'intacctUrl'>({
    //     isStored: true,
    //     isPublished: true,
    //     dataType: () => xtremIntacctFinance.dataTypes.intacctUrl,
    // })
    // readonly intacctUrl: Promise<string>;

    /**
     *  Init for intacct query AR Invoice
     * @param payload
     * @returns
     */
    static async queryIntacctArInvoice(
        context: Context,
        payload: any,
    ): Promise<xtremIntacctFinance.interfaces.BankAccountMatching.IntacctArInvoice> {
        context.logger.debug(() => ` Payload : ${JSON.stringify(payload)}`);

        const term = await context
            .query(xtremMasterData.nodes.PaymentTerm, {
                filter: { name: payload.termName, businessEntityType: { _ne: 'supplier' } },
                first: 1,
            })
            .at(0);
        const currency = await context
            .query(xtremMasterData.nodes.Currency, { filter: { id: payload.currencyId }, first: 1 })
            .at(0);

        return { ...payload, term, currency };
    }

    @decorators.mutation<typeof AccountsReceivableInvoiceExtension, 'updateOpenItemFromIntacct'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [{ name: 'arInvoiceSysId', type: 'integer', isMandatory: true }],
        return: { type: 'object', properties: { wasSuccessful: 'boolean', message: 'string' } },
    })
    static async updateOpenItemFromIntacct(
        context: Context,
        arInvoiceSysId: integer,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        try {
            await context.runInWritableContext(async writableContext =>
                xtremIntacctFinance.functions.finance.updateOpenItemFromIntacct(
                    writableContext,
                    'ARINVOICE',
                    await writableContext.read(
                        xtremFinance.nodes.AccountsReceivableInvoice,
                        { _id: arInvoiceSysId },
                        { forUpdate: true },
                    ),
                    logger,
                ),
            );
            return { wasSuccessful: true, message: '' };
        } catch (error) {
            if (!(error instanceof LocalizedError)) throw error;
            return { message: error.message, wasSuccessful: false };
        }
    }
}

declare module '@sage/xtrem-finance/lib/nodes/accounts-receivable-invoice' {
    export interface AccountsReceivableInvoice extends AccountsReceivableInvoiceExtension {}
}
