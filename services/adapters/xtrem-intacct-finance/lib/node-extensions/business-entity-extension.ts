import { decorators, NodeExtension, NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { doWebusinessEntitySynchronize } from '../functions/synchronization';
import { synchronizeCustomerSupplier } from '../functions/synchronization/business-entity';

@decorators.nodeExtension<BusinessEntityExtension>({
    extends: () => xtremMasterData.nodes.BusinessEntity,
    async saveBegin() {
        this.isAddressCreatedDeleted =
            this.$.status === NodeStatus.modified &&
            (await this.addresses.length) !== (await (await this.$.old).addresses.length);
    },
    async saveEnd() {
        if (this.skipCallIntacctApi) {
            return;
        }
        const isMappedPropertyUpdate = await doWebusinessEntitySynchronize(
            this as xtremMasterData.nodes.BusinessEntity,
        );

        if (this.isAddressCreatedDeleted || isMappedPropertyUpdate) {
            await synchronizeCustomerSupplier(this as xtremMasterData.nodes.BusinessEntity);
        }
    },
})
export class BusinessEntityExtension extends NodeExtension<xtremMasterData.nodes.BusinessEntity> {
    skipCallIntacctApi: boolean;

    isAddressCreatedDeleted: boolean;
}

declare module '@sage/xtrem-master-data/lib/nodes/business-entity' {
    export interface BusinessEntity extends BusinessEntityExtension {}
}
