import type { Reference } from '@sage/xtrem-core';
import { SubNodeExtension1, decorators } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremIntacctFinance from '..';

/**
 *  Intacct object : ARPYMTDETAIL
 */

@decorators.subNodeExtension1<AccountsReceivablePaymentLineExtension>({
    extends: () => xtremFinance.nodes.AccountsReceivablePaymentLine,
    async saveBegin() {
        if (!(await this.intacctDocumentLine)) {
            await this.$.set({ intacctDocumentLine: {} });
        }
    },
})
export class AccountsReceivablePaymentLineExtension
    extends SubNodeExtension1<xtremFinance.nodes.AccountsReceivablePaymentLine>
    implements xtremSynchronization.interfaces.SynchronizationNodeLine
{
    getSyncStateLine() {
        return this.intacctDocumentLine;
    }

    @decorators.referenceProperty<AccountsReceivablePaymentLineExtension, 'intacctDocumentLine'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremIntacctFinance.nodes.IntacctAccountsReceivablePaymentLine,
        isNullable: true,
    })
    readonly intacctDocumentLine: Reference<xtremIntacctFinance.nodes.IntacctAccountsReceivablePaymentLine | null>;
}
declare module '@sage/xtrem-finance/lib/nodes/accounts-receivable-payment-line' {
    export interface AccountsReceivablePaymentLine extends AccountsReceivablePaymentLineExtension {}
}
