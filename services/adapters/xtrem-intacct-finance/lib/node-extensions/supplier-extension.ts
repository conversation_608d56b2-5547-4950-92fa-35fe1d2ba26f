import type { Reference } from '@sage/xtrem-core';
import { SubNodeExtension1, decorators } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremIntacctFinance from '..';
import { paymentMethodIntacct } from '../functions/supplier-extension';
import { deleteIntacct, launchSynchronization } from '../functions/synchronization';

@decorators.subNodeExtension1<SupplierExtension>({
    extends: () => xtremMasterData.nodes.Supplier,
    async deleteBegin() {
        if (await xtremIntacct.functions.isIntacctActive(this.$.context)) {
            await deleteIntacct(this.$.context, {
                intacctNode: 'VENDOR',
                recordNo: (await (await this.intacctSupplier)?.recordNo) || undefined,
            });
        }
    },
    async saveBegin() {
        if (!(await this.intacctSupplier)) {
            await this.$.set({ intacctSupplier: {} });
        }

        if (!(await xtremIntacct.functions.isIntacctActive(this.$.context))) {
            this.skipCallApi = true;
        }

        await this.$.set({ paymentMethod: paymentMethodIntacct(await this.paymentMethodSelect) });
    },
    async saveEnd() {
        await launchSynchronization(this as xtremMasterData.nodes.Supplier);
    },
})
export class SupplierExtension
    extends SubNodeExtension1<xtremMasterData.nodes.Supplier>
    implements xtremSynchronization.interfaces.SynchronizationNode
{
    skipCallApi: boolean;

    getSyncStateReference() {
        return this.intacctSupplier;
    }

    @decorators.referenceProperty<SupplierExtension, 'intacctSupplier'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'parent',
        node: () => xtremIntacctFinance.nodes.IntacctSupplier,
        isNullable: true,
        excludedFromPayload: true,
    })
    readonly intacctSupplier: Reference<xtremIntacctFinance.nodes.IntacctSupplier | null>;

    @decorators.enumProperty<SupplierExtension, 'paymentMethodSelect'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.enums.PaymentMethodDataType,
        defaultValue: null,
        isNullable: true,
    })
    readonly paymentMethodSelect: Promise<xtremMasterData.enums.PaymentMethod | null>;
}
declare module '@sage/xtrem-master-data/lib/nodes/supplier' {
    export interface Supplier extends SupplierExtension {}
}
