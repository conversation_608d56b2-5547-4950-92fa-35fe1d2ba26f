import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import {
    customerPayloadContinueOnError,
    itemPayloadContinueOnError,
    supplierPayloadContinueOnError,
} from '../functions/synchronization';
import type * as xtremIntacctFinance from '../index';

/**
 *  Intacct object : SPLIT
 */
@decorators.nodeExtension<JournalEntryLineDimensionExtension>({
    extends: () => xtremFinance.nodes.JournalEntryLineDimension,
})
export class JournalEntryLineDimensionExtension extends NodeExtension<xtremFinance.nodes.JournalEntryLineDimension> {
    @decorators.jsonProperty<JournalEntryLineDimensionExtension, 'intacctDimension'>({
        isPublished: true,
        excludedFromPayload: true,
        async computeValue() {
            const customer = await this.customer;
            const supplier = await this.supplier;
            const item = await this.item;
            return {
                ...(customer ? { CUSTOMERID: await customerPayloadContinueOnError(customer) } : {}),
                ...(supplier ? { VENDORID: await supplierPayloadContinueOnError(supplier) } : {}),
                ...(item ? { ITEMID: await itemPayloadContinueOnError(item) } : {}),
                PROJECTID: (await (await this.project)?.id)?.toString(),
                TASKID: (await (await this.task)?.id)?.toString(),
                EMPLOYEEID: (await (await this.employee)?.id)?.toString(),
                DEPARTMENTID: (await (await this.dimension01)?.intacctId)?.toString(),
                CLASSID: (await (await this.dimension02)?.intacctId)?.toString(),
            };
        },
    })
    readonly intacctDimension: Promise<xtremIntacctFinance.interfaces.JournalEntry.IntacctDimension>;
}

declare module '@sage/xtrem-finance/lib/nodes/journal-entry-line-dimension' {
    export interface JournalEntryLineDimension extends JournalEntryLineDimensionExtension {}
}
