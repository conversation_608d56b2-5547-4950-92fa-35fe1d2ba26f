import type { Reference } from '@sage/xtrem-core';
import { SubNodeExtension1, decorators } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremIntacctFinance from '..';

/**  Intacct object : ARINVOICEITEM */
@decorators.subNodeExtension1<AccountsReceivableInvoiceLineExtension>({
    extends: () => xtremFinance.nodes.AccountsReceivableInvoiceLine,
    async saveBegin() {
        if (!(await this.intacctDocumentLine)) {
            await this.$.set({ intacctDocumentLine: {} });
        }
    },
})
export class AccountsReceivableInvoiceLineExtension
    extends SubNodeExtension1<xtremFinance.nodes.AccountsReceivableInvoiceLine>
    implements xtremSynchronization.interfaces.SynchronizationNodeLine
{
    getSyncStateLine() {
        return this.intacctDocumentLine;
    }

    @decorators.referenceProperty<AccountsReceivableInvoiceLineExtension, 'intacctDocumentLine'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremIntacctFinance.nodes.IntacctAccountsReceivableInvoiceLine,
        isNullable: true,
    })
    readonly intacctDocumentLine: Reference<xtremIntacctFinance.nodes.IntacctAccountsReceivableInvoiceLine | null>;
}

declare module '@sage/xtrem-finance/lib/nodes/accounts-receivable-invoice-line' {
    export interface AccountsReceivableInvoiceLine extends AccountsReceivableInvoiceLineExtension {}
}
