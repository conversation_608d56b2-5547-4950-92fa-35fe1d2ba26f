import type { AnyRecord, Context, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, Logger, NodeExtension, NodeStatus } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';
import { getTaxCategory, getTaxCountry, getTaxSolution } from '../functions';
import * as xtremIntacctFinance from '../index';

const logger = Logger.getLogger(__filename, 'intacct-tax-extension');
@decorators.nodeExtension<TaxExtension>({
    extends: () => xtremTax.nodes.Tax,
    async prepare() {
        if (
            !(await this.primaryExternalReference) ||
            (!(await this.intacctSecondaryExternalReference) &&
                (await this.isReverseCharge) &&
                !(await this.isIntacctReverseCharge) &&
                (await this.isIntacct))
        ) {
            await this.$.set({ isActive: false });
        }
    },
    async controlBegin(cx) {
        await xtremIntacctFinance.functions.controls.controlBeginIsIntacctReverseCharge(cx, this as xtremTax.nodes.Tax);
        await xtremIntacctFinance.functions.controls.controlBeginIsIntacct(cx, this as xtremTax.nodes.Tax);
        await xtremIntacctFinance.functions.controls.controlBeginValidateTaxRate(cx, this as xtremTax.nodes.Tax);
        await xtremIntacctFinance.functions.controls.controlBeginType(cx, this as xtremTax.nodes.Tax);
    },
    async saveBegin() {
        if (this.$.status === NodeStatus.added && (await this.isIntacct)) {
            await this.$.set({ isActive: false });
        }
    },
    async controlDelete(cx): Promise<void> {
        await cx.error
            .withMessage(
                '@sage/xtrem-intacct-finance/nodes__tax_extension__deletion_forbidden',
                'You cannot delete this tax record. It is linked to Sage Intacct.',
            )
            .if(await this.isIntacct)
            .is.true();
    },
})
export class TaxExtension extends NodeExtension<xtremTax.nodes.Tax> {
    /**
     * is this Generated from intacct
     */
    @decorators.booleanProperty<TaxExtension, 'isIntacct'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: false,
    })
    readonly isIntacct: Promise<boolean>;

    /**
     * Corresponding to intacct NAME
     */
    @decorators.stringProperty<TaxExtension, 'uIntacctId'>({
        isStored: true,
        isPublished: false,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly uIntacctId: Promise<string>;

    @decorators.stringProperty<TaxExtension, 'intacctId'>({
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
        dependsOn: ['uIntacctId'],
        async getValue() {
            return (await this.uIntacctId) !== '' ? this.uIntacctId : this.name;
        },
        async setValue(value) {
            await this.$.set({ uIntacctId: value });
        },
    })
    readonly intacctId: Promise<string>;

    /**
     * Corresponding to intacct RECORDNO
     */
    @decorators.integerProperty<TaxExtension, 'recordNo'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly recordNo: Promise<integer | null>;

    /**
     *  Linked to intacct STATUS
     */
    @decorators.stringProperty<TaxExtension, 'statusIntacct'>({
        isPublished: true,
        async getValue() {
            return (await this.isActive) ? 'active' : 'inactive';
        },
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly statusIntacct: Promise<string>;

    /**
     * Linked to TAXTYPE
     */
    @decorators.stringProperty<TaxExtension, 'intacctTaxType'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly intacctTaxType: Promise<string>;

    /**
     * Linked to GLACCOUNT
     */
    @decorators.stringProperty<TaxExtension, 'intacctAccount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly intacctAccount: Promise<string>;

    /**
     * Linked to REVERSECHARGE
     */
    @decorators.booleanProperty<TaxExtension, 'isIntacctReverseCharge'>({
        isStored: true,
        isPublished: true,
    })
    readonly isIntacctReverseCharge: Promise<boolean>;

    /**
     * Linked to VALUE
     */
    @decorators.decimalProperty<TaxExtension, 'rate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly rate: Promise<decimal | null>;

    @decorators.booleanProperty<TaxExtension, 'isUpdateFromIntacct'>({
        isPublished: true,
        isTransientInput: true,
    })
    readonly isUpdateFromIntacct: Promise<boolean>;

    @decorators.referenceProperty<TaxExtension, 'intacctSecondaryExternalReference'>({
        isPublished: true,
        node: () => xtremTax.nodes.Tax,
        isNullable: true,
        isStored: true,
        filters: {
            control: {
                async name() {
                    return { _ne: await this.name };
                },
                isIntacctReverseCharge: true,
                taxCategory() {
                    return this.taxCategory;
                },
                country() {
                    return this.country;
                },
            },
        },
    })
    readonly intacctSecondaryExternalReference: Reference<xtremTax.nodes.Tax | null>;

    /**
     * Linked to TAXSOLUTIONID
     */
    @decorators.stringProperty<TaxExtension, 'intacctSolutionId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly intacctSolutionId: Promise<string>;

    /**
     * Custom delete logic
     * @param xtremSysId
     * @returns
     */
    static async deactivateRecord(context: Context, xtremSysId: number): Promise<void> {
        const tax = await context.read(xtremTax.nodes.Tax, { _id: xtremSysId }, { forUpdate: true });

        await tax.$.set({
            isActive: false,
            isIntacct: false,
            intacctId: '',
            recordNo: null,
            rate: null,
            intacctAccount: '',
        });

        await tax.$.save();
    }

    static async intacctFilter(context: Context): Promise<xtremIntacct.interfaces.WhereFields[]> {
        const intacctConfiguration = await xtremIntacct.nodes.Intacct.defaultInstance(context);
        if (intacctConfiguration) {
            const taxSolutionsArray = await getTaxSolution(intacctConfiguration);
            if (taxSolutionsArray.length) {
                return [
                    {
                        type: 'in',
                        where: 'TAXSOLUTIONID',
                        whereValue: '',
                        whereValueArray: taxSolutionsArray,
                    },
                ];
            }
        }
        return [];
    }

    /**
     *  Initialisator for mapping Payload
     * Returning Clean payload for create/update
     * @param payload
     * @returns
     */
    static async initPayload(context: Context, payload: any): Promise<AnyRecord> {
        logger.debug(() => ` Payload to save ${JSON.stringify(payload)}`);

        const taxes = await context
            .query(xtremTax.nodes.Tax, {
                filter: { recordNo: payload.recordNo },
            })
            .toArray();
        const tax = taxes.length > 0 ? taxes[0] : undefined;

        payload.isIntacct = true;
        payload.isUpdateFromIntacct = true;

        const intacctConfiguration = await xtremIntacct.nodes.Intacct.defaultInstance(context);
        payload.legislation = await intacctConfiguration?.legislation;
        payload.country = await tax?.country;
        if (!tax?.country && intacctConfiguration) {
            payload.country = await getTaxCountry(intacctConfiguration, payload.intacctSolutionId);
        }

        payload.taxCategory = await tax?.taxCategory;
        if (!tax?.taxCategory && intacctConfiguration) {
            payload.taxCategory = await getTaxCategory(intacctConfiguration, payload.intacctSolutionId);
        }

        payload.primaryExternalReference = payload.description;
        payload.legalMention = '';
        if (payload.intacctTaxType === '') {
            payload.type = 'purchasingAndSales';
        } else {
            payload.type = payload.intacctTaxType === 'Purchase' ? 'purchasing' : 'sales';
        }
        payload.taxValues = [
            {
                rate: payload.rate,
            },
        ];
        delete payload.description;
        return payload;
    }
}

declare module '@sage/xtrem-tax/lib/nodes/tax' {
    export interface Tax extends TaxExtension {}
}
