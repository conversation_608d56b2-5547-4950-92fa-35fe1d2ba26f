import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremFinanceData from '@sage/xtrem-finance-data';

@decorators.nodeExtension<DimensionTypeExtension>({
    extends: () => xtremFinanceData.nodes.DimensionType,
})
export class DimensionTypeExtension extends NodeExtension<xtremFinanceData.nodes.DimensionType> {
    @decorators.stringProperty<DimensionTypeExtension, 'intacctObject'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
        async defaultValue() {
            return DimensionTypeExtension.setIntacctDimensionType(await this.docProperty);
        },
        async updatedValue() {
            return DimensionTypeExtension.setIntacctDimensionType(await this.docProperty);
        },
    })
    readonly intacctObject: Promise<string>;

    static setIntacctDimensionType(id: string): string {
        switch (id) {
            case 'dimensionType01': {
                return 'DEPARTMENT';
            }
            case 'dimensionType02': {
                return 'CLASS';
            }
            default: {
                return '';
            }
        }
    }
}

declare module '@sage/xtrem-finance-data/lib/nodes/dimension-type' {
    export interface DimensionType extends DimensionTypeExtension {}
}
