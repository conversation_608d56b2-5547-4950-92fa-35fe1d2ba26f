import type { Reference } from '@sage/xtrem-core';
import { NodeStatus, SubNodeExtension1, decorators } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremIntacctFinance from '..';
import { deleteIntacct, launchSynchronization } from '../functions/synchronization';

@decorators.subNodeExtension1<CustomerExtension>({
    extends: () => xtremMasterData.nodes.Customer,
    async deleteEnd() {
        if (await xtremIntacct.functions.isIntacctActive(this.$.context)) {
            await deleteIntacct(this.$.context, {
                intacctNode: 'CUSTOMER',
                recordNo: (await (await this.intacctCustomer)?.recordNo) || undefined,
            });
        }
    },
    async saveBegin() {
        if (!(await this.intacctCustomer)) {
            await this.$.set({ intacctCustomer: {} });
        }

        if (!(await xtremIntacct.functions.isIntacctActive(this.$.context))) {
            this.skipCallApi = true;
        }
    },
    async saveEnd() {
        if (this.skipCallApi) {
            return;
        }
        await launchSynchronization(this as xtremMasterData.nodes.Customer);
    },

    async controlBegin(cx) {
        if (this.$.status === NodeStatus.modified) {
            await xtremIntacctFinance.functions.controls.isIntacctCreditLimit(
                this as xtremMasterData.nodes.Customer,
                cx,
            );
        }
    },
})
//
export class CustomerExtension
    extends SubNodeExtension1<xtremMasterData.nodes.Customer>
    implements xtremSynchronization.interfaces.SynchronizationNode
{
    skipCallApi: boolean;

    public canUpdateFromExternalIntegration = false;

    getSyncStateReference() {
        return this.intacctCustomer;
    }

    @decorators.referenceProperty<CustomerExtension, 'intacctCustomer'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'parent',
        node: () => xtremIntacctFinance.nodes.IntacctCustomer,
        isNullable: true,
        excludedFromPayload: true,
    })
    readonly intacctCustomer: Reference<xtremIntacctFinance.nodes.IntacctCustomer | null>;
}
declare module '@sage/xtrem-master-data/lib/nodes/customer' {
    export interface Customer extends CustomerExtension {}
}
