import type { Reference } from '@sage/xtrem-core';
import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremIntacctFinance from '..';

/** *  Intacct object : GLENTRY */
@decorators.nodeExtension<JournalEntryLineExtension>({
    extends: () => xtremFinance.nodes.JournalEntryLine,
    async saveBegin() {
        if (!(await this.intacctDocumentLine)) {
            await this.$.set({ intacctDocumentLine: {} });
        }
    },
})
export class JournalEntryLineExtension
    extends NodeExtension<xtremFinance.nodes.JournalEntryLine>
    implements xtremSynchronization.interfaces.SynchronizationNodeLine
{
    getSyncStateLine() {
        return this.intacctDocumentLine;
    }

    @decorators.referenceProperty<JournalEntryLineExtension, 'intacctDocumentLine'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremIntacctFinance.nodes.IntacctJournalEntryLine,
        isNullable: true,
    })
    readonly intacctDocumentLine: Reference<xtremIntacctFinance.nodes.IntacctJournalEntryLine | null>;
}

declare module '@sage/xtrem-finance/lib/nodes/journal-entry-line' {
    export interface JournalEntryLine extends JournalEntryLineExtension {}
}
