import type { Reference } from '@sage/xtrem-core';
import { SubNodeExtension1, decorators } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremIntacctFinance from '..';

/** Intacct object : ARDAVANCEITEM */
@decorators.subNodeExtension1<AccountsReceivableAdvanceLineExtension>({
    extends: () => xtremFinance.nodes.AccountsReceivableAdvanceLine,
    async saveBegin() {
        if (!(await this.intacctDocumentLine)) {
            await this.$.set({ intacctDocumentLine: {} });
        }
    },
})
export class AccountsReceivableAdvanceLineExtension
    extends SubNodeExtension1<xtremFinance.nodes.AccountsReceivableAdvanceLine>
    implements xtremSynchronization.interfaces.SynchronizationNodeLine
{
    getSyncStateLine() {
        return this.intacctDocumentLine;
    }

    @decorators.referenceProperty<AccountsReceivableAdvanceLineExtension, 'intacctDocumentLine'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremIntacctFinance.nodes.IntacctAccountsReceivableAdvanceLine,
        isNullable: true,
    })
    readonly intacctDocumentLine: Reference<xtremIntacctFinance.nodes.IntacctAccountsReceivableAdvanceLine | null>;
}

declare module '@sage/xtrem-finance/lib/nodes/accounts-receivable-advance-line' {
    export interface AccountsReceivableAdvanceLine extends AccountsReceivableAdvanceLineExtension {}
}
