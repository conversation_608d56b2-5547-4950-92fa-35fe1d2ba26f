import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremIntacctFinance from '..';

export const frp1000OptionManagementExtension = new ActivityExtension({
    extends: xtremStructure.activities.baseOptionManagement,
    __filename,
    permissions: [],
    operationGrants: {
        read: [
            {
                operations: ['isServiceOptionActiveFunction'],
                on: [() => xtremIntacctFinance.nodes.IntacctCashBookManagement],
            },
        ],
        update: [
            { operations: ['serviceOptionChange'], on: [() => xtremIntacctFinance.nodes.IntacctCashBookManagement] },
        ],
    },
});
