import type { OperationGrant } from '@sage/xtrem-core';
import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSynchronization from '@sage/xtrem-synchronization';
import * as xtremIntacctFinance from '..';

const commonMappingOperations: OperationGrant[] = [
    {
        operations: ['lookup'],
        on: [
            () => xtremIntacctFinance.nodes.IntacctContact,
            () => xtremIntacctFinance.nodes.IntacctMap,
            () => xtremSynchronization.nodes.SynchronizationState,
        ],
    },
    {
        operations: ['synchronizeNode'],
        on: [() => xtremIntacctFinance.nodes.IntacctListener],
    },
];

export const supplierIntacctExtension = new ActivityExtension({
    extends: xtremMasterData.activities.supplier,
    __filename,
    permissions: [],
    operationGrants: {
        read: [...commonMappingOperations],
        create: [...commonMappingOperations],
        update: [...commonMappingOperations],
        delete: [...commonMappingOperations],
    },
});
