import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

export const companyExtension = new ActivityExtension({
    extends: xtremSystem.activities.company,
    __filename,
    permissions: [],
    operationGrants: {
        read: [
            {
                operations: ['syncCompanyOnHold'],
                on: [() => xtremSystem.nodes.Company],
            },
        ],
        create: [{ operations: ['syncCompanyOnHold'], on: [() => xtremSystem.nodes.Company] }],
        update: [{ operations: ['syncCompanyOnHold'], on: [() => xtremSystem.nodes.Company] }],
        delete: [{ operations: ['syncCompanyOnHold'], on: [() => xtremSystem.nodes.Company] }],
    },
});
