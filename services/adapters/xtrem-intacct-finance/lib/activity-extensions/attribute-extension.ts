import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';

export const attributeExtension = new ActivityExtension({
    extends: xtremFinanceData.activities.attribute,
    __filename,
    permissions: [],
    operationGrants: {
        manage: [{ operations: ['updateRecordNoOnEmployee'], on: [() => xtremFinanceData.nodes.Attribute] }],
    },
});
