import { Activity } from '@sage/xtrem-core';
import * as xtremIntacctFinance from '..';
import { cashManagerActivities } from '../functions/authorisations';

export const intacctBankAccountTransactionFeed = new Activity({
    description: 'Bank Manager',
    node: () => xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed,
    __filename,
    permissions: ['manage'],
    operationGrants: {
        manage: [
            ...cashManagerActivities,
            {
                // XT-80680 searchIntacctData removed during bank account refactor since cash book management will be removed
                operations: ['read', 'create', 'update', 'delete', 'setCleared', 'getMatchLine'],
                on: [() => xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed],
            },
        ],
    },
});
