import * as xtremCommunication from '@sage/xtrem-communication';
import { Activity } from '@sage/xtrem-core';
import { IntacctMap } from '../nodes';

export const intacctMap = new Activity({
    description: 'Map',
    node: () => IntacctMap,
    __filename,
    permissions: ['read', 'manage', 'synchronizationWithSageIntacct'],

    operationGrants: {
        read: [
            {
                operations: [
                    'getAvailableXtremObjectList',
                    'getObject',
                    'getIntacctTransactionsList',
                    'getDataIntacct',
                    'getStructure',
                    'getIntacctUrl',
                ],
                on: [() => IntacctMap],
            },
            {
                operations: ['lookup'],
                on: [() => xtremCommunication.nodes.SysNotificationState],
            },
        ],
        manage: [
            {
                operations: [
                    'read',
                    'create',
                    'update',
                    'delete',
                    'updateCustomMapping',
                    'writeStructure',
                    'deleteXtrem',
                    'getAvailableXtremObjectList',
                    'getObject',
                    'getIntacctTransactionsList',
                    'getDataIntacct',
                    'getStructure',
                    'getIntacctUrl',
                    'xtreemMassCreationJob',
                    'intacctMassCreationJob',
                ],
                on: [() => IntacctMap],
            },
            {
                operations: ['lookup'],
                on: [() => xtremCommunication.nodes.SysNotificationState],
            },
        ],
        synchronizationWithIntacct: [
            {
                operations: [
                    'read',
                    'createUpdateAllIntacct',
                    'xtreemMassCreationJob',
                    'getAvailableXtremObjectList',
                    'getObject',
                    'getIntacctTransactionsList',
                    'getDataIntacct',
                    'getStructure',
                    'getIntacctUrl',
                    'xtreemMassCreationJob',
                    'intacctMassCreationJob',
                ],
                on: [() => IntacctMap],
            },
            {
                operations: ['lookup'],
                on: [() => xtremCommunication.nodes.SysNotificationState],
            },
        ],
    },
});
