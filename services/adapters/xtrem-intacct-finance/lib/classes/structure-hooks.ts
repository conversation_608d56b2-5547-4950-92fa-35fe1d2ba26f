import type { Context } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremIntacctFinance from '..';

Object.assign(xtremStructure.classes.IntacctActivationOptionHooks, {
    async onEnabled(context: Context) {
        await xtremIntacctFinance.functions.IntacctActivationOption.intacctActivationOptionEnableControl(context);
        await xtremIntacctFinance.functions.IntacctOption.intacctOptionChangeUpdates(context, true);
    },
    async onDisabled(context: Context) {
        await xtremIntacctFinance.functions.IntacctActivationOption.intacctActivationOptionDisableControl(context);
        await xtremIntacctFinance.functions.IntacctOption.intacctOptionChangeUpdates(context, false);
    },
});
