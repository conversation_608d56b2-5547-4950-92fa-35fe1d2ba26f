import type * as xtremCommunication from '@sage/xtrem-communication';
import type { AnyValue, Context, Diagnose, NodeUpdateData, Reference, UpdateSetFunctionSet } from '@sage/xtrem-core';
import { BusinessRuleError, SystemError, ValidationSeverity, asyncArray } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import type { IntacctKey } from '@sage/xtrem-intacct/lib/interfaces/synchronization';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSynchronization from '@sage/xtrem-synchronization';
import { get, isArray, set } from 'lodash';
import * as xtremIntacctFinance from '..';
import { getMapInstance } from '../functions';
import { formatIntacctData } from '../functions/synchronization';
import type { SyncState } from '../interfaces/synchronization';
import { GenericIfunction } from './intacct/generic-ifunction';

export class IntacctSynchronizationManager {
    public intacctKey?: xtremIntacct.interfaces.IntacctKey;

    public synchronizationState: xtremIntacctFinance.nodes.IntacctSynchronizationState;

    public doNotThrow: boolean;

    getMapping(): Reference<xtremIntacctFinance.nodes.IntacctMap> {
        if (!this.synchronizationState) {
            throw new SystemError('IntacctSynchronizationManager - No synchronizationState');
        }
        return this.synchronizationState.getMapping();
    }

    async getIntacctId(): Promise<string> {
        if (await this.synchronizationState?.intacctId) {
            return this.synchronizationState?.intacctId;
        }
        return this.synchronizationState.getIntacctId();
    }

    mapping: xtremIntacctFinance.nodes.IntacctMap;

    nodeDescription: string;

    intacctId: string;

    typeRequest: 'create' | 'update' | 'no';

    /** Current synchronizing version */
    currentVersion: number;

    /** SynchronizationState where */
    /** Typing issue : 'NodeKey<IntacctSynchronizationState>' is not assignable to type
     * 'NodeOpFilter<SynchronizationState, SynchronizationState>'.
     * We can't use NodeOpFilter
     * We want to use this primary key to read & to bulkUpdate
     */
    async getWhere() {
        return {
            integration: '#intacct',
            sysId: await this.synchronizationState.sysId,
            node: await this.synchronizationState.node,
        };
    }

    static async createSyncState(context: Context, syncState: SyncState) {
        if (!syncState._id) {
            const nodeFactory = await context.read(xtremMetadata.nodes.MetaNodeFactory, { name: syncState.node });

            const intacctNode: xtremIntacctFinance.nodes.IntacctSynchronizationState | null =
                await IntacctSynchronizationManager.withWritableContext(context, async writableContext => {
                    const instance = (await writableContext.read(
                        await nodeFactory.getNode(),
                        { _id: syncState.sysId },
                        { forUpdate: true },
                    )) as xtremSynchronization.interfaces.SynchronizationNode;

                    const intacctNodeCreate = {};
                    set(intacctNodeCreate, `intacct${syncState.node}`, { sysId: instance._id.toString() });
                    await instance.$.set(intacctNodeCreate);
                    await instance.$.save();
                    const intacctSyncState =
                        (await instance.getSyncStateReference()) as xtremIntacctFinance.nodes.IntacctSynchronizationState;

                    if (!intacctSyncState) {
                        throw new SystemError('Cannot create IntacctSynchronizationManager');
                    }
                    return intacctSyncState;
                });

            return new IntacctSynchronizationManager(context).init({
                synchronizationState: intacctNode,
                doNotThrow: syncState.continueOnError,
            });
        }
        const synchronizationState = await context.read(xtremIntacctFinance.nodes.IntacctSynchronizationState, {
            _id: syncState._id,
        });
        return new IntacctSynchronizationManager(context).init({
            synchronizationState,
            doNotThrow: syncState.continueOnError,
        });
    }

    static create(
        context: Context,
        synchronizationState: xtremIntacctFinance.nodes.IntacctSynchronizationState,
    ): Promise<IntacctSynchronizationManager> {
        return new IntacctSynchronizationManager(context).init({ synchronizationState });
    }

    static async createFromIntacct(
        context: Context,
        intacct: { id: string; name: string },
    ): Promise<xtremIntacctFinance.classes.IntacctSynchronizationManager> {
        const mapInstance = await getMapInstance(context, intacct.name);

        const intacctNodeQuery = context.query(xtremIntacctFinance.nodes.IntacctSynchronizationState, {
            filter: {
                intacctId: intacct.id,
                node: await mapInstance.nodeFactory,
                integration: '#intacct',
            },
        });

        const intacctNode = await intacctNodeQuery.at(0);

        if (!intacctNode) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-intacct-finance/nodes__intacct-map/desynchronized_not_exist',
                    'Desynchronized {{node}}: {{name}} Sage Intacct ID: {{id}} could not be found.',
                    { ...intacct, node: await (await mapInstance.nodeFactory)?.title },
                ),
            );
        }
        return new IntacctSynchronizationManager(context).init({ synchronizationState: intacctNode });
    }

    private constructor(public readonly context: Context) {}

    protected async baseInit(synchronizationState: xtremIntacctFinance.nodes.IntacctSynchronizationState) {
        this.synchronizationState = synchronizationState;
        this.mapping = await this.synchronizationState.getMapping();
        this.nodeDescription = await (await this.mapping.nodeFactory).name;
        this.intacctId = await this.getIntacctId();
        this.currentVersion = await synchronizationState.version;
        this.intacctKey = await synchronizationState.checkExist();
    }

    protected async init(param: {
        synchronizationState: xtremIntacctFinance.nodes.IntacctSynchronizationState;
        doNotThrow?: boolean;
    }): Promise<this> {
        await this.baseInit(param.synchronizationState);
        this.doNotThrow = !!param.doNotThrow;

        this.typeRequest = (await this.mapping.canCreate) ? 'create' : 'no';

        if (this.intacctKey) {
            this.typeRequest = (await this.mapping.canUpdate) ? 'update' : this.typeRequest;
            if (this.intacctKey?.RECORDNO !== (await this.synchronizationState.recordNo)) {
                this.synchronizationState = await this.updateIntacctSynchronizationState({
                    recordNo: this.intacctKey?.RECORDNO,
                    intacctId: this.intacctId,
                });
            }
        }

        return this;
    }

    /**
     *  Issue :  i want to bulkUpdate xtremIntacctFinance.nodes.IntacctSynchronizationState,
     *  but i can't update properties from xtremSynchronization.nodes.SynchronizationState
     *  i get an sql error
     *  Jira ticket XT-52635 :
     *  When solved move to IntacctSynchronizationState node
     * @param set
     */
    async updateSynchronizationState(
        setPayload: UpdateSetFunctionSet<xtremSynchronization.nodes.SynchronizationState>,
    ) {
        await IntacctSynchronizationManager.withWritableContext(this.context, async writableContext => {
            const where = await this.getWhere();
            return writableContext.bulkUpdate(xtremSynchronization.nodes.SynchronizationState, {
                set: setPayload,
                where,
            });
        });
    }

    /** TODO : delete this when it will be fully refactored  */
    protected static withWritableContext<T extends AnyValue>(
        context: Context,
        body: (ctx: Context) => Promise<T>,
    ): Promise<T> {
        if (context.isWritable) {
            return body(context);
        }
        return context.runInWritableContext(writableContext => body(writableContext));
    }

    updateIntacctSynchronizationState(
        setPayload: NodeUpdateData<xtremIntacctFinance.nodes.IntacctSynchronizationState>,
    ) {
        return IntacctSynchronizationManager.withWritableContext(this.context, async writableContext => {
            const updateSynchronizationState = await writableContext.read(
                xtremIntacctFinance.nodes.IntacctSynchronizationState,
                await this.getWhere(),
                { forUpdate: true },
            );
            await updateSynchronizationState.$.set(setPayload);
            await updateSynchronizationState.$.save();
            return updateSynchronizationState;
        });
    }

    async getIntacctRequest(typeRequest: 'create' | 'update') {
        const requestManager = await GenericIfunction.create<xtremIntacct.interfaces.IntacctKey>(
            this.context,
            this.synchronizationState,
        );
        requestManager.typeRequest = typeRequest;
        return requestManager;
    }

    async checkIntacctId() {
        if ((await this.synchronizationState.intacctId) === '' && this.intacctId !== '') {
            return IntacctSynchronizationManager.withWritableContext(this.context, async writableContext => {
                this.synchronizationState = await writableContext.read(
                    xtremIntacctFinance.nodes.IntacctSynchronizationState,
                    await this.getWhere(),
                    { forUpdate: true },
                );
                await this.synchronizationState.$.set({ intacctId: this.intacctId });

                if (!(await this.synchronizationState.$.trySave())) {
                    const lastMessage = writableContext.diagnoses.map(diag => diag.message).join('\n');
                    await this.context.batch.logMessage('error', lastMessage);
                    await this.synchronizationState.$.set({ state: 'error', intacctId: '', lastMessage });
                    await this.synchronizationState.$.save();
                    return false;
                }
                return true;
            });
        }
        return true;
    }

    async executeIntacctQuery(): Promise<{ queryResult: xtremIntacct.interfaces.IntacctKey[]; diagnoses: Diagnose[] }> {
        if (this.typeRequest === 'no') {
            const message = `${await this.mapping.intacctDescription}: canCreate :${await this.mapping
                .canCreate} canUpdate:${await this.mapping
                .canUpdate} - will not execute creation & return ${JSON.stringify(this.intacctKey)}`;
            await this.context.batch.logMessage('test', message);
            return {
                queryResult: [...(this.intacctKey ? [this.intacctKey] : [])],
                diagnoses: [{ path: ['executeIntacctQuery'], severity: ValidationSeverity.warn, message }],
            };
        }

        const requestManager = await this.getIntacctRequest(this.typeRequest);
        const xmlRequest = await requestManager.xmlWithoutFunction(true);

        await this.context.batch.logMessage('result', `${this.typeRequest}: ${xmlRequest}`, { data: { xmlRequest } });
        const executeResult = await requestManager.execute();

        if (!executeResult || !isArray(executeResult)) {
            const lastMessage = requestManager.diagnosesMessages();
            await this.updateSynchronizationState({ state: 'error', lastMessage });

            await this.context.batch.logMessage('result', `Error : ${lastMessage}`, {
                data: {
                    diagnose: requestManager.diagnoses,
                },
            });

            /** If we throw an error there the transaction will be not commited  */
            if (this.doNotThrow || this.context.isWritable) {
                return { queryResult: [], diagnoses: requestManager.diagnoses };
            }
            throw new BusinessRuleError(lastMessage);
        }
        await this.context.batch.logMessage('info', `Response: \n ${JSON.stringify(executeResult)}`);

        return { queryResult: executeResult, diagnoses: requestManager.diagnoses };
    }

    /** get the values that are not returned by the create update intacct ( recordUrl ) */
    async queryAfter(key: IntacctKey): Promise<{ RECORD_URL: string; intacctId: string }> {
        const fields: xtremIntacct.interfaces.QueryFields[] = [{ name: 'RECORD_URL', type: 'select' }];
        const intacctField = this.synchronizationState.intacctIdField;
        fields.push({ name: intacctField, type: 'select' });

        const intacctquery = new xtremIntacct.classes.sdk.Functions.Query<{ RECORD_URL: string; intacctId: string }[]>(
            this.context,
            {
                objectName: await this.mapping.thirdPartyObjectName,
                fields,
                filter: new xtremIntacct.classes.sdk.Functions.QueryOperator.Filter('RECORDNO').equalTo(
                    key.RECORDNO.toString(),
                ),
                resultFunction: queryResult => {
                    if (!queryResult.data) {
                        return [];
                    }
                    return queryResult.data.map(line => {
                        return {
                            RECORD_URL: line.RECORD_URL,
                            intacctId: get(line, intacctField),
                        };
                    });
                },
            },
        );
        intacctquery.showPrivate = true;

        const result = await intacctquery.execute();

        if (!result.length) {
            throw new BusinessRuleError(`${JSON.stringify(result)}`);
        }

        return result[0];
    }

    async synchronize(): Promise<Partial<SyncState>> {
        await this.updateSynchronizationState({ state: 'pending' });
        /** Some upgrade set the intacctId to empty - fix  */
        if (!(await this.checkIntacctId())) {
            return { state: 'error' };
        }

        /** queryResult as only RECORDNO  when financeDocument    */
        const { queryResult, diagnoses } = await this.executeIntacctQuery();

        const [synchroniseResult] = queryResult;

        const { RECORD_URL, intacctId } = synchroniseResult
            ? await this.queryAfter(synchroniseResult)
            : { RECORD_URL: '', intacctId: '' };

        await this.context.batch.logMessage('result', `${intacctId} - get url: ${RECORD_URL}`, {
            data: { url: RECORD_URL },
        });

        const state: xtremCommunication.enums.IntegrationState = synchroniseResult ? 'success' : 'error';
        const synchronizationStateUpdate: NodeUpdateData<xtremIntacctFinance.nodes.IntacctSynchronizationState> = {
            ...(this.typeRequest === 'create' && synchroniseResult
                ? {
                      recordNo: synchroniseResult.RECORDNO,
                  }
                : undefined),
            ...(this.intacctId === '' && intacctId
                ? {
                      intacctId,
                  }
                : undefined),
            ...(RECORD_URL ? { url: RECORD_URL } : undefined),
            state,
            lastMessage: diagnoses.map(diag => diag.message).join('\n'),
        };

        this.synchronizationState = await this.updateIntacctSynchronizationState(synchronizationStateUpdate);

        if (this.currentVersion !== (await this.synchronizationState.version)) {
            await this.context.batch.logMessage(
                'info',
                `Version change: ${this.currentVersion}/${await this.synchronizationState.version} Restart`,
            );
            this.currentVersion = await this.synchronizationState.version;
            await this.synchronize();
        }

        return {
            _id: this.synchronizationState._id,
            intacctId: await this.synchronizationState.intacctId,
            state,
            sysId: await this.synchronizationState.sysId,
            diagnoses,
        };
    }

    async getIntacctData() {
        return new xtremIntacct.classes.sdk.Functions.ReadByName(this.context, {
            fields: [...(await this.mapping.intacctFields), ...this.synchronizationState.fromIntacctFields],
            objectName: await this.mapping.thirdPartyObjectName,
            filter: [this.intacctId || ''],
            resultFunction: result => result.data,
        }).execute();
    }

    async eventDesynchronized() {
        const intacctData = await this.getIntacctData();

        if (intacctData.length !== 1) {
            throw new BusinessRuleError(`${this.intacctId}: ${intacctData.length} records found`);
        }
        const fields = (await this.mapping.relationMapping)?.fields;
        if (!fields) {
            throw new BusinessRuleError(`${await this.mapping.thirdPartyObjectName}: No lookup properties `);
        }
        const intacctDataFormated = xtremIntacctFinance.functions.removeEmpty(
            formatIntacctData(intacctData[0], fields),
        );

        await this.synchronizationState.onDesynchronized(intacctData);

        const requestManager = await GenericIfunction.create<xtremIntacct.interfaces.IntacctKey>(
            this.context,
            this.synchronizationState,
        );

        const xtremData = await requestManager.xmlWithoutFunctionAsJson({});

        const objectDiff = xtremIntacctFinance.functions.allObjectPaths(
            xtremIntacctFinance.functions.deepDifference(xtremData, intacctDataFormated, [
                ...(await this.mapping.getIntacctWrileOnlyFields()),
                'RECORDNO',
                'RECORD_URL',
            ]),
        );

        const difference = await asyncArray(objectDiff)
            .map(async diff => {
                return {
                    name: `${diff} / ${await this.mapping.getXtremPropertyFromIntacctProperty(diff)}`,
                    thirdPartyValue: `${get(intacctDataFormated, diff)}`,
                    xtreemValue: `${get(xtremData, diff)}`,
                };
            })
            .toArray();

        if (difference.length) {
            this.context.logger.debug(() =>
                difference.map(diff => `${diff.name}: ${diff.thirdPartyValue}/${diff.xtreemValue}`).join('\n'),
            );
        } else {
            this.context.logger.debug(() => `${this.intacctId} Not desynchronized`);
        }

        await this.updateIntacctSynchronizationState({
            state: difference.length ? 'desynchronized' : 'success',
            difference: { differences: difference },
        });

        return difference;
    }
}
