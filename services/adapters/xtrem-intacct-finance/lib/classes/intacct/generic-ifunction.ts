import type { <PERSON><PERSON><PERSON><PERSON>, AnyValue, Collection, Context, Node } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, date, Logger, tryJsonParse } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import { get, isArray, set, toInteger } from 'lodash';
import * as XMLBuilder from 'xmlbuilder';
import * as xtremIntacctFinance from '../..';
import { isSyncState } from '../../functions/synchronization';
import type { SyncState } from '../../interfaces/synchronization';

/** Will be call by intacct synchronization manager to execute the intacct query  */
export class GenericIfunction<T extends AnyValue> extends xtremIntacct.classes.sdk.Functions.DefaultQuery<T[]> {
    static hideThisField = 'isHidden';

    typeRequest: 'create' | 'update';

    /** Intacct node  */
    synchronizationState: xtremIntacctFinance.nodes.IntacctSynchronizationState;

    /** Intacct node  */
    synchronizationStateLine: xtremSynchronization.interfaces.ThirdPartySynchronizationNodeLine;

    isLine: boolean;

    // Manage header or line
    getData() {
        return this.isLine ? this.referenceNodeLine : this.referenceNode;
    }

    /** Node to synchronize  */
    referenceNode: xtremSynchronization.interfaces.SynchronizationNode;

    /** Line to synchronize */
    referenceNodeLine: xtremSynchronization.interfaces.SynchronizationNodeLine;

    mapNode: xtremIntacctFinance.nodes.IntacctMap;

    public mappingObject: xtremIntacctFinance.sharedFunctions.interfaces.LookupObject;

    intacctKey: xtremIntacct.interfaces.IntacctKey;

    initLogger() {
        this.logger = Logger.getLogger(__filename, 'generic');
    }

    static create<T extends AnyValue>(
        context: Context,
        synchronizationState: xtremIntacctFinance.nodes.IntacctSynchronizationState,
    ): Promise<GenericIfunction<T>> {
        return new GenericIfunction<T>(context).init(synchronizationState);
    }

    static createLine<T extends AnyValue>(
        context: Context,
        line: {
            data: xtremSynchronization.interfaces.SynchronizationNodeLine;
            mapping: xtremIntacctFinance.nodes.IntacctMap;
        },
    ): Promise<GenericIfunction<T>> {
        return new GenericIfunction<T>(context).initLine(line);
    }

    protected async initLine(line: {
        data: xtremSynchronization.interfaces.SynchronizationNodeLine;
        mapping: xtremIntacctFinance.nodes.IntacctMap;
    }) {
        this.initLogger();
        this.isLine = true;

        this.referenceNodeLine = line.data;
        const synchronizationStateLine = await line.data.getSyncStateLine();
        if (!synchronizationStateLine) {
            throw new BusinessRuleError(
                `${await (await line.mapping.nodeFactory).name} ${line.data.$.factory.name} No intacct object line `,
            );
        }
        this.synchronizationStateLine = synchronizationStateLine;
        this.mapNode = line.mapping;
        this.mappingObject = await this.mapNode.relationMapping;
        await this.logger.debugAsync(async () => `${await (await line.mapping.nodeFactory).name} initLine `);
        return this;
    }

    protected async init(_synchronizationState: xtremIntacctFinance.nodes.IntacctSynchronizationState): Promise<this> {
        this.initLogger();

        this.isLine = false;
        this.synchronizationState = _synchronizationState;
        this.referenceNode = await this.synchronizationState.getSyncStateReference();
        this.mapNode = await this.synchronizationState.getMapping();
        this.mappingObject = await this.mapNode.relationMapping;
        this.options.entityId = await this.synchronizationState.entityId;

        this.intacctKey = {
            NAME: (await this.synchronizationState.intacctId) || (await this.synchronizationState.getIntacctId()),
            RECORDNO: (await this.synchronizationState.recordNo) || 0,
        };

        return this;
    }

    private constructor(public override readonly context: Context) {
        super(context, {
            synchronous: false,
            resultFunction: (result: any) => result.data,
        });
        this.typeRequest = 'create';
    }

    private async getValueAtPath(node: Node, path: string[]): Promise<AnyValue> {
        const [name, ...rest] = path;
        const value = (await node.$?.getValue(name)) || get(node, name);
        if ((value == null || value === '') && rest.length > 0) return undefined;
        return rest.length === 0 ? value : this.getValueAtPath(value as Node, rest);
    }

    /**
     * Get the value of the property from _nodeData
     * @param property
     * @param _nodeData Optionnal * if null we get the data from this.nodeData
     */
    async getValue(property: string): Promise<AnyValue> {
        const nodeData = this.getData();
        if (!nodeData) {
            throw new BusinessRuleError(
                ` Data not found ${property} mapping ${await (await this.mapNode.nodeFactory).title}} `,
            );
        }
        if (typeof property !== 'string') {
            throw new BusinessRuleError(`${property} Not found `);
        }
        const propertyPath = property.split('.');

        // TODO : manageDependencies that are not created into intacct  ( if last(propertyPath) === 'intacctId')

        return this.context.withLocalizedTextAsJson(async () => {
            /** There we can get values on both nodes, the reference & the subNode */
            const value = await this.getValueAtPath(nodeData, propertyPath);

            /** To handle localized text & always send en translation to intacct */
            if (typeof value === 'string') {
                const localizedString = tryJsonParse(value);
                return localizedString?.en ? localizedString.en : value;
            }
            return value;
        });
    }

    /**
     * format value for intacct create update
     * @param DATATYPE BOLLEAN, INTEGER DECIMAL STRING TEXT
     * @param xtremProperty property to get
     */
    private async format(DATATYPE: string, xtremProperty: string): Promise<AnyValue> {
        switch (DATATYPE) {
            case 'BOOLEAN':
                return (await this.getValue(xtremProperty)) ? 'true' : 'false';
            case 'CURRENCY':
            case 'DECIMAL':
                return Number(await this.getValue(xtremProperty));
            case 'INTEGER':
                return toInteger(await this.getValue(xtremProperty));
            case 'STRING':
            case 'TEXT':
            default:
                return this.getValue(xtremProperty);
        }
    }

    public override async writeXml(xml: xtremIntacct.classes.sdk.IaXmlWriter): Promise<void> {
        xml.writeStartElement('function');
        xml.writeAttribute('controlid', this.controlId, true);
        xml.writeStartElement(this.typeRequest);

        await this.writeXMLCore(xml, true); // Object

        xml.writeEndElement(); // create Or Update
        xml.writeEndElement(); // function
    }

    async writeIntacctIDField(
        xml: xtremIntacct.classes.sdk.IaXmlWriter,
        propIntacctId: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties,
    ) {
        const writeNull = false;
        if (this.typeRequest === 'update') {
            this.logger.debug(
                () =>
                    `Update property for ${propIntacctId.ID} will be set with intacctId : ${propIntacctId.xtremProperty}`,
            );
            xml.writeElement(propIntacctId.ID, await this.format(propIntacctId.DATATYPE, 'intacctId'), writeNull);
        } else {
            /** On creation we don't have the intacctID so we have to take the other field  */
            this.logger.debug(() => `Create property for ${propIntacctId.ID} is : ${propIntacctId.xtremProperty}`);
            xml.writeElement(
                propIntacctId.ID,
                await this.format(
                    propIntacctId.DATATYPE,
                    propIntacctId.xtremProperty
                        ?.split(',')
                        .filter(noIntacctID => noIntacctID.trim() !== 'intacctId')
                        .shift()
                        ?.trim() || '',
                ),
                writeNull,
            );
        }
    }

    async writeRelationShipPart(xml: xtremIntacct.classes.sdk.IaXmlWriter, writeNull = true) {
        const generateRelationShip = {};
        if (this.mappingObject.relationshipFields) {
            await asyncArray(this.mappingObject.relationshipFields.filter(property => property.xtremProperty)).forEach(
                async field => {
                    if (field.xtremProperty) {
                        set(generateRelationShip, field.ID, await this.format(field.DATATYPE, field.xtremProperty));
                    }
                },
            );
            this.logger.debug(() => `object relationShip : ${JSON.stringify(generateRelationShip)}`);
        }

        await this.objectXmlWrite(xml, generateRelationShip, writeNull);
    }

    /**
     * Generate the core of the object
     * @param xml IaXmlWriter
     * @param writeNull boolean true==> will write if value is null
     */
    public async writeXMLCore(xml: xtremIntacct.classes.sdk.IaXmlWriter, writeNull = true): Promise<void> {
        xml.writeStartElement(this.mappingObject.name); // CLASS

        /**
         * Rules : READONLY fields & RECORDNO id can't be created or updated
         * On update : the intacctID field must be use ( the )
         */
        await asyncArray(
            this.mappingObject.fields.filter(
                propFilter => propFilter.xtremProperty && propFilter.ID !== 'RECORDNO' && !propFilter.READONLY,
            ),
        )
            .filter(async propIntacctId => {
                const returnVal = propIntacctId.xtremProperty?.match(/^intacctId|[^.]intacctId/);
                if (returnVal) {
                    await this.writeIntacctIDField(xml, propIntacctId);
                }
                return !returnVal;
            })
            .forEach(field => this.writeField(xml, field, writeNull));

        await this.writeRelationShipPart(xml, writeNull);

        /** TODO : ADD LINES actually there is no lines on ITEM but we will have in others nodes keep this to remember */
        /**  keeping this:  await this.mapNode.lines.forEach(async line => this.writeXMLLines(xml, line));  keeping this */
        await this.mapNode.lines.forEach(line => this.writeXMLLines(xml, line, writeNull));

        xml.writeEndElement(); // CLASS
    }

    controlValidValues(field: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties, value: any) {
        // Check VALIDVALUES comming from intacct api
        if (
            field.VALIDVALUES &&
            Array.isArray(field.VALIDVALUES.VALIDVALUE) &&
            !field.VALIDVALUES.VALIDVALUE.some(validValue => validValue === value || validValue.match(value as string))
        ) {
            const error = `${this.mappingObject.name} - ${field.xtremProperty}= '${value}' isn't in validValues ${field.VALIDVALUES.VALIDVALUE}`;
            this.logger.error(() => error);
            throw new BusinessRuleError(error);
        }
    }

    public async writeField(
        xml: xtremIntacct.classes.sdk.IaXmlWriter,
        field: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties,
        writeNull = true,
    ): Promise<void> {
        this.logger.debug(() => `Looking for ${field.xtremProperty} to be create in ${field.ID}`);
        /** On Create Update we have only one xtremProperty corresponding to the intacct one ( we take the first one ) */
        const propertyArray = field.xtremProperty?.split(',');

        const mainIntacctProperty = propertyArray?.shift();
        if (mainIntacctProperty && (!field.CREATEONLY || ['create', 'read'].includes(this.typeRequest))) {
            const value = await this.format(field.DATATYPE, mainIntacctProperty);
            if (value === GenericIfunction.hideThisField) {
                return;
            }
            this.controlValidValues(field, value);

            this.logger.debug(
                () =>
                    `format function : ${mainIntacctProperty} ${field.DATATYPE} value : '${JSON.stringify(
                        value,
                    )}' (${typeof value}) for ${field.ID}`,
            );

            await this.writeElement(xml, {
                fieldId: field.ID,
                value,
                writeNull,
            });
            return;
        }
        this.logger.info(`writeField : Field ${field.ID} - ${field.DESCRIPTION} No xtreem property ! `);
    }

    // TODO : create an interface for this one
    private async writeElement(
        xml: xtremIntacct.classes.sdk.IaXmlWriter,
        element: {
            fieldId: string;
            value: any;
            writeNull?: boolean;
        },
    ): Promise<void> {
        const { fieldId, value, writeNull } = { writeNull: true, ...element };

        switch (typeof value) {
            case 'number':
            case 'string':
                xml.writeElement(fieldId, value.toString(), writeNull);
                break;
            case 'boolean':
                xml.writeElement(fieldId, value ? 'true' : 'false', writeNull);
                break;
            case 'object':
                if (value instanceof date) {
                    xml.writeElement(fieldId, value.format('MM/DD/YYYY'), writeNull);
                    return;
                }
                if (isSyncState(value)) {
                    await this.writeSyncState(xml, element);
                    return;
                }
                await this.objectXmlWrite(xml, value, writeNull);
                break;
            case 'undefined':
                break;
            default:
                xml.writeElement(fieldId, value, writeNull);
                break;
        }
    }

    /**
     * Generate the xml structure for the relationShipFields and json objects like CONTACT_LIST_INFO
     * @param xml IaXmlWriter
     * @param objectToWrite can be an array of obj or an array
     */
    public async objectXmlWrite(
        xml: xtremIntacct.classes.sdk.IaXmlWriter,
        objectToWrite: any,
        writeNull = true,
    ): Promise<void> {
        this.logger.debug(() => `objectXmlWrite - params :  ${JSON.stringify(objectToWrite)}`);

        if (isArray(objectToWrite)) {
            await asyncArray(objectToWrite).forEach(line => this.objectXmlWrite(xml, line, writeNull));
            return;
        }
        if (!objectToWrite) {
            return;
        }
        /** If we have dependencies  */
        if (isSyncState(objectToWrite)) {
            await this.writeSyncState(xml, { value: objectToWrite, writeNull });
        }

        await asyncArray(Object.keys(objectToWrite)).forEach(async fieldId => {
            const value = get(objectToWrite, fieldId);

            if (typeof value === 'object') {
                xml.writeStartElement(fieldId);
                if (isSyncState(value)) {
                    await this.writeSyncState(xml, { value, writeNull });
                } else {
                    await this.objectXmlWrite(xml, value, writeNull);
                }
                xml.writeEndElement();
            } else {
                await this.writeElement(xml, { fieldId, value, writeNull });
            }
        });
    }

    private async writeSyncState(
        xml: xtremIntacct.classes.sdk.IaXmlWriter,
        syncState: {
            fieldId?: string;
            value: SyncState;
            writeNull?: boolean;
        },
    ) {
        const checkSyncState =
            syncState.value.state !== 'success' ? await this.manageDependencie(syncState.value) : syncState.value;

        if (checkSyncState.state !== 'success' && !syncState.value.continueOnError) {
            throw new BusinessRuleError(`${JSON.stringify(checkSyncState, null, 4)} - Dependencie issue`);
        }

        if (syncState.fieldId) {
            xml.writeStartElement(syncState.fieldId);
        }

        xml.writeString(checkSyncState.intacctId || '');

        if (syncState.fieldId) {
            xml.writeEndElement();
        }
    }

    /**
     *
     * @param xml xml to write in
     * @param lineObject MapLine containing info to link
     */
    public async writeXMLLines(
        xml: xtremIntacct.classes.sdk.IaXmlWriter,
        lineObject: xtremIntacctFinance.nodes.MapLine,
        writeNull: boolean,
    ): Promise<void> {
        const collection = get(
            this.referenceNode,
            await lineObject.propertyLine,
        ) as Collection<xtremSynchronization.interfaces.SynchronizationNodeLine>;

        await this.logger.debugAsync(
            async () => `write ${await lineObject.collectionName} lines ${(await collection.length) || 0} `,
        );

        xml.writeStartElement(await lineObject.collectionName); // LINES NAME

        await collection
            .filter(async dataLineFilter => {
                const syncLine = await dataLineFilter.getSyncStateLine();
                return !(await syncLine?.excludeRecord);
            })
            .forEach(async data => {
                const auto = await xtremIntacctFinance.classes.intacct.GenericIfunction.createLine(this.context, {
                    data,
                    mapping: await lineObject.line,
                });
                return auto.writeXMLCore(xml, writeNull);
            });

        xml.writeEndElement(); // LINES NAME
    }

    async manageDependencie(
        syncState: xtremIntacctFinance.interfaces.synchronization.SyncState,
    ): Promise<Partial<xtremIntacctFinance.interfaces.synchronization.SyncState>> {
        this.logger.debug(() => `Managing depencies ${JSON.stringify(syncState)}`);
        const depencie = syncState.sysId
            ? await this.context.tryRead(xtremIntacctFinance.nodes.IntacctSynchronizationState, {
                  integration: '#intacct',
                  node: `#${syncState.node}`,
                  sysId: syncState.sysId.toString(),
              })
            : null;

        if (depencie && (await depencie.state) === 'success') {
            return {
                intacctId: await depencie.intacctId,
                state: 'success',
            };
        }
        await this.context.batch.logMessage(
            'info',
            `${syncState.node} - ${syncState.sysId} not find - creating intacct node `,
        );

        return (
            await xtremIntacctFinance.classes.IntacctSynchronizationManager.createSyncState(this.context, syncState)
        ).synchronize();
    }

    async xmlWithoutFunctionAsJson(option: { writeNull?: boolean }): Promise<AnyValue> {
        const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));
        await this.writeXMLCore(xml, !!option.writeNull); // Object
        return get((await xml.flushToJson()).request as AnyRecord, await this.mapNode.id);
    }
}
