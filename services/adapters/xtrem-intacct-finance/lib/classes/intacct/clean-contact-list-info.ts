import type { AnyValue, Context } from '@sage/xtrem-core';
import { SystemError } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';

/**
 * Lookup intacct api :
 *
 */
export class CleanContactListInfo<T extends AnyValue> extends xtremIntacct.classes.sdk.Functions.DefaultQuery<T> {
    intacctIDName = 'CUSTOMERID';

    constructor(
        public override context: Context,
        public id: string,
        public objectName: 'CUSTOMER' | 'VENDOR' = 'CUSTOMER',
    ) {
        super(context);
        if (!id) {
            throw new SystemError('No id for CleanContactListInfo function');
        }
        this.intacctIDName = objectName === 'CUSTOMER' ? 'CUSTOMERID' : 'VENDORID';
    }

    public override writeXml(xml: xtremIntacct.classes.sdk.IaXmlWriter): void {
        xml.writeStartElement('function');
        xml.writeAttribute('controlid', this.controlId, true);

        xml.writeStartElement('update');
        xml.writeStartElement(this.objectName);

        xml.writeElement(this.intacctIDName, this.id, true);
        xml.writeElement('CONTACT_LIST_INFO', null, true);

        xml.writeEndElement(); // CUSTOMER OR SUPPLIER
        xml.writeEndElement(); // read

        xml.writeEndElement(); // function
    }
}
