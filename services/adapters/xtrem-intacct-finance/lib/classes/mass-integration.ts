import type { Context, NodeQueryFilter } from '@sage/xtrem-core';
import { asyncArray, datetime, SystemError } from '@sage/xtrem-core';
import type * as xtremIntacct from '@sage/xtrem-intacct';
import type * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSynchronization from '@sage/xtrem-synchronization';
import { set } from 'lodash';
import * as xtremIntacctFinance from '..';
import { getMapInstance } from '../functions';

export class MassIntegration extends xtremSynchronization.classes.SynchronizationManager {
    isNewImplemtation: boolean;

    intacctName: string;

    mapInstance: xtremIntacctFinance.nodes.IntacctMap;

    xtremNode: typeof xtremIntacct.nodes.IntacctNode;

    timeStarted: datetime;

    numberOfLinesToProcess() {
        return this.context.queryCount(this.xtremNode);
    }

    async getAllSysIdsToIntegrate(_filter?: NodeQueryFilter<xtremIntacct.nodes.IntacctNode>) {
        const filter = _filter || {};
        return (await this.context.select(this.xtremNode, { _id: true }, { filter })).map(inst => inst._id);
    }

    async getIntacctNode(sysId: string): Promise<xtremIntacctFinance.nodes.IntacctSynchronizationState | undefined> {
        const nodeIntacct = await this.context.tryRead(xtremIntacctFinance.nodes.IntacctSynchronizationState, {
            integration: '#intacct',
            sysId,
            node: `#${this.xtremNode.name}`, // Will be change to mapping.node one it be available
        });
        if (nodeIntacct) {
            return nodeIntacct;
        }
        /** The intacct node can not be create, create it */
        return this.createIntacctNode(sysId);
    }

    /** Will not launch integration on save when creating intacctNode */
    createIntacctNode(sysId: string): Promise<xtremIntacctFinance.nodes.IntacctSynchronizationState | undefined> {
        return this.context.runInWritableContext(
            async writableContext => {
                const instance = await writableContext.read(this.xtremNode, { _id: sysId }, { forUpdate: true });
                const intacctPayload = {};
                set(intacctPayload, `intacct${this.xtremNode.name}`, { sysId });
                await instance.$.set(intacctPayload);
                const isIntacctNodeCreated = await instance.$.trySave();

                if (!isIntacctNodeCreated) {
                    await this.context.batch.logMessage(
                        'error',
                        `Can't save node: ${writableContext.diagnoses.map(diagnose => diagnose.message).join('\n')} `,
                    );
                    return undefined;
                }
                const node = `#${this.xtremNode.name}`;

                return writableContext.read(xtremIntacctFinance.nodes.IntacctSynchronizationState, {
                    integration: '#intacct',
                    sysId,
                    node,
                });
            },
            // To not launch intacct import when creating intacctNode V0
            { source: 'import' },
        );
    }

    protected async init(_intacctName: string): Promise<this> {
        this.intacctName = _intacctName;
        this.isNewImplemtation = ['ITEM', 'CUSTOMER', 'SUPPLIER', 'VENDOR', 'CONTACT'].includes(_intacctName);
        /** We only need the mapInstance to get xtremNode ?  */
        const mapInstance = await getMapInstance(this.context, _intacctName);
        this.xtremNode = await mapInstance.xtremNode;

        this.timeStarted = (await this.context.batch.getProgress()).startTime;
        this.progress = { errorCount: 0, successCount: 0, totalCount: 0 };

        return this;
    }

    constructor(
        override readonly context: Context,
        override readonly parameters: {
            nodeFactory: xtremMetadata.nodes.MetaNodeFactory;
            integration: xtremSynchronization.nodes.ThirdPartyApplication;
            xtremNode: typeof xtremIntacct.nodes.IntacctNode;
        },
    ) {
        super(context, parameters);
    }

    static async create(context: Context, intacctName: string): Promise<MassIntegration> {
        const mapInstance = await getMapInstance(context, intacctName);
        const nodeFactory = await mapInstance.nodeFactory;
        const integration = await context.read(xtremSynchronization.nodes.ThirdPartyApplication, { _id: '#intacct' });
        if (!nodeFactory) {
            throw new SystemError(`No nodefactory ${await mapInstance.id}`);
        }

        return new MassIntegration(context, {
            integration,
            nodeFactory,
            xtremNode: await mapInstance.xtremNode,
        }).init(intacctName);
    }

    /** Will create All Intacct Node  */
    async startAllXtreem(filter?: NodeQueryFilter<xtremIntacct.nodes.IntacctNode>) {
        this.progress.totalCount = await this.numberOfLinesToProcess();
        this.progress.detail = await this.parameters.nodeFactory.title;

        await this.context.batch.updateProgress({
            ...this.progress,
            phase: 'start',
        });

        await this.context.batch.logMessage(
            'info',
            this.context.localize(
                '@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-number-of-lines',
                '{{numberOfLines}} to create/update.',
                { numberOfLines: this.progress.totalCount },
            ),
        );

        const sysIds = await this.getAllSysIdsToIntegrate(filter);

        if (!this.progress.successCount) {
            this.progress.successCount = 0;
        }

        await asyncArray(sysIds).forEachParallel(2, async _id => {
            if (await this.context.batch.isStopRequested()) {
                return;
            }

            try {
                const nodeIntacct = await this.getIntacctNode(_id.toString());
                if (nodeIntacct) {
                    await (
                        await xtremIntacctFinance.classes.IntacctSynchronizationManager.create(
                            this.context,
                            nodeIntacct,
                        )
                    ).synchronize();
                    this.progress.successCount = (this.progress.successCount || 0) + 1;
                }
            } catch (ex) {
                await this.context.batch.logMessage('error', ex.message);
                this.progress.errorCount = (this.progress.errorCount || 0) + 1;
            }
            if (((this.progress.successCount || 0) + (this.progress.errorCount || 0)) % 10) {
                await this.context.batch.updateProgress(this.progress);
            }
        });
        await this.context.batch.logMessage(
            'info',
            this.context.localize(
                '@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end',
                'Mass creation of {{intacctName}} as finish at {{dateTime}}. \n {{errorCount}}errors, {{successCount}} success /{{totalCount}} total ',
                { intacctName: this.intacctName, dateTime: datetime.now().toString(), ...this.progress },
            ),
        );

        this.progress.phase = this.progress.errorCount
            ? this.context.localize(
                  '@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end-with-errors-phase',
                  'Finished with errors.',
              )
            : this.context.localize(
                  '@sage/xtrem-intacct-finance/nodes__intacct-map/intacct-mass-creation-end-without-errors-phase',
                  'Finished.',
              );

        await this.context.batch.updateProgress(this.progress);
    }

    override async synchronize(
        synchronizationStateSysId: number,
    ): Promise<xtremSynchronization.functions.SynchronizationReturn> {
        const nodeIntacct = await this.context.read(xtremIntacctFinance.nodes.IntacctSynchronizationState, {
            _id: synchronizationStateSysId,
        });
        if (!nodeIntacct) {
            throw new SystemError(
                `${await this.parameters.nodeFactory
                    .title} No ${synchronizationStateSysId} on IntacctSynchronizationState `,
            );
        }
        const synchronizationManager = await xtremIntacctFinance.classes.IntacctSynchronizationManager.create(
            this.context,
            nodeIntacct,
        );
        const { state } = await synchronizationManager.synchronize();
        return { state };
    }
}
