import type { Context } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import { intacctOptionChangeUpdates } from '../functions/intacct-option-management-extension';

Object.assign(xtremIntacct.classes.IntacctOptionHooks, {
    async onEnabled(context: Context) {
        await intacctOptionChangeUpdates(context, true);
    },
    async onDisabled(context: Context) {
        await intacctOptionChangeUpdates(context, false);
    },
});
