import type { Currency } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<IntacctBankAccountTransactionFeed>({
    title: 'Transaction feed',
    objectTypeSingular: 'Bank account transaction feed',
    objectTypePlural: 'Bank account transaction feeds',
    idField() {
        return this.description;
    },
    node: '@sage/xtrem-intacct-finance/IntacctBankAccountTransactionFeed',
    mode: 'default',
    module: 'xtrem-intacct-finance',
    navigationPanel: {
        listItem: {
            line2: ui.nestedFields.text({ bind: 'intacctId', title: 'ID', canFilter: true }),
            title: ui.nestedFields.text({ bind: 'description', title: 'Description', canFilter: true }),
        },
        optionsMenu: [
            {
                title: 'All',
                page: '@sage/xtrem-intacct-finance/IntacctBankAccountTransactionFeed',
            },
        ],
    },
    access: {
        node: '@sage/xtrem-intacct-finance/IntacctBankAccountTransactionFeed',
    },
})
export class IntacctBankAccountTransactionFeed extends ui.Page {
    @ui.decorators.section<IntacctBankAccountTransactionFeed>({
        title: 'General',
        isOpen: true,
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainSection;
        },
        title: 'Transaction',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'ID',
        isReadOnly: true,
    })
    intacctId: ui.fields.Text;

    @ui.decorators.textField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Number',
        isReadOnly: true,
    })
    number: ui.fields.Text;

    @ui.decorators.textField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Financial entity',
        isReadOnly: true,
    })
    entity: ui.fields.Text;

    @ui.decorators.textField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Financial entity name',
        isReadOnly: true,
    })
    entityName: ui.fields.Text;

    @ui.decorators.textField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Financial account type',
        isReadOnly: true,
    })
    accountType: ui.fields.Text;

    @ui.decorators.textField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Financial account feed key',
        isReadOnly: true,
    })
    accountFeedKey: ui.fields.Text;

    @ui.decorators.textField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Transaction ID',
        isReadOnly: true,
    })
    transactionId: ui.fields.Text;

    @ui.decorators.textField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Bank account reconciliation key',
        isReadOnly: true,
    })
    accountReconKey: ui.fields.Text;

    @ui.decorators.dateField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Posting date',
        isReadOnly: true,
    })
    postingDate: ui.fields.Date;

    @ui.decorators.dateField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Reconciliation date',
        isReadOnly: true,
    })
    reconcilitationDate: ui.fields.Date;

    @ui.decorators.textField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Transaction type',
        isReadOnly: true,
    })
    transactionType: ui.fields.Text;

    @ui.decorators.textField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Document type',
        isReadOnly: true,
    })
    documentType: ui.fields.Text;

    @ui.decorators.textField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Document number',
        isReadOnly: true,
    })
    documentNumber: ui.fields.Text;

    @ui.decorators.textField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Payee',
        isReadOnly: true,
    })
    payee: ui.fields.Text;

    @ui.decorators.numericField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Amount',
        isReadOnly: true,
    })
    amount: ui.fields.Numeric;

    @ui.decorators.numericField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Amount to match',
        isReadOnly: true,
    })
    amountToMatch: ui.fields.Numeric;

    @ui.decorators.textField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Description',
        isReadOnly: true,
    })
    description: ui.fields.Text;

    @ui.decorators.textField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Cleared',
        isReadOnly: true,
    })
    cleared: ui.fields.Text;

    @ui.decorators.referenceField<IntacctBankAccountTransactionFeed, Currency>({
        parent() {
            return this.mainBlock;
        },
        title: 'Currency',
        node: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        width: 'small',
        isReadOnly: true,
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.numericField<IntacctBankAccountTransactionFeed>({
        parent() {
            return this.mainBlock;
        },
        title: 'Feed type',
        isReadOnly: true,
    })
    feedType: ui.fields.Numeric;
}
