import type { Account } from '@sage/xtrem-finance-data-api';
import type { Currency } from '@sage/xtrem-master-data-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<BankAccount>({
    title: 'Bank account',
    objectTypeSingular: 'Bank account',
    objectTypePlural: 'Bank accounts',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-finance-data/BankAccount',
    mode: 'default',
    module: 'xtrem-finance-data',
    navigationPanel: {
        listItem: {
            line2: ui.nestedFields.text({ bind: 'id' }),
            title: ui.nestedFields.text({ bind: 'name' }),
            line3: ui.nestedFields.text({ bind: 'megaEntityId', title: 'Entity ID' }),
        },
        optionsMenu: [
            {
                title: 'All',
                page: '@sage/xtrem-finance-data/BankAccount',
            },
        ],
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
        this.isCreateMode = !this._id.value;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
    },
})
export class BankAccount extends ui.Page {
    isCreateMode: boolean;

    @ui.decorators.section<BankAccount>({
        title: 'General',
        isOpen: true,
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<BankAccount>({
        parent() {
            return this.mainSection;
        },
        title: 'Bank account information',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<BankAccount>({
        title: 'ID',
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.switchField<BankAccount>({
        parent() {
            return this.mainBlock;
        },
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<BankAccount>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorFromIsActive: ui.fields.Separator;

    @ui.decorators.textField<BankAccount>({
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
        maxLength: 50,
        isMandatory: true,
        width: 'medium',
        isReadOnly() {
            return !this.isCreateMode;
        },
    })
    name: ui.fields.Text;

    @ui.decorators.textField<BankAccount>({
        parent() {
            return this.mainBlock;
        },
        title: 'ID',
        isMandatory: true,
        isReadOnly() {
            return !this.isCreateMode;
        },
    })
    id: ui.fields.Text;

    @ui.decorators.referenceField<BankAccount, Account>({
        parent() {
            return this.mainBlock;
        },
        title: 'Account',
        node: '@sage/xtrem-finance-data/account',
        valueField: 'name',
        width: 'small',
        isReadOnly() {
            return !this.isCreateMode;
        },
    })
    account: ui.fields.Reference<Account>;

    @ui.decorators.referenceField<BankAccount, Currency>({
        parent() {
            return this.mainBlock;
        },
        title: 'Currency',
        width: 'medium',
        node: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select currency',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ISO 4217 code', bind: 'id' }),
            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol' }),
            ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
        ],
        filter() {
            return { isActive: { _eq: true } };
        },
        isReadOnly() {
            return !this.isCreateMode;
        },
    })
    currency: ui.fields.Reference<Currency>;

    /** Intacct part  */

    @ui.decorators.textField<BankAccount>({
        parent() {
            return this.mainBlock;
        },
        title: 'Entity ID',
        isReadOnly: true,
    })
    megaEntityId: ui.fields.Text;
}
