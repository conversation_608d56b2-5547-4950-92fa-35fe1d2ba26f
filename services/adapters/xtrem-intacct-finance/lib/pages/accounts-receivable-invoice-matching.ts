import type { AccountsReceivableAdvance, AccountsReceivableInvoiceLine } from '@sage/xtrem-finance-api';
import type { Account, BankAccount, PostingStatus, TargetDocumentType } from '@sage/xtrem-finance-data-api';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type { GraphApi } from '@sage/xtrem-intacct-finance-api';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import * as ui from '@sage/xtrem-ui';
import type {
    IntacctArInvoice,
    MatchingCriteria,
    ParametersForArInvoiceMatching,
} from '../client-functions/interfaces/bank-statement';
import { BankAccountMatching } from '../shared-functions/index';
import type {
    ApArInvoicePaymentType,
    ApArPaymentCreationData,
    ApArPaymentMatchingData,
    ArInvoiceMatchingPageResult,
    ArMatch,
    IntacctBankAccountTransactionFeedArPayment,
    IntacctCustomer,
    MatchedArInvoice,
} from '../shared-functions/interfaces';

@ui.decorators.page<AccountsReceivableInvoiceMatching>({
    title: 'Accounts receivable invoice matching',
    mode: 'default',
    isTransient: true,
    skipDirtyCheck: true,
    access: {
        node: '@sage/xtrem-intacct-finance/IntacctBankAccountTransactionFeed',
    },
    businessActions() {
        return [this.cancel, this.ok, this.createArPayment, this.createArAdvance];
    },
    async onLoad() {
        const parameters = this.$.queryParameters as ParametersForArInvoiceMatching;
        if (parameters) {
            this.bankAccount.value = JSON.parse(parameters.bankAccount || '') || {};
            this.bankFeedAmount = parameters.amountToMatch || 0;
            this.bankFeedDescription = parameters.description || '';
            this.bankFeedPayee = parameters.payee || '';
            this.postingDate = parameters.postingDate || '';
            this.usedArInvoices = JSON.parse(parameters.usedArInvoices || '');
            if (
                parameters.arInvoicesAlreadySelected &&
                JSON.parse(parameters.arInvoicesAlreadySelected || '').matchedArInvoices?.length
            ) {
                this.arInvoicesAlreadySelected = JSON.parse(
                    parameters.arInvoicesAlreadySelected || '',
                ).matchedArInvoices;
            } else {
                this.arInvoicesAlreadySelected = [];
            }
            this.arMatch = JSON.parse(parameters.arMatch || '');
            this.arPaymentAdvanceData.payToCustomerId = parameters.payToCustomerId || '';
            this.arPaymentAdvanceData.payToCustomerName = parameters.payToCustomerName || '';
            this.arPaymentAdvanceData.paymentMethod = parameters.paymentMethod || '';
            this.arPaymentAdvanceData.paymentDate = parameters.paymentDate || '';
            this.arPaymentAdvanceData.receiptDate = parameters.receiptDate || '';
            this.arPaymentAdvanceData.account = parameters.account || '{}';
            this.arPaymentAdvanceData.storedAttributes = parameters.storedAttributes;
            this.arPaymentAdvanceData.storedDimensions = parameters.storedDimensions;
            this.arPaymentAdvanceCreationData.siteId = this.bankAccount.value?.financialSite?._id
                ? +this.bankAccount.value.financialSite._id
                : 0;
            this.arPaymentAdvanceCreationData.currencyId = this.bankAccount.value?.currency?._id
                ? +this.bankAccount.value.currency._id
                : 0;
            this.arPaymentAdvanceCreationData.bankFeedId = parameters.bankFeedId || 0;
            this.financeIntegrationStatus = parameters.financeIntegrationStatus || 'notPosted';
            this.financeDocumentCreatedNumber = parameters.financeDocumentCreatedNumber || '';
            this.financeDocumentCreatedSysId = parameters.financeDocumentCreatedSysId || 0;
            this.financeDocumentGenerationErrorMessage = parameters.financeDocumentGenerationErrorMessage || '';
            this.chartOfAccountSysId = parameters.chartOfAccountSysId || '';
            if (!['generated', 'postingInProgress', 'postingError', 'posted'].includes(this.financeIntegrationStatus)) {
                this.readOnlyMode = false;
            }
            this.financeIntegrationAppUrl = parameters.financeIntegrationAppUrl || '';
            this.financeIntegrationAppRecordId = parameters.financeIntegrationAppRecordId || '';
        }
        this.$.loader.isHidden = false;
        await this.init();
        this.$.loader.isHidden = true;
    },
})
export class AccountsReceivableInvoiceMatching extends ui.Page<GraphApi> {
    // ar match object from the bank feed. We may set the arPaymentType and the matching reasons
    arMatch: ArMatch;

    // bank feed posting date, to use as default date
    postingDate: string;

    // bank feed amount to match with ar invoices or create advance
    bankFeedAmount: number;

    // bank feed description to select the eligible ar invoices
    bankFeedDescription: string;

    // bank feed payee to select the eligible ar invoices
    bankFeedPayee: string;

    // the result of the ar invoice search done on init
    availableArOutstandingInvoices: IntacctArInvoice[];

    // if we have a match by invoiceNo, to inform the user
    matchedInvoiceNo: string;

    // if we have a match by customerId , to inform the user
    matchedCustomerId: string;

    // if we have a match by customerName, to inform the user
    matchedCustomerName: string;

    // when we don't have a customerId, the selectedCustomerId wil be the customerId from the first ar invoice selected.
    // This will then be used to filter all the other records (we want the user to select invoices only from one customer)
    selectedCustomerId: string;

    // when we don't have a customerId, the selectedCustomerName wil be the customerName from the first ar invoice selected.
    selectedCustomerName: string;

    // ar invoices previously selected for this bank feed
    arInvoicesAlreadySelected: MatchedArInvoice[];

    // ar invoices previously selected on all the other bank feed records. We need to exclude this ones so that we cannot
    // select the same record on different bank feed records.
    usedArInvoices: number[];

    // an array of all intacct customers (id and name)
    intacctCustomers: IntacctCustomer[];

    // an array of all intacct customer names
    intacctCustomersNames: string[];

    // Data entered by the user for the AR payment/advance creation - begin
    arPaymentAdvanceData: ApArPaymentMatchingData = {
        payToCustomerId: '',
        payToCustomerName: '',
        paymentMethod: '',
        paymentDate: '',
        receiptDate: '',
        account: '{}',
        storedAttributes: '{}',
        storedDimensions: '{}',
        targetDocumentType: 'accountsReceivablePayment',
    };
    // Data entered by the user for the AR payment/advance creation - end

    // Data sent by the intacct feed for the AR payment/advance creation - begin
    arPaymentAdvanceCreationData: ApArPaymentCreationData = {
        siteId: 0,
        currencyId: 0,
        bankFeedId: 0,
    };
    // Data sent by the intacct feed for the AR payment/advance creation - end

    financeIntegrationStatus: PostingStatus;

    financeDocumentCreatedNumber: string;

    financeDocumentCreatedSysId: number;

    financeDocumentGenerationErrorMessage: string;

    financeIntegrationAppUrl: string;

    financeIntegrationAppRecordId: string;

    targetDocumentType: TargetDocumentType;

    readOnlyMode: boolean = true;

    chartOfAccountSysId: string;

    @ui.decorators.section<AccountsReceivableInvoiceMatching>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<AccountsReceivableInvoiceMatching>({
        parent() {
            return this.mainSection;
        },
        title: 'General',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.referenceField<AccountsReceivableInvoiceMatching, BankAccount>({
        parent() {
            return this.mainBlock;
        },
        node: '@sage/xtrem-finance-data/BankAccount',
        valueField: 'id',
        title: 'Bank account',
        isDisabled: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-system/Site',
                valueField: '_id',
                bind: 'financialSite',
                isHidden: true,
                columns: [],
            }),
            ui.nestedFields.text({ bind: 'megaEntityId', isHidden: true }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-master-data/Currency',
                valueField: '_id',
                bind: 'currency',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.text({ bind: 'decimalDigits' }),
                ],
            }),
        ],
    })
    bankAccount: ui.fields.Reference<BankAccount>;

    @ui.decorators.numericField<AccountsReceivableInvoiceMatching>({
        parent() {
            return this.mainBlock;
        },
        title: 'Amount to match',
        bind: 'amountToMatch',
        scale: 2,
        isDisabled: true,
    })
    amountToMatch: ui.fields.Numeric;

    @ui.decorators.separatorField<AccountsReceivableInvoiceMatching>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    matchingReasonSeparator: ui.fields.Separator;

    @ui.decorators.textField<AccountsReceivableInvoiceMatching>({
        parent() {
            return this.mainBlock;
        },
        title: 'Matching reason',
        isTitleHidden: true,
        isDisabled: true,
        width: 'large',
    })
    matchingReason: ui.fields.Text;

    @ui.decorators.tableField<AccountsReceivableInvoiceMatching>({
        parent() {
            return this.mainSection;
        },
        isDisabled() {
            return this.readOnlyMode;
        },
        title: 'Invoices',
        orderBy: { dueDate: +1, invoiceNo: +1 },
        isTransient: true,
        canSelect: true,
        pageSize: 10,
        columns: [
            ui.nestedFields.numeric({
                bind: 'recordNo',
                isHidden: true,
            }),
            ui.nestedFields.text({
                title: 'Invoice number',
                bind: 'invoiceNo',
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Customer name',
                bind: 'customerName',
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Customer ID',
                bind: 'customerId',
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Reference number',
                bind: 'referenceNumber',
                isDisabled: true,
            }),
            ui.nestedFields.numeric({
                title: 'Amount to match',
                bind: 'amountToMatch',
                scale: 2,
                isDisabled: true,
            }),
            ui.nestedFields.numeric({
                title: 'Amount due',
                bind: 'totalDue',
                scale: 2,
                isDisabled: true,
            }),
            ui.nestedFields.date({
                title: 'Invoice date',
                bind: 'date',
                isDisabled: true,
            }),
            ui.nestedFields.date({
                title: 'Due date',
                bind: 'dueDate',
                isDisabled: true,
            }),
            ui.nestedFields.reference<AccountsReceivableInvoiceMatching>({
                title: 'Term',
                node: '@sage/xtrem-master-data/PaymentTerm',
                isDisabled: true,
                valueField: 'name',
                bind: 'term',
                minLookupCharacters: 1,
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'description' })],
            }),
            ui.nestedFields.numeric({
                title: 'Total amount',
                bind: 'totalAmount',
                scale: 2,
                isDisabled: true,
            }),
            ui.nestedFields.numeric({
                title: 'Total paid',
                bind: 'totalPaid',
                scale: 2,
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Entity ID',
                bind: 'entityId',
                isDisabled: true,
            }),
        ],
        onRowSelected(recordId, rowItem) {
            let bankFeedAmountToMatch = this.amountToMatch.value || 0;
            // isValidCustomerId controls that we don't mix customers
            if (bankFeedAmountToMatch > 0 && this.isValidCustomerId(rowItem.customerId)) {
                // if there's not a selectedCustomerId we set this value and we remove from the grid all lines from other customers
                if (!this.selectedCustomerId) {
                    this.selectedCustomerId = rowItem.customerId;
                    this.selectedCustomerName = rowItem.customerName;
                    this.invoices.value
                        .filter(invoice => invoice.customerId !== rowItem.customerId)
                        .forEach(invoice => this.invoices.removeRecord(invoice._id));
                    this.invoices.selectRecord(recordId);
                }
                // we update the amounts (the remaining to match, the amounts on the line and the payment amounts)
                const arInvoiceAmountToMatch =
                    bankFeedAmountToMatch >= rowItem.totalDue ? rowItem.totalDue : bankFeedAmountToMatch;
                bankFeedAmountToMatch -= arInvoiceAmountToMatch;
                rowItem.amountToMatch = arInvoiceAmountToMatch;
                this.invoices.addOrUpdateRecordValue(rowItem);
                this.amountToMatch.value = bankFeedAmountToMatch;

                this.setPaymentToCreate();
            } else {
                // if we already have the amount totally matched or the customer is not valid
                // the line cannot be selected
                this.invoices.unselectRecord(recordId);
            }
        },
        async onRowUnselected(recordId, rowItem) {
            // we update the amounts (the remaining to match, the amounts on the line and the payment amounts)
            this.amountToMatch.value = (this.amountToMatch.value || 0) + rowItem.amountToMatch;
            rowItem.amountToMatch = 0;
            this.invoices.addOrUpdateRecordValue(rowItem);
            this.setPaymentToCreate();
            // if there is no selected lines we show all availableArOutstandingInvoices again
            // and we prepare an advance to be created
            if (!this.invoices.selectedRecords.length) {
                this.selectedCustomerId = '';
                this.selectedCustomerName = '';
                this.availableArOutstandingInvoices
                    .filter(outstandingInvoice => outstandingInvoice.customerId !== rowItem.customerId)
                    .forEach(outstandingInvoice => this.invoices.addRecord(outstandingInvoice));
                if (!(this.intacctCustomers && this.intacctCustomers.length)) {
                    await this.getIntacctCustomerList();
                }
                this.advanceToCreate.value = [];
                this.advanceToCreate.addRecord({
                    receiptDate: this.postingDate,
                    paymentDate: this.postingDate,
                    totalAmount: this.bankFeedAmount,
                });
                this.targetDocumentType = 'accountsReceivableAdvance';
            }
        },
    })
    invoices: ui.fields.Table;

    @ui.decorators.tableField<AccountsReceivableInvoiceMatching>({
        parent() {
            return this.mainSection;
        },
        isChangeIndicatorDisabled: true,
        title: 'Payment generation',
        isHidden() {
            return !this.invoices.selectedRecords.length;
        },
        isTransient: true,
        canSelect: false,
        pageSize: 10,
        columns: [
            ui.nestedFields.text({
                title: 'Customer ID',
                bind: 'customerId',
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Customer name',
                bind: 'customerName',
                isDisabled: true,
            }),
            ui.nestedFields.numeric({
                title: 'Total amount',
                bind: 'totalAmount',
                scale: 2,
                isDisabled: true,
            }),
            ui.nestedFields.date({
                title: 'Receipt date',
                bind: 'receiptDate',
                minDate() {
                    return this.getMinDate();
                },
                isDisabled() {
                    return this.readOnlyMode;
                },
            }),
            ui.nestedFields.date({
                title: 'Payment date',
                bind: 'paymentDate',
                minDate() {
                    return this.getMinDate();
                },
                isDisabled() {
                    return this.readOnlyMode;
                },
            }),
            ui.nestedFields.dropdownList({
                title: 'Payment method',
                options: ['Printed Check', 'Cash', 'EFT'],
                bind: 'paymentMethod',
                isDisabled() {
                    return this.readOnlyMode;
                },
            }),
            ui.nestedFields.link({
                bind: 'financeDocumentCreatedNumber',
                isHidden: true,
                isFullWidth: true,
                // TODO: to implement when we have an ar payment page
                // onClick(_id, rowData: any) {
                // },
            }),
            ui.nestedFields.link({
                title: 'Sage Intacct AR payment',
                bind: 'intacctArDocumentID',
                onClick() {
                    this.$.router.goToExternal(this.financeIntegrationAppUrl);
                },
                isHidden() {
                    return !(this.financeIntegrationAppUrl.length > 0);
                },
            }),
            ui.nestedFields.text({
                title: 'Message',
                bind: 'errorMessage',
                isHidden() {
                    return !(this.financeDocumentGenerationErrorMessage.length > 0);
                },
                isDisabled: true,
            }),
        ],
    })
    paymentToCreate: ui.fields.Table;

    @ui.decorators.tableField<AccountsReceivableInvoiceMatching>({
        parent() {
            return this.mainSection;
        },
        isChangeIndicatorDisabled: true,
        title: 'Advance generation',
        isHidden() {
            return !!this.invoices.selectedRecords.length;
        },
        isTransient: true,
        canSelect: false,
        pageSize: 10,
        columns: [
            ui.nestedFields.text({
                title: 'Customer ID',
                bind: 'customerId',
                isDisabled: true,
            }),
            ui.nestedFields.select({
                title: 'Customer name',
                bind: 'customerName',
                options() {
                    return this.intacctCustomersNames || [];
                },
                isDisabled() {
                    return this.readOnlyMode;
                },
                onChange(_id, rowData) {
                    rowData.customerId =
                        this.intacctCustomers.find(
                            intacctCustomer => intacctCustomer.customerName === rowData.customerName,
                        )?.customerId || '';
                    this.advanceToCreate.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.reference<AccountsReceivableInvoiceMatching, AccountsReceivableInvoiceLine, Account>({
                bind: 'account',
                node: '@sage/xtrem-finance-data/Account',
                valueField: 'name',
                isAutoSelectEnabled: true,
                title: 'Account',
                filter() {
                    return { chartOfAccount: { _id: `${this.chartOfAccountSysId}` } };
                },
                isDisabled() {
                    return this.readOnlyMode;
                },
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.select({
                        title: 'Tax management',
                        bind: 'taxManagement',
                        optionType: '@sage/xtrem-finance-data/TaxManagement',
                    }),
                    ui.nestedFields.reference({
                        bind: 'chartOfAccount',
                        node: '@sage/xtrem-structure/ChartOfAccount',
                        valueField: { legislation: { id: true } },
                        isHidden: true,
                    }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Total amount',
                bind: 'totalAmount',
                isDisabled: true,
                scale: 2,
            }),
            ui.nestedFields.date({
                isDisabled() {
                    return this.readOnlyMode;
                },
                title: 'Receipt date',
                bind: 'receiptDate',
            }),
            ui.nestedFields.date({
                isDisabled() {
                    return this.readOnlyMode;
                },
                title: 'Payment date',
                bind: 'paymentDate',
            }),
            ui.nestedFields.dropdownList({
                isDisabled() {
                    return this.readOnlyMode;
                },
                title: 'Payment method',
                options: ['Printed Check', 'Cash', 'EFT', 'Credit Card'],
                bind: 'paymentMethod',
            }),
            ui.nestedFields.text({
                bind: 'arAdvance',
                isHidden: true,
            }),
            ui.nestedFields.link({
                title: 'Sage Intacct AR advance',
                bind: 'intacctArDocumentID',
                onClick() {
                    this.$.router.goToExternal(this.financeIntegrationAppUrl);
                },
                isHidden() {
                    return !(this.financeIntegrationAppUrl.length > 0);
                },
            }),
            ui.nestedFields.text({
                isHidden: true,
                bind: 'storedAttributes',
            }),
            ui.nestedFields.text({
                isHidden: true,
                bind: 'storedDimensions',
            }),
            ui.nestedFields.text({
                title: 'Message',
                bind: 'errorMessage',
                isHidden() {
                    return !(this.financeDocumentGenerationErrorMessage.length > 0);
                },
                isDisabled: true,
            }),
        ],
        fieldActions() {
            return [this.addAdvanceDimensionAttribute];
        },
        dropdownActions: [
            {
                icon: 'delete',
                title: 'Delete line',
                isDestructive: true,
                onClick(rowId: string) {
                    this.advanceToCreate.removeRecord(rowId);
                },
                isHidden() {
                    return this.readOnlyMode;
                },
            },
        ],
    })
    advanceToCreate: ui.fields.Table;

    @ui.decorators.pageAction<AccountsReceivableInvoiceMatching>({
        icon: 'edit',
        title: 'Dimensions',
        async onClick() {
            const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(
                this.advanceToCreate.value[0],
            );
            await utils.applyPanelToLineIfChanged(
                this.advanceToCreate,
                dimensionPanelHelpers.editDisplayDimensions(
                    this,
                    {
                        documentLine: rowData,
                    },
                    {
                        editable: !this.readOnlyMode,
                    },
                ),
            );
        },
    })
    addAdvanceDimensionAttribute: ui.PageAction;

    @ui.decorators.pageAction<AccountsReceivableInvoiceMatching>({
        title: 'Cancel',
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.pageAction<AccountsReceivableInvoiceMatching>({
        title: 'Ok',
        isDisabled() {
            return this.readOnlyMode;
        },
        onClick() {
            this.$.finish({
                arInvoices: this.getSelectedInvoices(),
                arMatch: this.getArMatch(),
                arPaymentAdvanceData: this.getArPaymentAdvanceData(),
            } as ArInvoiceMatchingPageResult);
        },
    })
    ok: ui.PageAction;

    @ui.decorators.pageAction<AccountsReceivableInvoiceMatching>({
        title: 'Create AR payment',
        isDisabled() {
            return this.readOnlyMode;
        },
        isHidden() {
            return (
                BankAccountMatching.getFeedRecordStatusForArPayments(this.getBankFeedDataForArPaymentStatusUpdate()) !==
                    'readyForArPaymentGeneration' || !(this.arPaymentAdvanceCreationData.bankFeedId > 0)
            );
        },
        onError(e) {
            this.$.loader.isHidden = true;
            if (
                e.errors &&
                e.errors.length &&
                e.errors[0].extensions &&
                e.errors[0].extensions.diagnoses &&
                e.errors[0].extensions.diagnoses.length
            ) {
                this.$.showToast(e.errors[0].extensions.diagnoses[0].message, { type: 'error', timeout: 20000 });
            } else {
                this.$.showToast(e.message, { timeout: 0, type: 'error' });
            }
            this.readOnlyMode = true;
            this.ok.isDisabled = true;
            this.createArPayment.isDisabled = true;
        },
        async onClick() {
            const arPaymentAdvanceData = this.getArPaymentAdvanceData();

            const confirmation = await utils.confirmDialogToBoolean(
                this.$.dialog.confirmation(
                    'warn',
                    ui.localize('@sage/xtrem-intacct-finance/create-ar-payment-title', 'Create AR payment'),

                    ui.localize(
                        '@sage/xtrem-intacct-finance/create-ar-payment-context',
                        'You are about to create a {{currencySymbol}}{{amount}} payment to {{customer}}.',
                        {
                            amount: this.paymentToCreate.value[0].totalAmount,
                            currencySymbol: this.bankAccount.value?.currency?.symbol || '',
                            customer: arPaymentAdvanceData.payToCustomerName,
                        },
                    ),
                    {
                        acceptButton: {
                            text: ui.localize('@sage/xtrem-intacct-finance/confirm-creation', 'Confirm creation'),
                        },
                        cancelButton: {
                            text: ui.localize('@sage/xtrem-intacct-finance/cancel', 'Cancel'),
                        },
                    },
                ),
            );

            this.readOnlyMode = true;
            this.createArPayment.isDisabled = true;

            if (confirmation) {
                this.readOnlyMode = true;
                this.createArPayment.isDisabled = true;

                const arInvoices = this.getSelectedInvoices();

                const arPayment = await this.$.graph
                    .node('@sage/xtrem-finance/AccountsReceivablePayment')
                    .mutations.createArPayment(
                        { number: true, financeIntegrationStatus: true },
                        {
                            data: {
                                bankAccount: this.bankAccount.value?._id ? this.bankAccount.value._id : 0,
                                financialSite: this.arPaymentAdvanceCreationData.siteId.toString(),
                                payToCustomerId: arPaymentAdvanceData.payToCustomerId,
                                payToCustomerName: arPaymentAdvanceData.payToCustomerName,
                                description: this.bankFeedDescription,
                                paymentMethod: arPaymentAdvanceData.paymentMethod,
                                postingDate: arPaymentAdvanceData.receiptDate,
                                paymentDate: arPaymentAdvanceData.paymentDate,
                                currency: this.arPaymentAdvanceCreationData.currencyId.toString(),
                                companyFxRate: 1,
                                companyFxRateDivisor: 1,
                                fxRateDate: arPaymentAdvanceData.receiptDate,
                                paymentAmount: this.paymentToCreate.value[0].totalAmount,
                                paymentCompanyAmount: this.paymentToCreate.value[0].totalAmount,
                                bankFeed: this.arPaymentAdvanceCreationData.bankFeedId,
                                arMatch: this.getArMatch(),
                                jsonArInvoices: { matchedArInvoices: this.getSelectedInvoices() },
                                lines: arInvoices.map(arInvoice => ({
                                    type: 'salesInvoice',
                                    arInvoiceRecordNo: arInvoice.recordNo,
                                    financialSite: this.arPaymentAdvanceCreationData.siteId.toString(),
                                    currency: this.arPaymentAdvanceCreationData.currencyId.toString(),
                                    paymentAmount: arInvoice.arInvoiceAmountMatched,
                                    paymentCompanyAmount: arInvoice.arInvoiceAmountMatched,
                                })),
                            },
                        },
                    )
                    .execute();

                if (arPayment) {
                    this.financeIntegrationStatus = arPayment.financeIntegrationStatus;
                    this.$.showToast(
                        `${ui.localize(
                            '@sage/xtrem-intacct-finance/pages_accounts_receivable_invoice_matching_payment_created',
                            'Payment created:',
                        )} ${arPayment.number}`,
                        { type: 'success' },
                    );
                    this.$.finish({
                        arInvoices: this.getSelectedInvoices(),
                        arMatch: this.getArMatch(),
                        arPaymentAdvanceData: this.getArPaymentAdvanceData(),
                        newFinanceIntegrationStatus: this.financeIntegrationStatus,
                        newStatus: BankAccountMatching.getFeedRecordStatusForArPayments(
                            this.getBankFeedDataForArPaymentStatusUpdate(),
                        ),
                    } as ArInvoiceMatchingPageResult);
                }
            }
        },
    })
    createArPayment: ui.PageAction;

    @ui.decorators.pageAction<AccountsReceivableInvoiceMatching>({
        title: 'Create AR advance',
        isDisabled() {
            return this.readOnlyMode;
        },
        isHidden() {
            return (
                BankAccountMatching.getFeedRecordStatusForArPayments(this.getBankFeedDataForArPaymentStatusUpdate()) !==
                'readyForArAdvanceGeneration'
            );
        },
        onError(e) {
            this.$.loader.isHidden = true;
            if (
                e.errors &&
                e.errors.length &&
                e.errors[0].extensions &&
                e.errors[0].extensions.diagnoses &&
                e.errors[0].extensions.diagnoses.length
            ) {
                this.$.showToast(e.errors[0].extensions.diagnoses[0].message, { type: 'error', timeout: 20000 });
            } else {
                this.$.showToast(e.message, { timeout: 0, type: 'error' });
            }
            this.readOnlyMode = true;
            this.ok.isDisabled = true;
            this.createArPayment.isDisabled = true;
        },
        async onClick() {
            const arPaymentAdvanceData = this.getArPaymentAdvanceData();

            const confirmation = await utils.confirmDialogToBoolean(
                this.$.dialog.confirmation(
                    'warn',
                    ui.localize('@sage/xtrem-intacct-finance/create-ar-advance-title', 'Create AR advance'),

                    ui.localize(
                        '@sage/xtrem-intacct-finance/create-ar-advance-context',
                        'You are about to create a {{currencySymbol}}{{amount}} advance to {{customer}}.',
                        {
                            amount: this.advanceToCreate.value[0].totalAmount,
                            currencySymbol: this.bankAccount.value?.currency?.symbol || '',
                            customer: arPaymentAdvanceData.payToCustomerName,
                        },
                    ),
                    {
                        acceptButton: {
                            text: ui.localize('@sage/xtrem-intacct-finance/confirm-creation', 'Confirm creation'),
                        },
                        cancelButton: {
                            text: ui.localize('@sage/xtrem-intacct-finance/cancel', 'Cancel'),
                        },
                    },
                ),
            );

            if (confirmation) {
                this.readOnlyMode = true;
                this.createArAdvance.isDisabled = true;
                this.targetDocumentType = 'accountsReceivableAdvance';

                const arAdvanceData = this.getArAdvances();
                const arAdvance: Partial<AccountsReceivableAdvance> = await this.$.graph
                    .node('@sage/xtrem-finance/AccountsReceivableAdvance')
                    .mutations.createArAdvance(
                        { number: true, financeIntegrationStatus: true },
                        {
                            data: {
                                bankAccount: this.bankAccount.value?._id ? this.bankAccount.value._id : 0,
                                financialSite: this.arPaymentAdvanceCreationData.siteId.toString(),
                                payToCustomerId: arPaymentAdvanceData.payToCustomerId,
                                payToCustomerName: arPaymentAdvanceData.payToCustomerName,
                                description: this.bankFeedDescription,
                                paymentMethod: arPaymentAdvanceData.paymentMethod,
                                postingDate: arPaymentAdvanceData.receiptDate,
                                paymentDate: arPaymentAdvanceData.paymentDate,
                                currency: this.arPaymentAdvanceCreationData.currencyId.toString(),
                                companyFxRate: 1,
                                companyFxRateDivisor: 1,
                                fxRateDate: arPaymentAdvanceData.receiptDate,
                                bankFeed: this.arPaymentAdvanceCreationData.bankFeedId,
                                arMatch: this.getArMatch(),
                                advanceAmount: this.advanceToCreate.value[0].totalAmount,
                                advanceCompanyAmount: this.advanceToCreate.value[0].totalAmount,
                                lines: arAdvanceData.map(arAdvanceLine => ({
                                    financialSite: this.arPaymentAdvanceCreationData.siteId.toString(),
                                    account: arAdvanceLine.account._id,
                                    currency: this.arPaymentAdvanceCreationData.currencyId.toString(),
                                    advanceAmount: arAdvanceLine.totalAmount,
                                    advanceCompanyAmount: arAdvanceLine.totalAmount,
                                    storedAttributes: arAdvanceLine.storedAttributes
                                        ? arAdvanceLine.storedAttributes
                                        : '{}',
                                    storedDimensions: arAdvanceLine.storedDimensions
                                        ? arAdvanceLine.storedDimensions
                                        : '{}',
                                })),
                            },
                        },
                    )
                    .execute();

                if (arAdvance) {
                    this.financeIntegrationStatus = arAdvance.financeIntegrationStatus;
                    this.$.showToast(
                        `${ui.localize(
                            '@sage/xtrem-intacct-finance/pages_accounts_receivable_invoice_matching_advance_created',
                            'Advance created:',
                        )} ${arAdvance.number}`,
                        { type: 'success' },
                    );
                    this.$.finish({
                        arInvoices: this.getSelectedInvoices(),
                        arMatch: this.getArMatch(),
                        arPaymentAdvanceData: this.getArPaymentAdvanceData(),
                        newFinanceIntegrationStatus: this.financeIntegrationStatus,
                        newStatus: BankAccountMatching.getFeedRecordStatusForArPayments(
                            this.getBankFeedDataForArPaymentStatusUpdate(),
                        ),
                    } as ArInvoiceMatchingPageResult);
                }
            }
        },
    })
    createArAdvance: ui.PageAction;

    /**
     * Gets the most recent date from all the selected ar invoices
     * @return date of undefined
     */
    getMinDate(): string | undefined {
        if (!this.invoices.selectedRecords.length) {
            return undefined;
        }
        const mostRecentInvoiceId = this.invoices.selectedRecords.reduce((prevInvoiceId, currInvoiceId) => {
            if (prevInvoiceId) {
                const prevInvoice = this.invoices.getRecordValue(prevInvoiceId);
                const currInvoice = this.invoices.getRecordValue(currInvoiceId);

                return new Date(Date.parse(prevInvoice?.date)) > new Date(Date.parse(currInvoice?.date))
                    ? prevInvoiceId
                    : currInvoiceId;
            }
            return currInvoiceId;
        });
        return this.invoices.getRecordValue(mostRecentInvoiceId)?.date;
    }

    /**
     * Sets the matching reason. It can be because we found the invoice number and\or the customer id and\or customer name
     */
    setMatchingReason() {
        if (this.arMatch.matchingReasons) {
            this.matchingReason.value = this.arMatch.matchingReasons;
            return;
        }
        this.matchingReason.value = this.matchingReason.value || '';
        if (this.matchedInvoiceNo) {
            this.matchingReason.value += `${ui.localize(
                '@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__invoice_number_was_found',
                'Invoice number {{invoiceNumber}} was found.',
                { invoiceNumber: this.matchedInvoiceNo },
            )} `;
        }
        if (this.matchedCustomerId) {
            this.matchingReason.value += `${ui.localize(
                '@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__customer_id_was_found',
                'Customer ID {{customerId}} was found.',
                { customerId: this.matchedCustomerId },
            )} `;
        }
        if (this.matchedCustomerName) {
            this.matchingReason.value += `${ui.localize(
                '@sage/xtrem-intacct-finance/pages__accounts_receivable_invoice_matching__customer_name_was_found',
                'Customer name {{customerName}} was found.',
                { customerName: this.matchedCustomerName },
            )} `;
        }
    }

    initDates(): { receiptDate: string; paymentDate: string } {
        const minDate = this.getMinDate();
        const receiptDate =
            minDate && new Date(Date.parse(minDate)) > new Date(Date.parse(this.postingDate))
                ? minDate
                : this.postingDate;
        const paymentDate = receiptDate;

        return { receiptDate, paymentDate };
    }

    /**
     * Sets the payment to be created based on the selected invoice lines.
     */
    setPaymentToCreate() {
        const arPaymentAdvanceData = this.getArPaymentAdvanceData();
        const minDate = this.getMinDate();
        const { receiptDate, paymentDate } = this.initDates();

        this.paymentToCreate.value = [];
        if (this.invoices.selectedRecords) {
            const paymentLine = {
                customerId: this.selectedCustomerId,
                customerName: this.selectedCustomerName,
                totalAmount: this.invoices.value.reduce((amount, invoice) => amount + (invoice.amountToMatch || 0), 0),
                paymentMethod: arPaymentAdvanceData.paymentMethod,
                receiptDate:
                    arPaymentAdvanceData.receiptDate &&
                    minDate &&
                    new Date(Date.parse(arPaymentAdvanceData.receiptDate)) > new Date(Date.parse(minDate))
                        ? arPaymentAdvanceData.receiptDate
                        : receiptDate,
                paymentDate:
                    arPaymentAdvanceData.paymentDate &&
                    minDate &&
                    new Date(Date.parse(arPaymentAdvanceData.paymentDate)) > new Date(Date.parse(minDate))
                        ? arPaymentAdvanceData.paymentDate
                        : paymentDate,
            };

            if (paymentLine.totalAmount) {
                this.paymentToCreate.addRecord(paymentLine);
                this.targetDocumentType = 'accountsReceivablePayment';
            }
        }
    }

    setSelectedCustomer(customerOutstandingInvoice: IntacctArInvoice) {
        if (!this.selectedCustomerId) {
            this.selectedCustomerId = customerOutstandingInvoice.customerId ?? '';
        }
        if (!this.selectedCustomerName) {
            this.selectedCustomerName = customerOutstandingInvoice.customerName ?? '';
        }
    }

    /**
     * Adds and selects on the invoice table all the already selected invoices (runs on the onLoad)
     */
    selectInvoicesAlreadyMatched(): number {
        let bankFeedAmountAmountToMatch = this.bankFeedAmount;

        // this.arInvoicesAlreadySelected -> invoices selected by user on previous actions on this page
        // this.availableArOutstandingInvoices -> the invoices fetched from intacct and purged from the
        //                                        invoices already used on other bank feed lines
        if (this.arInvoicesAlreadySelected.length) {
            this.arInvoicesAlreadySelected.forEach(arInvoiceAlreadySelected => {
                const customerOutstandingInvoice = this.availableArOutstandingInvoices.find(
                    fetchedOutstandingInvoice =>
                        fetchedOutstandingInvoice.recordNo === arInvoiceAlreadySelected.recordNo,
                );
                if (customerOutstandingInvoice) {
                    this.setSelectedCustomer(customerOutstandingInvoice);
                    const arInvoice = {
                        ...customerOutstandingInvoice,
                        amountToMatch: arInvoiceAlreadySelected.arInvoiceAmountMatched,
                    };
                    bankFeedAmountAmountToMatch -= arInvoice.amountToMatch;
                    const newLine = this.invoices.addOrUpdateRecordValue(arInvoice);
                    if (arInvoice.amountToMatch) {
                        this.invoices.selectRecord(newLine._id);
                    }
                }
            });
        }

        return bankFeedAmountAmountToMatch;
    }

    /**
     * Check if we can match a given ar invoice with the bank feed. Order is the following:
     * 1/ we try to check if we have the invoice number on the bank feed description or payee
     * 2/ we try to check if we have the customer id on the bank feed description or payee
     * 3/ we try to check if we have the customer name on the bank feed description or payee
     * @return boolean
     */
    arInvoiceMatch(arInvoice: IntacctArInvoice, criteria: MatchingCriteria): boolean {
        if (
            (criteria === 'invoiceNo' || criteria === '') &&
            (this.bankFeedDescription.match(new RegExp(arInvoice.invoiceNo, 'i')) ||
                this.bankFeedPayee.match(new RegExp(arInvoice.invoiceNo, 'i')))
        ) {
            this.matchedInvoiceNo = this.matchedInvoiceNo || arInvoice.invoiceNo;
            return true;
        }

        if (
            (criteria === 'customerId' || criteria === '') &&
            (this.bankFeedDescription.match(new RegExp(arInvoice.customerId, 'i')) ||
                this.bankFeedPayee.match(new RegExp(arInvoice.customerId, 'i')))
        ) {
            this.matchedCustomerId = this.matchedCustomerId || arInvoice.customerId;
            return true;
        }

        if (
            (criteria === 'customerName' || criteria === '') &&
            (this.bankFeedDescription.match(new RegExp(arInvoice.customerName, 'i')) ||
                this.bankFeedPayee.match(new RegExp(arInvoice.customerName, 'i')))
        ) {
            this.matchedCustomerName = this.matchedCustomerName || arInvoice.customerName;
            return true;
        }

        return false;
    }

    addCustomerOutstandingInvoices(bankFeedAmountAmountToMatch: number, criteria: MatchingCriteria): number {
        let returnBankFeedAmountAmountToMatch = bankFeedAmountAmountToMatch;

        this.availableArOutstandingInvoices.forEach(customerOutstandingInvoice => {
            // if the record is not already on the table
            if (!this.invoices.getRecordByFieldValue('recordNo', customerOutstandingInvoice.recordNo)) {
                this.setSelectedCustomer(customerOutstandingInvoice);
                const arInvoice = { ...customerOutstandingInvoice, amountToMatch: 0 };
                if (criteria) {
                    if (
                        returnBankFeedAmountAmountToMatch > 0 &&
                        this.arInvoiceMatch(customerOutstandingInvoice, criteria)
                    ) {
                        arInvoice.amountToMatch =
                            returnBankFeedAmountAmountToMatch >= +(arInvoice.totalDue ?? 0)
                                ? +(arInvoice.totalDue ?? 0)
                                : returnBankFeedAmountAmountToMatch;
                        returnBankFeedAmountAmountToMatch = Math.max(
                            returnBankFeedAmountAmountToMatch - +(arInvoice.totalDue ?? 0),
                            0,
                        );

                        const newLine = this.invoices.addOrUpdateRecordValue(arInvoice);
                        if (arInvoice.amountToMatch) {
                            this.invoices.selectRecord(newLine._id);
                        }
                    }
                } else {
                    this.invoices.addOrUpdateRecordValue(arInvoice);
                }
            }
        });
        return returnBankFeedAmountAmountToMatch;
    }

    async initPaymentAdvance() {
        const minDate = this.getMinDate();
        const { receiptDate, paymentDate } = this.initDates();
        const paymentAdvanceData = {
            receiptDate:
                (this.arPaymentAdvanceData.receiptDate &&
                    minDate &&
                    new Date(Date.parse(this.arPaymentAdvanceData.receiptDate)) > new Date(Date.parse(minDate))) ||
                (!minDate && this.arPaymentAdvanceData.receiptDate)
                    ? this.arPaymentAdvanceData.receiptDate
                    : receiptDate,
            paymentDate:
                (this.arPaymentAdvanceData.paymentDate &&
                    minDate &&
                    new Date(Date.parse(this.arPaymentAdvanceData.paymentDate)) > new Date(Date.parse(minDate))) ||
                (!minDate && this.arPaymentAdvanceData.paymentDate)
                    ? this.arPaymentAdvanceData.paymentDate
                    : paymentDate,
            totalAmount:
                this.arMatch.arPaymentType === 'advance'
                    ? this.bankFeedAmount
                    : this.bankFeedAmount - +(this?.amountToMatch?.value || 0),
            customerId: this.arPaymentAdvanceData.payToCustomerId || this.selectedCustomerId,
            customerName: this.arPaymentAdvanceData.payToCustomerName || this.selectedCustomerName,
            paymentMethod: this.arPaymentAdvanceData.paymentMethod,
            account: JSON.parse(this.arPaymentAdvanceData.account || '{}'),
            storedAttributes: this.arPaymentAdvanceData.storedAttributes,
            storedDimensions: this.arPaymentAdvanceData.storedDimensions,
            financeDocumentCreatedNumber: this.financeDocumentCreatedNumber,
            intacctArDocumentID: this.financeIntegrationAppRecordId,
            errorMessage: this.financeDocumentGenerationErrorMessage,
        };

        switch (this.arMatch.arPaymentType) {
            case 'advance': {
                await this.getIntacctCustomerList();
                this.advanceToCreate.addRecord(paymentAdvanceData);
                this.targetDocumentType = 'accountsReceivableAdvance';
                return;
            }
            case 'payment': {
                this.paymentToCreate.addRecord(paymentAdvanceData);
                this.targetDocumentType = 'accountsReceivablePayment';
                return;
            }
            default: {
                // When there's non selected, we need to fetch all customers from intacct and prepare an advance to create
                if (!this.invoices.selectedRecords.length) {
                    await this.getIntacctCustomerList();
                    this.advanceToCreate.addRecord({
                        receiptDate: this.postingDate,
                        paymentDate: this.postingDate,
                        totalAmount: this.bankFeedAmount,
                    });
                }
            }
        }
    }

    /**
     * Loads the invoices table
     */

    getQueryParametersInvoices() {
        return {
            documentNos: this.arInvoicesAlreadySelected.map(matchedArinvoice => matchedArinvoice.recordNo.toString()),
        };
    }

    getQueryParametersNoInvoices() {
        return this.arMatch.customerId
            ? {
                  customerId: this.arMatch.customerId,
                  currencyId: this.bankAccount.value?.currency?.id || '',
                  orderField: { name: 'WHENDUE', isAscending: true },
              }
            : {
                  currencyId: this.bankAccount.value?.currency?.id || '',
                  orderField: { name: 'WHENDUE', isAscending: true },
              };
    }

    async init() {
        const parameters = this.readOnlyMode ? this.getQueryParametersInvoices() : this.getQueryParametersNoInvoices();

        // get the ar invoices
        const intacctOutstandingInvoices = await this.$.graph
            .node('@sage/xtrem-intacct-finance/IntacctBankAccountMatching')
            .queries.queryIntacctArInvoice(
                {
                    recordNo: true,
                    invoiceNo: true,
                    customerName: true,
                    customerId: true,
                    referenceNumber: true,
                    date: true,
                    dueDate: true,
                    totalAmount: true,
                    totalDue: true,
                    totalPaid: true,
                    term: { _id: true, name: true, description: true },
                    entityId: true,
                },
                {
                    parameters: {
                        ...parameters,
                        megaEntityId:
                            this.bankAccount.value?.megaEntityId || this.bankAccount.value?.financialSite?.id || '',
                    },
                },
            )
            .execute();

        // we remove all the ones already used on other bank feed records
        this.availableArOutstandingInvoices = this.readOnlyMode
            ? intacctOutstandingInvoices
            : intacctOutstandingInvoices.filter(
                  intacctOutstandingInvoice =>
                      !this.usedArInvoices.find(usedArInvoice => usedArInvoice === intacctOutstandingInvoice.recordNo),
              );

        // first we load and select on the grid the ones previously selected for this bank feed record
        let bankFeedAmountAmountToMatch = this.selectInvoicesAlreadyMatched();

        if (!this.readOnlyMode) {
            // then we load the rest
            if (this.arMatch.customerId && this.arMatch.arPaymentType !== 'advance') {
                // we do 4 passes to respect the matching rules
                // but if we have already selected records from previous saves, we just run one pass because we don't need to match anymore
                if (!this.invoices.selectedRecords?.length) {
                    bankFeedAmountAmountToMatch = this.addCustomerOutstandingInvoices(
                        bankFeedAmountAmountToMatch,
                        'invoiceNo',
                    );
                    bankFeedAmountAmountToMatch = this.addCustomerOutstandingInvoices(
                        bankFeedAmountAmountToMatch,
                        'customerId',
                    );
                    bankFeedAmountAmountToMatch = this.addCustomerOutstandingInvoices(
                        bankFeedAmountAmountToMatch,
                        'customerName',
                    );
                }
                bankFeedAmountAmountToMatch = this.addCustomerOutstandingInvoices(bankFeedAmountAmountToMatch, '');
                this.setMatchingReason();
            } else {
                // if we don't have a customer id, we just load all, without selecting any
                this.availableArOutstandingInvoices.forEach(customerOutstandingInvoice => {
                    if (!this.invoices.getRecordByFieldValue('recordNo', customerOutstandingInvoice.recordNo)) {
                        // we just need to control if there was some previously loaded, so we don't mix customers
                        if (
                            !(
                                this.selectedCustomerId &&
                                this.selectedCustomerId !== customerOutstandingInvoice.customerId
                            )
                        ) {
                            const arInvoice = { ...customerOutstandingInvoice, amountToMatch: 0 };
                            this.invoices.addOrUpdateRecordValue(arInvoice);
                        }
                    }
                });
            }
        }

        this.amountToMatch.value = bankFeedAmountAmountToMatch;
        await this.initPaymentAdvance();
    }

    /**
     * Checks is a customer (from an invoice) is valid. If we have a customer id, we need to use always that customer.
     * If we don't but we have a selected customer id, we need to use always that customer.
     * @return boolean
     */
    isValidCustomerId(customerId: string): boolean {
        if (this.arMatch.customerId) {
            return this.arMatch.customerId === customerId;
        }
        return this.selectedCustomerId ? this.selectedCustomerId === customerId : true;
    }

    /**
     * Gets the list of intacct customers.
     */
    async getIntacctCustomerList() {
        this.intacctCustomers = (await this.$.graph
            .node('@sage/xtrem-intacct-finance/IntacctBankAccountMatching')
            .queries.queryIntacctCustomers(
                {
                    customerId: true,
                    customerName: true,
                },
                {},
            )
            .execute()) as IntacctCustomer[];

        if (this.intacctCustomers && this.intacctCustomers.length) {
            this.intacctCustomersNames = this.intacctCustomers.map(intacctCustomer => intacctCustomer.customerName);
        }
    }

    getBankFeedDataForArPaymentStatusUpdate(): IntacctBankAccountTransactionFeedArPayment {
        return {
            ...this.getArPaymentAdvanceData(),
            amountToMatch: this.bankFeedAmount,
            arMatch: this.getArMatch(),
            jsonArInvoices: { matchedArInvoices: this.getSelectedInvoices() },
            financeIntegrationStatus: this.financeIntegrationStatus,
            targetDocumentType: this.targetDocumentType,
        };
    }

    getArMatch(): ArMatch {
        let arPaymentType: ApArInvoicePaymentType = '';
        if (this.paymentToCreate.value.length) {
            arPaymentType = 'payment';
        } else if (this.advanceToCreate.value.length) {
            arPaymentType = 'advance';
        }
        return { ...this.arMatch, arPaymentType, matchingReasons: this.matchingReason.value || '' };
    }

    getSelectedInvoices(): { recordNo: number; arInvoiceAmountMatched: number }[] {
        return this.invoices.selectedRecords.map(selectedInvoice => {
            const arInvoice = this.invoices.getRecordValue(selectedInvoice);
            return { recordNo: arInvoice?.recordNo, arInvoiceAmountMatched: arInvoice?.amountToMatch };
        });
    }

    getArAdvances() {
        return this.advanceToCreate.value.map(arAdvanceLine => {
            const arAdvance = this.advanceToCreate.getRecordValue(arAdvanceLine._id);
            return { ...arAdvance };
        });
    }

    getArPaymentAdvanceData(): ApArPaymentMatchingData {
        const arPaymentAdvanceData: ApArPaymentMatchingData = {
            payToCustomerId: '',
            payToCustomerName: '',
            paymentMethod: '',
            paymentDate: '',
            receiptDate: '',
            account: 'null',
            storedAttributes: '{}',
            storedDimensions: '{}',
        };
        if (this.paymentToCreate.value.length) {
            arPaymentAdvanceData.payToCustomerId = this.paymentToCreate.value[0].customerId;
            arPaymentAdvanceData.payToCustomerName = this.paymentToCreate.value[0].customerName;
            arPaymentAdvanceData.paymentMethod = this.paymentToCreate.value[0].paymentMethod;
            arPaymentAdvanceData.paymentDate = this.paymentToCreate.value[0].paymentDate;
            arPaymentAdvanceData.receiptDate = this.paymentToCreate.value[0].receiptDate;
            arPaymentAdvanceData.targetDocumentType = 'accountsReceivablePayment';
        } else if (this.advanceToCreate.value.length) {
            arPaymentAdvanceData.payToCustomerId = this.advanceToCreate.value[0].customerId;
            arPaymentAdvanceData.payToCustomerName = this.advanceToCreate.value[0].customerName;
            arPaymentAdvanceData.paymentMethod = this.advanceToCreate.value[0].paymentMethod;
            arPaymentAdvanceData.paymentDate = this.advanceToCreate.value[0].paymentDate;
            arPaymentAdvanceData.receiptDate = this.advanceToCreate.value[0].receiptDate;
            arPaymentAdvanceData.account = JSON.stringify(this.advanceToCreate.value[0].account);
            arPaymentAdvanceData.storedAttributes = this.advanceToCreate.value[0].storedAttributes;
            arPaymentAdvanceData.storedDimensions = this.advanceToCreate.value[0].storedDimensions;
            arPaymentAdvanceData.targetDocumentType = 'accountsReceivableAdvance';
        }
        return arPaymentAdvanceData;
    }
}
