import type { Account } from '@sage/xtrem-finance-data-api';
import type { GraphApi } from '@sage/xtrem-intacct-finance-api';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import * as ui from '@sage/xtrem-ui';
import type {
    IntacctFinanceDocument,
    ParametersForFinanceDocumentSearch,
} from '../client-functions/interfaces/bank-statement';

@ui.decorators.page<IntacctFinancialDocumentSearchDialog>({
    priority: 200,
    title: 'Document search',
    objectTypeSingular: 'Sage Intacct document search',
    mode: 'tabs',
    isTransient: true,
    access: { node: '@sage/xtrem-intacct-finance/IntacctBankAccountTransactionFeed' },
    businessActions() {
        return [this.select];
    },
    async onLoad() {
        this.parameters = this.$.queryParameters;
        if (this.parameters) {
            this.account.value = this.parameters.account ? JSON.parse(this.parameters.account) : {};
            this.documentType.value = this.parameters.documentType || 'JournalEntry';
            this.date.value = this.parameters.date || null;
            this.amount.value = this.parameters.amount || null;
            this.description.value = this.parameters.description || '';
        }

        this.$.loader.isHidden = false;
        await this.getIntacctConfig();
        await this.init();
        this.$.loader.isHidden = true;
    },
    onClose() {},
    onError(error) {
        return utils.formatError(this, error);
    },
})
export class IntacctFinancialDocumentSearchDialog extends ui.Page<GraphApi> {
    /** parsed queryParameters */
    parameters: ParametersForFinanceDocumentSearch;

    @ui.decorators.section<IntacctFinancialDocumentSearchDialog>({
        isOpen: true,
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<IntacctFinancialDocumentSearchDialog>({
        parent() {
            return this.mainSection;
        },
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.textField<IntacctFinancialDocumentSearchDialog>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Description',
    })
    description: ui.fields.Text;

    @ui.decorators.dateField<IntacctFinancialDocumentSearchDialog>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Date',
    })
    date: ui.fields.Date;

    @ui.decorators.numericField<IntacctFinancialDocumentSearchDialog>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Amount',
    })
    amount: ui.fields.Numeric;

    @ui.decorators.dropdownListField<IntacctFinancialDocumentSearchDialog>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Document type',
        placeholder: 'Select document type',
        options: ['JournalEntry'],
        width: 'small',
    })
    documentType: ui.fields.DropdownList;

    @ui.decorators.referenceField<IntacctFinancialDocumentSearchDialog, Account>({
        node: '@sage/xtrem-finance-data/Account',
        valueField: 'name',
        isAutoSelectEnabled: true,
        bind: 'account',
        title: 'Account',
        parent() {
            return this.criteriaBlock;
        },
        columns: [ui.nestedFields.text({ bind: 'id' }), ui.nestedFields.text({ bind: 'name' })],
        orderBy: { name: 1 },
        filter() {
            return { chartOfAccount: { _id: this.chartOfAccountSysId } };
        },
    })
    account: ui.fields.Reference<Account>;

    @ui.decorators.buttonField<IntacctFinancialDocumentSearchDialog>({
        parent() {
            return this.criteriaBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-intacct-finance/search', 'Search');
        },
        isTransient: true,
        width: 'small',
        async onClick() {
            this.$.loader.isHidden = false;
            await this.init();
            this.$.loader.isHidden = true;
        },
    })
    searchButton: ui.fields.Button;

    @ui.decorators.tableField<IntacctFinancialDocumentSearchDialog, IntacctFinanceDocument>({
        title: 'Results',
        isHelperTextHidden: true,
        isTransient: true,
        isReadOnly: true,
        canSelect: true,
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.link({
                title: 'Accounting integration reference',
                bind: 'url',
                map(id, rowData: IntacctFinanceDocument) {
                    return rowData.batchNo;
                },
                onClick(_id, data: IntacctFinanceDocument) {
                    this.$.router.goToExternal(data.url);
                },
            }),
            ui.nestedFields.text({ bind: 'recordNo', isHidden: true }),
            ui.nestedFields.text({ title: 'Document type', bind: 'documentType' }),
            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
            ui.nestedFields.text({ title: 'Journal', bind: 'journal' }),
            ui.nestedFields.text({ title: 'Location', bind: 'location' }),
            ui.nestedFields.text({ title: 'Date', bind: 'date' }),
            ui.nestedFields.text({ title: 'Amount', bind: 'amount' }),
            ui.nestedFields.text({ title: 'Account', bind: 'account' }),
            ui.nestedFields.text({ bind: 'batchNo', isHidden: true }),
            ui.nestedFields.text({ title: 'Entity', bind: 'entityId' }),
        ],
        onRowSelected(id) {
            this.financialDocuments.unselectAllRecords();
            this.financialDocuments.selectRecord(id);
        },
    })
    financialDocuments: ui.fields.Table<IntacctFinanceDocument>;

    @ui.decorators.pageAction<IntacctFinancialDocumentSearchDialog>({
        title: 'Save',
        onClick() {
            switch (this.financialDocuments.selectedRecords.length) {
                case 0:
                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-intacct-finance/pages_document-no-document-selected',
                            'No document selected.',
                        ),
                        { timeout: 10000, type: 'warning' },
                    );
                    this.$.finish({ isDocumentSelected: false });
                    break;
                case 1:
                    this.$.finish({
                        isDocumentSelected: true,
                        ...this.financialDocuments.getRecordValue(this.financialDocuments.selectedRecords[0]),
                    });
                    break;
                default:
                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-intacct-finance/pages_document-search-more-than-one',
                            "You can't select more than one document.",
                        ),
                        { timeout: 10000, type: 'warning' },
                    );
                    break;
            }
        },
    })
    select: ui.PageAction;

    async init() {
        const parameters: ParametersForFinanceDocumentSearch = {};

        if (this.amount.value) {
            parameters.amount = this.amount.value;
        }
        if (this.date.value) {
            parameters.date = this.date.value;
        }
        if (this.description.value) {
            parameters.description = this.description.value;
        }
        if (this.account.value && this.account.value.id) {
            parameters.account = this.account.value.id;
        }

        const result = (await this.$.graph
            .node('@sage/xtrem-intacct-finance/IntacctBankAccountMatching')
            .queries.queryIntacctDocument(
                {
                    description: true,
                    documentType: true,
                    journal: true,
                    location: true,
                    recordNo: true,
                    date: true,
                    amount: true,
                    account: true,
                    url: true,
                    batchNo: true,
                    entityId: true,
                },
                {
                    parameters,
                    documentType: this.documentType.value || 'JournalEntry',
                },
            )
            .execute()) as IntacctFinanceDocument[];

        let selectedRecordId = '';
        this.financialDocuments.value = result.map((intacctDocument, index) => {
            if (intacctDocument.batchNo === this.parameters.batchNoSelected) {
                selectedRecordId = index.toString();
            }
            return {
                ...intacctDocument,
                _id: index.toString(),
            } as IntacctFinanceDocument;
        });
        if (selectedRecordId) {
            this.financialDocuments.selectRecord(selectedRecordId);
        }
    }

    async getIntacctConfig() {
        const intacctConfiguration = await this.$.graph
            .node('@sage/xtrem-intacct/Intacct')
            .queries.defaultInstance(
                {
                    legislation: { id: true },
                    chartOfAccount: { _id: true, name: true },
                },
                {},
            )
            .execute();

        this.chartOfAccountSysId = intacctConfiguration.chartOfAccount._id;
    }

    chartOfAccountSysId: string;
}
