import { withoutEdges } from '@sage/xtrem-client';
import type { Account, BankAccount } from '@sage/xtrem-finance-data-api';
import { getIsSubjectToGlTaxExcludedAmount } from '@sage/xtrem-finance-data/build/lib/client-functions/common';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type {
    GraphApi,
    IntacctBankAccountMatching,
    IntacctBankAccountMatchingInput,
    IntacctRecordTransactionType,
} from '@sage/xtrem-intacct-finance-api';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { Site } from '@sage/xtrem-system-api';
import type { Tax } from '@sage/xtrem-tax-api';
import * as ui from '@sage/xtrem-ui';
import { confirmDialogWithAcceptButtonText } from '../client-functions/page-functions';

@ui.decorators.page<IntacctBankAccountMatchingDialog>({
    title: 'Matching rules',
    objectTypeSingular: 'Sage Intacct bank account matching rule',
    objectTypePlural: 'Sage Intacct bank account matching rules',
    mode: 'default',
    isTransient: true,
    access: {
        node: '@sage/xtrem-intacct-finance/IntacctBankAccountTransactionFeed',
    },
    async onLoad() {
        this.isDialogMode = Boolean(this.$.queryParameters.isDialog) || false;

        await this.getIntacctConfig();
        this.isSubjectToGlTaxExcludedAmount = await getIsSubjectToGlTaxExcludedAmount(this, 'ZA');
        await this.init();
    },

    businessActions() {
        return [this.save, this.apply, this.cancel];
    },
})
export class IntacctBankAccountMatchingDialog extends ui.Page<GraphApi> {
    isSubjectToGlTaxExcludedAmount: boolean;

    isDialogMode: boolean;

    @ui.decorators.section<IntacctBankAccountMatchingDialog>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.tableField<
        IntacctBankAccountMatchingDialog,
        IntacctBankAccountMatching & { isModified?: boolean; updateAccountTaxManagement?: boolean }
    >({
        title: 'Matching rules',
        isHelperTextHidden: true,
        isTransient: true,
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: 'isModified' }),
            ui.nestedFields.reference<IntacctBankAccountMatchingDialog, IntacctBankAccountMatching, BankAccount>({
                node: '@sage/xtrem-finance-data/BankAccount',
                valueField: 'id',
                bind: 'bankAccount',
                title: 'Bank account',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
            ui.nestedFields.numeric({ title: 'Priority', bind: 'priority' }),
            ui.nestedFields.dropdownList({
                bind: 'type',
                title: 'Type',
                optionType: '@sage/xtrem-intacct-finance/intacctMatchingType',
            }),
            ui.nestedFields.text({ title: 'Keyword', bind: 'keyword' }),
            ui.nestedFields.reference<IntacctBankAccountMatchingDialog, IntacctBankAccountMatching, Account>({
                node: '@sage/xtrem-finance-data/Account',
                valueField: 'name',
                isAutoSelectEnabled: true,
                bind: 'account',
                title: 'Account',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'taxManagement', title: 'Tax management' }),
                    ui.nestedFields.reference({
                        bind: 'chartOfAccount',
                        node: '@sage/xtrem-structure/ChartOfAccount',
                        valueField: { legislation: { id: true } },
                        isHidden: true,
                    }),
                ],
                orderBy: { name: 1 },
                filter() {
                    return { chartOfAccount: { _id: this.chartOfAccountSysId } };
                },
                async onChange(rowId, rowData) {
                    if (rowData.account.taxManagement !== 'excludingTax') {
                        rowData.tax = '';
                    }
                    rowData.updateAccountTaxManagement = await this.taxManagementUpdate(rowData);
                    this.matchingRules.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.dropdownList({
                bind: 'transactionType',
                title: 'Transaction type',
                optionType: '@sage/xtrem-intacct-finance/IntacctRecordTransactionType',
                hasEmptyValue: true,
            }),
            ui.nestedFields.reference<IntacctBankAccountMatchingDialog, IntacctBankAccountMatching, Tax>({
                node: '@sage/xtrem-tax/Tax',
                valueField: 'name',
                isAutoSelectEnabled: true,
                bind: 'tax',
                title: 'Tax',
                columns: [ui.nestedFields.text({ bind: 'name' })],
                filter() {
                    return { legislation: { id: this.legislationId } };
                },
                isMandatory(rowId, rowData) {
                    return rowData?.account.taxManagement === 'excludingTax';
                },
                async onChange(rowId, rowData) {
                    rowData.updateAccountTaxManagement = await this.taxManagementUpdate(rowData);
                    this.matchingRules.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.reference<IntacctBankAccountMatchingDialog, IntacctBankAccountMatching, Site>({
                node: '@sage/xtrem-system/Site',
                isHidden: true,
                valueField: 'name',
                bind: 'location',
                columns: [ui.nestedFields.text({ bind: 'name' })],
            }),
            ui.nestedFields.text<IntacctBankAccountMatchingDialog, IntacctBankAccountMatching>({
                isHidden: true,
                bind: 'storedAttributes',
            }),

            ui.nestedFields.text<IntacctBankAccountMatchingDialog, IntacctBankAccountMatching>({
                isHidden: true,
                bind: 'storedDimensions',
            }),
            ui.nestedFields.checkbox<
                IntacctBankAccountMatchingDialog,
                IntacctBankAccountMatching & { updateAccountTaxManagement?: boolean }
            >({
                bind: 'updateAccountTaxManagement',
                isTransientInput: true,
                isHidden: true,
            }),
        ],
        orderBy: { _id: 1 },
        dropdownActions: [
            {
                icon: 'delete',
                title: 'Delete',
                isDestructive: true,
                async onClick(rowId, rowData) {
                    if (
                        await confirmDialogWithAcceptButtonText(this, {
                            acceptButtonText: ui.localize(
                                '@sage/xtrem-intacct-finance/pages__delete_page_dialog_title',
                                'Confirm deletion',
                            ),
                            message: ui.localize(
                                '@sage/xtrem-intacct-finance/pages__delete_page_dialog_content',
                                'You are about to delete this record.',
                            ),
                            title: ui.localize('@sage/xtrem-intacct-finance/pages-confirm-delete', 'Delete'),
                        })
                    ) {
                        await this.$.graph
                            .node('@sage/xtrem-intacct-finance/IntacctBankAccountMatching')
                            .deleteById(rowData._id.toString())
                            .execute();
                        await this.init(); // Reload
                    }
                },
            },
            {
                icon: 'save',
                title: `Save`,
                onError(e) {
                    this.$.showToast(e.message, { timeout: 0, type: 'error' });
                },
                async onClick(rowId, rowData) {
                    await this.createOrUpdateLine(rowData); // create or update
                    await this.init(); // Reload
                },
            },
            {
                icon: 'edit',
                title: 'Dimensions',
                async onClick(rowId, rowItem) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await utils.applyPanelToLineIfChanged(
                        this.matchingRules,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable: true,
                            },
                        ),
                    );
                },
            },
        ],
        fieldActions() {
            return [this.addRule];
        },
    })
    matchingRules: ui.fields.Table<
        IntacctBankAccountMatching & { isModified?: boolean; updateAccountTaxManagement?: boolean }
    >;

    @ui.decorators.pageAction<IntacctBankAccountMatchingDialog>({
        icon: 'add',
        title: 'Add rule',
        onClick() {
            this.matchingRules.addRecord({ isModified: true });
        },
    })
    addRule: ui.PageAction;

    async taxManagementUpdate(rowData: IntacctBankAccountMatching): Promise<boolean> {
        if (
            this.isSubjectToGlTaxExcludedAmount &&
            rowData.account &&
            rowData.tax &&
            rowData.account.taxManagement !== 'excludingTax' &&
            rowData.account.chartOfAccount?.legislation?.id === 'ZA'
        ) {
            const confirmation = await utils.confirmDialogToBoolean(
                this.$.dialog.confirmation(
                    'warn',
                    ui.localize(
                        '@sage/xtrem-intacct-finance/update-account-tax-management-title',
                        'Confirm account tax management update.',
                    ),
                    ui.localize(
                        '@sage/xtrem-intacct-finance/update-account-tax-management-context',
                        "You are about to set the account management to 'Excluding tax'.",
                    ),
                    {
                        acceptButton: {
                            text: ui.localize('@sage/xtrem-intacct-finance/confirm-update', 'Confirm update'),
                        },
                        cancelButton: {
                            text: ui.localize('@sage/xtrem-intacct-finance/cancel', 'Cancel'),
                        },
                    },
                ),
            );
            if (!confirmation) {
                await this.$.dialog.message(
                    'error',
                    ui.localize('@sage/xtrem-intacct-finance/error', 'Error'),
                    ui.localize(
                        '@sage/xtrem-intacct-finance/cannot_set_tax_with_account_not_subjected_to_taxes',
                        'You cannot set a tax detail with an account that is not subjected to taxes.',
                    ),
                    { resolveOnCancel: true },
                );
            }
            return confirmation;
        }
        return false;
    }

    async createOrUpdateLine(rowData: ui.PartialNodeWithId<IntacctBankAccountMatching & { isModified?: boolean }>) {
        delete rowData.isModified;

        const data = {
            ...rowData,
            account: rowData.account?._id || null,
            tax: rowData.tax?._id || null,
            location: rowData.location?._id || null,
        } as IntacctBankAccountMatchingInput;

        if (+rowData._id > 0) {
            await this.$.graph
                .node('@sage/xtrem-intacct-finance/IntacctBankAccountMatching')
                .update({ _id: true }, { data })
                .execute();
        } else {
            await this.$.graph
                .node('@sage/xtrem-intacct-finance/IntacctBankAccountMatching')
                .create({ _id: true }, { data })
                .execute();
        }
    }

    async init() {
        this.matchingRules.value = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-intacct-finance/IntacctBankAccountMatching')
                .query(
                    {
                        edges: {
                            node: {
                                priority: true,
                                _id: true,
                                keyword: true,
                                type: true,
                                bankAccount: {
                                    _id: true,
                                    id: true,
                                    name: true,
                                },
                                account: {
                                    _id: true,
                                    name: true,
                                    taxManagement: true,
                                    chartOfAccount: { legislation: { id: true } },
                                },
                                transactionType: true,
                                tax: {
                                    name: true,
                                    _id: true,
                                },
                                location: {
                                    name: true,
                                    _id: true,
                                },
                                storedAttributes: true,
                                storedDimensions: true,
                            },
                        },
                    },
                    { first: 500 },
                )
                .execute(),
        );
    }

    async getIntacctConfig() {
        const intacctConfiguration = await this.$.graph
            .node('@sage/xtrem-intacct/Intacct')
            .queries.defaultInstance(
                {
                    legislation: { id: true },
                    country: { id: true },
                    chartOfAccount: { _id: true, name: true },
                },
                {},
            )
            .execute();

        this.chartOfAccountSysId = intacctConfiguration.chartOfAccount._id;
        this.legislationId = intacctConfiguration.legislation.id;
    }

    chartOfAccountSysId: string;

    legislationId: string;

    @ui.decorators.pageAction<IntacctBankAccountMatchingDialog>({
        title: 'Save',
        onError(e) {
            this.$.loader.isHidden = true;
            if (
                e.errors &&
                e.errors.length &&
                e.errors[0].extensions &&
                e.errors[0].extensions.diagnoses &&
                e.errors[0].extensions.diagnoses.length
            ) {
                this.$.showToast(e.errors[0].extensions.diagnoses[0].message, { type: 'error', timeout: 20000 });
            } else {
                this.$.showToast(e.message, { timeout: 0, type: 'error' });
            }
        },
        async onClick() {
            this.$.loader.isHidden = false;
            await this.bulkSave();
            await this.init();
            this.$.loader.isHidden = true;
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<IntacctBankAccountMatchingDialog>({
        title: 'Apply matching rules',
        buttonType: 'secondary',
        isHidden() {
            return !this.isDialogMode;
        },
        onClick() {
            this.$.finish({ isApplyMatchingRuleAsked: true, matchingRuleList: this.matchingRules.selectedRecords });
        },
    })
    apply: ui.PageAction;

    @ui.decorators.pageAction<IntacctBankAccountMatchingDialog>({
        title: 'Cancel',
        buttonType: 'tertiary',
        isHidden() {
            return !this.isDialogMode;
        },
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    /**
     * Saves collection as bulk
     */
    async bulkSave() {
        this.$.loader.isHidden = false;

        const saveLines = await this.$.graph
            .node('@sage/xtrem-intacct-finance/IntacctBankAccountMatching')
            .mutations.bulkSave(true, {
                intacctBankAccountMatchings: this.matchingRules.value.map(
                    line =>
                        ({
                            _id: line._id,
                            bankAccount: line.bankAccount?._id ? line.bankAccount._id : null,
                            priority: line.priority ? line.priority : 0,
                            keyword: line.keyword || '',
                            account: line.account?._id ? line.account._id : null,
                            transactionType: line.transactionType
                                ? (line.transactionType as IntacctRecordTransactionType)
                                : null,
                            tax: line.tax?._id ? line.tax?._id : null,
                            location: line.location?._id ? line.location?._id : null,
                            storedAttributes: line.storedAttributes?.length ? line.storedAttributes : null,
                            storedDimensions: line.storedDimensions?.length ? line.storedDimensions : null,
                            type: line.type,
                            updateAccountTaxManagement: line.updateAccountTaxManagement || false,
                        }) as IntacctBankAccountMatchingInput,
                ),
            })
            .execute();

        if (saveLines) {
            this.$.showToast(
                ui.localize('@sage/xtrem-intacct-finance/pages__matching_rules_bulk_save', 'Matching rules saved.'),
                { type: 'success' },
            );

            this.$.loader.isHidden = true;
        }
    }
}
