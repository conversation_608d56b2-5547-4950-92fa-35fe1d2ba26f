import type { OperationResultType } from '@sage/xtrem-client';
import type { IntacctMap as IntacctMapNode, MapLine, MapProperty } from '@sage/xtrem-intacct-finance-api';
import type { IntacctMap$Queries } from '@sage/xtrem-intacct-finance-api-partial';
import { intacct } from '@sage/xtrem-intacct/build/lib/menu-items/intacct';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { MetaNodeFactory, MetaNodeProperty } from '@sage/xtrem-metadata-api';
import type { SynchronizationDirection } from '@sage/xtrem-synchronization-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { noop } from 'lodash';
import { formatErrorIntacctMapping, outbound } from '../client-functions/common';
import { IntacctMapBase } from '../client-functions/intacct-map-base';
import {
    isDisableCreateAll,
    isDisableCreateAllIntacct,
    isEditable,
    recordValueDefaultforSpecificFields,
    resetRelationMappingFunc,
} from '../client-functions/intacct-map-lib';
import type { AdditionnalLink, LinkedData, LookupObject, LookupProperties } from '../shared-functions/interfaces';

@ui.decorators.page<IntacctMap, IntacctMapNode>({
    module: 'xtrem-intacct-finance',
    node: '@sage/xtrem-intacct-finance/IntacctMap',
    title: 'Mapping',
    objectTypeSingular: 'Sage Intacct mapping',
    objectTypePlural: 'Sage Intacct mappings',
    idField() {
        return this.nodeFactory.value?.title || '';
    },
    menuItem: intacct,
    mode: 'tabs',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'id', title: 'Sage Intacct object' }),
            line2: ui.nestedFields.text({ bind: 'intacctDescription', title: 'Sage Intacct transactions' }),
            titleRight: ui.nestedFields.text({ bind: { nodeFactory: { title: true } }, title: 'Sage DMO node' }),
            line_4: ui.nestedFields.switch<IntacctMap>({ title: 'Smart Event', bind: 'isSmartEvent' }),
            line_5: ui.nestedFields.dropdownList<IntacctMap>({
                title: 'Synchronization way',
                bind: 'synchronizationDirection',
                optionType: '@sage/xtrem-synchronisation/SynchronizationDirection',
            }),
            line6: ui.nestedFields.switch<IntacctMap>({ title: 'Include private', bind: 'isPrivateShow' }),
        },
    },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return [this.createAll, this.createAllIntacct, this.writeMappingFile, this.save];
    },
    async onLoad() {
        await this.baseOnload();

        this.intacctNameSelect.options = this.intacctObjectList
            ? this.intacctObjectList.map(intacctObject => intacctObject.name)
            : [''];
        if (
            this.intacctDescription.value &&
            this.intacctObjectList.find(objectFind => objectFind.name === this.intacctDescription.value)
        ) {
            this.intacctNameSelect.value = this.intacctDescription.value;
        }

        if (this.id.value) {
            if (this.synchronizationDirection.value === outbound) {
                this.dataSection.isHidden = true;
            } else {
                this.setTitlesForData();
                this.dataSection.isHidden = false;
            }
            this.lookupObject = (
                this.relationMapping.value ? JSON.parse(this.relationMapping.value) : {}
            ) as LookupObject;
            this.fields.value = this.lookupObject.fields as ui.PartialNodeWithId<LookupProperties>[];
            this.relationshipFields.value =
                (this.lookupObject.relationshipFields as ui.PartialNodeWithId<LookupProperties>[]) || [];
        }

        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.save,
        });
        this.$.setPageClean();

        this.additionnalLinkFormated.value = this.additionnalLink?.value
            ? JSON.parse(this.additionnalLink.value)
                  .map(
                      (link: AdditionnalLink) =>
                          `${link.xtremProperty}=[${link.xtremValues?.join(', ') || link.intacctConfigProperty}]`,
                  )
                  .join(' / ')
            : '';

        const synchronizationDirection = this.synchronizationDirection.value as SynchronizationDirection;
        /** The isDisabled() decorator in the pageAction doesn't work anymore   */
        this.createAll.isDisabled = isDisableCreateAll(synchronizationDirection);
        this.createAllIntacct.isDisabled = isDisableCreateAllIntacct(synchronizationDirection);

        await this.manageMassImport();
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.save,
        });
        this.createAll.isDisabled = isDirty;
        this.createAllIntacct.isDisabled = isDirty;
    },
    onError(error) {
        if (!(error instanceof Error) && typeof error !== 'string') {
            return noop();
        }
        return utils.formatError(this, error as any);
    },
})
export class IntacctMap extends IntacctMapBase {
    @ui.decorators.pageAction<IntacctMap>({
        title: 'Save',
        access: { bind: '$update' },
        async onClick() {
            await this.$standardSaveAction.execute(true);
            await this.updateRelationMapping(false);
        },
    })
    save: ui.PageAction;

    @ui.decorators.section<IntacctMap>({
        title: 'Mapping',
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<IntacctMap>({
        parent() {
            return this.mainSection;
        },
        title: 'Configuration',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<IntacctMap>({}) _id: ui.fields.Text;

    @ui.decorators.textField<IntacctMap>({}) notificationId: ui.fields.Text;

    @ui.decorators.switchField<IntacctMap>({
        parent() {
            return this.mainBlock;
        },
        title: 'Active',
        isHidden: true,
    })
    isActive: ui.fields.Switch;

    @ui.decorators.textField<IntacctMap>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        title: 'Sage Intacct object',
    })
    id: ui.fields.Text;

    @ui.decorators.textField<IntacctMap>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        isHidden: true,
        title: 'Sage Intacct transactions',
    })
    intacctDescription: ui.fields.Text;

    @ui.decorators.selectField<IntacctMap>({
        parent() {
            return this.mainBlock;
        },
        isTransient: true,
        isReadOnly() {
            return !!this.$.recordId;
        },
        async onChange() {
            if (this.intacctNameSelect.value) {
                const objectListFind = this.intacctObjectList.find(
                    objectToFind => objectToFind.name === this.intacctNameSelect.value,
                );
                if (objectListFind) {
                    this.id.value = objectListFind.object;
                    this.intacctDescription.value = objectListFind.name;
                }
                await this.updateRelationMapping();
            }
        },
        title: 'Sage Intacct transactions',
    })
    intacctNameSelect: ui.fields.Select;

    @ui.decorators.referenceField<IntacctMap, MetaNodeFactory>({
        node: '@sage/xtrem-metadata/MetaNodeFactory',
        title: 'Node factory',
        parent() {
            return this.mainBlock;
        },
        valueField: 'title',
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Node' }),
            ui.nestedFields.text({ bind: 'title', title: 'Node' }),
        ],
        async onChange() {
            await this.nodeProperties.refresh();
        },
    })
    nodeFactory: ui.fields.Reference<MetaNodeFactory>;

    @ui.decorators.tableField<IntacctMap, MetaNodeProperty>({
        parent() {
            return this.mainBlock;
        },
        title: 'Fields',
        bind: { nodeFactory: { properties: true } },
        node: '@sage/xtrem-metadata/MetaNodeProperty',
        isHidden: true,
        columns: [
            ui.nestedFields.technical({ bind: { _id: true } }),
            ui.nestedFields.technical({ bind: { name: true } }),
            ui.nestedFields.technical({ bind: { title: true } }),
        ],
    })
    nodeProperties: ui.fields.Table<MetaNodeProperty>;

    @ui.decorators.switchField<IntacctMap>({
        parent() {
            return this.mainBlock;
        },
        title: 'Smart Event',
    })
    isSmartEvent: ui.fields.Switch;

    @ui.decorators.switchField<IntacctMap>({
        parent() {
            return this.mainBlock;
        },
        title: 'Include private',
    })
    isPrivateShow: ui.fields.Switch;

    @ui.decorators.dropdownListField<IntacctMap>({
        title: 'Synchronization way',
        parent() {
            return this.mainBlock;
        },
        optionType: '@sage/xtrem-synchronization/SynchronizationDirection',
    })
    synchronizationDirection: ui.fields.DropdownList;

    @ui.decorators.labelField<IntacctMap>({
        parent() {
            return this.mainBlock;
        },
        isTransient: true,
        optionType: '@sage/xtrem-communication/NotificationStatus',
        title: 'Mass import status',
        async onClick() {
            await this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationState',
                { _id: this.notificationSysId },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    notificationStatus: ui.fields.Label;

    @ui.decorators.tableField<IntacctMap, LookupProperties & { _id: string }>({
        parent() {
            return this.mainSection;
        },
        title: 'Fields',
        canSelect: false,
        isTransient: true,
        canExport: true,
        canResizeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: 'ID', title: 'ID', isReadOnly: true }),
            ui.nestedFields.text({ bind: 'LABEL', title: 'Label', isReadOnly: true }),
            ui.nestedFields.text({
                bind: 'DESCRIPTION',
                title: 'Description',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.checkbox({
                bind: 'REQUIRED',
                title: 'Required',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.checkbox({
                bind: 'READONLY',
                title: 'Read only',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text({ bind: 'DATATYPE', title: 'Data type', isReadOnly: true, isHiddenOnMainField: true }),
            ui.nestedFields.checkbox({ bind: 'ISCUSTOM', title: 'Custom', isReadOnly: true }),
            ui.nestedFields.text({
                bind: 'xtremProperty',
                title: 'Sage DMO property',
                icon: 'hide',
                isReadOnly(_value, rowValue) {
                    return !isEditable(rowValue as LookupProperties);
                },
            }),
            ui.nestedFields.icon({
                bind: 'isEditable',
                title: 'Editable',
                map(_value, rowValue) {
                    return isEditable(rowValue as LookupProperties) ? 'tick' : 'none';
                },
            }),
            ui.nestedFields.text({ bind: 'xtremDefaultProperty', title: 'Default property', isReadOnly: true }),
        ],
        fieldActions() {
            return [this.refreshRelationMapping, this.resetRelationMapping];
        },
    })
    fields: ui.fields.Table<LookupProperties & { _id: string }>;

    @ui.decorators.pageAction<IntacctMap>({
        icon: 'refresh',
        title: 'Refresh',
        async onClick() {
            await this.updateRelationMapping(true);
        },
    })
    refreshRelationMapping: ui.PageAction;

    @ui.decorators.pageAction<IntacctMap>({
        icon: 'remove',
        title: 'Reset',
        async onClick() {
            await resetRelationMappingFunc(this);
        },
    })
    resetRelationMapping: ui.PageAction;

    @ui.decorators.tableField<IntacctMap, LookupProperties & { _id: string }>({
        parent() {
            return this.mainSection;
        },
        title: 'Relationship',
        canSelect: false,
        canExport: true,
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: 'ID', title: 'ID', isReadOnly: true }),
            ui.nestedFields.text({ bind: 'LABEL', title: 'Label', isReadOnly: true }),
            ui.nestedFields.text({ bind: 'DESCRIPTION', title: 'Description', isReadOnly: true }),
            ui.nestedFields.checkbox({
                bind: 'REQUIRED',
                title: 'Required',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.checkbox({
                bind: 'READONLY',
                title: 'Read only',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text({ bind: 'DATATYPE', title: 'Data type', isReadOnly: true, isHiddenOnMainField: true }),
            ui.nestedFields.checkbox({ bind: 'ISCUSTOM', title: 'Custom', isReadOnly: true }),
            ui.nestedFields.text({
                bind: 'xtremProperty',
                title: 'Sage DMO property',

                isReadOnly(_value, rowValue) {
                    return !isEditable(rowValue as LookupProperties);
                },
            }),
            ui.nestedFields.icon({
                bind: 'isEditable',
                title: 'Editable',
                map(_value, rowValue) {
                    return isEditable(rowValue as LookupProperties) ? 'tick' : 'none';
                },
            }),
            ui.nestedFields.text({ bind: 'xtremDefaultProperty', title: 'Default property', isReadOnly: true }),
            ui.nestedFields.technical({ bind: 'isEditable' }),
        ],
    })
    relationshipFields: ui.fields.Table<LookupProperties & { _id: string }>;

    @ui.decorators.textField<IntacctMap>({ isHidden: true }) relationMapping: ui.fields.Text;

    getSerializedValues() {
        const functionClean = (val: any) => {
            if (val._id) delete val._id;
            if (val.__action) delete val.__action;
            return val;
        };

        this.lookupObject.xtremObject = this.nodeFactory.value?.name || '';
        this.lookupObject.fields = this.fields.value.map(val => functionClean(val)) || [];
        this.lookupObject.relationshipFields = this.relationshipFields.value.map(val => functionClean(val)) || [];

        const { values } = this.$;
        values.relationMapping = JSON.stringify(this.lookupObject);

        return values;
    }

    /** **** Collection / detail part   */
    @ui.decorators.section<IntacctMap>({
        title: 'Collections/Lines',
        isTitleHidden: true,
    })
    lineSection: ui.containers.Section;

    @ui.decorators.tableField<IntacctMap, MapLine>({
        parent() {
            return this.lineSection;
        },
        title: 'Mapping',
        node: '@sage/xtrem-intacct-finance/MapLine',
        canSelect: true,
        columns: [
            ui.nestedFields.text({ bind: 'collectionName', title: 'Collection name', canFilter: false }),
            ui.nestedFields.reference({
                bind: 'line',
                valueField: 'id',
                title: 'Object',
                isReadOnly: true,
                node: '@sage/xtrem-intacct-finance/IntacctMap',
            }),
            ui.nestedFields.reference<IntacctMap>({
                bind: 'line',
                valueField: 'intacctDescription',
                title: 'Name',
                node: '@sage/xtrem-intacct-finance/IntacctMap',
            }),
            ui.nestedFields.text({ bind: 'propertyHeader', title: 'Header property', canFilter: false }),
            ui.nestedFields.text({ bind: 'propertyLine', title: 'Line property', canFilter: false }),
        ],
    })
    lines: ui.fields.Table<MapLine>;

    @ui.decorators.section<IntacctMap>({
        title: 'Data',
        isTitleHidden: true,
    })
    dataSection: ui.containers.Section;

    @ui.decorators.block<IntacctMap>({
        parent() {
            return this.dataSection;
        },
        title: 'Sage Intacct',
        isTitleHidden: true,
    })
    intacctDataBlock: ui.containers.Block;

    @ui.decorators.textField<IntacctMap>({
        parent() {
            return this.intacctDataBlock;
        },
        title: 'Additionnal links',
        isHidden: true,
    })
    additionnalLink: ui.fields.Text;

    @ui.decorators.textField<IntacctMap>({
        parent() {
            return this.intacctDataBlock;
        },
        title: 'Sage Intacct filters',
        width: 'large',
        isReadOnly: true,
    })
    intacctFilter: ui.fields.Text;

    @ui.decorators.textField<IntacctMap>({
        parent() {
            return this.intacctDataBlock;
        },
        title: 'Additionnal links',
        width: 'large',
        isTransient: true,
        isReadOnly: true,
    })
    additionnalLinkFormated: ui.fields.Text;

    @ui.decorators.tableField<IntacctMap, MapProperty & { whereValue: string }>({
        parent() {
            return this.dataSection;
        },
        title: 'Technical properties',
        node: '@sage/xtrem-intacct-finance/MapProperty',
        canFilter: false,
        canSelect: false,
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Sage Intacct property name' }),
            ui.nestedFields.dropdownList({
                bind: 'type',
                title: 'Type',
                optionType: '@sage/xtrem-intacct-finance/PropertyType',
            }),
            ui.nestedFields.text({ bind: 'whereValue', title: 'Filter', isTransient: true }),
        ],
        dropdownActions: [
            {
                icon: 'minus',
                title: 'Remove',
                onClick(rowId: string) {
                    this.specificFields.removeRecord(rowId);
                },
            },
        ],
        onChange() {
            this.setTitlesForData();
        },
        fieldActions() {
            return [this.addLineSpecificFields];
        },
    })
    specificFields: ui.fields.Table<MapProperty & { whereValue: string }>;

    @ui.decorators.pageAction<IntacctMap>({
        icon: 'add',
        title: 'Add component',
        onClick() {
            this.specificFields.addOrUpdateRecordValue({
                type: recordValueDefaultforSpecificFields(this.specificFields.value),
                name: '',
            });
        },
    })
    addLineSpecificFields: ui.PageAction;

    @ui.decorators.tableField<IntacctMap, LinkedData>({
        parent() {
            return this.dataSection;
        },
        title: 'Results',
        canSelect: false,
        isTransient: true,
        isReadOnly: true,
        columns: [
            ui.nestedFields.link({
                bind: 'id',
                title() {
                    return this.columnTitleProperty.intacctId;
                },
                onClick(_id, data: LinkedData) {
                    this.$.router.goToExternal(data.url);
                },
            }),
            ui.nestedFields.text({
                bind: 'name',
                title() {
                    return this.columnTitleProperty.intacctName;
                },
            }),
            ui.nestedFields.text({
                bind: 'description',
                title() {
                    return this.columnTitleProperty.intacctDescription;
                },
            }),
            ui.nestedFields.date({ bind: 'whenCreated', title: 'Create' }),
            ui.nestedFields.date({ bind: 'whenModified', title: 'Update' }),
            ui.nestedFields.text({
                bind: 'megaEntityId',
                title: 'Entity ID',
                isHidden() {
                    return !this.isPrivateShow.value;
                },
            }),
            ui.nestedFields.checkbox({ bind: 'isLinked', title: 'Linked' }),
            ui.nestedFields.text({
                bind: 'xtremSysId',
                title() {
                    return this.columnTitleProperty.xtremId;
                },
                isHidden: true,
            }),
            ui.nestedFields.text({
                bind: 'xtremID',
                title() {
                    return this.columnTitleProperty.xtremId;
                },
            }),
            ui.nestedFields.text({
                bind: 'xtremName',
                title() {
                    return this.columnTitleProperty.xtremName;
                },
            }),
            ui.nestedFields.text({
                bind: 'xtremDescription',
                title() {
                    return this.columnTitleProperty.xtremDescription;
                },
            }),
            ui.nestedFields.text({
                bind: 'integrationStatus',
                isHidden() {
                    return this.synchronizationDirection.value === 'inbound';
                },
                title: 'Integration status',
            }),
            ui.nestedFields.technical({ bind: 'url' }),
        ],
        dropdownActions: [
            {
                icon: 'attach',
                title: 'Create in Sage DMO',
                isDisabled(rowId, rowData) {
                    if (this.synchronizationDirection.value === 'outbound') {
                        return true;
                    }
                    return +(rowData.xtremSysId || 0) !== 0;
                },
                onError(error) {
                    return formatErrorIntacctMapping(this, error);
                },
                async onClick(rowId, rowData) {
                    await this.updateXtrem({ id: rowData.id || '', xtremSysId: rowData.xtremSysId || '' });
                },
            },
            {
                icon: 'chevron_up',
                title: 'Update in Sage DMO',
                isDisabled(rowId, rowData) {
                    if (this.synchronizationDirection.value === 'outbound') {
                        return true;
                    }
                    if (!rowData.id) {
                        return true;
                    }
                    return +(rowData.xtremSysId || 0) === 0;
                },
                onError(error) {
                    return formatErrorIntacctMapping(this, error);
                },
                async onClick(rowId, rowData) {
                    await this.updateXtrem({ id: rowData.id || '', xtremSysId: rowData.xtremSysId || '' });
                },
            },
            {
                icon: 'cross',
                title: 'Delete in Sage DMO',
                isDisabled(rowId, rowData) {
                    return +(rowData.xtremSysId || 0) === 0;
                },
                async onClick(rowId, rowData) {
                    this.$.loader.isHidden = false;
                    if (this.id.value) {
                        await this.$.graph
                            .node('@sage/xtrem-intacct-finance/IntacctMap')
                            .mutations.deleteXtrem(
                                { message: true, path: true, severity: true },
                                {
                                    intacctIdValue: rowData.id,
                                    intacctName: this.id.value,
                                    xtremSysId: rowData.xtremSysId,
                                },
                            )
                            .execute()
                            .then(result => {
                                this.$.showToast(`The record has been updated ${result}`, { type: 'success' });
                            })
                            .catch(e => {
                                this.$.showToast(e.message, { timeout: 0, type: 'error' });
                            })
                            .finally(() => {
                                this.$.loader.isHidden = true;
                            });
                        this.linkedData.value = await this.loadIntacctXtremData();
                    }
                },
            },
        ],
        fieldActions() {
            return [this.refreshData];
        },
    })
    linkedData: ui.fields.Table<LinkedData>;

    @ui.decorators.pageAction<IntacctMap>({
        icon: 'refresh',
        title: 'Refresh',
        async onClick() {
            this.linkedData.value = (await this.loadIntacctXtremData()) as [];
        },
    })
    refreshData: ui.PageAction;

    @ui.decorators.pageAction<IntacctMap>({
        icon: 'analysis',
        title: 'Create all in Sage DMO',
        isDisabled() {
            return !['both', 'inbound'].includes(this.synchronizationDirection.value || '');
        },
        async onError(error) {
            this.$.loader.isHidden = true;
            await this.$.dialog.message(
                'error',
                ui.localize(
                    '@sage/xtrem-intacct-finance/page__intacct_mapping_create_all_in_intacct_manufacturing',
                    'Create all in Sage DMO.',
                ),
                formatErrorIntacctMapping(this, error),
                { acceptButton: { text: 'Ok' }, mdContent: true },
            );
        },
        async onClick() {
            this.$.loader.isHidden = false;
            await this.updateXtrem();
            this.$.loader.isHidden = true;
        },
    })
    createAll: ui.PageAction;

    @ui.decorators.pageAction<IntacctMap>({
        icon: 'analysis',
        title: 'Create all in Sage Intacct',
        isDisabled() {
            return !['both', 'outbound'].includes(this.synchronizationDirection.value || '');
        },
        onError(error) {
            this.$.loader.isHidden = true;
            return error;
        },
        async onClick() {
            if (this.id.value) {
                this.$.loader.isHidden = false;
                const result = await this.$.graph
                    .node('@sage/xtrem-intacct-finance/IntacctMap')
                    .mutations.createUpdateAllIntacct(true, { intacctName: this.id.value })
                    .execute();
                if (result) {
                    await this.$.dialog.message(
                        'error',
                        ui.localize(
                            '@sage/xtrem-intacct-finance/page__intacct_mapping_create_all_in_intacct',
                            'Create all in Sage Intacct.',
                        ),
                        formatErrorIntacctMapping(this, result),
                        { acceptButton: { text: 'Ok' }, mdContent: true },
                    );
                }
                await this.notificationId.refresh();
                this.$.loader.isHidden = true;
                await this.manageMassImport();
            }
        },
    })
    createAllIntacct: ui.PageAction;

    @ui.decorators.pageAction<IntacctMap>({
        icon: 'analysis',
        title: 'Write mapping file',
        access: {
            node: '@sage/xtrem-intacct-finance/IntacctMap',
            bind: 'writeStructure',
        },
        async onClick() {
            if (this.id.value) {
                await this.$.graph
                    .node('@sage/xtrem-intacct-finance/IntacctMap')
                    .mutations.writeStructure(true, { object: this.id.value })
                    .execute();
            }
        },
    })
    writeMappingFile: ui.PageAction;

    /**
     * update Or create xtrem (SDMO) data
     * @param rowData id & xtremSysId parameter
     */
    async updateXtrem(rowData?: { id: string; xtremSysId: string }) {
        if (this.id.value) {
            const parameters = rowData
                ? { intacctIdValue: rowData?.id, xtremSysId: rowData?.xtremSysId.toString() }
                : {};

            const updateReturn = await this.$.graph
                .node('@sage/xtrem-intacct-finance/IntacctMap')
                .asyncOperations.xtreemMassCreationJob.runToCompletion(
                    { created: true, updated: true, error: true, message: true },
                    {
                        data: { isThrowingDiagnose: true, intacctName: this.id.value, ...parameters },
                    },
                )
                .execute();

            if (updateReturn) {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-intacct-finance/update-xtreem-return',
                        'Records created: {{created}}. \n Records updated: {{updated}}. \n {{message}}',
                        updateReturn,
                    ),
                    {
                        type: 'info',
                    },
                );
            }
            if (rowData) {
                this.linkedData.value = (await this.loadIntacctXtremData()) as [];
            }
        }
    }

    /**
     *  Get the relationMapping object ( file if not stored ) & fill fields & relationshipFields
     * @param isUpdate to sync with intacct ( check if new fields ISCUSTOM as been created )
     */
    async updateRelationMapping(isUpdate = false) {
        this.lookupObject = await this.loadIntacctObject(
            { id: this.id.value, intacctName: this.intacctDescription.value },
            isUpdate,
        );
        this.fields.value = this.lookupObject.fields as ui.PartialNodeWithId<LookupProperties>[];
        this.relationshipFields.value =
            (this.lookupObject.relationshipFields as ui.PartialNodeWithId<LookupProperties>[]) || [];
    }

    loadIntacctXtremData(): Promise<OperationResultType<IntacctMap$Queries['getDataIntacct']>> {
        if (this.id.value && this.intacctDescription.value) {
            const filters = this.specificFields.value
                .filter(fieldFiler => fieldFiler.name && fieldFiler.whereValue)
                .map(field => ({ where: field.name, whereValue: field.whereValue }));
            return this.$.graph
                .node('@sage/xtrem-intacct-finance/IntacctMap')
                .queries.getDataIntacct(
                    {
                        _id: true,
                        id: true,
                        name: true,
                        description: true,
                        whenCreated: true,
                        whenModified: true,
                        url: true,
                        megaEntityId: true,
                        isLinked: true,
                        xtremSysId: true,
                        xtremID: true,
                        xtremName: true,
                        xtremDescription: true,
                        integrationStatus: true,
                    },
                    { name: this.id.value, transaction: this.intacctDescription.value, maxData: 500, filters },
                )
                .execute();
        }
        return Promise.resolve([]);
    }

    setTitlesForData() {
        /**
         *  5 : xtremIntacctFinance.enums.PropertyType.intacctId
         *  6 : xtremIntacctFinance.enums.PropertyType.name
         *  2 : xtremIntacctFinance.enums.PropertyType.description
         */
        this.columnTitleProperty.xtremId = `xtrem ${
            this.specificFields.value.find(field => field.type === 'intacctId')?.name ||
            ui.localize('@sage/xtrem-intacct-finance/id', 'ID')
        }`;
        this.columnTitleProperty.intacctId = `intacct ${
            this.specificFields.value.find(field => field.type === 'intacctId')?.name ||
            ui.localize('@sage/xtrem-intacct-finance/id', 'ID')
        }`;

        this.columnTitleProperty.xtremName = `xtrem ${
            this.specificFields.value.find(field => field.type === 'name')?.name ||
            ui.localize('@sage/xtrem-intacct-finance/name', 'Name')
        }`;
        this.columnTitleProperty.intacctName = `intacct ${
            this.specificFields.value.find(field => field.type === 'name')?.name ||
            ui.localize('@sage/xtrem-intacct-finance/name', 'Name')
        }`;

        this.columnTitleProperty.xtremDescription = `xtrem ${
            this.specificFields.value.find(field => field.type === 'description')?.name ||
            ui.localize('@sage/xtrem-intacct-finance/description', 'Description')
        }`;
        this.columnTitleProperty.intacctDescription = `intacct ${
            this.specificFields.value.find(field => field.type === 'description')?.name ||
            ui.localize('@sage/xtrem-intacct-finance/description', 'Description')
        }`;
    }

    async manageMassImport() {
        this.notificationStatus.isHidden = true;
        if (this.notificationId.value) {
            const asyncProcess = await this.$.graph
                .node('@sage/xtrem-communication/SysNotificationState')
                .read({ status: true, _id: true }, this.notificationId.value)
                .execute();
            if (asyncProcess) {
                this.notificationStatus.isHidden = false;
                this.notificationStatus.value = asyncProcess.status;
                this.notificationSysId = asyncProcess._id;
            }
        }
    }
}
