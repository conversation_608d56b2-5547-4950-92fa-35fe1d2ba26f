import type { Account, BankAccount } from '@sage/xtrem-finance-data-api';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type {
    GraphApi,
    IntacctBankAccountMatchingInput,
    IntacctBankAccountTransactionFeed,
    IntacctBankAccountTransactionFeedLine,
    IntacctMap,
} from '@sage/xtrem-intacct-finance-api';
import type {
    IntacctBankAccountTransactionFeedInput,
    IntacctImportSessionInput,
} from '@sage/xtrem-intacct-finance-api-partial';
import type { Currency } from '@sage/xtrem-master-data-api';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { decimal } from '@sage/xtrem-shared';
import type { Site } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import type { Tax } from '@sage/xtrem-tax-api';
import * as ui from '@sage/xtrem-ui';
import { ControlRules } from '../shared-functions/index';

@ui.decorators.page<IntacctTransactionFeedQuery>({
    title: 'Transaction workbench',
    objectTypeSingular: '',
    objectTypePlural: 'Transaction feeds',
    mode: 'default',
    node: '@sage/xtrem-intacct-finance/IntacctImportSession',
    access: {
        node: '@sage/xtrem-intacct-finance/IntacctBankAccountTransactionFeed',
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.reference({
                bind: 'bankAccount',
                title: 'Bank',
                node: '@sage/xtrem-finance-data/BankAccount',
                valueField: 'name',
                tunnelPage: undefined,
            }),
            line2: ui.nestedFields.text({ bind: 'description' }),
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: { mapObject: { id: 'BANKACCTTXNRECORD' } },
            },
        ],
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.post, this.$standardSaveAction];
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return utils.formatError(this, error);
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
        await this.getIntacctConfig();
        this.taxCache = [];
        if (this._id.value && this.queryParameters.value && this.counter.value) {
            const queryParameters = JSON.parse(this.queryParameters.value);
            this.transactionType.value = queryParameters.transactionType || null;
            this.dateFrom.value = queryParameters.dateFrom || null;
            this.dateTo.value = queryParameters.dateTo || null;
            this.matchStatus.value = queryParameters.status || null;

            this.status.isHidden = this.status.value === 'pending';

            const counterValues = JSON.parse(this.counter.value);
            this.totalDeposit.value = +counterValues.totalDeposit;
            this.totalWithdrawal.value = +counterValues.totalWithdrawal;
            this.totalAmount.value = +counterValues.totalAmount;
            this.unMatched.value = counterValues.unMatch;
            this.matched.value = counterValues.matched;

            this.setCriteriaReadOnly(true);
            // if we set the transactionFeed as not transient we don't update this anymore when loading the page
        } else {
            this.setCriteriaReadOnly(false);
            this.matchStatus.value = 'Unmatched';
            this.mapObject.value = await this.$.graph
                .node('@sage/xtrem-intacct-finance/IntacctMap')
                .read(
                    {
                        _id: true,
                        id: true,
                        intacctDescription: true,
                    },
                    '#intacct|BANKACCTTXNRECORD|IntacctBankAccountTransactionFeed',
                )
                .execute();

            if (!this.mapObject.value) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-intacct-finance/pages__intact_transaction_feed_query__map_object_mandatory',
                        'The mapping object Bank account transaction feed records not defined',
                    ),
                );
            }
        }
        this.searchButton.isDisabled =
            !this._id.value || (!!this._id.value && this.linesImported.value !== this.numberOfLinesToImport.value);
        this.$.setPageClean();
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class IntacctTransactionFeedQuery extends ui.Page<GraphApi> {
    taxCache: { sysId: string; date: string; rate: decimal }[];

    async calculateTax(
        value: IntacctBankAccountTransactionFeedLine,
    ): Promise<{ taxRate: decimal; taxAmount: decimal }> {
        if (!value.tax) {
            return { taxRate: 0, taxAmount: 0 };
        }

        const { taxAmount, rate } = await this.$.graph
            .node('@sage/xtrem-tax/Tax')
            .queries.calculateTax(
                {
                    rate: true,
                    taxAmount: true,
                },
                { tax: value.tax._id, taxDate: value.document.postingDate, amount: value.amount || 0 },
            )
            .execute();

        return {
            taxRate: +rate,
            taxAmount: +taxAmount,
        };
    }

    @ui.decorators.section<IntacctTransactionFeedQuery>({
        title: 'Transaction workbench',
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<IntacctTransactionFeedQuery>({
        parent() {
            return this.mainSection;
        },
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    setCriteriaReadOnly(isReadOnly: boolean) {
        this.bankAccount.isReadOnly = isReadOnly;
        this.transactionType.isReadOnly = isReadOnly;
        this.dateFrom.isReadOnly = isReadOnly;
        this.dateTo.isReadOnly = isReadOnly;
        this.matchStatus.isReadOnly = isReadOnly;
    }

    getSerializedValues() {
        const { values } = this.$;

        const jsonQueryParameters = {
            transactionType: this.transactionType.value,
            dateFrom: this.dateFrom.value,
            dateTo: this.dateTo.value,
            status: this.matchStatus.value,
        };

        values.queryParameters = JSON.stringify(jsonQueryParameters);

        values.bankAccount = this.bankAccount.value?._id;
        values.description = '';

        if (jsonQueryParameters.transactionType) {
            values.description += `Type: ${this.transactionType.value} `;
        }
        if (jsonQueryParameters.dateFrom) {
            values.description += `Date: ${this.dateFrom.value} - ${this.dateTo.value} `;
        }
        if (jsonQueryParameters.status) {
            values.description += `Status : ${this.matchStatus.value} `;
        }

        // When create delete the status on the IntacctBankAccountTransactionFeedLine
        this.transactionFeed.value
            .filter(feedFilter => +feedFilter._id < 0)
            .forEach((feed: ui.PartialNodeWithId<IntacctBankAccountTransactionFeed>) =>
                feed.lines?.forEach(line => delete line.status),
            );

        return values;
    }

    @ui.decorators.dateField<IntacctTransactionFeedQuery>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Creation date',
        isHidden: true,
    })
    dateCreation: ui.fields.Date;

    @ui.decorators.textField<IntacctTransactionFeedQuery>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'ID',
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<IntacctTransactionFeedQuery>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Etag',
        isHidden: true,
    })
    _etag: ui.fields.Text;

    @ui.decorators.textField<IntacctTransactionFeedQuery>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Description',
        isHidden: true,
    })
    description: ui.fields.Text;

    @ui.decorators.labelField<IntacctTransactionFeedQuery>({})
    queryParameters: ui.fields.Label;

    @ui.decorators.labelField<IntacctTransactionFeedQuery>({})
    counter: ui.fields.Label;

    @ui.decorators.labelField<IntacctTransactionFeedQuery>({})
    numberOfLinesToImport: ui.fields.Label;

    @ui.decorators.labelField<IntacctTransactionFeedQuery>({})
    linesImported: ui.fields.Label;

    @ui.decorators.labelField<IntacctTransactionFeedQuery>({
        parent() {
            return this.tileContainer;
        },
        optionType: '@sage/xtrem-import-export/ImportStatus',
        isHidden: true,
        async onClick() {
            await this.refreshImportStatus();
        },
    })
    status: ui.fields.Label;

    async refreshImportStatus() {
        if (this._id.value) {
            await this.numberOfLinesToImport.refresh();
            await this.linesImported.refresh();
            await this.status.refresh();
            await this.counter.refresh();
            await this.withPostableFeeds.refresh();
            await this._etag.refresh();

            const counterValues: {
                totalDeposit: number;
                totalWithdrawal: number;
                totalAmount: number;
                unMatch: number;
                matched: number;
            } = this.counter.value ? JSON.parse(this.counter.value) : {};
            this.totalDeposit.value = +counterValues.totalDeposit;
            this.totalWithdrawal.value = +counterValues.totalWithdrawal;
            this.totalAmount.value = +counterValues.totalAmount;
            this.unMatched.value = counterValues.unMatch;
            this.matched.value = counterValues.matched;

            this.status.helperText = `${this.linesImported.value}/${this.numberOfLinesToImport.value}`;
            this.post.isDisabled = this.isPostDisabled();
            this.searchButton.isDisabled = this.numberOfLinesToImport.value !== this.linesImported.value;
        }
    }

    @ui.decorators.referenceField<IntacctTransactionFeedQuery, IntacctMap>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Mapping',
        node: '@sage/xtrem-intacct-finance/IntacctMap',
        valueField: 'intacctDescription',
        columns: [
            ui.nestedFields.text({ bind: 'intacctDescription', title: 'Name' }),
            ui.nestedFields.text({ bind: 'id' }),
        ],
        isMandatory: true,
        isReadOnly: true,
        isHidden: true,
    })
    mapObject: ui.fields.Reference<IntacctMap>;

    @ui.decorators.referenceField<IntacctTransactionFeedQuery, BankAccount>({
        parent() {
            return this.criteriaBlock;
        },
        node: '@sage/xtrem-finance-data/BankAccount',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'megaEntityId', isHidden: true }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-system/Site',
                valueField: '_id',
                bind: 'financialSite',
                isHidden: true,
                columns: [ui.nestedFields.text({ bind: 'id' })],
            }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-master-data/Currency',
                valueField: '_id',
                bind: 'currency',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.text({ bind: 'decimalDigits' }),
                ],
            }),
        ],
        valueField: 'id',
        title: 'Bank account',
    })
    bankAccount: ui.fields.Reference<BankAccount>;

    @ui.decorators.dropdownListField<IntacctTransactionFeedQuery>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Transaction type',
        placeholder: 'Transaction type',
        optionType: '@sage/xtrem-intacct-finance/IntacctRecordTransactionType',
        width: 'small',
        hasEmptyValue: true,
        isTransient: true,
    })
    transactionType: ui.fields.DropdownList;

    // Date range – from and to
    @ui.decorators.dateField<IntacctTransactionFeedQuery>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From date',
        isMandatory: true,
        isTransient: true,
    })
    dateFrom: ui.fields.Date;

    @ui.decorators.dateField<IntacctTransactionFeedQuery>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To date',
        isMandatory: true,
        isTransient: true,
    })
    dateTo: ui.fields.Date;

    @ui.decorators.dropdownListField<IntacctTransactionFeedQuery>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Status',
        placeholder: 'Select status',
        options: ['Matched', 'Unmatched'],
        width: 'small',
        hasEmptyValue: true,
        isTransient: true,
    })
    matchStatus: ui.fields.DropdownList;

    @ui.decorators.buttonField<IntacctTransactionFeedQuery>({
        parent() {
            return this.criteriaBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-intacct-finance/search', 'Search');
        },
        isTransient: true,
        width: 'small',
        onClick() {
            if (!this.bankAccount.value) {
                throw Error(
                    ui.localize(
                        '@sage/xtrem-intacct-finance/transaction-feed-query-provide-bank-account',
                        'Enter the bank account number.',
                    ),
                );
            }
            if (!this.dateFrom.value && !this.dateTo.value) {
                throw Error(
                    ui.localize(
                        '@sage/xtrem-intacct-finance/transaction-feed-query-provide-dates',
                        'Enter values in the date range.',
                    ),
                );
            }
            this.$.loader.isHidden = false;
            this.searchButton.isDisabled = true;
            this.$.loader.isHidden = true;
        },
        isDisabled() {
            return (
                !this._id.value || (!!this._id.value && this.linesImported.value !== this.numberOfLinesToImport.value)
            );
        },
    })
    searchButton: ui.fields.Button;

    /** Technical field - Hidden */
    @ui.decorators.checkboxField<IntacctTransactionFeedQuery>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'With postable feeds',
        isHidden: true,
    })
    withPostableFeeds: ui.fields.Checkbox;

    @ui.decorators.tile<IntacctTransactionFeedQuery>({
        parent() {
            return this.mainSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.numericField<IntacctTransactionFeedQuery>({
        parent() {
            return this.tileContainer;
        },
        title: 'Total deposit',
        isTransient: true,
    })
    totalDeposit: ui.fields.Numeric;

    @ui.decorators.numericField<IntacctTransactionFeedQuery>({
        parent() {
            return this.tileContainer;
        },
        title: 'Total withdrawal',
        isTransient: true,
    })
    totalWithdrawal: ui.fields.Numeric;

    @ui.decorators.numericField<IntacctTransactionFeedQuery>({
        parent() {
            return this.tileContainer;
        },
        title: 'Total amount',
        isTransient: true,
    })
    totalAmount: ui.fields.Numeric;

    @ui.decorators.numericField<IntacctTransactionFeedQuery>({
        parent() {
            return this.tileContainer;
        },
        title: 'Matched',
        isTransient: true,
    })
    matched: ui.fields.Numeric;

    @ui.decorators.numericField<IntacctTransactionFeedQuery>({
        parent() {
            return this.tileContainer;
        },
        title: 'Unmatched',
        isTransient: true,
    })
    unMatched: ui.fields.Numeric;

    @ui.decorators.nestedGridField<
        IntacctTransactionFeedQuery,
        [IntacctBankAccountTransactionFeed, IntacctBankAccountTransactionFeedLine]
    >({
        parent() {
            return this.mainSection;
        },
        title: 'Records',
        canSelect: false,
        levels: [
            {
                node: '@sage/xtrem-intacct-finance/IntacctBankAccountTransactionFeed',
                childProperty: 'lines',
                columns: [
                    ui.nestedFields.technical<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'jsonArInvoices',
                    }),
                    ui.nestedFields.technical<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'paymentDate',
                    }),
                    ui.nestedFields.technical<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'receiptDate',
                    }),
                    ui.nestedFields.technical<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'payToCustomerId',
                    }),
                    ui.nestedFields.technical<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'payToCustomerName',
                    }),
                    ui.nestedFields.technical<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'paymentMethod',
                    }),
                    ui.nestedFields.technical<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'arMatch',
                    }),
                    ui.nestedFields.technical<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'financeIntegrationAppUrl',
                    }),
                    ui.nestedFields.technical<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'financeIntegrationAppRecordId',
                    }),
                    ui.nestedFields.label<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'cleared',
                        optionType: '@sage/xtrem-intacct-finance/IntacctMatchingStatus',
                        isHidden: true,
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'intacctId',
                        isHidden: true,
                    }),
                    ui.nestedFields.date<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'postingDate',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'number',
                        isHidden: true,
                    }),
                    ui.nestedFields.numeric<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        title: 'Amount',
                        bind: 'amount',
                        scale: 2,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        title: 'Amount to match',
                        bind: 'amountToMatch',
                        scale: 2,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        title: 'Payee',
                        bind: 'payee',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        title: 'Description',
                        bind: 'description',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.dropdownList<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        title: 'Transaction type',
                        bind: 'transactionType',
                        optionType: '@sage/xtrem-intacct-finance/IntacctRecordTransactionType',
                        isReadOnly: true,
                    }),

                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'entity',
                        isHidden: true,
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'entityName',
                        isHidden: true,
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'accountType',
                        isHidden: true,
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'accountFeedKey',
                        isHidden: true,
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'transactionId',
                        isHidden: true,
                    }),
                    ui.nestedFields.label<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'internalFinanceIntegrationStatus',
                        isHidden: true,
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'accountReconKey',
                        isHidden: true,
                    }),
                    ui.nestedFields.date<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'reconcilitationDate',
                        isHidden: true,
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'documentType',
                        isHidden: true,
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'documentNumber',
                        isHidden: true,
                    }),

                    ui.nestedFields.reference<
                        IntacctTransactionFeedQuery,
                        IntacctBankAccountTransactionFeed,
                        BankAccount
                    >({
                        node: '@sage/xtrem-finance-data/BankAccount',
                        valueField: 'name',
                        bind: 'bankAccount',
                        isHidden: true,
                        isReadOnly: true,
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.reference({
                                node: '@sage/xtrem-system/Site',
                                valueField: '_id',
                                bind: 'financialSite',
                                columns: [],
                            }),
                            ui.nestedFields.reference({
                                node: '@sage/xtrem-master-data/Currency',
                                valueField: '_id',
                                bind: 'currency',
                                isHidden: true,
                                columns: [
                                    ui.nestedFields.text({ bind: 'symbol' }),
                                    ui.nestedFields.text({
                                        bind: 'decimalDigits',
                                        isHidden: true,
                                    }),
                                ],
                            }),
                        ],
                    }),

                    ui.nestedFields.reference<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed, Currency>(
                        {
                            node: '@sage/xtrem-master-data/Currency',
                            valueField: 'name',
                            isAutoSelectEnabled: true,
                            bind: 'currency',
                            title: 'Currency',
                            isReadOnly: true,
                            columns: [
                                ui.nestedFields.text({ bind: 'name' }),
                                ui.nestedFields.text({ bind: 'id', title: 'Name' }),
                            ],
                        },
                    ),
                    ui.nestedFields.numeric<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'feedType',
                        isHidden: true,
                    }),
                    ui.nestedFields.reference({
                        bind: 'account',
                        node: '@sage/xtrem-finance-data/Account',
                        valueField: 'name',
                        isAutoSelectEnabled: true,
                        columns: [ui.nestedFields.text({ bind: 'id' }), ui.nestedFields.text({ bind: 'name' })],
                        isHidden: true,
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        isHidden: true,
                        bind: 'storedAttributes',
                    }),

                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        isHidden: true,
                        bind: 'storedDimensions',
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'financeDocumentCreatedNumber',
                        isHidden: true,
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'financeDocumentCreatedSysId',
                        isHidden: true,
                    }),
                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeed>({
                        bind: 'financeDocumentGenerationErrorMessage',
                        isHidden: true,
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'document_tick',
                        title: 'AR payment',
                    },
                ],
            },
            {
                node: '@sage/xtrem-intacct-finance/IntacctBankAccountTransactionFeedLine',
                columns: [
                    ui.nestedFields.technical<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeedLine>({
                        bind: '_id',
                    }),
                    ui.nestedFields.label<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeedLine>({
                        title: 'Line status',
                        bind: 'status',
                        optionType: '@sage/xtrem-intacct-finance/FeedLineMatchingStatus',
                    }),

                    ui.nestedFields.reference<
                        IntacctTransactionFeedQuery,
                        IntacctBankAccountTransactionFeedLine,
                        IntacctBankAccountTransactionFeed
                    >({
                        node: '@sage/xtrem-intacct-finance/IntacctBankAccountTransactionFeed',
                        valueField: '_id',
                        isAutoSelectEnabled: true,
                        bind: 'document',
                        columns: [
                            ui.nestedFields.text({ bind: 'postingDate' }),
                            ui.nestedFields.text({ bind: 'description' }),
                            ui.nestedFields.text({ bind: 'amount' }),
                        ],
                        isHidden: true,
                    }),
                    ui.nestedFields.reference<
                        IntacctTransactionFeedQuery,
                        IntacctBankAccountTransactionFeedLine,
                        Account
                    >({
                        node: '@sage/xtrem-finance-data/Account',
                        valueField: 'name',
                        isAutoSelectEnabled: true,
                        bind: 'account',
                        title: 'Account',
                        isDisabled(rowId, rowData) {
                            return this.computeIsArStatus(rowData?.document._id);
                        },
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.select({
                                title: 'Tax management',
                                bind: 'taxManagement',
                                optionType: '@sage/xtrem-finance-data/TaxManagement',
                            }),
                        ],
                        orderBy: { name: 1 },
                        filter() {
                            return { chartOfAccount: { _id: this.chartOfAccountSysId } };
                        },
                    }),
                    ui.nestedFields.reference<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeedLine, Tax>({
                        node: '@sage/xtrem-tax/Tax',
                        valueField: 'name',
                        isAutoSelectEnabled: true,
                        bind: 'tax',
                        title: 'Tax',
                        isDisabled(value, rowValue) {
                            return rowValue?.account?.taxManagement !== 'excludingTax';
                        },
                        isMandatory(value, rowValue?) {
                            return rowValue?.account?.taxManagement === 'excludingTax';
                        },
                        columns: [ui.nestedFields.text({ bind: 'name' })],
                        filter() {
                            return { legislation: { id: this.legislationId } };
                        },
                        async onChange(_id: string, value: IntacctBankAccountTransactionFeedLine) {
                            const { taxRate, taxAmount } = await this.calculateTax(value);
                            value.taxAmount = taxAmount.toFixed(2);
                            value.taxRate = taxRate.toString();
                            this.transactionFeed.setRecordValue(value, 1);
                        },
                    }),
                    ui.nestedFields.numeric<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeedLine>({
                        title: 'Amount',
                        bind: 'amount',
                        scale: 2,
                        isDisabled(rowId, rowData) {
                            return !rowData?.document ? true : this.computeIsArStatus(rowData?.document._id);
                        },
                        async onChange(_id: string, value: IntacctBankAccountTransactionFeedLine) {
                            const { taxRate, taxAmount } = await this.calculateTax(value);
                            value.taxAmount = taxAmount.toString(2);
                            value.taxRate = taxRate.toString();
                            this.transactionFeed.setRecordValue(value, 1);
                        },
                    }),
                    ui.nestedFields.numeric<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeedLine>({
                        title: 'Tax amount',
                        bind: 'taxAmount',
                        isReadOnly: true,
                        scale: 2,
                    }),
                    ui.nestedFields.numeric<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeedLine>({
                        bind: 'taxRate',
                        isHidden: true,
                    }),
                    ui.nestedFields.link<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeedLine>({
                        title: 'Accounting integration reference',
                        bind: 'intacctJournalEntryBatchNo',
                        onClick(_id, data) {
                            this.$.router.goToExternal(data.intacctJournalEntryUrl);
                        },
                    }),
                    ui.nestedFields.link<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeedLine>({
                        bind: 'intacctJournalEntryUrl',
                        isHidden: true,
                    }),

                    ui.nestedFields.reference<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeedLine, Site>(
                        {
                            node: '@sage/xtrem-system/Site',
                            valueField: 'name',
                            isReadOnly: true,
                            bind: 'location',
                            title: 'Location',
                            columns: [ui.nestedFields.text({ bind: 'name' })],
                        },
                    ),

                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeedLine>({
                        isHidden: true,
                        bind: 'storedAttributes',
                    }),

                    ui.nestedFields.text<IntacctTransactionFeedQuery, IntacctBankAccountTransactionFeedLine>({
                        isHidden: true,
                        bind: 'storedDimensions',
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'delete',
                        title: 'Delete line',
                        isDestructive: true,
                        onClick(rowId: string, rowData, level) {
                            this.transactionFeed.removeRecord(rowId, level);
                        },
                        isHidden(rowId, rowData: IntacctBankAccountTransactionFeedLine, level, parentIds) {
                            const parentRecord = this.transactionFeed.getRecordValue(parentIds[0], 0);
                            return !ControlRules.intacctBankTransactionLineEditing.editStatusAllowed.includes(
                                parentRecord?.status,
                            );
                        },
                    },
                    {
                        icon: 'add',
                        title: 'Split line',
                        onClick(rowId: string, rowData, level, parentIds) {
                            this.transactionFeed.addRecord(
                                {
                                    document: rowData.document,
                                    location: rowData.location,
                                } as IntacctBankAccountTransactionFeedLine,
                                level,
                                parentIds[0],
                            );
                        },
                        isHidden(rowId, rowData: IntacctBankAccountTransactionFeedLine, level, parentIds) {
                            const parentRecord = this.transactionFeed.getRecordValue(parentIds[0], 0);
                            return !ControlRules.intacctBankTransactionLineEditing.editStatusAllowed.includes(
                                parentRecord?.status,
                            );
                        },
                    },
                    {
                        icon: 'edit',
                        title: 'Dimensions',
                        async onClick(rowId: string, rowData, level) {
                            await utils.applyPanelToLineIfChanged(
                                this.transactionFeed,
                                dimensionPanelHelpers.editDisplayDimensions(
                                    this,
                                    {
                                        documentLine: rowData,
                                    },
                                    {
                                        editable: true,
                                    },
                                ),
                                level,
                            );
                        },
                        isHidden(rowId, rowData: IntacctBankAccountTransactionFeedLine, level, parentIds) {
                            const parentRecord = this.transactionFeed.getRecordValue(parentIds[0], 0);
                            return !ControlRules.intacctBankTransactionLineEditing.editStatusAllowed.includes(
                                parentRecord?.status,
                            );
                        },
                    },
                    {
                        icon: 'lookup',
                        title: 'Search',
                        isHidden(rowId, rowData: IntacctBankAccountTransactionFeedLine, level, parentIds) {
                            const parentRecord = this.transactionFeed.getRecordValue(parentIds[0], 0);
                            return parentRecord?.isPosted || false;
                        },
                    },
                    {
                        icon: 'add',
                        title: 'Create rule',
                        async onClick(rowId: string, rowItem, level, parentIds) {
                            const parentRecord = this.transactionFeed.getRecordValue(parentIds[0], 0);

                            const data = {
                                priority: 0,
                                bankAccount: this.bankAccount.value?._id || null,
                                transactionType: parentRecord?.transactionType,
                                keyword: parentRecord?.description,
                                storedDimensions: rowItem.storedDimensions,
                                storedAttributes: rowItem.storedAttributes,
                                account: rowItem.account?._id || null,
                                tax: rowItem.tax?._id || null,
                                location: rowItem.location?._id || null,
                            } as IntacctBankAccountMatchingInput;

                            await this.$.graph
                                .node('@sage/xtrem-intacct-finance/IntacctBankAccountMatching')
                                .create({ _id: true }, { data })
                                .execute();

                            this.$.showToast(
                                ui.localize(
                                    '@sage/xtrem-intacct-finance/pages_intacct_transaction_feed_query_create_rule',
                                    'Matching rule saved.',
                                ),
                                { type: 'success' },
                            );
                        },
                        isHidden(rowId, rowData: IntacctBankAccountTransactionFeedLine, level, parentIds) {
                            const parentRecord = this.transactionFeed.getRecordValue(parentIds[0], 0);
                            return !ControlRules.intacctBankTransactionLineEditing.editStatusAllowed.includes(
                                parentRecord?.status,
                            );
                        },
                    },
                ],
            },
        ],
        fieldActions() {
            return [this.matchingRules, this.refreshTransactionFeed];
        },
    })
    transactionFeed: ui.fields.NestedGrid;

    @ui.decorators.pageAction<IntacctTransactionFeedQuery>({
        title: 'Refresh',
        isHidden() {
            return !this._id.value;
        },
        icon: 'refresh',
        async onClick() {
            await this.transactionFeed.refresh();
            await this.refreshImportStatus();
        },
    })
    refreshTransactionFeed: ui.PageAction;

    manageRowData(rowData: ui.PartialNodeWithId<IntacctBankAccountTransactionFeed>): IntacctImportSessionInput {
        if (!JSON.parse(rowData.arMatch || '')?.isArMatch) {
            // we check this because if there's an ar match we don't want to add a line
            const returnLines = rowData.lines?.map(row => {
                delete row.document;
                delete row.status;
                return {
                    ...row,
                    account: row.account?._id || null,
                    location: row.location?._id || null,
                    tax: row.tax?._id || null,
                } as ui.PartialNodeWithId<IntacctBankAccountTransactionFeedLine>;
            });

            rowData.lines = returnLines;
        }
        delete rowData.internalFinanceIntegrationStatus;

        const dataLine = {
            ...rowData,

            bankAccount: this.bankAccount.value?._id || '',
            currency: rowData?.currency?._id,
        } as IntacctBankAccountTransactionFeedInput;

        return {
            _id: this._id.value,
            transactionFeed: [
                {
                    _action: +rowData._id < 0 ? 'create' : 'update',
                    ...dataLine,
                },
            ],
        } as IntacctImportSessionInput;
    }

    async saveLine(rowId: string) {
        const rowData: ui.PartialNodeWithId<IntacctBankAccountTransactionFeed> | null =
            this.transactionFeed.getRecordValue(rowId, 0);

        if (rowData) {
            await this.$.graph
                .node('@sage/xtrem-intacct-finance/IntacctImportSession')
                .update(
                    { _id: true },
                    {
                        data: this.manageRowData(rowData),
                    },
                )
                .execute();
        }
    }

    async getIntacctConfig() {
        const intacctConfiguration = await this.$.graph
            .node('@sage/xtrem-intacct/Intacct')
            .queries.defaultInstance(
                {
                    legislation: { id: true },
                    country: { id: true },
                    chartOfAccount: { _id: true, name: true },
                },
                {},
            )
            .execute();

        this.chartOfAccountSysId = intacctConfiguration.chartOfAccount._id;
        this.legislationId = intacctConfiguration.legislation.id;
    }

    chartOfAccountSysId: string;

    legislationId: string;

    @ui.decorators.pageAction<IntacctTransactionFeedQuery>({
        title: 'Open matching rules',
        icon: 'document_tick',
        onError(error) {
            this.$.loader.isHidden = true;
            return error;
        },
    })
    matchingRules: ui.PageAction;

    @ui.decorators.pageAction<IntacctTransactionFeedQuery>({
        title: 'Post',
        // XT-36166 Enable posting only if all lines are saved.
        isDisabled() {
            return this.isPostDisabled();
        },
    })
    post: ui.PageAction;

    isPostDisabled(): boolean {
        // Disable posting if page is dirty.
        if (this.$.isDirty) {
            return true;
        }

        // Disable if there is no line in the result grid.
        if (!this.transactionFeed.value.length) {
            return true;
        }

        // Disable posting if there is a line that has not yet been saved.
        const linesNotSaved = this.transactionFeed.value.filter(line => +line._id < 0);
        if (linesNotSaved.length) {
            return true;
        }

        // TODO: DNE - XT-36166 - Disable posting if there are no postable lines.
        if (!this.withPostableFeeds.value) {
            return true;
        }

        // Otherise enable the post button
        return false;
    }

    async clearNestedGrid() {
        this.transactionFeed.value.forEach((row: ui.PartialNodeWithId<IntacctBankAccountTransactionFeed>) => {
            this.transactionFeed.removeRecord(row._id, 0);
        });
        this.transactionFeed.unselectAllRecords();
        await this.transactionFeed.redraw();
    }

    computeIsArStatus(rowId: string) {
        const record = this.transactionFeed.getRecordValue(rowId, 0);
        const statusValue: boolean = [
            'draftArMatch',
            'readyForArPaymentGeneration',
            'arPaymentGenerated',
            'arPaymentGenerationInProgress',
            'arPaymentPostingInProgress',
            'arPaymentPosted',
            'readyForArAdvanceGeneration',
            'arAdvancePosted',
            'partialArMatch',
        ].includes(record?.status);
        return statusValue;
    }
}
