{"data": {"xtremIntacctFinance": {"intacctMap": {"query": {"edges": [{"node": {"id": "VENDOR", "intacctDescription": "<PERSON><PERSON><PERSON>", "nodeFactory": {"title": "Supplier"}, "isActive": true, "mappedPropertyNames": ["intacctSupplier.intacctId", "businessEntity.id", "businessEntity.name", "parent.intacctSupplier.intacctId", "paymentTerm.name", "businessEntity.taxIdNumber", "intacctSupplier.status", "intacctSupplier.hideDisplayContact", "businessEntity.currency.id", "paymentMethod", "intacctSupplier.contactList", "primaryContact.address.intacctBusinessEntityAddress.prefix", "primaryContact.firstName", "primaryContact.lastName", "primaryAddress.intacctBusinessEntityAddress.printAs", "primaryContact.locationPhoneNumber", "primaryContact.email", "primaryAddress.addressLine1", "primaryAddress.addressLine2", "primaryAddress.city", "primaryAddress.region", "primaryAddress.postcode", "primaryAddress.country.id", "intacctSupplier.primaryContact", "intacctSupplier.payToAddress", "intacctSupplier.returnToAddress"]}}]}}}}}