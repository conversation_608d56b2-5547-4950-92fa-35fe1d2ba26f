import type { Context } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import type * as xtremIntacctFinance from '../../lib';

export async function arAdvancePayload(
    context: Context,
): Promise<xtremIntacctFinance.sharedFunctions.interfaces.ArAdvanceCreateData> {
    const financialSite = (await context.read(xtremSystem.nodes.Site, { _id: '#ETS2-S02' }))._id;
    const bankAccount = (await context.read(xtremFinanceData.nodes.BankAccount, { _id: '#700_CHK' }))._id;
    const currency = (await context.read(xtremMasterData.nodes.Currency, { _id: '#USD' }))._id;

    return {
        bankAccount,
        financialSite,
        payToCustomerId: '10093',
        payToCustomerName: 'Local Customer',
        description: 'Never found document 002',
        paymentMethod: 'creditCard',
        postingDate: '2022-11-01',
        paymentDate: '2022-11-01',
        currency,
        companyFxRate: 1,
        companyFxRateDivisor: 1,
        fxRateDate: '2022-11-01',
        advanceAmount: 540,
        advanceCompanyAmount: 540,
        bankFeed: **********,
        arMatch: { isArMatch: false, arPaymentType: 'payment', matchingReasons: '' },
        lines: [
            {
                financialSite,
                currency,
                advanceAmount: 540,
                advanceCompanyAmount: 540,
                account: '94',
                storedAttributes: {
                    project: 'AttPROJ',
                    task: '',
                    employee: '',
                },
                storedDimensions: {
                    dimensionType02: 'CHANNELVALUE1',
                    dimensionType03: 'DIMTYPE1VALUE1',
                    dimensionType04: 'DIMTYPE2VALUE2',
                },
            },
        ],
    };
}
