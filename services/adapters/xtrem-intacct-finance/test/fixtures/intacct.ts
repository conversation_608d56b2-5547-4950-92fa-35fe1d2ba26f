import * as IA from '@intacct/intacct-sdk';
import type { Context, NodeUpdateData } from '@sage/xtrem-core';
import { SystemError } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremStructure from '@sage/xtrem-structure';
import * as sinon from 'sinon';

export const emptyOnlineResponse = {
    getResult: () => {
        return new IA.Xml.Response.Result({
            status: 'success',
            data: [],
            function: 'Query',
            controlid: '1234',
        });
    },
} as xtremIntacct.classes.sdk.Xml.OnlineResponse;

export async function disableIntegration(context: Context): Promise<void> {
    const intacctInstance = await context
        .query(xtremIntacct.nodes.Intacct, {
            filter: { isActive: true },
            forUpdate: true,
        })
        .at(0);
    if (intacctInstance) {
        await intacctInstance.$.set({ isActive: false });
        await intacctInstance?.$.save();
    }
}

export async function checkActiveIntegration(context: Context): Promise<void> {
    const numberOfActiveIntacctInstance = await context.queryCount(xtremIntacct.nodes.Intacct, {
        filter: { isActive: true },
    });
    if (numberOfActiveIntacctInstance > 1) {
        throw new SystemError('You can have only one intacct instance active');
    }
    if (numberOfActiveIntacctInstance === 0) {
        throw new SystemError('Intacct integration tests need one intacct instance active');
    }
}

/** Function to be able all the intacctOptions we want :
 * isContactListCleaned / isCategoryNameClean / isDisplayContactHidden / isActive  ...
 */
export async function changeIntacctOption(context: Context, options: NodeUpdateData<xtremIntacct.nodes.Intacct>) {
    const sandbox = sinon.createSandbox();

    sandbox
        .stub(xtremIntacct.classes.sdk.Functions.ValidCredentials.prototype, 'executeResult')
        .callsFake(async (): Promise<{ isMock: boolean; result: any }> => {
            const returnValue = await Promise.resolve({ isMock: true, result: {} });
            return returnValue;
        });

    sandbox
        .stub(xtremStructure.serviceOptions.intacctActivationOption, 'onEnabled')
        .callsFake(async (): Promise<void> => {
            await Promise.resolve();
        });

    const filter = options.id ? { id: options.id } : { isActive: true };

    const intacctInstance = await context.query(xtremIntacct.nodes.Intacct, { filter, forUpdate: true }).at(0);
    if (!intacctInstance) {
        throw new Error(`Intacct instance not found for ${JSON.stringify(filter)}`);
    }

    await intacctInstance.$.set(options);
    await intacctInstance?.$.save();
    sandbox.restore();
}

export async function enableIntegration(context: Context, integration: { id: string }): Promise<void> {
    await changeIntacctOption(context, { id: integration.id, isActive: true });
}
