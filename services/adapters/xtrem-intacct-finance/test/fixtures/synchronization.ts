import type { Context, Node, NodeCreateData, Node<PERSON><PERSON> } from '@sage/xtrem-core';
import { date } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import type * as xtremIntacct from '@sage/xtrem-intacct';
import type { IntacctKey } from '@sage/xtrem-intacct/lib/interfaces/_index';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { assert } from 'chai';
import type * as sinon from 'sinon';
import * as xtremIntacctFinance from '../../lib';

const baseLine: NodeCreateData<xtremFinance.nodes.JournalEntryLine> = {
    financialSite: '#US001',
    chartOfAccount: '#US_DEFAULT',
    transactionCurrency: '#EUR',
    companyFxRate: 10,
    companyFxRateDivisor: 1,
    fxRateDate: date.parse('2020-05-05'),
    description: 'manual entry',
};

export async function createIntacctDocument(
    context: Context,
    key: NodeKey<xtremFinance.nodes.JournalEntry>,
): Promise<xtremIntacctFinance.nodes.IntacctJournalEntry | null> {
    const journalEntry = await context.read(xtremFinance.nodes.JournalEntry, key, { forUpdate: true });
    const intacctDocument = key._id === 7 ? { intacctId: await journalEntry.number, recordNo: 192 } : {};
    await journalEntry.$.set({ intacctDocument });
    await journalEntry.lines.forEach(line => line.$.set({ intacctDocumentLine: {} }));
    if ((await journalEntry.lines.length) === 0) {
        await journalEntry.lines.append({
            ...baseLine,
            account: '#60410|TEST_US_DEFAULT',
            sign: 'C',
            transactionAmount: 1000,
            companyAmount: 1000,
            commonReference: 'entry-1',
            journalEntryTypeLine: '#US|miscellaneousStockReceipt|journalEntry|100',
        });
        await journalEntry.lines.append({
            ...baseLine,
            account: '#60660|TEST_US_DEFAULT',
            sign: 'D',
            transactionAmount: 1000,
            companyAmount: 1000,
            commonReference: 'entry-2',
            journalEntryTypeLine: '#US|miscellaneousStockReceipt|journalEntry|200',
        });
    }
    await journalEntry.$.save();

    return journalEntry.intacctDocument;
}

/** get journal entry number IJ-********** & return the genericFuncion */
export async function getJournalEntryQueryClass(context: Context) {
    const number = 'IJ-**********';

    const intacctDocument = await createIntacctDocument(context, { number });

    if (!intacctDocument) {
        assert.fail(`No intacct document for journal entry ${number}`);
    }

    return xtremIntacctFinance.classes.intacct.GenericIfunction.create(context, intacctDocument);
}

/** IJ-********** */
export const journalEntry = {
    BATCH_DATE: '05/05/2021',
    BATCH_TITLE: 'Inventory ',
    JOURNAL: 'IJ',
    REFERENCENO: 'IJ-**********',
    ENTRIES: {
        GLENTRY: [
            {
                TR_TYPE: '-1',
                AMOUNT: '1000',
                ACCOUNTNO: '60410',
                LOCATION: 'US001',
                DOCUMENT: 'ENTRY-1',
                DESCRIPTION: 'manual entry',
                CURRENCY: 'EUR',
                EXCH_RATE_DATE: '05/05/2020',
                EXCHANGE_RATE: '10',
            },
            {
                TR_TYPE: '1',
                AMOUNT: '1000',
                ACCOUNTNO: '60660',
                LOCATION: 'US001',
                DOCUMENT: 'ENTRY-2',
                DESCRIPTION: 'manual entry',
                CURRENCY: 'EUR',
                EXCH_RATE_DATE: '05/05/2020',
                EXCHANGE_RATE: '10',
            },
        ],
    },
};

export async function getRequestManager(
    context: Context,
    sandbox: sinon.SinonSandbox,
    intacctDocument: xtremIntacctFinance.nodes.IntacctSynchronizationState,
) {
    sandbox
        .stub(xtremIntacctFinance.nodes.IntacctSynchronizationState.prototype, 'checkExist')
        .callsFake(async (): Promise<xtremIntacct.interfaces.IntacctKey | undefined> => {
            const returnValue = await Promise.resolve(undefined);
            return returnValue;
        });

    const synchronizationManager = await xtremIntacctFinance.classes.IntacctSynchronizationManager.create(
        context,
        intacctDocument,
    );

    sandbox.restore();
    sandbox.reset();

    return synchronizationManager.getIntacctRequest('create');
}

// Todo : add possibilitu to add the return of the checkExist
export async function getRequestManagerForFinanceDocuments(
    context: Context,
    sandbox: sinon.SinonSandbox,
    intacctDocument: xtremIntacctFinance.nodes.IntacctSynchronizationState,
) {
    const synchronizationManager = await xtremIntacctFinance.classes.IntacctSynchronizationManager.create(
        context,
        intacctDocument,
    );
    return synchronizationManager.getIntacctRequest('create');
}

export interface SyncStateNode extends Node {
    id: Promise<string>;
}

export function xmlWithoutFunctionAsJson(
    context: Context,
    params: {
        sandbox: sinon.SinonSandbox;
        requestManager: xtremIntacctFinance.classes.intacct.GenericIfunction<IntacctKey>;
        isXmlReturn?: boolean;
    },
) {
    params.sandbox
        .stub(xtremIntacctFinance.classes.intacct.GenericIfunction.prototype, 'manageDependencie')
        .callsFake(
            async (
                syncState: xtremIntacctFinance.interfaces.synchronization.SyncState,
            ): Promise<xtremIntacctFinance.interfaces.synchronization.SyncState> => {
                if (syncState.intacctId && syncState.state === 'success') {
                    return syncState;
                }
                const instance = (await context.read(
                    await (
                        await context.read(xtremMetadata.nodes.MetaNodeFactory, { _id: `#${syncState.node}` })
                    ).getNode(),
                    { _id: syncState.sysId },
                )) as SyncStateNode;
                // As we don't have id on supplier & customer we need to get the businessEntity
                const intacctId = (await instance.id) || (await (await (instance as any).businessEntity).id);

                return { ...syncState, intacctId, state: 'success' };
            },
        );
    return params.isXmlReturn
        ? params.requestManager.xmlWithoutFunction(true)
        : params.requestManager.xmlWithoutFunctionAsJson({});
}
