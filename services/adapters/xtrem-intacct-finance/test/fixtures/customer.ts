import type { NodeCreateData } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import { IntacctSynchronizationManager } from '../../lib/classes';

/** Customer delivery Address */
export const customerDeliveryAddress: NodeCreateData<xtremMasterData.nodes.BusinessEntityAddress> = {
    deliveryDetail: {
        leadTime: 0,
        mode: '#TEST_SEA_VALENCIA',
        incoterm: null,
        isActive: true,
        isFridayWorkDay: false,
        isMondayWorkDay: false,
        isPrimary: true,
        isSaturdayWorkDay: false,
        isSundayWorkDay: false,
        isThursdayWorkDay: false,
        isTuesdayWorkDay: false,
        isWednesdayWorkDay: false,
        shipmentSite: null,
    },
    address: -1,
};

/**  TESTBECUS1 */
export const customerData: NodeCreateData<xtremMasterData.nodes.Customer> = {
    payByAddress: -1,
    billToAddress: -2,
    paymentTerm: '#TEST_NET_15_CUSTOMER',
    payByCustomer: -1,
    billToCustomer: -1,

    primaryAddress: -1,
    isActive: true,
    minimumOrderAmount: 1,
};

export const contactListCust01 = [
    { CATEGORYNAME: 'Cust address', intacctId: 'Universal Alarmsa One (15)' },
    { CATEGORYNAME: 'Cust address other', intacctId: 'Universal Alarmsa Eleven (21)' },
    { CATEGORYNAME: 'Cust address inactive', intacctId: 'Universal Alarmsa Twenty One (22)' },
];

export const cleanContactListCust01 = [
    { CATEGORYNAME: 'Primary address', intacctId: 'Universal Alarmsa One (15)' },
    { CATEGORYNAME: 'Other address', intacctId: 'Universal Alarmsa Eleven (21)' },
    { CATEGORYNAME: 'Other address', intacctId: 'Universal Alarmsa Twenty One (22)' },
];

export function isIntacctSynchronizationManager(instance: any): instance is IntacctSynchronizationManager {
    return instance instanceof IntacctSynchronizationManager;
}
