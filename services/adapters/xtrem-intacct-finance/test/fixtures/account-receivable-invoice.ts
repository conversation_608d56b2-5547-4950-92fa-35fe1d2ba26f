import type { Context } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';

export async function getAccountReceivableInvoicePayload(
    context: Context,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument> {
    const currencySysId = (await context.read(xtremMasterData.nodes.Currency, { _id: '#USD' }))._id;
    const itemSysId = (await context.read(xtremMasterData.nodes.Item, { _id: '#Consulting01' }))._id;
    const financialSiteSysId = (await context.read(xtremSystem.nodes.Site, { _id: '#US001' }))._id;
    const customerSysId = (await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' }))._id;

    return {
        batchId: '582893b0-fd86-4395-910b-a18f41f139c5',
        batchSize: 1,
        documentSysId: 11,
        documentNumber: 'SI11',
        documentDate: '2021-12-13',
        documentType: 'salesInvoice',
        targetDocumentType: 'accountsReceivableInvoice',
        currencySysId,
        financialSiteSysId,
        documentLines: [
            {
                baseDocumentLineSysId: 0,
                movementType: 'document',
                sourceDocumentNumber: '',
                customerSysId,
                currencySysId,
                companyFxRate: 1,
                companyFxRateDivisor: 1,
                fxRateDate: '2021-12-13',
                itemSysId,
                storedDimensions: { dimensionType01: '300', dimensionType02: 'CHANNELVALUE1' },
                storedAttributes: {
                    item: 'Consulting01', // No intacctId for Consulting01
                    customer: 'US019',
                    stockSite: 'US001',
                    businessSite: 'US001',
                    financialSite: 'US001',
                    project: '',
                    task: '',
                    employee: '',
                },
                amounts: [
                    {
                        amountType: 'amountExcludingTax',
                        amount: 12.0,
                        documentLineType: 'documentLine',
                    },
                ],
            },
        ],
    };
}

export const intacctJsonQuery = {
    CUSTOMERID: 'US019',
    DOCNUMBER: 'SI11',
    TERMNAME: 'Due Upon Receipt',
    WHENCREATED: '12/13/2021',
    WHENPOSTED: '12/13/2021',
    WHENDUE: '12/13/2021',
    BASECURR: 'USD',
    CURRENCY: 'USD',
    EXCH_RATE_DATE: '12/13/2021',
    ARINVOICEITEMS: {
        ARINVOICEITEM: {
            ACCOUNTNO: '40900',
            OFFSETGLACCOUNTNO: '12100',
            TRX_AMOUNT: '12',
            DEPARTMENTID: '300',
            CLASSID: 'CHANNELVALUE1',
            ITEMID: 'Consulting01',
            LOCATIONID: 'US001',
            CUSTOMERID: 'US019',
        },
    },
};
