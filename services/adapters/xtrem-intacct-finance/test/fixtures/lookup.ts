import type * as xtremIntacctFinance from '../../index';

export const lookupVendor: xtremIntacctFinance.sharedFunctions.interfaces.LookupObject = {
    name: 'VENDOR',
    documentType: '',
    xtremObject: '',
    fields: [
        {
            ID: 'RECORDNO',
            LABEL: 'Record Number',
            DESCRIPTION: 'Record Number',
            REQUIRED: false,
            READONLY: false,
            DATATYPE: 'INTEGER',
            ISCUSTOM: false,
            xtremProperty: '',
        },
        {
            ID: 'VENDORID',
            LABEL: 'Vendor ID',
            DESCRIPTION: 'Unique ID of Vendor',
            REQUIRED: true,
            READONLY: false,
            DATATYPE: 'TEXT',
            ISCUSTOM: false,
            xtremProperty: '',
        },
        {
            ID: 'PAYTO.CONTACTNAME',
            LABEL: 'Pay To Contact',
            DESCRIPTION: 'Contact Name for Payments',
            REQUIRED: false,
            READONLY: false,
            DATATYPE: 'ENUM',
            ISCUSTOM: false,
            xtremProperty: '',
        },
    ],
    relationships: [
        {
            OBJECTPATH: 'PARENT',
            OBJECTNAME: 'VENDOR',
            LABEL: 'Parent Vendor',
            RELATIONSHIPTYPE: 'MANY2ONE',
            RELATEDBY: 'PARENTID',
            xtremProperty: '',
        },
        {
            OBJECTPATH: 'PAYTOCONTACT',
            OBJECTNAME: 'CONTACT',
            LABEL: 'Pay to Contact',
            RELATIONSHIPTYPE: 'MANY2ONE',
            RELATEDBY: 'PAYTO.CONTACTNAME',
            xtremProperty: '',
        },
    ],
};
