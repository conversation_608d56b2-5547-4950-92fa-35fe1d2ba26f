/* no unused vars for isRequestSend */
import type { Context, NodePayloadData } from '@sage/xtrem-core';
import { assert } from 'chai';
import type * as sinon from 'sinon';
import * as xtremIntacctFinance from '../../lib';

export function getIntacctTransactionFeedStub(
    sandbox: sinon.SinonSandbox,
    feedsToReturn: NodePayloadData<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed>[],
) {
    sandbox
        .stub(xtremIntacctFinance.functions.transactionFeedSearch, 'getIntacctTransactionFeed')
        .callsFake(
            async (
                _context: Context,
                filter: xtremIntacctFinance.interfaces.BankAccountMatching.AccountMatchingQueryFilters,
            ) => {
                await Promise.resolve();
                assert.isNotEmpty(filter);
                return feedsToReturn;
            },
        );
}
