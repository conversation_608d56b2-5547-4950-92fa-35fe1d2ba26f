import type { Context } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';

export async function getAccountPayableInvoicePayload(
    context: Context,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument> {
    const currencySysId = (await context.read(xtremMasterData.nodes.Currency, { _id: '#USD' }))._id;
    const itemSysId = (await context.read(xtremMasterData.nodes.Item, { _id: '#NonStockManagedItem' }))._id;
    const financialSiteSysId = (await context.read(xtremSystem.nodes.Site, { _id: '#US001' }))._id;
    const supplierSysId = (await context.read(xtremMasterData.nodes.Supplier, { _id: '#LECLERC' }))._id;

    return {
        batchId: '7a1cc449-85dc-4fd0-98a4-08724c686226',
        batchSize: 1,
        documentSysId: 8,
        documentNumber: 'PI8',
        documentDate: '2022-05-02',
        documentType: 'purchaseInvoice',
        targetDocumentType: 'accountsPayableInvoice',
        currencySysId,
        financialSiteSysId,
        documentLines: [
            {
                baseDocumentLineSysId: 0,
                movementType: 'document',
                sourceDocumentNumber: '',
                supplierSysId,
                currencySysId,
                companyFxRate: 1,
                companyFxRateDivisor: 1,
                fxRateDate: '2022-05-02',
                itemSysId,
                storedDimensions: { dimensionType01: '300', dimensionType02: 'CHANNELVALUE1' },
                storedAttributes: {
                    item: 'NonStockManagedItem',
                    supplier: 'LECLERC',
                    stockSite: 'US001',
                    businessSite: 'US001',
                    financialSite: 'US001',
                    project: '',
                    task: '',
                    employee: '',
                },
                amounts: [
                    {
                        amountType: 'amountExcludingTax',
                        amount: 12.0,
                        documentLineType: 'documentLine',
                    },
                ],
            },
        ],
    };
}

export const intacctJsonQuery = {
    VENDORID: 'LECLERC',
    DOCNUMBER: 'PI8',
    TERMNAME: 'Net 30',
    WHENCREATED: '05/02/2022',
    WHENPOSTED: '05/02/2022',
    WHENDUE: '06/01/2022',
    BASECURR: 'USD',
    CURRENCY: 'USD',
    EXCH_RATE_DATE: '05/02/2022',
    EXCHANGE_RATE: '1',
    INCLUSIVETAX: 'false',
    APBILLITEMS: {
        APBILLITEM: {
            ACCOUNTNO: '20680',
            OFFSETGLACCOUNTNO: '20100',
            TRX_AMOUNT: '12',
            DEPARTMENTID: '300',
            ITEMID: 'NonStockManagedItem',
            LOCATIONID: 'US001',
            VENDORID: 'LECLERC',
            CLASSID: 'CHANNELVALUE1',
        },
    },
};
