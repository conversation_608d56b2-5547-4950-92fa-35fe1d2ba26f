import type { NodeCreateData } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';

/** TESTBECUS1 US / USD with 2 Addresses Main & Second */
export const businessEntityCreatePayload: NodeCreateData<xtremMasterData.nodes.BusinessEntity> = {
    id: 'TESTBECUS1',
    name: 'TESTBECUS1',
    country: '#US',
    currency: '#USD',
    taxIdNumber: '111-11-5020',
    siret: '',
    addresses: [
        {
            name: 'Main address',
            isActive: true,
            isPrimary: true,
            country: '#US',
            addressLine1: 'Street1',
            addressLine2: 'Second line',
            city: 'Some city',
            region: 'Some region',
            postcode: '01234',
            locationPhoneNumber: '+***********',
        },
        {
            name: 'Second address',
            isActive: true,
            isPrimary: false,
            country: '#US',
            addressLine1: 'Street2',
            addressLine2: 'Second line',
            city: 'Some city',
            region: 'Some region',
            postcode: '01234',
            locationPhoneNumber: '+***********',
        },
    ],
};
