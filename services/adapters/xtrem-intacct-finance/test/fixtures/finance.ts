import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { date } from '@sage/xtrem-core';
import type * as xtremFinance from '@sage/xtrem-finance';
import * as xtremFinanceData from '@sage/xtrem-finance-data';

export async function journalEntryPayload(context: Context): Promise<NodeCreateData<xtremFinance.nodes.JournalEntry>> {
    const accStaging = await context.query(xtremFinanceData.nodes.AccountingStaging, { first: 1 }).at(0);

    return {
        financialSite: '#US001',
        postingStatus: 'posted',
        postingDate: date.today(),
        description: 'Inventory receipt',
        reference: 'Inventory receipt',
        origin: 'stock',
        journal: '#US|IJ',
        lines: [
            {
                financialSite: '#US001',
                chartOfAccount: {
                    name: 'US chart of accounts',
                },
                transactionCurrency: '#USD',
                companyFxRate: 10.0,
                fxRateDate: date.parse('2020-05-05'),
                account: '#13100|US_DEFAULT',
                sign: 'C',
                transactionAmount: 1000,
                companyAmount: 1000,
                description: 'REC-**********',
                journalEntryTypeLine: '#US|miscellaneousStockReceipt|journalEntry|100',
                attributesAndDimensions: [
                    {
                        storedAttributes: {
                            financialSite: 'US001',
                            stockSite: 'US001',
                            customer: 'US019',
                            supplier: 'US018',
                            project: '',
                            task: '',
                            employee: '',
                        },
                        storedDimensions: {
                            dimensionType03: 'DIMTYPE1VALUE1',
                            dimensionType04: 'DIMTYPE2VALUE2',
                        },
                        transactionAmount: 100,
                        companyAmount: 100,
                    },
                    {
                        storedAttributes: {
                            financialSite: 'US001',
                            stockSite: 'US001',
                            customer: 'US019',
                            supplier: 'US018',
                            project: '',
                            task: '',
                            employee: '',
                        },
                        storedDimensions: {
                            dimensionType03: 'DIMTYPE1VALUE1',
                            dimensionType04: 'DIMTYPE2VALUE2',
                        },
                        transactionAmount: 200,
                        companyAmount: 200,
                    },
                    {
                        storedAttributes: {
                            financialSite: 'US001',
                            stockSite: 'US001',
                            customer: 'US019',
                            supplier: 'US018',
                            project: '',
                            task: '',
                            employee: '',
                        },
                        storedDimensions: {
                            dimensionType03: 'DIMTYPE1VALUE1',
                            dimensionType04: 'DIMTYPE2VALUE2',
                        },
                        transactionAmount: 700,
                        companyAmount: 700,
                    },
                ],
                accountingStagingLines: [{ _action: 'create', accountingStaging: { _id: accStaging?._id } }],
            },
            {
                financialSite: '#US001',
                chartOfAccount: {
                    name: 'US chart of accounts',
                },
                transactionCurrency: '#USD',
                companyFxRate: 10.0,
                fxRateDate: date.parse('2020-05-05'),
                account: '13100|US_DEFAULT',
                sign: 'D',
                transactionAmount: 1000,
                companyAmount: 1000,
                description: 'REC-**********',
                journalEntryTypeLine: '#US|miscellaneousStockReceipt|journalEntry|200',
                attributesAndDimensions: [
                    {
                        storedAttributes: {
                            financialSite: 'US001',
                            stockSite: 'US001',
                            customer: 'US019',
                            supplier: 'US018',
                            project: '',
                            task: '',
                            employee: '',
                        },
                        storedDimensions: {
                            dimensionType03: 'DIMTYPE1VALUE1',
                            dimensionType04: 'DIMTYPE2VALUE2',
                        },
                        transactionAmount: 1000.0,
                        companyAmount: 1000.0,
                    },
                ],
                accountingStagingLines: [{ _action: 'create', accountingStaging: { _id: accStaging?._id } }],
            },
        ],
    };
}
