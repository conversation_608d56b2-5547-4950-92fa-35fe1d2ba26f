import type * as xtremIntacctFinance from '../../lib/index';

export function transFormContactList(contactList: xtremIntacctFinance.interfaces.synchronization.ContactList[]) {
    return contactList.map(contact => {
        return {
            CATEGORYNAME: contact.CONTACT_LIST_INFO.CATEGORYNAME,
            intacctId: contact.CONTACT_LIST_INFO.contact.name.intacctId,
        };
    });
}

export function transFormContactListSysId(contactList: xtremIntacctFinance.interfaces.synchronization.ContactList[]) {
    return contactList.map(contact => {
        return {
            CATEGORYNAME: contact.CONTACT_LIST_INFO.CATEGORYNAME,
            sysId: contact.CONTACT_LIST_INFO.contact.name.sysId,
        };
    });
}
