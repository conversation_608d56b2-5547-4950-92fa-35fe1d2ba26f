import { Test, asyncArray } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import * as sinon from 'sinon';
import * as xtremIntacctFinance from '../../../lib';

const dirname = __dirname.replace('/build/', '/');

let sandbox: sinon.SinonSandbox;

describe('Intacct customer', () => {
    beforeEach(() => {
        sandbox = sinon.createSandbox();
    });
    afterEach(() => {
        sandbox.reset();
        sandbox.restore();
    });

    it('Customer - get the create query', () =>
        Test.withContext(async context => {
            const casinoCustomer = await context.read(
                xtremMasterData.nodes.Customer,
                { _id: '#US017' },
                { forUpdate: true },
            );

            await casinoCustomer.$.set({ intacctCustomer: { sysId: casinoCustomer._id.toString() } });
            await casinoCustomer.$.save();

            const intacctCustomer = await casinoCustomer.intacctCustomer;
            if (!intacctCustomer) {
                assert.fail('No intacct customer for US017');
            }
            sandbox.reset();
            sandbox
                .stub(xtremIntacctFinance.nodes.IntacctSynchronizationState.prototype, 'checkExist')
                .callsFake(async (): Promise<xtremIntacct.interfaces.IntacctKey> => {
                    const returnValue = await Promise.resolve({ RECORDNO: 10, NAME: 'US017' });
                    return returnValue;
                });

            const syncManager = await xtremIntacctFinance.classes.IntacctSynchronizationManager.create(
                context,
                intacctCustomer,
            );

            assert.deepEqual(syncManager.intacctKey, { RECORDNO: 10, NAME: 'US017' });

            const [casinoCustomerWithIntacctId] = await context
                .query(xtremMasterData.nodes.Customer, {
                    filter: { businessEntity: { id: 'US017' } },
                    forUpdate: true,
                })
                .toArray();

            assert.equal(await (await casinoCustomerWithIntacctId.intacctCustomer)?.intacctId, 'US017');

            const genericFunc = await xtremIntacctFinance.classes.intacct.GenericIfunction.create(
                context,
                intacctCustomer,
            );
            /** To not call intacct ( contaccts are not created ) */
            sandbox
                .stub(xtremIntacctFinance.classes.IntacctSynchronizationManager.prototype, 'synchronize')
                .callsFake(async () => {
                    await Promise.resolve();
                    return { state: 'success', intacctId: 'fakeIntacctId (stub)' };
                });

            const xmlRequest = await genericFunc.xmlWithoutFunction(true);

            const createCustomerxmlPath = path.resolve(dirname, 'xml-requests/create-customer.xml');
            const expectedXml = fs.readFileSync(createCustomerxmlPath).toString();

            assert.equal(xmlRequest, expectedXml);

            sandbox.reset();
            sandbox.restore();
        }));

    it('Customer credit limit - Fail when update is normal save and Intacct is active', () =>
        Test.withContext(async context => {
            const customer = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' }, { forUpdate: true });
            assert.equal(await customer?.creditLimit, 0);

            await customer.$.set({
                creditLimit: 13.45,
            });

            await assert.isRejected(customer.$.save());
            assert.deepEqual(customer.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'You cannot update credit limit if Sage Intacct is active.',
                },
            ]);

            // Turn Intacct off and save
            const defaultInstance = await xtremIntacct.nodes.Intacct.defaultInstance(context);

            if (defaultInstance) {
                const intactInstance = await context.read(
                    xtremIntacct.nodes.Intacct,
                    { _id: defaultInstance._id },
                    { forUpdate: true },
                );
                await intactInstance.$.set({
                    isActive: false,
                });
                await intactInstance.$.save();

                await customer.$.save();
            }
        }));

    it('Customer - onDesynchronized test', () =>
        Test.withContext(async context => {
            const casinoCustomer = await context.read(
                xtremMasterData.nodes.Customer,
                { _id: '#US017' },
                { forUpdate: true },
            );

            await casinoCustomer.$.set({ intacctCustomer: { sysId: casinoCustomer._id.toString() } });
            await casinoCustomer.$.save();

            const intacctCustomer = await casinoCustomer.intacctCustomer;
            if (!intacctCustomer) {
                assert.fail('No intacct customer for US017');
            }

            const testData: any = [
                {
                    intacctData: [{ creditLimit: 1000.12, onHold: 'true' }],
                    expectedResult: { creditLimit: 1000.12, isOnHold: true },
                },
                {
                    intacctData: [{ creditLimit: -123, onHold: 'false' }],
                    expectedResult: { creditLimit: -123.0, isOnHold: false },
                },
                {
                    intacctData: [{ creditLimit: '1000.23', onHold: 'falsy' }],
                    expectedResult: { creditLimit: 1000.23, isOnHold: false },
                },
                {
                    intacctData: [{ creditLimit: 'xxx', onHold: 'xxx' }],
                    expectedResult: { creditLimit: 0.0, isOnHold: false },
                },
                { intacctData: [{}], expectedResult: { creditLimit: 0.0, isOnHold: false } },
                {
                    intacctData: [{ creditLimit: null, onHold: null }],
                    expectedResult: { creditLimit: 0.0, isOnHold: false },
                },
            ];

            await asyncArray(testData).forEach(async (test: any, index) => {
                const message = `Test case ${index} failed. intacctData=${JSON.stringify(test.intacctData)}`;

                await intacctCustomer.onDesynchronized(test.intacctData);

                const customerAfter = await context.read(xtremMasterData.nodes.Customer, { businessEntity: '#US017' });
                assert.deepEqual(await customerAfter.creditLimit, test.expectedResult.creditLimit, message);
                assert.deepEqual(await customerAfter.isOnHold, test.expectedResult.isOnHold, message);
            });
        }));
});
