import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as fs from 'fs';
import { before } from 'mocha';
import * as path from 'path';
import * as sinon from 'sinon';
import * as xtremIntacctFinance from '../../../lib';

const dirname = __dirname.replace('/build/', '/');
let sandbox = sinon.createSandbox();

describe('Intacct contact', () => {
    before(() => {
        sandbox = sinon.createSandbox();
    });
    after(() => {
        sandbox.restore();
    });
    it('Contact - get the create query', () =>
        Test.withContext(async context => {
            const [casinoContact] = await context
                .query(xtremMasterData.nodes.BusinessEntityAddress, {
                    filter: { name: 'Géant CASINO Seynod' },
                    forUpdate: true,
                })
                .toArray();
            assert.isNotNull(casinoContact);

            await casinoContact.$.set({ intacctBusinessEntityAddress: { intacctId: 'Siège social S01 PARIS (69)' } });
            await casinoContact.$.save();

            const intacctContact = await casinoContact.intacctBusinessEntityAddress;
            if (!intacctContact) {
                assert.fail('No intacct contact for Géant CASINO Seynod');
            }
            assert.equal(await intacctContact.intacctId, 'Siège social S01 PARIS (69)');

            const genericFunc = await xtremIntacctFinance.classes.intacct.GenericIfunction.create(
                context,
                intacctContact,
            );

            const xmlRequest = await genericFunc.xmlWithoutFunction(true);

            const createContactXmlPath = path.resolve(dirname, 'xml-requests/create-contact.xml');
            const expectedXml = fs.readFileSync(createContactXmlPath).toString();

            assert.equal(xmlRequest, expectedXml);
        }));

    it.skip('Contact - get the create query (to be mock)  ', () =>
        Test.withContext(async context => {
            const [casinoContact] = await context
                .query(xtremMasterData.nodes.BusinessEntityAddress, {
                    filter: { name: 'Géant CASINO Seynod' },
                    forUpdate: true,
                })
                .toArray();
            // Will create intacctBusinessEntityAddress
            await casinoContact.$.save();
            const intacctContact = await casinoContact.intacctBusinessEntityAddress;
            if (!intacctContact) {
                assert.fail('No intacct contact for Géant CASINO Seynod');
            }

            sandbox
                .stub(xtremIntacctFinance.classes.intacct.GenericIfunction.prototype, 'execute')
                .callsFake(() => Promise.resolve([{ RECORDNO: '566', CONTACTNAME: 'Siège social S01 PARIS(69)' }]));

            await xtremIntacctFinance.nodes.IntacctListener.synchronizeNode(context, intacctContact?._id.toString());

            sandbox.reset();
            sandbox.restore();

            const intacctContactRead = await context.read(xtremIntacctFinance.nodes.IntacctSynchronizationState, {
                _id: intacctContact._id,
            });

            assert.equal(await intacctContactRead.state, 'success');
            assert.equal(await intacctContactRead.version, 1);
            assert.equal(await intacctContactRead.intacctId, 'Siège social S01 PARIS (69)');
        }));
    it('Contact - US019', () =>
        Test.withContext(async context => {
            const contactCust01 = await context.read(
                xtremMasterData.nodes.BusinessEntityAddress,
                { _id: '#US019|1500' },
                { forUpdate: true },
            );
            await contactCust01.$.set({ city: 'toto' });
            await contactCust01.$.save();

            const intacctContact = await contactCust01.getSyncStateReference();

            if (!intacctContact) {
                assert.fail('No intacct contact');
            }

            sandbox
                .stub(xtremIntacctFinance.nodes.IntacctSynchronizationState.prototype, 'checkExist')
                .callsFake(() => Promise.resolve({ RECORDNO: 566, NAME: 'Universal Alarmsa One (15)' }));

            const syncManager = await xtremIntacctFinance.classes.IntacctSynchronizationManager.create(
                context,
                intacctContact,
            );
            assert.equal(syncManager.intacctId, 'Universal Alarmsa One (15)');
            assert.deepEqual(syncManager.intacctKey, { NAME: 'Universal Alarmsa One (15)', RECORDNO: 566 });

            sandbox.restore();
            sandbox.reset();
        }));

    it('Contact - US019 - checkExist function ', () =>
        Test.withContext(
            async context => {
                const contactCust01 = await context.read(
                    xtremMasterData.nodes.BusinessEntityAddress,
                    { _id: '#US019|1500' },
                    { forUpdate: true },
                );
                await contactCust01.$.set({ city: 'toto' });
                await contactCust01.$.save();

                const intacctContact = await contactCust01.getSyncStateReference();

                assert.equal(intacctContact?.intacctIdField, 'CONTACTNAME');

                const intacctKey = await intacctContact?.checkExist();

                assert.deepEqual(intacctKey as any, { NAME: 'Universal Alarmsa One (15)', RECORDNO: '38809' });
            },
            {
                scenario: 'contact-check--exist',
                directory: __dirname,
                testAttributes: { isCreationMode: false },
            },
        ));
});
