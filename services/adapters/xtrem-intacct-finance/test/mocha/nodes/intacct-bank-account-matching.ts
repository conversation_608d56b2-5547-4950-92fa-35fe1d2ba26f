import { Test } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremIntacctFinance from '../../../lib';
import * as fixtures from '../../fixtures';

let sandbox = sinon.createSandbox();

describe(' IntacctBankAccountMatching ', () => {
    before(() => {
        sandbox = sinon.createSandbox();
    });
    after(() => {
        sandbox.restore();
    });

    it(' queryIntacctArInvoice from intacct  ', () =>
        Test.withContext(
            async context => {
                const parameters: xtremIntacctFinance.interfaces.BankAccountMatching.IntacctDocumentParameters = {
                    customerId: 'ATSE00074',
                    megaEntityId: '100',
                };

                let isRequestSend = false;
                sandbox
                    .stub(xtremIntacct.classes.sdk.Client.prototype, 'executeOnlineRequest')
                    .callsFake(
                        async (
                            query: xtremIntacct.classes.sdk.Functions.Query<
                                xtremIntacctFinance.interfaces.BankAccountMatching.IntacctArInvoice[]
                            >,
                        ) => {
                            await Promise.resolve();
                            assert.isFalse(query.showPrivate);
                            assert.equal((await query.client).config?.entityId, '100');
                            isRequestSend = true;

                            return fixtures.emptyOnlineResponse;
                        },
                    );

                const arInvoice = await xtremIntacctFinance.nodes.IntacctBankAccountMatching.queryIntacctArInvoice(
                    context,
                    parameters,
                );

                assert.isTrue(isRequestSend);
                assert.isArray(arInvoice);
            },
            {
                testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption],
            },
        ));
});
