describe('IntacctBankAccountTransactionFeed', () => {
    // XT-80680 commented during bank account refactor since cash book management will be removed
    // it('Create IntacctBankAccountTransactionFeed ', () =>
    //     Test.withContext(
    //         async context => {
    //             const mapObject = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
    //                 _id: '#intacct|BANKACCTTXNRECORD|IntacctBankAccountTransactionFeed',
    //             });
    //             const importSession = await context.create(xtremIntacctFinance.nodes.IntacctImportSession, {
    //                 bankAccount: { _id: 1 },
    //                 mapObject, // BANKACCTTXNRECORD
    //             });
    //             await importSession.$.save();
    //             const payload = {
    //                 importSession: { _id: importSession._id },
    //                 number: '259',
    //                 intacctId: '259',
    //                 entity: '700_CHK',
    //                 entityName: 'Demo Bank',
    //                 accountType: 'Bank',
    //                 accountFeedKey: 12,
    //                 transactionId: 1,
    //                 accountReconKey: 0,
    //                 postingDate: date.make(2021, 2, 19),
    //                 reconcilitationDate: null,
    //                 transactionType: 'deposit',
    //                 documentType: 'CREDIT',
    //                 documentNumber: 'Check 12345',
    //                 payee: 'CASH',
    //                 amount: 110,
    //                 description: 'Transaction Narrative',
    //                 intacctCleared: 'Unmatched',
    //                 amountToMatch: 110,
    //                 feedType: 'onl',
    //                 currency: { id: 'ZAR' },
    //                 arMatch: { isArMatch: false, arPaymentType: '' },
    //                 bankAccount: { _id: 1 },
    //                 cleared: 'unmatched',
    //                 lines: [{ amount: 110, location: { _id: 18 } }],
    //             } as NodeCreateData<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed>;
    //             const transactionFeed = await context.create(
    //                 xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed,
    //                 payload,
    //             );
    //             await transactionFeed.$.save();
    //             const savedtransactionFeed = await context.read(
    //                 xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed,
    //                 { _id: transactionFeed._id },
    //             );
    //             assert.isNull(await savedtransactionFeed.financeIntegrationApp);
    //             // assert.strictEqual(await savedtransactionFeed.financeIntegrationStatus, 'notPosted');
    //             assert.strictEqual(await savedtransactionFeed.internalFinanceIntegrationStatus, 'toBeRecorded');
    //             assert.strictEqual(await savedtransactionFeed.financeDocumentCreatedNumber, '');
    //             assert.strictEqual(await savedtransactionFeed.financeDocumentCreatedSysId, 0);
    //         },
    //         {
    //             testActiveServiceOptions: [xtremIntacctFinance.serviceOptions.intacctCashbookManagement],
    //         },
    //     ));
});
