import { Test } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import { changeIntacctOption } from '../../fixtures';

describe('Intacct customer', () => {
    it('Customer - displayContact hidden set to false  ', () =>
        Test.withContext(async context => {
            const intacctConfiguration = await xtremIntacct.nodes.Intacct.defaultInstance(context);
            // By default it must be true
            assert.isTrue(await intacctConfiguration?.isDisplayContactHidden);

            await changeIntacctOption(context, { isDisplayContactHidden: false });

            const cust01 = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' });
            const intacctCustomer = await cust01.intacctCustomer;

            if (!intacctCustomer) {
                throw new Error('intacctCustomer is null');
            }

            assert.isNull(await intacctCustomer.primaryContact);
            assert.isNull(await intacctCustomer.billToAddress);
            assert.isNull(await intacctCustomer.shipToAddress);
        }));
    it('Customer - displayContact hidden set to true  ', () =>
        Test.withContext(async context => {
            const intacctConfiguration = await xtremIntacct.nodes.Intacct.defaultInstance(context);
            // By default it must be true
            assert.isTrue(await intacctConfiguration?.isDisplayContactHidden);

            const cust01 = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' });
            const intacctCustomer = await cust01.intacctCustomer;

            if (!intacctCustomer) {
                throw new Error('intacctCustomer is null');
            }

            const intacctIdPrimaryContact = 'Universal Alarmsa One (15)';

            assert.equal((await intacctCustomer.primaryContact)?.intacctId, intacctIdPrimaryContact);
            assert.equal((await intacctCustomer.billToAddress)?.intacctId, intacctIdPrimaryContact);
            assert.equal((await intacctCustomer.shipToAddress)?.intacctId, intacctIdPrimaryContact);
        }));
});

describe('Intacct Supplier', () => {
    it('Supplier - displayContact hidden set to false  ', () =>
        Test.withContext(async context => {
            const intacctConfiguration = await xtremIntacct.nodes.Intacct.defaultInstance(context);
            // By default it must be true
            assert.isTrue(await intacctConfiguration?.isDisplayContactHidden);

            await changeIntacctOption(context, { isDisplayContactHidden: false });

            const za500 = await context.read(xtremMasterData.nodes.Supplier, { _id: '#500' }, { forUpdate: true });
            const intacctSupplier = await za500.intacctSupplier;

            if (!intacctSupplier) {
                throw new Error('intacctCustomer is null');
            }

            assert.notEqual((await za500.payToAddress)._id, (await za500.primaryAddress)._id);
            assert.notEqual((await za500.returnToAddress)._id, (await za500.primaryAddress)._id);

            assert.isNull(await intacctSupplier.primaryContact);
            assert.isNotNull(await intacctSupplier.payToAddress);
            assert.isNotNull(await intacctSupplier.returnToAddress);

            await za500.$.set({
                payToAddress: await za500.primaryAddress,
                returnToAddress: await za500.primaryAddress,
            });

            await za500.$.save();

            assert.isNull(await intacctSupplier.payToAddress);
            assert.isNull(await intacctSupplier.returnToAddress);
        }));
    it('Supplier - displayContact hidden set to true  ', () =>
        Test.withContext(async context => {
            const intacctConfiguration = await xtremIntacct.nodes.Intacct.defaultInstance(context);
            // By default it must be true
            assert.isTrue(await intacctConfiguration?.isDisplayContactHidden);

            const za500 = await context.read(xtremMasterData.nodes.Supplier, { _id: '#500' });
            const intacctSupplier = await za500.intacctSupplier;
            if (!intacctSupplier) {
                throw new Error('intacctSupplier is null');
            }

            const intacctIdPrimaryContact = 'Vodacom South Africa (Pty) Somewhere (8)';

            assert.equal((await intacctSupplier.primaryContact)?.intacctId, intacctIdPrimaryContact);
            assert.isNotNull(await intacctSupplier.payToAddress); // we will send but not synchronized
            assert.isNotNull(await intacctSupplier.returnToAddress); // we will send but not synchronized
        }));
});
