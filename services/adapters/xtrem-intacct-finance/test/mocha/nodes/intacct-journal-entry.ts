import { Test } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import * as sinon from 'sinon';
import * as xtremIntacctFinance from '../../../lib';
import { disableIntegration, enableIntegration } from '../../fixtures';
import { xmlWithoutFunctionAsJson } from '../../fixtures/synchronization';

const dirname = __dirname.replace('/build/', '/');
let sandbox: sinon.SinonSandbox;

describe('Journal Entry', () => {
    beforeEach(() => {
        sandbox = sinon.createSandbox();
    });
    afterEach(() => {
        sandbox.reset();
        sandbox.restore();
    });

    it('Journal entry XML payload to intacct', () =>
        Test.withContext(async context => {
            const myJournalEntry = await context.read(
                xtremFinance.nodes.JournalEntry,
                { _id: '#STK-2021040123|US|STK' },
                { forUpdate: true },
            );
            await myJournalEntry.$.save();

            const intacctJournalEntry = await myJournalEntry.intacctDocument;

            if (!intacctJournalEntry) {
                assert.fail('No intacct journal entry ');
            }
            // We only Check exist if the intacctJournalEntry is pending error or success
            const myClass = await xtremIntacctFinance.classes.IntacctSynchronizationManager.create(
                context,
                intacctJournalEntry,
            );

            const requestManager = await myClass.getIntacctRequest('create');

            const xmlRequest = `${await xmlWithoutFunctionAsJson(context, {
                sandbox,
                requestManager,
                isXmlReturn: true,
            })}`;

            const createJournalEntryXmlPath = path.resolve(dirname, 'xml-requests/create-journal-entry.xml');
            const expectedXml = fs.readFileSync(createJournalEntryXmlPath).toString().trimEnd();
            assert.equal(xmlRequest, expectedXml, xmlRequest);

            // STK-2021040123|US|STK
        }));
    it('Journal entry XML payload to intacct', () =>
        Test.withContext(async context => {
            const myJournalEntry = await context.read(
                xtremFinance.nodes.JournalEntry,
                {
                    _id: '#STK-2021040123|US|STK',
                },
                { forUpdate: true },
            );
            await myJournalEntry.$.save();

            const intacctJournalEntry = await myJournalEntry.intacctDocument;

            if (!intacctJournalEntry) {
                assert.fail('No intacct journal entry ');
            }

            await intacctJournalEntry.$.set({ state: 'pending' });

            await myJournalEntry.$.save();

            let checkExistCall = false;
            sandbox
                .stub(xtremIntacctFinance.nodes.IntacctSynchronizationFinanceState.prototype, 'checkExistForRetry')
                // eslint-disable-next-line require-await
                .callsFake(async () => {
                    checkExistCall = true;
                    return Promise.resolve(undefined);
                });

            assert.isTrue(await intacctJournalEntry.before());

            assert.isTrue(checkExistCall);
        }));
    it('Journal entry - check entityId for transactionIntegrationLevel = entityLevel', () =>
        Test.withContext(async context => {
            await disableIntegration(context);
            await enableIntegration(context, { id: 'UK DEV' });

            const myJournalEntry: xtremFinance.nodes.JournalEntry = await context.read(
                xtremFinance.nodes.JournalEntry,
                {
                    _id: '#STK-2021040123|US|STK',
                },
            );

            const intacctJournalEntry = await myJournalEntry.intacctDocument;

            if (!intacctJournalEntry) {
                assert.fail('No intacct journal entry ');
            }

            assert.deepEqual(await intacctJournalEntry.entityId, 'US001');
        }));
    it('Journal entry - check entityId for transactionIntegrationLevel = topLevel', () =>
        Test.withContext(async context => {
            const myJournalEntry: xtremFinance.nodes.JournalEntry = await context.read(
                xtremFinance.nodes.JournalEntry,
                {
                    _id: '#STK-2021040123|US|STK',
                },
            );

            const intacctJournalEntry = await myJournalEntry.intacctDocument;

            if (!intacctJournalEntry) {
                assert.fail('No intacct journal entry ');
            }

            assert.deepEqual(await intacctJournalEntry.entityId, '');
        }));
});
