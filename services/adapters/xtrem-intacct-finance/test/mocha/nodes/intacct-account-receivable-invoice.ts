import { Test } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import * as sinon from 'sinon';
import * as xtremIntacctFinance from '../../../lib';
import { disableIntegration, enableIntegration } from '../../fixtures';
import { xmlWithoutFunctionAsJson } from '../../fixtures/synchronization';

const dirname = __dirname.replace('/build/', '/');

describe('Intacct account receivable invoice', () => {
    it('Accounts receivable invoice XML payload to intacct', () =>
        Test.withContext(async context => {
            const myArInvoice = await context.read(
                xtremFinance.nodes.AccountsReceivableInvoice,
                {
                    _id: '#AR-**********|salesInvoice',
                },
                { forUpdate: true },
            );
            await myArInvoice.$.save();

            const intacctArInvoice = await myArInvoice.intacctDocument;

            if (!intacctArInvoice) {
                assert.fail('No intacct accounts receivable invoice');
            }
            // We only Check exist if the intacctAccountsReceivableInvoice is pending error or success
            const myClass = await xtremIntacctFinance.classes.IntacctSynchronizationManager.create(
                context,
                intacctArInvoice,
            );

            const secondLine = await myArInvoice.lines.at(1);
            const intacctArInvoiceLine2 = await secondLine?.intacctDocumentLine;

            if (!intacctArInvoiceLine2) {
                assert.fail('No second line on myArInvoice');
            }

            assert.equal((await intacctArInvoiceLine2.vendorId)?.intacctId, '');

            const requestManager = await myClass.getIntacctRequest('create');
            const sandbox = sinon.createSandbox();

            const xmlRequest = `${await xmlWithoutFunctionAsJson(context, {
                sandbox,
                requestManager,
                isXmlReturn: true,
            })}`;

            const createArInvoiceXmlPath = path.resolve(dirname, 'xml-requests/create-ar-invoice.xml');
            const expectedXml = fs.readFileSync(createArInvoiceXmlPath).toString().trimEnd();

            assert.equal(xmlRequest, expectedXml, xmlRequest);

            sandbox.reset();
            sandbox.restore();
        }));
    it('Account receivable invoice - check entityId for transactionIntegrationLevel = entityLevel', () =>
        Test.withContext(async context => {
            await disableIntegration(context);
            await enableIntegration(context, { id: 'UK DEV' });

            const myArInvoice: xtremFinance.nodes.AccountsReceivableInvoice = await context.read(
                xtremFinance.nodes.AccountsReceivableInvoice,
                { _id: '#AR-**********|salesInvoice' },
            );

            const intacctArInvoice = await myArInvoice.intacctDocument;

            if (!intacctArInvoice) {
                assert.fail('No intacct account receivable invoice');
            }

            assert.deepEqual(await intacctArInvoice.entityId, 'US006');
        }));
    it('Account receivable invoice - check entityId for transactionIntegrationLevel = topLevel', () =>
        Test.withContext(async context => {
            const myArInvoice: xtremFinance.nodes.AccountsReceivableInvoice = await context.read(
                xtremFinance.nodes.AccountsReceivableInvoice,
                { _id: '#AR-**********|salesInvoice' },
            );

            const intacctArInvoice = await myArInvoice.intacctDocument;

            if (!intacctArInvoice) {
                assert.fail('No intacct account receivable invoice');
            }

            assert.deepEqual(await intacctArInvoice.entityId, '');
        }));
});
