// tslint:disable:no-duplicate-string
import { Test } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';

describe('Intacct Listner', () => {
    it('Node - Customer Callback Creation', () =>
        Test.withContext(async context => {
            const secondCustomer = await context.read(xtremMasterData.nodes.Customer, { _id: '#US020' });
            assert.deepEqual(await (await secondCustomer.businessEntity).name, 'Siège social S02 PARIS');
        }));
    it('Intacct Event - return notImplemented if message kind is different than IntacctEvent ', () =>
        Test.withReadonlyContext(async context => {
            const intacctEventReturn = await xtremIntacctFinance.nodes.IntacctListener.intacctEvent(context, {
                attributes: { MessageKind: 'IntacctCallback' },
                payload: {
                    change: '',
                    companyId: '',
                    intacctIdName: '',
                    object: '',
                    recordId: '',
                    recordNumber: 0,
                    lineId: '',
                },
            });
            assert.deepEqual(intacctEventReturn, {
                message: ['IntacctCallback not implemented'],
                receivedRequest:
                    '{"change":"","companyId":"","intacctIdName":"","object":"","recordId":"","recordNumber":0,"lineId":""}',
                status: xtremIntacct.enums.ListenerStatusEnum.received,
            });
        }));
    it('Intacct Event - throw if message kind is different than IntacctEvent ', () =>
        Test.withReadonlyContext(async context => {
            const intacctEventReturn = await xtremIntacctFinance.nodes.IntacctListener.intacctEvent(context, {
                attributes: { MessageKind: 'whatever' },
                payload: {
                    change: '',
                    companyId: '',
                    intacctIdName: '',
                    object: 'LOCATIONENTITY',
                    recordId: '',
                    recordNumber: 0,
                    lineId: '',
                },
            });
            assert.deepEqual(intacctEventReturn, {
                message: ['Asynchronous test received'],
                receivedRequest:
                    '{"change":"","companyId":"","intacctIdName":"","object":"LOCATIONENTITY","recordId":"","recordNumber":0,"lineId":""}',
                status: xtremIntacct.enums.ListenerStatusEnum.done,
            });
        }));
    it('Intacct Event - Not managed object  ', () =>
        Test.withReadonlyContext(async context => {
            const intacctEventReturn = await xtremIntacctFinance.nodes.IntacctListener.intacctEvent(context, {
                attributes: { MessageKind: 'IntacctEvent' },
                payload: {
                    change: 'create',
                    companyId: '',
                    intacctIdName: '',
                    object: 'NOTMANAGED',
                    recordId: '',
                    recordNumber: 0,
                    lineId: '',
                },
            });
            assert.deepEqual(intacctEventReturn, {
                message: ['Not implemented: create:NOTMANAGED.'],
                receivedRequest:
                    '{"change":"create","companyId":"","intacctIdName":"","object":"NOTMANAGED","recordId":"","recordNumber":0,"lineId":""}',
                status: xtremIntacct.enums.ListenerStatusEnum.error,
            });
        }));
});
