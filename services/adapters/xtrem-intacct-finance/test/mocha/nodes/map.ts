import { Test } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacct from '@sage/xtrem-intacct';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../index';
import { getPayLoad } from '../../../lib/functions';
import { updateXtrem } from '../../../lib/functions/map';

describe('Map node', () => {
    before(() => {});

    it('Get additionnal filter - departement  ', () =>
        Test.withContext(async context => {
            const departmentMap = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|DEPARTMENT|Dimension',
            });
            assert.deepEqual(await departmentMap.getAdditionalFilter<xtremFinanceData.nodes.Dimension>(), {
                dimensionType: { intacctObject: { _in: ['DEPARTMENT'] } },
            });
        }));

    it('Get additionnal filter - EMPLOYEE  ', () =>
        Test.withContext(async context => {
            const employeeMap = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|EMPLOYEE|Attribute',
            });
            assert.deepEqual(await employeeMap.getAdditionalFilter<xtremFinanceData.nodes.Attribute>(), {
                attributeType: { id: { _in: ['employee'] } },
            });
        }));

    it('Get additionnal filter - APTERM  ', () =>
        Test.withContext(async context => {
            const termsMap = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|APTERM|PaymentTerm',
            });
            assert.deepEqual(await termsMap.getAdditionalFilter<xtremMasterData.nodes.PaymentTerm>(), {
                businessEntityType: { _in: ['supplier', 'all'] },
            });
        }));

    it('Get additionnal filter - GLACCOUNT  ', () =>
        Test.withContext(
            async context => {
                const glAccount = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                    _id: '#intacct|GLACCOUNT|Account',
                });
                const additionnalFilter = await glAccount.getAdditionalFilter<xtremFinanceData.nodes.Account>();
                assert.deepEqual(additionnalFilter, { chartOfAccount: { _eq: 1 } });
            },
            {
                testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption],
            },
        ));

    it('Get Intacct transactions  ', () =>
        Test.withContext(
            async context => {
                const intacctTransactionList =
                    await xtremIntacctFinance.nodes.IntacctMap.getIntacctTransactionsList(context);
                assert.isTrue(intacctTransactionList.length > 40, ` Lenght : ${intacctTransactionList.length}`);
                assert.equal(
                    intacctTransactionList.find(transaction => transaction.object === 'PROVIDERPAYMENTMETHOD')?.name,
                    'Provider payment method',
                );
            },
            {
                scenario: 'map-transactions',
                directory: __dirname,
            },
        ));

    it('Reading map node - test functions & methods ', () =>
        Test.withContext(async context => {
            const supplierMap = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|VENDOR|Supplier',
            });
            assert.instanceOf(supplierMap, xtremIntacctFinance.nodes.IntacctMap);
            assert.strictEqual(await (await supplierMap.nodeFactory).name, 'Supplier');
            /** Get Mapping File  */
            assert.isObject(await supplierMap.getFile);

            assert.strictEqual((await supplierMap.getFile).documentType, '');
            assert.isArray((await supplierMap.getFile).fields);
            assert.isArray((await supplierMap.getFile).relationshipFields);
            assert.isArray((await supplierMap.getFile).relationships);

            /** Get the intacctID Field  */
            assert.strictEqual(await (await supplierMap.intacctIDField)?.name, 'VENDORID');
            assert.strictEqual(await (await supplierMap.intacctIDField)?.type, 'intacctId');

            /**  Get the list of field to request */
            assert.deepEqual(
                await supplierMap.intacctFields,
                [
                    'VENDORID',
                    'NAME',
                    'PARENTID',
                    'TERMNAME',
                    'TAXID',
                    'STATUS',
                    'CURRENCY',
                    'PAYMETHODKEY',
                    'DISPLAYCONTACT.PREFIX',
                    'DISPLAYCONTACT.FIRSTNAME',
                    'DISPLAYCONTACT.LASTNAME',
                    'DISPLAYCONTACT.PRINTAS',
                    'DISPLAYCONTACT.PHONE1',
                    'DISPLAYCONTACT.EMAIL1',
                    'DISPLAYCONTACT.MAILADDRESS.ADDRESS1',
                    'DISPLAYCONTACT.MAILADDRESS.ADDRESS2',
                    'DISPLAYCONTACT.MAILADDRESS.CITY',
                    'DISPLAYCONTACT.MAILADDRESS.STATE',
                    'DISPLAYCONTACT.MAILADDRESS.ZIP',
                    'DISPLAYCONTACT.MAILADDRESS.COUNTRYCODE',
                    'CONTACTINFO.CONTACTNAME',
                    'PAYTO.CONTACTNAME',
                    'RETURNTO.CONTACTNAME',
                ],
                JSON.stringify(await supplierMap.intacctFields, null, 4),
            );
        }));

    it('Get Data Api', () =>
        Test.withContext(
            context => xtremIntacctFinance.nodes.IntacctMap.getDataIntacct(context, 'SODOCUMENT', 'Sales Order'),
            {
                scenario: 'map-SODODCUMENT',
                directory: __dirname,
            },
        ));

    it('Reading map node - test functions & methods ', () =>
        Test.withContext(async context => {
            const supplierMap = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|TEST|Site',
            });
            const payload = await xtremIntacctFinance.functions.getPayLoadWithMapData(supplierMap, [
                {
                    PROPERTY: '1234',
                    SECONDPROPERTY: '12345',
                },
            ]);
            assert.deepEqual(payload, [
                {
                    test: '1234',
                    id: '12345',
                },
            ]);
        }));

    it('getPayLoad Api ', () =>
        Test.withContext(
            async context => {
                const intacctAllArterms = await getPayLoad(context, 'ARTERM');
                assert.equal(intacctAllArterms.length, 9);
            },
            {
                scenario: 'ARTERM-payload-all',
                directory: __dirname,
            },
        ));

    it('Xtrem Deletion from intacct', () =>
        Test.withContext(context =>
            (() => xtremIntacctFinance.nodes.IntacctMap.deleteXtrem(context, 'ARTERM', 'Net 60'))(),
        ));

    it('Xtrem Creation from intacct', () =>
        Test.withContext(
            context =>
                (() =>
                    updateXtrem(context, {
                        intacctName: 'ARTERM',
                        intacctIdValue: 'Net 30',
                        isThrowingDiagnose: true,
                    }))(),
            {
                scenario: 'ARTERM-Net30',
                directory: __dirname,
            },
        ));

    it('Xtrem Update from intacct', () =>
        Test.withContext(
            context =>
                (() =>
                    updateXtrem(context, {
                        intacctName: 'ARTERM',
                        intacctIdValue: 'Net 60',
                        isThrowingDiagnose: true,
                    }))(),
            {
                scenario: 'ARTERM-Net60',
                directory: __dirname,
            },
        ));
});
