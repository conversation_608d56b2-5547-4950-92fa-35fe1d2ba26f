import { Test } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';

describe('Company sync with Intacct', () => {
    it('Company on hold check sync with Intacct', () =>
        Test.withContext(
            async context => {
                const company = await context.read(xtremSystem.nodes.Company, { id: 'S1' }, { forUpdate: true });
                assert.equal(await company?.customerOnHoldCheck, 'blocking');

                const success = await xtremIntacctFinance.nodeExtensions.CompanyExtension.syncCompanyOnHold(
                    context,
                    String(company._id),
                    false,
                );

                if (success) {
                    const updatedCompany = await context.read(
                        xtremSystem.nodes.Company,
                        { id: 'S1' },
                        { forUpdate: true },
                    );
                    assert.equal(await updatedCompany?.customerOnHoldCheck, 'warning');
                }
            },
            {
                scenario: 'company-on-hold',
                directory: __dirname,
                testAttributes: { isCreationMode: false },
            },
        ));

    it('Company on hold check - Fail when update is normal save and Intacct is active', () =>
        Test.withContext(async context => {
            const company = await context.read(xtremSystem.nodes.Company, { id: 'S1' }, { forUpdate: true });
            assert.equal(await company?.customerOnHoldCheck, 'blocking');

            await company.$.set({
                customerOnHoldCheck: 'warning',
            });

            await assert.isRejected(company.$.save());
            assert.deepEqual(company.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'You cannot update customer on hold if Sage Intacct is active.',
                },
            ]);

            // Turn Intacct off and save
            const defaultInstance = await xtremIntacct.nodes.Intacct.defaultInstance(context);

            if (defaultInstance) {
                const intactInstance = await context.read(
                    xtremIntacct.nodes.Intacct,
                    { _id: defaultInstance._id },
                    { forUpdate: true },
                );
                await intactInstance.$.set({
                    isActive: false,
                });
                await intactInstance.$.save();

                await company.$.save();
            }
        }));
});
