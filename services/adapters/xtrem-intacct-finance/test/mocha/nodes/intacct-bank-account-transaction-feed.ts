import type { NodePayloadData } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremIntacctFinance from '../../../lib';
import * as fixtures from '../../fixtures';

let sandbox = sinon.createSandbox();

describe(' transaction Feed  ', () => {
    before(() => {
        sandbox = sinon.createSandbox();
    });
    after(() => {
        sandbox.restore();
    });

    it('Call to backSearchIntacctData', () =>
        Test.withContext(
            async context => {
                const intacctImportSession = await context
                    .query(xtremIntacctFinance.nodes.IntacctImportSession, {
                        filter: { description: { _eq: 'for Test' } },
                        first: 1,
                    })
                    .at(0);
                if (!intacctImportSession) {
                    assert.fail('No import session for Test');
                }

                const feeds: NodePayloadData<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed>[] = [
                    {
                        amount: 100,
                        description: 'test',
                    },
                ];

                fixtures.sinon.getIntacctTransactionFeedStub(sandbox, feeds);
            },
            {
                testActiveServiceOptions: [
                    xtremIntacct.serviceOptions.intacctOption,
                    xtremIntacctFinance.serviceOptions.intacctCashbookManagement,
                ],
            },
        ));
});
