import * as xtremCommunication from '@sage/xtrem-communication';
import { Test, TextStream } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../index';

describe('Intacct Listner', () => {
    const intacctEventEnvelope: xtremCommunication.MessageEnvelope<xtremIntacct.interfaces.IntacctEventPayload> = {
        attributes: { MessageKind: 'IntacctEvent' },
        payload: {
            change: 'create',
            companyId: 'Int-X3-WD',
            object: 'test',
            intacctIdName: 'testID',
            recordId: '100',
            recordNumber: 1,
            lineId: '1235',
        },
    };

    it(' Node - communication history check   ', () =>
        Test.withContext(async context => {
            const history = await xtremIntacctFinance.nodes.IntacctListener.checkMessageHistory(
                context,
                intacctEventEnvelope,
            );
            assert.isNull(history);
            const historyCreated = await xtremIntacctFinance.nodes.IntacctListener.checkMessageHistory(
                context,
                intacctEventEnvelope,
            );
            assert.deepEqual(await historyCreated?.integrationSolution, 'intacct');
            assert.deepEqual(await historyCreated?.id, '1235');
        }));

    it('Node - communication history update - lineId', () =>
        Test.withContext(async context => {
            const history = await xtremIntacctFinance.nodes.IntacctListener.checkMessageHistory(
                context,
                intacctEventEnvelope,
            );
            assert.isNull(history);

            const historyUpdated1 = await context.read(xtremCommunication.nodes.SysMessageHistory, {
                id: intacctEventEnvelope.payload.lineId,
            });

            assert.deepEqual(await historyUpdated1.status, 'received');

            await xtremIntacctFinance.nodes.IntacctListener.updateMessageHistory(context, intacctEventEnvelope, {
                status: xtremIntacct.enums.ListenerStatusEnum.done,
                message: ['Ok'],
                receivedRequest: 'payload',
            });

            const historyUpdated2 = await context.read(xtremCommunication.nodes.SysMessageHistory, {
                id: '1235',
            });

            assert.deepEqual(await historyUpdated2.status, 'success');
        }));

    it(' Node - communication history update - LineID     ', () =>
        Test.withContext(async context => {
            const history = await xtremIntacctFinance.nodes.IntacctListener.checkMessageHistory(
                context,
                intacctEventEnvelope,
            );
            assert.isNull(history);

            const historyUpdated1 = await context.read(xtremCommunication.nodes.SysMessageHistory, {
                id: intacctEventEnvelope.payload.lineId,
            });

            assert.deepEqual(await historyUpdated1.status, 'received');

            await xtremIntacctFinance.nodes.IntacctListener.updateMessageHistory(context, intacctEventEnvelope, {
                status: xtremIntacct.enums.ListenerStatusEnum.done,
                message: ['Ok'],
                receivedRequest: intacctEventEnvelope.payload,
            });

            const historyUpdated2 = await context.read(xtremCommunication.nodes.SysMessageHistory, {
                id: intacctEventEnvelope.payload.lineId,
            });

            assert.deepEqual(await historyUpdated2.status, 'success');

            assert.deepEqual(
                (await historyUpdated2.receivedRequest).toString(),
                '{"change":"create","companyId":"Int-X3-WD","object":"test","intacctIdName":"testID","recordId":"100","recordNumber":1,"lineId":"1235"}',
            );
        }));
    it(' Node - SysMessageHistory Create     ', () =>
        Test.withContext(context =>
            xtremCommunication.nodes.SysMessageHistory.createOrUpdateMessageHistory(context, {
                integrationSolution: 'intacct',
                id: '12345',
                sentRequest: TextStream.empty,
                receivedRequest: TextStream.fromJsonObject({}),
                status: 'received',
                attributes: {},
            }),
        ));
});
