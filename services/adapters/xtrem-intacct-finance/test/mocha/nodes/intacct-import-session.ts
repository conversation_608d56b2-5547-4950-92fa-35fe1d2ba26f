import { Test } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';

describe('Intacct import session Node', () => {
    before(() => {});

    it(' Test replayMatchingRules ', () =>
        Test.withContext(
            async context => {
                const importSessionQuery = await context
                    .query(xtremIntacctFinance.nodes.IntacctImportSession, {
                        filter: { description: { _eq: 'for Test' } },
                        forUpdate: true,
                    })
                    .toArray();

                const listOfMatchingRules = context.query(xtremIntacctFinance.nodes.IntacctBankAccountMatching, {});
                assert.equal(importSessionQuery.length, 1, 'No IntacctImportSession ');

                const importSession = importSessionQuery[0];

                const numberFeedMatchToto = await importSession.transactionFeed.filter(async feed => {
                    return (await feed.description).search('toto') !== -1;
                }).length;
                assert.equal(numberFeedMatchToto, 2, 'the feed will not match toto');

                const numberFeedMatchCharity = await importSession.transactionFeed.filter(async feed => {
                    return (await feed.description) === 'Charity';
                }).length;
                assert.equal(numberFeedMatchCharity, 2, 'the feed will not match Charity');

                assert.equal(await listOfMatchingRules.length, 8, ' the number of matching rules changes');
            },
            {
                testActiveServiceOptions: [
                    xtremIntacct.serviceOptions.intacctOption,
                    xtremIntacctFinance.serviceOptions.intacctCashbookManagement,
                ],
            },
        ));
});
