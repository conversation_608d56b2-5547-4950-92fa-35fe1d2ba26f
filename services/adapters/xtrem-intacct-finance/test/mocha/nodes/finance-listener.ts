// tslint:disable:no-duplicate-string
import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremIntacctFinance from '../../../lib';
import { createIntacctDocument } from '../../fixtures/synchronization';

describe('Finance Listener', () => {
    it('retryFinanceDocument mutation - post document to intacct again', () =>
        Test.withContext(async context => {
            await createIntacctDocument(context, { _id: 6 });

            const financeTransaction = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                batchId: 'cdf35002-6b75-4e4b-8-MISC_RECEIPT_10',
                documentNumber: 'MISC_RECEIP10',
                documentType: 'miscellaneousStockReceipt',
                targetDocumentType: 'journalEntry',
            });

            const result = await xtremIntacctFinance.nodes.FinanceListener.retryFinanceDocument(
                context,
                financeTransaction,
                6,
            );
            assert.strictEqual(result.wasSuccessful, true);

            assert.strictEqual(result.message, 'Processing.');
        }));

    it('retryFinanceDocument mutation - target document type not supported', () =>
        Test.withContext(async context => {
            await createIntacctDocument(context, { _id: 6 });

            const financeTransaction = await context.read(
                xtremFinanceData.nodes.FinanceTransaction,
                {
                    batchId: 'invalid-target-docyment-type-01',
                    documentNumber: 'FAKECASH01',
                    documentType: 'bankReconciliationDeposit',
                    targetDocumentType: 'accountsReceivablePayment',
                },
                { forUpdate: true },
            );

            await assert.isRejected(
                xtremIntacctFinance.nodes.FinanceListener.retryFinanceDocument(
                    context,
                    financeTransaction,
                    6, // not six, but it will catch the wrong target document type issue first.
                ),
                'accountsReceivablePayment: Target document type not supported.',
            );
        }));

    it('retryFinanceDocument mutation - Status updated.', () =>
        Test.withContext(async context => {
            const intacctDocument = await createIntacctDocument(context, { _id: 7 });

            let financeTransaction = await context.read(
                xtremFinanceData.nodes.FinanceTransaction,
                {
                    batchId: 'cdf35002-6b75-4e4b-8-MISC_RECEIPT_11',
                    documentNumber: 'MISC_RECEIP11',
                    documentType: 'miscellaneousStockReceipt',
                    targetDocumentType: 'journalEntry',
                },
                { forUpdate: true },
            );

            const replyFinanceSpy = sinon.spy(context, 'reply');

            const result = await xtremIntacctFinance.nodes.FinanceListener.retryFinanceDocument(
                context,
                financeTransaction,
                7,
            );

            // There is no intacct id there we launch the notification to execute
            assert.strictEqual(result.message, 'Status updated.');

            assert.equal(replyFinanceSpy.getCalls().length, 1);
            const replyDocumentPayload = replyFinanceSpy.args.at(0);

            assert.equal(replyDocumentPayload?.length, 3);
            assert.equal(replyDocumentPayload?.at(0), 'MiscellaneousStockReceipt/accountingInterface');
            assert.equal(
                JSON.stringify(replyDocumentPayload?.at(1)),
                JSON.stringify({
                    batchId: await financeTransaction.batchId,
                    documentNumber: await financeTransaction.documentNumber,
                    documentType: await financeTransaction.documentType,
                    targetDocumentType: await financeTransaction.targetDocumentType,
                    targetDocumentNumber: await (await intacctDocument?.document)?.number,
                    targetDocumentSysId: (await intacctDocument?.document)?._id,
                    validationMessages: [],
                    status: 'posted',
                    financeExternalIntegration: { app: 'intacct', recordId: 'IJ-011', url: '' },
                    isJustForStatus: true,
                }),
            );
            assert.equal(
                JSON.stringify(replyDocumentPayload?.at(2)),
                JSON.stringify({ replyId: '3_AmIteYhGk6i_QZ4-001' }),
            );

            financeTransaction = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                batchId: 'cdf35002-6b75-4e4b-8-MISC_RECEIPT_11',
                documentNumber: 'MISC_RECEIP11',
                documentType: 'miscellaneousStockReceipt',
                targetDocumentType: 'journalEntry',
            });

            const fTransaction = {
                targetDocumentNumber: await financeTransaction.targetDocumentNumber,
                targetDocumentSysId: await financeTransaction.targetDocumentSysId,
                message: await financeTransaction.message,
                status: await financeTransaction.status,
            };

            assert.deepEqual(fTransaction, {
                targetDocumentNumber: 'IJ-011',
                targetDocumentSysId: 7,
                message: '',
                status: 'posted',
            });
        }));
});
