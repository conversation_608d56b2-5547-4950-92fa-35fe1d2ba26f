import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import { changeIntacctOption } from '../../fixtures';
import { transFormContactListSysId } from '../../fixtures/customer-supplier';

describe('Intacct customer', () => {
    it('Customer - contactList - with category Names clean  ', () =>
        Test.withContext(async context => {
            await changeIntacctOption(context, { isCategoryNameClean: true });
            const beCust07 = await context.read(
                xtremMasterData.nodes.BusinessEntity,
                { _id: '#CUST07' },
                { forUpdate: true },
            );
            await beCust07.$.save(); // will create automaticly the intacctCustomer

            const cust07 = await beCust07.customer;

            if (!cust07) {
                throw new Error('No customer CUST07');
            }
            const contactList = await (await cust07.intacctCustomer)?.contactList;

            if (!contactList) {
                throw new Error('contactList is null');
            }

            // get the shipToAddress that is not the primary & not the one that is also billTo address
            const shipToAddress = await beCust07.addresses.find(
                async address =>
                    !!(await address.deliveryDetail) &&
                    !(await (
                        await address.deliveryDetail
                    )?.isPrimary) &&
                    address._id !== (await cust07.billToAddress)?._id,
            );

            const contactListCust07 = [
                {
                    CATEGORYNAME: 'Primary business entity address',
                    sysId: `${(await beCust07.primaryAddress)?._id ?? ''}`,
                },
                { CATEGORYNAME: 'Primary address', sysId: `${(await cust07.primaryAddress)?._id ?? ''}` },
                { CATEGORYNAME: 'Ship-to address', sysId: `${shipToAddress?._id ?? ''}` },
                { CATEGORYNAME: 'Bill-to address', sysId: `${(await cust07.billToAddress)?._id ?? ''}` },
                { CATEGORYNAME: 'Primary ship-to address', sysId: `${(await cust07.primaryShipToAddress)?._id ?? ''}` },
            ];
            assert.deepEqual(
                transFormContactListSysId(contactList),
                contactListCust07,
                contactList.map(contact => JSON.stringify(contact)).join('\n'),
            );
        }));
});
