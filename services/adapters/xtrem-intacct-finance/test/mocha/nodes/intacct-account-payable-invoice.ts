import { Test } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import * as sinon from 'sinon';
import * as xtremIntacctFinance from '../../../lib';
import { disableIntegration, enableIntegration } from '../../fixtures';
import { xmlWithoutFunctionAsJson } from '../../fixtures/synchronization';

const dirname = __dirname.replace('/build/', '/');
let sandbox: sinon.SinonSandbox;

describe('Intacct account payable invoice', () => {
    beforeEach(() => {
        sandbox = sinon.createSandbox();
    });
    afterEach(() => {
        sandbox.reset();
        sandbox.restore();
    });

    it('Accounts payable invoice XML payload to intacct', () =>
        Test.withContext(async context => {
            const myApInvoice = await context.read(
                xtremFinance.nodes.AccountsPayableInvoice,
                { _id: '#PIINTACCT01|purchaseInvoice' },
                { forUpdate: true },
            );
            await myApInvoice.$.save();

            const intacctApInvoice = await myApInvoice.intacctDocument;

            if (!intacctApInvoice) {
                assert.fail('No intacct accounts payable invoice');
            }
            // We only Check exist if the intacctAccountsPayableInvoice is pending error or success
            const myClass = await xtremIntacctFinance.classes.IntacctSynchronizationManager.create(
                context,
                intacctApInvoice,
            );
            const supplierSyncState = await intacctApInvoice.billBySupplier;

            assert.equal(supplierSyncState?.intacctId, '');

            const requestManager = await myClass.getIntacctRequest('create');

            const xmlRequest = `${await xmlWithoutFunctionAsJson(context, {
                sandbox,
                requestManager,
                isXmlReturn: true,
            })}`;

            const createApInvoiceXmlPath = path.resolve(dirname, 'xml-requests/create-ap-invoice.xml');
            const expectedXml = fs.readFileSync(createApInvoiceXmlPath).toString().trimEnd();

            assert.equal(xmlRequest, expectedXml, xmlRequest);
        }));
    it('Account payable invoice - check entityId for transactionIntegrationLevel = entityLevel', () =>
        Test.withContext(async context => {
            await disableIntegration(context);
            await enableIntegration(context, { id: 'UK DEV' });

            const myApInvoice: xtremFinance.nodes.AccountsPayableInvoice = await context.read(
                xtremFinance.nodes.AccountsPayableInvoice,
                { _id: '#AP-**********|purchaseInvoice' },
            );

            const intacctApInvoice = await myApInvoice.intacctDocument;

            if (!intacctApInvoice) {
                assert.fail('No intacct account payable invoice');
            }

            assert.deepEqual(await intacctApInvoice.entityId, 'US006');
        }));
    it('Account payable invoice - check entityId for transactionIntegrationLevel = topLevel', () =>
        Test.withContext(async context => {
            const myApInvoice: xtremFinance.nodes.AccountsPayableInvoice = await context.read(
                xtremFinance.nodes.AccountsPayableInvoice,
                { _id: '#AP-**********|purchaseInvoice' },
            );

            const intacctApInvoice = await myApInvoice.intacctDocument;

            if (!intacctApInvoice) {
                assert.fail('No intacct account payable invoice');
            }

            assert.deepEqual(await intacctApInvoice.entityId, '');
        }));
});
