import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import { changeIntacctOption } from '../../fixtures';
import { cleanContactListCust01, contactListCust01 } from '../../fixtures/customer';
import { transFormContactList } from '../../fixtures/customer-supplier';
import { cleanContactListZa001, contactListZa001 } from '../../fixtures/supplier';

describe('Intacct customer', () => {
    it('Customer - contactList - standard - category Names  ', () =>
        Test.withContext(async context => {
            const cust01 = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' });
            const contactList = await (await cust01.intacctCustomer)?.contactList;

            if (!contactList) {
                throw new Error('contactList is null');
            }
            assert.deepEqual(transFormContactList(contactList), contactListCust01);
        }));
    it('Customer - contactList - clean category Names  ', () =>
        Test.withContext(async context => {
            await changeIntacctOption(context, { isCategoryNameClean: true });

            const cust01 = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' });
            const contactList = await (await cust01.intacctCustomer)?.contactList;
            if (!contactList) {
                throw new Error('contactList is null');
            }
            assert.deepEqual(transFormContactList(contactList), cleanContactListCust01);
        }));
});

describe('Intacct supplier', () => {
    it('Supplier - contactList - standard - category Names  ', () =>
        Test.withContext(async context => {
            const za500 = await context.read(xtremMasterData.nodes.Supplier, { _id: '#500' });
            const contactList = await (await za500.intacctSupplier)?.contactList;
            if (!contactList) {
                throw new Error('contactList is null');
            }
            assert.deepEqual(transFormContactList(contactList), contactListZa001);
        }));
    it('Supplier - contactList - clean category Names  ', () =>
        Test.withContext(async context => {
            await changeIntacctOption(context, { isCategoryNameClean: true });

            const za500 = await context.read(xtremMasterData.nodes.Supplier, { _id: '#500' });
            const contactList = await (await za500.intacctSupplier)?.contactList;
            if (!contactList) {
                throw new Error('contactList is null');
            }
            assert.deepEqual(transFormContactList(contactList), cleanContactListZa001);
        }));
});
