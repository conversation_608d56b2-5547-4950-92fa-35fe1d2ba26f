import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremIntacctFinance from '../../../lib';

describe('Item', () => {
    it('Item - custom fields', () =>
        Test.withContext(async context => {
            const itemChemiCalA = await context.read(
                xtremMasterData.nodes.Item,
                { id: 'Chemical A' },
                { forUpdate: true },
            );
            await itemChemiCalA.$.set({ _customData: { customString: 'my custom string' } });

            await itemChemiCalA.$.save();

            const intacctItem = await itemChemiCalA.intacctItem;

            if (!intacctItem) {
                assert.fail('No intacct item');
            }

            const intacctRequest = await xtremIntacctFinance.classes.intacct.GenericIfunction.create(
                context,
                intacctItem,
            );

            const itemPayload = await itemChemiCalA.$.payload();

            assert.deepEqual(itemPayload._customData, { customString: 'my custom string' });

            const customDataValue = await itemChemiCalA._customData;

            assert.deepEqual(customDataValue, { customString: 'my custom string' });

            const customData = await intacctRequest.getValue('_customData');
            assert.deepEqual(customData, { customString: 'my custom string' });

            const customString = await intacctRequest.getValue('_customData.customString');

            assert.equal(customString, 'my custom string');
            assert.deepEqual(customData, { customString: 'my custom string' });
        }));

    it('map Xtrem Item ', () =>
        Test.withContext(async context => {
            const payload = {
                recordNo: 11088,
                id: 'Item A',
                intacctItem: {
                    intacctId: 'Item A',
                    status: 'active',
                    valuationMethod: 'Average',
                    type: 'Inventory',
                    shipWeight: 2,
                },
                name: 'Item A',
                description: 'Item A',
            };
            const validPayload = await xtremIntacctFinance.nodes.IntacctItem.initPayload(context, payload);

            assert.strictEqual(validPayload.id, 'Item A');
        }));
    it(' Item - synchronize node notify call  ', () =>
        Test.withContext(async context => {
            const notifyStub = sinon.stub(context, 'notify');

            const itemChemiCalA = await context.read(
                xtremMasterData.nodes.Item,
                { id: 'Chemical A' },
                { forUpdate: true },
            );
            const currentVersion = (await (await itemChemiCalA.intacctItem)?.version) || 0;
            const intacctItemSysId = (await itemChemiCalA.intacctItem)?._id;
            await itemChemiCalA.$.set({ name: 'My chemical A' });
            await itemChemiCalA.$.save();
            /** Will call the  synchronizeNode & update the version */
            sinon.assert.calledOnceWithExactly(
                notifyStub,
                'IntacctListener/synchronizeNode/start',
                {
                    intacctNode: (await itemChemiCalA.getSyncStateReference())?._id,
                },
                { replyTopic: 'SysNotificationState/updateStatus' },
            );

            // Issue on select :
            // SELECT  AS t4.version AS t_4_version ....
            // const [{ intacctItem }] = await context.select(
            //     xtremMasterData.nodes.Item,
            //     { intacctItem: { version: true } },
            //     { filter: { id: 'Chemical A' } },
            // );

            const [{ version }] = await context.select(
                xtremIntacctFinance.nodes.IntacctItem,
                { version: true },
                { filter: { _id: intacctItemSysId } },
            );

            assert.equal(version, currentVersion + 1);
        }));
    it("Item - don't synchronize", () =>
        Test.withContext(async context => {
            const notifyStub = sinon.stub(context, 'notify');

            const itemChemiCalA = await context.read(
                xtremMasterData.nodes.Item,
                { id: 'Chemical A' },
                { forUpdate: true },
            );
            const currentVersion = (await (await itemChemiCalA.intacctItem)?.version) || 0;
            const intacctItemSysId = (await itemChemiCalA.intacctItem)?._id;
            await itemChemiCalA.$.save();

            sinon.assert.notCalled(notifyStub);
            const [{ version }] = await context.select(
                xtremIntacctFinance.nodes.IntacctItem,
                { version: true },
                { filter: { _id: intacctItemSysId } },
            );

            assert.equal(version, currentVersion);
        }));
});
