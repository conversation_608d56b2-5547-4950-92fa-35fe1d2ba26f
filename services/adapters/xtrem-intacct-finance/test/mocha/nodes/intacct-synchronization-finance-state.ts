import { Test } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import { assert } from 'chai';

describe('Intacct synchronization finance state', () => {
    it('Journal entry', () =>
        Test.withContext(async context => {
            const journalEntry = await context.read(xtremFinance.nodes.JournalEntry, { _id: '#STK-2021040123|US|STK' });

            const intacctJournalEntry = await journalEntry.intacctDocument;

            assert.equal(intacctJournalEntry?.documentNumberField, 'REFERENCENO');
            assert.equal(intacctJournalEntry?.intacctIdField, 'BATCHNO');

            assert.strictEqual(await intacctJournalEntry?.intacctId, '61');
        }));
});
