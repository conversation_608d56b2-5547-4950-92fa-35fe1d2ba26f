import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import * as xtremIntacctFinance from '../../../lib';

const dirname = __dirname.replace('/build/', '/');

describe('Generic Classes For Intacct - item', () => {
    it('Generic I function item', () =>
        Test.withContext(async context => {
            const itemChemiCalA = await context.read(
                xtremMasterData.nodes.Item,
                { id: 'Chemical A' },
                { forUpdate: true },
            );
            await itemChemiCalA.$.save();

            const intacctItem = await itemChemiCalA.getSyncStateReference();
            assert.isNotNull(intacctItem);

            if (!intacctItem) {
                throw new Error('No intacct item');
            }
            const intacctRequest = await xtremIntacctFinance.classes.intacct.GenericIfunction.create(
                context,
                intacctItem,
            );
            intacctRequest.typeRequest = 'create';
            const xmlCreateRequest = await intacctRequest.xmlWithoutFunction(true);

            const createItemXmlPath = path.resolve(dirname, 'xml-requests/create-item.xml');
            const expectedXml = fs.readFileSync(createItemXmlPath).toString();

            assert.equal(xmlCreateRequest, expectedXml);
        }));
});
