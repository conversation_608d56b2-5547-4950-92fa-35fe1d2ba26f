import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremStructure from '@sage/xtrem-structure';
import { assert } from 'chai';
// import * as xtremIntacctFinance from '../../../lib';

describe('Service options controls', () => {
    it('Enable and disable payment tracking option and control against intacct activation option', () =>
        Test.withContext(
            async context => {
                // initial state of the paymentTrackingOption service option
                let isPaymentTrackingServiceOptionEnabled = await context.isServiceOptionEnabled(
                    xtremFinanceData.serviceOptions.paymentTrackingOption,
                );
                // initial state of the intacctActivationOption service option
                let isintacctActivationServiceOptionEnabled = await context.isServiceOptionEnabled(
                    xtremStructure.serviceOptions.intacctActivationOption,
                );
                // initial state of the intacct connection
                let intacctConnection = await context.read(
                    xtremIntacct.nodes.Intacct,
                    { id: 'XTREM' },
                    { forUpdate: true },
                );

                // at the beginning, both service options are false, but intacct connections is active
                assert.equal(isPaymentTrackingServiceOptionEnabled, false);
                assert.equal(isintacctActivationServiceOptionEnabled, false);
                assert.equal(await intacctConnection.isActive, true);

                // So we nee to put it coherent and set intacctActivationOption to true
                await context.serviceOptionManager.setServiceOptionActive(
                    context,
                    xtremStructure.serviceOptions.intacctActivationOption,
                    true,
                );
                assert.deepEqual(context.diagnoses, [
                    {
                        message: 'There are AP invoices that are not yet posted to the GL.',
                        path: [],
                        severity: 2,
                    },
                    {
                        message: 'There are AR invoices that are not yet posted to the GL.',
                        path: [],
                        severity: 2,
                    },
                ]);

                isintacctActivationServiceOptionEnabled = await context.isServiceOptionEnabled(
                    xtremStructure.serviceOptions.intacctActivationOption,
                );
                assert.equal(isintacctActivationServiceOptionEnabled, true);

                // Now, if we try to set the paymentTrackingOption service option on, it should not be possible
                await assert.isRejected(
                    context.serviceOptionManager.setServiceOptionActive(
                        context,
                        xtremFinanceData.serviceOptions.paymentTrackingOption,
                        true,
                    ),
                    'The record was not updated.',
                );
                isPaymentTrackingServiceOptionEnabled = await context.isServiceOptionEnabled(
                    xtremFinanceData.serviceOptions.paymentTrackingOption,
                );
                assert.equal(isPaymentTrackingServiceOptionEnabled, false);
                assert.deepEqual(context.diagnoses, [
                    {
                        severity: 4,
                        path: ['isActive'],
                        message: 'Payment tracking is not possible if intacct integration is active.',
                    },
                ]);

                // Now we'll set the intacct connection to off. That should also disable the intacctActivationOption
                // and then we should be able to activate the payment tracking service option
                await intacctConnection.$.set({ isActive: false });
                await intacctConnection.$.save();

                isintacctActivationServiceOptionEnabled = await context.isServiceOptionEnabled(
                    xtremStructure.serviceOptions.intacctActivationOption,
                );
                assert.equal(isintacctActivationServiceOptionEnabled, false);

                await context.serviceOptionManager.setServiceOptionActive(
                    context,
                    xtremFinanceData.serviceOptions.paymentTrackingOption,
                    true,
                );

                isPaymentTrackingServiceOptionEnabled = await context.isServiceOptionEnabled(
                    xtremFinanceData.serviceOptions.paymentTrackingOption,
                );
                assert.equal(isPaymentTrackingServiceOptionEnabled, true);

                // Finally, since we have payment tracking active, we should not be able to activate the intacct integration
                intacctConnection = await context.read(
                    xtremIntacct.nodes.Intacct,
                    { id: 'XTREM' },
                    { forUpdate: true },
                );
                await intacctConnection.$.set({ isActive: true });
                await assert.isRejected(intacctConnection.$.save(), 'The record was not updated.');

                assert.equal(context.diagnoses.length, 1);

                const firstDiagnosis = context.diagnoses[0];
                assert.equal(firstDiagnosis.severity, 4);
                assert.deepEqual(firstDiagnosis.path, []);
                assert.match(
                    firstDiagnosis.message,
                    /While saving SysServiceOptionState\([\d]+\): Intacct activation is not possible if payment tracking is active./,
                );
            },

            {
                scenario: 'intacct-activation-option',
                directory: __dirname,
            },
        ));
});
