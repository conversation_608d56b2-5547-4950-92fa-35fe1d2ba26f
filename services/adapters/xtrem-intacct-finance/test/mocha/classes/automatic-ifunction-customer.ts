import { Test } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as XMLBuilder from 'xmlbuilder';
import * as xtremIntacctFinance from '../../../lib';
import { IntacctSynchronizationManager } from '../../../lib/classes';
import { isIntacctSynchronizationManager } from '../../fixtures/customer';

describe('IntacctSynchronizationManager - CUSTOMER', () => {
    it('CreateSyncState For US017 customer', () =>
        Test.withContext(async context => {
            const casino = await context.read(xtremMasterData.nodes.Customer, { _id: '#US017' });

            const intacctNodeCasinoNull = await casino.getSyncStateReference();

            assert.isNull(intacctNodeCasinoNull);

            const sandbox = sinon.createSandbox();
            sandbox
                .stub(xtremIntacctFinance.nodes.IntacctSynchronizationState.prototype, 'checkExist')
                .callsFake(async (): Promise<xtremIntacct.interfaces.IntacctKey> => {
                    const returnValue = await Promise.resolve({ RECORDNO: 92, NAME: 'US017' });
                    return returnValue;
                });

            const syncManager = await IntacctSynchronizationManager.createSyncState(context, {
                _id: 0,
                node: 'Customer',
                state: 'not',
                sysId: casino._id.toString(),
                intacctId: '',
            });

            sandbox.reset();
            sandbox.restore();

            assert.isTrue(isIntacctSynchronizationManager(syncManager));
        }));

    it('Automatic I function customer', () =>
        Test.withContext(
            async context => {
                let casino = await context.read(xtremMasterData.nodes.Customer, { _id: '#US017' });

                const intacctNodeCasinoNull = await casino.getSyncStateReference();

                assert.isNull(intacctNodeCasinoNull);

                const syncManager = await IntacctSynchronizationManager.createSyncState(context, {
                    _id: 0,
                    node: 'Customer',
                    state: 'not',
                    sysId: casino._id.toString(),
                    intacctId: '',
                });

                casino = await context.read(xtremMasterData.nodes.Customer, { _id: '#US017' });

                const intacctNodeCasino = await casino.getSyncStateReference();

                if (!intacctNodeCasino) {
                    assert.fail('getIntacctNode ');
                    return;
                }

                const shipto = await casino.primaryShipToAddress;

                const result = await syncManager.synchronize();

                assert.deepEqual(result, {
                    _id: intacctNodeCasino._id,
                    state: 'success',
                    intacctId: 'US017',
                    diagnoses: [],
                    sysId: (await intacctNodeCasino.parent)._id.toString(),
                });

                const autoFunctionItemChemA = await xtremIntacctFinance.classes.intacct.GenericIfunction.create(
                    context,
                    intacctNodeCasino,
                );

                const jsonItemObject = await autoFunctionItemChemA.xmlWithoutFunctionAsJson({});

                assert.deepEqual((await intacctNodeCasino.checkExist()) as any, {
                    RECORDNO: '6201', // recordno must be an integer, intacct sdk return it as a string
                    NAME: 'Siège social S01 PARIS',
                });

                const casinoAddresses = (await casino.businessEntity).addresses;
                assert.deepEqual(await casinoAddresses.length, 3);

                const address0 = await casinoAddresses.elementAt(0); // 29
                const address1 = await casinoAddresses.elementAt(1); // 30
                const address2 = await casinoAddresses.elementAt(2); // 31

                assert.equal(await address0.name, '960 street 44');
                assert.equal(await address1.name, 'Géant CASINO Seynod');
                assert.equal(await address2.name, 'First company');

                const customerCasino = {
                    CUSTOMERID: 'US017',
                    NAME: 'Siège social S01 PARIS',
                    TERMNAME: 'Net 45',
                    TAXID: 'FR58483849894',
                    STATUS: 'active',
                    HIDEDISPLAYCONTACT: 'true',
                    CURRENCY: 'EUR',
                    CONTACT_LIST_INFO: [
                        {
                            CATEGORYNAME: '960 street 44',
                            contact: { name: `Siège social S01 PARIS (${address0._id})` },
                        },
                        {
                            CATEGORYNAME: 'Géant CASINO Seynod',
                            contact: { name: `Siège social S01 PARIS (${address1._id})` },
                        },
                        {
                            CATEGORYNAME: 'First company',
                            contact: { name: `Siège social S01 PARIS (${address2._id})` },
                        },
                    ],
                    DISPLAYCONTACT: {
                        EMAIL1: '<EMAIL>',
                        FIRSTNAME: 'Georges',
                        LASTNAME: 'Abitbol',
                        PREFIX: 'Mr',
                        PHONE1: '+27825554321',
                        PRINTAS: 'Georges Abitbol Siège social S01 PARIS',
                        MAILADDRESS: {
                            ADDRESS1: '960 street 44',
                            STATE: 'First region',
                            ZIP: '20746',
                            COUNTRYCODE: 'FR',
                        },
                    },
                    CONTACTINFO: { CONTACTNAME: `Siège social S01 PARIS (${address0._id})` },
                    SHIPTO: { CONTACTNAME: `Siège social S01 PARIS (${address1._id})` },
                    BILLTO: { CONTACTNAME: `Siège social S01 PARIS (${address0._id})` },
                };
                const shipToSysId = shipto?._id;

                const contact = await context.read(xtremMasterData.nodes.BusinessEntityAddress, { _id: shipToSysId });

                assert.equal(
                    await (
                        await contact.intacctBusinessEntityAddress
                    )?.intacctId,
                    `Siège social S01 PARIS (${shipToSysId})`,
                );

                assert.deepEqual(jsonItemObject, customerCasino, JSON.stringify(jsonItemObject, null, 4));

                const compare = await syncManager.eventDesynchronized();
                // because _id of addresses changes & contact is there
                assert.deepEqual(compare.length, 0);
            },
            {
                scenario: 'customer-intacct',
                directory: __dirname,
                testAttributes: { isCreationMode: true },
            },
        ));
    it('Automatic I function customer - localized text - country.name  ', () =>
        Test.withContext(async context => {
            const cust01 = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' });

            await context.withLocalizedTextAsJson(async () => {
                const countryName = await (await (await cust01.businessEntity).country).name;
                const coutryNamejsonValue = JSON.parse(countryName);

                assert.deepEqual(
                    coutryNamejsonValue,
                    { en: 'United States of America', 'en-US': 'United States of America', 'fr-FR': 'Etats-Unis' },
                    countryName,
                );
                const intacctCustomer = await cust01.intacctCustomer;

                if (!intacctCustomer) {
                    assert.fail('No intacct customer US019');
                    return;
                }

                const intacctRequestCust01 = await xtremIntacctFinance.classes.intacct.GenericIfunction.create(
                    context,
                    intacctCustomer,
                );

                const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

                await intacctRequestCust01.writeField(xml, {
                    DATATYPE: 'DISPLAYCONTACT',
                    ID: 'COUNTRY',
                    DESCRIPTION: 'COUNTRY',
                    READONLY: true,
                    CREATEONLY: false,
                    ISCUSTOM: false,
                    REQUIRED: false,
                    LABEL: 'COUNTRY',
                    xtremProperty: 'businessEntity.country.name',
                });

                assert.deepEqual(
                    xml.flush(),
                    '<?xml version="1.0"?><request><COUNTRY>United States of America</COUNTRY></request>',
                    xml.flush(),
                );
            });
        }));
});
