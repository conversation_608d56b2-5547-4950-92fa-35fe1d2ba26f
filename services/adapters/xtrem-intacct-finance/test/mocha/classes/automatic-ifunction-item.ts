import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';

describe('Generic Classes For Intacct - Compare item ', () => {
    it(' Automatic I function item', () =>
        Test.withContext(
            async context => {
                const nodeData = await context.read(
                    xtremMasterData.nodes.Item,
                    { id: 'Chemical A' },
                    { forUpdate: true },
                );
                await nodeData.$.set({ intacctItem: {} });
                await nodeData.$.save();
                const intacctItem = await nodeData.intacctItem;

                if (!intacctItem) {
                    assert.fail('No intacct item');
                }

                const syncManager = await xtremIntacctFinance.classes.IntacctSynchronizationManager.create(
                    context,
                    intacctItem,
                );
                const intacctRequest = await syncManager.getIntacctRequest('create');

                const jsonItemObject = await intacctRequest.xmlWithoutFunctionAsJson({});

                assert.deepEqual((await intacctItem.checkExist()) as any, {
                    RECORDNO: '390',
                    NAME: 'Chemical description A',
                });

                const itemChemiCalA = {
                    ITEMID: 'Chemical A',
                    STATUS: 'active',
                    NAME: 'Chemical description A',
                    ITEMTYPE: 'Non-Inventory',
                    EXTENDED_DESCRIPTION: 'Chemical contains xyz',
                };

                assert.deepEqual(await intacctItem.type, 'Non-Inventory');

                assert.deepEqual(jsonItemObject, itemChemiCalA, JSON.stringify(jsonItemObject, null, 4));

                const compare = await syncManager.eventDesynchronized();

                assert.deepEqual(compare.length, 1);

                assert.isTrue(compare.some(cmp => cmp.name === 'ITEMTYPE / intacctItem.type'));
            },
            {
                scenario: 'item-intacct',
                directory: __dirname,
                testAttributes: { isCreationMode: false },
            },
        ));
});
