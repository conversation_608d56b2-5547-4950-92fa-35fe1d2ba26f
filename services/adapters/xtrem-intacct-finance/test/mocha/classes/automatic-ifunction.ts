import type * as xtremCommunication from '@sage/xtrem-communication';
import { Test } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import * as sinon from 'sinon';
import * as XMLBuilder from 'xmlbuilder';
import * as xtremIntacctFinance from '../../../lib';
import type { SyncState } from '../../../lib/interfaces/synchronization';
import { getJournalEntryQueryClass, journalEntry } from '../../fixtures/synchronization';

let sandbox = sinon.createSandbox();
const dirname = __dirname.replace('/build/', '/');

describe('Generic Classes For Intacct  ', () => {
    before(() => {
        sandbox = sinon.createSandbox();
    });
    after(() => {
        sandbox.restore();
    });

    it('IGenericFunction for supplier', () =>
        Test.withContext(async context => {
            const leclercSupplier = await context.read(xtremMasterData.nodes.Supplier, { _id: '#LECLERC' });

            const intacctSupplier = await leclercSupplier.intacctSupplier;

            sandbox
                .stub(xtremIntacctFinance.nodes.IntacctSynchronizationState.prototype, 'checkExist')
                .callsFake(async (): Promise<xtremIntacct.interfaces.IntacctKey> => {
                    const returnValue = await Promise.resolve({ RECORDNO: 92, NAME: 'LECLERC supermarket' });
                    return returnValue;
                });
            sandbox
                .stub(xtremIntacctFinance.classes.intacct.GenericIfunction.prototype, 'manageDependencie')
                .callsFake(async (): Promise<Partial<SyncState>> => {
                    const returnValue = await Promise.resolve({
                        intacctId: 'LECLERC supermarket (12)',
                        state: 'success' as xtremCommunication.enums.IntegrationState,
                    });
                    return returnValue;
                });

            if (!intacctSupplier) {
                assert.fail('No intacct supplier for LECLERC');
            }

            const myClass = await xtremIntacctFinance.classes.IntacctSynchronizationManager.create(
                context,
                intacctSupplier,
            );

            const primaryContact = await leclercSupplier.primaryContact;
            if (!primaryContact) {
                assert.fail('No primary contact for LECLERC');
            }
            assert.equal(await primaryContact.firstName, 'John');

            const primaryAddress = await leclercSupplier.primaryAddress;
            if (!primaryAddress) {
                assert.fail('No primary address for LECLERC');
            }
            assert.equal(await primaryAddress.addressLine1, '1 First avenue');

            const data = await (await myClass.getIntacctRequest('create')).xmlWithoutFunctionAsJson({});

            assert.deepEqual(
                data,
                {
                    VENDORID: 'LECLERC',
                    NAME: 'LECLERC supermarket',
                    TERMNAME: 'Net 30',
                    TAXID: 'FR58328666809',
                    STATUS: 'active',
                    HIDEDISPLAYCONTACT: 'true',
                    CURRENCY: 'EUR',
                    DISPLAYCONTACT: {
                        EMAIL1: '<EMAIL>',
                        PREFIX: 'Mr',
                        PRINTAS: 'John Doe LECLERC supermarket',
                        FIRSTNAME: 'John',
                        LASTNAME: 'Doe',
                        PHONE1: '************',
                        MAILADDRESS: {
                            ADDRESS1: '1 First avenue',
                            STATE: 'First region',
                            ZIP: '0100',
                            COUNTRYCODE: 'FR',
                        },
                    },
                    CONTACTINFO: { CONTACTNAME: 'LECLERC supermarket (12)' },
                    CONTACT_LIST_INFO: [
                        {
                            CATEGORYNAME: 'Store',
                            contact: {
                                name: 'LECLERC supermarket (12)',
                            },
                        },
                        {
                            CATEGORYNAME: 'E.LECLERC Cran Gevrier',
                            contact: {
                                name: 'LECLERC supermarket (12)',
                            },
                        },
                    ],
                    PAYTO: { CONTACTNAME: 'LECLERC supermarket (12)' },
                    RETURNTO: { CONTACTNAME: 'LECLERC supermarket (12)' },
                },
                JSON.stringify(data, null, 4),
            );
            sandbox.restore();
            sandbox.reset();
        }));

    it('Create Generic Class, Location - getNodeFromTableName ', () =>
        Test.withContext(async context => {
            const test = context.introspection.getNodeFromTableName('location');

            assert.deepEqual(test?.name, 'Location');

            const nodeData = await context.tryRead(test!, { id: 'LOC1', locationZone: '#US001|Loading dock' } as any);

            const nodeDataClean = await context.tryRead(xtremMasterData.nodes.Location!, {
                id: 'LOC1',
                locationZone: '#US001|Loading dock',
            });

            assert.deepEqual(await nodeData?.$.payload(), await nodeDataClean?.$.payload());
        }));

    it('AutomaticIFunction - updateIntegrationState  ', () =>
        Test.withContext(async context => {
            sandbox
                .stub(xtremIntacctFinance.nodes.IntacctSynchronizationFinanceState.prototype, 'checkExist')
                // eslint-disable-next-line require-await
                .callsFake(async () => Promise.resolve(undefined));

            const autoFunctionJournalEntry = await getJournalEntryQueryClass(context);
            const jsonRequest = await autoFunctionJournalEntry.xmlWithoutFunctionAsJson({ writeNull: true });

            sandbox.reset();
            sandbox.restore();

            assert.deepEqual(jsonRequest, journalEntry, JSON.stringify(jsonRequest || {}, null, 4));

            const nodeData = await context.read(xtremFinance.nodes.JournalEntry, { number: 'IJ-2021040123' });

            assert.deepEqual(await (await nodeData?.intacctDocument)?.state, 'not');
        }));

    it('AutomaticIFunction - objectXmlWrite  ', () =>
        Test.withContext(
            async context => {
                const autoFunctionJournalEntry = await getJournalEntryQueryClass(context);

                const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

                await autoFunctionJournalEntry.objectXmlWrite(xml, {
                    test: 'myTest',
                    undefinedValue: undefined,
                    nullValue: null,
                    emptyString: '',
                    number: 1,
                    array: [{ test: 'firstLine' }, { test: 'secondLine' }],
                });

                assert.equal(
                    xml.flush(false),
                    '<?xml version="1.0"?><request><test>myTest</test><nullValue/><emptyString/><number>1</number><array><test>firstLine</test><test>secondLine</test></array></request>',
                    xml.flush(false),
                );
            },
            {
                scenario: 'test-exist-journal-entry',
                directory: __dirname,
            },
        ));

    it('Journal Entry auto sync ', () =>
        Test.withContext(
            async context => {
                const autoFunctionJournalEntry = await getJournalEntryQueryClass(context);

                const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

                await autoFunctionJournalEntry.writeXml(xml);

                const xmlTest = path.resolve(dirname, 'xml-requests/journal-entry-create.xml');
                const result = fs.readFileSync(xmlTest);

                assert.match(
                    (await autoFunctionJournalEntry.xmlWithoutFunction(true)).replace(/\s/g, ''),
                    new RegExp(result.toString().replace(/\s/g, ''), 'gmiu'),
                    await autoFunctionJournalEntry.xmlWithoutFunction(true),
                );
            },
            {
                scenario: 'test-exist-journal-entry',
                directory: __dirname,
            },
        ));
    it(' Journal Entry get lines ', () =>
        Test.withContext(
            async context => {
                const autoFunctionJournalEntry = await getJournalEntryQueryClass(context);

                const xml = new xtremIntacct.classes.sdk.IaXmlWriter(XMLBuilder.create('request', {}));

                await autoFunctionJournalEntry.mapNode.lines.forEach(line =>
                    autoFunctionJournalEntry.writeXMLLines(xml, line, true),
                );

                const xmlTest = path.resolve(dirname, 'xml-requests/journal-entry-lines.xml');
                const result = fs.readFileSync(xmlTest);

                const xmlResult = xtremIntacct.functions.withoutFunction(xml.flush(true));

                assert.match(
                    xmlResult.replace(/\s/g, ''),
                    new RegExp(result.toString().replace(/\s/g, ''), 'gmiu'),
                    xml.flush(true),
                );
            },
            {
                scenario: 'test-exist-journal-entry',
                directory: __dirname,
            },
        ));
});
