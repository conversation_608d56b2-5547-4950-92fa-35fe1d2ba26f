import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as sinon from 'sinon';

describe(' Intacct synchronization manager', () => {
    it(' Item synchronization manager  ', () =>
        Test.withContext(async context => {
            const notifyStub = sinon.stub(context, 'notify');

            const itemChemiCalA = await context.read(
                xtremMasterData.nodes.Item,
                { id: 'Chemical A' },
                { forUpdate: true },
            );
            await itemChemiCalA.$.set({ name: 'My chemical A' });
            await itemChemiCalA.$.save();

            sinon.assert.calledOnceWithExactly(
                notifyStub,
                'IntacctListener/synchronizeNode/start',
                {
                    intacctNode: (await itemChemiCalA.getSyncStateReference())?._id,
                },
                { replyTopic: 'SysNotificationState/updateStatus' },
            );
        }));
});
