import { Test } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import * as sinon from 'sinon';
import * as xtremIntacctFinance from '../../../lib';

let sandbox = sinon.createSandbox();

const dirname = __dirname.replace('/build/', '/');

describe('GenericIfunction', () => {
    beforeEach(() => {
        sandbox = sinon.createSandbox();
    });
    afterEach(() => {
        sandbox.restore();
    });
    it('Create Generic Class Vendor update', () =>
        Test.withContext(async context => {
            const supplier = await context.read(xtremMasterData.nodes.Supplier, { _id: '#700' }, { forUpdate: true });
            await supplier.$.save();

            const intacctSupplier = await supplier.intacctSupplier;
            if (!intacctSupplier) {
                assert.fail('intacctSupplier not found');
            }

            sandbox
                .stub(xtremIntacctFinance.nodes.IntacctSynchronizationState.prototype, 'checkExist')
                .callsFake(async (): Promise<xtremIntacct.interfaces.IntacctKey> => {
                    const returnValue = await Promise.resolve({ RECORDNO: 1, NAME: 'ZA 700' });
                    return returnValue;
                });

            const businessentityAddress2 = await (await supplier.businessEntity).addresses.elementAt(0);

            const businessentityAddress1 = await (await supplier.businessEntity).addresses.elementAt(1);

            // Will call dependencies but as address are success will not do intacct call
            await (
                await context.create(xtremIntacctFinance.nodes.IntacctContact, {
                    contact: businessentityAddress1._id,
                    integration: '#intacct',
                    intacctId: 'ZA 700 (66)',
                    node: '#BusinessEntityAddress',
                    state: 'success',
                    sysId: businessentityAddress1._id.toString(),
                })
            ).$.save();

            await (
                await context.create(xtremIntacctFinance.nodes.IntacctContact, {
                    contact: businessentityAddress2._id,
                    integration: '#intacct',
                    intacctId: 'ZA 700 (11)',
                    node: '#BusinessEntityAddress',
                    state: 'success',
                    sysId: businessentityAddress2._id.toString(),
                })
            ).$.save();

            const genericFunc = await xtremIntacctFinance.classes.intacct.GenericIfunction.create(
                context,
                intacctSupplier,
            );
            // TODO : why do we have paymentMethodKey empty there ?
            const xml = await genericFunc.xmlWithoutFunction(true);

            sandbox.restore();
            sandbox.reset();

            const vendorAutomaticXmlPath = path.resolve(dirname, 'xml-requests/vendor-automatic.xml');
            const result = fs.readFileSync(vendorAutomaticXmlPath).toString();

            assert.equal(xml, result, xml);
        }));
    it('Delete Vendor/Supplier class ', () =>
        Test.withContext(async context => {
            const deleteObject = new xtremIntacct.classes.sdk.Functions.Delete(context, {
                objectName: 'VENDOR',
                recordNo: '92',
            });

            const xmlString = await deleteObject.xmlWithoutFunction(false);

            const result = '<delete><object>VENDOR</object><keys>92</keys></delete>';
            assert.equal(xmlString, result);
        }));
});
