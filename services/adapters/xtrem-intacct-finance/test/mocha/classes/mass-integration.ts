import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';

describe('Mass integration class', () => {
    it('Item Mass integration', () =>
        Test.withContext(async context => {
            const massIntegration = await xtremIntacctFinance.classes.MassIntegration.create(context, 'ITEM');
            const sysIds = await massIntegration.getAllSysIdsToIntegrate({});
            assert.isArray(sysIds);

            assert.equal(sysIds.length, 179);

            assert.equal(await massIntegration.parameters.nodeFactory.name, 'Item');

            const tenItems = context.query(xtremMasterData.nodes.Item, {
                filter: {
                    id: { _in: ['17890', '17890-B', '17891', '17892', '17893', '7628', '7890', '8553'] },
                },
                forUpdate: true,
            });

            const filter = await massIntegration.getFilter();

            assert.deepEqual(filter, { integration: '#intacct', node: '#Item' });

            await tenItems.forEach(async item => {
                await item.$.set({ intacctItem: { sysId: item._id.toString() } });
                await item.$.save();
            });

            const allSysId = await massIntegration.getAllSynchronizationStateId();

            assert.equal(allSysId.length, 25);
        }));
});
