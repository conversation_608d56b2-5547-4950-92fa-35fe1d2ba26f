import type { NodeCreateData } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import { createOrUpdateFromIntacctCall } from '../../../lib/functions';

describe('Map lib functions ', () => {
    it('createOrUpdateFromIntacct', () =>
        Test.withContext(async context => {
            const payload: NodeCreateData<xtremFinanceData.nodes.Account> = {
                intacctId: 'forUnitTest',
                id: '1000',
                chartOfAccount: '#TEST_PT_DEFAULT',
                isActive: true,
                name: 'PT Account 1000',
                isDirectEntryForbidden: false,
                isControl: false,
                taxManagement: 'tax',
                attributeTypes: [{ attributeType: '#financialSite', isRequired: true }],
                dimensionTypes: [{ dimensionType: '#dimensionType03', isRequired: true }],
            };

            const { isCreated, isError, isUpdated, diagnoses } = await createOrUpdateFromIntacctCall(context, {
                instanceSysId: -1,
                // For testing because Account isn't an IntacctNode
                xtremNode: xtremFinanceData.nodes.Account as any,
                xtremPayload: { ...payload },
            });

            assert.isTrue(isError);
            assert.isFalse(isCreated);
            assert.isFalse(isUpdated);
            assert.deepEqual(diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message:
                        "The record was not created.\nWhen tax information is excluded from journal entries, you need to set tax management to 'Other.'",
                },
                {
                    severity: 3,
                    path: ['createOrUpdateFromIntacct', 'updateXtrem'],
                    message: 'Sage Intacct ID __forUnitTest__ not saved.',
                },
            ]);
        }));
});
