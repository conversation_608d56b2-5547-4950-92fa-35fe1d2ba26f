import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';

describe('CSV check ', () => {
    it('Check intacct item test csv', () =>
        Test.withContext(async context => {
            const allIntacctItems = context.query(xtremIntacctFinance.nodes.IntacctItem, {});

            const itemIssues = await allIntacctItems.reduce(async (issues: string[], intacctItem) => {
                if (+(await intacctItem.sysId) !== (await intacctItem.item)._id) {
                    issues.push(`sysId: ${await intacctItem.sysId} mustBe ${(await intacctItem.item)._id}`);
                }
                return issues;
            }, []);

            if (itemIssues.length > 0) {
                assert.fail(itemIssues.join('\n'));
            }
        }));
    it('Check intacct customer test csv', () =>
        Test.withContext(async context => {
            const allIntacctCustomers = context.query(xtremIntacctFinance.nodes.IntacctCustomer, {});

            const customersIssues = await allIntacctCustomers.reduce(async (issues: string[], intacctCustomer) => {
                if (+(await intacctCustomer.sysId) !== (await intacctCustomer.parent)._id) {
                    issues.push(`sysId: ${await intacctCustomer.sysId} must be ${(await intacctCustomer.parent)._id}`);
                }
                return issues;
            }, []);

            if (customersIssues.length > 0) {
                assert.fail(customersIssues.join('\n'));
            }
        }));
    it('Check intacct Supplier test csv', () =>
        Test.withContext(async context => {
            const allIntacctSuppliers = context.query(xtremIntacctFinance.nodes.IntacctSupplier, {});

            const suppliersIssues = await allIntacctSuppliers.reduce(async (issues: string[], intacctSupplier) => {
                if (+(await intacctSupplier.sysId) !== (await intacctSupplier.parent)._id) {
                    issues.push(`sysId: ${await intacctSupplier.sysId} mustBe ${(await intacctSupplier.parent)._id}`);
                }
                return issues;
            }, []);

            if (suppliersIssues.length > 0) {
                assert.fail(suppliersIssues.join('\n'));
            }
        }));

    it('Check intacct Contact test csv', () =>
        Test.withContext(async context => {
            const allIntacctContacts = context.query(xtremIntacctFinance.nodes.IntacctContact, {});

            const contactsIssues = await allIntacctContacts.reduce(async (issues: string[], intacctContact: any) => {
                if (+(await intacctContact.sysId) !== (await intacctContact.contact)._id) {
                    issues.push(`sysId: ${await intacctContact.sysId} mustBe ${(await intacctContact.contact)._id}`);
                }
                return issues;
            }, []);

            if (contactsIssues.length > 0) {
                assert.fail(contactsIssues.join('\n'));
            }
        }));
});
