// XT-80680 commented during bank account refactor since cash book management will be removed
// interface MatchingRuleCreator {
//     bankAccountId?: string;
//     account: { id: string; chartOfAccount: string };
//     keyword: string;
//     type: xtremIntacctFinance.enums.IntacctMatchingType;
//     priority?: number;
// }

// XT-80680 commented during bank account refactor since cash book management will be removed
// interface Feed {
//     /** defaulted to 700_CHK */ bankAccountId?: string;
//     description: string;
//     accountWanted: { id: string; chartOfAccount: string };
// }

// XT-80680 commented during bank account refactor since cash book management will be removed
// interface MatchingRuleParameters {
//     rules: MatchingRuleCreator[];
//     feeds: Feed[];
// }

// function matchRulesCreator(context: Context, payloadArray: MatchingRuleCreator[]) {
//     return asyncArray(payloadArray)
//         .map(async rule => {
//             const bankAccount = rule.bankAccountId
//                 ? await context.tryRead(xtremFinanceData.nodes.BankAccount, { id: rule.bankAccountId })
//                 : undefined;
//             const account = await context.read(xtremFinanceData.nodes.Account, {
//                 id: rule.account.id,
//                 chartOfAccount: rule.account.chartOfAccount,
//             });

//             return context.create(
//                 xtremIntacctFinance.nodes.IntacctBankAccountMatching,
//                 { bankAccount, account, keyword: rule.keyword, type: rule.type, priority: rule.priority || 0 },
//                 { isTransient: true },
//             );
//         })
//         .toArray();
// }

// XT-80680 commented during bank account refactor since cash book management will be removed
// async function matchingRuleFeedTester(
//     context: Context,
//     feeds: Feed[],
//     matchingRules: xtremIntacctFinance.nodes.IntacctBankAccountMatching[],
// ) {
//     const bankAccountDefault = await context.read(xtremFinanceData.nodes.BankAccount, { id: '700_CHK' });

//     await asyncArray(feeds).forEach(async feed => {
//         const bankAccount = feed.bankAccountId
//             ? await context.tryRead(xtremFinanceData.nodes.BankAccount, { id: feed.bankAccountId })
//             : bankAccountDefault;

//         const account = await context.read(xtremFinanceData.nodes.Account, {
//             id: feed.accountWanted.id,
//             chartOfAccount: feed.accountWanted.chartOfAccount,
//         });

//         const feedToMatch = {
//             bankAccount,
//             transactionType: 'deposit',
//             amount: 100,
//             description: feed.description,
//         } as NodeCreateData<xtremIntacctFinance.nodes.IntacctBankAccountTransactionFeed>;

//         const updatePayload = await xtremIntacctFinance.functions.transactionFeed.getMatchLineProcess(
//             context,
//             feedToMatch,
//             matchingRules,
//             Logger.getLogger(__filename, 'bank-manager'),
//         );
//         const line = updatePayload?.lines?.shift();

//         assert.equal(line?.status, 'draftMatch');
//         assert.equal(
//             (line?.account as NodeCreateData<xtremFinanceData.nodes.Account>)._id,
//             account._id,
//             `actual : ${JSON.stringify(line?.account)} expected : ${await account.composedDescription}`,
//         );
//     });
// }

// async function matchingRuleTester(context: Context, parameters: MatchingRuleParameters) {
//     const matchRules = await matchRulesCreator(context, parameters.rules);
//     await matchingRuleFeedTester(context, parameters.feeds, matchRules);
// }

describe(' Matching rules tester  ', () => {
    before(() => {});

    // it(' getMatchLineProcess  ', () =>
    //     Test.withContext(async context => {
    //         const accountFrDefault = { id: '99999', chartOfAccount: '#TEST_US_DEFAULT_INACTIVE' };
    //         await matchingRuleTester(context, {
    //             rules: [{ keyword: 'toto', type: 'regularExpression', account: accountFrDefault }],
    //             feeds: [{ description: 'toto', accountWanted: accountFrDefault }],
    //         });

    //         await matchingRuleTester(context, {
    //             rules: [{ keyword: 'toto', type: 'contains', account: accountFrDefault }],
    //             feeds: [{ description: 'toto', accountWanted: accountFrDefault }],
    //         });

    //         await matchingRuleTester(context, {
    //             rules: [{ keyword: 'toto', type: 'equals', account: accountFrDefault }],
    //             feeds: [{ description: 'toto', accountWanted: accountFrDefault }],
    //         });

    //         await matchingRuleTester(context, {
    //             rules: [{ keyword: 'Charity', type: 'regularExpression', account: accountFrDefault }],
    //             feeds: [{ description: 'Charity19', accountWanted: accountFrDefault }],
    //         });
    //     }));

    // it(' Multiple rules - getMatchLineProcess  ', () =>
    //     Test.withContext(async context => {
    //         const accountFrDefault = { id: '99999', chartOfAccount: '#TEST_US_DEFAULT_INACTIVE' };
    //         const accountUsDefault1 = { id: '355000', chartOfAccount: '#TEST_US_DEFAULT' };
    //         const accountUsDefault2 = { id: '7', chartOfAccount: '#TEST_US_DEFAULT' };
    //         await matchingRuleTester(context, {
    //             rules: [
    //                 { bankAccountId: 'BAN', keyword: 'toto', type: 'contains', account: accountUsDefault1 },
    //                 { bankAccountId: '700_CHK', keyword: 'toto', type: 'contains', account: accountUsDefault2 },
    //                 { keyword: 'toto', type: 'contains', account: accountFrDefault },
    //             ],
    //             feeds: [{ bankAccountId: 'BAN', description: 'toto', accountWanted: accountUsDefault1 }],
    //         });
    //     }));

    // it('getMatchingRules ', () =>
    //     Test.withContext(
    //         async context => {
    //             const bankAccount = await context.read(xtremFinanceData.nodes.BankAccount, { id: '700_CHK' });
    //             const matchingRules = await getMatchingRules(context, bankAccount, {});

    //             assert.equal(matchingRules.length, 7);

    //             const firstRule = matchingRules.shift();

    //             assert.equal(await firstRule?.bankAccount, bankAccount);
    //         },
    //         {
    //             testActiveServiceOptions: [
    //                 xtremIntacct.serviceOptions.intacctOption,
    //                 xtremIntacctFinance.serviceOptions.intacctCashbookManagement,
    //             ],
    //         },
    //     ));

    // it('Apply unit test matchingRules ', () =>
    //     Test.withContext(
    //         async context => {
    //             const bankAccount700 = await context.read(xtremFinanceData.nodes.BankAccount, { id: '700_CHK' }); // ID 1

    //             const matchingRules = await getMatchingRules(context, bankAccount700, {});

    //             const accountUsDefault1 = { id: '12400', chartOfAccount: '#US_DEFAULT' };
    //             const accountUsDefault2 = { id: '12100', chartOfAccount: '#US_DEFAULT' };

    //             await matchingRuleFeedTester(
    //                 context,
    //                 [
    //                     { bankAccountId: '700_CHK', description: 'Charity19', accountWanted: accountUsDefault1 },
    //                     { bankAccountId: '700_CHK', description: 'Charity', accountWanted: accountUsDefault2 },
    //                 ],
    //                 matchingRules,
    //             );
    //         },
    //         {
    //             testActiveServiceOptions: [
    //                 xtremIntacct.serviceOptions.intacctOption,
    //                 xtremIntacctFinance.serviceOptions.intacctCashbookManagement,
    //             ],
    //         },
    //     ));
});
