import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';

describe('Custom mapping functionnality', () => {
    it('Get custom fields', () => {
        const custom: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties[] = [
            {
                ID: 'XTREEM_ID',
                LABEL: 'ID Xtrem',
                DATATYPE: 'INTEGER',
                ISCUSTOM: true,
                READONLY: false,
                REQUIRED: false,
                DESCRIPTION: 'ID Xtrem',
                xtremProperty: '_id',
            },
            {
                ID: 'RECORDNO',
                LABEL: 'Recordno',
                DATATYPE: 'INTEGER',
                ISCUSTOM: true,
                READONLY: false,
                REQUIRED: false,
                DESCRIPTION: 'Record no',
                xtremProperty: 'recordno',
            },
        ];
        const defaultFields = [
            {
                ID: 'RECORDNO',
                LABEL: 'Recordno',
                DATATYPE: 'INTEGER',
                ISCUSTOM: true,
                READONLY: false,
                REQUIRED: false,
                DESCRIPTION: 'Record no',
                xtremProperty: 'recordno',
            },
            {
                ID: 'TOBEDELETED',
                LABEL: 'to be deleted',
                DATATYPE: 'INTEGER',
                ISCUSTOM: true,
                READONLY: false,
                REQUIRED: false,
                DESCRIPTION: 'Must be convert into DELETEDProperty',
                xtremProperty: 'recordno',
            },
        ];
        const customFields = xtremIntacctFinance.functions.mapping.extractCustomFields({
            custom,
            default: defaultFields,
            editableFields: ['TOBEDELETED'],
        });

        assert.deepStrictEqual(
            customFields,
            [
                {
                    ID: 'XTREEM_ID',
                    LABEL: 'ID Xtrem',
                    DATATYPE: 'INTEGER',
                    ISCUSTOM: true,
                    READONLY: false,
                    REQUIRED: false,
                    DESCRIPTION: 'ID Xtrem',
                    xtremProperty: '_id',
                },
                {
                    ID: 'TOBEDELETED',
                    LABEL: 'to be deleted',
                    DATATYPE: 'INTEGER',
                    ISCUSTOM: true,
                    READONLY: false,
                    REQUIRED: false,
                    DESCRIPTION: 'Must be convert into DELETEDProperty',
                    xtremProperty: 'DELETEDProperty',
                },
            ],
            JSON.stringify(customFields, null, 4),
        );
    });
});
