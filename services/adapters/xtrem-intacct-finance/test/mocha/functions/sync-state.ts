import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import { supplierPayloadContinueOnError } from '../../../lib/functions/synchronization';

describe('Sync State Functions ', () => {
    it(' supplierPayloadContinueOnError ', () =>
        Test.withContext(async context => {
            const za500 = await context.read(xtremMasterData.nodes.Supplier, { _id: '#500' });
            const za700 = await context.read(xtremMasterData.nodes.Supplier, { _id: '#700' });

            const syncStateZa500 = await supplierPayloadContinueOnError(za500);
            const syncStateZa700 = await supplierPayloadContinueOnError(za700);

            assert.equal(syncStateZa500?.intacctId, '500');
            assert.equal(syncStateZa700?.intacctId, '');
        }));
});
