import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../index';
import {
    contacctMappingBase,
    contactMappingWithoutChanges,
    customContacctMapping,
} from '../../fixtures/contact-mapping';

describe('Mapping rules', () => {
    it('Contact extract custom mapping', () =>
        Test.withContext(async context => {
            const contacctMapping = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|CONTACT|BusinessEntityAddress',
            });
            assert.deepEqual(await contacctMapping.editableFields, ['PREFIX', 'PRINTAS', 'PHONE2']);
            assert.deepEqual(
                await contacctMapping.intacctFields,
                [
                    'RECORDNO',
                    'CONTACTNAME',
                    'PREFIX',
                    'FIRSTNAME',
                    'LASTNAME',
                    'PRINTAS',
                    'PHONE1',
                    'PHONE2',
                    'EMAIL1',
                    'STATUS',
                    'MAILADDRESS.ADDRESS1',
                    'MAILADDRESS.ADDRESS2',
                    'MAILADDRESS.CITY',
                    'MAILADDRESS.STATE',
                    'MAILADDRESS.ZIP',
                    'MAILADDRESS.COUNTRYCODE',
                ],
                (await contacctMapping.intacctFields).join('\n'),
            );

            const relationMapping = await contacctMapping.relationMapping;

            assert.deepEqual(relationMapping, contactMappingWithoutChanges, JSON.stringify(relationMapping));

            const mappingToSave = { ...contacctMappingBase };

            const companyNameField = mappingToSave.fields.splice(
                mappingToSave.fields.findIndex(field => field.ID === 'COMPANYNAME'),
                1,
            );
            assert.equal(companyNameField.length, 1);
            assert.equal(companyNameField[0].ID, 'COMPANYNAME');

            /** This one must be reset  */
            const fieldChangedPhone1 = mappingToSave.fields.find(field => field.ID === 'PHONE1');
            const fieldChangedPhone2 = mappingToSave.fields.find(field => field.ID === 'PHONE2');

            /** Deleting DESCRIPTION field  */
            mappingToSave.fields = mappingToSave.fields.filter(field => !['PHONE2'].includes(field.ID));

            if (fieldChangedPhone2) {
                fieldChangedPhone2.xtremProperty = 'toto';
                mappingToSave.fields.push(fieldChangedPhone2);
            }

            if (fieldChangedPhone1) {
                fieldChangedPhone1.xtremProperty = 'toto';
                mappingToSave.fields.push(fieldChangedPhone1);
            }

            if (companyNameField && companyNameField.length) {
                companyNameField[0].xtremProperty = 'primaryContact.lastName';
                mappingToSave.fields.push(companyNameField[0]);
            }

            const customMapping = await xtremIntacctFinance.functions.mapping.mappingRules({
                map: contacctMapping,
                fullCustom: mappingToSave,
            });

            assert.deepEqual(customMapping, customContacctMapping, JSON.stringify(customMapping, null, 4));
        }));
});
