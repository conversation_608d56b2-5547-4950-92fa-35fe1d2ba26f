import * as sinon from 'sinon';

let sandbox = sinon.createSandbox();

describe('Transaction feed search', () => {
    before(() => {
        sandbox = sinon.createSandbox();
    });
    after(() => {
        sandbox.restore();
    });

    // XT-80680 commented during bank account refactor since cash book management will be removed
    // it('getOutStandingInvoices from intacct', () =>
    //     Test.withContext(
    //         async context => {
    //             const bankAccount = await context.read(xtremFinanceData.nodes.BankAccount, { id: 'BAN' });
    //             const currency = await context.read(xtremMasterData.nodes.Currency, { id: 'USD' });

    //             const filters = {
    //                 bankAccount: bankAccount._id.toString(),
    //                 currencyId: currency._id.toString(),
    //                 dateFrom: date.today(),
    //                 dateTo: date.today(),
    //                 status: '',
    //             } as xtremIntacctFinance.interfaces.BankAccountMatching.AccountMatchingQueryFilters;

    //             const intacctImportSession = await context.create(
    //                 xtremIntacctFinance.nodes.IntacctImportSession,
    //                 {
    //                     description: 'Transiant import session',
    //                     mapObject: 'intacct|BANKACCTTXNRECORD|IntacctBankAccountTransactionFeed',
    //                     queryParameters: filters,
    //                     bankAccount: filters.bankAccount,
    //                 },
    //                 { isTransient: true },
    //             );

    //             let isRequestSend = false;
    //             sandbox
    //                 .stub(xtremIntacct.classes.sdk.Client.prototype, 'executeOnlineRequest')
    //                 .callsFake(
    //                     async (
    //                         query: xtremIntacct.classes.sdk.Functions.Query<
    //                             xtremIntacctFinance.interfaces.BankAccountMatching.IntacctArInvoice[]
    //                         >,
    //                     ) => {
    //                         await Promise.resolve();
    //                         assert.isFalse(query.showPrivate);
    //                         assert.equal((await query.client).config?.entityId, 'US006');
    //                         isRequestSend = true;

    //                         return fixtures.emptyOnlineResponse;
    //                     },
    //                 );

    //             const arInvoices = await xtremIntacctFinance.functions.transactionFeedSearch.getOutStandingInvoices(
    //                 context,
    //                 intacctImportSession,
    //                 '1',
    //             );

    //             assert.isArray(arInvoices);
    //             assert.isTrue(isRequestSend);
    //         },
    //         {
    //             testActiveServiceOptions: [
    //                 xtremIntacct.serviceOptions.intacctOption,
    //                 xtremIntacctFinance.serviceOptions.intacctCashbookManagement,
    //             ],
    //         },
    //     ));
});
