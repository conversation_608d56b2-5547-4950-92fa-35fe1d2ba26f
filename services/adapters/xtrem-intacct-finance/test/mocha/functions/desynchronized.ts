import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';
import { setDesynchronizedXtrem } from '../../../lib/functions/synchronization';

describe('Desynchronised functions', () => {
    it('Item is desynchronized', () =>
        Test.withContext(
            async context => {
                const chair = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' }, { forUpdate: true });
                await chair.$.set({ name: 'My chair' });
                await chair.$.save({ flushDeferredActions: true });

                const intacctChair = await context.read(
                    xtremIntacctFinance.nodes.IntacctSynchronizationState,
                    {
                        integration: '#intacct',
                        sysId: chair._id.toString(),
                        node: '#Item',
                    },
                    { forUpdate: true },
                );
                await intacctChair.$.set({ intacctId: 'Chair' });
                await intacctChair.$.save();

                assert.equal(await intacctChair.state, 'not');

                const diagnose = await setDesynchronizedXtrem(context, { name: 'ITEM', id: 'Chair' });

                assert.isArray(diagnose);

                const itemChair = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });

                const difference = await (await itemChair.intacctItem)?.difference;

                assert.deepEqual(difference?.differences, [
                    {
                        name: 'NAME / name',
                        thirdPartyValue: 'Chair',
                        xtreemValue: 'My chair',
                    },
                    {
                        name: 'ITEMTYPE / intacctItem.type',
                        thirdPartyValue: 'Non-Inventory (Purchase only)',
                        xtreemValue: 'Non-Inventory',
                    },
                ]);

                assert.equal(await (await itemChair.intacctItem)?.state, 'desynchronized');
            },
            {
                scenario: 'item-desync',
                directory: __dirname,
                testAttributes: { isCreationMode: false },
            },
        ));
    it(' Contact is desynchronized   ', () =>
        Test.withContext(
            async context => {
                const diffs = await setDesynchronizedXtrem(context, {
                    name: 'CONTACT',
                    id: 'Vodacom South Africa (Pty) Somewhere (8)',
                });

                const diffsToCheck = [
                    { name: 'PREFIX / intacctBusinessEntityAddress.prefix', thirdPartyValue: 'Mr.', xtreemValue: 'Mr' },
                    {
                        name: 'PRINTAS / intacctBusinessEntityAddress.printAs',
                        thirdPartyValue: 'John DoeVodacom South Africa (Pty) Ltda',
                        xtreemValue: 'John Doe ZA 500',
                    },
                    {
                        name: 'EMAIL1 / primaryContact.email',
                        thirdPartyValue: '<EMAIL>',
                        xtreemValue: '<EMAIL>',
                    },
                    { name: 'MAILADDRESS.STATE / region', thirdPartyValue: 'GT', xtreemValue: 'Gauteng' },
                ];

                assert.deepEqual(diffs, diffsToCheck);

                // Vodacom South Africa (Pty) Ltd Somewhere V(8) is _id 500|800
                const intacctContact = await (
                    await context.read(xtremMasterData.nodes.BusinessEntityAddress, {
                        _id: '#500|800',
                    })
                ).intacctBusinessEntityAddress;

                assert.equal(await intacctContact?.state, 'desynchronized');
                assert.deepEqual((await intacctContact?.difference)?.differences, diffsToCheck);
            },
            {
                scenario: 'contact-desync',
                directory: __dirname,
            },
        ));
    it('Customer is desynchronized', () =>
        Test.withContext(
            async context => {
                const diffs = await setDesynchronizedXtrem(context, { name: 'CUSTOMER', id: 'US019' });
                const diffsToCheck = [
                    {
                        name: 'TERMNAME / paymentTerm.name',
                        thirdPartyValue: 'Net 45',
                        xtreemValue: 'Payment due upon receipt',
                    },
                    {
                        name: 'DISPLAYCONTACT.PREFIX / primaryContact.address.intacctBusinessEntityAddress.prefix',
                        thirdPartyValue: 'Ms.',
                        xtreemValue: 'Ms',
                    },
                ];

                assert.deepEqual(diffs.length, 2);

                const custo01 = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' });
                const differences = (await (await custo01.intacctCustomer)?.difference)?.differences;
                assert.equal(await (await custo01.intacctCustomer)?.state, 'desynchronized');

                assert.deepEqual(differences, diffsToCheck, JSON.stringify(differences, null, 4));
            },
            {
                scenario: 'customer-desync',
                directory: __dirname,
                testAttributes: { isCreationMode: true },
            },
        ));

    it('Customer is desynchronized - credit limit', () =>
        Test.withContext(
            async context => {
                await setDesynchronizedXtrem(context, { name: 'CUSTOMER', id: 'US019' });
                const custo01 = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' });
                assert.deepEqual(await custo01.creditLimit, 100.99);
                assert.deepEqual(await custo01.isOnHold, false);
            },
            {
                scenario: 'customer-desync-credit-limit',
                directory: __dirname,
                testAttributes: { isCreationMode: true },
            },
        ));
});
