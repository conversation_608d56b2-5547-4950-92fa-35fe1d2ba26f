import { Test } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremTax from '@sage/xtrem-tax';
import { assert } from 'chai';
import { intacctOptionChangeUpdates } from '../../../lib/functions/intacct-option-management-extension';

describe('Intacct service option update business rules for France', () => {
    it('test intacctOption = true without all AP Invoices posted ', () =>
        Test.withContext(async context => {
            const intacctOption = true;
            await context.bulkUpdate(xtremFinance.nodes.AccountsReceivableInvoice, {
                set: { postingStatus: 'posted' },
            });

            let taxSolutionLine = await context.read(xtremTax.nodes.TaxSolutionLine, {
                taxSolution: '#FRSOL',
                _sortValue: '100',
            });
            assert.isTrue(await taxSolutionLine.isSubjectToGlTaxExcludedAmount);

            await intacctOptionChangeUpdates(context, intacctOption);

            assert.deepEqual(context.diagnoses, [
                {
                    severity: 2,
                    path: [],
                    message: 'There are AP invoices that are not yet posted to the GL.',
                },
            ]);

            taxSolutionLine = await context.read(
                xtremTax.nodes.TaxSolutionLine,
                {
                    taxSolution: '#FRSOL',
                    _sortValue: '100',
                },
                { forUpdate: true },
            );
            assert.isFalse(await taxSolutionLine.isSubjectToGlTaxExcludedAmount);

            await taxSolutionLine.$.set({ isSubjectToGlTaxExcludedAmount: true });
            await taxSolutionLine.$.save();

            const account = await context.read(xtremFinanceData.nodes.Account, {
                id: '3800',
                chartOfAccount: '#DE_DEFAULT',
            });
            assert.equal(await account.taxManagement, 'other');
        }));
    it('test intacctOption = true without all AR Invoices posted ', () =>
        Test.withContext(async context => {
            const intacctOption = true;
            await context.bulkUpdate(xtremFinance.nodes.AccountsPayableInvoice, { set: { postingStatus: 'posted' } });

            let taxSolutionLine = await context.read(xtremTax.nodes.TaxSolutionLine, {
                taxSolution: '#FRSOL',
                _sortValue: '100',
            });
            assert.isTrue(await taxSolutionLine.isSubjectToGlTaxExcludedAmount);

            await intacctOptionChangeUpdates(context, intacctOption);

            assert.deepEqual(context.diagnoses, [
                {
                    severity: 2,
                    path: [],
                    message: 'There are AR invoices that are not yet posted to the GL.',
                },
            ]);

            taxSolutionLine = await context.read(
                xtremTax.nodes.TaxSolutionLine,
                {
                    taxSolution: '#FRSOL',
                    _sortValue: '100',
                },
                { forUpdate: true },
            );
            assert.isFalse(await taxSolutionLine.isSubjectToGlTaxExcludedAmount);

            await taxSolutionLine.$.set({ isSubjectToGlTaxExcludedAmount: true });
            await taxSolutionLine.$.save();

            const account = await context.read(xtremFinanceData.nodes.Account, {
                id: '3800',
                chartOfAccount: '#DE_DEFAULT',
            });
            assert.equal(await account.taxManagement, 'other');
        }));
    it('test intacctOption = false ', () =>
        Test.withContext(async context => {
            const intacctOption = false;

            // Update tax solution to false
            let taxSolutionLine = await context.read(
                xtremTax.nodes.TaxSolutionLine,
                {
                    taxSolution: '#FRSOL',
                    _sortValue: '100',
                },
                { forUpdate: true },
            );
            await taxSolutionLine.$.set({ isSubjectToGlTaxExcludedAmount: false });
            await taxSolutionLine.$.save();
            assert.deepEqual(taxSolutionLine.$.context.diagnoses, []);

            assert.isFalse(await taxSolutionLine.isSubjectToGlTaxExcludedAmount);

            await intacctOptionChangeUpdates(context, intacctOption);

            taxSolutionLine = await context.read(xtremTax.nodes.TaxSolutionLine, {
                taxSolution: '#FRSOL',
                _sortValue: '100',
            });
            assert.isTrue(await taxSolutionLine.isSubjectToGlTaxExcludedAmount);
        }));

    it('test intacctOption = true ', () =>
        Test.withContext(async context => {
            const intacctOption = true;
            await context.bulkUpdate(xtremFinance.nodes.AccountsPayableInvoice, {
                set: { postingStatus: 'posted' },
            });
            await context.bulkUpdate(xtremFinance.nodes.AccountsReceivableInvoice, {
                set: { postingStatus: 'posted' },
            });

            let taxSolutionLine = await context.read(xtremTax.nodes.TaxSolutionLine, {
                taxSolution: '#FRSOL',
                _sortValue: '100',
            });
            assert.isTrue(await taxSolutionLine.isSubjectToGlTaxExcludedAmount);

            await intacctOptionChangeUpdates(context, intacctOption);

            taxSolutionLine = await context.read(xtremTax.nodes.TaxSolutionLine, {
                taxSolution: '#FRSOL',
                _sortValue: '100',
            });
            assert.isFalse(await taxSolutionLine.isSubjectToGlTaxExcludedAmount);

            const account = await context.read(xtremFinanceData.nodes.Account, {
                id: '3800',
                chartOfAccount: '#DE_DEFAULT',
            });
            assert.equal(await account.taxManagement, 'other');
        }));
});
