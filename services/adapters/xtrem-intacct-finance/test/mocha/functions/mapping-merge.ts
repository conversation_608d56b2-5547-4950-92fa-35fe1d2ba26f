import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../index';
import { mergeCustomMapping } from '../../../lib/functions/mapping/mapping-merge';
import { customContacctMapping, mappingMerged } from '../../fixtures/contact-mapping';

describe('Merging mapping', () => {
    it('Contact merge mapping', () =>
        Test.withContext(async context => {
            const contacctMapping = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|CONTACT|BusinessEntityAddress',
            });

            const mappingMergedResult = mergeCustomMapping({
                default: await contacctMapping.getFile,
                custom: customContacctMapping,
                editableFields: await contacctMapping.editableFields,
            });

            const phone2Field = mappingMergedResult.fields.find(field => field.ID === 'PHONE2');
            const companyNameField = mappingMergedResult.fields.find(field => field.ID === 'COMPANYNAME');
            const prefixField = mappingMergedResult.fields.find(field => field.ID === 'PREFIX');

            /**  on customContacctMapping we add 3 modified fields */

            assert.equal(phone2Field?.xtremProperty, 'toto');
            assert.equal(phone2Field?.xtremDefaultProperty, 'locationPhoneNumber');

            assert.equal(companyNameField?.xtremProperty, 'primaryContact.lastName');
            assert.equal(companyNameField?.xtremDefaultProperty, '');

            assert.equal(prefixField?.xtremProperty, 'intacctBusinessEntityAddress.prefix');

            assert.deepEqual(mappingMergedResult, mappingMerged, JSON.stringify(mappingMergedResult));
        }));
});
