import { date, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';

describe(' Bank account matching functions  ', () => {
    before(() => {});

    it(' manageQueryFilter  ', () => {
        const filters: xtremIntacctFinance.interfaces.BankAccountMatching.AccountMatchingQueryFilters = {
            bankAccount: 'My bank account',
            currencyId: 'ZAR',
            transactionType: undefined,
            startFromTo: undefined, // dateRange; TODO : use this instead of dateFrom / dateTo
            dateFrom: date.make(2022, 10, 1),
            dateTo: date.make(2022, 10, 9),
            status: 'Matched',
        };

        const filter = xtremIntacctFinance.functions.intacctQueries.filters.bankAccountTransactionFeed(filters);

        assert.deepEqual(filter.length, 3);
    });

    it(' queryIntacctJournalEntry , empty parameters return empty arrays', () =>
        Test.withContext(async context => {
            const result = await xtremIntacctFinance.functions.intacctQueries.document(context, {}, 'JournalEntry');
            assert.isArray(result);
            assert.deepEqual(result.length, 0);
        }));

    it('Query intacct journal entry', () =>
        Test.withContext(
            async context => {
                const result = await xtremIntacctFinance.functions.intacctQueries.document(
                    context,
                    { description: 'test', date: date.make(2020, 11, 9), amount: 2000, account: '40200' },
                    'JournalEntry',
                );
                assert.isArray(result);
                assert.deepEqual(result.length, 1);
            },
            {
                scenario: 'query-journal-entry',
                directory: __dirname,
            },
        ));
    it(' queryIntacctArInvoice  ', () =>
        Test.withContext(
            async context => {
                const result = await xtremIntacctFinance.functions.intacctQueries.document(
                    context,
                    { customerId: 'ATSE00074' },
                    'ArInvoice',
                );
                assert.isArray(result);
                assert.deepEqual(result.length, 6);
            },
            {
                scenario: 'query-ar-invoice',
                directory: __dirname,
            },
        ));

    it('getFeedRecordStatusForArPayments', () => {
        const result = xtremIntacctFinance.sharedFunctions.BankAccountMatching.getFeedRecordStatusForArPayments({
            amountToMatch: 100,
            arMatch: { isArMatch: true, arPaymentType: 'payment' },
            jsonArInvoices: { matchedArInvoices: [] },
            financeIntegrationStatus: 'posted',
            payToCustomerId: '',
            payToCustomerName: '',
            paymentMethod: '',
            paymentDate: '',
            receiptDate: '',
            account: 'string',
            storedAttributes: '{}',
            storedDimensions: '{}',
            targetDocumentType: 'accountsReceivablePayment',
        });
        assert.strictEqual(result, 'arPaymentPosted');
    });

    it('getFeedRecordStatusForArPayments', () => {
        const result = xtremIntacctFinance.sharedFunctions.BankAccountMatching.getFeedRecordStatus(
            {
                _id: 55,
                amountToMatch: 85,
                cleared: 'unmatched',
                arMatch: {
                    isArMatch: true,
                    customerId: '10081',
                    arPaymentType: 'advance',
                    matchingReasons: 'Customer name SA_Customer found. ',
                },
                jsonArInvoices: { matchedArInvoices: [] },
                financeIntegrationStatus: 'notPosted',
                payToCustomerId: '10051',
                payToCustomerName: 'Admire Arts',
                paymentMethod: 'Credit Card',
                paymentDate: '2022-10-01',
                receiptDate: '2022-10-01',
            },
            [],
        );
        assert.strictEqual(result, 'readyForArAdvanceGeneration');
    });
});
