import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as fs from 'fs';
import * as fps from 'path';
import * as xtremIntacctFinance from '../../../lib';

describe('Read Write Functions', () => {
    const dataTest: xtremIntacctFinance.sharedFunctions.interfaces.LookupObject = {
        name: 'test_tmp',
        documentType: '',
        xtremObject: 'test',
        fields: [
            {
                DATATYPE: '',
                DESCRIPTION: '',
                ID: 'property',
                ISCUSTOM: false,
                CREATEONLY: false,
                LABEL: '',
                READONLY: true,
                REQUIRED: true,
                xtremProperty: 'test',
            },
        ],
        relationshipFields: [
            {
                DATATYPE: '',
                DESCRIPTION: '',
                ID: 'property',
                ISCUSTOM: false,
                CREATEONLY: false,
                LABEL: '',
                READONLY: true,
                REQUIRED: true,
                xtremProperty: 'test',
            },
        ],
        relationships: [
            {
                LABEL: 'relationShip',
                OBJECTNAME: '',
                OBJECTPATH: '',
                RELATEDBY: '',
                RELATIONSHIPTYPE: '',
                xtremProperty: 'relationShip',
            },
        ],
    };

    before(() => {});
    /**
     * Skipped because we call external API, will fail & block jenkins if the webservice is down
     */
    it('Write Data', () =>
        Test.withContext(
            async context => {
                await xtremIntacctFinance.functions.mapping.writeMappingFile(context, dataTest);

                const currentDirectory = context.introspection.getNodeDescriptor(xtremIntacctFinance.nodes.IntacctMap)
                    .package.dir;

                const filePath = fps.resolve(currentDirectory, 'data/mapping', `${dataTest.name}.json`);

                assert.isTrue(fs.existsSync(filePath), filePath);

                const readMappingFile = xtremIntacctFinance.functions.mapping.readMappingFile(context, {
                    name: dataTest.name,
                });

                assert.isNotNull(readMappingFile);
                /**
                 * Delete file
                 */
                fs.unlinkSync(filePath);
            },
            {
                config: { layers: ['no'] },
            },
        ));
});
