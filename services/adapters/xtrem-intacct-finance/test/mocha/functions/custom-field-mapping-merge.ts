import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';

describe('Custom mapping functionnality', () => {
    it('Merge fields', () => {
        const custom: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties[] = [
            {
                ID: 'XTREEM_ID',
                LABEL: 'ID Xtrem',
                DATATYPE: 'INTEGER',
                ISCUSTOM: true,
                READONLY: false,
                REQUIRED: false,
                DESCRIPTION: 'ID Xtrem',
                xtremProperty: '_id',
            },
            {
                ID: 'TOBEDELETED',
                LABEL: 'to be deleted',
                DATATYPE: 'INTEGER',
                ISCUSTOM: true,
                READONLY: false,
                REQUIRED: false,
                DESCRIPTION: 'Must be convert into DELETEDProperty',
                xtremProperty: 'DELETEDProperty',
            },
            {
                ID: 'CHANGEDPROPERTY',
                LABEL: 'Recordno',
                DATATYPE: 'INTEGER',
                ISCUSTOM: true,
                READONLY: false,
                REQUIRED: false,
                DESCRIPTION: 'Record no',
                xtremProperty: 'customValue',
            },
        ];
        const defaultFields = [
            {
                ID: 'RECORDNO',
                LABEL: 'Recordno',
                DATATYPE: 'INTEGER',
                ISCUSTOM: true,
                READONLY: false,
                REQUIRED: false,
                DESCRIPTION: 'Record no',
                xtremProperty: 'recordno',
            },
            {
                ID: 'CHANGEDPROPERTY',
                LABEL: 'Recordno',
                DATATYPE: 'INTEGER',
                ISCUSTOM: true,
                READONLY: false,
                REQUIRED: false,
                DESCRIPTION: 'Record no',
                xtremProperty: 'defaultValue',
            },
            {
                ID: 'TOBEDELETED',
                LABEL: 'to be deleted',
                DATATYPE: 'INTEGER',
                ISCUSTOM: true,
                READONLY: false,
                REQUIRED: false,
                DESCRIPTION: 'Must be convert into DELETEDProperty',
                xtremProperty: 'recordno',
            },
        ];
        const customFields = xtremIntacctFinance.functions.mapping.mergeCustomFields({
            custom,
            default: defaultFields,
            editableFields: ['TOBEDELETED', 'CHANGEDPROPERTY'],
        });

        assert.deepStrictEqual(
            customFields,
            [
                {
                    ID: 'XTREEM_ID',
                    LABEL: 'ID Xtrem',
                    DATATYPE: 'INTEGER',
                    ISCUSTOM: true,
                    READONLY: false,
                    REQUIRED: false,
                    DESCRIPTION: 'ID Xtrem',
                    isEditable: true,
                    xtremProperty: '_id',
                    xtremDefaultProperty: '',
                },
                {
                    ID: 'TOBEDELETED',
                    LABEL: 'to be deleted',
                    DATATYPE: 'INTEGER',
                    ISCUSTOM: true,
                    READONLY: false,
                    REQUIRED: false,
                    isEditable: true,
                    DESCRIPTION: 'Must be convert into DELETEDProperty',
                    xtremProperty: '',
                    xtremDefaultProperty: 'recordno',
                },
                {
                    ID: 'CHANGEDPROPERTY',
                    LABEL: 'Recordno',
                    DATATYPE: 'INTEGER',
                    ISCUSTOM: true,
                    READONLY: false,
                    REQUIRED: false,
                    isEditable: true,
                    DESCRIPTION: 'Record no',
                    xtremProperty: 'customValue',
                    xtremDefaultProperty: 'defaultValue',
                },
                {
                    ID: 'RECORDNO',
                    LABEL: 'Recordno',
                    DATATYPE: 'INTEGER',
                    ISCUSTOM: true,
                    READONLY: false,
                    REQUIRED: false,
                    DESCRIPTION: 'Record no',
                    xtremProperty: 'recordno',
                },
            ],
            JSON.stringify(customFields, null, 4),
        );
    });
});
