import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacctAdapter from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';
import {
    createUpdateXtreem,
    getFieldsName,
    getFieldsToRequest,
    getIntacctDataForCreateUpdate,
    getWhenFields,
    getXtreemData,
    getXtreemDataFormated,
    getXtreemFieldsName,
} from '../../../lib/functions/map';
import { inspectVendor, lookupVendor } from '../../fixtures';

describe('Mapping functions ', () => {
    before(() => {});

    it(' getIntacctNode  ', () =>
        Test.withContext(context => {
            const DimensionNode = xtremIntacctAdapter.functions.getIntacctNode(context, 'dimension');
            assert.equal(typeof DimensionNode, 'function');
            assert.equal(DimensionNode as any, xtremFinanceData.nodes.Dimension);
        }));

    it('transform to Payload ', () =>
        Test.withContext(async context => {
            const intacctData: any = { 'DISPLAYCONTACT.MAILADDRESS.COUNTRYCODE': 'FR' };

            const field: xtremIntacctFinance.sharedFunctions.interfaces.LookupProperties = {
                DATATYPE: 'DISPLAYCONTACT',
                ID: 'DISPLAYCONTACT.MAILADDRESS.COUNTRYCODE',
                DESCRIPTION: 'MAILADDRESS.COUNTRY',
                READONLY: true,
                CREATEONLY: false,
                ISCUSTOM: false,
                REQUIRED: false,
                LABEL: 'COUNTRY',
                xtremProperty: 'country',
                xtremPropertyOption: 'country.id',
            };
            const countryNode = context.introspection.getNodeFromTableName('country');
            assert.isDefined(countryNode);
            const result = await xtremIntacctFinance.functions.mapping.transformToPayload(context, intacctData, field);
            if (countryNode) {
                const country = await context.read(countryNode, { _id: '#FR' });
                assert.deepStrictEqual(result, country._id);
            }
        }));
    it('Merge lookup & inspect', () => {
        const lookupObject = xtremIntacctFinance.functions.mapping.mergeLookupAndInspect(lookupVendor, inspectVendor);

        assert.isArray(lookupObject.fields);
        assert.isArray(lookupObject.relationshipFields);
        assert.isArray(lookupObject.relationships);

        assert.lengthOf(lookupObject.fields, 2);
        assert.lengthOf(lookupObject.relationshipFields!, 1);
        assert.lengthOf(lookupObject.relationships, 2);

        assert.isUndefined(lookupObject.fields.find(field => field.ID === 'PAYTO.CONTACTNAME'));
        assert.isDefined(lookupObject.relationshipFields?.find(field => field.ID === 'PAYTO.CONTACTNAME'));

        assert.isUndefined(lookupObject.fields.find(field => field.ID.search('.')));
    });

    it(' getFieldsName  ', () =>
        Test.withContext(async context => {
            const intacctMapLocation = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|LOCATION|Location',
            });

            const specificFields = await intacctMapLocation.specificFields.toArray();

            assert.equal(specificFields.length, 3);

            assert.equal(await specificFields[0].name, 'LOCATIONID');
            assert.equal(await specificFields[1].name, 'NAME');
            assert.equal(await specificFields[2].name, 'STATUS');
            assert.equal(await specificFields[0].type, 'intacctId');
            assert.equal(await specificFields[1].type, 'name');
            assert.equal(await specificFields[2].type, 'description');

            const { descriptionFieldName, idFieldName, nameFieldName } = await getFieldsName(
                intacctMapLocation.specificFields,
            );

            assert.equal(descriptionFieldName, 'STATUS');
            assert.equal(idFieldName, 'LOCATIONID');
            assert.equal(nameFieldName, 'NAME');
        }));
    it(' getXtreemFieldsName  ', () =>
        Test.withContext(async context => {
            const intacctMapLocation = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|LOCATION|Location',
            });

            const file = await intacctMapLocation.getFile;

            const { descriptionFieldXtremName, idFieldXtremName, nameFieldXtremName } = getXtreemFieldsName(
                file.fields,
                await getFieldsName(intacctMapLocation.specificFields),
            );

            assert.equal(descriptionFieldXtremName, 'status');
            assert.equal(idFieldXtremName, 'id');
            assert.equal(nameFieldXtremName, 'name');
        }));
    it(' getWhenFields  ', () =>
        Test.withContext(async context => {
            const intacctMapLocation = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|LOCATION|Location',
            });

            const file = await intacctMapLocation.getFile;

            const whenFields = getWhenFields(file.fields);

            assert.deepEqual(whenFields, ['WHENMODIFIED', 'WHENCREATED']);
        }));
    it('getFieldsToRequest  ', () =>
        Test.withContext(async context => {
            const intacctMapLocation = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|LOCATION|Location',
            });

            const requestedFields = await getFieldsToRequest(intacctMapLocation);

            assert.deepEqual(requestedFields, [
                'LOCATIONID',
                'NAME',
                'STATUS',
                'WHENMODIFIED',
                'WHENCREATED',
                'RECORD_URL',
                'RECORDNO',
            ]);
        }));
    it('getXtreemData', () =>
        Test.withContext(async context => {
            const intacctMapLocation = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|APTERM|PaymentTerm',
            });

            const xtremData = await getXtreemData(context, intacctMapLocation, {});
            assert.equal(xtremData?.length, 10);
        }));
    it('getXtreemData -', () =>
        Test.withContext(async context => {
            const intacctMapLocation = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|APTERM|PaymentTerm',
            });

            const xtremData = await getXtreemData(context, intacctMapLocation, { xtremSysId: '1' });

            assert.equal(xtremData?.length, 1);
        }));

    // Not used anymore for customer keep it to adapt to an other node
    it.skip('getXtreemDataFormated - CUSTOMER', () =>
        Test.withContext(async context => {
            const intacctMapLocation = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#CUSTOMER|Customer',
            });
            const file = await intacctMapLocation.getFile;

            const xtremData = await getXtreemData(context, intacctMapLocation, {});

            assert.equal(xtremData?.length, 8);

            const xtremDataFormated = xtremData?.length
                ? await getXtreemDataFormated(
                      xtremData[1],
                      getXtreemFieldsName(file.fields, await getFieldsName(intacctMapLocation.specificFields)),
                  )
                : undefined;

            assert.deepEqual(xtremDataFormated, {
                integrationStatus: 'not',
                xtremDescription: 'active',
                xtremID: 'US019',
                xtremName: 'Dépot de TOULOUSE - Sud Ouest',
                xtremSysId: '1',
            });
        }));
    it('getIntacctDataForCreateUpdate', () =>
        Test.withContext(
            async context => {
                const intacctMapLocation = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                    _id: '#intacct|APTERM|PaymentTerm',
                });

                const intacctData = await getIntacctDataForCreateUpdate(context, intacctMapLocation, {
                    intacctIdValue: '30 days EoM',
                });
                assert.equal(intacctData.length, 1);
                assert.equal(intacctData[0].intacctId, '30 days EoM');
            },
            {
                scenario: 'get-intacct-ap-term',
                directory: __dirname,
            },
        ));
    it('createUpdateXtreem Intacct to Xtreem - create & update ', () =>
        Test.withContext(
            async context => {
                const intacctMapLocation = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                    _id: '#intacct|APTERM|PaymentTerm',
                });
                const intacctIdValue = 'Net 120 days';
                const intacctPayload = await getIntacctDataForCreateUpdate(context, intacctMapLocation, {
                    intacctIdValue,
                });
                // First - creation
                let resultCreateUpdate = await createUpdateXtreem(
                    context,
                    intacctMapLocation,
                    { intacctIdValue },
                    intacctPayload,
                );
                assert.equal(resultCreateUpdate.created, 1);
                const paymentTerm = await context
                    .query(xtremMasterData.nodes.PaymentTerm, {
                        filter: { intacctId: 'Net 120 days' },
                        first: 1,
                    })
                    .toArray();
                assert.equal(paymentTerm.length, 1);
                assert.isTrue(await paymentTerm[0].isIntacct);

                const createdNodeData = await getXtreemData(context, intacctMapLocation, { intacctIdValue });
                assert.equal(createdNodeData.length, 1);
                assert.isTrue(await createdNodeData[0].isIntacct);

                // Second will update the previous created
                resultCreateUpdate = await createUpdateXtreem(
                    context,
                    intacctMapLocation,
                    { intacctIdValue },
                    intacctPayload,
                );
                assert.equal(resultCreateUpdate.updated, 1);
            },
            {
                scenario: 'get-intacct-ap-term-120',
                directory: __dirname,
            },
        ));
});
