import { Test } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';

describe('Finance functions  ', () => {
    before(() => {});

    it('updateOpenItemFromIntacct', () =>
        Test.withContext(
            async context => {
                const arInvoice = await context.read(
                    xtremFinance.nodes.AccountsReceivableInvoice,
                    { _id: 1 },
                    { forUpdate: true },
                );
                await xtremIntacctFinance.functions.finance.updateOpenItemFromIntacct(
                    context,
                    'ARINVOICE',
                    arInvoice,
                    context.logger,
                );

                assert.equal(await (await arInvoice.openItems.elementAt(0)).companyAmountPaid, 100);
                assert.equal(await (await arInvoice.openItems.elementAt(0)).financialSiteAmountPaid, 100);
                assert.equal(await (await arInvoice.openItems.elementAt(0)).transactionAmountPaid, 100);
                assert.equal(await (await arInvoice.openItems.elementAt(0)).status, 'partiallyPaid');
            },
            {
                scenario: 'update-open-item-from-intacct',
                directory: __dirname,
            },
        ));
});
