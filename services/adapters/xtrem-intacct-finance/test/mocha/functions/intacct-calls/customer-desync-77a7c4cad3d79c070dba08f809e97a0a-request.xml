<readByName>
    <object>CUS<PERSON><PERSON><PERSON></object>
    <keys>US019</keys>
    <fields>R<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>AM<PERSON>,PA<PERSON><PERSON><PERSON>,TER<PERSON>NA<PERSON>,T<PERSON><PERSON>ID,STATUS,<PERSON><PERSON><PERSON>NCY,RECORD_URL,DISPLAYCONTACT.PREFIX,DISPLAYCONTACT.FIRSTNAME,DISPLAYCONTACT.LASTNAME,DISPLAYCONTACT.PRINTAS,DISPLAYCONTACT.PHONE1,DISPLAYCONTACT.EMAIL1,DISPLAYCONTACT.MAILADDRESS.ADDRESS1,DISPLAYCONTACT.MAILADDRESS.ADDRESS2,DISPLAYCONTACT.MAILADDRESS.CITY,DISPLAYCONTACT.MAILADDRESS.STATE,DISPLAYCONTACT.MAILADDRESS.ZIP,DISPLAYCONTACT.MAILADDRESS.COUNTRYCODE,CONTACTINFO.CONTAC<PERSON><PERSON><PERSON>,SHIPTO.CONTAC<PERSON><PERSON><PERSON>,BIL<PERSON><PERSON>.CONTACTNAME,creditLimit,onHold</fields>
    <returnFormat>xml</returnFormat>
</readByName>