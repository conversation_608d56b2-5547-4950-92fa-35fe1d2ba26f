<readByName>
    <object>ITEM</object>
    <keys>Chemical A,Chemical B,Chemical C,Chemical D,Milk,<PERSON><PERSON><PERSON>,ITME_LOT,SIMPLE_ITEM,SIMPLE_TEST1,SIMPLE_TEST2,SIMPLE_TEST3,LOT_TEST1,LOT_TEST2,LOT_TEST,SIMPLE_TEST,17890-B,17890,7625,3426,6545,5467,8553,1324,3542,BPEC100ML,SC100,LBH<PERSON>DR100,BOX 50BT,ChairLeg,ChairBack,ChairSeat,Chair,7047,7890,5980,3409,3376,17891,17892,17893,7626,7627,7628,Aqua,Service001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,</keys>
    <fields>ITEMID,NAME,STATUS,EXTENDED_DESCRIPTION,UOMGRP,BA<PERSON>UOM,<PERSON><PERSON><PERSON><PERSON>UP,ITEMTYP<PERSON>,SHIP_WEIGHT,ENABLE_BINS,ENABL<PERSON>_EXPIRATION,ENABLE_LOT_CATEGORY,ENABL<PERSON>_SERIALNO,MEGAENTITYKEY,MEGAENTITYID,MEGAENTITYNAME</fields>
    <returnFormat>xml</returnFormat>
</readByName>