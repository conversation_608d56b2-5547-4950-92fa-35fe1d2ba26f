<readByName>
    <object>Customer</object>
    <keys>US019</keys>
    <fields>R<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>MERI<PERSON>,<PERSON>AM<PERSON>,<PERSON><PERSON><PERSON><PERSON>,TER<PERSON><PERSON><PERSON>,TA<PERSON><PERSON>,STATUS,ONH<PERSON>D,<PERSON><PERSON><PERSON>NCY,RECOR<PERSON>_URL,DISPLAYCONTACT.PREFIX,DISPLAYCONTACT.FIRSTNAME,DISPLAYCONTACT.LASTNAME,DISPLAYCONTACT.PRINTAS,DISPLAYCONTACT.PHONE1,DISPLAYCONTACT.EMAIL1,DISPLAYCONTACT.MAILADDRESS.ADDRESS1,DIS<PERSON>AYCONTACT.MAILADDRESS.ADDRESS2,DISPLAYCONTACT.MAILADDRESS.CITY,DISPLAYCONTACT.MAILADDRESS.STATE,DISPLAYCONTACT.MAILADDRESS.ZIP,DISPLAYCONTACT.MAILADDRESS.COUNTRYCODE,CONTAC<PERSON>NFO.CONTAC<PERSON><PERSON><PERSON>,SHIPTO.CONTACT<PERSON><PERSON>,BILLTO.CONTACTNAME</fields>
    <returnFormat>xml</returnFormat>
</readByName>