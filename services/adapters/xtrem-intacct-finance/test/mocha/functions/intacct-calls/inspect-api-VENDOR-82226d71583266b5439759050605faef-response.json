{"_status": "success", "_functionName": "inspect", "_controlId": "7daf257a-b491-4a85-87bb-d31671bbe7e8", "_listType": "All", "_count": 1, "_data": [{"$": {"Name": "VENDOR"}, "Fields": {"Field": ["RECORDNO", "VENDORID", "NAME", "NAME1099", "PARENTKEY", "PARENTID", "PARENTNAME", "DISPLAYCONTACT.CONTACTNAME", "DISPLAYCONTACT.COMPANYNAME", "DISPLAYCONTACT.PREFIX", "DISPLAYCONTACT.FIRSTNAME", "DISPLAYCONTACT.LASTNAME", "DISPLAYCONTACT.INITIAL", "DISPLAYCONTACT.PRINTAS", "DISPLAYCONTACT.TAXABLE", "DISPLAYCONTACT.TAXGROUP", "DISPLAYCONTACT.TAXID", "DISPLAYCONTACT.PHONE1", "DISPLAYCONTACT.PHONE2", "DISPLAYCONTACT.CELLPHONE", "DISPLAYCONTACT.PAGER", "DISPLAYCONTACT.FAX", "DISPLAYCONTACT.TAXIDVALIDATIONDATE", "DISPLAYCONTACT.GSTREGISTERED", "DISPLAYCONTACT.TAXCOMPANYNAME", "DISPLAYCONTACT.TAXADDRESS", "DISPLAYCONTACT.EMAIL1", "DISPLAYCONTACT.EMAIL2", "DISPLAYCONTACT.URL1", "DISPLAYCONTACT.URL2", "DISPLAYCONTACT.VISIBLE", "DISPLAYCONTACT.MAILADDRESS.ADDRESS1", "DISPLAYCONTACT.MAILADDRESS.ADDRESS2", "DISPLAYCONTACT.MAILADDRESS.CITY", "DISPLAYCONTACT.MAILADDRESS.STATE", "DISPLAYCONTACT.MAILADDRESS.ZIP", "DISPLAYCONTACT.MAILADDRESS.COUNTRY", "DISPLAYCONTACT.MAILADDRESS.COUNTRYCODE", "DISPLAYCONTACT.MAILADDRESS.LATITUDE", "DISPLAYCONTACT.MAILADDRESS.LONGITUDE", "DISPLAYCONTACT.STATUS", "ENTITY", "TERMNAME", "TERMVALUE", "VENDORACCOUNTNO", "TAXID", "CREDITLIMIT", "TOTALDUE", "BILLINGTYPE", "VENDTYPE", "VENDTYPE1099TYPE", "GLGROUP", "PRICESCHEDULE", "DISCOUNT", "PRICELIST", "COMMENTS", "ACCOUNTLABEL", "APACCOUNT", "APACCOUNTTITLE", "FORM1099TYPE", "FORM1099BOX", "PAYMENTPRIORITY", "CONTACTINFO.CONTACTNAME", "CONTACTINFO.PREFIX", "CONTACTINFO.FIRSTNAME", "CONTACTINFO.INITIAL", "CONTACTINFO.LASTNAME", "CONTACTINFO.COMPANYNAME", "CONTACTINFO.PRINTAS", "CONTACTINFO.PHONE1", "CONTACTINFO.PHONE2", "CONTACTINFO.CELLPHONE", "CONTACTINFO.PAGER", "CONTACTINFO.FAX", "CONTACTINFO.EMAIL1", "CONTACTINFO.EMAIL2", "CONTACTINFO.URL1", "CONTACTINFO.URL2", "CONTACTINFO.VISIBLE", "CONTACTINFO.MAILADDRESS.ADDRESS1", "CONTACTINFO.MAILADDRESS.ADDRESS2", "CONTACTINFO.MAILADDRESS.CITY", "CONTACTINFO.MAILADDRESS.STATE", "CONTACTINFO.MAILADDRESS.ZIP", "CONTACTINFO.MAILADDRESS.COUNTRY", "CONTACTINFO.MAILADDRESS.COUNTRYCODE", "PAYTO.CONTACTNAME", "PAYTO.PREFIX", "PAYTO.FIRSTNAME", "PAYTO.INITIAL", "PAYTO.LASTNAME", "PAYTO.COMPANYNAME", "PAYTO.PRINTAS", "PAYTO.PHONE1", "PAYTO.PHONE2", "PAYTO.CELLPHONE", "PAYTO.PAGER", "PAYTO.FAX", "PAYTO.EMAIL1", "PAYTO.EMAIL2", "PAYTO.URL1", "PAYTO.URL2", "PAYTO.VISIBLE", "PAYTO.MAILADDRESS.ADDRESS1", "PAYTO.MAILADDRESS.ADDRESS2", "PAYTO.MAILADDRESS.CITY", "PAYTO.MAILADDRESS.STATE", "PAYTO.MAILADDRESS.ZIP", "PAYTO.MAILADDRESS.COUNTRY", "PAYTO.MAILADDRESS.COUNTRYCODE", "PAYTO.TAXGROUP", "PAYTO.TAXID", "RETURNTO.CONTACTNAME", "RETURNTO.PREFIX", "RETURNTO.FIRSTNAME", "RETURNTO.INITIAL", "RETURNTO.LASTNAME", "RETURNTO.COMPANYNAME", "RETURNTO.PRINTAS", "RETURNTO.PHONE1", "RETURNTO.PHONE2", "RETURNTO.CELLPHONE", "RETURNTO.PAGER", "RETURNTO.FAX", "RETURNTO.EMAIL1", "RETURNTO.EMAIL2", "RETURNTO.URL1", "RETURNTO.URL2", "RETURNTO.VISIBLE", "RETURNTO.MAILADDRESS.ADDRESS1", "RETURNTO.MAILADDRESS.ADDRESS2", "RETURNTO.MAILADDRESS.CITY", "RETURNTO.MAILADDRESS.STATE", "RETURNTO.MAILADDRESS.ZIP", "RETURNTO.MAILADDRESS.COUNTRY", "RETURNTO.MAILADDRESS.COUNTRYCODE", "STATUS", "PAYDATEVALUE", "ONETIME", "ONHOLD", "WHENMODIFIED", "ISOWNER", "DONOTCUTCHECK", "OWNER.EQGLACCOUNT", "OWNER.EQGLACCOUNTLABEL", "OWNER.HOLDDIST", "OWNER.ACCOUNTLABEL.LABEL", "CURRENCY", "ACHENABLED", "WIREENABLED", "CHECKENABLED", "ACHBANKROUTINGNUMBER", "ACHACCOUNTNUMBER", "ACHACCOUNTTYPE", "ACHREMITTANCETYPE", "WIREBANKNAME", "WIREBANKROUTINGNUMBER", "WIREACCOUNTTYPE", "PMPLUSREMITTANCETYPE", "PMPLUSEMAIL", "PMPLUSFAX", "DISPLAYTERMDISCOUNT", "OEPRCLSTKEY", "DISPLOCACCTNOCHECK", "WHENCREATED", "CREATEDBY", "MODIFIEDBY", "PAYMENTNOTIFY", "PAYMETHODKEY", "OBJECTRESTRICTION", "MERGEPAYMENTREQ", "DISPLAYCONTACTKEY", "PRIMARYCONTACTKEY", "PAYTOKEY", "RETURNTOKEY", "ACCOUNTLABELKEY", "ACCOUNTKEY", "VENDTYPEKEY", "GLGRPKEY", "TERMSKEY", "VENDORACCTNOKEY", "PAYMETHODREC", "OUTSOURCECHECK", "OUTSOURCECHECKSTATE", "OUTSOURCEACH", "OUTSOURCEACHSTATE", "OUTSOURCECARD", "OUTSOURCECARDOVERRIDE", "CARDSTATE", "VENDORACHACCOUNTID", "VENDORACCOUNTOUTSOURCEACH", "OFFSETGLACCOUNTNO", "OFFSETGLACCOUNTNOTITLE", "CONTACTTO1099.CONTACTNAME", "CONTACTTO1099.PREFIX", "CONTACTTO1099.INITIAL", "CONTACTTO1099.COMPANYNAME", "CONTACTTO1099.PRINTAS", "CONTACTTO1099.PHONE1", "CONTACTTO1099.PHONE2", "CONTACTTO1099.CELLPHONE", "CONTACTTO1099.PAGER", "CONTACTTO1099.FAX", "CONTACTTO1099.EMAIL1", "CONTACTTO1099.EMAIL2", "CONTACTTO1099.URL1", "CONTACTTO1099.URL2", "CONTACTTO1099.VISIBLE", "CONTACTTO1099.MAILADDRESS.ADDRESS1", "CONTACTTO1099.MAILADDRESS.ADDRESS2", "CONTACTTO1099.MAILADDRESS.CITY", "CONTACTTO1099.MAILADDRESS.STATE", "CONTACTTO1099.MAILADDRESS.ZIP", "CONTACTTO1099.MAILADDRESS.COUNTRY", "CONTACTTO1099.MAILADDRESS.COUNTRYCODE", "CONTACTKEY1099", "SUPDOCID", "VENDOR_AMEX_ORGANIZATION_ID", "VENDOR_AMEX_ORG_ADDRESS_ID", "VENDOR_AMEX_CD_AFFILIATE_ID", "VENDOR_AMEX_CARD_AFFILIATE_ID", "AMEX_BANK_ACCOUNT_ID", "AMEX_BANK_ACCOUNT_ADDRESS_ID", "DEFAULT_LEAD_TIME", "RETAINAGEPERCENTAGE", "MEGAENTITYKEY", "MEGAENTITYID", "MEGAENTITYNAME", "XTREEM_VENDOR", "XTREEM_ID", "RECORD_URL"]}}]}