{"_status": "success", "_functionName": "lookup", "_controlId": "65c9886a-9513-4acc-bc93-12d1f2f756c9", "_listType": "All", "_count": 1, "_data": [{"$": {"Name": "VENDOR", "DocumentType": ""}, "Fields": {"Field": [{"ID": "RECORDNO", "LABEL": "Record Number", "DESCRIPTION": "Record Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "VENDORID", "LABEL": "Vendor ID", "DESCRIPTION": "Unique ID of Vendor", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "NAME", "LABEL": "Vendor Name", "DESCRIPTION": "Name of <PERSON><PERSON><PERSON>", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "NAME1099", "LABEL": "1099 Name", "DESCRIPTION": "1099 Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PARENTKEY", "LABEL": "Parent Key", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PARENTID", "LABEL": "<PERSON><PERSON>", "DESCRIPTION": "Name of <PERSON><PERSON>", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PARENTNAME", "LABEL": "<PERSON><PERSON>or Name", "DESCRIPTION": "Name of <PERSON><PERSON>", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "TERMNAME", "LABEL": "Term", "DESCRIPTION": "Term", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "VENDORACCOUNTNO", "LABEL": "Number", "DESCRIPTION": "Vendor Account No.", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "TAXID", "LABEL": "Tax ID", "DESCRIPTION": "Tax Identification Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CREDITLIMIT", "LABEL": "Credit Limit", "DESCRIPTION": "Credit Limit", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "TOTALDUE", "LABEL": "Total Due", "DESCRIPTION": "Total Due", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "BILLINGTYPE", "LABEL": "Vendor Billing Type", "DESCRIPTION": "Vendor Billing Type", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["openitem", "balanceforward"]}, "ISCUSTOM": "false"}, {"ID": "VENDTYPE", "LABEL": "Vendor Type ID", "DESCRIPTION": "Vendor Type", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "VENDTYPE1099TYPE", "LABEL": "Vendor Form 1099 Type", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "GLGROUP", "LABEL": "GL Group", "DESCRIPTION": "GL Group", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PRICESCHEDULE", "LABEL": "Price Schedule", "DESCRIPTION": "Price Schedule", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISCOUNT", "LABEL": "Discount (%)", "DESCRIPTION": "Discount1", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "COMMENTS", "LABEL": "Comments", "DESCRIPTION": "Comments", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "ACCOUNTLABEL", "LABEL": "Account Label", "DESCRIPTION": "AccountLabel Option", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "APACCOUNT", "LABEL": "Default expense account", "DESCRIPTION": "Account Option", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "APACCOUNTTITLE", "LABEL": "AP Account Title", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "FORM1099TYPE", "LABEL": "Form 1099 Type", "DESCRIPTION": "Form 1099 Type", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "FORM1099BOX", "LABEL": "Form 1099 Box", "DESCRIPTION": "Form 1099 Box", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYMENTPRIORITY", "LABEL": "Payment Priority", "DESCRIPTION": "Priority", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["<PERSON><PERSON>", "High", "Normal", "Low"]}, "ISCUSTOM": "false"}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Active-Non-Posting/Inactive", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "active non-posting", "inactive"]}, "ISCUSTOM": "false"}, {"ID": "PAYDATEVALUE", "LABEL": "Default Bill Payment Date", "DESCRIPTION": "PAYDATEVALUE", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "ONETIME", "LABEL": "One-time use", "DESCRIPTION": "One-Time", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "ONHOLD", "LABEL": "On Hold", "DESCRIPTION": "On Hold", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TIMESTAMP", "ISCUSTOM": "false"}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TIMESTAMP", "ISCUSTOM": "false"}, {"ID": "DONOTCUTCHECK", "LABEL": "Don't pay", "DESCRIPTION": "Don't pay", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "CURRENCY", "LABEL": "<PERSON><PERSON><PERSON>", "DESCRIPTION": "<PERSON><PERSON><PERSON>", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PYMTCOUNTRYCODE", "LABEL": "Payment country", "DESCRIPTION": "Country Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["", "AU", "ZA", "GB"]}, "ISCUSTOM": "false"}, {"ID": "FILEPAYMENTSERVICE", "LABEL": "File payment service type", "DESCRIPTION": "File payment service type", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["ACH", "BANKFILE", "NONE"]}, "ISCUSTOM": "false"}, {"ID": "ACHENABLED", "LABEL": "Enable ACH", "DESCRIPTION": "Enable ACH", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "WIREENABLED", "LABEL": "Enable Wire Transfer", "DESCRIPTION": "Enable Wire Transfer", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "CHECKENABLED", "LABEL": "Enable Check Outsourcing", "DESCRIPTION": "Enable Check Outsourcing", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "ACHBANKROUTINGNUMBER", "LABEL": "ACH Bank Routing Number", "DESCRIPTION": "ACH Bank Routing Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "ACHACCOUNTNUMBER", "LABEL": "Account Number", "DESCRIPTION": "ACH Account Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "ACHACCOUNTTYPE", "LABEL": "Account Type", "DESCRIPTION": "ACH Account Type", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Checking Account", "Savings Account"]}, "ISCUSTOM": "false"}, {"ID": "ACHREMITTANCETYPE", "LABEL": "Account classification", "DESCRIPTION": "Account classification", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["CTX", "PPD", "CCD"]}, "ISCUSTOM": "false"}, {"ID": "WIREBANKNAME", "LABEL": "Name of Bank", "DESCRIPTION": "Wire Transfer Bank Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "WIREBANKROUTINGNUMBER", "LABEL": "Bank Routing Number", "DESCRIPTION": "Wire Transfer Bank Routing Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "WIREACCOUNTNUMBER", "LABEL": "Account Number", "DESCRIPTION": "Wire Transfer Account Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "WIREACCOUNTTYPE", "LABEL": "Account Type", "DESCRIPTION": "WIRE Account Type", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": "Demand Deposit Account"}, "ISCUSTOM": "false"}, {"ID": "PMPLUSREMITTANCETYPE", "LABEL": "Remittance Delivery Type", "DESCRIPTION": "Payment Manager Plus Remittance Delivery Type", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Email", "Fax", "EDI"]}, "ISCUSTOM": "false"}, {"ID": "PMPLUSEMAIL", "LABEL": "Remittance Email Address", "DESCRIPTION": "Payment Manager Plus Remittance Email Address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PMPLUSFAX", "LABEL": "Remittance Fax Number", "DESCRIPTION": "Payment Manager Plus Remittance Fax Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYTERMDISCOUNT", "LABEL": "Display the term discount on the check stub", "DESCRIPTION": "Display the term discount on the check stub", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "PAYMETHODKEY", "LABEL": "Preferred Payment Method", "DESCRIPTION": "Prefered Payment Method", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["", "Printed Check", "Charge Card", "EFT", "Cash", "ACH", "WF Check", "WF USD Wire", "WF Domestic ACH"]}, "ISCUSTOM": "false"}, {"ID": "DISPLOCACCTNOCHECK", "LABEL": "Display the vendor-assigned account number, per entity, on the check stub", "DESCRIPTION": "Display the vendor-assigned account number, per entity, on the check stub", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "VENDORACCTNOKEY", "LABEL": "Vendor Account Number Key", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "PAYMENTNOTIFY", "LABEL": "Send Automatic Payment Notification", "DESCRIPTION": "Send Payment Notification", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "MERGEPAYMENTREQ", "LABEL": "Merge payment requests", "DESCRIPTION": "Merge Payment Requests", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACTKEY", "LABEL": "Display Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "PRIMARYCONTACTKEY", "LABEL": "Primary Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "PAYTOKEY", "LABEL": "Pay-to Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "RETURNTOKEY", "LABEL": "Return-to Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "ACCOUNTLABELKEY", "LABEL": "Account Label Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "ACCOUNTKEY", "LABEL": "Account Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "VENDTYPEKEY", "LABEL": "Vendor Type Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "GLGRPKEY", "LABEL": "GL Group Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "TERMSKEY", "LABEL": "Term Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "PAYMETHODREC", "LABEL": "Payment Method Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "OUTSOURCECHECK", "LABEL": "Enable Check Delivery Service", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "OUTSOURCECHECKSTATE", "LABEL": "Services state", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Validation Failed", "Validation Passed", "Pending Activation", "queued", "Subscribed"]}, "ISCUSTOM": "false"}, {"ID": "OUTSOURCEACH", "LABEL": "Enable American Express ACH Payment Service", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "OUTSOURCEACHSTATE", "LABEL": "Amex ACH Services status", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Active", "In Progress", "Not Enabled", "Validation Failed", "Validation Passed", "Pending Activation", "queued", "Subscribed", "Inactive"]}, "ISCUSTOM": "false"}, {"ID": "OUTSOURCECARD", "LABEL": "Enable American Express Card Payment Service", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "CARDSTATE", "LABEL": "Amex Credit Card Services state", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Active", "In Progress", "Not Enabled", "Validation Failed", "Validation Passed", "Pending Activation", "queued", "Subscribed", "Inactive"]}, "ISCUSTOM": "false"}, {"ID": "OFFSETGLACCOUNTNO", "LABEL": "AP Account", "DESCRIPTION": "AP Account", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "OFFSETGLACCOUNTNOTITLE", "LABEL": "Default AP Account", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTKEY1099", "LABEL": "1099 Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "VENDOR_AMEX_ORGANIZATION_ID", "LABEL": "Organization ID generated by Amex for vendor", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "VENDOR_AMEX_ORG_ADDRESS_ID", "LABEL": "Organization address ID generated by AMEX for vendor", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "VENDOR_AMEX_CD_AFFILIATE_ID", "LABEL": "Vendor Organization Check Delivery affiliate ID", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "VENDOR_AMEX_CARD_AFFILIATE_ID", "LABEL": "Vendor Organization Corporate Card affiliate ID", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "AMEX_BANK_ACCOUNT_ID", "LABEL": "AMEX bank account ID", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "AMEX_BANK_ACCOUNT_ADDRESS_ID", "LABEL": "AMEX bank account address ID", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETAINAGEPERCENTAGE", "LABEL": "Default retainage percentage", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "PERCENT", "ISCUSTOM": "false"}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "XTREEM_VENDOR", "LABEL": "XTreeM", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "true"}, {"ID": "XTREEM_ID", "LABEL": "XTreeM_id", "DESCRIPTION": "Instance _id in XTreeM", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DECIMAL", "ISCUSTOM": "true"}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}]}, "Relationships": {"Relationship": [{"OBJECTPATH": "PARENT", "OBJECTNAME": "VENDOR", "LABEL": "<PERSON><PERSON>", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PARENTID"}, {"OBJECTPATH": "PAYTOCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Pay to Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PAYTO.CONTACTNAME"}, {"OBJECTPATH": "RETURNTOCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Return to Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "RETURNTO.CONTACTNAME"}, {"OBJECTPATH": "CONTACT1099", "OBJECTNAME": "CONTACT", "LABEL": "1099 Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CONTACTTO1099.CONTACTNAME"}, {"OBJECTPATH": "DISPLAYCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "DISPLAYCONTACT.CONTACTNAME"}, {"OBJECTPATH": "PRIMARYCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Primary Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CONTACTINFO.CONTACTNAME"}, {"OBJECTPATH": "APACCOUNTLABEL", "OBJECTNAME": "APACCOUNTLABEL", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "ACCOUNTLABEL"}, {"OBJECTPATH": "APACCOUNT", "OBJECTNAME": "GLACCOUNT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "APACCOUNT"}, {"OBJECTPATH": "TERM", "OBJECTNAME": "APTERM", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "TERMNAME"}, {"OBJECTPATH": "GLGROUP", "OBJECTNAME": "VENDGLGROUP", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "GLGROUP"}, {"OBJECTPATH": "PRICELIST", "OBJECTNAME": "INVPRICELIST", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PRICESCHEDULE"}, {"OBJECTPATH": "OFFSETGLACCOUNTNO", "OBJECTNAME": "GLACCOUNT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "OFFSETGLACCOUNTNO"}, {"OBJECTPATH": "VENDTYPE", "OBJECTNAME": "VENDTYPE", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "VENDTYPE"}, {"OBJECTPATH": "BANKFILE", "OBJECTNAME": "VENDORBANKFILEDETAIL", "LABEL": "Bank File", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "VENDORKEY"}, {"OBJECTPATH": "MELOCATION", "OBJECTNAME": "LOCATION", "LABEL": "Created At Entity Information", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MEGAENTITYID"}]}}]}