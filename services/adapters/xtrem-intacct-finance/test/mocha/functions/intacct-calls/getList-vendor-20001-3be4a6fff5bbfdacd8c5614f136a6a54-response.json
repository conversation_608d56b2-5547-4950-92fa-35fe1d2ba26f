{"_status": "success", "_functionName": "get_list", "_controlId": "df0eb2c2-7c8f-422e-8c7a-be0eb02c3260", "_listType": "[object Object]", "_totalCount": 1, "_start": 0, "_end": 0, "_data": [{"recordno": "31", "vendorid": "20001", "name": "Massachusetts Department of Revenue", "parentid": "1234", "termname": "Net 30", "glaccountno": "60130", "accountlabel": "", "vendtype": "Indirect Purchases", "taxid": "97-3397671", "creditlimit": "100000", "totaldue": "6800", "billingtype": "openitem", "vendoraccountno": "", "comments": "", "outsourcecheck": "", "servicestate": "", "donotcutcheck": "false", "onhold": "F", "form1099": "false", "name1099": "Test", "form1099type": "", "form1099box": "", "supdocid": "", "status": "active", "currency": "USD", "onetime": "false", "whenmodified": "05/10/2021 20:11:37", "primary": {"contactname": "<PERSON>"}, "returnto": {"contactname": "<PERSON>"}, "payto": {"contactname": "<PERSON>"}, "contactto1099": {"contactname": "<PERSON>"}, "contactinfo": {"contact": {"contactname": "Tax", "printas": "Massachusetts Department of Revenue", "companyname": "Massachusetts Department of Revenue", "taxable": "true", "taxgroup": "AU - Taxable", "prefix": "", "firstname": "<PERSON>", "lastname": "<PERSON>", "initial": "", "phone1": "************", "phone2": "", "cellphone": "*********", "pager": "", "fax": "", "email1": "<EMAIL>", "email2": "", "url1": "http://google.fr", "url2": "", "mailaddress": {"address1": "100 Trade Center", "address2": "", "city": "Chelsea", "state": "MA", "zip": "02150", "country": "United States", "latitude": "", "longitude": ""}}}, "contactlist": {"contactitem": [{"category": "RETURN ADDRESS", "contactname": "<PERSON>"}, {"category": "PAYMENT ADDRESS", "contactname": "<PERSON>"}, {"category": "THIRD ADDRESS", "contactname": "Botes, Maryna"}, {"category": "AUXILARY ADDRESS", "contactname": "benoit"}]}, "paymentnotify": "false", "achenabled": "", "wireenabled": "false", "checkenabled": "false", "achbankroutingnumber": "", "achaccountnumber": "", "achaccounttype": "", "achremittancetype": "", "wirebankname": "", "wirebankroutingnumber": "", "wireaccountnumber": "", "wireaccounttype": "", "pmplusremittancetype": "", "pmplusemail": "", "pmplusfax": "", "displaytermdiscount": "false", "displocacctnocheck": "false", "mergepaymentreq": "true", "offsetglaccountno": "", "visibility": {"visibility_type": "Unrestricted", "restricted_locs": "", "restricted_depts": ""}, "retainagepercentage": ""}]}