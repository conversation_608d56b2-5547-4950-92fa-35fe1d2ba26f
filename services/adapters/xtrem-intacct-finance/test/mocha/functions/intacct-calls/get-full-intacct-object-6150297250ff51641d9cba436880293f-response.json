{"_status": "success", "_functionName": "lookup", "_controlId": "ae2cbb58-be7c-4cf4-b55d-ca151691833f", "_listType": "All", "_count": 1, "_data": [{"$": {"Name": "VENDOR", "DocumentType": ""}, "Fields": {"Field": [{"ID": "RECORDNO", "LABEL": "Record Number", "DESCRIPTION": "Record Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "VENDORID", "LABEL": "Vendor ID", "DESCRIPTION": "Unique ID of Vendor", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "NAME", "LABEL": "Vendor Name", "DESCRIPTION": "Name of <PERSON><PERSON><PERSON>", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "NAME1099", "LABEL": "1099 Name", "DESCRIPTION": "1099 Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PARENTKEY", "LABEL": "Parent Key", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PARENTID", "LABEL": "<PERSON><PERSON>", "DESCRIPTION": "Name of <PERSON><PERSON>", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PARENTNAME", "LABEL": "<PERSON><PERSON>or Name", "DESCRIPTION": "Name of <PERSON><PERSON>", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.CONTACTNAME", "LABEL": "Contact name", "DESCRIPTION": "Unique Name to be used to identify the contact in lists", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.COMPANYNAME", "LABEL": "Company name", "DESCRIPTION": "Full name of the company", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.PREFIX", "LABEL": "Mr./Ms./Mrs.", "DESCRIPTION": "Mr./Ms./Mrs.", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.FIRSTNAME", "LABEL": "First Name", "DESCRIPTION": "First Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.LASTNAME", "LABEL": "Last Name", "DESCRIPTION": "Last Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.INITIAL", "LABEL": "Middle Name", "DESCRIPTION": "Middle Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.PRINTAS", "LABEL": "Print As", "DESCRIPTION": "Print As", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.TAXABLE", "LABEL": "Taxable", "DESCRIPTION": "Taxable ", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.TAXGROUP", "LABEL": "Contact tax group", "DESCRIPTION": "Tax Group", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.TAXID", "LABEL": "Tax ID", "DESCRIPTION": "Tax Identification Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.PHONE1", "LABEL": "Phone Number", "DESCRIPTION": "Phone Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.PHONE2", "LABEL": "Secondary phone", "DESCRIPTION": "Seconday phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.CELLPHONE", "LABEL": "Cellular Phone Number", "DESCRIPTION": "Cellular Phone Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.PAGER", "LABEL": "Pager Number", "DESCRIPTION": "Pager Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.FAX", "LABEL": "Fax Number", "DESCRIPTION": "Fax Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.TAXIDVALIDATIONDATE", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "DATE", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.GSTREGISTERED", "LABEL": "GST registered", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["T", "F"]}, "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.TAXCOMPANYNAME", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.TAXADDRESS", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.EMAIL1", "LABEL": "Email Address", "DESCRIPTION": "Email Address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.EMAIL2", "LABEL": "Secondary email addresses", "DESCRIPTION": "Secondary Email Address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.URL1", "LABEL": "URL", "DESCRIPTION": "URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.URL2", "LABEL": "Secondary URL", "DESCRIPTION": "Secondary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.VISIBLE", "LABEL": "Visible", "DESCRIPTION": "Visible", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.MAILADDRESS.ADDRESS1", "LABEL": "Address line 1", "DESCRIPTION": "Address Line 1", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.MAILADDRESS.ADDRESS2", "LABEL": "Address line 2", "DESCRIPTION": "Address Line 2", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.MAILADDRESS.CITY", "LABEL": "City", "DESCRIPTION": "City", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.MAILADDRESS.STATE", "LABEL": "State/Territory", "DESCRIPTION": "State/Province", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.MAILADDRESS.ZIP", "LABEL": "Zip code/Post code", "DESCRIPTION": "Zip/Postal Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.MAILADDRESS.COUNTRY", "LABEL": "Country", "DESCRIPTION": "Country", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.MAILADDRESS.COUNTRYCODE", "LABEL": "Country code", "DESCRIPTION": "Country Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["US", "AF", "AX", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG", "AR", "AM", "AW", "AU", "AT", "AZ", "BS", "BH", "BD", "BB", "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BQ", "BA", "BW", "BV", "BR", "IO", "BN", "BG", "BF", "BI", "KH", "CM", "CA", "CV", "KY", "CF", "TD", "CL", "CN", "CX", "CC", "CO", "KM", "CG", "CD", "CK", "CR", "CI", "HR", "CU", "CW", "CY", "CZ", "DK", "DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE", "SZ", "ET", "FK", "FO", "FJ", "FI", "FR", "GF", "PF", "TF", "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP", "GU", "GT", "GG", "GN", "GW", "GY", "HT", "HM", "HN", "HK", "HU", "IS", "IN", "ID", "IR", "IQ", "IE", "IM", "IL", "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI", "KR", "KP", "XK", "KW", "KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT", "LU", "MO", "MK", "MG", "MW", "MY", "MV", "ML", "MT", "MH", "MQ", "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "ME", "MS", "MA", "MZ", "MM", "NA", "NR", "NP", "NL", "AN", "NC", "NZ", "NI", "NE", "NG", "NU", "NF", "MP", "NO", "OM", "PK", "PW", "PS", "PA", "PG", "PY", "PE", "PH", "PN", "PL", "PT", "PR", "QA", "RE", "RO", "RU", "RW", "BL", "SH", "KN", "LC", "MF", "PM", "VC", "WS", "SM", "ST", "SA", "SN", "RS", "SC", "SL", "SG", "SX", "SK", "SI", "SB", "SO", "ZA", "GS", "ES", "LK", "SD", "SS", "SR", "SJ", "SE", "CH", "SY", "TW", "TJ", "TZ", "TH", "TL", "TG", "TK", "TO", "TT", "TN", "TR", "TM", "TC", "TV", "UG", "UA", "AE", "GB", "UM", "UY", "UZ", "VU", "VA", "VE", "VN", "VG", "VI", "WF", "EH", "YE", "ZM", "ZW"]}, "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.MAILADDRESS.LATITUDE", "LABEL": "Latitude", "DESCRIPTION": "Latitude", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.MAILADDRESS.LONGITUDE", "LABEL": "Longitude", "DESCRIPTION": "Longitude", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACT.STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Inactive", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "inactive"]}, "ISCUSTOM": "false"}, {"ID": "ENTITY", "LABEL": "Entity", "DESCRIPTION": "Entity", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "TERMNAME", "LABEL": "Term", "DESCRIPTION": "Term", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "TERMVALUE", "LABEL": "Term Value", "DESCRIPTION": "Term Value", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "VENDORACCOUNTNO", "LABEL": "Number", "DESCRIPTION": "Vendor Account No.", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "TAXID", "LABEL": "Tax ID", "DESCRIPTION": "Tax Identification Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CREDITLIMIT", "LABEL": "Credit Limit", "DESCRIPTION": "Credit Limit", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "TOTALDUE", "LABEL": "Total Due", "DESCRIPTION": "Total Due", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "BILLINGTYPE", "LABEL": "Vendor Billing Type", "DESCRIPTION": "Vendor Billing Type", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["openitem", "balanceforward"]}, "ISCUSTOM": "false"}, {"ID": "VENDTYPE", "LABEL": "Vendor Type ID", "DESCRIPTION": "Vendor Type", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "VENDTYPE1099TYPE", "LABEL": "Vendor Form 1099 Type", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "GLGROUP", "LABEL": "GL Group", "DESCRIPTION": "GL Group", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PRICESCHEDULE", "LABEL": "Price Schedule", "DESCRIPTION": "Price Schedule", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISCOUNT", "LABEL": "Discount (%)", "DESCRIPTION": "Discount1", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DECIMAL", "ISCUSTOM": "false"}, {"ID": "PRICELIST", "LABEL": "Vendor Price List", "DESCRIPTION": "Vendor Price Schedule", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "COMMENTS", "LABEL": "Comments", "DESCRIPTION": "Comments", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "ACCOUNTLABEL", "LABEL": "Account Label", "DESCRIPTION": "AccountLabel Option", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "APACCOUNT", "LABEL": "Default expense account", "DESCRIPTION": "Account Option", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "APACCOUNTTITLE", "LABEL": "AP Account Title", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "FORM1099TYPE", "LABEL": "Form 1099 Type", "DESCRIPTION": "Form 1099 Type", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "FORM1099BOX", "LABEL": "Form 1099 Box", "DESCRIPTION": "Form 1099 Box", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYMENTPRIORITY", "LABEL": "Payment Priority", "DESCRIPTION": "Priority", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["<PERSON><PERSON>", "High", "Normal", "Low"]}, "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.CONTACTNAME", "LABEL": "Primary Contact", "DESCRIPTION": "Primary Contact Name", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "ENUM", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.PREFIX", "LABEL": "Contact - Prefix", "DESCRIPTION": "Mr./Ms./Mrs.", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.FIRSTNAME", "LABEL": "Contact - First Name", "DESCRIPTION": "First Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.INITIAL", "LABEL": "Contact - MI", "DESCRIPTION": "Middle Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.LASTNAME", "LABEL": "Contact - Last Name", "DESCRIPTION": "Last Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.COMPANYNAME", "LABEL": "Contact - Company Name", "DESCRIPTION": "Full name of the company", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.PRINTAS", "LABEL": "Contact - Print As", "DESCRIPTION": "Name as appears on official documents", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.PHONE1", "LABEL": "Contact - Phone 1", "DESCRIPTION": "Primary phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.PHONE2", "LABEL": "Contact - Phone 2", "DESCRIPTION": "Seconday phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.CELLPHONE", "LABEL": "Contact - Mobile", "DESCRIPTION": "Cellular Phone Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.PAGER", "LABEL": "Contact - Pager", "DESCRIPTION": "Pager", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.FAX", "LABEL": "Contact - Fax", "DESCRIPTION": "Fax", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.EMAIL1", "LABEL": "Contact - Email 1", "DESCRIPTION": "Primary email address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.EMAIL2", "LABEL": "Contact - Email 2", "DESCRIPTION": "Secondary Email Address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.URL1", "LABEL": "Contact - URL 1", "DESCRIPTION": "Primary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.URL2", "LABEL": "Contact - URL 2", "DESCRIPTION": "Secondary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.VISIBLE", "LABEL": "Primary - Contact Visible", "DESCRIPTION": "Visible", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.MAILADDRESS.ADDRESS1", "LABEL": "Contact Address - Addr1", "DESCRIPTION": "Address Line 1", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.MAILADDRESS.ADDRESS2", "LABEL": "Contact Address - Addr2", "DESCRIPTION": "Address Line 2", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.MAILADDRESS.CITY", "LABEL": "Contact Address - City", "DESCRIPTION": "City", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.MAILADDRESS.STATE", "LABEL": "Contact Address - State/Territory", "DESCRIPTION": "State/Province", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.MAILADDRESS.ZIP", "LABEL": "Contact Address - Zip code/Post code", "DESCRIPTION": "Zip/Postal Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.MAILADDRESS.COUNTRY", "LABEL": "Contact Address - Country", "DESCRIPTION": "Country", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTINFO.MAILADDRESS.COUNTRYCODE", "LABEL": "Contact Address - Country Code", "DESCRIPTION": "Country Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["US", "AF", "AX", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG", "AR", "AM", "AW", "AU", "AT", "AZ", "BS", "BH", "BD", "BB", "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BQ", "BA", "BW", "BV", "BR", "IO", "BN", "BG", "BF", "BI", "KH", "CM", "CA", "CV", "KY", "CF", "TD", "CL", "CN", "CX", "CC", "CO", "KM", "CG", "CD", "CK", "CR", "CI", "HR", "CU", "CW", "CY", "CZ", "DK", "DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE", "SZ", "ET", "FK", "FO", "FJ", "FI", "FR", "GF", "PF", "TF", "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP", "GU", "GT", "GG", "GN", "GW", "GY", "HT", "HM", "HN", "HK", "HU", "IS", "IN", "ID", "IR", "IQ", "IE", "IM", "IL", "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI", "KR", "KP", "XK", "KW", "KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT", "LU", "MO", "MK", "MG", "MW", "MY", "MV", "ML", "MT", "MH", "MQ", "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "ME", "MS", "MA", "MZ", "MM", "NA", "NR", "NP", "NL", "AN", "NC", "NZ", "NI", "NE", "NG", "NU", "NF", "MP", "NO", "OM", "PK", "PW", "PS", "PA", "PG", "PY", "PE", "PH", "PN", "PL", "PT", "PR", "QA", "RE", "RO", "RU", "RW", "BL", "SH", "KN", "LC", "MF", "PM", "VC", "WS", "SM", "ST", "SA", "SN", "RS", "SC", "SL", "SG", "SX", "SK", "SI", "SB", "SO", "ZA", "GS", "ES", "LK", "SD", "SS", "SR", "SJ", "SE", "CH", "SY", "TW", "TJ", "TZ", "TH", "TL", "TG", "TK", "TO", "TT", "TN", "TR", "TM", "TC", "TV", "UG", "UA", "AE", "GB", "UM", "UY", "UZ", "VU", "VA", "VE", "VN", "VG", "VI", "WF", "EH", "YE", "ZM", "ZW"]}, "ISCUSTOM": "false"}, {"ID": "PAYTO.CONTACTNAME", "LABEL": "Pay To Contact", "DESCRIPTION": "Contact Name for Payments", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "ENUM", "ISCUSTOM": "false"}, {"ID": "PAYTO.PREFIX", "LABEL": "Pay To Contact - Prefix", "DESCRIPTION": "Mr./Ms./Mrs.", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.FIRSTNAME", "LABEL": "Pay To Contact - First Name", "DESCRIPTION": "First Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.INITIAL", "LABEL": "Pay To Contact - MI", "DESCRIPTION": "Middle Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.LASTNAME", "LABEL": "Pay To Contact - Last Name", "DESCRIPTION": "Last Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.COMPANYNAME", "LABEL": "Pay To Contact - Company Name", "DESCRIPTION": "Full name of the company", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.PRINTAS", "LABEL": "Pay To Contact - Print As", "DESCRIPTION": "Name as appears on official documents", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.PHONE1", "LABEL": "Pay To Contact - Phone 1", "DESCRIPTION": "Primary phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.PHONE2", "LABEL": "Pay To Contact - Phone 2", "DESCRIPTION": "Seconday phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.CELLPHONE", "LABEL": "Pay To Contact - Mobile", "DESCRIPTION": "Cellular Phone Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.PAGER", "LABEL": "Pay To Contact - Pager", "DESCRIPTION": "Pager", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.FAX", "LABEL": "Pay To Contact - Fax", "DESCRIPTION": "Fax", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.EMAIL1", "LABEL": "Pay To Contact - Email 1", "DESCRIPTION": "Primary email address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.EMAIL2", "LABEL": "Pay To Contact - Email 2", "DESCRIPTION": "Secondary Email Address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.URL1", "LABEL": "Pay To Contact - URL 1", "DESCRIPTION": "Primary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.URL2", "LABEL": "Pay To Contact - URL 2", "DESCRIPTION": "Secondary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.VISIBLE", "LABEL": "Pay To Contact - Visible", "DESCRIPTION": "Visible", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "PAYTO.MAILADDRESS.ADDRESS1", "LABEL": "Pay To Contact Address - Addr1", "DESCRIPTION": "Address Line 1", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.MAILADDRESS.ADDRESS2", "LABEL": "Pay To Contact Address - Addr2", "DESCRIPTION": "Address Line 2", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.MAILADDRESS.CITY", "LABEL": "Pay To Contact Address - City", "DESCRIPTION": "City", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.MAILADDRESS.STATE", "LABEL": "Pay To Contact Address - State/Territory", "DESCRIPTION": "State/Province", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.MAILADDRESS.ZIP", "LABEL": "Pay To Contact Address - Zip code/Post code", "DESCRIPTION": "Zip/Postal Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.MAILADDRESS.COUNTRY", "LABEL": "Pay To Contact Address - Country", "DESCRIPTION": "Country", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.MAILADDRESS.COUNTRYCODE", "LABEL": "Pay To Contact Address - Country Code", "DESCRIPTION": "Country Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["US", "AF", "AX", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG", "AR", "AM", "AW", "AU", "AT", "AZ", "BS", "BH", "BD", "BB", "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BQ", "BA", "BW", "BV", "BR", "IO", "BN", "BG", "BF", "BI", "KH", "CM", "CA", "CV", "KY", "CF", "TD", "CL", "CN", "CX", "CC", "CO", "KM", "CG", "CD", "CK", "CR", "CI", "HR", "CU", "CW", "CY", "CZ", "DK", "DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE", "SZ", "ET", "FK", "FO", "FJ", "FI", "FR", "GF", "PF", "TF", "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP", "GU", "GT", "GG", "GN", "GW", "GY", "HT", "HM", "HN", "HK", "HU", "IS", "IN", "ID", "IR", "IQ", "IE", "IM", "IL", "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI", "KR", "KP", "XK", "KW", "KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT", "LU", "MO", "MK", "MG", "MW", "MY", "MV", "ML", "MT", "MH", "MQ", "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "ME", "MS", "MA", "MZ", "MM", "NA", "NR", "NP", "NL", "AN", "NC", "NZ", "NI", "NE", "NG", "NU", "NF", "MP", "NO", "OM", "PK", "PW", "PS", "PA", "PG", "PY", "PE", "PH", "PN", "PL", "PT", "PR", "QA", "RE", "RO", "RU", "RW", "BL", "SH", "KN", "LC", "MF", "PM", "VC", "WS", "SM", "ST", "SA", "SN", "RS", "SC", "SL", "SG", "SX", "SK", "SI", "SB", "SO", "ZA", "GS", "ES", "LK", "SD", "SS", "SR", "SJ", "SE", "CH", "SY", "TW", "TJ", "TZ", "TH", "TL", "TG", "TK", "TO", "TT", "TN", "TR", "TM", "TC", "TV", "UG", "UA", "AE", "GB", "UM", "UY", "UZ", "VU", "VA", "VE", "VN", "VG", "VI", "WF", "EH", "YE", "ZM", "ZW"]}, "ISCUSTOM": "false"}, {"ID": "PAYTO.TAXGROUP", "LABEL": "Pay To Contact - Contact tax group", "DESCRIPTION": "Tax Group", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PAYTO.TAXID", "LABEL": "Tax ID", "DESCRIPTION": "Tax Identification Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.CONTACTNAME", "LABEL": "Return To Contact", "DESCRIPTION": "Contact Name for Return Shipping", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "ENUM", "ISCUSTOM": "false"}, {"ID": "RETURNTO.PREFIX", "LABEL": "Return To Contact - Prefix", "DESCRIPTION": "Mr./Ms./Mrs.", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.FIRSTNAME", "LABEL": "Return To Contact - First Name", "DESCRIPTION": "First Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.INITIAL", "LABEL": "Return To Contact - MI", "DESCRIPTION": "Middle Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.LASTNAME", "LABEL": "Return To Contact - Last Name", "DESCRIPTION": "Last Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.COMPANYNAME", "LABEL": "Return To Contact - Company Name", "DESCRIPTION": "Full name of the company", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.PRINTAS", "LABEL": "Return To Contact - Print As", "DESCRIPTION": "Name as appears on official documents", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.PHONE1", "LABEL": "Return To Contact - Phone 1", "DESCRIPTION": "Primary phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.PHONE2", "LABEL": "Return To Contact - Phone 2", "DESCRIPTION": "Seconday phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.CELLPHONE", "LABEL": "Return To Contact - Mobile", "DESCRIPTION": "Cellular Phone Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.PAGER", "LABEL": "Return To Contact - Pager", "DESCRIPTION": "Pager", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.FAX", "LABEL": "Return To Contact - Fax", "DESCRIPTION": "Fax", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.EMAIL1", "LABEL": "Return To Contact - Email 1", "DESCRIPTION": "Primary email address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.EMAIL2", "LABEL": "Return To Contact - Email 2", "DESCRIPTION": "Secondary Email Address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.URL1", "LABEL": "Return To Contact - URL 1", "DESCRIPTION": "Primary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.URL2", "LABEL": "Return To Contact - URL 2", "DESCRIPTION": "Secondary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.VISIBLE", "LABEL": "Return To Primary - Contact Visible", "DESCRIPTION": "Visible", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "RETURNTO.MAILADDRESS.ADDRESS1", "LABEL": "Return To Contact Address - Addr1", "DESCRIPTION": "Address Line 1", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.MAILADDRESS.ADDRESS2", "LABEL": "Return To Contact Address - Addr2", "DESCRIPTION": "Address Line 2", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.MAILADDRESS.CITY", "LABEL": "Return To Contact Address - City", "DESCRIPTION": "City", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.MAILADDRESS.STATE", "LABEL": "Return To Contact Address - State/Territory", "DESCRIPTION": "State/Province", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.MAILADDRESS.ZIP", "LABEL": "Return To Contact Address - Zip code/Post code", "DESCRIPTION": "Zip/Postal Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.MAILADDRESS.COUNTRY", "LABEL": "Return To Contact Address - Country", "DESCRIPTION": "Country", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "RETURNTO.MAILADDRESS.COUNTRYCODE", "LABEL": "Return To Contact Address - Country Code", "DESCRIPTION": "Country Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["US", "AF", "AX", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG", "AR", "AM", "AW", "AU", "AT", "AZ", "BS", "BH", "BD", "BB", "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BQ", "BA", "BW", "BV", "BR", "IO", "BN", "BG", "BF", "BI", "KH", "CM", "CA", "CV", "KY", "CF", "TD", "CL", "CN", "CX", "CC", "CO", "KM", "CG", "CD", "CK", "CR", "CI", "HR", "CU", "CW", "CY", "CZ", "DK", "DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE", "SZ", "ET", "FK", "FO", "FJ", "FI", "FR", "GF", "PF", "TF", "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP", "GU", "GT", "GG", "GN", "GW", "GY", "HT", "HM", "HN", "HK", "HU", "IS", "IN", "ID", "IR", "IQ", "IE", "IM", "IL", "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI", "KR", "KP", "XK", "KW", "KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT", "LU", "MO", "MK", "MG", "MW", "MY", "MV", "ML", "MT", "MH", "MQ", "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "ME", "MS", "MA", "MZ", "MM", "NA", "NR", "NP", "NL", "AN", "NC", "NZ", "NI", "NE", "NG", "NU", "NF", "MP", "NO", "OM", "PK", "PW", "PS", "PA", "PG", "PY", "PE", "PH", "PN", "PL", "PT", "PR", "QA", "RE", "RO", "RU", "RW", "BL", "SH", "KN", "LC", "MF", "PM", "VC", "WS", "SM", "ST", "SA", "SN", "RS", "SC", "SL", "SG", "SX", "SK", "SI", "SB", "SO", "ZA", "GS", "ES", "LK", "SD", "SS", "SR", "SJ", "SE", "CH", "SY", "TW", "TJ", "TZ", "TH", "TL", "TG", "TK", "TO", "TT", "TN", "TR", "TM", "TC", "TV", "UG", "UA", "AE", "GB", "UM", "UY", "UZ", "VU", "VA", "VE", "VN", "VG", "VI", "WF", "EH", "YE", "ZM", "ZW"]}, "ISCUSTOM": "false"}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Active-Non-Posting/Inactive", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "active non-posting", "inactive"]}, "ISCUSTOM": "false"}, {"ID": "PAYDATEVALUE", "LABEL": "Default Bill Payment Date", "DESCRIPTION": "PAYDATEVALUE", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "ONETIME", "LABEL": "One-time use", "DESCRIPTION": "One-Time", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "ONHOLD", "LABEL": "On Hold", "DESCRIPTION": "On Hold", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TIMESTAMP", "ISCUSTOM": "false"}, {"ID": "ISOWNER", "LABEL": "This vendor is an owner", "DESCRIPTION": "Owner", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "DONOTCUTCHECK", "LABEL": "Don't pay", "DESCRIPTION": "Don't pay", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "OWNER.EQGLACCOUNT", "LABEL": "Default Equity Account", "DESCRIPTION": "Equity GL Account", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "OWNER.EQGLACCOUNTLABEL", "LABEL": "Default Equity Account Label", "DESCRIPTION": "Equity GL Account", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "OWNER.HOLDDIST", "LABEL": "Hold Distribution", "DESCRIPTION": "Hold Distribution", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "OWNER.ACCOUNTLABEL.LABEL", "LABEL": "Account Label", "DESCRIPTION": "Account Label", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CURRENCY", "LABEL": "<PERSON><PERSON><PERSON>", "DESCRIPTION": "<PERSON><PERSON><PERSON>", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PYMTCOUNTRYCODE", "LABEL": "Payment country", "DESCRIPTION": "Country Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["", "AU", "ZA", "GB"]}, "ISCUSTOM": "false"}, {"ID": "FILEPAYMENTSERVICE", "LABEL": "File payment service type", "DESCRIPTION": "File payment service type", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["ACH", "BANKFILE", "NONE"]}, "ISCUSTOM": "false"}, {"ID": "ACHENABLED", "LABEL": "Enable ACH", "DESCRIPTION": "Enable ACH", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "WIREENABLED", "LABEL": "Enable Wire Transfer", "DESCRIPTION": "Enable Wire Transfer", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "CHECKENABLED", "LABEL": "Enable Check Outsourcing", "DESCRIPTION": "Enable Check Outsourcing", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "ACHBANKROUTINGNUMBER", "LABEL": "ACH Bank Routing Number", "DESCRIPTION": "ACH Bank Routing Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "ACHACCOUNTNUMBER", "LABEL": "Account Number", "DESCRIPTION": "ACH Account Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "ACHACCOUNTTYPE", "LABEL": "Account Type", "DESCRIPTION": "ACH Account Type", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Checking Account", "Savings Account"]}, "ISCUSTOM": "false"}, {"ID": "ACHREMITTANCETYPE", "LABEL": "Account classification", "DESCRIPTION": "Account classification", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["CTX", "PPD", "CCD"]}, "ISCUSTOM": "false"}, {"ID": "WIREBANKNAME", "LABEL": "Name of Bank", "DESCRIPTION": "Wire Transfer Bank Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "WIREBANKROUTINGNUMBER", "LABEL": "Bank Routing Number", "DESCRIPTION": "Wire Transfer Bank Routing Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "WIREACCOUNTNUMBER", "LABEL": "Account Number", "DESCRIPTION": "Wire Transfer Account Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "WIREACCOUNTTYPE", "LABEL": "Account Type", "DESCRIPTION": "WIRE Account Type", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": "Demand Deposit Account"}, "ISCUSTOM": "false"}, {"ID": "PMPLUSREMITTANCETYPE", "LABEL": "Remittance Delivery Type", "DESCRIPTION": "Payment Manager Plus Remittance Delivery Type", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Email", "Fax", "EDI"]}, "ISCUSTOM": "false"}, {"ID": "PMPLUSEMAIL", "LABEL": "Remittance Email Address", "DESCRIPTION": "Payment Manager Plus Remittance Email Address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "PMPLUSFAX", "LABEL": "Remittance Fax Number", "DESCRIPTION": "Payment Manager Plus Remittance Fax Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DISPLAYTERMDISCOUNT", "LABEL": "Display the term discount on the check stub", "DESCRIPTION": "Display the term discount on the check stub", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "OEPRCLSTKEY", "LABEL": "OE Price List Key", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "DISPLOCACCTNOCHECK", "LABEL": "Display the vendor-assigned account number, per entity, on the check stub", "DESCRIPTION": "Display the vendor-assigned account number, per entity, on the check stub", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TIMESTAMP", "ISCUSTOM": "false"}, {"ID": "CREATEDBY", "LABEL": "Created by", "DESCRIPTION": "User who created this.", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DESCRIPTION": "User who modified this.", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "PAYMENTNOTIFY", "LABEL": "Send Automatic Payment Notification", "DESCRIPTION": "Send Payment Notification", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "PAYMETHODKEY", "LABEL": "Preferred Payment Method", "DESCRIPTION": "Prefered Payment Method", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["", "Printed Check", "Charge Card", "EFT", "Cash", "ACH", "WF Check", "WF USD Wire", "WF Domestic ACH"]}, "ISCUSTOM": "false"}, {"ID": "OBJECTRESTRICTION", "LABEL": "", "DESCRIPTION": "", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Unrestricted", "RootOnly", "Restricted"]}, "ISCUSTOM": "false"}, {"ID": "MERGEPAYMENTREQ", "LABEL": "Merge payment requests", "DESCRIPTION": "Merge Payment Requests", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "DISPLAYCONTACTKEY", "LABEL": "Display Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "PRIMARYCONTACTKEY", "LABEL": "Primary Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "PAYTOKEY", "LABEL": "Pay-to Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "RETURNTOKEY", "LABEL": "Return-to Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "ACCOUNTLABELKEY", "LABEL": "Account Label Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "ACCOUNTKEY", "LABEL": "Account Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "VENDTYPEKEY", "LABEL": "Vendor Type Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "GLGRPKEY", "LABEL": "GL Group Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "TERMSKEY", "LABEL": "Term Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "VENDORACCTNOKEY", "LABEL": "Vendor Account Number Key", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "PAYMETHODREC", "LABEL": "Payment Method Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "OUTSOURCECHECK", "LABEL": "Enable Check Delivery Service", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "OUTSOURCECHECKSTATE", "LABEL": "Services state", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Validation Failed", "Validation Passed", "Pending Activation", "queued", "Subscribed"]}, "ISCUSTOM": "false"}, {"ID": "OUTSOURCEACH", "LABEL": "Enable American Express ACH Payment Service", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "OUTSOURCEACHSTATE", "LABEL": "Amex ACH Services status", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Active", "In Progress", "Not Enabled", "Validation Failed", "Validation Passed", "Pending Activation", "queued", "Subscribed", "Inactive"]}, "ISCUSTOM": "false"}, {"ID": "OUTSOURCECARD", "LABEL": "Enable American Express Card Payment Service", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "OUTSOURCECARDOVERRIDE", "LABEL": "Vendor Match Override", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "CARDSTATE", "LABEL": "Amex Credit Card Services state", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Active", "In Progress", "Not Enabled", "Validation Failed", "Validation Passed", "Pending Activation", "queued", "Subscribed", "Inactive"]}, "ISCUSTOM": "false"}, {"ID": "VENDORACHACCOUNTID", "LABEL": "Vendor ACH Account ID", "DESCRIPTION": "Unique ID to maintain vendor ACH account details", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "VENDORACCOUNTOUTSOURCEACH", "LABEL": "Enable Amex ACH for vendor account", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["false", "true", "Pending Activation", "queued", "Subscribed"]}, "ISCUSTOM": "false"}, {"ID": "OFFSETGLACCOUNTNO", "LABEL": "AP Account", "DESCRIPTION": "AP Account", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "OFFSETGLACCOUNTNOTITLE", "LABEL": "Default AP Account", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.CONTACTNAME", "LABEL": "1099 Contact", "DESCRIPTION": "Contact Name for 1099", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "ENUM", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.PREFIX", "LABEL": "1099 Contact - Prefix", "DESCRIPTION": "Mr./Ms./Mrs.", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.FIRSTNAME", "LABEL": "1099 Contact - First Name", "DESCRIPTION": "First Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.INITIAL", "LABEL": "1099 Contact - MI", "DESCRIPTION": "Middle Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.LASTNAME", "LABEL": "1099 Contact - Last Name", "DESCRIPTION": "Last Name", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.COMPANYNAME", "LABEL": "1099 Contact - Company Name", "DESCRIPTION": "Full name of the company", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.PRINTAS", "LABEL": "1099 Contact - Print As", "DESCRIPTION": "Name as appears on official documents", "REQUIRED": "true", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.PHONE1", "LABEL": "1099 Contact - Phone 1", "DESCRIPTION": "Primary phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.PHONE2", "LABEL": "1099 Contact - Phone 2", "DESCRIPTION": "Seconday phone", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.CELLPHONE", "LABEL": "1099 Contact - Mobile", "DESCRIPTION": "Cellular Phone Number", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.PAGER", "LABEL": "1099 Contact - Pager", "DESCRIPTION": "Pager", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.FAX", "LABEL": "1099 Contact - Fax", "DESCRIPTION": "Fax", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.EMAIL1", "LABEL": "1099 Contact - Email 1", "DESCRIPTION": "Primary email address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.EMAIL2", "LABEL": "1099 Contact - Email 2", "DESCRIPTION": "Secondary Email Address", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.URL1", "LABEL": "1099 Contact - URL 1", "DESCRIPTION": "Primary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.URL2", "LABEL": "1099 Contact - URL 2", "DESCRIPTION": "Secondary URL", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.VISIBLE", "LABEL": "1099 Primary - Contact Visible", "DESCRIPTION": "Visible", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.MAILADDRESS.ADDRESS1", "LABEL": "1099 Contact Address - Addr1", "DESCRIPTION": "Address Line 1", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.MAILADDRESS.ADDRESS2", "LABEL": "1099 Contact Address - Addr2", "DESCRIPTION": "Address Line 2", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.MAILADDRESS.CITY", "LABEL": "1099 Contact Address - City", "DESCRIPTION": "City", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.MAILADDRESS.STATE", "LABEL": "1099 Contact Address - State/Territory", "DESCRIPTION": "State/Province", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.MAILADDRESS.ZIP", "LABEL": "1099 Contact Address - Zip code/Post code", "DESCRIPTION": "Zip/Postal Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.MAILADDRESS.COUNTRY", "LABEL": "1099 Contact Address - Country", "DESCRIPTION": "Country", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "CONTACTTO1099.MAILADDRESS.COUNTRYCODE", "LABEL": "1099 Contact Address - Country Code", "DESCRIPTION": "Country Code", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["US", "AF", "AX", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG", "AR", "AM", "AW", "AU", "AT", "AZ", "BS", "BH", "BD", "BB", "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BQ", "BA", "BW", "BV", "BR", "IO", "BN", "BG", "BF", "BI", "KH", "CM", "CA", "CV", "KY", "CF", "TD", "CL", "CN", "CX", "CC", "CO", "KM", "CG", "CD", "CK", "CR", "CI", "HR", "CU", "CW", "CY", "CZ", "DK", "DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE", "SZ", "ET", "FK", "FO", "FJ", "FI", "FR", "GF", "PF", "TF", "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP", "GU", "GT", "GG", "GN", "GW", "GY", "HT", "HM", "HN", "HK", "HU", "IS", "IN", "ID", "IR", "IQ", "IE", "IM", "IL", "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI", "KR", "KP", "XK", "KW", "KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT", "LU", "MO", "MK", "MG", "MW", "MY", "MV", "ML", "MT", "MH", "MQ", "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "ME", "MS", "MA", "MZ", "MM", "NA", "NR", "NP", "NL", "AN", "NC", "NZ", "NI", "NE", "NG", "NU", "NF", "MP", "NO", "OM", "PK", "PW", "PS", "PA", "PG", "PY", "PE", "PH", "PN", "PL", "PT", "PR", "QA", "RE", "RO", "RU", "RW", "BL", "SH", "KN", "LC", "MF", "PM", "VC", "WS", "SM", "ST", "SA", "SN", "RS", "SC", "SL", "SG", "SX", "SK", "SI", "SB", "SO", "ZA", "GS", "ES", "LK", "SD", "SS", "SR", "SJ", "SE", "CH", "SY", "TW", "TJ", "TZ", "TH", "TL", "TG", "TK", "TO", "TT", "TN", "TR", "TM", "TC", "TV", "UG", "UA", "AE", "GB", "UM", "UY", "UZ", "VU", "VA", "VE", "VN", "VG", "VI", "WF", "EH", "YE", "ZM", "ZW"]}, "ISCUSTOM": "false"}, {"ID": "CONTACTKEY1099", "LABEL": "1099 Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "SUPDOCID", "LABEL": "Attachment", "DESCRIPTION": "Attachment", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "VENDOR_AMEX_ORGANIZATION_ID", "LABEL": "Organization ID generated by Amex for vendor", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "VENDOR_AMEX_ORG_ADDRESS_ID", "LABEL": "Organization address ID generated by AMEX for vendor", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "VENDOR_AMEX_CD_AFFILIATE_ID", "LABEL": "Vendor Organization Check Delivery affiliate ID", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "VENDOR_AMEX_CARD_AFFILIATE_ID", "LABEL": "Vendor Organization Corporate Card affiliate ID", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "AMEX_BANK_ACCOUNT_ID", "LABEL": "AMEX bank account ID", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "AMEX_BANK_ACCOUNT_ADDRESS_ID", "LABEL": "AMEX bank account address ID", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "DEFAULT_LEAD_TIME", "LABEL": "Lead time default (days)", "DESCRIPTION": "Lead time default (days) for Replenishment", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "RETAINAGEPERCENTAGE", "LABEL": "Default retainage percentage", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "PERCENT", "ISCUSTOM": "false"}, {"ID": "ISINDIVIDUAL", "LABEL": "This vendor is an individual person", "DESCRIPTION": "Flag vendor as an individual person with PII for security purposes when using payment services.", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "PROVIDERSTATUS", "LABEL": "Provider status", "DESCRIPTION": "Has a provider enabled for the vendor", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "false"}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "INTEGER", "ISCUSTOM": "false"}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}, {"ID": "XTREEM_VENDOR", "LABEL": "XTreeM", "DESCRIPTION": "No description specified", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": "true"}, {"ID": "XTREEM_ID", "LABEL": "XTreeM_id", "DESCRIPTION": "Instance _id in XTreeM", "REQUIRED": "false", "READONLY": "false", "DATATYPE": "DECIMAL", "ISCUSTOM": "true"}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": "false", "READONLY": "true", "DATATYPE": "TEXT", "ISCUSTOM": "false"}]}, "Relationships": {"Relationship": [{"OBJECTPATH": "PARENT", "OBJECTNAME": "VENDOR", "LABEL": "<PERSON><PERSON>", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PARENTID"}, {"OBJECTPATH": "PAYTOCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Pay to Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PAYTO.CONTACTNAME"}, {"OBJECTPATH": "RETURNTOCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Return to Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "RETURNTO.CONTACTNAME"}, {"OBJECTPATH": "CONTACT1099", "OBJECTNAME": "CONTACT", "LABEL": "1099 Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CONTACTTO1099.CONTACTNAME"}, {"OBJECTPATH": "DISPLAYCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "DISPLAYCONTACT.CONTACTNAME"}, {"OBJECTPATH": "PRIMARYCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Primary Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CONTACTINFO.CONTACTNAME"}, {"OBJECTPATH": "APACCOUNTLABEL", "OBJECTNAME": "APACCOUNTLABEL", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "ACCOUNTLABEL"}, {"OBJECTPATH": "APACCOUNT", "OBJECTNAME": "GLACCOUNT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "APACCOUNT"}, {"OBJECTPATH": "TERM", "OBJECTNAME": "APTERM", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "TERMNAME"}, {"OBJECTPATH": "GLGROUP", "OBJECTNAME": "VENDGLGROUP", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "GLGROUP"}, {"OBJECTPATH": "PRICELIST", "OBJECTNAME": "INVPRICELIST", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PRICESCHEDULE"}, {"OBJECTPATH": "OFFSETGLACCOUNTNO", "OBJECTNAME": "GLACCOUNT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "OFFSETGLACCOUNTNO"}, {"OBJECTPATH": "VENDTYPE", "OBJECTNAME": "VENDTYPE", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "VENDTYPE"}, {"OBJECTPATH": "BANKFILE", "OBJECTNAME": "VENDORBANKFILEDETAIL", "LABEL": "Bank File", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "VENDORKEY"}, {"OBJECTPATH": "PROVIDERVENDOR", "OBJECTNAME": "PROVIDERVENDOR", "LABEL": "Provider <PERSON>", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PROVIDERVENDOR.VENDORKEY"}, {"OBJECTPATH": "MELOCATION", "OBJECTNAME": "LOCATION", "LABEL": "Created At Entity Information", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MEGAENTITYID"}]}}]}