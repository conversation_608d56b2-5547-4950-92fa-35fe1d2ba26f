{"_status": "success", "_functionName": "inspect", "_controlId": "1550fa2f-d07c-4025-828e-c99753c2bfda", "_listType": "All", "_count": 1, "_data": [{"$": {"typename": "region"}, "$value": "Region"}, {"$": {"typename": "xtreem_audit_trail"}, "$value": "XTreeM_Audit_Trail"}, {"$": {"typename": "APADJUSTMENT"}, "$value": "AP Adjustment"}, {"$": {"typename": "APADJUSTMENTITEM"}, "$value": "AP Adjustment Detail"}, {"$": {"typename": "APBILL"}, "$value": "AP Bill"}, {"$": {"typename": "APBILLITEM"}, "$value": "AP Bill <PERSON>"}, {"$": {"typename": "APBILLPAYMENT"}, "$value": "AP Bill Payment"}, {"$": {"typename": "APPAYMENT"}, "$value": "AP Payment"}, {"$": {"typename": "APPAYMENTITEM"}, "$value": "AP Payment Detail"}, {"$": {"typename": "ARADJUSTMENT"}, "$value": "AR Adjustment"}, {"$": {"typename": "ARADJUSTMENTITEM"}, "$value": "AR Adjustment Detail"}, {"$": {"typename": "ARINVOICE"}, "$value": "AR Invoice"}, {"$": {"typename": "ARINVOICEITEM"}, "$value": "AR Invoice Detail"}, {"$": {"typename": "ARINVOICEPAYMENT"}, "$value": "AR Invoice Payment"}, {"$": {"typename": "ARPAYMENT"}, "$value": "AR Payment"}, {"$": {"typename": "ARPAYMENTITEM"}, "$value": "AR Payment Detail"}, {"$": {"typename": "CLASS"}, "$value": "Class"}, {"$": {"typename": "CNSACCOUNT"}, "$value": "Consolidation Account"}, {"$": {"typename": "CNSACCTBAL"}, "$value": "Consolidation Account <PERSON>"}, {"$": {"typename": "CNSPERIOD"}, "$value": "Consolidation Period"}, {"$": {"typename": "COMPANY"}, "$value": "Company Information"}, {"$": {"typename": "COMPANYPREF"}, "$value": "Company Preference"}, {"$": {"typename": "CUSTOMER"}, "$value": "Customer"}, {"$": {"typename": "DEPARTMENT"}, "$value": "Department"}, {"$": {"typename": "EEXPENSES"}, "$value": "Employee Expense"}, {"$": {"typename": "EEXPENSESITEM"}, "$value": "Employee Expense Detail"}, {"$": {"typename": "EEXPENSESPAYMENT"}, "$value": "Employee Expenses Payment"}, {"$": {"typename": "EMPLOYEE"}, "$value": "Employee"}, {"$": {"typename": "EPPAYMENT"}, "$value": "Employee Expense Reimbursement"}, {"$": {"typename": "EPPAYMENTITEM"}, "$value": "Employee Payment Detail"}, {"$": {"typename": "EXCHANGERATE"}, "$value": "Exchange Rate"}, {"$": {"typename": "EXCHANGERATEENTRY"}, "$value": "Exchange Rate Entry"}, {"$": {"typename": "GLACCOUNT"}, "$value": "GL Account"}, {"$": {"typename": "GLBATCH"}, "$value": "GL Batch"}, {"$": {"typename": "GLENTRY"}, "$value": "GL Entry"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inventory Document"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inventory Document Detail"}, {"$": {"typename": "ITEM"}, "$value": "<PERSON><PERSON>"}, {"$": {"typename": "LOCATION"}, "$value": "Location"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Purchasing Document"}, {"$": {"typename": "PODOCUMENTAPPROVAL"}, "$value": "Purchasing Approval History"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Purchasing Document Detail"}, {"$": {"typename": "PROJECT"}, "$value": "Project"}, {"$": {"typename": "RENEWALMACRO"}, "$value": "Renewal Template"}, {"$": {"typename": "REVRECSCHEDULE"}, "$value": "Revenue Recognition Schedule"}, {"$": {"typename": "REVRECSCHEDULEENTRY"}, "$value": "Revenue Recognition Schedule Entry"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales document"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Document Detail"}, {"$": {"typename": "SUBSIDIARY"}, "$value": "Subsidiary"}, {"$": {"typename": "TRXCURRENCIES"}, "$value": "Currency And Format Setup"}, {"$": {"typename": "USERINFO"}, "$value": "User"}, {"$": {"typename": "VENDOR"}, "$value": "<PERSON><PERSON><PERSON>"}, {"$": {"typename": "WAREHOUSE"}, "$value": "Warehouse"}, {"$": {"typename": "WFPMBATCH"}, "$value": "Wells Fargo Payment Manager Summary"}, {"$": {"typename": "WFPMPAYMENTREQUEST"}, "$value": "Wells Fargo Payment Request"}, {"$": {"typename": "VENDTYPE"}, "$value": "Vendor Type"}, {"$": {"typename": "TASK"}, "$value": "Task"}, {"$": {"typename": "TIMESHEET"}, "$value": "Timesheet"}, {"$": {"typename": "TIMESHEETENTRY"}, "$value": "Timesheet Entry"}, {"$": {"typename": "APTERM"}, "$value": "AP Term"}, {"$": {"typename": "ARTERM"}, "$value": "AR Term"}, {"$": {"typename": "ARPAYMENTBATCH"}, "$value": "AR Payment Summary"}, {"$": {"typename": "GLJOURNAL"}, "$value": "GL Journal"}, {"$": {"typename": "ALLOCATION"}, "$value": "Allocation"}, {"$": {"typename": "APBILLBATCH"}, "$value": "AP Bill <PERSON>"}, {"$": {"typename": "ARINVOICEBATCH"}, "$value": "AR Invoice Summary"}, {"$": {"typename": "ARACCOUNTLABEL"}, "$value": "AR Account Label"}, {"$": {"typename": "APACCOUNTLABEL"}, "$value": "AP Account Label"}, {"$": {"typename": "CONTACT"}, "$value": "Contact"}, {"$": {"typename": "CREDITCARD"}, "$value": "Creditcard"}, {"$": {"typename": "CHECKINGACCOUNT"}, "$value": "Checking Account"}, {"$": {"typename": "SAVINGSACCOUNT"}, "$value": "Bank Account"}, {"$": {"typename": "BANKACCOUNT"}, "$value": "Bank Account"}, {"$": {"typename": "STATJOURNAL"}, "$value": "Statistical Journal"}, {"$": {"typename": "APPAYMENTREQUEST"}, "$value": "AP Payment Request"}, {"$": {"typename": "APRECURBILL"}, "$value": "AP Recurring Bill"}, {"$": {"typename": "BILLABLEEXPENSES"}, "$value": "Billable Expense"}, {"$": {"typename": "CHECKLAYOUT"}, "$value": "Check Layout"}, {"$": {"typename": "RECURDOCUMENTENTRY"}, "$value": "Recurring document entry"}, {"$": {"typename": "EPPAYMENTREQUEST"}, "$value": "Pending Reimbursement"}, {"$": {"typename": "EXCHANGERATETYPES"}, "$value": "Exchange Rate Type"}, {"$": {"typename": "EXPENSESAPPROVAL"}, "$value": "Expense <PERSON>"}, {"$": {"typename": "FINANCIALACCOUNT"}, "$value": "Financial Account"}, {"$": {"typename": "GAAPADJJRNL"}, "$value": "GAAP Adjustment Journal"}, {"$": {"typename": "GLDETAIL"}, "$value": "General <PERSON><PERSON>"}, {"$": {"typename": "IERELATION"}, "$value": "Inter Entity Relationship"}, {"$": {"typename": "INVOICERUN"}, "$value": "Invoice Run"}, {"$": {"typename": "INVRECURDOCUMENT"}, "$value": "Recurring Inventory Transaction"}, {"$": {"typename": "LOCATIONENTITY"}, "$value": "Entity"}, {"$": {"typename": "PORECURDOCUMENT"}, "$value": "Recurring Purchasing Transaction"}, {"$": {"typename": "PROJECTRESOURCES"}, "$value": "Project Resource"}, {"$": {"typename": "PROJECTSTATUS"}, "$value": "Project Status"}, {"$": {"typename": "PROJECTTYPE"}, "$value": "Project Type"}, {"$": {"typename": "RECURGLBATCH"}, "$value": "Recurring Journal Entry"}, {"$": {"typename": "RECURGLENTRY"}, "$value": "Recurring Journal Entry Details"}, {"$": {"typename": "REVRECCHANGELOG"}, "$value": "Revenue Recognition Change History"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Recurring Order Entry Transaction"}, {"$": {"typename": "STKITDOCUMENT"}, "$value": "Build/Disassemble Kits Transaction"}, {"$": {"typename": "STKITDOCUMENTENTRY"}, "$value": "Build/Disassemble Kits Transaction Detail"}, {"$": {"typename": "TASKRESOURCES"}, "$value": "Task Resource"}, {"$": {"typename": "TAXADJJRNL"}, "$value": "Tax Adjustment Journal"}, {"$": {"typename": "TIMESHEETAPPROVAL"}, "$value": "Timesheet Approval History"}, {"$": {"typename": "TIMETYPE"}, "$value": "Time Type"}, {"$": {"typename": "BUDGETHEADER"}, "$value": "Budget"}, {"$": {"typename": "GLBUDGET"}, "$value": "GL Budget"}, {"$": {"typename": "REPORTINGPERIOD"}, "$value": "Reporting Period"}, {"$": {"typename": "STATACCOUNT"}, "$value": "Statistical Account"}, {"$": {"typename": "ACCTTITLEBYLOC"}, "$value": "Account Title By Entity"}, {"$": {"typename": "REVRECTEMPLATE"}, "$value": "Revenue Recognition Template"}, {"$": {"typename": "REVRECTEMPLENTRY"}, "$value": "Revenue Recognition Template Entry"}, {"$": {"typename": "REVRECTEMPLMILESTONE"}, "$value": "Revenue Recognition Template Entry"}, {"$": {"typename": "REVRECSCHEDULEENTRYTASK"}, "$value": "Revenue Recognition Schedule Entry Task Detail"}, {"$": {"typename": "EARNINGTYPE"}, "$value": "Earning Type"}, {"$": {"typename": "EMPLOYEERATE"}, "$value": "Employee Rate"}, {"$": {"typename": "AUDUSERTRAIL"}, "$value": "User Permissions Activity"}, {"$": {"typename": "ACTIVITYLOG"}, "$value": "Activity Trail"}, {"$": {"typename": "COMMENTS"}, "$value": "Comment"}, {"$": {"typename": "GLACCOUNTBALANCE"}, "$value": "GL Account Balance"}, {"$": {"typename": "BILLINGTEMPLATE"}, "$value": "Billing Template"}, {"$": {"typename": "BILLINGSCHEDULE"}, "$value": "Billing Schedule"}, {"$": {"typename": "JOURNAL"}, "$value": "Journal"}, {"$": {"typename": "OPENBOOKS"}, "$value": "Open book log"}, {"$": {"typename": "CLOSEBOOKS"}, "$value": "Close book log"}, {"$": {"typename": "UOM"}, "$value": "Unit Of Measure"}, {"$": {"typename": "UOMDETAIL"}, "$value": "Unit Of Measure Detail"}, {"$": {"typename": "INVPRICELIST"}, "$value": "Price List"}, {"$": {"typename": "INVPRICELISTENTRY"}, "$value": "Price List Entry"}, {"$": {"typename": "SOPRICELIST"}, "$value": "SO Price List"}, {"$": {"typename": "SOPRICELISTENTRY"}, "$value": "SO Price List Entry"}, {"$": {"typename": "POPRICELIST"}, "$value": "PO Price List"}, {"$": {"typename": "POPRICELISTENTRY"}, "$value": "PO Price List Entry"}, {"$": {"typename": "GLACCTGRP"}, "$value": "GL Account Group"}, {"$": {"typename": "ACCTRANGE"}, "$value": "GL Account Group Range"}, {"$": {"typename": "GLACCTGRPMEMBER"}, "$value": "GL Account Group Member"}, {"$": {"typename": "GLCOMPGRPMEMBER"}, "$value": "GL Computation Group Member"}, {"$": {"typename": "GLCOACATMEMBER"}, "$value": "GL Account Category"}, {"$": {"typename": "ALLOCATIONENTRY"}, "$value": "Allocation Entry"}, {"$": {"typename": "EMPLOYEETYPE"}, "$value": "Employee Type"}, {"$": {"typename": "EMPLOYEEENTITYCONTACTS"}, "$value": "Employee Entity Contact"}, {"$": {"typename": "GLRESOLVE"}, "$value": "GL Entry Resolve"}, {"$": {"typename": "ACHBANK"}, "$value": "ACH Bank"}, {"$": {"typename": "AISLE"}, "$value": "Aisle"}, {"$": {"typename": "BIN"}, "$value": "Bin"}, {"$": {"typename": "ICROW"}, "$value": "Row"}, {"$": {"typename": "PRODUCTLINE"}, "$value": "Product Line"}, {"$": {"typename": "ITEMVENDOR"}, "$value": "Item/Vendor Info"}, {"$": {"typename": "ITEMWAREHOUSEINFO"}, "$value": "Item/Warehouse Info"}, {"$": {"typename": "ITEMCOMPONENT"}, "$value": "Kit Components"}, {"$": {"typename": "GLACCTGRPHIERARCHY"}, "$value": "Account Groups Hierarchy"}, {"$": {"typename": "TAXGROUP"}, "$value": "Contact Tax Group"}, {"$": {"typename": "ITEMTAXGROUP"}, "$value": "Item Tax Group"}, {"$": {"typename": "ACCTLABELTAXGROUP"}, "$value": "AR Account Label Tax Group"}, {"$": {"typename": "LOCATIONGROUP"}, "$value": "Location Group"}, {"$": {"typename": "DEPARTMENTGROUP"}, "$value": "Department Group"}, {"$": {"typename": "VENDORGROUP"}, "$value": "Vendor Group"}, {"$": {"typename": "CUSTOMERGROUP"}, "$value": "Customer Group"}, {"$": {"typename": "PROJECTGROUP"}, "$value": "Project Group"}, {"$": {"typename": "EMPLOYEEGROUP"}, "$value": "Employee Group"}, {"$": {"typename": "CLASSGROUP"}, "$value": "Class Group"}, {"$": {"typename": "ITEMGROUP"}, "$value": "Item Group"}, {"$": {"typename": "EEACCOUNTLABEL"}, "$value": "Expense Type"}, {"$": {"typename": "USERROLES"}, "$value": "User Role"}, {"$": {"typename": "KITCOSTING"}, "$value": "Kit <PERSON>sting"}, {"$": {"typename": "CUSTOMERVISIBILITY"}, "$value": "Customer Visibility"}, {"$": {"typename": "VENDORVISIBILITY"}, "$value": "Vendor Visibility"}, {"$": {"typename": "CUSTTYPE"}, "$value": "Customer Type"}, {"$": {"typename": "TERRITORYGROUP"}, "$value": "Territory Group"}, {"$": {"typename": "TERRITORYGRPMEMBER"}, "$value": "Territory Group Member"}, {"$": {"typename": "GLDOCDETAIL"}, "$value": "General Ledger Document Detail"}, {"$": {"typename": "POSITIONSKILL"}, "$value": "Positions and Skills"}, {"$": {"typename": "EMPLOYEEPOSITIONSKILL"}, "$value": "Employee Positions and Skills"}, {"$": {"typename": "OUTOFOFFICE"}, "$value": "Out of Office"}, {"$": {"typename": "EMPLOYEEOUTOFOFFICE"}, "$value": "Employee out of Office"}, {"$": {"typename": "PROJECTTOTALS"}, "$value": "Project Totals"}, {"$": {"typename": "PRTAXENTRY"}, "$value": "Prtaxentry"}, {"$": {"typename": "TRANSTMPLENTRY"}, "$value": "Line Items"}, {"$": {"typename": "INVDOCUMENTSUBTOTALS"}, "$value": "Document Sub Totals"}, {"$": {"typename": "PODOCUMENTSUBTOTALS"}, "$value": "Purchasing Document Subtotals"}, {"$": {"typename": "SODOCUMENTSUBTOTALS"}, "$value": "Order Entry Transaction Subtotals"}, {"$": {"typename": "INVRECURSUBTOTALS"}, "$value": "INV Recurring Sub Totals"}, {"$": {"typename": "PORECURSUBTOTALS"}, "$value": "PO Recurring Sub Totals"}, {"$": {"typename": "SORECURSUBTOTALS"}, "$value": "SO Recurring Sub Totals"}, {"$": {"typename": "APRECURBILLENTRY"}, "$value": "Recurring AP <PERSON>"}, {"$": {"typename": "TRANSACTIONRULE"}, "$value": "Transaction Rule"}, {"$": {"typename": "TRANSACTIONRULEDETAIL"}, "$value": "Transaction Rule Detail"}, {"$": {"typename": "PROJECTTRANSACTIONRULE"}, "$value": "Project Transaction Rule"}, {"$": {"typename": "EXPENSEPAYMENTTYPE"}, "$value": "Expense Payment Type"}, {"$": {"typename": "POAPPROVALRULE"}, "$value": "Purchasing Approval Rule"}, {"$": {"typename": "POAPPROVALRULEDETAIL"}, "$value": "Purchasing Approval Rule Details"}, {"$": {"typename": "POAPPROVALPOLICY"}, "$value": "Purchasing Approval Policy"}, {"$": {"typename": "POAPPROVALPOLICYDETAIL"}, "$value": "Purchasing Approval Policy Details"}, {"$": {"typename": "POAPPROVALDELEGATE"}, "$value": "Approval Delegate"}, {"$": {"typename": "POAPPROVALDELEGATEDETAIL"}, "$value": "Manage Delegates"}, {"$": {"typename": "POAPPROVALRULESET"}, "$value": "Value approval rule set"}, {"$": {"typename": "SOSUBTOTALTEMPLATE"}, "$value": "Order Entry Subtotal Template"}, {"$": {"typename": "SOSUBTOTALTEMPLATEDETAIL"}, "$value": "SubTotal Template Detail"}, {"$": {"typename": "POSUBTOTALTEMPLATE"}, "$value": "Purchasing Subtotal Template"}, {"$": {"typename": "POSUBTOTALTEMPLATEDETAIL"}, "$value": "SubTotal Template Detail"}, {"$": {"typename": "APAPPROVALRULE"}, "$value": "AP Approval Rule"}, {"$": {"typename": "APAPPROVALPOLICY"}, "$value": "AP Approval Policy"}, {"$": {"typename": "APAPPROVALRULESET"}, "$value": "Value approval rule set"}, {"$": {"typename": "INTERENTITYSETUP"}, "$value": "Inter Entity Setup"}, {"$": {"typename": "ENTITYACCTDEFAULT"}, "$value": "Entityacctdefault"}, {"$": {"typename": "ENTITYACCTOVERRIDE"}, "$value": "Inter Entity Relationship"}, {"$": {"typename": "ITEMCROSSREF"}, "$value": "Item Cross Reference"}, {"$": {"typename": "ARRECURINVOICEENTRY"}, "$value": "Recurring AR Invoice Detail"}, {"$": {"typename": "APADVANCE"}, "$value": "AP Advance"}, {"$": {"typename": "APADVANCEITEM"}, "$value": "AP Advance Detail"}, {"$": {"typename": "ARADVANCE"}, "$value": "AR Advance"}, {"$": {"typename": "ARADVANCEITEM"}, "$value": "AR Advance Detail"}, {"$": {"typename": "ARRECURINVOICE"}, "$value": "Recurring AR Invoice"}, {"$": {"typename": "EXPENSEADJUSTMENTS"}, "$value": "Expense Adjustments"}, {"$": {"typename": "EXPENSEADJUSTMENTSITEM"}, "$value": "Expense Adjustments Detail"}, {"$": {"typename": "TRANSTMPLBATCH"}, "$value": "Transaction Template"}, {"$": {"typename": "PRENTRY"}, "$value": "PR Entry"}, {"$": {"typename": "CCTRANSACTION"}, "$value": "Credit Card Transaction"}, {"$": {"typename": "CCTRANSACTIONENTRY"}, "$value": "Credit Card Transaction Entry"}, {"$": {"typename": "OTHERRECEIPTS"}, "$value": "Other Receipts"}, {"$": {"typename": "OTHERRECEIPTSENTRY"}, "$value": "Other Receipts Entry"}, {"$": {"typename": "CREDITCARDFEE"}, "$value": "Credit Card Charges and Other Fees"}, {"$": {"typename": "CREDITCARDFEEENTRY"}, "$value": "Credit Card Charges and Other Fees Entry"}, {"$": {"typename": "BANKFEE"}, "$value": "Bank Interest and Charges"}, {"$": {"typename": "BANKFEEENTRY"}, "$value": "Bank Interest and Charges Entry"}, {"$": {"typename": "FUNDSTRANSFER"}, "$value": "Funds Transfer"}, {"$": {"typename": "FUNDSTRANSFERENTRY"}, "$value": "Funds Transfer Entry"}, {"$": {"typename": "CHARGEPAYOFF"}, "$value": "Charge Payoffs"}, {"$": {"typename": "CHARGEPAYOFFENTRY"}, "$value": "Charge Payoffs Details"}, {"$": {"typename": "DEPOSIT"}, "$value": "Deposits"}, {"$": {"typename": "DEPOSITENTRY"}, "$value": "Deposits Details"}, {"$": {"typename": "ARRECORD"}, "$value": "AR Record"}, {"$": {"typename": "ARDETAIL"}, "$value": "AR Detail"}, {"$": {"typename": "USERADJBOOK"}, "$value": "User-Defined Book"}, {"$": {"typename": "USERADJJRNL"}, "$value": "User-Defined Journal"}, {"$": {"typename": "DDSJOB"}, "$value": "Data Delivery Service Job"}, {"$": {"typename": "APIUSAGEDETAIL"}, "$value": "API Usage Detail"}, {"$": {"typename": "REPORTINGACHEADER"}, "$value": "Reporting Accounts"}, {"$": {"typename": "REPORTINGAC"}, "$value": "Reporting Accounts"}, {"$": {"typename": "VENDAGING"}, "$value": "Vendor Aging Report"}, {"$": {"typename": "CUSTAGING"}, "$value": "Customer Aging Report"}, {"$": {"typename": "EMAILTEMPLATE"}, "$value": "<PERSON>ail Te<PERSON>late"}, {"$": {"typename": "APRECORD"}, "$value": "AP Record"}, {"$": {"typename": "APDETAIL"}, "$value": "AP Detail"}, {"$": {"typename": "CMRECORD"}, "$value": "CM Record"}, {"$": {"typename": "CMDETAIL"}, "$value": "CM Detail"}, {"$": {"typename": "EERECORD"}, "$value": "EE Record"}, {"$": {"typename": "EEDETAIL"}, "$value": "EE Detail"}, {"$": {"typename": "USERRIGHTS"}, "$value": "User Permissions"}, {"$": {"typename": "GLBUDGETHEADER"}, "$value": "Budget"}, {"$": {"typename": "GLBUDGETITEM"}, "$value": "GL Budget"}, {"$": {"typename": "BILLBACKTEMPLATE"}, "$value": "Bill<PERSON>ack Template"}, {"$": {"typename": "CUSTOMERENTITYCONTACTS"}, "$value": "Customer Entity Contacts"}, {"$": {"typename": "DOCRECALLS"}, "$value": "Docrecalls"}, {"$": {"typename": "DOCUMENTPARINVGL"}, "$value": "Inventory GL Definitions"}, {"$": {"typename": "DOCUMENTPARPRGL"}, "$value": "GL Definitions"}, {"$": {"typename": "DOCUMENTPARSUBTOTAL"}, "$value": "Document Params SubTotal"}, {"$": {"typename": "INVDOCUMENTPARTOTALS"}, "$value": "Document Paramaters Total"}, {"$": {"typename": "INITOPENITEMS"}, "$value": "Initopenitems"}, {"$": {"typename": "INVDOCUMENTPARAMS"}, "$value": "Inventory Transaction Definition"}, {"$": {"typename": "ITEMGLGROUP"}, "$value": "Item GL Group"}, {"$": {"typename": "PODOCUMENTPARAMS"}, "$value": "Purchase Transaction Definition"}, {"$": {"typename": "PARTNERFIELDMAP"}, "$value": "Partnerfieldmap"}, {"$": {"typename": "ROLEUSERS"}, "$value": "Role users"}, {"$": {"typename": "ROLES"}, "$value": "Roles"}, {"$": {"typename": "SODOCUMENTPARAMS"}, "$value": "SO Transaction Definition"}, {"$": {"typename": "SUMMARYBYENTITY"}, "$value": "Summarybyentity"}, {"$": {"typename": "TAXDETAIL"}, "$value": "Tax Detail"}, {"$": {"typename": "USERGROUP"}, "$value": "User Group"}, {"$": {"typename": "VENDORACCTNOLOCHEAD"}, "$value": "<PERSON><PERSON><PERSON> Account Numbers"}, {"$": {"typename": "VENDORENTITYCONTACTS"}, "$value": "Vendor Entity Contacts"}, {"$": {"typename": "APIUSAGESUMMARY"}, "$value": "API Usage Summary"}, {"$": {"typename": "APPYMT"}, "$value": "AP Payables Payment"}, {"$": {"typename": "APPYMTENTRY"}, "$value": "New AP Payment Line Detail"}, {"$": {"typename": "CONTRACT"}, "$value": "Contract"}, {"$": {"typename": "CONTRACTDETAIL"}, "$value": "Contract Line"}, {"$": {"typename": "CONTRACTREVENUETEMPLATE"}, "$value": "Revenue Template"}, {"$": {"typename": "CONTRACTBILLINGTEMPLATE"}, "$value": "Contract Billing Template"}, {"$": {"typename": "CONTRACTBILLINGTEMPLATEENTRY"}, "$value": "Contract Billing Template Entry"}, {"$": {"typename": "CONTRACTREVENUESCHEDULE"}, "$value": "Contract Revenue Schedule 1"}, {"$": {"typename": "CONTRACTREVENUE2SCHEDULE"}, "$value": "Contract Revenue Schedule 2"}, {"$": {"typename": "CONTRACTREVENUESCHEDULEENTRY"}, "$value": "Contract Revenue Schedule Entry"}, {"$": {"typename": "CONTRACTBILLINGSCHEDULE"}, "$value": "Contract Billing Schedule"}, {"$": {"typename": "CONTRACTBILLINGSCHEDULEENTRY"}, "$value": "Contract Billing Schedule Entry"}, {"$": {"typename": "CONTRACTREVENUEGLCONFIG"}, "$value": "Contract Posting Configuration - Revenue"}, {"$": {"typename": "CONTRACTEXPENSEGLCONFIG"}, "$value": "Contract Posting Configuration - Expense"}, {"$": {"typename": "CONTRACTEXPENSETEMPLATE"}, "$value": "Contract Expense Template"}, {"$": {"typename": "CONTRACTEXPENSE"}, "$value": "Contract Expense"}, {"$": {"typename": "CONTRACTEXPENSESCHEDULE"}, "$value": "Contract Expense Schedule 1"}, {"$": {"typename": "CONTRACTEXPENSESCHEDULEENTRY"}, "$value": "Contract Expense Schedule Entry"}, {"$": {"typename": "CONTRACTUSAGE"}, "$value": "Contract Usage Data"}, {"$": {"typename": "CONTRACTSCHFORECAST"}, "$value": "Contract Schedule Forecast"}, {"$": {"typename": "CONTRACTMEAPRICELIST"}, "$value": "MEA Price List"}, {"$": {"typename": "CONTRACTMEAITEMPRICELIST"}, "$value": "MEA Price List Entry"}, {"$": {"typename": "CONTRACTMEAITEMPRICELISTENTRY"}, "$value": "MEA Price List Entry Detail"}, {"$": {"typename": "CONTRACTPRICELIST"}, "$value": "Billing Price List"}, {"$": {"typename": "CONTRACTITEMPRICELIST"}, "$value": "Billing Price List Entry"}, {"$": {"typename": "CONTRACTITEMPRICELISTENTRY"}, "$value": "Billing Price List Entry Detail"}, {"$": {"typename": "CONTRACTUSAGEBILLING"}, "$value": "Contract Usage Billing"}, {"$": {"typename": "CUSTOMEREMAILTEMPLATE"}, "$value": "Customer <PERSON><PERSON>"}, {"$": {"typename": "CONTRACTEXPENSE2SCHEDULE"}, "$value": "Contract Expense Schedule 2"}, {"$": {"typename": "CONTRACTITEMPRCLSTENTYTIER"}, "$value": "Billing Price List Entry Detail Tier"}, {"$": {"typename": "CONTRACTCOMPLIANCETASKITEM"}, "$value": "Contract Compliance Task Item"}, {"$": {"typename": "CONTRACTCOMPLIANCETASK"}, "$value": "Contract Compliance Checklist"}, {"$": {"typename": "NOTE"}, "$value": "Note"}, {"$": {"typename": "CONTRACTCOMPLIANCENOTE"}, "$value": "Contract Compliance Note"}, {"$": {"typename": "CONTRACTRESOLVE"}, "$value": "Contract Subledger Links"}, {"$": {"typename": "CONTRACTMEABUNDLE"}, "$value": "Contract MEA Allocation Scheme"}, {"$": {"typename": "CONTRACTMEABUNDLEENTRY"}, "$value": "Contract MEA Bundle Entry"}, {"$": {"typename": "WAREHOUSEGROUP"}, "$value": "Warehouse Group"}, {"$": {"typename": "CONTRACTGROUP"}, "$value": "Contract Group"}, {"$": {"typename": "DROPSHIPHISTORY"}, "$value": "Drop Ship History"}, {"$": {"typename": "APPYMTDETAIL"}, "$value": "AP Payables Payment Details"}, {"$": {"typename": "ROLEGROUPS"}, "$value": "Role groups"}, {"$": {"typename": "ROLEPOLICYASSIGNMENT"}, "$value": "Role Policy Assignment"}, {"$": {"typename": "MEMBERUSERGROUP"}, "$value": "Member User Groups"}, {"$": {"typename": "CUSTOMROLEPOLASSIGNMENT"}, "$value": "Custom Role Policy Assignment"}, {"$": {"typename": "ROLEASSIGNMENT"}, "$value": "Role assignments"}, {"$": {"typename": "CONTRACTALLOCATIONFORBUNDLE"}, "$value": "Contract MEA Allocation Details"}, {"$": {"typename": "CONTRACTALLOCATIONDETAIL"}, "$value": "Contract MEA Allocation Details"}, {"$": {"typename": "CONTRACTMRRRESOLVE"}, "$value": "Contract MRR links"}, {"$": {"typename": "RENEWALPRICINGOVERRIDE"}, "$value": "Custom Renewal Amounts"}, {"$": {"typename": "AUDITHISTORY"}, "$value": "Audit History"}, {"$": {"typename": "CONTRACTNEGATIVEBILLING"}, "$value": "Contract Negative Billing"}, {"$": {"typename": "CONTRACTNEGATIVEBILLINGENTRY"}, "$value": "Contract Negative Billing Entry"}, {"$": {"typename": "GENINVOICEPREBILL"}, "$value": "Generate Invoices Preview Snapshot Run"}, {"$": {"typename": "GENINVOICEPREBILLHEADER"}, "$value": "Generate Invoices Preview Snapshot Invoice"}, {"$": {"typename": "GENINVOICEPREBILLLINE"}, "$value": "Generate Invoices Preview Snapshot Line"}, {"$": {"typename": "GENINVOICEPREVIEW"}, "$value": "Generate Invoices"}, {"$": {"typename": "GENINVOICEPREVIEWHEADER"}, "$value": "Generate Invoices Preview Header"}, {"$": {"typename": "GENINVOICEPREVIEWLINE"}, "$value": "Generate Invoices Preview Line"}, {"$": {"typename": "GENINVOICERUN"}, "$value": "Generate Invoices Run"}, {"$": {"typename": "GENINVOICEFILTERS"}, "$value": "Generate Invoice Filter Set"}, {"$": {"typename": "CONTRACTREVENUETEMPLATEENTRY"}, "$value": "Contract Revenue Template Entry"}, {"$": {"typename": "ICTRANSFER"}, "$value": "Warehouse Transfer"}, {"$": {"typename": "ICTRANSFERITEM"}, "$value": "Warehouse Transfer Items"}, {"$": {"typename": "DOCUMENTENTRYTRACKDETAIL"}, "$value": "Document Entry Tracking Details"}, {"$": {"typename": "SCITEMGLGROUP"}, "$value": "Scitemglgroup"}, {"$": {"typename": "SCPURCHASINGDOC"}, "$value": "Scpurchasingdoc"}, {"$": {"typename": "OBSPCTCOMPLETED"}, "$value": "Observed Percent Completed"}, {"$": {"typename": "USERRESTRICTION"}, "$value": "User Restriction"}, {"$": {"typename": "COSTHISTORY"}, "$value": "Cost history"}, {"$": {"typename": "INVHLTHRUN"}, "$value": "Maintain Inventory Valuation"}, {"$": {"typename": "MEACATEGORY"}, "$value": "MEA Fair Value Category"}, {"$": {"typename": "ADVAUDITHISTORY"}, "$value": "Advanced Audit History"}, {"$": {"typename": "JOBQUEUERECORD"}, "$value": "Offline job queue"}, {"$": {"typename": "BANKACCTRECON"}, "$value": "Bank reconciliation"}, {"$": {"typename": "LANDEDCOSTHISTORY"}, "$value": "Landed cost history"}, {"$": {"typename": "REPLENISHMENT"}, "$value": "Replenishment Report"}, {"$": {"typename": "COGSCLOSEDJE"}, "$value": "COGS Closed JE"}, {"$": {"typename": "MGLACCOUNTBALANCE"}, "$value": "MGL Account Balance"}, {"$": {"typename": "GLACCTALLOCATION"}, "$value": "GL Account Allocation"}, {"$": {"typename": "GLACCTALLOCATIONSOURCE"}, "$value": "GL Account Allocation Source"}, {"$": {"typename": "GLACCTALLOCATIONBASIS"}, "$value": "GL Account Allocation Basis"}, {"$": {"typename": "GLACCTALLOCATIONTARGET"}, "$value": "GL Account Allocation Target"}, {"$": {"typename": "GLACCTALLOCATIONREVERSE"}, "$value": "GL Account Allocation Reverse"}, {"$": {"typename": "GLACCTALLOCATIONRUN"}, "$value": "Allocation log"}, {"$": {"typename": "GLACCTALLOCATIONGRP"}, "$value": "Account Allocation Groups"}, {"$": {"typename": "GLACCTALLOCATIONGRPMEMBER"}, "$value": "Account Allocation Group Member"}, {"$": {"typename": "GLACCTALLOCATIONSOURCEADJBOOKS"}, "$value": "Glacctallocationsourceadjbooks"}, {"$": {"typename": "GLACCTALLOCATIONBASISADJBOOKS"}, "$value": "Glacctallocationbasisadjbooks"}, {"$": {"typename": "JOURNALSEQNUM"}, "$value": "Accounting Sequence"}, {"$": {"typename": "JOURNALSEQNUMENTRY"}, "$value": "Accounting Sequence Number Entry"}, {"$": {"typename": "COSTTYPE"}, "$value": "Cost Type"}, {"$": {"typename": "COSTTYPEGROUP"}, "$value": "Cost type Group"}, {"$": {"typename": "COSTTYPEGRPMEMBER"}, "$value": "Cost type Group Members"}, {"$": {"typename": "COSTTYPENGROUPPICK"}, "$value": "CostType/Group"}, {"$": {"typename": "COSTTYPEPICK"}, "$value": "Cost Type"}, {"$": {"typename": "STANDARDCOSTTYPE"}, "$value": "Standard cost type"}, {"$": {"typename": "ACCUMULATIONTYPE"}, "$value": "Accumulation Type"}, {"$": {"typename": "STANDARDTASK"}, "$value": "Standard Task"}, {"$": {"typename": "REPLENISHFORECAST"}, "$value": "Replenishment Forecast Table"}, {"$": {"typename": "BANKACCTTXNFEED"}, "$value": "Bank account transaction feed"}, {"$": {"typename": "BANKACCTTXNRECORD"}, "$value": "Bank account transaction feed records"}, {"$": {"typename": "TASKGROUP"}, "$value": "Task Group"}, {"$": {"typename": "GCBOOK"}, "$value": "IGC Book"}, {"$": {"typename": "GCBOOKENTITY"}, "$value": "Global consolidation book entities"}, {"$": {"typename": "GCBOOKELIMACCOUNT"}, "$value": "Global consolidatoion book elimination accounts"}, {"$": {"typename": "GCBOOKACCTRATETYPE"}, "$value": "Global consolidation book rate types"}, {"$": {"typename": "GCBOOKADJJOURNAL"}, "$value": "Global consolidation Adj book journals"}, {"$": {"typename": "PODOCUMENTLCESTENTRY"}, "$value": "Document Estimate Landed Cost Entry"}, {"$": {"typename": "TASKGRPMEMBER"}, "$value": "Task Group Members"}, {"$": {"typename": "TASKNGROUPPICK"}, "$value": "Task/Group"}, {"$": {"typename": "TASKPICK"}, "$value": "Task"}, {"$": {"typename": "PRODUCTIONUNITS"}, "$value": "Production units"}, {"$": {"typename": "PJESTIMATE"}, "$value": "Project Estimate"}, {"$": {"typename": "PJESTIMATEENTRY"}, "$value": "Project Estimate Entry"}, {"$": {"typename": "PJESTIMATETYPE"}, "$value": "Estimate Type"}, {"$": {"typename": "COSTCHANGEHISTORY"}, "$value": "Cost Change History"}, {"$": {"typename": "RECURGLACCTALLOCATION"}, "$value": "Recurglacctallocation"}, {"$": {"typename": "CONTRACTTYPE"}, "$value": "Contract Type"}, {"$": {"typename": "REPLENISHFORECASTDETAIL"}, "$value": "Replenishment Forecast Detail Table"}, {"$": {"typename": "ARPYMT"}, "$value": "AR Receivables Payment"}, {"$": {"typename": "ARPYMTDETAIL"}, "$value": "AR Receivables Payment Details"}, {"$": {"typename": "ARPYMTENTRY"}, "$value": "AR Receivables Payment Line Detail"}, {"$": {"typename": "CONTRACTRSLVADDLDATA"}, "$value": "Contract resolve additional data"}, {"$": {"typename": "CONTRACTACPRUN"}, "$value": "Process Contract Schedules"}, {"$": {"typename": "GLACCTGRPPURPOSE"}, "$value": "Account Group Purpose"}, {"$": {"typename": "INVENTORYTOTALDETAIL"}, "$value": "Inventory Total Detail"}, {"$": {"typename": "BANKACCTRECONRECORD"}, "$value": "Bank reconciliation records"}, {"$": {"typename": "BUYTOORDERHISTORY"}, "$value": "Buy to order History"}, {"$": {"typename": "APRETAINAGERELEASE"}, "$value": "AP Retainage release"}, {"$": {"typename": "APRETAINAGERELEASEENTRY"}, "$value": "AP Retainage release entry"}, {"$": {"typename": "ARRETAINAGERELEASE"}, "$value": "AR Retainage release"}, {"$": {"typename": "ARRETAINAGERELEASEENTRY"}, "$value": "AR Retainage release entry"}, {"$": {"typename": "ARRELEASEABLERECORD"}, "$value": "AR Releaseable Retainage Record"}, {"$": {"typename": "APRELEASEABLERECORD"}, "$value": "AP Releaseable Retainage Record"}, {"$": {"typename": "IAACTIVITY"}, "$value": "Activity"}, {"$": {"typename": "CONTRACTACPRUNENTRY"}, "$value": "Contract Schedules Processing Results Entry"}, {"$": {"typename": "ZONE"}, "$value": "Zone"}, {"$": {"typename": "BINSIZE"}, "$value": "Bin size"}, {"$": {"typename": "BINFACE"}, "$value": "Bin face"}, {"$": {"typename": "GLREPORTTYPE"}, "$value": "Report Type"}, {"$": {"typename": "GLREPORTAUDIENCE"}, "$value": "Report Audience"}, {"$": {"typename": "ICCYCLECOUNT"}, "$value": "Cycle Counts"}, {"$": {"typename": "ICCYCLECOUNTENTRY"}, "$value": "Cycle Counts Entry"}, {"$": {"typename": "PTAPPLICATION"}, "$value": "Platform Application"}, {"$": {"typename": "CREDITACCTRECON"}, "$value": "Credit card reconciliation"}, {"$": {"typename": "CREDITACCTRECONRECORD"}, "$value": "Credit reconciliation records"}, {"$": {"typename": "IETRECONCILIATIONS"}, "$value": "Inter-Entity Transactions"}, {"$": {"typename": "FINANCIALINSTITUTION"}, "$value": "Financial Institution"}, {"$": {"typename": "AVAILABLEINVENTORY"}, "$value": "Available Inventory"}, {"$": {"typename": "CHANGEREQUESTTYPE"}, "$value": "Change Request Type"}, {"$": {"typename": "CHANGEREQUESTSTATUS"}, "$value": "Change Request Status"}, {"$": {"typename": "CHANGEREQUEST"}, "$value": "Change Request"}, {"$": {"typename": "CHANGEREQUESTENTRY"}, "$value": "Change Request Entry"}, {"$": {"typename": "PRGLPOSTING"}, "$value": "Link PR and GL"}, {"$": {"typename": "GLIETPOSTING"}, "$value": "Link PR and GL"}, {"$": {"typename": "PROVIDERPAYMENTMETHOD"}, "$value": "Provider payment method"}, {"$": {"typename": "PROVIDERBANKACCOUNT"}, "$value": "Provider Bank Account"}, {"$": {"typename": "PROVIDERVENDOR"}, "$value": "Provider vendor"}, {"$": {"typename": "PROJECTCHANGEORDER"}, "$value": "Project Change Order"}, {"$": {"typename": "DEGLPOSTING"}, "$value": "Link docentry and glentry"}, {"$": {"typename": "DEGLSUBTOTALPOSTING"}, "$value": "Link docentry subtotal and glentry"}, {"$": {"typename": "PRIORPERIODCOGSPOSTING"}, "$value": "Link prior periods cogs adjustments and glentry"}, {"$": {"typename": "INVENTORYTASKLIST"}, "$value": "Task List"}, {"$": {"typename": "INVENTORYWORKQUEUE"}, "$value": "Work Queue"}, {"$": {"typename": "FULFILLMENTDOCENTRY"}, "$value": "Sales Document Detail"}, {"$": {"typename": "BANKTXNRULE"}, "$value": "Bank transaction rule"}, {"$": {"typename": "BANKTXNRULEATTR"}, "$value": "Bank transaction rule attribute"}, {"$": {"typename": "BANKTXNRULESET"}, "$value": "Bank transaction ruleset"}, {"$": {"typename": "BANKTXNRULEMAP"}, "$value": "Bank transaction rule and ruleset map"}, {"$": {"typename": "BANKTXNRULERUN"}, "$value": "Bank transaction rule run"}, {"$": {"typename": "VENDOREMAILTEMPLATE"}, "$value": "<PERSON><PERSON><PERSON>"}, {"$": {"typename": "RUNOBJECTSUMMARY"}, "$value": "Run Object Summary"}, {"$": {"typename": "INVENTORYWQORDER"}, "$value": "Orders in Fulfillment"}, {"$": {"typename": "CONTRACTMEAINSTRUCTION"}, "$value": "Contract MEA instruction"}, {"$": {"typename": "CONTRACTMEAINSTPART"}, "$value": "Contract MEA instruction part"}, {"$": {"typename": "GCOWNERSHIPSTRUCTURE"}, "$value": "Gcownershipstructure"}, {"$": {"typename": "GCOWNERSHIPENTITY"}, "$value": "Gcownershipentity"}, {"$": {"typename": "GCOWNERSHIPCHILDENTITY"}, "$value": "Gcownershipchildentity"}, {"$": {"typename": "GCOWNERSHIPSTRUCTUREDETAIL"}, "$value": "Gcownershipstructuredetail"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adj Increase Machine Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adj Increase Material Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adjustment Decrease Qty"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adjustment Decrease Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adjustment Decrease Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adjustment Increase Qty"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adjustment Increase Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Adjustment Increase Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Beginning Balance"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inv Damaged Goods Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inv Scrap Spoilage Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inv Transfer In Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inv Transfer Out Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inventory Issue Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inventory Receipt Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "Inventory Shipper Qty Value"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "SYS-LC Actuals Adj Incr"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "SYS-LC Estimates Adj Incr"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "SYS-LC Estimates Rev Adj <PERSON>r"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "SYS-Warehouse Transfer In"}, {"$": {"typename": "INVDOCUMENT"}, "$value": "SYS-Warehouse Transfer Out"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adj Increase Machine Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adj Increase Material Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adjustment Decrease Qty Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adjustment Decrease Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adjustment Decrease Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adjustment Increase Qty Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adjustment Increase Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Adjustment Increase Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Beginning Balance Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inv Damaged Goods Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inv Scrap Spoilage Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inv Transfer In Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inv Transfer Out Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inventory Issue Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inventory Receipt Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "Inventory Shipper Qty Value Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "SYS-LC Actuals Adj Incr Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "SYS-LC Estimates Adj Incr Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "SYS-LC Estimates Rev Adj <PERSON>r Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "SYS-Warehouse Transfer In Detail"}, {"$": {"typename": "INVDOCUMENTENTRY"}, "$value": "SYS-Warehouse Transfer Out Detail"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Credit Memo"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Invoice"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Invoice - Simple tax"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Invoice-Inventory"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Order"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Order-Inventory"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales quote"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Return-Inventory"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Sales Return-Inventory ship"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Shipper-Inventory"}, {"$": {"typename": "SODOCUMENT"}, "$value": "Shipper-Inventory stock"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Credit Memo Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Invoice Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Invoice - Simple tax Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Invoice-Inventory Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Order Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Order-Inventory Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales quote Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Return-Inventory Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Sales Return-Inventory ship Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Shipper-Inventory Detail"}, {"$": {"typename": "SODOCUMENTENTRY"}, "$value": "Shipper-Inventory stock Detail"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Fulfillment"}, {"$": {"typename": "PODOCUMENT"}, "$value": "PO Receiver-Inventory"}, {"$": {"typename": "PODOCUMENT"}, "$value": "PO Receiver-Inventory XT"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Purchase Order"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Purchase Order AM"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Purchase Order-Inventory"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Purchase Order-Inventory XT"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Purchase Requisition"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Purchasing Debit Memo"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Return"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Return_Inventory"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Vendor Invoice"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Vendor Invoice - simple tax"}, {"$": {"typename": "PODOCUMENT"}, "$value": "Vendor Invoice-Inventory"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Fulfillment Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "PO Receiver-Inventory Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "PO Receiver-Inventory XT Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Purchase Order Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Purchase Order AM Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Purchase Order-Inventory Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Purchase Order-Inventory XT Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Purchase Requisition Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Purchasing Debit Memo Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Return Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Return_Inventory Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Vendor Invoice Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Vendor Invoice - simple tax Detail"}, {"$": {"typename": "PODOCUMENTENTRY"}, "$value": "Vendor Invoice-Inventory Detail"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Credit Memo"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Invoice"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Invoice - Simple tax"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Invoice-Inventory"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Order"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Order-Inventory"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales quote"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Return-Inventory"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Sales Return-Inventory ship"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Shipper-Inventory"}, {"$": {"typename": "SORECURDOCUMENT"}, "$value": "Shipper-Inventory stock"}]}