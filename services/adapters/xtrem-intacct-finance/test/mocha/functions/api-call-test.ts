import { Logger, Test } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';

const logger = Logger.getLogger(__filename, 'intacct-api-call-test');

describe('Tests API ', () => {
    before(() => {});

    it(' Inspect function All Intacct Objects', () =>
        Test.withContext(
            async context => {
                const list = await new xtremIntacct.classes.sdk.Functions.Inspect(context, '*').allObject();
                logger.debug(() => JSON.stringify(list));
                assert.isArray(list);
            },
            {
                scenario: 'inspect-api',
                directory: __dirname,
            },
        ));
    it(' Inspect function Objects Fields', () =>
        Test.withContext(
            async context => {
                const list = await new xtremIntacct.classes.sdk.Functions.Inspect(context, 'ZONE').allFields();
                assert.deepEqual(list, {
                    $: { Name: 'ZONE' },
                    Fields: {
                        Field: [
                            'RECORDNO',
                            'ZONEID',
                            'ZONEDESC',
                            'WHENCREATED',
                            'WHENMODIFIED',
                            'CREATEDBY',
                            'MODIFIEDBY',
                            'RECORD_URL',
                        ],
                    },
                });
            },
            {
                scenario: 'inspect-api-ZONE',
                directory: __dirname,
            },
        ));

    it('Inspect function', () =>
        Test.withContext(
            async context => {
                const inspectVendor = await new xtremIntacct.classes.sdk.Functions.Inspect(
                    context,
                    'VENDOR',
                ).allFields();
                logger.debug(() => JSON.stringify(inspectVendor));
                assert.isArray(inspectVendor.Fields.Field);
            },
            {
                scenario: 'inspect-api-VENDOR',
                directory: __dirname,
            },
        ));

    it('Lookup function', () =>
        Test.withContext(
            async context => {
                const lookupVendor = (await xtremIntacctFinance.functions.lookupApi(context, { object: 'VENDOR' })).at(
                    0,
                );
                logger.debug(() => JSON.stringify(lookupVendor));
                assert.isArray(lookupVendor?.fields);
            },
            {
                scenario: 'lookup-api-VENDOR',
                directory: __dirname,
            },
        ));

    it('getFullObject function', () =>
        Test.withContext(
            async context => {
                const list = await xtremIntacctFinance.functions.mapping.getFullObject(context, 'VENDOR');
                assert.isObject(list);
                assert.deepEqual(list?.name, 'VENDOR');

                const vendorIdField = list?.fields.find(field => field.ID === 'VENDORID');

                assert.deepEqual(
                    vendorIdField,
                    {
                        ID: 'VENDORID',
                        LABEL: 'Vendor ID',
                        DESCRIPTION: 'Unique ID of Vendor',
                        REQUIRED: true,
                        READONLY: false,
                        DATATYPE: 'TEXT',
                        ISCUSTOM: false,
                        xtremProperty: '',
                    },
                    JSON.stringify(vendorIdField, null, 4),
                );
            },
            {
                scenario: 'get-full-intacct-object',
                directory: __dirname,
            },
        ));
});
