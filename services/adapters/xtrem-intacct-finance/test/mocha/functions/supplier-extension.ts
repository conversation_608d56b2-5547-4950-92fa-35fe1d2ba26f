import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import { paymentMethodIntacct } from '../../../lib/functions/supplier-extension';

describe('Supplier extension', () => {
    it('paymentMethodIntacct test', () => {
        const paymentMethods = xtremMasterData.enums.PaymentMethodDataType.values;

        paymentMethods.forEach((paymentMethod: xtremMasterData.enums.PaymentMethod) => {
            switch (paymentMethod) {
                case 'printedCheck':
                    assert.equal(paymentMethodIntacct(paymentMethod), 'Printed Check');
                    break;
                case 'creditCard':
                    assert.equal(paymentMethodIntacct(paymentMethod), 'Charge Card');
                    break;
                case 'cash':
                    assert.equal(paymentMethodIntacct(paymentMethod), 'Cash');
                    break;
                case 'ACH':
                case 'EFT':
                    assert.equal(paymentMethodIntacct(paymentMethod), paymentMethod);
                    break;
                default:
                    break;
            }
        });
    });
});
