import type { NodePayloadOptions } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';
import { changeIntacctOption, disableIntegration } from '../../fixtures/intacct';

describe(' Common functions', () => {
    before(() => {});

    it(' getObjectDiff   ', () => {
        const obj1 = { d: 4, e: 5, g: 'test', h: '', i: null, intacctId: '10' };
        const obj2 = { e: 4, g: 'test', h: '', i: null, intacctId: '20' };

        const diff = xtremIntacctFinance.functions.getObjectDiff({ obj1, obj2 });

        assert.deepEqual(diff, ['e', 'd']);
    });

    it(' getObjectDiff second level  ', () => {
        const obj1 = { secondLevel: { d: 4, e: 5, g: 'test', h: '', i: null, intacctId: '10' } };
        const obj2 = { secondLevel: { e: 4, g: 'test', h: '', i: null, intacctId: '20' } };

        const diff = xtremIntacctFinance.functions.getObjectDiff({ obj1, obj2 });
        assert.deepEqual(diff, ['secondLevel'], diff.join('/'));
    });

    it(' delete property  ', () => {
        const object1 = { secondLevel: { d: 4, e: 5, g: 'test', h: '', i: null, intacctId: '10' } };

        xtremIntacctFinance.functions.deleteProperty(object1, 'secondLevel.intacctId');

        assert.isUndefined(object1.secondLevel.intacctId);
    });

    it(' All object paths   ', () => {
        const object1 = { secondLevel: { d: 4, g: 'test', h: '', i: null }, then: 'toto', else: { my: 1 } };

        xtremIntacctFinance.functions.allObjectPaths(object1);

        assert.deepEqual(xtremIntacctFinance.functions.allObjectPaths(object1), [
            'secondLevel.d',
            'secondLevel.g',
            'secondLevel.h',
            'secondLevel.i',
            'then',
            'else.my',
        ]);
    });

    it(' deepDifference second level  ', () => {
        const object1 = { secondLevel: { d: 4, e: 5, g: 'test', h: '', i: null, intacctId: '10' } };
        const object2 = { secondLevel: { e: 4, g: 'test', h: '', i: null, intacctId: '20' } };

        const diff = xtremIntacctFinance.functions.deepDifference(object1, object2, ['secondLevel.intacctId']);
        assert.deepEqual(diff, { secondLevel: { d: 4, e: 5 } }, diff);

        assert.deepEqual(xtremIntacctFinance.functions.allObjectPaths(diff), ['secondLevel.d', 'secondLevel.e']);
    });

    it(' getObjectDiff  object & array  ', () => {
        const obj1 = {
            object: { toto: 'bim' },
            array: [{ one: '1' }, { one: '2' }],
        };
        const obj2 = {
            object: { toto: 'bim' },
            array: [{ one: '1' }, { one: '2' }],
        };

        assert.deepEqual(xtremIntacctFinance.functions.getObjectDiff({ obj1, obj2 }).length, 0);
    });

    it(' getObjectDiff  - itemSites  ', () =>
        Test.withContext(async context => {
            const myItem = await context.read(xtremMasterData.nodes.Item, { id: 'Chemical A' });

            assert.deepEqual(
                xtremIntacctFinance.functions.getObjectDiff({
                    obj1: await myItem.$.payload(),
                    obj2: await myItem.$.payload(),
                }).length,
                0,
            );
        }));

    it(' getObjectDiff  - Customer ', () =>
        Test.withContext(async context => {
            const custZA001 = await context.read(
                xtremMasterData.nodes.Customer,
                { _id: '#US017' },
                { forUpdate: true },
            );

            assert.deepEqual(await (await custZA001.businessEntity).id, 'US017');

            const payloadOptions = { withIds: true } as NodePayloadOptions<xtremMasterData.nodes.Customer>;

            const payload = await custZA001.$.payload(payloadOptions);

            assert.equal(Object.keys(payload).length, 35);

            assert.deepEqual(
                xtremIntacctFinance.functions.getObjectDiff({
                    obj1: await custZA001.$.payload(payloadOptions),
                    obj2: await custZA001.$.payload(payloadOptions),
                }).length,
                0,
            );
        }));
    // TODO: https://jira.sage.com/browse/XT-76365
    it.skip('getTaxSolutionId test', () =>
        Test.withContext(async context => {
            // Deactivate the current default instance
            await disableIntegration(context);

            // Activate the service option again
            await changeIntacctOption(context, { id: 'xtrem-fr-dev-qa', isActive: true });

            // Read the account payable invoice for FR
            const accountPayableInvoice = await context.read(xtremIntacctFinance.nodes.IntacctAccountsPayableInvoice, {
                _id: '#TESTINTACCTFR|purchaseInvoice',
            });
            assert.equal(await accountPayableInvoice.taxSolutionId, 'TVA française - SYS');
        }));

    it('manageTaxEntries test', () =>
        Test.withContext(async context => {
            // Read the account payable invoice for FR
            const accountPayableInvoiceLine = await context.read(
                xtremIntacctFinance.nodes.IntacctAccountsPayableInvoiceLine,
                { documentLine: '#TESTINTACCTFR|purchaseInvoice|10' },
            );

            const expectedResult: xtremIntacctFinance.interfaces.Tax.IntacctTaxEntries[] = [
                { TAXENTRIES: [{ TAXENTRY: { DETAILID: 'Déductible débits taux normal', TRX_TAX: '28.8' } }] },
                { TAXENTRIES: [{ TAXENTRY: { DETAILID: 'TEST. Taux normal', TRX_TAX: '3.02' } }] },
            ];
            assert.deepStrictEqual(await accountPayableInvoiceLine.taxEntries, expectedResult);
        }));

    it('manageTaxEntries test with isReverseCharge', () =>
        Test.withContext(async context => {
            // Read the account payable invoice for FR and update the isReverseCharge
            const invoiceTaxLine = await context.read(
                xtremFinance.nodes.AccountsPayableInvoiceLineTax,
                { document: '#TESTINTACCTFR|purchaseInvoice|10', _sortValue: 10 },
                { forUpdate: true },
            );
            await invoiceTaxLine.$.set({ isReverseCharge: true });
            await invoiceTaxLine.$.save();
            assert.deepEqual(invoiceTaxLine.$.context.diagnoses, []);
            assert.isTrue(await invoiceTaxLine.isReverseCharge);

            // Read the tax for FR and update the secondaryExternalReference
            const tax = await context.read(
                xtremTax.nodes.Tax,
                { id: 'FR_TVA_NORMAL_DEDUCTIBLE_ON_DEBITS' },
                { forUpdate: true },
            );
            await tax.$.set({ secondaryExternalReference: 'Secondary external reference test' });
            await tax.$.save();
            assert.deepEqual(tax.$.context.diagnoses, []);
            assert.equal(await tax.secondaryExternalReference, 'Secondary external reference test');

            // Read the account payable invoice for FR
            const accountPayableInvoiceLine = await context.read(
                xtremIntacctFinance.nodes.IntacctAccountsPayableInvoiceLine,
                { documentLine: '#TESTINTACCTFR|purchaseInvoice|10' },
            );

            const expectedResult: xtremIntacctFinance.interfaces.Tax.IntacctTaxEntries[] = [
                {
                    TAXENTRIES: [
                        { TAXENTRY: { DETAILID: 'Déductible débits taux normal', TRX_TAX: '28.8' } },
                        { TAXENTRY: { DETAILID: 'Secondary external reference test', TRX_TAX: '-28.8' } },
                    ],
                },
                { TAXENTRIES: [{ TAXENTRY: { DETAILID: 'TEST. Taux normal', TRX_TAX: '3.02' } }] },
            ];
            assert.deepStrictEqual(await accountPayableInvoiceLine.taxEntries, expectedResult);
        }));
    it('When Intacct service option is on openItemPageOption', () =>
        Test.withContext(
            async context => {
                await xtremIntacct.nodes.IntacctOptionManagement.serviceOptionChange(context);

                assert.isFalse(
                    await context.serviceOptionManager.isServiceOptionEnabled(
                        context,
                        xtremIntacct.serviceOptions.intacctOption,
                    ),
                );
                assert.isTrue(
                    await context.serviceOptionManager.isServiceOptionEnabled(
                        context,
                        xtremStructure.serviceOptions.openItemPageOption,
                    ),
                );
                await xtremSystem.nodes.SysPackAllocation.activate(context, '@sage/xtrem-intacct-finance');
            },
            {
                testActiveServiceOptions: [
                    xtremIntacct.serviceOptions.intacctOption,
                    xtremStructure.serviceOptions.openItemPageOption,
                ],
            },
        ));
});
