import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as sinon from 'sinon';

describe('Supplier', () => {
    it('Reading supplier node - launch sync', () =>
        Test.withContext(async context => {
            const casinoSupplier = await context.read(
                xtremMasterData.nodes.Supplier,
                { _id: '#700' },
                { forUpdate: true },
            ); // ZA 700
            // 700  as never been synchronize to intacct
            assert.strictEqual(await (await casinoSupplier.businessEntity).id, '700');
            assert.instanceOf(casinoSupplier, xtremMasterData.nodes.Supplier);
            assert.isNull(await casinoSupplier.intacctSupplier);

            const notifySpy = sinon.spy(context, 'notify');

            await casinoSupplier.$.set({ paymentTerm: '#TEST_NET_30_SUPPLIER' });

            await casinoSupplier.$.save();

            const numberOfaddresses = await (await casinoSupplier.businessEntity).addresses.length;

            assert.equal(numberOfaddresses, 2);
            // TODO - explore how we can avoid the second synchronization
            sinon.assert.callCount(notifySpy, 1);

            sinon.assert.calledOnceWithExactly(
                notifySpy,
                'IntacctListener/synchronizeNode/start',
                { intacctNode: 57 }, // contact
                { replyTopic: 'SysNotificationState/updateStatus' },
            );
        }));

    it('Update the supplier ', () =>
        Test.withContext(async context => {
            const updateSupplier = await context.read(
                xtremMasterData.nodes.Supplier,
                { _id: '#500' },
                { forUpdate: true },
            );
            const notifySpy = sinon.spy(context, 'notify');
            assert.isNotNull(updateSupplier);
            assert.strictEqual(await (await updateSupplier?.businessEntity)?.id, '500');

            if (updateSupplier) {
                await updateSupplier.$.set({ minimumOrderAmount: 1000, paymentTerm: '#TEST_NET_45_ALL' });
                await updateSupplier.$.save();
            } else {
                assert.fail('updateSupplier empty');
            }

            sinon.assert.calledOnceWithExactly(
                notifySpy,
                'IntacctListener/synchronizeNode/start',
                { intacctNode: (await updateSupplier.intacctSupplier)?._id },
                { replyTopic: 'SysNotificationState/updateStatus' },
            );
        }));
});
