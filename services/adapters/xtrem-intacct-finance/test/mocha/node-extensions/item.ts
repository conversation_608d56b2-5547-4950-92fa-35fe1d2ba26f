import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremIntacctFinance from '../../../index';
import { checkActiveIntegration, disableIntegration } from '../../fixtures';

describe('Item', () => {
    it('Update Item - create intacctItem  ', () =>
        Test.withContext(async context => {
            /** Item with no intacct Item  */
            const item = await context.read(xtremMasterData.nodes.Item, { id: '17891' }, { forUpdate: true });
            assert.isNull(await item.intacctItem);
            const notifySpy = sinon.spy(context, 'notify');
            await item.$.save();
            /** After saving we have an intacctItem  */
            assert.isNotNull(await item.intacctItem);

            /** We launch a notify on synchronizeNode after updating the item */
            assert.isTrue(
                notifySpy.calledOnceWith(
                    'IntacctListener/synchronizeNode/start',
                    { intacctNode: (await item.intacctItem)?._id || '' },
                    { replyTopic: 'SysNotificationState/updateStatus' },
                ),
            );

            const intacctItem = await item.getSyncStateReference();
            if (!intacctItem) {
                assert.fail('No intacct item from getSyncStateReference');
            }
            assert.equal(await intacctItem.state, 'not');
        }));

    it('Update Milk Item ', () =>
        Test.withContext(async context => {
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Milk' }, { forUpdate: true });
            await item.$.set({ name: 'Milk - updated ' });
            await item.$.save();

            const itemUpdated = await context.read(xtremMasterData.nodes.Item, { id: 'Milk' });

            const intacctItem = await itemUpdated.intacctItem;

            assert.isNotNull(intacctItem);

            assert.equal(await (await intacctItem?.integration)?.name, 'Sage Intacct Financial');
            assert.equal(await (await intacctItem?.node)?.$.getNaturalKeyValue(), 'Item');
            assert.equal(await intacctItem?.naturalKey, 'Milk');
        }));

    it('Launch function update intacct Milk Item ', () =>
        Test.withContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'Milk' }, { forUpdate: true });
                await item.$.set({ intacctItem: {} });
                await item.$.save();

                const intacctItem = await item.intacctItem;
                if (!intacctItem) {
                    assert.fail('No intacct item');
                }
                assert.equal(await intacctItem.intacctId, 'Milk');

                const synchronizationManager = await xtremIntacctFinance.classes.IntacctSynchronizationManager.create(
                    context,
                    intacctItem,
                );
                const synchronizeResult = await synchronizationManager.synchronize();
                assert.equal(synchronizeResult.state, 'success', JSON.stringify(synchronizeResult));
            },
            {
                scenario: 'update-item-milk',
                directory: __dirname,
                testAttributes: { isCreationMode: false },
            },
        ));

    it(' Create/Delete an item ', () =>
        Test.withContext(async context => {
            await checkActiveIntegration(context);

            const notifySpy = sinon.spy(context, 'notify');
            const createdItem = await context.create(xtremMasterData.nodes.Item, {
                id: 'MyNewChemical',
                name: 'Chemical B ',
                description: 'Chemical description B',
                stockUnit: { id: 'METER' },
                isBought: true,
            });

            await createdItem.$.save();

            assert.isTrue(
                notifySpy.calledOnceWith(
                    'IntacctListener/synchronizeNode/start',
                    { intacctNode: (await createdItem.intacctItem)?._id || '' },
                    { replyTopic: 'SysNotificationState/updateStatus' },
                ),
            );

            const item = await context.read(
                xtremMasterData.nodes.Item,
                {
                    _id: await createdItem.$.getNaturalKeyValue(),
                },
                { forUpdate: true },
            );

            const intacctItem = await item.intacctItem;
            if (!intacctItem) {
                assert.fail('No intacct item ');
            }

            assert.equal(await intacctItem?.naturalKey, 'MyNewChemical');

            await intacctItem.$.set({ recordNo: 9999 });

            await intacctItem.$.save();

            await context.delete(xtremMasterData.nodes.Item, { id: 'MyNewChemical' });

            assert.isTrue(
                notifySpy.calledWith(
                    'IntacctListener/deleteIntacct/start',
                    { intacctNode: 'ITEM', recordNo: '9999' },
                    { replyTopic: 'SysNotificationState/updateStatus' },
                ),
            );
        }));
    it('Create an item with no intacct configuration - not integrated', () =>
        Test.withContext(async context => {
            await disableIntegration(context);

            const notifySpy = sinon.spy(context, 'notify');

            const createdItem = await context.create(xtremMasterData.nodes.Item, {
                id: 'MyNewChemical',
                name: 'Chemical B ',
                description: 'Chemical description B',
                stockUnit: { id: 'METER' },
                isBought: true,
            });

            await createdItem.$.save();

            assert.isTrue(notifySpy.notCalled);

            const intacctItem = await createdItem.intacctItem;

            if (!intacctItem) {
                assert.fail('No intacct item');
            }

            assert.equal(await intacctItem.state, 'not');
        }));
});
