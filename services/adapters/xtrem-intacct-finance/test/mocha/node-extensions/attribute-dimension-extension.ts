// tslint:disable:no-duplicate-string
import type { AnyRecord } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../index';

describe('Attribute And Dimension ', () => {
    before(() => {});

    it(' Attribute create From Employee - initPayload function ', () =>
        Test.withContext(async context => {
            const payload = await xtremIntacctFinance.nodeExtensions.AttributeExtension.initPayload(context, {
                id: 'JohnId',
                intacctId: 'JohnId',
                recordNo: 8,
                employeeRecordNo: 8,
                name: ' <PERSON>',
                statusIntacct: 'active',
            });

            assert.equal((payload.attributeType as AnyRecord).id, 'employee');
            assert.equal(payload.isActive, true);

            /* does not throw */ await (async () =>
                (await context.create(xtremFinanceData.nodes.Attribute, payload)).$.save())();
        }));

    it(' Attribute create from Task - initPayload function ', () =>
        Test.withContext(async context => {
            const payload = await xtremIntacctFinance.nodeExtensions.AttributeExtension.initPayload(context, {
                id: 'Task99',
                intacctProject: 'AttPROJ',
                recordNo: 997,
                taskRecordNo: 997,
                name: 'Attribute of Task type 99',
                attributeType: '#task',
            });

            assert.equal((payload.attributeType as AnyRecord).id, 'task');
            assert.equal(payload.isActive, true);

            /* does not throw */ await (async () =>
                (await context.create(xtremFinanceData.nodes.Attribute, payload)).$.save())();
        }));

    it(' Attribute update from Task - initPayload function ', () =>
        Test.withContext(async context => {
            const payload = await xtremIntacctFinance.nodeExtensions.AttributeExtension.initPayload(context, {
                id: 'Task2',
                intacctProject: 'AttPROJ2',
                recordNo: 998,
                taskRecordNo: 998,
                name: 'Attribute of Task type 2 unit test',
                attributeType: '#task',
            });

            assert.equal(payload.attributeType, undefined);
            assert.equal(payload.isActive, true);
        }));

    it(' Attribute create fail from Task - initPayload function ', () =>
        Test.withContext(async context => {
            const payload = await xtremIntacctFinance.nodeExtensions.AttributeExtension.initPayload(context, {
                id: 'Task99',
                recordNo: 997,
                taskRecordNo: 997,
                name: 'Attribute of Task type 99',
                attributeType: '#task',
            });

            assert.equal((payload.attributeType as AnyRecord).id, 'task');
            assert.equal(payload.isActive, true);

            const task = await context.create(xtremFinanceData.nodes.Attribute, payload);
            await assert.isRejected(task.$.save());
            assert.deepEqual(task.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'You need to set the "Restricted to" to a valid attribute.',
                },
            ]);
        }));

    it('Attribute - Mapping configuration additionnal filters  ', () =>
        Test.withContext(async context => {
            const attributeMap = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|EMPLOYEE|Attribute',
            });
            const filter = await attributeMap.getAdditionalFilter<xtremFinanceData.nodes.Attribute>();
            assert.doesNotThrow(() => context.query(xtremFinanceData.nodes.Attribute, { filter }));
        }));

    it('Dimension - Mapping configuration additionnal filters  ', () =>
        Test.withContext(async context => {
            const departmentMap = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|DEPARTMENT|Dimension',
            });
            const filter = await departmentMap.getAdditionalFilter<xtremFinanceData.nodes.Attribute>();
            assert.doesNotThrow(() => context.query(xtremFinanceData.nodes.Attribute, { filter }));
        }));

    it(' Dimension create From Departement - initPayload function ', () =>
        Test.withContext(async context => {
            const payload = await xtremIntacctFinance.nodeExtensions.DimensionExtension.initPayload(context, {
                id: 'JohnId',
                intacctId: 'JohnId',
                recordNo: 8,
                departmentRecordNo: 8,
                name: ' John',
                statusIntacct: 'active',
            });
            const dimensionType = await context.read(xtremFinanceData.nodes.DimensionType, {
                docProperty: 'dimensionType01',
            });
            assert.equal(payload.dimensionType, dimensionType._id);
            assert.equal(payload.isActive, true);

            /* does not throw */ await (async () =>
                (await context.create(xtremFinanceData.nodes.Dimension, payload)).$.save())();
        }));

    // TODO // Improve unit test try to set the payload of the initpayload result
});
