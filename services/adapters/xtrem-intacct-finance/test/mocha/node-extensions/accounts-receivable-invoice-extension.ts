import { Test } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import { getAccountReceivableInvoicePayload, intacctJsonQuery } from '../../fixtures/account-receivable-invoice';
import { getRequestManagerForFinanceDocuments, xmlWithoutFunctionAsJson } from '../../fixtures/synchronization';

describe('Accounts receivable invoice - intacct integration - test ', () => {
    it('Creation of an accounts receivable invoice', () =>
        Test.withContext(async context => {
            const financeDocument = await getAccountReceivableInvoicePayload(context);

            (context as any)._contextValues.notificationId = 'eo34b4E02CiGyiiG0obC-';
            (context as any)._contextValues.replyTopic = 'ReceivableInvoice/accountingInterface';
            await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                financeDocument,
                notificationId: 'SI11',
                replyTopic: 'SalesInvoice/accountingInterface',
                isProcessed: false,
            });

            const createdAccountsReceivableInvoice = await xtremFinance.functions.createOrUpdateAPARInvoice(
                context,
                'accountsReceivableInvoice',
                'salesInvoice',
                'SI11',
                '582893b0-fd86-4395-910b-a18f41f139c5',
                'ReceivableInvoice/accountingInterface',
            );

            assert.deepStrictEqual(createdAccountsReceivableInvoice.documentsCreated.length, 1);

            const arInvoice = await context.read(xtremFinance.nodes.AccountsReceivableInvoice, { number: 'SI11' });

            const intacctDocument = await arInvoice.intacctDocument;

            if (!intacctDocument) {
                assert.fail('No intacctDocument');
            }

            const intacctDocumentLines = arInvoice.lines
                .filter(async filterLine => !!(await filterLine.intacctDocumentLine))
                .map(line => line.intacctDocumentLine);

            assert.equal(await intacctDocumentLines.length, await arInvoice.lines.length);

            const intacctDocumentLine = await intacctDocumentLines.at(0);

            if (!intacctDocumentLine) {
                assert.fail('No intacct document line');
            }

            assert.equal(await intacctDocumentLine?.signedAmountExcludingTax, 12);

            const sandbox = sinon.createSandbox();
            const requestManager = await getRequestManagerForFinanceDocuments(context, sandbox, intacctDocument);

            const intacctQuery = await xmlWithoutFunctionAsJson(context, { sandbox, requestManager });

            sandbox.reset();
            sandbox.restore();

            assert.deepEqual(intacctQuery, intacctJsonQuery, JSON.stringify(intacctQuery, null, 4));
        }));
});
