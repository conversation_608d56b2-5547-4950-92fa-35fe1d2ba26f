import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as sinon from 'sinon';

describe('Customer', () => {
    it('Update the customer - check notification sent', () =>
        Test.withContext(async context => {
            const updateCustomer = await context.read(
                xtremMasterData.nodes.Customer,
                { _id: '#US020' },
                { forUpdate: true },
            );
            const notifySpy = sinon.spy(context, 'notify');

            await updateCustomer.$.set({ minimumOrderAmount: 1000 });
            await updateCustomer.$.save();

            notifySpy.calledOnceWith(
                'IntacctListener/synchronizeNode/start',
                { intacctNode: (await updateCustomer.intacctCustomer)?._id || '' },
                { replyTopic: 'SysNotificationState/updateStatus' },
            );
        }));

    it.skip('Create customer intacct integrations', () =>
        Test.withContext(context => {
            context.logger.info('Create customer intacct integrations');
            // Todo : create customer & see the intacct integration
        }));
});
