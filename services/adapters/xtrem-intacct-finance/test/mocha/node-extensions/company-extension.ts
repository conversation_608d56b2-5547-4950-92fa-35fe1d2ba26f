import type { Context } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import { disableIntegration } from '../../fixtures/intacct';

function createCompany(
    context: Context,
    id: string,
    name: string,
    description: string,
): Promise<xtremSystem.nodes.Company> {
    return context.create(xtremSystem.nodes.Company, {
        id,
        name,
        description,
        legislation: { id: 'FR' },
        country: { id: 'FR' },
        currency: { id: 'EUR' },
        legalForm: 'SARL',
        naf: '58.29A',
        siren: '*********',
        rcs: 'RCS Paris A *********',
        addresses: [
            {
                isActive: true,
                isPrimary: true,
                name: 'Main',
            },
        ],
    });
}

it('Create FR company node to check presetting for posting with intacctOption on', () =>
    Test.withContext(
        async context => {
            assert.isTrue(await context.isServiceOptionEnabled(xtremIntacct.serviceOptions.intacctOption));
            const company = await createCompany(
                context,
                'FR999',
                'Franchising company',
                'French company for unit testing',
            );
            await company.$.save();

            assert.deepEqual(company.$.context.diagnoses, []);
            assert.equal(await company.id, 'FR999');
            assert.isFalse(await company.doApPosting);
            assert.isFalse(await company.doArPosting);
        },
        { testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption] },
    ));

it('Create FR company node to check presetting for posting with intacctOption off and xtrem-intacct-finance package on', () =>
    Test.withContext(
        async context => {
            await context.serviceOptionManager.deactivateServiceOptions(context, [
                xtremIntacct.serviceOptions.intacctOption,
            ]);
            assert.isFalse(await context.isServiceOptionEnabled(xtremIntacct.serviceOptions.intacctOption));
            const company = await createCompany(
                context,
                'FR999',
                'Franchising company',
                'French company for unit testing',
            );
            await company.$.save();

            assert.deepEqual(company.$.context.diagnoses, []);
            assert.equal(await company.id, 'FR999');
            assert.isTrue(await company.doApPosting);
            assert.isTrue(await company.doArPosting);
        },
        { testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption] },
    ));

it('Create FR company node to check presetting for posting without any intacct configuration on', () =>
    Test.withContext(
        async context => {
            // Disable current instance without deactivating the service option
            await disableIntegration(context);

            const company = await createCompany(
                context,
                'FR999',
                'Franchising company',
                'French company for unit testing',
            );
            await company.$.save();

            assert.deepEqual(company.$.context.diagnoses, []);
            assert.equal(await company.id, 'FR999');
            assert.isTrue(await company.doApPosting);
            assert.isTrue(await company.doArPosting);
        },
        { testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption] },
    ));
