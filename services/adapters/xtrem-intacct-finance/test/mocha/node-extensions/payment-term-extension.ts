import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../index';
import { updateXtrem } from '../../../lib/functions/map';

describe('PaymentTerm', () => {
    before(() => {});

    it('initPayload PaymentTerm', () =>
        Test.withContext(async context => {
            const payload = xtremIntacctFinance.nodeExtensions.PaymentTermExtension.initPayload(context, {
                statusIntacct: 'inactive',
                intacctName: 'APTERM',
                discountFromIntacct: 'from invoice/bill date',
                discountTypeIntacct: '$',
                penaltyTypeIntacct: '%',
                name: 'Net 16',
            });
            assert.isNotNull(payload);
            assert.strictEqual(payload?.isActive, false);
            assert.strictEqual(payload?.businessEntityType, 'supplier');
            assert.strictEqual(payload?.discountFrom, 'afterInvoiceDate');
            assert.strictEqual(payload?.discountType, 'amount');
            assert.strictEqual(payload?.penaltyType, 'percentage');

            /* does not throw */ await (async () =>
                (await context.create(xtremMasterData.nodes.PaymentTerm, payload)).$.save())();
        }));

    it('PaymentTerm - Mapping configuration additionnal filters APTERM ', () =>
        Test.withContext(async context => {
            const paymentTermMap = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|APTERM|PaymentTerm',
            });
            const filter = await paymentTermMap.getAdditionalFilter();
            assert.doesNotThrow(() => context.query(xtremMasterData.nodes.PaymentTerm, { filter }));
        }));
    it('PaymentTerm - Mapping configuration additionnal filters ARTERM ', () =>
        Test.withContext(async context => {
            const paymentTermMap = await context.read(xtremIntacctFinance.nodes.IntacctMap, {
                _id: '#intacct|ARTERM|PaymentTerm',
            });
            const filter = await paymentTermMap.getAdditionalFilter();
            assert.doesNotThrow(() => context.query(xtremMasterData.nodes.PaymentTerm, { filter }));
        }));

    it('Create PaymentTerm from APTERM (supplier)', () =>
        Test.withContext(
            async context => {
                await updateXtrem(context, {
                    intacctName: 'APTERM',
                    intacctIdValue: 'Net 15',
                    isThrowingDiagnose: true,
                });
                const createdPaymentTerm = await context
                    .query(xtremMasterData.nodes.PaymentTerm, {
                        filter: {
                            name: 'Net 15',
                            businessEntityType: 'supplier',
                        },
                    })
                    .at(0);
                assert.isNotNull(createdPaymentTerm);

                assert.strictEqual(await createdPaymentTerm?.isActive, true);
                assert.strictEqual(await createdPaymentTerm?.businessEntityType, 'supplier');

                assert.strictEqual(await createdPaymentTerm?.description, 'Net 15 Update');
            },
            {
                scenario: 'create-APTERM',
                directory: __dirname,
            },
        ));
    it('Create PaymentTerm from ARTERM (customer)', () =>
        Test.withContext(
            async context => {
                await updateXtrem(context, {
                    intacctName: 'ARTERM',
                    intacctIdValue: 'Net 30',
                    isThrowingDiagnose: true,
                });
                const createdPaymentTerm = (
                    await context
                        .query(xtremMasterData.nodes.PaymentTerm, {
                            filter: {
                                name: 'Net 30',
                                businessEntityType: 'customer',
                            },
                        })
                        .toArray()
                )[0];

                assert.isNotNull(createdPaymentTerm);

                assert.strictEqual(await createdPaymentTerm?.isActive, true);
                assert.strictEqual(await createdPaymentTerm?.businessEntityType, 'customer');

                assert.strictEqual(
                    await createdPaymentTerm?.description,
                    'Net 30',
                    JSON.stringify(await createdPaymentTerm.$.payload()),
                );
            },
            {
                scenario: 'create-ARTERM',
                directory: __dirname,
            },
        ));
});
