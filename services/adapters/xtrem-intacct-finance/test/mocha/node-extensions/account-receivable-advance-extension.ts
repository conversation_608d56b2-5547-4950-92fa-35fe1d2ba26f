import { Test } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremIntacctFinance from '../../../lib';
import { arAdvancePayload } from '../../fixtures/account-receivable-advance';
import { getRequestManager } from '../../fixtures/synchronization';

describe('Accounts receivable advance', () => {
    // Will be fix by refactoring pr naturalKey => sysId for synchronizationState
    it.skip('Test createArAdvance mutation', () =>
        Test.withContext(
            async context => {
                const arAdvanceCreate =
                    await xtremIntacctFinance.nodeExtensions.AccountsReceivableAdvanceExtension.createArAdvance(
                        context,
                        await arAdvancePayload(context),
                    );

                const number = await arAdvanceCreate.number;
                assert.equal(number, 'ARAEI220001');

                const arAdvance = await context.read(xtremFinance.nodes.AccountsReceivableAdvance, {
                    _id: arAdvanceCreate._id,
                });

                const intacctDocument = await arAdvance.intacctDocument;

                if (!intacctDocument) {
                    assert.fail('No intacctDocument');
                }
                assert.equal(await arAdvance.lines.length, 1);

                const sandbox = sinon.createSandbox();
                const requestManager = await getRequestManager(context, sandbox, intacctDocument);

                const queryJson = await requestManager.xmlWithoutFunctionAsJson({});

                assert.deepEqual(queryJson, {}, JSON.stringify(queryJson));
            },
            {
                testActiveServiceOptions: [xtremIntacctFinance.serviceOptions.intacctCashbookManagement],
            },
        ));
});
