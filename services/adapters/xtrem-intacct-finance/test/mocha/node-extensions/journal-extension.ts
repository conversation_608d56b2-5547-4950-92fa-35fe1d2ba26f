import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremIntacctFinance from '../../../index';

describe(' Journal extension ', () => {
    before(() => {});

    it(' Journal - initPayload function ', () =>
        Test.withContext(
            async context => {
                const legislation = await context.read(xtremStructure.nodes.Legislation, {
                    id: 'US',
                });

                const payload = await xtremIntacctFinance.nodeExtensions.JournalExtension.initPayload(context, {
                    id: 'myJournal',
                    legislation,
                    intacctId: 'myJournal',
                    name: 'my <PERSON>',
                    recordNo: 8,
                    intacctName: 'JOURNAL',
                    statusIntacct: 'active',
                });

                /* does not throw */ await (async () =>
                    (await context.create(xtremFinanceData.nodes.Journal, payload)).$.save())();
            },
            {
                testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption],
            },
        ));
});
