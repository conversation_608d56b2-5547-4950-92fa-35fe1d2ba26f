import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacct from '@sage/xtrem-intacct';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../index';

describe(' Account extension ', () => {
    before(() => {});

    it(' Account - initPayload function ', () =>
        Test.withContext(
            async context => {
                const payload = await xtremIntacctFinance.nodeExtensions.AccountExtension.initPayload(context, {
                    id: 'myAccount',
                    intacctId: 'myAccount',
                    name: 'my account',
                    recordNo: 8,
                    isRequiredLocation: true,
                    isRequiredItem: true,
                    isRequiredSupplier: true,
                    isRequiredCustomer: true,
                    isRequiredProject: true,
                    isRequiredEmploye: false, // The attribute type is inactive
                    isRequiredDepartement: true,
                    isRequiredClass: true,
                    intacctName: 'ACCOUNT',
                    statusIntacct: 'active',
                    isDirectEntryForbidden: true,
                    taxManagement: 'other',
                });

                assert.isArray(payload.attributeTypes);
                assert.isArray(payload.dimensionTypes);

                /* does not throw */ await (async () =>
                    (await context.create(xtremFinanceData.nodes.Account, payload)).$.save())();
            },
            {
                testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption],
            },
        ));
});
