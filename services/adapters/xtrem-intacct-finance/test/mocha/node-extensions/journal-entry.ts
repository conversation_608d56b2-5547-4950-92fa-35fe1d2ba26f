import { Test } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../lib';
import { journalEntryPayload } from '../../fixtures/finance';

describe('Journal Entry', () => {
    it('Create the journal entry check if intacctDocuments are created  ', () =>
        Test.withContext(
            async context => {
                const journalEntry = await context.create(
                    xtremFinance.nodes.JournalEntry,
                    await journalEntryPayload(context),
                );
                await journalEntry.$.save();

                const intacctDocument = await journalEntry.intacctDocument;

                if (!intacctDocument) {
                    assert.fail('No intacct document for journal entry ');
                }

                assert.instanceOf(intacctDocument, xtremIntacctFinance.nodes.IntacctJournalEntry);

                await journalEntry.lines.forEach(async line => {
                    const intacctDocumentLine = await line.intacctDocumentLine;
                    if (!intacctDocumentLine) {
                        assert.fail('No intacct document line for journal entry ');
                    }
                });
            },
            {
                today: '2020-11-24',
            },
        ));
    it('Update the journal entry check if intacctDocuments are created  ', () =>
        Test.withContext(
            async context => {
                const number = 'IJ-2021040123';
                const journalEntry = await context.read(
                    xtremFinance.nodes.JournalEntry,
                    { number },
                    { forUpdate: true },
                );
                await journalEntry.$.save();

                const intacctDocument = await journalEntry.intacctDocument;

                if (!intacctDocument) {
                    assert.fail('No intacct document for journal entry ');
                }

                assert.instanceOf(intacctDocument, xtremIntacctFinance.nodes.IntacctJournalEntry);

                await journalEntry.lines.forEach(async line => {
                    const intacctDocumentLine = await line.intacctDocumentLine;
                    if (!intacctDocumentLine) {
                        assert.fail('No intacct document line for journal entry ');
                    }
                });
            },
            {
                today: '2020-11-24',
            },
        ));

    it('Update a journal entry status', () =>
        Test.withContext(async context => {
            const journal = await context.read(xtremFinanceData.nodes.Journal, {
                _id: '#US|IJ',
            });
            const journalEntry = await context.read(
                xtremFinance.nodes.JournalEntry,
                { number: 'IJ-001', journal },
                { forUpdate: true },
            );

            await journalEntry.$.set({
                intacctDocument: {
                    intacctId: 'newIntacctID',
                    recordNo: 1234,
                    state: 'success',
                },
            });

            const financeTransactionRecord: xtremFinanceData.interfaces.FinanceTransactionRecord = {
                batchId: 'cdf35002-6b75-4e4b-8a41-7ab11c18a52A',
                documentNumber: 'MISC_RECEIPT1',
                documentType: 'miscellaneousStockReceipt',
                targetDocumentType: 'journalEntry',
            };
            await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, {
                ...financeTransactionRecord,
                status: 'posted',
                targetDocumentNumber: await journalEntry.number,
                targetDocumentSysId: journalEntry._id,
                validationMessages: [],
            });
            let financeTransactionStatus = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                ...financeTransactionRecord,
            });
            assert.equal(await financeTransactionStatus.status, 'posted');

            financeTransactionRecord.batchId = 'cdf35002-6b75-4e4b-8a41-7ab11c18a53A';
            financeTransactionRecord.documentNumber = 'MISC_RECEIPT2';
            await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, {
                ...financeTransactionRecord,
                targetDocumentNumber: await journalEntry.number,
                targetDocumentSysId: journalEntry._id,
                status: 'posted',
                validationMessages: [],
            });
            financeTransactionStatus = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                ...financeTransactionRecord,
            });
            assert.equal(await financeTransactionStatus.status, 'posted');

            financeTransactionRecord.batchId = 'cdf35002-6b75-4e4b-8a41-7ab11c18a54A';
            financeTransactionRecord.documentNumber = 'MISC_RECEIPT3';
            await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, {
                ...financeTransactionRecord,
                targetDocumentNumber: await journalEntry.number,
                targetDocumentSysId: journalEntry._id,
                status: 'posted',
                validationMessages: [],
            });
            financeTransactionStatus = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                ...financeTransactionRecord,
            });
            assert.equal(await financeTransactionStatus.status, 'posted');
        }));
});
