import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as sinon from 'sinon';

describe('BuisnessEntity - Contacts & address', () => {
    it('Update supplier - address', () =>
        Test.withContext(async context => {
            const updateZA001 = await context.read(
                xtremMasterData.nodes.BusinessEntity,
                {
                    _id: '#500',
                },
                { forUpdate: true },
            );
            assert.isTrue(await updateZA001.isSupplier);
            const address1 = await updateZA001.addresses.at(0);

            if (!address1) {
                assert.fail('No address for 500');
            }
            const notifySpy = sinon.spy(context, 'notify');

            // update the contact then update the link supplier
            assert.isTrue(await address1.isPrimary);

            assert.equal(await address1.city, 'Somewhere');
            await address1.$.set({ city: 'toto' });
            await updateZA001.$.save();

            sinon.assert.callCount(notifySpy, 2);

            const supplierZa001 = await context.read(xtremMasterData.nodes.Supplier, { _id: '#500' });

            assert.isNotNull(supplierZa001);
            assert.strictEqual(await (await supplierZa001.intacctSupplier)?.intacctId, '500');
        }));

    it('New address without supplier or customer link to be', () =>
        Test.withContext(async context => {
            const updateBE = await context.read(
                xtremMasterData.nodes.BusinessEntity,
                { _id: '#500' },
                { forUpdate: true },
            );
            const notifySpy = sinon.spy(context, 'notify');

            await updateBE.addresses.append({ country: '#ZA', name: 'unit Test address' });

            await updateBE.$.save();

            // we create the new address, then we launch the update of the customer
            // Todo : see why 3 times
            sinon.assert.callCount(notifySpy, 3);

            const addressCreated = (await updateBE.addresses.find(
                async address => (await address.name) === 'unit Test address',
            ))!;
            assert.isNotNull(addressCreated);

            const intacctContact = await addressCreated.intacctBusinessEntityAddress;

            assert.isNotNull(intacctContact);

            const readUS0001 = await context.read(xtremMasterData.nodes.BusinessEntity, { _id: '#500' });

            const myAddress = await readUS0001.addresses.find(
                async address => (await address.name) === 'unit Test address',
            );

            assert.isNotNull(myAddress);
        }));
    it('Create two addresses ', () =>
        Test.withContext(async context => {
            const updateBE = await context.read(
                xtremMasterData.nodes.BusinessEntity,
                { _id: '#500' },
                { forUpdate: true },
            );

            await updateBE.addresses.append({ country: '#ZA', name: 'unit Test first address' });
            await updateBE.addresses.append({ country: '#ZA', name: 'unit Test second address' });

            await updateBE.$.save();

            await updateBE.addresses.forEach(async address => {
                const intacctAddress = await address.getSyncStateReference();
                assert.isNotNull(intacctAddress);
            });
        }));
    it('Create two addresses ', () =>
        Test.withContext(async context => {
            const businessEntity = await context.create(xtremMasterData.nodes.BusinessEntity, {
                id: 'SUPP07',
                isActive: true,
                name: 'Seventh Supplier',
                country: '#US',
                currency: '#USD',
                taxIdNumber: '202-12-2365',
                addresses: [
                    {
                        name: 'Main address',
                        country: '#US',
                    },
                    {
                        name: 'Second address',
                        country: '#US',
                    },
                ],
            });
            await businessEntity.$.save();

            /** To be able to read intacctAddress.sysId  */
            // rollbackCache function will flush deferred actions and saves
            await Test.rollbackCache(context);

            const beCreated = await context.read(xtremMasterData.nodes.BusinessEntity, { _id: '#SUPP07' });

            await beCreated.addresses.forEach(async address => {
                const intacctAddress = await address.getSyncStateReference();
                if (!intacctAddress) {
                    assert.fail('No intacct address');
                }
                assert.equal(await intacctAddress.sysId, address._id.toString());
            });
        }));
});
