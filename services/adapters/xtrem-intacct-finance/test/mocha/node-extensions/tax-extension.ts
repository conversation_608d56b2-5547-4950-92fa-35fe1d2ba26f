import { date, Test } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremTax from '@sage/xtrem-tax';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../index';

describe('Tax detail CRUD', () => {
    before(() => {});

    it('initPayload TaxDetail', () =>
        Test.withContext(
            async context => {
                const payload = (await xtremIntacctFinance.nodeExtensions.TaxExtension.initPayload(context, {
                    statusIntacct: 'active',
                    intacctId: 'SalesTax',
                    name: 'SalesTax',
                    description: 'Sales tax test',
                    intacctTaxType: 'Sale',
                    intacctAccount: '20610',
                    rate: '14',
                })) as { taxValues: { rate: any }[] };

                assert.isNotNull(payload);

                assert.strictEqual(payload?.taxValues[0].rate, '14');

                /* does not throw */ await (async () => (await context.create(xtremTax.nodes.Tax, payload)).$.save())();

                const tax =
                    (await context
                        .query(xtremTax.nodes.Tax, {
                            filter: { uIntacctId: 'SalesTax' },
                            forUpdate: true,
                        })
                        .at(0)) || null;

                assert.isNotNull(tax);

                if (tax) {
                    assert.strictEqual(await tax.isActive, false);
                }
            },
            {
                testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption],
            },
        ));

    it('initPayload Update TaxDetail', () =>
        Test.withContext(
            async context => {
                const firstPayload = await xtremIntacctFinance.nodeExtensions.TaxExtension.initPayload(context, {
                    statusIntacct: 'active',
                    intacctId: 'SalesTax',
                    name: 'SalesTax',
                    description: 'Sales tax test',
                    intacctTaxType: 'Sale',
                    intacctAccount: '20610',
                    rate: '14',
                });

                /* does not throw */ await (async () =>
                    (await context.create(xtremTax.nodes.Tax, firstPayload)).$.save())();

                const tax =
                    (await context
                        .query(xtremTax.nodes.Tax, {
                            filter: { uIntacctId: 'SalesTax' },
                            forUpdate: true,
                        })
                        .at(0)) || null;

                assert.isNotNull(tax);

                if (tax) {
                    await tax.$.set({ isActive: true });
                    await tax.$.set({ isUpdateFromIntacct: true });
                    await tax.$.save();
                    assert.strictEqual(await tax.isActive, true);
                }

                const updatedPayload = (await xtremIntacctFinance.nodeExtensions.TaxExtension.initPayload(context, {
                    intacctId: 'SalesTax',
                    name: 'SalesTax',
                    description: 'Sales tax test',
                    intacctTaxType: 'Sale',
                    intacctAccount: '20610',
                    rate: '20',
                })) as { taxValues: { rate: any }[] };

                assert.isNotNull(updatedPayload);
                assert.strictEqual(updatedPayload?.taxValues[0].rate, '20');

                if (tax) {
                    await tax.$.set(updatedPayload);
                    /* does not throw */ await (() => tax.$.save())();
                    assert.strictEqual(await tax.isActive, true);
                }
            },
            {
                testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption],
            },
        ));

    it('Delete TaxDetail', () =>
        Test.withContext(
            async context => {
                const payload = (await xtremIntacctFinance.nodeExtensions.TaxExtension.initPayload(context, {
                    statusIntacct: 'active',
                    intacctId: 'SalesTax',
                    name: 'SalesTax',
                    description: 'Sales tax test',
                    intacctTaxType: 'Sale',
                    intacctAccount: '20610',
                    rate: '14',
                })) as { taxValues: { rate: any }[] };

                assert.isNotNull(payload);
                assert.strictEqual(payload?.taxValues[0].rate, '14');

                /* does not throw */ await (async () => (await context.create(xtremTax.nodes.Tax, payload)).$.save())();

                const tax =
                    (await context
                        .query(xtremTax.nodes.Tax, {
                            filter: { uIntacctId: 'SalesTax' },
                            forUpdate: true,
                        })
                        .at(0)) || null;

                assert.isNotNull(tax);
                if (tax) {
                    await tax.$.set({ isActive: true });
                    await tax.$.save();
                    assert.strictEqual(await tax.isIntacct, true);

                    /* does not throw */ await (() =>
                        xtremIntacctFinance.nodeExtensions.TaxExtension.deactivateRecord(context, tax._id))();

                    const updateTaxRecord = await context.read(xtremTax.nodes.Tax, { _id: tax._id });
                    assert.strictEqual(await updateTaxRecord.isIntacct, false);
                }
            },
            {
                testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption],
            },
        ));

    it('Updating is active fail', () =>
        Test.withContext(
            async context => {
                const thirdPayload = await xtremIntacctFinance.nodeExtensions.TaxExtension.initPayload(context, {
                    statusIntacct: 'active',
                    intacctId: 'SalesTax',
                    name: 'SalesTax',
                    description: 'Sales tax test',
                    intacctTaxType: 'Sale',
                    intacctAccount: '20610',
                    rate: '14',
                    isIntacctReverseCharge: true,
                });

                /* does not throw */ await (async () =>
                    (await context.create(xtremTax.nodes.Tax, thirdPayload)).$.save())();

                const tax =
                    (await context
                        .query(xtremTax.nodes.Tax, {
                            filter: { uIntacctId: 'SalesTax' },
                            forUpdate: true,
                        })
                        .at(0)) || null;

                assert.isNotNull(tax);

                if (tax) {
                    await tax.$.set({
                        isActive: true,
                    });
                    await assert.isRejected(tax.$.save(), 'The record was not updated.');
                }
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption],
            },
        ));

    it('Updating is reverse charge fail', () =>
        Test.withContext(
            async context => {
                const secondPayload = await xtremIntacctFinance.nodeExtensions.TaxExtension.initPayload(context, {
                    statusIntacct: 'active',
                    intacctId: 'SalesTax',
                    name: 'SalesTax',
                    description: 'Sales tax test',
                    intacctTaxType: 'Sale',
                    intacctAccount: '20610',
                    rate: '14',
                    isIntacctReverseCharge: 'true',
                });

                /* does not throw */ await (async () =>
                    (await context.create(xtremTax.nodes.Tax, secondPayload)).$.save())();

                const tax =
                    (await context
                        .query(xtremTax.nodes.Tax, {
                            filter: { uIntacctId: 'SalesTax' },
                            forUpdate: true,
                        })
                        .at(0)) || null;

                assert.isNotNull(tax);

                if (tax) {
                    await tax.$.set({
                        isReverseCharge: true,
                    });
                    await assert.isRejected(tax.$.save(), 'The record was not updated.');
                }
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption],
            },
        ));

    it('Updating country fail', () =>
        Test.withContext(
            async context => {
                const secondPayload = await xtremIntacctFinance.nodeExtensions.TaxExtension.initPayload(context, {
                    statusIntacct: 'active',
                    intacctId: 'SalesTax',
                    name: 'SalesTax',
                    description: 'Sales tax test',
                    intacctTaxType: 'Sale',
                    intacctAccount: '20610',
                    rate: '14',
                    isIntacctReverseCharge: 'true',
                });

                /* does not throw */ await (async () =>
                    (await context.create(xtremTax.nodes.Tax, secondPayload)).$.save())();

                const tax =
                    (await context
                        .query(xtremTax.nodes.Tax, {
                            filter: { uIntacctId: 'SalesTax' },
                            forUpdate: true,
                        })
                        .at(0)) || null;

                assert.isNotNull(tax);

                if (tax) {
                    await tax.$.set({
                        country: '#ZA',
                        intacctSecondaryExternalReference: '#FR001',
                    });
                    await assert.isRejected(tax.$.save(), 'The record was not updated.');
                }
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption],
            },
        ));

    it('Updating primary external reference fail', () =>
        Test.withContext(
            async context => {
                const secondPayload = await xtremIntacctFinance.nodeExtensions.TaxExtension.initPayload(context, {
                    statusIntacct: 'active',
                    intacctId: 'SalesTax',
                    name: 'SalesTax',
                    description: 'Sales tax test',
                    intacctTaxType: 'Sale',
                    intacctAccount: '20610',
                    rate: '14',
                });

                /* does not throw */ await (async () =>
                    (await context.create(xtremTax.nodes.Tax, secondPayload)).$.save())();

                const tax =
                    (await context
                        .query(xtremTax.nodes.Tax, {
                            filter: { uIntacctId: 'SalesTax' },
                            forUpdate: true,
                        })
                        .at(0)) || null;

                assert.isNotNull(tax);

                if (tax) {
                    await tax.$.set({
                        primaryExternalReference: 'Test',
                    });
                    await assert.isRejected(tax.$.save(), 'The record was not updated.');
                }
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption],
            },
        ));

    it('Delete tax record failed', () =>
        Test.withContext(
            async context => {
                const firstPayload = await xtremIntacctFinance.nodeExtensions.TaxExtension.initPayload(context, {
                    statusIntacct: 'active',
                    intacctId: 'SalesTax',
                    name: 'SalesTax',
                    description: 'Sales tax test',
                    intacctTaxType: 'Sale',
                    intacctAccount: '20610',
                    rate: '14',
                });

                /* does not throw */ await (async () =>
                    (await context.create(xtremTax.nodes.Tax, firstPayload)).$.save())();

                const tax =
                    (await context
                        .query(xtremTax.nodes.Tax, {
                            filter: { uIntacctId: 'SalesTax' },
                            forUpdate: true,
                        })
                        .at(0)) || null;

                assert.isNotNull(tax);

                if (tax) {
                    await assert.isRejected(tax.$.delete(), 'The record was not deleted.');
                    assert.deepEqual(context.diagnoses, [
                        {
                            message: 'You cannot delete this tax record. It is linked to Sage Intacct.',
                            severity: 3,
                            path: [],
                        },
                    ]);
                }
            },
            {
                testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption],
            },
        ));

    it('Updating tax line fail', () =>
        Test.withContext(async context => {
            const myTax = await context.create(xtremTax.nodes.Tax, {
                primaryExternalReference: 'test',
                legalMention: 'Tax 88 legal mention',
                isActive: false,
                name: JSON.stringify({ en: 'Tax 88 name', 'fr-FR': 'French name ' }),
                country: await context.read(xtremStructure.nodes.Country, { id: 'FR' }),
                taxCategory: await context.read(xtremTax.nodes.TaxCategory, { id: 'US_TVA' }),
                taxValues: [{ rate: 0, deductibleRate: 0 }],
                isUpdateFromIntacct: false,
                isIntacct: true,
            });

            await myTax.$.save();

            const tax =
                (await context
                    .query(xtremTax.nodes.Tax, {
                        filter: { _id: myTax._id },
                        forUpdate: true,
                    })
                    .at(0)) || null;

            assert.isNotNull(tax);

            if (tax) {
                await tax.$.set({
                    taxValues: [
                        {
                            _id: (await myTax.taxValues.elementAt(0))._id,
                            endDate: date.parse('2021-12-29', context.currentLocale as any),
                            _action: 'update',
                        },
                    ],
                });
                await assert.isRejected(tax.$.save(), 'The record was not updated.');
            }
        }));

    it('Updating tax line fail 2', () =>
        Test.withContext(async context => {
            const myTax = await context.create(xtremTax.nodes.Tax, {
                primaryExternalReference: 'test',
                legalMention: 'Tax 88 legal mention',
                isActive: false,
                name: JSON.stringify({ en: 'Tax 88 name', 'fr-FR': 'French name ' }),
                country: await context.read(xtremStructure.nodes.Country, { id: 'FR' }),
                taxCategory: await context.read(xtremTax.nodes.TaxCategory, { id: 'US_TVA' }),
                taxValues: [{ rate: 0, deductibleRate: 0 }],
                isUpdateFromIntacct: false,
                isIntacct: true,
            });

            await myTax.$.save();

            const tax =
                (await context
                    .query(xtremTax.nodes.Tax, {
                        filter: { _id: myTax._id },
                        forUpdate: true,
                    })
                    .at(0)) || null;

            assert.isNotNull(tax);

            if (tax) {
                await tax.$.set({
                    taxValues: [
                        {
                            _id: (await myTax.taxValues.elementAt(0))._id,
                            rate: -1,
                            _action: 'update',
                        },
                    ],
                });
                await assert.isRejected(tax.$.save(), 'The record was not updated.');
            }
        }));

    it('Updating tax type fails', () =>
        Test.withContext(
            async context => {
                const secondPayload = await xtremIntacctFinance.nodeExtensions.TaxExtension.initPayload(context, {
                    statusIntacct: 'active',
                    intacctId: 'SalesTax',
                    name: 'SalesTax',
                    description: 'Sales tax test',
                    intacctTaxType: 'Sale',
                    intacctAccount: '20610',
                    rate: '14',
                    isIntacctReverseCharge: 'true',
                    type: 'sales',
                });

                /* does not throw */ await (async () =>
                    (await context.create(xtremTax.nodes.Tax, secondPayload)).$.save())();

                const tax =
                    (await context
                        .query(xtremTax.nodes.Tax, {
                            filter: { uIntacctId: 'SalesTax' },
                            forUpdate: true,
                        })
                        .at(0)) || null;

                assert.isNotNull(tax);

                if (tax) {
                    await tax.$.set({
                        type: 'purchasing',
                    });
                    await assert.isRejected(tax.$.save());
                    assert.deepEqual(tax.$.context.diagnoses, [
                        {
                            severity: 3,
                            path: [],
                            message: 'You cannot edit the tax type. It is linked to Sage Intacct.',
                        },
                    ]);
                    await assert.isRejected(tax.$.save(), 'The record was not updated.');
                }
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [xtremIntacct.serviceOptions.intacctOption],
            },
        ));
});
