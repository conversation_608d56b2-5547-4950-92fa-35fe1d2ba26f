import { Test } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import { getAccountPayableInvoicePayload, intacctJsonQuery } from '../../fixtures/account-payable-invoice';
import { getRequestManagerForFinanceDocuments, xmlWithoutFunctionAsJson } from '../../fixtures/synchronization';

let sandbox: sinon.SinonSandbox;

describe('Accounts payable invoice - intacct integration - test ', () => {
    beforeEach(() => {
        sandbox = sinon.createSandbox();
    });
    afterEach(() => {
        sandbox.reset();
        sandbox.restore();
    });

    it('Creation of an accounts payable invoice', () =>
        Test.withContext(async context => {
            const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument =
                await getAccountPayableInvoicePayload(context);

            (context as any)._contextValues.notificationId = 'eo34b4E02CiGyiiG0obC-';
            (context as any)._contextValues.replyTopic = 'PurchaseInvoice/accountingInterface';

            await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                financeDocument,
                notificationId: 'PI8',
                replyTopic: 'PurchaseInvoice/accountingInterface',
                isProcessed: false,
            });

            const createdAccountsPayableInvoice = await xtremFinance.functions.createOrUpdateAPARInvoice(
                context,
                'accountsPayableInvoice',
                'purchaseInvoice',
                'PI8',
                '7a1cc449-85dc-4fd0-98a4-08724c686226',
                'PurchaseInvoice/accountingInterface',
            );

            const apInvoice = await context.read(xtremFinance.nodes.AccountsPayableInvoice, { number: 'PI8' });

            assert.lengthOf(createdAccountsPayableInvoice.documentsCreated, 1);
            assert.deepEqual(createdAccountsPayableInvoice.documentsCreated.at(0), {
                documentNumber: 'PI8',
                documentSysId: apInvoice._id,
                type: 'accountsPayableInvoice',
            });

            assert.isEmpty(createdAccountsPayableInvoice.validationMessages);

            const intacctDocument = await apInvoice.intacctDocument;

            if (!intacctDocument) {
                assert.fail('No intacctDocument');
            }
            assert.equal(await apInvoice.lines.length, 1);

            const intacctDocumentLine = await (await apInvoice.lines.at(0))?.intacctDocumentLine;

            if (!intacctDocumentLine) {
                assert.fail('No intacct document line');
            }

            const requestManager = await getRequestManagerForFinanceDocuments(context, sandbox, intacctDocument);

            const intacctQuery = await xmlWithoutFunctionAsJson(context, { sandbox, requestManager });

            sandbox.reset();
            sandbox.restore();

            assert.deepEqual(intacctQuery, intacctJsonQuery, JSON.stringify(intacctQuery, null, 4));
        }));
});
