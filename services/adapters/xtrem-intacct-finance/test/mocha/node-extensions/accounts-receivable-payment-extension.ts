import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremIntacctFinance from '../../../index';

describe('Accounts receivable payment', () => {
    // Will be fix by refactoring pr naturalKey => sysId for synchronizationState
    it.skip('Test createArPayment mutation', () =>
        Test.withContext(
            async context => {
                const arPayment =
                    await xtremIntacctFinance.nodeExtensions.AccountsReceivablePaymentExtension.createArPayment(
                        context,
                        {
                            bankAccount: 1,
                            financialSite: 18,
                            payToCustomerId: '10093',
                            payToCustomerName: 'Local Customer',
                            description: 'Never found document 002',
                            paymentMethod: 'creditCard',
                            postingDate: '2022-11-01',
                            paymentDate: '2022-11-01',
                            currency: 1,
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2022-11-01',
                            paymentAmount: 540,
                            paymentCompanyAmount: 540,
                            bankFeed: **********,
                            arMatch: { isArMatch: false, arPaymentType: 'payment', matchingReasons: '' },
                            jsonArInvoices: { matchedArInvoices: [{ recordNo: 1391, arInvoiceAmountMatched: 540 }] },
                            lines: [
                                {
                                    type: 'salesInvoice',
                                    arInvoiceRecordNo: 1391,
                                    financialSite: 18,
                                    currency: 1,
                                    paymentAmount: 540,
                                    paymentCompanyAmount: 540,
                                },
                            ],
                        },
                    );

                assert.strictEqual(await arPayment.number, 'ARPEI220001');
            },
            {
                testActiveServiceOptions: [xtremIntacctFinance.serviceOptions.intacctCashbookManagement],
            },
        ));
});
