@xtrem_intacct_finance
Feature: smoke-test-static

    #Case with navigation panel full width
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                   | NavigationPanelTitle  | Title                |
            | @sage/xtrem-intacct-finance/IntacctMap | Sage Intacct mappings | Sage Intacct mapping |
            | @sage/xtrem-master-data/Item           | Items                 | Item                 |
            | @sage/xtrem-master-data/PaymentTerm    | Payment terms         | Payment term         |
            | @sage/xtrem-finance-data/DimensionType | Dimension types       | Dimension type       |

    #Case with navigation panel full width with multi action button
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled multi action button on the navigation panel
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                             | NavigationPanelTitle | Title    |
            | @sage/xtrem-master-data/Customer | Customers            | Customer |
            | @sage/xtrem-master-data/Supplier | Suppliers            | Supplier |

    # Positive test to ensure that the error dialog is definitely exist and open
    Scenario: sts \ xtrem-intacct-finance \ desktop \ check an error dialog appears when the page does not load
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Wazup"
        Then an error dialog appears on the main page
