@xtrem_intacct_finance
Feature: smoke-test-data

    #Case without navigation panel full width
    Scenario Outline: std \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                                    | Title                             |
            | @sage/xtrem-intacct-finance/IntacctMap/eyJfaWQiOiIxMCJ9 | Sage Intacct mapping Payment term |
            | @sage/xtrem-intacct/Intacct/eyJfaWQiOiIxIn0=            | Sage Intacct configuration XTREM  |
            | @sage/xtrem-master-data/Customer/eyJfaWQiOiIyNSJ9       | Customer Siège social S01 PARIS   |
            | @sage/xtrem-master-data/Item/eyJfaWQiOiI0MyJ9           | Item A bottle of milk             |
            | @sage/xtrem-master-data/Supplier/eyJfaWQiOiI0In0=       | Supplier Siège social S01 PARIS   |
            | @sage/xtrem-master-data/PaymentTerm/eyJfaWQiOiI3In0=    | Payment term 30 days EoM          |
            | @sage/xtrem-finance-data/DimensionType/eyJfaWQiOiIyIn0= | Dimension type Channel            |
