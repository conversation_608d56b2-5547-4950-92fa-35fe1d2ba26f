"document";"integration";"node";"sys_id";"natural_key";"state";"url";"secondary_page_link";"last_message";"notification_tracking_ids";"creation_stamp";"update_stamp";"difference";"intacct_id";"record_no"
"AP-**********|purchaseInvoice";"intacct";"AccountsPayableInvoice";"1";;"error";;"{}";"BL01001973 GL balancing account on line item 1 is not valid. [Support ID: AKCN-EB030%7EZR-6XP0940d-guZWBlWuzwAAAA0] Select a valid GL balancing account, and try again.<<}}LF{{>>BL01001973 Currently, we cannot create the transaction 'TOTO2015'. Check the transaction for errors or inconsistencies, then try again.";"[]";"2023-10-06T07:40:21.000Z";"2023-10-06T07:40:21.000Z";"{""differences"":[]}";;
"PIINTACCT01|purchaseInvoice";"intacct";"AccountsPayableInvoice";"6";;"not";;"{""page"":"""",""text"":"""",""parameters"":{}}";;"[]";"2024-01-18T10:24:17.000Z";"2024-01-18T10:24:17.000Z";"{""differences"":[]}";;
"PI240002|purchaseInvoice";"intacct";"AccountsPayableInvoice";"7";;"error";;"file:secondary-page-link--pi-240002-purchase-invoice.json";"Validation failed";"[]";"2024-04-03T09:45:42.000Z";"2024-04-03T09:45:42.000Z";"{""differences"":[]}";;
"PI240003|purchaseInvoice";"intacct";"AccountsPayableInvoice";"8";;"success";"https://www.intacct.com/ia/acct/ur.phtml?.r=qsnvkK7kvv6dRoreawSibFdArNuxeqm0V_07m2_EYCM";"file:secondary-page-link--pi-240003-purchase-invoice.json";;"[]";"2024-04-23T08:23:34.000Z";"2024-04-23T08:23:34.000Z";"{""differences"":[]}";"1982";"1982"
"TESTINTACCTFR|purchaseInvoice";"intacct";"AccountsPayableInvoice";"9";;"error";;"file:secondary-page-link--testintacctfr-purchase-invoice.json";"Validation failed";"[]";"2024-07-30T13:45:08.000Z";"2024-07-30T13:45:08.000Z";"{""differences"":[]}";;
"PI250007|purchaseInvoice";"intacct";"AccountsPayableInvoice";"13";;"error";;"file:secondary-page-link--pi-250007-purchase-invoice.json";"BL03002128 Enter a Bill number, and try again. [Support ID: 2zphb1-WEB001%7EZ7cgfP5o0rP4EKo9flr2lgAAAAI]<<}}LF{{>>BL01001973 Currently, we cannot create the transaction Check the transaction for errors or inconsistencies, then try again.";"[]";"2025-02-20T12:30:34.000Z";"2025-02-20T12:30:34.000Z";"{""differences"":[]}";;
"PI250008|purchaseInvoice";"intacct";"AccountsPayableInvoice";"14";;"error";;"file:secondary-page-link--pi-250008-purchase-invoice.json";"PL04000076 This user is in the LOCKEDOUT state [Support ID: aoJ2G1-WEB004%7EZ7imoP5X0or4TMp97DyOLQAAAAw]<<}}LF{{>>XL03000006 Login information is incorrect";"[]";"2025-02-21T16:15:27.000Z";"2025-02-21T16:15:27.000Z";"{""differences"":[]}";;
"PC250002|purchaseCreditMemo";"intacct";"AccountsPayableInvoice";"15";;"error";;"file:secondary-page-link--pc-250002-purchase-credit-memo.json";"PL04000076 This user is in the LOCKEDOUT state [Support ID: 2UtFl1-WEB004%7EZ7inGP5R0xh4gCA91c0DKAAAAAA]<<}}LF{{>>XL03000006 Login information is incorrect";"[]";"2025-02-21T16:17:30.000Z";"2025-02-21T16:17:30.000Z";"{""differences"":[]}";;
"PI250010|purchaseInvoice";"intacct";"AccountsPayableInvoice";"16";;"not";;"{""page"":"""",""text"":"""",""parameters"":{}}";;"[]";"2025-03-06T12:50:27.000Z";"2025-03-06T12:50:27.000Z";"{""differences"":[]}";;
"PI250011|purchaseInvoice";"intacct";"AccountsPayableInvoice";"17";;"not";;"{""page"":"""",""text"":"""",""parameters"":{}}";;"[]";"2025-03-06T12:51:15.000Z";"2025-03-06T12:51:15.000Z";"{""differences"":[]}";;
"PI250016|purchaseInvoice";"intacct";"AccountsPayableInvoice";"18";"PI250016|purchaseInvoice";"error";;"file:secondary-page-link--pi-250016-purchase-invoice.json";"XL03000006 Incorrect Intacct XML Partner ID or password. [Support ID: QoNHp1-WEB150%7EaH4WNP5-0PD45ZN9QE2hAwAAAAo]";"[""f3_0kWOiageonqZVYrwGq""]";"2025-07-21T10:28:03.000Z";"2025-07-21T10:28:03.000Z";"{""differences"":[]}";;
"PI250017|purchaseInvoice";"intacct";"AccountsPayableInvoice";"19";"PI250017|purchaseInvoice";"not";;"file:secondary-page-link--pi-250017-purchase-invoice.json";;"[]";"2025-07-21T10:40:27.000Z";"2025-07-21T10:40:27.000Z";"{""differences"":[]}";;
