"id";"execution_user";"_vendor";"operation";"description";"is_active";"execution_locale";"parameter_values";"cron_schedule";"time_zone"
"companyOnHold_1";"<EMAIL>";"sage";"Company|syncCompanyOnHold|start";"Update company on hold check from Intacct";"Y";"en-US";"{""isAllCompanies"":""true""}";"0 3 * * *";"Europe/Paris"
"updateCustomMapping";"<EMAIL>";"sage";"IntacctMap|updateCustomMapping|start";"Update the custom mapping";"Y";"en-US";"{""filter"":""{_id:{_nin:[]}}""}";"0 8 * * 6";"Europe/Paris"
"updateRecordNoOnEmployee";"<EMAIL>";"sage";"Attribute|updateRecordNoOnEmployee|start";"Update recordNo on employee";"Y";"en-US";"file:parameter-values--update-record-no-on-employee-root-localhost-domain.json";"0 2,14 * * 3";"Europe/Paris"
