"role";"activity";"_vendor";"_sort_value";"has_all_permissions";"permissions";"is_active"
"100";"intacctMap";"sage";"1500";"Y";"[]";"Y"
"100";"financeListener";"sage";"15400";"Y";"[]";"Y"
"Admin - Technical";"intacctMap";"sage";"1000";"Y";"[]";"Y"
"Support User";"intacctBankAccountTransactionFeed";"sage";"1400";"Y";"[]";"Y"
"Support User";"intacctMap";"sage";"1500";"Y";"[]";"Y"
"Support User Read-only";"intacctBankAccountTransactionFeed";"sage";"1400";"N";"[""read""]";"Y"
"Support User Read-only";"intacctMap";"sage";"1500";"N";"[""read""]";"Y"
"Admin";"intacctMap";"sage";"11300";"Y";"[]";"Y"
"Admin";"intacctBankAccountTransactionFeed";"sage";"11400";"Y";"[]";"Y"
"Admin";"financeListener";"sage";"11800";"Y";"[]";"Y"
"Business User";"intacctBankAccountTransactionFeed";"sage";"2700";"Y";"[]";"Y"
"Business User";"financeListener";"sage";"2800";"Y";"[]";"Y"
"1100";"financeListener";"sage";"260";"N";"[""retryFinanceDocument""]";"Y"
"1100";"intacctMap";"sage";"270";"N";"[""read"",""manage"",""synchronizationWithSageIntacct""]";"Y"
