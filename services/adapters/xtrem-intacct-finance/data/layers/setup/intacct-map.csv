"application";"third_party_object_name";"node_factory";"_vendor";"synchronization_direction";"is_active";"can_delete";"can_create";"can_update";"id";"intacct_description";"is_smart_event";"editable_fields"
"intacct";"CONTACT";"BusinessEntityAddress";"sage";"outbound";"Y";"Y";"Y";"Y";"CONTACT";"Contact";"N";"[""PREFIX"",""PRINTAS"",""PHONE2""]"
"intacct";"ITEM";"Item";"sage";"outbound";"Y";"Y";"Y";"Y";"ITEM";"Item";"Y";"[""NAME"",""EXTENDED_DESCRIPTION""]"
"intacct";"VENDOR";"Supplier";"sage";"outbound";"Y";"Y";"Y";"Y";"VENDOR";"Vendor";"Y";"[""HIDEDISPLAYCONTACT""]"
"intacct";"CUSTOMER";"Customer";"sage";"outbound";"Y";"Y";"Y";"Y";"CUSTOMER";"Customer";"Y";"[""HIDEDISPLAYCONTACT""]"
"intacct";"DEPARTMENT";"Dimension";"sage";"inbound";"Y";"Y";"Y";"Y";"DEPARTMENT";"Department";"Y";"[]"
"intacct";"EMPLOYEE";"Attribute";"sage";"inbound";"Y";"Y";"Y";"Y";"EMPLOYEE";"Employee";"Y";"[]"
"intacct";"CLASS";"Dimension";"sage";"inbound";"Y";"Y";"Y";"Y";"CLASS";"Class";"Y";"[]"
"intacct";"PROJECT";"Attribute";"sage";"inbound";"Y";"Y";"Y";"Y";"PROJECT";"Project";"Y";"[]"
"intacct";"APTERM";"PaymentTerm";"sage";"inbound";"Y";"Y";"Y";"Y";"APTERM";"AP Term";"Y";"[]"
"intacct";"ARTERM";"PaymentTerm";"sage";"inbound";"Y";"Y";"Y";"Y";"ARTERM";"AR Term";"Y";"[]"
"intacct";"GLACCOUNT";"Account";"sage";"inbound";"Y";"Y";"Y";"Y";"GLACCOUNT";"GL Account";"Y";"[""REQUIREVENDOR"",""REQUIREPROJECT"",""REQUIRETASK"",""REQUIRELOC"",""REQUIREITEM"",""REQUIREEMPLOYEE"",""REQUIREDEPT"",""REQUIRECUSTOMER"",""REQUIRECLASS"",""SUBLEDGERCONTROLON""]"
"intacct";"JOURNAL";"Journal";"sage";"inbound";"Y";"Y";"Y";"Y";"JOURNAL";"Journal";"Y";"[""NAME"",""STATUS""]"
"intacct";"GLBATCH";"JournalEntry";"sage";"outbound";"Y";"N";"Y";"N";"GLBATCH";"GL Batch";"N";"[""BATCH_TITLE""]"
"intacct";"GLENTRY";"JournalEntryLine";"sage";"outbound";"Y";"N";"Y";"N";"GLENTRY";"GL Entry";"N";"[""DOCUMENT"",""DESCRIPTION""]"
"intacct";"APBILL";"AccountsPayableInvoice";"sage";"outbound";"Y";"N";"Y";"N";"APBILL";"AP Bill";"N";"[""DESCRIPTION"",""TERMNAME"",""INCLUSIVETAX""]"
"intacct";"APBILLITEM";"AccountsPayableInvoiceLine";"sage";"outbound";"Y";"N";"Y";"N";"APBILLITEM";"AP Bill Detail";"N";"[""OFFSETGLACCOUNTNO""]"
"intacct";"ARINVOICE";"AccountsReceivableInvoice";"sage";"outbound";"Y";"N";"Y";"N";"ARINVOICE";"AR Invoice";"N";"[""DESCRIPTION""]"
"intacct";"ARINVOICEITEM";"AccountsReceivableInvoiceLine";"sage";"outbound";"Y";"N";"Y";"N";"ARINVOICEITEM";"AR Invoice Detail";"N";"[""OFFSETGLACCOUNTNO""]"
"intacct";"TAXDETAIL";"Tax";"sage";"inbound";"Y";"N";"Y";"Y";"TAXDETAIL";"Tax Detail";"N";"[""DESCRIPTION""]"
"intacct";"BANKACCTTXNRECORD";"IntacctBankAccountTransactionFeed";"sage";"inbound";"Y";"Y";"Y";"Y";"BANKACCTTXNRECORD";"Bank account transaction feed records";"N";"[]"
"intacct";"ARPYMT";"AccountsReceivablePayment";"sage";"outbound";"Y";"N";"Y";"N";"ARPYMT";"AR Receivables Payment";"N";"[""DESCRIPTION""]"
"intacct";"ARPYMTDETAIL";"AccountsReceivablePaymentLine";"sage";"outbound";"Y";"N";"Y";"N";"ARPYMTDETAIL";"AR Receivables Payment Details";"N";"[]"
"intacct";"ARADVANCE";"AccountsReceivableAdvance";"sage";"outbound";"Y";"N";"Y";"N";"ARADVANCE";"AR Advance";"N";"[]"
"intacct";"ARADVANCEITEM";"AccountsReceivableAdvanceLine";"sage";"outbound";"Y";"N";"Y";"N";"ARADVANCEITEM";"AR Advance Detail";"N";"[]"
"intacct";"TASK";"Attribute";"sage";"inbound";"Y";"Y";"Y";"Y";"TASK";"Task";"Y";"[]"
