{"name": "GLACCOUNT", "documentType": "GL Account", "xtremObject": "account", "additionnalLink": [{"xtremProperty": "chartOfAccount", "type": "node", "intacctConfigProperty": "chartOfAccount"}], "fields": [{"ID": "ACCOUNTNO", "LABEL": "Account number", "DESCRIPTION": "Account number", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctId, id"}, {"ID": "TITLE", "LABEL": "Title", "DESCRIPTION": "Title", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "name"}, {"ID": "ACCOUNTTYPE", "LABEL": "Account type", "DESCRIPTION": "Account type", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["balancesheet", "incomestatement"]}, "ISCUSTOM": false}, {"ID": "NORMALBALANCE", "LABEL": "Normal balance", "DESCRIPTION": "Noraml balance", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["debit", "credit"]}, "ISCUSTOM": false}, {"ID": "CLOSINGTYPE", "LABEL": "Period end closing type", "DESCRIPTION": "Period end closing type", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["non-closing account", "closing account", "closed to account"]}, "ISCUSTOM": false}, {"ID": "CLOSINGACCOUNTNO", "LABEL": "Close into account", "DESCRIPTION": "Close to Account", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Inactive", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "inactive"]}, "ISCUSTOM": false, "xtremProperty": "statusIntacct"}, {"ID": "REQUIREDEPT", "LABEL": "Department", "DESCRIPTION": "Require Department", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": "isRequiredDepartement"}, {"ID": "REQUIRELOC", "LABEL": "Location", "DESCRIPTION": "Require Location", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": "isRequiredLocation"}, {"ID": "TAXABLE", "LABEL": "Taxable", "DESCRIPTION": "Is Taxable", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "TAXCODE", "LABEL": "Tax return code", "DESCRIPTION": "Tax return code", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MRCCODE", "LABEL": "M-3 return code", "DESCRIPTION": "M-3 Return Code", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CATEGORY", "LABEL": "QuickStart Category", "DESCRIPTION": "Category", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CLOSETOACCTKEY", "LABEL": "Close-to account record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "ALTERNATIVEACCOUNT", "LABEL": "GL account alternative", "DESCRIPTION": "GL account alternative", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["None", "Payables account", "Receivables account"]}, "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "CREATEDBY", "LABEL": "Created by", "DESCRIPTION": "User who created this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DESCRIPTION": "User who modified this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "SUBLEDGERCONTROLON", "LABEL": "Disallow Direct Posting", "DESCRIPTION": "Disallow Direct Posting", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": "isDirectEntryForbidden"}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "REQUIREPROJECT", "LABEL": "Project", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": "isRequiredProject"}, {"ID": "REQUIRETASK", "LABEL": "Task", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": "isRequiredTask"}, {"ID": "REQUIRECUSTOMER", "LABEL": "Customer", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": "isRequiredCustomer"}, {"ID": "REQUIREVENDOR", "LABEL": "<PERSON><PERSON><PERSON>", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": "isRequiredSupplier"}, {"ID": "REQUIREEMPLOYEE", "LABEL": "Employee", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": "isRequiredEmploye"}, {"ID": "REQUIREITEM", "LABEL": "<PERSON><PERSON>", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": "isRequiredItem"}, {"ID": "REQUIRECLASS", "LABEL": "Class", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": "isRequiredClass"}, {"ID": "REQUIREWAREHOUSE", "LABEL": "Warehouse", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}], "relationships": [{"OBJECTPATH": "CRWGLACCTGROUP", "OBJECTNAME": "CRWGLACCTGROUP", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "RECORDNO"}, {"OBJECTPATH": "MELOCATION", "OBJECTNAME": "LOCATION", "LABEL": "Created At Entity Information", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MEGAENTITYID"}], "relationshipFields": [{"DATATYPE": "RECORDNO", "ID": "RECORDNO", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "RECORDNO", "xtremProperty": ""}, {"DATATYPE": "CLOSINGACCOUNTTITLE", "ID": "CLOSINGACCOUNTTITLE", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CLOSINGACCOUNTTITLE", "xtremProperty": ""}, {"DATATYPE": "CATEGORYKEY", "ID": "CATEGORYKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CATEGORYKEY", "xtremProperty": ""}, {"DATATYPE": "REQUIREGLDIMREGION", "ID": "REQUIREGLDIMREGION", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "REQUIREGLDIMREGION", "xtremProperty": ""}, {"DATATYPE": "RDEPARTMENT", "ID": "RDEPARTMENT", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "RDEPARTMENT", "xtremProperty": ""}]}