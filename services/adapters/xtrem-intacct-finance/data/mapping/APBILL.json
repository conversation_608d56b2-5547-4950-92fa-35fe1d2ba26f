{"name": "APBILL", "fields": [{"ID": "RECORDNO", "LABEL": "Record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "recordNo,intacctId"}, {"ID": "RECORDTYPE", "LABEL": "Record type", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RECORDID", "LABEL": "Bill number", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "supplierDocumentNumber"}, {"ID": "FINANCIALENTITY", "LABEL": "Financial entity", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "STATE", "LABEL": "State", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RAWSTATE", "LABEL": "Raw state", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "VENDORID", "LABEL": "Vendor ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocument.billBySupplier"}, {"ID": "VENDORNAME", "LABEL": "Vendor name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "FORM1099TYPE", "LABEL": "Form 1099 type", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "FORM1099BOX", "LABEL": "Form 1099 box", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "VENDTYPE1099TYPE", "LABEL": "Vendor type form 1099 type", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_ENTITYDUE", "LABEL": "Vendor due", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "DOCNUMBER", "LABEL": "Reference number", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "number"}, {"ID": "DESCRIPTION", "LABEL": "Description", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "reference"}, {"ID": "DESCRIPTION2", "LABEL": "Document ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TERMNAME", "LABEL": "Term", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "paymentTerm.intacctId"}, {"ID": "TERMKEY", "LABEL": "Term key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TERMVALUE", "LABEL": "Term value", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENCREATED", "LABEL": "Date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "invoiceDate"}, {"ID": "WHENPOSTED", "LABEL": "GL posting date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "postingDate"}, {"ID": "WHENDISCOUNT", "LABEL": "Discount date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENDUE", "LABEL": "Due date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "dueDate"}, {"ID": "WHENPAID", "LABEL": "Date fully paid", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RECPAYMENTDATE", "LABEL": "Recommended to pay on", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PAYMENTPRIORITY", "LABEL": "Payment priority", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": {"VALIDVALUE": ["urgent", "high", "normal", "low"]}, "xtremProperty": ""}, {"ID": "ONHOLD", "LABEL": "Place this bill on hold", "DATATYPE": "BOOLEAN", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "xtremProperty": ""}, {"ID": "BASECURR", "LABEL": "Base currency", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "financialSite.legalCompany.currency.id"}, {"ID": "CURRENCY", "LABEL": "Transaction currency", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "currency.id"}, {"ID": "EXCH_RATE_DATE", "LABEL": "Exchange rate date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "fxRateDate"}, {"ID": "EXCH_RATE_TYPE_ID", "LABEL": "Exchange rate type", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "EXCHANGE_RATE", "LABEL": "Exchange rate", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "companyFxRate"}, {"ID": "TOTALENTERED", "LABEL": "Total amount", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALSELECTED", "LABEL": "Total selected", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALPAID", "LABEL": "Total paid", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALDUE", "LABEL": "Total due", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALENTERED", "LABEL": "Total transaction amount", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALSELECTED", "LABEL": "Total transaction amount selected", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALPAID", "LABEL": "Total transaction amount paid", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALDUE", "LABEL": "Total transaction amount due", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTOPAYTOCONTACTNAME", "LABEL": "Pay to", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocument.billToPayToContactName"}, {"ID": "SHIPTORETURNTOCONTACTNAME", "LABEL": "Return to", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocument.shipToReturnToContactName"}, {"ID": "BILLTOPAYTOKEY", "LABEL": "Pay-to contact key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTORETURNTOKEY", "LABEL": "Return-to contact key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PRBATCH", "LABEL": "Summary", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PRBATCHKEY", "LABEL": "Summary key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MODULEKEY", "LABEL": "Module key", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SCHOPKEY", "LABEL": "Recurrung schedule key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SYSTEMGENERATED", "LABEL": "System generated", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": {"VALIDVALUE": ["T", "F"]}, "xtremProperty": ""}, {"ID": "AUWHENCREATED", "LABEL": "Audit when created", "DATATYPE": "TIMESTAMP", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Audit timestamp when record was created.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DATATYPE": "TIMESTAMP", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CREATEDBY", "LABEL": "Created by", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "User who created this.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "User who modified this.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "DUE_IN_DAYS", "LABEL": "Due in", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PAYTO.TAXGROUP.NAME", "LABEL": "Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "Name", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PAYTO.TAXID", "LABEL": "Tax ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Tax Identification Number", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "INCLUSIVETAX", "LABEL": "Inclusive taxes", "DATATYPE": "BOOLEAN", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "xtremProperty": "inclusiveTax"}, {"ID": "TAXSOLUTIONID", "LABEL": "Tax solution", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Tax solution", "VALIDVALUES": null, "xtremProperty": "intacctDocument.taxSolutionId"}, {"ID": "RETAINAGEPERCENTAGE", "LABEL": "Default retainage percentage", "DATATYPE": "PERCENT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALRETAINED", "LABEL": "Total txn amount retained", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALRELEASED", "LABEL": "Total txn amount released", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALRETAINED", "LABEL": "Total retained amount", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SUPDOCID", "LABEL": "Attachment", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLBACKTEMPLATE", "LABEL": "<PERSON> back template", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "USERID", "LABEL": "Modified by", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Modified by", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CREATEDUSERID", "LABEL": "Created by", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Created By", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "DOCSOURCE", "LABEL": "Source", "DATATYPE": "ENUM", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": {"VALIDVALUE": null}, "xtremProperty": ""}, {"ID": "UPLOADSTATUS", "LABEL": "Import exceptions", "DATATYPE": "ENUM", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": {"VALIDVALUE": ["Resolve", "Completed", "Pending", "Draft ready"]}, "xtremProperty": ""}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "VALIDVALUES": null, "xtremProperty": "intacctUrl"}], "xtremObject": "accounts_payable_invoice", "documentType": "AP Bill", "relationships": [{"LABEL": "", "RELATEDBY": "VENDORID", "OBJECTNAME": "VENDOR", "OBJECTPATH": "VENDOR", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "RECORD#", "OBJECTNAME": "PODOCUMENT", "OBJECTPATH": "PODOCUMENT", "xtremProperty": "", "RELATIONSHIPTYPE": "ONE2ONE"}, {"LABEL": "Pay to Contact", "RELATEDBY": "BILLTOPAYTOKEY", "OBJECTNAME": "CONTACTVERSION", "OBJECTPATH": "PAYTO", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "Return to Contact", "RELATEDBY": "SHIPTORETURNTOKEY", "OBJECTNAME": "CONTACTVERSION", "OBJECTPATH": "RETURNTO", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "Term", "RELATEDBY": "TERMNAME", "OBJECTNAME": "APTERM", "OBJECTPATH": "TERM", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "RECORDNO", "OBJECTNAME": "EXCHANGERATEINFO", "OBJECTPATH": "EXCHANGERATEINFO", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "SCHOPKEY", "OBJECTNAME": "APRECURBILL", "OBJECTPATH": "APRECURBILL", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "TAXSOLUTIONKEY", "OBJECTNAME": "TAXSOLUTION", "OBJECTPATH": "TAXSOLUTION", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "Created At Entity Information", "RELATEDBY": "MEGAENTITYID", "OBJECTNAME": "LOCATION", "OBJECTPATH": "MELOCATION", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}], "relationshipFields": [{"ID": "PAYTO.TAXGROUP.RECORDNO", "LABEL": "RECORDNO", "DATATYPE": "PAYTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "TAXGROUP.RECORDNO", "VALIDVALUES": null, "xtremProperty": ""}]}