{"name": "BANKACCTTXNRECORD", "additionnalLink": [], "fields": [{"ID": "RECORDNO", "LABEL": "Record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "number , intacctId"}, {"ID": "FINANCIALENTITY", "LABEL": "Bank account ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "entity"}, {"ID": "FINANCIALENTITYNAME", "LABEL": "Bank name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "entityName"}, {"ID": "FINANCIALACCOUNTTYPE", "LABEL": "Account type", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "accountType"}, {"ID": "FINACCTTXNFEEDKEY", "LABEL": "Financial account feed record no", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "accountFeedKey"}, {"ID": "TRANSACTIONID", "LABEL": "Transaction ID", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "transactionId"}, {"ID": "BANKACCTRECONKEY", "LABEL": "Account recon#", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "accountReconKey"}, {"ID": "POSTINGDATE", "LABEL": "Posting date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "Payment posting date", "VALIDVALUES": null, "xtremProperty": "postingDate"}, {"ID": "RECDATE", "LABEL": "Reconciliation date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "Reconciliation date", "VALIDVALUES": null, "xtremProperty": "reconcilitationDate"}, {"ID": "TRANSACTIONTYPE", "LABEL": "Transaction type", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "transactionType"}, {"ID": "DOCTYPE", "LABEL": "Document type", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "documentType"}, {"ID": "DOCNO", "LABEL": "Document number", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "documentNumber"}, {"ID": "PAYEE", "LABEL": "Payee", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "payee"}, {"ID": "AMOUNT", "LABEL": "Amount", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "amount"}, {"ID": "DESCRIPTION", "LABEL": "Description", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "description"}, {"ID": "CLEARED", "LABEL": "Status", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Cleared", "VALIDVALUES": {"VALIDVALUE": ["Unmatched", "Cleared", "Matched", "Partially matched", "Selected to match", "Selected to unmatch", "Ignored", "Draft matched"]}, "xtremProperty": "intacctCleared"}, {"ID": "AMOUNTTOMATCH", "LABEL": "Amount to match", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "amountToMatch"}, {"ID": "WHENCREATED", "LABEL": "When created", "DATATYPE": "TIMESTAMP", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "timestamp marking last time this was created.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DATATYPE": "TIMESTAMP", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "timestamp marking last time this was changed.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CREATEDBY", "LABEL": "Created by", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "User who created this.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "User who modified this.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "FEEDTYPE", "LABEL": "Feed type", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "feedType"}, {"ID": "CURRENCY", "LABEL": "<PERSON><PERSON><PERSON><PERSON>", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "<PERSON><PERSON><PERSON><PERSON>", "VALIDVALUES": null, "xtremProperty": "currency.id"}, {"ID": "RMYOWNBANKRECON", "LABEL": "MyOwnBankRecon", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "VALIDVALUES": null, "xtremProperty": ""}], "xtremObject": "intacct_bank_account_transaction_feed", "documentType": "", "relationships": [{"LABEL": "MyOwnBankRecon", "RELATEDBY": "RMYOWNBANKRECON", "OBJECTNAME": "MYOWNBANKRECON", "OBJECTPATH": "R10010", "xtremProperty": null, "RELATIONSHIPTYPE": "MANY2ONE"}], "relationshipFields": []}