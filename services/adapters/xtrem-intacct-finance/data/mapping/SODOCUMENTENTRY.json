{"name": "SODOCUMENTENTRY", "documentType": "Sales Order Detail", "fields": [{"ID": "DOCHDRNO", "LABEL": "Document header number", "DESCRIPTION": "Document Header Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "LINE_NO", "LABEL": "Line number", "DESCRIPTION": "Line Number", "REQUIRED": true, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "intacctId"}, {"ID": "ITEMID", "LABEL": "Item ID", "DESCRIPTION": "Item ID", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "item", "xtremPropertyOption": "item.id"}, {"ID": "ITEMDESC", "LABEL": "Item description", "DESCRIPTION": "Item Description", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "UNIT", "LABEL": "Unit", "DESCRIPTION": "Unit", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "stockUnit", "xtremPropertyOption": "unit_of_measure.intacctName"}, {"ID": "MEMO", "LABEL": "Memo", "DESCRIPTION": "Memo", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PRICECALCMEMO", "LABEL": "Price calculation memo", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "QUANTITY", "LABEL": "Quantity", "DESCRIPTION": "Quantity", "REQUIRED": true, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false, "xtremPropertyOption": "", "xtremProperty": "quantityInStockUnit"}, {"ID": "QTY_CONVERTED", "LABEL": "Qty converted", "DESCRIPTION": "Quantity Converted", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "PRICE", "LABEL": "Price", "DESCRIPTION": "Price", "REQUIRED": true, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "TOTAL", "LABEL": "Extended price", "DESCRIPTION": "Extended Total Price", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "Date created", "DESCRIPTION": "Date Created", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "Date modified", "DESCRIPTION": "Date Modified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "AUWHENCREATED", "LABEL": "Audit when created", "DESCRIPTION": "Audit timestamp when record was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "EXTENDED_DESCRIPTION", "LABEL": "Extended description", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "STATE", "LABEL": "State", "DESCRIPTION": "State", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Inactive", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "inactive"]}, "ISCUSTOM": false}, {"ID": "COST", "LABEL": "Cost", "DESCRIPTION": "Cost", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "COST_METHOD", "LABEL": "Cost method", "DESCRIPTION": "Cost Method", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "UIQTY", "LABEL": "Quantity", "DESCRIPTION": "Quantity", "REQUIRED": true, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "DISCOUNTPERCENT", "LABEL": "Discount %", "DESCRIPTION": "Discount %", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "DISCOUNT_MEMO", "LABEL": "Notes", "DESCRIPTION": "Discount/Charge", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MULTIPLIER", "LABEL": "Multiplier", "DESCRIPTION": "Multiplier", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "UIPRICE", "LABEL": "Price", "DESCRIPTION": "Price", "REQUIRED": true, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "UIVALUE", "LABEL": "Extended price", "DESCRIPTION": "Extended Total Price", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "LOCATIONID", "LABEL": "Location ID", "DESCRIPTION": "Locationid", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "LOCATIONNAME", "LABEL": "Location name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DEPARTMENTID", "LABEL": "Department  ID", "DESCRIPTION": "Departmentid", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DEPARTMENTNAME", "LABEL": "Department name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DEPTKEY", "LABEL": "Department key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "LOCATIONKEY", "LABEL": "Location key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SOURCE_DOCKEY", "LABEL": "Source document key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SOURCE_DOCLINEKEY", "LABEL": "Source document line key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "LOCATION", "LABEL": "Location", "DESCRIPTION": "Location", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DEPARTMENT", "LABEL": "Department", "DESCRIPTION": "Department", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TRX_CURRENCY_SYMBOL", "LABEL": "Transaction currency symbol", "DESCRIPTION": "Transaction Currency Symbol", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "BILLABLE", "LABEL": "Billable", "DESCRIPTION": "Billable", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "BILLED", "LABEL": "Billed", "DESCRIPTION": "Billed", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "TRX_PRICE", "LABEL": "Transaction price", "DESCRIPTION": "Transaction Price", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "TRX_VALUE", "LABEL": "Extended transaction price", "DESCRIPTION": "Extended Transaction Price", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "PERCENTVAL", "LABEL": "Tax rate", "DESCRIPTION": "Line Tax Rate", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "TAXABSVAL", "LABEL": "Tax", "DESCRIPTION": "Line Tax", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "LINETOTAL", "LABEL": "Gross amount", "DESCRIPTION": "Totals amount for line", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "DISCOUNT", "LABEL": "Discount", "DESCRIPTION": "Line Discount", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "TRX_TAXABSVAL", "LABEL": "Transaction tax", "DESCRIPTION": "Transaction Line Tax", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "TRX_LINETOTAL", "LABEL": "Transaction gross amount", "DESCRIPTION": "Transaction totals amount for line", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "TOTAL_AMOUNT_CONVERTED", "LABEL": "Total amount converted", "DESCRIPTION": "Total Amount Converted", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "TOTAL_AMOUNT_REMAINING", "LABEL": "Total amount remaining", "DESCRIPTION": "Total Amount Remaining", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "QTY_REMAINING", "LABEL": "Quantity remaining", "DESCRIPTION": "Quantity Remaining", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "PRICE_CONVERTED", "LABEL": "Price converted", "DESCRIPTION": "Price converted", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "CONVERSIONTYPE", "LABEL": "Conversion type", "DESCRIPTION": "Conversion type", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["Quantity", "Price"]}, "ISCUSTOM": false}, {"ID": "BUNDLENUMBER", "LABEL": "Bundle", "DESCRIPTION": "Bundle Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RETAILPRICE", "LABEL": "Suggested price", "DESCRIPTION": "RetailPrice", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "REVRECSTARTDATE", "LABEL": "Start date", "DESCRIPTION": "Revenue Recognition Start Date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "REVRECENDDATE", "LABEL": "End date", "DESCRIPTION": "Revenue Recognition End Date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "TIMENOTES", "LABEL": "Timesheet notes", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "GENINVOICELINEKEY", "LABEL": "Preview line key", "DESCRIPTION": "Preview line key", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "LINELEVELSIMPLETAXTYPE", "LABEL": "Tax Type", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "NEEDBYDATE", "LABEL": "Need by date", "DESCRIPTION": "Need by date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "SHIPBY", "LABEL": "Ship by date", "DESCRIPTION": "Ship by date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "DONOTSHIPBEFOREDATE", "LABEL": "Do not ship before date", "DESCRIPTION": "Do not ship before date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "DONOTSHIPAFTERDATE", "LABEL": "Do not ship after date", "DESCRIPTION": "Do not ship after date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "DATEPICKTICKETPRINTED", "LABEL": "Pick ticket printed date", "DESCRIPTION": "Pick ticket printed date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "CANCELAFTERDATE", "LABEL": "Cancel after date", "DESCRIPTION": "Cancel after date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "BTOSHIPTOCONTACTNAME", "LABEL": "Buy-to-order Deliver to", "DESCRIPTION": "Buy to Order Deliver to", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RETAINAGEPERCENTAGE", "LABEL": "Retainage Percentage", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "PERCENT", "ISCUSTOM": false}, {"ID": "AMOUNTRETAINED", "LABEL": "Amount retained", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "TRX_AMOUNTRETAINED", "LABEL": "Txn amount retained", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "SHIPPEDDATE", "LABEL": "Shipped date", "DESCRIPTION": "Shipped date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "CLASSNAME", "LABEL": "Class Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CLASSID", "LABEL": "Class", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CUSTOMERNAME", "LABEL": "Customer Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CUSTOMERID", "LABEL": "Customer", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "VENDORNAME", "LABEL": "Vendor Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "VENDORID", "LABEL": "<PERSON><PERSON><PERSON>", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "EMPLOYEENAME", "LABEL": "Employee Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "EMPLOYEEID", "LABEL": "Employee", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PROJECTNAME", "LABEL": "Project Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PROJECTID", "LABEL": "Project", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "GLDIMREGION", "LABEL": "Region", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}], "relationships": [{"OBJECTPATH": "ITEM", "OBJECTNAME": "ITEM", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "ITEMID"}, {"OBJECTPATH": "DEPARTMENT", "OBJECTNAME": "DEPARTMENT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "DEPARTMENTID"}, {"OBJECTPATH": "LOCATION", "OBJECTNAME": "LOCATION", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "LOCATIONID"}, {"OBJECTPATH": "WAREHOUSE", "OBJECTNAME": "WAREHOUSE", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "WAREHOUSE.LOCATION_NO"}, {"OBJECTPATH": "SODOCUMENT", "OBJECTNAME": "SODOCUMENT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "DOCHDRID"}, {"OBJECTPATH": "TASK", "OBJECTNAME": "TASK", "LABEL": "Task", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "TASKKEY"}, {"OBJECTPATH": "SHIPTO", "OBJECTNAME": "CONTACTVERSION", "LABEL": "Ship-to contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "SHIPTORETURNTOKEY"}, {"OBJECTPATH": "RENEWALMACRO", "OBJECTNAME": "RENEWALMACRO", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "RENEWALMACROKEY"}, {"OBJECTPATH": "BILLED_TIMESHEETENTRY", "OBJECTNAME": "TIMESHEETENTRY", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "BILLABLETIMEENTRYKEY"}, {"OBJECTPATH": "BILLED_EEXPENSESITEM", "OBJECTNAME": "EEXPENSESITEM", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "BILLABLEPRENTRYKEY"}, {"OBJECTPATH": "BILLED_SODOCUMENTENTRY", "OBJECTNAME": "SODOCUMENTENTRY", "LABEL": "Billed Purchase Order Transaction Detail", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "BILLABLEDOCENTRYKEY"}, {"OBJECTPATH": "RGLDIM401000000204851_10040", "OBJECTNAME": "REGION", "LABEL": "Region", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "GLDIMREGION"}], "relationshipFields": [{"DATATYPE": "DOCHDRID", "ID": "DOCHDRID", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "DOCHDRID", "xtremProperty": ""}, {"DATATYPE": "WAREHOUSE", "ID": "WAREHOUSE.LOCATION_NO", "DESCRIPTION": "LOCATION_NO", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LOCATION_NO", "xtremProperty": ""}, {"DATATYPE": "WAREHOUSE", "ID": "WAREHOUSE.NAME", "DESCRIPTION": "NAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "NAME", "xtremProperty": ""}, {"DATATYPE": "RENEWALMACROKEY", "ID": "RENEWALMACROKEY", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "RENEWALMACROKEY", "xtremProperty": ""}, {"DATATYPE": "BILLABLETIMEENTRYKEY", "ID": "BILLABLETIMEENTRYKEY", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "BILLABLETIMEENTRYKEY", "xtremProperty": ""}, {"DATATYPE": "BILLABLEPRENTRYKEY", "ID": "BILLABLEPRENTRYKEY", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "BILLABLEPRENTRYKEY", "xtremProperty": ""}, {"DATATYPE": "BILLABLEDOCENTRYKEY", "ID": "BILLABLEDOCENTRYKEY", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "BILLABLEDOCENTRYKEY", "xtremProperty": ""}, {"DATATYPE": "TASKKEY", "ID": "TASKKEY", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TASKKEY", "xtremProperty": ""}]}