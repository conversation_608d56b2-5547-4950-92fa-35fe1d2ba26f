{"name": "VENDOR", "documentType": "", "xtremObject": "supplier", "fields": [{"ID": "XTREEM_ID", "LABEL": "ID Xtrem", "DESCRIPTION": "ID Xtrem", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": true, "xtremProperty": ""}, {"ID": "RECORDNO", "LABEL": "Record Number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "VENDORID", "LABEL": "Vendor ID", "DESCRIPTION": "Unique ID of Vendor", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctSupplier.intacctId,businessEntity.id"}, {"ID": "NAME", "LABEL": "Vendor Name", "DESCRIPTION": "Name of <PERSON><PERSON><PERSON>", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "businessEntity.name"}, {"ID": "NAME1099", "LABEL": "1099 Name", "DESCRIPTION": "1099 Name", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PARENTKEY", "LABEL": "Parent Key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PARENTID", "LABEL": "<PERSON><PERSON>", "DESCRIPTION": "Name of <PERSON><PERSON>", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "parent.intacctSupplier.intacctId"}, {"ID": "PARENTNAME", "LABEL": "<PERSON><PERSON>or Name", "DESCRIPTION": "Name of <PERSON><PERSON>", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TERMNAME", "LABEL": "Term", "DESCRIPTION": "Term", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "paymentTerm.name"}, {"ID": "VENDORACCOUNTNO", "LABEL": "Number", "DESCRIPTION": "Vendor Account No.", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "TAXID", "LABEL": "Tax ID", "DESCRIPTION": "Tax Identification Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "businessEntity.taxIdNumber"}, {"ID": "CREDITLIMIT", "LABEL": "Credit Limit", "DESCRIPTION": "Credit Limit", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "TOTALDUE", "LABEL": "Total Due", "DESCRIPTION": "Total Due", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "BILLINGTYPE", "LABEL": "Vendor Billing Type", "DESCRIPTION": "Vendor Billing Type", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["openitem", "balanceforward"]}, "ISCUSTOM": false}, {"ID": "VENDTYPE", "LABEL": "Vendor Type ID", "DESCRIPTION": "Vendor Type", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "VENDTYPE1099TYPE", "LABEL": "Vendor Form 1099 Type", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "GLGROUP", "LABEL": "GL Group", "DESCRIPTION": "GL Group", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PRICESCHEDULE", "LABEL": "Price Schedule", "DESCRIPTION": "Price Schedule", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DISCOUNT", "LABEL": "Discount (%)", "DESCRIPTION": "Discount1", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "COMMENTS", "LABEL": "Comments", "DESCRIPTION": "Comments", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ACCOUNTLABEL", "LABEL": "Account Label", "DESCRIPTION": "AccountLabel Option", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "APACCOUNT", "LABEL": "Default expense account", "DESCRIPTION": "Account Option", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "APACCOUNTTITLE", "LABEL": "AP Account Title", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "FORM1099TYPE", "LABEL": "Form 1099 Type", "DESCRIPTION": "Form 1099 Type", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "FORM1099BOX", "LABEL": "Form 1099 Box", "DESCRIPTION": "Form 1099 Box", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PAYMENTPRIORITY", "LABEL": "Payment Priority", "DESCRIPTION": "Priority", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["<PERSON><PERSON>", "High", "Normal", "Low"]}, "ISCUSTOM": false}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Active-Non-Posting/Inactive", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "active non-posting", "inactive"]}, "ISCUSTOM": false, "xtremProperty": "intacctSupplier.status"}, {"ID": "HIDEDISPLAYCONTACT", "LABEL": "Status", "DESCRIPTION": "Exclude from contact list. Use false for No, true for Yes. (De<PERSON><PERSON>: false)", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "WRITEONLY": true, "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": "intacctSupplier.hideDisplayContact"}, {"ID": "PAYDATEVALUE", "LABEL": "Default Bill Payment Date", "DESCRIPTION": "PAYDATEVALUE", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ONETIME", "LABEL": "One-time use", "DESCRIPTION": "One-Time", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "ONHOLD", "LABEL": "On Hold", "DESCRIPTION": "On Hold", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "DONOTCUTCHECK", "LABEL": "Don't pay", "DESCRIPTION": "Don't pay", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "CURRENCY", "LABEL": "<PERSON><PERSON><PERSON>", "DESCRIPTION": "<PERSON><PERSON><PERSON>", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "businessEntity.currency.id"}, {"ID": "PYMTCOUNTRYCODE", "LABEL": "Payment country", "DESCRIPTION": "Country Code", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["", "AU", "ZA", "GB"]}, "ISCUSTOM": false}, {"ID": "FILEPAYMENTSERVICE", "LABEL": "File payment service type", "DESCRIPTION": "File payment service type", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["ACH", "BANKFILE", "NONE"]}, "ISCUSTOM": false}, {"ID": "ACHENABLED", "LABEL": "Enable ACH", "DESCRIPTION": "Enable ACH", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "WIREENABLED", "LABEL": "Enable Wire Transfer", "DESCRIPTION": "Enable Wire Transfer", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "CHECKENABLED", "LABEL": "Enable Check Outsourcing", "DESCRIPTION": "Enable Check Outsourcing", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "ACHBANKROUTINGNUMBER", "LABEL": "ACH Bank Routing Number", "DESCRIPTION": "ACH Bank Routing Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ACHACCOUNTNUMBER", "LABEL": "Account Number", "DESCRIPTION": "ACH Account Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ACHACCOUNTTYPE", "LABEL": "Account Type", "DESCRIPTION": "ACH Account Type", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Checking Account", "Savings Account"]}, "ISCUSTOM": false}, {"ID": "ACHREMITTANCETYPE", "LABEL": "Account classification", "DESCRIPTION": "Account classification", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["CTX", "PPD", "CCD"]}, "ISCUSTOM": false}, {"ID": "WIREBANKNAME", "LABEL": "Name of Bank", "DESCRIPTION": "Wire Transfer Bank Name", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "WIREBANKROUTINGNUMBER", "LABEL": "Bank Routing Number", "DESCRIPTION": "Wire Transfer Bank Routing Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "WIREACCOUNTNUMBER", "LABEL": "Account Number", "DESCRIPTION": "Wire Transfer Account Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "WIREACCOUNTTYPE", "LABEL": "Account Type", "DESCRIPTION": "WIRE Account Type", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Demand Deposit Account"]}, "ISCUSTOM": false}, {"ID": "PMPLUSREMITTANCETYPE", "LABEL": "Remittance Delivery Type", "DESCRIPTION": "Payment Manager Plus Remittance Delivery Type", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Email", "Fax", "EDI"]}, "ISCUSTOM": false}, {"ID": "PMPLUSEMAIL", "LABEL": "Remittance Email Address", "DESCRIPTION": "Payment Manager Plus Remittance Email Address", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PMPLUSFAX", "LABEL": "Remittance Fax Number", "DESCRIPTION": "Payment Manager Plus Remittance Fax Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DISPLAYTERMDISCOUNT", "LABEL": "Display the term discount on the check stub", "DESCRIPTION": "Display the term discount on the check stub", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "PAYMETHODKEY", "LABEL": "Preferred Payment Method", "DESCRIPTION": "Prefered Payment Method", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["", "Printed Check", "Charge Card", "EFT", "Cash", "ACH", "WF Check", "WF USD Wire", "WF Domestic ACH"]}, "ISCUSTOM": false, "xtremProperty": "paymentMethod"}, {"ID": "DISPLOCACCTNOCHECK", "LABEL": "Display the vendor-assigned account number, per entity, on the check stub", "DESCRIPTION": "Display the vendor-assigned account number, per entity, on the check stub", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "VENDORACCTNOKEY", "LABEL": "Vendor Account Number Key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "PAYMENTNOTIFY", "LABEL": "Send Automatic Payment Notification", "DESCRIPTION": "Send Payment Notification", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "MERGEPAYMENTREQ", "LABEL": "Merge payment requests", "DESCRIPTION": "Merge Payment Requests", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "DISPLAYCONTACTKEY", "LABEL": "Display Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "PRIMARYCONTACTKEY", "LABEL": "Primary Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "PAYTOKEY", "LABEL": "Pay-to Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "RETURNTOKEY", "LABEL": "Return-to Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "ACCOUNTLABELKEY", "LABEL": "Account Label Record Number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "ACCOUNTKEY", "LABEL": "Account Record Number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "VENDTYPEKEY", "LABEL": "Vendor Type Record Number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "GLGRPKEY", "LABEL": "GL Group Record Number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "TERMSKEY", "LABEL": "Term Record Number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "PAYMETHODREC", "LABEL": "Payment Method Record Number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "OUTSOURCECHECK", "LABEL": "Enable Check Delivery Service", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "OUTSOURCECHECKSTATE", "LABEL": "Services state", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Validation Failed", "Validation Passed", "Pending Activation", "queued", "Subscribed"]}, "ISCUSTOM": false}, {"ID": "OUTSOURCEACH", "LABEL": "Enable American Express ACH Payment Service", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "OUTSOURCEACHSTATE", "LABEL": "Amex ACH Services status", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Active", "In Progress", "Not Enabled", "Validation Failed", "Validation Passed", "Pending Activation", "queued", "Subscribed", "Inactive"]}, "ISCUSTOM": false}, {"ID": "OUTSOURCECARD", "LABEL": "Enable American Express Card Payment Service", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "CARDSTATE", "LABEL": "Amex Credit Card Services state", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Active", "In Progress", "Not Enabled", "Validation Failed", "Validation Passed", "Pending Activation", "queued", "Subscribed", "Inactive"]}, "ISCUSTOM": false}, {"ID": "OFFSETGLACCOUNTNO", "LABEL": "AP Account", "DESCRIPTION": "AP Account", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "OFFSETGLACCOUNTNOTITLE", "LABEL": "Default AP Account", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CONTACTKEY1099", "LABEL": "1099 Contact Record Number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "VENDOR_AMEX_ORGANIZATION_ID", "LABEL": "Organization ID generated by amex for vendor", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "VENDOR_AMEX_ORG_ADDRESS_ID", "LABEL": "Organization address ID generated by amex for vendor", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "VENDOR_AMEX_CD_AFFILIATE_ID", "LABEL": "Vendor Organization Check Delivery affiliate ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "VENDOR_AMEX_CARD_AFFILIATE_ID", "LABEL": "Vendor Organization Corporate Card affiliate ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "AMEX_BANK_ACCOUNT_ID", "LABEL": "AMEX bank account ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "AMEX_BANK_ACCOUNT_ADDRESS_ID", "LABEL": "AMEX bank account address ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RETAINAGEPERCENTAGE", "LABEL": "Default retainage percentage", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "PERCENT", "ISCUSTOM": false}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CONTACT_LIST_INFO", "LABEL": "Contact list", "DESCRIPTION": " Multiple CONTACT_LIST_INFO elements may then be passed.", "REQUIRED": false, "WRITEONLY": true, "READONLY": false, "DATATYPE": "OBJECT", "ISCUSTOM": false, "xtremProperty": "intacctSupplier.contactList"}], "relationships": [{"OBJECTPATH": "PARENT", "OBJECTNAME": "VENDOR", "LABEL": "<PERSON><PERSON>", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PARENTID", "xtremProperty": "parent.businessEntity.id"}, {"OBJECTPATH": "PAYTOCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Pay to Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PAYTO.CONTACTNAME", "xtremProperty": "payTo.name"}, {"OBJECTPATH": "RETURNTOCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Return to Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "RETURNTO.CONTACTNAME"}, {"OBJECTPATH": "CONTACT1099", "OBJECTNAME": "CONTACT", "LABEL": "1099 Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CONTACTTO1099.CONTACTNAME"}, {"OBJECTPATH": "DISPLAYCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "DISPLAYCONTACT.CONTACTNAME", "xtremProperty": ""}, {"OBJECTPATH": "PRIMARYCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Primary Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CONTACTINFO.CONTACTNAME", "xtremProperty": ""}, {"OBJECTPATH": "APACCOUNTLABEL", "OBJECTNAME": "APACCOUNTLABEL", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "ACCOUNTLABEL"}, {"OBJECTPATH": "APACCOUNT", "OBJECTNAME": "GLACCOUNT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "APACCOUNT"}, {"OBJECTPATH": "TERM", "OBJECTNAME": "APTERM", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "TERMNAME"}, {"OBJECTPATH": "GLGROUP", "OBJECTNAME": "VENDGLGROUP", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "GLGROUP", "xtremProperty": ""}, {"OBJECTPATH": "PRICELIST", "OBJECTNAME": "INVPRICELIST", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PRICESCHEDULE"}, {"OBJECTPATH": "OFFSETGLACCOUNTNO", "OBJECTNAME": "GLACCOUNT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "OFFSETGLACCOUNTNO"}, {"OBJECTPATH": "VENDTYPE", "OBJECTNAME": "VENDTYPE", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "VENDTYPE"}, {"OBJECTPATH": "MELOCATION", "OBJECTNAME": "LOCATION", "LABEL": "Created At Entity Information", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MEGAENTITYID"}], "relationshipFields": [{"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.COMPANYNAME", "DESCRIPTION": "COMPANYNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.PREFIX", "DESCRIPTION": "PREFIX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREFIX", "xtremProperty": "primaryContact.address.intacctBusinessEntityAddress.prefix"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.FIRSTNAME", "DESCRIPTION": "FIRSTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FIRSTNAME", "xtremProperty": "primaryContact.firstName"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.LASTNAME", "DESCRIPTION": "LASTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LASTNAME", "xtremProperty": "primaryContact.lastName"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.INITIAL", "DESCRIPTION": "INITIAL", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "INITIAL", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.PRINTAS", "DESCRIPTION": "PRINTAS", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTAS", "xtremProperty": "primaryAddress.intacctBusinessEntityAddress.printAs"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.TAXABLE", "DESCRIPTION": "TAXABLE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXABLE", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.TAXGROUP", "DESCRIPTION": "TAXGROUP", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXGROUP", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.TAXID", "DESCRIPTION": "TAXID", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXID", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": "primaryContact.locationPhoneNumber"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.CELLPHONE", "DESCRIPTION": "CELLPHONE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CELLPHONE", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.PAGER", "DESCRIPTION": "PAGER", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PAGER", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.FAX", "DESCRIPTION": "FAX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FAX", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.TAXIDVALIDATIONDATE", "DESCRIPTION": "TAXIDVALIDATIONDATE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXIDVALIDATIONDATE", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.TAXCOMPANYNAME", "DESCRIPTION": "TAXCOMPANYNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXCOMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.TAXADDRESS", "DESCRIPTION": "TAXADDRESS", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXADDRESS", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.EMAIL1", "DESCRIPTION": "EMAIL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL1", "xtremProperty": "primaryContact.email"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.EMAIL2", "DESCRIPTION": "EMAIL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL2", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.URL1", "DESCRIPTION": "URL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL1", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.URL2", "DESCRIPTION": "URL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL2", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.VISIBLE", "DESCRIPTION": "VISIBLE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VISIBLE", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": "primaryAddress.addressLine1"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": "primaryAddress.addressLine2"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": "primaryAddress.city"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": "primaryAddress.region"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": "primaryAddress.postcode"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": "primaryAddress.country.id"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.LATITUDE", "DESCRIPTION": "MAILADDRESS.LATITUDE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LATITUDE", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.LONGITUDE", "DESCRIPTION": "MAILADDRESS.LONGITUDE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LONGITUDE", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.STATUS", "DESCRIPTION": "STATUS", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATUS", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": "intacctSupplier.primaryContact"}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.PREFIX", "DESCRIPTION": "PREFIX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREFIX", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.FIRSTNAME", "DESCRIPTION": "FIRSTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FIRSTNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.INITIAL", "DESCRIPTION": "INITIAL", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "INITIAL", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.LASTNAME", "DESCRIPTION": "LASTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LASTNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.COMPANYNAME", "DESCRIPTION": "COMPANYNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.PRINTAS", "DESCRIPTION": "PRINTAS", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTAS", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.CELLPHONE", "DESCRIPTION": "CELLPHONE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CELLPHONE", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.PAGER", "DESCRIPTION": "PAGER", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PAGER", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.FAX", "DESCRIPTION": "FAX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FAX", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.EMAIL1", "DESCRIPTION": "EMAIL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL1", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.EMAIL2", "DESCRIPTION": "EMAIL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL2", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.URL1", "DESCRIPTION": "URL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL1", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.URL2", "DESCRIPTION": "URL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL2", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.VISIBLE", "DESCRIPTION": "VISIBLE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VISIBLE", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": "intacctSupplier.payToAddress"}, {"DATATYPE": "PAYTO", "ID": "PAYTO.PREFIX", "DESCRIPTION": "PREFIX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREFIX", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.FIRSTNAME", "DESCRIPTION": "FIRSTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FIRSTNAME", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.INITIAL", "DESCRIPTION": "INITIAL", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "INITIAL", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.LASTNAME", "DESCRIPTION": "LASTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LASTNAME", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.COMPANYNAME", "DESCRIPTION": "COMPANYNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.PRINTAS", "DESCRIPTION": "PRINTAS", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTAS", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.CELLPHONE", "DESCRIPTION": "CELLPHONE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CELLPHONE", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.PAGER", "DESCRIPTION": "PAGER", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PAGER", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.FAX", "DESCRIPTION": "FAX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FAX", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.EMAIL1", "DESCRIPTION": "EMAIL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL1", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.EMAIL2", "DESCRIPTION": "EMAIL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL2", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.URL1", "DESCRIPTION": "URL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL1", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.URL2", "DESCRIPTION": "URL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL2", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.VISIBLE", "DESCRIPTION": "VISIBLE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VISIBLE", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.TAXGROUP", "DESCRIPTION": "TAXGROUP", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXGROUP", "xtremProperty": ""}, {"DATATYPE": "PAYTO", "ID": "PAYTO.TAXID", "DESCRIPTION": "TAXID", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXID", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": "intacctSupplier.returnToAddress"}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.PREFIX", "DESCRIPTION": "PREFIX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREFIX", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.FIRSTNAME", "DESCRIPTION": "FIRSTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FIRSTNAME", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.INITIAL", "DESCRIPTION": "INITIAL", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "INITIAL", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.LASTNAME", "DESCRIPTION": "LASTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LASTNAME", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.COMPANYNAME", "DESCRIPTION": "COMPANYNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.PRINTAS", "DESCRIPTION": "PRINTAS", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTAS", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.CELLPHONE", "DESCRIPTION": "CELLPHONE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CELLPHONE", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.PAGER", "DESCRIPTION": "PAGER", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PAGER", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.FAX", "DESCRIPTION": "FAX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FAX", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.EMAIL1", "DESCRIPTION": "EMAIL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL1", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.EMAIL2", "DESCRIPTION": "EMAIL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL2", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.URL1", "DESCRIPTION": "URL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL1", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.URL2", "DESCRIPTION": "URL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL2", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.VISIBLE", "DESCRIPTION": "VISIBLE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VISIBLE", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "RETURNTO", "ID": "RETURNTO.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.PREFIX", "DESCRIPTION": "PREFIX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREFIX", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.INITIAL", "DESCRIPTION": "INITIAL", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "INITIAL", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.COMPANYNAME", "DESCRIPTION": "COMPANYNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.PRINTAS", "DESCRIPTION": "PRINTAS", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTAS", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.CELLPHONE", "DESCRIPTION": "CELLPHONE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CELLPHONE", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.PAGER", "DESCRIPTION": "PAGER", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PAGER", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.FAX", "DESCRIPTION": "FAX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FAX", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.EMAIL1", "DESCRIPTION": "EMAIL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL1", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.EMAIL2", "DESCRIPTION": "EMAIL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL2", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.URL1", "DESCRIPTION": "URL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL1", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.URL2", "DESCRIPTION": "URL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL2", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.VISIBLE", "DESCRIPTION": "VISIBLE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VISIBLE", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "CONTACTTO1099", "ID": "CONTACTTO1099.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": ""}]}