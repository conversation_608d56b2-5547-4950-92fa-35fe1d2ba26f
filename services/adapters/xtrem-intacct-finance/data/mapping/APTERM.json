{"name": "APTERM", "documentType": "AP Term", "xtremObject": "payment_term", "additionnalLink": [{"xtremProperty": "businessEntityType", "type": "enum", "xtremValues": ["supplier", "all"]}], "fields": [{"ID": "NAME", "LABEL": "Name", "DESCRIPTION": "Name", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctId, name"}, {"ID": "DESCRIPTION", "LABEL": "Description", "DESCRIPTION": "Description", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "description"}, {"ID": "VALUE", "LABEL": "Encoded term value", "DESCRIPTION": "Contains encoding of term properties", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Inactive", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "inactive"]}, "ISCUSTOM": false, "xtremProperty": "statusIntacct"}, {"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DESCRIPTION": "User who modified this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "CREATEDBY", "LABEL": "Created by", "DESCRIPTION": "User who created this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "DUEDATE", "LABEL": "Due date", "DESCRIPTION": "Name", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "days"}, {"ID": "DUEFROM", "LABEL": "Due from", "DESCRIPTION": "Due From", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["from invoice/bill date", "of the month of invoice/bill date", "of next month from invoice/bill date", "of 2nd month from invoice/bill date", "of 3rd month from invoice/bill date", "of 4th month from invoice/bill date", "of 5th month from invoice/bill date", "of 6th month from invoice/bill date", "after end of month of invoice/bill date", "from invoice/bill date extending to eom"]}, "ISCUSTOM": false, "xtremProperty": "dueFromIntacct"}, {"ID": "DISCDATE", "LABEL": "Day", "DESCRIPTION": "Day", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "discountDate"}, {"ID": "DISCFROM", "LABEL": "Discount from", "DESCRIPTION": "Discount From", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["from invoice/bill date", "of the month of invoice/bill date", "of next month from invoice/bill date", "of 2nd month from invoice/bill date", "of 3rd month from invoice/bill date", "of 4th month from invoice/bill date", "of 5th month from invoice/bill date", "of 6th month from invoice/bill date", "after end of month of invoice/bill date", "from invoice/bill date extending to eom"]}, "ISCUSTOM": false, "xtremProperty": "discountFromIntacct"}, {"ID": "DISCAMOUNT", "LABEL": "Amount", "DESCRIPTION": "Discount Amount", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "discountAmount"}, {"ID": "DISCPERCAMN", "LABEL": "Discount from", "DESCRIPTION": "Discount From", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["$", "%"]}, "ISCUSTOM": false, "xtremProperty": "discountTypeIntacct"}, {"ID": "DISCFUDGEDAYS", "LABEL": "Grace days", "DESCRIPTION": "Discount Grace Days", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "PEN_TYPES", "LABEL": "Penalty option", "DESCRIPTION": "Penalty Options", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["No Penalty", "Daily", "Weekly", "Biweekly", "Monthly", "Bimonthly", "Quarterly", "Half yearly", "Annually"]}, "ISCUSTOM": false}, {"ID": "PENAMOUNT", "LABEL": "Amount", "DESCRIPTION": "Penalty Amount", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "penaltyAmount"}, {"ID": "PENPERCAMN", "LABEL": "Penalty period", "DESCRIPTION": "Penalty Period", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["$", "%"]}, "ISCUSTOM": false, "xtremProperty": "penaltyTypeIntacct"}, {"ID": "PENFUDGEDAYS", "LABEL": "Grace days", "DESCRIPTION": "Penalty Grace Days", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "DISCCALCON", "LABEL": "Calculate on the", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["Line items total", "Bill total"]}, "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}], "relationships": [""], "relationshipFields": []}