{"name": "ARPYMTDETAIL", "fields": [{"ID": "RECORDNO", "LABEL": "Record Number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RECORDKEY", "LABEL": "Invoice record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.arInvoiceRecordNo"}, {"ID": "ENTRYKEY", "LABEL": "Invoice entry record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "POSADJKEY", "LABEL": "Positive adjustment record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "POSADJENTRYKEY", "LABEL": "Positive adjustment record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "INLINEKEY", "LABEL": "Inline transaction record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "INLINEENTRYKEY", "LABEL": "Inline transaction entry record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "INLINEAMOUNT", "LABEL": "Inline transaction base amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_INLINEAMOUNT", "LABEL": "Inline transaction amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ADVANCEKEY", "LABEL": "Advance record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ADVANCEENTRYKEY", "LABEL": "Advance entry record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "POSTEDADVANCEKEY", "LABEL": "Posted advance record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "POSTEDADVANCEENTRYKEY", "LABEL": "Posted advance entry record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "POSTEDADVANCEAMOUNT", "LABEL": "Posted advance base amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_POSTEDADVANCEAMOUNT", "LABEL": "Posted advance transaction amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "OVERPAYMENTKEY", "LABEL": "Overpayment record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "OVERPAYMENTENTRYKEY", "LABEL": "Overpayment entry record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "POSTEDOVERPAYMENTKEY", "LABEL": "Posted overpayment record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "POSTEDOVERPAYMENTENTRYKEY", "LABEL": "Posted overpayment entry record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "POSTEDOVERPAYMENTAMOUNT", "LABEL": "Posted overpayment base amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_POSTEDOVERPAYMENTAMOUNT", "LABEL": "Posted overpayment transaction amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "NEGATIVEINVOICEKEY", "LABEL": "Negative Invoice transaction record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "NEGATIVEINVOICEENTRYKEY", "LABEL": "Negative Invoice transaction entry record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "NEGATIVEINVOICEAMOUNT", "LABEL": "Negative Invoice transaction base amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_NEGATIVEINVOICEAMOUNT", "LABEL": "Negative Invoice transaction amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ADJUSTMENTKEY", "LABEL": "Adjustment transaction record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ADJUSTMENTENTRYKEY", "LABEL": "Adjustment transaction entry record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ADJUSTMENTAMOUNT", "LABEL": "Adjustment transaction base amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_ADJUSTMENTAMOUNT", "LABEL": "Adjustment transaction amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PAYMENTKEY", "LABEL": "Payment record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PAYMENTENTRYKEY", "LABEL": "Payment entry record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PAYMENTDATE", "LABEL": "Payment date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PAYMENTAMOUNT", "LABEL": "Payment base amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_PAYMENTAMOUNT", "LABEL": "Payment transaction amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "paymentAmount"}, {"ID": "CURRENCY", "LABEL": "Payment currency", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENCREATED", "LABEL": "When created", "DATATYPE": "TIMESTAMP", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "timestamp marking last time this was created.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DATATYPE": "TIMESTAMP", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "timestamp marking last time this was changed.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CREATEDBY", "LABEL": "Created by", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "User who created this.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "User who modified this.", "VALIDVALUES": null, "xtremProperty": ""}], "xtremObject": "accounts_receivable_payment_line", "documentType": "", "relationships": [{"LABEL": "", "RELATEDBY": "RECORDKEY", "OBJECTNAME": "ARINVOICE", "OBJECTPATH": "ARINVOICE", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "ENTRYKEY", "OBJECTNAME": "ARINVOICEITEM", "OBJECTPATH": "ARINVOICEITEM", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "POSADJKEY", "OBJECTNAME": "ARADJUSTMENT", "OBJECTPATH": "POSADJ", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "POSADJENTRYKEY", "OBJECTNAME": "ARADJUSTMENTENTRY", "OBJECTPATH": "POSADJITEM", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "PAYMENTKEY", "OBJECTNAME": "ARPYMT", "OBJECTPATH": "ARPYMT", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "PAYMENTENTRYKEY", "OBJECTNAME": "ARPYMTENTRY", "OBJECTPATH": "ARPYMTENTRY", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "INLINEKEY", "OBJECTNAME": "ARINVOICE", "OBJECTPATH": "INLINEINVOICE", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "INLINEENTRYKEY", "OBJECTNAME": "ARINVOICEITEM", "OBJECTPATH": "INLINEINVOICEITEM", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "ARADJUSTMENTKEY", "OBJECTNAME": "ARADJUSTMENT", "OBJECTPATH": "ARADJUSTMENT", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "ARADJUSTMENTENTRYKEY", "OBJECTNAME": "ARADJUSTMENTENTRY", "OBJECTPATH": "ARADJUSTMENTENTRY", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "ADVANCEKEY", "OBJECTNAME": "ARPOSTEDADVANCE", "OBJECTPATH": "ARPOSTEDADVANCE", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "ADVANCEENTRYKEY", "OBJECTNAME": "ARPOSTEDADVANCEENTRY", "OBJECTPATH": "ARPOSTEDADVANCEENTRY", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "OVERPAYMENTKEY", "OBJECTNAME": "ARPOSTEDOVERPAYMENT", "OBJECTPATH": "ARPOSTEDOVERPAYMENT", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "OVERPAYMENTENTRYKEY", "OBJECTNAME": "ARPOSTEDOVERPAYMENTENTRY", "OBJECTPATH": "ARPOSTEDOVERPAYMENTEENTRY", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "NEGBILLINVKEY", "OBJECTNAME": "ARINVOICE", "OBJECTPATH": "NEGATIVEINVOICE", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "NEGBILLINVENTRYKEY", "OBJECTNAME": "ARINVOICEITEM", "OBJECTPATH": "NEGATIVEINVOICEITEM", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}], "relationshipFields": []}