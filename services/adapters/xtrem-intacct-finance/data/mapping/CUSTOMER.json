{"name": "CUSTOMER", "documentType": "Customer", "xtremObject": "customer", "fields": [{"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "intacctCustomer.recordNo"}, {"ID": "CUSTOMERID", "LABEL": "Customer ID", "DESCRIPTION": "Unique ID of Customer", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctCustomer.intacctId,businessEntity.id"}, {"ID": "NAME", "LABEL": "Customer name", "DESCRIPTION": "Name of Customer", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "businessEntity.name"}, {"ID": "PARENTKEY", "LABEL": "Parent key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "PARENTID", "LABEL": "Parent customer", "DESCRIPTION": "Name of Parent Customer", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "parent.intacctCustomer.intacctId"}, {"ID": "PARENTNAME", "LABEL": "Parent name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TERMNAME", "LABEL": "Term", "DESCRIPTION": "Term", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "paymentTerm.name"}, {"ID": "CUSTREPID", "LABEL": "Customer rep", "DESCRIPTION": "Customer Rep", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RESALENO", "LABEL": "Resale number", "DESCRIPTION": "Resale Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TAXID", "LABEL": "Tax ID", "DESCRIPTION": "Tax Identification Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "businessEntity.taxIdNumber"}, {"ID": "CREDITLIMIT", "LABEL": "Credit limit", "DESCRIPTION": "Credit Limit", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "TOTALDUE", "LABEL": "Total due", "DESCRIPTION": "Total Due", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "COMMENTS", "LABEL": "Comments", "DESCRIPTION": "Comments", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ACCOUNTLABEL", "LABEL": "Account label", "DESCRIPTION": "AccountLabel Option", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ARACCOUNT", "LABEL": "Default revenue account", "DESCRIPTION": "Account Option", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "LAST_INVOICEDATE", "LABEL": "Last invoice date", "DESCRIPTION": "Last Invoice Date", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "LAST_STATEMENTDATE", "LABEL": "Last statement date", "DESCRIPTION": "Last Statement Date", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DELIVERY_OPTIONS", "LABEL": "Delivery options", "DESCRIPTION": "Delivery Options", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Print", "E-Mail", "Print#~#E-Mail"]}, "ISCUSTOM": false}, {"ID": "TERRITORYID", "LABEL": "Territory ID", "DESCRIPTION": "Territory ID", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TERRITORYNAME", "LABEL": "Territory Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SHIPPINGMETHOD", "LABEL": "Shipping method", "DESCRIPTION": "Shipping Method", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CUSTTYPE", "LABEL": "Customer type ID", "DESCRIPTION": "Customer Type", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "GLGROUP", "LABEL": "GL group", "DESCRIPTION": "GL Group", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PRICESCHEDULE", "LABEL": "Price schedule", "DESCRIPTION": "Price Schedule", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DISCOUNT", "LABEL": "Discount %", "DESCRIPTION": "Discount", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "PRICELIST", "LABEL": "Customer price list", "DESCRIPTION": "Customer Price Schedule", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "VSOEPRICELIST", "LABEL": "Fair value price list", "DESCRIPTION": "Fair Value Price Schedule", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Active-Non-Posting/Inactive", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "active non-posting", "inactive"]}, "ISCUSTOM": false, "xtremProperty": "intacctCustomer.status"}, {"ID": "HIDEDISPLAYCONTACT", "LABEL": "Status", "DESCRIPTION": "Exclude from contact list. Use false for No, true for Yes. (De<PERSON><PERSON>: false)", "REQUIRED": false, "READONLY": false, "WRITEONLY": true, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": "intacctCustomer.hideDisplayContact"}, {"ID": "ONETIME", "LABEL": "One-time use", "DESCRIPTION": "One-time use", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "CUSTMESSAGEID", "LABEL": "Default customer message", "DESCRIPTION": "Default Customer Message", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ONHOLD", "LABEL": "On hold", "DESCRIPTION": "On Hold", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "CURRENCY", "LABEL": "Default currency", "DESCRIPTION": "<PERSON><PERSON><PERSON>", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "businessEntity.currency.id"}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "timestamp marking last time this customer was changed.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "ARINVOICEPRINTTEMPLATEID", "LABEL": "AR invoice printing template", "DESCRIPTION": "Format template for printing AR Invoice document (XSL or DOC or DOX)", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "OEQUOTEPRINTTEMPLATEID", "LABEL": "Order entry - Quote printing template", "DESCRIPTION": "Format template for printing Order Entry Quote documents (XSL or DOC or DOX)", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "OEORDERPRINTTEMPLATEID", "LABEL": "Order entry - Order printing template", "DESCRIPTION": "Format template for printing Order Entry Order documents (XSL or DOC or DOX)", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "OELISTPRINTTEMPLATEID", "LABEL": "Order entry - List printing template", "DESCRIPTION": "Format template for printing Order Entry List documents (XSL or DOC or DOX)", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "OEINVOICEPRINTTEMPLATEID", "LABEL": "Order entry - Invoice printing template", "DESCRIPTION": "Format template for printing Order Entry Invoice documents (XSL or DOC or DOX)", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "OEADJPRINTTEMPLATEID", "LABEL": "Order entry - Adjustment printing template", "DESCRIPTION": "Format template for printing Order Entry Adjustment documents (XSL or DOC or DOX)", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "OEOTHERPRINTTEMPLATEID", "LABEL": "Order entry - Other printing template", "DESCRIPTION": "Format template for printing Order Entry Other documents (XSL or DOC or DOX)", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DISPLAYCONTACTKEY", "LABEL": "Display contact record number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "CONTACTKEY", "LABEL": "Primary contact record number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "SHIPTOKEY", "LABEL": "Ship-to contact record number", "DESCRIPTION": "Ship-to contact record number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "BILLTOKEY", "LABEL": "Bill-to contact record number", "DESCRIPTION": "Bill-to Contact Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "CUSTREPKEY", "LABEL": "Customer representative employee record number", "DESCRIPTION": "Customer Representative Employee Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "SHIPVIAKEY", "LABEL": "Ship-via record number", "DESCRIPTION": "Ship-via Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "TERRITORYKEY", "LABEL": "Territory record number", "DESCRIPTION": "Territory Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TERMSKEY", "LABEL": "Term record number", "DESCRIPTION": "Term Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "ACCOUNTLABELKEY", "LABEL": "Account label record number", "DESCRIPTION": "Account Label Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "ACCOUNTKEY", "LABEL": "GL account record number", "DESCRIPTION": "GL Account Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "CUSTTYPEKEY", "LABEL": "Customer type record number", "DESCRIPTION": "Customer Type Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "PRICESCHEDULEKEY", "LABEL": "Price schedule record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "OFFSETGLACCOUNTNO", "LABEL": "AR account", "DESCRIPTION": "AR Account", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "OFFSETGLACCOUNTNOTITLE", "LABEL": "Default AR account", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ADVBILLBY", "LABEL": "Bill in advance", "DESCRIPTION": "Bill in advance", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "ADVBILLBYTYPE", "LABEL": "before contract start date", "DESCRIPTION": "Bill in advance type", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["", "days", "months"]}, "ISCUSTOM": false}, {"ID": "RETAINAGEPERCENTAGE", "LABEL": "Default retainage percentage", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "PERCENT", "ISCUSTOM": false}, {"ID": "EMAILOPTIN", "LABEL": "Accepts emailed invoices", "DESCRIPTION": "Accepts emailed invoices", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "XTREEM_ID", "LABEL": "XTreeM_id", "DESCRIPTION": "Instance _id in XTreeM", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": true}, {"ID": "RCLASS", "LABEL": "Class", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RDEPARTMENT", "LABEL": "Department", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctCustomer.url"}, {"ID": "CONTACT_LIST_INFO", "LABEL": "Contact list", "DESCRIPTION": " Multiple CONTACT_LIST_INFO elements may then be passed.", "REQUIRED": false, "WRITEONLY": true, "READONLY": false, "DATATYPE": "OBJECT", "ISCUSTOM": false, "xtremProperty": "intacctCustomer.contactList"}], "relationships": [{"OBJECTPATH": "PARENT", "OBJECTNAME": "CUSTOMER", "LABEL": "Parent Customer", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PARENTID"}, {"OBJECTPATH": "SHIPTOCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Ship to Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "SHIPTO.CONTACTNAME"}, {"OBJECTPATH": "BILLTOCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Bill to Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "BILLTO.CONTACTNAME"}, {"OBJECTPATH": "DISPLAYCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "DISPLAYCONTACT.CONTACTNAME"}, {"OBJECTPATH": "PRIMARYCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Primary Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CONTACTINFO.CONTACTNAME"}, {"OBJECTPATH": "ACCOUNTLABEL", "OBJECTNAME": "ARACCOUNTLABEL", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "ACCOUNTLABEL"}, {"OBJECTPATH": "ARACCOUNT", "OBJECTNAME": "GLACCOUNT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "ARACCOUNT"}, {"OBJECTPATH": "TERM", "OBJECTNAME": "ARTERM", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "TERMNAME"}, {"OBJECTPATH": "GLGROUP", "OBJECTNAME": "CUSTGLGROUP", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "GLGROUP"}, {"OBJECTPATH": "CUSTREP", "OBJECTNAME": "EMPLOYEE", "LABEL": "Customer Rep", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CUSTREPID"}, {"OBJECTPATH": "SHIPMETHOD", "OBJECTNAME": "SHIPMETHOD", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "SHIPPINGMETHOD"}, {"OBJECTPATH": "TERRITORY", "OBJECTNAME": "TERRITORY", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "TERRITORYID"}, {"OBJECTPATH": "CUSTTYPE", "OBJECTNAME": "CUSTTYPE", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CUSTTYPE"}, {"OBJECTPATH": "OFFSETGLACCOUNTNO", "OBJECTNAME": "GLACCOUNT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "OFFSETGLACCOUNTNO"}, {"OBJECTPATH": "MELOCATION", "OBJECTNAME": "LOCATION", "LABEL": "Created At Entity Information", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MEGAENTITYID"}, {"OBJECTPATH": "R10007", "OBJECTNAME": "CLASS", "LABEL": "Customer", "RELATIONSHIPTYPE": "ONE2ONE", "RELATEDBY": "RCLASS"}, {"OBJECTPATH": "R10008", "OBJECTNAME": "DEPARTMENT", "LABEL": "Customer", "RELATIONSHIPTYPE": "ONE2ONE", "RELATEDBY": "RDEPARTMENT"}], "relationshipFields": [{"DATATYPE": "ENTITY", "ID": "ENTITY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ENTITY", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.COMPANYNAME", "DESCRIPTION": "COMPANYNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.PREFIX", "DESCRIPTION": "PREFIX", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREFIX", "xtremProperty": "primaryContact.address.intacctBusinessEntityAddress.prefix"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.FIRSTNAME", "DESCRIPTION": "FIRSTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FIRSTNAME", "xtremProperty": "primaryContact.firstName"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.LASTNAME", "DESCRIPTION": "LASTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LASTNAME", "xtremProperty": "primaryContact.lastName"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.INITIAL", "DESCRIPTION": "INITIAL", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "INITIAL", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.PRINTAS", "DESCRIPTION": "PRINTAS", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTAS", "xtremProperty": "primaryAddress.intacctBusinessEntityAddress.printAs"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.TAXABLE", "DESCRIPTION": "TAXABLE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXABLE", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.TAXGROUP", "DESCRIPTION": "TAXGROUP", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXGROUP", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.TAXID", "DESCRIPTION": "TAXID", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXID", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": "primaryContact.locationPhoneNumber"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.CELLPHONE", "DESCRIPTION": "CELLPHONE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CELLPHONE", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.PAGER", "DESCRIPTION": "PAGER", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PAGER", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.FAX", "DESCRIPTION": "FAX", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FAX", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.TAXIDVALIDATIONDATE", "DESCRIPTION": "TAXIDVALIDATIONDATE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXIDVALIDATIONDATE", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.GSTREGISTERED", "DESCRIPTION": "GSTREGISTERED", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "GSTREGISTERED", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.TAXCOMPANYNAME", "DESCRIPTION": "TAXCOMPANYNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXCOMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.TAXADDRESS", "DESCRIPTION": "TAXADDRESS", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXADDRESS", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.EMAIL1", "DESCRIPTION": "EMAIL1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL1", "xtremProperty": "primaryContact.email"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.EMAIL2", "DESCRIPTION": "EMAIL2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL2", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.URL1", "DESCRIPTION": "URL1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL1", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.URL2", "DESCRIPTION": "URL2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL2", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.VISIBLE", "DESCRIPTION": "VISIBLE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VISIBLE", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": "primaryAddress.addressLine1"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": "primaryAddress.addressLine2"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": "primaryAddress.city"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": "primaryAddress.region"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": "primaryAddress.postcode"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": "primaryAddress.country.id"}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.LATITUDE", "DESCRIPTION": "MAILADDRESS.LATITUDE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LATITUDE", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.MAILADDRESS.LONGITUDE", "DESCRIPTION": "MAILADDRESS.LONGITUDE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LONGITUDE", "xtremProperty": ""}, {"DATATYPE": "DISPLAYCONTACT", "ID": "DISPLAYCONTACT.STATUS", "DESCRIPTION": "STATUS", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATUS", "xtremProperty": ""}, {"DATATYPE": "TERMVALUE", "ID": "TERMVALUE", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TERMVALUE", "xtremProperty": ""}, {"DATATYPE": "CUSTREPNAME", "ID": "CUSTREPNAME", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CUSTREPNAME", "xtremProperty": ""}, {"DATATYPE": "ARACCOUNTTITLE", "ID": "ARACCOUNTTITLE", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ARACCOUNTTITLE", "xtremProperty": ""}, {"DATATYPE": "GLGRPKEY", "ID": "GLGRPKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "GLGRPKEY", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": "intacctCustomer.primaryContact"}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.PREFIX", "DESCRIPTION": "PREFIX", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREFIX", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.FIRSTNAME", "DESCRIPTION": "FIRSTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FIRSTNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.INITIAL", "DESCRIPTION": "INITIAL", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "INITIAL", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.LASTNAME", "DESCRIPTION": "LASTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LASTNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.COMPANYNAME", "DESCRIPTION": "COMPANYNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.PRINTAS", "DESCRIPTION": "PRINTAS", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTAS", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.CELLPHONE", "DESCRIPTION": "CELLPHONE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CELLPHONE", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.PAGER", "DESCRIPTION": "PAGER", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PAGER", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.FAX", "DESCRIPTION": "FAX", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FAX", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.EMAIL1", "DESCRIPTION": "EMAIL1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL1", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.EMAIL2", "DESCRIPTION": "EMAIL2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL2", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.URL1", "DESCRIPTION": "URL1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL1", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.URL2", "DESCRIPTION": "URL2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL2", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.VISIBLE", "DESCRIPTION": "VISIBLE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VISIBLE", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": "intacctCustomer.shipToAddress"}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.PREFIX", "DESCRIPTION": "PREFIX", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREFIX", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.FIRSTNAME", "DESCRIPTION": "FIRSTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FIRSTNAME", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.INITIAL", "DESCRIPTION": "INITIAL", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "INITIAL", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.LASTNAME", "DESCRIPTION": "LASTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LASTNAME", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.COMPANYNAME", "DESCRIPTION": "COMPANYNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.PRINTAS", "DESCRIPTION": "PRINTAS", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTAS", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.TAXABLE", "DESCRIPTION": "TAXABLE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXABLE", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.TAXGROUP", "DESCRIPTION": "TAXGROUP", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXGROUP", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.TAXID", "DESCRIPTION": "TAXID", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXID", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.CELLPHONE", "DESCRIPTION": "CELLPHONE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CELLPHONE", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.PAGER", "DESCRIPTION": "PAGER", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PAGER", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.FAX", "DESCRIPTION": "FAX", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FAX", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.EMAIL1", "DESCRIPTION": "EMAIL1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL1", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.EMAIL2", "DESCRIPTION": "EMAIL2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL2", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.URL1", "DESCRIPTION": "URL1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL1", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.URL2", "DESCRIPTION": "URL2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL2", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.VISIBLE", "DESCRIPTION": "VISIBLE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VISIBLE", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": "intacctCustomer.billToAddress"}, {"DATATYPE": "BILLTO", "ID": "BILLTO.PREFIX", "DESCRIPTION": "PREFIX", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREFIX", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.FIRSTNAME", "DESCRIPTION": "FIRSTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FIRSTNAME", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.INITIAL", "DESCRIPTION": "INITIAL", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "INITIAL", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.LASTNAME", "DESCRIPTION": "LASTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LASTNAME", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.COMPANYNAME", "DESCRIPTION": "COMPANYNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.PRINTAS", "DESCRIPTION": "PRINTAS", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTAS", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.TAXABLE", "DESCRIPTION": "TAXABLE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXABLE", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.TAXGROUP", "DESCRIPTION": "TAXGROUP", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXGROUP", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.CELLPHONE", "DESCRIPTION": "CELLPHONE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CELLPHONE", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.PAGER", "DESCRIPTION": "PAGER", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PAGER", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.FAX", "DESCRIPTION": "FAX", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FAX", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.EMAIL1", "DESCRIPTION": "EMAIL1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL1", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.EMAIL2", "DESCRIPTION": "EMAIL2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL2", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.URL1", "DESCRIPTION": "URL1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL1", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.URL2", "DESCRIPTION": "URL2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL2", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.VISIBLE", "DESCRIPTION": "VISIBLE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VISIBLE", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": ""}, {"DATATYPE": "PRCLST_OVERRIDE", "ID": "PRCLST_OVERRIDE", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRCLST_OVERRIDE", "xtremProperty": ""}, {"DATATYPE": "OEPRCLSTKEY", "ID": "OEPRCLSTKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "OEPRCLSTKEY", "xtremProperty": ""}, {"DATATYPE": "OEPRICESCHEDKEY", "ID": "OEPRICESCHEDKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "OEPRICESCHEDKEY", "xtremProperty": ""}, {"DATATYPE": "ENABLEONLINECARDPAYMENT", "ID": "ENABLEONLINECARDPAYMENT", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ENABLEONLINECARDPAYMENT", "xtremProperty": ""}, {"DATATYPE": "ENABLEONLINEACHPAYMENT", "ID": "ENABLEONLINEACHPAYMENT", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ENABLEONLINEACHPAYMENT", "xtremProperty": ""}, {"DATATYPE": "VSOEPRCLSTKEY", "ID": "VSOEPRCLSTKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VSOEPRCLSTKEY", "xtremProperty": ""}, {"DATATYPE": "CREATEDBY", "ID": "CREATEDBY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CREATEDBY", "xtremProperty": ""}, {"DATATYPE": "MODIFIEDBY", "ID": "MODIFIEDBY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "MODIFIEDBY", "xtremProperty": ""}, {"DATATYPE": "OBJECTRESTRICTION", "ID": "OBJECTRESTRICTION", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "OBJECTRESTRICTION", "xtremProperty": ""}, {"DATATYPE": "SUPDOCID", "ID": "SUPDOCID", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "SUPDOCID", "xtremProperty": ""}]}