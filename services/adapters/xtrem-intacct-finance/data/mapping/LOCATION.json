{"name": "LOCATION", "documentType": "Location", "fields": [{"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "LOCATIONID", "LABEL": "ID", "DESCRIPTION": "Unique Identifier", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "id, intacctId"}, {"ID": "NAME", "LABEL": "Name", "DESCRIPTION": "Free form name for this warehouse location.", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "name"}, {"ID": "PARENTID", "LABEL": "Parent", "DESCRIPTION": "Parent Location", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SUPERVISORID", "LABEL": "Manager", "DESCRIPTION": "Manager In Charge", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "STARTDATE", "LABEL": "Start date", "DESCRIPTION": "Start Date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "ENDDATE", "LABEL": "End date", "DESCRIPTION": "End Date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Active-Non-Posting/Inactive", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "active non-posting", "inactive"]}, "ISCUSTOM": false, "xtremProperty": "status"}, {"ID": "CURRENCY", "LABEL": "Base currency", "DESCRIPTION": "Entity Base Currency", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TAXID", "LABEL": "Tax ID", "DESCRIPTION": "Tax Identification Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "CREATEDBY", "LABEL": "Created by", "DESCRIPTION": "User who created this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DESCRIPTION": "User who modified this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "ADDRESSCOUNTRYDEFAULT", "LABEL": "Default country for addresses", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["United States", "Afghanistan", "Aland Islands", "Albania", "Algeria", "American Samoa", "Andorra", "Angola", "<PERSON><PERSON><PERSON>", "Antarctica", "Antigua and Barbuda", "Argentina", "Armenia", "Aruba", "Australia", "Austria", "Azerbaijan", "Bahamas", "Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium", "Belize", "Benin", "Bermuda", "Bhutan", "Bolivia", "Bonaire, Sint Eustatius and Saba", "Bosnia and Herzegovina", "Botswana", "Bouvet Island", "Brazil", "British Indian Ocean Territory", "Brunei Darussalam", "Bulgaria", "Burkina Faso", "Burundi", "Cambodia", "Cameroon", "Canada", "Cape Verde", "Cayman Islands", "Central African Republic", "Chad", "Chile", "China", "Christmas Island", "Cocos (Keeling) Islands", "Colombia", "Comoros", "Congo", "Congo, Democratic Republic", "Cook Islands", "Costa Rica", "Côte d'Ivoire", "Croatia", "Cuba", "Curaçao", "Cyprus", "Czech Republic", "Denmark", "Djibouti", "Dominica", "Dominican Republic", "Ecuador", "Egypt", "El Salvador", "Equatorial Guinea", "Eritrea", "Estonia", "<PERSON><PERSON><PERSON><PERSON>", "Ethiopia", "Falkland Islands (Malvinas)", "Faroe Islands", "Fiji", "Finland", "France", "French Guiana", "French Polynesia", "French Southern Territories", "Gabon", "Gambia", "Georgia", "Germany", "Ghana", "Gibraltar", "Greece", "Greenland", "Grenada", "Guadeloupe", "Guam", "Guatemala", "Guernsey", "Guinea", "Guinea-Bissau", "Guyana", "Haiti", "Heard Is. & Mcdonald Islands", "Honduras", "Hong Kong", "Hungary", "Iceland", "India", "Indonesia", "Iran, Islamic Republic of", "Iraq", "Ireland", "Isle of Man", "Israel", "Italy", "Jamaica", "Japan", "Jersey", "Jordan", "Kazakhstan", "Kenya", "Kiribati", "Korea, Republic of", "Korea, <PERSON><PERSON>. People's Rep.", "Kosovo", "Kuwait", "Kyrgyzstan", "Lao", "Latvia", "Lebanon", "Lesotho", "Liberia", "Libyan Arab Jam<PERSON>riya", "Liechtenstein", "Lithuania", "Luxembourg", "Macao", "Macedonia", "Madagascar", "Malawi", "Malaysia", "Maldives", "Mali", "Malta", "Marshall Islands", "Martinique", "Mauritania", "Mauritius", "Mayotte", "Mexico", "Micronesia", "Moldova, Republic of", "Monaco", "Mongolia", "Montenegro", "Montserrat", "Morocco", "Mozambique", "Myanmar", "Namibia", "Nauru", "Nepal", "Netherlands", "Netherlands Antilles", "New Caledonia", "New Zealand", "Nicaragua", "Niger", "Nigeria", "Niue", "Norfolk Island", "Northern Mariana Islands", "Norway", "Oman", "Pakistan", "<PERSON><PERSON>", "Palestinian Territory, Occupied", "Panama", "Papua New Guinea", "Paraguay", "Peru", "Philippines", "Pitcairn", "Poland", "Portugal", "Puerto Rico", "Qatar", "Reunion", "Romania", "Russian Federation", "Rwanda", "<PERSON>", "Saint Helena", "Saint Kitts and Nevis", "Saint Lucia", "Saint <PERSON>", "Saint Pierre and Miquelon", "Saint Vincent and the Grenadines", "Samoa", "San Marino", "Sao Tome and Principe", "Saudi Arabia", "Senegal", "Serbia", "Seychelles", "Sierra Leone", "Singapore", "Sint Maarten", "Slovakia", "Slovenia", "Solomon Islands", "Somalia", "South Africa", "S. Georgia & S. Sandwich Is.", "Spain", "Sri Lanka", "Sudan", "South Sudan", "Suriname", "Svalbard and <PERSON>", "Sweden", "Switzerland", "Syrian Arab Republic", "Taiwan", "Tajikistan", "Tanzania, United Republic of", "Thailand", "Timor-Leste", "Togo", "Tokelau", "Tonga", "Trinidad and Tobago", "Tunisia", "Turkey", "Turkmenistan", "Turks and Caicos Islands", "Tuvalu", "Uganda", "Ukraine", "United Arab Emirates", "United Kingdom", "US Minor Outlying Islands", "Uruguay", "Uzbekistan", "Vanuatu", "Vatican City State", "Venezuela", "Vietnam", "Virgin Islands, British", "Virgin Islands, U.S.", "Wallis and Futuna", "Western Sahara", "Yemen", "Zambia", "Zimbabwe"]}, "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}], "relationships": [{"OBJECTPATH": "PARENT", "OBJECTNAME": "LOCATION", "LABEL": "Parent Location", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PARENTID"}, {"OBJECTPATH": "CONTACT", "OBJECTNAME": "CONTACT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CONTACTINFO.CONTACTNAME"}, {"OBJECTPATH": "MANAGER", "OBJECTNAME": "EMPLOYEE", "LABEL": "Manager", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "SUPERVISORID"}, {"OBJECTPATH": "SHIPTO", "OBJECTNAME": "CONTACT", "LABEL": "Ship to Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "SHIPTO.CONTACTNAME"}], "relationshipFields": [{"DATATYPE": "SUPERVISORNAME", "ID": "SUPERVISORNAME", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "SUPERVISORNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.PRINTAS", "DESCRIPTION": "PRINTAS", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTAS", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.EMAIL1", "DESCRIPTION": "EMAIL1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL1", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.EMAIL2", "DESCRIPTION": "EMAIL2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL2", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.FAX", "DESCRIPTION": "FAX", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FAX", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": ""}, {"DATATYPE": "FEDERALID", "ID": "FEDERALID", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FEDERALID", "xtremProperty": ""}, {"DATATYPE": "FIRSTMONTH", "ID": "FIRSTMONTH", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FIRSTMONTH", "xtremProperty": ""}, {"DATATYPE": "WEEKSTART", "ID": "WEEKSTART", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "WEEKSTART", "xtremProperty": ""}, {"DATATYPE": "IEPAYABLE", "ID": "IEPAYABLE.ACCOUNT", "DESCRIPTION": "ACCOUNT", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ACCOUNT", "xtremProperty": ""}, {"DATATYPE": "IEPAYABLE", "ID": "IEPAYABLE.NUMBER", "DESCRIPTION": "NUMBER", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "NUMBER", "xtremProperty": ""}, {"DATATYPE": "IERECEIVABLE", "ID": "IERECEIVABLE.ACCOUNT", "DESCRIPTION": "ACCOUNT", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ACCOUNT", "xtremProperty": ""}, {"DATATYPE": "IERECEIVABLE", "ID": "IERECEIVABLE.NUMBER", "DESCRIPTION": "NUMBER", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "NUMBER", "xtremProperty": ""}, {"DATATYPE": "MESSAGE_TEXT", "ID": "MESSAGE_TEXT", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "MESSAGE_TEXT", "xtremProperty": ""}, {"DATATYPE": "MARKETING_TEXT", "ID": "MARKETING_TEXT", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "MARKETING_TEXT", "xtremProperty": ""}, {"DATATYPE": "FOOTNOTETEXT", "ID": "FOOTNOTETEXT", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FOOTNOTETEXT", "xtremProperty": ""}, {"DATATYPE": "REPORTPRINTAS", "ID": "REPORTPRINTAS", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "REPORTPRINTAS", "xtremProperty": ""}, {"DATATYPE": "ISROOT", "ID": "ISROOT", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ISROOT", "xtremProperty": ""}, {"DATATYPE": "RESERVEAMT", "ID": "RESERVEAMT", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "RESERVEAMT", "xtremProperty": ""}, {"DATATYPE": "VENDORNAME", "ID": "VENDORNAME", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VENDORNAME", "xtremProperty": ""}, {"DATATYPE": "VENDORID", "ID": "VENDORID", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VENDORID", "xtremProperty": ""}, {"DATATYPE": "CUSTOMERID", "ID": "CUSTOMERID", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CUSTOMERID", "xtremProperty": ""}, {"DATATYPE": "CUSTOMERNAME", "ID": "CUSTOMERNAME", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CUSTOMERNAME", "xtremProperty": ""}, {"DATATYPE": "HAS_IE_RELATION", "ID": "HAS_IE_RELATION", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "HAS_IE_RELATION", "xtremProperty": ""}, {"DATATYPE": "CUSTTITLE", "ID": "CUSTTITLE", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CUSTTITLE", "xtremProperty": ""}, {"DATATYPE": "BUSINESSDAYS", "ID": "BUSINESSDAYS", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "BUSINESSDAYS", "xtremProperty": ""}, {"DATATYPE": "WEEKENDS", "ID": "WEEKENDS", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "WEEKENDS", "xtremProperty": ""}, {"DATATYPE": "FIRSTMONTHTAX", "ID": "FIRSTMONTHTAX", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FIRSTMONTHTAX", "xtremProperty": ""}, {"DATATYPE": "CONTACTKEY", "ID": "CONTACTKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTKEY", "xtremProperty": ""}, {"DATATYPE": "SUPERVISORKEY", "ID": "SUPERVISORKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "SUPERVISORKEY", "xtremProperty": ""}, {"DATATYPE": "PARENTKEY", "ID": "PARENTKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PARENTKEY", "xtremProperty": ""}, {"DATATYPE": "SHIPTOKEY", "ID": "SHIPTOKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "SHIPTOKEY", "xtremProperty": ""}, {"DATATYPE": "VENDENTITY", "ID": "VENDENTITY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VENDENTITY", "xtremProperty": ""}, {"DATATYPE": "CUSTENTITY", "ID": "CUSTENTITY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CUSTENTITY", "xtremProperty": ""}]}