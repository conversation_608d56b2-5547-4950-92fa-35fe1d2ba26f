{"name": "SODOCUMENTPARAMS", "documentType": "SO Transaction Definition", "fields": [{"ID": "DOCID", "LABEL": "Template name", "DESCRIPTION": "Template name", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DOCCLASS", "LABEL": "Template type", "DESCRIPTION": "Template type", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["Quote", "Order", "List", "Invoice", "Adjustment", "Other"]}, "ISCUSTOM": false}, {"ID": "CATEGORY", "LABEL": "Workflow category", "DESCRIPTION": "Workflow category", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["Quote", "Order", "Shipping", "Invoice", "Return"]}, "ISCUSTOM": false}, {"ID": "USERPERM", "LABEL": "Enable permissions for specific users/groups", "DESCRIPTION": "Enable permissions for specific users/groups", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "DESCRIPTION", "LABEL": "Description", "DESCRIPTION": "Description", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "UPDATES_INV", "LABEL": "Affects inventory", "DESCRIPTION": "Affects Inventory", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["No", "Quantity", "Value", "Quantity and Value"]}, "ISCUSTOM": false}, {"ID": "UPDATES_GL", "LABEL": "Transaction posting", "DESCRIPTION": "Transaction posting", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["A", "G", "N"]}, "ISCUSTOM": false}, {"ID": "POSTTOGL", "LABEL": "Enable additional posting", "DESCRIPTION": "Enable additional posting", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "CREDITLIMITCHECK", "LABEL": "Enforce credit limit", "DESCRIPTION": "Enforce credit limit", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "CUST_VEND", "LABEL": "Customer, vendor, or warehouse", "DESCRIPTION": "Customer, Vendor,  or Warehouse", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["Customer", "<PERSON><PERSON><PERSON>", "Warehouse"]}, "ISCUSTOM": false}, {"ID": "RECALLONLY", "LABEL": "Freeze recalled values", "DESCRIPTION": "Freeze Recalled Values", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "SALE_PUR_TRANS", "LABEL": "Sale/Purchase/Internal", "DESCRIPTION": "Sale/Purchase/Internal", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["Sale", "Purchase", "Internal", "TimeBill"]}, "ISCUSTOM": false}, {"ID": "CONVERT_UNITS", "LABEL": "Convert units to standard on save", "DESCRIPTION": "Convert Units to Standard on Save", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "EDITABLE_PRICE", "LABEL": "Editable price", "DESCRIPTION": "Editable Price", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "SHOW_TOTALS", "LABEL": "Enable subtotals", "DESCRIPTION": "Enable subtotals", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "SHOWEXPANDEDTOTALS", "LABEL": "Show expanded tax details", "DESCRIPTION": "Show expanded tax details", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "FORCE_PRICES", "LABEL": "Price must be supplied", "DESCRIPTION": "Price must be supplied", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "FIXED_MESG", "LABEL": "Optional default text", "DESCRIPTION": "Optional default text", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "EDITTYPE", "LABEL": "Edit policy", "DESCRIPTION": "Edit policy", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["No Edit", "Before Printing", "Edit in Draft Only", "All"]}, "ISCUSTOM": false}, {"ID": "DELTYPE", "LABEL": "Delete policy", "DESCRIPTION": "Delete policy", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["No Delete", "Before Printing", "All"]}, "ISCUSTOM": false}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Inactive", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "inactive"]}, "ISCUSTOM": false}, {"ID": "XSLTEMPLATE", "LABEL": "Printed document template", "DESCRIPTION": "Printed document template", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SUBTOTALTEMPLATE", "LABEL": "Subtotal template", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DEPTOVERRIDE", "LABEL": "Department", "DESCRIPTION": "Department", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "LOCOVERRIDE", "LABEL": "Location", "DESCRIPTION": "Location", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "SHIPPINGMETHOD", "LABEL": "Default ship via", "DESCRIPTION": "Default ship via", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SHOWTITLE1", "LABEL": "<PERSON> on Print", "DESCRIPTION": "<PERSON> on Print", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "SHOWTITLE2", "LABEL": "Ship To Show on Print", "DESCRIPTION": "Ship To Show on Print", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "CONTACTTITLE1", "LABEL": "Title of contact 1", "DESCRIPTION": "Title of Contact 1", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CONTACTTITLE2", "LABEL": "Title of contact 2", "DESCRIPTION": "Title of Contact 2", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "WARNONLOWQTY", "LABEL": "Warn user if item ONHAND quantity falls below zero", "DESCRIPTION": "Warn user if item ONHAND quantity falls below zero", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "CONVTYPE", "LABEL": "Partial conversion handling", "DESCRIPTION": "Partial conversion handling", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["Leave Transaction Open", "Close Original and Create Back Order", "Close Transaction"]}, "ISCUSTOM": false}, {"ID": "ALLOWEDITBILLTO", "LABEL": "<PERSON>ow Editing", "DESCRIPTION": "<PERSON>ow Editing", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "ALLOWEDITSHIPTO", "LABEL": "Ship To Allow Editing", "DESCRIPTION": "Ship To Allow Editing", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "DEFAULT_WAREHOUSE", "LABEL": "De<PERSON>ult warehouse", "DESCRIPTION": "De<PERSON>ult warehouse", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "WAREHOUSESELMETHOD", "LABEL": "Warehouse selection method", "DESCRIPTION": "Warehouse selection method", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["Sort by ID", "Sort by Name", "Warehouse with Available Inventory", "Use the default warehouse"]}, "ISCUSTOM": false}, {"ID": "ENABLEPAYMENTS", "LABEL": "Enable payment processing", "DESCRIPTION": "Enable payment processing", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "ENABLE_COSTING", "LABEL": "Enable costing for non-inventoried items", "DESCRIPTION": "Enable costing for non-inventoried items", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "CREATETYPE", "LABEL": "Create policy", "DESCRIPTION": "Create policy", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["New document or Convert", "Convert only"]}, "ISCUSTOM": false}, {"ID": "TD_CREATION_RULE", "LABEL": "Create transactions in", "DESCRIPTION": "Create transactions in", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Top level or Entity", "Top level only", "Entity only"]}, "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DESCRIPTION": "User who modified this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "CREATEDBY", "LABEL": "Created by", "DESCRIPTION": "User who created this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "ENABLE_RETAINAGE", "LABEL": "Enable retainage", "DESCRIPTION": "Enable retainage", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "ENABLEADDINFOSCOPE", "LABEL": "Enable scope", "DESCRIPTION": "Enable scope", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "ENABLEADDINFOSCHEDULE", "LABEL": "Enable schedule", "DESCRIPTION": "Enable schedule", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "ENABLEADDINFOINTERNALREF", "LABEL": "Enable internal reference ", "DESCRIPTION": "Enable internal reference", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "ENABLEADDINFOEXTERNALREF", "LABEL": "Enable external reference ", "DESCRIPTION": "Enable external reference", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "ENABLEADDINFOBOND", "LABEL": "Enable bond", "DESCRIPTION": "Enable bond", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "ENABLEDOCCHANGE", "LABEL": "Change document values", "DESCRIPTION": "Change document values", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["No Change", "Enable Change", "Change Order"]}, "ISCUSTOM": false}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}], "relationships": [{"OBJECTPATH": "MELOCATION", "OBJECTNAME": "LOCATION", "LABEL": "Created At Entity Information", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MEGAENTITYID"}], "relationshipFields": []}