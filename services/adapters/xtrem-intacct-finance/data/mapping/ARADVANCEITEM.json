{"name": "ARADVANCEITEM", "fields": [{"ID": "RECORDNO", "LABEL": "Record Number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Record number", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RECORDKEY", "LABEL": "Parent key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Parent record no", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ACCOUNTKEY", "LABEL": "Account key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Account key", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ACCOUNTNO", "LABEL": "Account", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "GL account", "VALIDVALUES": null, "xtremProperty": "account.intacctId"}, {"ID": "ACCOUNTTITLE", "LABEL": "Account title", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Account title", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ACCOUNTLABEL", "LABEL": "Account label", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Account label", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "AMOUNT", "LABEL": "Base amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": true, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "Base amount", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_AMOUNT", "LABEL": "Transaction amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "Transaction amount", "VALIDVALUES": null, "xtremProperty": "advanceAmount"}, {"ID": "DEPARTMENTID", "LABEL": "Department ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Department ID", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.departmentId"}, {"ID": "DEPARTMENTNAME", "LABEL": "Department name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Department name", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "LOCATIONID", "LABEL": "Location ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Location ID", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "LOCATIONNAME", "LABEL": "Location name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Location name", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ENTRY_DATE", "LABEL": "Entry date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Entry date", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ENTRYDESCRIPTION", "LABEL": "Memo", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Line description/memo", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "EXCH_RATE_DATE", "LABEL": "Exchange rate date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Exchange rate date", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "EXCH_RATE_TYPE_ID", "LABEL": "Exchange rate type", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Exchange rate type", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "EXCHANGE_RATE", "LABEL": "Exchange rate", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Exchange rate", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "LINEITEM", "LABEL": "Line item", "DATATYPE": "ENUM", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Line item", "VALIDVALUES": {"VALIDVALUE": ["T", "F"]}, "xtremProperty": ""}, {"ID": "LINE_NO", "LABEL": "Line no", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Line no.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BASECURR", "LABEL": "Base currency", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "Base currency", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CURRENCY", "LABEL": "Transaction currency", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "Transaction currency", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALPAID", "LABEL": "Total paid", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Total paid", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALPAID", "LABEL": "Total transaction paid", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Total transaction paid", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALSELECTED", "LABEL": "Total selected", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Total selected", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALSELECTED", "LABEL": "Total transaction selected", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Total transaction selected", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RECORDTYPE", "LABEL": "Record type", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Record type", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BASELOCATION", "LABEL": "Base location key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Base location key", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PARENTENTRY", "LABEL": "Parent entry key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Parent entry key", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENCREATED", "LABEL": "When created", "DATATYPE": "TIMESTAMP", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "timestamp marking last time this was created.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DATATYPE": "TIMESTAMP", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "timestamp marking last time this was changed.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CREATEDBY", "LABEL": "Created by", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "User who created this.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "User who modified this.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CLASSDIMKEY", "LABEL": "Classdimkey", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CLASSID", "LABEL": "Class", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.classId"}, {"ID": "CLASSNAME", "LABEL": "Class Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CUSTOMERDIMKEY", "LABEL": "Customerd<PERSON>key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CUSTOMERID", "LABEL": "Customer", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.customerId"}, {"ID": "CUSTOMERNAME", "LABEL": "Customer Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "VENDORDIMKEY", "LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "VENDORID", "LABEL": "<PERSON><PERSON><PERSON>", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.vendorId"}, {"ID": "VENDORNAME", "LABEL": "Vendor Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "EMPLOYEEDIMKEY", "LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "EMPLOYEEID", "LABEL": "Employee", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "EMPLOYEENAME", "LABEL": "Employee Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ITEMDIMKEY", "LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ITEMID", "LABEL": "<PERSON><PERSON>", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.itemId"}, {"ID": "ITEMNAME", "LABEL": "Item Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PROJECTDIMKEY", "LABEL": "Projectdimkey", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PROJECTID", "LABEL": "Project", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.projectId"}, {"ID": "PROJECTNAME", "LABEL": "Project Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WAREHOUSEDIMKEY", "LABEL": "Warehousedimkey", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WAREHOUSEID", "LABEL": "Warehouse", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WAREHOUSENAME", "LABEL": "Warehouse Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TASKDIMKEY", "LABEL": "<PERSON>dimkey", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TASKID", "LABEL": "Task", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.taskId"}, {"ID": "TASKNAME", "LABEL": "Task Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "VALIDVALUES": null, "xtremProperty": ""}], "xtremObject": "accounts_receivable_advance_line", "documentType": "", "relationships": [{"LABEL": "", "RELATEDBY": "ACCOUNTNO", "OBJECTNAME": "GLACCOUNT", "OBJECTPATH": "GLACCOUNT", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "DEPARTMENTID", "OBJECTNAME": "DEPARTMENT", "OBJECTPATH": "DEPARTMENT", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "LOCATIONID", "OBJECTNAME": "LOCATION", "OBJECTPATH": "LOCATION", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "ACCOUNTLABEL", "OBJECTNAME": "ARACCOUNTLABEL", "OBJECTPATH": "ACCOUNTLABEL", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}], "relationshipFields": []}