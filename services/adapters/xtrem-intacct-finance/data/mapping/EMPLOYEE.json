{"name": "EMPLOYEE", "documentType": "Employee", "xtremObject": "attribute", "additionnalLink": [{"xtremProperty": "attributeType.id", "type": "string", "xtremValues": ["employee"]}], "fields": [{"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "record<PERSON><PERSON>,employeeRecordNo"}, {"ID": "EMPLOYEEID", "LABEL": "Employee ID", "DESCRIPTION": "Unique ID of Employee", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "id"}, {"ID": "SSN", "LABEL": "SSN#", "DESCRIPTION": "Social security number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TITLE", "LABEL": "Title", "DESCRIPTION": "Title", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "LOCATIONID", "LABEL": "Location ID", "DESCRIPTION": "Location", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DEPARTMENTID", "LABEL": "Department ID", "DESCRIPTION": "Department", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "BIRTHDATE", "LABEL": "Birth date", "DESCRIPTION": "Birth Date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "STARTDATE", "LABEL": "Start date", "DESCRIPTION": "Start date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "ENDDATE", "LABEL": "End date", "DESCRIPTION": "End date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Inactive", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "inactive"]}, "ISCUSTOM": false, "xtremProperty": "statusIntacct"}, {"ID": "EMPLOYEETYPE", "LABEL": "Employee type", "DESCRIPTION": "Employee type", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "EMPLOYEETYPE1099TYPE", "LABEL": "Employee form 1099 type", "DESCRIPTION": "Form 1099 type", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "GENDER", "LABEL": "Gender", "DESCRIPTION": "Gender", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["", "female", "male"]}, "ISCUSTOM": false}, {"ID": "TERMINATIONTYPE", "LABEL": "Termination type", "DESCRIPTION": "Termination type", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["", "voluntary", "involuntary", "deceased", "disability", "retired"]}, "ISCUSTOM": false}, {"ID": "NAME1099", "LABEL": "1099 name", "DESCRIPTION": "1099 name", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "FORM1099TYPE", "LABEL": "Form 1099 type", "DESCRIPTION": "Form 1099 Type", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "FORM1099BOX", "LABEL": "Form 1099 box", "DESCRIPTION": "Form 1099 box", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "POSTACTUALCOST", "LABEL": "Post actual labor costs with variance", "DESCRIPTION": "Post actual labor costs with variance", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "PAYMETHODKEY", "LABEL": "Preferred payment method", "DESCRIPTION": "Preferred payment method", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["", "Printed Check", "EFT", "Cash", "ACH"]}, "ISCUSTOM": false}, {"ID": "ACHENABLED", "LABEL": "Enable ACH", "DESCRIPTION": "Enable ACH", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "ACHBANKROUTINGNUMBER", "LABEL": "ACH bank routing number", "DESCRIPTION": "ACH bank routing number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ACHACCOUNTNUMBER", "LABEL": "Account number", "DESCRIPTION": "ACH account number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ACHACCOUNTTYPE", "LABEL": "Account type", "DESCRIPTION": "ACH account type", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Checking Account", "Savings Account"]}, "ISCUSTOM": false}, {"ID": "ACHREMITTANCETYPE", "LABEL": "Account classification", "DESCRIPTION": "Account classification", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Personal(PPD)", "Business(CCD)", "Business(CTX)"]}, "ISCUSTOM": false}, {"ID": "PAYMENTNOTIFY", "LABEL": "Send automatic payment notification", "DESCRIPTION": "Send Payment Notification", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "GENERIC", "LABEL": "Placeholder resource", "DESCRIPTION": "Placeholder Resource", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "CONTACTKEY", "LABEL": "Contact record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "CURRENCY", "LABEL": "Default currency", "DESCRIPTION": "<PERSON><PERSON><PERSON>", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}], "relationships": [{"OBJECTPATH": "DEPARTMENT", "OBJECTNAME": "DEPARTMENT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "DEPARTMENTID"}, {"OBJECTPATH": "CONTACT", "OBJECTNAME": "CONTACT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PERSONALINFO.CONTACTNAME"}, {"OBJECTPATH": "MANAGER", "OBJECTNAME": "EMPLOYEE", "LABEL": "Manager", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "SUPERVISORID"}, {"OBJECTPATH": "LOCATION", "OBJECTNAME": "LOCATION", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "LOCATIONID"}, {"OBJECTPATH": "EMPLOYEERATE", "OBJECTNAME": "EMPLOYEERATE", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "EMPLOYEEID"}, {"OBJECTPATH": "EARNINGTYPE", "OBJECTNAME": "EARNINGTYPE", "LABEL": "Earning Type", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "EARNINGTYPEKEY"}, {"OBJECTPATH": "CLASS", "OBJECTNAME": "CLASS", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CLASSID"}, {"OBJECTPATH": "MELOCATION", "OBJECTNAME": "LOCATION", "LABEL": "Created At Entity Information", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MEGAENTITYID"}], "relationshipFields": [{"DATATYPE": "LOCATIONKEY", "ID": "LOCATIONKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LOCATIONKEY", "xtremProperty": ""}, {"DATATYPE": "DEPARTMENTKEY", "ID": "DEPARTMENTKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "DEPARTMENTKEY", "xtremProperty": ""}, {"DATATYPE": "PARENTKEY", "ID": "PARENTKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PARENTKEY", "xtremProperty": ""}, {"DATATYPE": "SUPERVISORID", "ID": "SUPERVISORID", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "SUPERVISORID", "xtremProperty": ""}, {"DATATYPE": "SUPERVISORNAME", "ID": "SUPERVISORNAME", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "SUPERVISORNAME", "xtremProperty": ""}, {"DATATYPE": "EMPTYPEKEY", "ID": "EMPTYPEKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMPTYPEKEY", "xtremProperty": ""}, {"DATATYPE": "CONTACT_NAME", "ID": "CONTACT_NAME", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACT_NAME", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.PREFIX", "DESCRIPTION": "PREFIX", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREFIX", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.FIRSTNAME", "DESCRIPTION": "FIRSTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FIRSTNAME", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.INITIAL", "DESCRIPTION": "INITIAL", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "INITIAL", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.LASTNAME", "DESCRIPTION": "LASTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LASTNAME", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.COMPANYNAME", "DESCRIPTION": "COMPANYNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.PRINTAS", "DESCRIPTION": "PRINTAS", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTAS", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.CELLPHONE", "DESCRIPTION": "CELLPHONE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CELLPHONE", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.PAGER", "DESCRIPTION": "PAGER", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PAGER", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.FAX", "DESCRIPTION": "FAX", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FAX", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.EMAIL1", "DESCRIPTION": "EMAIL1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL1", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.EMAIL2", "DESCRIPTION": "EMAIL2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL2", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.URL1", "DESCRIPTION": "URL1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL1", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.URL2", "DESCRIPTION": "URL2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL2", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "PERSONALINFO.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": ""}, {"DATATYPE": "ENTITY", "ID": "ENTITY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ENTITY", "xtremProperty": ""}, {"DATATYPE": "SUPDOCFOLDERNAME", "ID": "SUPDOCFOLDERNAME", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "SUPDOCFOLDERNAME", "xtremProperty": ""}, {"DATATYPE": "EARNINGTYPEKEY", "ID": "EARNINGTYPEKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EARNINGTYPEKEY", "xtremProperty": ""}, {"DATATYPE": "EARNINGTYPENAME", "ID": "EARNINGTYPENAME", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EARNINGTYPENAME", "xtremProperty": ""}, {"DATATYPE": "EARNINGTYPEMETHOD", "ID": "EARNINGTYPEMETHOD", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EARNINGTYPEMETHOD", "xtremProperty": ""}, {"DATATYPE": "CREATEDBY", "ID": "CREATEDBY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CREATEDBY", "xtremProperty": ""}, {"DATATYPE": "MODIFIEDBY", "ID": "MODIFIEDBY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "MODIFIEDBY", "xtremProperty": ""}, {"DATATYPE": "CLASSID", "ID": "CLASSID", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CLASSID", "xtremProperty": ""}, {"DATATYPE": "CLASSNAME", "ID": "CLASSNAME", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CLASSNAME", "xtremProperty": ""}, {"DATATYPE": "CLASSKEY", "ID": "CLASSKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CLASSKEY", "xtremProperty": ""}, {"DATATYPE": "MERGEPAYMENTREQ", "ID": "MERGEPAYMENTREQ", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "MERGEPAYMENTREQ", "xtremProperty": ""}, {"DATATYPE": "PERSONALINFO", "ID": "CONTACT.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": "name"}]}