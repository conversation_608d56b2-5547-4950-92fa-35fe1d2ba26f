{"name": "TASK", "documentType": "Task", "xtremObject": "attribute", "additionnalLink": [{"xtremProperty": "attributeType.id", "type": "string", "xtremValues": ["task"]}], "fields": [{"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "recordNo,taskRecordNo"}, {"ID": "NAME", "LABEL": "Task name", "DESCRIPTION": "No description specified", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "name"}, {"ID": "PROJECTID", "LABEL": "Project ID", "DESCRIPTION": "No description specified", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctProject"}, {"ID": "STANDARDTASKKEY", "LABEL": "Standard task key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "STANDARDTASKID", "LABEL": "Standard task ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "STANDARDTASKNAME", "LABEL": "Standard task name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TASKID", "LABEL": "Task ID", "DESCRIPTION": "No description specified", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "id"}, {"ID": "PBEGINDATE", "LABEL": "Planned begin date", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "PENDDATE", "LABEL": "Planned end date", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "CLASSID", "LABEL": "Class", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CLASSNAME", "LABEL": "Class Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ITEMID", "LABEL": "<PERSON><PERSON>", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "item.id"}, {"ID": "ITEMNAME", "LABEL": "Item Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "BILLABLE", "LABEL": "Billable", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "DESCRIPTION", "LABEL": "Description", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ISMILESTONE", "LABEL": "Milestone", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "UTILIZED", "LABEL": "Utilized", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "PRIORITY", "LABEL": "Priority", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TASKNO", "LABEL": "WBS code", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TASKSTATUS", "LABEL": "Task status", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PARENTKEY", "LABEL": "Parent task key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "PARENTID", "LABEL": "Parent task ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PARENTNAME", "LABEL": "Parent task name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SUPDOCID", "LABEL": "Attachment", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BUDGETQTY", "LABEL": "Budgeted duration (hours)", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "ESTQTY", "LABEL": "Estimated duration", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "CREATEDBY", "LABEL": "Created by", "DESCRIPTION": "User who created this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DESCRIPTION": "User who modified this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}], "relationships": [{"OBJECTPATH": "PARENT", "OBJECTNAME": "TASK", "LABEL": "Parent Task", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PARENTID"}, {"OBJECTPATH": "STANDARD", "OBJECTNAME": "TASK", "LABEL": "Standard Task", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "STANDARDTASKID"}, {"OBJECTPATH": "PROJECT", "OBJECTNAME": "PROJECT", "LABEL": "Project", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PROJECTID"}], "relationshipFields": [{"DATATYPE": "TASKSTATUSKEY", "ID": "TASKSTATUSKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": true, "LABEL": "TASKSTATUSKEY", "xtremProperty": ""}, {"DATATYPE": "CLASSID", "ID": "CLASSID", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CLASSID", "xtremProperty": ""}, {"DATATYPE": "CLASSNAME", "ID": "CLASSNAME", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CLASSNAME", "xtremProperty": ""}, {"DATATYPE": "CLASSKEY", "ID": "CLASSKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CLASSKEY", "xtremProperty": ""}, {"DATATYPE": "ITEMID", "ID": "ITEMID", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ITEMID", "xtremProperty": ""}, {"DATATYPE": "ITEMNAME", "ID": "ITEMNAME", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ITEMNAME", "xtremProperty": ""}, {"DATATYPE": "ITEMKEY", "ID": "ITEMKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ITEMKEY", "xtremProperty": ""}, {"DATATYPE": "SUPDOCID", "ID": "SUPDOCID", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "SUPDOCID", "xtremProperty": ""}]}