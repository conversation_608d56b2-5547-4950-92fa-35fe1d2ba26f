{"name": "GLENTRY", "documentType": "GL Entry", "xtremObject": "journal_entry_line", "fields": [{"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "BATCHNO", "LABEL": "Batch number", "DESCRIPTION": "Batch Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "USERNO", "LABEL": "User number", "DESCRIPTION": "User No", "REQUIRED": true, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "LINE_NO", "LABEL": "Line number", "DESCRIPTION": "Line Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "TR_TYPE", "LABEL": "TR type", "DESCRIPTION": "TR Type", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "VALIDVALUES": {"VALIDVALUE": ["1", "-1"]}, "ISCUSTOM": false, "xtremProperty": "intacctDocumentLine.sign"}, {"ID": "ENTRY_DATE", "LABEL": "Entry date", "DESCRIPTION": "Entry Date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "AMOUNT", "LABEL": "Amount", "DESCRIPTION": "Amount", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false, "xtremProperty": "intacctDocumentLine.absoluteTransactionAmount"}, {"ID": "TRX_AMOUNT", "LABEL": "Trx amount", "DESCRIPTION": "<PERSON>r<PERSON> Amount", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "BATCH_NUMBER", "LABEL": "Batch number", "DESCRIPTION": "Batch Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "BATCH_DATE", "LABEL": "Batch date", "DESCRIPTION": "Batch Date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "BATCHTITLE", "LABEL": "Batch title", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ACCOUNTKEY", "LABEL": "GL account key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "ACCOUNTNO", "LABEL": "Account", "DESCRIPTION": "Account", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "account.id"}, {"ID": "ACCOUNTTITLE", "LABEL": "Account title", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "STATISTICAL", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DEPARTMENTKEY", "LABEL": "Department key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "DEPARTMENT", "LABEL": "Department", "DESCRIPTION": "Department", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DEPARTMENTTITLE", "LABEL": "Department title", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "LOCATIONKEY", "LABEL": "Location key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "LOCATION", "LABEL": "Location", "DESCRIPTION": "Location", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctDocumentLine.locationWhenNoSplit"}, {"ID": "LOCATIONNAME", "LABEL": "Location name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DOCUMENT", "LABEL": "Doc", "DESCRIPTION": "Doc", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "commonReference"}, {"ID": "DESCRIPTION", "LABEL": "Memo", "DESCRIPTION": "Memo", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "description"}, {"ID": "UNITS", "LABEL": "Units", "DESCRIPTION": "Units", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "BASECURR", "LABEL": "Base currency", "DESCRIPTION": "Base Currency", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CURRENCY", "LABEL": "Txn currency", "DESCRIPTION": "Transaction Currency", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "transactionCurrency.id"}, {"ID": "CLEARED", "LABEL": "Cleared", "DESCRIPTION": "Cleared", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["T", "F", "M"]}, "ISCUSTOM": false}, {"ID": "CLRDATE", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "TIMEPERIOD", "LABEL": "Time period", "DESCRIPTION": "Time Period", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "ADJ", "LABEL": "<PERSON><PERSON>", "DESCRIPTION": "<PERSON><PERSON>", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["T", "F"]}, "ISCUSTOM": false}, {"ID": "EXCH_RATE_DATE", "LABEL": "Exchange rate date", "DESCRIPTION": "Exchange Rate Date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false, "xtremProperty": "fxRateDate"}, {"ID": "EXCH_RATE_TYPE_ID", "LABEL": "Exchange rate type", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "EXCHANGE_RATE", "LABEL": "Exchange rate", "DESCRIPTION": "Exchange Rate", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false, "xtremProperty": "companyFxRate"}, {"ID": "RECON_DATE", "LABEL": "Reconciliation date", "DESCRIPTION": "Date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "ALLOCATION", "LABEL": "Allocation", "DESCRIPTION": "Allocation", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctDocumentLine.allocationSplit"}, {"ID": "ALLOCATIONKEY", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "CREATEDBY", "LABEL": "Created by", "DESCRIPTION": "User who created this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DESCRIPTION": "User who modified this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "STATE", "LABEL": "State", "DESCRIPTION": "No description specified", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Draft", "Submitted", "Partially Approved", "Approved", "Posted", "Declined", "Reversal Pending", "Reversed"]}, "ISCUSTOM": false}, {"ID": "BILLABLE", "LABEL": "Billable", "DESCRIPTION": "Billable", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "BILLED", "LABEL": "Billed", "DESCRIPTION": "Billed", "REQUIRED": false, "READONLY": true, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "CLASSDIMKEY", "LABEL": "Classdimkey", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "CLASSID", "LABEL": "Class", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CLASSNAME", "LABEL": "Class Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CUSTOMERDIMKEY", "LABEL": "Customerd<PERSON>key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "CUSTOMERID", "LABEL": "Customer", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CUSTOMERNAME", "LABEL": "Customer Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "VENDORDIMKEY", "LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "VENDORID", "LABEL": "<PERSON><PERSON><PERSON>", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "VENDORNAME", "LABEL": "Vendor Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "EMPLOYEEDIMKEY", "LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "EMPLOYEEID", "LABEL": "Employee", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "EMPLOYEENAME", "LABEL": "Employee Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ITEMDIMKEY", "LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "ITEMID", "LABEL": "<PERSON><PERSON>", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ITEMNAME", "LABEL": "Item Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PROJECTDIMKEY", "LABEL": "Projectdimkey", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "PROJECTID", "LABEL": "Project", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PROJECTNAME", "LABEL": "Project Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "WAREHOUSEDIMKEY", "LABEL": "Warehousedimkey", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "WAREHOUSEID", "LABEL": "Warehouse", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "WAREHOUSENAME", "LABEL": "Warehouse Name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TASKID", "LABEL": "Task", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "DESCRIPTION": "No description specified"}, {"ID": "GLDIMREGION", "LABEL": "Region", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SPLIT", "LABEL": "Split", "DESCRIPTION": " Custom allocation split. Required if ALLOCATION equals Custom. Multiple SPLIT elements may then be passed.", "REQUIRED": false, "READONLY": false, "DATATYPE": "OBJECT", "ISCUSTOM": true, "xtremProperty": "intacctDocumentLine.dimensionSplit"}, {"ID": "TAXENTRIES", "LABEL": "Tax entries", "DATATYPE": "OBJECT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Tax entries", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.taxEntries"}], "relationships": [{"OBJECTPATH": "GLACCOUNT", "OBJECTNAME": "GLACCOUNT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "ACCOUNTNO"}, {"OBJECTPATH": "RGLDIM401000000204851_10018", "OBJECTNAME": "REGION", "LABEL": "Region", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "GLDIMREGION"}], "relationshipFields": [{"DATATYPE": "GLDIMRegion", "ID": "GLDIMRegion", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "GLDIMRegion", "xtremProperty": ""}]}