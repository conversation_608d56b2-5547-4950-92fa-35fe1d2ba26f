{"name": "DEPARTMENT", "documentType": "Department", "xtremObject": "dimension", "additionnalLink": [{"xtremProperty": "dimensionType.intacctObject", "type": "string", "xtremValues": ["DEPARTMENT"]}], "fields": [{"ID": "DEPARTMENTID", "LABEL": "ID", "DESCRIPTION": "Unique Identifier", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "id,intacctId"}, {"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "recordNo,departmentRecordNo"}, {"ID": "TITLE", "LABEL": "Name", "DESCRIPTION": "Free-form name for this department.", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "name"}, {"ID": "PARENTKEY", "LABEL": "Parent department record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "PARENTID", "LABEL": "Parent", "DESCRIPTION": "Parent Department", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SUPERVISORKEY", "LABEL": "Manager record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "SUPERVISORID", "LABEL": "Manager", "DESCRIPTION": "Manager", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "SUPERVISORNAME", "LABEL": "Manager name", "DESCRIPTION": "Department Manager Name", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Active-Non-Posting/Inactive", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "active non-posting", "inactive"]}, "ISCUSTOM": false, "xtremProperty": "statusIntacct"}, {"ID": "CUSTTITLE", "LABEL": "Department title", "DESCRIPTION": "Department title", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CREATEDBY", "LABEL": "Created by", "DESCRIPTION": "User who created this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DESCRIPTION": "User who modified this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}], "relationships": [{"OBJECTPATH": "PARENT", "OBJECTNAME": "DEPARTMENT", "LABEL": "Parent Department", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PARENTID"}, {"OBJECTPATH": "MANAGER", "OBJECTNAME": "EMPLOYEE", "LABEL": "Manager", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "SUPERVISORID"}], "relationshipFields": [{"DATATYPE": "RCUSTOMER", "ID": "RCUSTOMER", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "RCUSTOMER", "xtremProperty": ""}, {"DATATYPE": "RGLACCOUNT", "ID": "RGLACCOUNT", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "RGLACCOUNT", "xtremProperty": ""}]}