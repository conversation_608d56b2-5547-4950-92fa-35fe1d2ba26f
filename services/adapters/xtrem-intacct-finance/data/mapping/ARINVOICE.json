{"name": "ARINVOICE", "fields": [{"ID": "RECORDNO", "LABEL": "Record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocument.recordNo,intacctDocument.intacctId"}, {"ID": "RECORDTYPE", "LABEL": "Record type", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RECORDID", "LABEL": "Invoice number", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACTTAXGROUP", "LABEL": "Contact tax group", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "The customer shipto contact tax group", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "STATE", "LABEL": "State", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RAWSTATE", "LABEL": "Raw state", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CUSTOMERID", "LABEL": "Customer ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocument.billToCustomer"}, {"ID": "CUSTOMERNAME", "LABEL": "Customer name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CUSTEMAILOPTIN", "LABEL": "Accepts emailed invoices", "DATATYPE": "BOOLEAN", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Accepts emailed invoices", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "xtremProperty": ""}, {"ID": "TRX_ENTITYDUE", "LABEL": "Customer due", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CUSTMESSAGEID", "LABEL": "Message", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CUSTMESSAGE.MESSAGE", "LABEL": "Message text", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "DELIVERY_OPTIONS", "LABEL": "Customer delivery options", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": {"VALIDVALUE": ["Print", "E-Mail", "Print#~#E-Mail"]}, "xtremProperty": ""}, {"ID": "DOCNUMBER", "LABEL": "Reference number", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "number"}, {"ID": "DESCRIPTION", "LABEL": "Description", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "reference"}, {"ID": "DESCRIPTION2", "LABEL": "Document ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TERMNAME", "LABEL": "Term", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "paymentTerm.intacctId"}, {"ID": "TERMKEY", "LABEL": "Term key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TERMVALUE", "LABEL": "Term value", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENCREATED", "LABEL": "Date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "invoiceDate"}, {"ID": "WHENPOSTED", "LABEL": "GL posting date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "postingDate"}, {"ID": "WHENDISCOUNT", "LABEL": "Discount date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENDUE", "LABEL": "Due date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "dueDate"}, {"ID": "WHENPAID", "LABEL": "Date fully paid", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BASECURR", "LABEL": "Base currency", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "financialSite.legalCompany.currency.id"}, {"ID": "CURRENCY", "LABEL": "Transaction currency", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "currency.id"}, {"ID": "EXCH_RATE_DATE", "LABEL": "Exchange rate date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "fxRateDate"}, {"ID": "EXCH_RATE_TYPE_ID", "LABEL": "Exchange rate type", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "EXCHANGE_RATE", "LABEL": "Exchange rate", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALENTERED", "LABEL": "Total amount", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALSELECTED", "LABEL": "Total selected", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALPAID", "LABEL": "Total paid", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALDUE", "LABEL": "Total due", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALENTERED", "LABEL": "Total transaction amount", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALSELECTED", "LABEL": "Total transaction amount selected", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALPAID", "LABEL": "Total transaction amount paid", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALDUE", "LABEL": "Total transaction amount due", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTOPAYTOCONTACTNAME", "LABEL": "Bill to", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTORETURNTOCONTACTNAME", "LABEL": "Ship to", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTOPAYTOKEY", "LABEL": "Pay-to contact key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTORETURNTOKEY", "LABEL": "Return-to contact key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.VISIBLE", "LABEL": "Visible", "DATATYPE": "BOOLEAN", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Visible", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "xtremProperty": ""}, {"ID": "PRBATCH", "LABEL": "Summary", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PRBATCHKEY", "LABEL": "Summary key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MODULEKEY", "LABEL": "Module key", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SCHOPKEY", "LABEL": "Recurrung schedule key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SYSTEMGENERATED", "LABEL": "System generated", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": {"VALIDVALUE": ["T", "F"]}, "xtremProperty": ""}, {"ID": "HASPOSTEDREVREC", "LABEL": "Has posted rev rec", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": {"VALIDVALUE": ["T", "F"]}, "xtremProperty": ""}, {"ID": "BILLBACKTEMPLATEKEY", "LABEL": "<PERSON> back template key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "AUWHENCREATED", "LABEL": "Audit when created", "DATATYPE": "TIMESTAMP", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Audit timestamp when record was created.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DATATYPE": "TIMESTAMP", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CREATEDBY", "LABEL": "Created by", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "User who created this.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "User who modified this.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "DUE_IN_DAYS", "LABEL": "Due in", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.TAXGROUP.NAME", "LABEL": "Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "Name", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.TAXID", "LABEL": "Tax ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Tax Identification Number", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TAXSOLUTIONID", "LABEL": "Tax solution", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Tax solution", "VALIDVALUES": null, "xtremProperty": "intacctDocument.taxSolutionId"}, {"ID": "RETAINAGEPERCENTAGE", "LABEL": "Default retainage percentage", "DATATYPE": "PERCENT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALRETAINED", "LABEL": "Total txn amount retained", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALRELEASED", "LABEL": "Total txn amount released", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALRETAINED", "LABEL": "Total retained amount", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SUPDOCID", "LABEL": "Attachment", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PROJECTCONTRACTKEY", "LABEL": "Project contract key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Project contract key", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PROJECTCONTRACTID", "LABEL": "Project contract ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Project contract id", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "DUNNINGCOUNT", "LABEL": "Dunning notices", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "VALIDVALUES": null, "xtremProperty": ""}], "xtremObject": "accounts_receivable_invoice", "documentType": "", "relationships": [{"LABEL": "", "RELATEDBY": "CUSTOMERID", "OBJECTNAME": "CUSTOMER", "OBJECTPATH": "CUSTOMER", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "RECORD#", "OBJECTNAME": "SODOCUMENT", "OBJECTPATH": "SODOCUMENT", "xtremProperty": "", "RELATIONSHIPTYPE": "ONE2ONE"}, {"LABEL": "", "RELATEDBY": "SCHOPKEY", "OBJECTNAME": "ARRECURINVOICE", "OBJECTPATH": "ARRECURINVOICE", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "Bill to Contact", "RELATEDBY": "BILLTOPAYTOKEY", "OBJECTNAME": "CONTACTVERSION", "OBJECTPATH": "BILLTO", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "Ship to Contact", "RELATEDBY": "SHIPTORETURNTOKEY", "OBJECTNAME": "CONTACTVERSION", "OBJECTPATH": "SHIPTO", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "Term", "RELATEDBY": "TERMNAME", "OBJECTNAME": "ARTERM", "OBJECTPATH": "TERM", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "RECORDNO", "OBJECTNAME": "EXCHANGERATEINFO", "OBJECTPATH": "EXCHANGERATEINFO", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "TAXSOLUTIONKEY", "OBJECTNAME": "TAXSOLUTION", "OBJECTPATH": "TAXSOLUTION", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "PROJECTCONTRACTKEY", "OBJECTNAME": "PROJECTCONTRACT", "OBJECTPATH": "PROJECTCONTRACT", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "Created At Entity Information", "RELATEDBY": "MEGAENTITYID", "OBJECTNAME": "LOCATION", "OBJECTPATH": "MELOCATION", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}], "relationshipFields": [{"ID": "CONTACT.CONTACTNAME", "LABEL": "CONTACTNAME", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "CONTACTNAME", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.PREFIX", "LABEL": "PREFIX", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PREFIX", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.FIRSTNAME", "LABEL": "FIRSTNAME", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "FIRSTNAME", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.INITIAL", "LABEL": "INITIAL", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "INITIAL", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.LASTNAME", "LABEL": "LASTNAME", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "LASTNAME", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.COMPANYNAME", "LABEL": "COMPANYNAME", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "COMPANYNAME", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.PRINTAS", "LABEL": "PRINTAS", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PRINTAS", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.PHONE1", "LABEL": "PHONE1", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PHONE1", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.PHONE2", "LABEL": "PHONE2", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PHONE2", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.CELLPHONE", "LABEL": "CELLPHONE", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "CELLPHONE", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.PAGER", "LABEL": "PAGER", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PAGER", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.FAX", "LABEL": "FAX", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "FAX", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.EMAIL1", "LABEL": "EMAIL1", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "EMAIL1", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.EMAIL2", "LABEL": "EMAIL2", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "EMAIL2", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.URL1", "LABEL": "URL1", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "URL1", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.URL2", "LABEL": "URL2", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "URL2", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.VISIBLE", "LABEL": "VISIBLE", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "VISIBLE", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.MAILADDRESS.ADDRESS1", "LABEL": "ADDRESS1", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.ADDRESS1", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.MAILADDRESS.ADDRESS2", "LABEL": "ADDRESS2", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.ADDRESS2", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.MAILADDRESS.CITY", "LABEL": "CITY", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.CITY", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.MAILADDRESS.STATE", "LABEL": "STATE", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.STATE", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.MAILADDRESS.ZIP", "LABEL": "ZIP", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.ZIP", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.MAILADDRESS.COUNTRY", "LABEL": "COUNTRY", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.COUNTRY", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTACT.MAILADDRESS.COUNTRYCODE", "LABEL": "COUNTRYCODE", "DATATYPE": "CONTACT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.CONTACTNAME", "LABEL": "CONTACTNAME", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "CONTACTNAME", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.PREFIX", "LABEL": "PREFIX", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PREFIX", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.FIRSTNAME", "LABEL": "FIRSTNAME", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "FIRSTNAME", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.INITIAL", "LABEL": "INITIAL", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "INITIAL", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.LASTNAME", "LABEL": "LASTNAME", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "LASTNAME", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.COMPANYNAME", "LABEL": "COMPANYNAME", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "COMPANYNAME", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.PRINTAS", "LABEL": "PRINTAS", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PRINTAS", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.PHONE1", "LABEL": "PHONE1", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PHONE1", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.PHONE2", "LABEL": "PHONE2", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PHONE2", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.CELLPHONE", "LABEL": "CELLPHONE", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "CELLPHONE", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.PAGER", "LABEL": "PAGER", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PAGER", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.FAX", "LABEL": "FAX", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "FAX", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.EMAIL1", "LABEL": "EMAIL1", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "EMAIL1", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.EMAIL2", "LABEL": "EMAIL2", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "EMAIL2", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.URL1", "LABEL": "URL1", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "URL1", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.URL2", "LABEL": "URL2", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "URL2", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.VISIBLE", "LABEL": "VISIBLE", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "VISIBLE", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.MAILADDRESS.ADDRESS1", "LABEL": "ADDRESS1", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.ADDRESS1", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.MAILADDRESS.ADDRESS2", "LABEL": "ADDRESS2", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.ADDRESS2", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.MAILADDRESS.CITY", "LABEL": "CITY", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.CITY", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.MAILADDRESS.STATE", "LABEL": "STATE", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.STATE", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.MAILADDRESS.ZIP", "LABEL": "ZIP", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.ZIP", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.MAILADDRESS.COUNTRY", "LABEL": "COUNTRY", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.COUNTRY", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.MAILADDRESS.COUNTRYCODE", "LABEL": "COUNTRYCODE", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.CONTACTNAME", "LABEL": "CONTACTNAME", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "CONTACTNAME", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.PREFIX", "LABEL": "PREFIX", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PREFIX", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.FIRSTNAME", "LABEL": "FIRSTNAME", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "FIRSTNAME", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.INITIAL", "LABEL": "INITIAL", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "INITIAL", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.LASTNAME", "LABEL": "LASTNAME", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "LASTNAME", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.COMPANYNAME", "LABEL": "COMPANYNAME", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "COMPANYNAME", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.PRINTAS", "LABEL": "PRINTAS", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PRINTAS", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.PHONE1", "LABEL": "PHONE1", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PHONE1", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.PHONE2", "LABEL": "PHONE2", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PHONE2", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.CELLPHONE", "LABEL": "CELLPHONE", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "CELLPHONE", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.PAGER", "LABEL": "PAGER", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "PAGER", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.FAX", "LABEL": "FAX", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "FAX", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.EMAIL1", "LABEL": "EMAIL1", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "EMAIL1", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.EMAIL2", "LABEL": "EMAIL2", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "EMAIL2", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.URL1", "LABEL": "URL1", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "URL1", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.URL2", "LABEL": "URL2", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "URL2", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.MAILADDRESS.ADDRESS1", "LABEL": "ADDRESS1", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.ADDRESS1", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.MAILADDRESS.ADDRESS2", "LABEL": "ADDRESS2", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.ADDRESS2", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.MAILADDRESS.CITY", "LABEL": "CITY", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.CITY", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.MAILADDRESS.STATE", "LABEL": "STATE", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.STATE", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.MAILADDRESS.ZIP", "LABEL": "ZIP", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.ZIP", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.MAILADDRESS.COUNTRY", "LABEL": "COUNTRY", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.COUNTRY", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BILLTO.MAILADDRESS.COUNTRYCODE", "LABEL": "COUNTRYCODE", "DATATYPE": "BILLTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SHIPTO.TAXGROUP.RECORDNO", "LABEL": "RECORDNO", "DATATYPE": "SHIPTO", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": false, "DESCRIPTION": "TAXGROUP.RECORDNO", "VALIDVALUES": null, "xtremProperty": ""}]}