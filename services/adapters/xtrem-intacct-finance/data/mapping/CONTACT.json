{"name": "CONTACT", "documentType": "", "xtremObject": "business_entity_address", "fields": [{"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "intacctBusinessEntityAddress.recordNo"}, {"ID": "CONTACTNAME", "LABEL": "Contact name", "DESCRIPTION": "Unique Name to be used to identify the contact in lists", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctBusinessEntityAddress.intacctId, intacctBusinessEntityAddress.name"}, {"ID": "COMPANYNAME", "LABEL": "Company name", "DESCRIPTION": "Full name of the company", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PREFIX", "LABEL": "Mr./Ms./Mrs.", "DESCRIPTION": "Mr./Ms./Mrs.", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctBusinessEntityAddress.prefix"}, {"ID": "FIRSTNAME", "LABEL": "First name", "DESCRIPTION": "First Name", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "primaryContact.firstName"}, {"ID": "LASTNAME", "LABEL": "Last name", "DESCRIPTION": "Last Name", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "primaryContact.lastName"}, {"ID": "INITIAL", "LABEL": "Middle name", "DESCRIPTION": "Middle Name", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PRINTAS", "LABEL": "Print as", "DESCRIPTION": "Name as appears on official documents", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctBusinessEntityAddress.printAs"}, {"ID": "TAXABLE", "LABEL": "Taxable", "DESCRIPTION": "Taxable ", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "TAXGROUP", "LABEL": "Contact tax group", "DESCRIPTION": "Tax Group", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PHONE1", "LABEL": "Primary phone", "DESCRIPTION": "Primary phone", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "primaryContact.locationPhoneNumber"}, {"ID": "PHONE2", "LABEL": "Secondary phone", "DESCRIPTION": "Seconday phone", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "locationPhoneNumber"}, {"ID": "CELLPHONE", "LABEL": "Mobile", "DESCRIPTION": "Cellular Phone Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PAGER", "LABEL": "Pager", "DESCRIPTION": "Pager", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "FAX", "LABEL": "Fax", "DESCRIPTION": "Fax", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "EMAIL1", "LABEL": "Primary email address", "DESCRIPTION": "Primary email address", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "primaryContact.email"}, {"ID": "EMAIL2", "LABEL": "Secondary email addresses", "DESCRIPTION": "Secondary Email Address", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "URL1", "LABEL": "Primary URL", "DESCRIPTION": "Primary URL", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "URL2", "LABEL": "Secondary URL", "DESCRIPTION": "Secondary URL", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "VISIBLE", "LABEL": "Visible", "DESCRIPTION": "Visible", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Inactive", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "inactive"]}, "ISCUSTOM": false, "xtremProperty": "intacctBusinessEntityAddress.status"}, {"ID": "PRICELISTKEY", "LABEL": "Price list record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "TAXID", "LABEL": "Tax ID", "DESCRIPTION": "Tax Identification Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TAXGROUPKEY", "LABEL": "Tax group record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "PRICESCHEDULEKEY", "LABEL": "Price schedule record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "CREATEDBY", "LABEL": "Created by", "DESCRIPTION": "User who created this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DESCRIPTION": "User who modified this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}], "relationships": [{"OBJECTPATH": "MAILADDRESS", "OBJECTNAME": "MAILADDRESS", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MAILADDRESS.RECORDKEY"}, {"OBJECTPATH": "MELOCATION", "OBJECTNAME": "LOCATION", "LABEL": "Created At Entity Information", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MEGAENTITYID"}], "relationshipFields": [{"DATATYPE": "MAILADDRESS", "ID": "MAILADDRESS.ADDRESS1", "DESCRIPTION": "ADDRESS1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": "addressLine1"}, {"DATATYPE": "MAILADDRESS", "ID": "MAILADDRESS.ADDRESS2", "DESCRIPTION": "ADDRESS2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": "addressLine2"}, {"DATATYPE": "MAILADDRESS", "ID": "MAILADDRESS.CITY", "DESCRIPTION": "CITY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": "city"}, {"DATATYPE": "MAILADDRESS", "ID": "MAILADDRESS.STATE", "DESCRIPTION": "STATE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": "region"}, {"DATATYPE": "MAILADDRESS", "ID": "MAILADDRESS.ZIP", "DESCRIPTION": "ZIP", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": "postcode"}, {"DATATYPE": "MAILADDRESS", "ID": "MAILADDRESS.COUNTRY", "DESCRIPTION": "COUNTRY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "MAILADDRESS", "ID": "MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "COUNTRYCODE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": "country.id"}, {"DATATYPE": "MAILADDRESS", "ID": "MAILADDRESS.LATITUDE", "DESCRIPTION": "LATITUDE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LATITUDE", "xtremProperty": ""}, {"DATATYPE": "MAILADDRESS", "ID": "MAILADDRESS.LONGITUDE", "DESCRIPTION": "LONGITUDE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LONGITUDE", "xtremProperty": ""}]}