{"name": "ITEM", "documentType": "", "xtremObject": "item", "fields": [{"ID": "XTREEM_ID", "LABEL": "ID Xtrem", "DESCRIPTION": "ID Xtrem", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": true, "xtremProperty": ""}, {"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "recordNo"}, {"ID": "ITEMID", "LABEL": "Item ID", "DESCRIPTION": "Item Unique Identifier", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctItem.intacctId,id"}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Inactive", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "inactive"]}, "ISCUSTOM": false, "xtremProperty": "intacctItem.status"}, {"ID": "MRR", "LABEL": "MRR", "DESCRIPTION": "MRR", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "NAME", "LABEL": "Name", "DESCRIPTION": "name", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "name"}, {"ID": "EXTENDED_DESCRIPTION", "LABEL": "Extended description", "DESCRIPTION": "Extended Description", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "description"}, {"ID": "PODESCRIPTION", "LABEL": "Description on purchase transactions", "DESCRIPTION": "Extended Description", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SODESCRIPTION", "LABEL": "Description on sales transactions", "DESCRIPTION": "Extended Description", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CNDEFAULTBUNDLE", "LABEL": "Contract default bundle", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "CNMEACATEGORYNAME", "LABEL": "Contract fair value category", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PRODUCTLINEID", "LABEL": "Product line ID", "DESCRIPTION": "Product Line ID", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CYCLE", "LABEL": "Inventory cycle", "DESCRIPTION": "Inventory Cycle", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PRODUCTTYPE", "LABEL": "Product type", "DESCRIPTION": "Product Type", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Item for Resale", "Item not for Resale", "Discontinued", "Service", "Misc Charge"]}, "ISCUSTOM": false}, {"ID": "SUBSTITUTEID", "LABEL": "Substitute item", "DESCRIPTION": "Substitute Item ID", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SHIP_WEIGHT", "LABEL": "Shipping weight", "DESCRIPTION": "Shipping Weight", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "WHENLASTSOLD", "LABEL": "Date last sold", "DESCRIPTION": "Date Last Sold", "REQUIRED": false, "READONLY": true, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "WHENLASTRECEIVED", "LABEL": "Date last received", "DESCRIPTION": "Date Last Received", "REQUIRED": false, "READONLY": true, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "ALLOW_BACKORDER", "LABEL": "Allow backorder", "DESCRIPTION": "<PERSON><PERSON>", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "TAXABLE", "LABEL": "Taxable", "DESCRIPTION": "Taxable", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "COST_METHOD", "LABEL": "Cost method", "DESCRIPTION": "Cost Method", "REQUIRED": true, "READONLY": false, "CREATEONLY": true, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Standard", "Average", "FIFO", "LIFO"]}, "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "STANDARD_COST", "LABEL": "Standard cost", "DESCRIPTION": "Standard Cost", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "UOMGRP", "LABEL": "Unit of measure", "DESCRIPTION": "Unique Name for the Unit Group", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "DEFAULT_WAREHOUSE", "LABEL": "De<PERSON>ult warehouse", "DESCRIPTION": "Default Warehouse", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "NOTE", "LABEL": "Note", "DESCRIPTION": "Note", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "INV_PRECISION", "LABEL": "Inventory", "DESCRIPTION": "Inventory", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PO_PRECISION", "LABEL": "Purchasing", "DESCRIPTION": "Purchasing", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SO_PRECISION", "LABEL": "Sales", "DESCRIPTION": "Sales", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ITEMTYPE", "LABEL": "Item type", "DESCRIPTION": "Item Type", "REQUIRED": true, "READONLY": false, "CREATEONLY": true, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Inventory", "Non-Inventory", "Non-Inventory (Purchase only)", "Non-Inventory (Sales only)", "<PERSON>", "Stockable Kit"]}, "ISCUSTOM": false, "xtremProperty": "intacctItem.type"}, {"ID": "ENABLE_SERIALNO", "LABEL": "Enable serial tracking", "DESCRIPTION": "Enable Serial Tracking", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "SERIAL_MASKKEY", "LABEL": "Serial number mask", "DESCRIPTION": "Serial Number Mask", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ENABLE_LOT_CATEGORY", "LABEL": "Enable lot tracking", "DESCRIPTION": "Enable Lot Tracking", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "LOT_CATEGORYKEY", "LABEL": "Lot category", "DESCRIPTION": "Lot Category", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "ENABLE_BINS", "LABEL": "Enable bin tracking", "DESCRIPTION": "Enable Bin Tracking", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "ENABLE_EXPIRATION", "LABEL": "Enable expiration tracking", "DESCRIPTION": "Enable Expiration Tracking", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "UPC", "LABEL": "UPC", "DESCRIPTION": "UPC", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "REVPOSTING", "LABEL": "Revenue posting", "DESCRIPTION": "", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Component Level", "Kit Level"]}, "ISCUSTOM": false}, {"ID": "REVPRINTING", "LABEL": "Print format", "DESCRIPTION": "", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Individual Components", "<PERSON>"]}, "ISCUSTOM": false}, {"ID": "BASEPRICE", "LABEL": "Base price", "DESCRIPTION": "Base Price", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "VSOECATEGORY", "LABEL": "Fair value category", "DESCRIPTION": "Fair Value Category", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Product - Specified", "Software", "Product - Unspecified", "Upgrade - Unspecified", "Upgrade - Specified", "Services", "Post Contract Support(PCS)"]}, "ISCUSTOM": false}, {"ID": "VSOEDLVRSTATUS", "LABEL": "Default delivery status", "DESCRIPTION": "Default Delivery Status", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Delivered", "Undelivered"]}, "ISCUSTOM": false}, {"ID": "VSOEREVDEFSTATUS", "LABEL": "Default deferral status", "DESCRIPTION": "Default Deferral Status", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Defer until item is delivered", "Defer bundle until item is delivered"]}, "ISCUSTOM": false}, {"ID": "HASSTARTENDDATES", "LABEL": "Item has start and end date", "DESCRIPTION": "Item Has Start and End Date ", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "TERMPERIOD", "LABEL": "Periods measured in", "DESCRIPTION": "Term Period", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Days", "Weeks", "Months", "Years"]}, "ISCUSTOM": false}, {"ID": "TOTALPERIODS", "LABEL": "Number of periods", "DESCRIPTION": "Default Number of Periods", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "COMPUTEFORSHORTTERM", "LABEL": "Allow to prorate price", "DESCRIPTION": "Compute Price for Short Term ", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "TAXCODE", "LABEL": "Tax code", "DESCRIPTION": "Tax Code", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "CREATEDBY", "LABEL": "Created by", "DESCRIPTION": "User who created this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DESCRIPTION": "User who modified this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "GLGRPKEY", "LABEL": "GL group record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "UOMGRPKEY", "LABEL": "UOM group record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "DROPSHIP", "LABEL": "Available for drop ship", "DESCRIPTION": "Available for drop ship", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "BUYTOORDER", "LABEL": "Available for buy to order", "DESCRIPTION": "Available for buy to order", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "DEFCONTRACTDELIVERYSTATUS", "LABEL": "Default contract delivery status", "DESCRIPTION": "Default contract delivery status", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Delivered", "Undelivered"]}, "ISCUSTOM": false}, {"ID": "DEFCONTRACTDEFERRALSTATUS", "LABEL": "Default contract deferral status", "DESCRIPTION": "Default deferral status", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Defer revenue until item is delivered", "Defer revenue until all items are delivered"]}, "ISCUSTOM": false}, {"ID": "ENABLE_REPLENISHMENT", "LABEL": "Enable replenishment for this item", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "REPLENISHMENT_METHOD", "LABEL": "Replenishment method", "DESCRIPTION": "Replenishment method", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "VALIDVALUES": {"VALIDVALUE": ["Demand forecast by single value", "Reorder point", "Demand forecast by fluctuating values"]}, "ISCUSTOM": false}, {"ID": "DEFAULT_REPLENISHMENT_UOM", "LABEL": "Units of measure default", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "REORDER_POINT", "LABEL": "Reorder point", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "SAFETY_STOCK", "LABEL": "Safety stock", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "REORDER_QTY", "LABEL": "Quantity to reorder", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MAX_ORDER_QTY", "LABEL": "Maximum order quantity", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "AUTOPRINTLABEL", "LABEL": "Auto print label", "DESCRIPTION": "Auto print label", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "ALLOWMULTIPLETAXGRPS", "LABEL": "Enable multiple item tax groups, one per each tax solution", "DESCRIPTION": "Enable multiple item tax groups, one per each tax solution", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "XTREEM", "LABEL": "XTreeM", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": true}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremObject": "intacctItem.url"}], "relationships": [{"OBJECTPATH": "GLGROUP", "OBJECTNAME": "ITEMGLGROUP", "LABEL": "Item GL group", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "GLGRPKEY"}, {"OBJECTPATH": "PRODUCTLINE", "OBJECTNAME": "PRODUCTLINE", "LABEL": "Product line", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PRODUCTLINEID"}, {"OBJECTPATH": "TAXGROUP", "OBJECTNAME": "TAXGROUP", "LABEL": "Item tax group", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "TAXGROUPKEY"}, {"OBJECTPATH": "CNMEACATEGORY", "OBJECTNAME": "MEACATEGORY", "LABEL": "Billing template", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CNMEACATEGORYNAME"}, {"OBJECTPATH": "CNBILLINGTEMPLATE", "OBJECTNAME": "CONTRACTBILLINGTEMPLATE", "LABEL": "Billing template", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CNBILLINGTEMPLATENAME"}, {"OBJECTPATH": "CNREVENUETEMPLATE", "OBJECTNAME": "CONTRACTREVENUETEMPLATE", "LABEL": "Revenue template # 1", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CNREVENUETEMPLATENAME"}, {"OBJECTPATH": "CNREVENUE2TEMPLATE", "OBJECTNAME": "CONTRACTREVENUETEMPLATE", "LABEL": "Revenue template # 2", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CNREVENUE2TEMPLATENAME"}, {"OBJECTPATH": "CNEXPENSETEMPLATE", "OBJECTNAME": "CONTRACTEXPENSETEMPLATE", "LABEL": "Expense template # 1", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CNEXPENSETEMPLATENAME"}, {"OBJECTPATH": "CNEXPENSE2TEMPLATE", "OBJECTNAME": "CONTRACTEXPENSETEMPLATE", "LABEL": "Expense template # 2", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CNEXPENSE2TEMPLATENAME"}, {"OBJECTPATH": "MELOCATION", "OBJECTNAME": "LOCATION", "LABEL": "Created At Entity Information", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MEGAENTITYID"}], "relationshipFields": [{"DATATYPE": "CNBILLINGTEMPLATENAME", "ID": "CNBILLINGTEMPLATENAME", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CNBILLINGTEMPLATENAME", "xtremProperty": ""}, {"DATATYPE": "CNREVENUETEMPLATENAME", "ID": "CNREVENUETEMPLATENAME", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CNREVENUETEMPLATENAME", "xtremProperty": ""}, {"DATATYPE": "CNREVENUE2TEMPLATENAME", "ID": "CNREVENUE2TEMPLATENAME", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CNREVENUE2TEMPLATENAME", "xtremProperty": ""}, {"DATATYPE": "CNEXPENSETEMPLATENAME", "ID": "CNEXPENSETEMPLATENAME", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CNEXPENSETEMPLATENAME", "xtremProperty": ""}, {"DATATYPE": "CNEXPENSE2TEMPLATENAME", "ID": "CNEXPENSE2TEMPLATENAME", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CNEXPENSE2TEMPLATENAME", "xtremProperty": ""}, {"DATATYPE": "TAXGROUPKEY", "ID": "TAXGROUPKEY", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TAXGROUPKEY", "xtremProperty": ""}]}