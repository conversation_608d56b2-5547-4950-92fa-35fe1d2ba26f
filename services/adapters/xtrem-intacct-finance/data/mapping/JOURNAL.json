{"name": "JOURNAL", "documentType": "Journal", "xtremObject": "journal", "fields": [{"ID": "RECORD#", "LABEL": "Record number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "recordNo"}, {"ID": "SYMBOL", "LABEL": "Symbol", "DESCRIPTION": "Symbol", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "id, intacctId"}, {"ID": "TITLE", "LABEL": "Title", "DESCRIPTION": "Title", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "name"}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Inactive", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "inactive"]}, "ISCUSTOM": false, "xtremProperty": "statusIntacct"}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "CREATEDBY", "LABEL": "Created by", "DESCRIPTION": "User who created this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DESCRIPTION": "User who modified this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "BILLABLE", "LABEL": "Enable project billing flag", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}], "relationships": [null], "relationshipFields": []}