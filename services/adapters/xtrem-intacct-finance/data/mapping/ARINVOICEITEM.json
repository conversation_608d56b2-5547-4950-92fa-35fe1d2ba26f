{"name": "ARINVOICEITEM", "fields": [{"ID": "RECORDNO", "LABEL": "Record Number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RECORDKEY", "LABEL": "Parent key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ACCOUNTKEY", "LABEL": "Account key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ACCOUNTNO", "LABEL": "Account", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "account.intacctId"}, {"ID": "OFFSETACCOUNTKEY", "LABEL": "Offset Account key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "OFFSETGLACCOUNTNO", "LABEL": "AR account", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "document.account.intacctId"}, {"ID": "OFFSETGLACCOUNTTITLE", "LABEL": "AR Account title", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ACCOUNTTITLE", "LABEL": "Account title", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ACCOUNTLABELKEY", "LABEL": "Account label key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ACCOUNTLABEL", "LABEL": "Account label", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ENTRY_DATE", "LABEL": "Entry Date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "AMOUNT", "LABEL": "Base amount", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_AMOUNT", "LABEL": "Transaction amount", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.signedAmountExcludingTax"}, {"ID": "DEPARTMENTID", "LABEL": "Department ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.departmentId"}, {"ID": "DEPARTMENTNAME", "LABEL": "Department name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "LOCATIONID", "LABEL": "Location ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "financialSite.id"}, {"ID": "LOCATIONNAME", "LABEL": "Location name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ENTRYDESCRIPTION", "LABEL": "Memo", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.memo"}, {"ID": "EXCH_RATE_DATE", "LABEL": "Exchange rate date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "EXCH_RATE_TYPE_ID", "LABEL": "Exchange rate type", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "EXCHANGE_RATE", "LABEL": "Exchange rate", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ALLOCATIONKEY", "LABEL": "Allocation key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ALLOCATION", "LABEL": "Allocation", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "LINEITEM", "LABEL": "Line item", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": {"VALIDVALUE": ["T", "F"]}, "xtremProperty": ""}, {"ID": "LINE_NO", "LABEL": "Line no", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CURRENCY", "LABEL": "Transaction currency", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BASECURR", "LABEL": "Base currency", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALPAID", "LABEL": "Total paid", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALPAID", "LABEL": "Total transaction paid", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALSELECTED", "LABEL": "Total selected", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALSELECTED", "LABEL": "Total transaction selected", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SUBTOTAL", "LABEL": "Is sub total", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": {"VALIDVALUE": ["S", "T"]}, "xtremProperty": ""}, {"ID": "PARENTENTRY", "LABEL": "Parent entry key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "DEFERREVENUE", "LABEL": "Defer revenue", "DATATYPE": "BOOLEAN", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "xtremProperty": ""}, {"ID": "REVRECTEMPLATEKEY", "LABEL": "Rev rec template key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "REVRECTEMPLATE", "LABEL": "Rev rec template", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "DEFERREDREVACCTKEY", "LABEL": "Deferred rev rec account key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "DEFERREDREVACCTNO", "LABEL": "Deferred rev rec account", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "DEFERREDREVACCTTITLE", "LABEL": "Deferred rev rec account title", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "REVRECSTARTDATE", "LABEL": "Rev rec start date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "REVRECENDDATE", "LABEL": "Rev rec end date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "BASELOCATION", "LABEL": "Base location key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "STATE", "LABEL": "State", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RECORDTYPE", "LABEL": "Record type", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "DETAILKEY", "LABEL": "Tax detail key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENCREATED", "LABEL": "When created", "DATATYPE": "TIMESTAMP", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "timestamp marking last time this was created.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DATATYPE": "TIMESTAMP", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "timestamp marking last time this was changed.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CREATEDBY", "LABEL": "Created by", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "User who created this.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "User who modified this.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RETAINAGEPERCENTAGE", "LABEL": "Retainage Percentage", "DATATYPE": "PERCENT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_AMOUNTRETAINED", "LABEL": "Txn amount retained", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "AMOUNTRETAINED", "LABEL": "Base amount retained", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_AMOUNTRELEASED", "LABEL": "Txn amount released", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RETAINAGE_OFFSETGLACCOUNTNO", "LABEL": "Retainage offset account no", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PROJECTCONTRACTID", "LABEL": "Project contract ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Project contract id", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PROJECTCONTRACTKEY", "LABEL": "Project contract key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Project contract key", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PROJECTCONTRACTLINEID", "LABEL": "Project contract line ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PROJECTCONTRACTLINEKEY", "LABEL": "Project contract line key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CLASSDIMKEY", "LABEL": "Classdimkey", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CLASSID", "LABEL": "Class", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.classId"}, {"ID": "CLASSNAME", "LABEL": "Class Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CUSTOMERDIMKEY", "LABEL": "Customerd<PERSON>key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CUSTOMERID", "LABEL": "Customer", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.customerId"}, {"ID": "CUSTOMERNAME", "LABEL": "Customer Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "VENDORDIMKEY", "LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "VENDORID", "LABEL": "<PERSON><PERSON><PERSON>", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.vendorId"}, {"ID": "VENDORNAME", "LABEL": "Vendor Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "EMPLOYEEDIMKEY", "LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "EMPLOYEEID", "LABEL": "Employee", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.employeeId"}, {"ID": "EMPLOYEENAME", "LABEL": "Employee Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ITEMDIMKEY", "LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "ITEMID", "LABEL": "<PERSON><PERSON>", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.itemId"}, {"ID": "ITEMNAME", "LABEL": "Item Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PROJECTDIMKEY", "LABEL": "Projectdimkey", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PROJECTID", "LABEL": "Project", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.projectId"}, {"ID": "PROJECTNAME", "LABEL": "Project Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WAREHOUSEDIMKEY", "LABEL": "Warehousedimkey", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WAREHOUSEID", "LABEL": "Warehouse", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WAREHOUSENAME", "LABEL": "Warehouse Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TASKDIMKEY", "LABEL": "<PERSON>dimkey", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TASKID", "LABEL": "Task", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.taskId"}, {"ID": "TASKNAME", "LABEL": "Task Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTRACTDIMKEY", "LABEL": "Contract<PERSON><PERSON><PERSON>", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTRACTID", "LABEL": "Contract", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CONTRACTNAME", "LABEL": "Contract Name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TAXENTRIES", "LABEL": "Tax entries", "DATATYPE": "OBJECT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Tax entries", "VALIDVALUES": null, "xtremProperty": "intacctDocumentLine.taxEntries"}], "xtremObject": "accounts_receivable_invoice_line", "documentType": "", "relationships": [{"LABEL": "", "RELATEDBY": "ACCOUNTNO", "OBJECTNAME": "GLACCOUNT", "OBJECTPATH": "GLACCOUNT", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "AR Account", "RELATEDBY": "OFFSETGLACCOUNTNO", "OBJECTNAME": "GLACCOUNT", "OBJECTPATH": "OFFSETGLACCOUNTNO", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "DEPARTMENTID", "OBJECTNAME": "DEPARTMENT", "OBJECTPATH": "DEPARTMENT", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "LOCATIONID", "OBJECTNAME": "LOCATIONENTITY", "OBJECTPATH": "LOCATIONENTITY", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "PROJECTCONTRACTKEY", "OBJECTNAME": "PROJECTCONTRACT", "OBJECTPATH": "PROJECTCONTRACT", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "PROJECTCONTRACTLINEKEY", "OBJECTNAME": "PROJECTCONTRACTLINE", "OBJECTPATH": "PROJECTCONTRACTLINE", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "ACCOUNTLABEL", "OBJECTNAME": "ARACCOUNTLABEL", "OBJECTPATH": "ACCOUNTLABEL", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "ALLOCATION", "OBJECTNAME": "ALLOCATION", "OBJECTPATH": "ALLOCATION", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "REVRECTEMPLATEKEY", "OBJECTNAME": "REVRECTEMPLATE", "OBJECTPATH": "REVRECTEMPLATE", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "Deferred Revenue GL Account", "RELATEDBY": "DEFERREDREVACCTNO", "OBJECTNAME": "GLACCOUNT", "OBJECTPATH": "DEFERREDREVENUEACCT", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}], "relationshipFields": []}