{"name": "GLBATCH", "documentType": "GL Batch", "xtremObject": "journal_entry", "fields": [{"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "intacctDocument.recordNo"}, {"ID": "BATCHNO", "LABEL": "Transaction number", "DESCRIPTION": "Transaction No", "REQUIRED": true, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "intacctDocument.intacctId"}, {"ID": "BATCH_TITLE", "LABEL": "Description", "DESCRIPTION": "Description", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "description"}, {"ID": "BALANCE", "LABEL": "Balance", "DESCRIPTION": "Balance", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "JOURNAL", "LABEL": "Journal", "DESCRIPTION": "Journal", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "journal.id"}, {"ID": "BATCH_DATE", "LABEL": "Posting date", "DESCRIPTION": "Posting date", "REQUIRED": true, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false, "xtremProperty": "postingDate"}, {"ID": "MODULE", "LABEL": "<PERSON><PERSON><PERSON>", "DESCRIPTION": "<PERSON><PERSON><PERSON>", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "USERKEY", "LABEL": "User", "DESCRIPTION": "USERKEY", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "REFERENCENO", "LABEL": "Reference number", "DESCRIPTION": "Reference Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "number"}, {"ID": "REVERSED", "LABEL": "Automatic reversal date", "DESCRIPTION": "Automatic reversal date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "REVERSEDKEY", "LABEL": "Reversed key", "DESCRIPTION": "Reversed Key", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "REVERSEDFROM", "LABEL": "Reversed from", "DESCRIPTION": "Reversed From", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TEMPLATEKEY", "LABEL": "Template key", "DESCRIPTION": "Template Key", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "PRBATCHKEY", "LABEL": "PRBatch key", "DESCRIPTION": "PRBatch Key", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MODIFIED", "LABEL": "Last modified date and time", "DESCRIPTION": "Last modified date and time", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "MODIFIEDBYID", "LABEL": "Last modified by", "DESCRIPTION": "Last Modified By", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SCHOPKEY", "LABEL": "Scheduled operation key", "DESCRIPTION": "Scheduled Operation Key", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "BASELOCATION", "LABEL": "No label specified", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "BASELOCATION_NO", "LABEL": "Source entity", "DESCRIPTION": "Source Entity", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": ""}, {"ID": "USERINFO.LOGINID", "LABEL": "User ID", "DESCRIPTION": "User ID", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "CREATEDBY", "LABEL": "Created by", "DESCRIPTION": "User who created this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DESCRIPTION": "User who modified this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "STATE", "LABEL": "State", "DESCRIPTION": "No description specified", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Draft", "Submitted", "Partially Approved", "Approved", "Posted", "Declined", "Reversal pending", "Reversed"]}, "ISCUSTOM": false}, {"ID": "JOURNALSEQNO", "LABEL": "Accounting sequence number", "DESCRIPTION": "Accounting sequence number", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TAXIMPLICATIONS", "LABEL": "Tax implications", "DATATYPE": "ENUM", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "DESCRIPTION": "Indicate whether you want to add taxes to the journal entries. Valid values are None/Inbound/Outbound", "VALIDVALUES": {"VALIDVALUE": ["None", "Inbound", "Outbound"]}, "xtremObject": "taxImplications", "xtremProperty": "intacctDocument.taxImplications"}, {"ID": "VATVENDORID", "LABEL": "<PERSON><PERSON><PERSON>", "DESCRIPTION": "Vendor id when tax implication is Inbound taxes (purchases)", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "VATCUSTOMERID", "LABEL": "Customer", "DESCRIPTION": "Customer id when tax implication is Outbound taxes (sales)", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "VATCONTACTID", "LABEL": "Contact", "DESCRIPTION": "Customer Shipto contact id (for sales journals) or the Vendor Payto contact id (for purchase journals)", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TAXSOLUTIONID", "LABEL": "Tax solution", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "DESCRIPTION": "Tax solution", "xtremObject": "taxSolutionId", "xtremProperty": "intacctDocument.taxSolutionId"}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}], "relationships": [{"OBJECTPATH": "JOURNAL", "OBJECTNAME": "JOURNAL", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "JOURNAL"}, {"OBJECTPATH": "MELOCATION", "OBJECTNAME": "LOCATION", "LABEL": "Created At Entity Information", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MEGAENTITYID"}], "relationshipFields": []}