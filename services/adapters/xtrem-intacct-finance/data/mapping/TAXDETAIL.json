{"name": "TAXDETAIL", "documentType": "Tax Detail", "xtremObject": "tax", "fields": [{"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "recordNo"}, {"ID": "DETAILID", "LABEL": "Name", "DESCRIPTION": "Name", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctId,name"}, {"ID": "DESCRIPTION", "LABEL": "Description", "DESCRIPTION": "Description", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "description"}, {"ID": "TAXTYPE", "LABEL": "Tax type", "DESCRIPTION": "Tax type can be Purchase or Sale", "REQUIRED": false, "READONLY": false, "DATATYPE": "ENUM", "ISCUSTOM": false, "xtremProperty": "intacctTaxType"}, {"ID": "VALUE", "LABEL": "Percent", "DESCRIPTION": "Tax rate percent", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false, "xtremProperty": "rate"}, {"ID": "GLACCOUNT", "LABEL": "GL account", "DESCRIPTION": "GL account that will be used to post the tax lines", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctAccount"}, {"ID": "REVERSECHARGE", "LABEL": "Is reverse charge", "DESCRIPTION": "Reverse charge flag", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "ISCUSTOM": false, "xtremProperty": "isIntacctReverseCharge", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}}, {"ID": "TAXSOLUTIONID", "LABEL": "Tax solution", "DESCRIPTION": "Tax solution", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctSolutionId"}]}