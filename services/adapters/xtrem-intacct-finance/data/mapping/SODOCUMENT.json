{"name": "SODOCUMENT", "documentType": "Sales Order", "fields": [{"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "Record Number", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DOCNO", "LABEL": "Document number", "DESCRIPTION": "Document Number", "REQUIRED": true, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "intacctId"}, {"ID": "DOCID", "LABEL": "Document ID", "DESCRIPTION": "Document ID", "REQUIRED": true, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "number"}, {"ID": "CREATEDFROM", "LABEL": "Converted from", "DESCRIPTION": "Converted From", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "STATE", "LABEL": "State", "DESCRIPTION": "State", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Submitted", "Approved", "Partially Approved", "Declined", "Draft", "Pending", "Closed", "In Progress", "Converted", "Partially Converted"]}, "ISCUSTOM": false}, {"ID": "CLOSED", "LABEL": "Closed", "DESCRIPTION": "Closed", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "Date", "DESCRIPTION": "Date created", "REQUIRED": true, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "AUWHENCREATED", "LABEL": "Audit when created", "DESCRIPTION": "Audit timestamp when record was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "Modified date", "DESCRIPTION": "timestamp marking last time this was changed.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENDUE", "LABEL": "Date due", "DESCRIPTION": "Date Due", "REQUIRED": true, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Inactive", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "inactive"]}, "ISCUSTOM": false}, {"ID": "PONUMBER", "LABEL": "Reference number", "DESCRIPTION": "PO Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "VENDORDOCNO", "LABEL": "Vendor document number", "DESCRIPTION": "Vendor Document Number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DOCPARID", "LABEL": "Type", "DESCRIPTION": "Document Template ID", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DOCPARKEY", "LABEL": "Transaction defintion key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "NOTE", "LABEL": "Note", "DESCRIPTION": "Note", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MESSAGE", "LABEL": "Message", "DESCRIPTION": "Ms<PERSON><PERSON>", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PRINTED", "LABEL": "Printed", "DESCRIPTION": "Document Printed", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Yes", "No"]}, "ISCUSTOM": false}, {"ID": "BACKORDER", "LABEL": "Back order?", "DESCRIPTION": "Back Order", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Yes", "No"]}, "ISCUSTOM": false}, {"ID": "SUBTOTAL", "LABEL": "Subtotal", "DESCRIPTION": "Subtotal", "REQUIRED": false, "READONLY": true, "DATATYPE": "CURRENCY", "ISCUSTOM": false}, {"ID": "TOTAL", "LABEL": "Total", "DESCRIPTION": "Total", "REQUIRED": false, "READONLY": true, "DATATYPE": "CURRENCY", "ISCUSTOM": false}, {"ID": "TRX_CURRENCY_SYMBOL", "LABEL": "Transaction currency symbol", "DESCRIPTION": "Transaction Currency Symbol", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "currency", "xtremPropertyOption": "currency.id"}, {"ID": "SUPDOCID", "LABEL": "Attachment", "DESCRIPTION": "Attachment", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SYSTEMGENERATED", "LABEL": "Scheduler generated", "DESCRIPTION": "True/False", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "TRX_SUBTOTAL", "LABEL": "Transaction subtotal", "DESCRIPTION": "Transaction Subtotal", "REQUIRED": false, "READONLY": true, "DATATYPE": "CURRENCY", "ISCUSTOM": false}, {"ID": "TRX_TOTAL", "LABEL": "Transaction total", "DESCRIPTION": "Transaction Total", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "DOCPAR_IN_OUT", "LABEL": "Increases/decreases inventory", "DESCRIPTION": "Increases/Decreases Inventory", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Increase", "Decrease"]}, "ISCUSTOM": false}, {"ID": "WHENPOSTED", "LABEL": "GL posting date", "DESCRIPTION": "GL Posting Date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "DATEPRINTED", "LABEL": "Last delivered date", "DESCRIPTION": "Last delivered date", "REQUIRED": false, "READONLY": false, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "TAXSOLUTIONID", "LABEL": "Tax solution", "DESCRIPTION": "Tax solution", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ORIGDOCDATE", "LABEL": "Original invoice date", "DESCRIPTION": "", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "INVOICERUN_EXPENSEPRICEMAR<PERSON>UP", "LABEL": "Expense price % markup", "DESCRIPTION": "Expense Price % Markup", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "INVOICERUN_DESCRIPTION", "LABEL": "Description", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TRX_TOTALDUE", "LABEL": "Amount due", "DESCRIPTION": "Amount Due", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "TRX_TOTALPAID", "LABEL": "Total paid", "DESCRIPTION": "Total Paid", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "PAYMENTSTATUS", "LABEL": "Payment status", "DESCRIPTION": "Payment Status", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SIGN_FLAG", "LABEL": "Sign flag", "DESCRIPTION": "Sign Flag", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "PROJECT", "LABEL": "Project", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PROJECTNAME", "LABEL": "Project name", "DESCRIPTION": "Project Name", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CNCONTRACTID", "LABEL": "Contract ID", "DESCRIPTION": "Contract ID", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TRANSACTIONSIGN", "LABEL": "Transaction Sign", "DESCRIPTION": "Transaction Sign", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "NEEDBYDATE", "LABEL": "Need by date", "DESCRIPTION": "Need by date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "SHIPBYDATE", "LABEL": "Ship by date", "DESCRIPTION": "Ship by date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "CANCELAFTERDATE", "LABEL": "Cancel after date", "DESCRIPTION": "Cancel after date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "DONOTSHIPBEFOREDATE", "LABEL": "Do not ship before date", "DESCRIPTION": "Do not ship before date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "DONOTSHIPAFTERDATE", "LABEL": "Do not ship after date", "DESCRIPTION": "Do not ship after date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "SERVICEDELIVERYDATE", "LABEL": "Service delivery date", "DESCRIPTION": "Service delivery date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "TRACKINGNUMBER", "LABEL": "Tracking number", "DESCRIPTION": "Tracking number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SHIPPEDDATE", "LABEL": "Shipped date", "DESCRIPTION": "Shipped date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "CUSTOMERPONUMBER", "LABEL": "Customer PO number", "DESCRIPTION": "Customer PO number", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RETAINAGEPERCENTAGE", "LABEL": "Default retainage percentage", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "PERCENT", "ISCUSTOM": false}, {"ID": "SCOPE", "LABEL": "<PERSON><PERSON>", "DESCRIPTION": "<PERSON><PERSON>", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "INCLUSIONS", "LABEL": "Inclusions", "DESCRIPTION": "Inclusions", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "EXCLUSIONS", "LABEL": "Exclusions", "DESCRIPTION": "Exclusions", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TERMS", "LABEL": "Terms", "DESCRIPTION": "Terms", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SCHEDULESTARTDATE", "LABEL": "Scheduled start date", "DESCRIPTION": "Scheduled start date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "ACTUALSTARTDATE", "LABEL": "Actual start date", "DESCRIPTION": "Actual start date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "SCHEDULEDCOMPLETIONDATE", "LABEL": "Scheduled completion date", "DESCRIPTION": "Scheduled completion date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "REVISEDCOMPLETIONDATE", "LABEL": "Revised completion date", "DESCRIPTION": "Revised completion date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "SUBSTANTIALCOMPLETIONDATE", "LABEL": "Substantial completion date", "DESCRIPTION": "Substantial completion date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "ACTUALCOMPLETIONDATE", "LABEL": "Actual completion date", "DESCRIPTION": "Actual completion date", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "NOTICETOPROCEED", "LABEL": "Notice to proceed", "DESCRIPTION": "Notice to proceed", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "RESPONSEDUE", "LABEL": "Response due", "DESCRIPTION": "Response due", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "EXECUTEDON", "LABEL": "Executed on", "DESCRIPTION": "Executed on", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "SCHEDULEIMPACT", "LABEL": "Schedule impact", "DESCRIPTION": "Schedule impact", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "INTERNALREFNO", "LABEL": "Internal reference no", "DESCRIPTION": "Internal reference no", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "INTERNALINITIATEDBY", "LABEL": "Internal initiated by", "DESCRIPTION": "Internal initiated by", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "INTERNALVERBALBY", "LABEL": "Internal verbal by", "DESCRIPTION": "Internal verbal by", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "INTERNALISSUEDBY", "LABEL": "Internal issued by", "DESCRIPTION": "Internal issued by", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "INTERNALISSUEDON", "LABEL": "Internal issued on", "DESCRIPTION": "Internal issued on", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "INTERNALAPPROVEDBY", "LABEL": "Internal approved by", "DESCRIPTION": "Internal approved by", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "INTERNALAPPROVEDON", "LABEL": "Internal approved on", "DESCRIPTION": "Internal approved on", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "INTERNALSIGNEDBY", "LABEL": "Internal signed by", "DESCRIPTION": "Internal signed by", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "INTERNALSIGNEDON", "LABEL": "Internal signed on", "DESCRIPTION": "Internal signed on", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "INTERNALSOURCE", "LABEL": "Internal source", "DESCRIPTION": "Internal source", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "INTERNALSOURCEREFNO", "LABEL": "Internal source reference no", "DESCRIPTION": "Internal source reference no", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "EXTERNALREFNO", "LABEL": "External reference no", "DESCRIPTION": "External refernce no", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "EXTERNALVERBALBY", "LABEL": "External verbal by", "DESCRIPTION": "External verbal by", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "EXTERNALAPPROVEDBY", "LABEL": "External approved by", "DESCRIPTION": "External approved by", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "EXTERNALAPPROVEDON", "LABEL": "External approved on", "DESCRIPTION": "External approved on", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "EXTERNALSIGNEDBY", "LABEL": "External signed by", "DESCRIPTION": "External signed by", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "EXTERNALSIGNEDON", "LABEL": "External signed on", "DESCRIPTION": "External signed on", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "PERFORMANCEBONDREQUIRED", "LABEL": "Performance bond required", "DESCRIPTION": "Performance bond required", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "PERFORMANCEBONDRECEIVED", "LABEL": "Performance bond received", "DESCRIPTION": "Performance bond received", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "PERFORMANCEBONDAMOUNT", "LABEL": "Performance bond amount", "DESCRIPTION": "Performance bond amount", "REQUIRED": false, "READONLY": false, "DATATYPE": "CURRENCY", "ISCUSTOM": false}, {"ID": "PERFORMANCESURETYCOMPANY", "LABEL": "Performance surety company", "DESCRIPTION": "Performance surety company", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PAYMENTBONDREQUIRED", "LABEL": "Payment bond required", "DESCRIPTION": "Payment bond required", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "PAYMENTBONDRECEIVED", "LABEL": "Payment bond received", "DESCRIPTION": "Payment bond received", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "PAYMENTBONDAMOUNT", "LABEL": "Payment bond amount", "DESCRIPTION": "Payment bond amount", "REQUIRED": false, "READONLY": false, "DATATYPE": "CURRENCY", "ISCUSTOM": false}, {"ID": "PAYMENTSURETYCOMPANY", "LABEL": "Payment surety company", "DESCRIPTION": "Payment surety company", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}], "relationships": [{"OBJECTPATH": "BILLTO", "OBJECTNAME": "CONTACT", "LABEL": "Bill-to contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "BILLTO.CONTACTNAME"}, {"OBJECTPATH": "SHIPTO", "OBJECTNAME": "CONTACT", "LABEL": "Ship-to contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "SHIPTO.CONTACTNAME"}, {"OBJECTPATH": "WAREHOUSE", "OBJECTNAME": "WAREHOUSE", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "WAREHOUSE.LOCATIONID"}, {"OBJECTPATH": "SHIPVIA", "OBJECTNAME": "SHIPMETHOD", "LABEL": "Ship via", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "SHIPVIA"}, {"OBJECTPATH": "USERINFO", "OBJECTNAME": "USERINFO", "LABEL": "Last updated User", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "USERID"}, {"OBJECTPATH": "USERINFO2", "OBJECTNAME": "USERINFO", "LABEL": "Created by user", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CREATEDUSERID"}, {"OBJECTPATH": "USERINFO3", "OBJECTNAME": "USERINFO", "LABEL": "Printed by user", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PRINTEDUSERID"}, {"OBJECTPATH": "CONTACT", "OBJECTNAME": "CONTACT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CONTACT.CONTACTNAME"}, {"OBJECTPATH": "CUSTOMER", "OBJECTNAME": "CUSTOMER", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CUSTVENDID"}, {"OBJECTPATH": "ARINVOICE", "OBJECTNAME": "ARINVOICE", "LABEL": "", "RELATIONSHIPTYPE": "ONE2ONE", "RELATEDBY": "PRRECORDKEY"}, {"OBJECTPATH": "TERM", "OBJECTNAME": "ARTERM", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "TERM.NAME"}, {"OBJECTPATH": "INVOICERUN", "OBJECTNAME": "INVOICERUN", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "INVOICERUNKEY"}, {"OBJECTPATH": "PROJECT", "OBJECTNAME": "PROJECT", "LABEL": "Project", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PROJECT"}, {"OBJECTPATH": "CONTRACT", "OBJECTNAME": "CONTRACT", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CNCONTRACTKEY"}, {"OBJECTPATH": "GENINVOICEPREVIEWHEADER", "OBJECTNAME": "GENINVOICEPREVIEWHEADER", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PREVIEWHEADERKEY"}, {"OBJECTPATH": "DOCUMENTPARAMS", "OBJECTNAME": "SODOCUMENTPARAMS", "LABEL": "Transaction Definition", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "DOCPARKEY"}, {"OBJECTPATH": "MELOCATION", "OBJECTNAME": "LOCATION", "LABEL": "Created At Entity Information", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MEGAENTITYID"}], "relationshipFields": [{"DATATYPE": "TERM", "ID": "TERM.NAME", "DESCRIPTION": "NAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "NAME", "xtremProperty": ""}, {"DATATYPE": "WAREHOUSE", "ID": "WAREHOUSE.LOCATIONID", "DESCRIPTION": "LOCATIONID", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LOCATIONID", "xtremProperty": ""}, {"DATATYPE": "SHIPVIA", "ID": "SHIPVIA", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "SHIPVIA", "xtremProperty": ""}, {"DATATYPE": "USERID", "ID": "USERID", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "USERID", "xtremProperty": ""}, {"DATATYPE": "CREATEDUSERID", "ID": "CREATEDUSERID", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CREATEDUSERID", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.PREFIX", "DESCRIPTION": "PREFIX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREFIX", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.FIRSTNAME", "DESCRIPTION": "FIRSTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FIRSTNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.INITIAL", "DESCRIPTION": "INITIAL", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "INITIAL", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.LASTNAME", "DESCRIPTION": "LASTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LASTNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.COMPANYNAME", "DESCRIPTION": "COMPANYNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.PRINTAS", "DESCRIPTION": "PRINTAS", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTAS", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.CELLPHONE", "DESCRIPTION": "CELLPHONE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CELLPHONE", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.PAGER", "DESCRIPTION": "PAGER", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PAGER", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.FAX", "DESCRIPTION": "FAX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FAX", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.EMAIL1", "DESCRIPTION": "EMAIL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL1", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.EMAIL2", "DESCRIPTION": "EMAIL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL2", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.URL1", "DESCRIPTION": "URL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL1", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.URL2", "DESCRIPTION": "URL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL2", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.VISIBLE", "DESCRIPTION": "VISIBLE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VISIBLE", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "CONTACT", "ID": "CONTACT.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.PREFIX", "DESCRIPTION": "PREFIX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREFIX", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.FIRSTNAME", "DESCRIPTION": "FIRSTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FIRSTNAME", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.INITIAL", "DESCRIPTION": "INITIAL", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "INITIAL", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.LASTNAME", "DESCRIPTION": "LASTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LASTNAME", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.COMPANYNAME", "DESCRIPTION": "COMPANYNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.PRINTAS", "DESCRIPTION": "PRINTAS", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTAS", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.CELLPHONE", "DESCRIPTION": "CELLPHONE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CELLPHONE", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.PAGER", "DESCRIPTION": "PAGER", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PAGER", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.FAX", "DESCRIPTION": "FAX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FAX", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.EMAIL1", "DESCRIPTION": "EMAIL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL1", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.EMAIL2", "DESCRIPTION": "EMAIL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL2", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.URL1", "DESCRIPTION": "URL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL1", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.URL2", "DESCRIPTION": "URL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL2", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.VISIBLE", "DESCRIPTION": "VISIBLE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VISIBLE", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.PREFIX", "DESCRIPTION": "PREFIX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREFIX", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.FIRSTNAME", "DESCRIPTION": "FIRSTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FIRSTNAME", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.INITIAL", "DESCRIPTION": "INITIAL", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "INITIAL", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.LASTNAME", "DESCRIPTION": "LASTNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "LASTNAME", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.COMPANYNAME", "DESCRIPTION": "COMPANYNAME", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COMPANYNAME", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.PRINTAS", "DESCRIPTION": "PRINTAS", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTAS", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.PHONE1", "DESCRIPTION": "PHONE1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE1", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.PHONE2", "DESCRIPTION": "PHONE2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PHONE2", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.CELLPHONE", "DESCRIPTION": "CELLPHONE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CELLPHONE", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.PAGER", "DESCRIPTION": "PAGER", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PAGER", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.FAX", "DESCRIPTION": "FAX", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "FAX", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.EMAIL1", "DESCRIPTION": "EMAIL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL1", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.EMAIL2", "DESCRIPTION": "EMAIL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "EMAIL2", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.URL1", "DESCRIPTION": "URL1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL1", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.URL2", "DESCRIPTION": "URL2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "URL2", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.VISIBLE", "DESCRIPTION": "VISIBLE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "VISIBLE", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.MAILADDRESS.ADDRESS1", "DESCRIPTION": "MAILADDRESS.ADDRESS1", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS1", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.MAILADDRESS.ADDRESS2", "DESCRIPTION": "MAILADDRESS.ADDRESS2", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ADDRESS2", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.MAILADDRESS.CITY", "DESCRIPTION": "MAILADDRESS.CITY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CITY", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.MAILADDRESS.STATE", "DESCRIPTION": "MAILADDRESS.STATE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "STATE", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.MAILADDRESS.ZIP", "DESCRIPTION": "MAILADDRESS.ZIP", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "ZIP", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.MAILADDRESS.COUNTRY", "DESCRIPTION": "MAILADDRESS.COUNTRY", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRY", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.MAILADDRESS.COUNTRYCODE", "DESCRIPTION": "MAILADDRESS.COUNTRYCODE", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "COUNTRYCODE", "xtremProperty": ""}, {"DATATYPE": "PRRECORDKEY", "ID": "PRRECORDKEY", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRRECORDKEY", "xtremProperty": ""}, {"DATATYPE": "INVOICERUNKEY", "ID": "INVOICERUNKEY", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "INVOICERUNKEY", "xtremProperty": ""}, {"DATATYPE": "PRINTEDUSERID", "ID": "PRINTEDUSERID", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PRINTEDUSERID", "xtremProperty": ""}, {"DATATYPE": "CUSTVENDID", "ID": "CUSTVENDID", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CUSTVENDID", "xtremProperty": ""}, {"DATATYPE": "PREVIEWHEADERKEY", "ID": "PREVIEWHEADERKEY", "DESCRIPTION": "", "READONLY": true, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREVIEWHEADERKEY", "xtremProperty": ""}]}