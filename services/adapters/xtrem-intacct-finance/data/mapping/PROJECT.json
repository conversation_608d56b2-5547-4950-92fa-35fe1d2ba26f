{"name": "PROJECT", "documentType": "Project", "xtremObject": "attribute", "additionnalLink": [{"xtremProperty": "attributeType.id", "type": "string", "xtremValues": ["project"]}], "fields": [{"ID": "RECORDNO", "LABEL": "Record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false, "xtremProperty": "recordNo,projectRecordNo"}, {"ID": "PROJECTID", "LABEL": "Project ID", "DESCRIPTION": "No description specified", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "id"}, {"ID": "NAME", "LABEL": "Project name", "DESCRIPTION": "No description specified", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "name"}, {"ID": "DESCRIPTION", "LABEL": "Description", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CURRENCY", "LABEL": "Project currency", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PROJECTCATEGORY", "LABEL": "Project category", "DESCRIPTION": "No description specified", "REQUIRED": true, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Contract", "Capitalized", "Internal Non-billable", "Internal Billable"]}, "ISCUSTOM": false}, {"ID": "PROJECTSTATUS", "LABEL": "Project status", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "STATUS", "LABEL": "Status", "DESCRIPTION": "Active/Inactive", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["active", "inactive"]}, "ISCUSTOM": false, "xtremProperty": "statusIntacct"}, {"ID": "BEGINDATE", "LABEL": "Begin date", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "ENDDATE", "LABEL": "End date", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DATE", "ISCUSTOM": false}, {"ID": "BUDGETAMOUNT", "LABEL": "Budgeted billing amount", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "CONTRACTAMOUNT", "LABEL": "Contract amount", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "ACTUALAMOUNT", "LABEL": "Actual amount", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "BUDGETQTY", "LABEL": "Budgeted duration (hours)", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "ESTQTY", "LABEL": "Estimated duration", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "ACTUALQTY", "LABEL": "Actual duration", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "APPROVEDQTY", "LABEL": "Approved duration", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "REMAININGQTY", "LABEL": "Remaining duration", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "PERCENTCOMPLETE", "LABEL": "Calculated % completed", "DESCRIPTION": "Calculated % Completed", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "OBSPERCENTCOMPLETE", "LABEL": "Observed % completed", "DESCRIPTION": "Observed % Completed", "REQUIRED": false, "READONLY": true, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "BILLINGTYPE", "LABEL": "Billing type", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["", "Time & Material", "Fixed Fee", "Fixed Fee & Expenses"]}, "ISCUSTOM": false}, {"ID": "SONUMBER", "LABEL": "Sales order number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PONUMBER", "LABEL": "Purchase order number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "POAMOUNT", "LABEL": "Purchase order amount", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "PQNUMBER", "LABEL": "Purchase quote number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SFDCKEY", "LABEL": "Salesforce key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "QARROWKEY", "LABEL": "Quick arrow key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "OAKEY", "LABEL": "Open air key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PARENTKEY", "LABEL": "Parent project key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "PARENTID", "LABEL": "Parent project ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PARENTNAME", "LABEL": "Parent project name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "INVOICEWITHPARENT", "LABEL": "Invoice with parent", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "CUSTOMERKEY", "LABEL": "Customer key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "CUSTOMERID", "LABEL": "Customer ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "CUSTOMERNAME", "LABEL": "Customer name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "SALESCONTACTKEY", "LABEL": "Sales contact key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "SALESCONTACTID", "LABEL": "Sales contact", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PROJECTTYPEKEY", "LABEL": "Project type key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "PROJECTTYPE", "LABEL": "Project type", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MANAGERKEY", "LABEL": "Project manager key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MANAGERID", "LABEL": "Project manager ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PROJECTDEPTKEY", "LABEL": "Department key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "DEPARTMENTID", "LABEL": "Department ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DEPARTMENTNAME", "LABEL": "Department name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "PROJECTLOCATIONKEY", "LABEL": "Location key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "LOCATIONID", "LABEL": "Location ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false, "xtremProperty": "site.id"}, {"ID": "LOCATIONNAME", "LABEL": "Location name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "TERMNAME", "LABEL": "Term", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "DOCNUMBER", "LABEL": "Reference number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "WHENCREATED", "LABEL": "When created", "DESCRIPTION": "timestamp marking last time this was created.", "REQUIRED": false, "READONLY": true, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TIMESTAMP", "ISCUSTOM": false}, {"ID": "CREATEDBY", "LABEL": "Created by", "DESCRIPTION": "User who created this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DESCRIPTION": "User who modified this.", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "BUDGETEDCOST", "LABEL": "Budgeted cost", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "USERRESTRICTIONS", "LABEL": "Timesheet and expense user restrictions", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["System Default", "Any User", "Project Users", "Project Task Users"]}, "ISCUSTOM": false}, {"ID": "BILLABLEEXPDEFAULT", "LABEL": "Billable employee expenses", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "BILLABLEAPPODEFAULT", "LABEL": "Billable AP / PO", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "BUDGETID", "LABEL": "GL budget ID", "DESCRIPTION": "GL Budget ID", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "BILLINGRATE", "LABEL": "Default labor rate/fee %", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "BILLINGPRICING", "LABEL": "Labor pricing option", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Billing rate", "Cost plus fee"]}, "ISCUSTOM": false}, {"ID": "EXPENSERATE", "LABEL": "Default expense rate/fee %", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "EXPENSEPRICING", "LABEL": "Expense pricing option", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Billing rate", "Cost plus fee"]}, "ISCUSTOM": false}, {"ID": "POAPRATE", "LABEL": "Default AP/PO rate/fee %", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "DECIMAL", "ISCUSTOM": false}, {"ID": "POAPPRICING", "LABEL": "AP/PO pricing option", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Cost plus fee"]}, "ISCUSTOM": false}, {"ID": "CONTACTKEY", "LABEL": "Contact record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "SHIPTOKEY", "LABEL": "Ship-to contact record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "BILLTOKEY", "LABEL": "Bill-to contact record number", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "INVOICEMESSAGE", "LABEL": "Invoice message", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "INVOICECURRENCY", "LABEL": "Invoice currency override", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "BILLINGOVERMAX", "LABEL": "If actual billings exceed budgeted billing amount", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "TEXT", "VALIDVALUES": {"VALIDVALUE": ["Do nothing", "Issue a warning message", "Prevent billing"]}, "ISCUSTOM": false}, {"ID": "EXCLUDEEXPENSES", "LABEL": "Exclude expenses", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": false, "DATATYPE": "BOOLEAN", "VALIDVALUES": {"VALIDVALUE": ["true", "false"]}, "ISCUSTOM": false}, {"ID": "CONTRACTID", "LABEL": "Contract", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ROOTPARENTKEY", "LABEL": "Root project key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "ROOTPARENTID", "LABEL": "Root project ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "ROOTPARENTNAME", "LABEL": "Root project name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "INTEGER", "ISCUSTOM": false}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DESCRIPTION": "No description specified", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "REQUIRED": false, "READONLY": true, "DATATYPE": "TEXT", "ISCUSTOM": false}], "relationships": [{"OBJECTPATH": "PARENT", "OBJECTNAME": "PROJECT", "LABEL": "Parent Project", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "PARENTID"}, {"OBJECTPATH": "SHIPTOCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Ship to Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "SHIPTO.CONTACTNAME"}, {"OBJECTPATH": "BILLTOCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Bill to Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "BILLTO.CONTACTNAME"}, {"OBJECTPATH": "PRIMARYCONTACT", "OBJECTNAME": "CONTACT", "LABEL": "Primary Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CONTACTINFO.CONTACTNAME"}, {"OBJECTPATH": "SALESCONTACT", "OBJECTNAME": "EMPLOYEE", "LABEL": "Sales Contact", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "SALESCONTACTID"}, {"OBJECTPATH": "TERM", "OBJECTNAME": "ARTERM", "LABEL": "", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "TERMNAME"}, {"OBJECTPATH": "MANAGER", "OBJECTNAME": "EMPLOYEE", "LABEL": "Manager", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MANAGERID"}, {"OBJECTPATH": "CUSTOMER", "OBJECTNAME": "CUSTOMER", "LABEL": "Customer", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CUSTOMERID"}, {"OBJECTPATH": "USERINFOMST", "OBJECTNAME": "USERINFO", "LABEL": "External user (customer)", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "CUSTUSERID"}, {"OBJECTPATH": "ROOTPARENT", "OBJECTNAME": "PROJECT", "LABEL": "Root Project", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "ROOTPARENTID"}, {"OBJECTPATH": "MELOCATION", "OBJECTNAME": "LOCATION", "LABEL": "Created At Entity Information", "RELATIONSHIPTYPE": "MANY2ONE", "RELATEDBY": "MEGAENTITYID"}], "relationshipFields": [{"DATATYPE": "PROJECTSTATUSKEY", "ID": "PROJECTSTATUSKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PROJECTSTATUSKEY", "xtremProperty": ""}, {"DATATYPE": "PREVENTTIMESHEET", "ID": "PREVENTTIMESHEET", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREVENTTIMESHEET", "xtremProperty": ""}, {"DATATYPE": "PREVENTEXPENSE", "ID": "PREVENTEXPENSE", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREVENTEXPENSE", "xtremProperty": ""}, {"DATATYPE": "PREVENTAPPO", "ID": "PREVENTAPPO", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREVENTAPPO", "xtremProperty": ""}, {"DATATYPE": "PREVENTGENINVOICE", "ID": "PREVENTGENINVOICE", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "PREVENTGENINVOICE", "xtremProperty": ""}, {"DATATYPE": "SALESCONTACTNAME", "ID": "SALESCONTACTNAME", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "SALESCONTACTNAME", "xtremProperty": ""}, {"DATATYPE": "MANAGERCONTACTNAME", "ID": "MANAGERCONTACTNAME", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "MANAGERCONTACTNAME", "xtremProperty": ""}, {"DATATYPE": "CONTACTINFO", "ID": "CONTACTINFO.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": ""}, {"DATATYPE": "SHIPTO", "ID": "SHIPTO.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": ""}, {"DATATYPE": "BILLTO", "ID": "BILLTO.CONTACTNAME", "DESCRIPTION": "CONTACTNAME", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTACTNAME", "xtremProperty": ""}, {"DATATYPE": "TERMSKEY", "ID": "TERMSKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "TERMSKEY", "xtremProperty": ""}, {"DATATYPE": "CUSTUSERKEY", "ID": "CUSTUSERKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CUSTUSERKEY", "xtremProperty": ""}, {"DATATYPE": "CUSTUSERID", "ID": "CUSTUSERID", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CUSTUSERID", "xtremProperty": ""}, {"DATATYPE": "CLASSID", "ID": "CLASSID", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CLASSID", "xtremProperty": ""}, {"DATATYPE": "CLASSNAME", "ID": "CLASSNAME", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CLASSNAME", "xtremProperty": ""}, {"DATATYPE": "CLASSKEY", "ID": "CLASSKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CLASSKEY", "xtremProperty": ""}, {"DATATYPE": "BUDGETKEY", "ID": "BUDGETKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "BUDGETKEY", "xtremProperty": ""}, {"DATATYPE": "CONTRACTKEY", "ID": "CONTRACTKEY", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "CONTRACTKEY", "xtremProperty": ""}, {"DATATYPE": "SUPDOCID", "ID": "SUPDOCID", "DESCRIPTION": "", "READONLY": true, "CREATEONLY": false, "ISCUSTOM": false, "REQUIRED": false, "LABEL": "SUPDOCID", "xtremProperty": ""}]}