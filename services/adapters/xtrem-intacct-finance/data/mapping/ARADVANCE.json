{"name": "ARADVANCE", "fields": [{"ID": "RECORDNO", "LABEL": "Record number", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Record number", "VALIDVALUES": null, "xtremProperty": "recordNo,intacctId"}, {"ID": "PRBATCHKEY", "LABEL": "Summary key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Summary key", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PRBATCH", "LABEL": "Payment summary", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Payment summary", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "FINANCIALENTITY", "LABEL": "Financial entity", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Financial entity", "VALIDVALUES": null, "xtremProperty": "bankAccount.id"}, {"ID": "UNDEPOSITEDACCOUNTNO", "LABEL": "Undeposited funds account no", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Undeposited funds account", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PAYMENTMETHOD", "LABEL": "Payment method", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "Payment method", "VALIDVALUES": {"VALIDVALUE": ["Printed Check", "Credit Card", "EFT", "Cash"]}, "xtremProperty": "intacctDocument.paymentMethod"}, {"ID": "PAYMENTMETHODKEY", "LABEL": "Payment method key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Payment method key", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CUSTOMERID", "LABEL": "Customer ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "Customer ID", "VALIDVALUES": null, "xtremProperty": "payToCustomerId"}, {"ID": "CUSTOMERNAME", "LABEL": "Customer name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Customer name", "VALIDVALUES": null, "xtremProperty": "payToCustomerName"}, {"ID": "DOCNUMBER", "LABEL": "Document/Check no", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Document/Check No", "VALIDVALUES": null, "xtremProperty": "number"}, {"ID": "DESCRIPTION", "LABEL": "Payment memo", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Payment memo", "VALIDVALUES": null, "xtremProperty": "description"}, {"ID": "BASECURR", "LABEL": "Base currency", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "Base currency", "VALIDVALUES": null, "xtremProperty": "financialSite.legalCompany.currency.id"}, {"ID": "CURRENCY", "LABEL": "<PERSON><PERSON><PERSON><PERSON>", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": true, "CREATEONLY": null, "DESCRIPTION": "Transaction currency", "VALIDVALUES": null, "xtremProperty": "currency.id"}, {"ID": "RECEIPTDATE", "LABEL": "Receipt date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Receipt Date", "VALIDVALUES": null, "xtremProperty": "postingDate"}, {"ID": "PAYMENTDATE", "LABEL": "Payment date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Payment date", "VALIDVALUES": null, "xtremProperty": "paymentDate"}, {"ID": "EXCH_RATE_DATE", "LABEL": "Exchange rate date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Exchange rate date", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "EXCH_RATE_TYPE_ID", "LABEL": "Exchange rate type", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Exchange rate type", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "EXCHANGE_RATE", "LABEL": "Exchange rate", "DATATYPE": "DECIMAL", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Exchange rate", "VALIDVALUES": null, "xtremProperty": "companyFxRate"}, {"ID": "RECORDTYPE", "LABEL": "Record type", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Record type", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "STATE", "LABEL": "State", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "State", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALENTERED", "LABEL": "Payment amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Total payment amount", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALENTERED", "LABEL": "Txn payment amount", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Transaction payment amount", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALPAID", "LABEL": "Total applied", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Total applied amount", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALPAID", "LABEL": "Total transaction amount paid", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Total transaction amount paid", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TOTALDUE", "LABEL": "Total due", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Total due amount", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "TRX_TOTALDUE", "LABEL": "Total transaction amount due", "DATATYPE": "CURRENCY", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Total transaction amount due", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "AUWHENCREATED", "LABEL": "Audit when created", "DATATYPE": "TIMESTAMP", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Audit timestamp when record was created.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENMODIFIED", "LABEL": "When modified", "DATATYPE": "TIMESTAMP", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "timestamp marking last time this was changed.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CREATEDBY", "LABEL": "Created by", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "User who created this.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MODIFIEDBY", "LABEL": "Modified by", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "User who modified this.", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RAWSTATE", "LABEL": "Raw state", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Raw state", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENCREATED", "LABEL": "Created date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Created date", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "WHENPAID", "LABEL": "When paid", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Paid date", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "CLEARED", "LABEL": "Reconciliation status", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Reconciliation status", "VALIDVALUES": {"VALIDVALUE": ["T", "F", ""]}, "xtremProperty": ""}, {"ID": "CLRDATE", "LABEL": "Reconciliation date", "DATATYPE": "DATE", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Reconciliation Date", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "SYSTEMGENERATED", "LABEL": "System generated", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "System generated", "VALIDVALUES": {"VALIDVALUE": ["T", "F"]}, "xtremProperty": ""}, {"ID": "SUPDOCID", "LABEL": "Attachment", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "Attachment", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "PARENTPAYMENT", "LABEL": "Parent payment key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": false, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MEGAENTITYKEY", "LABEL": "Created at - Entity key", "DATATYPE": "INTEGER", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MEGAENTITYID", "LABEL": "Created at - Entity ID", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "MEGAENTITYNAME", "LABEL": "Created at - Entity name", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": "No description specified", "VALIDVALUES": null, "xtremProperty": ""}, {"ID": "RECORD_URL", "LABEL": "Record URL", "DATATYPE": "TEXT", "ISCUSTOM": false, "READONLY": true, "REQUIRED": false, "CREATEONLY": null, "DESCRIPTION": " A portable, user-independent, deep-link URL for viewing this record", "VALIDVALUES": null, "xtremProperty": ""}], "xtremObject": "accounts_receivable_advance", "documentType": "", "relationships": [{"LABEL": "", "RELATEDBY": "CUSTOMERID", "OBJECTNAME": "CUSTOMER", "OBJECTPATH": "CUSTOMER", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "", "RELATEDBY": "RECORDNO", "OBJECTNAME": "EXCHANGERATEINFO", "OBJECTPATH": "EXCHANGERATEINFO", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}, {"LABEL": "Created At Entity Information", "RELATEDBY": "MEGAENTITYID", "OBJECTNAME": "LOCATION", "OBJECTPATH": "MELOCATION", "xtremProperty": "", "RELATIONSHIPTYPE": "MANY2ONE"}], "relationshipFields": []}