{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "application.ts", "lib/**/*", "test/**/*.ts", "test/**/*.json", "api/api.d.ts"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/page-extensions/**/*", "lib/page-fragments/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png", "lib/client-functions/**/*"], "references": [{"path": "../../../platform/system/xtrem-authorization"}, {"path": "../../../platform/front-end/xtrem-client"}, {"path": "../../../platform/system/xtrem-communication"}, {"path": "../../../platform/back-end/xtrem-core"}, {"path": "../../../platform/shared/xtrem-date-time"}, {"path": "../../../platform/shared/xtrem-decimal"}, {"path": "../../applications/xtrem-finance"}, {"path": "../../shared/xtrem-finance-data"}, {"path": "../../../platform/system/xtrem-import-export"}, {"path": "../xtrem-intacct"}, {"path": "../../../platform/system/xtrem-mailer"}, {"path": "../../shared/xtrem-master-data"}, {"path": "../../../platform/system/xtrem-metadata"}, {"path": "../../../platform/system/xtrem-reporting"}, {"path": "../../../platform/shared/xtrem-shared"}, {"path": "../../shared/xtrem-structure"}, {"path": "../../../platform/system/xtrem-synchronization"}, {"path": "../../../platform/system/xtrem-system"}, {"path": "../../shared/xtrem-tax"}, {"path": "../../../platform/front-end/xtrem-ui"}, {"path": "../../../platform/back-end/eslint-plugin-xtrem"}, {"path": "../../../platform/cli/xtrem-cli"}, {"path": "../../../platform/system/xtrem-communication/api"}, {"path": "../../applications/xtrem-finance/api"}, {"path": "../../shared/xtrem-finance-data/api"}, {"path": "../../../platform/system/xtrem-mailer/api"}, {"path": "../../shared/xtrem-master-data/api"}, {"path": "../../../platform/system/xtrem-routing"}, {"path": "../../shared/xtrem-structure/api"}, {"path": "../../../platform/system/xtrem-system/api"}, {"path": "../../shared/xtrem-tax/api"}]}