{"@sage/xtrem-ap-automation": [{"topic": "ApAutomationCompany/asyncExport/start", "queue": "import-export", "sourceFileName": "ap-automation-company.ts"}, {"topic": "ApAutomationConfiguration/asyncExport/start", "queue": "import-export", "sourceFileName": "ap-automation-configuration.ts"}, {"topic": "SysServiceOptionState/apAutomationOption/activate", "queue": "purchasing", "sourceFileName": "uploaded-purchasing-document.ts"}, {"topic": "SysServiceOptionState/apAutomationOption/deactivate", "queue": "purchasing", "sourceFileName": "uploaded-purchasing-document.ts"}, {"topic": "UploadedFile/processUpload", "queue": "purchasing", "sourceFileName": "uploaded-purchasing-document.ts"}, {"topic": "UploadedPurchasingDocument/asyncExport/start", "queue": "import-export", "sourceFileName": "uploaded-purchasing-document.ts"}, {"topic": "UploadedPurchasingDocument/confirmBulk/start", "queue": "purchasing", "sourceFileName": "uploaded-purchasing-document.ts"}, {"topic": "UploadedPurchasingDocument/syncDocumentsTask/start", "queue": "purchasing", "sourceFileName": "uploaded-purchasing-document.ts"}, {"topic": "UploadedPurchasingDocument/uploadDocument/start", "queue": "purchasing", "sourceFileName": "uploaded-purchasing-document.ts"}, {"topic": "UploadedPurchasingDocumentLine/asyncExport/start", "queue": "import-export", "sourceFileName": "uploaded-purchasing-document-line.ts"}]}