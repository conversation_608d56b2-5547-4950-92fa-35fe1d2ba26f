import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { Company as CompanyNode } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
import type { ApAutomationConfiguration as ApAutomationConfigurationPage } from '../pages/ap-automation-configuration';

function resetConfiguration(page: ApAutomationConfigurationPage): Promise<boolean> {
    return page.$.graph
        .node('@sage/xtrem-ap-automation/ApAutomationConfiguration')
        .mutations.resetConfiguration(true, {})
        .execute();
}

function confirmRegistration(page: ApAutomationConfigurationPage): Promise<boolean> {
    return MasterDataUtils.confirmDialogToBoolean(
        page.$.dialog.confirmation(
            'info',
            ui.localize(
                `@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__dialog_title`,
                'Sage AI connection',
            ),
            ui.localize(
                `@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__dialog_message`,
                `New tenant detected. Register the connection again or cancel?`,
            ),
            {
                acceptButton: {
                    text: ui.localize(
                        `@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__register_button`,
                        'Register',
                    ),
                },
                cancelButton: {
                    text: ui.localize(
                        `@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__cancel_button`,
                        'Cancel',
                    ),
                },
            },
        ),
    );
}

function getCompaniesWithDifferentTenantId(
    page: ApAutomationConfigurationPage,
    tenantId: string,
): PartialCollectionValueWithIds<CompanyNode>[] {
    return page.companies.value.filter(
        company =>
            company.isInboxActive &&
            company.customerUniqueId !== '' &&
            company.originalTenantId !== '' &&
            tenantId !== company.originalTenantId,
    );
}

function getTenantInformationByContext(page: ApAutomationConfigurationPage): Promise<{ tenantId: string }> {
    return page.$.graph
        .node('@sage/xtrem-system/SysTenant')
        .queries.getTenantInformation({ tenantId: true }, {})
        .execute();
}

export async function tenantVerification(page: ApAutomationConfigurationPage): Promise<void> {
    const { tenantId } = await getTenantInformationByContext(page);
    const companiesWithDifferentTenantId = getCompaniesWithDifferentTenantId(page, tenantId);
    if (
        (page.isActive.value && page.originalTenantId.value !== '' && page.originalTenantId.value !== tenantId) ||
        companiesWithDifferentTenantId.length > 0
    ) {
        page.$.loader.isHidden = true;
        if (await confirmRegistration(page)) {
            page.$.loader.isHidden = false;
            await page.$standardSaveAction.execute(true);
        } else {
            page.$.loader.isHidden = false;
            await resetConfiguration(page);
        }
        page.$.loader.isHidden = true;
        await page.$.router.refresh();
    }
}
