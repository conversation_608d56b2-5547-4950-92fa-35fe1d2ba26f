import type { ConfirmUploadedPurchasingDocument as ConfirmUploadedPurchasingDocumentPage } from '../pages/confirm-uploaded-purchasing-document';
import type { CreatePurchaseDocumentResult } from './interfaces/confirm-uploaded-purchasing-document';

export function createPurchaseDocument(
    page: ConfirmUploadedPurchasingDocumentPage,
): Promise<CreatePurchaseDocumentResult> {
    return page.$.graph
        .node('@sage/xtrem-ap-automation/UploadedPurchasingDocument')
        .mutations.createPurchaseDocumentFromUploadedPurchasingDocument(
            { _id: true, number: true },
            { uploadedPurchasingDocument: page.$.queryParameters._id.toString() ?? '' },
        )
        .execute();
}
