import type { ApAutomationDocumentType } from '@sage/xtrem-ap-automation-api';
import type { PurchaseCreditMemo } from '@sage/xtrem-purchasing/build/lib/pages/purchase-credit-memo';
import type { PurchaseInvoice } from '@sage/xtrem-purchasing/build/lib/pages/purchase-invoice';
import * as ui from '@sage/xtrem-ui';
import type { ExtensionMembers } from '@sage/xtrem-ui/build/lib/service/page-extension';
import type { PurchaseCreditMemoExtension } from '../page-extensions/purchase-credit-memo-extension';
import type { PurchaseInvoiceExtension } from '../page-extensions/purchase-invoice-extension';
import type { ConfirmUploadedPurchasingDocument as ConfirmUploadedPurchasingDocumentPage } from '../pages/confirm-uploaded-purchasing-document';
import type { UploadedPurchasingDocument as UploadedPurchasingDocumentPage } from '../pages/uploaded-purchasing-document';

type PurchaseInvoicePage = ExtensionMembers<PurchaseInvoice & PurchaseInvoiceExtension>;
type PurchaseInvoicePageExtension = PurchaseInvoicePage | PurchaseInvoiceExtension;
type PurchaseCreditMemoPage = ExtensionMembers<PurchaseCreditMemo & PurchaseCreditMemoExtension>;
type PurchaseCreditMemoPageExtension = PurchaseCreditMemoPage | PurchaseCreditMemoExtension;

function openPage(parameters: {
    page: UploadedPurchasingDocumentPage | ConfirmUploadedPurchasingDocumentPage;
    purchaseDocumentSysId: number;
    path: string;
}): void {
    return parameters.page.$.router.goTo(parameters.path, { _id: parameters.purchaseDocumentSysId });
}

export function openDocumentLinkPage(parameters: {
    page: UploadedPurchasingDocumentPage | ConfirmUploadedPurchasingDocumentPage;
    purchaseDocumentSysId: number;
    documentType: ApAutomationDocumentType | null;
}): void {
    switch (parameters.documentType) {
        case 'invoice':
            return openPage({ ...parameters, path: `@sage/xtrem-purchasing/PurchaseInvoice` });
        case 'creditMemo':
            return openPage({ ...parameters, path: `@sage/xtrem-purchasing/PurchaseCreditMemo` });
        default:
            return undefined;
    }
}

export function mapDocumentNumber(parameters: {
    page: UploadedPurchasingDocumentPage;
    documentType: ApAutomationDocumentType | null;
}) {
    switch (parameters.documentType) {
        case 'invoice':
            return parameters.page.purchaseInvoice.value?.at(0)?.number ?? '';
        case 'creditMemo':
            return parameters.page.purchaseCreditMemo.value?.at(0)?.number ?? '';
        default:
            return '';
    }
}

export function mapSupplierDocumentNumber(
    page: PurchaseInvoicePageExtension | PurchaseCreditMemoPageExtension,
): string {
    return page.uploadedDocument?.value?.supplierDocumentNumber ?? '';
}

export function isRecordReadOnly(page: UploadedPurchasingDocumentPage): boolean {
    return page.isReviewDone.value || !page.isResultPopulated.value;
}

export async function updateDocumentIfResultNotPopulated(page: UploadedPurchasingDocumentPage): Promise<void> {
    if (!page.isResultPopulated.value && page.processingStatus.value === 'completed') {
        await page.$.graph
            .node('@sage/xtrem-ap-automation/UploadedPurchasingDocument')
            .mutations.updateDocumentWithDetails(true, { document: page.$.recordId ?? '' })
            .execute();
        await page.$.router.refresh();
        await page.$.refreshNavigationPanel();
    }
}

function isConfigurationActive(page: UploadedPurchasingDocumentPage): Promise<boolean> {
    return page.$.graph
        .node('@sage/xtrem-ap-automation/ApAutomationConfiguration')
        .queries.isConfigurationActive(true, {})
        .execute();
}

async function configurationIsNotActiveInfo(
    isConfigurationExists: boolean,
    page: UploadedPurchasingDocumentPage,
): Promise<void> {
    if (!isConfigurationExists) {
        page.$.loader.isHidden = true;
        await page.$.dialog.message(
            'info',
            ui.localize(
                '@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__no_configuration_active_info_title',
                'Ap automation configuration',
            ),
            ui.localize(
                '@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__no_configuration_active_info',
                'You need to register tenants or companies from the AP Automation configuration page first.',
            ),
            { resolveOnCancel: true },
        );
    }
}

async function enableDisableUploadAndSyncButtons(page: UploadedPurchasingDocumentPage): Promise<void> {
    const isConfigurationExists = await isConfigurationActive(page);
    if (!page.$.recordId) {
        await configurationIsNotActiveInfo(isConfigurationExists, page);
    }
    if (!isConfigurationExists) {
        page.uploadDocument.isDisabled = true;
        page.syncDocuments.isDisabled = true;
    } else {
        page.uploadDocument.isDisabled = false;
        page.syncDocuments.isDisabled = false;
    }
}

function hideShowDeleteButton(page: UploadedPurchasingDocumentPage) {
    page.$standardDeleteAction.isHidden =
        page.processingStatus.value === 'processing' ||
        (page.processingStatus.value === 'completed' && (page.isReviewDone.value ?? false));
}

export async function initPage(page: UploadedPurchasingDocumentPage): Promise<void> {
    const isDisabled = isRecordReadOnly(page);
    page.documentNumberLink.value = mapDocumentNumber({ page, documentType: page.type.value });
    page.matchingBlock.isDisabled = isDisabled;
    page.type.isDisabled = isDisabled; // Dropdown is not being disabled with the block

    await enableDisableUploadAndSyncButtons(page);
    hideShowDeleteButton(page);

    if (page.processingStatus.value === 'completed') {
        page.displaySupplierDocumentNumber.value = page.supplierDocumentNumber.value;
        page.displaySupplier.value = page.supplier.value?.businessEntity?.name ?? '';
        page.displaySite.value = page.site.value?.name ?? '';
        page.displayType.value = page.type.value;
    }
}

function isSupplierDocumentNumberUsed(page: UploadedPurchasingDocumentPage): Promise<boolean> {
    return page.$.graph
        .node('@sage/xtrem-ap-automation/UploadedPurchasingDocument')
        .queries.isSupplierDocumentNumberUsed(true, {
            supplier: {
                id: page.supplier.value?._id,
                supplierDocumentNumber: page.supplierDocumentNumber.value ?? '',
                nodeToQuery: page.type.value === 'creditMemo' ? 'PurchaseCreditMemo' : 'PurchaseInvoice',
            },
        })
        .execute();
}

export async function supplierDocumentNumberWarning(page: UploadedPurchasingDocumentPage): Promise<void> {
    if (page.supplier.value && (await isSupplierDocumentNumberUsed(page))) {
        await page.$.dialog.message(
            'warn',
            ui.localize(
                '@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__is_supplier_document_number_used_warn_title',
                'Supplier document number',
            ),
            ui.localize(
                '@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__is_supplier_document_number_used_warn',
                'Supplier document number already exists.',
            ),
        );
    }
}

export function isSiteLinked(siteId: string, linkedSites: string[]): boolean {
    return linkedSites.includes(siteId);
}
