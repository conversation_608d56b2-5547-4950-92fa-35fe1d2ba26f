import { colorfulPillPattern } from '@sage/xtrem-system/build/lib/client-functions/color-pattern';
import type { LabelFieldStyle } from '@sage/xtrem-ui';

// Display status pill features (color, filled, etc)
export function getUploadedDocumentStatusPillFeatures(value?: string | null): Required<LabelFieldStyle> {
    switch (value) {
        case 'draft':
            return colorfulPillPattern.filledNeutral;
        case 'reviewInProgress':
        case 'processing':
            return colorfulPillPattern.filledInformation;
        case 'completed':
        case 'reviewDone':
            return colorfulPillPattern.filledPositive;
        case 'error':
            return colorfulPillPattern.filledNegative;
        default:
            return colorfulPillPattern.filledClosing;
    }
}
