import type { Dict } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';

const processing = ui.localize(
    '@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_processing',
    'Processing',
);

const draft = ui.localize(
    '@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_draft',
    'Draft',
);

const reviewInProgress = ui.localize(
    '@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_review_in_progress',
    'Review in progress',
);

const reviewDone = ui.localize(
    '@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_review_done',
    'Review done',
);

export function getStepSequence() {
    return [processing, draft, reviewInProgress, reviewDone];
}

function getProcessingStatus(processingStatus: string): ui.StepSequenceStatus {
    return processingStatus === 'processing' ? 'current' : 'complete';
}

function getDraftStatus(status: string, processingStatus: string): ui.StepSequenceStatus {
    if (processingStatus === 'processing') return 'incomplete';
    if (processingStatus === 'completed' && status === 'draft') return 'current';
    return 'complete';
}

function getReviewInProgressStatus(status: string, processingStatus: string): ui.StepSequenceStatus {
    if (processingStatus === 'processing' || status === 'draft') return 'incomplete';
    if (processingStatus === 'completed' && status === 'reviewInProgress') return 'current';
    return 'complete';
}

function getReviewDoneStatus(status: string, processingStatus: string): ui.StepSequenceStatus {
    return processingStatus === 'completed' && status === 'reviewDone' ? 'complete' : 'incomplete';
}

export function getDisplayStatusStepSequence(status: string, processingStatus: string): Dict<ui.StepSequenceStatus> {
    if (processingStatus === 'error') {
        return {
            [processing]: 'current',
            [draft]: 'incomplete',
            [reviewInProgress]: 'incomplete',
            [reviewDone]: 'incomplete',
        };
    }

    if (status === 'error') {
        return {
            [processing]: 'complete',
            [draft]: 'complete',
            [reviewInProgress]: 'current',
            [reviewDone]: 'incomplete',
        };
    }

    return {
        [processing]: getProcessingStatus(processingStatus),
        [draft]: getDraftStatus(status, processingStatus),
        [reviewInProgress]: getReviewInProgressStatus(status, processingStatus),
        [reviewDone]: getReviewDoneStatus(status, processingStatus),
    };
}
