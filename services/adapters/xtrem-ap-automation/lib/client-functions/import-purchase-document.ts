import { withoutEdges } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';
import type { ImportPurchaseDocument as ImportPurchaseDocumentPage } from '../pages/import-purchase-document';

export async function getApAutomationConfiguration(
    page: ImportPurchaseDocumentPage,
): Promise<{ isActive: boolean; customerUniqueId: string } | undefined> {
    const apAutomationConfigurationNode = page.$.graph.node('@sage/xtrem-ap-automation/ApAutomationConfiguration');
    return withoutEdges(
        await apAutomationConfigurationNode
            .query(
                ui.queryUtils.edgesSelector({ isActive: true, customerUniqueId: true }, { filter: { id: 'default' } }),
            )
            .execute(),
    ).at(0);
}
