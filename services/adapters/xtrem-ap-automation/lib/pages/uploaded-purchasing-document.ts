import type {
    ApAutomationCompany,
    ApAutomationDocumentOrigin,
    ApAutomationDocumentType,
    ApAutomationProcessingStatus,
    ApAutomationUploadedDocumentStatus,
    GraphApi,
    UploadedPurchasingDocumentLine,
    UploadedPurchasingDocumentLineBinding,
    UploadedPurchasingDocument as UploadedPurchasingDocumentNode,
} from '@sage/xtrem-ap-automation-api';
import type { Currency, Item, ReasonCode, Supplier, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import { purchasing } from '@sage/xtrem-master-data/build/lib/menu-items/purchasing';
import type {
    PurchaseCreditMemo as PurchaseCreditMemoNode,
    PurchaseInvoice as PurchaseInvoiceNode,
} from '@sage/xtrem-purchasing-api';
import type { Site } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { getUploadedDocumentStatusPillFeatures } from '../client-functions/pill-color';
import {
    initPage,
    isRecordReadOnly,
    isSiteLinked,
    mapDocumentNumber,
    openDocumentLinkPage,
    supplierDocumentNumberWarning,
    updateDocumentIfResultNotPopulated,
} from '../client-functions/uploaded-purchasing-document';
import * as stepSequence from '../client-functions/uploaded-purchasing-document-step-sequence';

@ui.decorators.page<UploadedPurchasingDocument, UploadedPurchasingDocumentNode>({
    title: 'Uploaded purchasing documents',
    objectTypeSingular: 'Uploaded purchasing document',
    idField() {
        return this.supplierDocumentNumber.value;
    },
    objectTypePlural: 'Uploaded purchasing documents',
    priority: 601,
    node: '@sage/xtrem-ap-automation/UploadedPurchasingDocument',
    menuItem: purchasing,
    headerSection() {
        return this.headerSection;
    },
    createAction() {
        return [this.uploadDocument, this.syncDocuments];
    },
    businessActions() {
        return [this.resetAll, this.$standardCancelAction, this.confirm, this.save];
    },
    headerLabel() {
        return this.isResultPopulated.value ? this.status : this.processingStatus;
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.save,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
        await initPage(this);

        await updateDocumentIfResultNotPopulated(this);
        this.uploadedPurchasingDocumentStepSequence.statuses = stepSequence.getDisplayStatusStepSequence(
            this.status.value ?? '',
            this.processingStatus.value ?? '',
        );

        if (isRecordReadOnly(this)) {
            this.save.isDisabled = true;
        }
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this.uploadedPurchasingDocumentStepSequence.statuses = stepSequence.getDisplayStatusStepSequence(
            this.status.value ?? '',
            this.processingStatus.value ?? '',
        );
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'confirmBulk',
                title: 'Confirm',
                icon: 'document_tick',
                buttonType: 'primary',
                isDestructive: false,
            },
        ],
        listItem: {
            title: ui.nestedFields.text({ bind: 'supplierDocumentNumber' }),
            type: ui.nestedFields.label({ title: 'Invoice type', bind: 'type' }),
            line2: ui.nestedFields.text({ bind: { supplier: { businessEntity: { name: true } } }, title: 'Supplier' }),
            supplierId: ui.nestedFields.text({
                bind: { supplier: { businessEntity: { name: true } } },
                title: 'Supplier ID',
                isHiddenOnMainField: true,
            }),
            isResultPopulated: ui.nestedFields.technical({ bind: 'isResultPopulated' }),
            siteId: ui.nestedFields.text({ bind: { site: { id: true } }, title: 'Financial site' }),
            line2Right: ui.nestedFields.date({ bind: { supplierDocumentDate: true } }),
            totalAmountIncludingTax: ui.nestedFields.numeric({
                bind: 'totalAmountIncludingTax',
                title: 'Total including tax',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            totalAmountExcludingTax: ui.nestedFields.numeric({
                bind: 'totalAmountExcludingTax',
                title: 'Total excluding tax',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            totalTaxAmount: ui.nestedFields.numeric({
                bind: 'totalTaxAmount',
                title: 'Tax',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            currencyId: ui.nestedFields.text({
                bind: { currency: { id: true } },
                title: 'Currency',
                isHiddenOnMainField: true,
            }),
            dueDate: ui.nestedFields.date({ bind: 'dueDate' }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                optionType: '@sage/xtrem-ap-automation/ApAutomationUploadedDocumentStatus',
                style: (_id, rowValue) => getUploadedDocumentStatusPillFeatures(rowValue?.processingStatus),
                isHidden(_value, rowValue: Partial<UploadedPurchasingDocumentNode>) {
                    return !rowValue.isResultPopulated;
                },
            }),
            origin: ui.nestedFields.select({ bind: 'origin' }),
            line3: ui.nestedFields.text({
                bind: 'uuid',
                isHiddenOnMainField: true,
                isHidden(_value, rowValue: Partial<UploadedPurchasingDocumentNode>) {
                    return rowValue.isResultPopulated ?? false;
                },
            }),
            line3Right: ui.nestedFields.label({
                bind: 'processingStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-ap-automation/ApAutomationProcessingStatus',
                style: (_id, rowValue) => getUploadedDocumentStatusPillFeatures(rowValue?.processingStatus),
                isHidden(_value, rowValue: Partial<UploadedPurchasingDocumentNode>) {
                    return rowValue.isResultPopulated ?? false;
                },
            }),
            currency: ui.nestedFields.technical({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                nestedFields: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                ],
            }),
            created: ui.nestedFields.date({ bind: '_createStamp', title: 'Created', isHiddenOnMainField: true }),
            updated: ui.nestedFields.date({ bind: '_updateStamp', title: 'Updated', isHiddenOnMainField: true }),
            recipientEmail: ui.nestedFields.text({
                bind: 'recipientEmail',
                title: 'Recipient email',
                isHiddenOnMainField: true,
            }),
            senderEmail: ui.nestedFields.text({
                bind: 'senderEmail',
                title: 'Sender email',
                isHiddenOnMainField: true,
            }),
        },
        optionsMenu: [
            {
                title: 'All open statuses',
                graphQLFilter: { status: { _ne: 'reviewDone' }, isResultPopulated: true },
            },
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Draft', graphQLFilter: { status: { _eq: 'draft' }, isResultPopulated: true } },
            { title: 'Review in progress', graphQLFilter: { status: { _eq: 'reviewInProgress' } } },
            { title: 'Error', graphQLFilter: { status: { _eq: 'error' } } },
            { title: 'Confirmed', graphQLFilter: { status: { _eq: 'reviewDone' } } },
            { title: 'Not processed', graphQLFilter: { isResultPopulated: false } },
        ],
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction, this.technicalInfo],
        });
    },
    mode: 'tabs',
})
export class UploadedPurchasingDocument extends ui.Page<GraphApi, UploadedPurchasingDocumentNode> {
    @ui.decorators.pageAction<UploadedPurchasingDocument>({
        title: 'Save',
        access: { bind: '$update' },
        buttonType: 'primary',
        async onClick() {
            await supplierDocumentNumberWarning(this);
            await this.$standardSaveAction.execute(true);
            this.$.setPageClean();
        },
    })
    save: ui.PageAction;

    @ui.decorators.referenceField<UploadedPurchasingDocument, ApAutomationCompany>({})
    apAutomationCompany: ui.fields.Reference<Site>;

    @ui.decorators.checkboxField<UploadedPurchasingDocument>({})
    isReviewDone: ui.fields.Checkbox;

    @ui.decorators.checkboxField<UploadedPurchasingDocument>({})
    isResultPopulated: ui.fields.Checkbox;

    @ui.decorators.textField<UploadedPurchasingDocument>({})
    documentMimeType: ui.fields.Text;

    @ui.decorators.dropdownListField<UploadedPurchasingDocument>({
        optionType: '@sage/xtrem-ap-automation/ApAutomationDocumentOrigin',
        isHidden: true,
    })
    origin: ui.fields.DropdownList<ApAutomationDocumentOrigin>;

    @ui.decorators.tableField<UploadedPurchasingDocument, PurchaseInvoiceNode>({
        bind: { purchaseInvoice: true },
        node: '@sage/xtrem-purchasing/PurchaseInvoice',
        columns: [ui.nestedFields.technical({ bind: '_id' }), ui.nestedFields.technical({ bind: 'number' })],
    })
    purchaseInvoice: ui.fields.Table<PurchaseInvoiceNode>;

    @ui.decorators.tableField<UploadedPurchasingDocument, PurchaseCreditMemoNode>({
        bind: { purchaseCreditMemo: true },
        node: '@sage/xtrem-purchasing/PurchaseCreditMemo',
        columns: [ui.nestedFields.technical({ bind: '_id' }), ui.nestedFields.technical({ bind: 'number' })],
    })
    purchaseCreditMemo: ui.fields.Table<PurchaseCreditMemoNode>;

    @ui.decorators.section<UploadedPurchasingDocument>({
        title: 'Header',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<UploadedPurchasingDocument>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.stepSequenceField<UploadedPurchasingDocument>({
        parent() {
            return this.headerBlock;
        },
        options() {
            return stepSequence.getStepSequence();
        },
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    uploadedPurchasingDocumentStepSequence: ui.fields.StepSequence;

    @ui.decorators.labelField<UploadedPurchasingDocument>({
        optionType: '@sage/xtrem-ap-automation/ApAutomationProcessingStatus',
        style: (_id, rowValue) => getUploadedDocumentStatusPillFeatures(rowValue?.processingStatus),
        isHidden() {
            return this.isResultPopulated.value ?? false;
        },
    })
    processingStatus: ui.fields.Label<ApAutomationProcessingStatus>;

    @ui.decorators.textField<UploadedPurchasingDocument>({
        parent() {
            return this.headerBlock;
        },
        isReadOnly: true,
        width: 'medium',
        isHidden() {
            return !!this.isResultPopulated.value;
        },
    })
    uuid: ui.fields.Text;

    @ui.decorators.textField<UploadedPurchasingDocument>({
        parent() {
            return this.headerBlock;
        },
        width: 'large',
        isHidden() {
            return !!this.isResultPopulated.value;
        },
    })
    processingDetails: ui.fields.Text;

    @ui.decorators.labelField<UploadedPurchasingDocument>({
        title: 'Status',
        width: 'medium',
        optionType: '@sage/xtrem-ap-automation/ApAutomationUploadedDocumentStatus',
        style: (_id, rowValue) => getUploadedDocumentStatusPillFeatures(rowValue?.processingStatus),
        isHidden() {
            return !this.isResultPopulated.value;
        },
    })
    status: ui.fields.Label<ApAutomationUploadedDocumentStatus>;

    @ui.decorators.dropdownListField<UploadedPurchasingDocument>({
        optionType: '@sage/xtrem-ap-automation/ApAutomationDocumentType',
        parent() {
            return this.headerBlock;
        },
        isHidden() {
            return this.processingStatus.value !== 'completed';
        },
        isTransient: true,
        isReadOnly: true,
        title: 'Type',
    })
    displayType: ui.fields.DropdownList<ApAutomationDocumentType>;

    @ui.decorators.textField<UploadedPurchasingDocument>({
        title: 'Financial site',
        parent() {
            return this.headerBlock;
        },
        isHidden() {
            return this.processingStatus.value !== 'completed';
        },
        isTransient: true,
        isReadOnly: true,
    })
    displaySite: ui.fields.Text;

    @ui.decorators.textField<UploadedPurchasingDocument>({
        title: 'Supplier',
        parent() {
            return this.headerBlock;
        },
        isTransient: true,
        isHidden() {
            return this.processingStatus.value !== 'completed';
        },
        isReadOnly: true,
    })
    displaySupplier: ui.fields.Text;

    @ui.decorators.textField<UploadedPurchasingDocument>({
        title: 'Supplier document number',
        width: 'small',
        parent() {
            return this.headerBlock;
        },
        isTransient: true,
        isHidden() {
            return this.processingStatus.value !== 'completed';
        },
        isReadOnly: true,
    })
    displaySupplierDocumentNumber: ui.fields.Text;

    @ui.decorators.linkField<UploadedPurchasingDocument>({
        parent() {
            return this.headerBlock;
        },
        title: 'Link',
        width: 'medium',
        isTransient: true,
        onClick() {
            openDocumentLinkPage({
                page: this,
                purchaseDocumentSysId:
                    (this.type.value === 'invoice'
                        ? Number(this.purchaseInvoice.value?.at(0)?._id)
                        : Number(this.purchaseCreditMemo.value?.at(0)?._id)) ?? 0,
                documentType: this.type.value,
            });
        },
        map() {
            return mapDocumentNumber({ page: this, documentType: this.type.value });
        },
        isHidden() {
            return !this.isReviewDone.value;
        },
    })
    documentNumberLink: ui.fields.Link;

    @ui.decorators.section<UploadedPurchasingDocument>({
        title: 'Matching',
        isTitleHidden: true,
    })
    matchingSection: ui.containers.Section;

    @ui.decorators.block<UploadedPurchasingDocument>({
        parent() {
            return this.matchingSection;
        },
        width: 'medium',
    })
    previewBlock: ui.containers.Block;

    @ui.decorators.previewField<UploadedPurchasingDocument>({
        title: 'Uploaded Document',
        parent() {
            return this.previewBlock;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
        hasPaginationControls: true,
    })
    document: ui.fields.Preview;

    @ui.decorators.block<UploadedPurchasingDocument>({
        parent() {
            return this.matchingSection;
        },
        width: 'medium',
    })
    matchingBlock: ui.containers.Block;

    @ui.decorators.messageField<UploadedPurchasingDocument>({
        isTransient: true,
        parent() {
            return this.matchingBlock;
        },
        width: 'large',
        content: 'Check that the scanned values are correct before confirming.',
        variant: 'warning',
        isHidden() {
            return isRecordReadOnly(this);
        },
    })
    warningMessage: ui.fields.Message;

    @ui.decorators.dropdownListField<UploadedPurchasingDocument>({
        optionType: '@sage/xtrem-ap-automation/ApAutomationDocumentType',
        parent() {
            return this.matchingBlock;
        },
    })
    type: ui.fields.DropdownList<ApAutomationDocumentType>;

    @ui.decorators.referenceField<UploadedPurchasingDocument, Site>({
        title: 'Financial site',
        parent() {
            return this.matchingBlock;
        },
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select financial site',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID ', bind: 'id' }),
        ],
        warningMessage() {
            if (
                this.apAutomationCompany.value &&
                this.site.value &&
                this.linkedSites.value.length > 0 &&
                !isSiteLinked(
                    this.site.value?._id ?? '',
                    this.linkedSites.value.map(site => site._id ?? ''),
                )
            ) {
                return ui.localize(
                    '@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__site_warning',
                    'This site does not belong to the same company as your configuration setup.',
                );
            }
            return '';
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<UploadedPurchasingDocument, Supplier>({
        title: 'Supplier',
        lookupDialogTitle: 'Select supplier',
        parent() {
            return this.matchingBlock;
        },
        fetchesDefaults: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: { businessEntity: { name: true } } }),
            ui.nestedFields.text({ title: 'ID', bind: { businessEntity: { name: true } } }),
            ui.nestedFields.text({ title: 'Country', bind: { businessEntity: { country: { name: true } } } }),
        ],
    })
    supplier: ui.fields.Reference<Supplier>;

    @ui.decorators.textField<UploadedPurchasingDocument>({
        title: 'Supplier document number',
        width: 'small',
        parent() {
            return this.matchingBlock;
        },
    })
    supplierDocumentNumber: ui.fields.Text;

    @ui.decorators.dateField<UploadedPurchasingDocument>({
        title: 'Supplier document date',
        parent() {
            return this.matchingBlock;
        },
    })
    supplierDocumentDate: ui.fields.Date;

    @ui.decorators.dateField<UploadedPurchasingDocument>({
        title: 'Due date',
        parent() {
            return this.matchingBlock;
        },
    })
    dueDate: ui.fields.Date;

    @ui.decorators.referenceField<UploadedPurchasingDocument, Currency>({
        title: 'Currency',
        lookupDialogTitle: 'Select currency',
        parent() {
            return this.matchingBlock;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
        ],
        filter: { isActive: true },
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        width: 'small',
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.numericField<UploadedPurchasingDocument>({
        title: 'Total supplier amount excl. tax',
        parent() {
            return this.matchingBlock;
        },
        unit() {
            return this.currency?.value;
        },
        scale: null,
    })
    totalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<UploadedPurchasingDocument>({
        title: 'Total supplier tax',
        parent() {
            return this.matchingBlock;
        },
        unit() {
            return this.currency?.value;
        },
        scale: null,
    })
    totalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericField<UploadedPurchasingDocument>({
        title: 'Total supplier amount incl. tax',
        parent() {
            return this.matchingBlock;
        },
        unit() {
            return this.currency?.value;
        },
        scale: null,
    })
    totalAmountIncludingTax: ui.fields.Numeric;

    @ui.decorators.dateField<UploadedPurchasingDocument>({
        title: 'Posting date',
        width: 'small',
        parent() {
            return this.matchingBlock;
        },
    })
    postingDate: ui.fields.Date;

    @ui.decorators.textField<UploadedPurchasingDocument>({
        parent() {
            return this.matchingBlock;
        },
        isReadOnly: true,
        width: 'medium',
        isHidden() {
            return this.origin.value !== 'email';
        },
    })
    senderEmail: ui.fields.Text;

    @ui.decorators.referenceField<UploadedPurchasingDocument, ReasonCode>({
        lookupDialogTitle: 'Select reason',
        minLookupCharacters: 0,
        parent() {
            return this.matchingBlock;
        },
        isHidden() {
            return this.type.value !== 'creditMemo';
        },
    })
    reason: ui.fields.Reference<ReasonCode>;

    @ui.decorators.tableField<UploadedPurchasingDocument, UploadedPurchasingDocumentLineBinding>({
        title: 'Lines',
        isFullWidth: true,
        canSelect: false,
        canAddNewLine: false,
        hasLineNumbers: true,
        orderBy: { _sortValue: +1 },
        // TODO: We hide this content for now
        isHidden: true,
        columns: [
            ui.nestedFields.image({
                title: 'Image',
                isTitleHidden: true,
                bind: { item: { image: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.reference<UploadedPurchasingDocument, UploadedPurchasingDocumentLineBinding, Item>({
                title: 'Item name',
                lookupDialogTitle: 'Select item',
                minLookupCharacters: 3,
                bind: 'item',
                shouldSuggestionsIncludeColumns: true,
                isAutoSelectEnabled: true,
                isMandatory: true,
                fetchesDefaults: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                ],
                orderBy: { name: -1, stockUnit: { id: +1 } },
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                isMandatory: true,
            }),
            ui.nestedFields.reference<UploadedPurchasingDocument, UploadedPurchasingDocumentLineBinding, UnitOfMeasure>(
                {
                    title: 'Purchase unit',
                    lookupDialogTitle: 'Select unit',
                    minLookupCharacters: 0,
                    bind: 'purchaseUnit',
                    node: '@sage/xtrem-master-data/UnitOfMeasure',
                    tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                    valueField: 'symbol',
                    isMandatory: true,
                    fetchesDefaults: true,
                    columns: [
                        ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                        ui.nestedFields.technical({ bind: 'id' }),
                        ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                        ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ],
                },
            ),
            ui.nestedFields.numeric({
                title: 'Quantity in purchase unit',
                bind: 'quantity',
                isMandatory: true,
                unitMode: 'unitOfMeasure',
                unit: (_rowId, rowData) => rowData?.purchaseUnit,
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax',
                bind: 'amountExcludingTax',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                width: 'small',
            }),
            ui.nestedFields.numeric({
                title: 'Tax amount',
                bind: 'taxAmount',
                width: 'small',
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Total including tax',
                bind: 'amountIncludingTax',
                width: 'large',
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
        ],
    })
    lines: ui.fields.Table<UploadedPurchasingDocumentLine>;

    @ui.decorators.multiReferenceField<UploadedPurchasingDocument, Site>({
        bind: 'linkedSites',
        node: '@sage/xtrem-system/Site',
        valueField: 'name',
        isHidden: true,
    })
    linkedSites: ui.fields.MultiReference<Site>;

    @ui.decorators.pageAction<UploadedPurchasingDocument>({
        title: 'Reset all',
        isHidden() {
            return isRecordReadOnly(this) || this.status.value === 'draft' || this.$.isDirty;
        },
        async onClick() {
            this.$.loader.isHidden = false;
            await this.$.graph
                .node('@sage/xtrem-ap-automation/UploadedPurchasingDocument')
                .mutations.resetUploadedDocument(true, { uploadedPurchasingDocument: this.$.recordId ?? '' })
                .execute();
            await this.$.router.refresh();
            await this.$.refreshNavigationPanel();
            this.$.loader.isHidden = true;
        },
    })
    resetAll: ui.PageAction;

    @ui.decorators.pageAction<UploadedPurchasingDocument>({
        title: 'Confirm',
        isHidden() {
            // It's the same condition to read-only for this button
            return isRecordReadOnly(this) || this.$.isDirty;
        },
        async onClick() {
            await this.$.dialog.page(
                '@sage/xtrem-ap-automation/ConfirmUploadedPurchasingDocument',
                { _id: this.$.recordId ?? '', invoiceType: this.type.value ?? '' },
                { resolveOnCancel: true },
            );
            await this.$.router.refresh();
            await this.$.refreshNavigationPanel();
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<UploadedPurchasingDocument>({
        title: 'Sync emails',
        icon: 'sync',
        onError(error) {
            this.$.loader.isHidden = true;
            return error.message;
        },
        async onClick() {
            this.$.loader.isHidden = false;
            await this.$.graph
                .node('@sage/xtrem-ap-automation/UploadedPurchasingDocument')
                .mutations.syncDocuments(true, {})
                .execute();
            this.$.loader.isHidden = true;
            await this.$.refreshNavigationPanel();
        },
    })
    syncDocuments: ui.PageAction;

    @ui.decorators.pageAction<UploadedPurchasingDocument>({
        title: 'Upload a document',
        icon: 'upload',
        async onClick() {
            await this.$.dialog.page('@sage/xtrem-ap-automation/ImportPurchaseDocument', {}, { resolveOnCancel: true });
            await this.$.refreshNavigationPanel();
        },
    })
    uploadDocument: ui.PageAction;

    @ui.decorators.pageAction<UploadedPurchasingDocument>({
        title: 'Technical information',
        async onClick() {
            await this.$.dialog.page(
                '@sage/xtrem-ap-automation/TechnicalInfo',
                { _id: this.$.recordId ?? '' },
                { resolveOnCancel: true },
            );
        },
    })
    technicalInfo: ui.PageAction;
}
