import type {
    ApAutomationCompany,
    ApAutomationProcessingStatus,
    GraphApi,
    UploadedPurchasingDocument,
} from '@sage/xtrem-ap-automation-api';
import * as ui from '@sage/xtrem-ui';
import { getApAutomationConfiguration } from '../client-functions/import-purchase-document';

@ui.decorators.page<ImportPurchaseDocument, UploadedPurchasingDocument>({
    title: 'Upload purchasing documents',
    node: '@sage/xtrem-ap-automation/UploadedPurchasingDocument',
    businessActions() {
        return [this.scan];
    },
    async onLoad() {
        const apAutomationConfiguration = await getApAutomationConfiguration(this);
        this.isTenantConfigurationActive.value = apAutomationConfiguration?.isActive ?? false;
        this.customerUniqueId.value = this.isTenantConfigurationActive.value
            ? (apAutomationConfiguration?.customerUniqueId ?? '')
            : '';
    },
})
export class ImportPurchaseDocument extends ui.Page<GraphApi, UploadedPurchasingDocument> {
    @ui.decorators.textField<ImportPurchaseDocument>({})
    documentMimeType: ui.fields.Text;

    @ui.decorators.selectField<ImportPurchaseDocument>({})
    processingStatus: ui.fields.Select<ApAutomationProcessingStatus>;

    @ui.decorators.switchField<ImportPurchaseDocument>({})
    isTempUuid: ui.fields.Switch;

    @ui.decorators.checkboxField<ImportPurchaseDocument>({ isTransient: true, isHidden: true })
    isTenantConfigurationActive: ui.fields.Checkbox;

    @ui.decorators.textField<ImportPurchaseDocument>({ isHidden: true })
    customerUniqueId: ui.fields.Text;

    @ui.decorators.section<ImportPurchaseDocument>({
        title: 'Upload',
        isTitleHidden: true,
    })
    uploadSection: ui.containers.Section;

    @ui.decorators.block<ImportPurchaseDocument>({
        parent() {
            return this.uploadSection;
        },
    })
    companyBlock: ui.containers.Block;

    @ui.decorators.referenceField<ImportPurchaseDocument, ApAutomationCompany>({
        parent() {
            return this.companyBlock;
        },
        node: '@sage/xtrem-ap-automation/ApAutomationCompany',
        isTransient: true,
        title: 'Company',
        valueField: { company: { name: true } },
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: { company: { id: true } } }),
            ui.nestedFields.text({ bind: { company: { name: true } } }),
            ui.nestedFields.technical({ bind: { customerUniqueId: true } }),
        ],
        isHidden() {
            return this.isTenantConfigurationActive.value ?? false;
        },
        onChange() {
            this.customerUniqueId.value = this.company.value?.customerUniqueId ?? '';
        },
    })
    company: ui.fields.Reference<ApAutomationCompany>;

    @ui.decorators.block<ImportPurchaseDocument>({
        parent() {
            return this.uploadSection;
        },
    })
    uploadBlock: ui.containers.Block;

    @ui.decorators.fileDepositField<ImportPurchaseDocument>({
        parent() {
            return this.uploadBlock;
        },
        title: 'Financial document',
        node: '@sage/xtrem-upload/UploadedFile',
        fileTypes:
            'application/pdf, image/jpg, image/jpeg, image/tiff, image/tif, image/png, image/heic, image/heif, application/xml, text/xml',
        isMandatory: true,
        kind: 'upload',
    })
    uploadedFile: ui.fields.FileDeposit;

    @ui.decorators.pageAction<ImportPurchaseDocument>({
        title: 'Scan',
        buttonType: 'gradient-white',
        async onClick() {
            if (this.uploadedFile.value) {
                this.isTempUuid.value = true;
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-ap-automation/uploaded_purchasing_documents_process_start_notification.',
                        '{{fileName}} has been submitted for processing. You will be notified when it is completed.',
                        { fileName: this.uploadedFile.value.filename },
                    ),
                    { timeout: 10000, type: 'success' },
                );

                await this.$standardSaveAction.execute(true);
            }
        },
        isDisabled() {
            return !['uploaded', 'verified'].includes(this.uploadedFile.value?.status ?? '');
        },
    })
    scan: ui.PageAction;
}
