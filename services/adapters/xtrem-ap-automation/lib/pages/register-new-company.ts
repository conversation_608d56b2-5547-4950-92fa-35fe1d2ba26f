import type { GraphApi, UploadedPurchasingDocument } from '@sage/xtrem-ap-automation-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib//client-functions/utils';
import type { Company } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<RegisterNewCompany>({
    title: 'Register new company',
    access: { node: '@sage/xtrem-ap-automation/UploadedPurchasingDocument' },
    isTransient: true,
    businessActions() {
        return [this.register];
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
})
export class RegisterNewCompany extends ui.Page<GraphApi, UploadedPurchasingDocument> {
    @ui.decorators.section<RegisterNewCompany>({ isTitleHidden: true })
    registerNewCompanySection: ui.containers.Section;

    @ui.decorators.block<RegisterNewCompany>({
        isTitleHidden: true,
        parent() {
            return this.registerNewCompanySection;
        },
    })
    registerNewCompanyBlock: ui.containers.Block;

    @ui.decorators.referenceField<RegisterNewCompany, Company>({
        parent() {
            return this.registerNewCompanyBlock;
        },
        node: '@sage/xtrem-system/Company',
        title: 'Company',
        filter() {
            return { id: { _nin: JSON.parse(this.$.queryParameters.companies as string) } };
        },
        valueField: 'name',
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
        ],
    })
    company: ui.fields.Reference<Company>;

    @ui.decorators.pageAction<RegisterNewCompany>({
        title: 'Register',
        buttonType: 'primary',
        async onClick() {
            this.$.loader.isHidden = false;
            await this.$.graph
                .node('@sage/xtrem-ap-automation/ApAutomationCompany')
                .mutations.registerCompanyOnSageAi(true, { company: this.company.value?._id ?? '' })
                .execute();
            this.$.loader.isHidden = true;
            this.$.setPageClean();
            this.$.router.goTo('@sage/xtrem-ap-automation/ApAutomationConfiguration');
        },
        isDisabled() {
            return this.company.value === null;
        },
    })
    register: ui.PageAction;
}
