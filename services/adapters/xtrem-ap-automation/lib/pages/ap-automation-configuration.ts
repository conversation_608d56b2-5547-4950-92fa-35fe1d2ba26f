import type {
    ApAutomationCompany as ApAutomationCompanyNode,
    ApAutomationConfiguration as ApAutomationConfigurationNode,
    GraphApi,
} from '@sage/xtrem-ap-automation-api';
import type { Country } from '@sage/xtrem-structure-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import { integrations } from '@sage/xtrem-system/build/lib/menu-items/integrations';
import * as ui from '@sage/xtrem-ui';
import { tenantVerification } from '../client-functions/ap-automation-configuration';

@ui.decorators.page<ApAutomationConfiguration>({
    node: '@sage/xtrem-ap-automation/ApAutomationConfiguration',
    title: 'AP Automation configuration',
    menuItem: integrations,
    navigationPanel: undefined,
    async defaultEntry() {
        const recordId = await this.$.graph
            .node('@sage/xtrem-ap-automation/ApAutomationConfiguration')
            .mutations.createOrGetDefaultRecord(true, {})
            .execute();
        return recordId.toString();
    },
    businessActions() {
        return [this.$standardCancelAction, this.save];
    },
    async onLoad() {
        await tenantVerification(this);

        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.save,
            cancel: this.$standardCancelAction,
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        // Disable the register new company button when isDirty
        this.registerNewCompany.isDisabled = isDirty;
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
        });
    },
})
export class ApAutomationConfiguration extends ui.Page<GraphApi, ApAutomationConfigurationNode> {
    @ui.decorators.pageAction<ApAutomationConfiguration>({
        title: 'Save',
        access: { bind: '$update' },
        buttonType: 'primary',
        async onClick() {
            await this.$standardSaveAction.execute(true);
            this.$.setPageClean();
            await this.$.router.hardRefresh();
        },
    })
    save: ui.PageAction;

    @ui.decorators.section<ApAutomationConfiguration>({
        title: 'General',
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<ApAutomationConfiguration>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.radioField<ApAutomationConfiguration>({
        title: 'Configuration level',
        parent() {
            return this.mainBlock;
        },
        onChange() {
            this.tenantBlock.isHidden = this.configurationLevel.value !== 'tenant';
            this.companiesBlock.isHidden = this.configurationLevel.value === 'tenant';
        },
    })
    configurationLevel: ui.fields.Radio;

    @ui.decorators.checkboxField<ApAutomationConfiguration>({ isHidden: true })
    isActive: ui.fields.Checkbox;

    @ui.decorators.block<ApAutomationConfiguration>({
        parent() {
            return this.mainSection;
        },
        title: 'Tenant',
        isHidden() {
            return !this.isActive.value;
        },
    })
    tenantBlock: ui.containers.Block;

    @ui.decorators.textField<ApAutomationConfiguration>({ isHidden: true })
    originalTenantId: ui.fields.Text;

    @ui.decorators.referenceField<ApAutomationConfiguration, Country>({
        parent() {
            return this.tenantBlock;
        },
        isMandatory: true,
        minLookupCharacters: 1,
        isReadOnly() {
            return this.customerUniqueId.value !== '';
        },
    })
    country: ui.fields.Reference<Country>;

    @ui.decorators.textField<ApAutomationConfiguration>({
        title: 'Customer ID',
        isReadOnly: true,
        isFullWidth: true,
        parent() {
            return this.tenantBlock;
        },
    })
    customerUniqueId: ui.fields.Text;

    @ui.decorators.textField<ApAutomationConfiguration>({
        title: 'Incoming accounts payable email address',
        isReadOnly: true,
        isFullWidth: true,
        parent() {
            return this.tenantBlock;
        },
    })
    accountsPayableEmail: ui.fields.Text;

    @ui.decorators.block<ApAutomationConfiguration>({
        parent() {
            return this.mainSection;
        },
        title: 'Companies',
        isHidden() {
            return this.isActive.value ?? false;
        },
    })
    companiesBlock: ui.containers.Block;

    @ui.decorators.pageAction<ApAutomationConfiguration>({
        icon: 'add',
        title: 'Register new company',
        async onClick() {
            JSON.stringify(this.companies.value.map(companyConfiguration => companyConfiguration.company?.id));
            await this.$.dialog.page('@sage/xtrem-ap-automation/RegisterNewCompany', {
                companies: JSON.stringify(
                    this.companies.value.map(companyConfiguration => companyConfiguration.company?.id),
                ),
            });
            await this.companies.refresh();
        },
    })
    registerNewCompany: ui.PageAction;

    @ui.decorators.tableField<ApAutomationConfiguration, ApAutomationCompanyNode>({
        isTitleHidden: true,
        isFullWidth: true,
        canSelect: false,
        canResizeColumns: true,
        node: '@sage/xtrem-ap-automation/ApAutomationCompany',
        parent() {
            return this.companiesBlock;
        },
        headerBusinessActions() {
            return [this.registerNewCompany];
        },
        columns: [
            ui.nestedFields.text({ bind: { company: { id: true } }, title: 'ID', isReadOnly: true, size: 'small' }),
            ui.nestedFields.text({ bind: { company: { name: true } }, title: 'Name', isReadOnly: true }),
            ui.nestedFields.reference<ApAutomationConfiguration, ApAutomationCompanyNode, Country>({
                bind: { company: { country: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text({ bind: 'customerUniqueId', title: 'Customer ID', isReadOnly: true }),
            ui.nestedFields.text({
                bind: 'accountsPayableEmail',
                title: 'Incoming accounts payable email address',
                isReadOnly: true,
                size: 'large',
            }),
            ui.nestedFields.technical({ bind: { _id: true } }),
            ui.nestedFields.technical({ bind: { originalTenantId: true } }),
            ui.nestedFields.technical({ bind: { isInboxActive: true } }),
        ],
    })
    companies: ui.fields.Table<ApAutomationCompanyNode>;
}
