import type { ApAutomationDocumentOrigin, GraphApi, UploadedPurchasingDocument } from '@sage/xtrem-ap-automation-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<TechnicalInfo>({
    title: 'Technical information',
    node: '@sage/xtrem-ap-automation/UploadedPurchasingDocument',
})
export class TechnicalInfo extends ui.Page<GraphApi, UploadedPurchasingDocument> {
    @ui.decorators.checkboxField<TechnicalInfo>({})
    isResultPopulated: ui.fields.Checkbox;

    @ui.decorators.section<TechnicalInfo>({
        title: 'Technical information',
    })
    technicalSection: ui.containers.Section;

    @ui.decorators.block<TechnicalInfo>({
        parent() {
            return this.technicalSection;
        },
    })
    technicalBlock: ui.containers.Block;

    @ui.decorators.textField<TechnicalInfo>({
        parent() {
            return this.technicalBlock;
        },
        isReadOnly: true,
    })
    uuid: ui.fields.Text;

    @ui.decorators.textField<TechnicalInfo>({
        parent() {
            return this.technicalBlock;
        },
        isReadOnly: true,
    })
    customerUniqueId: ui.fields.Text;

    @ui.decorators.dropdownListField<TechnicalInfo>({
        optionType: '@sage/xtrem-ap-automation/ApAutomationDocumentOrigin',
        parent() {
            return this.technicalBlock;
        },
        isReadOnly: true,
    })
    origin: ui.fields.DropdownList<ApAutomationDocumentOrigin>;

    @ui.decorators.textField<TechnicalInfo>({
        parent() {
            return this.technicalBlock;
        },
        isReadOnly: true,
        isHidden() {
            return this.origin.value !== 'email';
        },
    })
    recipientEmail: ui.fields.Text;
}
