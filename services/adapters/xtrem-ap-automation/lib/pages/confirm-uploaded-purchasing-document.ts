import type { ApAutomationDocumentType, GraphApi, UploadedPurchasingDocument } from '@sage/xtrem-ap-automation-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib//client-functions/utils';
import * as ui from '@sage/xtrem-ui';
import { createPurchaseDocument } from '../client-functions/confirm-uploaded-purchasing-document';
import { openDocumentLinkPage } from '../client-functions/uploaded-purchasing-document';

@ui.decorators.page<ConfirmUploadedPurchasingDocument>({
    title: 'Confirm and create',
    node: '@sage/xtrem-ap-automation/UploadedPurchasingDocument',
    isTransient: true,
    businessActions() {
        return [this.$standardCancelAction, this.confirmAndUpload, this.confirmAndOpen];
    },
    onLoad() {
        this.type.value = this.$.queryParameters.invoiceType as ApAutomationDocumentType;
        this.text.value = `You are about to create the following document: ${ui.localizeEnumMember('@sage/xtrem-ap-automation/ApAutomationDocumentType', this.type.value)}`;
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
})
export class ConfirmUploadedPurchasingDocument extends ui.Page<GraphApi, UploadedPurchasingDocument> {
    @ui.decorators.section<ConfirmUploadedPurchasingDocument>({ isTitleHidden: true })
    confirmSection: ui.containers.Section;

    @ui.decorators.textField<ConfirmUploadedPurchasingDocument>({})
    _id: ui.fields.Text;

    @ui.decorators.labelField<ConfirmUploadedPurchasingDocument>({
        optionType: '@sage/xtrem-ap-automation/ApAutomationDocumentType',
    })
    type: ui.fields.Label<ApAutomationDocumentType>;

    @ui.decorators.block<ConfirmUploadedPurchasingDocument>({
        isTitleHidden: true,
        parent() {
            return this.confirmSection;
        },
    })
    confirmBlock: ui.containers.Block;

    @ui.decorators.staticContentField<ConfirmUploadedPurchasingDocument>({
        parent() {
            return this.confirmBlock;
        },
        isFullWidth: true,
        isTitleHidden: true,
    })
    text: ui.fields.StaticContent;

    @ui.decorators.pageAction<ConfirmUploadedPurchasingDocument>({
        title: 'Confirm and upload new',
        buttonType: 'primary',
        async onClick() {
            this.$.loader.isHidden = false;
            await createPurchaseDocument(this);
            this.$.loader.isHidden = true;
            this.$.router.goTo('@sage/xtrem-ap-automation/UploadedPurchasingDocument');
        },
    })
    confirmAndUpload: ui.PageAction;

    @ui.decorators.pageAction<ConfirmUploadedPurchasingDocument>({
        title: 'Confirm and view',
        buttonType: 'primary',
        async onClick() {
            this.$.loader.isHidden = false;
            const purchaseDocument = await createPurchaseDocument(this);
            this.$.loader.isHidden = true;
            openDocumentLinkPage({
                page: this,
                purchaseDocumentSysId: purchaseDocument._id,
                documentType: this.type.value,
            });
        },
    })
    confirmAndOpen: ui.PageAction;
}
