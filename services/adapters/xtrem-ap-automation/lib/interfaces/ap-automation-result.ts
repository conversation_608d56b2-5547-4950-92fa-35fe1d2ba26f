import type { DateValue } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremApAutomation from '../index';

export interface ApAutomationResultMetadata {
    identifier: string;
    request_origin: string;
    file_name?: string;
    file_upload_time?: string;
    email?: ApAutomationResultEmail;
}

export interface ApAutomationResultValue<T> {
    value: T;
    confidence?: number;
    tagged: boolean;
}

export interface ApAutomationResultEmail {
    sender: string;
    subject?: string;
    recipients: string;
}

export interface ApAutomationResultHeader {
    billing_address?: ApAutomationResultValue<string>;
    currency?: ApAutomationResultValue<string>;
    document_id?: ApAutomationResultValue<string>;
    document_language?: ApAutomationResultValue<string>;
    document_type?: ApAutomationResultValue<string>;
    due_date?: ApAutomationResultValue<string>;
    invoice_type?: ApAutomationResultValue<string>;
    issue_date?: ApAutomationResultValue<string>;
    po_number?: ApAutomationResultValue<string>;
    tax_amount?: ApAutomationResultValue<string>;
    total_amount?: ApAutomationResultValue<string>;
    total_without_tax?: ApAutomationResultValue<string>;
    bill_of_lading_number?: ApAutomationResultValue<string>;
    rate_confirmation_number?: ApAutomationResultValue<string>;
}
export interface ApAutomationResultPayment {
    paid_amount?: ApAutomationResultValue<string>;
    payable_amount?: ApAutomationResultValue<string>;
    payment_method?: ApAutomationResultValue<string>;
    payment_status?: ApAutomationResultValue<string>;
    payment_term?: ApAutomationResultValue<string>;
}

export interface ApAutomationResultRecipient {
    address?: ApAutomationResultValue<string>;
    company_id?: ApAutomationResultValue<string>;
    country?: ApAutomationResultValue<string>;
    email?: ApAutomationResultValue<string>;
    name: ApAutomationResultValue<string>;
    phone_number?: ApAutomationResultValue<string>;
    tax_id?: ApAutomationResultValue<string>;
    website?: ApAutomationResultValue<string>;
    zipcode?: ApAutomationResultValue<string>;
}

export interface ApAutomationResultVendor {
    external_id?: ApAutomationResultValue<string>;
    address?: ApAutomationResultValue<string>;
    bic?: ApAutomationResultValue<string>;
    company_id?: ApAutomationResultValue<string>;
    country?: ApAutomationResultValue<string>;
    email?: ApAutomationResultValue<string>;
    iban?: ApAutomationResultValue<string>;
    name?: ApAutomationResultValue<string>;
    region?: ApAutomationResultValue<string>;
    remittance_address?: ApAutomationResultValue<string>;
    tax_id?: ApAutomationResultValue<string>;
    website?: ApAutomationResultValue<string>;
    zipcode?: ApAutomationResultValue<string>;
}

export interface ApAutomationResultVendorFromDirectory extends ApAutomationResultVendor {
    external_id?: ApAutomationResultValue<string>;
    extra_external_id_recommendations?: ApAutomationResultValue<string>[];
}

export interface ApAutomationResultTax {
    line_description?: ApAutomationResultValue<string>;
    tax?: ApAutomationResultValue<string>;
    tax_percentage?: ApAutomationResultValue<string>;
    tax_rate_name?: ApAutomationResultValue<string>;
    tax_rate_code?: ApAutomationResultValue<string>;
    taxable_amount?: ApAutomationResultValue<string>;
    total_amount?: ApAutomationResultValue<string>;
}

export interface ApAutomationItem {
    description?: ApAutomationResultValue<string>;
    quantity?: ApAutomationResultValue<string>;
    tax_amount?: ApAutomationResultValue<string>;
    tax_percentage?: ApAutomationResultValue<string>;
    total_amount?: ApAutomationResultValue<string>;
    total_without_tax?: ApAutomationResultValue<string>;
    unit_price?: ApAutomationResultValue<string>;
}

export interface ApAutomationResultExtraction {
    header?: ApAutomationResultHeader;
    payment?: ApAutomationResultPayment;
    recipient?: ApAutomationResultRecipient;
    vendor?: ApAutomationResultVendor;
    vendor_from_directory?: ApAutomationResultVendorFromDirectory;
    tax_table?: ApAutomationResultTax[];
    line_items?: ApAutomationItem[];
}

export interface ApAutomationGeneralLedgerEntry {
    description?: ApAutomationResultValue<string>;
    quantity?: ApAutomationResultValue<string>;
    tax_amount?: ApAutomationResultValue<string>;
    tax_percentage?: ApAutomationResultValue<string>;
    total_amount?: ApAutomationResultValue<string>;
    total_without_tax?: ApAutomationResultValue<string>;
    unit_price?: ApAutomationResultValue<string>;
    account?: ApAutomationResultValue<string>;
    account_base_category?: ApAutomationResultValue<string>;
    account_name?: ApAutomationResultValue<string>;
    class_code?: ApAutomationResultValue<string>;
    customer?: ApAutomationResultValue<string>;
    department?: ApAutomationResultValue<string>;
    employee?: ApAutomationResultValue<string>;
    item?: ApAutomationResultValue<string>;
    location?: ApAutomationResultValue<string>;
    project?: ApAutomationResultValue<string>;
    task?: ApAutomationResultValue<string>;
}

export interface ApAutomationResult {
    metadata: ApAutomationResultMetadata;
    extraction: ApAutomationResultExtraction;
    general_ledger?: ApAutomationGeneralLedgerEntry[];
    split?: any;
}

export interface ApAutomationDocumentDates {
    supplierDocumentDate: DateValue | null;
    dueDate: DateValue | null;
}

export interface ApAutomationHeaderAmounts {
    totalAmountIncludingTax: number;
    totalAmountExcludingTax: number;
    totalTaxAmount: number;
}

export interface ApAutomationProcessedResult {
    origin: xtremApAutomation.enums.ApAutomationDocumentOrigin;
    site: xtremSystem.nodes.Site | null;
    supplier: xtremMasterData.nodes.Supplier | null;
    supplierDocumentDate: DateValue | null;
    dueDate: DateValue | null;
    currency?: xtremMasterData.nodes.Currency | null;
    supplierDocumentNumber: string;
    totalAmountIncludingTax: number;
    totalAmountExcludingTax: number;
    totalTaxAmount: number;
    type: xtremApAutomation.enums.ApAutomationDocumentType;
    result: xtremApAutomation.interfaces.ApAutomationResult;
    isResultPopulated: boolean;
    recipientEmail: string;
    senderEmail: string;
}

export type ApAutomationReportStatus = 'processing' | 'completed' | 'error';

export interface ApAutomationReportData {
    status?: ApAutomationReportStatus;
    has_feedback?: boolean;
    details?: string;
    status_code?: string;
    detail?: string;
}
