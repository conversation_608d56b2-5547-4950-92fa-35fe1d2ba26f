import type { BinaryStream, Context } from '@sage/xtrem-core';
import { DataInputError, Logger, asyncArray, date } from '@sage/xtrem-core';
import { Datetime } from '@sage/xtrem-date-time';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremApAutomation from '../index';

const logger = Logger.getLogger(__filename, 'pending-document-manager');

export class UploadedDocumentManager {
    private readonly client: xtremApAutomation.classes.ApAutomationServiceClient;

    constructor(private readonly context: Context) {
        this.client = xtremApAutomation.classes.ApAutomationServiceClient.instance(context);
    }

    /**
     * Imports new documents from the AP Automation service and saves them as pending documents.
     */
    public async importNewDocuments({
        customerUniqueId,
        startDate = date.today().addDays(-6),
        endDate = date.today().addDays(1),
        isBatchTask = false,
        companyName = '',
    }: {
        customerUniqueId: string;
        startDate?: date;
        endDate?: date;
        isBatchTask?: boolean;
        companyName?: string;
    }): Promise<number> {
        const { context } = this;
        if (customerUniqueId === '') {
            if (isBatchTask) {
                await context.batch.logMessage(
                    'info',
                    context.localize(
                        '@sage/xtrem-ap-automation/uploaded_document_manager_no_customer_id_found',
                        'No customer ID found in the configuration.',
                    ),
                );
            }
            return 0;
        }
        const documentIds = await this.client.getDocumentList({ customerUniqueId, startDate, endDate });

        if (documentIds.length === 0) {
            if (isBatchTask) {
                await context.batch.logMessage(
                    'info',
                    context.localize(
                        '@sage/xtrem-ap-automation/uploaded_document_manager_no_documents_to_be_processed',
                        'No documents to process.',
                    ),
                );
                await context.batch.updateProgress({
                    detail: 'complete',
                    errorCount: 0,
                    successCount: 0,
                    totalCount: 0,
                    phase: 'done',
                });
            }
            return 0;
        }

        const filteredDocumentIds = await this.filterDocuments(context, endDate, startDate, documentIds);

        let counter = 0;
        let errorCounter = 0;

        await asyncArray(filteredDocumentIds).forEach(async (uuid: string) => {
            const documentStatus = await this.createUploadedDocument(context, uuid, customerUniqueId);
            counter += 1;

            if (documentStatus.status === 'error') {
                errorCounter += 1;
                logger.verbose(() => `Document is in error status=${uuid}, error message: ${documentStatus.details}`);

                if (isBatchTask) {
                    await context.batch.logMessage(
                        'error',
                        context.localize(
                            '@sage/xtrem-ap-automation/uploaded_document_manager_log_error',
                            'Error processing document {{uuid}}. Error message: {{errorMessage}}.',
                            { uuid, errorMessage: documentStatus.details },
                        ),
                    );
                }
            }
        });

        logger.debug(() => `Imported ${counter} new documents for tenant ${this.context.tenantId}.`);
        logger.debug(() => `Found ${errorCounter} documents with errors on Sage AI AP Automation.`);

        if (isBatchTask) {
            if (filteredDocumentIds.length === 0) {
                if (companyName.length > 0) {
                    await context.batch.logMessage(
                        'info',
                        this.context.localize(
                            '@sage/xtrem-ap-automation/uploaded_document_manager_no_documents_to_be_processed_company',
                            'No documents to process for this company: {{companyName}}.',
                            { companyName },
                        ),
                    );
                } else {
                    await context.batch.logMessage(
                        'info',
                        this.context.localize(
                            '@sage/xtrem-ap-automation/uploaded_document_manager_no_documents_to_be_processed',
                            'No documents to process.',
                        ),
                    );
                }
            }
        }

        return counter;
    }

    /**
     * Populates all pending documents with details that are not yet have the processing result set.
     */
    public populateAllPendingDocumentsWithDetails(isBatchTask = false): Promise<void> {
        const documents = this.context.query(xtremApAutomation.nodes.UploadedPurchasingDocument, {
            filter: { isResultPopulated: false },
            forUpdate: true,
        });
        return documents.forEach(document => this.populatePendingDocumentWithDetails(document, isBatchTask));
    }

    public async populatePendingDocumentWithDetails(
        document: xtremApAutomation.nodes.UploadedPurchasingDocument,
        isBatchTask = false,
    ): Promise<void> {
        const customerUniqueId = await document.customerUniqueId;
        const uuid = await document.uuid;
        const status = await document.processingStatus;
        if (status === 'completed') {
            if ((await document.origin) === 'email') {
                const downloadedDocument = await this.client.downloadDocument({ customerUniqueId, uuid });
                if (downloadedDocument) {
                    await document.$.set({ document: downloadedDocument });
                    if (isBatchTask) {
                        await this.context.batch.logMessage(
                            'info',
                            this.context.localize(
                                '@sage/xtrem-ap-automation/uploaded_document_manager_complete',
                                'Document {{uuid}} downloaded successfully',
                                { uuid },
                            ),
                        );
                    }
                }
            }
            const result = await this.client.getDocumentResults({ customerUniqueId, documentId: uuid });
            await document.$.set({
                status: 'draft',
                documentMimeType:
                    xtremApAutomation.functions.identifyOrigin(result.metadata) === 'email'
                        ? xtremApAutomation.functions.identifyMimeType(result.metadata)
                        : await document.documentMimeType,
                ...(await xtremApAutomation.functions.processResult(this.context, result)),
            });
            await document.$.save();
            return;
        }
        logger.debug(() => `Processing of ${uuid} is not completed, status: ${status}.`);
    }

    private async createPurchaseInvoiceFromPendingDocument(
        uploadedPurchasingDocument: xtremApAutomation.nodes.UploadedPurchasingDocument,
    ): Promise<xtremPurchasing.nodes.PurchaseInvoice> {
        const purchaseInvoiceData = await this.context.create(
            xtremPurchasing.nodes.PurchaseInvoice,
            await xtremApAutomation.functions.getPurchaseInvoicePayload(uploadedPurchasingDocument),
        );
        await purchaseInvoiceData.$.save({ flushDeferredActions: true });
        await this.uploadedPurchasingDocumentStatusUpdate(await uploadedPurchasingDocument.uuid, 'reviewDone');
        return purchaseInvoiceData;
    }

    private async createPurchaseCreditMemoFromPendingDocument(
        uploadedPurchasingDocument: xtremApAutomation.nodes.UploadedPurchasingDocument,
    ): Promise<xtremPurchasing.nodes.PurchaseCreditMemo> {
        const purchaseCreditMemoData = await this.context.create(
            xtremPurchasing.nodes.PurchaseCreditMemo,
            await xtremApAutomation.functions.getPurchaseCreditMemoPayload(uploadedPurchasingDocument),
        );
        await purchaseCreditMemoData.$.save({ flushDeferredActions: true });
        await this.uploadedPurchasingDocumentStatusUpdate(await uploadedPurchasingDocument.uuid, 'reviewDone');
        return purchaseCreditMemoData;
    }

    private async uploadedPurchasingDocumentStatusUpdate(
        uuid: string,
        status: xtremApAutomation.enums.ApAutomationUploadedDocumentStatus,
    ): Promise<void> {
        const uploadedPurchasingDocument = await this.context.read(
            xtremApAutomation.nodes.UploadedPurchasingDocument,
            { uuid },
            { forUpdate: true },
        );
        await uploadedPurchasingDocument.$.set({ status });
        await uploadedPurchasingDocument.$.save();
    }

    public async confirmUploadedPurchasingDocument(
        uploadedPurchasingDocument: xtremApAutomation.nodes.UploadedPurchasingDocument,
    ): Promise<xtremPurchasing.nodes.BasePurchaseDocument> {
        const documentType = await uploadedPurchasingDocument.type;
        switch (documentType) {
            case 'invoice':
                return this.createPurchaseInvoiceFromPendingDocument(uploadedPurchasingDocument);
            case 'creditMemo':
                return this.createPurchaseCreditMemoFromPendingDocument(uploadedPurchasingDocument);
            default:
                throw new DataInputError(
                    this.context.localize(
                        '@sage/xtrem-ap-automation/classes__updated-document-manager__document_type_not_supported',
                        'Document type is not supported: {{documentType}}',
                        { documentType },
                    ),
                );
        }
    }

    /**
     * Creates the corresponding purchase invoice or credit memo.
     * @param uploadedPurchasingDocument
     * @returns A reference to the created document.
     */
    public confirmDocument(
        uploadedPurchasingDocument: xtremApAutomation.nodes.UploadedPurchasingDocument,
    ): Promise<xtremPurchasing.nodes.BasePurchaseDocument> {
        return this.context.withErrorHandler(
            () => this.confirmUploadedPurchasingDocument(uploadedPurchasingDocument),
            async error => {
                await this.uploadedPurchasingDocumentStatusUpdate(await uploadedPurchasingDocument.uuid, 'error');
                throw error;
            },
        );
    }

    public sendDocument(document: { file: BinaryStream; fileType: string; customerUniqueId: string }): Promise<string> {
        return this.client.uploadDocument({
            fileType: document.fileType.split('/')[1],
            customerUniqueId: document.customerUniqueId,
            file: document.file,
        });
    }

    // eslint-disable-next-line class-methods-use-this
    private async filterDocuments(
        context: Context,
        endDate: date,
        startDate: date,
        documents: string[],
    ): Promise<string[]> {
        const existingDocuments = await context
            .query(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                filter: {
                    _createStamp: {
                        _lte: Datetime.fromJsDate(endDate.toJsDate()),
                        _gte: Datetime.fromJsDate(startDate.toJsDate()),
                    },
                },
            })
            .map(document => document.uuid)
            .toArray();

        const setExistingDocuments = new Set(existingDocuments);
        return documents.filter(documentId => !setExistingDocuments.has(documentId));
    }

    // eslint-disable-next-line class-methods-use-this
    private async createUploadedDocument(
        context: Context,
        uuid: string,
        customerUniqueId: string,
    ): Promise<xtremApAutomation.interfaces.ApAutomationReportData> {
        const uploadedPurchasingDocument = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
            customerUniqueId,
            uuid,
            origin: 'email',
        });
        await uploadedPurchasingDocument.$.save();

        return xtremApAutomation.classes.ApAutomationServiceClient.instance(context).getDocumentStatus({
            customerUniqueId,
            documentId: uuid,
        });
    }

    public changeEmailSettings(customerUniqueId: string, isEnabled: boolean): Promise<boolean> {
        return this.client.changeEmailSettings({ customerUniqueId, isEnabled });
    }

    public deleteExtraction({ customerUniqueId, uuid }: { customerUniqueId: string; uuid: string }): Promise<void> {
        return this.client.deleteExtraction({ customerUniqueId, uuid });
    }

    public createCompany({
        name,
        customerUniqueId,
        country,
    }: {
        name: string;
        customerUniqueId: string;
        country: string;
    }): Promise<string> {
        return this.client.createCompany({ name, customerUniqueId, country });
    }

    public getEmailAddresses({
        customerUniqueId,
    }: {
        customerUniqueId: string;
    }): Promise<{ accounts_payable: string; employee_expense: string }> {
        return this.client.getEmailAddresses({ customerUniqueId });
    }
}
