import type { Context, Dict } from '@sage/xtrem-core';
import { BinaryStream, Logger, Uuid, date } from '@sage/xtrem-core';
import type { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import axios from 'axios';
import * as jwt from 'jsonwebtoken';
import { isArray } from 'lodash';
import type { ApAutomationConfig } from '../interfaces/ap-automation-config';
import type { ApAutomationReportData, ApAutomationResult } from '../interfaces/ap-automation-result';

export type ApAutomationReportStatus = 'processing' | 'completed' | 'error';

export class ApAutomationServiceClient {
    static #instance: ApAutomationServiceClient;

    static #config: ApAutomationConfig;

    static #logger: Logger;

    static #apAutomationClient: AxiosInstance;

    static #sageIdOauthClient: AxiosInstance;

    private token: string | null = null;

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private constructor() {}

    private static readonly errorResponseInterceptor = (error: AxiosError) => {
        ApAutomationServiceClient.#logger.warn(error);
        if (error.response?.data) {
            ApAutomationServiceClient.#logger.debug(
                () => `Error response interceptor${JSON.stringify(error.response?.data, null, 4)}`,
            );
        }
        return error;
    };

    private static readonly successResponseInterceptor = (response: AxiosResponse<any, any>) => {
        ApAutomationServiceClient.#logger.debug(
            () =>
                `AP Automation service response: ${response.config.method} ${response.config.baseURL}${response.config.url} ${response.status}`,
        );
        ApAutomationServiceClient.#logger.debug(() => `Headers:\n${JSON.stringify(response.headers, null, 4)}`);

        if (response?.data) {
            ApAutomationServiceClient.#logger.debug(() => JSON.stringify(response?.data, null, 4));
        }
        return response;
    };

    private static readonly requestInterceptor = (request: InternalAxiosRequestConfig<any>) => {
        ApAutomationServiceClient.#logger.debug(
            () => `AP Automation service request: ${request.method} ${request.baseURL}${request.url}`,
        );
        ApAutomationServiceClient.#logger.debug(() => `Headers:\n${JSON.stringify(request.headers, null, 4)}`);
        if (request?.data) {
            ApAutomationServiceClient.#logger.debug(() => `Body:\n${JSON.stringify(request?.data, null, 4)}`);
        }
        return request;
    };

    public static instance(context: Pick<Context, 'configuration'>): ApAutomationServiceClient {
        if (!ApAutomationServiceClient.#instance) {
            ApAutomationServiceClient.#logger = Logger.getLogger(__filename, 'ap-automation');

            const config = context.configuration.getPackageConfig<ApAutomationConfig>('@sage/xtrem-ap-automation');

            if (!config?.clientId || !config?.clientSecret || !config?.oauthEndpointUrl || !config?.serviceUrl) {
                ApAutomationServiceClient.#logger.error('Missing configuration');
                throw new Error('Missing configuration');
            }

            ApAutomationServiceClient.#config = config;

            ApAutomationServiceClient.#apAutomationClient = axios.create({
                baseURL: ApAutomationServiceClient.#config.serviceUrl,
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                },
            });
            ApAutomationServiceClient.#apAutomationClient.interceptors.response.use(
                ApAutomationServiceClient.successResponseInterceptor,
                ApAutomationServiceClient.errorResponseInterceptor,
            );
            ApAutomationServiceClient.#apAutomationClient.interceptors.request.use(
                ApAutomationServiceClient.requestInterceptor,
            );

            ApAutomationServiceClient.#sageIdOauthClient = axios.create({
                baseURL: ApAutomationServiceClient.#config.oauthEndpointUrl,
            });
            ApAutomationServiceClient.#sageIdOauthClient.interceptors.response.use(
                ApAutomationServiceClient.successResponseInterceptor,
                ApAutomationServiceClient.errorResponseInterceptor,
            );
            ApAutomationServiceClient.#sageIdOauthClient.interceptors.request.use(
                ApAutomationServiceClient.requestInterceptor,
            );

            ApAutomationServiceClient.#instance = new ApAutomationServiceClient();
        }

        return ApAutomationServiceClient.#instance;
    }

    private isCurrentTokenValid(): boolean {
        const { token } = this;
        if (!token) {
            return false;
        }

        try {
            const decodedToken = jwt.decode(token) as jwt.JwtPayload;
            if (!decodedToken) {
                return false;
            }
            return !!decodedToken.exp && decodedToken.exp > Date.now() / 1000;
        } catch (error) {
            ApAutomationServiceClient.#logger.warn(`Error while decoding token: ${error}`);
            return false;
        }
    }

    private async getAccessToken(): Promise<string> {
        if (!this.token || !this.isCurrentTokenValid()) {
            const result = await ApAutomationServiceClient.#sageIdOauthClient({
                method: 'POST',
                headers: { 'content-type': 'application/x-www-form-urlencoded' },
                data: new URLSearchParams({
                    client_secret: ApAutomationServiceClient.#config.clientSecret,
                    client_id: ApAutomationServiceClient.#config.clientId,
                    grant_type: 'client_credentials',
                    audience: 'SAIL/mercury_orchestration',
                }).toString(),
                url: '/oauth/token',
            });

            if (!result.data.access_token) {
                throw new Error('No access token found in auth response');
            }

            this.token = String(result.data.access_token);
        }

        return this.token;
    }

    public async createCompany({
        name,
        customerUniqueId,
        country,
    }: {
        name: string;
        customerUniqueId: string;
        country: string;
    }): Promise<string> {
        const token = await this.getAccessToken();
        const result = await ApAutomationServiceClient.#apAutomationClient.post(
            '/v1/company/create',
            {
                company: {
                    name,
                    unique_id: customerUniqueId,
                    created_at: new Date().toISOString(),
                    is_active: true,
                    localization: {
                        country,
                    },
                },
            },
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            },
        );

        if (!result.data?.customer_unique_id) {
            throw new Error('No customer_unique_id found in response');
        }

        return result.data.customer_unique_id;
    }

    public async uploadDocument({
        customerUniqueId,
        fileType,
        file,
    }: {
        file: BinaryStream;
        fileType: string;
        customerUniqueId: string;
    }): Promise<string> {
        const token = await this.getAccessToken();
        const result = await ApAutomationServiceClient.#apAutomationClient.post(
            '/v1/accounts-payable/upload',
            {
                request_uuid: Uuid.generate().toString(),
                customer_unique_id: customerUniqueId,
                file_type: fileType,
                file_base_64: file.value.toString('base64'),
                settings: { check_duplicates: false },
            },
            { headers: { Authorization: `Bearer ${token}` } },
        );

        if (!result.data.orchestration_id) {
            throw new Error('No orchestration_id found in response');
        }

        return result.data.orchestration_id;
    }

    public async getDocumentStatus({
        documentId,
        customerUniqueId,
    }: {
        documentId: string;
        customerUniqueId: string;
    }): Promise<ApAutomationReportData> {
        const token = await this.getAccessToken();
        const result = await ApAutomationServiceClient.#apAutomationClient.get(
            `/v1/feed/${customerUniqueId}/${documentId}/status`,
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            },
        );

        return result.data as ApAutomationReportData;
    }

    public async getBatchDocumentStatuses({
        customerUniqueId,
        uuids,
    }: {
        customerUniqueId: string;
        uuids: string[];
    }): Promise<Dict<ApAutomationReportStatus>> {
        const token = await this.getAccessToken();
        const result = await ApAutomationServiceClient.#apAutomationClient.post(
            `/v1/feed/batch_status`,
            {
                customer_unique_id: customerUniqueId,
                identifiers: uuids,
            },
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            },
        );

        if (!isArray(result.data)) {
            throw new Error('No array found in response');
        }

        return result.data.reduce((prevValue, value) => {
            prevValue[value.identifier] = value.status;
            return prevValue;
        }, {} as Dict<ApAutomationReportStatus>);
    }

    public async downloadDocument({
        customerUniqueId,
        uuid,
    }: {
        customerUniqueId: string;
        uuid: string;
    }): Promise<BinaryStream | null> {
        const token = await this.getAccessToken();

        const result = await ApAutomationServiceClient.#apAutomationClient.get(
            `/v1/feed/${customerUniqueId}/${uuid}/download`,
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            },
        );

        if (!result.data.download_url) {
            // throw new Error('No download_url found in response');
            return null;
        }

        const fileResult = await axios.get(result.data.download_url, {
            responseType: 'arraybuffer',
        });

        return BinaryStream.fromBuffer(fileResult.data);
    }

    public async getDocumentList({
        customerUniqueId,
        startDate = date.today().addDays(-6),
        endDate = date.today().addDays(1),
    }: {
        customerUniqueId: string;
        startDate?: date;
        endDate?: date;
    }): Promise<string[]> {
        const pageSize = 100;

        const token = await this.getAccessToken();
        const documents: string[] = [];
        let counter = 0;
        // eslint-disable-next-line no-constant-condition
        while (true) {
            const result = await ApAutomationServiceClient.#apAutomationClient.get(
                `/v1/feed/${customerUniqueId}?start_date=${startDate.toString()}&end_date=${endDate.toString()}&size=${pageSize}&skip=${counter}`,
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                },
            );

            if (!isArray(result.data)) {
                throw new Error('No array found in response');
            }

            documents.push(...result.data);

            if (result.data.length < pageSize) {
                break;
            }

            counter += pageSize;
        }

        return documents;
    }

    public async getEmailAddresses({
        customerUniqueId,
    }: {
        customerUniqueId: string;
    }): Promise<{ accounts_payable: string; employee_expense: string }> {
        const token = await this.getAccessToken();
        const result = await ApAutomationServiceClient.#apAutomationClient.get(
            `/v1/company/${customerUniqueId}/email/addresses`,
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            },
        );

        if (!result.data.email_addresses) {
            throw new Error('No email_addresses found in response');
        }
        return result.data.email_addresses;
    }

    public async getDocumentResults({
        documentId,
        customerUniqueId,
    }: {
        documentId: string;
        customerUniqueId: string;
    }): Promise<ApAutomationResult> {
        const token = await this.getAccessToken();
        const result = await ApAutomationServiceClient.#apAutomationClient.get(
            `/v1/feed/${customerUniqueId}/${documentId}/results`,
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            },
        );

        return result.data as ApAutomationResult;
    }

    public async deleteExtraction({
        customerUniqueId,
        uuid,
    }: {
        customerUniqueId: string;
        uuid: string;
    }): Promise<void> {
        const token = await this.getAccessToken();
        const result = await ApAutomationServiceClient.#apAutomationClient.post(
            `/v1/delete?customer_unique_id=${customerUniqueId}`,
            {
                customer_unique_id: customerUniqueId,
                orchestration_id: uuid,
            },
            { headers: { Authorization: `Bearer ${token}` } },
        );
        if (result.status !== 204) {
            const errorMessage = result as unknown as AxiosError;
            throw new Error(`Error when deleting the extraction: ${errorMessage.message}`);
        }
    }

    public async changeEmailSettings({
        customerUniqueId,
        isEnabled,
    }: {
        customerUniqueId: string;
        isEnabled: boolean;
    }): Promise<boolean> {
        const token = await this.getAccessToken();
        const result = await ApAutomationServiceClient.#apAutomationClient.post(
            `/v1/company/${customerUniqueId}/email/settings`,
            {
                enabled: isEnabled,
            },
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            },
        );

        if (result.status !== 200) {
            const errorMessage = result as unknown as AxiosError;
            throw new Error(`Error when changing status of email address: ${errorMessage.message}`);
        }

        return true;
    }
}
