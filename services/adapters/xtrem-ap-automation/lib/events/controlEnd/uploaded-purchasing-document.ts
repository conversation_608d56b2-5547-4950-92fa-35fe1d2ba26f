import type { Context, ValidationContext } from '@sage/xtrem-core';
import * as xtremApAutomation from '../../index';

export async function customerUniqueIdValidation(
    context: Context,
    cx: ValidationContext,
    uploadedPurchasingDocument: xtremApAutomation.nodes.UploadedPurchasingDocument,
): Promise<void> {
    const tenantInfo = await xtremApAutomation.functions.getTenantInformationByContext(context);
    const apAutomationConfigurationTenant = await xtremApAutomation.functions.getDefaultConfiguration(context);
    await cx.error
        .withMessage(
            '@sage/xtrem-ap-automation/events__updated-purchasing-document__no_default_configuration_found',
            'No default configuration found.',
        )
        .if(apAutomationConfigurationTenant === null)
        .is.true();

    switch (await apAutomationConfigurationTenant?.configurationLevel) {
        case 'tenant':
            await cx.error
                .withMessage(
                    '@sage/xtrem-ap-automation/events__updated-purchasing-document__customer_unique_id_is_not_valid_tenant',
                    'The unique customer ID does not match tenant configuration.',
                )
                .if(
                    (await uploadedPurchasingDocument.customerUniqueId) ===
                        (await apAutomationConfigurationTenant?.customerUniqueId) &&
                        (await apAutomationConfigurationTenant?.originalTenantId) === tenantInfo.id,
                )
                .is.false();
            break;
        case 'company':
            await cx.error
                .withMessage(
                    '@sage/xtrem-ap-automation/events__updated-purchasing-document__customer_unique_id_is_not_valid_companies',
                    'The unique customer ID does not match any company configuration.',
                )
                .if(
                    (await apAutomationConfigurationTenant?.companies.some(
                        async companyConfiguration =>
                            (await companyConfiguration.customerUniqueId) ===
                                (await uploadedPurchasingDocument.customerUniqueId) &&
                            (await companyConfiguration.originalTenantId) === tenantInfo.id,
                    )) ?? false,
                )
                .is.false();
            break;
        default:
    }
}
