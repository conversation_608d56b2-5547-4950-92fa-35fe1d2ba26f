import type * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremApAutomation from '../../../index';

export async function updateUploadedPurchasingDocumentStatus(
    purchaseDocument: xtremPurchasing.nodes.PurchaseCreditMemo | xtremPurchasing.nodes.PurchaseInvoice,
): Promise<void> {
    const purchaseInvoiceUploadedDocument = await purchaseDocument.uploadedDocument;
    if (purchaseInvoiceUploadedDocument) {
        const uploadedDocument = await purchaseDocument.$.context.read(
            xtremApAutomation.nodes.UploadedPurchasingDocument,
            { _id: purchaseInvoiceUploadedDocument._id },
            { forUpdate: true },
        );
        await uploadedDocument?.$.set({ status: 'reviewInProgress' });
        await uploadedDocument?.$.save();
    }
}
