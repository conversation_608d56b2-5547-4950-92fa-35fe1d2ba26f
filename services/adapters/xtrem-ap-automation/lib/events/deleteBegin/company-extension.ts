import type { Context } from '@sage/xtrem-core';
import * as xtremApAutomation from '../../../index';

export async function deactivateEmailAddress(context: Context, companySysId: number): Promise<void> {
    const apAutomationCompany = await context.tryRead(xtremApAutomation.nodes.ApAutomationCompany, {
        company: companySysId,
    });
    if (apAutomationCompany) {
        await xtremApAutomation.functions.setEmailInboxSageAi(
            context,
            await apAutomationCompany.customerUniqueId,
            false,
        );
    }
}
