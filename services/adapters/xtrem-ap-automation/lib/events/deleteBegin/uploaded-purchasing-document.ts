import type { Context } from '@sage/xtrem-core';
import * as xtremApAutomation from '../../../index';
import { isCustomerUniqueIdActive } from '../../functions/common';

export async function deleteDocumentSageAi(
    context: Context,
    document: {
        processingStatus: xtremApAutomation.enums.ApAutomationProcessingStatus | null;
        isReviewDone: boolean;
        customerUniqueId: string;
        uuid: string;
    },
): Promise<void> {
    if (
        (document.processingStatus === 'error' ||
            (document.processingStatus === 'completed' && !document.isReviewDone)) &&
        (await isCustomerUniqueIdActive(context, {
            customerUniqueId: document.customerUniqueId,
            tenantId: context.tenantId,
        }))
    ) {
        const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
        await manager.deleteExtraction({ customerUniqueId: document.customerUniqueId, uuid: document.uuid });
    }
}
