import type * as xtremApAutomation from '../../../index';

export function updateStatusToReviewInProgress(parameters: {
    status: {
        current: xtremApAutomation.enums.ApAutomationUploadedDocumentStatus;
        old: xtremApAutomation.enums.ApAutomationUploadedDocumentStatus;
    };
    oldIsResultPopulated: boolean;
}): xtremApAutomation.enums.ApAutomationUploadedDocumentStatus | undefined {
    if (['error', 'reviewInProgress'].includes(parameters.status.old) && parameters.status.current === 'draft')
        return undefined;
    if (
        parameters.status.current !== 'reviewDone' &&
        ((parameters.status.current === 'draft' && parameters.oldIsResultPopulated) ||
            parameters.status.old === 'error')
    ) {
        return 'reviewInProgress';
    }
    return undefined;
}
