import type { BinaryStream, date, decimal, Reference } from '@sage/xtrem-core';
import { decorators, SubNodeExtension3 } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremApAutomation from '..';

@decorators.subNodeExtension3<PurchaseCreditMemoExtension>({
    extends: () => xtremPurchasing.nodes.PurchaseCreditMemo,
    async deleteEnd() {
        await xtremApAutomation.events.deleteEnd.PurchaseDocumentExtension.updateUploadedPurchasingDocumentStatus(
            this as xtremPurchasing.nodes.PurchaseCreditMemo,
        );
    },
})
export class PurchaseCreditMemoExtension extends SubNodeExtension3<xtremPurchasing.nodes.PurchaseCreditMemo> {
    @decorators.referenceProperty<PurchaseCreditMemoExtension, 'uploadedDocument'>({
        isStored: true,
        isPublished: true,
        node: () => xtremApAutomation.nodes.UploadedPurchasingDocument,
        isNullable: true,
    })
    readonly uploadedDocument: Reference<xtremApAutomation.nodes.UploadedPurchasingDocument | null>;

    @decorators.referencePropertyOverride<PurchaseCreditMemoExtension, 'site'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseCreditMemoExtension, 'billBySupplier'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly billBySupplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.stringPropertyOverride<PurchaseCreditMemoExtension, 'supplierDocumentNumber'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly supplierDocumentNumber: Promise<string>;

    @decorators.datePropertyOverride<PurchaseCreditMemoExtension, 'supplierDocumentDate'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly supplierDocumentDate: Promise<date>;

    @decorators.decimalPropertyOverride<PurchaseCreditMemoExtension, 'totalTaxAmount'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly totalTaxAmount: Promise<decimal>;

    @decorators.decimalPropertyOverride<PurchaseCreditMemoExtension, 'totalAmountExcludingTax'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly totalAmountExcludingTax: Promise<decimal>;

    @decorators.referencePropertyOverride<PurchaseCreditMemoExtension, 'reason'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly reason: Reference<xtremMasterData.nodes.ReasonCode>;

    @decorators.binaryStreamPropertyOverride<PurchaseCreditMemoExtension, 'pdfSupplierCreditMemo'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly pdfSupplierCreditMemo: Promise<BinaryStream | null>;

    @decorators.referencePropertyOverride<PurchaseCreditMemoExtension, 'currency'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;
}

declare module '@sage/xtrem-purchasing/lib/nodes/purchase-credit-memo' {
    export interface PurchaseCreditMemo extends PurchaseCreditMemoExtension {}
}
