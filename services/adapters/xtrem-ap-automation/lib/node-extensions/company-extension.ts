import { NodeExtension, decorators } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremApAutomation from '..';

@decorators.nodeExtension<CompanyExtension>({
    extends: () => xtremSystem.nodes.Company,
    async deleteBegin() {
        const { context } = this.$;
        await xtremApAutomation.events.deleteBegin.CompanyExtension.deactivateEmailAddress(context, this._id);
    },
})
export class CompanyExtension extends NodeExtension<xtremSystem.nodes.Company> {}

declare module '@sage/xtrem-system/lib/nodes/company' {
    export interface PurchaseInvoice extends CompanyExtension {}
}
