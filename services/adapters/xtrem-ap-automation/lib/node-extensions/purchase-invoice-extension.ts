import type { BinaryStream, Reference, date, decimal } from '@sage/xtrem-core';
import { SubNodeExtension3, decorators } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremApAutomation from '..';

@decorators.subNodeExtension3<PurchaseInvoiceExtension>({
    extends: () => xtremPurchasing.nodes.PurchaseInvoice,
    async deleteEnd() {
        await xtremApAutomation.events.deleteEnd.PurchaseDocumentExtension.updateUploadedPurchasingDocumentStatus(
            this as xtremPurchasing.nodes.PurchaseInvoice,
        );
    },
})
export class PurchaseInvoiceExtension extends SubNodeExtension3<xtremPurchasing.nodes.PurchaseInvoice> {
    @decorators.referenceProperty<PurchaseInvoiceExtension, 'uploadedDocument'>({
        isStored: true,
        isPublished: true,
        node: () => xtremApAutomation.nodes.UploadedPurchasingDocument,
        isNullable: true,
    })
    readonly uploadedDocument: Reference<xtremApAutomation.nodes.UploadedPurchasingDocument | null>;

    @decorators.referencePropertyOverride<PurchaseInvoiceExtension, 'site'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseInvoiceExtension, 'billBySupplier'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly billBySupplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.stringPropertyOverride<PurchaseInvoiceExtension, 'supplierDocumentNumber'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly supplierDocumentNumber: Promise<string>;

    @decorators.binaryStreamPropertyOverride<PurchaseInvoiceExtension, 'pdfSupplierInvoice'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly pdfSupplierInvoice: Promise<BinaryStream | null>;

    @decorators.datePropertyOverride<PurchaseInvoiceExtension, 'supplierDocumentDate'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly supplierDocumentDate: Promise<date>;

    @decorators.decimalPropertyOverride<PurchaseInvoiceExtension, 'totalTaxAmount'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly totalTaxAmount: Promise<decimal>;

    @decorators.decimalPropertyOverride<PurchaseInvoiceExtension, 'totalAmountExcludingTax'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly totalAmountExcludingTax: Promise<decimal>;

    @decorators.referencePropertyOverride<PurchaseInvoiceExtension, 'currency'>({
        async isFrozen() {
            return (await this.uploadedDocument) !== null;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;
}

declare module '@sage/xtrem-purchasing/lib/nodes/purchase-invoice' {
    export interface PurchaseInvoice extends PurchaseInvoiceExtension {}
}
