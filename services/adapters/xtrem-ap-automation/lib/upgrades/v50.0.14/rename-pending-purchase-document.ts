import { CustomSqlAction, SchemaAllowShorterStringAction } from '@sage/xtrem-system';
import { UploadedPurchasingDocument } from '../../nodes';

export const allowShorterString = new SchemaAllowShorterStringAction({
    node: () => UploadedPurchasingDocument,
    propertyName: 'supplierDocumentNumber',
    allowedShorterLength: 20,
});

export const setSupplierDocumentNumber = new CustomSqlAction({
    description: 'Set supplier document number from 24 to 20',
    body: async helper => {
        await helper.executeSql(`UPDATE ${helper.schemaName}.uploaded_purchasing_document upd
        SET supplier_document_number = LEFT(upd.supplier_document_number,20)
        WHERE LENGTH(upd.supplier_document_number) > 20;
        `);
    },
});
