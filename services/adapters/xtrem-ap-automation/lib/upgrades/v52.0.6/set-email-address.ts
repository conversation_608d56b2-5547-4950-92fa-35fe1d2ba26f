import { CustomSqlAction } from '@sage/xtrem-system';

export const setEmailAddresses = new CustomSqlAction({
    description: 'Set the value of recipientEmail and senderEmail on UploadedPurchaseDocument',
    fixes: {
        notNullableColumns: [
            { table: 'uploaded_purchasing_document', column: 'recipient_email' },
            { table: 'uploaded_purchasing_document', column: 'sender_email' },
        ],
    },
    body: async helper => {
        await helper.executeSql(`DO $$
BEGIN
    UPDATE ${helper.schemaName}.uploaded_purchasing_document t0
    SET
        recipient_email = COALESCE(upd."result"->'metadata'->'email'->>'recipients', ''),
        sender_email = COALESCE(upd."result"->'metadata'->'email'->>'sender', '')
    FROM ${helper.schemaName}.uploaded_purchasing_document upd
    WHERE
        t0._id = upd._id AND
        t0._tenant_id = upd._tenant_id AND
        upd.origin = 'email' AND
        upd.is_result_populated = TRUE;
END;
$$;`);
    },
});
