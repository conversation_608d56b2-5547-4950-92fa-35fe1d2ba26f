import { CustomSqlAction } from '@sage/xtrem-system';

export const setCustomerUniqueId = new CustomSqlAction({
    description: 'Set the value of customerUniqueId on UploadedPurchaseDocument',
    body: async helper => {
        await helper.executeSql(`UPDATE ${helper.schemaName}.uploaded_purchasing_document t0
SET customer_unique_id = (SELECT customer_unique_id FROM ${helper.schemaName}.ap_automation_configuration t1 WHERE t0._tenant_id = t1._tenant_id LIMIT 1)
`);
    },
});
