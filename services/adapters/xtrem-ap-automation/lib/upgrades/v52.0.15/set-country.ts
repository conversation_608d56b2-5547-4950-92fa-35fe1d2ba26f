import { CustomSqlAction } from '@sage/xtrem-system';

export const setCountry = new CustomSqlAction({
    description: 'Set the value of customerUniqueId on UploadedPurchaseDocument',
    body: async helper => {
        await helper.executeSql(`UPDATE ${helper.schemaName}.ap_automation_configuration t0
SET country = (SELECT _id  FROM ${helper.schemaName}.country t1 WHERE t1.id='GB' AND t0._tenant_id = t1._tenant_id)
`);
    },
});
