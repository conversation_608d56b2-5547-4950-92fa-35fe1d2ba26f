import { DataUpdateAction } from '@sage/xtrem-system';
import * as xtremApAutomation from '../../index';

export const addConfigurationLevelData = new DataUpdateAction({
    description: 'Default value for configuration level and id',
    node: () => xtremApAutomation.nodes.ApAutomationConfiguration,
    async where() {
        return (await this.customerUniqueId) !== '';
    },
    set: {
        id: 'default',
        configurationLevel: 'tenant',
    },
});
