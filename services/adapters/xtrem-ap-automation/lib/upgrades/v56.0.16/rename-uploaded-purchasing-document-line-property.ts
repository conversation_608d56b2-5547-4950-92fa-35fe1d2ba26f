import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import { UploadedPurchasingDocumentLine } from '../../nodes';

export const renamePropertyUploadedPurchasingDocumentLineForLineAmountIncludingTax = new SchemaRenamePropertyAction({
    node: () => UploadedPurchasingDocumentLine,
    oldPropertyName: 'lineAmountIncludingTax',
    newPropertyName: 'amountIncludingTax',
});

export const renamePropertyUploadedPurchasingDocumentLineForLineAmountExcludingTax = new SchemaRenamePropertyAction({
    node: () => UploadedPurchasingDocumentLine,
    oldPropertyName: 'lineAmountExcludingTax',
    newPropertyName: 'amountExcludingTax',
});
