import { CustomSqlAction } from '@sage/xtrem-system';

export const setDefaultOriginalTenantId = new CustomSqlAction({
    description: 'Set original_tenant_id default value',
    body: async helper => {
        await helper.executeSql(
            `UPDATE ${helper.schemaName}.ap_automation_configuration SET original_tenant_id = _tenant_id`,
        );
    },
    fixes: { notNullableColumns: [{ table: 'ap_automation_configuration', column: 'original_tenant_id' }] },
});
