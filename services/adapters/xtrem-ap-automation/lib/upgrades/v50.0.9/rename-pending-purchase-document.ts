import { SchemaAllowShorterStringAction, SchemaRenameNodeAction, SchemaRenamePropertyAction } from '@sage/xtrem-system';
import { UploadedPurchasingDocument, UploadedPurchasingDocumentLine } from '../../nodes';

export const renamePendingPurchaseDocument = new SchemaRenameNodeAction({
    node: () => UploadedPurchasingDocument,
    oldNodeName: 'PendingPurchaseDocument',
});

export const renamePendingPurchaseDocumentLine = new SchemaRenameNodeAction({
    node: () => UploadedPurchasingDocumentLine,
    oldNodeName: 'PendingPurchaseDocumentLine',
});

export const allowShorterString = new SchemaAllowShorterStringAction({
    node: () => UploadedPurchasingDocument,
    propertyName: 'supplierDocumentNumber',
    allowedShorterLength: 20,
});

export const renameNumberAction = new SchemaRenamePropertyAction({
    node: () => UploadedPurchasingDocument,
    oldPropertyName: 'number',
    newPropertyName: 'supplierDocumentNumber',
});
