import type { Collection } from '@sage/xtrem-core';
import type * as xtremApAutomation from '../index';

/**
 * Registers a company on Sage AI
 * @param context Current context
 * @param name Name to be used for registration
 * @returns Sage AI registration details
 */
export async function isTenantChanged(
    tenantId: string,
    defaultConfiguration: {
        isActive: boolean;
        originalTenantId: string;
        companies: Collection<xtremApAutomation.nodes.ApAutomationCompany>;
    },
): Promise<boolean> {
    return defaultConfiguration.isActive
        ? defaultConfiguration.originalTenantId !== tenantId
        : (await defaultConfiguration.companies.filter(
              async companyConfiguration =>
                  (await companyConfiguration.customerUniqueId) !== '' &&
                  (await companyConfiguration.originalTenantId) !== tenantId,
          ).length) > 0;
}
