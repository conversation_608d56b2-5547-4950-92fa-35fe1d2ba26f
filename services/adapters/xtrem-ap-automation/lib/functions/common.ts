import type { TenantInfo } from '@sage/xtrem-core';
import { BusinessRuleError, Context, Logger, Uuid } from '@sage/xtrem-core';
import * as xtremApAutomation from '../index';

const logger = Logger.getLogger(__filename, 'process-result');

export function validateSageAiRegistration(parameters: {
    originalTenantId: string;
    currentTenantId: string;
    customerUniqueId: string;
}) {
    return (
        (parameters.originalTenantId !== '' && parameters.currentTenantId !== parameters.originalTenantId) ||
        parameters.customerUniqueId === ''
    );
}

export async function getTenantInformationByContext(context: Context): Promise<TenantInfo> {
    const user = await context.user;

    if (!user) {
        throw new Error('User not found');
    }

    if (!context.tenantId) {
        throw new Error('Tenant not found');
    }

    const [tenantInfo] = await Context.tenantManager.getTenantsInfo(context, context.tenantId);
    if (!tenantInfo) {
        throw new Error('Tenant not found');
    }
    return tenantInfo;
}

/**
 * Registers a company on Sage AI
 * @param context Current context
 * @param name Name to be used for registration
 * @returns Sage AI registration details
 */
export async function sageAiRegistration(
    context: Context,
    parameters: {
        name: string;
        country: string;
    },
): Promise<xtremApAutomation.interfaces.ApAutomationRegisterOnSageAi> {
    const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
    const customerUniqueId = await manager.createCompany({
        name: parameters.name,
        customerUniqueId: Uuid.generate().toString(),
        country: parameters.country,
    });
    const emailAddresses = await manager.getEmailAddresses({ customerUniqueId });
    return {
        customerUniqueId,
        accountsPayableEmail: emailAddresses.accounts_payable,
        employeeExpenseEmail: emailAddresses.employee_expense,
    };
}

export function getDefaultConfiguration(
    context: Context,
): Promise<xtremApAutomation.nodes.ApAutomationConfiguration | null> {
    return context.tryRead(xtremApAutomation.nodes.ApAutomationConfiguration, {
        id: 'default',
    });
}

export async function checkTenantConfigurationIsActive(context: Context): Promise<void> {
    const apAutomationConfigurationTenant = await xtremApAutomation.functions.getDefaultConfiguration(context);
    if (await apAutomationConfigurationTenant?.isActive) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/functions__common__cannot_register_company_tenant_configuration_is_active',
                'You cannot register a company with Sage AI while the tenant configuration is active.',
            ),
        );
    }
}

export async function setEmailInboxSageAi(
    context: Context,
    customerUniqueId: string,
    isEnabled: boolean,
): Promise<void> {
    const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);

    await manager.changeEmailSettings(customerUniqueId, isEnabled);
}

async function isTenantConfigurationCustomerIdActive(
    apAutomationConfigurationTenant: xtremApAutomation.nodes.ApAutomationConfiguration | null,
    document: { customerUniqueId: string; tenantId: string | null },
): Promise<boolean> {
    return (
        (await apAutomationConfigurationTenant?.customerUniqueId) === document.customerUniqueId &&
        (await apAutomationConfigurationTenant?.originalTenantId) === document.tenantId
    );
}

async function isCompanyConfigurationCustomerIdActive(
    context: Context,
    document: { customerUniqueId: string; tenantId: string | null },
): Promise<boolean> {
    return (
        (await context.queryCount(xtremApAutomation.nodes.ApAutomationCompany, {
            filter: { customerUniqueId: document.customerUniqueId, originalTenantId: document.tenantId },
        })) > 0
    );
}

export async function isCustomerUniqueIdActive(
    context: Context,
    document: { customerUniqueId: string; tenantId: string | null },
): Promise<boolean> {
    const apAutomationConfigurationTenant = await xtremApAutomation.functions.getDefaultConfiguration(context);
    const isTenantConfigurationActive = await apAutomationConfigurationTenant?.isActive;
    if (
        isTenantConfigurationActive &&
        (await isTenantConfigurationCustomerIdActive(apAutomationConfigurationTenant, document))
    ) {
        return true;
    }
    if (!isTenantConfigurationActive && (await isCompanyConfigurationCustomerIdActive(context, document))) {
        return true;
    }
    logger.debug(() => `Customer ID ${document.customerUniqueId} is missing or inactive.`);
    return false;
}
