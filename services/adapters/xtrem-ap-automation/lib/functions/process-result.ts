import type { Context, NodeCreateData, NodeQueryFilter } from '@sage/xtrem-core';
import { Decimal, Logger, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremSystem from '@sage/xtrem-system';
import type * as xtremApAutomation from '../index';

const logger = Logger.getLogger(__filename, 'process-result');

async function identifyItem(
    context: Context,
    line?: xtremApAutomation.interfaces.ApAutomationItem,
): Promise<xtremMasterData.nodes.Item | null> {
    const itemName = line?.description?.value;
    if (!itemName) {
        logger.debug(() => 'Item could not be identified because line description is empty.');
        return null;
    }

    const itemByNameEquals = await context
        .query(xtremMasterData.nodes.Item, {
            filter: { name: itemName, isBought: true },
            first: 1,
        })
        .at(0);
    if (itemByNameEquals) {
        return itemByNameEquals;
    }

    const itemByEanNumberEquals = await context
        .query(xtremMasterData.nodes.Item, {
            // Remove all spaces from the item name as the EAN codes stored without spaces
            filter: { eanNumber: itemName.replace(/\s+/g, ''), isBought: true },
            first: 1,
        })
        .at(0);

    if (itemByEanNumberEquals) {
        return itemByEanNumberEquals;
    }

    const itemByNameContains = await context
        .query(xtremMasterData.nodes.Item, {
            filter: { name: { _regex: itemName, _options: 'i' }, isBought: true },
            first: 1,
        })
        .at(0);

    if (itemByNameContains) {
        return itemByNameContains;
    }

    // Replace all new lines with spaces and remove all extra spaces
    const itemNameClean = itemName.replace(/\n/g, ' ').replace(/\s+/g, ' ');

    const itemByCleanNameEquals = await context
        .query(xtremMasterData.nodes.Item, {
            filter: { name: itemNameClean, isBought: true },
            first: 1,
        })
        .at(0);
    if (itemByCleanNameEquals) {
        return itemByCleanNameEquals;
    }

    const itemByCleanNameContains = await context
        .query(xtremMasterData.nodes.Item, {
            filter: { name: { _regex: itemNameClean }, isBought: true },
            first: 1,
        })
        .at(0);

    if (itemByCleanNameContains) {
        return itemByCleanNameContains;
    }

    logger.debug(() => `Item could not be identified: ${itemName}`);

    return null;
}

function findSupplierByField(
    context: Context,
    searchCriteria: {
        field: string;
        value: string | undefined;
    },
): Promise<xtremMasterData.nodes.Supplier | undefined> {
    return context
        .query(xtremMasterData.nodes.Supplier, { filter: { [searchCriteria.field]: searchCriteria.value }, first: 1 })
        .at(0);
}

async function identifySupplier(
    context: Context,
    vendor?: xtremApAutomation.interfaces.ApAutomationResultVendor,
): Promise<xtremMasterData.nodes.Supplier | null> {
    if (!vendor) {
        logger.debug(() => 'Supplier could not be identified because vendor node is empty.');
        return null;
    }
    /*
    Search for the businessEntity that has taxIdNumber = vendor.company_id
    if we don't find anything, search for the businessEntity that has id = vendor.external_id
    if we don't find anything, search for the businessEntity that has name = vendor.name
    */
    const searchCriteria = [
        { field: 'taxIdNumber', value: vendor.company_id?.value.replace(/\s+/g, '') },
        { field: 'id', value: vendor.external_id?.value },
        { field: 'name', value: vendor.name?.value },
    ];

    // taxIdNumber
    if (searchCriteria[0].value) {
        const supplier = await findSupplierByField(context, searchCriteria[0]);
        if (supplier) {
            logger.debug(() => `Supplier found by ${searchCriteria[0].field}: ${searchCriteria[0].value}`);
            return supplier;
        }
    }

    // id
    if (searchCriteria[1].value) {
        const supplier = await findSupplierByField(context, searchCriteria[1]);
        if (supplier) {
            logger.debug(() => `Supplier found by ${searchCriteria[1].field}: ${searchCriteria[1].value}`);
            return supplier;
        }
    }

    // name
    if (searchCriteria[2].value) {
        const supplier = await findSupplierByField(context, searchCriteria[2]);
        if (supplier) {
            logger.debug(() => `Supplier found by ${searchCriteria[2].field}: ${searchCriteria[2].value}`);
            return supplier;
        }
    }

    logger.debug(() => 'Supplier could not be identified.');
    return null;
}

async function identifyCurrency(
    context: Context,
    header?: xtremApAutomation.interfaces.ApAutomationResultHeader,
): Promise<xtremMasterData.nodes.Currency | null> {
    if (!header) {
        logger.debug(() => 'Currency could not be identified because header node is empty.');
        return null;
    }

    const currencyCode = header.currency?.value;
    if (currencyCode) {
        const currency = await context.tryRead(xtremMasterData.nodes.Currency, { id: currencyCode });
        if (currency) {
            return currency;
        }
        logger.debug(() => `Unknown currency code: ${currencyCode}`);
    }

    logger.debug(() => 'Currency could not be identified.');

    return null;
}

function identifyDates(
    header?: xtremApAutomation.interfaces.ApAutomationResultHeader,
): xtremApAutomation.interfaces.ApAutomationDocumentDates {
    if (!header) {
        return { supplierDocumentDate: null, dueDate: null };
    }

    return {
        supplierDocumentDate: header?.issue_date?.value ? date.parse(header?.issue_date?.value) : null,
        dueDate: header?.due_date?.value ? date.parse(header?.due_date?.value) : null,
    };
}

function identifySupplierDocumentNumber(header?: xtremApAutomation.interfaces.ApAutomationResultHeader): string {
    if (header?.document_id?.value) {
        return header?.document_id?.value ?? '';
    }

    logger.debug(() => 'Supplier document number could not be identified.');
    return '';
}

function identifyHeaderAmounts(
    header?: xtremApAutomation.interfaces.ApAutomationResultHeader,
): xtremApAutomation.interfaces.ApAutomationHeaderAmounts {
    if (!header) {
        return { totalAmountIncludingTax: 0, totalAmountExcludingTax: 0, totalTaxAmount: 0 };
    }

    return {
        totalAmountIncludingTax: header?.total_amount?.value ? Decimal.make(header?.total_amount?.value).toNumber() : 0,
        totalAmountExcludingTax: header?.total_without_tax?.value
            ? Decimal.make(header?.total_without_tax?.value).toNumber()
            : 0,
        totalTaxAmount: header?.tax_amount?.value ? Decimal.make(header?.tax_amount?.value).toNumber() : 0,
    };
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function identifyLines(
    context: Context,
    lines?: xtremApAutomation.interfaces.ApAutomationItem[],
): Promise<Array<NodeCreateData<xtremApAutomation.nodes.UploadedPurchasingDocumentLine>>> {
    const pendingLines: Array<NodeCreateData<xtremApAutomation.nodes.UploadedPurchasingDocumentLine>> = [];
    if (!lines || lines.length === 0) {
        logger.debug(() => 'No document lines were found.');
        return pendingLines;
    }

    // eslint-disable-next-line no-restricted-syntax
    for (const line of lines) {
        const pendingLine: NodeCreateData<xtremApAutomation.nodes.UploadedPurchasingDocumentLine> = {};
        pendingLine.amountIncludingTax = line.total_amount?.value
            ? Decimal.make(line.total_amount?.value).toNumber()
            : 0;
        pendingLine.amountExcludingTax = line.total_without_tax?.value
            ? Decimal.make(line.total_without_tax?.value).toNumber()
            : 0;
        pendingLine.taxAmount = line.tax_amount?.value ? Decimal.make(line.tax_amount?.value).toNumber() : 0;
        pendingLine.quantity = line.quantity?.value ? Decimal.make(line.quantity?.value).toNumber() : 0;
        pendingLine.itemDescription = line.description?.value ?? '';
        pendingLine.item = await identifyItem(context, line);
        pendingLines.push(pendingLine);
    }

    return pendingLines;
}

function identifyDocumentType(
    header?: xtremApAutomation.interfaces.ApAutomationResultHeader,
): xtremApAutomation.enums.ApAutomationDocumentType {
    return !header?.invoice_type?.value || header?.invoice_type?.value === 'debit' ? 'invoice' : 'creditMemo';
}

export function identifyOrigin(
    metadata?: xtremApAutomation.interfaces.ApAutomationResultMetadata,
): xtremApAutomation.enums.ApAutomationDocumentOrigin {
    return metadata?.request_origin === 'API' ? 'uploaded' : 'email';
}

function findSiteByFilter(
    context: Context,
    filter: NodeQueryFilter<xtremSystem.nodes.Site>,
): Promise<xtremSystem.nodes.Site | undefined> {
    return context.query(xtremSystem.nodes.Site, { filter, first: 1 }).at(0);
}

async function identifySite(
    context: Context,
    recipient?: xtremApAutomation.interfaces.ApAutomationResultRecipient,
): Promise<xtremSystem.nodes.Site | null> {
    if (!recipient) {
        logger.debug(() => 'Site could not be identified because recipient node is empty.');
        return null;
    }

    const { company_id, name } = recipient;

    if (company_id?.value) {
        const siteByTaxId = await findSiteByFilter(context, {
            isFinance: true,
            taxIdNumber: company_id.value.replace(/\s+/g, ''),
        });

        if (siteByTaxId) {
            logger.debug(() => 'Site found by TaxIdNumber.');
            return siteByTaxId;
        }
    }

    if (name?.value) {
        const siteByName = await findSiteByFilter(context, {
            isFinance: true,
            name: name.value,
        });

        if (siteByName) {
            logger.debug(() => 'Site found by Name.');
            return siteByName;
        }
    }

    logger.debug(() => 'Site could not be identified.');
    return null;
}

export function identifyMimeType(metadata?: xtremApAutomation.interfaces.ApAutomationResultMetadata): string {
    switch (metadata?.file_name?.split('.').pop()?.toLowerCase()) {
        case 'pdf':
            return 'application/pdf';
        case 'jpg':
        case 'jpeg':
            return 'image/jpeg';
        case 'tif':
        case 'tiff':
            return 'image/tiff';
        case 'png':
            return 'image/png';
        case 'xml':
            return 'application/xml';
        case 'heic':
            return 'image/heic';
        case 'heif':
            return 'image/heif';
        default:
            return 'application/pdf';
    }
}

export async function processResult(
    context: Context,
    result: xtremApAutomation.interfaces.ApAutomationResult,
): Promise<xtremApAutomation.interfaces.ApAutomationProcessedResult> {
    const { supplierDocumentDate, dueDate } = identifyDates(result.extraction.header);
    const { totalAmountIncludingTax, totalAmountExcludingTax, totalTaxAmount } = identifyHeaderAmounts(
        result.extraction.header,
    );
    // We activate this later on
    // const lines = await identifyLines(context, result.extraction.line_items);
    return {
        isResultPopulated: true,
        origin: identifyOrigin(result.metadata),
        site: await identifySite(context, result.extraction.recipient),
        supplier: await identifySupplier(context, result.extraction.vendor),
        supplierDocumentDate,
        dueDate,
        currency: await identifyCurrency(context, result.extraction.header),
        supplierDocumentNumber: identifySupplierDocumentNumber(result.extraction.header),
        totalAmountIncludingTax,
        totalAmountExcludingTax,
        totalTaxAmount,
        type: identifyDocumentType(result.extraction.header),
        recipientEmail: result.metadata.request_origin === 'email' ? (result.metadata.email?.recipients ?? '') : '',
        senderEmail: result.metadata.request_origin === 'email' ? (result.metadata.email?.sender ?? '') : '',
        result,
    };
}

async function getPurchaseDocumentPayload(
    uploadedPurchasingDocument: xtremApAutomation.nodes.UploadedPurchasingDocument,
): Promise<
    Omit<
        NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice | xtremPurchasing.nodes.PurchaseCreditMemo>,
        'displayStatus' | 'lines'
    >
> {
    return {
        uploadedDocument: uploadedPurchasingDocument,
        currency: await uploadedPurchasingDocument.currency,
        billBySupplier: await uploadedPurchasingDocument.supplier,
        site: await uploadedPurchasingDocument.site,
        supplierDocumentDate: (await uploadedPurchasingDocument.supplierDocumentDate) || undefined,
        dueDate: (await uploadedPurchasingDocument.dueDate) || undefined,
        totalAmountExcludingTax: await uploadedPurchasingDocument.totalAmountExcludingTax,
        totalTaxAmount: await uploadedPurchasingDocument.totalTaxAmount,
        supplierDocumentNumber: (await uploadedPurchasingDocument.supplierDocumentNumber) || undefined,
    };
}

export async function getPurchaseInvoicePayload(
    uploadedPurchasingDocument: xtremApAutomation.nodes.UploadedPurchasingDocument,
): Promise<NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice>> {
    return {
        ...(await getPurchaseDocumentPayload(uploadedPurchasingDocument)),
        invoiceDate: (await uploadedPurchasingDocument.postingDate) || undefined,
        pdfSupplierInvoice:
            (await uploadedPurchasingDocument.documentMimeType) === 'application/pdf'
                ? await uploadedPurchasingDocument.document
                : null,
    };
}

export async function getPurchaseCreditMemoPayload(
    uploadedPurchasingDocument: xtremApAutomation.nodes.UploadedPurchasingDocument,
): Promise<NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo>> {
    return {
        ...(await getPurchaseDocumentPayload(uploadedPurchasingDocument)),
        reason: await uploadedPurchasingDocument.reason,
        date: (await uploadedPurchasingDocument.postingDate) || undefined,
        pdfSupplierCreditMemo:
            (await uploadedPurchasingDocument.documentMimeType) === 'application/pdf'
                ? await uploadedPurchasingDocument.document
                : null,
    };
}
