import type { Collection, Context, DateValue } from '@sage/xtrem-core';
import { BusinessRuleError, asyncArray } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremPurchasing from '@sage/xtrem-purchasing';
import type { InitialNotificationAction } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import type * as xtremUpload from '@sage/xtrem-upload';
import { promisify } from 'util';
import * as xtremApAutomation from '../index';
import type { UploadedPurchasingDocument } from '../nodes';

/**
 * Check the the document status on Sage Ai and return a status object
 * @param context
 * @param documentId - uuid of the Uploaded Purchasing Document
 */
export function getSageAiStatus(
    context: Context,
    ids: {
        document: string;
        customer: string;
    },
): Promise<xtremApAutomation.interfaces.ApAutomationReportData> {
    return xtremApAutomation.classes.ApAutomationServiceClient.instance(context).getDocumentStatus({
        customerUniqueId: ids.customer,
        documentId: ids.document,
    });
}

function checkDocumentDatesNotNull(
    context: Context,
    dates: { supplierDocument: DateValue | null; posting: DateValue | null },
): void {
    if (!dates.supplierDocument) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/function__updated-purchasing-document__supplier_document_number_cannot_be_empty',
                'Supplier document number cannot be empty.',
            ),
        );
    }

    if (!dates.posting) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/function__updated-purchasing-document__posting_date_cannot_be_empty',
                'Posting date cannot be empty.',
            ),
        );
    }
}

export async function checkIfPurchaseDocumentExists(
    context: Context,
    purchaseInvoice: Collection<xtremPurchasing.nodes.PurchaseInvoice>,
    purchaseCreditMemo: Collection<xtremPurchasing.nodes.PurchaseCreditMemo>,
) {
    if ((await purchaseInvoice.length) > 0 || (await purchaseCreditMemo.length) > 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__purchase_document_already_created',
                'A purchasing document was already created.',
            ),
        );
    }
}

function compareDocumentDates(
    context: Context,
    dates: { supplierDocument: DateValue | null; posting: DateValue | null; due: DateValue | null },
): void {
    if (dates.due && dates.supplierDocument && dates.due.compare(dates.supplierDocument) < 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/function__updated-purchasing-document__due_date_cannot_be_before_supplier_document_date',
                'Due date cannot be before supplier document date.',
            ),
        );
    }

    if (dates.posting && dates.supplierDocument && dates.posting.compare(dates.supplierDocument) < 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/function__updated-purchasing-document__posting_date_cannot_be_before_supplier_document_date',
                'Posting date cannot be before supplier document date.',
            ),
        );
    }
}

export async function checkIfReviewDone(
    context: Context,
    status: Promise<xtremApAutomation.enums.ApAutomationUploadedDocumentStatus>,
) {
    if ((await status) === 'reviewDone') {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__purchase_document_status_review_done',
                'The purchasing document status is review done.',
            ),
        );
    }
}

export async function sleepMillis(ms: number): Promise<void> {
    await promisify(setTimeout)(ms);
}

/**
 * Notify the user that the document is ready for review
 * @param context
 * @param uploadedDocumentId - _id of the Uploaded Purchasing Document
 * @param uploadedDocumentName - name of the document
 */
export async function notifyUserDocumentReady(
    context: Context,
    uploadedDocumentId: string,
    uploadedDocumentName: string,
): Promise<void> {
    const urlId = btoa(JSON.stringify({ _id: uploadedDocumentId }));

    await context.notifyUser({
        title: context.localize('@sage/xtrem-ap-automation/success_notification_title', 'Document upload'),
        description: context.localize(
            '@sage/xtrem-ap-automation/success_notification_description',
            `Document uploaded and ready for review: {{documentName}}.`,
            {
                documentName: uploadedDocumentName,
            },
        ),
        icon: 'tick',
        level: 'success',
        shouldDisplayToast: true,
        actions: [
            {
                link: `@sage/xtrem-ap-automation/UploadedPurchasingDocument/${urlId}`,
                title: context.localize(
                    '@sage/xtrem-ap-automation/success_notification_description__view_link',
                    'View',
                ),
                icon: 'link',
                style: 'tertiary',
            },
        ],
    });
}

/**
 * Notify the user that the document is still processing
 * @param context
 * @param uploadedDocumentId - _id of the Uploaded Purchasing Document
 * @param uploadedDocumentName - name of the document
 */
export async function notifyUserDocumentProcessing(
    context: Context,
    uploadedDocumentId: string,
    uploadedDocumentName: string,
): Promise<void> {
    const urlId = btoa(JSON.stringify({ _id: uploadedDocumentId }));

    await context.notifyUser({
        title: context.localize('@sage/xtrem-ap-automation/success_notification_title_processing', 'Document upload'),
        description: context.localize(
            '@sage/xtrem-ap-automation/success_notification_description_processing',
            `Document uploaded: Sage AI scan in progress: {{documentName}}.`,
            { documentName: uploadedDocumentName },
        ),
        icon: 'tick',
        level: 'success',
        shouldDisplayToast: true,
        actions: [
            {
                link: `@sage/xtrem-ap-automation/UploadedPurchasingDocument/${urlId}`,
                title: context.localize(
                    '@sage/xtrem-ap-automation/success_notification_description__view_link',
                    'View',
                ),
                icon: 'link',
                style: 'tertiary',
            },
        ],
    });
}

/**
 * Notify the user that the document is in error
 * @param context
 * @param uploadedDocumentId - _id of the Uploaded Purchasing Document
 * @param uploadedDocumentName - name of the document
 */
export async function notifyUserDocumentError(
    context: Context,
    uploadedDocumentId: string,
    uploadedDocumentName: string,
): Promise<void> {
    const urlId = btoa(JSON.stringify({ _id: uploadedDocumentId }));

    await context.notifyUser({
        title: context.localize('@sage/xtrem-ap-automation/success_notification_title_error', 'Document upload'),
        description: context.localize(
            '@sage/xtrem-ap-automation/success_notification_description_error',
            `This document was uploaded successfully, but Sage AI could not process it: {{documentName}}.`,
            {
                documentName: uploadedDocumentName,
            },
        ),
        icon: 'cross',
        level: 'error',
        shouldDisplayToast: true,
        actions: [
            {
                link: `@sage/xtrem-ap-automation/UploadedPurchasingDocument/${urlId}`,
                title: context.localize(
                    '@sage/xtrem-ap-automation/success_notification_description__view_link',
                    'View',
                ),
                icon: 'link',
                style: 'tertiary',
            },
        ],
    });
}

/**
 * Notify the user that the document failed scan
 * @param context
 * @param uploadedDocumentName - name of the document
 */
export async function notifyUserDocumentScanFailed(context: Context, uploadedDocumentName: string): Promise<void> {
    await context.batch.logMessage(
        'error',
        context.localize(
            '@sage/xtrem-ap-automation/uploaded_purchasing_documents_upload_documents_failed_scan',
            'Document has failed the scan.',
        ),
    );

    await context.notifyUser({
        title: context.localize('@sage/xtrem-ap-automation/success_notification_title_scan_failed', 'Document upload'),
        description: context.localize(
            '@sage/xtrem-ap-automation/success_notification_description_scan_failed',
            `Document could not be scanned: {{documentName}}. Review the document and try again.`,
            {
                documentName: uploadedDocumentName,
            },
        ),
        icon: 'cross',
        level: 'error',
        shouldDisplayToast: true,
        actions: [],
    });
}

export async function refreshDocument(
    context: Context,
    uploadedDocument: xtremApAutomation.nodes.UploadedPurchasingDocument,
    uploadedFile: xtremUpload.nodes.UploadedFile,
): Promise<void> {
    const documentUpdated = await context.read(xtremApAutomation.nodes.UploadedPurchasingDocument, {
        _id: uploadedDocument._id,
    });

    switch (await documentUpdated.processingStatus) {
        case 'completed':
            await xtremApAutomation.nodes.UploadedPurchasingDocument.updateDocumentWithDetails(
                context,
                documentUpdated,
            );
            await notifyUserDocumentReady(context, String(uploadedDocument._id), await uploadedFile.filename);
            break;
        case 'processing':
            await notifyUserDocumentProcessing(context, String(uploadedDocument._id), await uploadedFile.filename);
            break;

        case 'error':
            await notifyUserDocumentError(context, String(uploadedDocument._id), await uploadedFile.filename);
            break;

        default:
    }
}

function validateUploadedPurchasingDocumentDates(
    context: Context,
    dates: { supplierDocument: DateValue | null; posting: DateValue | null; due: DateValue | null },
): void {
    checkDocumentDatesNotNull(context, dates);
    compareDocumentDates(context, dates);
}

async function validateSupplierDocumentNumber(
    context: Context,
    supplier: xtremPurchasing.interfaces.DocumentForSupplierDocumentNumberValidation,
): Promise<void> {
    if (supplier.supplierId === 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/function__updated-purchasing-document__supplier_cannot_be_empty',
                'Supplier cannot be empty.',
            ),
        );
    }

    if (!supplier.supplierDocumentNumber || supplier.supplierDocumentNumber === '') {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/function__updated-purchasing-document__supplier_document_number_cannot_be_empty',
                'Supplier document number cannot be empty.',
            ),
        );
    }

    if (
        supplier.supplierId > 0 &&
        (await xtremApAutomation.nodes.UploadedPurchasingDocument.isSupplierDocumentNumberUsed(context, supplier))
    ) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__is_supplier_document_number_used',
                'Supplier document number already exists.',
            ),
        );
    }
}

function validateInvoiceType(
    context: Context,
    invoiceType: string | null,
    reason: xtremMasterData.nodes.ReasonCode | null,
): void {
    if (!invoiceType) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__invoice_type_cannot_be_empty',
                'Invoice type cannot be empty.',
            ),
        );
    }
    if (invoiceType === 'creditMemo' && !reason) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__reason_code_cannot_be_empty',
                'Reason code cannot be empty.',
            ),
        );
    }
}

function validateCurrency(context: Context, currency: xtremMasterData.nodes.Currency | null): void {
    if (!currency) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__currency_cannot_be_empty',
                'Currency cannot be empty.',
            ),
        );
    }
}

function validateAmounts(
    context: Context,
    totalAmounts: { includingTax: number; excludingTax: number; tax: number },
): void {
    if (totalAmounts.includingTax !== totalAmounts.excludingTax + totalAmounts.tax) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__total_amount_including_tax_must_be_equal_to_total_amount_excluding_tax_plus_total_tax_amount',
                'Total amount including tax needs to be equal to the total amount excluding tax plus the total tax amount.',
            ),
        );
    }
}

function validateSite(context: Context, site: xtremSystem.nodes.Site | null): void {
    if (!site) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__site_cannot_be_empty',
                'Financial site cannot be empty.',
            ),
        );
    }
}

/**
 * Check the the document status on Sage Ai and return a status object
 * @param context
 * @param documentId - uuid of the Uploaded Purchasing Document
 */
export async function validateCreatePurchaseDocumentFromUploadedPurchasingDocument(
    context: Context,
    uploadedPurchasingDocument: UploadedPurchasingDocument,
): Promise<void> {
    await validateSupplierDocumentNumber(context, {
        supplierId: (await uploadedPurchasingDocument.supplier)?._id ?? 0,
        supplierDocumentNumber: await uploadedPurchasingDocument.supplierDocumentNumber,
        nodeToQuery:
            (await uploadedPurchasingDocument.type) === 'creditMemo' ? 'PurchaseCreditMemo' : 'PurchaseInvoice',
    });

    validateUploadedPurchasingDocumentDates(context, {
        supplierDocument: await uploadedPurchasingDocument.supplierDocumentDate,
        posting: await uploadedPurchasingDocument.postingDate,
        due: await uploadedPurchasingDocument.dueDate,
    });
    validateSite(context, await uploadedPurchasingDocument.site);
    validateInvoiceType(context, await uploadedPurchasingDocument.type, await uploadedPurchasingDocument.reason);
    validateCurrency(context, await uploadedPurchasingDocument.currency);
    validateAmounts(context, {
        includingTax: await uploadedPurchasingDocument.totalAmountIncludingTax,
        excludingTax: await uploadedPurchasingDocument.totalAmountExcludingTax,
        tax: await uploadedPurchasingDocument.totalTaxAmount,
    });
}

/**
 * Notify the user that the document is ready for review
 * @param context
 * @param documents - Array of documents confirmed
 */
export async function notifyUserBulkConfirm(
    context: Context,
    documents: xtremApAutomation.interfaces.CreatePurchaseDocumentFromUploadedPurchasingDocumentResult[],
): Promise<void> {
    const batchAction: InitialNotificationAction = {
        link: `@sage/xtrem-communication/SysNotificationState/`,
        title: context.localize(
            '@sage/xtrem-ap-automation/fail__bulk_confirm_notification_description__view_link',
            'Batch task logs',
        ),
        icon: 'link',
        style: 'tertiary',
    };

    if (documents.length === 0) {
        await context.notifyUser({
            title: context.localize(
                '@sage/xtrem-ap-automation/success_notification__bulk_confirm_title_error',
                'Purchase document confirm',
            ),
            description: context.localize(
                '@sage/xtrem-ap-automation/notification_bulk_confirm__description_error',
                `The bulk confirm for all documents failed. Review batch task logs for more information.`,
            ),
            icon: 'cross',
            level: 'error',
            shouldDisplayToast: true,
            actions: [batchAction],
        });

        return;
    }

    const actions: InitialNotificationAction[] = [];

    const invoices = documents.filter(doc => doc.documentType === 'invoice').map(doc => doc.number);

    if (invoices.length > 0) {
        actions.push({
            link: xtremSystem.functions.linkToFilteredMainList<xtremPurchasing.nodes.PurchaseInvoice>(
                '@sage/xtrem-purchasing/PurchaseInvoice',
                {
                    number: { _in: invoices },
                },
            ),
            title: context.localize(
                '@sage/xtrem-ap-automation/success_notification_description__purchase_invoice_invoices_link',
                'Invoice',
            ),
            icon: 'link',
            style: 'tertiary',
        });
    }

    const creditMemos = documents.filter(doc => doc.documentType === 'creditMemo').map(doc => doc.number);

    if (creditMemos.length > 0) {
        actions.push({
            link: xtremSystem.functions.linkToFilteredMainList<xtremPurchasing.nodes.PurchaseCreditMemo>(
                '@sage/xtrem-purchasing/PurchaseCreditMemo',
                {
                    number: { _in: creditMemos },
                },
            ),
            title: context.localize(
                '@sage/xtrem-ap-automation/success_notification_description__purchase_credit_memo_invoices_link',
                'Credit Memo',
            ),
            icon: 'link',
            style: 'tertiary',
        });
    }

    actions.push(batchAction);

    await context.notifyUser({
        title: context.localize(
            '@sage/xtrem-ap-automation/success_notification__bulk_confirm_title_success',
            'Purchase document confirm',
        ),
        description: context.localize(
            '@sage/xtrem-ap-automation/notification_bulk_confirm__description_success',
            `Purchasing documents confirmed.`,
        ),
        icon: 'tick',
        level: 'success',
        shouldDisplayToast: true,
        actions: [...actions],
    });
}

function queryUploadedPurchasingDocumentByUploadedFile(
    context: Context,
    uploadedFile: number,
): Promise<UploadedPurchasingDocument[]> {
    return context.query(xtremApAutomation.nodes.UploadedPurchasingDocument, { filter: { uploadedFile } }).toArray();
}

export async function uploadedPurchasingDocumentByUploadedFile(
    context: Context,
    uploadedFile: number,
): Promise<UploadedPurchasingDocument | undefined> {
    const uploadedDocuments = await queryUploadedPurchasingDocumentByUploadedFile(context, uploadedFile);
    if (uploadedDocuments.length > 1) {
        throw new Error(`Multiple documents cannot have same upload file.`);
    }
    return uploadedDocuments.at(0);
}

export const allowedMimeTypes = [
    'application/pdf',
    'image/jpg',
    'image/jpeg',
    'image/tiff',
    'image/tif',
    'image/png',
    'image/heic',
    'image/heif',
    'application/xml',
    'text/xml',
];

export async function importDocuments(
    context: Context,
    manager: xtremApAutomation.classes.UploadedDocumentManager,
    isBatchTask = false,
): Promise<number> {
    const apConfiguration = await xtremApAutomation.functions.getDefaultConfiguration(context);

    if (!apConfiguration) {
        if (isBatchTask) {
            await context.batch.logMessage(
                'info',
                context.localize(
                    '@sage/xtrem-ap-automation/uploaded_document_manager_no_configuration_active_error',
                    'No configuration active.',
                ),
            );
            return 0;
        }
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-ap-automation/uploaded_document_manager_no_configuration_active_error',
                'No configuration active.',
            ),
        );
    }

    let importedDocuments = 0;
    if ((await apConfiguration.configurationLevel) === 'tenant') {
        importedDocuments = await manager.importNewDocuments({
            customerUniqueId: await apConfiguration.customerUniqueId,
            isBatchTask,
        });
    } else {
        const tenantInfo = await xtremApAutomation.functions.getTenantInformationByContext(context);

        await asyncArray(await apConfiguration.companies.toArray())
            .filter(
                async companyConfiguration =>
                    (await companyConfiguration.customerUniqueId) !== '' &&
                    (await companyConfiguration.originalTenantId) === tenantInfo.id,
            )
            .forEach(async apCompany => {
                importedDocuments += await manager.importNewDocuments({
                    customerUniqueId: await apCompany.customerUniqueId,
                    isBatchTask,
                    companyName: await (await apCompany.company).name,
                });
            });
    }

    if (isBatchTask) {
        await context.batch.updateProgress({
            detail: 'complete',
            phase: 'done',
        });
    }

    return importedDocuments;
}
