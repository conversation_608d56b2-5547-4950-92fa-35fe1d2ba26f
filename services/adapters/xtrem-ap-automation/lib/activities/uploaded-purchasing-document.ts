import { Activity } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import { ApAutomationConfiguration } from '../nodes/ap-automation-configuration';
import { UploadedPurchasingDocument } from '../nodes/uploaded-purchasing-document';

export const uploadedPurchasingDocument = new Activity({
    description: 'Uploaded purchasing document',
    node: () => UploadedPurchasingDocument,
    __filename,
    permissions: ['manage'],
    operationGrants: {
        manage: [
            {
                operations: [
                    'create',
                    'update',
                    'syncDocuments',
                    'updateDocumentWithDetails',
                    'syncDocumentsTask',
                    'createPurchaseDocumentFromUploadedPurchasingDocument',
                    'isSupplierDocumentNumberUsed',
                    'confirmBulk',
                    'uploadDocument',
                    'resetUploadedDocument',
                ],
                on: [() => UploadedPurchasingDocument],
            },
            {
                operations: ['getTenantInformation'],
                on: [() => xtremSystem.nodes.SysTenant],
            },
            {
                operations: ['lookup'],
                on: [() => ApAutomationConfiguration],
            },
            {
                operations: ['create', 'update'],
                on: [() => xtremUpload.nodes.UploadedFile],
            },
        ],
    },
});
