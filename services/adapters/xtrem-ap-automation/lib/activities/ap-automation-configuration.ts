import { Activity } from '@sage/xtrem-core';
import { ApAutomationConfiguration } from '../nodes/ap-automation-configuration';

export const apAutomationConfiguration = new Activity({
    description: 'AP configuration',
    node: () => ApAutomationConfiguration,
    __filename,
    permissions: ['manage'],
    operationGrants: {
        manage: [
            {
                operations: [
                    'read',
                    'update',
                    'resetConfiguration',
                    'isConfigurationActive',
                    'createOrGetDefaultRecord',
                ],
                on: [() => ApAutomationConfiguration],
            },
        ],
    },
});
