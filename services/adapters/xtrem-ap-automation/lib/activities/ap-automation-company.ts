import { Activity } from '@sage/xtrem-core';
import { ApAutomationCompany } from '../nodes/ap-automation-company';

export const apAutomationCompany = new Activity({
    description: 'AP configuration company',
    node: () => ApAutomationCompany,
    __filename,
    permissions: ['manage'],
    operationGrants: {
        manage: [
            {
                operations: ['read', 'update', 'registerCompanyOnSageAi'],
                on: [() => ApAutomationCompany],
            },
        ],
    },
});
