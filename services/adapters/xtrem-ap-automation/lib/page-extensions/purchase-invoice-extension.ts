import type { GraphApi, UploadedPurchasingDocument } from '@sage/xtrem-ap-automation-api';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { PurchaseInvoice } from '@sage/xtrem-purchasing/build/lib/pages/purchase-invoice';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { mapSupplierDocumentNumber } from '../client-functions/uploaded-purchasing-document';

@ui.decorators.pageExtension<PurchaseInvoiceExtension>({
    extends: '@sage/xtrem-purchasing/PurchaseInvoice',
    onLoad() {
        this.uploadedDocumentLink.value = mapSupplierDocumentNumber(this);
    },
})
export class PurchaseInvoiceExtension extends ui.PageExtension<PurchaseInvoice, GraphApi> {
    @ui.decorators.referenceField<PurchaseInvoiceExtension>({
        parent() {
            return this.informationBlock;
        },
        title: 'Uploaded document',

        isHidden: true,
    })
    uploadedDocument: ui.fields.Reference<UploadedPurchasingDocument>;

    @ui.decorators.linkField<PurchaseInvoiceExtension>({
        parent() {
            return this.informationBlock;
        },
        title: 'Uploaded document',
        isTransient: true,
        async onClick() {
            await this.$.dialog.page(
                '@sage/xtrem-ap-automation/UploadedPurchasingDocument',
                { _id: this.uploadedDocument?.value?._id ?? '' },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
        map() {
            return mapSupplierDocumentNumber(this);
        },
        isHidden() {
            return !this.uploadedDocument?.value;
        },
    })
    uploadedDocumentLink: ui.fields.Link<PurchaseInvoiceExtension>;

    @ui.decorators.referenceFieldOverride<PurchaseInvoiceExtension, Site>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceFieldOverride<PurchaseInvoiceExtension, Supplier>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    billBySupplier: ui.fields.Reference<Supplier>;

    @ui.decorators.textFieldOverride<PurchaseInvoiceExtension>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    supplierDocumentNumber: ui.fields.Text;

    @ui.decorators.fileFieldOverride<PurchaseInvoiceExtension>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    pdfSupplierInvoice: ui.fields.File;

    @ui.decorators.dateFieldOverride<PurchaseInvoiceExtension>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    supplierDocumentDate: ui.fields.Date;

    @ui.decorators.numericFieldOverride<PurchaseInvoiceExtension>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    totalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericFieldOverride<PurchaseInvoiceExtension>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    totalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericFieldOverride<PurchaseInvoiceExtension>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    totalAmountIncludingTax: ui.fields.Numeric;
}

declare module '@sage/xtrem-purchasing/build/lib/pages/purchase-invoice' {
    export interface PurchaseInvoice extends PurchaseInvoiceExtension {}
}
