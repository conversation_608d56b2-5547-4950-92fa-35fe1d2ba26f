import type { GraphApi, UploadedPurchasingDocument } from '@sage/xtrem-ap-automation-api';
import type { ReasonCode, Supplier } from '@sage/xtrem-master-data-api';
import type { PurchaseCreditMemo } from '@sage/xtrem-purchasing/build/lib/pages/purchase-credit-memo';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { mapSupplierDocumentNumber } from '../client-functions/uploaded-purchasing-document';

@ui.decorators.pageExtension<PurchaseCreditMemoExtension>({
    extends: '@sage/xtrem-purchasing/PurchaseCreditMemo',
    onLoad() {
        this.uploadedDocumentLink.value = mapSupplierDocumentNumber(this);
    },
})
export class PurchaseCreditMemoExtension extends ui.PageExtension<PurchaseCreditMemo, GraphApi> {
    @ui.decorators.referenceField<PurchaseCreditMemoExtension>({
        parent() {
            return this.informationBlock;
        },
        title: 'Uploaded document',
        isHidden: true,
    })
    uploadedDocument: ui.fields.Reference<UploadedPurchasingDocument>;

    @ui.decorators.linkField<PurchaseCreditMemoExtension>({
        parent() {
            return this.informationBlock;
        },
        title: 'Uploaded document',
        isTransient: true,
        async onClick() {
            await this.$.dialog.page(
                '@sage/xtrem-ap-automation/UploadedPurchasingDocument',
                { _id: this.uploadedDocument?.value?._id ?? '' },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
        map() {
            return mapSupplierDocumentNumber(this);
        },
        isHidden() {
            return !this.uploadedDocument?.value;
        },
    })
    uploadedDocumentLink: ui.fields.Link<PurchaseCreditMemoExtension>;

    @ui.decorators.referenceFieldOverride<PurchaseCreditMemoExtension, Site>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceFieldOverride<PurchaseCreditMemoExtension, Supplier>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    billBySupplier: ui.fields.Reference<Supplier>;

    @ui.decorators.textFieldOverride<PurchaseCreditMemoExtension>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    supplierDocumentNumber: ui.fields.Text;

    @ui.decorators.dateFieldOverride<PurchaseCreditMemoExtension>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    supplierDocumentDate: ui.fields.Date;

    @ui.decorators.numericFieldOverride<PurchaseCreditMemoExtension>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    totalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericFieldOverride<PurchaseCreditMemoExtension>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    totalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericFieldOverride<PurchaseCreditMemoExtension>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    totalAmountIncludingTax: ui.fields.Numeric;

    @ui.decorators.referenceFieldOverride<PurchaseCreditMemoExtension, ReasonCode>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    reason: ui.fields.Reference<ReasonCode>;

    @ui.decorators.fileFieldOverride<PurchaseCreditMemoExtension>({
        isReadOnly() {
            return this.uploadedDocument.value !== null;
        },
    })
    pdfSupplierCreditMemo: ui.fields.File;
}

declare module '@sage/xtrem-purchasing/build/lib/pages/purchase-credit-memo' {
    export interface PurchaseCreditMemo extends PurchaseCreditMemoExtension {}
}
