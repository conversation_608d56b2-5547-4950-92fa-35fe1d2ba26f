{"@sage/xtrem-ap-automation/activity__ap_automation_company__name": "Société AP Automation", "@sage/xtrem-ap-automation/activity__ap_automation_configuration__name": "Configuration AP Automation", "@sage/xtrem-ap-automation/activity__uploaded_purchasing_document__name": "Document d'achat téléchargé", "@sage/xtrem-ap-automation/classes__updated-document-manager__document_type_not_supported": "Type de document non pris en charge : {{documentType}}", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__cancel_button": "Annuler", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__dialog_message": "Détection d'un nouveau tenant. Enregistrer de nouveau la connexion ou annuler.", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__dialog_title": "Connexion à Sage AI", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__register_button": "Enregister", "@sage/xtrem-ap-automation/data_types__ap_automation_configuration_level_enum__name": "Enum niveau de configuration AP Automation", "@sage/xtrem-ap-automation/data_types__ap_automation_document_origin_enum__name": "Enum origine document AP Automation", "@sage/xtrem-ap-automation/data_types__ap_automation_document_type_enum__name": "Enum type document AP Automation", "@sage/xtrem-ap-automation/data_types__ap_automation_processing_status_enum__name": "Enum statut de traitement AP Automation", "@sage/xtrem-ap-automation/data_types__ap_automation_uploaded_document_status_enum__name": "Enum statut document téléchargé AP Automation", "@sage/xtrem-ap-automation/data_types__uploaded_purchasing_document__name": "Document d'achat téléchargé", "@sage/xtrem-ap-automation/enums__ap_automation_configuration_level__company": "Société", "@sage/xtrem-ap-automation/enums__ap_automation_configuration_level__tenant": "Tenant", "@sage/xtrem-ap-automation/enums__ap_automation_document_origin__email": "E-mail", "@sage/xtrem-ap-automation/enums__ap_automation_document_origin__uploaded": "Télécharg<PERSON>", "@sage/xtrem-ap-automation/enums__ap_automation_document_type__creditMemo": "Avoir", "@sage/xtrem-ap-automation/enums__ap_automation_document_type__invoice": "Facture", "@sage/xtrem-ap-automation/enums__ap_automation_processing_status__completed": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/enums__ap_automation_processing_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/enums__ap_automation_processing_status__processing": "Traitement", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__draft": "Brouillon", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__reviewDone": "Vérification effectuée", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__reviewInProgress": "Vérification en cours", "@sage/xtrem-ap-automation/events__updated-purchasing-document__customer_unique_id_is_not_valid_companies": "Le code client unique ne correspond à aucune configuration de société.", "@sage/xtrem-ap-automation/events__updated-purchasing-document__customer_unique_id_is_not_valid_tenant": "Le code client unique ne correspond à aucune configuration de tenant.", "@sage/xtrem-ap-automation/events__updated-purchasing-document__no_default_configuration_found": "Configuration par défaut introuvable.", "@sage/xtrem-ap-automation/fail__bulk_confirm_notification_description__view_link": "Traces de tâches batch", "@sage/xtrem-ap-automation/function__updated-purchasing-document__due_date_cannot_be_before_supplier_document_date": "La date d'échéance ne peut pas être antérieure à la date du document fournisseur.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__posting_date_cannot_be_before_supplier_document_date": "La date de comptabilisation ne peut pas être antérieure à la date du document fournisseur.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__posting_date_cannot_be_empty": "La date de comptabilisation ne peut pas être vide.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__supplier_cannot_be_empty": "Le fournis<PERSON>ur ne peut pas être vide.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__supplier_document_number_cannot_be_empty": "Le numéro de document fournisseur ne peut pas être vide.", "@sage/xtrem-ap-automation/functions__common__cannot_register_company_tenant_configuration_is_active": "Vous ne pouvez pas enregistrer une société auprès de Sage AI tant que la configuration du tenant est active.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__currency_cannot_be_empty": "La devise ne peut pas être vide.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__invoice_type_cannot_be_empty": "Le type de facture ne peut pas être vide.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__is_supplier_document_number_used": "Le numéro de document fournisseur existe déjà.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__purchase_document_already_created": "Un document d'achat a déjà été créé.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__purchase_document_status_review_done": "Le document d'achat est en statut Revue effectuée.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__reason_code_cannot_be_empty": "Le code motif ne peut pas être vide.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__site_cannot_be_empty": "Le site financier ne peut pas être vide.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__total_amount_including_tax_must_be_equal_to_total_amount_excluding_tax_plus_total_tax_amount": "Le montant total TTC doit être égal au montant total HT plus le montant de taxe total.", "@sage/xtrem-ap-automation/mime_type_not_allowed": "Vous ne pouvez pas utiliser ce type de fichier.", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__billBySupplier": "Fournisseur facturant", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__currency": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__pdfSupplierCreditMemo": "PDF de l'avoir fournisseur", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__reason": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__site": "Site", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__supplierDocumentDate": "Date du document fournisseur", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__supplierDocumentNumber": "Numéro du document fournisseur", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__totalAmountExcludingTax": "Montant total HT", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__totalTaxAmount": "Montant total de taxes", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__uploadedDocument": "Document téléchargé", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__billBySupplier": "Fournisseur facturant", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__currency": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__pdfSupplierInvoice": "PDF de la facture fournisseur", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__site": "Site", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__supplierDocumentDate": "Date du document fournisseur", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__supplierDocumentNumber": "Numéro du document fournisseur", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__totalAmountExcludingTax": "Montant total HT", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__totalTaxAmount": "Montant total de taxes", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__uploadedDocument": "Document téléchargé", "@sage/xtrem-ap-automation/nodes__ap_automation_company__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-ap-automation/nodes__ap_automation_company__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-ap-automation/nodes__ap_automation_company__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-ap-automation/nodes__ap_automation_company__mutation__registerCompanyOnSageAi": "Enregistrer la société sur Sage AI", "@sage/xtrem-ap-automation/nodes__ap_automation_company__mutation__registerCompanyOnSageAi__failed": "Échec d'enregistrement de la société sur Sage AI.", "@sage/xtrem-ap-automation/nodes__ap_automation_company__mutation__registerCompanyOnSageAi__parameter__company": "Société", "@sage/xtrem-ap-automation/nodes__ap_automation_company__node_name": "Société AP Automation", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__accountsPayableEmail": "E-mail factures comptables fournisseurs", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__company": "Société", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__configuration": "Configuration", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__customerUniqueId": "Code client unique", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__employeeExpenseEmail": "E-mail dépenses collaborateurs", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__isInboxActive": "Boite de réception active", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__originalTenantId": "ID du tenant d'origine", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__country_is_mandatory": "Le pays est obligatoire.", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__createOrGetDefaultRecord": "<PERSON><PERSON><PERSON> ou récupérer fiche par défaut", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__createOrGetDefaultRecord__failed": "Échec de création ou de récupération de l'enregistrement par défaut.", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__resetConfiguration": "Réinitialiser configuration", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__resetConfiguration__failed": "Échec de réinitialisation de configuration.", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__node_name": "Configuration AP Automation", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__accountsPayableEmail": "E-mail factures comptables fournisseurs", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__companies": "Sociétés", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__configurationLevel": "Niveau de configuration", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__country": "Pays", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__customerUniqueId": "Code client unique", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__employeeExpenseEmail": "E-mail dépenses collaborateurs", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__id": "Code", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__isActive": "Active", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__isInboxActive": "Boite de réception active", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__originalTenantId": "ID du tenant d'origine", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__query__isConfigurationActive": "Configuration active", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__query__isConfigurationActive__failed": "Échec de configuration active.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__syncDocumentsTask": "Tâche documents synchro", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__syncDocumentsTask__failed": "Échec de synchro des tâches documents.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__uploadDocument": "Télécharger document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__uploadDocument__failed": "Échec de la réinitialisation des documents du tenant.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__uploadDocument__parameter__uploadedPurchasingDocument": "Document d'achat téléchargé", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__bulkMutation__confirmBulk": "Confirmer masse", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__bulkMutation__confirmBulk__failed": "Échec de confirmation de masse.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__cannot_delete_record": "Vous ne pouvez pas supprimer un document d'achat téléchargé en cours de traitement ou de vérification.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__cannot_delete_record_customer_id_inactive_nonexistent": "Votre code client n'est pas renseigné ou est inactif.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromPendingDocument": "<PERSON><PERSON><PERSON> le document d'achat à partir du document en attente", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromPendingDocument__parameter__uploadedPurchasingDocument": "Document d'achat téléchargé", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromUploadedPurchasingDocument": "<PERSON><PERSON><PERSON> le document d'achat à partir du document d'achat téléchargé", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromUploadedPurchasingDocument__failed": "Échec de création du document d'achat à partir du document d'achat téléchargé.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromUploadedPurchasingDocument__parameter__uploadedPurchasingDocument": "Document d'achat téléchargé", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__resetUploadedDocument": "Réinitialiser le document téléchargé", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__resetUploadedDocument__failed": "Échec de la réinitialisation des documents du tenant.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__resetUploadedDocument__parameter__uploadedPurchasingDocument": "Document d'achat téléchargé", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__syncDocuments": "Documents de synchro", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__syncDocuments__failed": "Échec de synchronisation de documents.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__updateDocumentWithDetails": "Mettre le document à jour avec des détails", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__updateDocumentWithDetails__failed": "Échec de la mise à jour du document avec des détails.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__updateDocumentWithDetails__parameter__document": "Document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__node_name": "Document d'achat téléchargé", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__apAutomationCompany": "Société AP Automation", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__currency": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__customerUniqueId": "Code client unique", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__document": "Document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__documentMimeType": "Document de type MIME", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__dueDate": "Date d'échéance", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__isResultPopulated": "Résultat alimenté", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__isReviewDone": "Vérification effectuée", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__isTempUuid": "Est un UUID temporaire", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__linkedSites": "Sites liés", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__origin": "Origine", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__postingDate": "Date de comptabilisation", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__processingDetails": "Détails de traitement", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__processingStatus": "Statut de traitement", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__purchaseCreditMemo": "Avoir d'achat", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__purchaseInvoice": "Facture d'achat", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__reason": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__recipientEmail": "E-mail destinataire", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__result": "Résultat", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__senderEmail": "E-mail expéditeur", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__site": "Site", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__status": "Statut", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__supplier": "Fournisseur", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__supplierDocumentDate": "Date du document fournisseur", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__supplierDocumentNumber": "Numéro du document fournisseur", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__totalAmountExcludingTax": "Montant total HT", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__totalAmountIncludingTax": "Montant total TTC", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__totalTaxAmount": "Montant total de taxes", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__type": "Type", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__uploadedFile": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__uuid": "UUID", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__query__isSupplierDocumentNumberUsed": "Numéro du document fournisseur  utilisé", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__query__isSupplierDocumentNumberUsed__failed": "Échec d'utilisation de numéro de document fournisseur.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__query__isSupplierDocumentNumberUsed__parameter__supplier": "Fournisseur", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__node_name": "Ligne de document d'achat téléchargée", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__amountExcludingTax": "Montant HT", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__amountIncludingTax": "Montant TTC", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__item": "Article", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__itemDescription": "Description de l'article", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__lineAmountExcludingTax": "Montant ligne HT", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__lineAmountIncludingTax": "Montant ligne TTC", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__parent": "Parent", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__purchaseUnit": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__quantity": "Quantité", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__taxAmount": "Montant de taxe", "@sage/xtrem-ap-automation/notification_bulk_confirm__description_error": "La confirmation de masse pour tous les documents a échoué. Vérifiez les traces de tâches batch pour plus d'informations.", "@sage/xtrem-ap-automation/notification_bulk_confirm__description_success": "Documents d'achat confirmés", "@sage/xtrem-ap-automation/package__name": "Sage DMO AP Automation", "@sage/xtrem-ap-automation/page-extensions__purchase_credit_memo_extension__uploadedDocument____title": "Document téléchargé", "@sage/xtrem-ap-automation/page-extensions__purchase_credit_memo_extension__uploadedDocumentLink____title": "Document téléchargé", "@sage/xtrem-ap-automation/page-extensions__purchase_invoice_extension__uploadedDocument____title": "Document téléchargé", "@sage/xtrem-ap-automation/page-extensions__purchase_invoice_extension__uploadedDocumentLink____title": "Document téléchargé", "@sage/xtrem-ap-automation/pages__ap_automation_configuration____title": "Configuration AP Automation", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__accountsPayableEmail____title": "Adresse e-mail factures comptables fournisseurs en entrée", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__accountsPayableEmail": "Adresse e-mail factures comptables fournisseurs en entrée", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__company__id": "Code", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__company__name": "Nom", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__customerUniqueId": "Code client", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companiesBlock____title": "Sociétés", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__configurationLevel____title": "Niveau de configuration", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__customerUniqueId____title": "Code client", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__mainSection____title": "Général", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__registerNewCompany____title": "Inscrire nouvelle société", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__save____title": "Enregistrer", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__tenantBlock____title": "Tenant", "@sage/xtrem-ap-automation/pages__confirm_uploaded_purchasing_document____title": "Con<PERSON>rm<PERSON> et créer", "@sage/xtrem-ap-automation/pages__confirm_uploaded_purchasing_document__confirmAndOpen____title": "Confirmer et afficher", "@sage/xtrem-ap-automation/pages__confirm_uploaded_purchasing_document__confirmAndUpload____title": "Confirmer et télécharger nouveau", "@sage/xtrem-ap-automation/pages__import_purchase_document____title": "Télécharger les documents d'achat", "@sage/xtrem-ap-automation/pages__import_purchase_document__company____title": "Société", "@sage/xtrem-ap-automation/pages__import_purchase_document__file_type_not_supported": "Type de fichier non pris en charge : {{mimeType}}. Types de fichier pris en charge : PDF, JPG, JPEG, TIF, TIFF, PNG, HEIC, HEIF, et XML.", "@sage/xtrem-ap-automation/pages__import_purchase_document__scan____title": "Analyser", "@sage/xtrem-ap-automation/pages__import_purchase_document__uploadedFile____title": "Document financier", "@sage/xtrem-ap-automation/pages__import_purchase_document__uploadSection____title": "Télécharger", "@sage/xtrem-ap-automation/pages__register_new_company____title": "Inscrire nouvelle société", "@sage/xtrem-ap-automation/pages__register_new_company__company____columns__title__id": "Code", "@sage/xtrem-ap-automation/pages__register_new_company__company____columns__title__name": "Nom", "@sage/xtrem-ap-automation/pages__register_new_company__company____title": "Société", "@sage/xtrem-ap-automation/pages__register_new_company__register____title": "Enregister", "@sage/xtrem-ap-automation/pages__technical_info____title": "Information technique", "@sage/xtrem-ap-automation/pages__technical_info__technicalSection____title": "Information technique", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__bulkActions__title": "Confirmer", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__created__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__currencyId__title": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__line2__title": "Fournisseur", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__recipientEmail__title": "E-mail destinataire", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__senderEmail__title": "E-mail expéditeur", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__siteId__title": "Site financier", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__supplierId__title": "Code fournisseur", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__totalAmountExcludingTax__title": "Total HT", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__totalAmountIncludingTax__title": "Total TTC", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__totalTaxAmount__title": "Taxe", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__type__title": "Type de facture", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__updated__title": "Mis à jour", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title": "Tous les statuts ouverts", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__2": "Tous les statuts", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__3": "Brouillon", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__4": "Vérification en cours", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__5": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__6": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__7": "Non traité", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____objectTypePlural": "Documents d'achat téléchargés", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____objectTypeSingular": "Document d'achat téléchargé", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____title": "Documents d'achat téléchargés", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__confirm____title": "Confirmer", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____columns__title__id": "Code ISO 4217", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____columns__title__symbol": "Symbole", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____placeholder": "Sélectionner la devise", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____title": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displaySite____title": "Site financier", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displaySupplier____title": "Fournisseur", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displaySupplierDocumentNumber____title": "Numéro du document fournisseur", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displayType____title": "Type", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__document____title": "Document téléchargé", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__documentNumberLink____title": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__dueDate____title": "Date d'échéance", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__headerSection____title": "<PERSON>-tête", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__image____title": "Image", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__is_supplier_document_number_used_warn": "Le numéro de document fournisseur existe déjà.", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__is_supplier_document_number_used_warn_title": "Numéro du document fournisseur", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__columns__item__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__columns__purchaseUnit__symbol__title": "Nom", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__columns__purchaseUnit__symbol__title__2": "Symbole", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__lookupDialogTitle__item": "Sélectionner l'article", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__lookupDialogTitle__purchaseUnit__symbol": "Sélectionner l'unité", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__amountExcludingTax": "Total HT", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__amountIncludingTax": "Total TTC", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__item": "Nom article", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__item__id": "Code article", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__item__image": "Image", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__itemDescription": "Description article", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__lineAmountExcludingTax": "Total HT", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__lineAmountIncludingTax": "Total TTC", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__purchaseUnit__symbol": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__quantity": "Qté UA", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__taxAmount": "Mnt taxe", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__matchingSection____title": "Rapprochement", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__no_configuration_active_info": "V<PERSON> devez d'abord enregistrer les tenants ou les sociétés à partir de la page de configuration de AP Automation.", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__no_configuration_active_info_title": "Configuration AP Automation", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__pdfView____title": "Document téléchargé", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__postingDate____title": "Date de comptabilisation", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__reason____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> le motif", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__resetAll____title": "Réinitialiser tout", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__save____title": "Enregistrer", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____columns__title__id": "Code ", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____columns__title__name": "Nom", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____lookupDialogTitle": "Sélectionner le site financier", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____title": "Site financier", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__status____title": "Statut", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____columns__title__businessEntity__country__name": "Pays", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____columns__title__businessEntity__name": "Nom", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____columns__title__businessEntity__name__2": "Code", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____lookupDialogTitle": "Sélectionner le fournisseur", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____title": "Fournisseur", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplierDocumentDate____title": "Date du document fournisseur", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplierDocumentNumber____title": "Numéro du document fournisseur", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__syncDocuments____title": "Synchro e-mails", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__technicalInfo____title": "Information technique", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__totalAmountExcludingTax____title": "Montant total fournisseur HT", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__totalAmountIncludingTax____title": "Montant total fournisseur TTC", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__totalTaxAmount____title": "Taxe total fournisseur", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__uploadDocument____title": "Télécharger un document", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__warningMessage____content": "Contrôlez que les valeurs analysées sont correctes avant de confirmer.", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__site_warning": "Ce site n'appartient pas à la même société que le paramétrage de votre configuration.", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_draft": "Brouillon", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_processing": "Traitement", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_review_done": "Vérification effectuée", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_review_in_progress": "Vérification en cours", "@sage/xtrem-ap-automation/permission__manage__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/service_options__ap_automation_option__name": "Option AP Automation", "@sage/xtrem-ap-automation/success_notification__bulk_confirm_title_error": "Confirmation document d'achat", "@sage/xtrem-ap-automation/success_notification__bulk_confirm_title_success": "Confirmation document d'achat", "@sage/xtrem-ap-automation/success_notification_description": "Document téléchargé et prêt à être vérifié : {{documentName}}.", "@sage/xtrem-ap-automation/success_notification_description__purchase_credit_memo_invoices_link": "Avoir", "@sage/xtrem-ap-automation/success_notification_description__purchase_invoice_invoices_link": "Facture", "@sage/xtrem-ap-automation/success_notification_description__view_link": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/success_notification_description_error": "Le document a bien téléchargé mais <PERSON> AI n'a pas réussi à le traiter : {{documentName}}.", "@sage/xtrem-ap-automation/success_notification_description_processing": "Document téléchargé: <PERSON><PERSON><PERSON> de Sage AI en cours : {{documentName}}.", "@sage/xtrem-ap-automation/success_notification_description_scan_failed": "Ce document n'a pas pu être analysé : {{documentName}}. Vérifiez le document et réessayez.", "@sage/xtrem-ap-automation/success_notification_title": "Téléchargement document", "@sage/xtrem-ap-automation/success_notification_title_error": "Téléchargement document", "@sage/xtrem-ap-automation/success_notification_title_processing": "Téléchargement document", "@sage/xtrem-ap-automation/success_notification_title_scan_failed": "Téléchargement document", "@sage/xtrem-ap-automation/uploaded_document_manager_complete": "Téléchargement du document {{uuid}} effectué", "@sage/xtrem-ap-automation/uploaded_document_manager_log_error": "Erreur de traitement du document {{uuid}}. Message d'erreur : {{errorMessage}}", "@sage/xtrem-ap-automation/uploaded_document_manager_no_configuration_active": "Aucune configuration active.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_configuration_active_error": "Aucune configuration active.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_customer_id_found": "Aucun code client trouvé dans la configuration.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_documents_to_be_processed": "Aucun document à traiter.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_documents_to_be_processed_company": "Aucun document à traiter pour cette société : {{companyName}}.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents__sync_documents_task__end": "Synchronisation des documents effectuée.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents__sync_documents_task__start": "Synchronisation des documents démarrée.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents_process_start_notification.": "{{fileName}} est en cours de traitement. Vous recevrez une notification lorsque le traitement a été effectué.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents_upload_documents_failed_scan": "Le document n'a pas pu être analysé."}