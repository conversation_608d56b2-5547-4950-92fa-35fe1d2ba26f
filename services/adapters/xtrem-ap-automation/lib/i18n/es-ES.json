{"@sage/xtrem-ap-automation/activity__ap_automation_company__name": "AP Automation company", "@sage/xtrem-ap-automation/activity__ap_automation_configuration__name": "Configuración de automatización de cuentas por pagar", "@sage/xtrem-ap-automation/activity__uploaded_purchasing_document__name": "Documento de compra subido", "@sage/xtrem-ap-automation/classes__updated-document-manager__document_type_not_supported": "El tipo de documento {{documentType}} no es compatible.", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__cancel_button": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__dialog_message": "Se ha detectado una nueva instancia. ¿Quieres registrar la conexión de nuevo?", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__dialog_title": "Conectar a Sage Ai", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__register_button": "Registrar", "@sage/xtrem-ap-automation/data_types__ap_automation_configuration_level_enum__name": "AP Automation configuration level enum", "@sage/xtrem-ap-automation/data_types__ap_automation_document_origin_enum__name": "AP Automation document origin enum", "@sage/xtrem-ap-automation/data_types__ap_automation_document_type_enum__name": "Allocation document type enum", "@sage/xtrem-ap-automation/data_types__ap_automation_processing_status_enum__name": "AP Automation processing status enum", "@sage/xtrem-ap-automation/data_types__ap_automation_uploaded_document_status_enum__name": "AP Automation uploaded document status enum", "@sage/xtrem-ap-automation/data_types__uploaded_purchasing_document__name": "Uploaded purchasing document", "@sage/xtrem-ap-automation/enums__ap_automation_configuration_level__company": "Sociedad", "@sage/xtrem-ap-automation/enums__ap_automation_configuration_level__tenant": "Instancia", "@sage/xtrem-ap-automation/enums__ap_automation_document_origin__email": "E-mail", "@sage/xtrem-ap-automation/enums__ap_automation_document_origin__uploaded": "Subido", "@sage/xtrem-ap-automation/enums__ap_automation_document_type__creditMemo": "Factura rectificativa", "@sage/xtrem-ap-automation/enums__ap_automation_document_type__invoice": "Factura", "@sage/xtrem-ap-automation/enums__ap_automation_processing_status__completed": "Finalizado", "@sage/xtrem-ap-automation/enums__ap_automation_processing_status__error": "Error", "@sage/xtrem-ap-automation/enums__ap_automation_processing_status__processing": "En curso", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__draft": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__error": "Error", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__reviewDone": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__reviewInProgress": "En revisión", "@sage/xtrem-ap-automation/events__updated-purchasing-document__customer_unique_id_is_not_valid_companies": "El id. único del cliente no coincide con ninguna configuración de sociedad.", "@sage/xtrem-ap-automation/events__updated-purchasing-document__customer_unique_id_is_not_valid_tenant": "El id. único del cliente no coincide con la configuración de la instancia.", "@sage/xtrem-ap-automation/events__updated-purchasing-document__no_default_configuration_found": "No se ha encontrado ninguna configuración por defecto.", "@sage/xtrem-ap-automation/fail__bulk_confirm_notification_description__view_link": "Trazas de tareas por lotes", "@sage/xtrem-ap-automation/function__updated-purchasing-document__due_date_cannot_be_before_supplier_document_date": "La fecha de vencimiento no puede ser anterior a la del documento de proveedor.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__posting_date_cannot_be_before_supplier_document_date": "La fecha de contabilización no puede ser anterior a la del documento de proveedor.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__posting_date_cannot_be_empty": "La fecha de contabilización es obligatoria.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__supplier_cannot_be_empty": "El proveedor es obligatorio.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__supplier_document_number_cannot_be_empty": "El número de documento de proveedor es obligatorio.", "@sage/xtrem-ap-automation/functions__common__cannot_register_company_tenant_configuration_is_active": "No puedes registrar una sociedad con Sage Ai si la configuración de la instancia está activa.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__currency_cannot_be_empty": "La divisa es obligatoria.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__invoice_type_cannot_be_empty": "El tipo de factura es obligatorio.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__is_supplier_document_number_used": "Este número de documento de proveedor ya existe.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__purchase_document_already_created": "El documento de compra ya está creado.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__purchase_document_status_review_done": "El documento de compra se ha revisado.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__reason_code_cannot_be_empty": "El motivo es obligatorio.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__site_cannot_be_empty": "La planta financiera es obligatoria.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__total_amount_including_tax_must_be_equal_to_total_amount_excluding_tax_plus_total_tax_amount": "El importe total con impuestos debe equivaler a la suma del importe total sin impuestos y los impuestos.", "@sage/xtrem-ap-automation/mime_type_not_allowed": "No puedes utilizar este tipo de archivo.", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__billBySupplier": "Proveedor <PERSON>dor", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__currency": "Divisa", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__pdfSupplierCreditMemo": "Factura rectificativa de proveedor en PDF", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__reason": "Motivo", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__site": "Planta", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__supplierDocumentDate": "<PERSON><PERSON> de documento de proveedor", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__supplierDocumentNumber": "Número de documento de proveedor", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__totalAmountExcludingTax": "Total sin impuestos", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__totalTaxAmount": "Impuestos", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__uploadedDocument": "Documento subido", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__billBySupplier": "Proveedor <PERSON>dor", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__currency": "Divisa", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__pdfSupplierInvoice": "Factura de proveedor en PDF", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__site": "Planta", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__supplierDocumentDate": "<PERSON><PERSON> de documento de proveedor", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__supplierDocumentNumber": "Número de documento de proveedor", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__totalAmountExcludingTax": "Total sin impuestos", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__totalTaxAmount": "Impuestos", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__uploadedDocument": "Documento subido", "@sage/xtrem-ap-automation/nodes__ap_automation_company__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-ap-automation/nodes__ap_automation_company__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-ap-automation/nodes__ap_automation_company__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-ap-automation/nodes__ap_automation_company__mutation__registerCompanyOnSageAi": "Registrar sociedad con Sage Ai", "@sage/xtrem-ap-automation/nodes__ap_automation_company__mutation__registerCompanyOnSageAi__failed": "Error al registrar la sociedad con Sage Ai", "@sage/xtrem-ap-automation/nodes__ap_automation_company__mutation__registerCompanyOnSageAi__parameter__company": "Sociedad", "@sage/xtrem-ap-automation/nodes__ap_automation_company__node_name": "AP Automation company", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__accountsPayableEmail": "E-mail de documento de proveedor", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__company": "Sociedad", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__configuration": "Configuración", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__customerUniqueId": "Id. único de cliente", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__employeeExpenseEmail": "Employee expense email", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__isInboxActive": "Bandeja de entrada activa", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__originalTenantId": "Id. de instancia original", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__country_is_mandatory": "Introduce el país.", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__createOrGetDefaultRecord": "Crear u obtener registro por defecto", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__createOrGetDefaultRecord__failed": "Error al crear u obtener el registro por defecto", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__resetConfiguration": "Restablecer configuración", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__resetConfiguration__failed": "Error al restablecer la configuración", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__node_name": "Configuración de automatización de cuentas por pagar", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__accountsPayableEmail": "E-mail de documento de proveedor", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__companies": "Sociedades", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__configurationLevel": "Nivel de configuración", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__country": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__customerUniqueId": "Id. único de cliente", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__employeeExpenseEmail": "Employee expense email", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__id": "Id.", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__isActive": "Activa", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__isInboxActive": "Bandeja de entrada activa", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__originalTenantId": "Id. de instancia original", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__query__isConfigurationActive": "Configuración activa", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__query__isConfigurationActive__failed": "Error de configuración activa", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__syncDocumentsTask": "Sincronizar tarea de documentos", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__syncDocumentsTask__failed": "Error al sincronizar la tarea de los documentos", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__uploadDocument": "Subir documento", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__uploadDocument__failed": "Error al subir el documento", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__uploadDocument__parameter__uploadedPurchasingDocument": "Documento de compra subido", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__bulkMutation__confirmBulk": "Confirmar en masa", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__bulkMutation__confirmBulk__failed": "Error al confirmar en masa", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__cannot_delete_record": "No puedes eliminar un documento de compra subido que esté en curso o revisado.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__cannot_delete_record_customer_id_inactive_nonexistent": "El id. de cliente no se ha introducido o está inactivo.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromPendingDocument": "Create purchase document from pending document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromPendingDocument__parameter__uploadedPurchasingDocument": "Uploaded purchasing document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromUploadedPurchasingDocument": "Crear documento de compra a partir de documento subido", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromUploadedPurchasingDocument__failed": "Error al crear el documento de compra a partir del documento subido", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromUploadedPurchasingDocument__parameter__uploadedPurchasingDocument": "Documento de compra subido", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__resetUploadedDocument": "Restablecer documento subido", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__resetUploadedDocument__failed": "Error al restablecer el documento subido", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__resetUploadedDocument__parameter__uploadedPurchasingDocument": "Documento de compra subido", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__syncDocuments": "Sincronizar documentos", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__syncDocuments__failed": "Error al sincronizar los documentos", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__updateDocumentWithDetails": "Detallar documento", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__updateDocumentWithDetails__failed": "Error al detallar el documento", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__updateDocumentWithDetails__parameter__document": "Documento", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__node_name": "Documento de compra subido", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__apAutomationCompany": "AP Automation company", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__currency": "Divisa", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__customerUniqueId": "Id. único de cliente", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__document": "Documento", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__documentMimeType": "Tipo MIME de documento", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__isResultPopulated": "Alimentación de resultados", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__isReviewDone": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__isTempUuid": "UUID temporal", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__lines": "Líneas", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__linkedSites": "Plantas vinculadas", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__origin": "Origen", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__postingDate": "Fecha de contabilización", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__processingDetails": "Detalles de procesamiento", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__processingStatus": "Estado de procesamiento", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__purchaseCreditMemo": "Factura rectificativa de compra", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__purchaseInvoice": "Factura de compra", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__reason": "Motivo", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__recipientEmail": "E-mail de destinatario", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__result": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__senderEmail": "E-mail de emisor", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__site": "Planta", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__status": "Estado", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__supplierDocumentDate": "<PERSON><PERSON> de documento de proveedor", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__supplierDocumentNumber": "Número de documento de proveedor", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__totalAmountExcludingTax": "Importe total sin impuestos", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__totalAmountIncludingTax": "Importe total con impuestos", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__totalTaxAmount": "Impuestos", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__type": "Tipo", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__uploadedFile": "Archivo subido", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__uuid": "UUID", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__query__isSupplierDocumentNumberUsed": "Número de documento de proveedor utilizado", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__query__isSupplierDocumentNumberUsed__failed": "Error de número de documento de proveedor utilizado", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__query__isSupplierDocumentNumberUsed__parameter__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__node_name": "Línea de documento de compra subido", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__amountExcludingTax": "Importe sin impuestos", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__amountIncludingTax": "Importe con impuestos", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__itemDescription": "Descripción de artículo", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__lineAmountExcludingTax": "Importe sin impuestos de línea", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__lineAmountIncludingTax": "Importe con impuestos de línea", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__parent": "Línea primaria", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__purchaseUnit": "Unidad de compra", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__quantity": "Cantidad", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__taxAmount": "Impuestos", "@sage/xtrem-ap-automation/notification_bulk_confirm__description_error": "Ha habido un error al confirmar los documentos. Para más información, revisa las trazas de las tareas por lotes.", "@sage/xtrem-ap-automation/notification_bulk_confirm__description_success": "Los documentos de compra se han confirmado.", "@sage/xtrem-ap-automation/package__name": "Automatización de cuentas por pagar en Sage DMO", "@sage/xtrem-ap-automation/page-extensions__purchase_credit_memo_extension__uploadedDocument____title": "Documento subido", "@sage/xtrem-ap-automation/page-extensions__purchase_credit_memo_extension__uploadedDocumentLink____title": "Documento subido", "@sage/xtrem-ap-automation/page-extensions__purchase_invoice_extension__uploadedDocument____title": "Documento subido", "@sage/xtrem-ap-automation/page-extensions__purchase_invoice_extension__uploadedDocumentLink____title": "Documento subido", "@sage/xtrem-ap-automation/pages__ap_automation_configuration____title": "Configuración de automatización de cuentas por pagar", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__accountsPayableEmail____title": "E-mail de documento de proveedor entrante", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__accountsPayableEmail": "E-mail de documento de proveedor entrante", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__company__id": "Id.", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__company__name": "Nombre", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__customerUniqueId": "Id. de cliente", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companiesBlock____title": "Sociedades", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__configurationLevel____title": "Nivel de configuración", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__customerUniqueId____title": "Id. de cliente", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__mainSection____title": "General", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__registerNewCompany____title": "Registrar sociedad", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__save____title": "Guardar", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__tenantBlock____title": "Instancia", "@sage/xtrem-ap-automation/pages__confirm_uploaded_purchasing_document____title": "Confirmar y crear", "@sage/xtrem-ap-automation/pages__confirm_uploaded_purchasing_document__confirmAndOpen____title": "Confirmar y ver", "@sage/xtrem-ap-automation/pages__confirm_uploaded_purchasing_document__confirmAndUpload____title": "Confirmar y subir nuevo", "@sage/xtrem-ap-automation/pages__import_purchase_document____title": "Subida de documentos de compra", "@sage/xtrem-ap-automation/pages__import_purchase_document__company____title": "Sociedad", "@sage/xtrem-ap-automation/pages__import_purchase_document__file_type_not_supported": "El tipo de archivo {{mimeType}} no es compatible. Debe ser PDF, JPG, JPEG, TIF, TIFF, PNG, HEIC, HEIF o XML.", "@sage/xtrem-ap-automation/pages__import_purchase_document__scan____title": "Escanear", "@sage/xtrem-ap-automation/pages__import_purchase_document__uploadedFile____title": "Documento financiero", "@sage/xtrem-ap-automation/pages__import_purchase_document__uploadSection____title": "Subir", "@sage/xtrem-ap-automation/pages__register_new_company____title": "Registrar sociedad", "@sage/xtrem-ap-automation/pages__register_new_company__company____columns__title__id": "Id.", "@sage/xtrem-ap-automation/pages__register_new_company__company____columns__title__name": "Nombre", "@sage/xtrem-ap-automation/pages__register_new_company__company____title": "Sociedad", "@sage/xtrem-ap-automation/pages__register_new_company__register____title": "Registrar", "@sage/xtrem-ap-automation/pages__technical_info____title": "Información técnica", "@sage/xtrem-ap-automation/pages__technical_info__technicalSection____title": "Información técnica", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__bulkActions__title": "Confirmar", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__created__title": "Creación", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__currencyId__title": "Divisa", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__line2__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__recipientEmail__title": "E-mail de destinatario", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__senderEmail__title": "E-mail de emisor", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__siteId__title": "Planta financiera", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__supplierId__title": "<PERSON><PERSON>. de <PERSON>edor", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__totalAmountExcludingTax__title": "Total sin impuestos", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__totalAmountIncludingTax__title": "Total con impuestos", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__totalTaxAmount__title": "Impuestos", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__type__title": "Tipo de factura", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__updated__title": "Actualización", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__2": "Todos", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__3": "Borradores", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__4": "En revisión", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__5": "Error", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__6": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__7": "Sin procesar", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____objectTypePlural": "Documentos de compra subidos", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____objectTypeSingular": "Documento de compra subido", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____title": "Documentos de compra subidos", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__confirm____title": "Confirmar", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____columns__title__id": "Código ISO 4217", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____columns__title__symbol": "Símbolo", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____placeholder": "Seleccionar divisa", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____title": "Divisa", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displaySite____title": "Planta financiera", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displaySupplier____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displaySupplierDocumentNumber____title": "Número de documento de proveedor", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displayType____title": "Tipo", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__document____title": "Documento subido", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__documentNumberLink____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__dueDate____title": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__headerSection____title": "Cabecera", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__image____title": "Imagen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__is_supplier_document_number_used_warn": "Este número de documento de proveedor ya existe.", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__is_supplier_document_number_used_warn_title": "Número de documento de proveedor", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__columns__item__title": "Categoría", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__columns__purchaseUnit__symbol__title": "Nombre", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__columns__purchaseUnit__symbol__title__2": "Símbolo", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__lookupDialogTitle__item": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__lookupDialogTitle__purchaseUnit__symbol": "Seleccionar unidad", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__amountExcludingTax": "Total sin impuestos", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__amountIncludingTax": "Total con impuestos", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__item": "Nombre de artículo", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__item__image": "Imagen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__itemDescription": "Descripción de artículo", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__lineAmountExcludingTax": "Total sin impuestos", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__lineAmountIncludingTax": "Total con impuestos", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__purchaseUnit__symbol": "Unidad de compra", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__quantity": "Cantidad en unidad de compra", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__taxAmount": "Impuestos", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____title": "Líneas", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__matchingSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__no_configuration_active_info": "You need to register tenants or companies from the AP Automation configuration page first.", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__no_configuration_active_info_title": "Configuración de automatización de cuentas por pagar", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__pdfView____title": "Documento subido", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__postingDate____title": "Fecha de contabilización", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__reason____lookupDialogTitle": "Seleccionar motivo", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__resetAll____title": "Restablecer todo", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__save____title": "Guardar", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____columns__title__id": "Id. ", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____columns__title__name": "Nombre", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____lookupDialogTitle": "Seleccionar planta financiera", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____title": "Planta financiera", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__status____title": "Estado", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____columns__title__businessEntity__country__name": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____columns__title__businessEntity__name": "Nombre", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____columns__title__businessEntity__name__2": "Id.", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplierDocumentDate____title": "<PERSON><PERSON> de documento de proveedor", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplierDocumentNumber____title": "Número de documento de proveedor", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__syncDocuments____title": "Sincronizar e-mails", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__technicalInfo____title": "Información técnica", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__totalAmountExcludingTax____title": "Total de proveedor sin impuestos", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__totalAmountIncludingTax____title": "Total de proveedor con impuestos", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__totalTaxAmount____title": "Impuestos de proveedor", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__uploadDocument____title": "Subir documento", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__warningMessage____content": "Comprueba que los valores escaneados sean correctos antes de confirmar.", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__site_warning": "La planta no pertenece a la sociedad indicada en la configuración.", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_draft": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_processing": "En curso", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_review_done": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_review_in_progress": "En revisión", "@sage/xtrem-ap-automation/permission__manage__name": "Gestionar", "@sage/xtrem-ap-automation/service_options__ap_automation_option__name": "Opción de automatización de cuentas por pagar", "@sage/xtrem-ap-automation/success_notification__bulk_confirm_title_error": "Error de confirmación de documentos de compra", "@sage/xtrem-ap-automation/success_notification__bulk_confirm_title_success": "Documentos de compra confirmados", "@sage/xtrem-ap-automation/success_notification_description": "El documento \"{{documentName}}\" se ha subido y está listo para revisar.", "@sage/xtrem-ap-automation/success_notification_description__purchase_credit_memo_invoices_link": "Factura rectificativa", "@sage/xtrem-ap-automation/success_notification_description__purchase_invoice_invoices_link": "Factura", "@sage/xtrem-ap-automation/success_notification_description__view_link": "<PERSON>er", "@sage/xtrem-ap-automation/success_notification_description_error": "El documento {{documentName}} se ha subido correctamente, pero no se ha podido procesar en Sage Ai.", "@sage/xtrem-ap-automation/success_notification_description_processing": "El documento \"{{documentName}}\" se ha subido y se está escaneando en Sage Ai.", "@sage/xtrem-ap-automation/success_notification_description_scan_failed": "El documento {{documentName}} no se ha escaneado. Revísalo e inténtalo de nuevo.", "@sage/xtrem-ap-automation/success_notification_title": "Subir documento", "@sage/xtrem-ap-automation/success_notification_title_error": "Subir documento", "@sage/xtrem-ap-automation/success_notification_title_processing": "Subir documento", "@sage/xtrem-ap-automation/success_notification_title_scan_failed": "Subir documento", "@sage/xtrem-ap-automation/uploaded_document_manager_complete": "El documento {{uuid}} se ha descargado.", "@sage/xtrem-ap-automation/uploaded_document_manager_log_error": "Error al procesar el documento {{uuid}}. {{errorMessage}}", "@sage/xtrem-ap-automation/uploaded_document_manager_no_configuration_active": "No hay ninguna configuración activa.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_configuration_active_error": "No hay ninguna configuración activa.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_customer_id_found": "No se ha encontrado ningún id. de cliente en la configuración.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_documents_to_be_processed": "No hay ningún documento que procesar.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_documents_to_be_processed_company": "No hay ningún documento que procesar para la sociedad {{companyName}}.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents__sync_documents_task__end": "La sincronización de documentos ha finalizado.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents__sync_documents_task__start": "La sincronización de documentos se ha iniciado.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents_process_start_notification.": "{{fileName}} has been submitted for processing. You will be notified when it is completed.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents_upload_documents_failed_scan": "Ha habido un error al escanear el documento."}