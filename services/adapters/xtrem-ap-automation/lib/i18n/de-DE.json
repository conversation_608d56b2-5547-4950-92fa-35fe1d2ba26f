{"@sage/xtrem-ap-automation/activity__ap_automation_company__name": "Unternehmen Eingangsrechnungsautomatisierung", "@sage/xtrem-ap-automation/activity__ap_automation_configuration__name": "Konfiguration Eingangsrechnungsautomatisierung", "@sage/xtrem-ap-automation/activity__uploaded_purchasing_document__name": "Hochgeladenes Einkaufsdokument", "@sage/xtrem-ap-automation/classes__updated-document-manager__document_type_not_supported": "Der Dokumenttyp wird nicht unterstützt: {{documentType}}", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__cancel_button": "Abbrechen", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__dialog_message": "Neuer Tenant gefunden. Verbindung erneut registrieren oder abbrechen?", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__dialog_title": "Verbindung Sage AI", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__register_button": "Registrieren", "@sage/xtrem-ap-automation/data_types__ap_automation_configuration_level_enum__name": "Enum Konfigurationsebene Eingangsrechnungsautomatisierung", "@sage/xtrem-ap-automation/data_types__ap_automation_document_origin_enum__name": "Enum Ursprung Dokument Eingangsrechnungsautomatisierung", "@sage/xtrem-ap-automation/data_types__ap_automation_document_type_enum__name": "Enum Typ Dokument Eingangsrechnungsautomatisierung", "@sage/xtrem-ap-automation/data_types__ap_automation_processing_status_enum__name": "Enum Status Verarbeitung Eingangsrechnungsautomatisierung", "@sage/xtrem-ap-automation/data_types__ap_automation_uploaded_document_status_enum__name": "Enum Status hochgeladenes Dokument Eingangsrechnungsautomatisierung", "@sage/xtrem-ap-automation/data_types__uploaded_purchasing_document__name": "Hochgeladenes Einkaufsdokument", "@sage/xtrem-ap-automation/enums__ap_automation_configuration_level__company": "Unternehmen", "@sage/xtrem-ap-automation/enums__ap_automation_configuration_level__tenant": "Tenant", "@sage/xtrem-ap-automation/enums__ap_automation_document_origin__email": "E-Mail", "@sage/xtrem-ap-automation/enums__ap_automation_document_origin__uploaded": "Hochgeladen", "@sage/xtrem-ap-automation/enums__ap_automation_document_type__creditMemo": "Gutschrift", "@sage/xtrem-ap-automation/enums__ap_automation_document_type__invoice": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/enums__ap_automation_processing_status__completed": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/enums__ap_automation_processing_status__error": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/enums__ap_automation_processing_status__processing": "Verarbeitung", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__draft": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__error": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__reviewDone": "Überprüfung durchgeführt", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__reviewInProgress": "Überprüfung läuft", "@sage/xtrem-ap-automation/events__updated-purchasing-document__customer_unique_id_is_not_valid_companies": "Die eindeutige Kunden-ID entspricht keiner Unternehmenskonfiguration.", "@sage/xtrem-ap-automation/events__updated-purchasing-document__customer_unique_id_is_not_valid_tenant": "Die eindeutige Kunden-ID entspricht nicht der Tenant-Konfiguration.", "@sage/xtrem-ap-automation/events__updated-purchasing-document__no_default_configuration_found": "Keine Standardkonfiguration gefunden.", "@sage/xtrem-ap-automation/fail__bulk_confirm_notification_description__view_link": "Protokolle <PERSON>ufgaben", "@sage/xtrem-ap-automation/function__updated-purchasing-document__due_date_cannot_be_before_supplier_document_date": "Das Fälligkeitsdatum kann nicht vor dem Lieferantendokumentdatum liegen.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__posting_date_cannot_be_before_supplier_document_date": "Das Buchungsdatum kann nicht vor dem Lieferantendokumentdatum liegen.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__posting_date_cannot_be_empty": "Das Buchungsdatum kann nicht leer sein.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__supplier_cannot_be_empty": "Der Lieferant kann nicht leer sein.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__supplier_document_number_cannot_be_empty": "Die Lieferantendokumentnummer kann nicht leer sein.", "@sage/xtrem-ap-automation/functions__common__cannot_register_company_tenant_configuration_is_active": "Solange die Tenant-Konfiguration aktiv ist, können Si<PERSON> kein Unternehmen bei Sage AI registrieren.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__currency_cannot_be_empty": "Die Währung kann nicht leer sein.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__invoice_type_cannot_be_empty": "<PERSON> Rechnungstyp kann nicht leer sein.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__is_supplier_document_number_used": "Die Lieferantendokumentnummer ist bereits vorhanden.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__purchase_document_already_created": "Ein Einkaufsdokument wurde bereits erstellt.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__purchase_document_status_review_done": "Das Einkaufsdokument hat den Status 'Überprüfung durchgeführt'.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__reason_code_cannot_be_empty": "Der Grundcode kann nicht leer sein.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__site_cannot_be_empty": "Der Buchhaltungsstandort kann nicht leer sein.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__total_amount_including_tax_must_be_equal_to_total_amount_excluding_tax_plus_total_tax_amount": "Der Gesamtbetrag inkl. Steuern muss gleich dem Gesamtbetrag exkl. Steuern + dem Gesamtsteuerbetrag sein.", "@sage/xtrem-ap-automation/mime_type_not_allowed": "<PERSON><PERSON> können diesen Dateityp nicht verwenden.", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__billBySupplier": "Rechnungssteller", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__currency": "Währung", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__pdfSupplierCreditMemo": "PDF Lieferantengutschrift", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__reason": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__site": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__supplierDocumentDate": "Datum Lieferantendokument", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__supplierDocumentNumber": "Lieferantendokumentnummer", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__totalAmountExcludingTax": "Gesamtbetrag exkl. Steuern", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__totalTaxAmount": "Gesamtsteuerbetrag", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__uploadedDocument": "Hochgeladenes Dokument", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__billBySupplier": "Rechnungssteller", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__currency": "Währung", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__pdfSupplierInvoice": "PDF Lieferantenrechnung", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__site": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__supplierDocumentDate": "Datum Lieferantendokument", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__supplierDocumentNumber": "Lieferantendokumentnummer", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__totalAmountExcludingTax": "Gesamtbetrag exkl. Steuern", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__totalTaxAmount": "Gesamtsteuerbetrag", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__uploadedDocument": "Hochgeladenes Dokument", "@sage/xtrem-ap-automation/nodes__ap_automation_company__asyncMutation__asyncExport": "Export", "@sage/xtrem-ap-automation/nodes__ap_automation_company__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-ap-automation/nodes__ap_automation_company__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-ap-automation/nodes__ap_automation_company__mutation__registerCompanyOnSageAi": "Unternehmen bei Sage AI registrieren", "@sage/xtrem-ap-automation/nodes__ap_automation_company__mutation__registerCompanyOnSageAi__failed": "Unternehmen bei Sage AI registrieren fehlgeschlagen.", "@sage/xtrem-ap-automation/nodes__ap_automation_company__mutation__registerCompanyOnSageAi__parameter__company": "Unternehmen", "@sage/xtrem-ap-automation/nodes__ap_automation_company__node_name": "Unternehmen Eingangsrechnungsautomatisierung", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__accountsPayableEmail": "E-Mail Eingangsrechnung", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__company": "Unternehmen", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__configuration": "Konfiguration", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__customerUniqueId": "Eindeutige Kunden-ID", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__employeeExpenseEmail": "E-Mail Mitarbeiterausgaben", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__isInboxActive": "Ist Posteingang aktiv", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__originalTenantId": "Ursprüngliche Tenant-ID", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__asyncMutation__asyncExport": "Export", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__country_is_mandatory": "Das Land ist erforderlich.", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__createOrGetDefaultRecord": "Standarddatensatz erstellen oder abrufen", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__createOrGetDefaultRecord__failed": "Standarddatensatz erstellen oder abrufen fehlgeschlagen.", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__resetConfiguration": "Konfiguration zurücksetzen", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__resetConfiguration__failed": "Konfiguration zurücksetzen fehlgeschlagen.", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__node_name": "Konfiguration Eingangsrechnungsautomatisierung", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__accountsPayableEmail": "E-Mail Eingangsrechnung", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__companies": "Unternehmen", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__configurationLevel": "Konfigurations<PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__country": "Land", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__customerUniqueId": "Eindeutige Kunden-ID", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__employeeExpenseEmail": "E-Mail Mitarbeiterausgaben", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__id": "ID", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__isActive": "Ist aktiv", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__isInboxActive": "Ist Posteingang aktiv", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__originalTenantId": "Ursprüngliche Tenant-ID", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__query__isConfigurationActive": "Ist Konfiguration aktiv", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__query__isConfigurationActive__failed": "Ist Konfiguration aktiv fehlgeschlagen.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__asyncExport": "Export", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__syncDocumentsTask": "Aufgabe Dokumente synchronisieren", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__syncDocumentsTask__failed": "Aufgabe Dokumente synchronisieren fehlgeschlagen.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__uploadDocument": "Dokument hochladen", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__uploadDocument__failed": "Dokument hochladen fehlgeschlagen.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__uploadDocument__parameter__uploadedPurchasingDocument": "Hochgeladenes Einkaufsdokument", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__bulkMutation__confirmBulk": "Massenbestätigen", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__bulkMutation__confirmBulk__failed": "Massenbestätigen fehlgeschlagen.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__cannot_delete_record": "Sie können ein hochgeladenes Einkaufsdokument nicht löschen, während es verarbeitet ist oder wenn die Überprüfung durchgeführt wurde.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__cannot_delete_record_customer_id_inactive_nonexistent": "Ihre Kunden-ID fehlt oder ist inaktiv.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromPendingDocument": "Einkaufsdokument aus ausstehendem Dokument erstellen", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromPendingDocument__parameter__uploadedPurchasingDocument": "Hochgeladenes Einkaufsdokument", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromUploadedPurchasingDocument": "Einkaufsdokument aus hochgeladenem Einkaufsdokument erstellen", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromUploadedPurchasingDocument__failed": "Einkaufsdokument aus hochgeladenem Einkaufsdokument erstellen fehlgeschlagen.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromUploadedPurchasingDocument__parameter__uploadedPurchasingDocument": "Hochgeladenes Einkaufsdokument", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__resetUploadedDocument": "Hochgeladenes Dokument zurücksetzen", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__resetUploadedDocument__failed": "Hochgeladenes Dokument zurücksetzen fehlgeschlagen.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__resetUploadedDocument__parameter__uploadedPurchasingDocument": "Hochgeladenes Einkaufsdokument", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__syncDocuments": "Dokumente synchronisieren", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__syncDocuments__failed": "Dokumente synchronisieren fehlgeschlagen.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__updateDocumentWithDetails": "Dokument mit Details aktualisieren", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__updateDocumentWithDetails__failed": "Dokument mit Details aktualisieren fehlgeschlagen.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__updateDocumentWithDetails__parameter__document": "Dokument", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__node_name": "Hochgeladenes Einkaufsdokument", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__apAutomationCompany": "Unternehmen Eingangsrechnungsautomatisierung", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__currency": "Währung", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__customerUniqueId": "Eindeutige Kunden-ID", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__document": "Dokument", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__documentMimeType": "MIME-Typ Dokument", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__dueDate": "Fälligkeitsdatum", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__isResultPopulated": "Ist Ergebnis ausgefüllt", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__isReviewDone": "Ist Überprüfung durchgeführt", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__isTempUuid": "Ist temporäre UUID", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__linkedSites": "Verknüpfte Standorte", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__origin": "Ursprung", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__postingDate": "Buchungsdatum", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__processingDetails": "Verarbeitungsdetails", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__processingStatus": "Verarbeitungsstatus", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__purchaseCreditMemo": "Einkaufsgutschrift", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__purchaseInvoice": "Einkaufsrechnung", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__reason": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__recipientEmail": "E-Mail Empfänger", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__result": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__senderEmail": "E-Mail-Absender", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__site": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__status": "Status", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__supplier": "Lieferant", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__supplierDocumentDate": "Datum Lieferantendokument", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__supplierDocumentNumber": "Lieferantendokumentnummer", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__totalAmountExcludingTax": "Gesamtbetrag exkl. Steuern", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__totalAmountIncludingTax": "Gesamtbetrag inkl. Steuern", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__totalTaxAmount": "Gesamtsteuerbetrag", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__type": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__uploadedFile": "<PERSON><PERSON> ho<PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__uuid": "UUID", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__query__isSupplierDocumentNumberUsed": "Ist Lieferantendokumentnummer verwendet", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__query__isSupplierDocumentNumberUsed__failed": "Ist Lieferantendokumentnummer verwendet fehlgeschlagen.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__query__isSupplierDocumentNumberUsed__parameter__supplier": "Lieferant", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__node_name": "Hochgeladene Einkaufsdokumentzeile", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__amountExcludingTax": "Betrag exkl. Steuern", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__amountIncludingTax": "Betrag inkl. Steuern", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__item": "Artikel", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__itemDescription": "Artikelbezeichnung", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__parent": "Übergeordnet", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__purchaseUnit": "Einkaufseinheit", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__quantity": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__taxAmount": "Steuerbetrag", "@sage/xtrem-ap-automation/notification_bulk_confirm__description_error": "Die Massenbestätigung für alle Dokumente ist fehlgeschlagen. Überprüfen Sie die Protokolle der Batchaufgaben, um weitere Informationen zu erhalten.", "@sage/xtrem-ap-automation/notification_bulk_confirm__description_success": "Einkaufsdokumente bestätigt.", "@sage/xtrem-ap-automation/package__name": "Sage DMO Eingangsrechnungsautomatisierung", "@sage/xtrem-ap-automation/page-extensions__purchase_credit_memo_extension__uploadedDocument____title": "Hochgeladenes Dokument", "@sage/xtrem-ap-automation/page-extensions__purchase_credit_memo_extension__uploadedDocumentLink____title": "Hochgeladenes Dokument", "@sage/xtrem-ap-automation/page-extensions__purchase_invoice_extension__uploadedDocument____title": "Hochgeladenes Dokument", "@sage/xtrem-ap-automation/page-extensions__purchase_invoice_extension__uploadedDocumentLink____title": "Hochgeladenes Dokument", "@sage/xtrem-ap-automation/pages__ap_automation_configuration____title": "Konfiguration Eingangsrechnungsautomatisierung", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__accountsPayableEmail____title": "E-Mail-Adresse eingehende Eingangsrechnungen", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__accountsPayableEmail": "E-Mail-Adresse eingehende Eingangsrechnungen", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__company__id": "ID", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__company__name": "Name", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__customerUniqueId": "Kunden-ID", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companiesBlock____title": "Unternehmen", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__configurationLevel____title": "Konfigurations<PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__customerUniqueId____title": "Kunden-ID", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__mainSection____title": "Allgemein", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__registerNewCompany____title": "Neues Unternehmen registrieren", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__save____title": "Speichern", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__tenantBlock____title": "Tenant", "@sage/xtrem-ap-automation/pages__confirm_uploaded_purchasing_document____title": "Bestätigen und erstellen", "@sage/xtrem-ap-automation/pages__confirm_uploaded_purchasing_document__confirmAndOpen____title": "Bestätigen und anzeigen", "@sage/xtrem-ap-automation/pages__confirm_uploaded_purchasing_document__confirmAndUpload____title": "Bestätigen und neu hochladen", "@sage/xtrem-ap-automation/pages__import_purchase_document____title": "Einkaufsdokumente hochladen", "@sage/xtrem-ap-automation/pages__import_purchase_document__company____title": "Unternehmen", "@sage/xtrem-ap-automation/pages__import_purchase_document__file_type_not_supported": "Dateityp wird nicht unterstützt: {{mimeType}}. Die folgenden Dateitypen werden unterstützt: PDF, JPG, JPEG, TIF, TIFF, PNG, HEIC, HEIF und XML.", "@sage/xtrem-ap-automation/pages__import_purchase_document__scan____title": "Scannen", "@sage/xtrem-ap-automation/pages__import_purchase_document__uploadedFile____title": "Finanzdokument", "@sage/xtrem-ap-automation/pages__import_purchase_document__uploadSection____title": "Upload", "@sage/xtrem-ap-automation/pages__register_new_company____title": "Neues Unternehmen registrieren", "@sage/xtrem-ap-automation/pages__register_new_company__company____columns__title__id": "ID", "@sage/xtrem-ap-automation/pages__register_new_company__company____columns__title__name": "Name", "@sage/xtrem-ap-automation/pages__register_new_company__company____title": "Unternehmen", "@sage/xtrem-ap-automation/pages__register_new_company__register____title": "Registrieren", "@sage/xtrem-ap-automation/pages__technical_info____title": "Technische Informationen", "@sage/xtrem-ap-automation/pages__technical_info__technicalSection____title": "Technische Informationen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__bulkActions__title": "Bestätigen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__created__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__currencyId__title": "Währung", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__line2__title": "Lieferant", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__recipientEmail__title": "E-Mail Empfänger", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__senderEmail__title": "E-Mail-Absender", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__siteId__title": "Buchhaltungsstandort", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__supplierId__title": "ID Lieferant", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__totalAmountExcludingTax__title": "Summe exkl. Steuern", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__totalAmountIncludingTax__title": "Summe inkl. Steu<PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__totalTaxAmount__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__type__title": "Rechnungstyp", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__updated__title": "<PERSON>ktual<PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title": "Alle offenen Statuswerte", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__2": "Alle Statuswerte", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__4": "Überprüfung läuft", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__5": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__6": "Bestätigt", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__7": "<PERSON>cht verarbeitet", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____objectTypePlural": "Hochgeladene Einkaufsdokumente", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____objectTypeSingular": "Hochgeladenes Einkaufsdokument", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____title": "Hochgeladene Einkaufsdokumente", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__confirm____title": "Bestätigen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____columns__title__id": "ISO 4217-Code", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____columns__title__symbol": "Symbol", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____placeholder": "Währung auswählen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____title": "Währung", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displaySite____title": "Buchhaltungsstandort", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displaySupplier____title": "Lieferant", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displaySupplierDocumentNumber____title": "Lieferantendokumentnummer", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displayType____title": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__document____title": "Hochgeladenes Dokument", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__documentNumberLink____title": "Verknüpfung", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__dueDate____title": "Fälligkeitsdatum", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__headerSection____title": "Kopfzeile", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__image____title": "Bild", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__is_supplier_document_number_used_warn": "Die Lieferantendokumentnummer ist bereits vorhanden.", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__is_supplier_document_number_used_warn_title": "Lieferantendokumentnummer", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__columns__item__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__columns__purchaseUnit__symbol__title": "Name", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__columns__purchaseUnit__symbol__title__2": "Symbol", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__lookupDialogTitle__item": "Artikel auswählen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__lookupDialogTitle__purchaseUnit__symbol": "Einheit auswählen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__amountExcludingTax": "Summe exkl. Steuern", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__amountIncludingTax": "Summe inkl. Steu<PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__item": "Artikelname", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__item__id": "Artikel-ID", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__item__image": "Bild", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__itemDescription": "Artikelbezeichnung", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__purchaseUnit__symbol": "Einkaufseinheit", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__quantity": "Menge in Einkaufseinheit", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__taxAmount": "Steuerbetrag", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__matchingSection____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__no_configuration_active_info": "Sie müssen zuerst Tenants oder Unternehmen auf der Konfigurationsseite der Eingangsrechnungsautomatisierung registrieren.", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__no_configuration_active_info_title": "Konfiguration Eingangsrechnungsautomatisierung", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__pdfView____title": "Hochgeladenes Dokument", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__postingDate____title": "Buchungsdatum", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__reason____lookupDialogTitle": "<PERSON><PERSON>d auswählen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__resetAll____title": "Alle zurücksetzen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__save____title": "Speichern", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____columns__title__id": "ID ", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____columns__title__name": "Name", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____lookupDialogTitle": "Buchhaltungsstandort auswählen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____title": "Buchhaltungsstandort", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__status____title": "Status", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____columns__title__businessEntity__country__name": "Land", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____columns__title__businessEntity__name__2": "ID", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____lookupDialogTitle": "Lieferant auswählen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____title": "Lieferant", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplierDocumentDate____title": "Datum Lieferantendokument", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplierDocumentNumber____title": "Lieferantendokumentnummer", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__syncDocuments____title": "E-Mails synchronisieren", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__technicalInfo____title": "Technische Informationen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__totalAmountExcludingTax____title": "Summe Lieferantenbetrag exkl. Steuern", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__totalAmountIncludingTax____title": "Summe Lieferantenbetrag inkl. Steuern", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__totalTaxAmount____title": "Summe Lieferantensteuer", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__uploadDocument____title": "Ein Dokument hochladen", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__warningMessage____content": "Überprüfen Sie die gescannten Werte, bevor Si<PERSON> bestätigen.", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__site_warning": "Dieser Standort gehört nicht zum gleichen Unternehmen wie Ihre Konfigurationseinstellungen.", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_draft": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_processing": "Verarbeitung", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_review_done": "Überprüfung durchgeführt", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_review_in_progress": "Überprüfung läuft", "@sage/xtrem-ap-automation/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/service_options__ap_automation_option__name": "Option Eingangsrechnungsautomatisierung", "@sage/xtrem-ap-automation/success_notification__bulk_confirm_title_error": "Bestätigung Einkaufsdokument", "@sage/xtrem-ap-automation/success_notification__bulk_confirm_title_success": "Bestätigung Einkaufsdokument", "@sage/xtrem-ap-automation/success_notification_description": "Dokument hochgeladen und bereit zur Überprüfung: {{documentName}}.", "@sage/xtrem-ap-automation/success_notification_description__purchase_credit_memo_invoices_link": "Gutschrift", "@sage/xtrem-ap-automation/success_notification_description__purchase_invoice_invoices_link": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/success_notification_description__view_link": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/success_notification_description_error": "Das Dokument {{documentName}} wurde erfolgreich hochgeladen, die Verarbeitung durch Sage AI war jedoch nicht möglich.", "@sage/xtrem-ap-automation/success_notification_description_processing": "<PERSON><PERSON><PERSON> ho<PERSON>geladen: <PERSON> in Bearbeitung: {{documentName}}.", "@sage/xtrem-ap-automation/success_notification_description_scan_failed": "Das Dokument konnte nicht gescannt werden: {{documentName}}. Prüfen Sie das Dokument und versuchen Sie es erneut.", "@sage/xtrem-ap-automation/success_notification_title": "Upload Dokument", "@sage/xtrem-ap-automation/success_notification_title_error": "Upload Dokument", "@sage/xtrem-ap-automation/success_notification_title_processing": "Upload Dokument", "@sage/xtrem-ap-automation/success_notification_title_scan_failed": "Upload Dokument", "@sage/xtrem-ap-automation/uploaded_document_manager_complete": "Dokument {{uuid}} erfolg<PERSON>ich heruntergeladen.", "@sage/xtrem-ap-automation/uploaded_document_manager_log_error": "Fehler bei der Verarbeitung des Dokuments {{uuid}}. Fehlermeldung: {{errorMessage}}.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_configuration_active": "Keine Konfiguration aktiv.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_configuration_active_error": "Keine Konfiguration aktiv.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_customer_id_found": "<PERSON><PERSON>-ID in der Konfiguration gefunden.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_documents_to_be_processed": "<PERSON><PERSON> Dokument<PERSON> zu verarbeiten.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_documents_to_be_processed_company": "<PERSON>ine Dokumente zu verarbeiten für dieses Unternehmen: {{companyName}}.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents__sync_documents_task__end": "Synchronisierung der Dokumente durchgeführt.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents__sync_documents_task__start": "Synchronisierung der Dokumente gestartet.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents_process_start_notification.": "{{fileName}} wurde zur Verarbeitung übermittelt. Sie werden benachrichtigt, wenn die Verarbeitung abgeschlossen ist.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents_upload_documents_failed_scan": "Das Scannen des Dokuments ist fehlgeschlagen."}