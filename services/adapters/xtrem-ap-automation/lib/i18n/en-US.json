{"@sage/xtrem-ap-automation/activity__ap_automation_company__name": "AP Automation company", "@sage/xtrem-ap-automation/activity__ap_automation_configuration__name": "AP Automation configuration", "@sage/xtrem-ap-automation/activity__uploaded_purchasing_document__name": "Uploaded purchasing document", "@sage/xtrem-ap-automation/classes__updated-document-manager__document_type_not_supported": "Document type is not supported: {{documentType}}", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__cancel_button": "Cancel", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__dialog_message": "New tenant detected. Register the connection again or cancel?", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__dialog_title": "Sage AI connection", "@sage/xtrem-ap-automation/client_functions__ap_automation_configuration__register_button": "Register", "@sage/xtrem-ap-automation/data_types__ap_automation_configuration_level_enum__name": "AP Automation configuration level enum", "@sage/xtrem-ap-automation/data_types__ap_automation_document_origin_enum__name": "AP Automation document origin enum", "@sage/xtrem-ap-automation/data_types__ap_automation_document_type_enum__name": "AP Automation document type enum", "@sage/xtrem-ap-automation/data_types__ap_automation_processing_status_enum__name": "AP Automation processing status enum", "@sage/xtrem-ap-automation/data_types__ap_automation_uploaded_document_status_enum__name": "AP Automation uploaded document status enum", "@sage/xtrem-ap-automation/data_types__uploaded_purchasing_document__name": "Uploaded purchasing document", "@sage/xtrem-ap-automation/enums__ap_automation_configuration_level__company": "Company", "@sage/xtrem-ap-automation/enums__ap_automation_configuration_level__tenant": "Tenant", "@sage/xtrem-ap-automation/enums__ap_automation_document_origin__email": "Email", "@sage/xtrem-ap-automation/enums__ap_automation_document_origin__uploaded": "Uploaded", "@sage/xtrem-ap-automation/enums__ap_automation_document_type__creditMemo": "Credit memo", "@sage/xtrem-ap-automation/enums__ap_automation_document_type__invoice": "Invoice", "@sage/xtrem-ap-automation/enums__ap_automation_processing_status__completed": "Completed", "@sage/xtrem-ap-automation/enums__ap_automation_processing_status__error": "Error", "@sage/xtrem-ap-automation/enums__ap_automation_processing_status__processing": "Processing", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__draft": "Draft", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__error": "Error", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__reviewDone": "Review done", "@sage/xtrem-ap-automation/enums__ap_automation_uploaded_document_status__reviewInProgress": "Review in progress", "@sage/xtrem-ap-automation/events__updated-purchasing-document__customer_unique_id_is_not_valid_companies": "The unique customer ID does not match any company configuration.", "@sage/xtrem-ap-automation/events__updated-purchasing-document__customer_unique_id_is_not_valid_tenant": "The unique customer ID does not match tenant configuration.", "@sage/xtrem-ap-automation/events__updated-purchasing-document__no_default_configuration_found": "No default configuration found.", "@sage/xtrem-ap-automation/fail__bulk_confirm_notification_description__view_link": "Batch task logs", "@sage/xtrem-ap-automation/function__updated-purchasing-document__due_date_cannot_be_before_supplier_document_date": "Due date cannot be before supplier document date.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__posting_date_cannot_be_before_supplier_document_date": "Posting date cannot be before supplier document date.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__posting_date_cannot_be_empty": "Posting date cannot be empty.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__supplier_cannot_be_empty": "Supplier cannot be empty.", "@sage/xtrem-ap-automation/function__updated-purchasing-document__supplier_document_number_cannot_be_empty": "Supplier document number cannot be empty.", "@sage/xtrem-ap-automation/functions__common__cannot_register_company_tenant_configuration_is_active": "You cannot register a company with Sage AI while the tenant configuration is active.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__currency_cannot_be_empty": "Currency cannot be empty.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__invoice_type_cannot_be_empty": "Invoice type cannot be empty.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__is_supplier_document_number_used": "Supplier document number already exists.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__purchase_document_already_created": "A purchasing document was already created.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__purchase_document_status_review_done": "The purchasing document status is review done.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__reason_code_cannot_be_empty": "Reason code cannot be empty.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__site_cannot_be_empty": "Financial site cannot be empty.", "@sage/xtrem-ap-automation/functions__uploaded_purchasing_document__total_amount_including_tax_must_be_equal_to_total_amount_excluding_tax_plus_total_tax_amount": "Total amount including tax needs to be equal to the total amount excluding tax plus the total tax amount.", "@sage/xtrem-ap-automation/mime_type_not_allowed": "You cannot use this file type.", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__billBySupplier": "Bill-by supplier", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__pdfSupplierCreditMemo": "PDF supplier credit memo", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__reason": "Reason", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__site": "Site", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__supplierDocumentDate": "Supplier document date", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__supplierDocumentNumber": "Supplier document number", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__totalAmountExcludingTax": "Total amount excluding tax", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__totalTaxAmount": "Total tax amount", "@sage/xtrem-ap-automation/node-extensions__purchase_credit_memo_extension__property__uploadedDocument": "Uploaded document", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__billBySupplier": "Bill-by supplier", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__pdfSupplierInvoice": "PDF supplier invoice", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__site": "Site", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__supplierDocumentDate": "Supplier document date", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__supplierDocumentNumber": "Supplier document number", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__totalAmountExcludingTax": "Total amount excluding tax", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__totalTaxAmount": "Total tax amount", "@sage/xtrem-ap-automation/node-extensions__purchase_invoice_extension__property__uploadedDocument": "Uploaded document", "@sage/xtrem-ap-automation/nodes__ap_automation_company__asyncMutation__asyncExport": "Export", "@sage/xtrem-ap-automation/nodes__ap_automation_company__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-ap-automation/nodes__ap_automation_company__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-ap-automation/nodes__ap_automation_company__mutation__registerCompanyOnSageAi": "Register company on Sage AI", "@sage/xtrem-ap-automation/nodes__ap_automation_company__mutation__registerCompanyOnSageAi__failed": "Register company on Sage Ai failed.", "@sage/xtrem-ap-automation/nodes__ap_automation_company__mutation__registerCompanyOnSageAi__parameter__company": "Company", "@sage/xtrem-ap-automation/nodes__ap_automation_company__node_name": "AP Automation company", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__accountsPayableEmail": "Accounts payable email", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__company": "Company", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__configuration": "Configuration", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__customerUniqueId": "Customer unique ID", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__employeeExpenseEmail": "Employee expense email", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__isInboxActive": "Is inbox active", "@sage/xtrem-ap-automation/nodes__ap_automation_company__property__originalTenantId": "Original tenant ID", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__asyncMutation__asyncExport": "Export", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__country_is_mandatory": "Country is mandatory.", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__createOrGetDefaultRecord": "Create or get default record", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__createOrGetDefaultRecord__failed": "Create or get default record failed.", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__resetConfiguration": "Reset configuration", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__mutation__resetConfiguration__failed": "Reset configuration failed.", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__node_name": "AP Automation configuration", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__accountsPayableEmail": "Accounts payable email", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__companies": "Companies", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__configurationLevel": "Configuration level", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__country": "Country", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__customerUniqueId": "Customer unique ID", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__employeeExpenseEmail": "Employee expense email", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__id": "ID", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__isActive": "Is active", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__isInboxActive": "Is inbox active", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__property__originalTenantId": "Original tenant ID", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__query__isConfigurationActive": "Is configuration active", "@sage/xtrem-ap-automation/nodes__ap_automation_configuration__query__isConfigurationActive__failed": "Is configuration active failed.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__asyncExport": "Export", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__syncDocumentsTask": "Sync documents task", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__syncDocumentsTask__failed": "Sync documents task failed.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__uploadDocument": "Upload document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__uploadDocument__failed": "Upload document failed.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__asyncMutation__uploadDocument__parameter__uploadedPurchasingDocument": "Uploaded purchasing document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__bulkMutation__confirmBulk": "Confirm bulk", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__bulkMutation__confirmBulk__failed": "Confirm bulk failed.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__cannot_delete_record": "You cannot delete an uploaded purchasing document while it is being processed or if the review is done.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__cannot_delete_record_customer_id_inactive_nonexistent": "Your customer ID is missing or inactive.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromPendingDocument": "Create purchase document from pending document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromPendingDocument__parameter__uploadedPurchasingDocument": "Uploaded purchasing document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromUploadedPurchasingDocument": "Create purchase document from uploaded purchasing document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromUploadedPurchasingDocument__failed": "Create purchase document from uploaded purchasing document failed.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__createPurchaseDocumentFromUploadedPurchasingDocument__parameter__uploadedPurchasingDocument": "Uploaded purchasing document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__resetUploadedDocument": "Reset uploaded document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__resetUploadedDocument__failed": "Reset uploaded document failed.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__resetUploadedDocument__parameter__uploadedPurchasingDocument": "Uploaded purchasing document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__syncDocuments": "Sync documents", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__syncDocuments__failed": "Sync documents failed.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__updateDocumentWithDetails": "Update document with details", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__updateDocumentWithDetails__failed": "Update document with details failed.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__mutation__updateDocumentWithDetails__parameter__document": "Document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__node_name": "Uploaded purchasing document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__apAutomationCompany": "AP Automation company", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__customerUniqueId": "Customer unique ID", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__document": "Document", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__documentMimeType": "Document MIME type", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__dueDate": "Due date", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__isResultPopulated": "Is result populated", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__isReviewDone": "Is review done", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__isTempUuid": "Is temp uuid", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__lines": "Lines", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__linkedSites": "Linked sites", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__origin": "Origin", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__postingDate": "Posting date", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__processingDetails": "Processing details", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__processingStatus": "Processing status", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__purchaseCreditMemo": "Purchase credit memo", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__purchaseInvoice": "Purchase invoice", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__reason": "Reason", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__recipientEmail": "Recipient email", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__result": "Result", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__senderEmail": "Sender email", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__site": "Site", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__status": "Status", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__supplier": "Supplier", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__supplierDocumentDate": "Supplier document date", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__supplierDocumentNumber": "Supplier document number", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__totalAmountExcludingTax": "Total amount excluding tax", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__totalAmountIncludingTax": "Total amount including tax", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__totalTaxAmount": "Total tax amount", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__type": "Type", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__uploadedFile": "Uploaded file", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__property__uuid": "UUID", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__query__isSupplierDocumentNumberUsed": "Is supplier document number used", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__query__isSupplierDocumentNumberUsed__failed": "Is supplier document number used failed.", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__query__isSupplierDocumentNumberUsed__parameter__supplier": "Supplier", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__node_name": "Uploaded purchasing document line", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__amountExcludingTax": "Amount excluding tax", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__amountIncludingTax": "Amount including tax", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__itemDescription": "Item description", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__lineAmountExcludingTax": "Line amount excluding tax", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__lineAmountIncludingTax": "Line amount including tax", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__parent": "Parent", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__purchaseUnit": "Purchase unit", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__quantity": "Quantity", "@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document_line__property__taxAmount": "Tax amount", "@sage/xtrem-ap-automation/notification_bulk_confirm__description_error": "The bulk confirm for all documents failed. Review batch task logs for more information.", "@sage/xtrem-ap-automation/notification_bulk_confirm__description_success": "Purchasing documents confirmed.", "@sage/xtrem-ap-automation/package__name": "Sage DMO AP Automation", "@sage/xtrem-ap-automation/page-extensions__purchase_credit_memo_extension__uploadedDocument____title": "Uploaded document", "@sage/xtrem-ap-automation/page-extensions__purchase_credit_memo_extension__uploadedDocumentLink____title": "Uploaded document", "@sage/xtrem-ap-automation/page-extensions__purchase_invoice_extension__uploadedDocument____title": "Uploaded document", "@sage/xtrem-ap-automation/page-extensions__purchase_invoice_extension__uploadedDocumentLink____title": "Uploaded document", "@sage/xtrem-ap-automation/pages__ap_automation_configuration____title": "AP Automation configuration", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__accountsPayableEmail____title": "Incoming accounts payable email address", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__accountsPayableEmail": "Incoming accounts payable email address", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__company__id": "ID", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__company__name": "Name", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companies____columns__title__customerUniqueId": "Customer ID", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__companiesBlock____title": "Companies", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__configurationLevel____title": "Configuration level", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__customerUniqueId____title": "Customer ID", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__mainSection____title": "General", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__registerNewCompany____title": "Register new company", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__save____title": "Save", "@sage/xtrem-ap-automation/pages__ap_automation_configuration__tenantBlock____title": "Tenant", "@sage/xtrem-ap-automation/pages__confirm_uploaded_purchasing_document____title": "Confirm and create", "@sage/xtrem-ap-automation/pages__confirm_uploaded_purchasing_document__confirmAndOpen____title": "Confirm and view", "@sage/xtrem-ap-automation/pages__confirm_uploaded_purchasing_document__confirmAndUpload____title": "Confirm and upload new", "@sage/xtrem-ap-automation/pages__import_purchase_document____title": "Upload purchasing documents", "@sage/xtrem-ap-automation/pages__import_purchase_document__company____title": "Company", "@sage/xtrem-ap-automation/pages__import_purchase_document__scan____title": "<PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__import_purchase_document__uploadedFile____title": "Financial document", "@sage/xtrem-ap-automation/pages__import_purchase_document__uploadSection____title": "Upload", "@sage/xtrem-ap-automation/pages__register_new_company____title": "Register new company", "@sage/xtrem-ap-automation/pages__register_new_company__company____columns__title__id": "ID", "@sage/xtrem-ap-automation/pages__register_new_company__company____columns__title__name": "Name", "@sage/xtrem-ap-automation/pages__register_new_company__company____title": "Company", "@sage/xtrem-ap-automation/pages__register_new_company__register____title": "Register", "@sage/xtrem-ap-automation/pages__technical_info____title": "Technical information", "@sage/xtrem-ap-automation/pages__technical_info__technicalSection____title": "Technical information", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__bulkActions__title": "Confirm", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__created__title": "Created", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__currencyId__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__line2__title": "Supplier", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__recipientEmail__title": "Recipient email", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__senderEmail__title": "Sender email", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__siteId__title": "Financial site", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__supplierId__title": "Supplier ID", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__totalAmountExcludingTax__title": "Total excluding tax", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__totalAmountIncludingTax__title": "Total including tax", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__totalTaxAmount__title": "Tax", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__type__title": "Invoice type", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__listItem__updated__title": "Updated", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__3": "Draft", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__4": "Review in progress", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__5": "Error", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__6": "Confirmed", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____navigationPanel__optionsMenu__title__7": "Not processed", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____objectTypePlural": "Uploaded purchasing documents", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____objectTypeSingular": "Uploaded purchasing document", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document____title": "Uploaded purchasing documents", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__confirm____title": "Confirm", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____columns__title__id": "ISO 4217 code", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____columns__title__symbol": "Symbol", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____placeholder": "Select currency", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displaySite____title": "Financial site", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displaySupplier____title": "Supplier", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displaySupplierDocumentNumber____title": "Supplier document number", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__displayType____title": "Type", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__document____title": "Uploaded Document", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__documentNumberLink____title": "Link", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__dueDate____title": "Due date", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__headerSection____title": "Header", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__is_supplier_document_number_used_warn": "Supplier document number already exists.", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__is_supplier_document_number_used_warn_title": "Supplier document number", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__columns__item__title": "Category", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__columns__purchaseUnit__symbol__title": "Name", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__columns__purchaseUnit__symbol__title__2": "Symbol", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__lookupDialogTitle__item": "Select item", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__lookupDialogTitle__purchaseUnit__symbol": "Select unit", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__amountExcludingTax": "Total excluding tax", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__amountIncludingTax": "Total including tax", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__item": "Item name", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__item__id": "Item ID", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__item__image": "Image", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__lineAmountExcludingTax": "Total excluding tax", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__lineAmountIncludingTax": "Total including tax", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__purchaseUnit__symbol": "Purchase unit", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__quantity": "Quantity in purchase unit", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____columns__title__taxAmount": "Tax amount", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__lines____title": "Lines", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__matchingSection____title": "Matching", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__no_configuration_active_info": "You need to register tenants or companies from the AP Automation configuration page first.", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__no_configuration_active_info_title": "AP Automation configuration", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__postingDate____title": "Posting date", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__reason____lookupDialogTitle": "Select reason", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__resetAll____title": "Reset all", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__save____title": "Save", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____columns__title__id": "ID ", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____columns__title__name": "Name", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____lookupDialogTitle": "Select financial site", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__site____title": "Financial site", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__status____title": "Status", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____columns__title__businessEntity__name__2": "ID", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____lookupDialogTitle": "Select supplier", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplier____title": "Supplier", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplierDocumentDate____title": "Supplier document date", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__supplierDocumentNumber____title": "Supplier document number", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__syncDocuments____title": "Sync emails", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__technicalInfo____title": "Technical information", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__totalAmountExcludingTax____title": "Total supplier amount excl. tax", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__totalAmountIncludingTax____title": "Total supplier amount incl. tax", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__totalTaxAmount____title": "Total supplier tax", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__uploadDocument____title": "Upload a document", "@sage/xtrem-ap-automation/pages__uploaded_purchasing_document__warningMessage____content": "Check that the scanned values are correct before confirming.", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__site_warning": "This site does not belong to the same company as your configuration setup.", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_draft": "Draft", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_processing": "Processing", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_review_done": "Review done", "@sage/xtrem-ap-automation/pages__uploaded-purchasing-document__step_sequence_review_in_progress": "Review in progress", "@sage/xtrem-ap-automation/permission__manage__name": "Manage", "@sage/xtrem-ap-automation/service_options__ap_automation_option__name": "AP Automation option", "@sage/xtrem-ap-automation/success_notification__bulk_confirm_title_error": "Purchase document confirm", "@sage/xtrem-ap-automation/success_notification__bulk_confirm_title_success": "Purchase document confirm", "@sage/xtrem-ap-automation/success_notification_description": "Document uploaded and ready for review: {{documentName}}.", "@sage/xtrem-ap-automation/success_notification_description__purchase_credit_memo_invoices_link": "Credit memo", "@sage/xtrem-ap-automation/success_notification_description__purchase_invoice_invoices_link": "Invoice", "@sage/xtrem-ap-automation/success_notification_description__view_link": "View", "@sage/xtrem-ap-automation/success_notification_description_error": "This document was uploaded successfully, but Sage AI could not process it: {{documentName}}.", "@sage/xtrem-ap-automation/success_notification_description_processing": "Document uploaded: Sage AI scan in progress: {{documentName}}.", "@sage/xtrem-ap-automation/success_notification_description_scan_failed": "Document could not be scanned: {{documentName}}. Review the document and try again.", "@sage/xtrem-ap-automation/success_notification_title": "Document upload", "@sage/xtrem-ap-automation/success_notification_title_error": "Document upload", "@sage/xtrem-ap-automation/success_notification_title_processing": "Document upload", "@sage/xtrem-ap-automation/success_notification_title_scan_failed": "Document upload", "@sage/xtrem-ap-automation/uploaded_document_manager_complete": "Document {{uuid}} downloaded successfully", "@sage/xtrem-ap-automation/uploaded_document_manager_log_error": "Error processing document {{uuid}}. Error message: {{errorMessage}}.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_configuration_active": "No configuration active.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_configuration_active_error": "No configuration active.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_customer_id_found": "No customer ID found in the configuration.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_documents_to_be_processed": "No documents to process.", "@sage/xtrem-ap-automation/uploaded_document_manager_no_documents_to_be_processed_company": "No documents to process for this company: {{companyName}}.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents__sync_documents_task__end": "Synchronizing documents completed.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents__sync_documents_task__start": "Synchronizing documents started.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents_process_start_notification.": "{{fileName}} has been submitted for processing. You will be notified when it is completed.", "@sage/xtrem-ap-automation/uploaded_purchasing_documents_upload_documents_failed_scan": "Document has failed the scan."}