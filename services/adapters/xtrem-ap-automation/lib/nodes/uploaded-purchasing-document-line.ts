import type { decimal, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremApAutomation from '../index';

@decorators.node<UploadedPurchasingDocumentLine>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    canCreate: true,
    isVitalCollectionChild: true,
    serviceOptions: () => [xtremApAutomation.serviceOptions.apAutomationOption],
})
export class UploadedPurchasingDocumentLine extends Node {
    @decorators.referenceProperty<UploadedPurchasingDocumentLine, 'parent'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremApAutomation.nodes.UploadedPurchasingDocument,
    })
    readonly parent: Reference<xtremApAutomation.nodes.UploadedPurchasingDocument>;

    @decorators.decimalProperty<UploadedPurchasingDocumentLine, 'amountExcludingTax'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
        defaultValue: 0,
    })
    readonly amountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<UploadedPurchasingDocumentLine, 'amountIncludingTax'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
        defaultValue: 0,
    })
    readonly amountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<UploadedPurchasingDocumentLine, 'taxAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
        defaultValue: 0,
    })
    readonly taxAmount: Promise<decimal>;

    @decorators.decimalProperty<UploadedPurchasingDocumentLine, 'quantity'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantity,
        defaultValue: 0,
    })
    readonly quantity: Promise<decimal>;

    @decorators.referenceProperty<UploadedPurchasingDocumentLine, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Item,
        isNullable: true,
    })
    readonly item: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.stringProperty<UploadedPurchasingDocumentLine, 'itemDescription'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
        isNotEmpty: false,
    })
    readonly itemDescription: Promise<string>;

    @decorators.referenceProperty<UploadedPurchasingDocumentLine, 'purchaseUnit'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        isNullable: true,
    })
    readonly purchaseUnit: Reference<xtremMasterData.nodes.UnitOfMeasure | null>;
}
