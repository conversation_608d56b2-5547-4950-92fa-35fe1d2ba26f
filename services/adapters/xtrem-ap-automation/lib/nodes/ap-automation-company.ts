import type { Context, Reference } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremApAutomation from '../index';

@decorators.node<ApAutomationCompany>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    serviceOptions: () => [xtremApAutomation.serviceOptions.apAutomationOption],
    indexes: [{ orderBy: { company: 1 }, isUnique: true, isNaturalKey: true }],
    isVitalReferenceChild: true,
})
export class ApAutomationCompany extends Node {
    @decorators.referenceProperty<ApAutomationCompany, 'configuration'>({
        isStored: true,
        isPublished: true,
        node: () => xtremApAutomation.nodes.ApAutomationConfiguration,
        isFrozen: true,
    })
    readonly configuration: Reference<xtremApAutomation.nodes.ApAutomationConfiguration>;

    @decorators.referenceProperty<ApAutomationCompany, 'company'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.Company,
        isVitalParent: true,
    })
    readonly company: Reference<xtremSystem.nodes.Company>;

    @decorators.stringProperty<ApAutomationCompany, 'customerUniqueId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.uuid,
    })
    readonly customerUniqueId: Promise<string>;

    @decorators.stringProperty<ApAutomationCompany, 'originalTenantId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.nanoid,
    })
    readonly originalTenantId: Promise<string>;

    @decorators.stringProperty<ApAutomationCompany, 'accountsPayableEmail'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.email,
    })
    readonly accountsPayableEmail: Promise<string>;

    @decorators.stringProperty<ApAutomationCompany, 'employeeExpenseEmail'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.email,
    })
    readonly employeeExpenseEmail: Promise<string>;

    @decorators.booleanProperty<ApAutomationCompany, 'isInboxActive'>({
        isPublished: true,
        isStored: true,
        defaultValue: false,
    })
    readonly isInboxActive: Promise<boolean>;

    /**
     *  Registers a company on Sage AI
     * @param company Company to register
     * @param context Current context
     * @returns array of companies that have customerId
     */
    @decorators.mutation<typeof ApAutomationCompany, 'registerCompanyOnSageAi'>({
        isPublished: true,
        parameters: [
            {
                name: 'company',
                type: 'reference',
                node: () => xtremSystem.nodes.Company,
                isMandatory: true,
                isWritable: true,
            },
        ],
        return: { type: 'boolean' },
    })
    static async registerCompanyOnSageAi(context: Context, company: xtremSystem.nodes.Company): Promise<boolean> {
        // Check if tenant configuration is Active
        await xtremApAutomation.functions.checkTenantConfigurationIsActive(context);

        const tenantInfo = await xtremApAutomation.functions.getTenantInformationByContext(context);

        let apAutomationCompany = await context.tryRead(
            xtremApAutomation.nodes.ApAutomationCompany,
            { company },
            { forUpdate: true },
        );
        const setProperties = {
            ...(await xtremApAutomation.functions.sageAiRegistration(context, {
                name: await company.name,
                country: await (await company.country).id,
            })),
            originalTenantId: tenantInfo.id,
            isInboxActive: true,
        };
        if (apAutomationCompany) {
            // (Re)Activate email address on Sage AI here
            if (
                xtremApAutomation.functions.validateSageAiRegistration({
                    currentTenantId: tenantInfo.id,
                    originalTenantId: await apAutomationCompany.originalTenantId,
                    customerUniqueId: await apAutomationCompany.customerUniqueId,
                })
            ) {
                await apAutomationCompany.$.set({ ...setProperties });
                await apAutomationCompany.$.save();
                return true;
            }
            return false;
        }
        // Ensure if tenant configuration is created
        const configuration = await xtremApAutomation.nodes.ApAutomationConfiguration.createOrGetDefaultRecord(context);
        apAutomationCompany = await context.create(xtremApAutomation.nodes.ApAutomationCompany, {
            ...setProperties,
            company,
            configuration,
        });
        await apAutomationCompany.$.save();
        return true;
    }
}
