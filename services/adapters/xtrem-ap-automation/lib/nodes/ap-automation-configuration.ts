import type { Collection, Context, Reference } from '@sage/xtrem-core';
import { Node, NodeStatus, decorators } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import { dataTypes } from '@sage/xtrem-system';
import { setEmailInboxSageAi } from '../functions/common';
import * as xtremApAutomation from '../index';

@decorators.node<ApAutomationConfiguration>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    serviceOptions: () => [xtremApAutomation.serviceOptions.apAutomationOption],
    indexes: [{ orderBy: { id: 1, customerUniqueId: 1 }, isUnique: true, isNaturalKey: true }],
    async saveBegin() {
        const { context } = this.$;
        const isTenantConfigurationActive = await this.isActive;
        const { companies } = this;

        const tenantInfo = await xtremApAutomation.functions.getTenantInformationByContext(context);

        const tenantIdHasChanged = await xtremApAutomation.functions.isTenantChanged(tenantInfo.id, {
            isActive: isTenantConfigurationActive,
            companies,
            originalTenantId: await this.originalTenantId,
        });

        const customerUniqueId = await this.customerUniqueId;

        if (this.$.status === NodeStatus.modified || tenantIdHasChanged) {
            if (isTenantConfigurationActive) {
                // Activate email inbox
                if (customerUniqueId !== '') {
                    await setEmailInboxSageAi(context, customerUniqueId, true);
                    await this.$.set({ isInboxActive: true });
                }

                if (tenantIdHasChanged || customerUniqueId === '') {
                    await this.$.set({
                        ...(await xtremApAutomation.functions.sageAiRegistration(context, {
                            name: tenantInfo.name,
                            country: (await (await this.country)?.id) ?? 'UK',
                        })),
                        originalTenantId: tenantInfo.id,
                        isInboxActive: true,
                    });
                }

                await this.companies.forEach(
                    async (apAutomationCompany: xtremApAutomation.nodes.ApAutomationCompany) => {
                        await setEmailInboxSageAi(context, await apAutomationCompany.customerUniqueId, false);
                    },
                );

                await context.bulkUpdate(xtremApAutomation.nodes.ApAutomationCompany, {
                    set: { isInboxActive: false },
                });
            } else {
                if (customerUniqueId !== '') {
                    await setEmailInboxSageAi(context, customerUniqueId, false);
                    await this.$.set({ isInboxActive: false });
                }
                // Ensure that a new registration in Sage AI is done when there is a new tenant id(duplication of tenants)
                await this.companies.forEach(async companyConfiguration => {
                    const apAutomationCompany = await context.read(
                        xtremApAutomation.nodes.ApAutomationCompany,
                        { _id: companyConfiguration._id },
                        { forUpdate: true },
                    );
                    if (tenantIdHasChanged || customerUniqueId === '') {
                        const company = await apAutomationCompany.company;
                        await apAutomationCompany.$.set({
                            ...(await xtremApAutomation.functions.sageAiRegistration(context, {
                                name: await company.name,
                                country: await (await company.country).id,
                            })),
                            originalTenantId: tenantInfo.id,
                        });
                    }

                    await setEmailInboxSageAi(context, await companyConfiguration.customerUniqueId, true);
                    await apAutomationCompany.$.set({ isInboxActive: true });

                    await apAutomationCompany.$.save();
                });
            }
        }
    },
})
export class ApAutomationConfiguration extends Node {
    @decorators.stringProperty<ApAutomationConfiguration, 'id'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dataType: () => dataTypes.id,
        defaultValue: 'default',
    })
    readonly id: Promise<string>;

    @decorators.referenceProperty<ApAutomationConfiguration, 'country'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dependsOn: ['isActive'],
        node: () => xtremStructure.nodes.Country,
        async isFrozen() {
            return (await this.customerUniqueId) !== '';
        },
        async control(cx, val) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-ap-automation/nodes__ap_automation_configuration__country_is_mandatory',
                    'Country is mandatory.',
                )
                .if(this._id > 0 && (await this.isActive) && val === null)
                .is.true();
        },
    })
    readonly country: Reference<xtremStructure.nodes.Country | null>;

    @decorators.stringProperty<ApAutomationConfiguration, 'customerUniqueId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.uuid,
    })
    readonly customerUniqueId: Promise<string>;

    @decorators.stringProperty<ApAutomationConfiguration, 'originalTenantId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.nanoid,
        defaultValue() {
            return this.$.context.tenantId;
        },
    })
    readonly originalTenantId: Promise<string>;

    @decorators.stringProperty<ApAutomationConfiguration, 'accountsPayableEmail'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.email,
    })
    readonly accountsPayableEmail: Promise<string>;

    @decorators.stringProperty<ApAutomationConfiguration, 'employeeExpenseEmail'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.email,
    })
    readonly employeeExpenseEmail: Promise<string>;

    @decorators.booleanProperty<ApAutomationConfiguration, 'isInboxActive'>({
        isPublished: true,
        isStored: true,
        defaultValue: false,
    })
    readonly isInboxActive: Promise<boolean>;

    @decorators.enumProperty<ApAutomationConfiguration, 'configurationLevel'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        defaultValue: 'company',
        dataType: () => xtremApAutomation.enums.ApAutomationConfigurationLevelDataType,
    })
    readonly configurationLevel: Promise<xtremApAutomation.enums.ApAutomationConfigurationLevel>;

    @decorators.booleanProperty<ApAutomationConfiguration, 'isActive'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            return (await this.configurationLevel) === 'tenant';
        },
    })
    readonly isActive: Promise<boolean>;

    @decorators.collectionProperty<ApAutomationConfiguration, 'companies'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremApAutomation.nodes.ApAutomationCompany,
        reverseReference: 'configuration',
    })
    readonly companies: Collection<xtremApAutomation.nodes.ApAutomationCompany>;

    /**
     *  Check if there is any configuration active for the current tenantId
     * @returns boolean
     */
    @decorators.query<typeof ApAutomationConfiguration, 'isConfigurationActive'>({
        isPublished: true,
        parameters: [],
        return: { type: 'boolean' },
    })
    static async isConfigurationActive(context: Context): Promise<boolean> {
        const tenantInfo = await xtremApAutomation.functions.getTenantInformationByContext(context);
        const apAutomationConfigurationTenant = await xtremApAutomation.functions.getDefaultConfiguration(context);
        if (apAutomationConfigurationTenant) {
            const isTenantConfigurationActive = await apAutomationConfigurationTenant.isActive;
            const { companies } = apAutomationConfigurationTenant;
            if (
                await xtremApAutomation.functions.isTenantChanged(tenantInfo.id, {
                    isActive: isTenantConfigurationActive,
                    companies,
                    originalTenantId: await apAutomationConfigurationTenant.originalTenantId,
                })
            ) {
                return false;
            }
            return (
                isTenantConfigurationActive ||
                (await companies.filter(
                    async companyConfiguration => (await companyConfiguration.customerUniqueId) !== '',
                ).length) > 0
            );
        }
        return false;
    }

    /**
     *  Resets the configuration to it's default
     * @returns boolean
     */
    @decorators.mutation<typeof ApAutomationConfiguration, 'resetConfiguration'>({
        isPublished: true,
        parameters: [],
        return: { type: 'boolean' },
    })
    static async resetConfiguration(context: Context): Promise<boolean> {
        const configurationByTenant = await context.tryRead(
            xtremApAutomation.nodes.ApAutomationConfiguration,
            { id: 'default' },
            { forUpdate: true },
        );
        if (configurationByTenant) {
            const propertiesToUpdate = {
                customerUniqueId: '',
                accountsPayableEmail: '',
                employeeExpenseEmail: '',
                originalTenantId: '',
                isInboxActive: false,
            };
            await configurationByTenant?.$.set({ ...propertiesToUpdate, configurationLevel: 'company' });
            await configurationByTenant?.$.save();

            await context.bulkUpdate(xtremApAutomation.nodes.ApAutomationCompany, {
                set: { ...propertiesToUpdate },
            });
            return true;
        }
        return false;
    }

    /**
     *  Create or returns the default configuration
     * @returns boolean
     */
    @decorators.mutation<typeof ApAutomationConfiguration, 'createOrGetDefaultRecord'>({
        isPublished: true,
        parameters: [],
        return: { type: 'integer' },
    })
    static async createOrGetDefaultRecord(context: Context): Promise<number> {
        let configurationByTenant = await context.tryRead(xtremApAutomation.nodes.ApAutomationConfiguration, {
            id: 'default',
        });
        if (!configurationByTenant) {
            configurationByTenant = await context.create(xtremApAutomation.nodes.ApAutomationConfiguration, {
                customerUniqueId: '',
                accountsPayableEmail: '',
                employeeExpenseEmail: '',
                originalTenantId: '',
                country: null,
                isInboxActive: false,
            });
            await configurationByTenant.$.save();
        }
        return configurationByTenant._id;
    }
}
