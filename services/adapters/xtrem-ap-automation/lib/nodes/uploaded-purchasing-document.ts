import type { Collection, Context, decimal, Reference } from '@sage/xtrem-core';
import { BinaryStream, date, decorators, Node, NodeStatus, unsafeRandomizeCharacters } from '@sage/xtrem-core';
import { InfrastructureHelper } from '@sage/xtrem-infrastructure-adapter';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremScheduler from '@sage/xtrem-scheduler';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import type { Readable } from 'stream';
import { buffer } from 'stream/consumers';
import { isCustomerUniqueIdActive } from '../functions/common';
import {
    allowedMimeTypes,
    checkIfPurchaseDocumentExists,
    checkIfReviewDone,
    getSageAiStatus,
    notifyUserBulkConfirm,
    notifyUserDocumentScanFailed,
    refreshDocument,
    sleepMillis,
    validateCreatePurchaseDocumentFromUploadedPurchasingDocument,
} from '../functions/uploaded-purchasing-document';
import * as xtremApAutomation from '../index';

@decorators.node<UploadedPurchasingDocument>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    canCreate: true,
    canDelete: true,
    serviceOptions: () => [xtremApAutomation.serviceOptions.apAutomationOption],
    indexes: [{ orderBy: { uuid: 1 }, isUnique: true, isNaturalKey: true }],
    isClearedByReset: true,
    async saveBegin() {
        // If the document is modified and the status is error or draft, we need to set it to review_in_progress
        if (this.$.status === NodeStatus.modified && !(await this.isTempUuid)) {
            const status = xtremApAutomation.events.saveBegin.UploadedPurchasingDocument.updateStatusToReviewInProgress(
                {
                    status: { current: await this.status, old: await (await this.$.old).status },
                    oldIsResultPopulated: await (await this.$.old).isResultPopulated,
                },
            );
            if (status) {
                await this.$.set({ status });
            }
        }
    },
    async controlEnd(cx) {
        const { context } = this.$;
        if (this.$.status === NodeStatus.added) {
            await xtremApAutomation.events.controlEnd.UploadedPurchasingDocument.customerUniqueIdValidation(
                context,
                cx,
                this,
            );
        }

        // Check if the document number has already
        const supplier = await this.supplier;
        if (supplier) {
            await xtremPurchasing.events.controls.documentNumberExistsForSupplier(context, cx, {
                supplierId: supplier._id,
                supplierDocumentNumber: await this.supplierDocumentNumber,
                nodeToQuery: (await this.type) === 'creditMemo' ? 'PurchaseCreditMemo' : 'PurchaseInvoice',
                currentDocumentSysId:
                    (await this.purchaseInvoice.at(0))?._id ?? (await this.purchaseCreditMemo.at(0))?._id,
            });
        }
    },
    async saveEnd() {
        const uploadedFile = await this.uploadedFile;
        // We call listener  process again because if verified before record is created  the document will not be processed yet
        if ((await uploadedFile?.status) === 'verified' && (await this.isTempUuid)) {
            await this.$.context.notify('UploadedFile/processUpload', {
                uploadedFileId: uploadedFile?._id,
            });
        }
    },
    async controlDelete(cx) {
        const processingStatus = await this.processingStatus;
        await cx.error
            .withMessage(
                '@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__cannot_delete_record',
                'You cannot delete an uploaded purchasing document while it is being processed or if the review is done.',
            )
            .if(
                ((await (await this.uploadedFile)?.status) === 'verified' && processingStatus === 'processing') ||
                    (processingStatus === 'completed' && (await this.isReviewDone)),
            )
            .is.true();

        await cx.error
            .withMessage(
                '@sage/xtrem-ap-automation/nodes__uploaded_purchasing_document__cannot_delete_record_customer_id_inactive_nonexistent',
                'Your customer ID is missing or inactive.',
            )
            .if(
                await isCustomerUniqueIdActive(this.$.context, {
                    customerUniqueId: await this.customerUniqueId,
                    tenantId: this.$.context.tenantId,
                }),
            )
            .is.false();
    },
    async deleteBegin() {
        const { context } = this.$;
        await xtremApAutomation.events.deleteBegin.UploadedPurchasingDocument.deleteDocumentSageAi(context, {
            processingStatus: await this.processingStatus,
            isReviewDone: await this.isReviewDone,
            customerUniqueId: await this.customerUniqueId,
            uuid: await this.uuid,
        });
    },
})
export class UploadedPurchasingDocument extends Node {
    @decorators.stringProperty<UploadedPurchasingDocument, 'uuid'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.uuid,
        defaultValue() {
            return xtremSystem.functions.generateNanoId();
        },
        async isFrozen() {
            return !(await this.isTempUuid);
        },
    })
    readonly uuid: Promise<string>;

    @decorators.stringProperty<UploadedPurchasingDocument, 'supplierDocumentNumber'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: false,
        dataType: () => xtremPurchasing.dataTypes.supplierDocumentNumberDataType,
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
    })
    readonly supplierDocumentNumber: Promise<string | null>;

    @decorators.collectionProperty<UploadedPurchasingDocument, 'lines'>({
        isPublished: true,
        reverseReference: 'parent',
        isVital: true,
        node: () => xtremApAutomation.nodes.UploadedPurchasingDocumentLine,
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
    })
    readonly lines: Collection<xtremApAutomation.nodes.UploadedPurchasingDocumentLine>;

    @decorators.binaryStreamProperty<UploadedPurchasingDocument, 'document'>({
        isStored: true,
        isPublished: true,
        async isFrozen() {
            return (await (await this.$.old).document).value.length !== 0;
        },
    })
    readonly document: Promise<BinaryStream>;

    @decorators.referenceProperty<UploadedPurchasingDocument, 'uploadedFile'>({
        isStored: true,
        isPublished: true,
        node: () => xtremUpload.nodes.UploadedFile,
        isNullable: true,
        isFrozen: true,
        async control(cx, value) {
            if (value && !allowedMimeTypes.includes(await value.mimeType)) {
                cx.error.addLocalized(
                    '@sage/xtrem-ap-automation/mime_type_not_allowed',
                    'You cannot use this file type.',
                );
            }
        },
    })
    readonly uploadedFile: Reference<xtremUpload.nodes.UploadedFile | null>;

    @decorators.stringProperty<UploadedPurchasingDocument, 'documentMimeType'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.mimeType,
        defaultValue: 'application/pdf',
        async isFrozen() {
            const origin = await this.origin;
            return (
                (origin === 'email' && (await this.isResultPopulated)) ||
                (origin === 'uploaded' && !(await this.isTempUuid))
            );
        },
    })
    readonly documentMimeType: Promise<string>;

    @decorators.referenceProperty<UploadedPurchasingDocument, 'supplier'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Supplier,
        isNullable: true,
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.referenceProperty<UploadedPurchasingDocument, 'site'>({
        filters: { control: { isFinance: true } },
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        isNullable: true,
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
        provides: ['site'],
    })
    readonly site: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<UploadedPurchasingDocument, 'currency'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        isNullable: true,
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency | null>;

    @decorators.dateProperty<UploadedPurchasingDocument, 'dueDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
    })
    readonly dueDate: Promise<date | null>;

    @decorators.dateProperty<UploadedPurchasingDocument, 'supplierDocumentDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
    })
    readonly supplierDocumentDate: Promise<date | null>;

    @decorators.decimalProperty<UploadedPurchasingDocument, 'totalTaxAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
        defaultValue: 0,
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
    })
    readonly totalTaxAmount: Promise<decimal>;

    @decorators.decimalProperty<UploadedPurchasingDocument, 'totalAmountExcludingTax'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        defaultValue: 0,
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
    })
    readonly totalAmountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<UploadedPurchasingDocument, 'totalAmountIncludingTax'>({
        isPublished: true,
        isStored: true,
        defaultValue: 0,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
    })
    readonly totalAmountIncludingTax: Promise<decimal>;

    @decorators.enumProperty<UploadedPurchasingDocument, 'processingStatus'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremApAutomation.enums.apAutomationProcessingStatusType,
        dependsOn: ['uuid', 'isResultPopulated', 'isTempUuid', 'customerUniqueId'],
        async computeValue() {
            if (await this.isResultPopulated) {
                return 'completed';
            }
            if ((await this.isTempUuid) || this._id < 0) {
                return 'processing';
            }
            const documentStatus = await getSageAiStatus(this.$.context, {
                document: await this.uuid,
                customer: await this.customerUniqueId,
            });
            return documentStatus.status ?? 'error';
        },
    })
    readonly processingStatus: Promise<xtremApAutomation.enums.ApAutomationProcessingStatus | null>;

    @decorators.stringProperty<UploadedPurchasingDocument, 'processingDetails'>({
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
        dependsOn: ['uuid', 'isResultPopulated', 'isTempUuid', 'customerUniqueId'],
        async computeValue() {
            if (await this.isResultPopulated) {
                return '';
            }

            if ((await this.isTempUuid) || this._id < 0) {
                return '';
            }

            const documentStatus = await getSageAiStatus(this.$.context, {
                document: await this.uuid,
                customer: await this.customerUniqueId,
            });
            return documentStatus.details ?? documentStatus.detail ?? '';
        },
    })
    readonly processingDetails: Promise<string>;

    @decorators.enumProperty<UploadedPurchasingDocument, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremApAutomation.enums.apAutomationUploadedDocumentStatusType,
        defaultValue: 'draft',
    })
    readonly status: Promise<xtremApAutomation.enums.ApAutomationUploadedDocumentStatus>;

    @decorators.booleanProperty<UploadedPurchasingDocument, 'isResultPopulated'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        isFrozen() {
            return this.isReviewDone;
        },
    })
    readonly isResultPopulated: Promise<boolean>;

    @decorators.enumProperty<UploadedPurchasingDocument, 'type'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremApAutomation.enums.apAutomationDocumentTypeType,
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
    })
    readonly type: Promise<xtremApAutomation.enums.ApAutomationDocumentType | null>;

    @decorators.enumProperty<UploadedPurchasingDocument, 'origin'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremApAutomation.enums.apAutomationDocumentOriginType,
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
    })
    readonly origin: Promise<xtremApAutomation.enums.ApAutomationDocumentOrigin | null>;

    @decorators.jsonProperty<UploadedPurchasingDocument, 'result'>({
        isPublished: true,
        isStored: true,
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
    })
    readonly result: Promise<xtremApAutomation.interfaces.ApAutomationResult>;

    @decorators.collectionProperty<UploadedPurchasingDocument, 'purchaseInvoice'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseInvoice,
        join: {
            uploadedDocument() {
                return this._id;
            },
        },
    })
    readonly purchaseInvoice: Collection<xtremPurchasing.nodes.PurchaseInvoice>;

    @decorators.collectionProperty<UploadedPurchasingDocument, 'purchaseCreditMemo'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseCreditMemo,
        join: {
            uploadedDocument() {
                return this._id;
            },
        },
    })
    readonly purchaseCreditMemo: Collection<xtremPurchasing.nodes.PurchaseCreditMemo>;

    @decorators.dateProperty<UploadedPurchasingDocument, 'postingDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        defaultValue() {
            return date.today();
        },
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
    })
    readonly postingDate: Promise<date | null>;

    @decorators.referenceProperty<UploadedPurchasingDocument, 'reason'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.ReasonCode,
        async isFrozen() {
            return (await this.isReviewDone) || !(await this.isResultPopulated);
        },
    })
    readonly reason: Reference<xtremMasterData.nodes.ReasonCode | null>;

    @decorators.booleanProperty<UploadedPurchasingDocument, 'isReviewDone'>({
        isPublished: true,
        async getValue() {
            return (await this.status) === 'reviewDone';
        },
    })
    readonly isReviewDone: Promise<boolean>;

    @decorators.booleanProperty<UploadedPurchasingDocument, 'isTempUuid'>({
        isPublished: true,
        isStored: true,
        defaultValue: false,
    })
    readonly isTempUuid: Promise<boolean>;

    @decorators.stringProperty<UploadedPurchasingDocument, 'customerUniqueId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.uuid,
        isFrozen: true,
    })
    readonly customerUniqueId: Promise<string>;

    @decorators.stringProperty<UploadedPurchasingDocument, 'recipientEmail'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.email,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'custom',
        anonymizeValue(value) {
            return value.endsWith('@localhost.domain') ? value : unsafeRandomizeCharacters(value);
        },
    })
    readonly recipientEmail: Promise<string>;

    @decorators.stringProperty<UploadedPurchasingDocument, 'senderEmail'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.email,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'custom',
        anonymizeValue(value) {
            return value.endsWith('@localhost.domain') ? value : unsafeRandomizeCharacters(value);
        },
    })
    readonly senderEmail: Promise<string>;

    @decorators.referenceProperty<UploadedPurchasingDocument, 'apAutomationCompany'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremApAutomation.nodes.ApAutomationCompany,
        async computeValue() {
            const apAutomationCompany = await this.$.context
                .query(xtremApAutomation.nodes.ApAutomationCompany, {
                    filter: { customerUniqueId: await this.customerUniqueId },
                })
                .toArray();
            return apAutomationCompany[0] ?? null;
        },
    })
    readonly apAutomationCompany: Reference<xtremApAutomation.nodes.ApAutomationCompany | null>;

    @decorators.referenceArrayProperty<UploadedPurchasingDocument, 'linkedSites'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['apAutomationCompany'],
        async computeValue() {
            const getDefaultConfiguration = await xtremApAutomation.functions.getDefaultConfiguration(this.$.context);
            const apConfig = !(await getDefaultConfiguration?.isActive)
                ? ((await this.apAutomationCompany) ?? null)
                : null;
            const company = await apConfig?.company;
            return company
                ? this.$.context
                      .query(xtremSystem.nodes.Site, {
                          filter: { legalCompany: company._id },
                      })
                      .toArray()
                : [];
        },
    })
    readonly linkedSites: Promise<xtremSystem.nodes.Site[]>;

    @decorators.mutation<typeof UploadedPurchasingDocument, 'updateDocumentWithDetails'>({
        isPublished: true,
        parameters: [
            {
                name: 'document',
                type: 'reference',
                node: () => UploadedPurchasingDocument,
                isMandatory: true,
                isWritable: true,
            },
        ],
        return: { type: 'boolean' },
    })
    static async updateDocumentWithDetails(context: Context, document: UploadedPurchasingDocument): Promise<boolean> {
        const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
        await manager.populatePendingDocumentWithDetails(document);
        return true;
    }

    @decorators.asyncMutation<typeof UploadedPurchasingDocument, 'syncDocumentsTask'>({
        isPublished: true,
        startsReadOnly: false,
        isSchedulable: true,
        parameters: [],
        return: { type: 'boolean' },
    })
    static async syncDocumentsTask(context: Context): Promise<boolean> {
        await context.batch.updateProgress({
            detail: 'synchronized',
            errorCount: 0,
            successCount: 0,
            totalCount: 0,
            phase: 'start',
        });

        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-ap-automation/uploaded_purchasing_documents__sync_documents_task__start',
                'Synchronizing documents started.',
            ),
        );
        const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);

        const importedDocuments = await xtremApAutomation.functions.importDocuments(context, manager, true);

        if (importedDocuments) {
            await manager.populateAllPendingDocumentsWithDetails(true);
        }
        await context.batch.logMessage(
            'result',
            context.localize(
                '@sage/xtrem-ap-automation/uploaded_purchasing_documents__sync_documents_task__end',
                'Synchronizing documents completed.',
            ),
        );

        return Promise.resolve(true);
    }

    @decorators.mutation<typeof UploadedPurchasingDocument, 'syncDocuments'>({
        isPublished: true,
        parameters: [],
        return: { type: 'boolean' },
    })
    static async syncDocuments(context: Context): Promise<boolean> {
        const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);

        const importedDocuments = await xtremApAutomation.functions.importDocuments(context, manager, false);

        if (importedDocuments) {
            await manager.populateAllPendingDocumentsWithDetails();
        }

        return true;
    }

    @decorators.mutation<typeof UploadedPurchasingDocument, 'createPurchaseDocumentFromUploadedPurchasingDocument'>({
        isPublished: true,
        parameters: [
            {
                name: 'uploadedPurchasingDocument',
                type: 'reference',
                node: () => UploadedPurchasingDocument,
                isMandatory: true,
            },
        ],
        // startsReadOnly: true,
        return: {
            name: 'supplier',
            type: 'object',
            properties: { _id: 'integer', number: 'string' },
        },
    })
    static async createPurchaseDocumentFromUploadedPurchasingDocument(
        context: Context,
        uploadedPurchasingDocument: UploadedPurchasingDocument,
    ): Promise<xtremApAutomation.interfaces.CreatePurchaseDocumentFromUploadedPurchasingDocumentResult> {
        await checkIfPurchaseDocumentExists(
            context,
            uploadedPurchasingDocument.purchaseInvoice,
            uploadedPurchasingDocument.purchaseCreditMemo,
        );

        await validateCreatePurchaseDocumentFromUploadedPurchasingDocument(context, uploadedPurchasingDocument);

        const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
        const purchaseDocument = await manager.confirmDocument(uploadedPurchasingDocument);

        return {
            _id: purchaseDocument._id,
            number: (await purchaseDocument?.number) ?? '',
            documentType: (await uploadedPurchasingDocument.type) ?? undefined,
        };
    }

    @decorators.query<typeof UploadedPurchasingDocument, 'isSupplierDocumentNumberUsed'>({
        isPublished: true,
        parameters: [
            {
                name: 'supplier',
                type: 'object',
                properties: {
                    id: 'integer',
                    supplierDocumentNumber: 'string',
                    nodeToQuery: 'string',
                },
            },
        ],
        return: { type: 'boolean' },
    })
    static async isSupplierDocumentNumberUsed(
        context: Context,
        supplier: xtremPurchasing.interfaces.DocumentForSupplierDocumentNumberValidation,
    ): Promise<boolean> {
        const supplierDocumentNumberCount = await xtremPurchasing.functions.queryCountSupplierDocumentNumberBySupplier(
            context,
            { ...supplier },
        );
        return supplierDocumentNumberCount > 0;
    }

    @decorators.notificationListener<typeof UploadedPurchasingDocument>({
        topic: 'SysServiceOptionState/apAutomationOption/activate',
    })
    static async apAutomationOptionActivate(context: Context): Promise<void> {
        const jobSchedule = await context.read(
            xtremScheduler.nodes.SysJobSchedule,
            { id: 'syncDocumentsTask_1' },
            { forUpdate: true },
        );
        await jobSchedule.$.set({ isActive: true });
        await jobSchedule.$.save();
    }

    @decorators.notificationListener<typeof UploadedPurchasingDocument>({
        topic: 'SysServiceOptionState/apAutomationOption/deactivate',
    })
    static async apAutomationOptionDeactivate(context: Context): Promise<void> {
        const jobSchedule = await context.read(
            xtremScheduler.nodes.SysJobSchedule,
            { id: 'syncDocumentsTask_1' },
            { forUpdate: true },
        );
        await jobSchedule.$.set({ isActive: false });
        await jobSchedule.$.save();
    }

    @decorators.mutation<typeof UploadedPurchasingDocument, 'resetUploadedDocument'>({
        isPublished: true,
        parameters: [
            {
                name: 'uploadedPurchasingDocument',
                type: 'reference',
                node: () => UploadedPurchasingDocument,
                isMandatory: true,
                isWritable: true,
            },
        ],
        return: 'boolean',
    })
    static async resetUploadedDocument(
        context: Context,
        uploadedPurchasingDocument: UploadedPurchasingDocument,
    ): Promise<boolean> {
        await checkIfReviewDone(context, uploadedPurchasingDocument.status);
        await checkIfPurchaseDocumentExists(
            context,
            uploadedPurchasingDocument.purchaseInvoice,
            uploadedPurchasingDocument.purchaseCreditMemo,
        );

        await uploadedPurchasingDocument.$.set({
            ...(await xtremApAutomation.functions.processResult(context, await uploadedPurchasingDocument.result)),
            status: 'draft',
        });
        await uploadedPurchasingDocument.$.save();
        return true;
    }

    @decorators.notificationListener<typeof UploadedPurchasingDocument>({
        topic: 'UploadedFile/processUpload',
        startsReadOnly: true,
    })
    static async onProcessUpload(context: Context, payload: { uploadedFileId: number }): Promise<void> {
        const uploadedPurchasingDocument = await xtremApAutomation.functions.uploadedPurchasingDocumentByUploadedFile(
            context,
            payload.uploadedFileId,
        );

        if (
            uploadedPurchasingDocument &&
            (await (await uploadedPurchasingDocument?.uploadedFile)?.status) === 'verified'
        ) {
            await context.runInWritableContext(async writableContext => {
                await xtremApAutomation.nodes.UploadedPurchasingDocument.uploadDocument(
                    writableContext,
                    uploadedPurchasingDocument,
                );
            });
        }
    }

    @decorators.asyncMutation<typeof UploadedPurchasingDocument, 'uploadDocument'>({
        isPublished: true,
        startsReadOnly: false,
        parameters: [
            {
                name: 'uploadedPurchasingDocument',
                type: 'reference',
                node: () => xtremApAutomation.nodes.UploadedPurchasingDocument,
                isMandatory: true,
            },
        ],
        return: { type: 'boolean' },
    })
    static async uploadDocument(
        context: Context,
        uploadedPurchasingDocument: xtremApAutomation.nodes.UploadedPurchasingDocument,
    ): Promise<boolean> {
        const uploadedPurchasingDocumentUpdate = await context.read(
            xtremApAutomation.nodes.UploadedPurchasingDocument,
            { _id: uploadedPurchasingDocument._id },
            { forUpdate: true },
        );

        const uploadedFile = await uploadedPurchasingDocumentUpdate.uploadedFile;

        if (uploadedFile && (await uploadedFile.status) === 'verified') {
            const contextId = await uploadedFile.key;

            const s3ReadResult = await InfrastructureHelper.readAsyncUploadResult(
                context,
                contextId,
                'CONTEXT_FILE_UPLOAD',
            );

            if (!s3ReadResult?.body) throw Error('No body in s3ReadResult');

            const data = await buffer(s3ReadResult.body as Readable);

            const document = BinaryStream.fromBuffer(data);

            const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
            const uuid = await manager.sendDocument({
                file: document,
                fileType: await uploadedFile.mimeType,
                customerUniqueId: await uploadedPurchasingDocument.customerUniqueId,
            });

            await uploadedPurchasingDocumentUpdate.$.set({
                uuid,
                documentMimeType: await uploadedFile.mimeType,
                status: 'draft',
                document,
                isTempUuid: false,
            });

            await uploadedPurchasingDocumentUpdate.$.save();

            await sleepMillis(10000); // We need to wait for the document to be processed by Sage AI
            await refreshDocument(context, uploadedPurchasingDocumentUpdate, uploadedFile);
        } else if (uploadedFile) {
            await notifyUserDocumentScanFailed(context, await uploadedFile.filename);
            await uploadedPurchasingDocumentUpdate.$.delete(); // here we delete document that failed to be scanned
        }

        return Promise.resolve(true);
    }

    @decorators.bulkMutation<typeof UploadedPurchasingDocument, 'confirmBulk'>({
        isPublished: true,
        async onComplete(context, documents) {
            await notifyUserBulkConfirm(context, documents);
        },
    })
    static confirmBulk(context: Context, document: UploadedPurchasingDocument) {
        return xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
            context,
            document,
        );
    }
}
