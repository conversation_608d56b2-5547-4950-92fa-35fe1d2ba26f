declare module '@sage/xtrem-ap-automation-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { Package as SageXtremDistribution$Package } from '@sage/xtrem-distribution-api';
    import type { Package as SageXtremFinance$Package } from '@sage/xtrem-finance-api';
    import type {
        BankAccount,
        BaseOpenItem,
        CompanyAttributeType,
        CompanyAttributeTypeBinding,
        CompanyAttributeTypeInput,
        CompanyDefaultAttribute,
        CompanyDefaultAttributeBinding,
        CompanyDefaultAttributeInput,
        CompanyDefaultDimension,
        CompanyDefaultDimensionBinding,
        CompanyDefaultDimensionInput,
        CompanyDimensionType,
        CompanyDimensionTypeBinding,
        CompanyDimensionTypeInput,
        FinanceTransaction,
        Package as SageXtremFinanceData$Package,
        PaymentTracking,
        PaymentTrackingBinding,
        PaymentTrackingInput,
        PostingClass,
    } from '@sage/xtrem-finance-data-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type { Package as SageXtremLandedCost$Package } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        Address,
        BusinessEntityAddress,
        CompanyAddress,
        CompanyAddressBinding,
        CompanyAddressInput,
        CompanyContact,
        CompanyContactBinding,
        CompanyContactInput,
        Contact,
        Currency,
        Item,
        Package as SageXtremMasterData$Package,
        PaymentTerm,
        ReasonCode,
        Supplier,
        UnitOfMeasure,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type {
        Package as SageXtremPurchasing$Package,
        PurchaseCreditMemo,
        PurchaseCreditMemoInput,
        PurchaseCreditMemoLine,
        PurchaseCreditMemoLineBinding,
        PurchaseCreditMemoLineInput,
        PurchaseInvoice,
        PurchaseInvoiceInput,
        PurchaseInvoiceLine,
        PurchaseInvoiceLineBinding,
        PurchaseInvoiceLineInput,
    } from '@sage/xtrem-purchasing-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremSales$Package } from '@sage/xtrem-sales-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type { Package as SageXtremStockData$Package } from '@sage/xtrem-stock-data-api';
    import type {
        ChartOfAccount,
        Country,
        Legislation,
        Package as SageXtremStructure$Package,
    } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type { Company, Package as SageXtremSystem$Package, Site, User } from '@sage/xtrem-system-api';
    import type {
        DocumentTax,
        DocumentTaxBinding,
        DocumentTaxInput,
        Package as SageXtremTax$Package,
    } from '@sage/xtrem-tax-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
        UploadedFile,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface ApAutomationConfigurationLevel$Enum {
        tenant: 0;
        company: 1;
    }
    export type ApAutomationConfigurationLevel = keyof ApAutomationConfigurationLevel$Enum;
    export interface ApAutomationDocumentOrigin$Enum {
        uploaded: 0;
        email: 1;
    }
    export type ApAutomationDocumentOrigin = keyof ApAutomationDocumentOrigin$Enum;
    export interface ApAutomationDocumentType$Enum {
        invoice: 0;
        creditMemo: 1;
    }
    export type ApAutomationDocumentType = keyof ApAutomationDocumentType$Enum;
    export interface ApAutomationProcessingStatus$Enum {
        processing: 0;
        completed: 1;
        error: 2;
    }
    export type ApAutomationProcessingStatus = keyof ApAutomationProcessingStatus$Enum;
    export interface ApAutomationUploadedDocumentStatus$Enum {
        draft: 0;
        reviewInProgress: 1;
        reviewDone: 2;
        error: 3;
    }
    export type ApAutomationUploadedDocumentStatus = keyof ApAutomationUploadedDocumentStatus$Enum;
    export interface ApAutomationCompany extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        configuration: ApAutomationConfiguration;
        company: Company;
        customerUniqueId: string;
        originalTenantId: string;
        accountsPayableEmail: string;
        employeeExpenseEmail: string;
        isInboxActive: boolean;
    }
    export interface ApAutomationCompanyInput extends VitalClientNodeInput {
        configuration?: integer | string;
        customerUniqueId?: string;
        originalTenantId?: string;
        accountsPayableEmail?: string;
        employeeExpenseEmail?: string;
        isInboxActive?: boolean | string;
    }
    export interface ApAutomationCompanyBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        configuration: ApAutomationConfiguration;
        company: Company;
        customerUniqueId: string;
        originalTenantId: string;
        accountsPayableEmail: string;
        employeeExpenseEmail: string;
        isInboxActive: boolean;
    }
    export interface ApAutomationCompany$Mutations {
        registerCompanyOnSageAi: Node$Operation<
            {
                company: string;
            },
            boolean
        >;
    }
    export interface ApAutomationCompany$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ApAutomationCompany$Lookups {
        configuration: QueryOperation<ApAutomationConfiguration>;
    }
    export interface ApAutomationCompany$Operations {
        query: QueryOperation<ApAutomationCompany>;
        read: ReadOperation<ApAutomationCompany>;
        aggregate: {
            read: AggregateReadOperation<ApAutomationCompany>;
            query: AggregateQueryOperation<ApAutomationCompany>;
        };
        update: UpdateOperation<ApAutomationCompanyInput, ApAutomationCompany>;
        updateById: UpdateByIdOperation<ApAutomationCompanyInput, ApAutomationCompany>;
        mutations: ApAutomationCompany$Mutations;
        asyncOperations: ApAutomationCompany$AsyncOperations;
        lookups(dataOrId: string | { data: ApAutomationCompanyInput }): ApAutomationCompany$Lookups;
        getDefaults: GetDefaultsOperation<ApAutomationCompany>;
    }
    export interface ApAutomationConfiguration extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        customerUniqueId: string;
        originalTenantId: string;
        accountsPayableEmail: string;
        employeeExpenseEmail: string;
        isInboxActive: boolean;
        configurationLevel: ApAutomationConfigurationLevel;
        isActive: boolean;
        companies: ClientCollection<ApAutomationCompany>;
        country: Country;
    }
    export interface ApAutomationConfigurationInput extends ClientNodeInput {
        id?: string;
        customerUniqueId?: string;
        originalTenantId?: string;
        accountsPayableEmail?: string;
        employeeExpenseEmail?: string;
        isInboxActive?: boolean | string;
        configurationLevel?: ApAutomationConfigurationLevel;
        country?: integer | string;
    }
    export interface ApAutomationConfigurationBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        customerUniqueId: string;
        originalTenantId: string;
        accountsPayableEmail: string;
        employeeExpenseEmail: string;
        isInboxActive: boolean;
        configurationLevel: ApAutomationConfigurationLevel;
        isActive: boolean;
        companies: ClientCollection<ApAutomationCompany>;
        country: Country;
    }
    export interface ApAutomationConfiguration$Queries {
        isConfigurationActive: Node$Operation<{}, boolean>;
    }
    export interface ApAutomationConfiguration$Mutations {
        resetConfiguration: Node$Operation<{}, boolean>;
        createOrGetDefaultRecord: Node$Operation<{}, integer>;
    }
    export interface ApAutomationConfiguration$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ApAutomationConfiguration$Lookups {
        country: QueryOperation<Country>;
    }
    export interface ApAutomationConfiguration$Operations {
        query: QueryOperation<ApAutomationConfiguration>;
        read: ReadOperation<ApAutomationConfiguration>;
        aggregate: {
            read: AggregateReadOperation<ApAutomationConfiguration>;
            query: AggregateQueryOperation<ApAutomationConfiguration>;
        };
        queries: ApAutomationConfiguration$Queries;
        update: UpdateOperation<ApAutomationConfigurationInput, ApAutomationConfiguration>;
        updateById: UpdateByIdOperation<ApAutomationConfigurationInput, ApAutomationConfiguration>;
        mutations: ApAutomationConfiguration$Mutations;
        asyncOperations: ApAutomationConfiguration$AsyncOperations;
        lookups(dataOrId: string | { data: ApAutomationConfigurationInput }): ApAutomationConfiguration$Lookups;
        getDefaults: GetDefaultsOperation<ApAutomationConfiguration>;
    }
    export interface UploadedPurchasingDocument extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        uuid: string;
        supplierDocumentNumber: string;
        lines: ClientCollection<UploadedPurchasingDocumentLine>;
        document: BinaryStream;
        uploadedFile: UploadedFile;
        documentMimeType: string;
        supplier: Supplier;
        site: Site;
        currency: Currency;
        dueDate: string;
        supplierDocumentDate: string;
        totalTaxAmount: string;
        totalAmountExcludingTax: string;
        totalAmountIncludingTax: string;
        status: ApAutomationUploadedDocumentStatus;
        isResultPopulated: boolean;
        type: ApAutomationDocumentType;
        origin: ApAutomationDocumentOrigin;
        result: string;
        purchaseInvoice: ClientCollection<PurchaseInvoice>;
        purchaseCreditMemo: ClientCollection<PurchaseCreditMemo>;
        postingDate: string;
        reason: ReasonCode;
        isReviewDone: boolean;
        isTempUuid: boolean;
        customerUniqueId: string;
        recipientEmail: string;
        senderEmail: string;
        apAutomationCompany: ApAutomationCompany;
        linkedSites: Site[];
        processingStatus: ApAutomationProcessingStatus;
        processingDetails: string;
    }
    export interface UploadedPurchasingDocumentInput extends ClientNodeInput {
        uuid?: string;
        supplierDocumentNumber?: string;
        lines?: Partial<UploadedPurchasingDocumentLineInput>[];
        document?: BinaryStream;
        uploadedFile?: integer | string;
        documentMimeType?: string;
        supplier?: integer | string;
        site?: integer | string;
        currency?: integer | string;
        dueDate?: string;
        supplierDocumentDate?: string;
        totalTaxAmount?: decimal | string;
        totalAmountExcludingTax?: decimal | string;
        totalAmountIncludingTax?: decimal | string;
        status?: ApAutomationUploadedDocumentStatus;
        isResultPopulated?: boolean | string;
        type?: ApAutomationDocumentType;
        origin?: ApAutomationDocumentOrigin;
        result?: string;
        postingDate?: string;
        reason?: integer | string;
        isTempUuid?: boolean | string;
        customerUniqueId?: string;
        recipientEmail?: string;
        senderEmail?: string;
    }
    export interface UploadedPurchasingDocumentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        uuid: string;
        supplierDocumentNumber: string;
        lines: ClientCollection<UploadedPurchasingDocumentLineBinding>;
        document: BinaryStream;
        uploadedFile: UploadedFile;
        documentMimeType: string;
        supplier: Supplier;
        site: Site;
        currency: Currency;
        dueDate: string;
        supplierDocumentDate: string;
        totalTaxAmount: string;
        totalAmountExcludingTax: string;
        totalAmountIncludingTax: string;
        status: ApAutomationUploadedDocumentStatus;
        isResultPopulated: boolean;
        type: ApAutomationDocumentType;
        origin: ApAutomationDocumentOrigin;
        result: any;
        purchaseInvoice: ClientCollection<PurchaseInvoice>;
        purchaseCreditMemo: ClientCollection<PurchaseCreditMemo>;
        postingDate: string;
        reason: ReasonCode;
        isReviewDone: boolean;
        isTempUuid: boolean;
        customerUniqueId: string;
        recipientEmail: string;
        senderEmail: string;
        apAutomationCompany: ApAutomationCompany;
        linkedSites: Site[];
        processingStatus: ApAutomationProcessingStatus;
        processingDetails: string;
    }
    export interface UploadedPurchasingDocument$Queries {
        isSupplierDocumentNumberUsed: Node$Operation<
            {
                supplier?: {
                    id?: integer | string;
                    supplierDocumentNumber?: string;
                    nodeToQuery?: string;
                };
            },
            boolean
        >;
    }
    export interface UploadedPurchasingDocument$Mutations {
        updateDocumentWithDetails: Node$Operation<
            {
                document: string;
            },
            boolean
        >;
        syncDocuments: Node$Operation<{}, boolean>;
        createPurchaseDocumentFromUploadedPurchasingDocument: Node$Operation<
            {
                uploadedPurchasingDocument: string;
            },
            {
                _id: integer;
                number: string;
            }
        >;
        resetUploadedDocument: Node$Operation<
            {
                uploadedPurchasingDocument: string;
            },
            boolean
        >;
    }
    export interface UploadedPurchasingDocument$AsyncOperations {
        syncDocumentsTask: AsyncOperation<{}, boolean>;
        uploadDocument: AsyncOperation<
            {
                uploadedPurchasingDocument: string;
            },
            boolean
        >;
        confirmBulk: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface UploadedPurchasingDocument$Lookups {
        uploadedFile: QueryOperation<UploadedFile>;
        supplier: QueryOperation<Supplier>;
        site: QueryOperation<Site>;
        currency: QueryOperation<Currency>;
        reason: QueryOperation<ReasonCode>;
        apAutomationCompany: QueryOperation<ApAutomationCompany>;
    }
    export interface UploadedPurchasingDocument$Operations {
        query: QueryOperation<UploadedPurchasingDocument>;
        read: ReadOperation<UploadedPurchasingDocument>;
        aggregate: {
            read: AggregateReadOperation<UploadedPurchasingDocument>;
            query: AggregateQueryOperation<UploadedPurchasingDocument>;
        };
        queries: UploadedPurchasingDocument$Queries;
        create: CreateOperation<UploadedPurchasingDocumentInput, UploadedPurchasingDocument>;
        getDuplicate: GetDuplicateOperation<UploadedPurchasingDocument>;
        update: UpdateOperation<UploadedPurchasingDocumentInput, UploadedPurchasingDocument>;
        updateById: UpdateByIdOperation<UploadedPurchasingDocumentInput, UploadedPurchasingDocument>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: UploadedPurchasingDocument$Mutations;
        asyncOperations: UploadedPurchasingDocument$AsyncOperations;
        lookups(dataOrId: string | { data: UploadedPurchasingDocumentInput }): UploadedPurchasingDocument$Lookups;
        getDefaults: GetDefaultsOperation<UploadedPurchasingDocument>;
    }
    export interface UploadedPurchasingDocumentLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        parent: UploadedPurchasingDocument;
        amountExcludingTax: string;
        amountIncludingTax: string;
        taxAmount: string;
        quantity: string;
        item: Item;
        itemDescription: string;
        purchaseUnit: UnitOfMeasure;
    }
    export interface UploadedPurchasingDocumentLineInput extends VitalClientNodeInput {
        amountExcludingTax?: decimal | string;
        amountIncludingTax?: decimal | string;
        taxAmount?: decimal | string;
        quantity?: decimal | string;
        item?: integer | string;
        itemDescription?: string;
        purchaseUnit?: integer | string;
    }
    export interface UploadedPurchasingDocumentLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        parent: UploadedPurchasingDocument;
        amountExcludingTax: string;
        amountIncludingTax: string;
        taxAmount: string;
        quantity: string;
        item: Item;
        itemDescription: string;
        purchaseUnit: UnitOfMeasure;
    }
    export interface UploadedPurchasingDocumentLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface UploadedPurchasingDocumentLine$Lookups {
        item: QueryOperation<Item>;
        purchaseUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface UploadedPurchasingDocumentLine$Operations {
        query: QueryOperation<UploadedPurchasingDocumentLine>;
        read: ReadOperation<UploadedPurchasingDocumentLine>;
        aggregate: {
            read: AggregateReadOperation<UploadedPurchasingDocumentLine>;
            query: AggregateQueryOperation<UploadedPurchasingDocumentLine>;
        };
        create: CreateOperation<UploadedPurchasingDocumentLineInput, UploadedPurchasingDocumentLine>;
        getDuplicate: GetDuplicateOperation<UploadedPurchasingDocumentLine>;
        update: UpdateOperation<UploadedPurchasingDocumentLineInput, UploadedPurchasingDocumentLine>;
        updateById: UpdateByIdOperation<UploadedPurchasingDocumentLineInput, UploadedPurchasingDocumentLine>;
        asyncOperations: UploadedPurchasingDocumentLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: UploadedPurchasingDocumentLineInput },
        ): UploadedPurchasingDocumentLine$Lookups;
        getDefaults: GetDefaultsOperation<UploadedPurchasingDocumentLine>;
    }
    export interface CompanyExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        name: string;
        description: string;
        legislation: Legislation;
        chartOfAccount: ChartOfAccount;
        hierarchyChartContent: TextStream;
        siren: string;
        naf: string;
        rcs: string;
        legalForm: LegalForm;
        country: Country;
        currency: Currency;
        priceScale: integer;
        addresses: ClientCollection<CompanyAddress>;
        primaryAddress: CompanyAddress;
        primaryContact: CompanyContact;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        customerOnHoldCheck: CustomerOnHoldType;
        contacts: ClientCollection<CompanyContact>;
        postingClass: PostingClass;
        taxEngine: TaxEngine;
        doStockPosting: boolean;
        doWipPosting: boolean;
        doArPosting: boolean;
        doApPosting: boolean;
        doNonAbsorbedPosting: boolean;
        attributeTypes: ClientCollection<CompanyAttributeType>;
        dimensionTypes: ClientCollection<CompanyDimensionType>;
        defaultAttributes: ClientCollection<CompanyDefaultAttribute>;
        defaultDimensions: ClientCollection<CompanyDefaultDimension>;
        datevConsultantNumber: integer;
        datevCustomerNumber: integer;
        bankAccount: BankAccount;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface CompanyInputExtension {
        id?: string;
        isActive?: boolean | string;
        name?: string;
        description?: string;
        legislation?: integer | string;
        chartOfAccount?: integer | string;
        siren?: string;
        naf?: string;
        rcs?: string;
        legalForm?: LegalForm;
        country?: integer | string;
        currency?: integer | string;
        addresses?: Partial<CompanyAddressInput>[];
        sequenceNumberId?: string;
        customerOnHoldCheck?: CustomerOnHoldType;
        contacts?: Partial<CompanyContactInput>[];
        postingClass?: integer | string;
        taxEngine?: TaxEngine;
        doStockPosting?: boolean | string;
        doWipPosting?: boolean | string;
        doNonAbsorbedPosting?: boolean | string;
        attributeTypes?: Partial<CompanyAttributeTypeInput>[];
        dimensionTypes?: Partial<CompanyDimensionTypeInput>[];
        defaultAttributes?: Partial<CompanyDefaultAttributeInput>[];
        defaultDimensions?: Partial<CompanyDefaultDimensionInput>[];
        datevConsultantNumber?: integer | string;
        datevCustomerNumber?: integer | string;
        bankAccount?: integer | string;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface CompanyBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        name: string;
        description: string;
        legislation: Legislation;
        chartOfAccount: ChartOfAccount;
        hierarchyChartContent: TextStream;
        siren: string;
        naf: string;
        rcs: string;
        legalForm: LegalForm;
        country: Country;
        currency: Currency;
        priceScale: integer;
        addresses: ClientCollection<CompanyAddressBinding>;
        primaryAddress: CompanyAddress;
        primaryContact: CompanyContact;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        customerOnHoldCheck: CustomerOnHoldType;
        contacts: ClientCollection<CompanyContactBinding>;
        postingClass: PostingClass;
        taxEngine: TaxEngine;
        doStockPosting: boolean;
        doWipPosting: boolean;
        doArPosting: boolean;
        doApPosting: boolean;
        doNonAbsorbedPosting: boolean;
        attributeTypes: ClientCollection<CompanyAttributeTypeBinding>;
        dimensionTypes: ClientCollection<CompanyDimensionTypeBinding>;
        defaultAttributes: ClientCollection<CompanyDefaultAttributeBinding>;
        defaultDimensions: ClientCollection<CompanyDefaultDimensionBinding>;
        datevConsultantNumber: integer;
        datevCustomerNumber: integer;
        bankAccount: BankAccount;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface PurchaseCreditMemoExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        netBalance: string;
        supplier: Supplier;
        billBySupplier: Supplier;
        creditMemoDate: string;
        billByLinkedAddress: BusinessEntityAddress;
        billByAddress: Address;
        billByContact: Contact;
        payToSupplier: Supplier;
        payToLinkedAddress: BusinessEntityAddress;
        payToAddress: Address;
        payToContact: Contact;
        supplierDocumentNumber: string;
        supplierDocumentDate: string;
        pdfSupplierCreditMemo: BinaryStream;
        matchingUser: User;
        reason: ReasonCode;
        apOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        isOpenItemPageOptionActive: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        uploadedDocument: UploadedPurchasingDocument;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        businessRelation: Supplier;
        fxRateDate: string;
        paymentTerm: PaymentTerm;
        paymentTracking: PaymentTracking;
        dueDate: string;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTax>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        lines: ClientCollection<PurchaseCreditMemoLine>;
        totalTaxAmountAdjusted: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        calculatedTotalAmountExcludingTax: string;
        calculatedTotalAmountExcludingTaxInCompanyCurrency: string;
        varianceTotalAmountExcludingTax: string;
        varianceTotalTaxAmount: string;
        calculatedTotalAmountIncludingTaxInCompanyCurrency: string;
        calculatedTotalTaxAmount: string;
        calculatedTotalTaxAmountAdjusted: string;
        calculatedTotalTaxableAmount: string;
        calculatedTotalExemptAmount: string;
        displayStatus: BaseDisplayStatus;
        totalAmountIncludingTax: string;
        calculatedTotalAmountIncludingTax: string;
        varianceTotalAmountIncludingTax: string;
    }
    export interface PurchaseCreditMemoInputExtension {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        invoiceStatus?: InvoiceStatus;
        taxCalculationStatus?: TaxCalculationStatus;
        billBySupplier?: integer | string;
        creditMemoDate?: string;
        billByLinkedAddress?: integer | string;
        billByAddress?: integer | string;
        billByContact?: integer | string;
        payToSupplier?: integer | string;
        payToLinkedAddress?: integer | string;
        payToAddress?: integer | string;
        payToContact?: integer | string;
        supplierDocumentNumber?: string;
        supplierDocumentDate?: string;
        pdfSupplierCreditMemo?: BinaryStream;
        matchingUser?: integer | string;
        reason?: integer | string;
        uploadedDocument?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        date?: string;
        businessRelation?: integer | string;
        fxRateDate?: string;
        paymentTerm?: integer | string;
        paymentTracking?: PaymentTrackingInput;
        dueDate?: string;
        currency?: integer | string;
        internalNote?: TextStream;
        companyFxRateDivisor?: decimal | string;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        taxes?: Partial<DocumentTaxInput>[];
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        lines?: Partial<PurchaseCreditMemoLineInput>[];
        totalTaxAmountAdjusted?: decimal | string;
        displayStatus?: BaseDisplayStatus;
    }
    export interface PurchaseCreditMemoBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        netBalance: string;
        supplier: Supplier;
        billBySupplier: Supplier;
        creditMemoDate: string;
        billByLinkedAddress: BusinessEntityAddress;
        billByAddress: Address;
        billByContact: Contact;
        payToSupplier: Supplier;
        payToLinkedAddress: BusinessEntityAddress;
        payToAddress: Address;
        payToContact: Contact;
        supplierDocumentNumber: string;
        supplierDocumentDate: string;
        pdfSupplierCreditMemo: BinaryStream;
        matchingUser: User;
        reason: ReasonCode;
        apOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        isOpenItemPageOptionActive: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        uploadedDocument: UploadedPurchasingDocument;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        businessRelation: Supplier;
        fxRateDate: string;
        paymentTerm: PaymentTerm;
        paymentTracking: PaymentTrackingBinding;
        dueDate: string;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTaxBinding>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        lines: ClientCollection<PurchaseCreditMemoLineBinding>;
        totalTaxAmountAdjusted: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        calculatedTotalAmountExcludingTax: string;
        calculatedTotalAmountExcludingTaxInCompanyCurrency: string;
        varianceTotalAmountExcludingTax: string;
        varianceTotalTaxAmount: string;
        calculatedTotalAmountIncludingTaxInCompanyCurrency: string;
        calculatedTotalTaxAmount: string;
        calculatedTotalTaxAmountAdjusted: string;
        calculatedTotalTaxableAmount: string;
        calculatedTotalExemptAmount: string;
        displayStatus: BaseDisplayStatus;
        totalAmountIncludingTax: string;
        calculatedTotalAmountIncludingTax: string;
        varianceTotalAmountIncludingTax: string;
    }
    export interface PurchaseCreditMemoExtension$Lookups {
        uploadedDocument: QueryOperation<UploadedPurchasingDocument>;
    }
    export interface PurchaseCreditMemoExtension$Operations {
        lookups(dataOrId: string | { data: PurchaseCreditMemoInput }): PurchaseCreditMemoExtension$Lookups;
    }
    export interface PurchaseInvoiceExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        netBalance: string;
        supplier: Supplier;
        billBySupplier: Supplier;
        invoiceDate: string;
        billByLinkedAddress: BusinessEntityAddress;
        billByAddress: Address;
        billByContact: Contact;
        payToSupplier: Supplier;
        payToLinkedAddress: BusinessEntityAddress;
        payToAddress: Address;
        payToContact: Contact;
        supplierDocumentNumber: string;
        supplierDocumentDate: string;
        pdfSupplierInvoice: BinaryStream;
        returnLinkedAddress: BusinessEntityAddress;
        matchingUser: User;
        financeStatus: PurchaseInvoiceStatus;
        apOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        isOpenItemPageOptionActive: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        uploadedDocument: UploadedPurchasingDocument;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        businessRelation: Supplier;
        fxRateDate: string;
        paymentTerm: PaymentTerm;
        paymentTracking: PaymentTracking;
        dueDate: string;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTax>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        lines: ClientCollection<PurchaseInvoiceLine>;
        totalTaxAmountAdjusted: string;
        matchingStatus: PurchaseInvoiceMatchingStatus;
        calculatedTotalAmountExcludingTax: string;
        calculatedTotalAmountExcludingTaxInCompanyCurrency: string;
        varianceTotalAmountExcludingTax: string;
        varianceTotalTaxAmount: string;
        calculatedTotalAmountIncludingTaxInCompanyCurrency: string;
        calculatedTotalTaxAmount: string;
        calculatedTotalTaxAmountAdjusted: string;
        calculatedTotalTaxableAmount: string;
        calculatedTotalExemptAmount: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        calculatedTotalRemainingQuantityToCredit: string;
        displayStatus: BaseDisplayStatus;
        totalAmountIncludingTax: string;
        calculatedTotalAmountIncludingTax: string;
        varianceTotalAmountIncludingTax: string;
    }
    export interface PurchaseInvoiceInputExtension {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        invoiceStatus?: InvoiceStatus;
        taxCalculationStatus?: TaxCalculationStatus;
        billBySupplier?: integer | string;
        invoiceDate?: string;
        billByLinkedAddress?: integer | string;
        billByAddress?: integer | string;
        billByContact?: integer | string;
        payToSupplier?: integer | string;
        payToLinkedAddress?: integer | string;
        payToAddress?: integer | string;
        payToContact?: integer | string;
        supplierDocumentNumber?: string;
        supplierDocumentDate?: string;
        pdfSupplierInvoice?: BinaryStream;
        returnLinkedAddress?: integer | string;
        matchingUser?: integer | string;
        financeStatus?: PurchaseInvoiceStatus;
        uploadedDocument?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        date?: string;
        businessRelation?: integer | string;
        fxRateDate?: string;
        paymentTerm?: integer | string;
        paymentTracking?: PaymentTrackingInput;
        dueDate?: string;
        currency?: integer | string;
        internalNote?: TextStream;
        companyFxRateDivisor?: decimal | string;
        totalAmountExcludingTax?: decimal | string;
        totalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        taxes?: Partial<DocumentTaxInput>[];
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        lines?: Partial<PurchaseInvoiceLineInput>[];
        totalTaxAmountAdjusted?: decimal | string;
        matchingStatus?: PurchaseInvoiceMatchingStatus;
        displayStatus?: BaseDisplayStatus;
    }
    export interface PurchaseInvoiceBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        invoiceStatus: InvoiceStatus;
        taxCalculationStatus: TaxCalculationStatus;
        taxEngine: TaxEngine;
        netBalance: string;
        supplier: Supplier;
        billBySupplier: Supplier;
        invoiceDate: string;
        billByLinkedAddress: BusinessEntityAddress;
        billByAddress: Address;
        billByContact: Contact;
        payToSupplier: Supplier;
        payToLinkedAddress: BusinessEntityAddress;
        payToAddress: Address;
        payToContact: Contact;
        supplierDocumentNumber: string;
        supplierDocumentDate: string;
        pdfSupplierInvoice: BinaryStream;
        returnLinkedAddress: BusinessEntityAddress;
        matchingUser: User;
        financeStatus: PurchaseInvoiceStatus;
        apOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        isOpenItemPageOptionActive: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        uploadedDocument: UploadedPurchasingDocument;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        businessRelation: Supplier;
        fxRateDate: string;
        paymentTerm: PaymentTerm;
        paymentTracking: PaymentTrackingBinding;
        dueDate: string;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        rateDescription: string;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        taxes: ClientCollection<DocumentTaxBinding>;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        lines: ClientCollection<PurchaseInvoiceLineBinding>;
        totalTaxAmountAdjusted: string;
        matchingStatus: PurchaseInvoiceMatchingStatus;
        calculatedTotalAmountExcludingTax: string;
        calculatedTotalAmountExcludingTaxInCompanyCurrency: string;
        varianceTotalAmountExcludingTax: string;
        varianceTotalTaxAmount: string;
        calculatedTotalAmountIncludingTaxInCompanyCurrency: string;
        calculatedTotalTaxAmount: string;
        calculatedTotalTaxAmountAdjusted: string;
        calculatedTotalTaxableAmount: string;
        calculatedTotalExemptAmount: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        calculatedTotalRemainingQuantityToCredit: string;
        displayStatus: BaseDisplayStatus;
        totalAmountIncludingTax: string;
        calculatedTotalAmountIncludingTax: string;
        varianceTotalAmountIncludingTax: string;
    }
    export interface PurchaseInvoiceExtension$Lookups {
        uploadedDocument: QueryOperation<UploadedPurchasingDocument>;
    }
    export interface PurchaseInvoiceExtension$Operations {
        lookups(dataOrId: string | { data: PurchaseInvoiceInput }): PurchaseInvoiceExtension$Lookups;
    }
    export interface Package {
        '@sage/xtrem-ap-automation/ApAutomationCompany': ApAutomationCompany$Operations;
        '@sage/xtrem-ap-automation/ApAutomationConfiguration': ApAutomationConfiguration$Operations;
        '@sage/xtrem-ap-automation/UploadedPurchasingDocument': UploadedPurchasingDocument$Operations;
        '@sage/xtrem-ap-automation/UploadedPurchasingDocumentLine': UploadedPurchasingDocumentLine$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremDistribution$Package,
            SageXtremFinance$Package,
            SageXtremFinanceData$Package,
            SageXtremImportExport$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremPurchasing$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremSales$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-ap-automation-api' {
    export type * from '@sage/xtrem-ap-automation-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-distribution-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-purchasing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-sales-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-ap-automation-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type {
        CompanyBindingExtension,
        CompanyExtension,
        CompanyInputExtension,
    } from '@sage/xtrem-ap-automation-api';
    export interface Company extends CompanyExtension {}
    export interface CompanyBinding extends CompanyBindingExtension {}
    export interface CompanyInput extends CompanyInputExtension {}
}
declare module '@sage/xtrem-purchasing-api-partial' {
    import type {
        PurchaseCreditMemoBindingExtension,
        PurchaseCreditMemoExtension,
        PurchaseCreditMemoExtension$Lookups,
        PurchaseCreditMemoExtension$Operations,
        PurchaseCreditMemoInputExtension,
        PurchaseInvoiceBindingExtension,
        PurchaseInvoiceExtension,
        PurchaseInvoiceExtension$Lookups,
        PurchaseInvoiceExtension$Operations,
        PurchaseInvoiceInputExtension,
    } from '@sage/xtrem-ap-automation-api';
    export interface PurchaseCreditMemo extends PurchaseCreditMemoExtension {}
    export interface PurchaseCreditMemoBinding extends PurchaseCreditMemoBindingExtension {}
    export interface PurchaseCreditMemoInput extends PurchaseCreditMemoInputExtension {}
    export interface PurchaseCreditMemo$Lookups extends PurchaseCreditMemoExtension$Lookups {}
    export interface PurchaseCreditMemo$Operations extends PurchaseCreditMemoExtension$Operations {}
    export interface PurchaseInvoice extends PurchaseInvoiceExtension {}
    export interface PurchaseInvoiceBinding extends PurchaseInvoiceBindingExtension {}
    export interface PurchaseInvoiceInput extends PurchaseInvoiceInputExtension {}
    export interface PurchaseInvoice$Lookups extends PurchaseInvoiceExtension$Lookups {}
    export interface PurchaseInvoice$Operations extends PurchaseInvoiceExtension$Operations {}
}
