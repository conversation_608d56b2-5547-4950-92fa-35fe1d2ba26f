import { BinaryStream } from '@sage/xtrem-core';
import { readFile } from 'fs/promises';
import * as path from 'path';

const dirname = __dirname.replace('/build/', '/');

export function salesInvoicePdfPath(): string {
    return path.resolve(dirname, 'sales_invoice.pdf');
}

export async function salesInvoiceToByteStream(): Promise<BinaryStream> {
    return BinaryStream.fromBuffer(await readFile(salesInvoicePdfPath()));
}
