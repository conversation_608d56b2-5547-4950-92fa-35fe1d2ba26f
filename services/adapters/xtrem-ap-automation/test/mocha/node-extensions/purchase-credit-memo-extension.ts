import type { Context } from '@sage/xtrem-core';
import { Test, date } from '@sage/xtrem-core';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremApAutomation from '../../../index';
import type { ApAutomationConfig } from '../../../lib/interfaces';
import { salesInvoiceToByteStream } from '../../fixtures/sales-invoice-to-binary-stream';
import fixtureGenerator from '../functions/process-result-fixture';
import { createUploadedFile } from '../functions/uploaded-purchasing-document';

let fixtureDocument: xtremApAutomation.interfaces.ApAutomationResult;
const sandbox = sinon.createSandbox();

async function createTenantApAutomationConfiguration(context: Context) {
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
        .returns(Promise.resolve('customer_id_test'));
    sandbox.stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses').returns(
        Promise.resolve({
            accounts_payable: '<EMAIL>',
            employee_expense: '<EMAIL>',
        }),
    );
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'changeEmailSettings')
        .returns(Promise.resolve(true));

    const apAutomationConfigurationDefaultId =
        await xtremApAutomation.nodes.ApAutomationConfiguration.createOrGetDefaultRecord(context);
    const apAutomationConfiguration = await context.read(
        xtremApAutomation.nodes.ApAutomationConfiguration,
        { _id: apAutomationConfigurationDefaultId },
        { forUpdate: true },
    );
    await apAutomationConfiguration.$.set({ configurationLevel: 'tenant', country: 'DE' });
    await apAutomationConfiguration.$.save();
    assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);
}

describe('Purchase invoice extension', () => {
    beforeEach(() => {
        fixtureDocument = fixtureGenerator();
    });
    afterEach(() => {
        sandbox.restore();
    });
    it('Get package info  ', () =>
        Test.withContext(context => {
            Test.patchConfig({
                packages: {
                    '@sage/xtrem-ap-automation': {
                        clientId: 'sageIdClientId',
                        clientSecret: 'pass1234',
                        oauthEndpointUrl: 'https://sageid.test.com',
                        serviceUrl: 'https://unittests.for.sageAI.com/external',
                    },
                },
            });
            const packageConfig =
                context.configuration.getPackageConfig<ApAutomationConfig>('@sage/xtrem-ap-automation');
            assert.isNotNull(packageConfig);
        }));
    /**
     *  the patchConfig will be effective after the iteration !
     */
    it('Test frozen properties', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                let document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    supplier: '#US017',
                    site: '#ETS1-S01',
                    currency: '#EUR',
                    supplierDocumentDate: date.today().addDays(-1),
                    uploadedFile: await createUploadedFile(context),
                    document: await salesInvoiceToByteStream(),
                    documentMimeType: 'application/pdf',
                    isTempUuid: false,
                    type: 'creditMemo',
                    reason: 'R3',
                    customerUniqueId: 'customer_id_test',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
                const newPurchaseDocument = await manager.confirmDocument(document);
                await context.flushDeferredActions();
                const purchaseDocumentNumber = await newPurchaseDocument?.number;
                assert.equal(purchaseDocumentNumber, 'PC240003');

                document = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: fixtureDocument.metadata.identifier },
                    { forUpdate: true },
                );
                assert.equal(await document.status, 'reviewDone');

                // Test frozen properties
                const purchaseCreditMemo = await context.read(
                    xtremPurchasing.nodes.PurchaseCreditMemo,
                    { number: purchaseDocumentNumber },
                    { forUpdate: true },
                );

                await assert.isRejected(
                    purchaseCreditMemo.$.set({ site: 'DEP1-S01' }),
                    'PurchaseCreditMemo.site: cannot set value on frozen property',
                );

                await assert.isRejected(
                    purchaseCreditMemo.$.set({ billBySupplier: 'DESUP01' }),
                    'PurchaseCreditMemo.billBySupplier: cannot set value on frozen property',
                );

                await assert.isRejected(
                    purchaseCreditMemo.$.set({ supplierDocumentNumber: 'test frozen property' }),
                    'PurchaseCreditMemo.supplierDocumentNumber: cannot set value on frozen property',
                );

                await assert.isRejected(
                    purchaseCreditMemo.$.set({ supplierDocumentDate: date.today().addDays(-10) }),
                    'PurchaseCreditMemo.supplierDocumentDate: cannot set value on frozen property',
                );

                await assert.isRejected(
                    purchaseCreditMemo.$.set({ totalTaxAmount: 1234 }),
                    'PurchaseCreditMemo.totalTaxAmount: cannot set value on frozen property',
                );

                await assert.isRejected(
                    purchaseCreditMemo.$.set({ totalAmountExcludingTax: 1234 }),
                    'PurchaseCreditMemo.totalAmountExcludingTax: cannot set value on frozen property',
                );

                await assert.isRejected(
                    purchaseCreditMemo.$.set({ reason: 'R1' }),
                    'PurchaseCreditMemo.reason: cannot set value on frozen property',
                );

                await assert.isRejected(
                    purchaseCreditMemo.$.set({ pdfSupplierCreditMemo: null }),
                    'PurchaseCreditMemo.pdfSupplierCreditMemo: cannot set value on frozen property',
                );

                await assert.isRejected(
                    purchaseCreditMemo.$.set({ currency: 'USD' }),
                    'PurchaseCreditMemo.currency: cannot set value on frozen property',
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));
});
