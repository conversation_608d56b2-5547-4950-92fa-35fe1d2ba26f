import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremApAutomation from '../../../index';
import type { ApAutomationConfig } from '../../../lib/interfaces';

const sandbox = sinon.createSandbox();

function mockCreateCompany(parameters: {
    customerUniqueId: string;
    accountsPayableEmail: string;
    employeeExpenseEmail: string;
}) {
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
        .returns(Promise.resolve(parameters.customerUniqueId));
    sandbox.stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses').returns(
        Promise.resolve({
            accounts_payable: parameters.accountsPayableEmail,
            employee_expense: parameters.employeeExpenseEmail,
        }),
    );
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'changeEmailSettings')
        .returns(Promise.resolve(true));
}

describe('Delete company', () => {
    afterEach(() => {
        sandbox.restore();
    });

    it('Get package info  ', () =>
        Test.withContext(context => {
            Test.patchConfig({
                packages: {
                    '@sage/xtrem-ap-automation': {
                        clientId: 'sageIdClientId',
                        clientSecret: 'pass1234',
                        oauthEndpointUrl: 'https://sageid.test.com',
                        serviceUrl: 'https://unittests.for.sageAI.com/external',
                    },
                },
            });

            const packageConfig =
                context.configuration.getPackageConfig<ApAutomationConfig>('@sage/xtrem-ap-automation');
            assert.isNotNull(packageConfig);
        }));

    /**
     *  the patchConfig will be effective after the iteration !
     */
    it('Register Company on Sage AI and Delete company', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                mockCreateCompany({
                    customerUniqueId: 'test_company_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });
                const newCompany = await context.create(xtremSystem.nodes.Company, {
                    id: 'NewCompany',
                    name: 'New Company',
                    legislation: '#US',
                    country: '#US',
                    currency: '#USD',
                    addresses: [
                        { name: 'add1', addressLine1: '1', addressLine2: '2', city: 'New York', isActive: true },
                    ],
                });
                await newCompany.$.save();
                assert.deepEqual(newCompany.$.context.diagnoses, []);
                await xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, newCompany);

                const companyConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationCompany, {
                    company: newCompany,
                });
                assert.equal(await companyConfiguration?.customerUniqueId, 'test_company_customer_id');
                assert.equal(await companyConfiguration?.accountsPayableEmail, '<EMAIL>');
                assert.equal(await companyConfiguration?.employeeExpenseEmail, '<EMAIL>');
                assert.isTrue(await companyConfiguration?.isInboxActive);

                const companyId = await newCompany.id;
                await newCompany.$.delete();
                assert.deepEqual(newCompany.$.context.diagnoses, []);

                const queryCountApAutomationCompany = await context.queryCount(
                    xtremApAutomation.nodes.ApAutomationCompany,
                    {
                        filter: { company: { id: companyId } },
                    },
                );
                assert.equal(queryCountApAutomationCompany, 0);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));
});
