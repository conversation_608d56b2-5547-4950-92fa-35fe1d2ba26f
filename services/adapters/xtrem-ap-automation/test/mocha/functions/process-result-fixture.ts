import type * as xtremApAutomation from '../../../index';

export default (): xtremApAutomation.interfaces.ApAutomationResult => ({
    metadata: {
        identifier: 'c065a876-a2a2-439a-a30e-ce34a64738e5',
        request_origin: 'API',
        file_upload_time: '2024-10-16T13:14:33.764003+00:00',
        email: {
            sender: '<EMAIL>',
            recipients: '<EMAIL>',
        },
    },
    extraction: {
        header: {
            billing_address: {
                value: 'LONDON\nUnited Kingdom',
                confidence: 0.938,
                tagged: false,
            },
            currency: {
                value: 'GBP',
                confidence: 0.9999999996103914,
                tagged: false,
            },
            document_id: {
                value: 'SISW240005',
                confidence: 0.966,
                tagged: false,
            },
            document_language: {
                value: 'eng',
                confidence: 1,
                tagged: false,
            },
            document_type: {
                value: 'invoice',
                tagged: false,
            },
            due_date: {
                value: '2024-10-31',
                confidence: 0.966,
                tagged: false,
            },
            invoice_type: {
                value: 'debit',
                confidence: 0.97,
                tagged: true,
            },
            issue_date: {
                value: '2024-10-16',
                confidence: 0.966,
                tagged: false,
            },
            po_number: {
                value: 'PUR-\nSERVICE',
                confidence: 0.52,
                tagged: false,
            },
            tax_amount: {
                value: '195.10',
                confidence: 0.95,
                tagged: false,
            },
            total_amount: {
                value: '1170.60',
                confidence: 0.95,
                tagged: false,
            },
            total_without_tax: {
                value: '975.50',
                confidence: 0.95,
                tagged: false,
            },
            bill_of_lading_number: {
                value: 'SISW240005',
                confidence: 0.966,
                tagged: false,
            },
            rate_confirmation_number: {
                value: 'SISW240005',
                confidence: 0.966,
                tagged: false,
            },
        },
        recipient: {
            address: {
                value: 'LONDON United Kingdom',
                confidence: 0.938,
                tagged: false,
            },
            name: {
                value: 'UK Limited',
                confidence: 0.657,
                tagged: false,
            },
        },
        vendor: {
            address: {
                value: 'Deer Park Court Donnigton Wood TF2 7NB Telford United Kingdom',
                confidence: 0.872,
                tagged: false,
            },
            company_id: {
                value: '*********',
                tagged: false,
            },
            country: {
                value: 'GB',
                tagged: false,
            },
            name: {
                value: 'Lyreco',
                confidence: 0.958,
                tagged: false,
            },
            region: {
                value: 'london',
                tagged: false,
            },
        },
        tax_table: [
            {
                tax: {
                    value: '195.10',
                    confidence: 0.95,
                    tagged: false,
                },
                tax_percentage: {
                    value: '20.000',
                    confidence: 0.95,
                    tagged: false,
                },
                tax_rate_name: {
                    value: 'VAT',
                    confidence: 0.95,
                    tagged: false,
                },
                taxable_amount: {
                    value: '975.50',
                    confidence: 0.95,
                    tagged: false,
                },
                total_amount: {
                    value: '1170.60',
                    confidence: 0.95,
                    tagged: false,
                },
            },
        ],
        line_items: [
            {
                description: {
                    value: 'Service',
                    confidence: 0.955,
                    tagged: false,
                },
                quantity: {
                    value: '1.0',
                    confidence: 0.956,
                    tagged: false,
                },
                tax_amount: {
                    value: '195.10',
                    confidence: 0.956,
                    tagged: true,
                },
                tax_percentage: {
                    value: '20.000',
                    confidence: 0.956,
                    tagged: true,
                },
                total_amount: {
                    value: '1170.6',
                    confidence: 0.958,
                    tagged: false,
                },
                total_without_tax: {
                    value: '975.50',
                    confidence: 0.958,
                    tagged: true,
                },
                unit_price: {
                    value: '975.5',
                    confidence: 0.958,
                    tagged: false,
                },
            },
        ],
    },
});
