import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { Test, date } from '@sage/xtrem-core';
import * as xtremUpload from '@sage/xtrem-upload';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremApAutomation from '../../../index';
import type { ApAutomationConfig } from '../../../lib/interfaces';
import fixtureGenerator from './process-result-fixture';

export async function createUploadedFile(context: Context): Promise<xtremUpload.nodes.UploadedFile> {
    let uploadedFile = await context.create(xtremUpload.nodes.UploadedFile, {
        key: '5DZMKDHTiKBt_qCpyfElB',
        filename: 'Facture_F20220023-LE_FOURNISSEUR-POUR-LE_CLIENT_EN_16931 2.pdf',
        mimeType: 'application/pdf',
        lastModified: null,
        contentLength: 486044.0,
        kind: 'attachment',
        canSkipAntivirusScan: true,
    });
    await uploadedFile.$.save();
    assert.deepEqual(uploadedFile.$.context.diagnoses, []);

    uploadedFile = await context.read(
        xtremUpload.nodes.UploadedFile,
        { key: '5DZMKDHTiKBt_qCpyfElB' },
        { forUpdate: true },
    );
    await uploadedFile.$.set({ status: 'verified' });
    await uploadedFile.$.save();
    assert.deepEqual(uploadedFile.$.context.diagnoses, []);

    return uploadedFile;
}

export async function createUploadedFileMimeType(context: Context): Promise<xtremUpload.nodes.UploadedFile> {
    let uploadedFile = await context.create(xtremUpload.nodes.UploadedFile, {
        key: '5DZMKDHTiKBt_qCpyfElB',
        filename: 'Wrong_file_type.zip',
        mimeType: ' application/zip',
        lastModified: null,
        contentLength: 486044.0,
        kind: 'attachment',
        canSkipAntivirusScan: true,
    });
    await uploadedFile.$.save();
    assert.deepEqual(uploadedFile.$.context.diagnoses, []);

    uploadedFile = await context.read(
        xtremUpload.nodes.UploadedFile,
        { key: '5DZMKDHTiKBt_qCpyfElB' },
        { forUpdate: true },
    );
    await uploadedFile.$.set({ status: 'verified' });
    await uploadedFile.$.save();
    assert.deepEqual(uploadedFile.$.context.diagnoses, []);

    return uploadedFile;
}

describe('validateCreatePurchaseDocumentFromUploadedPurchasingDocument function', () => {
    let fixtureDocument: xtremApAutomation.interfaces.ApAutomationResult;
    const sandbox = sinon.createSandbox();

    const UploadedPurchasingDocumentCustomizedData: NodeCreateData<xtremApAutomation.nodes.UploadedPurchasingDocument> =
        {
            uuid: 'unit_test_document',
            supplier: '#US017',
            site: '#ETS1-S01',
            currency: '#EUR',
            supplierDocumentDate: date.today().addDays(-1),
            postingDate: date.today(),
            dueDate: date.today().addDays(30),
            type: 'creditMemo',
            reason: 'R3',
            customerUniqueId: 'customer_id_test',
        };

    async function createUploadedPurchasingDocument(
        context: Context,
        dataToBeCreated: NodeCreateData<xtremApAutomation.nodes.UploadedPurchasingDocument>,
    ): Promise<xtremApAutomation.nodes.UploadedPurchasingDocument> {
        const newUploadedPurchasingDocument = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
            ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
            ...dataToBeCreated,
            uploadedFile: await createUploadedFile(context),
        });
        await newUploadedPurchasingDocument.$.save();
        return newUploadedPurchasingDocument;
    }

    async function createTenantApAutomationConfiguration(context: Context) {
        sandbox
            .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
            .returns(Promise.resolve('customer_id_test'));
        sandbox.stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses').returns(
            Promise.resolve({
                accounts_payable: '<EMAIL>',
                employee_expense: '<EMAIL>',
            }),
        );
        sandbox
            .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'changeEmailSettings')
            .returns(Promise.resolve(true));

        const apAutomationConfigurationDefaultId =
            await xtremApAutomation.nodes.ApAutomationConfiguration.createOrGetDefaultRecord(context);
        const apAutomationConfiguration = await context.read(
            xtremApAutomation.nodes.ApAutomationConfiguration,
            { _id: apAutomationConfigurationDefaultId },
            { forUpdate: true },
        );
        await apAutomationConfiguration.$.set({ configurationLevel: 'tenant', country: 'DE' });
        await apAutomationConfiguration.$.save();
        assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);
    }

    beforeEach(() => {
        fixtureDocument = fixtureGenerator();
    });
    afterEach(() => {
        sandbox.restore();
    });
    describe('validateSupplierDocumentNumber', () => {
        it('Get package info  ', () =>
            Test.withContext(context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                const packageConfig =
                    context.configuration.getPackageConfig<ApAutomationConfig>('@sage/xtrem-ap-automation');
                assert.isNotNull(packageConfig);
            }));
        it('Supplier cannot be empty.', () =>
            Test.withContext(
                async context => {
                    Test.patchConfig({
                        packages: {
                            '@sage/xtrem-ap-automation': {
                                clientId: 'sageIdClientId',
                                clientSecret: 'pass1234',
                                oauthEndpointUrl: 'https://sageid.test.com',
                                serviceUrl: 'https://unittests.for.sageAI.com/external',
                            },
                        },
                    });
                    await createTenantApAutomationConfiguration(context);

                    const document = await createUploadedPurchasingDocument(context, {
                        ...UploadedPurchasingDocumentCustomizedData,
                        supplier: null,
                    });

                    await assert.isRejected(
                        xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
                            context,
                            document,
                        ),
                        'Supplier cannot be empty.',
                    );
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));
        it('Supplier document number cannot be empty.', () =>
            Test.withContext(
                async context => {
                    Test.patchConfig({
                        packages: {
                            '@sage/xtrem-ap-automation': {
                                clientId: 'sageIdClientId',
                                clientSecret: 'pass1234',
                                oauthEndpointUrl: 'https://sageid.test.com',
                                serviceUrl: 'https://unittests.for.sageAI.com/external',
                            },
                        },
                    });
                    await createTenantApAutomationConfiguration(context);

                    const document = await createUploadedPurchasingDocument(context, {
                        ...UploadedPurchasingDocumentCustomizedData,
                        supplierDocumentNumber: '',
                    });

                    await assert.isRejected(
                        xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
                            context,
                            document,
                        ),
                        'Supplier document number cannot be empty.',
                    );
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));

        it('Supplier document number already exists.', () =>
            Test.withContext(
                async context => {
                    Test.patchConfig({
                        packages: {
                            '@sage/xtrem-ap-automation': {
                                clientId: 'sageIdClientId',
                                clientSecret: 'pass1234',
                                oauthEndpointUrl: 'https://sageid.test.com',
                                serviceUrl: 'https://unittests.for.sageAI.com/external',
                            },
                        },
                    });
                    await createTenantApAutomationConfiguration(context);

                    const document = await createUploadedPurchasingDocument(context, {
                        ...UploadedPurchasingDocumentCustomizedData,
                        supplierDocumentNumber: 'Sage-CM658',
                    });

                    await assert.isRejected(
                        xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
                            context,
                            document,
                        ),
                        'Supplier document number already exists.',
                    );
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));
    });
    describe('validateSite', () => {
        it('Financial site cannot be empty.', () =>
            Test.withContext(
                async context => {
                    Test.patchConfig({
                        packages: {
                            '@sage/xtrem-ap-automation': {
                                clientId: 'sageIdClientId',
                                clientSecret: 'pass1234',
                                oauthEndpointUrl: 'https://sageid.test.com',
                                serviceUrl: 'https://unittests.for.sageAI.com/external',
                            },
                        },
                    });
                    await createTenantApAutomationConfiguration(context);

                    const document = await createUploadedPurchasingDocument(context, {
                        ...UploadedPurchasingDocumentCustomizedData,
                        site: null,
                    });

                    await assert.isRejected(
                        xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
                            context,
                            document,
                        ),
                        'Financial site cannot be empty.',
                    );
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));
    });
    describe('validateInvoiceType', () => {
        it('Invoice type cannot be empty.', () =>
            Test.withContext(
                async context => {
                    Test.patchConfig({
                        packages: {
                            '@sage/xtrem-ap-automation': {
                                clientId: 'sageIdClientId',
                                clientSecret: 'pass1234',
                                oauthEndpointUrl: 'https://sageid.test.com',
                                serviceUrl: 'https://unittests.for.sageAI.com/external',
                            },
                        },
                    });
                    await createTenantApAutomationConfiguration(context);

                    const document = await createUploadedPurchasingDocument(context, {
                        ...UploadedPurchasingDocumentCustomizedData,
                        type: null,
                    });

                    await assert.isRejected(
                        xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
                            context,
                            document,
                        ),
                        'Invoice type cannot be empty.',
                    );
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));

        it('Reason code cannot be empty.', () =>
            Test.withContext(
                async context => {
                    Test.patchConfig({
                        packages: {
                            '@sage/xtrem-ap-automation': {
                                clientId: 'sageIdClientId',
                                clientSecret: 'pass1234',
                                oauthEndpointUrl: 'https://sageid.test.com',
                                serviceUrl: 'https://unittests.for.sageAI.com/external',
                            },
                        },
                    });
                    await createTenantApAutomationConfiguration(context);

                    const document = await createUploadedPurchasingDocument(context, {
                        ...UploadedPurchasingDocumentCustomizedData,
                        type: 'creditMemo',
                        reason: null,
                    });

                    await assert.isRejected(
                        xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
                            context,
                            document,
                        ),
                        'Reason code cannot be empty.',
                    );
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));
    });
    describe('validateCurrency', () => {
        it('Currency cannot be empty.', () =>
            Test.withContext(
                async context => {
                    Test.patchConfig({
                        packages: {
                            '@sage/xtrem-ap-automation': {
                                clientId: 'sageIdClientId',
                                clientSecret: 'pass1234',
                                oauthEndpointUrl: 'https://sageid.test.com',
                                serviceUrl: 'https://unittests.for.sageAI.com/external',
                            },
                        },
                    });
                    await createTenantApAutomationConfiguration(context);

                    const document = await createUploadedPurchasingDocument(context, {
                        ...UploadedPurchasingDocumentCustomizedData,
                        currency: null,
                    });

                    await assert.isRejected(
                        xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
                            context,
                            document,
                        ),
                        'Currency cannot be empty.',
                    );
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));
    });
    describe('validateUploadedPurchasingDocumentDates', () => {
        it('Supplier document number cannot be empty.', () =>
            Test.withContext(
                async context => {
                    Test.patchConfig({
                        packages: {
                            '@sage/xtrem-ap-automation': {
                                clientId: 'sageIdClientId',
                                clientSecret: 'pass1234',
                                oauthEndpointUrl: 'https://sageid.test.com',
                                serviceUrl: 'https://unittests.for.sageAI.com/external',
                            },
                        },
                    });
                    await createTenantApAutomationConfiguration(context);

                    const document = await createUploadedPurchasingDocument(context, {
                        ...UploadedPurchasingDocumentCustomizedData,
                        supplierDocumentDate: null,
                    });

                    await assert.isRejected(
                        xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
                            context,
                            document,
                        ),
                        'Supplier document number cannot be empty.',
                    );
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));
        it('Posting date cannot be empty.', () =>
            Test.withContext(
                async context => {
                    Test.patchConfig({
                        packages: {
                            '@sage/xtrem-ap-automation': {
                                clientId: 'sageIdClientId',
                                clientSecret: 'pass1234',
                                oauthEndpointUrl: 'https://sageid.test.com',
                                serviceUrl: 'https://unittests.for.sageAI.com/external',
                            },
                        },
                    });
                    await createTenantApAutomationConfiguration(context);

                    const document = await createUploadedPurchasingDocument(context, {
                        ...UploadedPurchasingDocumentCustomizedData,
                        postingDate: null,
                    });

                    await assert.isRejected(
                        xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
                            context,
                            document,
                        ),
                        'Posting date cannot be empty.',
                    );
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));
        it('Due date cannot be before supplier document date.', () =>
            Test.withContext(
                async context => {
                    Test.patchConfig({
                        packages: {
                            '@sage/xtrem-ap-automation': {
                                clientId: 'sageIdClientId',
                                clientSecret: 'pass1234',
                                oauthEndpointUrl: 'https://sageid.test.com',
                                serviceUrl: 'https://unittests.for.sageAI.com/external',
                            },
                        },
                    });
                    await createTenantApAutomationConfiguration(context);

                    const document = await createUploadedPurchasingDocument(context, {
                        ...UploadedPurchasingDocumentCustomizedData,
                        dueDate: date.today().addDays(-2),
                        supplierDocumentDate: date.today().addDays(-1),
                    });

                    await assert.isRejected(
                        xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
                            context,
                            document,
                        ),
                        'Due date cannot be before supplier document date.',
                    );
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));
        it('Posting date cannot be before supplier document date.', () =>
            Test.withContext(
                async context => {
                    Test.patchConfig({
                        packages: {
                            '@sage/xtrem-ap-automation': {
                                clientId: 'sageIdClientId',
                                clientSecret: 'pass1234',
                                oauthEndpointUrl: 'https://sageid.test.com',
                                serviceUrl: 'https://unittests.for.sageAI.com/external',
                            },
                        },
                    });
                    await createTenantApAutomationConfiguration(context);

                    const document = await createUploadedPurchasingDocument(context, {
                        ...UploadedPurchasingDocumentCustomizedData,
                        postingDate: date.today().addDays(-2),
                        supplierDocumentDate: date.today().addDays(-1),
                    });

                    await assert.isRejected(
                        xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
                            context,
                            document,
                        ),
                        'Posting date cannot be before supplier document date.',
                    );
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));
        describe('validateAmounts', () => {
            it('Total amount including tax needs to be equal to the total amount excluding tax plus the total tax amount.', () =>
                Test.withContext(
                    async context => {
                        Test.patchConfig({
                            packages: {
                                '@sage/xtrem-ap-automation': {
                                    clientId: 'sageIdClientId',
                                    clientSecret: 'pass1234',
                                    oauthEndpointUrl: 'https://sageid.test.com',
                                    serviceUrl: 'https://unittests.for.sageAI.com/external',
                                },
                            },
                        });
                        await createTenantApAutomationConfiguration(context);

                        const document = await createUploadedPurchasingDocument(context, {
                            ...UploadedPurchasingDocumentCustomizedData,
                            totalAmountIncludingTax: 5,
                            totalAmountExcludingTax: 2,
                            totalTaxAmount: 2,
                        });

                        await assert.isRejected(
                            xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
                                context,
                                document,
                            ),
                            'Total amount including tax needs to be equal to the total amount excluding tax plus the total tax amount.',
                        );
                    },
                    { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
                ));
        });
    });
});

const sandbox = sinon.createSandbox();

describe('getSageAiStatus function', () => {
    afterEach(() => {
        sandbox.restore();
    });

    it('Uploaded purchasing document node - create', () =>
        Test.withContext(
            async context => {
                const mockEmails = {
                    accounts_payable: 'mockAccountsPayableEmail',
                    employee_expense: 'mockEmployeeExpenseEmail',
                };

                const mockDocumentStatus: xtremApAutomation.interfaces.ApAutomationReportData = {
                    status: 'completed',
                    has_feedback: false,
                    details: '',
                };
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
                    .returns(Promise.resolve(''));
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses')
                    .returns(Promise.resolve(mockEmails));

                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentStatus')
                    .returns(Promise.resolve(mockDocumentStatus));

                assert.deepEqual(
                    await xtremApAutomation.functions.getSageAiStatus(context, {
                        document: 'unit_test_document',
                        customer: 'customer_id_test',
                    }),
                    mockDocumentStatus,
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
});
