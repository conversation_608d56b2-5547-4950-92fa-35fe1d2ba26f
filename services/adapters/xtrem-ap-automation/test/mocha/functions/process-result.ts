import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremApAutomation from '../../../index';
import fixtureGenerator from './process-result-fixture';

describe('Process result', () => {
    let fixtureDocument: xtremApAutomation.interfaces.ApAutomationResult;

    beforeEach(() => {
        fixtureDocument = fixtureGenerator();
    });

    describe('identify origin', () => {
        it('API', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.metadata!.request_origin = 'API';
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    assert.equal(await document.origin, 'uploaded');
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));

        it('Email', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.metadata!.request_origin = 'email';

                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    assert.equal(await document.origin, 'email');
                    assert.equal(await document.recipientEmail, '<EMAIL>');
                    assert.equal(await document.senderEmail, '<EMAIL>');
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));
    });

    it('should extract supplier document number', () =>
        Test.withContext(
            async context => {
                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                });
                const supplierDocumentNumber = await document.supplierDocumentNumber;
                assert.isNotNull(supplierDocumentNumber);
                assert.equal(supplierDocumentNumber, 'SISW240005');
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));

    it('supplier document number could not be identified.', () =>
        Test.withContext(
            async context => {
                fixtureDocument.extraction.header!.document_id!.value = '';
                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                });
                assert.isEmpty(await document.supplierDocumentNumber);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));

    it('should extract the header amounts', () =>
        Test.withContext(
            async context => {
                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                });

                const totalAmountIncludingTax = await document.totalAmountIncludingTax;
                assert.isNotNull(totalAmountIncludingTax);
                assert.notStrictEqual(totalAmountIncludingTax, 1170.6);

                const totalAmountExcludingTax = await document.totalAmountExcludingTax;
                assert.isNotNull(totalAmountExcludingTax);
                assert.notStrictEqual(totalAmountIncludingTax, 975.5);

                const totalTaxAmount = await document.totalTaxAmount;
                assert.isNotNull(totalTaxAmount);
                assert.notStrictEqual(totalAmountExcludingTax, 195.1);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));

    it('No amounts found', () =>
        Test.withContext(
            async context => {
                fixtureDocument.extraction.header = {};
                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                });

                assert.equal(await document.totalAmountIncludingTax, 0);
                assert.equal(await document.totalAmountExcludingTax, 0);
                assert.equal(await document.totalTaxAmount, 0);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));

    it('should extract invoice date', () =>
        Test.withContext(
            async context => {
                fixtureDocument.extraction.header = {};
                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                });
                assert.isNull(await document.dueDate);
                assert.isNull(await document.supplierDocumentDate);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));

    describe('identify currency', () => {
        it('should find currency based on the currency code', () =>
            Test.withContext(
                async context => {
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    const currency = await document.currency;
                    assert.isNotNull(currency);
                    assert.equal(await currency.id, 'GBP');
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));

        it('should return null if the currency code is not supported', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.extraction.header!.currency!.value = 'CZK';
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    assert.isNull(await document.currency);
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));

        it('should return null if no header is found', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.extraction.header = {};
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    assert.isNull(await document.currency);
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));
    });

    describe('Identify site', () => {
        it('should find site based on tax ID', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.extraction.recipient!.company_id = { value: 'FR123456789', tagged: false };
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    const site = await document.site;
                    assert.isNotNull(site);
                    assert.equal(await site.name, 'Siège social S01  PARIS');
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));

        it('should find site based on name', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.extraction.recipient!.name = { value: 'Siège social S01  PARIS', tagged: false };
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    const site = await document.site;
                    assert.isNotNull(site);
                    assert.equal(await site.name, 'Siège social S01  PARIS');
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));

        it('Site could not be identified because recipient node is empty.', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.extraction = {};
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    assert.isNull(await document.supplier);
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));
    });

    describe('Identify supplier', () => {
        it('should find supplier based on tax ID', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.extraction.vendor!.company_id = { value: 'FR58483849894', tagged: false };
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    const supplier = await document.supplier;
                    assert.isNotNull(supplier);
                    assert.equal(await supplier.name, 'Siège social S01 PARIS');
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));

        it('should find supplier based on company ID', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.extraction.vendor!.external_id = { value: 'US017', tagged: false };
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    const supplier = await document.supplier;
                    assert.isNotNull(supplier);
                    assert.equal(await supplier.name, 'Siège social S01 PARIS');
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));

        it('should find supplier based on company name', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.extraction.vendor!.name = { value: 'Siège social S01 PARIS', tagged: false };
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    const supplier = await document.supplier;
                    assert.isNotNull(supplier);
                    assert.equal(await supplier.name, 'Siège social S01 PARIS');
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));

        it('supplier could not be identified because vendor node is empty.', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.extraction = {};
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    assert.isNull(await document.supplier);
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));
    });

    describe('document type', () => {
        it('should identify as invoice if it is set in the document header', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.extraction.header!.document_type = { value: 'debit', tagged: false };
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    const type = await document.type;
                    assert.equal(type, 'invoice');
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));

        it('should default to invoice if the document type is not set in the response', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.extraction.header!.document_type = undefined;
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    const documentType = await document.type;
                    assert.equal(documentType, 'invoice');
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));

        it('should identify as credit memo if any other value than invoice is set', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.extraction.header!.invoice_type = { value: 'credit', tagged: false };
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    const documentType = await document.type;
                    assert.equal(documentType, 'creditMemo');
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));
    });

    describe('mime type', () => {
        it('application/pdf', () => {
            fixtureDocument.metadata.file_name = 'invoice.pdf';
            assert.equal(xtremApAutomation.functions.identifyMimeType(fixtureDocument.metadata), 'application/pdf');
        });
        it('image/jpeg', () => {
            fixtureDocument.metadata.file_name = 'invoice.jpg';
            assert.equal(xtremApAutomation.functions.identifyMimeType(fixtureDocument.metadata), 'image/jpeg');
            fixtureDocument.metadata.file_name = 'invoice.jpeg';
            assert.equal(xtremApAutomation.functions.identifyMimeType(fixtureDocument.metadata), 'image/jpeg');
        });
        it('image/tiff', () => {
            fixtureDocument.metadata.file_name = 'invoice.tiff';
            assert.equal(xtremApAutomation.functions.identifyMimeType(fixtureDocument.metadata), 'image/tiff');
            fixtureDocument.metadata.file_name = 'invoice.tif';
            assert.equal(xtremApAutomation.functions.identifyMimeType(fixtureDocument.metadata), 'image/tiff');
        });
        it('image/png', () => {
            fixtureDocument.metadata.file_name = 'invoice.png';
            assert.equal(xtremApAutomation.functions.identifyMimeType(fixtureDocument.metadata), 'image/png');
        });
        it('application/xml', () => {
            fixtureDocument.metadata.file_name = 'invoice.xml';
            assert.equal(xtremApAutomation.functions.identifyMimeType(fixtureDocument.metadata), 'application/xml');
        });
        it('image/heic', () => {
            fixtureDocument.metadata.file_name = 'invoice.heic';
            assert.equal(xtremApAutomation.functions.identifyMimeType(fixtureDocument.metadata), 'image/heic');
        });
        it('image/heif', () => {
            fixtureDocument.metadata.file_name = 'invoice.heif';
            assert.equal(xtremApAutomation.functions.identifyMimeType(fixtureDocument.metadata), 'image/heif');
        });
        it('default', () => {
            fixtureDocument.metadata.file_name = 'invoice.123';
            assert.equal(xtremApAutomation.functions.identifyMimeType(fixtureDocument.metadata), 'application/pdf');
        });
    });
    // We add the lines later
    describe.skip('lines', () => {
        it('should find all lines', () =>
            Test.withContext(
                async context => {
                    fixtureDocument.extraction.vendor!.name = { value: 'Siège social S01 PARIS', tagged: false };
                    const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                        ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    });
                    const lineCount = await document.lines.length;
                    assert.equal(lineCount, 1);
                },
                {
                    testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption],
                },
            ));

        describe('item', () => {
            it('should find item by exact name match', () =>
                Test.withContext(
                    async context => {
                        fixtureDocument.extraction.line_items![0].description = {
                            value: 'Hydro-alcoholic 3 gel for hand antisepsis',
                            tagged: false,
                        };
                        const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                            ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                        });
                        const line = await document.lines.at(0);
                        const item = await line?.item;
                        assert.isNotNull(item);
                        assert.equal(await item?.name, 'Hydro-alcoholic 3 gel for hand antisepsis');
                        assert.equal(await item?.eanNumber, '3760141876403');
                    },
                    {
                        testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption],
                    },
                ));

            it('should find item by exact name with line breaks match', () =>
                Test.withContext(
                    async context => {
                        fixtureDocument.extraction.line_items![0].description = {
                            value: 'Hydro-alcoholic 3 gel\nfor hand antisepsis',
                            tagged: false,
                        };
                        const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                            ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                        });
                        const line = await document.lines.at(0);
                        const item = await line?.item;
                        assert.isNotNull(item);
                        assert.equal(await item?.name, 'Hydro-alcoholic 3 gel for hand antisepsis');
                        assert.equal(await item?.eanNumber, '3760141876403');
                    },
                    {
                        testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption],
                    },
                ));

            it('should find item by exact EAN number exact match', () =>
                Test.withContext(
                    async context => {
                        fixtureDocument.extraction.line_items![0].description = {
                            value: '3760141876403',
                            tagged: false,
                        };
                        const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                            ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                        });
                        const line = await document.lines.at(0);
                        const item = await line?.item;
                        assert.isNotNull(item);
                        assert.equal(await item?.name, 'Hydro-alcoholic 3 gel for hand antisepsis');
                        assert.equal(await item?.eanNumber, '3760141876403');
                    },
                    {
                        testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption],
                    },
                ));

            it('should find item by fragmented exact EAN number exact match', () =>
                Test.withContext(
                    async context => {
                        fixtureDocument.extraction.line_items![0].description = {
                            value: '3760 1418 764 03',
                            tagged: false,
                        };
                        const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                            ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                        });
                        const line = await document.lines.at(0);
                        const item = await line?.item;
                        assert.isNotNull(item);
                        assert.equal(await item?.name, 'Hydro-alcoholic 3 gel for hand antisepsis');
                        assert.equal(await item?.eanNumber, '3760141876403');
                    },
                    {
                        testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption],
                    },
                ));

            it('should find item by partial name match', () =>
                Test.withContext(
                    async context => {
                        fixtureDocument.extraction.line_items![0].description = {
                            value: 'HYDRO-alcoholic 3 gel',
                            tagged: false,
                        };
                        const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                            ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                        });
                        const line = await document.lines.at(0);
                        const item = await line?.item;
                        assert.isNotNull(item);
                        assert.equal(await item?.name, 'Hydro-alcoholic 3 gel for hand antisepsis');
                        assert.equal(await item?.eanNumber, '3760141876403');
                    },
                    {
                        testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption],
                    },
                ));
        });
    });
});
