import { Context, Test, Uuid } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremApAutomation from '../../../index';
import type { ApAutomationConfig } from '../../../lib/interfaces';

const sandbox = sinon.createSandbox();

describe('Common functions', () => {
    afterEach(() => {
        sandbox.restore();
    });
    it('Get package info  ', () =>
        Test.withContext(context => {
            Test.patchConfig({
                packages: {
                    '@sage/xtrem-ap-automation': {
                        clientId: 'sageIdClientId',
                        clientSecret: 'pass1234',
                        oauthEndpointUrl: 'https://sageid.test.com',
                        serviceUrl: 'https://unittests.for.sageAI.com/external',
                    },
                },
            });
            const packageConfig =
                context.configuration.getPackageConfig<ApAutomationConfig>('@sage/xtrem-ap-automation');
            assert.isNotNull(packageConfig);
        }));
    it('should create a new configuration successfully', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                assert.isNotNull(context.tenantId);
                const mockCustomerUniqueId = Uuid.generate().toString();
                const mockReturn = {
                    accounts_payable: 'mockAccountPayableEmail',
                    employee_expense: 'mockEmployeeResponseEmail',
                };
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
                    .returns(Promise.resolve(mockCustomerUniqueId));
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses')
                    .returns(Promise.resolve(mockReturn));

                const sageAiCompany = await xtremApAutomation.functions.sageAiRegistration(context, {
                    name: 'Test Company Name',
                    country: 'US',
                });

                assert.equal(sageAiCompany?.customerUniqueId, mockCustomerUniqueId);
                assert.equal(sageAiCompany?.accountsPayableEmail, mockReturn.accounts_payable);
                assert.equal(sageAiCompany?.employeeExpenseEmail, mockReturn.employee_expense);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('No user found', () =>
        Test.withContext(
            async context => {
                const customContext = { user: undefined, tenantId: context.tenantId };
                await assert.isRejected(
                    xtremApAutomation.functions.getTenantInformationByContext(customContext as any as Context),
                    'User not found',
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('No tenantId found', () =>
        Test.withContext(
            async context => {
                const customContext = { user: context.user, tenantId: '' };
                await assert.isRejected(
                    xtremApAutomation.functions.getTenantInformationByContext(customContext as any as Context),
                    'Tenant not found',
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('No tenantId found', () =>
        Test.withContext(
            async context => {
                sandbox.stub(Context.tenantManager, 'getTenantsInfo').returns(Promise.resolve([]));
                await assert.isRejected(
                    xtremApAutomation.functions.getTenantInformationByContext(context),
                    'Tenant not found',
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
});
