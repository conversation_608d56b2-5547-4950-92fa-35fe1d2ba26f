import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremApAutomation from '../../../index';
import type { ApAutomationConfig } from '../../../lib/interfaces/ap-automation-config';

describe('Ap automation configuration node', () => {
    it('Get package info', () =>
        Test.withContext(context => {
            Test.patchConfig({
                packages: {
                    '@sage/xtrem-ap-automation': {
                        clientId: '',
                        clientSecret: '',
                        oauthEndpointUrl: '',
                        serviceUrl: '',
                    },
                },
            });
            const packageConfig =
                context.configuration.getPackageConfig<ApAutomationConfig>('@sage/xtrem-ap-automation');
            assert.isNotNull(packageConfig);
        }));
    it('Error when there is configuration missing in @sage/xtrem-ap-automation', () =>
        Test.withContext(context => {
            assert.throws(
                () => new xtremApAutomation.classes.UploadedDocumentManager(context),
                'Missing configuration',
            );
        }));
});
