import type { Context } from '@sage/xtrem-core';
import { Test, date } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremApAutomation from '../../../index';
import type { ApAutomationConfig } from '../../../lib/interfaces/ap-automation-config';
import fixtureGenerator from '../functions/process-result-fixture';
import { createUploadedFile } from '../functions/uploaded-purchasing-document';

let fixtureDocument: xtremApAutomation.interfaces.ApAutomationResult;

const sandbox = sinon.createSandbox();

async function createTenantApAutomationConfiguration(context: Context) {
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
        .returns(Promise.resolve('customer_id_test'));
    sandbox.stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses').returns(
        Promise.resolve({
            accounts_payable: '<EMAIL>',
            employee_expense: '<EMAIL>',
        }),
    );
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'changeEmailSettings')
        .returns(Promise.resolve(true));

    const apAutomationConfigurationDefaultId =
        await xtremApAutomation.nodes.ApAutomationConfiguration.createOrGetDefaultRecord(context);
    const apAutomationConfiguration = await context.read(
        xtremApAutomation.nodes.ApAutomationConfiguration,
        { _id: apAutomationConfigurationDefaultId },
        { forUpdate: true },
    );
    await apAutomationConfiguration.$.set({ configurationLevel: 'tenant', country: 'DE' });
    await apAutomationConfiguration.$.save();
    assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);
}

describe('Ap automation configuration node', () => {
    beforeEach(() => {
        fixtureDocument = fixtureGenerator();
    });
    afterEach(() => {
        sandbox.restore();
    });
    it('Get package info', () =>
        Test.withContext(context => {
            Test.patchConfig({
                packages: {
                    '@sage/xtrem-ap-automation': {
                        clientId: 'sageIdClientId',
                        clientSecret: 'pass1234',
                        oauthEndpointUrl: 'https://sageid.test.com',
                        serviceUrl: 'https://unittests.for.sageAI.com/external',
                    },
                },
            });
            const packageConfig =
                context.configuration.getPackageConfig<ApAutomationConfig>('@sage/xtrem-ap-automation');
            assert.isNotNull(packageConfig);
        }));
    it('confirmUploadedPurchasingDocument - Fail when document type is not invoice or creditMemo', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    supplier: '#US017',
                    site: '#ETS1-S01',
                    currency: '#EUR',
                    supplierDocumentDate: date.today().addDays(-1),
                    type: null,
                    uploadedFile: await createUploadedFile(context),
                    isTempUuid: false,
                    customerUniqueId: 'customer_id_test',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
                await assert.isRejected(
                    manager.confirmUploadedPurchasingDocument(document),
                    'Document type is not supported:',
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
});
