import { Context, Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremApAutomation from '../../../index';
import type { ApAutomationConfig } from '../../../lib/interfaces';

const sandbox = sinon.createSandbox();
function mockCreateCompany(parameters: {
    customerUniqueId: string;
    accountsPayableEmail: string;
    employeeExpenseEmail: string;
}) {
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
        .returns(Promise.resolve(parameters.customerUniqueId));
    sandbox.stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses').returns(
        Promise.resolve({
            accounts_payable: parameters.accountsPayableEmail,
            employee_expense: parameters.employeeExpenseEmail,
        }),
    );
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'changeEmailSettings')
        .returns(Promise.resolve(true));
}

describe('Purchase invoice extension', () => {
    afterEach(() => {
        sandbox.restore();
    });

    it('Get package info  ', () =>
        Test.withContext(context => {
            Test.patchConfig({
                packages: {
                    '@sage/xtrem-ap-automation': {
                        clientId: 'sageIdClientId',
                        clientSecret: 'pass1234',
                        oauthEndpointUrl: 'https://sageid.test.com',
                        serviceUrl: 'https://unittests.for.sageAI.com/external',
                    },
                },
            });
            const packageConfig =
                context.configuration.getPackageConfig<ApAutomationConfig>('@sage/xtrem-ap-automation');
            assert.isNotNull(packageConfig);
        }));
    /**
     *  the patchConfig will be effective after the iteration !
     */
    it('Register Company on Sage AI', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                mockCreateCompany({
                    customerUniqueId: 'test_company_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });
                const company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });
                await xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, company);

                const companyConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationCompany, {
                    company,
                });
                assert.equal(await companyConfiguration.customerUniqueId, 'test_company_customer_id');
                assert.equal(await companyConfiguration.accountsPayableEmail, '<EMAIL>');
                assert.equal(await companyConfiguration.employeeExpenseEmail, '<EMAIL>');
                assert.isTrue(await companyConfiguration.isInboxActive);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));
    it('Register a company twice shouldn`t change anything', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                mockCreateCompany({
                    customerUniqueId: 'test_company_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });
                const company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });
                await xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, company);

                let companyConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationCompany, { company });
                assert.equal(await companyConfiguration.customerUniqueId, 'test_company_customer_id');
                assert.equal(await companyConfiguration.accountsPayableEmail, '<EMAIL>');
                assert.equal(await companyConfiguration.employeeExpenseEmail, '<EMAIL>');
                assert.isTrue(await companyConfiguration.isInboxActive);

                await xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, company);
                companyConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationCompany, { company });
                assert.equal(await companyConfiguration.customerUniqueId, 'test_company_customer_id');
                assert.equal(await companyConfiguration.accountsPayableEmail, '<EMAIL>');
                assert.equal(await companyConfiguration.employeeExpenseEmail, '<EMAIL>');
                assert.isTrue(await companyConfiguration.isInboxActive);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));
    it('Set company configuration active - Tenant duplication', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                // Register a company to check if the inbox is deactivated
                mockCreateCompany({
                    customerUniqueId: 'test_company_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });

                let company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });
                await xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, company);
                // Register company configuration
                let apAutomationConfiguration = await context.read(
                    xtremApAutomation.nodes.ApAutomationConfiguration,
                    { id: 'default' },
                    { forUpdate: true },
                );
                await apAutomationConfiguration.$.set({ configurationLevel: 'company' });
                await apAutomationConfiguration.$.save();

                assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);

                //  Simulate tenant duplication
                sandbox.restore();

                const [tenantInfo] = await Context.tenantManager.getTenantsInfo(context, context.tenantId ?? '');
                tenantInfo.id = 'TenantDuplication';
                sandbox.stub(Context.tenantManager, 'getTenantsInfo').returns(Promise.resolve([tenantInfo]));

                mockCreateCompany({
                    customerUniqueId: 'test_company_duplication_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });

                // Register the company configuration again
                const tenantConfigurationId =
                    await xtremApAutomation.nodes.ApAutomationConfiguration.createOrGetDefaultRecord(context);
                apAutomationConfiguration = await context.read(
                    xtremApAutomation.nodes.ApAutomationConfiguration,
                    { _id: tenantConfigurationId },
                    { forUpdate: true },
                );
                await apAutomationConfiguration.$.set({ configurationLevel: 'company' });
                await apAutomationConfiguration.$.save();
                assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);

                company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });
                const companyConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationCompany, {
                    company,
                });

                assert.equal(await companyConfiguration?.customerUniqueId, 'test_company_duplication_customer_id');
                assert.equal(
                    await companyConfiguration?.accountsPayableEmail,
                    '<EMAIL>',
                );
                assert.equal(
                    await companyConfiguration?.employeeExpenseEmail,
                    '<EMAIL>',
                );
                assert.isTrue(await companyConfiguration.isInboxActive);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));

    it('Set company configuration active using register on sage ai- Tenant duplication', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                // Register a company to check if the inbox is deactivated
                mockCreateCompany({
                    customerUniqueId: 'test_company_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });

                let company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });
                await xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, company);

                sandbox.restore();

                const [tenantInfo] = await Context.tenantManager.getTenantsInfo(context, context.tenantId ?? '');
                tenantInfo.id = 'TenantDuplication';
                sandbox.stub(Context.tenantManager, 'getTenantsInfo').returns(Promise.resolve([tenantInfo]));

                mockCreateCompany({
                    customerUniqueId: 'test_company_duplication_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });

                company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });
                await xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, company);

                const companyConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationCompany, {
                    company,
                });

                assert.equal(await companyConfiguration?.customerUniqueId, 'test_company_duplication_customer_id');
                assert.equal(
                    await companyConfiguration?.accountsPayableEmail,
                    '<EMAIL>',
                );
                assert.equal(
                    await companyConfiguration?.employeeExpenseEmail,
                    '<EMAIL>',
                );
                assert.isTrue(await companyConfiguration.isInboxActive);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
});
