import type { BinaryStream, NodeCreateData } from '@sage/xtrem-core';
import { Context, Test, ValidationSeverity, asyncArray, date } from '@sage/xtrem-core';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremApAutomation from '../../../index';
import type { ApAutomationConfig } from '../../../lib/interfaces';
import { salesInvoiceToByteStream } from '../../fixtures/sales-invoice-to-binary-stream';
import fixtureGenerator from '../functions/process-result-fixture';
import { createUploadedFile, createUploadedFileMimeType } from '../functions/uploaded-purchasing-document';

const sandbox = sinon.createSandbox();
let fixtureDocument: xtremApAutomation.interfaces.ApAutomationResult;

async function createTenantApAutomationConfiguration(context: Context) {
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
        .returns(Promise.resolve('test_customer_id'));
    sandbox.stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses').returns(
        Promise.resolve({
            accounts_payable: '<EMAIL>',
            employee_expense: '<EMAIL>',
        }),
    );
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'changeEmailSettings')
        .returns(Promise.resolve(true));

    const apAutomationConfigurationDefaultId =
        await xtremApAutomation.nodes.ApAutomationConfiguration.createOrGetDefaultRecord(context);
    const apAutomationConfiguration = await context.read(
        xtremApAutomation.nodes.ApAutomationConfiguration,
        { _id: apAutomationConfigurationDefaultId },
        { forUpdate: true },
    );
    if ((await apAutomationConfiguration.country) === null) {
        await apAutomationConfiguration.$.set({ country: 'DE' });
    }
    await apAutomationConfiguration.$.set({ configurationLevel: 'tenant' });
    await apAutomationConfiguration.$.save();
    assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);
}

async function createCompanyApAutomationConfiguration(context: Context) {
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
        .returns(Promise.resolve('test_company_customer_id'));
    sandbox.stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses').returns(
        Promise.resolve({
            accounts_payable: '<EMAIL>',
            employee_expense: '<EMAIL>',
        }),
    );
    const company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });
    await xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, company);
}

function mockCreateCompany(parameters: {
    customerUniqueId: string;
    accountsPayableEmail: string;
    employeeExpenseEmail: string;
}) {
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
        .returns(Promise.resolve(parameters.customerUniqueId));
    sandbox.stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses').returns(
        Promise.resolve({
            accounts_payable: parameters.accountsPayableEmail,
            employee_expense: parameters.employeeExpenseEmail,
        }),
    );
}

describe('Status management test', () => {
    beforeEach(() => {
        fixtureDocument = fixtureGenerator();
    });

    afterEach(() => {
        sandbox.restore();
    });

    it('Get package info  ', () =>
        Test.withContext(context => {
            Test.patchConfig({
                packages: {
                    '@sage/xtrem-ap-automation': {
                        clientId: 'sageIdClientId',
                        clientSecret: 'pass1234',
                        oauthEndpointUrl: 'https://sageid.test.com',
                        serviceUrl: 'https://unittests.for.sageAI.com/external',
                    },
                },
            });
            const packageConfig =
                context.configuration.getPackageConfig<ApAutomationConfig>('@sage/xtrem-ap-automation');
            assert.isNotNull(packageConfig);
        }));
    /**
     *  the patchConfig will be effective after the iteration !
     */
    it('Draft', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);
                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    uploadedFile: await createUploadedFile(context),
                    isTempUuid: false,
                    customerUniqueId: 'test_customer_id',
                });
                assert.equal(await document.status, 'draft');
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('Review in progress', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                let document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    uploadedFile: await createUploadedFile(context),
                    isTempUuid: false,
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                document = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: fixtureDocument.metadata.identifier },
                    { forUpdate: true },
                );
                await document.$.set({ totalAmountIncludingTax: 120 });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                assert.equal(await document.status, 'reviewInProgress');
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));
    it('Review done', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                let document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    supplier: '#US017',
                    site: '#ETS1-S01',
                    currency: '#EUR',
                    supplierDocumentDate: date.today().addDays(-1),
                    uploadedFile: await createUploadedFile(context),
                    documentMimeType: 'application/pdf',
                    isTempUuid: false,
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
                const newPurchaseDocument = await manager.confirmDocument(document);
                await context.flushDeferredActions();
                assert.equal(await newPurchaseDocument?.number, 'PI240007');

                document = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: fixtureDocument.metadata.identifier },
                    { forUpdate: true },
                );
                assert.equal(await document.status, 'reviewDone');

                // Once the record is review done, we can't change the UploadPurchasingDocument except the status

                await assert.isRejected(
                    document.$.set({ isResultPopulated: false }),
                    'UploadedPurchasingDocument.isResultPopulated: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ supplier: null }),
                    'UploadedPurchasingDocument.supplier: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ site: null }),
                    'UploadedPurchasingDocument.site: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ currency: { id: '#GDP' } }),
                    'UploadedPurchasingDocument.currency: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ type: 'creditMemo' }),
                    'UploadedPurchasingDocument.type: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ supplierDocumentDate: date.today().addDays(-2) }),
                    'UploadedPurchasingDocument.supplierDocumentDate: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ supplierDocumentNumber: 'SIPEN240001' }),
                    'UploadedPurchasingDocument.supplierDocumentNumber: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ lines: [] }),
                    'UploadedPurchasingDocument.lines: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ documentMimeType: 'jpg' }),
                    'UploadedPurchasingDocument.documentMimeType: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ dueDate: date.today().addDays(3) }),
                    'UploadedPurchasingDocument.dueDate: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ totalTaxAmount: 15 }),
                    'UploadedPurchasingDocument.totalTaxAmount: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ totalAmountExcludingTax: 135 }),
                    'UploadedPurchasingDocument.totalAmountExcludingTax: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ totalAmountIncludingTax: 150 }),
                    'UploadedPurchasingDocument.totalAmountIncludingTax: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ origin: null }),
                    'UploadedPurchasingDocument.origin: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ result: { extraction: {}, metadata: { identifier: '', request_origin: '' } } }),
                    'UploadedPurchasingDocument.result: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ postingDate: date.today().addDays(2) }),
                    'UploadedPurchasingDocument.postingDate: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ reason: 'R3' }),
                    'UploadedPurchasingDocument.reason: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ customerUniqueId: 'test' }),
                    'UploadedPurchasingDocument.customerUniqueId: cannot set value on frozen property',
                );

                // deleting the purchase invoice should update the status again
                const purchaseInvoice = await context.read(
                    xtremPurchasing.nodes.PurchaseInvoice,
                    { number: 'PI240007' },
                    { forUpdate: true },
                );

                await purchaseInvoice.$.delete();
                assert.deepEqual(purchaseInvoice.$.context.diagnoses, []);

                document = await context.read(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    uuid: fixtureDocument.metadata.identifier,
                });
                assert.equal(await document.status, 'reviewInProgress');
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));
    it('Result not populated - Frozen properties', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    uuid: fixtureDocument.metadata.identifier,
                    uploadedFile: await createUploadedFile(context),
                    isTempUuid: false,
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);
                assert.isFalse(await document.isResultPopulated);

                // When the record is not populated, we can't change the UploadPurchasingDocument except the status
                await assert.isRejected(
                    document.$.set({ supplier: null }),
                    'UploadedPurchasingDocument.supplier: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ site: null }),
                    'UploadedPurchasingDocument.site: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ currency: { id: '#GDP' } }),
                    'UploadedPurchasingDocument.currency: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ type: 'creditMemo' }),
                    'UploadedPurchasingDocument.type: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ supplierDocumentDate: date.today().addDays(-2) }),
                    'UploadedPurchasingDocument.supplierDocumentDate: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ supplierDocumentNumber: 'SIPEN240001' }),
                    'UploadedPurchasingDocument.supplierDocumentNumber: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ lines: [] }),
                    'UploadedPurchasingDocument.lines: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ documentMimeType: 'jpg' }),
                    'UploadedPurchasingDocument.documentMimeType: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ dueDate: date.today().addDays(3) }),
                    'UploadedPurchasingDocument.dueDate: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ totalTaxAmount: 15 }),
                    'UploadedPurchasingDocument.totalTaxAmount: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ totalAmountExcludingTax: 135 }),
                    'UploadedPurchasingDocument.totalAmountExcludingTax: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ totalAmountIncludingTax: 150 }),
                    'UploadedPurchasingDocument.totalAmountIncludingTax: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ origin: null }),
                    'UploadedPurchasingDocument.origin: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ result: { extraction: {}, metadata: { identifier: '', request_origin: '' } } }),
                    'UploadedPurchasingDocument.result: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ postingDate: date.today().addDays(2) }),
                    'UploadedPurchasingDocument.postingDate: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ reason: 'R3' }),
                    'UploadedPurchasingDocument.reason: cannot set value on frozen property',
                );

                await assert.isRejected(
                    document.$.set({ customerUniqueId: 'test' }),
                    'UploadedPurchasingDocument.customerUniqueId: cannot set value on frozen property',
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));
    it('Error', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                let document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    supplier: '#US017',
                    site: '#ETS1-S01',
                    currency: '#EUR',
                    supplierDocumentDate: date.today().addDays(30),
                    uploadedFile: await createUploadedFile(context),
                    isTempUuid: false,
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
                await assert.isRejected(manager.confirmDocument(document), 'The record was not created.');
                assert.deepEqual(context.diagnoses, [
                    {
                        severity: 3,
                        message: 'The date cannot be later than the current date.',
                        path: ['supplierDocumentDate'],
                    },
                ]);

                document = await context.read(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    uuid: fixtureDocument.metadata.identifier,
                });
                assert.equal(await document.status, 'error');
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));
    it('Change from Error to Review in progress', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                let document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    supplier: '#US017',
                    site: '#ETS1-S01',
                    currency: '#EUR',
                    supplierDocumentDate: date.today().addDays(30),
                    uploadedFile: await createUploadedFile(context),
                    isTempUuid: false,
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
                await assert.isRejected(manager.confirmDocument(document), 'The record was not created.');
                assert.deepEqual(document.$.context.diagnoses, [
                    {
                        severity: 3,
                        message: 'The date cannot be later than the current date.',
                        path: ['supplierDocumentDate'],
                    },
                ]);

                document = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: fixtureDocument.metadata.identifier },
                    { forUpdate: true },
                );
                assert.equal(await document.status, 'error');

                await document.$.set({ supplierDocumentDate: date.today().addDays(-1) });
                await document.$.save();

                document = await context.read(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    uuid: fixtureDocument.metadata.identifier,
                });
                assert.equal(await document.status, 'reviewInProgress');
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('Create credit memo', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                let document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    supplier: '#US017',
                    site: '#ETS1-S01',
                    currency: '#EUR',
                    supplierDocumentDate: date.today().addDays(-1),
                    type: 'creditMemo',
                    reason: 'R3',
                    uploadedFile: await createUploadedFile(context),
                    isTempUuid: false,
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
                const newPurchaseDocument = await manager.confirmDocument(document);
                await context.flushDeferredActions();
                assert.equal(await newPurchaseDocument?.number, 'PC240003');

                document = await context.read(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    uuid: fixtureDocument.metadata.identifier,
                });
                assert.equal(await document.status, 'reviewDone');

                // now, confirming the same document again should throw an error

                await assert.isRejected(
                    xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
                        context,
                        document,
                    ),
                    'A purchasing document was already created.',
                );

                // deleting the purchase credit memo should update the status again
                const purchaseCreditMemo = await context.read(
                    xtremPurchasing.nodes.PurchaseCreditMemo,
                    { number: await newPurchaseDocument.number },
                    { forUpdate: true },
                );

                await purchaseCreditMemo.$.delete();
                assert.deepEqual(purchaseCreditMemo.$.context.diagnoses, []);

                document = await context.read(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    uuid: fixtureDocument.metadata.identifier,
                });
                assert.equal(await document.status, 'reviewInProgress');
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('reset uploaded purchase document successfully', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                let document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    site: '#ETS1-S01',
                    currency: '#EUR',
                    supplierDocumentDate: date.today().addDays(-1),
                    type: 'creditMemo',
                    reason: 'R3',
                    uploadedFile: await createUploadedFile(context),
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                await document.$.set({ supplier: '#US017' });
                await document.$.save();

                document = await context.read(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    uuid: fixtureDocument.metadata.identifier,
                });
                assert.equal(await document.status, 'reviewInProgress');

                await xtremApAutomation.nodes.UploadedPurchasingDocument.resetUploadedDocument(context, document);
                const documentAfterReset = await context.read(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    uuid: fixtureDocument.metadata.identifier,
                });
                assert.equal(await documentAfterReset.status, 'draft');

                assert.isNull(await documentAfterReset.supplier);
                assert.isNull(await documentAfterReset.site);
                assert.equal((await documentAfterReset.supplierDocumentDate)?.value, 20241016);
                assert.equal(await documentAfterReset.type, 'invoice');
                assert.equal(await (await documentAfterReset.reason)?.id, 'R3'); // not modified by processResult
                assert.equal(await (await documentAfterReset.currency)?.id, 'GBP');
                assert.equal(await document.status, 'draft');
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('reset uploaded purchase credit memo with review done', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                let document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    supplier: '#US017',
                    site: '#ETS1-S01',
                    currency: '#EUR',
                    supplierDocumentDate: date.today().addDays(-1),
                    type: 'creditMemo',
                    reason: 'R3',
                    uploadedFile: await createUploadedFile(context),
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
                const purchaseCreditMemo = await manager.confirmDocument(document);
                await context.flushDeferredActions();
                assert.equal(await purchaseCreditMemo?.number, 'PC240003');

                document = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: fixtureDocument.metadata.identifier },
                    { forUpdate: true },
                );
                assert.equal(await document.status, 'reviewDone');

                await assert.isRejected(
                    xtremApAutomation.nodes.UploadedPurchasingDocument.resetUploadedDocument(context, document),
                    'The purchasing document status is review done.',
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('reset uploaded purchase invoice with review done', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                let document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    supplier: '#US017',
                    site: '#ETS1-S01',
                    currency: '#EUR',
                    supplierDocumentDate: date.today().addDays(-1),
                    type: 'invoice',
                    reason: 'R3',
                    uploadedFile: await createUploadedFile(context),
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
                const purchaseInvoice = await manager.confirmDocument(document);
                await context.flushDeferredActions();
                assert.equal(await purchaseInvoice?.number, 'PI240007');

                document = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: fixtureDocument.metadata.identifier },
                    { forUpdate: true },
                );
                assert.equal(await document.status, 'reviewDone');

                await assert.isRejected(
                    xtremApAutomation.nodes.UploadedPurchasingDocument.resetUploadedDocument(context, document),
                    'The purchasing document status is review done.',
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('reset existing uploaded purchase invoice document', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                let document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    supplier: '#US017',
                    site: '#ETS1-S01',
                    currency: '#EUR',
                    supplierDocumentDate: date.today().addDays(-1),
                    type: 'invoice',
                    reason: 'R3',
                    uploadedFile: await createUploadedFile(context),
                    isTempUuid: false,
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
                const purchaseInvoice = await manager.confirmDocument(document);
                await context.flushDeferredActions();
                assert.equal(await purchaseInvoice?.number, 'PI240007');

                await document.$.set({ status: 'reviewInProgress' }); // Bypass status reviewDone check
                await document.$.save();

                document = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: fixtureDocument.metadata.identifier },
                    { forUpdate: true },
                );
                assert.equal(await document.status, 'reviewInProgress');

                await assert.isRejected(
                    xtremApAutomation.nodes.UploadedPurchasingDocument.resetUploadedDocument(context, document),
                    'A purchasing document was already created.',
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('reset existing uploaded purchase creditMemo document', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                let document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    supplier: '#US017',
                    site: '#ETS1-S01',
                    currency: '#EUR',
                    supplierDocumentDate: date.today().addDays(-1),
                    type: 'creditMemo',
                    reason: 'R3',
                    uploadedFile: await createUploadedFile(context),
                    isTempUuid: false,
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
                const purchaseCreditMemo = await manager.confirmDocument(document);
                await context.flushDeferredActions();
                assert.equal(await purchaseCreditMemo?.number, 'PC240003');

                await document.$.set({ status: 'reviewInProgress' }); // Bypass status reviewDone check
                await document.$.save();

                document = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: fixtureDocument.metadata.identifier },
                    { forUpdate: true },
                );
                assert.equal(await document.status, 'reviewInProgress');

                await assert.isRejected(
                    xtremApAutomation.nodes.UploadedPurchasingDocument.resetUploadedDocument(context, document),
                    'A purchasing document was already created.',
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('Ensure purchase credit memos with equal supplier document numbers return a warning message', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                const changeFixtureDocumentValues: NodeCreateData<xtremApAutomation.nodes.UploadedPurchasingDocument> =
                    {
                        supplier: '#US017',
                        site: '#ETS1-S01',
                        currency: '#EUR',
                        supplierDocumentDate: date.today().addDays(-1),
                        type: 'creditMemo',
                        reason: 'R3',
                    };
                const uploadedFile = await createUploadedFile(context);

                const uploadedDocument1 = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    ...changeFixtureDocumentValues,
                    uuid: fixtureDocument.metadata.identifier,
                    uploadedFile,
                    customerUniqueId: 'test_customer_id',
                    isTempUuid: false,
                });
                await uploadedDocument1.$.save();
                assert.deepEqual(uploadedDocument1.$.context.diagnoses, []);

                const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
                const purchaseCreditMemo = await manager.confirmDocument(uploadedDocument1);
                await context.flushDeferredActions();
                assert.equal(await purchaseCreditMemo?.number, 'PC240003');

                const uploadedDocument2 = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    ...changeFixtureDocumentValues,
                    uuid: 'test supplier document date',
                    customerUniqueId: 'test_customer_id',
                    uploadedFile,
                });

                await uploadedDocument2.$.save();
                assert.deepStrictEqual(uploadedDocument2.$.context.diagnoses, [
                    {
                        severity: ValidationSeverity.warn,
                        path: [],
                        message: 'Supplier document number already exists.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('Processing status should return completed due to isResultPopulated being true', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    uuid: fixtureDocument.metadata.identifier,
                    isResultPopulated: true,
                    uploadedFile: await createUploadedFile(context),
                    isTempUuid: false,
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                const mockDocumentStatus: xtremApAutomation.interfaces.ApAutomationReportData = {
                    status: 'completed',
                    has_feedback: false,
                    details: '',
                };

                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentStatus')
                    .returns(Promise.resolve(mockDocumentStatus));

                assert.equal(await document.processingStatus, 'completed');
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('Processing details should be empty due to isResultPopulated being true', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    uuid: fixtureDocument.metadata.identifier,
                    isResultPopulated: true,
                    isTempUuid: false,
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                const mockDocumentStatus: xtremApAutomation.interfaces.ApAutomationReportData = {
                    status: 'completed',
                    has_feedback: false,
                    details: 'mockDetails',
                };

                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentStatus')
                    .returns(Promise.resolve(mockDocumentStatus));

                assert.equal(await document.processingDetails, '');
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('Processing details should return details from sage ai', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    uuid: fixtureDocument.metadata.identifier,
                    isTempUuid: false,
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();
                assert.deepEqual(document.$.context.diagnoses, []);

                const mockDocumentStatus: xtremApAutomation.interfaces.ApAutomationReportData = {
                    status: 'processing',
                    has_feedback: false,
                    details: 'mockDetails',
                };

                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentStatus')
                    .returns(Promise.resolve(mockDocumentStatus));

                assert.equal(await document.processingDetails, mockDocumentStatus.details);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));
    it('Error when trying to create a document with an invalid customer id - tenant configuration', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);
                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    uploadedFile: await createUploadedFile(context),
                    isTempUuid: false,
                    customerUniqueId: 'different_customer_unique_id',
                });
                await assert.isRejected(document.$.save());

                assert.deepEqual(document.$.context.diagnoses, [
                    { severity: 3, path: [], message: 'The unique customer ID does not match tenant configuration.' },
                ]);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('Error when trying to create a document with an invalid customer id - company configuration', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createCompanyApAutomationConfiguration(context);
                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    uploadedFile: await createUploadedFile(context),
                    isTempUuid: false,
                    customerUniqueId: 'different_customer_unique_id',
                });
                await assert.isRejected(document.$.save());

                assert.deepEqual(document.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: [],
                        message: 'The unique customer ID does not match any company configuration.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('Error when trying to create a document with an invalid customer id - company configuration - when the tenant is duplicated', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                // Register company
                await createCompanyApAutomationConfiguration(context);

                // Duplicate tenant
                const [tenantInfo] = await Context.tenantManager.getTenantsInfo(context, context.tenantId ?? '');
                tenantInfo.id = 'TenantDuplication';
                sandbox.stub(Context.tenantManager, 'getTenantsInfo').returns(Promise.resolve([tenantInfo]));

                // No configuration should be found because the tenant has changed even sending the older customer unique id (test_company_customer_id)
                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    uploadedFile: await createUploadedFile(context),
                    isTempUuid: false,
                    customerUniqueId: 'test_company_customer_id',
                });
                await assert.isRejected(document.$.save());

                assert.deepEqual(document.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: [],
                        message: 'The unique customer ID does not match any company configuration.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
});

describe('Update document with details mutation', () => {
    beforeEach(() => {
        fixtureDocument = fixtureGenerator();
    });

    afterEach(() => {
        sandbox.restore();
    });
    it('Get package info  ', () =>
        Test.withContext(context => {
            Test.patchConfig({
                packages: {
                    '@sage/xtrem-ap-automation': {
                        clientId: 'sageIdClientId',
                        clientSecret: 'pass1234',
                        oauthEndpointUrl: 'https://sageid.test.com',
                        serviceUrl: 'https://unittests.for.sageAI.com/external',
                    },
                },
            });
            const packageConfig =
                context.configuration.getPackageConfig<ApAutomationConfig>('@sage/xtrem-ap-automation');
            assert.isNotNull(packageConfig);
        }));
    it('Should populate document with details successfully', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                const document = await context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    uuid: fixtureDocument.metadata.identifier,
                    uploadedFile: await createUploadedFile(context),
                    customerUniqueId: 'test_customer_id',
                });
                await document.$.save();

                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentResults')
                    .returns(Promise.resolve(fixtureDocument));

                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentStatus')
                    .returns(Promise.resolve({ status: 'completed' }));

                assert.equal(await document.isResultPopulated, false);
                assert.isEmpty(await document.result);

                await xtremApAutomation.nodes.UploadedPurchasingDocument.updateDocumentWithDetails(context, document);

                assert.equal(await document.isResultPopulated, true);
                assert.isNotEmpty(await document.result);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
});

async function mockFunctionsToSyncDocuments(
    context: Context,
    parameters: {
        documentList: string[];
        salesInvoiceBinaryStream: BinaryStream;
        fixtureDocument: xtremApAutomation.interfaces.ApAutomationResult;
        status: xtremApAutomation.interfaces.ApAutomationReportStatus;
    },
) {
    await createTenantApAutomationConfiguration(context);
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentList')
        .returns(Promise.resolve(parameters.documentList));

    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentStatus')
        .returns(Promise.resolve({ status: parameters.status }));

    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'downloadDocument')
        .returns(Promise.resolve(parameters.salesInvoiceBinaryStream));

    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentResults')
        .returns(Promise.resolve(parameters.fixtureDocument));
}

async function mockFunctionsToSyncDocumentsForCompanies(
    context: Context,
    parameters: {
        documentList: string[];
        salesInvoiceBinaryStream: BinaryStream;
        fixtureDocument: xtremApAutomation.interfaces.ApAutomationResult;
        status: xtremApAutomation.interfaces.ApAutomationReportStatus;
    },
) {
    mockCreateCompany({
        customerUniqueId: 'test_tenant_customer_id',
        accountsPayableEmail: '<EMAIL>',
        employeeExpenseEmail: '<EMAIL>',
    });
    const company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });
    await xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, company);
    assert.deepEqual(company.$.context.diagnoses, []);

    // Register company configuration
    const apAutomationConfiguration = await context.read(
        xtremApAutomation.nodes.ApAutomationConfiguration,
        { id: 'default' },
        { forUpdate: true },
    );
    await apAutomationConfiguration.$.set({ configurationLevel: 'company' });
    await apAutomationConfiguration.$.save();
    assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);

    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentList')
        .returns(Promise.resolve(parameters.documentList));

    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentStatus')
        .returns(Promise.resolve({ status: parameters.status }));

    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'downloadDocument')
        .returns(Promise.resolve(parameters.salesInvoiceBinaryStream));

    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentResults')
        .returns(Promise.resolve(parameters.fixtureDocument));
}

describe('Sync documents', () => {
    beforeEach(() => {
        fixtureDocument = fixtureGenerator();
        fixtureDocument.metadata.file_name = 'salesInvoice.pdf';
        fixtureDocument.metadata.request_origin = 'email';
    });

    afterEach(() => {
        sandbox.restore();
    });

    it('Should sync documents for tenants successfully', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                const documentList = ['documentId1', 'documentId2', 'documentId3'];

                await mockFunctionsToSyncDocuments(context, {
                    documentList,
                    salesInvoiceBinaryStream: await salesInvoiceToByteStream(),
                    fixtureDocument,
                    status: 'completed',
                });

                const documentsBeforeSync = await context.queryCount(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                );
                assert.equal(documentsBeforeSync, 0);

                await xtremApAutomation.nodes.UploadedPurchasingDocument.syncDocuments(context);
                const docsAfterSync = await context.select(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: true, document: true, status: true },
                    { filter: {} },
                );
                assert.equal(docsAfterSync.length, 3);
                docsAfterSync.forEach((uploadedPurchasingDocument, idx) => {
                    assert.equal(uploadedPurchasingDocument.uuid, documentList[idx]);
                    assert.isNotEmpty(uploadedPurchasingDocument.document.value);
                    assert.equal(uploadedPurchasingDocument.status, 'draft');
                });
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('Should sync documents for companies successfully', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                const documentList = ['documentId1', 'documentId2', 'documentId3'];

                await mockFunctionsToSyncDocumentsForCompanies(context, {
                    documentList,
                    salesInvoiceBinaryStream: await salesInvoiceToByteStream(),
                    fixtureDocument,
                    status: 'completed',
                });

                const documentsBeforeSync = await context.queryCount(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                );
                assert.equal(documentsBeforeSync, 0);

                await xtremApAutomation.nodes.UploadedPurchasingDocument.syncDocuments(context);

                const docsAfterSync = await context
                    .query(xtremApAutomation.nodes.UploadedPurchasingDocument, {})
                    .toArray();

                assert.equal(docsAfterSync.length, 3);

                await asyncArray(docsAfterSync).forEach(async (uploadedPurchasingDocument, idx) => {
                    const uuid = await uploadedPurchasingDocument.uuid;
                    assert.equal(uuid, documentList[idx]);
                    const document = (await uploadedPurchasingDocument.document).value;
                    assert.isNotEmpty(document);
                    const status = await uploadedPurchasingDocument.status;
                    assert.equal(status, 'draft');
                    const apCompany = await uploadedPurchasingDocument.apAutomationCompany;
                    assert.isNotNull(apCompany);
                    const linkedSites = (await uploadedPurchasingDocument.linkedSites).length;
                    assert.equal(linkedSites, 1);
                });
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('Should sync documents but with no document associated due to status being error', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                const documentList = ['documentId1', 'documentId2', 'documentId3'];

                await mockFunctionsToSyncDocuments(context, {
                    documentList,
                    salesInvoiceBinaryStream: await salesInvoiceToByteStream(),
                    fixtureDocument,
                    status: 'error',
                });

                const docsBeforeSync = await context.queryCount(xtremApAutomation.nodes.UploadedPurchasingDocument);
                assert.equal(docsBeforeSync, 0);

                await xtremApAutomation.nodes.UploadedPurchasingDocument.syncDocuments(context);
                const docsAfterSync = await context.select(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: true, document: true },
                    { filter: {} },
                );
                assert.equal(docsAfterSync.length, 3);
                docsAfterSync.forEach(uploadedPurchasingDocument => {
                    assert.isEmpty(uploadedPurchasingDocument.document.value);
                });
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('Should sync documents but with no document associated due to status being processing', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                const documentList = ['documentId1'];

                await mockFunctionsToSyncDocuments(context, {
                    documentList,
                    salesInvoiceBinaryStream: await salesInvoiceToByteStream(),
                    fixtureDocument,
                    status: 'processing',
                });

                const docsBeforeSync = await context.queryCount(xtremApAutomation.nodes.UploadedPurchasingDocument);
                assert.equal(docsBeforeSync, 0);

                await xtremApAutomation.nodes.UploadedPurchasingDocument.syncDocuments(context);
                const docsAfterSync = await context.select(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: true, document: true, result: true },
                    { filter: {} },
                );
                assert.equal(docsAfterSync.length, 1);
                assert.isEmpty(docsAfterSync.at(0)?.document.value);
                assert.isEmpty(docsAfterSync.at(0)?.result);

                // In the meantime the document is completed on Sage AI
                sandbox.restore();
                await mockFunctionsToSyncDocuments(context, {
                    documentList,
                    salesInvoiceBinaryStream: await salesInvoiceToByteStream(),
                    fixtureDocument,
                    status: 'completed',
                });

                // The document should be downloaded when the user enters the record on the page(updateDocumentWithDetails function)
                let uploadedPurchasingDocument = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: docsAfterSync.at(0)?.uuid },
                );

                await xtremApAutomation.nodes.UploadedPurchasingDocument.updateDocumentWithDetails(
                    context,
                    uploadedPurchasingDocument,
                );
                uploadedPurchasingDocument = await context.read(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    uuid: docsAfterSync.at(0)?.uuid,
                });

                assert.equal(await uploadedPurchasingDocument.isResultPopulated, true);
                assert.isNotEmpty(await uploadedPurchasingDocument.result);
                assert.isNotEmpty((await uploadedPurchasingDocument.document).value);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));
});

describe('Sync documents Task', () => {
    beforeEach(() => {
        fixtureDocument = fixtureGenerator();
        fixtureDocument.metadata.file_name = 'salesInvoice.pdf';
        fixtureDocument.metadata.request_origin = 'email';
    });
    afterEach(() => {
        sandbox.restore();
    });
    it('Should sync documents successfully', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                const documentList = ['documentId1', 'documentId2', 'documentId3'];
                await mockFunctionsToSyncDocuments(context, {
                    documentList,
                    salesInvoiceBinaryStream: await salesInvoiceToByteStream(),
                    fixtureDocument,
                    status: 'completed',
                });

                const docsBeforeSync = await context.queryCount(xtremApAutomation.nodes.UploadedPurchasingDocument);
                assert.equal(docsBeforeSync, 0);

                await xtremApAutomation.nodes.UploadedPurchasingDocument.syncDocumentsTask(context);
                const docsAfterSync = await context.select(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: true, document: true, status: true, origin: true },
                    { filter: {} },
                );
                assert.equal(docsAfterSync.length, 3);

                docsAfterSync.forEach((uploadedPurchasingDocument, idx) => {
                    assert.equal(uploadedPurchasingDocument.uuid, documentList[idx]);
                    assert.isNotEmpty(uploadedPurchasingDocument.document.value);
                    assert.equal(uploadedPurchasingDocument.status, 'draft');
                    assert.equal(uploadedPurchasingDocument.origin, 'email');
                });
                const docAfterSync = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: docsAfterSync.at(0)?.uuid },
                    { forUpdate: true },
                );
                await assert.isRejected(
                    docAfterSync.$.set({ documentMimeType: 'image/jpg' }),
                    'UploadedPurchasingDocument.documentMimeType: cannot set value on frozen property',
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('Should sync documents but with no document associated due to status being error', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });

                const documentList = ['documentId1', 'documentId2', 'documentId3'];

                await mockFunctionsToSyncDocuments(context, {
                    documentList,
                    salesInvoiceBinaryStream: await salesInvoiceToByteStream(),
                    fixtureDocument,
                    status: 'error',
                });

                const docsBeforeSync = await context.queryCount(xtremApAutomation.nodes.UploadedPurchasingDocument);
                assert.equal(docsBeforeSync, 0);

                await xtremApAutomation.nodes.UploadedPurchasingDocument.syncDocumentsTask(context);
                const docsAfterSync = await context.select(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uuid: true, document: true, status: true },
                    { filter: {} },
                );
                assert.equal(docsAfterSync.length, 3);
                docsAfterSync.forEach(uploadedPurchasingDocument => {
                    assert.isEmpty(uploadedPurchasingDocument.document.value);
                });
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));
    it('No new documents to be imported', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });

                const documentList = ['documentId1', 'documentId2', 'documentId3'];

                await mockFunctionsToSyncDocuments(context, {
                    documentList,
                    salesInvoiceBinaryStream: await salesInvoiceToByteStream(),
                    fixtureDocument,
                    status: 'completed',
                });

                const docsBeforeSync = await context.queryCount(xtremApAutomation.nodes.UploadedPurchasingDocument);
                assert.equal(docsBeforeSync, 0);

                await xtremApAutomation.nodes.UploadedPurchasingDocument.syncDocumentsTask(context);
                const docsAfterSync = await context.queryCount(xtremApAutomation.nodes.UploadedPurchasingDocument);
                assert.equal(docsAfterSync, 3);
                await context.flushDeferredActions();

                // Second import to check new documents
                await xtremApAutomation.nodes.UploadedPurchasingDocument.syncDocumentsTask(context);
                const docsAfterSecondSync = await context.queryCount(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                );
                assert.equal(docsAfterSecondSync, 3);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));

    it('One new documents to be imported after first import', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                // Initial List of documents
                let documentList = ['documentId1', 'documentId2', 'documentId3'];

                await mockFunctionsToSyncDocuments(context, {
                    documentList,
                    salesInvoiceBinaryStream: await salesInvoiceToByteStream(),
                    fixtureDocument,
                    status: 'completed',
                });

                const docsBeforeSync = await context.queryCount(xtremApAutomation.nodes.UploadedPurchasingDocument);
                assert.equal(docsBeforeSync, 0);

                await xtremApAutomation.nodes.UploadedPurchasingDocument.syncDocumentsTask(context);
                const docsAfterSyncFirstList = await context.queryCount(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                );
                assert.equal(docsAfterSyncFirstList, 3);

                // Second list of documents with one new document(documentId4)
                sandbox.restore();
                documentList = ['documentId1', 'documentId2', 'documentId3', 'documentId4'];
                await mockFunctionsToSyncDocuments(context, {
                    documentList,
                    salesInvoiceBinaryStream: await salesInvoiceToByteStream(),
                    fixtureDocument,
                    status: 'completed',
                });

                // Second import to check new documents
                await xtremApAutomation.nodes.UploadedPurchasingDocument.syncDocumentsTask(context);
                const docsAfterSecondSync = await context.queryCount(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                );
                assert.equal(docsAfterSecondSync, 4);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));

    it('No documents to be sync from SageAI', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentList')
                    .returns(Promise.resolve([]));

                const docsBeforeSync = await context.queryCount(xtremApAutomation.nodes.UploadedPurchasingDocument);
                assert.equal(docsBeforeSync, 0);

                await xtremApAutomation.nodes.UploadedPurchasingDocument.syncDocumentsTask(context);

                const docsAfterSync = await context.queryCount(xtremApAutomation.nodes.UploadedPurchasingDocument);
                assert.equal(docsAfterSync, 0);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('No configuration done on SDMO', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentList')
                    .returns(Promise.resolve([]));

                const docsBeforeSync = await context.queryCount(xtremApAutomation.nodes.UploadedPurchasingDocument);
                assert.equal(docsBeforeSync, 0);

                await assert.isRejected(
                    xtremApAutomation.nodes.UploadedPurchasingDocument.syncDocuments(context),
                    'No configuration active',
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));

    it('No configuration done on SDMO - syncDocumentsTask', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentList')
                    .returns(Promise.resolve([]));

                const docsBeforeSync = await context.queryCount(xtremApAutomation.nodes.UploadedPurchasingDocument);
                assert.equal(docsBeforeSync, 0);

                assert.equal(await xtremApAutomation.nodes.UploadedPurchasingDocument.syncDocumentsTask(context), true);

                const docsAfterSync = await context.queryCount(xtremApAutomation.nodes.UploadedPurchasingDocument);
                assert.equal(docsAfterSync, 0);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));
    it('No configuration done on SDMO - no customerid', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createTenantApAutomationConfiguration(context);

                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentList')
                    .returns(Promise.resolve([]));

                const docsBeforeSync = await context.queryCount(xtremApAutomation.nodes.UploadedPurchasingDocument);
                assert.equal(docsBeforeSync, 0);

                await xtremApAutomation.nodes.UploadedPurchasingDocument.syncDocumentsTask(context);

                const docsAfterSync = await context.queryCount(xtremApAutomation.nodes.UploadedPurchasingDocument);
                assert.equal(docsAfterSync, 0);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption], today: '2024-12-05' },
        ));
});

describe('Uploaded purchasing documents validation', () => {
    afterEach(() => {
        sandbox.restore();
    });
    it('Mime type not allowed', () =>
        Test.withContext(
            async context => {
                await createTenantApAutomationConfiguration(context);

                const document = context.create(xtremApAutomation.nodes.UploadedPurchasingDocument, {
                    ...(await xtremApAutomation.functions.processResult(context, fixtureDocument)),
                    uuid: fixtureDocument.metadata.identifier,
                    uploadedFile: await createUploadedFileMimeType(context),
                    customerUniqueId: 'test_customer_id',
                    isTempUuid: false,
                });

                await assert.isRejected((await document).$.save());

                assert.deepEqual(context.diagnoses, [
                    {
                        severity: 3,
                        path: ['uploadedFile'],
                        message: 'You cannot use this file type.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
});
