import type { Context } from '@sage/xtrem-core';
import { Test, date } from '@sage/xtrem-core';
import { InfrastructureHelper } from '@sage/xtrem-infrastructure-adapter';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremScheduler from '@sage/xtrem-scheduler';
import type { InitialNotification } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import { assert } from 'chai';
import * as fs from 'fs';
import { readFile } from 'fs/promises';
import * as fsp from 'path';
import * as sinon from 'sinon';
import * as xtremApAutomation from '../../../index';
import type { ApAutomationConfig } from '../../../lib/interfaces/ap-automation-config';
import { salesInvoicePdfPath, salesInvoiceToByteStream } from '../../fixtures/sales-invoice-to-binary-stream';
import fixtureGenerator from '../functions/process-result-fixture2';

const sandbox = sinon.createSandbox();

let fixtureDocument: xtremApAutomation.interfaces.ApAutomationResult;

enum FileTimeToLive {
    Expire1Day = 'expire1day',
}

const contextId = xtremSystem.functions.generateNanoId();

async function createS3File(context: Context) {
    const filePath = salesInvoicePdfPath();
    // Simulate create file on S3
    await InfrastructureHelper.createFile(
        context,
        `uploads/${contextId}`,
        'Test data',
        await readFile(filePath),
        FileTimeToLive.Expire1Day,
    );

    // Mock the read
    const file = await InfrastructureHelper.readFile(context, `uploads/${contextId}`);
    sandbox
        .stub(InfrastructureHelper, 'readAsyncUploadResult')
        .returns(Promise.resolve({ body: file?.body, tagData: file?.tagData }));
}

async function createUploadedFile(
    context: Context,
    key: string,
    status: xtremUpload.enums.UploadStatus,
): Promise<xtremUpload.nodes.UploadedFile> {
    // Simulate the upload of the file
    let uploadedFile = await context.create(xtremUpload.nodes.UploadedFile, {
        key,
        filename: 'sales_invoice.pdf',
        mimeType: 'application/pdf',
        lastModified: null,
        contentLength: 0,
        kind: 'attachment',
        canSkipAntivirusScan: true,
    });
    await uploadedFile.$.save();
    assert.deepEqual(uploadedFile.$.context.diagnoses, []);

    uploadedFile = await context.read(xtremUpload.nodes.UploadedFile, { key }, { forUpdate: true });
    // set as verified to be able to continue
    await uploadedFile.$.set({ status });
    await uploadedFile.$.save();
    assert.deepEqual(uploadedFile.$.context.diagnoses, []);

    return uploadedFile;
}

/**
 * Clean up files created by tests
 */
function cleanLocalFiles(context: Context): void {
    const dirToDelete = fsp.join(context.application.tmpDir, context.tenantId as string);
    if (!fs.existsSync(dirToDelete)) {
        return;
    }
    fs.rmSync(dirToDelete, { recursive: true });
}

async function mockFunctionsForOnProcessUpload(
    context: Context,
    parameters: {
        customerUniqueId: string;
        emails: { accounts_payable: string; employee_expense: string };
        orchestrationId: string;
        documentStatus: xtremApAutomation.interfaces.ApAutomationReportData;
        fixtureDocument: xtremApAutomation.interfaces.ApAutomationResult;
    },
) {
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
        .returns(Promise.resolve(parameters.customerUniqueId));
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses')
        .returns(Promise.resolve(parameters.emails));
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'uploadDocument')
        .returns(Promise.resolve(parameters.orchestrationId));
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentStatus')
        .returns(Promise.resolve(parameters.documentStatus));
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getDocumentResults')
        .returns(Promise.resolve(parameters.fixtureDocument));
    sandbox
        .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'changeEmailSettings')
        .returns(Promise.resolve(true));

    const apAutomationConfigurationDefaultId =
        await xtremApAutomation.nodes.ApAutomationConfiguration.createOrGetDefaultRecord(context);
    const apAutomationConfiguration = await context.read(
        xtremApAutomation.nodes.ApAutomationConfiguration,
        { _id: apAutomationConfigurationDefaultId },
        { forUpdate: true },
    );
    if (
        (await apAutomationConfiguration.country) === null &&
        (await apAutomationConfiguration.customerUniqueId) === ''
    ) {
        await apAutomationConfiguration.$.set({ country: 'DE' });
    }
    await apAutomationConfiguration.$.set({ configurationLevel: 'tenant' });
    await apAutomationConfiguration.$.save();
    assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);
}

describe('uploadedPurchasingDocument', () => {
    beforeEach(() => {
        fixtureDocument = fixtureGenerator();
    });
    afterEach(() => {
        sandbox.restore();
    });
    after(async () => {
        await Test.withContext(context => cleanLocalFiles(context));
    });

    function notificationCheck(
        notifyUserSpy: sinon.SinonSpy<[notification: InitialNotification]>,
        expectedUserNotification: InitialNotification,
    ) {
        assert.equal(notifyUserSpy.getCalls().length, 1);
        const notifyUserPayload = notifyUserSpy.args.at(0);
        assert.equal(notifyUserPayload?.length, 1);
        const userNotification = notifyUserPayload?.at(0);

        assert.equal(userNotification?.title, expectedUserNotification.title);
        assert.equal(userNotification?.description, expectedUserNotification.description);
        assert.equal(userNotification?.icon, expectedUserNotification.icon);
        assert.equal(userNotification?.level, expectedUserNotification.level);
        assert.equal(userNotification?.shouldDisplayToast, expectedUserNotification.shouldDisplayToast);

        assert.equal(userNotification?.actions.length, 1);
        assert.equal(userNotification?.actions?.at(0)?.link, expectedUserNotification.actions.at(0)?.link);
        assert.equal(userNotification?.actions?.at(0)?.title, expectedUserNotification.actions.at(0)?.title);
        assert.equal(userNotification?.actions?.at(0)?.icon, expectedUserNotification.actions.at(0)?.icon);
        assert.equal(userNotification?.actions?.at(0)?.style, expectedUserNotification.actions.at(0)?.style);
    }

    it('Get package info  ', () =>
        Test.withContext(context => {
            Test.patchConfig({
                packages: {
                    '@sage/xtrem-ap-automation': {
                        clientId: 'sageIdClientId',
                        clientSecret: 'pass1234',
                        oauthEndpointUrl: 'https://sageid.test.com',
                        serviceUrl: 'https://unittests.for.sageAI.com/external',
                    },
                },
            });
            const packageConfig =
                context.configuration.getPackageConfig<ApAutomationConfig>('@sage/xtrem-ap-automation');
            assert.isNotNull(packageConfig);
        }));
    it('Upload document flow - complete', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createS3File(context);
                const orchestrationId = 'mockedOrchestrationId';
                await mockFunctionsForOnProcessUpload(context, {
                    customerUniqueId: 'mockCustomerUniqueId',
                    emails: {
                        accounts_payable: 'mockAccountPayableEmail',
                        employee_expense: 'mockEmployeeResponseEmail',
                    },
                    orchestrationId,
                    documentStatus: {
                        status: 'completed',
                        has_feedback: false,
                        details: '',
                    },
                    fixtureDocument,
                });

                const newUploadedPurchasingDocument = await context.create(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    {
                        uploadedFile: await createUploadedFile(context, 'testUploadFile', 'verified'),
                        isTempUuid: true,
                        customerUniqueId: 'mockCustomerUniqueId',
                    },
                );

                const notifySpy = sinon.spy(context, 'notify');
                await newUploadedPurchasingDocument.$.save();
                assert.deepEqual(newUploadedPurchasingDocument.$.context.diagnoses, []);
                assert.equal(await newUploadedPurchasingDocument.processingStatus, 'processing');
                assert.equal(await newUploadedPurchasingDocument.processingDetails, '');
                assert.equal(notifySpy.getCalls().length, 1);

                // When the record is temporary you can only change uuid, documentMimeType, document and status
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({ supplier: null }),
                    'UploadedPurchasingDocument.supplier: cannot set value on frozen property',
                );
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({ site: null }),
                    'UploadedPurchasingDocument.site: cannot set value on frozen property',
                );
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({ currency: { id: '#GDP' } }),
                    'UploadedPurchasingDocument.currency: cannot set value on frozen property',
                );
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({ type: 'creditMemo' }),
                    'UploadedPurchasingDocument.type: cannot set value on frozen property',
                );
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({ supplierDocumentDate: date.today().addDays(-2) }),
                    'UploadedPurchasingDocument.supplierDocumentDate: cannot set value on frozen property',
                );
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({ supplierDocumentNumber: 'SIPEN240001' }),
                    'UploadedPurchasingDocument.supplierDocumentNumber: cannot set value on frozen property',
                );
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({ lines: [] }),
                    'UploadedPurchasingDocument.lines: cannot set value on frozen property',
                );
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({ dueDate: date.today().addDays(3) }),
                    'UploadedPurchasingDocument.dueDate: cannot set value on frozen property',
                );
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({ totalTaxAmount: 15 }),
                    'UploadedPurchasingDocument.totalTaxAmount: cannot set value on frozen property',
                );
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({ totalAmountExcludingTax: 135 }),
                    'UploadedPurchasingDocument.totalAmountExcludingTax: cannot set value on frozen property',
                );
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({ totalAmountIncludingTax: 150 }),
                    'UploadedPurchasingDocument.totalAmountIncludingTax: cannot set value on frozen property',
                );
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({ origin: null }),
                    'UploadedPurchasingDocument.origin: cannot set value on frozen property',
                );
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({
                        result: { extraction: {}, metadata: { identifier: '', request_origin: '' } },
                    }),
                    'UploadedPurchasingDocument.result: cannot set value on frozen property',
                );
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({ postingDate: date.today().addDays(2) }),
                    'UploadedPurchasingDocument.postingDate: cannot set value on frozen property',
                );
                await assert.isRejected(
                    newUploadedPurchasingDocument.$.set({ reason: 'R3' }),
                    'UploadedPurchasingDocument.reason: cannot set value on frozen property',
                );

                const notifyUserSpy = sinon.spy(context, 'notifyUser');
                await xtremApAutomation.nodes.UploadedPurchasingDocument.onProcessUpload(
                    context,
                    notifySpy.args.at(0) as unknown as { uploadedFileId: number },
                );
                assert.equal(notifyUserSpy.getCalls().length, 1);

                const urlId = btoa(JSON.stringify({ _id: newUploadedPurchasingDocument._id.toString() }));
                const expectedUserNotification: InitialNotification = {
                    title: 'Document upload',
                    description: `Document uploaded and ready for review: sales_invoice.pdf.`,
                    icon: 'tick',
                    level: 'success',
                    shouldDisplayToast: true,
                    actions: [
                        {
                            link: `@sage/xtrem-ap-automation/UploadedPurchasingDocument/${urlId}`,
                            title: 'View',
                            icon: 'link',
                            style: 'tertiary',
                        },
                    ],
                };

                // Notification check
                assert.equal(notifyUserSpy.getCalls().length, 1);
                notificationCheck(notifyUserSpy, expectedUserNotification);

                const uploadedPurchasingDocument = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { _id: newUploadedPurchasingDocument._id },
                    { forUpdate: true },
                );

                // Properties that cannot be changed once the document is no longer temporary
                await assert.isRejected(
                    uploadedPurchasingDocument.$.set({ uuid: 'test-change' }),
                    'UploadedPurchasingDocument.uuid: cannot set value on frozen property',
                );
                await assert.isRejected(
                    uploadedPurchasingDocument.$.set({ documentMimeType: 'image/jpg' }),
                    'UploadedPurchasingDocument.documentMimeType: cannot set value on frozen property',
                );

                // Check uuid and supplier document number for completed
                assert.isFalse(await uploadedPurchasingDocument.isTempUuid);
                assert.equal(await uploadedPurchasingDocument.uuid, orchestrationId);
                assert.equal(await uploadedPurchasingDocument.supplierDocumentNumber, 'SIPEN240001');
                assert.equal(
                    (await uploadedPurchasingDocument.document).compareTo(await salesInvoiceToByteStream()),
                    0,
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));

    it('Upload document flow - processing', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });

                await createS3File(context);
                const orchestrationId = 'mockedOrchestrationId';
                await mockFunctionsForOnProcessUpload(context, {
                    customerUniqueId: 'mockCustomerUniqueId',
                    emails: {
                        accounts_payable: 'mockAccountPayableEmail',
                        employee_expense: 'mockEmployeeResponseEmail',
                    },
                    orchestrationId,
                    documentStatus: {
                        status: 'processing',
                        has_feedback: false,
                        details: '',
                    },
                    fixtureDocument,
                });

                const newUploadedPurchasingDocument = await context.create(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    {
                        uploadedFile: await createUploadedFile(context, 'testUploadFile', 'verified'),
                        customerUniqueId: 'mockCustomerUniqueId',
                        isTempUuid: true,
                    },
                );

                const notifySpy = sinon.spy(context, 'notify');
                await newUploadedPurchasingDocument.$.save();
                assert.deepEqual(newUploadedPurchasingDocument.$.context.diagnoses, []);
                assert.equal(notifySpy.getCalls().length, 1);

                const notifyUserSpy = sinon.spy(context, 'notifyUser');
                await xtremApAutomation.nodes.UploadedPurchasingDocument.onProcessUpload(
                    context,
                    notifySpy.args.at(0) as unknown as { uploadedFileId: number },
                );

                // Notification check
                const urlId = btoa(JSON.stringify({ _id: newUploadedPurchasingDocument._id.toString() }));
                assert.equal(notifyUserSpy.getCalls().length, 1);
                const expectedUserNotification: InitialNotification = {
                    title: 'Document upload',
                    description: `Document uploaded: Sage AI scan in progress: sales_invoice.pdf.`,
                    icon: 'tick',
                    level: 'success',
                    shouldDisplayToast: true,
                    actions: [
                        {
                            link: `@sage/xtrem-ap-automation/UploadedPurchasingDocument/${urlId}`,
                            title: 'View',
                            icon: 'link',
                            style: 'tertiary',
                        },
                    ],
                };
                notificationCheck(notifyUserSpy, expectedUserNotification);

                // Check uuid and status
                const uploadedPurchasingDocument = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { _id: newUploadedPurchasingDocument._id },
                );

                assert.equal(await uploadedPurchasingDocument.uuid, orchestrationId);
                assert.equal(await uploadedPurchasingDocument.processingStatus, 'processing');
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('Upload document flow - error', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });

                await createS3File(context);
                const orchestrationId = 'mockedOrchestrationId';
                await mockFunctionsForOnProcessUpload(context, {
                    customerUniqueId: 'mockCustomerUniqueId',
                    emails: {
                        accounts_payable: 'mockAccountPayableEmail',
                        employee_expense: 'mockEmployeeResponseEmail',
                    },
                    orchestrationId,
                    documentStatus: {
                        status: 'error',
                        has_feedback: false,
                        details: '',
                    },
                    fixtureDocument,
                });

                const newUploadedPurchasingDocument = await context.create(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    {
                        uploadedFile: await createUploadedFile(context, 'testUploadFile', 'verified'),
                        customerUniqueId: 'mockCustomerUniqueId',
                        isTempUuid: true,
                    },
                );

                const notifySpy = sinon.spy(context, 'notify');
                await newUploadedPurchasingDocument.$.save();
                assert.deepEqual(newUploadedPurchasingDocument.$.context.diagnoses, []);
                assert.equal(notifySpy.getCalls().length, 1);

                const notifyUserSpy = sinon.spy(context, 'notifyUser');
                await xtremApAutomation.nodes.UploadedPurchasingDocument.onProcessUpload(
                    context,
                    notifySpy.args.at(0) as unknown as { uploadedFileId: number },
                );
                // Notification check
                const urlId = btoa(JSON.stringify({ _id: newUploadedPurchasingDocument._id.toString() }));
                assert.equal(notifyUserSpy.getCalls().length, 1);
                const expectedUserNotification: InitialNotification = {
                    title: 'Document upload',
                    description: `This document was uploaded successfully, but Sage AI could not process it: sales_invoice.pdf.`,
                    icon: 'cross',
                    level: 'error',
                    shouldDisplayToast: true,
                    actions: [
                        {
                            link: `@sage/xtrem-ap-automation/UploadedPurchasingDocument/${urlId}`,
                            title: 'View',
                            icon: 'link',
                            style: 'tertiary',
                        },
                    ],
                };
                notificationCheck(notifyUserSpy, expectedUserNotification);

                // Check uuid and status
                const uploadedPurchasingDocument = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { _id: newUploadedPurchasingDocument._id },
                );

                assert.equal(await uploadedPurchasingDocument.uuid, orchestrationId);
                assert.equal(await uploadedPurchasingDocument.processingStatus, 'error');
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));

    it('Upload document flow - uploadDocument if uploadFile is not verified', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
                    .returns(Promise.resolve('test_customer_id'));
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses')
                    .returns(
                        Promise.resolve({
                            accounts_payable: '<EMAIL>',
                            employee_expense: '<EMAIL>',
                        }),
                    );
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'changeEmailSettings')
                    .returns(Promise.resolve(true));

                const apAutomationConfigurationDefaultId =
                    await xtremApAutomation.nodes.ApAutomationConfiguration.createOrGetDefaultRecord(context);
                const apAutomationConfiguration = await context.read(
                    xtremApAutomation.nodes.ApAutomationConfiguration,
                    { _id: apAutomationConfigurationDefaultId },
                    { forUpdate: true },
                );
                await apAutomationConfiguration.$.set({ configurationLevel: 'tenant', country: 'DE' });
                await apAutomationConfiguration.$.save();
                assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);

                const newUploadedPurchasingDocument = await context.create(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    {
                        uploadedFile: await createUploadedFile(context, 'testUploadFile', 'rejected'),
                        isTempUuid: true,
                        customerUniqueId: 'test_customer_id',
                    },
                );

                await newUploadedPurchasingDocument.$.save();
                assert.deepEqual(newUploadedPurchasingDocument.$.context.diagnoses, []);

                const notifyUserSpy = sinon.spy(context, 'notifyUser');
                await xtremApAutomation.nodes.UploadedPurchasingDocument.uploadDocument(
                    context,
                    newUploadedPurchasingDocument,
                );

                // Test the notification
                const expectedUserNotification = {
                    title: 'Document upload',
                    description: `Document could not be scanned: sales_invoice.pdf. Review the document and try again.`,
                    icon: 'cross',
                    level: 'error',
                    shouldDisplayToast: true,
                    actions: [],
                };
                assert.equal(notifyUserSpy.getCalls().length, 1);
                const notifyUserPayload = notifyUserSpy.args.at(0);
                assert.equal(notifyUserPayload?.length, 1);
                const userNotification = notifyUserPayload?.at(0);

                assert.equal(userNotification?.title, expectedUserNotification.title);
                assert.equal(userNotification?.description, expectedUserNotification.description);
                assert.equal(userNotification?.icon, expectedUserNotification.icon);
                assert.equal(userNotification?.level, expectedUserNotification.level);
                assert.equal(userNotification?.shouldDisplayToast, expectedUserNotification.shouldDisplayToast);
                assert.equal(userNotification?.actions.length, 0);

                // It should delete uploadedPurchasingDocument that failed the scan
                const checkUploadedPurchasingDocument = await context.queryCount(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { filter: { uuid: newUploadedPurchasingDocument.uuid } },
                );
                assert.equal(checkUploadedPurchasingDocument, 0);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('Upload document flow - uploadDocument no body on s3ReadResult', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
                    .returns(Promise.resolve('test_customer_id'));
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses')
                    .returns(
                        Promise.resolve({
                            accounts_payable: '<EMAIL>',
                            employee_expense: '<EMAIL>',
                        }),
                    );
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'changeEmailSettings')
                    .returns(Promise.resolve(true));

                const apAutomationConfigurationDefaultId =
                    await xtremApAutomation.nodes.ApAutomationConfiguration.createOrGetDefaultRecord(context);
                const apAutomationConfiguration = await context.read(
                    xtremApAutomation.nodes.ApAutomationConfiguration,
                    { _id: apAutomationConfigurationDefaultId },
                    { forUpdate: true },
                );
                await apAutomationConfiguration.$.set({ configurationLevel: 'tenant', country: 'DE' });
                await apAutomationConfiguration.$.save();
                // Mock readAsyncUploadResult to return no body or tags
                sandbox
                    .stub(InfrastructureHelper, 'readAsyncUploadResult')
                    .returns(Promise.resolve({ body: undefined, tagData: undefined }));

                assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);

                const newUploadedPurchasingDocument = await context.create(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    {
                        uploadedFile: await createUploadedFile(context, 'testUploadFile', 'verified'),
                        customerUniqueId: 'test_customer_id',
                        isTempUuid: true,
                    },
                );

                await newUploadedPurchasingDocument.$.save();
                assert.deepEqual(newUploadedPurchasingDocument.$.context.diagnoses, []);

                await assert.isRejected(
                    xtremApAutomation.nodes.UploadedPurchasingDocument.uploadDocument(
                        context,
                        newUploadedPurchasingDocument,
                    ),
                    'No body in s3ReadResult',
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('Upload document flow - No uploaded file found', () =>
        Test.withContext(
            async context => {
                await xtremApAutomation.nodes.UploadedPurchasingDocument.onProcessUpload(context, {
                    uploadedFileId: 0,
                });
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));

    it('Upload document flow - More than one record for the same uploadFile', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
                    .returns(Promise.resolve('test_customer_id'));
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses')
                    .returns(
                        Promise.resolve({
                            accounts_payable: '<EMAIL>',
                            employee_expense: '<EMAIL>',
                        }),
                    );
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'changeEmailSettings')
                    .returns(Promise.resolve(true));

                const apAutomationConfigurationDefaultId =
                    await xtremApAutomation.nodes.ApAutomationConfiguration.createOrGetDefaultRecord(context);
                const apAutomationConfiguration = await context.read(
                    xtremApAutomation.nodes.ApAutomationConfiguration,
                    { _id: apAutomationConfigurationDefaultId },
                    { forUpdate: true },
                );
                await apAutomationConfiguration.$.set({ configurationLevel: 'tenant', country: 'DE' });
                await apAutomationConfiguration.$.save();

                const uploadedFile = await createUploadedFile(context, 'testUploadFile', 'verified');

                const newUploadedPurchasingDocument = await context.create(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uploadedFile, isTempUuid: true, customerUniqueId: 'test_customer_id' },
                );
                await newUploadedPurchasingDocument.$.save();
                assert.deepEqual(newUploadedPurchasingDocument.$.context.diagnoses, []);

                const newUploadedPurchasingDocument2 = await context.create(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { uploadedFile, isTempUuid: true, customerUniqueId: 'test_customer_id' },
                );
                const notifySpy = sinon.spy(context, 'notify');
                await newUploadedPurchasingDocument2.$.save();
                assert.deepEqual(newUploadedPurchasingDocument2.$.context.diagnoses, []);
                assert.equal(notifySpy.getCalls().length, 1);

                await assert.isRejected(
                    xtremApAutomation.nodes.UploadedPurchasingDocument.onProcessUpload(
                        context,
                        notifySpy.args.at(0) as unknown as { uploadedFileId: number },
                    ),
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    describe('createPurchaseDocumentFromUploadedPurchasingDocument', () => {
        it('Confirm document - test if returns correctly the _id and number from manager.confirmDocument', () =>
            Test.withContext(
                async context => {
                    Test.patchConfig({
                        packages: {
                            '@sage/xtrem-ap-automation': {
                                clientId: 'sageIdClientId',
                                clientSecret: 'pass1234',
                                oauthEndpointUrl: 'https://sageid.test.com',
                                serviceUrl: 'https://unittests.for.sageAI.com/external',
                            },
                        },
                    });
                    await createS3File(context);
                    const orchestrationId = 'mockedOrchestrationId';
                    await mockFunctionsForOnProcessUpload(context, {
                        customerUniqueId: 'mockCustomerUniqueId',
                        emails: {
                            accounts_payable: 'mockAccountPayableEmail',
                            employee_expense: 'mockEmployeeResponseEmail',
                        },
                        orchestrationId,
                        documentStatus: {
                            status: 'completed',
                            has_feedback: false,
                            details: '',
                        },
                        fixtureDocument,
                    });

                    const newUploadedPurchasingDocument = await context.create(
                        xtremApAutomation.nodes.UploadedPurchasingDocument,
                        {
                            uploadedFile: await createUploadedFile(context, 'testUploadFile', 'verified'),
                            customerUniqueId: 'mockCustomerUniqueId',
                            isTempUuid: true,
                        },
                    );

                    const notifySpy = sinon.spy(context, 'notify');
                    await newUploadedPurchasingDocument.$.save();
                    assert.deepEqual(newUploadedPurchasingDocument.$.context.diagnoses, []);
                    assert.equal(notifySpy.getCalls().length, 1);

                    await xtremApAutomation.nodes.UploadedPurchasingDocument.onProcessUpload(
                        context,
                        notifySpy.args.at(0) as unknown as { uploadedFileId: number },
                    );

                    const uploadedPurchasingDocument = await context.read(
                        xtremApAutomation.nodes.UploadedPurchasingDocument,
                        { _id: newUploadedPurchasingDocument._id },
                        { forUpdate: true },
                    );
                    await uploadedPurchasingDocument.$.set({
                        supplier: '#US017',
                        site: '#ETS1-S01',
                        currency: '#EUR',
                        supplierDocumentDate: date.today().addDays(-1),
                    });
                    await uploadedPurchasingDocument.$.save();
                    assert.deepEqual(uploadedPurchasingDocument.$.context.diagnoses, []);

                    // Check uuid and supplier document number for completed
                    assert.equal(await uploadedPurchasingDocument.uuid, orchestrationId);
                    assert.equal(await uploadedPurchasingDocument.supplierDocumentNumber, 'SIPEN240001');
                    assert.equal(
                        (await uploadedPurchasingDocument.document).compareTo(await salesInvoiceToByteStream()),
                        0,
                    );
                    assert.equal(await (await uploadedPurchasingDocument.supplier)?.id, 'US017');
                    assert.equal(await (await uploadedPurchasingDocument.site)?.id, 'ETS1-S01');
                    assert.equal(await (await uploadedPurchasingDocument.currency)?.id, 'EUR');

                    // Need to mock this because flushDeferredActions needed to be called
                    const purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                        number: 'PI3',
                    });
                    sandbox
                        .stub(xtremApAutomation.classes.UploadedDocumentManager.prototype, 'confirmDocument')
                        .returns(Promise.resolve(purchaseInvoice));

                    const createdPurchaseDocument =
                        await xtremApAutomation.nodes.UploadedPurchasingDocument.createPurchaseDocumentFromUploadedPurchasingDocument(
                            context,
                            uploadedPurchasingDocument,
                        );
                    assert.equal(createdPurchaseDocument._id, purchaseInvoice._id);
                    assert.equal(createdPurchaseDocument.number, await purchaseInvoice.number);
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));

        it('Bulk confirm ', () =>
            Test.withContext(
                async context => {
                    Test.patchConfig({
                        packages: {
                            '@sage/xtrem-ap-automation': {
                                clientId: 'sageIdClientId',
                                clientSecret: 'pass1234',
                                oauthEndpointUrl: 'https://sageid.test.com',
                                serviceUrl: 'https://unittests.for.sageAI.com/external',
                            },
                        },
                    });
                    await createS3File(context);
                    const orchestrationId = 'mockedOrchestrationId';
                    await mockFunctionsForOnProcessUpload(context, {
                        customerUniqueId: 'mockCustomerUniqueId',
                        emails: {
                            accounts_payable: 'mockAccountPayableEmail',
                            employee_expense: 'mockEmployeeResponseEmail',
                        },
                        orchestrationId,
                        documentStatus: {
                            status: 'completed',
                            has_feedback: false,
                            details: '',
                        },
                        fixtureDocument,
                    });

                    const newUploadedPurchasingDocument = await context.create(
                        xtremApAutomation.nodes.UploadedPurchasingDocument,
                        {
                            uploadedFile: await createUploadedFile(context, 'testUploadFile', 'verified'),
                            customerUniqueId: 'mockCustomerUniqueId',
                            isTempUuid: true,
                        },
                    );

                    const notifySpy = sinon.spy(context, 'notify');
                    await newUploadedPurchasingDocument.$.save();
                    assert.deepEqual(newUploadedPurchasingDocument.$.context.diagnoses, []);
                    assert.equal(notifySpy.getCalls().length, 1);

                    await xtremApAutomation.nodes.UploadedPurchasingDocument.onProcessUpload(
                        context,
                        notifySpy.args.at(0) as unknown as { uploadedFileId: number },
                    );

                    const uploadedPurchasingDocument = await context.read(
                        xtremApAutomation.nodes.UploadedPurchasingDocument,
                        { _id: newUploadedPurchasingDocument._id },
                        { forUpdate: true },
                    );
                    await uploadedPurchasingDocument.$.set({
                        supplier: '#US017',
                        site: '#ETS1-S01',
                        currency: '#EUR',
                        supplierDocumentDate: date.today().addDays(-1),
                    });
                    await uploadedPurchasingDocument.$.save();
                    assert.deepEqual(uploadedPurchasingDocument.$.context.diagnoses, []);

                    // Need to mock this because flushDeferredActions needed to be called
                    const purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, {
                        number: 'PI3',
                    });
                    sandbox
                        .stub(xtremApAutomation.classes.UploadedDocumentManager.prototype, 'confirmDocument')
                        .returns(Promise.resolve(purchaseInvoice));

                    const createdPurchaseDocument =
                        await xtremApAutomation.nodes.UploadedPurchasingDocument.confirmBulk(
                            context,
                            uploadedPurchasingDocument,
                        );

                    assert.equal(createdPurchaseDocument._id, purchaseInvoice._id);
                    assert.equal(createdPurchaseDocument.number, await purchaseInvoice.number);
                },
                { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
            ));

        it('Bulk confirm - notifications error', () =>
            Test.withContext(
                async context => {
                    const documentsError: xtremApAutomation.interfaces.CreatePurchaseDocumentFromUploadedPurchasingDocumentResult[] =
                        [];

                    const notifyUserSpyError = sinon.spy(context, 'notifyUser');
                    await xtremApAutomation.functions.notifyUserBulkConfirm(context, documentsError);

                    assert.equal(notifyUserSpyError.getCalls().length, 1);

                    const expectedUserNotification: InitialNotification = {
                        title: 'Purchase document confirm',
                        description: `The bulk confirm for all documents failed. Review batch task logs for more information.`,
                        icon: 'cross',
                        level: 'error',
                        shouldDisplayToast: true,
                        actions: [
                            {
                                link: `@sage/xtrem-communication/SysNotificationState/`,
                                title: 'Batch task logs',
                                icon: 'link',
                                style: 'tertiary',
                            },
                        ],
                    };

                    // Notification check
                    assert.equal(notifyUserSpyError.getCalls().length, 1);
                    notificationCheck(notifyUserSpyError, expectedUserNotification);
                },
                {
                    testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption],
                },
            ));

        it('Bulk confirm - notifications invoice', () =>
            Test.withContext(
                async context => {
                    const documentsSuccessInvoice: xtremApAutomation.interfaces.CreatePurchaseDocumentFromUploadedPurchasingDocumentResult[] =
                        [{ _id: 123, number: 'PI3', documentType: 'invoice' }];

                    const notifyUserSpySuccessInvoice = sinon.spy(context, 'notifyUser');
                    await xtremApAutomation.functions.notifyUserBulkConfirm(context, documentsSuccessInvoice);

                    assert.equal(notifyUserSpySuccessInvoice.getCalls().length, 1);

                    const expectedUserNotificationSuccessInvoice: InitialNotification = {
                        title: 'Purchase document confirm',
                        description: `Purchasing documents confirmed.`,
                        icon: 'tick',
                        level: 'success',
                        shouldDisplayToast: true,
                        actions: [
                            {
                                link: xtremSystem.functions.linkToFilteredMainList<xtremPurchasing.nodes.PurchaseCreditMemo>(
                                    '@sage/xtrem-purchasing/PurchaseInvoice',
                                    {
                                        number: { _in: [documentsSuccessInvoice[0].number] },
                                    },
                                ),
                                title: 'Invoice',
                                icon: 'link',
                                style: 'tertiary',
                            },
                            {
                                link: `@sage/xtrem-communication/SysNotificationState/`,
                                title: 'Batch task logs',
                                icon: 'link',
                                style: 'tertiary',
                            },
                        ],
                    };

                    const notifyUserPayload = notifyUserSpySuccessInvoice.args.at(0);
                    assert.equal(notifyUserPayload?.length, 1);
                    const userNotification = notifyUserPayload?.at(0);

                    assert.equal(userNotification?.title, expectedUserNotificationSuccessInvoice.title);
                    assert.equal(userNotification?.description, expectedUserNotificationSuccessInvoice.description);
                    assert.equal(userNotification?.icon, expectedUserNotificationSuccessInvoice.icon);
                    assert.equal(userNotification?.level, expectedUserNotificationSuccessInvoice.level);
                    assert.equal(
                        userNotification?.shouldDisplayToast,
                        expectedUserNotificationSuccessInvoice.shouldDisplayToast,
                    );

                    assert.equal(userNotification?.actions.length, 2);
                    assert.equal(
                        userNotification?.actions?.at(0)?.link,
                        expectedUserNotificationSuccessInvoice.actions.at(0)?.link,
                    );
                    assert.equal(
                        userNotification?.actions?.at(0)?.title,
                        expectedUserNotificationSuccessInvoice.actions.at(0)?.title,
                    );
                    assert.equal(
                        userNotification?.actions?.at(0)?.icon,
                        expectedUserNotificationSuccessInvoice.actions.at(0)?.icon,
                    );
                    assert.equal(
                        userNotification?.actions?.at(0)?.style,
                        expectedUserNotificationSuccessInvoice.actions.at(0)?.style,
                    );
                },
                {
                    testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption],
                },
            ));

        it('Bulk confirm - notifications credit memo', () =>
            Test.withContext(
                async context => {
                    const documentsSuccessCreditMemo: xtremApAutomation.interfaces.CreatePurchaseDocumentFromUploadedPurchasingDocumentResult[] =
                        [{ _id: 123, number: 'PCM1', documentType: 'creditMemo' }];

                    const notifyUserSpySuccessInvoice = sinon.spy(context, 'notifyUser');
                    await xtremApAutomation.functions.notifyUserBulkConfirm(context, documentsSuccessCreditMemo);

                    assert.equal(notifyUserSpySuccessInvoice.getCalls().length, 1);

                    const expectedUserNotificationSuccessCreditMemo: InitialNotification = {
                        title: 'Purchase document confirm',
                        description: `Purchasing documents confirmed.`,
                        icon: 'tick',
                        level: 'success',
                        shouldDisplayToast: true,
                        actions: [
                            {
                                link: xtremSystem.functions.linkToFilteredMainList<xtremPurchasing.nodes.PurchaseCreditMemo>(
                                    '@sage/xtrem-purchasing/PurchaseCreditMemo',
                                    {
                                        number: { _in: [documentsSuccessCreditMemo[0].number] },
                                    },
                                ),
                                title: 'Credit Memo',
                                icon: 'link',
                                style: 'tertiary',
                            },
                            {
                                link: `@sage/xtrem-communication/SysNotificationState/`,
                                title: 'Batch task logs',
                                icon: 'link',
                                style: 'tertiary',
                            },
                        ],
                    };

                    const notifyUserPayload = notifyUserSpySuccessInvoice.args.at(0);
                    assert.equal(notifyUserPayload?.length, 1);
                    const userNotification = notifyUserPayload?.at(0);

                    assert.equal(userNotification?.title, expectedUserNotificationSuccessCreditMemo.title);
                    assert.equal(userNotification?.description, expectedUserNotificationSuccessCreditMemo.description);
                    assert.equal(userNotification?.icon, expectedUserNotificationSuccessCreditMemo.icon);
                    assert.equal(userNotification?.level, expectedUserNotificationSuccessCreditMemo.level);
                    assert.equal(
                        userNotification?.shouldDisplayToast,
                        expectedUserNotificationSuccessCreditMemo.shouldDisplayToast,
                    );

                    assert.equal(userNotification?.actions.length, 2);
                    assert.equal(
                        userNotification?.actions?.at(0)?.link,
                        expectedUserNotificationSuccessCreditMemo.actions.at(0)?.link,
                    );
                    assert.equal(
                        userNotification?.actions?.at(0)?.title,
                        expectedUserNotificationSuccessCreditMemo.actions.at(0)?.title,
                    );
                    assert.equal(
                        userNotification?.actions?.at(0)?.icon,
                        expectedUserNotificationSuccessCreditMemo.actions.at(0)?.icon,
                    );
                    assert.equal(
                        userNotification?.actions?.at(0)?.style,
                        expectedUserNotificationSuccessCreditMemo.actions.at(0)?.style,
                    );
                },
                {
                    testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption],
                },
            ));
    });
});
describe('Service option', () => {
    it('Activate/Deactivate Service option and Batch task', () =>
        Test.withContext(async context => {
            let jobSchedule = await context.read(xtremScheduler.nodes.SysJobSchedule, { id: 'syncDocumentsTask_1' });

            assert.isFalse(
                await context.serviceOptionManager.isServiceOptionEnabled(
                    context,
                    xtremApAutomation.serviceOptions.apAutomationOption,
                ),
            );
            assert.isFalse(await jobSchedule.isActive);

            const notifySpy = sinon.spy(context, 'notify');
            await context.serviceOptionManager.setServiceOptionActive(
                context,
                xtremApAutomation.serviceOptions.apAutomationOption,
                true,
            );
            assert.equal(notifySpy.getCalls().length, 1);
            assert.isTrue(
                await context.serviceOptionManager.isServiceOptionEnabled(
                    context,
                    xtremApAutomation.serviceOptions.apAutomationOption,
                ),
            );

            await xtremApAutomation.nodes.UploadedPurchasingDocument.apAutomationOptionActivate(context);

            jobSchedule = await context.read(xtremScheduler.nodes.SysJobSchedule, { id: 'syncDocumentsTask_1' });
            assert.isTrue(await jobSchedule.isActive);

            await context.serviceOptionManager.setServiceOptionActive(
                context,
                xtremApAutomation.serviceOptions.apAutomationOption,
                false,
            );
            assert.equal(notifySpy.getCalls().length, 3);
            assert.isFalse(
                await context.serviceOptionManager.isServiceOptionEnabled(
                    context,
                    xtremApAutomation.serviceOptions.apAutomationOption,
                ),
            );
            await xtremApAutomation.nodes.UploadedPurchasingDocument.apAutomationOptionDeactivate(context);
            jobSchedule = await context.read(xtremScheduler.nodes.SysJobSchedule, { id: 'syncDocumentsTask_1' });
            assert.isFalse(await jobSchedule.isActive);
        }));
});
describe('Uploaded Purchasing Document Deletion', () => {
    beforeEach(() => {
        fixtureDocument = fixtureGenerator();
    });
    afterEach(() => {
        sandbox.restore();
    });
    after(async () => {
        await Test.withContext(context => cleanLocalFiles(context));
    });
    it('Get package info  ', () =>
        Test.withContext(context => {
            Test.patchConfig({
                packages: {
                    '@sage/xtrem-ap-automation': {
                        clientId: 'sageIdClientId',
                        clientSecret: 'pass1234',
                        oauthEndpointUrl: 'https://sageid.test.com',
                        serviceUrl: 'https://unittests.for.sageAI.com/external',
                    },
                },
            });
            const packageConfig =
                context.configuration.getPackageConfig<ApAutomationConfig>('@sage/xtrem-ap-automation');
            assert.isNotNull(packageConfig);
        }));
    it('Cannot delete a document isReviewDone', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createS3File(context);
                const orchestrationId = 'mockedOrchestrationId';
                const customerUniqueId = 'mockCustomerUniqueId';
                await mockFunctionsForOnProcessUpload(context, {
                    customerUniqueId,
                    emails: {
                        accounts_payable: 'mockAccountPayableEmail',
                        employee_expense: 'mockEmployeeResponseEmail',
                    },
                    orchestrationId,
                    documentStatus: {
                        status: 'completed',
                        has_feedback: false,
                        details: '',
                    },
                    fixtureDocument,
                });

                const newUploadedPurchasingDocument = await context.create(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    {
                        uploadedFile: await createUploadedFile(context, 'testUploadFile', 'verified'),
                        isTempUuid: true,
                        customerUniqueId,
                    },
                );

                const notifySpy = sinon.spy(context, 'notify');
                await newUploadedPurchasingDocument.$.save();
                await xtremApAutomation.nodes.UploadedPurchasingDocument.onProcessUpload(
                    context,
                    notifySpy.args.at(0) as unknown as { uploadedFileId: number },
                );

                let uploadedPurchasingDocument = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { _id: newUploadedPurchasingDocument._id },
                    { forUpdate: true },
                );
                await uploadedPurchasingDocument.$.set({ supplier: '#US017', site: '#ETS1-S01' });
                await uploadedPurchasingDocument.$.save();
                assert.deepEqual(uploadedPurchasingDocument.$.context.diagnoses, []);

                const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
                await manager.confirmDocument(uploadedPurchasingDocument);

                uploadedPurchasingDocument = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { _id: newUploadedPurchasingDocument._id },
                    { forUpdate: true },
                );
                assert.isTrue(await uploadedPurchasingDocument.isReviewDone);

                await assert.isRejected(uploadedPurchasingDocument.$.delete(), 'The record was not deleted.');
                assert.deepEqual(context.diagnoses, [
                    {
                        message:
                            'You cannot delete an uploaded purchasing document while it is being processed or if the review is done.',
                        severity: 3,
                        path: [],
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));

    it('Cannot delete a document with processingStatus = processing', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });

                await createS3File(context);
                const orchestrationId = 'mockedOrchestrationId';
                const customerUniqueId = 'mockCustomerUniqueId';
                await mockFunctionsForOnProcessUpload(context, {
                    customerUniqueId,
                    emails: {
                        accounts_payable: 'mockAccountPayableEmail',
                        employee_expense: 'mockEmployeeResponseEmail',
                    },
                    orchestrationId,
                    documentStatus: {
                        status: 'processing',
                        has_feedback: false,
                        details: '',
                    },
                    fixtureDocument,
                });

                const newUploadedPurchasingDocument = await context.create(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    {
                        uploadedFile: await createUploadedFile(context, 'testUploadFile', 'verified'),
                        isTempUuid: true,
                        customerUniqueId,
                    },
                );

                const notifySpy = sinon.spy(context, 'notify');
                await newUploadedPurchasingDocument.$.save();
                assert.deepEqual(newUploadedPurchasingDocument.$.context.diagnoses, []);
                assert.equal(notifySpy.getCalls().length, 1);

                await xtremApAutomation.nodes.UploadedPurchasingDocument.onProcessUpload(
                    context,
                    notifySpy.args.at(0) as unknown as { uploadedFileId: number },
                );

                // Check uuid and status
                const uploadedPurchasingDocument = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { _id: newUploadedPurchasingDocument._id },
                    { forUpdate: true },
                );
                assert.equal(await uploadedPurchasingDocument.uuid, orchestrationId);
                assert.equal(await uploadedPurchasingDocument.processingStatus, 'processing');

                await assert.isRejected(uploadedPurchasingDocument.$.delete(), 'The record was not deleted.');
                assert.deepEqual(context.diagnoses, [
                    {
                        message:
                            'You cannot delete an uploaded purchasing document while it is being processed or if the review is done.',
                        severity: 3,
                        path: [],
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('Delete a document isReviewDone = false', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createS3File(context);
                const orchestrationId = 'mockedOrchestrationId';
                const customerUniqueId = 'mockCustomerUniqueId';
                await mockFunctionsForOnProcessUpload(context, {
                    customerUniqueId,
                    emails: {
                        accounts_payable: 'mockAccountPayableEmail',
                        employee_expense: 'mockEmployeeResponseEmail',
                    },
                    orchestrationId,
                    documentStatus: {
                        status: 'completed',
                        has_feedback: false,
                        details: '',
                    },
                    fixtureDocument,
                });
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'deleteExtraction')
                    .returns(Promise.resolve());

                const newUploadedPurchasingDocument = await context.create(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    {
                        uploadedFile: await createUploadedFile(context, 'testUploadFile', 'verified'),
                        isTempUuid: true,
                        customerUniqueId,
                    },
                );

                const notifySpy = sinon.spy(context, 'notify');
                await newUploadedPurchasingDocument.$.save();
                await xtremApAutomation.nodes.UploadedPurchasingDocument.onProcessUpload(
                    context,
                    notifySpy.args.at(0) as unknown as { uploadedFileId: number },
                );

                const uploadedPurchasingDocument = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { _id: newUploadedPurchasingDocument._id },
                    { forUpdate: true },
                );
                await uploadedPurchasingDocument.$.delete();
                assert.deepEqual(uploadedPurchasingDocument.$.context.diagnoses, []);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));

    it('Cannot delete a document with an inactive customer id', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                await createS3File(context);
                const orchestrationId = 'mockedOrchestrationId';
                const customerUniqueId = 'mockCustomerUniqueId';
                await mockFunctionsForOnProcessUpload(context, {
                    customerUniqueId,
                    emails: {
                        accounts_payable: 'mockAccountPayableEmail',
                        employee_expense: 'mockEmployeeResponseEmail',
                    },
                    orchestrationId,
                    documentStatus: {
                        status: 'completed',
                        has_feedback: false,
                        details: '',
                    },
                    fixtureDocument,
                });
                sandbox
                    .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'deleteExtraction')
                    .returns(Promise.resolve());

                const newUploadedPurchasingDocument = await context.create(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    {
                        uploadedFile: await createUploadedFile(context, 'testUploadFile', 'verified'),
                        isTempUuid: true,
                        customerUniqueId,
                    },
                );

                const notifySpy = sinon.spy(context, 'notify');
                await newUploadedPurchasingDocument.$.save();
                await xtremApAutomation.nodes.UploadedPurchasingDocument.onProcessUpload(
                    context,
                    notifySpy.args.at(0) as unknown as { uploadedFileId: number },
                );

                // Change to company configuration
                const apAutomationConfiguration = await context.read(
                    xtremApAutomation.nodes.ApAutomationConfiguration,
                    { id: 'default' },
                    { forUpdate: true },
                );
                await apAutomationConfiguration.$.set({ configurationLevel: 'company' });
                await apAutomationConfiguration.$.save();

                const uploadedPurchasingDocument = await context.read(
                    xtremApAutomation.nodes.UploadedPurchasingDocument,
                    { _id: newUploadedPurchasingDocument._id },
                    { forUpdate: true },
                );
                await assert.isRejected(uploadedPurchasingDocument.$.delete(), 'The record was not deleted.');
                assert.deepEqual(context.diagnoses, [
                    { message: 'Your customer ID is missing or inactive.', severity: 3, path: [] },
                ]);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
});
