import { Context, Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremApAutomation from '../../../index';
import type { ApAutomationConfig } from '../../../lib/interfaces';

const sandbox = sinon.createSandbox();

describe('Common functions', () => {
    function mockCreateCompany(parameters: {
        customerUniqueId: string;
        accountsPayableEmail: string;
        employeeExpenseEmail: string;
    }) {
        sandbox
            .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'createCompany')
            .returns(Promise.resolve(parameters.customerUniqueId));
        sandbox.stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'getEmailAddresses').returns(
            Promise.resolve({
                accounts_payable: parameters.accountsPayableEmail,
                employee_expense: parameters.employeeExpenseEmail,
            }),
        );
        sandbox
            .stub(xtremApAutomation.classes.ApAutomationServiceClient.prototype, 'changeEmailSettings')
            .returns(Promise.resolve(true));
    }

    afterEach(() => {
        sandbox.restore();
    });
    it('Get package info  ', () =>
        Test.withContext(context => {
            Test.patchConfig({
                packages: {
                    '@sage/xtrem-ap-automation': {
                        clientId: 'sageIdClientId',
                        clientSecret: 'pass1234',
                        oauthEndpointUrl: 'https://sageid.test.com',
                        serviceUrl: 'https://unittests.for.sageAI.com/external',
                    },
                },
            });
            const packageConfig =
                context.configuration.getPackageConfig<ApAutomationConfig>('@sage/xtrem-ap-automation');
            assert.isNotNull(packageConfig);
        }));
    it('Set tenant configuration active', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                mockCreateCompany({
                    customerUniqueId: 'test_tenant_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });
                const tenantConfigurationId =
                    await xtremApAutomation.nodes.ApAutomationConfiguration.createOrGetDefaultRecord(context);
                let apAutomationConfiguration = await context.read(
                    xtremApAutomation.nodes.ApAutomationConfiguration,
                    { _id: tenantConfigurationId },
                    { forUpdate: true },
                );
                await apAutomationConfiguration.$.set({ configurationLevel: 'tenant', country: 'FR' });
                await apAutomationConfiguration.$.save();
                assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);

                apAutomationConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationConfiguration, {
                    _id: tenantConfigurationId,
                });

                assert.equal(await apAutomationConfiguration?.customerUniqueId, 'test_tenant_customer_id');
                assert.equal(await apAutomationConfiguration?.accountsPayableEmail, '<EMAIL>');
                assert.equal(await apAutomationConfiguration?.employeeExpenseEmail, '<EMAIL>');
                assert.equal(await apAutomationConfiguration.isActive, true);

                // Check if its not possible to register a company while tenant configuration is active
                const company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });
                await assert.isRejected(
                    xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, company),
                    'You cannot register a company with Sage AI while the tenant configuration is active.',
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('Set company configuration active and register a company', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                mockCreateCompany({
                    customerUniqueId: 'test_company_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });
                const company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });

                await xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, company);
                assert.deepEqual(company.$.context.diagnoses, []);

                const companyConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationCompany, {
                    company,
                });
                assert.equal(await companyConfiguration.customerUniqueId, 'test_company_customer_id');
                assert.equal(await companyConfiguration.accountsPayableEmail, '<EMAIL>');
                assert.equal(await companyConfiguration.employeeExpenseEmail, '<EMAIL>');
                assert.isTrue(await companyConfiguration.isInboxActive);

                let apAutomationConfiguration = await context.read(
                    xtremApAutomation.nodes.ApAutomationConfiguration,
                    { id: 'default' },
                    { forUpdate: true },
                );
                await apAutomationConfiguration.$.set({ configurationLevel: 'company' });
                await apAutomationConfiguration.$.save();
                assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);

                apAutomationConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationConfiguration, {
                    id: 'default',
                });

                assert.equal(await apAutomationConfiguration?.customerUniqueId, '');
                assert.equal(await apAutomationConfiguration?.accountsPayableEmail, '');
                assert.equal(await apAutomationConfiguration?.employeeExpenseEmail, '');
                assert.isFalse(await apAutomationConfiguration.isActive);
                assert.isFalse(await apAutomationConfiguration.isInboxActive);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('Set tenant configuration active - Tenant duplication', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                // Register a company to check if the inbox is deactivated
                mockCreateCompany({
                    customerUniqueId: 'test_company_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });
                let company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });

                await xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, company);
                assert.deepEqual(company.$.context.diagnoses, []);

                // Register the tenant
                sandbox.restore();
                mockCreateCompany({
                    customerUniqueId: 'test_tenant_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });
                let apAutomationConfiguration = await context.read(
                    xtremApAutomation.nodes.ApAutomationConfiguration,
                    { id: 'default' },
                    { forUpdate: true },
                );
                await apAutomationConfiguration.$.set({ configurationLevel: 'tenant', country: 'FR' });
                await apAutomationConfiguration.$.save();
                assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);

                apAutomationConfiguration = await context.read(
                    xtremApAutomation.nodes.ApAutomationConfiguration,
                    { id: 'default' },
                    { forUpdate: true },
                );
                assert.equal(await apAutomationConfiguration?.customerUniqueId, 'test_tenant_customer_id');
                assert.equal(await apAutomationConfiguration?.accountsPayableEmail, '<EMAIL>');
                assert.equal(await apAutomationConfiguration?.employeeExpenseEmail, '<EMAIL>');
                assert.equal(await apAutomationConfiguration.isActive, true);

                //  Simulate tenant duplication
                sandbox.restore();

                const [tenantInfo] = await Context.tenantManager.getTenantsInfo(context, context.tenantId ?? '');
                tenantInfo.id = 'TenantDuplication';
                sandbox.stub(Context.tenantManager, 'getTenantsInfo').returns(Promise.resolve([tenantInfo]));

                mockCreateCompany({
                    customerUniqueId: 'test_tenant_duplication_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });

                // Register the tenant again
                await apAutomationConfiguration.$.save();
                assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);
                assert.equal(await apAutomationConfiguration?.customerUniqueId, 'test_tenant_duplication_customer_id');
                assert.equal(
                    await apAutomationConfiguration?.accountsPayableEmail,
                    '<EMAIL>',
                );
                assert.equal(
                    await apAutomationConfiguration?.employeeExpenseEmail,
                    '<EMAIL>',
                );
                assert.equal(await apAutomationConfiguration.isActive, true);

                // Check if inboxes were deactivated
                await Test.rollbackCache(context);
                company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });
                const companyConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationCompany, {
                    company,
                });
                assert.isFalse(await companyConfiguration.isInboxActive);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('resetConfiguration mutation', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                // Register a company
                mockCreateCompany({
                    customerUniqueId: 'test_company_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });
                let company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });
                await xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, company);
                assert.deepEqual(company.$.context.diagnoses, []);

                let companyConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationCompany, {
                    company,
                });
                assert.equal(await companyConfiguration.customerUniqueId, 'test_company_customer_id');
                assert.isTrue(await companyConfiguration.isInboxActive);

                // Register the tenant
                sandbox.restore();
                mockCreateCompany({
                    customerUniqueId: 'test_tenant_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });
                const tenantConfigurationId =
                    await xtremApAutomation.nodes.ApAutomationConfiguration.createOrGetDefaultRecord(context);
                let tenantConfiguration = await context.read(
                    xtremApAutomation.nodes.ApAutomationConfiguration,
                    { _id: tenantConfigurationId },
                    { forUpdate: true },
                );
                await tenantConfiguration.$.set({ configurationLevel: 'tenant', country: 'FR' });
                await tenantConfiguration.$.save();
                assert.deepEqual(tenantConfiguration.$.context.diagnoses, []);

                // Reset the configuration
                await xtremApAutomation.nodes.ApAutomationConfiguration.resetConfiguration(context);

                // // Reset cache for bulkaUpdate
                await Test.rollbackCache(context);

                tenantConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationConfiguration, {
                    _id: tenantConfigurationId,
                });
                assert.equal(await tenantConfiguration?.customerUniqueId, '');
                assert.equal(await tenantConfiguration?.accountsPayableEmail, '');
                assert.equal(await tenantConfiguration?.employeeExpenseEmail, '');
                assert.isFalse(await tenantConfiguration.isActive);
                assert.isFalse(await tenantConfiguration.isInboxActive);

                company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });
                companyConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationCompany, {
                    company,
                });
                assert.equal(await companyConfiguration?.customerUniqueId, '');
                assert.equal(await companyConfiguration?.accountsPayableEmail, '');
                assert.equal(await companyConfiguration?.employeeExpenseEmail, '');
                assert.isFalse(await companyConfiguration.isInboxActive);
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('resetConfiguration mutation - no configuration set', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                // Reset the configuration
                assert.isFalse(await xtremApAutomation.nodes.ApAutomationConfiguration.resetConfiguration(context));
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('isConfigurationActive when tenant is duplicated', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                // Register a company
                mockCreateCompany({
                    customerUniqueId: 'test_company_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });
                const company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });
                await xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, company);
                assert.deepEqual(company.$.context.diagnoses, []);

                const companyConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationCompany, {
                    company,
                });

                assert.equal(await companyConfiguration.customerUniqueId, 'test_company_customer_id');
                assert.isTrue(await companyConfiguration.isInboxActive);

                //  Simulate tenant duplication
                sandbox.restore();

                const [tenantInfo] = await Context.tenantManager.getTenantsInfo(context, context.tenantId ?? '');
                tenantInfo.id = 'TenantDuplication';
                sandbox.stub(Context.tenantManager, 'getTenantsInfo').returns(Promise.resolve([tenantInfo]));

                assert.isFalse(await xtremApAutomation.nodes.ApAutomationConfiguration.isConfigurationActive(context));
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('isConfigurationActive query when there is a company registered', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                // Register a company
                mockCreateCompany({
                    customerUniqueId: 'test_company_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });
                const company = await context.read(xtremSystem.nodes.Company, { id: 'UT02' });
                await xtremApAutomation.nodes.ApAutomationCompany.registerCompanyOnSageAi(context, company);
                assert.deepEqual(company.$.context.diagnoses, []);

                const companyConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationCompany, {
                    company,
                });
                assert.equal(await companyConfiguration.customerUniqueId, 'test_company_customer_id');
                assert.isTrue(await companyConfiguration.isInboxActive);

                assert.isTrue(await xtremApAutomation.nodes.ApAutomationConfiguration.isConfigurationActive(context));
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
    it('isConfigurationActive query when there is nothing registered', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                assert.isFalse(await xtremApAutomation.nodes.ApAutomationConfiguration.isConfigurationActive(context));
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));

    it('Disable email address', () =>
        Test.withContext(
            async context => {
                Test.patchConfig({
                    packages: {
                        '@sage/xtrem-ap-automation': {
                            clientId: 'sageIdClientId',
                            clientSecret: 'pass1234',
                            oauthEndpointUrl: 'https://sageid.test.com',
                            serviceUrl: 'https://unittests.for.sageAI.com/external',
                        },
                    },
                });
                mockCreateCompany({
                    customerUniqueId: 'test_tenant_customer_id',
                    accountsPayableEmail: '<EMAIL>',
                    employeeExpenseEmail: '<EMAIL>',
                });
                const tenantConfigurationId =
                    await xtremApAutomation.nodes.ApAutomationConfiguration.createOrGetDefaultRecord(context);
                let apAutomationConfiguration = await context.read(
                    xtremApAutomation.nodes.ApAutomationConfiguration,
                    { _id: tenantConfigurationId },
                    { forUpdate: true },
                );
                await apAutomationConfiguration.$.set({ configurationLevel: 'tenant', country: 'FR' });
                await apAutomationConfiguration.$.save();
                assert.deepEqual(apAutomationConfiguration.$.context.diagnoses, []);

                apAutomationConfiguration = await context.read(xtremApAutomation.nodes.ApAutomationConfiguration, {
                    _id: tenantConfigurationId,
                });

                const manager = new xtremApAutomation.classes.UploadedDocumentManager(context);
                assert.isTrue(
                    await manager.changeEmailSettings(await apAutomationConfiguration.customerUniqueId, false),
                );
            },
            { testActiveServiceOptions: [xtremApAutomation.serviceOptions.apAutomationOption] },
        ));
});
