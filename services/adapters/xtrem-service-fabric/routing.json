{"@sage/xtrem-service-fabric": [{"topic": "OrganisationServiceFabric/asyncExport/start", "queue": "import-export", "sourceFileName": "organisation-service-fabric.ts"}, {"topic": "serviceFabric/createTax", "queue": "sage-network", "sourceFileName": "service-fabric-listener.ts"}, {"topic": "ServiceFabricListener/asyncExport/start", "queue": "import-export", "sourceFileName": "service-fabric-listener.ts"}, {"topic": "ServiceFabricOptionManagement/asyncExport/start", "queue": "import-export", "sourceFileName": "service-fabric-option-management.ts"}, {"topic": "ServiceFabricTaxIdManagement/asyncExport/start", "queue": "import-export", "sourceFileName": "service-fabric-tax-id-management.ts"}, {"topic": "TaxRateRepository/asyncExport/start", "queue": "import-export", "sourceFileName": "tax-rate-repository.ts"}]}