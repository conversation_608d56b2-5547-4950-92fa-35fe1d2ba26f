declare module '@sage/xtrem-service-fabric-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type {
        BankAccount,
        CompanyAttributeType,
        CompanyAttributeTypeBinding,
        CompanyAttributeTypeInput,
        CompanyDefaultAttribute,
        CompanyDefaultAttributeBinding,
        CompanyDefaultAttributeInput,
        CompanyDefaultDimension,
        CompanyDefaultDimensionBinding,
        CompanyDefaultDimensionInput,
        CompanyDimensionType,
        CompanyDimensionTypeBinding,
        CompanyDimensionTypeInput,
        Package as SageXtremFinanceData$Package,
        PostingClass,
    } from '@sage/xtrem-finance-data-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type { Package as SageXtremLandedCost$Package } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        CompanyAddress,
        CompanyAddressBinding,
        CompanyAddressInput,
        CompanyContact,
        CompanyContactBinding,
        CompanyContactInput,
        Currency,
        Package as SageXtremMasterData$Package,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type { Package as SageXtremStockData$Package } from '@sage/xtrem-stock-data-api';
    import type {
        ChartOfAccount,
        Country,
        Legislation,
        Package as SageXtremStructure$Package,
    } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type { Company, Package as SageXtremSystem$Package, Site, SysVendor, User } from '@sage/xtrem-system-api';
    import type {
        Package as SageXtremTax$Package,
        Tax,
        TaxCategory,
        TaxValue,
        TaxValueBinding,
        TaxValueInput,
    } from '@sage/xtrem-tax-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface OrganisationServiceFabric extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        connected: string;
        name: string;
        xApplication: string;
        signingKey: string;
        authURL: string;
        apiURL: string;
        lookupURL: string;
        organisationId: string;
        sageCrmId: string;
        primaryCountry: string;
        adminEmail: string;
        defaultLanguage: string;
        externalId: string;
        primarySigningKey: string;
        token: string;
        tokenCreation: string;
        isTokenValid: boolean;
        organisation: string;
    }
    export interface OrganisationServiceFabricInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        isActive?: boolean | string;
        organisationId?: string;
        sageCrmId?: string;
        primaryCountry?: string;
        adminEmail?: string;
        defaultLanguage?: string;
        externalId?: string;
        primarySigningKey?: string;
        token?: string;
        tokenCreation?: string;
    }
    export interface OrganisationServiceFabricBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        connected: string;
        name: string;
        xApplication: string;
        signingKey: string;
        authURL: string;
        apiURL: string;
        lookupURL: string;
        organisationId: string;
        sageCrmId: string;
        primaryCountry: string;
        adminEmail: string;
        defaultLanguage: string;
        externalId: string;
        primarySigningKey: string;
        token: string;
        tokenCreation: string;
        isTokenValid: boolean;
        organisation: any;
    }
    export interface OrganisationServiceFabric$Queries {
        defaultInstance: Node$Operation<{}, OrganisationServiceFabric>;
        getOrganisation: Node$Operation<{}, string>;
        getCompanyList: Node$Operation<
            {},
            {
                name: string;
                externalId: string;
                taxNumber: string;
                standardIndustrialCode: string;
                contactTelNo: string;
                contactEmail: string;
                address: {
                    addressLine1: string;
                    addressLine2: string;
                    addressLine3: string;
                    addressLine4: string;
                    countrySubdivision: string;
                    postalCode: string;
                    country: string;
                }[];
            }[]
        >;
        getLinkedCompanyList: Node$Operation<
            {},
            {
                id: string;
                name: string;
                externalId: string;
                companyId: string;
                isCreated: boolean;
            }[]
        >;
        availableCountry: Node$Operation<{}, string[]>;
    }
    export interface OrganisationServiceFabric$Mutations {
        createOrganisation: Node$Operation<{}, boolean>;
        resetOrganisation: Node$Operation<{}, boolean>;
        saveToken: Node$Operation<{}, string>;
        createServiceFabricCompany: Node$Operation<
            {
                id?: string;
            },
            boolean
        >;
        companyIdValidation: Node$Operation<
            {
                country?: string;
                taxID?: string;
            },
            {
                countryCode: string;
                vatNumber: string;
                valid: boolean;
                status: string;
                statusCode: string;
                traderCity: string;
                traderPostCode: string;
                traderName: string;
                traderStreet: string;
                traderAddress: string;
                requestDate: string;
            }
        >;
        isAvailableCountry: Node$Operation<
            {
                country?: string;
            },
            boolean
        >;
    }
    export interface OrganisationServiceFabric$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface OrganisationServiceFabric$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface OrganisationServiceFabric$Operations {
        query: QueryOperation<OrganisationServiceFabric>;
        read: ReadOperation<OrganisationServiceFabric>;
        aggregate: {
            read: AggregateReadOperation<OrganisationServiceFabric>;
            query: AggregateQueryOperation<OrganisationServiceFabric>;
        };
        queries: OrganisationServiceFabric$Queries;
        create: CreateOperation<OrganisationServiceFabricInput, OrganisationServiceFabric>;
        getDuplicate: GetDuplicateOperation<OrganisationServiceFabric>;
        update: UpdateOperation<OrganisationServiceFabricInput, OrganisationServiceFabric>;
        updateById: UpdateByIdOperation<OrganisationServiceFabricInput, OrganisationServiceFabric>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: OrganisationServiceFabric$Mutations;
        asyncOperations: OrganisationServiceFabric$AsyncOperations;
        lookups(dataOrId: string | { data: OrganisationServiceFabricInput }): OrganisationServiceFabric$Lookups;
        getDefaults: GetDefaultsOperation<OrganisationServiceFabric>;
    }
    export interface TaxRateRepository extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        legislations: ClientCollection<Legislation>;
    }
    export interface TaxRateRepositoryInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
    }
    export interface TaxRateRepositoryBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        legislations: ClientCollection<Legislation>;
    }
    export interface TaxRateRepository$Queries {
        defaultInstance: Node$Operation<{}, TaxRateRepository>;
        healthCheck: Node$Operation<
            {},
            {
                serviceVersion: string;
                statusText: string;
                deploymentTag: string;
                dictionariesVersion: string;
                commitId: string;
                openApiSpecificationVersion: string;
            }
        >;
    }
    export interface TaxRateRepository$Mutations {
        getTaxLegislation: Node$Operation<
            {},
            {
                id: string;
                code: string;
                countryCode: string;
                description: string;
            }[]
        >;
        getTaxRegion: Node$Operation<
            {},
            {
                id: string;
                code: string;
                countryCode: string;
                description: string;
            }[]
        >;
        getTaxRate: Node$Operation<
            {
                filterPropertie?: string;
                filterValue?: string;
            },
            {
                id: string;
                code: string;
                description: string;
                isReverseCharge: boolean;
                order: integer;
                isTaxOnTax: boolean;
                percentOfNetTaxAppliedFor: integer;
                deductibleTaxPercentage: integer;
                isDeductibleTaxPercentageModifiable: boolean;
                dateFrom: string;
                dateTo: string;
                isActive: boolean;
                taxLegislationId: string;
                taxRegionId: string;
                taxTreatmentId: string;
                taxCodeId: string;
                taxItemTypeId: string;
                taxPercentageGroupId: string;
            }[]
        >;
        getTaxPercentage: Node$Operation<
            {
                filterPropertie?: string;
                filterValue?: string;
            },
            {
                id: string;
                groupId: string;
                code: string;
                description: string;
                dateFrom: string;
                dateTo: string;
                isActive: boolean;
                percentage: string;
                taxLegislationId: string;
                taxRegionId: string;
            }[]
        >;
        getTaxCode: Node$Operation<
            {
                filterPropertie?: string;
                filterValue?: string;
            },
            {
                id: string;
                code: string;
                description: string;
                dateFrom: string;
                dateTo: string;
                isActive: boolean;
                taxLegislationId: string;
                taxRegionId: string;
            }[]
        >;
        getTranslation: Node$Operation<
            {
                filterPropertie?: string;
                filterValue?: string;
            },
            {
                id: string;
                dictionaryName: string;
                dictionaryRowId: string;
                dictionaryFieldName: string;
                locale: string;
                translation: string;
            }[]
        >;
    }
    export interface TaxRateRepository$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface TaxRateRepository$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface TaxRateRepository$Operations {
        queries: TaxRateRepository$Queries;
        mutations: TaxRateRepository$Mutations;
        asyncOperations: TaxRateRepository$AsyncOperations;
        lookups(dataOrId: string | { data: TaxRateRepositoryInput }): TaxRateRepository$Lookups;
        getDefaults: GetDefaultsOperation<TaxRateRepository>;
    }
    export interface ServiceFabricOptionManagement extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
    }
    export interface ServiceFabricOptionManagementInput extends ClientNodeInput {}
    export interface ServiceFabricOptionManagementBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
    }
    export interface ServiceFabricOptionManagement$Queries {
        isServiceOptionActiveFunction: Node$Operation<{}, boolean>;
    }
    export interface ServiceFabricOptionManagement$Mutations {
        serviceOptionChange: Node$Operation<{}, boolean>;
    }
    export interface ServiceFabricOptionManagement$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ServiceFabricOptionManagement$Operations {
        queries: ServiceFabricOptionManagement$Queries;
        update: UpdateOperation<ServiceFabricOptionManagementInput, ServiceFabricOptionManagement>;
        updateById: UpdateByIdOperation<ServiceFabricOptionManagementInput, ServiceFabricOptionManagement>;
        mutations: ServiceFabricOptionManagement$Mutations;
        asyncOperations: ServiceFabricOptionManagement$AsyncOperations;
        getDefaults: GetDefaultsOperation<ServiceFabricOptionManagement>;
    }
    export interface ServiceFabricTaxIdManagement extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
    }
    export interface ServiceFabricTaxIdManagementInput extends ClientNodeInput {}
    export interface ServiceFabricTaxIdManagementBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
    }
    export interface ServiceFabricTaxIdManagement$Queries {
        isServiceOptionActiveFunction: Node$Operation<{}, boolean>;
    }
    export interface ServiceFabricTaxIdManagement$Mutations {
        serviceOptionChange: Node$Operation<{}, boolean>;
    }
    export interface ServiceFabricTaxIdManagement$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ServiceFabricTaxIdManagement$Operations {
        queries: ServiceFabricTaxIdManagement$Queries;
        update: UpdateOperation<ServiceFabricTaxIdManagementInput, ServiceFabricTaxIdManagement>;
        updateById: UpdateByIdOperation<ServiceFabricTaxIdManagementInput, ServiceFabricTaxIdManagement>;
        mutations: ServiceFabricTaxIdManagement$Mutations;
        asyncOperations: ServiceFabricTaxIdManagement$AsyncOperations;
        getDefaults: GetDefaultsOperation<ServiceFabricTaxIdManagement>;
    }
    export interface CompanyExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        name: string;
        description: string;
        legislation: Legislation;
        chartOfAccount: ChartOfAccount;
        hierarchyChartContent: TextStream;
        siren: string;
        naf: string;
        rcs: string;
        legalForm: LegalForm;
        country: Country;
        currency: Currency;
        priceScale: integer;
        addresses: ClientCollection<CompanyAddress>;
        primaryAddress: CompanyAddress;
        primaryContact: CompanyContact;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        customerOnHoldCheck: CustomerOnHoldType;
        contacts: ClientCollection<CompanyContact>;
        postingClass: PostingClass;
        taxEngine: TaxEngine;
        doStockPosting: boolean;
        doWipPosting: boolean;
        doArPosting: boolean;
        doApPosting: boolean;
        doNonAbsorbedPosting: boolean;
        attributeTypes: ClientCollection<CompanyAttributeType>;
        dimensionTypes: ClientCollection<CompanyDimensionType>;
        defaultAttributes: ClientCollection<CompanyDefaultAttribute>;
        defaultDimensions: ClientCollection<CompanyDefaultDimension>;
        datevConsultantNumber: integer;
        datevCustomerNumber: integer;
        bankAccount: BankAccount;
        serviceFabricExternalId: string;
        serviceFabricCompanyId: string;
        serviceFabricCompany: string;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface CompanyInputExtension {
        id?: string;
        isActive?: boolean | string;
        name?: string;
        description?: string;
        legislation?: integer | string;
        chartOfAccount?: integer | string;
        siren?: string;
        naf?: string;
        rcs?: string;
        legalForm?: LegalForm;
        country?: integer | string;
        currency?: integer | string;
        addresses?: Partial<CompanyAddressInput>[];
        sequenceNumberId?: string;
        customerOnHoldCheck?: CustomerOnHoldType;
        contacts?: Partial<CompanyContactInput>[];
        postingClass?: integer | string;
        taxEngine?: TaxEngine;
        doStockPosting?: boolean | string;
        doWipPosting?: boolean | string;
        doNonAbsorbedPosting?: boolean | string;
        attributeTypes?: Partial<CompanyAttributeTypeInput>[];
        dimensionTypes?: Partial<CompanyDimensionTypeInput>[];
        defaultAttributes?: Partial<CompanyDefaultAttributeInput>[];
        defaultDimensions?: Partial<CompanyDefaultDimensionInput>[];
        datevConsultantNumber?: integer | string;
        datevCustomerNumber?: integer | string;
        bankAccount?: integer | string;
        serviceFabricExternalId?: string;
        serviceFabricCompanyId?: string;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface CompanyBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        name: string;
        description: string;
        legislation: Legislation;
        chartOfAccount: ChartOfAccount;
        hierarchyChartContent: TextStream;
        siren: string;
        naf: string;
        rcs: string;
        legalForm: LegalForm;
        country: Country;
        currency: Currency;
        priceScale: integer;
        addresses: ClientCollection<CompanyAddressBinding>;
        primaryAddress: CompanyAddress;
        primaryContact: CompanyContact;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        customerOnHoldCheck: CustomerOnHoldType;
        contacts: ClientCollection<CompanyContactBinding>;
        postingClass: PostingClass;
        taxEngine: TaxEngine;
        doStockPosting: boolean;
        doWipPosting: boolean;
        doArPosting: boolean;
        doApPosting: boolean;
        doNonAbsorbedPosting: boolean;
        attributeTypes: ClientCollection<CompanyAttributeTypeBinding>;
        dimensionTypes: ClientCollection<CompanyDimensionTypeBinding>;
        defaultAttributes: ClientCollection<CompanyDefaultAttributeBinding>;
        defaultDimensions: ClientCollection<CompanyDefaultDimensionBinding>;
        datevConsultantNumber: integer;
        datevCustomerNumber: integer;
        bankAccount: BankAccount;
        serviceFabricExternalId: string;
        serviceFabricCompanyId: string;
        serviceFabricCompany: any;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface LegislationExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        defaultChartOfAccount: ChartOfAccount;
        countries: ClientCollection<Country>;
        companies: ClientCollection<Company>;
        taxes: ClientCollection<Tax>;
        doStockPosting: boolean;
        doNonAbsorbedPosting: boolean;
        doLandedCostGoodsInTransitPosting: boolean;
        doApPosting: boolean;
        doArPosting: boolean;
        doNonStockVariancePosting: boolean;
        serviceFabricId: string;
    }
    export interface LegislationInputExtension {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        isActive?: boolean | string;
        doStockPosting?: boolean | string;
        doNonAbsorbedPosting?: boolean | string;
        doLandedCostGoodsInTransitPosting?: boolean | string;
        doApPosting?: boolean | string;
        doArPosting?: boolean | string;
        doNonStockVariancePosting?: boolean | string;
        serviceFabricId?: string;
    }
    export interface LegislationBindingExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        defaultChartOfAccount: ChartOfAccount;
        countries: ClientCollection<Country>;
        companies: ClientCollection<Company>;
        taxes: ClientCollection<Tax>;
        doStockPosting: boolean;
        doNonAbsorbedPosting: boolean;
        doLandedCostGoodsInTransitPosting: boolean;
        doApPosting: boolean;
        doArPosting: boolean;
        doNonStockVariancePosting: boolean;
        serviceFabricId: string;
    }
    export interface TaxExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        name: string;
        primaryExternalReference: string;
        secondaryExternalReference: string;
        taxCategory: TaxCategory;
        country: Country;
        isReverseCharge: boolean;
        jurisdictionName: string;
        legalMention: string;
        taxValues: ClientCollection<TaxValue>;
        isUsed: boolean;
        type: TaxType;
        postingClass: PostingClass;
        postingKey: integer;
        serviceFabricId: string;
        legislation: Legislation;
    }
    export interface TaxInputExtension {
        _vendor?: integer | string;
        id?: string;
        isActive?: boolean | string;
        name?: string;
        primaryExternalReference?: string;
        secondaryExternalReference?: string;
        taxCategory?: integer | string;
        country?: integer | string;
        isReverseCharge?: boolean | string;
        jurisdictionName?: string;
        legalMention?: string;
        taxValues?: Partial<TaxValueInput>[];
        type?: TaxType;
        postingClass?: integer | string;
        postingKey?: integer | string;
        serviceFabricId?: string;
        legislation?: integer | string;
    }
    export interface TaxBindingExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        name: string;
        primaryExternalReference: string;
        secondaryExternalReference: string;
        taxCategory: TaxCategory;
        country: Country;
        isReverseCharge: boolean;
        jurisdictionName: string;
        legalMention: string;
        taxValues: ClientCollection<TaxValueBinding>;
        isUsed: boolean;
        type: TaxType;
        postingClass: PostingClass;
        postingKey: integer;
        serviceFabricId: string;
        legislation: Legislation;
    }
    export interface TaxExtension$Mutations {
        getServiceFabricLink: Node$Operation<
            {},
            {
                _id: integer;
                xtreemCategory: string;
                xtreemName: string;
                xtreemLeg: string;
                isLinked: boolean;
                sfName: string;
                sfId: string;
            }[]
        >;
        createFromServiceFabric: Node$Operation<
            {
                serviceFabricId?: string[];
                legislationID?: string;
            },
            boolean
        >;
    }
    export interface TaxExtension$Operations {
        mutations: TaxExtension$Mutations;
        getDefaults: GetDefaultsOperation<Tax>;
    }
    export interface TaxValueExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        tax: Tax;
        endDate: string;
        displaySort: string;
        rate: string;
        deductibleRate: string;
        serviceFabricId: string;
    }
    export interface TaxValueInputExtension {
        _vendor?: integer | string;
        endDate?: string;
        displaySort?: string;
        rate?: decimal | string;
        deductibleRate?: decimal | string;
        serviceFabricId?: string;
    }
    export interface TaxValueBindingExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        tax: Tax;
        endDate: string;
        displaySort: string;
        rate: string;
        deductibleRate: string;
        serviceFabricId: string;
    }
    export interface Package {
        '@sage/xtrem-service-fabric/OrganisationServiceFabric': OrganisationServiceFabric$Operations;
        '@sage/xtrem-service-fabric/TaxRateRepository': TaxRateRepository$Operations;
        '@sage/xtrem-service-fabric/ServiceFabricOptionManagement': ServiceFabricOptionManagement$Operations;
        '@sage/xtrem-service-fabric/ServiceFabricTaxIdManagement': ServiceFabricTaxIdManagement$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremFinanceData$Package,
            SageXtremImportExport$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-service-fabric-api' {
    export type * from '@sage/xtrem-service-fabric-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-service-fabric-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type {
        CompanyBindingExtension,
        CompanyExtension,
        CompanyInputExtension,
    } from '@sage/xtrem-service-fabric-api';
    export interface Company extends CompanyExtension {}
    export interface CompanyBinding extends CompanyBindingExtension {}
    export interface CompanyInput extends CompanyInputExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type {
        LegislationBindingExtension,
        LegislationExtension,
        LegislationInputExtension,
    } from '@sage/xtrem-service-fabric-api';
    export interface Legislation extends LegislationExtension {}
    export interface LegislationBinding extends LegislationBindingExtension {}
    export interface LegislationInput extends LegislationInputExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type {
        TaxBindingExtension,
        TaxExtension,
        TaxExtension$Mutations,
        TaxExtension$Operations,
        TaxInputExtension,
        TaxValueBindingExtension,
        TaxValueExtension,
        TaxValueInputExtension,
    } from '@sage/xtrem-service-fabric-api';
    export interface Tax extends TaxExtension {}
    export interface TaxBinding extends TaxBindingExtension {}
    export interface TaxInput extends TaxInputExtension {}
    export interface Tax$Mutations extends TaxExtension$Mutations {}
    export interface Tax$Operations extends TaxExtension$Operations {}
    export interface TaxValue extends TaxValueExtension {}
    export interface TaxValueBinding extends TaxValueBindingExtension {}
    export interface TaxValueInput extends TaxValueInputExtension {}
}
