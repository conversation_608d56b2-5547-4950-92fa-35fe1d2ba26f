import type { AllBePage, BusinessEntityPage } from '@sage/xtrem-master-data/lib/client-functions/business-entity';
import { taxIdNumberValidation } from '@sage/xtrem-master-data/lib/client-functions/business-entity-validation';
import type { GraphApi } from '@sage/xtrem-service-fabric-api';
import type * as ui from '@sage/xtrem-ui';
import type { BusinessEntityExtension } from '../page-extensions/business-entity-extension';

import type { ViesResponseUi } from '../shared-functions/interfaces/company-lookup';
import { taxIdNotValid, wrongName } from './messages';

type AllBePageExtension = BusinessEntityExtension;

export async function isOrganisationSet(page: ui.Page<GraphApi>) {
    const defaultInstance = (await page.$.graph
        .node('@sage/xtrem-service-fabric/OrganisationServiceFabric')
        .queries.defaultInstance({ organisationId: true, isActive: true, tokenCreation: true }, false)
        .execute()) || { organisationId: '', isActive: false, tokenCreation: null };

    return defaultInstance.isActive && !!defaultInstance.organisationId && !!defaultInstance.tokenCreation;
}

export function taxIdNumberValidationWithVies(
    page: AllBePageExtension,
    country: string,
    taxID: string,
    viesResponseUi: ViesResponseUi,
) {
    // XT-61637 : there we are calling client-functions that are located to master-data package types of pages are different
    const validation = country !== 'US' ? taxIdNumberValidation(page as unknown as AllBePage, taxID) : undefined;
    if (validation) {
        return validation;
    }
    if (
        viesResponseUi &&
        country === viesResponseUi.countryCode &&
        taxID.includes(viesResponseUi.vatNumber) &&
        !viesResponseUi.valid
    ) {
        return taxIdNotValid();
    }
    return '';
}

export function companyIdValidationRequest(
    page: ui.Page<GraphApi>,
    country: string,
    taxID: string,
): Promise<ViesResponseUi> {
    return page.$.graph
        .node('@sage/xtrem-service-fabric/OrganisationServiceFabric')
        .mutations.companyIdValidation(
            {
                status: true,
                statusCode: true,
                countryCode: true,
                requestDate: true,
                traderAddress: true,
                traderCity: true,
                traderName: true,
                traderPostCode: true,
                traderStreet: true,
                valid: true,
                vatNumber: true,
            },
            { country, taxID },
        )
        .execute();
}

export async function checkVies(
    page: AllBePageExtension,
    country: string,
    taxID: string,
): Promise<ViesResponseUi | null> {
    if (
        await page.$.graph
            .node('@sage/xtrem-service-fabric/OrganisationServiceFabric')
            .mutations.isAvailableCountry(false, { country })
            .execute()
    ) {
        return page.isOrganisationSetBoolean ? companyIdValidationRequest(page, country, taxID) : null;
    }
    return null;
}

/** because  AllBePageExtension don't have all properties of AllBePage -  // to be fix after XT-61637 typing issues  */
function isPageAllBePage(_page: any): _page is BusinessEntityPage {
    return true;
}

export async function taxIdNumberOnChangeAfterWithVies(page: AllBePageExtension) {
    if (isPageAllBePage(page) && page.isOrganisationSetBoolean && page.taxIdNumber.value) {
        const viesResponsePayload = await checkVies(page, page.country.value?.id || '', page.taxIdNumber.value);
        if (viesResponsePayload) {
            page.viesResponsePayload = viesResponsePayload;
            await page.taxIdNumber.validate();
            if (!page.name.value) {
                page.name.value = viesResponsePayload.traderName ? viesResponsePayload.traderName : null;

                // to be added  page.addAddressLine.isDisabled = true;
                // to be added  page.displayAddresses.isDisabled = true;
            } else {
                await page.name.validate();
            }
        }
    }
}

/**
 *  Validate the name
 * @param page
 * @returns
 */
export function nameValidation(page: AllBePageExtension) {
    if (
        page.viesResponsePayload &&
        page.viesResponsePayload.traderName &&
        page.viesResponsePayload.valid &&
        page.taxIdNumber.value &&
        page.taxIdNumber.value.includes(page.viesResponsePayload.vatNumber) &&
        page.viesResponsePayload.traderName !== page.name.value
    ) {
        return wrongName(page.viesResponsePayload.traderName || '');
    }
    return undefined;
}
