import { decorators, NodeExtension, Uuid } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import type * as xtremServiceFabric from '../index';

@decorators.nodeExtension<CompanyExtension>({
    extends: () => xtremSystem.nodes.Company,
})
export class CompanyExtension extends NodeExtension<xtremSystem.nodes.Company> {
    /**
     * External ID of a company for serviceFabric
     */
    @decorators.stringProperty<CompanyExtension, 'serviceFabricExternalId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.uuid,
    })
    readonly serviceFabricExternalId: Promise<string>;

    /**
     * CompanyID comming from serviceFabric when creating
     */
    @decorators.stringProperty<CompanyExtension, 'serviceFabricCompanyId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.uuid,
    })
    readonly serviceFabricCompanyId: Promise<string>;

    /**
     *  Payload to create an organisation
     */
    @decorators.jsonProperty<CompanyExtension, 'serviceFabricCompany'>({
        isPublished: true,
        async computeValue() {
            return {
                name: await this.name,
                externalId:
                    (await this.serviceFabricExternalId) !== ''
                        ? await this.serviceFabricExternalId
                        : Uuid.generate().toString('-'),
                taxNumber: (await this.naf) || '', // TODO : what's need to be insert RCS ? NAF ? SIREN ? ?
                standardIndustrialCode: '',
                contactTelNo: (await (await this.primaryContact)?.locationPhoneNumber) || '',
                contactEmail: (await (await this.primaryContact)?.email) || '',
                address: {
                    addressLine1: (await (await this.primaryAddress)?.addressLine1) || '',
                    addressLine2: (await (await this.primaryAddress)?.addressLine2) || '',
                    addressLine3: '',
                    addressLine4: '',
                    countrySubdivision: (await (await this.primaryAddress)?.region) || '',
                    postalCode: (await (await this.primaryAddress)?.postcode) || '',
                    country: (await (await (await this.primaryAddress)?.country)?.iso31661Alpha3) || '',
                },
            };
        },
    })
    readonly serviceFabricCompany: Promise<xtremServiceFabric.interfaces.Company>;
}
declare module '@sage/xtrem-system/lib/nodes/company' {
    export interface Company extends CompanyExtension {}
}
