import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';

@decorators.nodeExtension<TaxValueExtension>({
    extends: () => xtremTax.nodes.TaxValue,
})
export class TaxValueExtension extends NodeExtension<xtremTax.nodes.TaxValue> {
    /**
     * unique identifier.
     * Naming convention: TP(tax percentage) & sequence number & tax legislation code & code,
     * e.g.  TP1UKSTDRT
     */
    @decorators.stringProperty<TaxValueExtension, 'serviceFabricId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.uuid,
    })
    readonly serviceFabricId: Promise<string>;
}
declare module '@sage/xtrem-tax/lib/nodes/tax-value' {
    export interface TaxValue extends TaxValueExtension {}
}
