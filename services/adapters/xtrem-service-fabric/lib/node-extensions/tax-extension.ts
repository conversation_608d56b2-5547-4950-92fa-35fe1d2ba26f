import type { Context } from '@sage/xtrem-core';
import { asyncArray, decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremServiceFabric from '../index';

@decorators.nodeExtension<TaxExtension>({
    extends: () => xtremTax.nodes.Tax,
})
export class TaxExtension extends NodeExtension<xtremTax.nodes.Tax> {
    /**
     * Unique identifier. Naming convention:
     * TR (tax rate type) & sequence number & tax treatment code (e.g. UKSH) & item type id & tax code code
     *  e.g.  TR1UKSHGDS1UKSTDRT
     */
    @decorators.stringProperty<TaxExtension, 'serviceFabricId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.uuid,
        lookupAccess: true,
    })
    readonly serviceFabricId: Promise<string>;

    @decorators.mutation<typeof TaxExtension, 'getServiceFabricLink'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    _id: 'integer',
                    xtreemCategory: 'string',
                    xtreemName: 'string',
                    xtreemLeg: 'string',
                    isLinked: 'boolean',
                    sfName: 'string',
                    sfId: 'string',
                },
            },
        },
    })
    static async getServiceFabricLink(context: Context): Promise<
        {
            _id: number;
            xtreemCategory: string;
            xtreemName: string;
            xtreemLeg: string | undefined;
            isLinked: boolean;
            sfName: string;
            sfId: string;
            sfLeg: string;
        }[]
    > {
        const sfTaxArray = await xtremServiceFabric.functions.taxRateRepository.serviceFabricGetTaxRate(context);
        const xtremTaxQuery = await context.query(xtremTax.nodes.Tax).toArray();

        const linkArray = await asyncArray(xtremTaxQuery)
            .map(async xtTax => {
                const scTaxLinkedIndex = await asyncArray(sfTaxArray).findIndex(
                    async sfTax => sfTax.id === (await xtTax.serviceFabricId),
                );
                if (scTaxLinkedIndex > 0) {
                    const sfTax = sfTaxArray[scTaxLinkedIndex];
                    delete sfTaxArray[scTaxLinkedIndex];
                    return {
                        _id: xtTax._id,
                        xtreemCategory: await (await xtTax.taxCategory).name,
                        xtreemName: await xtTax.name,
                        xtreemLeg: await (await xtTax.legislation)?.id,
                        isLinked: true,
                        sfName: sfTax.description,
                        sfId: sfTax.id,
                        sfLeg: sfTax.taxLegislationId,
                    };
                }
                return {
                    _id: xtTax._id,
                    xtreemCategory: await (await xtTax.taxCategory).name,
                    xtreemName: await xtTax.name,
                    xtreemLeg: await (await xtTax.legislation)?.id,
                    isLinked: false,
                    sfName: '',
                    sfId: '',
                    sfLeg: '',
                };
            })
            .toArray();
        return linkArray;
    }

    @decorators.mutation<typeof TaxExtension, 'createFromServiceFabric'>({
        isPublished: true,
        parameters: [
            { name: 'serviceFabricId', type: 'array', item: { type: 'string' } },
            { name: 'legislationID', type: 'string' },
        ],
        return: {
            type: 'boolean',
        },
    })
    static async createFromServiceFabric(
        context: Context,
        serviceFabricId: string[],
        legislationID: string,
    ): Promise<boolean> {
        context.logger.debug(() => `${serviceFabricId.join('-')}`);
        await context.notify('serviceFabric/createTax', {
            idList: serviceFabricId,
            legislationID,
        } as xtremServiceFabric.interfaces.Listener.ServiceFabricIdList);
        return true;
    }
}
declare module '@sage/xtrem-tax/lib/nodes/tax' {
    export interface Tax extends TaxExtension {}
}
