import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.nodeExtension<LegislationExtension>({
    extends: () => xtremStructure.nodes.Legislation,
})
export class LegislationExtension extends NodeExtension<xtremStructure.nodes.Legislation> {
    /**
     * serviceFabric ID ( TL1UK - TR1NI )
     */
    @decorators.stringProperty<LegislationExtension, 'serviceFabricId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.uuid,
        lookupAccess: true,
    })
    readonly serviceFabricId: Promise<string>;
}
declare module '@sage/xtrem-structure/lib/nodes/legislation' {
    export interface Legislation extends LegislationExtension {}
}
