import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { commonServiceFabricActivities } from '../functions/common';

export const businessEntityExtension = new ActivityExtension({
    extends: xtremMasterData.activities.businessEntity,
    __filename,
    permissions: [],
    operationGrants: {
        read: [...commonServiceFabricActivities],
        create: [...commonServiceFabricActivities],
        update: [...commonServiceFabricActivities],
    },
});
