import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremServiceFabric from '../index';

export const serviceFabricOptionManagementExtension = new ActivityExtension({
    extends: xtremStructure.activities.baseOptionManagement,
    __filename,
    permissions: [],
    operationGrants: {
        read: [
            {
                operations: ['isServiceOptionActiveFunction'],
                on: [() => xtremServiceFabric.nodes.ServiceFabricOptionManagement],
            },
            {
                operations: ['isServiceOptionActiveFunction'],
                on: [() => xtremServiceFabric.nodes.ServiceFabricTaxIdManagement],
            },
        ],
        update: [
            { operations: ['serviceOptionChange'], on: [() => xtremServiceFabric.nodes.ServiceFabricOptionManagement] },
            { operations: ['serviceOptionChange'], on: [() => xtremServiceFabric.nodes.ServiceFabricTaxIdManagement] },
        ],
    },
});
