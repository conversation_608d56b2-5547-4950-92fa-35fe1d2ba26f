import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { withoutEdges } from '@sage/xtrem-client';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { GraphApi } from '@sage/xtrem-service-fabric-api';
import type { Tax } from '@sage/xtrem-tax-api-partial';
import * as ui from '@sage/xtrem-ui';
import type { TaxPercentage, TaxPercentageTable, TaxRate } from '../shared-functions/interfaces/tax-rate-repository';

@ui.decorators.page<TaxPercentageServiceFabricDialog>({
    title: 'Select tax percentage to add in Sage DMO',
    mode: 'default',
    isTransient: true,
    businessActions() {
        return [this.ok];
    },
    async onLoad() {
        this.tax.value = MasterDataUtils.tryParseJSON<ExtractEdgesPartial<Tax>>(this.$.queryParameters.tax.toString());
        await this.init();
        this.simTaxValue.value = this.$.queryParameters.xtremTaxValueToLink.toString();
        this.isTaxPercentageSelection = !!this.$.queryParameters.xtremTaxValueToLink;
        if (this.isTaxPercentageSelection) {
            this.taxPercentages.selectRecord(this.$.queryParameters.serviceFabricId.toString());
        }
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
})
export class TaxPercentageServiceFabricDialog extends ui.Page<GraphApi> {
    isTaxPercentageSelection: boolean;

    @ui.decorators.section<TaxPercentageServiceFabricDialog>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<TaxPercentageServiceFabricDialog>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.referenceField<TaxPercentageServiceFabricDialog, Tax>({
        parent() {
            return this.mainBlock;
        },
        title: 'Tax',
        isTransient: true,
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'serviceFabricId' })],
        node: '@sage/xtrem-tax/Tax',
        valueField: 'name',
        isFullWidth: true,
        minLookupCharacters: 0,
        isReadOnly: true,
    })
    tax: ui.fields.Reference<Tax>;

    @ui.decorators.textField<TaxPercentageServiceFabricDialog>({
        parent() {
            return this.mainBlock;
        },
        title: 'Sage DMO Tax value',
        isHidden: true,
        width: 'large',
    })
    simTaxValue: ui.fields.Text;

    @ui.decorators.switchField<TaxPercentageServiceFabricDialog>({
        title: 'Already selected',
        parent() {
            return this.mainBlock;
        },
        async onChange() {
            await this.init();
        },
    })
    isAlreadySelected: ui.fields.Switch;

    @ui.decorators.tableField<TaxPercentageServiceFabricDialog, TaxPercentageTable>({
        parent() {
            return this.mainBlock;
        },
        canExport: true,
        canResizeColumns: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                isHidden: true,
            }),
            ui.nestedFields.text({
                bind: 'id',
                title: 'ID',
                size: 'small',
                width: 'small',
            }),
            ui.nestedFields.text({
                bind: 'code',
                title: 'Code',
                size: 'small',
                width: 'small',
            }),
            ui.nestedFields.text({
                bind: 'dateFrom',
                title: 'Date from',
                size: 'small',
                width: 'small',
            }),
            ui.nestedFields.text({
                bind: 'dateTo',
                title: 'Date to',
                size: 'small',
                width: 'small',
            }),
            ui.nestedFields.text({
                bind: 'percentage',
                title: 'Percentage',
                size: 'small',
                width: 'small',
            }),
            ui.nestedFields.icon({
                title: 'Status',
                bind: 'isAlreadySelected' as any, // nestedField Icon : Can't bind a boolean property https://jira.sage.com/browse/XT-24059
                size: 'small',
                map(value: boolean) {
                    return value ? 'tick' : 'close';
                },
                backgroundColor(value: string) {
                    return value === '' ? ui.tokens.colorsSemanticNegative500 : ui.tokens.colorsSemanticPositive500;
                },
            }),
        ],
        onRowSelected(sysId: string) {
            this.selectRow(sysId);
        },
        onRowClick(sysId: string) {
            this.selectRow(sysId);
        },
    })
    taxPercentages: ui.fields.Table<TaxPercentageTable>;

    selectRow(_id: string) {
        if (this.isTaxPercentageSelection) {
            this.taxPercentages.unselectAllRecords();
            this.taxPercentages.selectRecord(_id);
        }
    }

    @ui.decorators.pageAction<TaxPercentageServiceFabricDialog>({
        title() {
            return this.isTaxPercentageSelection
                ? ui.localize('@sage/xtrem-service-fabric/update', 'Update')
                : ui.localize('@sage/xtrem-service-fabric/create', 'Create');
        },
        onClick() {
            this.$.finish(this.taxPercentages.selectedRecords);
        },
    })
    ok: ui.PageAction;

    async getFabricTaxPercentageList(scLegisilationId: string): Promise<TaxPercentage[]> {
        const sageFabricTaxRateList = await this.$.graph
            .node('@sage/xtrem-service-fabric/TaxRateRepository')
            .mutations.getTaxPercentage(
                { code: true, description: true, id: true },
                { filterPropertie: 'groupId', filterValue: scLegisilationId },
            )
            .execute()
            .then<TaxPercentage[]>((result): TaxPercentage[] => result as TaxPercentage[])
            .catch(error => {
                this.$.showToast(`${error.message}`);
                return [] as TaxPercentage[];
            });
        return sageFabricTaxRateList;
    }

    async getServiceFabricTaxPercentageGroupId(): Promise<string> {
        const sageFabricTaxRateList: TaxRate[] = await this.$.graph
            .node('@sage/xtrem-service-fabric/TaxRateRepository')
            .mutations.getTaxRate(
                { taxPercentageGroupId: true },
                { filterPropertie: 'id', filterValue: this.tax.value?.serviceFabricId },
            )
            .execute()
            .then<TaxRate[]>((result): TaxRate[] => result as TaxRate[])
            .catch(error => {
                this.$.showToast(`${error.message}`);
                return [] as TaxRate[];
            });
        return sageFabricTaxRateList[0].taxPercentageGroupId;
    }

    uServiceFabricIdLinkedToTaxValue: string[];

    private async getServiceFabricIdLinkedToTaxValues(): Promise<string[]> {
        if (this.uServiceFabricIdLinkedToTaxValue) {
            return this.uServiceFabricIdLinkedToTaxValue;
        }

        this.uServiceFabricIdLinkedToTaxValue = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-tax/TaxValue')
                .query(ui.queryUtils.edgesSelector({ serviceFabricId: true }), {
                    filter: { serviceFabricId: { _ne: '' } },
                })
                .execute(),
        ).map(line => line.serviceFabricId);

        return this.uServiceFabricIdLinkedToTaxValue;
    }

    private async init() {
        const taxPercentageList = await this.getFabricTaxPercentageList(
            await this.getServiceFabricTaxPercentageGroupId(),
        );
        const serviceFabricIdLinkedToTaxValues = await this.getServiceFabricIdLinkedToTaxValues();

        this.taxPercentages.value = taxPercentageList

            .map(taxPercentage => ({
                ...taxPercentage,
                isAlreadySelected: serviceFabricIdLinkedToTaxValues.includes(taxPercentage.id),
                _id: taxPercentage.id,
            }))
            .filter(taxRateFilter => (this.isAlreadySelected.value ? !taxRateFilter.isAlreadySelected : true));
    }
}
