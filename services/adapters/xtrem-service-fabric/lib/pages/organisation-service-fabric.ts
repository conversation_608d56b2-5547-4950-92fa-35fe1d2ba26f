import type { Graph<PERSON>pi } from '@sage/xtrem-service-fabric-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { companyIdValidationRequest } from '../client-functions/vies-lookup';
import { serviceFabric } from '../menu-items/service-fabric';

@ui.decorators.page<OrganisationServiceFabric>({
    module: 'xtrem-service-fabric',
    node: '@sage/xtrem-service-fabric/OrganisationServiceFabric',
    title: 'Configuration ',
    menuItem: serviceFabric,
    mode: 'tabs',
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
        });
        this.createOrganisation.isDisabled = isDirty;
        this.getToken.isDisabled = isDirty;
        this.resetOrganisation.isDisabled = isDirty;
    },
    businessActions() {
        return [this.createOrganisation, this.getToken, this.resetOrganisation, this.$standardSaveAction];
    },
    async defaultEntry() {
        return (
            (
                await this.$.graph
                    .node('@sage/xtrem-service-fabric/OrganisationServiceFabric')
                    .queries.defaultInstance({ _id: true }, false)
                    .execute()
            )?._id || null
        );
    },
    onError(error) {
        // TODO : Better handling of errors ( go on error.errors array filter with path )
        return error.message;
    },
    onLoad() {
        this.$.page.title = ui.localize(
            '@sage/xtrem-service-fabric/page__title__service_fabric_configuration',
            'Sage Service Fabric configuration',
        );
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
        });
    },
})
export class OrganisationServiceFabric extends ui.Page<GraphApi> {
    @ui.decorators.section<OrganisationServiceFabric>({
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<OrganisationServiceFabric>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.block<OrganisationServiceFabric>({
        parent() {
            return this.mainSection;
        },
    })
    urlsBlock: ui.containers.Block;

    @ui.decorators.section<OrganisationServiceFabric>({
        title: 'Company',
    })
    companySection: ui.containers.Section;

    @ui.decorators.block<OrganisationServiceFabric>({
        parent() {
            return this.companySection;
        },
    })
    companyBlock: ui.containers.Block;

    @ui.decorators.section<OrganisationServiceFabric>({
        title: 'API',
    })
    apiSection: ui.containers.Section;

    @ui.decorators.block<OrganisationServiceFabric>({
        parent() {
            return this.apiSection;
        },
        title: 'Company and tax ID look-up and validation  ',
        access: {
            node: '@sage/xtrem-service-fabric/OrganisationServiceFabric',
            bind: 'companyIdValidation',
        },
    })
    companyLookupBlock: ui.containers.Block;

    @ui.decorators.block<OrganisationServiceFabric>({
        parent() {
            return this.apiSection;
        },
        title: 'Tax rate repository',
        access: {
            node: '@sage/xtrem-service-fabric/TaxRateRepository',
            bind: 'getTaxRate',
        },
    })
    taxRateRepositoryBlock: ui.containers.Block;

    @ui.decorators.switchField<OrganisationServiceFabric>({
        parent() {
            return this.mainBlock;
        },
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<OrganisationServiceFabric>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    fieldSeparatorIsActive: ui.fields.Separator;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.mainBlock;
        },
        title: 'ID',
        isDisabled: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.mainBlock;
        },
        title: 'Connected',
        isReadOnly: true,
    })
    connected: ui.fields.Text;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.mainBlock;
        },
        title: 'Application',
        isReadOnly: true,
    })
    xApplication: ui.fields.Text;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.mainBlock;
        },
        title: 'Organization ID',
        isReadOnly: true,
    })
    organisationId: ui.fields.Text;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.mainBlock;
        },
        title: 'Sage CRM ID',
    })
    sageCrmId: ui.fields.Text;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.mainBlock;
        },
        title: 'Country',
    })
    primaryCountry: ui.fields.Text;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.mainBlock;
        },
        title: 'Admin email',
    })
    adminEmail: ui.fields.Text;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.mainBlock;
        },
        title: 'Default language',
    })
    defaultLanguage: ui.fields.Text;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.mainBlock;
        },
        title: 'External ID',
        isReadOnly: true,
    })
    externalId: ui.fields.Text;

    @ui.decorators.textField<OrganisationServiceFabric>({
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.urlsBlock;
        },
        title: 'Authentication URL',
        isReadOnly: true,
    })
    authURL: ui.fields.Text;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.urlsBlock;
        },
        title: 'API URL',
        isReadOnly: true,
    })
    apiURL: ui.fields.Text;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.urlsBlock;
        },
        title: 'Company ID lookup URL',
        isReadOnly: true,
    })
    lookupURL: ui.fields.Text;

    @ui.decorators.tableField<OrganisationServiceFabric>({
        parent() {
            return this.companySection;
        },
        title: 'Companies',
        isTransient: true,
        columns: [
            ui.nestedFields.text({
                bind: 'id',
                isHidden: true,
            }),
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'externalId',
                title: 'External ID',
                isReadOnly: true,
            }),
            ui.nestedFields.checkbox({
                bind: 'isCreated',
                title: 'Created',
                isReadOnly: true,
            }),
        ],
        dropdownActions: [
            {
                icon: 'attach',
                title: 'Create',
                isDisabled(rowId, rowData) {
                    return rowData.isCreated;
                },
                onError(error) {
                    return error.message;
                },
                async onClick(rowId, rowData) {
                    await this.$.graph
                        .node('@sage/xtrem-service-fabric/OrganisationServiceFabric')
                        .mutations.createServiceFabricCompany(true, { id: rowData.id })
                        .execute();
                    await this.companyList.refreshRecord(rowId);
                },
            },
        ],
        fieldActions() {
            return [this.refreshCompanyList];
        },
    })
    companyList: ui.fields.Table;

    @ui.decorators.pageAction<OrganisationServiceFabric>({
        icon: 'refresh',
        title: 'Refresh',
        async onClick() {
            this.companyList.value = (await this.$.graph
                .node('@sage/xtrem-service-fabric/OrganisationServiceFabric')
                .queries.getLinkedCompanyList({ id: true, externalId: true, isCreated: true, name: true }, true)
                .execute()) as [];
        },
    })
    refreshCompanyList: ui.PageAction;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.companyBlock;
        },
        isTransient: true,
        title: 'Company info',
        width: 'large',
        isReadOnly: true,
    })
    infos: ui.fields.Text;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.companyLookupBlock;
        },
        isTransient: true,
        title: 'Country',
        width: 'small',
    })
    country: ui.fields.Text;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.companyLookupBlock;
        },
        isTransient: true,
        title: 'Company ID',
        width: 'small',
    })
    companyId: ui.fields.Text;

    @ui.decorators.buttonField<OrganisationServiceFabric>({
        parent() {
            return this.companyLookupBlock;
        },
        isTransient: true,
        onError(error) {
            return error.message;
        },
        async onClick() {
            if (this.country.value && this.companyId.value) {
                const data = await companyIdValidationRequest(this, this.country.value, this.companyId.value);
                this.infosValidationApi.value = `Status : ${data.status} is valid : ${data.valid} `;
                await this.$.dialog.message(
                    'info',
                    ui.localize('@sage/xtrem-service-fabric/company_id_validation_request', 'Company ID validation'),
                    `Full Result : \n ${JSON.stringify(data, null, 4)}`,
                );
            }
        },
        map() {
            return ui.localize(
                '@sage/xtrem-service-fabric/pages__organisation_service_fabric__execute_button_text',
                'Execute',
            );
        },
    })
    execute: ui.fields.Button;

    @ui.decorators.textField<OrganisationServiceFabric>({
        parent() {
            return this.companyLookupBlock;
        },
        isTransient: true,
        title: 'Response',
        width: 'large',
        isReadOnly: true,
    })
    infosValidationApi: ui.fields.Text;

    @ui.decorators.buttonField<OrganisationServiceFabric>({
        parent() {
            return this.taxRateRepositoryBlock;
        },
        isTransient: true,
        onError(error) {
            return error.message;
        },
        async onClick() {
            const data = (await this.$.graph
                .node('@sage/xtrem-service-fabric/TaxRateRepository')
                .mutations.getTaxLegislation({ code: true, countryCode: true, description: true, id: true }, true)
                .execute()) as { code: string; countryCode: string; description: string; id: string }[];
            this.infosTaxRateRepositoryApi.value = data
                .map(line => `${line.id} : ${line.code} - ${line.countryCode} - ${line.description}`)
                .join('\n');
        },
        map() {
            return ui.localize(
                '@sage/xtrem-service-fabric/pages__organisation_service_fabric__tax_rate_repository_execute_button_text',
                'Get tax legislation',
            );
        },
    })
    taxRateRepositoryExecuteButton: ui.fields.Button;

    @ui.decorators.textAreaField<OrganisationServiceFabric>({
        parent() {
            return this.taxRateRepositoryBlock;
        },
        isTransient: true,
        title: 'Response',
        width: 'large',
        rows: 20,
        isReadOnly: true,
    })
    infosTaxRateRepositoryApi: ui.fields.TextArea;

    @ui.decorators.pageAction<OrganisationServiceFabric>({
        icon: 'analysis',
        title: 'Create organization',
        isHidden() {
            return this.organisationId.value !== '';
        },
        onError(error) {
            return error.message;
        },
        async onClick() {
            await this.$.graph
                .node('@sage/xtrem-service-fabric/OrganisationServiceFabric')
                .mutations.createOrganisation(true, true)
                .execute();
            await this.$.router.refresh();
        },
    })
    createOrganisation: ui.PageAction;

    @ui.decorators.pageAction<OrganisationServiceFabric>({
        icon: 'analysis',
        title: 'Reset organization',
        isHidden() {
            return !(this.organisationId.value !== '');
        },
        onError(error) {
            return error.message;
        },
        async onClick() {
            await this.$.graph
                .node('@sage/xtrem-service-fabric/OrganisationServiceFabric')
                .mutations.resetOrganisation(true, true)
                .execute();
            await this.$.router.refresh();
        },
    })
    resetOrganisation: ui.PageAction;

    @ui.decorators.pageAction<OrganisationServiceFabric>({
        icon: 'analysis',
        title: 'Get token',
        isHidden() {
            return !(this.organisationId.value !== '');
        },
        async onClick() {
            await this.$.graph
                .node('@sage/xtrem-service-fabric/OrganisationServiceFabric')
                .mutations.saveToken(true, true)
                .execute();
            await this.$.router.refresh();
        },
    })
    getToken: ui.PageAction;
}
