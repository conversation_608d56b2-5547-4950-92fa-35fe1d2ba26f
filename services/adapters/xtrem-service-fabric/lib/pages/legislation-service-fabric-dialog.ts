import { withoutEdges } from '@sage/xtrem-client';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { GraphApi } from '@sage/xtrem-service-fabric-api';
import * as ui from '@sage/xtrem-ui';
import type { TaxLegislation, TaxLegislationTable } from '../shared-functions/interfaces/tax-rate-repository';

@ui.decorators.page<LegislationServiceFabricDialog>({
    title: 'Select legislation to add in Sage DMO',
    mode: 'default',
    isTransient: true,
    businessActions() {
        return [this.ok];
    },
    async onLoad() {
        this.simLegislation.value = this.$.queryParameters.selectedLegislationId.toString();
        await this.init();
        this.legislations.selectRecord(this.$.queryParameters.serviceFabricId.toString());
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
})
export class LegislationServiceFabricDialog extends ui.Page<GraphApi> {
    @ui.decorators.section<LegislationServiceFabricDialog>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<LegislationServiceFabricDialog>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<LegislationServiceFabricDialog>({
        parent() {
            return this.mainBlock;
        },
        title: 'Sage DMO Legislation',
        isHidden: true,
        width: 'large',
    })
    simLegislation: ui.fields.Text;

    @ui.decorators.switchField<LegislationServiceFabricDialog>({
        title: 'Already selected',
        parent() {
            return this.mainBlock;
        },
        async onChange() {
            await this.init();
        },
    })
    isAlreadySelected: ui.fields.Switch;

    @ui.decorators.tableField<LegislationServiceFabricDialog, TaxLegislationTable>({
        parent() {
            return this.mainBlock;
        },
        canExport: true,
        canResizeColumns: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                isHidden: true,
            }),
            ui.nestedFields.text({
                bind: 'id',
                title: 'ID',
                size: 'small',
                width: 'small',
            }),
            ui.nestedFields.text({
                bind: 'code',
                title: 'Code',
                size: 'small',
                width: 'small',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                size: 'small',
                width: 'small',
            }),
            ui.nestedFields.icon({
                title: 'Status',
                bind: 'isAlreadySelected' as any, // nestedField Icon : Can't bind a boolean property https://jira.sage.com/browse/XT-24059
                size: 'small',
                map(value: boolean) {
                    return value ? 'tick' : 'close';
                },
                backgroundColor(value: string) {
                    return value === '' ? ui.tokens.colorsSemanticNegative500 : ui.tokens.colorsSemanticPositive500;
                },
            }),
        ],
        onRowSelected(sysId: string) {
            this.legislations.unselectAllRecords();
            this.legislations.selectRecord(sysId);
        },
        onRowClick(sysId: string) {
            this.legislations.unselectAllRecords();
            this.legislations.selectRecord(sysId);
        },
    })
    legislations: ui.fields.Table<TaxLegislationTable>;

    @ui.decorators.pageAction<LegislationServiceFabricDialog>({
        title: 'Update',
        onClick() {
            this.$.finish(this.legislations.selectedRecords);
        },
    })
    ok: ui.PageAction;

    uServiceFabricIdLinkedToLegislation: string[];

    private async getServiceFabricIdLinkedToLegislation() {
        if (this.uServiceFabricIdLinkedToLegislation) {
            return this.uServiceFabricIdLinkedToLegislation;
        }

        this.uServiceFabricIdLinkedToLegislation = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-structure/Legislation')
                .query(ui.queryUtils.edgesSelector({ serviceFabricId: true }), {
                    filter: { serviceFabricId: { _ne: '' } },
                })
                .execute(),
        ).map(line => line.serviceFabricId);

        return this.uServiceFabricIdLinkedToLegislation;
    }

    private async init() {
        const LegislationServiceFabricList = await this.getServiceFabricLegislation();
        const serviceFabricIdLinkedToLegislation = await this.getServiceFabricIdLinkedToLegislation();

        this.legislations.value = LegislationServiceFabricList.map(legislation => ({
            ...legislation,
            isAlreadySelected: serviceFabricIdLinkedToLegislation.includes(legislation.id),
            _id: legislation.id,
        })).filter(legFilter => (this.isAlreadySelected.value ? !legFilter.isAlreadySelected : true));
    }

    async getServiceFabricLegislation(): Promise<TaxLegislation[]> {
        const legislations = await this.$.graph
            .node('@sage/xtrem-service-fabric/TaxRateRepository')
            .mutations.getTaxLegislation({ code: true, countryCode: true, description: true, id: true }, false)
            .execute()
            .then<TaxLegislation[]>((result): TaxLegislation[] => result as TaxLegislation[])
            .catch(error => {
                this.$.showToast(`${error.message}`);
                return [] as TaxLegislation[];
            });
        return legislations;
    }
}
