import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { GraphApi } from '@sage/xtrem-service-fabric-api';
import type { Legislation } from '@sage/xtrem-structure-api-partial';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import type { Tax, TaxValue } from '@sage/xtrem-tax-api-partial';
import * as ui from '@sage/xtrem-ui';
import { serviceFabric } from '../menu-items/service-fabric';
import type { TaxLegislation, TaxPercentage, TaxRate } from '../shared-functions/interfaces/tax-rate-repository';

@ui.decorators.page<LinkServiceFabric>({
    module: 'xtrem-service-fabric',
    title: 'Links',
    menuItem: serviceFabric,
    node: '@sage/xtrem-service-fabric/TaxRateRepository',
    async defaultEntry() {
        return (
            (
                await this.$.graph
                    .node('@sage/xtrem-service-fabric/TaxRateRepository')
                    .queries.defaultInstance({ _id: true }, false)
                    .execute()
            )?._id || null
        );
    },
    mode: 'tabs',
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    detailPanel() {
        return {
            header: this.detailPanelHeaderSection,
            sections: [this.detailPanelLegislationSection, this.detailPanelTaxSection, this.detailPanelTaxValueSection],
        };
    },
    onError(error) {
        return MasterDataUtils.formatError(this, error);
    },
    onLoad() {
        if (this.$.detailPanel?.isHidden) this.$.detailPanel.isHidden = true;
        this.$.page.title = 'Sage Service Fabric Links';
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
    },
})
export class LinkServiceFabric extends ui.Page<GraphApi> {
    @ui.decorators.section<LinkServiceFabric>({
        title: 'Legislation',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<LinkServiceFabric>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.section<LinkServiceFabric>({
        title: 'Details',
        isHidden: true,
    })
    detailPanelHeaderSection: ui.containers.Section;

    @ui.decorators.section<LinkServiceFabric>({
        title: 'Legislation',
        isHidden: true,
    })
    detailPanelLegislationSection: ui.containers.Section;

    @ui.decorators.section<LinkServiceFabric>({
        title: 'Tax',
        isHidden: true,
    })
    detailPanelTaxSection: ui.containers.Section;

    @ui.decorators.section<LinkServiceFabric>({
        title: 'Tax value',
        isHidden: true,
    })
    detailPanelTaxValueSection: ui.containers.Section;

    @ui.decorators.section<LinkServiceFabric>({
        title: 'Region',
        isHidden: true,
    })
    detailPanelRegionSection: ui.containers.Section;

    sageFabricLegislationList: TaxLegislation[];

    sageFabricTaxRateList: TaxRate[];

    sageFabricTaxPercentageList: TaxPercentage[];

    @ui.decorators.nestedGridField<LinkServiceFabric, [Legislation, Tax, TaxValue]>({
        parent() {
            return this.mainBlock;
        },
        canActivate: true,
        isChangeIndicatorDisabled: true,
        onRowClick(_id: string, rowItem: Partial<Legislation | Tax | TaxValue>, level: number) {
            if (this.$.detailPanel?.isHidden) this.$.detailPanel.isHidden = false;
            this.detailPanelHeaderSection.isHidden = false;

            this.detailPanelLegislationSection.isHidden = true;
            this.detailPanelTaxSection.isHidden = true;
            this.detailPanelTaxValueSection.isHidden = true;

            const legislationRowItem: Partial<Legislation> = rowItem;
            const taxRowItem: Partial<Tax> = rowItem;
            const taxValueRowItem: Partial<TaxValue> = rowItem;

            switch (level) {
                case 0:
                    this.detailPanelLegislationSection.isHidden = false;
                    this.legislation.value = {
                        // Same field used for Legislation panel & Tax Panel
                        _id: legislationRowItem._id,
                        id: legislationRowItem.id,
                        name: legislationRowItem.name,
                        serviceFabricId: legislationRowItem.serviceFabricId,
                    };
                    this.legislationId.value = legislationRowItem.id ?? null;
                    this.legislationServiceFabricId.value = legislationRowItem.serviceFabricId ?? null;
                    this.legislationName.value = legislationRowItem.name ?? null;
                    break;
                case 1:
                    this.legislation.value = {
                        _id: taxRowItem.legislation?._id ?? '',
                        id: taxRowItem.legislation?.id ?? '',
                        name: taxRowItem.legislation?.name ?? '',
                        serviceFabricId: taxRowItem.legislation?.serviceFabricId ?? '',
                    };
                    this.detailPanelTaxSection.isHidden = false;
                    this.tax.value = {
                        _id: taxRowItem._id,
                        name: taxRowItem.name,
                        serviceFabricId: taxRowItem.serviceFabricId,
                    };
                    this.taxName.value = taxRowItem.name ?? null;
                    break;
                case 2:
                    this.detailPanelTaxValueSection.isHidden = false;
                    this.taxValueSysId.value = _id;
                    this.taxValueRate.value = taxValueRowItem.rate ?? null;
                    this.taxValueDeductibleRate.value = taxValueRowItem.deductibleRate ?? null;
                    break;
                default:
                    break;
            }

            /** Can the user modify the serviceFabricId one it is set ? waiting for PO */
            // this.legislationServiceFabric.isDisabled = rowItem.serviceFabricId !== '';
        },
        levels: [
            {
                node: '@sage/xtrem-structure/Legislation',
                childProperty: 'taxes',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'name',
                        title: 'Name',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.icon({
                        title: 'Status',
                        bind: 'serviceFabricId',
                        size: 'small',
                        map(value: string) {
                            return value === '' ? 'close' : 'tick';
                        },
                        backgroundColor(value: string) {
                            return value === ''
                                ? ui.tokens.colorsSemanticPositive500
                                : ui.tokens.colorsSemanticNegative500;
                        },
                    }),
                    ui.nestedFields.text({
                        bind: 'id',
                        size: 'small',
                        title: 'ID',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'serviceFabricId',
                        size: 'small',
                        title: 'Service Fabric ID',
                        isReadOnly: true,
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'edit',
                        title: 'Edit Legislation link',
                        async onClick(rowId: string, rowData: Partial<Legislation>) {
                            const returnedCode = await this.openLegislationDialog(rowData.id, rowData.serviceFabricId);
                            if (returnedCode.length === 1) {
                                await this.$.graph
                                    .node('@sage/xtrem-structure/Legislation')
                                    .updateById(
                                        { id: true },
                                        {
                                            _id: rowData._id ?? '',
                                            data: {
                                                serviceFabricId: returnedCode[0],
                                            },
                                        },
                                    )
                                    .execute();
                                await this.legislations.refreshRecord(rowId, 0);
                            }
                        },
                    },
                ],
            },
            {
                // /** https://jira.sage.com/browse/XT-24169  issue when having more than 20 taxes */
                node: '@sage/xtrem-tax/Tax',
                childProperty: 'taxValues',
                emptyStateClickableText: 'No taxes',
                /** * We are not able to know on witch level 0 we are  using the last legislation serviceFabricId
                 * Waiting for XT-23951
                 */
                // eslint-disable-next-line
                async onEmptyStateLinkClick() {
                    if (this.legislation.value && this.legislation.value.id) {
                        const taxIdList = await this.openTaxRateDialog(
                            MasterDataUtils.removeExtractEdgesPartial(this.legislation.value),
                        );
                        await this.createTax(this.legislation.value.id, taxIdList);
                    }
                },
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.reference({
                        bind: 'legislation',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'serviceFabricId' }),
                        ],
                        valueField: 'serviceFabricId',
                        isHidden: true,
                    }),
                    ui.nestedFields.checkbox({
                        bind: 'isActive',
                        title: 'Active',
                    }),
                    ui.nestedFields.text({
                        bind: 'name',
                        title: 'Name',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'legalMention',
                        title: 'legal Mention',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.icon({
                        title: 'Tax status',
                        bind: 'serviceFabricId',
                        size: 'small',
                        map(value: string) {
                            return value === '' ? 'close' : 'tick';
                        },
                        backgroundColor(value: string) {
                            return value === ''
                                ? ui.tokens.colorsSemanticPositive500
                                : ui.tokens.colorsSemanticNegative500;
                        },
                    }),

                    ui.nestedFields.reference({
                        bind: 'postingClass',
                        placeholder: 'Select posting class',
                        node: '@sage/xtrem-finance-data/PostingClass',
                        valueField: 'name',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.select({
                                bind: 'type',
                                title: 'Type',
                                optionType: '@sage/xtrem-finance-data/PostingClassType',
                            }),
                            ui.nestedFields.checkbox({ bind: 'isDetailed', isHidden: true }),
                        ],
                        /** Trying to have a selection of postingClass :
                         * the lookup is on Legislation not @sage/xtrem-tax/Tax
                         * the goal is to have this field editable
                         * https://jira.sage.com/browse/XT-23961  */
                    }),
                    ui.nestedFields.text({
                        bind: 'serviceFabricId',
                        size: 'small',
                        title: 'Service Fabric ID',
                        isReadOnly: true,
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'edit',
                        title: 'Edit tax link',
                        async onClick(rowId: string, rowData: Partial<Tax>) {
                            const returnedCode = await this.openTaxRateDialog(
                                rowData.legislation as Partial<Legislation>,
                                rowData.serviceFabricId || '',
                                rowData.name,
                            );
                            if (returnedCode.length === 1) {
                                await this.$.graph
                                    .node('@sage/xtrem-tax/Tax')
                                    .updateById(
                                        { name: true },
                                        {
                                            _id: rowId,
                                            data: {
                                                serviceFabricId: returnedCode[0],
                                            },
                                        },
                                    )
                                    .execute();
                                await this.legislations.refreshRecord(rowId, 1);
                            }
                        },
                    },
                    {
                        icon: 'delete',
                        title: 'Delete tax',
                        isDestructive: true,
                        async onClick(rowId: string) {
                            await this.$.graph.node('@sage/xtrem-tax/Tax').delete({ _id: rowId }).execute();
                            /** the refreshRecord do not refresh the record when it's deleted !
                             *  Enhancement or bug ?
                             */
                            const deletedTax = await this.legislations.refreshRecord(rowId, 1);
                            if (!deletedTax) {
                                this.legislations.removeRecord(rowId, 1);
                            }
                        },
                    },
                ],
            },
            {
                node: '@sage/xtrem-tax/TaxValue',
                dropdownActions: [
                    {
                        icon: 'edit',
                        title: 'Edit tax value link',
                        async onClick(rowId: string, rowData: Partial<TaxValue>) {
                            const returnedCode = await this.openTaxPercentageDialog(
                                rowData.tax as Partial<Tax>,
                                rowData.serviceFabricId,
                                `${rowData.rate} / ${rowData.deductibleRate}`,
                            );
                            if (returnedCode.length === 1) {
                                await this.$.graph
                                    .node('@sage/xtrem-tax/Tax')
                                    .updateById(
                                        { name: true },
                                        {
                                            _id: rowId,
                                            data: {
                                                serviceFabricId: returnedCode[0],
                                            },
                                        },
                                    )
                                    .execute();
                                await this.legislations.refreshRecord(rowId, 1);
                            }
                        },
                    },
                ],
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.reference({
                        bind: 'tax',
                        node: '@sage/xtrem-tax/Tax',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'serviceFabricId' }),
                        ],
                        valueField: 'serviceFabricId',
                        isHidden: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'rate',
                        title: 'Rate',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'deductibleRate',
                        title: 'Deductible rate',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.icon({
                        title: 'Tax value status ',
                        bind: 'serviceFabricId',
                        size: 'small',
                        map(value: string) {
                            return value === '' ? 'close' : 'tick';
                        },
                        backgroundColor(value: string) {
                            return value === ''
                                ? ui.tokens.colorsSemanticPositive500
                                : ui.tokens.colorsSemanticNegative500;
                        },
                    }),

                    ui.nestedFields.date({
                        bind: 'endDate',
                        title: 'End date',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'serviceFabricId',
                        size: 'small',
                        title: 'Service Fabric ID',
                        isReadOnly: true,
                    }),
                ],
            },
        ],
    })
    legislations: ui.fields.NestedGrid<[Legislation, Tax, TaxValue]>;

    @ui.decorators.block<LinkServiceFabric>({
        parent() {
            return this.detailPanelLegislationSection;
        },
        title: 'Legislation',
    })
    detailPanelLegislationBlockServiceFabric: ui.containers.Block;

    @ui.decorators.block<LinkServiceFabric>({
        parent() {
            return this.detailPanelTaxSection;
        },
        title: 'Tax rate',
    })
    detailPanelTaxBlockServiceFabric: ui.containers.Block;

    @ui.decorators.block<LinkServiceFabric>({
        parent() {
            return this.detailPanelTaxValueSection;
        },
        title: 'Tax value or percentage',
    })
    detailPanelTaxValueBlockServiceFabric: ui.containers.Block;

    @ui.decorators.textField<LinkServiceFabric>({
        parent() {
            return this.detailPanelTaxBlockServiceFabric;
        },
        title: 'Name',
        isFullWidth: true,
        isTransient: true,
    })
    taxName: ui.fields.Text;

    @ui.decorators.referenceField<LinkServiceFabric, Legislation>({
        parent() {
            return this.detailPanelTaxBlockServiceFabric;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'serviceFabricId' }),
        ],
        lookupDialogTitle: 'Select legislation',
        valueField: 'serviceFabricId',
        minLookupCharacters: 0,
        isReadOnly: true,
        isFullWidth: true,
        isTransient: true,
    })
    legislation: ui.fields.Reference<Legislation>;

    @ui.decorators.textField<LinkServiceFabric>({
        parent() {
            return this.detailPanelTaxBlockServiceFabric;
        },
        title: 'Tax sys ID',
        isHidden: true,
        isTransient: true,
    })
    taxSysId: ui.fields.Text;

    @ui.decorators.textField<LinkServiceFabric>({
        parent() {
            return this.detailPanelTaxValueBlockServiceFabric;
        },
        title: 'Tax value sys ID',
        isHidden: true,
        isTransient: true,
    })
    taxValueSysId: ui.fields.Text;

    @ui.decorators.textField<LinkServiceFabric>({
        parent() {
            return this.detailPanelLegislationBlockServiceFabric;
        },
        title: 'ID',
        isFullWidth: true,
        isTransient: true,
    })
    legislationId: ui.fields.Text;

    @ui.decorators.textField<LinkServiceFabric>({
        parent() {
            return this.detailPanelLegislationBlockServiceFabric;
        },
        title: 'Sage Service Fabric legislation ID',
        isFullWidth: true,
        isTransient: true,
    })
    legislationServiceFabricId: ui.fields.Text;

    @ui.decorators.textField<LinkServiceFabric>({
        parent() {
            return this.detailPanelLegislationBlockServiceFabric;
        },
        title: 'Name',
        isFullWidth: true,
        isTransient: true,
    })
    legislationName: ui.fields.Text;

    /** Limitation : Source has 3 element(s) but target allows only 2.    */
    // @ui.decorators.gridRowBlock<LinkServiceFabric>({
    //     parent() {
    //         return this.detailPanelTaxValueSection;
    //     },
    //     boundTo() {
    //         return this.legislations;
    //     },
    //     fieldFilter(columnId: string) {
    //         return ['rate', 'deductibleRate', 'endDate', 'serviceFabricId'].includes(columnId);
    //     },
    // })
    // detailPanelTaxValues: ui.containers.GridRowBlock;

    @ui.decorators.textField<LinkServiceFabric>({
        parent() {
            return this.detailPanelTaxValueBlockServiceFabric;
        },
        title: 'Rate',
        isTransient: true,
    })
    taxValueRate: ui.fields.Text;

    @ui.decorators.textField<LinkServiceFabric>({
        parent() {
            return this.detailPanelTaxValueBlockServiceFabric;
        },
        title: 'DeductibleRate',
        isTransient: true,
    })
    taxValueDeductibleRate: ui.fields.Text;

    @ui.decorators.referenceField<LinkServiceFabric, Tax>({
        parent() {
            return this.detailPanelTaxBlockServiceFabric;
        },
        title: 'Tax',
        isTransient: true,
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'serviceFabricId' })],
        node: '@sage/xtrem-tax/Tax',
        valueField: 'name',
        isFullWidth: true,
        minLookupCharacters: 0,
        isReadOnly: true,
    })
    tax: ui.fields.Reference<Tax>;

    @ui.decorators.buttonField<LinkServiceFabric>({
        parent() {
            return this.detailPanelLegislationBlockServiceFabric;
        },
        isFullWidth: true,
        isTransient: true,
        map() {
            return ui.localize('@sage/xtrem-service-fabric/add-new-rate', 'Add new rate');
        },
        onError(error) {
            return error.message;
        },
        async onClick() {
            if (this.legislation.value && this.legislationId.value) {
                const taxIdList = await this.openTaxRateDialog(
                    MasterDataUtils.removeExtractEdgesPartial(this.legislation.value),
                );
                await this.createTax(this.legislationId.value, taxIdList);
            }
            await this.legislations.refresh();
        },
    })
    addNewTaxRateButton: ui.fields.Button;

    @ui.decorators.buttonField<LinkServiceFabric>({
        parent() {
            return this.detailPanelTaxBlockServiceFabric;
        },
        isFullWidth: true,
        isTransient: true,
        map() {
            return ui.localize('@sage/xtrem-service-fabric/add-new-rate-values', 'Add new rate percentage / values');
        },
        onError(error) {
            return error.message;
        },
        async onClick() {
            if (this.tax.value && this.tax.value._id) {
                const taxPercentageIdList = await this.openTaxPercentageDialog(
                    MasterDataUtils.removeExtractEdgesPartial(this.tax.value),
                );
                this.createTaxPercentage(this.tax.value._id, taxPercentageIdList);
            }
        },
    })
    addNewTaxPercentageButton: ui.fields.Button;

    async openLegislationDialog(selectedLegislationId: string = '', serviceFabricId: string = ''): Promise<string[]> {
        const legislationSelect = (await this.$.dialog.page(
            '@sage/xtrem-service-fabric/LegislationServiceFabricDialog',
            { selectedLegislationId, serviceFabricId },
            { resolveOnCancel: true },
        )) as string[];
        this.$.showToast(`${legislationSelect?.join(' - ') || ''}`);
        return legislationSelect;
    }

    async openTaxRateDialog(
        legislation: Partial<Legislation>,
        selectedTaxId: string = '',
        xtreemTaxToLink: string = '',
    ): Promise<string[]> {
        const taxRateSelect = (await this.$.dialog.page(
            '@sage/xtrem-service-fabric/TaxRateServiceFabricDialog',
            { legislation: JSON.stringify(legislation), selectedTaxId, xtreemTaxToLink },
            { resolveOnCancel: true, fullScreen: true },
        )) as string[];
        this.$.showToast(`${taxRateSelect?.join(' - ') || ''}`);
        return taxRateSelect;
    }

    async openTaxPercentageDialog(
        selectedTax: Partial<Tax>,
        serviceFabricId: string = '',
        xtremTaxValueToLink: string = '',
    ): Promise<string[]> {
        const taxPercentageSelect = (await this.$.dialog.page(
            '@sage/xtrem-service-fabric/TaxPercentageServiceFabricDialog',
            { tax: JSON.stringify(selectedTax), serviceFabricId, xtremTaxValueToLink },
            { resolveOnCancel: true },
        )) as string[];
        this.$.showToast(`${taxPercentageSelect?.join(' - ') || ''}`);
        return taxPercentageSelect;
    }

    async createTax(legislationID: string, serviceFabricIdList: string[]) {
        await this.$.graph
            .node('@sage/xtrem-tax/Tax')
            .mutations.createFromServiceFabric(true, { serviceFabricId: serviceFabricIdList, legislationID })
            .execute();
    }

    createTaxPercentage(taxSysId: string, serviceFabricIdList: string[]) {
        this.$.showToast(`Create for ${taxSysId} , ${serviceFabricIdList.join(' - ')}`);
    }
}
