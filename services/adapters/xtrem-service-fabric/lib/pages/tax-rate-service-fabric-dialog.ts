import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { withoutEdges } from '@sage/xtrem-client';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { GraphApi } from '@sage/xtrem-service-fabric-api';
import type { Legislation } from '@sage/xtrem-structure-api-partial';
import * as ui from '@sage/xtrem-ui';
import type { TaxRate, TaxRateTable } from '../shared-functions/interfaces/tax-rate-repository';

@ui.decorators.page<TaxRateServiceFabricDialog>({
    title: 'Add tax rate to Sage DMO',
    mode: 'default',
    isTransient: true,
    businessActions() {
        return [this.ok];
    },
    async onLoad() {
        this.legislation.value = MasterDataUtils.tryParseJSON<ExtractEdgesPartial<Legislation>>(
            this.$.queryParameters.legislation.toString(),
        );
        await this.init();
        this.isTaxSelection = !!this.$.queryParameters.xtreemTaxToLink;
        if (this.isTaxSelection) {
            this.simTax.isHidden = false;
            this.simTax.value = this.$.queryParameters.xtreemTaxToLink.toString();
            this.taxRates.selectRecord(this.$.queryParameters.selectedTaxId.toString());
        }
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
})
export class TaxRateServiceFabricDialog extends ui.Page<GraphApi> {
    isTaxSelection: boolean;

    @ui.decorators.section<TaxRateServiceFabricDialog>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<TaxRateServiceFabricDialog>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<TaxRateServiceFabricDialog>({
        parent() {
            return this.mainBlock;
        },
        title: 'Sage DMO Tax',
        isHidden: true,
        width: 'large',
    })
    simTax: ui.fields.Text;

    @ui.decorators.referenceField<TaxRateServiceFabricDialog, Legislation>({
        parent() {
            return this.mainBlock;
        },
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'serviceFabricId' })],
        lookupDialogTitle: 'Select legislation',
        minLookupCharacters: 0,
        async onChange() {
            this.uTaxRateServiceFabricList = null;
            this.uServiceFabricIdLinkedToTax = null;
            await this.init();
        },
    })
    legislation: ui.fields.Reference<Legislation>;

    @ui.decorators.switchField<TaxRateServiceFabricDialog>({
        title: 'Already selected',
        parent() {
            return this.mainBlock;
        },
        async onChange() {
            await this.init();
        },
    })
    isAlreadySelected: ui.fields.Switch;

    @ui.decorators.tableField<TaxRateServiceFabricDialog, TaxRateTable>({
        parent() {
            return this.mainBlock;
        },
        canExport: true,
        canResizeColumns: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                isHidden: true,
            }),
            ui.nestedFields.text({
                bind: 'id',
                title: 'ID',
                size: 'small',
                width: 'small',
            }),
            ui.nestedFields.text({
                bind: 'code',
                title: 'Code',
                size: 'small',
                width: 'small',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                size: 'small',
                width: 'small',
            }),
            ui.nestedFields.icon({
                title: 'Status',
                bind: 'isAlreadySelected',
                size: 'small',
                map(value: boolean) {
                    return value ? 'tick' : 'close';
                },
                backgroundColor(value: string) {
                    return value === '' ? ui.tokens.colorsSemanticNegative500 : ui.tokens.colorsSemanticPositive500;
                },
            }),
        ],
        onRowSelected(sysId: string) {
            this.selectRow(sysId);
        },
        onRowClick(sysId: string) {
            this.selectRow(sysId);
        },
    })
    taxRates: ui.fields.Table<TaxRateTable>;

    selectRow(_id: string) {
        if (this.isTaxSelection) {
            this.taxRates.unselectAllRecords();
            this.taxRates.selectRecord(_id);
        }
    }

    @ui.decorators.pageAction<TaxRateServiceFabricDialog>({
        title() {
            return this.isTaxSelection
                ? ui.localize('@sage/xtrem-service-fabric/update', 'Update')
                : ui.localize('@sage/xtrem-service-fabric/create', 'Create');
        },
        onClick() {
            this.$.finish(this.taxRates.selectedRecords);
        },
    })
    ok: ui.PageAction;

    uServiceFabricIdLinkedToTax: string[] | null;

    private async getServiceFabricIdLinkedToTax() {
        if (this.uServiceFabricIdLinkedToTax) {
            return this.uServiceFabricIdLinkedToTax;
        }

        this.uServiceFabricIdLinkedToTax = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-tax/Tax')
                .query(ui.queryUtils.edgesSelector({ serviceFabricId: true }), {
                    filter: { serviceFabricId: { _ne: '' } },
                })
                .execute(),
        ).map(line => line.serviceFabricId);

        return this.uServiceFabricIdLinkedToTax;
    }

    uTaxRateServiceFabricList: TaxRate[] | null;

    async getTaxRate(taxLegislationId: string): Promise<TaxRate[]> {
        if (this.uTaxRateServiceFabricList) {
            return this.uTaxRateServiceFabricList;
        }
        this.uTaxRateServiceFabricList = (await this.$.graph
            .node('@sage/xtrem-service-fabric/TaxRateRepository')
            .mutations.getTaxRate(
                {
                    code: true,
                    dateFrom: true,
                    dateTo: true,
                    description: true,
                    deductibleTaxPercentage: true,
                    id: true,
                    isActive: true,
                    isDeductibleTaxPercentageModifiable: true,
                    isReverseCharge: true,
                    isTaxOnTax: true,
                    order: true,
                    percentOfNetTaxAppliedFor: true,
                    taxCodeId: true,
                    taxItemTypeId: true,
                    taxPercentageGroupId: true,
                    taxRegionId: true,
                    taxTreatmentId: true,
                },
                { filterPropertie: 'taxLegislationId', filterValue: taxLegislationId },
            )
            .execute()) as TaxRate[];

        return this.uTaxRateServiceFabricList;
    }

    private async init() {
        if (this.legislation.value?.serviceFabricId) {
            const taxRateList = await this.getTaxRate(this.legislation.value.serviceFabricId);
            const serviceFabricIdLinkedToTax = await this.getServiceFabricIdLinkedToTax();

            this.taxRates.value = taxRateList

                .map(taxRate => ({
                    ...taxRate,
                    isAlreadySelected: serviceFabricIdLinkedToTax.includes(taxRate.id),
                    _id: taxRate.id,
                }))
                .filter(taxRateFilter => (this.isAlreadySelected.value ? !taxRateFilter.isAlreadySelected : true));
        }
    }
}
