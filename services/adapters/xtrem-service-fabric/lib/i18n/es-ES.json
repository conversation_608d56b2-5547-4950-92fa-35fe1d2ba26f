{"@sage/xtrem-service-fabric/activity__organisation_service_fabric__name": "Organización en Sage Service Fabric", "@sage/xtrem-service-fabric/activity__service_fabric_option_management__name": "Gestión de opciones de Sage Service Fabric", "@sage/xtrem-service-fabric/activity__service_fabric_tax_id_management__name": "Gestión de ids. fiscales en Sage Service Fabric", "@sage/xtrem-service-fabric/activity__tax_rate_repository__name": "Repositorio de tipos impositivos", "@sage/xtrem-service-fabric/add-new-rate": "Añadir tipo impositivo", "@sage/xtrem-service-fabric/add-new-rate-values": "<PERSON><PERSON><PERSON> por<PERSON> o valor de impuestos", "@sage/xtrem-service-fabric/company_id_validation_request": "Validación de id. de sociedad", "@sage/xtrem-service-fabric/country-not-available": "{{country}} no está disponible para la búsqueda y validación de identificadores.", "@sage/xtrem-service-fabric/create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/functions__messages__check_the_tax_id_number": "Revisa el NIF-IVA.", "@sage/xtrem-service-fabric/functions__messages__wrong_name": "Introduce el nombre {{shouldbeName}} para este NIF-IVA.", "@sage/xtrem-service-fabric/menu_item__organisation-service-fabric": "Sage Service Fabric", "@sage/xtrem-service-fabric/no-active-configuration": "No hay ninguna configuración activa.", "@sage/xtrem-service-fabric/no-lookup-url-api-configuration": "No hay ninguna URL de la API de búsqueda para Sage Service Fabric en la configuración.", "@sage/xtrem-service-fabric/no-sigining-key": "No hay ninguna clave de firma.", "@sage/xtrem-service-fabric/no-token": "Sin token", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricCompany": "Sociedad en Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricCompanyId": "Id. de sociedad en Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricExternalId": "Id. externo en Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__legislation_extension__property__serviceFabricId": "Id. en Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric": "<PERSON><PERSON>r desde Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__failed": "Error al crear desde Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__parameter__legislationID": "Id. de legislación", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__parameter__serviceFabricId": "Id. en Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__getServiceFabricLink": "Obtener vínculo a Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__getServiceFabricLink__failed": "Error al obtener el vínculo a Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_extension__property__serviceFabricId": "Id. en Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_value_extension__property__serviceFabricId": "Id. en Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation": "Validar id. de sociedad", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__failed": "Error al validar el id. de la sociedad", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__parameter__country": "<PERSON><PERSON>", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__parameter__taxID": "NIF-IVA", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createOrganisation": "Crear organización", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createOrganisation__failed": "Error al crear la organización", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany": "Crear sociedad en Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany__failed": "Error al crear la sociedad en Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany__parameter__id": "Id.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry": "<PERSON><PERSON> disponible", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry__failed": "Error de país disponible", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry__parameter__country": "<PERSON><PERSON>", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__resetOrganisation": "Restablecer organización", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__resetOrganisation__failed": "Error al restablecer la organización", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__saveToken": "Guardar token", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__saveToken__failed": "Error al guardar el token", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__node_name": "Organización en Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__adminEmail": "E-mail de administrador", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__apiURL": "URL de API", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__authURL": "URL de autenticación", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__connected": "Conexión establecida", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__defaultLanguage": "Idioma por defecto", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__externalId": "Id. externo", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__id": "Id.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__isActive": "Activa", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__isTokenValid": "Token válido", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__lookupURL": "URL de búsqueda", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__name": "Nombre", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__organisation": "Organización", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__organisationId": "Id. de organización", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__primaryCountry": "<PERSON><PERSON> principal", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__primarySigningKey": "Clave de firma principal", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__sageCrmId": "Id. en CRM de Sage", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__setupId": "Id. de parametrización", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__signingKey": "Clave de firma", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__token": "Token", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__tokenCreation": "Creación de token", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__xApplication": "Aplicación X", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__availableCountry": "<PERSON><PERSON> disponible", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__availableCountry__failed": "Error de país disponible", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__defaultInstance": "Instancia por defecto", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__defaultInstance__failed": "Error de instancia por defecto", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getCompanyList": "Obtener lista de sociedades", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getCompanyList__failed": "Error al obtener la lista de nodos", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getLinkedCompanyList": "Obtener lista de sociedades vinculadas", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getLinkedCompanyList__failed": "Error al obtener la lista de sociedades vinculadas", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getOrganisation": "Obtener organización", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getOrganisation__failed": "Error al obtener la organización", "@sage/xtrem-service-fabric/nodes__service_fabric_listener__node_name": "Proceso de escucha en Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__mutation__serviceOptionChange": "Cambiar opción de servicio", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__mutation__serviceOptionChange__failed": "Error al cambiar la opción de servicio", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__node_name": "Gestión de opciones de Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__query__isServiceOptionActiveFunction": "Service option active function", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__query__isServiceOptionActiveFunction__failed": "Is service option active function failed.", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__mutation__serviceOptionChange": "Cambiar opción de servicio", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__mutation__serviceOptionChange__failed": "Error al cambiar la opción de servicio", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__node_name": "Gestión de ids. fiscales en Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__query__isServiceOptionActiveFunction": "Service option active function", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__query__isServiceOptionActiveFunction__failed": "Is service option active function failed.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode": "Obtener código de impuesto", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__failed": "Error al obtener el código de impuesto", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__parameter__filterPropertie": "Filter properties", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__parameter__filterValue": "Valor de filtro", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxLegislation": "Obtener legislación fiscal", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxLegislation__failed": "Error al obtener la legislación fiscal", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage": "Obtener porcentaje de impuestos", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__failed": "Error al obtener el porcentaje de impuestos", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__parameter__filterPropertie": "Filter properties", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__parameter__filterValue": "Valor de filtro", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate": "Obtener tipo impositivo", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__failed": "Error al obtener el tipo impositivo", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__parameter__filterPropertie": "Filter properties", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__parameter__filterValue": "Valor de filtro", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRegion": "Obtener región fiscal", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRegion__failed": "Error al obtener la región fiscal", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation": "Obtener traducción", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__failed": "Error al obtener la traducción", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__parameter__filterPropertie": "Filter properties", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__parameter__filterValue": "Valor de filtro", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__node_name": "Repositorio de tipos impositivos", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__property__id": "Id.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__property__legislations": "Legislaciones", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__property__setupId": "Id. de parametrización", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__defaultInstance": "Instancia por defecto", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__defaultInstance__failed": "Error de instancia por defecto", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__healthCheck": "Comprobación de estado", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__healthCheck__failed": "Error al comprobar el estado", "@sage/xtrem-service-fabric/organisation-not-created": "Ha habido un error al crear la organización en Sage Service Fabric.", "@sage/xtrem-service-fabric/organisation-service-fabric": "Página de configuración", "@sage/xtrem-service-fabric/package__name": "Sage Service Fabric", "@sage/xtrem-service-fabric/page__title__service_fabric_configuration": "Configuración de Sage Service Fabric", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__isServiceFabricServiceOptionActive____title": "Activa", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__isServiceFabricServiceTaxIdActive____title": "Activo", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricBlock____title": "Configuración", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricConfigurationPage____title": "Página de configuración de Sage Service Fabric", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricSection____title": "Sage Service Fabric", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricTaxIdBlock____title": "Validación de id. fiscal", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog____title": "Seleccionar legislación para añadirla a Sage DMO", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__isAlreadySelected____title": "En uso", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title": "Estado", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__code": "Código", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__description": "Descripción", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__id": "Id.", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__mainSection____title": "General", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__ok____title": "Actualizar", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__simLegislation____title": "Legislación en Sage DMO", "@sage/xtrem-service-fabric/pages__link_service_fabric____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelHeaderSection____title": "Detalles", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelLegislationBlock____title": "Detalles de línea de legislación", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelLegislationBlockServiceFabric____title": "Legislación", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelLegislationSection____title": "Legislación", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelRegionSection____title": "Región", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxBlockServiceFabric____title": "Tipo impositivo", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxSection____title": "Impuestos", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxValueBlockServiceFabric____title": "Valor o porcentaje de impuesto", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxValueSection____title": "Valor de impuesto", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislation____lookupDialogTitle": "Seleccionar legislación", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationId____title": "Id.", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title___id": "sysID", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title__id": "Id.", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title__name": "Nombre", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title__serviceFabricCode": "Estado", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title__serviceFabricCode__2": "Código de Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationName____title": "Nombre", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__columns__postingClass__name__title": "Tipo", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__placeholder__postingClass__name": "Seleccionar clase contable", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__deductibleRate": "Tipo impositivo deducible", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__endDate": "<PERSON><PERSON> de fin", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__id": "Id.", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__isActive": "Activa", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__legalMention": "Aviso legal", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__legislation__serviceFabricCode": "Legislación en Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__name": "Nombre", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__name__2": "Nombre", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__rate": "Tipo impositivo", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricCode": "Estado", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricCode__2": "Código en Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId": "Estado", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__2": "Id. en Sage Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__3": "Estado de impuesto", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__4": "Id. en Sage Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__5": "Estado de valor de impuesto ", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__6": "Id. en Sage Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title": "Editar vínculo a legislación", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__2": "Editar vínculo a impuesto", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__3": "Eliminar impuesto", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__4": "Editar vínculo a valor de impuesto", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__emptyStateClickableText": "Sin impuestos", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationSection____title": "Legislación", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____columns__title__code": "Código", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____columns__title__countryCode": "Código de <PERSON>ís", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____columns__title__description": "Descripción", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____columns__title__id": "Id.", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____title": "Código de Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabricId____title": "Id. de legislación en Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__mainSection____title": "Legislación", "@sage/xtrem-service-fabric/pages__link_service_fabric__regionSection____title": "Región", "@sage/xtrem-service-fabric/pages__link_service_fabric__tax____title": "Impuesto", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxName____title": "Nombre", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxPercentageServiceFabric____columns__title__code": "Código", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxPercentageServiceFabric____columns__title__description": "Descripción", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxPercentageServiceFabric____columns__title__id": "Id.", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxPercentageServiceFabric____title": "Id. en <PERSON> Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxRateServiceFabric____columns__title__code": "Código", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxRateServiceFabric____columns__title__description": "Descripción", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxRateServiceFabric____columns__title__id": "Id.", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxRateServiceFabric____title": "Id. en <PERSON> Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxSection____title": "Impuestos", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxSysId____title": "Id. de sistema de impuestos", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueDeductibleRate____title": "Tipo impositivo deducible", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueName____title": "Nombre", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueRate____title": "Tipo impositivo", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueSysId____title": "Id. de sistema del valor de impuesto", "@sage/xtrem-service-fabric/pages__organisation_service_fabric____subtitle": "Configuración", "@sage/xtrem-service-fabric/pages__organisation_service_fabric____title": "Configuración ", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__adminEmail____title": "E-mail de administrador", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__apiSection____title": "API", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__apiURL____title": "URL de API", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__authURL____title": "URL de autenticación", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyId____title": "Id. de sociedad", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__externalId": "Id. externo", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__isCreated": "<PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__name": "Nombre", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____title": "Sociedades", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyLookupBlock____title": "Búsqueda y validación del id. fiscal y de sociedad", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companySection____title": "Sociedad", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__connected____title": "Conexión establecida", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__country____title": "<PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__createOrganisation____title": "Crear organización", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__defaultLanguage____title": "Idioma por defecto", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__execute_button_text": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__externalId____title": "Id. externo", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__getToken____title": "Obtener token", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__id____title": "Id.", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infos____title": "Información de sociedad", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infosTaxRateRepositoryApi____title": "Respuesta", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infosValidationApi____title": "Respuesta", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__isActive____title": "Activa", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__lookupURL____title": "URL de búsqueda de id. de sociedad", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__mainSection____title": "General", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__organisationId____title": "Id. de organización", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__primaryCountry____title": "<PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__refreshCompanyList____title": "Actualizar", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__resetOrganisation____title": "Restablecer organización", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__sageCrmId____title": "Id. en CRM de Sage", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__tax_rate_repository_execute_button_text": "Obtener legislación fiscal", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__taxRateRepositoryBlock____title": "Repositorio de tipos impositivos", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__xApplication____title": "Aplicación", "@sage/xtrem-service-fabric/pages__supplier_tax_id_not_valid": "VIES Sage Service Fabric: no se ha encontrado el NIF-IVA {{taxID}}.", "@sage/xtrem-service-fabric/pages__supplier_wrong_name": "VIES Sage Service Fabric: introduce {{shouldbeName}} en lugar de {{name}}.", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog____title": "Seleccionar porcentaje de impuestos y añadirlo a Sage DMO", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__isAlreadySelected____title": "En uso", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__mainSection____title": "General", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__simTaxValue____title": "Valor de impuesto en Sage DMO", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__tax____title": "Impuesto", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title": "Estado", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__code": "Código", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__dateFrom": "Fecha de inicio", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__dateTo": "<PERSON><PERSON> de fin", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__id": "Id.", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__percentage": "Po<PERSON>entaj<PERSON>", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog____title": "Añadir tipo impositivo a Sage DMO", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__isAlreadySelected____title": "En uso", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__legislation____lookupDialogTitle": "Seleccionar legislación", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__mainSection____title": "General", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__simTax____title": "Impuesto en Sage DMO", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title": "Estado", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__code": "Código", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__description": "Descripción", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__id": "Id.", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__isAlreadySelected": "Estado", "@sage/xtrem-service-fabric/permission__is_service_option_active_function__name": "Service option active function", "@sage/xtrem-service-fabric/permission__manage__name": "Gestionar", "@sage/xtrem-service-fabric/permission__service_option_change__name": "Cambiar opción de servicio", "@sage/xtrem-service-fabric/save": "Guardar", "@sage/xtrem-service-fabric/service_options__service_fabric_option__name": "Opción de Sage Service Fabric", "@sage/xtrem-service-fabric/service_options__service_fabric_tax_id__name": "Id. fiscal en Sage Service Fabric", "@sage/xtrem-service-fabric/service_options__service_fabric_tax_repository__name": "Repositorio fiscal en Sage Service Fabric", "@sage/xtrem-service-fabric/update": "Actualizar"}