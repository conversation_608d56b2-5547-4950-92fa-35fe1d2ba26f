{"@sage/xtrem-service-fabric/activity__organisation_service_fabric__name": "Organisation Sage Service Fabric", "@sage/xtrem-service-fabric/activity__service_fabric_option_management__name": "Optionsverwaltung Sage Service Fabric", "@sage/xtrem-service-fabric/activity__service_fabric_tax_id_management__name": "Verwaltung Steuer-ID Sage Service Fabric", "@sage/xtrem-service-fabric/activity__tax_rate_repository__name": "Verzeichnis Steuersätze", "@sage/xtrem-service-fabric/add-new-rate": "Satz hinzufügen", "@sage/xtrem-service-fabric/add-new-rate-values": "Satzprozentsatz/Werte hinzufügen", "@sage/xtrem-service-fabric/company_id_validation_request": "Freigabe Unternehmens-ID", "@sage/xtrem-service-fabric/country-not-available": "{{country}} ist nicht verfügbar für die Freigabe und Suche der ID.", "@sage/xtrem-service-fabric/create": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/functions__messages__check_the_tax_id_number": "Überprüfen Sie die Steueridentifikationsnummer.", "@sage/xtrem-service-fabric/functions__messages__wrong_name": "<PERSON><PERSON> müssen den Namen {{shouldbeName}} für diese Steuer-ID angeben.", "@sage/xtrem-service-fabric/menu_item__organisation-service-fabric": "Sage Service Fabric", "@sage/xtrem-service-fabric/no-active-configuration": "Keine aktive Konfiguration", "@sage/xtrem-service-fabric/no-lookup-url-api-configuration": "Keine URL für die API-Suche Sage Service Fabric in der Konfiguration", "@sage/xtrem-service-fabric/no-sigining-key": "<PERSON><PERSON>.", "@sage/xtrem-service-fabric/no-token": "<PERSON><PERSON>", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricCompany": "Unternehmen Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricCompanyId": "Unternehmens-ID Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricExternalId": "Externe ID Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__legislation_extension__property__serviceFabricId": "ID Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric": "Aus Sage Service Fabric <PERSON>", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__failed": "Aus Service Fabric erstellen fehlgeschlagen.", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__parameter__legislationID": "Rechtsordnungs-ID", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__parameter__serviceFabricId": "ID Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__getServiceFabricLink": "Sage Service Fabric-Verknüpfung abrufen", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__getServiceFabricLink__failed": "Sage Service Fabric-Verknüpfung abrufen fehlgeschlagen.", "@sage/xtrem-service-fabric/node-extensions__tax_extension__property__serviceFabricId": "ID Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_value_extension__property__serviceFabricId": "ID Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport": "Export", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation": "Freigabe Unternehmens-ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__failed": "Freigabe Unternehmens-ID fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__parameter__country": "Land", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__parameter__taxID": "Steuer-ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createOrganisation": "Organisation erstellen", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createOrganisation__failed": "Organisation erstellen fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany": "Unternehmen Sage Service Fabric <PERSON>llen", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany__failed": "Unternehmen Service Fabric erstellen fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany__parameter__id": "ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry": "Verfügbares Land", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry__failed": "Ist verfügbares Land fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry__parameter__country": "Land", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__resetOrganisation": "Organisation zurücksetzen", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__resetOrganisation__failed": "Organisation zurücksetzen fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__saveToken": "Token speichern", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__saveToken__failed": "Token speichern fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__node_name": "Organisation Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__adminEmail": "E-Mail-<PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__apiURL": "URL API", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__authURL": "URL Authentifizierung", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__connected": "Verbunden", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__defaultLanguage": "Standardsprache", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__externalId": "Externe ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__id": "ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__isActive": "Aktiv", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__isTokenValid": "Gültigkeit Token", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__lookupURL": "URL Suche", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__name": "Name", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__organisation": "Organisation", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__organisationId": "ID Organisation", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__primaryCountry": "Primäres Land", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__primarySigningKey": "Primärer Signaturschlüssel", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__sageCrmId": "ID Sage CRM", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__setupId": "ID Einstellungen", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__signingKey": "Zeichnungsschlüssel", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__token": "Token", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__tokenCreation": "Token-Erstellung", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__xApplication": "X Anwendung", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__availableCountry": "Verfügbares Land", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__availableCountry__failed": "Verfügbares Land fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__defaultInstance": "Standardinstanz", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__defaultInstance__failed": "Standardinstanz fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getCompanyList": "Unternehmensliste abrufen", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getCompanyList__failed": "Unternehmensliste abrufen fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getLinkedCompanyList": "Verknüpfte Unternehmensliste abrufen", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getLinkedCompanyList__failed": "Verknüpfte Unternehmensliste abrufen fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getOrganisation": "Organisation abrufen", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getOrganisation__failed": "Organisation abrufen fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__service_fabric_listener__node_name": "Listener Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport": "Export", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__mutation__serviceOptionChange": "Änderung Dienstoption", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__mutation__serviceOptionChange__failed": "Änderung Dienstoption fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__node_name": "Optionsverwaltung Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__query__isServiceOptionActiveFunction": "Funktion Dienstoption aktiv", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__query__isServiceOptionActiveFunction__failed": "Ist Funktion Dienstoption aktiv fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport": "Export", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__mutation__serviceOptionChange": "Änderung Dienstoption", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__mutation__serviceOptionChange__failed": "Änderung Dienstoption fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__node_name": "Verwaltung Steuer-ID Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__query__isServiceOptionActiveFunction": "Funktion Dienstoption aktiv", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__query__isServiceOptionActiveFunction__failed": "Ist Funktion Dienstoption aktiv fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport": "Export", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode": "Steuercode abrufen", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__failed": "Steuercode abrufen fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__parameter__filterPropertie": "Filtereigenschaften", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__parameter__filterValue": "Filterwert", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxLegislation": "Rechtsordnung Steuer abrufen", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxLegislation__failed": "Rechtsordnung Steuer abrufen fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage": "Prozentsatz abrufen", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__failed": "Prozentsatz abrufen fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__parameter__filterPropertie": "Filtereigenschaften", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__parameter__filterValue": "Filterwert", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate": "Steuersatz abrufen", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__failed": "Steuersatz abrufen fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__parameter__filterPropertie": "Filtereigenschaften", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__parameter__filterValue": "Filterwert", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRegion": "Steuerregion abrufen", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRegion__failed": "Steuerregion abrufen fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation": "Übersetzung abrufen", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__failed": "Übersetzung abrufen fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__parameter__filterPropertie": "Filtereigenschaften", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__parameter__filterValue": "Filterwert", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__node_name": "Verzeichnis Steuersätze", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__property__id": "ID", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__property__legislations": "Rechtsordnungen", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__property__setupId": "ID Einstellungen", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__defaultInstance": "Standardinstanz", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__defaultInstance__failed": "Standardinstanz fehlgeschlagen.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__healthCheck": "Gesundheitsprüfung", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__healthCheck__failed": "Gesundheitsprüfung fehlgeschlagen.", "@sage/xtrem-service-fabric/organisation-not-created": "Die Erstellung der Sage Service Fabric-Organisation ist fehlgeschlagen.", "@sage/xtrem-service-fabric/organisation-service-fabric": "Konfigurationsseite", "@sage/xtrem-service-fabric/package__name": "Sage Service Fabric", "@sage/xtrem-service-fabric/page__title__service_fabric_configuration": "Konfiguration Sage Service Fabric", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__isServiceFabricServiceOptionActive____title": "Aktiv", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__isServiceFabricServiceTaxIdActive____title": "Aktiv", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricBlock____title": "Konfiguration", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricConfigurationPage____title": "Konfigurationsseite Sage Service Fabric", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricSection____title": "Sage Service Fabric", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricTaxIdBlock____title": "Freigabe Steuer-ID", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog____title": "Rechtsordnung auswählen und in Sage DMO hinzufügen", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__isAlreadySelected____title": "Bereits ausgewählt", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title": "Status", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__description": "Bezeichnung", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__id": "ID", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__mainSection____title": "Allgemein", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__ok____title": "Aktualisieren", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__simLegislation____title": "Rechtsordnung SDMO", "@sage/xtrem-service-fabric/pages__link_service_fabric____title": "Verknüpfungen", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelHeaderSection____title": "Details", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelLegislationBlock____title": "Details Rechtsordnungszeile", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelLegislationBlockServiceFabric____title": "Rechtsordnung", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelLegislationSection____title": "Rechtsordnung", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelRegionSection____title": "Region", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxBlockServiceFabric____title": "Steuersatz", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxValueBlockServiceFabric____title": "Steuerwert oder Prozentsatz", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxValueSection____title": "Steuerwert", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislation____lookupDialogTitle": "Rechtsordnung auswählen", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationId____title": "ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title___id": "sysID", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title__id": "ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title__name": "Name", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title__serviceFabricCode": "Status", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title__serviceFabricCode__2": "Service Fabric-Code", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationName____title": "Name", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__columns__postingClass__name__title": "<PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__placeholder__postingClass__name": "Buchungsklasse auswählen", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__deductibleRate": "Abzugsfähiger Steuersatz", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__endDate": "Enddatum", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__id": "ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__isActive": "Aktiv", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__legalMention": "Rechtshinweis", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__legislation__serviceFabricCode": "Rechtsordnung Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__name": "Name", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__name__2": "Name", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__rate": "Satz", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricCode": "Status", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricCode__2": "Service Fabric-Code", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId": "Status", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__2": "ID Sage Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__3": "Steu<PERSON>tat<PERSON>", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__4": "ID Sage Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__5": "Status Steuerwert ", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__6": "ID Sage Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title": "Rechtsordnungslink bearbeiten", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__2": "Steuerlink bearbeiten", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__3": "Steuer löschen", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__4": "Steuerwertlink bearbeiten", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__emptyStateClickableText": "<PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationSection____title": "Rechtsordnung", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____columns__title__countryCode": "Ländercode", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____columns__title__description": "Bezeichnung", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____columns__title__id": "ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____title": "Service Fabric-Code", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabricId____title": "Rechtsordnungs-ID Sage Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__mainSection____title": "Rechtsordnung", "@sage/xtrem-service-fabric/pages__link_service_fabric__regionSection____title": "Region", "@sage/xtrem-service-fabric/pages__link_service_fabric__tax____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxName____title": "Name", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxPercentageServiceFabric____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxPercentageServiceFabric____columns__title__description": "Bezeichnung", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxPercentageServiceFabric____columns__title__id": "ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxPercentageServiceFabric____title": "ID Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxRateServiceFabric____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxRateServiceFabric____columns__title__description": "Bezeichnung", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxRateServiceFabric____columns__title__id": "ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxRateServiceFabric____title": "ID Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxSysId____title": "Steuersystem-ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueDeductibleRate____title": "Abzugsfähiger Steuersatz", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueName____title": "Name", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueRate____title": "Satz", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueSysId____title": "Steuerwertsystem-ID", "@sage/xtrem-service-fabric/pages__organisation_service_fabric____subtitle": "Konfiguration", "@sage/xtrem-service-fabric/pages__organisation_service_fabric____title": "Konfiguration ", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__adminEmail____title": "E-Mail-<PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__apiSection____title": "API", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__apiURL____title": "URL API", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__authURL____title": "URL Authentifizierung", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyId____title": "Unternehmens-ID", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__externalId": "Externe ID", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__isCreated": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__name": "Name", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____title": "Unternehmen", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyLookupBlock____title": "Suche und Freigabe Unternehmens- und Steuer-ID", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companySection____title": "Unternehmen", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__connected____title": "Verbunden", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__country____title": "Land", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__createOrganisation____title": "Organisation erstellen", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__defaultLanguage____title": "Standardsprache", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__execute_button_text": "Ausführen", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__externalId____title": "Externe ID", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__getToken____title": "Token abrufen", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__id____title": "ID", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infos____title": "Info Unternehmen", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infosTaxRateRepositoryApi____title": "Antwort", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infosValidationApi____title": "Antwort", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__isActive____title": "Aktiv", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__lookupURL____title": "URL Suche Unternehmens-ID", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__mainSection____title": "Allgemein", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__organisationId____title": "ID Organisation", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__primaryCountry____title": "Land", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__refreshCompanyList____title": "Aktualisieren", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__resetOrganisation____title": "Organisation zurücksetzen", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__sageCrmId____title": "ID Sage CRM", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__tax_rate_repository_execute_button_text": "Rechtsordnung Steuer abrufen", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__taxRateRepositoryBlock____title": "Verzeichnis Steuersätze", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__xApplication____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__supplier_tax_id_not_valid": "MIAS Sage Service Fabric: Die Steuer-ID {{taxID}} wurde nicht gefunden.", "@sage/xtrem-service-fabric/pages__supplier_wrong_name": "MIAS Sage Service Fabric: <PERSON><PERSON><PERSON><PERSON> {{shouldbeName}} anstatt {{name}}.", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog____title": "Steuerprozentsatz auswählen und in Sage DMO hinzufügen", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__isAlreadySelected____title": "Bereits ausgewählt", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__mainSection____title": "Allgemein", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__simTaxValue____title": "Steuerwert SDMO", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__tax____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title": "Status", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__dateFrom": "Startdatum", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__dateTo": "Enddatum", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__id": "ID", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__percentage": "Prozentsatz", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog____title": "Steuersatz in Sage DMO hinzufügen", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__isAlreadySelected____title": "Bereits ausgewählt", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__legislation____lookupDialogTitle": "Rechtsordnung auswählen", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__mainSection____title": "Allgemein", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__simTax____title": "Steuer SDMO", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title": "Status", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__description": "Bezeichnung", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__id": "ID", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__isAlreadySelected": "Status", "@sage/xtrem-service-fabric/permission__is_service_option_active_function__name": "Ist Funktion Dienstoption aktiv", "@sage/xtrem-service-fabric/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/permission__service_option_change__name": "Änderung Dienstoption", "@sage/xtrem-service-fabric/save": "Speichern", "@sage/xtrem-service-fabric/service_options__service_fabric_option__name": "Sage Service Fabric-Option", "@sage/xtrem-service-fabric/service_options__service_fabric_tax_id__name": "Sage Service Fabric-Steuer-ID", "@sage/xtrem-service-fabric/service_options__service_fabric_tax_repository__name": "Sage Service Fabric-Steuerverzeichnis", "@sage/xtrem-service-fabric/update": "Aktualisieren"}