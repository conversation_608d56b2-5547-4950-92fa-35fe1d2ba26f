{"@sage/xtrem-service-fabric/activity__organisation_service_fabric__name": "Organisation Sage Service Fabric", "@sage/xtrem-service-fabric/activity__service_fabric_option_management__name": "Gestion des options Sage Service Fabric", "@sage/xtrem-service-fabric/activity__service_fabric_tax_id_management__name": "Gestion des codes de TVA Sage Service Fabric", "@sage/xtrem-service-fabric/activity__tax_rate_repository__name": "Répertoire de taux de taxe", "@sage/xtrem-service-fabric/add-new-rate": "A<PERSON>ter taux", "@sage/xtrem-service-fabric/add-new-rate-values": "Ajouter pourcentage / valeurs de taux", "@sage/xtrem-service-fabric/company_id_validation_request": "Validation code société", "@sage/xtrem-service-fabric/country-not-available": "{{country}} est indisponible pour la recherche et validation de codes.", "@sage/xtrem-service-fabric/create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/functions__messages__check_the_tax_id_number": "Vérifiez le numéro de TVA.", "@sage/xtrem-service-fabric/functions__messages__wrong_name": "<PERSON><PERSON> de<PERSON> renseigner le nom {{shouldbeName}} pour ce n° de TVA.", "@sage/xtrem-service-fabric/menu_item__organisation-service-fabric": "Sage Service Fabric", "@sage/xtrem-service-fabric/no-active-configuration": "Aucune configuration active", "@sage/xtrem-service-fabric/no-lookup-url-api-configuration": "Pas d’URL pour l’API de recherche Sage Service Fabric dans la configuration.", "@sage/xtrem-service-fabric/no-sigining-key": "Pas de clé de signature", "@sage/xtrem-service-fabric/no-token": "Aucun jeton", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricCompany": "Société Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricCompanyId": "Code législation Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricExternalId": "Code externe Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__legislation_extension__property__serviceFabricId": "Code Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric": "Créer à partir de Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__failed": "Échec de la création à partir de Service Fabric.", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__parameter__legislationID": "Code législation", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__parameter__serviceFabricId": "Code Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__getServiceFabricLink": "Obtenir le lien Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__getServiceFabricLink__failed": "Échec de l'obtention du lien Service Fabric.", "@sage/xtrem-service-fabric/node-extensions__tax_extension__property__serviceFabricId": "Code Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_value_extension__property__serviceFabricId": "Code Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation": "Validation code société", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__failed": "Échec de validation des codes société.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__parameter__country": "Pays", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__parameter__taxID": "N° de TVA", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createOrganisation": "Créer l'organisation", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createOrganisation__failed": "Échec de création d'organisation", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany": "Créer la société Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany__failed": "Échec de création de la société Service Fabric.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany__parameter__id": "Code", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry": "Pays disponible", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry__failed": "Échec de pays disponible.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry__parameter__country": "Pays", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__resetOrganisation": "Redéfinir l'organisation", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__resetOrganisation__failed": "Échec de création d'organisation.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__saveToken": "Enregis<PERSON>r le jeton", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__saveToken__failed": "Échec de sauvegarde du jeton", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__node_name": "Organisation Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__adminEmail": "E-mail admin", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__apiURL": "URL API", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__authURL": "Authentification URL", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__connected": "Connectée", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__defaultLanguage": "Langue par défaut", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__externalId": "Code externe", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__id": "Code", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__isActive": "Active", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__isTokenValid": "Validité des jetons", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__lookupURL": "URL de recherche", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__name": "Nom", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__organisation": "Organisation", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__organisationId": "Code d'organisation", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__primaryCountry": "Pays principal", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__primarySigningKey": "Clé de signature principale", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__sageCrmId": "Code Sage CRM", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__setupId": "Code paramétrage", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__signingKey": "Clé de signature", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__token": "<PERSON><PERSON>", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__tokenCreation": "Création de jetons", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__xApplication": "Application X", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__availableCountry": "Pays disponible", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__availableCountry__failed": "Échec de pays disponible.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__defaultInstance": "Instance par défaut", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__defaultInstance__failed": "Échec de l'instance par défaut.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getCompanyList": "Obtenir la liste des sociétés", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getCompanyList__failed": "Échec de récupération de la liste des sociétés.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getLinkedCompanyList": "Obtenir la liste des sociétés liées", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getLinkedCompanyList__failed": "Échec de l'obtention de la liste des sociétés liées.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getOrganisation": "Obtenir l'organisation", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getOrganisation__failed": "Échec d'obtention des traductions.", "@sage/xtrem-service-fabric/nodes__service_fabric_listener__node_name": "Listener Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__mutation__serviceOptionChange": "Modification option de service", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__mutation__serviceOptionChange__failed": "Échec de modification des options de service.", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__node_name": "Gestion des options Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__query__isServiceOptionActiveFunction": "Fonction options de service active", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__query__isServiceOptionActiveFunction__failed": "Échec de la fonction d'options de service active.", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__mutation__serviceOptionChange": "Modification option de service", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__mutation__serviceOptionChange__failed": "Échec de modification des options de service.", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__node_name": "Gestion des codes de TVA Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__query__isServiceOptionActiveFunction": "Fonction options de service active", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__query__isServiceOptionActiveFunction__failed": "Échec de la fonction d'options de service active.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode": "Obtenir code taxe", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__failed": "Échec d'obtention des codes taxe.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__parameter__filterPropertie": "Propriété des filtres", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__parameter__filterValue": "<PERSON>ur de filtre", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxLegislation": "Obtenir la législation fiscale", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxLegislation__failed": "Échec d'obtention des traductions.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage": "Obtenir le pourcentage de taxe", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__failed": "Échec d'obtention des pourcentages de taxe.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__parameter__filterPropertie": "Propriété des filtres", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__parameter__filterValue": "<PERSON>ur de filtre", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate": "Obt<PERSON><PERSON> le taux de taxe", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__failed": "Échec d'obtention des taux de taxe.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__parameter__filterPropertie": "Propriété des filtres", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__parameter__filterValue": "<PERSON>ur de filtre", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRegion": "Obtenir la région de taxe", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRegion__failed": "Échec d'obtention des régions de taxe.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation": "Obtenir la traduction", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__failed": "Échec d'obtention des traductions.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__parameter__filterPropertie": "Propriété des filtres", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__parameter__filterValue": "<PERSON>ur de filtre", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__node_name": "Répertoire de taux de taxe", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__property__id": "Code", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__property__legislations": "Législations", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__property__setupId": "Code paramétrage", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__defaultInstance": "Instance par défaut", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__defaultInstance__failed": "Échec de l'instance par défaut.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__healthCheck": "Contrôle santé", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__healthCheck__failed": "Échec du contrôle santé.", "@sage/xtrem-service-fabric/organisation-not-created": "L'organisation Sage Service Fabric n'a pas été créée.", "@sage/xtrem-service-fabric/organisation-service-fabric": "Page de configuration", "@sage/xtrem-service-fabric/package__name": "Sage Service Fabric", "@sage/xtrem-service-fabric/page__title__service_fabric_configuration": "Configuration Sage Service Fabric", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__isServiceFabricServiceOptionActive____title": "Active", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__isServiceFabricServiceTaxIdActive____title": "Active", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricBlock____title": "Configuration", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricConfigurationPage____title": "Page de configuration Sage Service Fabric", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricSection____title": "Sage Service Fabric", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricTaxIdBlock____title": "Validation du n° de TVA", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog____title": "Sélectionner la législation à ajouter à Sage DMO", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__isAlreadySelected____title": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title": "Statut", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__description": "Description", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__id": "Code", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__mainSection____title": "Général", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__ok____title": "Mettre à jour", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__simLegislation____title": "Législation Sage DMO", "@sage/xtrem-service-fabric/pages__link_service_fabric____title": "<PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelHeaderSection____title": "Détails", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelLegislationBlock____title": "Détails lignes législation", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelLegislationBlockServiceFabric____title": "Législation", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelLegislationSection____title": "Législation", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelRegionSection____title": "Région", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxBlockServiceFabric____title": "Taux de taxe", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxSection____title": "Taxe", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxValueBlockServiceFabric____title": "Valeur ou pourcentage de taxe", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxValueSection____title": "Valeur de taxe", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislation____lookupDialogTitle": "Sélectionner la législation", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationId____title": "Code", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title___id": "sysID", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title__id": "ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title__name": "Nom", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title__serviceFabricCode": "Statut", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationList____columns__title__serviceFabricCode__2": "Code Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationName____title": "Nom", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__columns__postingClass__name__title": "Type", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__placeholder__postingClass__name": "Sélectionner la classe comptabilisation", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__deductibleRate": "Taux de taxe déductible", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__endDate": "Date fin", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__id": "Code", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__isActive": "Active", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__legalMention": "Mention légale", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__legislation__serviceFabricCode": "Législation Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__name": "Nom", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__name__2": "Nom", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__rate": "<PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricCode": "Statut", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricCode__2": "Code Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId": "Statut", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__2": "Code Sage Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__3": "Statut taxe", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__4": "Code Sage Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__5": "Statut valeur taxe ", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__6": "Code Sage Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title": "Modifier lien législation", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__2": "Modifier lien taxe", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__3": "Supprimer taxe", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__4": "Modifier lien valeur taxe", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__emptyStateClickableText": "Aucune taxe", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationSection____title": "Législation", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____columns__title__countryCode": "Code pays", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____columns__title__description": "Description", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____columns__title__id": "Code", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabric____title": "Code Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabricId____title": "Code législation Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__mainSection____title": "Législation", "@sage/xtrem-service-fabric/pages__link_service_fabric__regionSection____title": "Région", "@sage/xtrem-service-fabric/pages__link_service_fabric__tax____title": "Taxe", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxName____title": "Nom", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxPercentageServiceFabric____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxPercentageServiceFabric____columns__title__description": "Description", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxPercentageServiceFabric____columns__title__id": "Code", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxPercentageServiceFabric____title": "Code Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxRateServiceFabric____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxRateServiceFabric____columns__title__description": "Description", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxRateServiceFabric____columns__title__id": "Code", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxRateServiceFabric____title": "Code Service Fabric", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxSection____title": "Taxe", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxSysId____title": "Code système taxe", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueDeductibleRate____title": "Taux de taxe déductible", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueName____title": "Nom", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueRate____title": "Cours", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueSysId____title": "Code système valeur taxe", "@sage/xtrem-service-fabric/pages__organisation_service_fabric____subtitle": "Configuration", "@sage/xtrem-service-fabric/pages__organisation_service_fabric____title": "Configuration ", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__adminEmail____title": "E-mail admin", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__apiSection____title": "API", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__apiURL____title": "URL API", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__authURL____title": "URL d'authentification", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyId____title": "Code société", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__externalId": "Code externe", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__isCreated": "<PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__name": "Nom", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____title": "Sociétés", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyLookupBlock____title": "Recherche et validation société et n° de TVA", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companySection____title": "Société", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__connected____title": "Connectée", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__country____title": "Pays", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__createOrganisation____title": "Créer l'organisation", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__defaultLanguage____title": "Langue par défaut", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__execute_button_text": "Exécuter", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__externalId____title": "Code externe", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__getToken____title": "<PERSON><PERSON><PERSON><PERSON> le jeton", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__id____title": "Code", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infos____title": "<PERSON><PERSON>", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infosTaxRateRepositoryApi____title": "Réponse", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infosValidationApi____title": "Réponse", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__isActive____title": "Active", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__lookupURL____title": "URL de recherche de code société", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__mainSection____title": "Général", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__organisationId____title": "Code organisation", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__primaryCountry____title": "Pays", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__refreshCompanyList____title": "Actualiser", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__resetOrganisation____title": "Redéfinir l'organisation", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__sageCrmId____title": "Code Sage CRM", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__tax_rate_repository_execute_button_text": "Obtenir la législation fiscale", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__taxRateRepositoryBlock____title": "Répertoire de taux de taxe", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__xApplication____title": "Application", "@sage/xtrem-service-fabric/pages__supplier_tax_id_not_valid": "Sage Service Fabric VIES : le numéro de TVA {{taxID}} est introuvable.", "@sage/xtrem-service-fabric/pages__supplier_wrong_name": "Sage Service Fabric VIES : renseigner {{shouldbeName}} au lieu de {{name}}.", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog____title": "Sélectionner le pourcentage de taxe à ajouter à Sage DMO", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__isAlreadySelected____title": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__mainSection____title": "Général", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__simTaxValue____title": "Valeur taxe Sage DMO", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__tax____title": "Taxe", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title": "Statut", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__dateFrom": "Date début", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__dateTo": "Date fin", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__id": "Code", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__percentage": "Pourcentage", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog____title": "Ajouter taux à Sage DMO", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__isAlreadySelected____title": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__legislation____lookupDialogTitle": "Sélectionner la législation", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__mainSection____title": "Général", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__simTax____title": "Taxe Sage DMO", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title": "Statut", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__description": "Description", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__id": "Code", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__isAlreadySelected": "Statut", "@sage/xtrem-service-fabric/permission__is_service_option_active_function__name": "Fonction option de service active", "@sage/xtrem-service-fabric/permission__manage__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-service-fabric/permission__service_option_change__name": "Modification option de service", "@sage/xtrem-service-fabric/save": "Enregistrer", "@sage/xtrem-service-fabric/service_options__service_fabric_option__name": "Option Sage Service Fabric", "@sage/xtrem-service-fabric/service_options__service_fabric_tax_id__name": "Code de TVA Sage Service Fabric", "@sage/xtrem-service-fabric/service_options__service_fabric_tax_repository__name": "Répertoire des taxes Sage Service Fabric", "@sage/xtrem-service-fabric/update": "Mettre à jour"}