{"@sage/xtrem-service-fabric/activity__organisation_service_fabric__name": "", "@sage/xtrem-service-fabric/activity__tax_rate_repository__name": "", "@sage/xtrem-service-fabric/add-new-rate": "", "@sage/xtrem-service-fabric/add-new-rate-values": "", "@sage/xtrem-service-fabric/company_id_validation_request": "", "@sage/xtrem-service-fabric/country-not-available": "", "@sage/xtrem-service-fabric/create": "", "@sage/xtrem-service-fabric/functions__messages__check_the_tax_id_number": "", "@sage/xtrem-service-fabric/functions__messages__wrong_name": "", "@sage/xtrem-service-fabric/menu_item__organisation-service-fabric": "", "@sage/xtrem-service-fabric/no-active-configuration": "", "@sage/xtrem-service-fabric/no-lookup-url-api-configuration": "", "@sage/xtrem-service-fabric/no-sigining-key": "", "@sage/xtrem-service-fabric/no-token": "", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricCompany": "", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricCompanyId": "", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricExternalId": "", "@sage/xtrem-service-fabric/node-extensions__legislation_extension__property__serviceFabricId": "", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric": "", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__failed": "", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__parameter__legislationID": "", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__parameter__serviceFabricId": "", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__getServiceFabricLink": "", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__getServiceFabricLink__failed": "", "@sage/xtrem-service-fabric/node-extensions__tax_extension__property__serviceFabricId": "", "@sage/xtrem-service-fabric/node-extensions__tax_value_extension__property__serviceFabricId": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__failed": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__parameter__country": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__parameter__taxID": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createOrganisation": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createOrganisation__failed": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany__failed": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany__parameter__id": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry__failed": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry__parameter__country": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__resetOrganisation": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__resetOrganisation__failed": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__saveToken": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__saveToken__failed": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__node_name": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__adminEmail": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__apiURL": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__authURL": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__connected": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__defaultLanguage": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__externalId": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__id": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__isActive": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__isTokenValid": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__lookupURL": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__name": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__organisation": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__organisationId": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__primaryCountry": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__primarySigningKey": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__sageCrmId": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__signingKey": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__token": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__tokenCreation": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__xApplication": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__availableCountry": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__availableCountry__failed": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__defaultInstance": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__defaultInstance__failed": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getCompanyList": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getCompanyList__failed": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getLinkedCompanyList": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getLinkedCompanyList__failed": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getOrganisation": "", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getOrganisation__failed": "", "@sage/xtrem-service-fabric/nodes__service_fabric_listener__node_name": "", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport": "", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__mutation__serviceOptionChange": "", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__mutation__serviceOptionChange__failed": "", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__node_name": "", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__query__isServiceOptionActiveFunction": "", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__query__isServiceOptionActiveFunction__failed": "", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport": "", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__mutation__serviceOptionChange": "", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__mutation__serviceOptionChange__failed": "", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__node_name": "", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__query__isServiceOptionActiveFunction": "", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__query__isServiceOptionActiveFunction__failed": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__failed": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__parameter__filterPropertie": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__parameter__filterValue": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxLegislation": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxLegislation__failed": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__failed": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__parameter__filterPropertie": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__parameter__filterValue": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__failed": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__parameter__filterPropertie": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__parameter__filterValue": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRegion": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRegion__failed": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__failed": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__parameter__filterPropertie": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__parameter__filterValue": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__node_name": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__property__id": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__property__legislations": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__defaultInstance": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__defaultInstance__failed": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__healthCheck": "", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__healthCheck__failed": "", "@sage/xtrem-service-fabric/organisation-not-created": "", "@sage/xtrem-service-fabric/organisation-service-fabric": "", "@sage/xtrem-service-fabric/package__name": "Sage服务结构", "@sage/xtrem-service-fabric/page__title__service_fabric_configuration": "", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__isServiceFabricServiceOptionActive____title": "", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__isServiceFabricServiceTaxIdActive____title": "", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricBlock____title": "", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricConfigurationPage____title": "", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricSection____title": "", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricTaxIdBlock____title": "", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog____title": "", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__isAlreadySelected____title": "", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title": "", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__code": "", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__description": "", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__id": "", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__mainSection____title": "", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__ok____title": "", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__simLegislation____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelHeaderSection____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelLegislationBlockServiceFabric____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelLegislationSection____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelRegionSection____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxBlockServiceFabric____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxSection____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxValueBlockServiceFabric____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxValueSection____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislation____lookupDialogTitle": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationId____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationName____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__columns__postingClass__name__title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__placeholder__postingClass__name": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__deductibleRate": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__endDate": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__id": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__isActive": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__legalMention": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__name": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__name__2": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__rate": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__2": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__3": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__4": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__5": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__6": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__2": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__3": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__4": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__emptyStateClickableText": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabricId____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__mainSection____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__tax____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxName____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxSysId____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueDeductibleRate____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueRate____title": "", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueSysId____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__adminEmail____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__apiSection____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__apiURL____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__authURL____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyId____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__externalId": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__isCreated": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__name": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____dropdownActions__title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyLookupBlock____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companySection____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__connected____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__country____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__createOrganisation____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__defaultLanguage____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__execute_button_text": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__externalId____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__getToken____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__id____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infos____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infosTaxRateRepositoryApi____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infosValidationApi____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__isActive____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__lookupURL____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__mainSection____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__organisationId____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__primaryCountry____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__refreshCompanyList____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__resetOrganisation____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__sageCrmId____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__tax_rate_repository_execute_button_text": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__taxRateRepositoryBlock____title": "", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__xApplication____title": "", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog____title": "", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__isAlreadySelected____title": "", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__mainSection____title": "", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__simTaxValue____title": "", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__tax____title": "", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title": "", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__code": "", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__dateFrom": "", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__dateTo": "", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__id": "", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__percentage": "", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog____title": "", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__isAlreadySelected____title": "", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__legislation____lookupDialogTitle": "", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__mainSection____title": "", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__simTax____title": "", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__code": "", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__description": "", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__id": "", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__isAlreadySelected": "", "@sage/xtrem-service-fabric/permission__manage__name": "", "@sage/xtrem-service-fabric/service_options__service_fabric_option__name": "", "@sage/xtrem-service-fabric/service_options__service_fabric_tax_id__name": "", "@sage/xtrem-service-fabric/service_options__service_fabric_tax_repository__name": "", "@sage/xtrem-service-fabric/update": ""}