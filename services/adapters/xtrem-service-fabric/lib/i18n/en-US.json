{"@sage/xtrem-service-fabric/activity__organisation_service_fabric__name": "Organization Sage Service Fabric", "@sage/xtrem-service-fabric/activity__tax_rate_repository__name": "Tax rate repository", "@sage/xtrem-service-fabric/add-new-rate": "Add rate", "@sage/xtrem-service-fabric/add-new-rate-values": "Add rate percentage/values", "@sage/xtrem-service-fabric/company_id_validation_request": "Company ID validation", "@sage/xtrem-service-fabric/country-not-available": "{{country}} is not available for the ID validation and lookup.", "@sage/xtrem-service-fabric/create": "Create", "@sage/xtrem-service-fabric/functions__messages__check_the_tax_id_number": "Check the tax ID number.", "@sage/xtrem-service-fabric/functions__messages__wrong_name": "You need to enter the name {{shouldbeName}} for this tax ID.", "@sage/xtrem-service-fabric/menu_item__organisation-service-fabric": "Sage Service Fabric", "@sage/xtrem-service-fabric/no-active-configuration": "No active configuration", "@sage/xtrem-service-fabric/no-lookup-url-api-configuration": "No URL for the Sage Service Fabric lookup API in the configuration", "@sage/xtrem-service-fabric/no-sigining-key": "No signing key.", "@sage/xtrem-service-fabric/no-token": "No token", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricCompany": "Sage Service Fabric company", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricCompanyId": "Sage Service Fabric company ID", "@sage/xtrem-service-fabric/node-extensions__company_extension__property__serviceFabricExternalId": "Sage Service Fabric external ID", "@sage/xtrem-service-fabric/node-extensions__legislation_extension__property__serviceFabricId": "Sage Service Fabric ID", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric": "Create from Sage Service Fabric", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__failed": "Create from Service Fabric failed.", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__parameter__legislationID": "Legislation ID", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__createFromServiceFabric__parameter__serviceFabricId": "Sage Service Fabric ID", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__getServiceFabricLink": "Get Sage Service Fabric link", "@sage/xtrem-service-fabric/node-extensions__tax_extension__mutation__getServiceFabricLink__failed": "Get Service Fabric link failed.", "@sage/xtrem-service-fabric/node-extensions__tax_extension__property__serviceFabricId": "Sage Service Fabric ID", "@sage/xtrem-service-fabric/node-extensions__tax_value_extension__property__serviceFabricId": "Sage Service Fabric ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport": "Export", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation": "Company ID validation", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__failed": "Company ID validation failed.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__parameter__country": "Country", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__companyIdValidation__parameter__taxID": "Tax ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createOrganisation": "Create organization", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createOrganisation__failed": "Create organization failed.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany": "Create Sage Service Fabric company", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany__failed": "Create Service Fabric company failed.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__createServiceFabricCompany__parameter__id": "ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry": "Available country", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry__failed": "Is available country failed.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__isAvailableCountry__parameter__country": "Country", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__resetOrganisation": "Reset organization", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__resetOrganisation__failed": "Reset organization failed.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__saveToken": "Save token", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__mutation__saveToken__failed": "Save token failed.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__node_name": "Organization Sage Service Fabric", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__adminEmail": "Admin email", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__apiURL": "API URL", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__authURL": "Authentification URL", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__connected": "Connected", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__defaultLanguage": "Default language", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__externalId": "External ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__id": "ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__isActive": "Active", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__isTokenValid": "Token valid", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__lookupURL": "Lookup URL", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__name": "Name", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__organisation": "Organization", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__organisationId": "Organization ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__primaryCountry": "Primary country", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__primarySigningKey": "Primary signing key", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__sageCrmId": "Sage CRM ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__setupId": "Setup ID", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__signingKey": "Signing key", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__token": "Token", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__tokenCreation": "Token creation", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__property__xApplication": "X Application", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__availableCountry": "Available country", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__availableCountry__failed": "Available country failed.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__defaultInstance": "Default instance", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__defaultInstance__failed": "Default instance failed.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getCompanyList": "Get company list", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getCompanyList__failed": "Get company list failed.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getLinkedCompanyList": "Get linked company list", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getLinkedCompanyList__failed": "Get linked company list failed.", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getOrganisation": "Get organization", "@sage/xtrem-service-fabric/nodes__organisation_service_fabric__query__getOrganisation__failed": "Get organisation failed.", "@sage/xtrem-service-fabric/nodes__service_fabric_listener__node_name": "Sage Service Fabric listener", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport": "Export", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__mutation__serviceOptionChange": "Service option change", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__mutation__serviceOptionChange__failed": "Service option change failed.", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__node_name": "Sage Service Fabric option management", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__query__isServiceOptionActiveFunction": "Service option active function", "@sage/xtrem-service-fabric/nodes__service_fabric_option_management__query__isServiceOptionActiveFunction__failed": "Is service option active function failed.", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport": "Export", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__mutation__serviceOptionChange": "Service option change", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__mutation__serviceOptionChange__failed": "Service option change failed.", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__node_name": "Sage Service Fabric tax ID management", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__query__isServiceOptionActiveFunction": "Service option active function", "@sage/xtrem-service-fabric/nodes__service_fabric_tax_id_management__query__isServiceOptionActiveFunction__failed": "Is service option active function failed.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport": "Export", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode": "Get tax code", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__failed": "Get tax code failed.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__parameter__filterPropertie": "Filter properties", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxCode__parameter__filterValue": "Filter value", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxLegislation": "Get tax legislation", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxLegislation__failed": "Get tax legislation failed.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage": "Get tax percentage", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__failed": "Get tax percentage failed.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__parameter__filterPropertie": "Filter properties", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxPercentage__parameter__filterValue": "Filter value", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate": "Get tax rate", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__failed": "Get tax rate failed.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__parameter__filterPropertie": "Filter properties", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRate__parameter__filterValue": "Filter value", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRegion": "Get tax region", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTaxRegion__failed": "Get tax region failed.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation": "Get translation", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__failed": "Get translation failed.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__parameter__filterPropertie": "Filter properties", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__mutation__getTranslation__parameter__filterValue": "Filter value", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__node_name": "Tax rate repository", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__property__id": "ID", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__property__legislations": "Legislations", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__property__setupId": "Setup ID", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__defaultInstance": "Default instance", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__defaultInstance__failed": "Default instance failed.", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__healthCheck": "Health check", "@sage/xtrem-service-fabric/nodes__tax_rate_repository__query__healthCheck__failed": "Health check failed.", "@sage/xtrem-service-fabric/organisation-not-created": "The Sage Service Fabric organization creation failed.", "@sage/xtrem-service-fabric/organisation-service-fabric": "Configuration page", "@sage/xtrem-service-fabric/package__name": "Sage Service Fabric", "@sage/xtrem-service-fabric/page__title__service_fabric_configuration": "Sage Service Fabric configuration", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__isServiceFabricServiceOptionActive____title": "Active", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__isServiceFabricServiceTaxIdActive____title": "Active", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricBlock____title": "Configuration", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricConfigurationPage____title": "Sage Service Fabric configuration page", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricSection____title": "Sage Service Fabric", "@sage/xtrem-service-fabric/page-extensions__option_management_base_extension__serviceFabricTaxIdBlock____title": "Tax ID validation", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog____title": "Select legislation to add in Sage DMO", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__isAlreadySelected____title": "Already selected", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title": "Status", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__description": "Description", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__legislations____columns__title__id": "ID", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__mainSection____title": "General", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__ok____title": "Update", "@sage/xtrem-service-fabric/pages__legislation_service_fabric_dialog__simLegislation____title": "Sage DMO legislation", "@sage/xtrem-service-fabric/pages__link_service_fabric____title": "Links", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelHeaderSection____title": "Details", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelLegislationBlockServiceFabric____title": "Legislation", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelLegislationSection____title": "Legislation", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelRegionSection____title": "Region", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxBlockServiceFabric____title": "Tax rate", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxSection____title": "Tax", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxValueBlockServiceFabric____title": "Tax value or percentage", "@sage/xtrem-service-fabric/pages__link_service_fabric__detailPanelTaxValueSection____title": "Tax value", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislation____lookupDialogTitle": "Select legislation", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationId____title": "ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationName____title": "Name", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__columns__postingClass__name__title": "Type", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__placeholder__postingClass__name": "Select posting class", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__deductibleRate": "Deductible tax rate", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__endDate": "End date", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__id": "ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__isActive": "Active", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__legalMention": "Legal mention", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__name": "Name", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__name__2": "Name", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__rate": "Rate", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId": "Status", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__2": "Sage Service Fabric ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__3": "Tax status", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__4": "Sage Service Fabric ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__5": "Tax value status ", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__columns__title__serviceFabricId__6": "Sage Service Fabric ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title": "Edit legislation link", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__2": "Edit tax link", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__3": "Delete tax", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__dropdownActions__title__4": "Edit tax value link", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislations____levels__emptyStateClickableText": "No taxes", "@sage/xtrem-service-fabric/pages__link_service_fabric__legislationServiceFabricId____title": "Sage Service Fabric legislation ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__mainSection____title": "Legislation", "@sage/xtrem-service-fabric/pages__link_service_fabric__tax____title": "Tax", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxName____title": "Name", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxSysId____title": "Tax system ID", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueDeductibleRate____title": "Deductible tax rate", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueRate____title": "Rate", "@sage/xtrem-service-fabric/pages__link_service_fabric__taxValueSysId____title": "Tax value system ID", "@sage/xtrem-service-fabric/pages__organisation_service_fabric____title": "Configuration ", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__adminEmail____title": "Admin email", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__apiSection____title": "API", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__apiURL____title": "API URL", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__authURL____title": "Authentication URL", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyId____title": "Company ID", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__externalId": "External ID", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__isCreated": "Created", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____columns__title__name": "Name", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____dropdownActions__title": "Create", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyList____title": "Companies", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companyLookupBlock____title": "Company and tax ID lookup and validation", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__companySection____title": "Company", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__connected____title": "Connected", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__country____title": "Country", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__createOrganisation____title": "Create organization", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__defaultLanguage____title": "Default language", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__execute_button_text": "Execute", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__externalId____title": "External ID", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__getToken____title": "Get token", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__id____title": "ID", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infos____title": "Company info", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infosTaxRateRepositoryApi____title": "Response", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__infosValidationApi____title": "Response", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__isActive____title": "Active", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__lookupURL____title": "Company ID lookup URL", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__mainSection____title": "General", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__organisationId____title": "Organization ID", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__primaryCountry____title": "Country", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__refreshCompanyList____title": "Refresh", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__resetOrganisation____title": "Reset organization", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__sageCrmId____title": "Sage CRM ID", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__tax_rate_repository_execute_button_text": "Get tax legislation", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__taxRateRepositoryBlock____title": "Tax rate repository", "@sage/xtrem-service-fabric/pages__organisation_service_fabric__xApplication____title": "Application", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog____title": "Select tax percentage to add in Sage DMO", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__isAlreadySelected____title": "Already selected", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__mainSection____title": "General", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__simTaxValue____title": "Sage DMO tax value", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__tax____title": "Tax", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title": "Status", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__dateFrom": "Start date", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__dateTo": "End date", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__id": "ID", "@sage/xtrem-service-fabric/pages__tax_percentage_service_fabric_dialog__taxPercentages____columns__title__percentage": "Percentage", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog____title": "Add tax rate to Sage DMO", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__isAlreadySelected____title": "Already selected", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__legislation____lookupDialogTitle": "Select legislation", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__mainSection____title": "General", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__simTax____title": "Sage DMO tax", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__code": "Code", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__description": "Description", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__id": "ID", "@sage/xtrem-service-fabric/pages__tax_rate_service_fabric_dialog__taxRates____columns__title__isAlreadySelected": "Status", "@sage/xtrem-service-fabric/permission__manage__name": "Manage", "@sage/xtrem-service-fabric/service_options__service_fabric_option__name": "Sage Service Fabric option", "@sage/xtrem-service-fabric/service_options__service_fabric_tax_id__name": "Sage Service Fabric tax ID", "@sage/xtrem-service-fabric/service_options__service_fabric_tax_repository__name": "Sage Service Fabric tax repository", "@sage/xtrem-service-fabric/update": "Update"}