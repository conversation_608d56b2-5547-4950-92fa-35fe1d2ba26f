import type { ViesResponse as ViesResponseShared } from '../shared-functions/interfaces/company-lookup';
/**
 *  structure return by polling request
 */
export interface PollLinks {
    links: {
        $key: string; // "POLLING-URL"
        $url: string; // "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/tasks/97cf8aee-e6c1-4e4c-84db-c282a29ec3f0"
    };
    stage: string; // "process_started"
    $diagnoses?: any;
}

export interface Files {
    files: File[];
    stage: string; // "submitted"
    statusCode: string; // "200"
    status: string; // "success"
}
export interface File {
    $fileType: string; // "processing results",
    $contentType: string; // "application/json",
    $url: string; // "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/files/26667108",
    $fileName: string; // "processing results.json",
    $stage: string; // "submitted"
}
export interface ViesResponseResult {
    vies_response: ViesResponse;
    diagnoses: Diagnoses[];
}

export interface ViesResponse extends ViesResponseShared {
    $status: string; // success failure,
    $statusCode: string; // 200 500,

    traderPostcodeMatch?: string;
    traderCityMatch?: string;
    traderNameMatch?: string;
    traderStreetMatch?: string;
    traderCompanyTypeMatch?: string;

    $diagnoses?: Diagnoses[];
}

export interface QueryParameters extends TraderInfo {
    /** type of validation */
    type: 'validation' | 'details';
}

export interface TraderInfo {
    /** company/ business name */
    traderName?: string;
    /** company/ business company type */
    traderCompanyType?: string;
    /** company/ business address line */
    traderStreet?: string;
    /** company/ business postal code */
    traderPostcode?: string;
    /** business city */
    traderCity?: string;
}

export interface Response {
    /** is company/ tax id valid */
    valid: boolean;
    /** validation status desc  */
    $status: string;
    /** 200 / 4xx 5xx */
    $statusCode: string;
    /** given in param */
    vatNumber: string;
    /** requested date  */
    requestDate: string;

    $diagnoses: Diagnoses[];
}

export interface Diagnoses {
    /** Error/ warning category */
    $severity: string;
    /** A human-readable explanation for the error */
    $message: string;
    /** A machine-readable error code  */
    $applicationCode: string;

    $type?: string;
}
