import type { Method } from 'axios';

export interface BaseString {
    method: Method;
    endpointUrl: string;
    bodyAndParameters?: string;
    xNonceUuid?: string;
}

export interface GenerateXsignature {
    baseString: BaseString;
    signingKey: string;
    uuid?: string;
}

export interface XsignatureAndUuid {
    xSignature: string;
    xNonceUuid: string;
}

export interface Headers {
    /** Generated with generateXSignature */
    'X-Signature': string;
    /** Uniq uuid of the request ( used to generate the Xsignature ) */
    'X-Nonce': string;
    /** Will sage.online.product but to be change on sage-intacct-manufacturing  */
    'X-Application': string;
    /**  */
    'X-Organisation-Id'?: string;
}

export interface Organisation {
    name: string;
    sageCrmId: string;
    primaryCountry: string;
    adminEmail: string;
    defaultLanguage: string;
    externalId: string;
    /** return by the create Organization */
    primarySigningKey?: string;
    /** return by the create Organization */
    organisationId?: string;
}

export interface Company {
    name: string;
    /** External COmpany ID needed to create on SageServiceFabric */
    externalId: string;
    /** Company ID Comming from SageServiceFabric when created  */
    companyId?: string;
    taxNumber: string;
    standardIndustrialCode: string;
    contactTelNo: string;
    contactEmail: string;
    address: Address;
}

export interface Address {
    addressLine1: string;
    addressLine2: string;
    addressLine3: string;
    addressLine4: string;
    countrySubdivision: string;
    postalCode: string;
    country: string;
}
