import type { Logger } from '@sage/xtrem-core';
import type { Method } from 'axios';

export interface Package {
    application: string;
    signingKey: string;
    authURL: string;
    apiURL: string;
    /** For id-validation-look-up */
    lookupURL: string;
}

export interface HeaderParametersCommon {
    'Content-Type': 'application/json';
    /** Constant string identifying calling application */
    'X-Signatory': 'sagepay' | 'sage_global_services' | 'sage_test' | 'sage-intacct' | 'sage-accounts-uk';
    /** The username or internal application ID that the customer used to sign in to the client machine  */
    'X-Api-User-ID': string;
    /** Organisation/ business/ desktop installation unique identifier. Represents an authorised installation of a Sage product. */
    'X-Business-ID': string;
    /** Company/ dataset unique identifier in the source system.
     * A Company is an entity that issues invoices, receives payments etc.
     * A Sage installation can be used for multiple companies. */
    'X-Company-ID': string;
    /** Name of the connecting application */
    'X-Application': string;
    /** Application version(s) – comma separated app modules' codes/versions. Used for troubleshooting and reporting. */
    'X-Application-Version': string;

    /** This is a unique token (such as a GUID) that your application should generate for each request. */
    'X-Request-ID': string;
}

export interface RequestGenericConstructor {
    method?: Method;
    url?: string;
    data?: any;
    urlParameters?: { [key: string]: string };
    logger?: Logger;
}

export interface RequestConstructor extends RequestGenericConstructor {
    additionnalHeaders?: any;
    withToken?: boolean;
    signingKey?: string;
}

export interface AuthenticationParams {
    api: 'organisations' | 'companies' | 'accesstoken';
}
