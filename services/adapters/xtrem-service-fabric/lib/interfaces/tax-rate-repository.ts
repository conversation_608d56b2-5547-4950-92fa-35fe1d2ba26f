import type {
    TaxLegislation as sharedTaxLegislation,
    TaxPercentage as sharedTaxPercentage,
    TaxRate as sharedTaxRate,
} from '../shared-functions/interfaces/tax-rate-repository';
import type { HeaderParametersCommon } from './common';

export interface TaxLegislation extends sharedTaxLegislation {}
export interface TaxRate extends sharedTaxRate {}
export interface TaxPercentage extends sharedTaxPercentage {}

export interface TaxRegion {
    id: string; // "TR1NI",
    code: string; // "TR1NI",
    description: string; // "Territorio Comun ES",
    isMainRegion: boolean; // true,
    useMainTaxes: boolean; // true,
    useFromTaxes: boolean; // true,
    taxLegislationId: string; // "TL1UK"
}

export interface TaxCode {
    id: string; // "TC1UKSTDRT",
    code: string; // "UKSTDRT",
    description: string; // "Standard Rate UK",
    dateFrom: string; // "2019-01-01",
    dateTo: string; // "2019-01-31",
    isActive: boolean; // true,
    taxLegislationId: string; // "TL1UK",
    taxRegionId: string | null; // null
}

export interface Translation {
    id: string; //  "TR661ATSDESTSVC2ATREDRT2TR24UKRDESTSVC2UKDESTEUes-ES",
    dictionaryName: string; //  "taxRateMappings",
    dictionaryRowId: string; // "TR660ATSDESTSVC2ATREDRT1TR476ESRDESTSVC2ESDESTEU",
    dictionaryFieldName: string; //  "operatingCompanyTaxRateCode",
    locale: string; //  "es-ES",
    translation: string; //  "Reducido (AT) 13%"
}

export interface HeaderParameters extends HeaderParametersCommon {
    'X-Signature'?: string;
    /** The username or internal application ID that the customer used to sign in to the client machine  */

    'X-User-IP': string;
    /** Identifier of the group of the requests related with a given workflow execution.
     * Allows to correlate API requests initiated on client side with the results on service side.
     * Crucial for troubleshooting. */
    'X-Execution-ID': string; // TODO : Handle this on Request class or  organisation side ??( to group getToken & request for example )
    /** ISO 3166-1 alpha-2 country code of the tenant (customer) using the Product ( GB ) */
    'X-Business-Country-Code': string;
    /** Bearer Auth */
    Authorization: string;
}

export interface HealthCheck {
    serviceVersion: string; // 1.2
    statusText: string; // \"TRR service is OK\"
    deploymentTag: string; // 469953
    dictionariesVersion: string; // 1.50
    commitId: string; // 917fa0317db838019246e3d76dea730bfa55a978
    openApiSpecificationVersion: string; // 0.5.5
}

export interface TrrError {
    code: string;
    message: string;
    target: string;
    errors: any[];
}
