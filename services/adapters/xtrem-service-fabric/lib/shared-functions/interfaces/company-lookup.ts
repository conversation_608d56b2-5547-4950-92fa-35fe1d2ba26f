export interface ViesResponse {
    countryCode: string; // "FR",
    vatNumber: string; // 'FR123456';
    valid: boolean;

    traderCity?: string;

    traderPostCode?: string;

    traderName?: string;

    traderStreet?: string;

    traderCompanyType?: string;

    traderAddress?: string;

    requestDate: string; // "2022-03-10T01:00:00.000+00:00", TODO: si if can put datetime
}

/** For ui $ are not allowed in graphql  */
export interface ViesResponseUi extends ViesResponse {
    status: string; // success failure,
    statusCode: string; // 200 500,
}
