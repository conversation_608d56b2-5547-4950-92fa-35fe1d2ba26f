export interface TaxLegislationTable extends TaxLegislation {
    _id: string;
    isAlreadySelected: boolean;
}
export interface TaxLegislation {
    id: string;
    code: string;
    countryCode: string;
    description: string;
}

export interface TaxRateTable extends TaxRate {
    _id: string;
    isAlreadySelected: boolean;
}

export interface TaxPercentageTable extends TaxPercentage {
    _id: string;
    isAlreadySelected: boolean;
}

export interface TaxRate {
    /** Unique identifier eg : "TR1UKSHGDS1UKSTDRT" */ id: string; // ,
    code: string; // "Standard 20%\"",
    description: string; // "UK Sales Home of Goods at Standard rate",
    isReverseCharge: boolean; // false,
    order: number; // 1,
    isTaxOnTax: boolean; // true,
    percentOfNetTaxAppliedFor: number; // 100,
    deductibleTaxPercentage: number; // 100,
    isDeductibleTaxPercentageModifiable: boolean; // false,
    dateFrom: string; // "2019-01-01",
    dateTo: string; // "2019-01-31",
    isActive: boolean; //  true;
    taxLegislationId: string; // "TL1UK",
    taxRegionId: string; // null,
    taxTreatmentId: string; // "TT1UKSH",
    taxCodeId: string; // "TC1UKSTDRT",
    taxItemTypeId: string; // "GDS1",
    taxPercentageGroupId: string; // "TPG1UKSTDRT"
}

export interface TaxPercentage {
    id: string; // "TP8FRREDRT2",
    groupId: string; // "TPG8FRREDRT2",
    code: string; // "REDRT2",
    description: string; // "Reduced Rate 2",
    dateFrom: string; // "2019-01-01",
    dateTo: string; // "2019-01-31",
    isActive: boolean; // true,
    percentage: number; // 5.5;
    taxLegislationId: string; // "TL3FR",
    taxRegionId: string | null; // null
}
