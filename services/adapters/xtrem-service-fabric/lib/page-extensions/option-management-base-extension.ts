import type { Graph<PERSON><PERSON> } from '@sage/xtrem-service-fabric-api';
import type { OptionManagementBase as OptionManagementBasePage } from '@sage/xtrem-structure/build/lib/pages/option-management-base';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<OptionManagementBaseExtension>({
    extends: '@sage/xtrem-structure/OptionManagementBase',
    async onLoad() {
        await this.checkServiceFabric();
        await this.checkServiceFabricTaxId();
        this.numberOfSaveAfter += 1;
    },
})
export class OptionManagementBaseExtension extends ui.PageExtension<OptionManagementBasePage, GraphApi> {
    /** Service Fabric  */
    isServiceFabricServiceOptionActiveBoolean: boolean;

    /** Tax ID */
    isServiceFabricServiceTaxIdActiveBoolean: boolean;

    @ui.decorators.section<OptionManagementBaseExtension>({
        insertBefore() {
            return this.otherSection;
        },
        title: 'Sage Service Fabric',
    })
    serviceFabricSection: ui.containers.Section;

    @ui.decorators.block<OptionManagementBaseExtension>({
        parent() {
            return this.serviceFabricSection;
        },
        title: 'Configuration',
        width: 'small',
    })
    serviceFabricBlock: ui.containers.Block;

    @ui.decorators.block<OptionManagementBaseExtension>({
        parent() {
            return this.serviceFabricSection;
        },
        title: 'Tax ID validation',
        width: 'small',
    })
    serviceFabricTaxIdBlock: ui.containers.Block;

    @ui.decorators.switchField<OptionManagementBaseExtension>({
        isTransient: true,
        parent() {
            return this.serviceFabricBlock;
        },
        title: 'Active',
    })
    isServiceFabricServiceOptionActive: ui.fields.Switch;

    @ui.decorators.switchField<OptionManagementBaseExtension>({
        isTransient: true,
        parent() {
            return this.serviceFabricTaxIdBlock;
        },
        title: 'Active',
    })
    isServiceFabricServiceTaxIdActive: ui.fields.Switch;

    @ui.decorators.linkField<OptionManagementBaseExtension>({
        isTransient: true,
        parent() {
            return this.serviceFabricBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-service-fabric/organisation-service-fabric', 'Configuration page');
        },
        page: '@sage/xtrem-service-fabric/OrganisationServiceFabric',
        title: 'Sage Service Fabric configuration page',
        isTitleHidden: true,
    })
    serviceFabricConfigurationPage: ui.fields.Link;

    async checkServiceFabric() {
        this.isServiceFabricServiceOptionActiveBoolean =
            (await this.$.graph
                .node('@sage/xtrem-service-fabric/ServiceFabricOptionManagement')
                .queries.isServiceOptionActiveFunction(true, false)
                .execute()) || false;
        this.isServiceFabricServiceOptionActive.value = this.isServiceFabricServiceOptionActiveBoolean;
    }

    async serviceFabricSaveOverload() {
        if (this.isServiceFabricServiceOptionActive.value !== this.isServiceFabricServiceOptionActiveBoolean) {
            await this.$.graph
                .node('@sage/xtrem-service-fabric/ServiceFabricOptionManagement')
                .mutations.serviceOptionChange(true, true)
                .execute();
        }
    }

    async checkServiceFabricTaxId() {
        this.isServiceFabricServiceTaxIdActiveBoolean =
            (await this.$.graph
                .node('@sage/xtrem-service-fabric/ServiceFabricTaxIdManagement')
                .queries.isServiceOptionActiveFunction(true, false)
                .execute()) || false;
        this.isServiceFabricServiceTaxIdActive.value = this.isServiceFabricServiceTaxIdActiveBoolean;
    }

    async serviceFabricTaxIdSaveOverload() {
        if (this.isServiceFabricServiceTaxIdActive.value !== this.isServiceFabricServiceTaxIdActiveBoolean) {
            await this.$.graph
                .node('@sage/xtrem-service-fabric/ServiceFabricTaxIdManagement')
                .mutations.serviceOptionChange(true, true)
                .execute();
        }
    }

    @ui.decorators.pageActionOverride<OptionManagementBaseExtension>({
        async onClickAfter() {
            await this.serviceFabricSaveOverload(); // save Service Fabric
            await this.serviceFabricTaxIdSaveOverload(); // save Tax Id
            await this.checkServiceFabricTaxId(); // check tax id
            await this.checkServiceFabric(); // check service Fabric
            await this.hardRefresh();
        },
    })
    save: ui.PageAction;
}

declare module '@sage/xtrem-structure/build/lib/pages/option-management-base' {
    interface OptionManagementBase extends OptionManagementBaseExtension {}
}
