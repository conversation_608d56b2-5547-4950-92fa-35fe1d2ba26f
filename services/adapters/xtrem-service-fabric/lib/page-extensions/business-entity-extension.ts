import type { BusinessEntity } from '@sage/xtrem-master-data/build/lib/pages/business-entity';
import type { GraphA<PERSON> } from '@sage/xtrem-service-fabric-api';
import * as ui from '@sage/xtrem-ui';
import {
    isOrganisationSet,
    nameValidation,
    taxIdNumberOnChangeAfterWithVies,
    taxIdNumberValidationWithVies,
} from '../client-functions/vies-lookup';
import type { ViesResponseUi } from '../shared-functions/interfaces/company-lookup';

@ui.decorators.pageExtension<BusinessEntityExtension>({
    extends: '@sage/xtrem-master-data/BusinessEntity',
    async onLoad() {
        this.isOrganisationSetBoolean = await isOrganisationSet(this);
    },
    extensionAccessBinding: {
        node: '@sage/xtrem-service-fabric/OrganisationServiceFabric',
        bind: 'companyIdValidation',
    },
})
export class BusinessEntityExtension extends ui.PageExtension<BusinessEntity, GraphApi> {
    viesResponsePayload: ViesResponseUi;

    isOrganisationSetBoolean: boolean;

    @ui.decorators.textFieldOverride<BusinessEntityExtension>({
        onChangeAfter() {
            // to be fix after XT-61637
            return taxIdNumberOnChangeAfterWithVies(this as unknown as BusinessEntityExtension);
        },
        validation() {
            // to be fix after XT-61637
            return taxIdNumberValidationWithVies(
                this as unknown as BusinessEntityExtension,
                this.country.value?.id || '',
                this.taxIdNumber.value || '',
                this.viesResponsePayload,
            );
        },
    })
    taxIdNumber: ui.fields.Text;

    @ui.decorators.textFieldOverride<BusinessEntityExtension>({
        validation() {
            return nameValidation(this as unknown as BusinessEntityExtension);
        },
    })
    name: ui.fields.Text;
}

declare module '@sage/xtrem-master-data/build/lib/pages/business-entity' {
    export interface BusinessEntity extends BusinessEntityExtension {}
}
