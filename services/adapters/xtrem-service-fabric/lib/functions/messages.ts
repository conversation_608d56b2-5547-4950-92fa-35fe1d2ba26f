import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';

export function organisationNotCreated(context: Context) {
    throw new BusinessRuleError(
        context.localize(
            '@sage/xtrem-service-fabric/organisation-not-created',
            'The Sage Service Fabric organization creation failed.',
        ),
    );
}

export function noUrlForLookupApi(context: Context) {
    throw new BusinessRuleError(
        context.localize(
            '@sage/xtrem-service-fabric/no-lookup-url-api-configuration',
            'No URL for the Sage Service Fabric lookup API in the configuration.',
        ),
    );
}
