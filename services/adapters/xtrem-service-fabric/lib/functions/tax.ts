import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { Lo<PERSON>, SystemError } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremServiceFabric from '../index';

const logger = Logger.getLogger(__filename, 'tax');

/**
 * Determine the tax Category by legislation
 * TODO : to be define elsewhere not the good way to do ! ( default serviceFabricTax in legislation extension ? )
 * to be define with <PERSON><PERSON> */
function getTaxCategory(context: Context, legislationID: string) {
    const getKey = (legislationIDForID: string) => {
        switch (legislationIDForID) {
            case 'FR':
                return 'TVA';
            case 'GB':
                return 'VAT';
            default:
                return ''; // Defaulted to VAT for other country /!\
        }
    };
    return context.read(xtremTax.nodes.TaxCategory, { id: getKey(legislationID) });
}

/**
 * get the traduction for dictonaryName/dictionaryFieldName for serviceFabricId as dictionaryRowId
 * @param context
 * @param serviceFabricId  for filter dictionaryRowId
 * @returns translation
 */
async function getTraduction(
    context: Context,
    serviceFabricId: string,
    baseString?: string,
    dictonaryName?: string,
    dictionaryFieldName?: string,
): Promise<string> {
    const translationArray = await xtremServiceFabric.nodes.TaxRateRepository.getTranslation(
        context,
        'dictionaryRowId',
        serviceFabricId,
    );

    const translationPart = ` ${translationArray
        .filter(
            translationFilter =>
                (!dictonaryName || dictonaryName === translationFilter.dictionaryName) &&
                (!dictionaryFieldName || dictionaryFieldName === translationFilter.dictionaryFieldName),
        )
        .map(translation => `"${translation.locale}":"${translation.translation}"`)
        .join(',')}`;

    return `{ "en":"${baseString}" ${translationPart ? ',' : `,"en-EN":"${baseString}"`}${translationPart} }`;
}

/**
 * Create the payload of the tax to create with the serviceFabric Tax rate payload
 * @param serviceFabricData
 * @param legislationID
 * @returns
 */
async function getTaxPayloadFromServiceFabric(
    context: Context,
    serviceFabricTax: xtremServiceFabric.interfaces.TaxRateRepository.TaxRate,
    legislationID: string,
): Promise<NodeCreateData<xtremTax.nodes.Tax>> {
    logger.debug(() => `Legislation id : ${legislationID}`);
    const legislation = await context.read(xtremStructure.nodes.Legislation, { id: legislationID });

    const taxRateCodeTranslated = await getTraduction(
        context,
        serviceFabricTax.id,
        serviceFabricTax.code,
        'taxRates',
        'code',
    );
    logger.debug(() => `taxRate code translated :  ${taxRateCodeTranslated}`);

    return {
        id: serviceFabricTax.id,
        // No translation for description
        name: `{ "en":"${serviceFabricTax.description}","en-EN":"${serviceFabricTax.description}"  }`, // Need more than 80 char for name ?
        //  -- Orginal ask from Cyrille Hamon but name can have only 80 char
        // name:`${serviceFabricTax.description} - ${serviceFabricTax.code}`
        primaryExternalReference: `${serviceFabricTax.code}`,
        secondaryExternalReference: '',
        isReverseCharge: serviceFabricTax.isReverseCharge,
        legalMention: `${taxRateCodeTranslated}`,
        taxCategory: await getTaxCategory(context, legislationID),
        legislation,
        isActive: false,
        serviceFabricId: serviceFabricTax.id,
    } as NodeCreateData<xtremTax.nodes.Tax>;
}

/**
 * Create the payload of the tax to create with the serviceFabric Tax percentage
 * @param serviceFabricData
 * @param legislationID
 * @returns
 * Commented because not used for now // linter
 */
function getTaxValuePayloadFromServiceFabric(
    serviceFabricTaxPercentage: xtremServiceFabric.interfaces.TaxRateRepository.TaxPercentage,
): NodeCreateData<xtremTax.nodes.TaxValue> {
    return {
        //  endDate: DateValue.fromInternalValue(Date.parse(serviceFabricTaxPercentage.dateTo)),
        serviceFabricId: serviceFabricTaxPercentage.id,
        // deductibleRate: , Comming from TaxRate
        rate: serviceFabricTaxPercentage.percentage,
    } as NodeCreateData<xtremTax.nodes.TaxValue>;
}

/**
 *  Return the only one of an array
 * @param array must have only one line
 * @param object Name of the object  for the error
 * @param id For systemErrorMessage
 * @returns the line
 */
function onlyOne<T>(array: T[], object: string, id: string): T {
    if (!array.length) {
        throw new SystemError(`No ${object} for ${id} `);
    }
    if (array.length === 1) {
        return array[0];
    }
    throw new SystemError(`More than one ${object} for ${id} `);
}

/**
 * get the taxRate for Tax synchronisation
 * @param context
 * @param serviceFabricId  for filter id
 * @returns service Fabric Tax
 */
async function getTaxRate(
    context: Context,
    serviceFabricId: string,
): Promise<xtremServiceFabric.interfaces.TaxRateRepository.TaxRate> {
    const rate = await xtremServiceFabric.nodes.TaxRateRepository.getTaxRate(context, 'id', serviceFabricId);
    return onlyOne(rate, 'tax rate', serviceFabricId);
}

/**
 * get the taxPercentage for TaxValue
 * @param context
 * @param serviceFabricId  for filter id
 * @returns service Fabric TaxPercentage
 */
async function getTaxPercentageByGroupId(
    context: Context,
    taxPercentageGroupId: string,
): Promise<xtremServiceFabric.interfaces.TaxRateRepository.TaxPercentage[]> {
    const taxPercentage = await xtremServiceFabric.nodes.TaxRateRepository.getTaxPercentage(
        context,
        'groupId',
        taxPercentageGroupId,
    );
    return taxPercentage;
}

export async function createTax(context: Context, serviceFabricId: string, legislationID: string): Promise<void> {
    logger.debug(() => `Create Service Fabric tax ${serviceFabricId} for legislation: ${legislationID}`);
    const taxRateToCreate = await getTaxRate(context, serviceFabricId);

    const taxPayload = await getTaxPayloadFromServiceFabric(context, taxRateToCreate, legislationID);

    /** TODO: to create with translation : context.withLocalizedTextAsJson(() => { } );  */

    /** get the taxPercentages to convert to taxe values  */
    const taxPercentageToCreate = await getTaxPercentageByGroupId(context, taxRateToCreate.taxPercentageGroupId);

    taxPayload.taxValues = taxPercentageToCreate.map(taxPercentage => {
        return { ...getTaxValuePayloadFromServiceFabric(taxPercentage) };
    });

    await context.withLocalizedTextAsJson(async () => {
        const taxToUpdate = await context.tryRead(xtremTax.nodes.Tax, { id: taxPayload.id }, { forUpdate: true });

        /** Updating the tax */
        if (taxToUpdate) {
            await taxToUpdate.$.set(taxPayload);
            await taxToUpdate.$.save();
        } else {
            const tax = await context.create(xtremTax.nodes.Tax, taxPayload);

            /**
             * name is localized how do we set the differents language ?
             */
            await tax.$.save();

            logger.info(() => `Created : ${JSON.stringify(taxPayload)}`);
        }
    });
}
