import type { Context } from '@sage/xtrem-core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, U<PERSON> } from '@sage/xtrem-core';
import * as crypto from 'crypto';
import type * as xtremServiceFabric from '../../index';

const logger = Logger.getLogger(__filename, 'sign');

/** Following how to generate an X-Signature :
 *   https://developers.sage.com/payments-out/guides/generate-x-signature/
 * */

/**
 *  Encoding parameters & body
 * @param parameters
 * @param body
 * @returns Use this value in the base string instead of the {JSON body and path parameters} placeholder.
 */
export function bodyAndParametersEncode(parameters: { [key: string]: string } = {}, body?: any): string {
    if (body) {
        /** Encode JSON body in Base64:  equivalent to BTOA */
        parameters.body = Buffer.from(JSON.stringify(body), 'binary').toString('base64');
    }
    /** Split the body and path parameters into key-value pairs and sort them in the ascending order based on key names:
     * Join the sorted key-value pairs using an ampersand (&):
     */
    const stringToEncode = Object.keys(parameters)
        .sort((a: string, b: string) => a.localeCompare(b))
        .reduce(
            (previous, current) =>
                `${previous}${previous ? '&' : ''}${encodeURIComponent(current)}=${encodeURIComponent(
                    parameters[current],
                )}`,
            '',
        );
    logger.debug(() => `encodeURIComponent : ${stringToEncode}`);
    // Percent-encode the string:
    return stringToEncode;
}

/**
 *
 * @param method // is the HTTP method used in the request for which you generate X-Signature.
 * @param enpointUrl // is the endpoint URL used in the request, percent-encoded.
 * @param xNonceUuid // is the value you use in the X-Nonce header of your request. This is an arbitrary unique value such as a GUID, percent-encoded.
 * @param bodyAndParameters // JSON body and path parameters
 */
export function getBaseString(base: xtremServiceFabric.interfaces.BaseString) {
    const xNonceUuid = base.xNonceUuid || '';
    return `${base.method.toUpperCase()}&${encodeURIComponent(base.endpointUrl)}&${encodeURIComponent(
        base.bodyAndParameters || '',
    )}&${encodeURIComponent(xNonceUuid)}`;
}

export function encryptXSignature(baseString: string, signingKey: string) {
    /**
     * var hash = CryptoJS.HmacSHA1(baseString, signingKey + '&null');
     * var signature = CryptoJS.enc.Base64.stringify(hash);
     *  */
    return crypto.createHmac('sha1', `${signingKey}&null`).update(baseString).digest('base64');
}

export function generateXSignature(
    params: xtremServiceFabric.interfaces.GenerateXsignature,
): xtremServiceFabric.interfaces.XsignatureAndUuid {
    const uuid = params.baseString.xNonceUuid || Uuid.generate().toString();

    const baseString = getBaseString({ ...params.baseString, xNonceUuid: uuid });
    logger.debug(() => `Base string to encrypt : ${baseString} \n signingKey:${params.signingKey} \n uuid:${uuid}`);

    return { xSignature: encryptXSignature(baseString, params.signingKey), xNonceUuid: uuid };
}

/**
 *  Throw BusinessRuleError if no signing key
 * @param context
 * @param signingKey
 */
export function checkSigningKey(context: Context, signingKey?: string) {
    if (!signingKey || signingKey.length <= 0) {
        throw new BusinessRuleError(context.localize('@sage/xtrem-service-fabric/no-sigining-key', 'No signing key '));
    }
}
