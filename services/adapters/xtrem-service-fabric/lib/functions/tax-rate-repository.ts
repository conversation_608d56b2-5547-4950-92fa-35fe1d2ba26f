import type { Context, NodeCreateData } from '@sage/xtrem-core';
import type * as xtremTax from '@sage/xtrem-tax';
import * as xtremServiceFabric from '../index';

/** empty taxRate  */
export const emptyServiceFabricTaxRate: xtremServiceFabric.interfaces.TaxRateRepository.TaxRate = {
    code: '',
    dateFrom: '',
    dateTo: '',
    deductibleTaxPercentage: 0,
    description: '',
    id: '',
    isActive: false,
    isDeductibleTaxPercentageModifiable: false,
    isReverseCharge: false,
    isTaxOnTax: false,
    order: 0,
    percentOfNetTaxAppliedFor: 0,
    taxCodeId: '',
    taxItemTypeId: '',
    taxLegislationId: '',
    taxPercentageGroupId: '',
    taxRegionId: '',
    taxTreatmentId: '',
};

export const emptyXtreemTaxRate: NodeCreateData<xtremTax.nodes.Tax> = {
    _id: -1,
    isActive: false,
    serviceFabricId: '',
    taxCategory: { id: 'TVA' },
    name: '',
    legislation: { id: '' },
};

/**
 * @returns  empty array if error on requesting on the api
 */
export async function serviceFabricGetTaxRate(
    context: Context,
): Promise<xtremServiceFabric.interfaces.TaxRateRepository.TaxRate[]> {
    try {
        return await xtremServiceFabric.nodes.TaxRateRepository.getTaxRate(context);
    } catch (error) {
        context.logger.error(() => error);
        return [{ ...emptyServiceFabricTaxRate, code: error.code, description: error.message }];
    }
}
