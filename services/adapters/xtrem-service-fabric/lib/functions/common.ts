import type { OperationGrant } from '@sage/xtrem-core';
import * as xtremServiceFabric from '../index';

export function instanceOfOrganisation(data: any): data is xtremServiceFabric.interfaces.Organisation {
    return 'name' in data;
}

export const commonServiceFabricActivities: OperationGrant[] = [
    {
        operations: ['defaultInstance', 'companyIdValidation', 'isAvailableCountry'],
        on: [() => xtremServiceFabric.nodes.OrganisationServiceFabric],
    },
];
