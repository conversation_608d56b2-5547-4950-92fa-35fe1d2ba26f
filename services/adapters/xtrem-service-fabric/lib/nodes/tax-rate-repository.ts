import type { Collection, Context } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import { dataTypes } from '@sage/xtrem-system';
import { Filter } from '../classes/filter';
import * as xtremServiceFabric from '../index';

@decorators.node<TaxRateRepository>({
    package: 'xtrem-service-fabric',
    storage: 'sql',
    isPublished: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
    serviceOptions: () => [xtremServiceFabric.serviceOptions.serviceFabricTaxRepository],
})
export class TaxRateRepository extends Node {
    /** Only one line - setup id is DEFAULT  */
    @decorators.stringProperty<TaxRateRepository, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
    })
    readonly id: Promise<string>;

    /** All Legislations */
    @decorators.collectionProperty<TaxRateRepository, 'legislations'>({
        isPublished: true,
        node: () => xtremStructure.nodes.Legislation,
        getFilter() {
            return {};
        },
    })
    readonly legislations: Collection<xtremStructure.nodes.Legislation>;

    /**
     * get the default record
     *
     */
    @decorators.query<typeof TaxRateRepository, 'defaultInstance'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'instance',
            node: () => TaxRateRepository,
        },
    })
    static async defaultInstance(context: Context): Promise<TaxRateRepository | null> {
        const defaultInstance = await context
            .query(xtremServiceFabric.nodes.TaxRateRepository, {
                filter: { id: 'DEFAULT' },
            })
            .toArray();
        return defaultInstance.length ? defaultInstance[0] : null;
    }

    /**
     * Call TaxRateRepository / taxLegislation
     * @param context
     * @returns
     */
    @decorators.mutation<typeof TaxRateRepository, 'getTaxLegislation'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    id: 'string',
                    code: 'string',
                    countryCode: 'string',
                    description: 'string',
                },
            },
        },
    })
    static getTaxLegislation(
        context: Context,
    ): Promise<xtremServiceFabric.interfaces.TaxRateRepository.TaxLegislation[]> {
        return new xtremServiceFabric.classes.TaxRateRepository(context).taxLegislation();
    }

    /**
     * Call TaxRateRepository / taxRegion
     * @param context
     * @returns
     */
    @decorators.mutation<typeof TaxRateRepository, 'getTaxRegion'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    id: 'string',
                    code: 'string',
                    countryCode: 'string',
                    description: 'string',
                },
            },
        },
    })
    static getTaxRegion(context: Context): Promise<xtremServiceFabric.interfaces.TaxRateRepository.TaxRegion[]> {
        return new xtremServiceFabric.classes.TaxRateRepository(context).taxRegion();
    }

    /**
     * Call TaxRateRepository / taxRegion
     * @param context
     * @returns
     */
    @decorators.mutation<typeof TaxRateRepository, 'getTaxRate'>({
        isPublished: true,
        parameters: [
            { name: 'filterPropertie', type: 'string' },
            { name: 'filterValue', type: 'string' },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    id: 'string', // "TR1UKSHGDS1UKSTDRT",
                    code: 'string', // "Standard 20%\"",
                    description: 'string', // "UK Sales Home of Goods at Standard rate",
                    isReverseCharge: 'boolean', // false,
                    order: 'integer', // 1,
                    isTaxOnTax: 'boolean', // true,
                    percentOfNetTaxAppliedFor: 'integer', // 100,
                    deductibleTaxPercentage: 'integer', // 100,
                    isDeductibleTaxPercentageModifiable: 'boolean', // false,
                    dateFrom: 'string', // "2019-01-01",
                    dateTo: 'string', // "2019-01-31",
                    isActive: 'boolean', // 'true',
                    taxLegislationId: 'string', // "TL1UK",
                    taxRegionId: 'string', // null,
                    taxTreatmentId: 'string', // "TT1UKSH",
                    taxCodeId: 'string', // "TC1UKSTDRT",
                    taxItemTypeId: 'string', // "GDS1",
                    taxPercentageGroupId: 'string', // "TPG1UKSTDRT"
                },
            },
        },
    })
    static getTaxRate(
        context: Context,
        filterPropertie?: string,
        filterValue?: string,
    ): Promise<xtremServiceFabric.interfaces.TaxRateRepository.TaxRate[]> {
        const filter =
            filterPropertie && filterValue
                ? new Filter({ filterArray: [{ property: filterPropertie, operator: 'eq', value: filterValue }] })
                : undefined;

        return new xtremServiceFabric.classes.TaxRateRepository(context).taxRate(filter);
    }

    /**
     * Call TaxRateRepository / taxRegion
     * @param context
     * @returns
     */
    @decorators.mutation<typeof TaxRateRepository, 'getTaxPercentage'>({
        isPublished: true,
        parameters: [
            { name: 'filterPropertie', type: 'string' },
            { name: 'filterValue', type: 'string' },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    id: 'string', // "TP8FRREDRT2",
                    groupId: 'string', // "TPG8FRREDRT2",
                    code: 'string', // "REDRT2",
                    description: 'string', // "Reduced Rate 2",
                    dateFrom: 'string', // "2019-01-01",
                    dateTo: 'string', // "2019-01-31",
                    isActive: 'boolean', // true,
                    percentage: 'decimal', // 5.5;
                    taxLegislationId: 'string', // "TL3FR",
                    taxRegionId: 'string', // null
                },
            },
        },
    })
    static getTaxPercentage(
        context: Context,
        filterPropertie: string,
        filterValue: string,
    ): Promise<xtremServiceFabric.interfaces.TaxRateRepository.TaxPercentage[]> {
        const filter =
            filterPropertie && filterValue
                ? new Filter({ filterArray: [{ property: filterPropertie, operator: 'eq', value: filterValue }] })
                : undefined;

        return new xtremServiceFabric.classes.TaxRateRepository(context).taxPercentages(filter);
    }

    /**
     * Call TaxRateRepository / taxRegion
     * @param context
     * @returns
     */
    @decorators.mutation<typeof TaxRateRepository, 'getTaxCode'>({
        isPublished: true,
        parameters: [
            { name: 'filterPropertie', type: 'string' },
            { name: 'filterValue', type: 'string' },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    id: 'string', // "TP8FRREDRT2",
                    code: 'string', // "REDRT2",
                    description: 'string', // "Reduced Rate 2",
                    dateFrom: 'string', // "2019-01-01",
                    dateTo: 'string', // "2019-01-31",
                    isActive: 'boolean', // true,
                    taxLegislationId: 'string', // "TL3FR",
                    taxRegionId: 'string', // null
                },
            },
        },
    })
    static getTaxCode(
        context: Context,
        filterPropertie: string,
        filterValue: string,
    ): Promise<xtremServiceFabric.interfaces.TaxRateRepository.TaxCode[]> {
        const filter =
            filterPropertie && filterValue
                ? new Filter({ filterArray: [{ property: filterPropertie, operator: 'eq', value: filterValue }] })
                : undefined;

        return new xtremServiceFabric.classes.TaxRateRepository(context).taxCodes(filter);
    }

    /**
     * Call TaxRateRepository / translations
     * @param context
     * @returns
     */
    @decorators.mutation<typeof TaxRateRepository, 'getTranslation'>({
        isPublished: true,
        parameters: [
            { name: 'filterPropertie', type: 'string' },
            { name: 'filterValue', type: 'string' },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    id: 'string', //  "TR661ATSDESTSVC2ATREDRT2TR24UKRDESTSVC2UKDESTEUes-ES",
                    dictionaryName: 'string', //  "taxRateMappings",
                    dictionaryRowId: 'string', // "TR660ATSDESTSVC2ATREDRT1TR476ESRDESTSVC2ESDESTEU",
                    dictionaryFieldName: 'string', //  "operatingCompanyTaxRateCode",
                    locale: 'string', //  "es-ES",
                    translation: 'string', //  "Reducido (AT) 13%"
                },
            },
        },
    })
    static getTranslation(
        context: Context,
        filterPropertie: string,
        filterValue: string,
    ): Promise<xtremServiceFabric.interfaces.TaxRateRepository.Translation[]> {
        const filter =
            filterPropertie && filterValue
                ? new Filter({ filterArray: [{ property: filterPropertie, operator: 'eq', value: filterValue }] })
                : undefined;

        return new xtremServiceFabric.classes.TaxRateRepository(context).translations(filter);
    }

    @decorators.query<typeof TaxRateRepository, 'healthCheck'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'object',

            properties: {
                serviceVersion: 'string',
                statusText: 'string',
                deploymentTag: 'string',
                dictionariesVersion: 'string',
                commitId: 'string',
                openApiSpecificationVersion: 'string',
            },
        },
    })
    static healthCheck(context: Context): Promise<{
        serviceVersion: string;
        statusText: string;
        deploymentTag: string;
        dictionariesVersion: string;
        commitId: string;
        openApiSpecificationVersion: string;
    }> {
        return xtremServiceFabric.classes.TaxRateRepository.healthCheck(context);
    }
}
