import type { Context } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import { serviceFabricTaxId } from '../service-options/service-fabric-tax-id';

@decorators.subNode<ServiceFabricTaxIdManagement>({
    extends: () => xtremStructure.nodes.BaseOptionManagement,
    isPublished: true,
})
export class ServiceFabricTaxIdManagement extends xtremStructure.nodes.BaseOptionManagement {
    @decorators.query<typeof ServiceFabricTaxIdManagement, 'isServiceOptionActiveFunction'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'boolean',
        },
    })
    static isServiceOptionActiveFunction(context: Context) {
        return ServiceFabricTaxIdManagement.baseIsServiceOptionActiveFunction(context, serviceFabricTaxId);
    }

    @decorators.mutation<typeof ServiceFabricTaxIdManagement, 'serviceOptionChange'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'boolean',
        },
    })
    static serviceOptionChange(context: Context): Promise<boolean> {
        return ServiceFabricTaxIdManagement.baseServiceOptionChange(context, serviceFabricTaxId);
    }
}
