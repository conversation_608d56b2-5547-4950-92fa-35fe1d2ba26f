import type { Context } from '@sage/xtrem-core';
import { asyncArray, datetime, decorators, Logger, Node, Test, Uuid } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremServiceFabric from '../index';
import type { ViesResponseUi } from '../shared-functions/interfaces/company-lookup';

const logger = Logger.getLogger(__filename, 'org');

@decorators.node<OrganisationServiceFabric>({
    package: 'xtrem-service-fabric',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
    serviceOptions: () => [xtremServiceFabric.serviceOptions.serviceFabricOption],
})
export class OrganisationServiceFabric extends Node {
    /**
     *  Get the xtrem-service-fabric package configuration
     */
    static getServiceFabricPackage(context: Context): xtremServiceFabric.interfaces.Package {
        const packageConfig =
            context.configuration.getPackageConfig<xtremServiceFabric.interfaces.Package>('@sage/xtrem-service-fabric');
        if (!packageConfig) {
            context.logger.warn(() => `Missing package config @sage/xtrem-service-fabric`);
        }
        return (
            packageConfig || {
                application: 'sage.online.product',
                authURL: 'https://sandbox-api-money.sage.com/auth-v1',
                signingKey: '',
                apiURL: 'https://compliance-eu1.qa-fabric.sage.com/api-v1',
                lookupURL: 'https://gcc-ror-app.eu.uat.sagecompliance.com',
            }
        );
    }

    /** Get the tenant name from SysTenant info  */
    static async getSystenantName(context: Context): Promise<string> {
        return (
            (await (await context.tryRead(xtremSystem.nodes.SysTenant, { tenantId: context.tenantId! }))?.name) ||
            Test.defaultTenantName
        );
    }

    @decorators.stringProperty<OrganisationServiceFabric, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
    })
    readonly id: Promise<string>;

    @decorators.booleanProperty<OrganisationServiceFabric, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        provides: ['isActive'],
        exportValue: false,
    })
    readonly isActive: Promise<boolean>;

    /** Check if  OrganisationServiceFabric is connected (using token ) */
    @decorators.stringProperty<OrganisationServiceFabric, 'connected'>({
        isPublished: true,
        async computeValue() {
            if (await this.$.decryptValue('token')) {
                return OrganisationServiceFabric.getOrganisation(this.$.context);
            }
            return this.$.context.localize('@sage/xtrem-service-fabric/no-token', 'No token');
        },
    })
    readonly connected: Promise<string>;

    /** Will be the name of the tenant */
    @decorators.stringProperty<OrganisationServiceFabric, 'name'>({
        isPublished: true,
        dataType: () => dataTypes.id,
        computeValue() {
            return OrganisationServiceFabric.getSystenantName(this.$.context);
        },
    })
    readonly name: Promise<string>;

    /** For header  */
    @decorators.stringProperty<OrganisationServiceFabric, 'xApplication'>({
        isPublished: true,
        dataType: () => dataTypes.id,
        computeValue() {
            return OrganisationServiceFabric.getServiceFabricPackage(this.$.context).application;
        },
    })
    readonly xApplication: Promise<string>;

    /** Signing key to generate X-Signature  */
    @decorators.stringProperty<OrganisationServiceFabric, 'signingKey'>({
        isPublished: true,
        dataType: () => dataTypes.id,
        computeValue() {
            return OrganisationServiceFabric.getServiceFabricPackage(this.$.context).signingKey;
        },
    })
    readonly signingKey: Promise<string>;

    /** authentication url   */
    @decorators.stringProperty<OrganisationServiceFabric, 'authURL'>({
        isPublished: true,
        dataType: () => dataTypes.url,
        computeValue() {
            return OrganisationServiceFabric.getServiceFabricPackage(this.$.context).authURL;
        },
    })
    readonly authURL: Promise<string>;

    /** authentication url   */
    @decorators.stringProperty<OrganisationServiceFabric, 'apiURL'>({
        isPublished: true,
        dataType: () => dataTypes.url,
        computeValue() {
            return OrganisationServiceFabric.getServiceFabricPackage(this.$.context).apiURL;
        },
    })
    readonly apiURL: Promise<string>;

    /** lookup id url   */
    @decorators.stringProperty<OrganisationServiceFabric, 'lookupURL'>({
        isPublished: true,
        dataType: () => dataTypes.url,
        computeValue() {
            const { lookupURL } = OrganisationServiceFabric.getServiceFabricPackage(this.$.context);
            if (!lookupURL) {
                xtremServiceFabric.functions.messages.noUrlForLookupApi(this.$.context);
            }
            return lookupURL;
        },
    })
    readonly lookupURL: Promise<string>;

    /**
     * return by create Organization : format like 15d1d904-**************-a68583463183
     */
    @decorators.stringProperty<OrganisationServiceFabric, 'organisationId'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.uuid,
        exportValue: '',
    })
    readonly organisationId: Promise<string>;

    /**
     * return by create Organization : format like 1234599860
     */
    @decorators.stringProperty<OrganisationServiceFabric, 'sageCrmId'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
        exportValue: '',
    })
    readonly sageCrmId: Promise<string>;

    /**
     * return by create Organization : format like FRA
     */
    @decorators.stringProperty<OrganisationServiceFabric, 'primaryCountry'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
    })
    readonly primaryCountry: Promise<string>;

    /**
     * return by create Organization : <NAME_EMAIL>
     */
    @decorators.stringProperty<OrganisationServiceFabric, 'adminEmail'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
        exportValue: '',
    })
    readonly adminEmail: Promise<string>;

    /**
     * return by create Organization : format like EN
     */
    @decorators.stringProperty<OrganisationServiceFabric, 'defaultLanguage'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
    })
    readonly defaultLanguage: Promise<string>;

    /**
     * return by create Organization : format like EN
     */
    @decorators.stringProperty<OrganisationServiceFabric, 'externalId'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.uuid,
        exportValue: '',
    })
    readonly externalId: Promise<string>;

    /**
     * primary Signing Key to sign request Format like : AE08EEC3942AD5B7AEFCC262DF84EFEBAD48DFFEBC8F70278DB1F98B0F19473B
     */
    @decorators.stringProperty<OrganisationServiceFabric, 'primarySigningKey'>({
        isStored: true,
        isPublished: true,
        isStoredEncrypted: true,
        dataType: () => dataTypes.password,
        exportValue: '',
    })
    readonly primarySigningKey: Promise<string>;

    /**
     *  deliver by getOrganisationToken
     */
    @decorators.stringProperty<OrganisationServiceFabric, 'token'>({
        isStored: true,
        isPublished: true,
        isStoredEncrypted: true,
        dataType: () => dataTypes.description,
        exportValue: '',
    })
    readonly token: Promise<string>;

    /**
     *  Creation of the token
     */
    @decorators.datetimeProperty<OrganisationServiceFabric, 'tokenCreation'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly tokenCreation: Promise<datetime | null>;

    /**
     *  is the token valid ?
     *  Token lifetime is 20 min
     */
    @decorators.booleanProperty<OrganisationServiceFabric, 'isTokenValid'>({
        isPublished: true,
        async computeValue() {
            await logger.debugAsync(
                async () =>
                    `token end of life : ${
                        datetime.now().compare((await this.tokenCreation) || datetime.now().addMinutes(-20)) / 60000
                    }mins `,
            );

            return !!(await this.tokenCreation) && datetime.now().compare((await this.tokenCreation)!) < 1200000;
        },
    })
    readonly isTokenValid: Promise<boolean>;

    /**
     * get the default record
     *
     */
    @decorators.query<typeof OrganisationServiceFabric, 'defaultInstance'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'instance',
            node: () => OrganisationServiceFabric,
        },
    })
    static async defaultInstance(context: Context): Promise<OrganisationServiceFabric | null> {
        const organisationInstance = await context
            .query(xtremServiceFabric.nodes.OrganisationServiceFabric, {
                filter: { id: 'DEFAULT' },
            })
            .toArray();
        return organisationInstance.length ? organisationInstance[0] : null;
    }

    /**
     *  Payload to create an organisation
     */
    @decorators.jsonProperty<OrganisationServiceFabric, 'organisation'>({
        isPublished: true,
        async computeValue() {
            return {
                name: await this.name,
                adminEmail: (await this.adminEmail) || (await this.$.context.user)!.email,
                defaultLanguage:
                    (await this.defaultLanguage) || (await this.$.context.user)?.locale?.substring(3) || 'EN',
                externalId: (await this.externalId) || Uuid.generate().toString('-'),
                sageCrmId: (await this.sageCrmId) || this.$.context.tenantId!,
                primaryCountry: (await this.primaryCountry) || 'FRA',
            };
        },
    })
    readonly organisation: Promise<xtremServiceFabric.interfaces.Organisation>;

    // @decorators.jsonProperty<OrganisationServiceFabric, 'companyList'>({
    //     isPublished: true,
    //     computeValue() {
    //         return wait_(xtremServiceFabric.classes.Authentication.getCompanies(this.$.context));
    //     },
    // })
    // companyList: xtremServiceFabric.interfaces.Company[];

    /**
     * Call to create Organization / Request Class
     * @param context
     * @returns
     */
    @decorators.mutation<typeof OrganisationServiceFabric, 'createOrganisation'>({
        isPublished: true,
        parameters: [],
        return: 'boolean',
    })
    static async createOrganisation(context: Context): Promise<boolean> {
        const organisation = await xtremServiceFabric.classes.Authentication.createOrganisation(context);

        const organisationInstance = await context.read(
            xtremServiceFabric.nodes.OrganisationServiceFabric,
            { id: 'DEFAULT' },
            { forUpdate: true },
        );

        await organisationInstance.$.set({
            sageCrmId: organisation.sageCrmId,
            adminEmail: organisation.adminEmail,
            defaultLanguage: organisation.defaultLanguage,
            primaryCountry: organisation.primaryCountry,
            externalId: organisation.externalId,
            organisationId: organisation.organisationId,
            primarySigningKey: organisation.primarySigningKey,
        });

        await organisationInstance.$.save();

        return true;
    }

    /**
     *  Reset the organisation
     * @param context
     * @returns
     */
    @decorators.mutation<typeof OrganisationServiceFabric, 'resetOrganisation'>({
        isPublished: true,
        parameters: [],
        return: 'boolean',
    })
    static async resetOrganisation(context: Context): Promise<boolean> {
        const organisationInstance = await context.read(
            xtremServiceFabric.nodes.OrganisationServiceFabric,
            { id: 'DEFAULT' },
            { forUpdate: true },
        );
        await organisationInstance.$.set({
            organisationId: '',
            primarySigningKey: '',
            token: '',
        });

        await organisationInstance.$.save();
        return true;
    }

    /**
     * Get the token from sageServiceFabric & Save the token to be able to use it
     * @param context
     * @returns
     */
    @decorators.mutation<typeof OrganisationServiceFabric, 'saveToken'>({
        isPublished: true,
        parameters: [],
        return: 'string',
    })
    static async saveToken(context: Context): Promise<string> {
        const token = await xtremServiceFabric.classes.Authentication.getOrganisationToken(context);

        if (context.isWritable) {
            const organisationInstance = await context.read(
                xtremServiceFabric.nodes.OrganisationServiceFabric,
                { id: 'DEFAULT' },
                { forUpdate: true },
            );

            if (!organisationInstance.$.isEffectivelyReadonly) {
                await organisationInstance.$.set({ token, tokenCreation: datetime.now() });
                await organisationInstance.$.trySave();
            }
        }

        return token;
    }

    @decorators.query<typeof OrganisationServiceFabric, 'getOrganisation'>({
        isPublished: true,
        parameters: [],
        return: 'string',
    })
    static async getOrganisation(context: Context): Promise<string> {
        const organisation = await xtremServiceFabric.classes.Authentication.getOrganisation(context);

        if (xtremServiceFabric.functions.instanceOfOrganisation(organisation)) {
            return organisation.name;
        }
        return organisation.message;
    }

    @decorators.query<typeof OrganisationServiceFabric, 'getCompanyList'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    name: 'string',
                    externalId: 'string',
                    taxNumber: 'string',
                    standardIndustrialCode: 'string',
                    contactTelNo: 'string',
                    contactEmail: 'string',
                    address: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                addressLine1: 'string',
                                addressLine2: 'string',
                                addressLine3: 'string',
                                addressLine4: 'string',
                                countrySubdivision: 'string',
                                postalCode: 'string',
                                country: 'string',
                            },
                        },
                    },
                },
            },
        },
    })
    static getCompanyList(context: Context): Promise<xtremServiceFabric.interfaces.Company[]> {
        return xtremServiceFabric.classes.Authentication.getCompanies(context);
    }

    @decorators.query<typeof OrganisationServiceFabric, 'getLinkedCompanyList'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    id: 'string',
                    name: 'string',
                    externalId: 'string',
                    companyId: 'string',
                    isCreated: 'boolean',
                },
            },
        },
    })
    static async getLinkedCompanyList(
        context: Context,
    ): Promise<{ id: string; name: string; externalId: string; isCreated: boolean }[]> {
        const serviceFabricCompany = await xtremServiceFabric.classes.Authentication.getCompanies(context);

        return asyncArray(await context.query(xtremSystem.nodes.Company).toArray())
            .map(async company => {
                return {
                    id: await company.id,
                    name: await company.name,
                    externalId: await company.serviceFabricExternalId,
                    companyId: await company.serviceFabricCompanyId,
                    isCreated:
                        Array.isArray(serviceFabricCompany) &&
                        (await asyncArray(serviceFabricCompany).some(
                            async sfCompany => sfCompany.externalId === (await company.serviceFabricExternalId),
                        )),
                };
            })
            .toArray();
    }

    /**
     * TODO : Move it to companyextension node
     * Call to create Company / Authentication Class
     * @param context
     * @returns
     */
    @decorators.mutation<typeof OrganisationServiceFabric, 'createServiceFabricCompany'>({
        isPublished: true,
        parameters: [{ name: 'id', type: 'string' }],
        return: 'boolean',
    })
    static async createServiceFabricCompany(context: Context, id: string): Promise<boolean> {
        const company = await context.read(xtremSystem.nodes.Company, { id }, { forUpdate: true });
        const companyCreated = await xtremServiceFabric.classes.Authentication.createCompany(
            context,
            await company.serviceFabricCompany,
        );
        if ((await company.serviceFabricExternalId) !== companyCreated.externalId) {
            await company.$.set({ serviceFabricExternalId: companyCreated.externalId });
        }
        await company.$.set({ serviceFabricCompanyId: companyCreated.companyId });
        return company.$.trySave();
    }

    /**
     * TODO : Move it to companyExtension node
     * Call to create Company / Authentication Class
     * mutation because we save the token if expired
     * @param context
     * @returns
     */
    @decorators.mutation<typeof OrganisationServiceFabric, 'companyIdValidation'>({
        isPublished: true,
        serviceOptions: () => [xtremServiceFabric.serviceOptions.serviceFabricTaxId],
        parameters: [
            { name: 'country', type: 'string' },
            { name: 'taxID', type: 'string' },
        ],
        return: {
            type: 'object',
            properties: {
                countryCode: 'string', // "FR",
                vatNumber: 'string', // 'FR123456';
                valid: 'boolean',
                status: 'string', // success failure,
                statusCode: 'string', // 200 500,
                traderCity: 'string',
                traderPostCode: 'string',
                traderName: 'string',
                traderStreet: 'string',
                traderAddress: 'string',
                requestDate: 'string', // "2022-03-10T01:00:00.000+00:00", TODO: si if can put datetime
            },
        },
    })
    static async companyIdValidation(context: Context, country: string, taxID: string): Promise<ViesResponseUi> {
        const data = await (await xtremServiceFabric.classes.CompanyLookup.create(context)).validation(country, taxID);
        return {
            ...data.vies_response,
            status: data.vies_response.$status,
            statusCode: data.vies_response.$statusCode,
        };
    }

    /**
     * @returns array of availables country for company taxId Lookup & Validation
     */
    @decorators.query<typeof OrganisationServiceFabric, 'availableCountry'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'array',
            item: { type: 'string' },
        },
        serviceOptions: () => [xtremServiceFabric.serviceOptions.serviceFabricTaxId],
    })
    static availableCountry(context: Context): string[] {
        context.logger.debug(() => `Supported country`);
        return xtremServiceFabric.classes.CompanyLookup.supportedCountry;
    }

    /**
     * @param country test if the supportedCountry array include the contry code pass in parameter
     * @returns Boolean
     */
    @decorators.mutation<typeof OrganisationServiceFabric, 'isAvailableCountry'>({
        isPublished: true,
        parameters: [{ name: 'country', type: 'string' }],
        return: {
            type: 'boolean',
        },
        serviceOptions: () => [xtremServiceFabric.serviceOptions.serviceFabricTaxId],
    })
    static isAvailableCountry(context: Context, country: string): boolean {
        context.logger.debug(() => `Test if supported country ${country}`);
        return xtremServiceFabric.classes.CompanyLookup.supportedCountry.includes(country);
    }
}
