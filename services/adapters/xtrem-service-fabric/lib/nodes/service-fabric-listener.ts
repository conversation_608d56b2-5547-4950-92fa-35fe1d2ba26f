import type * as xtremCommunication from '@sage/xtrem-communication';
import type { Context } from '@sage/xtrem-core';
import { asyncArray, decorators, Logger, Node } from '@sage/xtrem-core';
import * as xtremServiceFabric from '../index';

const logger = Logger.getLogger(__filename, 'listener');

@decorators.node<ServiceFabricListener>({
    package: 'xtrem-service-fabric',
})
export class ServiceFabricListener extends Node {
    /**
     *  Create tax from Service Fabric
     * @param listenerContext
     * @param payload
     */
    @decorators.notificationListener<typeof ServiceFabricListener>({
        topic: 'serviceFabric/createTax',
        startsReadOnly: true,
        onError(
            context: Context,
            envelope: xtremCommunication.NotificationEnvelope<xtremServiceFabric.interfaces.Listener.ServiceFabricIdList>,
            error: Error,
        ): void {
            context.logger.error(() => `${JSON.stringify(envelope)} ${JSON.stringify(error)}`);
        },
    })
    static async createTax(
        listenerContext: Context,
        payload: xtremServiceFabric.interfaces.Listener.ServiceFabricIdList,
    ): Promise<void> {
        logger.debug(() => `${payload.idList.join(' - ')}`);

        await asyncArray(payload.idList).forEach(async id => {
            await listenerContext.runInWritableContext(writableContext =>
                xtremServiceFabric.functions.tax.createTax(writableContext, id, payload.legislationID),
            );
        });
    }
}
