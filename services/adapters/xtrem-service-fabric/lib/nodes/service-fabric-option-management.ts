import type { Context } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import { serviceFabricOption } from '../service-options/service-fabric-option';

@decorators.subNode<ServiceFabricOptionManagement>({
    extends: () => xtremStructure.nodes.BaseOptionManagement,
    isPublished: true,
})
export class ServiceFabricOptionManagement extends xtremStructure.nodes.BaseOptionManagement {
    @decorators.query<typeof ServiceFabricOptionManagement, 'isServiceOptionActiveFunction'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'boolean',
        },
    })
    static isServiceOptionActiveFunction(context: Context) {
        return ServiceFabricOptionManagement.baseIsServiceOptionActiveFunction(context, serviceFabricOption);
    }

    @decorators.mutation<typeof ServiceFabricOptionManagement, 'serviceOptionChange'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'boolean',
        },
    })
    static serviceOptionChange(context: Context): Promise<boolean> {
        return ServiceFabricOptionManagement.baseServiceOptionChange(context, serviceFabricOption);
    }
}
