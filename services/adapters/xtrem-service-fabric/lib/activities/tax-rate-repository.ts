import { Activity } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import { TaxRateRepository } from '../nodes/tax-rate-repository';

export const taxRateRepository = new Activity({
    description: 'Tax rate repository',
    node: () => TaxRateRepository,
    __filename,
    permissions: ['manage'],
    operationGrants: {
        manage: [
            {
                operations: [
                    'getTaxLegislation',
                    'getTaxRegion',
                    'getTaxRate',
                    'getTaxPercentage',
                    'getTaxCode',
                    'healthCheck',
                    'getTranslation',
                    'defaultInstance',
                ],
                on: [() => TaxRateRepository],
            },
            { operations: ['lookup', 'update'], on: [() => xtremStructure.nodes.Legislation] },
        ],
    },
});
