import { Activity } from '@sage/xtrem-core';
import { OrganisationServiceFabric } from '../nodes/organisation-service-fabric';

export const organisationServiceFabric = new Activity({
    description: 'Configuration',
    node: () => OrganisationServiceFabric,
    __filename,
    permissions: ['manage'],
    operationGrants: {
        manage: [
            {
                operations: [
                    'read',
                    'create',
                    'update',
                    'delete',
                    'defaultInstance',
                    'getOrganisation',
                    'getCompanyList',
                    'availableCountry',
                    'getLinkedCompanyList',
                    'createOrganisation',
                    'resetOrganisation',
                    'saveToken',
                    'createServiceFabricCompany',
                    'companyIdValidation',
                    'isAvailableCountry',
                ],
                on: [() => OrganisationServiceFabric],
            },
        ],
    },
});
