import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, Diagnose, SystemError, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type { RawAxiosRequestHeaders } from 'axios';
import * as xtremServiceFabric from '../index';
import type { Filter } from './filter';
import { Request } from './request';
/**
 * ## TAX RATE REPOSITORY API
 * https://developer.sage.com/internal/compliance-service/api/reference/tax-rate/
 */
export class TaxRateRepository extends Request {
    public api = 'tax-rate-repository';

    /** Extends getHeader method for X-Business-Country-Code */
    override async getHeaders(): Promise<RawAxiosRequestHeaders> {
        const headers = await super.getHeaders();
        const company = await this.company;

        return {
            ...headers,
            'X-Business-Country-Code': company
                ? (await (await (await company.primaryAddress)?.country)?.iso31661Alpha3) || ''
                : '',
        };
    }

    /**
     * ## Supported tax legislations ##
     * @param filter Example: $filter=id eq 'TL1UK'* Filtering of data - single filter only. Based on Sage REST standards Supported operators: eq
     * @returns Returns list of configured tax legislations    */
    taxLegislation(filter?: Filter): Promise<xtremServiceFabric.interfaces.TaxRateRepository.TaxLegislation[]> {
        return this.getRequest<xtremServiceFabric.interfaces.TaxRateRepository.TaxLegislation>(
            'taxLegislations',
            filter,
        );
    }

    /**
     * ## Supported tax regions ##
     * @param filter Example: $filter=id eq 'TL1UK' Filtering of data - single filter only. Based on Sage REST standards Supported operators: eq
     * @returns Returns list of configured tax regions  */
    taxRegion(filter?: Filter): Promise<xtremServiceFabric.interfaces.TaxRateRepository.TaxRegion[]> {
        return this.getRequest<xtremServiceFabric.interfaces.TaxRateRepository.TaxRegion>('taxRegions', filter);
    }

    /** Request to taxRates api
     * Returns list of configured tax rates     */
    taxRate(filter?: Filter): Promise<xtremServiceFabric.interfaces.TaxRateRepository.TaxRate[]> {
        return this.getRequest<xtremServiceFabric.interfaces.TaxRateRepository.TaxRate>('taxRates', filter);
    }

    /** request to  taxPercentages API
     * Returns list of configured tax rates   */
    taxPercentages(filter?: Filter): Promise<xtremServiceFabric.interfaces.TaxRateRepository.TaxPercentage[]> {
        return this.getRequest<xtremServiceFabric.interfaces.TaxRateRepository.TaxPercentage>('taxPercentages', filter);
    }

    /** request to  taxPercentages API
     * Returns list of configured tax rates   */
    taxCodes(filter?: Filter): Promise<xtremServiceFabric.interfaces.TaxRateRepository.TaxCode[]> {
        return this.getRequest<xtremServiceFabric.interfaces.TaxRateRepository.TaxCode>('taxCodes', filter);
    }

    /** request to  translations API
     * Returns translation   */
    translations(filter?: Filter): Promise<xtremServiceFabric.interfaces.TaxRateRepository.Translation[]> {
        return this.getRequest<xtremServiceFabric.interfaces.TaxRateRepository.Translation>('translations', filter);
    }

    /**
     *  Generic getRequest execute & throw if error
     * @param apiName Name of the api to get
     * @param filter     */
    async getRequest<T>(apiName: string, filter?: Filter): Promise<T[]> {
        this.parameters = {
            method: 'GET',
            withToken: true,
            url: `${await (await this.activeInstance).apiURL}/${this.api}/${apiName}${filter?.toString() || ''}`,
        };

        const taxPercentageResult = await this.execute<{ data: T[] }>();

        if (taxPercentageResult.data) {
            return taxPercentageResult.data;
        }
        throw new BusinessRuleError(`${this.manageError(taxPercentageResult as any)}`);
    }

    /**
     * @param error code / Message
     * @returns Return the right error from error or from diagnose
     */
    manageError(error: xtremServiceFabric.interfaces.TaxRateRepository.TrrError): string {
        if (error && error.code && error.message) {
            this.diagnoses.push(
                new Diagnose(ValidationSeverity.error, ['TaxRateResository'], `${error.code} ${error.message}`),
            );
        }
        if (this.diagnoses.length) {
            return this.diagnoses.map(diag => `${diag.toString()}`).join('\n');
        }
        throw new SystemError(`Sage Service Fabric : Not managed error ${JSON.stringify(error)}`);
    }

    /**
     * Health check of tax-rate-repository api
     * @param context
     */
    static healthCheck(context: Context): Promise<xtremServiceFabric.interfaces.TaxRateRepository.HealthCheck> {
        const packageParam = xtremServiceFabric.nodes.OrganisationServiceFabric.getServiceFabricPackage(context);

        const healthCheck = new xtremMasterData.classes.RequestGeneric(context, {
            method: 'GET',
            url: `${packageParam.apiURL}/tax-rate-repository/health_check`,
        });
        return healthCheck.execute<xtremServiceFabric.interfaces.TaxRateRepository.HealthCheck>();
    }
}
