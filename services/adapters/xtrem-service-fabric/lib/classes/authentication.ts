import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, Logger } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremServiceFabric from '../index';
import { RequestMain } from './request-main';

const authLogger = Logger.getLogger(__filename, 'authentication');

/**
 *  Authentication class for Sage Service Fabric
 */
export class Authentication extends RequestMain {
    /**
     * Check if the organisation is created & stored
     */
    get isOrganisationCreated(): Promise<boolean> {
        return (async () => {
            return (
                (await (await this.activeInstance).isActive) &&
                !!(await (
                    await this.activeInstance
                ).externalId) &&
                !!(await (
                    await this.activeInstance
                ).primarySigningKey)
            );
        })();
    }

    // Overide RequestMain get the authURL from organisation ( xtrem-config.yml ) & the api
    override get url(): Promise<string> {
        return (async () => {
            return `${await (await this.activeInstance).authURL}/${this.api}`;
        })();
    }

    /**
     * Default api is organisations else it will come from params.api
     */
    get api() {
        return this.params?.api || 'organisations';
    }

    /**
     * Constructor method by default is the GET
     * @param context
     * @param params default api is organisations
     */
    constructor(
        context: Context,
        public params?: xtremServiceFabric.interfaces.AuthenticationParams,
    ) {
        super(context, { method: 'GET', withToken: false });
    }

    /** Throw an error if not active & no organisation info set   */
    async checkOrganisation(): Promise<void> {
        if (!(await this.isOrganisationCreated)) {
            xtremServiceFabric.functions.messages.organisationNotCreated(this.context);
        }
    }

    /**
     *  Create the organisation ( 1 Organisation = 1 Tenant )
     * @param organisation
     * @returns
     */
    async createOrganisation(
        organisation?: xtremServiceFabric.interfaces.Organisation,
    ): Promise<xtremServiceFabric.interfaces.Organisation> {
        const organisationData = organisation || (await (await this.activeInstance).organisation);
        this.parameters = {
            method: 'POST',
            withToken: false,
            data: { ...organisationData, sageCRMId: organisationData.sageCrmId },
        };
        const createdOrganisation = await this.execute<xtremServiceFabric.interfaces.Organisation>();
        authLogger.debug(
            () => `createOrganisation - ${createdOrganisation.organisationId} - ${createdOrganisation.externalId}`,
        );
        return createdOrganisation;
    }

    /**
     *  Get the organisation token
     *  TODO : Check if we can define a time there
     *  using organisationId & the signingKey received when create the organisation
     * @returns token
     */
    async getOrganisationToken(): Promise<string> {
        await this.checkOrganisation();

        this.params = { api: 'accesstoken' };
        this.parameters = {
            method: 'GET',
            withToken: false,
            additionnalHeaders: { 'X-Organisation-Id': await (await this.activeInstance).organisationId },
            signingKey: await (await this.activeInstance).$.decryptValue('primarySigningKey'),
        };

        const result = await this.execute<{ jwt: string }>();
        authLogger.debug(() => `getOrganisationToken - ${JSON.stringify(result.errors)}`);
        return result.jwt;
    }

    /**
     * Get the organisation link to the token
     * @returns Organisation or message error
     */
    async getOrganisation(): Promise<xtremServiceFabric.interfaces.Organisation | { message: string }> {
        await this.checkOrganisation();
        this.params = { api: 'organisations' };
        this.parameters = {
            method: 'GET',
            withToken: true,
        };
        const organisation = await this.execute<xtremServiceFabric.interfaces.Organisation>();
        authLogger.debug(() => `getOrganisation - ${organisation.organisationId} - ${organisation.externalId}`);
        return organisation;
    }

    /**
     * static - Create a company
     * @param company company payload
     * @returns Company info
     */
    async createCompany(
        company: xtremServiceFabric.interfaces.Company,
    ): Promise<xtremServiceFabric.interfaces.Company> {
        await this.checkOrganisation();
        this.params = { api: 'companies' };
        this.parameters = {
            method: 'POST',
            withToken: true,
            data: company,
        };
        const companyCreated = await this.execute<xtremServiceFabric.interfaces.Company>();

        if (this.diagnoses.length) {
            throw new BusinessRuleError(this.diagnoses.map(diag => `${diag.message}`).join(' \n '));
        }

        return companyCreated;
    }

    /** Get the list of the company of the organisation */
    async getCompanies(doNotThrow = true): Promise<xtremServiceFabric.interfaces.Company[]> {
        if (!(await this.isOrganisationCreated)) {
            if (doNotThrow) {
                return [];
            }
            xtremServiceFabric.functions.messages.organisationNotCreated(this.context);
        }

        this.params = { api: 'companies' };
        this.parameters = {
            method: 'GET',
            withToken: true,
        };

        const result = await this.execute<xtremServiceFabric.interfaces.Company[]>();

        if (!doNotThrow) {
            this.throwIfDiagnose();
        }
        return result;
    }

    /**
     * Get the info of a specific company
     * @param companyId company ID
     * @returns Company info
     */
    async getCompany(companyId: string): Promise<xtremServiceFabric.interfaces.Company[]> {
        this.params = { api: 'companies' };
        this.parameters = {
            method: 'GET',
            withToken: true,
            url: `${await (await this.activeInstance).authURL}/companies/${companyId}`,
        };

        const result = await this.execute<xtremServiceFabric.interfaces.Company[]>();
        authLogger.debug(() => `getCompany - ${JSON.stringify(result)}`);

        return result;
    }

    /**
     *  Update a company
     * @param company company payload
     * @returns
     */
    async updateCompany(
        company: xtremServiceFabric.interfaces.Company,
    ): Promise<xtremServiceFabric.interfaces.Company[]> {
        const data = xtremStructure.functions.getPathValue(company).map(line => {
            return {
                op: 'replace',
                ...line,
            };
        });

        this.parameters = {
            method: 'PATCH',
            withToken: true,
            url: `${await (await this.activeInstance).authURL}/companies/${company.externalId}`,
            data,
        };

        const result = await this.execute<xtremServiceFabric.interfaces.Company[]>();
        authLogger.debug(() => `updateCompany - ${JSON.stringify(result)}`);
        return result;
    }

    /** START OF STATICS  */

    /**
     * static - Create the organisation
     * @returns Organisation
     */
    static async createOrganisation(context: Context): Promise<xtremServiceFabric.interfaces.Organisation> {
        const createOrg = await new Authentication(context).createOrganisation();
        authLogger.debug(() => `createOrganisation - ${createOrg.organisationId}`);
        return createOrg;
    }

    /**
     * Get the token of the organisation
     * @returns the token
     */
    static async getOrganisationToken(context: Context): Promise<string> {
        const token = await new Authentication(context).getOrganisationToken();
        authLogger.debug(() => `getOrganisationToken - ${JSON.stringify(token)}`);

        return token;
    }

    /**
     * Get the current organisation in case of error return a message
     * @returns
     */
    static async getOrganisation(
        context: Context,
    ): Promise<xtremServiceFabric.interfaces.Organisation | { message: string }> {
        const getOrg = await new Authentication(context).getOrganisation();
        authLogger.debug(() => `getOrganisation - ${JSON.stringify(getOrg)}`);

        return getOrg;
    }

    /**
     * static - Create a company
     * @returns Company info
     */
    static async createCompany(
        context: Context,
        company: xtremServiceFabric.interfaces.Company,
    ): Promise<xtremServiceFabric.interfaces.Company> {
        const companyCreated = await new Authentication(context).createCompany(company);
        authLogger.debug(() => `createCompany - ${companyCreated.companyId}`);

        return companyCreated;
    }

    /**
     * Get company list
     * @returns
     */
    static async getCompanies(context: Context, doNotThrow = true): Promise<xtremServiceFabric.interfaces.Company[]> {
        const result = await new Authentication(context).getCompanies(doNotThrow);
        authLogger.debug(() => `getCompanies - ${JSON.stringify(result)}`);

        return result;
    }

    /**
     * Get a company with companyId ( set with _id )
     * @returns
     */
    static async getCompany(context: Context, companyId: string): Promise<xtremServiceFabric.interfaces.Company[]> {
        const company = await new Authentication(context).getCompany(companyId);
        authLogger.debug(() => `getCompany - ${JSON.stringify(company)}`);

        return company;
    }

    /**
     * Get a company with companyId ( set with _id )
     * @returns
     */
    static async updateCompany(
        context: Context,
        company: xtremServiceFabric.interfaces.Company,
    ): Promise<xtremServiceFabric.interfaces.Company[]> {
        const companyUpdate = await new Authentication(context).updateCompany(company);
        authLogger.debug(() => `updateCompany - ${JSON.stringify(companyUpdate)}`);

        return companyUpdate;
    }
}
