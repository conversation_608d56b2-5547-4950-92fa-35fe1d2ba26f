// eslint-disable-next-line @typescript-eslint/consistent-type-imports
import { Context, Logger, Uuid } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import type { RawAxiosRequestHeaders } from 'axios';
import { RequestMain } from './request-main';

// TODO : define where to find this variables
const XApiUserID = 'ff2c0b9a-9b0e-11ec-b909-0242ac120002';
const defaultXCompanyID = 'efa51259-254c-49bf-aa03-cecf7d3fc026';

const requestLogger = Logger.getLogger(__filename, 'request');

/**
 * Request class to Sage Service Fabric / companyLookupID & TRR/TDE
 */
export class Request extends RequestMain {
    protected xExecutionID: string;

    constructor(context: Context) {
        super(context);
        this.xExecutionID = Uuid.generate().toString('-');
        this.authHeader.push('X-Request-ID', 'X-Execution-ID', 'X-Application-Version');
    }

    private uCompany: xtremSystem.nodes.Company | undefined;

    get company(): Promise<xtremSystem.nodes.Company | undefined> {
        return (async () => {
            if (this.uCompany) {
                return this.uCompany;
            }

            this.uCompany = (
                await this.context
                    .query(xtremSystem.nodes.Company, {
                        filter: { serviceFabricCompanyId: { _ne: '' } },
                    })
                    .toArray()
            ).shift();

            if (!this.uCompany) {
                this.logger.warn(() => ` No company created for the organisation`);
            }

            return this.uCompany;
        })();
    }

    /** extend headers as it's not the same as authentication part  */
    override async getHeaders(): Promise<RawAxiosRequestHeaders> {
        requestLogger.debug(() => `Request - getHeaders`);

        const headers = await super.getHeaders();
        const company = await this.company;

        return {
            ...headers,
            'X-Request-ID': Uuid.generate().toString('-'),
            'X-Api-User-ID': XApiUserID,
            'X-Application-Version': this.context.application.version,
            'X-Business-ID': await (await this.activeInstance).organisationId,
            'X-Company-ID': company ? await company.serviceFabricCompanyId : defaultXCompanyID,
            'X-Signatory': 'sage-intacct',
            'X-Execution-ID': this.xExecutionID,
            'X-User-IP': this.context.userIp,
        };
    }
}

declare module '@sage/xtrem-core/lib/runtime/context' {
    export interface Context extends ContextInternal {}
}
