import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, Diagnose, SystemError, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremServiceFabric from '../index';
import { Request } from './request';

/**
 * ## Compliance Service API for ID Validation & Look-up
 *  Documentation : https://developer.sage.com/internal/compliance-service/api/reference/id-validation-look-up/
 *  /!\ Not up to date
 */
export class CompanyLookup extends Request {
    private constructor(context: Context) {
        super(context);
    }

    private async init(): Promise<this> {
        if (!(await (await this.activeInstance).lookupURL)) {
            xtremServiceFabric.functions.messages.noUrlForLookupApi(this.context);
        }
        return this;
    }

    static create(context: Context): Promise<CompanyLookup> {
        return new CompanyLookup(context).init();
    }

    /**
     * trim , then replace FR by '' to be validate
     * @param country country code
     * @param id to validate
     * @returns id in a correct format
     */
    static countryIdUsage(country: string, id: string) {
        const trimId = id.trim();
        switch (country) {
            case 'FR':
                if (trimId.includes('FR')) {
                    return trimId.replace('FR', '');
                }
                return trimId;
            default:
                return trimId;
        }
    }

    /**
     *  Validate company
     * @param country code
     * @param id id to validate
     * @returns
     */
    async validation(
        country: string,
        id: string,
    ): Promise<xtremServiceFabric.interfaces.CompanyLookup.ViesResponseResult> {
        if (!CompanyLookup.supportedCountry.includes(country)) {
            throw new BusinessRuleError(
                this.context.localize(
                    '@sage/xtrem-service-fabric/country-not-available',
                    '{{country}} not available for ID Validation & Look-up',
                    { country },
                ),
            );
        }
        const validID = CompanyLookup.countryIdUsage(country, id);

        const urlParameters: { [key: string]: string } = { 'company-search-by': 'VAT', serviceType: 'VIES' };
        this.parameters = {
            method: 'GET',
            withToken: true,
            urlParameters,
            url: `${await (await this.activeInstance).lookupURL}/api-v5/countries/${country}/companies/${validID}`,
        };

        const validationPolling = await this.execute<xtremServiceFabric.interfaces.CompanyLookup.PollLinks>();
        if (typeof validationPolling === 'string') {
            throw new BusinessRuleError(validationPolling);
        }
        if (validationPolling.$diagnoses) {
            throw new BusinessRuleError(validationPolling.$diagnoses);
        }

        const result = await this.validationPolling(validationPolling);

        this.logger.debug(() => `Returned result : ${JSON.stringify(result)}`);

        this.addDiagnose(result.vies_response?.$diagnoses);
        this.addDiagnose(result.diagnoses);

        return result;
    }

    /**
     *  Adding diagnose
     * TODO : * See if trr/tde have the same structure for diagnoses // can be put in a lower leverl
     * @param $diagnoses Array of diagnoses // can be null
     */
    addDiagnose($diagnoses?: xtremServiceFabric.interfaces.CompanyLookup.Diagnoses[]) {
        if ($diagnoses && Array.isArray($diagnoses) && $diagnoses.length > 0) {
            this.diagnoses.push(
                ...$diagnoses.map(diag => {
                    const severity: ValidationSeverity =
                        diag.$severity === 'error' ? ValidationSeverity.error : ValidationSeverity.info;
                    return new Diagnose(
                        severity,
                        ['CompanyLookup', 'validation', 'V00002'],
                        `${diag.$type}${diag.$message || ''}`,
                    );
                }),
            );
            this.throwIfDiagnose();
        }
    }

    /**
     * check if the payload is ok & if we have the pollingUrl
     *  Then launch the loop to wait for the completion of the poll
     * @param pollingPayload payload containing the link of the polling
     * @returns
     */
    async validationPolling(
        pollingPayload: xtremServiceFabric.interfaces.CompanyLookup.PollLinks,
    ): Promise<xtremServiceFabric.interfaces.CompanyLookup.ViesResponseResult> {
        if (
            pollingPayload.stage === 'process_started' &&
            pollingPayload.links &&
            pollingPayload.links.$key === 'POLLING-URL'
        ) {
            this.parameters!.url = pollingPayload.links.$url;
            delete this.parameters!.urlParameters;

            const pollingResult = await this.pollingRequestLoop();
            return this.getFilesFromUrl(pollingResult);
        }

        this.logger.error(() => `${JSON.stringify(pollingPayload, null, 4)}`);
        throw new SystemError('Error on validationPolling ');
    }

    getFilesFromUrl(
        pollingResult: xtremServiceFabric.interfaces.CompanyLookup.Files,
    ): Promise<xtremServiceFabric.interfaces.CompanyLookup.ViesResponseResult> {
        if (
            pollingResult &&
            pollingResult.files &&
            Array.isArray(pollingResult.files) &&
            pollingResult.files.length > 0
        ) {
            this.parameters!.url = pollingResult.files[0].$url;
            this.logger.debug(() => `get ${this.parameters!.url} file `);
            return this.execute<xtremServiceFabric.interfaces.CompanyLookup.ViesResponseResult>();
        }
        this.logger.error(() => `${JSON.stringify(pollingResult, null, 4)}`);
        return Promise.reject(new SystemError('Error on requesting files from poll '));
    }

    /**
     *  Loop on the pooling url :
     *  TODO : add error handling!
     * @returns
     */
    async pollingRequestLoop(): Promise<xtremServiceFabric.interfaces.CompanyLookup.Files> {
        const boolContinue = true;
        while (boolContinue) {
            await new Promise(resolve => {
                setTimeout(resolve, 1000);
            });
            const validationResult = await this.execute<xtremServiceFabric.interfaces.CompanyLookup.Files>();
            switch (validationResult.stage) {
                case 'submitted':
                    if (validationResult.files && validationResult.files.length > 0) {
                        return validationResult;
                    }
                    this.logger.debug(() => `submitted but no files`);
                    break;
                case 'submission_uncorrectable_errors':
                case 'documents_uncorrectable_errors':
                    if (validationResult.files && validationResult.files.length > 0) {
                        return validationResult;
                    }
                    this.logger.error(() => `Not handle : \n ${JSON.stringify(validationResult, null, 4)}`);
                    // TODO : catch & add on diagnose then throw ?
                    break;
                case 'process_started' /** Continue process */:
                case 'submission_requested':
                    break;
                default:
                    this.logger.warn(() => `Not handle : \n ${JSON.stringify(validationResult, null, 4)}`);
                    break;
            }
        }
        throw new SystemError('Error on polling request loop ');
    }

    /** Suuported country available for the api */
    static get supportedCountry() {
        return [
            'GB',
            'AT',
            'BE',
            'BG',
            'CY',
            'CZ',
            'DE',
            'DK',
            'EE',
            'ES',
            'FI',
            'FR',
            'HR',
            'HU',
            'IE',
            'IT',
            'LT',
            'LU',
            'LV',
            'MT',
            'NL',
            'PL',
            'PT',
            'RO',
            'SE',
            'SI',
            'SK',
            'XI',
        ];
    }
}
