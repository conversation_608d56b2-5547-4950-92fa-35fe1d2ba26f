import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, Logger } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type { AsyncResponse } from '@sage/xtrem-shared';
import type { Method, RawAxiosRequestHeaders } from 'axios';
import * as xtremServiceFabric from '../index';

export interface RequestConstructor {
    method: Method;
    urlParameters?: { [key: string]: string };
    data?: any;
    logger?: Logger;
    url?: string;
    additionnalHeaders?: any;
    withToken?: boolean;
    signingKey?: string;
}

const requestMainLogger = Logger.getLogger(__filename, 'request-main');

/**
 * Class Request Main linked to the organisation main instance
 */
export class RequestMain extends xtremMasterData.classes.RequestGeneric {
    private uActiveInstance: xtremServiceFabric.nodes.OrganisationServiceFabric;

    /** get the OrganisationServiceFabric default instance */
    get activeInstance() {
        if (this.uActiveInstance) {
            return this.uActiveInstance;
        }
        return this.activeInstanceRefresh();
    }

    async activeInstanceRefresh(): Promise<xtremServiceFabric.nodes.OrganisationServiceFabric> {
        const instance = await xtremServiceFabric.nodes.OrganisationServiceFabric.defaultInstance(this.context);
        if (!instance || !(await instance.isActive)) {
            throw new BusinessRuleError(
                this.context.localize('@sage/xtrem-service-fabric/no-active-configuration', 'No active configuration'),
            );
        }
        this.uActiveInstance = instance;
        return this.uActiveInstance;
    }

    /**
     * SignKey comming from configuration package, can be overloaded with parameters
     */
    get signKey(): AsyncResponse<string> {
        return this.parameters?.signingKey || this.uActiveInstance.signingKey;
    }

    /**
     *
     * @param context
     * @param parameters if no signingKey get the signingKey of the config file
     */
    constructor(
        public override readonly context: Context,
        public override parameters?: RequestConstructor,
    ) {
        super(context, {
            data: parameters?.data,
            logger: parameters?.logger,
            method: parameters?.method,
            urlParameters: parameters?.urlParameters,
            url: parameters?.url,
        });
        this.headers = {
            ...this.headers,
            ...parameters?.additionnalHeaders,
        };
    }

    /**
     *  Sign the request with the sign key
     * @returns Signature of the request & Nonce uuid
     */
    async getSignedRequest(): Promise<{ 'X-Signature': string; 'X-Nonce': string }> {
        /** Throw if no signingKey  */
        xtremServiceFabric.functions.checkSigningKey(this.context, await this.signKey);

        const bodyAndParameters = xtremServiceFabric.functions.bodyAndParametersEncode(
            this.urlParametersObject,
            this.data,
        );

        const xSignature = xtremServiceFabric.functions.generateXSignature({
            baseString: { endpointUrl: await this.url, method: this.method, bodyAndParameters },
            signingKey: await this.signKey,
        });
        this.authHeader.push('X-Nonce', 'X-Signature');
        return { 'X-Signature': xSignature.xSignature, 'X-Nonce': xSignature.xNonceUuid };
    }

    /**
     *  if the token isn't valid  return the new saved one else decrypt & returns
     * @returns
     */
    private async getToken(): Promise<string> {
        if (!(await (await this.activeInstance).isTokenValid)) {
            const token = await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(this.context);
            // Need to clear the cache
            await this.activeInstanceRefresh();
            return token;
        }
        return (await this.activeInstance).$.decryptValue('token');
    }

    /**
     *  Search the token  & return Authorization Bearer
     * if the token isn't valid & the context is writable , renew the token
     * @returns
     */
    async getBearerAuthentication(): Promise<{ Authorization: string }> {
        return {
            Authorization: `Bearer ${await this.getToken()}`,
        };
    }

    /**
     *  Get the authentication header ( Bearer or Sign )
     *  add the X-Application
     * @returns
     */
    async getHeaders(): Promise<RawAxiosRequestHeaders> {
        requestMainLogger.debug(() => `RequestMain - getHeaders`);
        const authenticationHeader = this.parameters?.withToken
            ? await this.getBearerAuthentication()
            : await this.getSignedRequest();

        return {
            ...this.headers,
            'X-Application': await (await this.activeInstance).xApplication,
            ...authenticationHeader,
            ...this.parameters?.additionnalHeaders,
        };
    }

    /**
     *  Overloading generic request
     * generateXSignature  & Execute axios call
     * @returns
     */
    override async execute<T extends {}>(): Promise<xtremMasterData.classes.RequestGenericResponse<T>> {
        requestMainLogger.debug(() => `RequestMain - execute`);
        this.headers = (await this.getHeaders()) as typeof this.headers;

        const result = await super.execute<T>();

        return result;
    }

    /**
     * Throw an BusinessRuleError if diagnose is filled
     */
    throwIfDiagnose() {
        if (this.diagnoses.length) {
            throw new BusinessRuleError(this.diagnoses.map(diag => `${diag.message}`).join(' \n '));
        }
    }
}
