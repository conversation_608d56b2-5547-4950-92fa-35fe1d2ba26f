import { SystemError } from '@sage/xtrem-core';

export interface FiltersConstructor {
    //  availableProperties:string[];
    filterArray: $filter[];
}
export interface $filter {
    property: string;
    operator: 'eq'; // eq is the only available operator
    value: string;
}
/**
 * Filtering of data - single filter only. Based on Sage REST standards Supported operators: eq
 */
export class Filter {
    availbleOperators: string[] = ['eq'];

    get filterStringified() {
        return (
            this.param.filterArray
                .map(filter => `${filter.property} ${filter.operator} '${filter.value}' `)
                .join(' & ') || ''
        );
    }

    constructor(public param: FiltersConstructor) {
        if (param.filterArray.length > 1) {
            throw new SystemError('Only one filter is allowed ');
        }
    }

    toString() {
        return this.param.filterArray.length ? `?$filter=${this.filterStringified}` : '';
    }
}
