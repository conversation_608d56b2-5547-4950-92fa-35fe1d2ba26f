{"extends": "../../tsconfig-base.json", "compilerOptions": {"target": "es2022", "module": "CommonJS", "moduleResolution": "node", "outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "application.ts", "lib/**/*", "test/**/*.ts", "test/**/*.json", "api/api.d.ts"], "exclude": [".eslintrc*.cjs", "lib/pages/**/*", "lib/page-extensions/**/*", "lib/page-fragments/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png", "lib/client-functions/**/*"], "references": [{"path": "../../../platform/system/xtrem-authorization"}, {"path": "../../../platform/front-end/xtrem-client"}, {"path": "../../../platform/system/xtrem-communication"}, {"path": "../../../platform/back-end/xtrem-config"}, {"path": "../../../platform/back-end/xtrem-core"}, {"path": "../../../platform/shared/xtrem-decimal"}, {"path": "../../shared/xtrem-finance-data"}, {"path": "../../shared/xtrem-master-data"}, {"path": "../../../platform/shared/xtrem-shared"}, {"path": "../../shared/xtrem-structure"}, {"path": "../../../platform/system/xtrem-system"}, {"path": "../../shared/xtrem-tax"}, {"path": "../../../platform/front-end/xtrem-ui"}, {"path": "../../../platform/back-end/eslint-plugin-xtrem"}, {"path": "../../../platform/cli/xtrem-cli"}, {"path": "../../shared/xtrem-finance-data/api"}, {"path": "../../shared/xtrem-master-data/api"}, {"path": "../../../platform/system/xtrem-routing"}, {"path": "../../shared/xtrem-structure/api"}, {"path": "../../../platform/system/xtrem-system/api"}, {"path": "../../shared/xtrem-tax/api"}]}