import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremServiceFabric from '../../../index';

describe('Service Fabric Client', () => {
    before(() => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-service-fabric': {
                    authURL: 'https://qa-api-money.sage.com/auth-v1',
                    // apiURL: 'https://gcc-ror-app.eu.uat.sagecompliance.com/', qa envurls
                    apiURL: 'https://compliance-eu1.qa-fabric.sage.com/api-v1',
                    application: 'sage.intacct.manufacturing',
                    signingKey: 'EC83F151A5F7F2FA9260DCC755E2AC943D87FDAC433C043CE8EBB7D63A81A6F7',
                    lookupURL: '',
                },
            },
        });
    });
    it('no lookupURL defined', () =>
        Test.withContext(
            async context => {
                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);
                // get token
                await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context);
                const serviceFabricConfiguration = await context.read(
                    xtremServiceFabric.nodes.OrganisationServiceFabric,
                    {
                        id: 'DEFAULT',
                    },
                    { forUpdate: true },
                );
                await assert.isRejected(
                    serviceFabricConfiguration.lookupURL,
                    'No URL for the Sage Service Fabric lookup API in the configuration.',
                );
            },
            {
                mocks: ['axios'],
                scenario: 'no-lookup-url-defined',
                directory: __dirname,
            },
        ));
});
