import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremServiceFabric from '../../../index';
import { systemConfig } from '../../fixtures/system';

describe('Service Fabric Client', () => {
    before(() => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-service-fabric': {
                    authURL: 'https://qa-api-money.sage.com/auth-v1',
                    // apiURL: 'https://gcc-ror-app.eu.uat.sagecompliance.com/', qa envurls
                    apiURL: 'https://compliance-eu1.qa-fabric.sage.com/api-v1',
                    application: 'sage.intacct.manufacturing',
                    signingKey: 'EC83F151A5F7F2FA9260DCC755E2AC943D87FDAC433C043CE8EBB7D63A81A6F7',
                    lookupURL: 'https://gcc-ror-app.eu.uat.sagecompliance.com',
                },
            },
        });
        systemConfig.stub();
    });

    after(() => systemConfig.restore());

    it('Reset, Create and Get token from Organisation Service Fabric node', () =>
        Test.withContext(
            async context => {
                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);
                // get token
                await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context);

                const serviceFabricConfiguration = await context.read(
                    xtremServiceFabric.nodes.OrganisationServiceFabric,
                    {
                        id: 'DEFAULT',
                    },
                    { forUpdate: true },
                );
                assert.equal(
                    await serviceFabricConfiguration.lookupURL,
                    'https://gcc-ror-app.eu.uat.sagecompliance.com',
                );
                assert.equal(
                    await serviceFabricConfiguration.apiURL,
                    'https://compliance-eu1.qa-fabric.sage.com/api-v1',
                );
                assert.equal(await serviceFabricConfiguration.connected, Test.defaultTenantName);
                assert.equal(await serviceFabricConfiguration.id, 'DEFAULT');
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'create-organisation',
                directory: __dirname,
            },
        ));
    it('No token', () =>
        Test.withContext(
            async context => {
                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);

                const serviceFabricConfiguration = await context.read(
                    xtremServiceFabric.nodes.OrganisationServiceFabric,
                    {
                        id: 'DEFAULT',
                    },
                    { forUpdate: true },
                );
                assert.equal(await serviceFabricConfiguration.connected, 'No token');
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'no-token',
                directory: __dirname,
            },
        ));
    it('Create company for Service Fabric and getCompanyList/getCompanyLinkedList', () =>
        Test.withContext(
            async context => {
                const company = await context.read(
                    xtremSystem.nodes.Company,
                    {
                        id: 'US001',
                    },
                    { forUpdate: true },
                );

                await company.$.set({
                    serviceFabricExternalId: '*********',
                });
                await company.$.save();

                // console.log(JSON.stringify(company.serviceFabricCompany));
                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);
                // get token
                await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context);

                // Create company for Service Fabric
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createServiceFabricCompany(context, 'US001');

                const getCompanyList = await xtremServiceFabric.nodes.OrganisationServiceFabric.getCompanyList(context);
                assert.equal(getCompanyList[0].name, 'US Process Manufacturing 001');

                const getLinkedCompanyList =
                    await xtremServiceFabric.nodes.OrganisationServiceFabric.getLinkedCompanyList(context);

                const listCompany = getLinkedCompanyList.find(c => c.id === 'US001');
                assert.equal(listCompany?.name, 'US Process Manufacturing 001');
                assert.equal(listCompany?.isCreated, false);
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'create-get-company-list',
                directory: __dirname,
            },
        ));
    it('companyIdValidation test', () =>
        Test.withContext(
            async context => {
                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);
                // get token
                await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context);
                const companyIdValidation =
                    await xtremServiceFabric.nodes.OrganisationServiceFabric.companyIdValidation(
                        context,
                        'FR',
                        'FR98765432101',
                    );
                assert.equal(companyIdValidation.valid, false);
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'company-id-validation',
                directory: __dirname,
            },
        ));
});
