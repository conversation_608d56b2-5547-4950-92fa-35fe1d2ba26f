[{"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/organisations", "method": "POST", "data": {"name": "Tenant for tests (automatic creation)", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "sageCrmId": "123456789900", "primaryCountry": "FRA", "sageCRMId": "123456789900"}}, "response": {"data": {"isMock": true, "organisationId": "ab28f9f5-3224-4d70-b485-aa58fdb2bfca", "name": "Tenant for tests (automatic creation)", "sageCrmId": "123456789900", "primaryCountry": "FRA", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "primarySigningKey": "62A994ADAB1A5B24F862FECF7F0770BD05CD8D9EF85185562C1BA20FED23D3E0"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Organisation-Id": "ab28f9f5-3224-4d70-b485-aa58fdb2bfca"}, "url": "https://qa-api-money.sage.com/auth-v1/accesstoken", "method": "GET"}, "response": {"data": {"isMock": true, "jwt": "xxx"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Api-User-ID": "ff2c0b9a-9b0e-11ec-b909-0242ac120002", "X-Business-ID": "ab28f9f5-3224-4d70-b485-aa58fdb2bfca", "X-Company-ID": "efa51259-254c-49bf-aa03-cecf7d3fc026", "X-Signatory": "sage-intacct", "X-User-IP": "************"}, "url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/countries/FR/companies/***********?company-search-by=VAT&serviceType=VIES", "method": "GET"}, "response": {"data": {"isMock": true, "links": {"$key": "POLLING-URL", "$url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/tasks/c1385b33-f7d7-40b4-9fa6-c878c4fd1ea5"}, "stage": "process_started"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Api-User-ID": "ff2c0b9a-9b0e-11ec-b909-0242ac120002", "X-Business-ID": "ab28f9f5-3224-4d70-b485-aa58fdb2bfca", "X-Company-ID": "efa51259-254c-49bf-aa03-cecf7d3fc026", "X-Signatory": "sage-intacct", "X-User-IP": "************"}, "url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/tasks/c1385b33-f7d7-40b4-9fa6-c878c4fd1ea5", "method": "GET"}, "response": {"data": {"isMock": true, "links": {"$key": "POLLING-URL", "$url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/tasks/c1385b33-f7d7-40b4-9fa6-c878c4fd1ea5"}, "stage": "process_started"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Api-User-ID": "ff2c0b9a-9b0e-11ec-b909-0242ac120002", "X-Business-ID": "ab28f9f5-3224-4d70-b485-aa58fdb2bfca", "X-Company-ID": "efa51259-254c-49bf-aa03-cecf7d3fc026", "X-Signatory": "sage-intacct", "X-User-IP": "************"}, "url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/tasks/c1385b33-f7d7-40b4-9fa6-c878c4fd1ea5", "method": "GET"}, "response": {"data": {"isMock": true, "links": {"$key": "POLLING-URL", "$url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/tasks/c1385b33-f7d7-40b4-9fa6-c878c4fd1ea5"}, "stage": "process_started"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Api-User-ID": "ff2c0b9a-9b0e-11ec-b909-0242ac120002", "X-Business-ID": "ab28f9f5-3224-4d70-b485-aa58fdb2bfca", "X-Company-ID": "efa51259-254c-49bf-aa03-cecf7d3fc026", "X-Signatory": "sage-intacct", "X-User-IP": "************"}, "url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/tasks/c1385b33-f7d7-40b4-9fa6-c878c4fd1ea5", "method": "GET"}, "response": {"data": {"isMock": true, "files": [{"$fileType": "processing results", "$contentType": "application/json", "$url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/files/28481805", "$fileName": "processing results.json", "$stage": "submitted"}], "stage": "submitted", "statusCode": "200", "status": "success"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Api-User-ID": "ff2c0b9a-9b0e-11ec-b909-0242ac120002", "X-Business-ID": "ab28f9f5-3224-4d70-b485-aa58fdb2bfca", "X-Company-ID": "efa51259-254c-49bf-aa03-cecf7d3fc026", "X-Signatory": "sage-intacct", "X-User-IP": "************"}, "url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/files/28481805", "method": "GET"}, "response": {"data": {"isMock": true, "vies_response": {"traderStreetMatch": "", "traderCityMatch": "", "traderPostcodeMatch": "", "$statusCode": "200", "traderStreet": null, "traderNameMatch": "", "traderAddress": "---", "valid": false, "traderCompanyType": "---", "countryCode": "FR", "requestDate": "2022-04-05T02:00:00.000+00:00", "traderCity": null, "$status": "success", "traderPostCode": null, "traderCompanyTypeMatch": "", "vatNumber": "***********", "traderName": "---"}}}}]