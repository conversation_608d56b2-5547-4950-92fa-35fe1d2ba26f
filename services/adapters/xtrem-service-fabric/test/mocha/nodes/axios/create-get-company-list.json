[{"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/organisations", "method": "POST", "data": {"name": "Tenant for tests (automatic creation)", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "sageCrmId": "*********900", "primaryCountry": "FRA", "sageCRMId": "*********900"}}, "response": {"data": {"isMock": true, "organisationId": "b96a41f1-e427-4e67-86c0-ceb42911ea2b", "name": "Tenant for tests (automatic creation)", "sageCrmId": "*********900", "primaryCountry": "FRA", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "primarySigningKey": "9BDA205298633EEC2F012A7D2B88113CADDAABE5E507C6132D5F53A1B3CD0DD9"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Organisation-Id": "b96a41f1-e427-4e67-86c0-ceb42911ea2b"}, "url": "https://qa-api-money.sage.com/auth-v1/accesstoken", "method": "GET"}, "response": {"data": {"isMock": true, "jwt": "xxx"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/companies", "method": "POST", "data": {"name": "US Process Manufacturing 001", "externalId": "*********", "taxNumber": "", "standardIndustrialCode": "", "contactTelNo": "+27821234567", "contactEmail": "<EMAIL>", "address": {"addressLine1": "1 Some street", "addressLine2": "Some building", "addressLine3": "", "addressLine4": "", "countrySubdivision": "Gauteng", "postalCode": "0123", "country": "ZAF"}}}, "response": {"data": {"isMock": true, "companyId": "b2397ba0-56f6-45eb-a50c-64a0af1766dd", "organisationId": "b96a41f1-e427-4e67-86c0-ceb42911ea2b", "name": "US Process Manufacturing 001", "externalId": "*********", "logoUrl": "", "address": {"addressLine1": "1 Some street", "addressLine2": "Some building", "addressLine3": "", "addressLine4": "", "countrySubdivision": "", "postalCode": "0123", "country": "ZAF"}, "taxNumber": "", "standardIndustrialCode": "", "contactTelNo": "+27821234567", "contactEmail": "<EMAIL>"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/companies", "method": "GET"}, "response": {"data": {"0": {"companyId": "b2397ba0-56f6-45eb-a50c-64a0af1766dd", "organisationId": "b96a41f1-e427-4e67-86c0-ceb42911ea2b", "name": "US Process Manufacturing 001", "externalId": "*********", "logoUrl": "", "address": {"addressLine1": "1 Some street", "addressLine2": "Some building", "addressLine3": "", "addressLine4": "", "countrySubdivision": "", "postalCode": "0123", "country": "ZAF"}, "taxNumber": "", "standardIndustrialCode": "", "contactTelNo": "+27821234567", "contactEmail": "<EMAIL>"}, "isMock": true}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/companies", "method": "GET"}, "response": {"data": {"0": {"companyId": "b2397ba0-56f6-45eb-a50c-64a0af1766dd", "organisationId": "b96a41f1-e427-4e67-86c0-ceb42911ea2b", "name": "US Process Manufacturing 001", "externalId": "*********", "logoUrl": "", "address": {"addressLine1": "1 Some street", "addressLine2": "Some building", "addressLine3": "", "addressLine4": "", "countrySubdivision": "", "postalCode": "0123", "country": "ZAF"}, "taxNumber": "", "standardIndustrialCode": "", "contactTelNo": "+27821234567", "contactEmail": "<EMAIL>"}, "isMock": true}}}]