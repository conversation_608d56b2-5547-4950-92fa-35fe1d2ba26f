[{"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/organisations", "method": "POST", "data": {"name": "Tenant for tests (automatic creation)", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "sageCrmId": "123456789900", "primaryCountry": "FRA", "sageCRMId": "123456789900"}}, "response": {"data": {"isMock": true, "organisationId": "e461590a-6e17-4558-a357-52166e7730e1", "name": "Tenant for tests (automatic creation)", "sageCrmId": "123456789900", "primaryCountry": "FRA", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "primarySigningKey": "D1F60CB3622FB34227F28C526CF9B775ADDF0F437B69FF705FA45A50FA196625"}}}]