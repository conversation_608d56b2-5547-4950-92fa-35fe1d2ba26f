import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremServiceFabric from '../../../index';

describe('Filter main class ', () => {
    before(() => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-service-fabric': {
                    authURL: 'https://qa-api-money.sage.com/auth-v1',
                    // apiURL: 'https://gcc-ror-app.eu.uat.sagecompliance.com/', qa envurls
                    apiURL: 'https://compliance-eu1.qa-fabric.sage.com/api-v1',
                    application: 'sage.intacct.manufacturing',
                    signingKey: 'EC83F151A5F7F2FA9260DCC755E2AC943D87FDAC433C043CE8EBB7D63A81A6F7',
                    lookupURL: 'https://gcc-ror-app.eu.uat.sagecompliance.com',
                },
            },
        });
    });
    it(' Filter Class - constructor', () =>
        Test.withContext(
            async context => {
                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);
                // get token
                await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context);

                const filterInstance = new xtremServiceFabric.classes.Filter({
                    filterArray: [
                        {
                            operator: 'eq',
                            property: 'test',
                            value: 'test',
                        },
                    ],
                });
                assert.equal(filterInstance.param.filterArray[0].operator, 'eq');
                assert.equal(filterInstance.availbleOperators.toString(), 'eq');
                assert.isNotEmpty(filterInstance.filterStringified);
                assert.isNotEmpty(filterInstance.toString());
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'filter-constructor',
                directory: __dirname,
            },
        ));
    it(' Filter Class - constructor fail', () =>
        Test.withContext(
            async context => {
                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);
                // get token
                await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context);
                assert.throw(
                    () =>
                        new xtremServiceFabric.classes.Filter({
                            filterArray: [
                                {
                                    operator: 'eq',
                                    property: 'test',
                                    value: 'test',
                                },
                                {
                                    operator: 'eq',
                                    property: 'test 2',
                                    value: 'test 2',
                                },
                            ],
                        }),
                    'Only one filter is allowed',
                );
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'filter-contructor-fail',
                directory: __dirname,
            },
        ));
});
