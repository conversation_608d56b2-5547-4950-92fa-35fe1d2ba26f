[{"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/organisations", "method": "POST", "data": {"name": "Tenant for tests (automatic creation)", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "sageCrmId": "123456789900", "primaryCountry": "FRA", "sageCRMId": "123456789900"}}, "response": {"data": {"isMock": true, "organisationId": "5f022a85-1fea-4c84-b742-75594d5b96b0", "name": "Tenant for tests (automatic creation)", "sageCrmId": "123456789900", "primaryCountry": "FRA", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "primarySigningKey": "16DD0D40E7C6B19CEE597609569CCCC197CD83C798547B49381513B0368CCDEB"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Organisation-Id": "5f022a85-1fea-4c84-b742-75594d5b96b0"}, "url": "https://qa-api-money.sage.com/auth-v1/accesstoken", "method": "GET"}, "response": {"data": {"isMock": true, "jwt": "xxx"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Api-User-ID": "ff2c0b9a-9b0e-11ec-b909-0242ac120002", "X-Business-ID": "5f022a85-1fea-4c84-b742-75594d5b96b0", "X-Company-ID": "efa51259-254c-49bf-aa03-cecf7d3fc026", "X-Signatory": "sage-intacct", "X-User-IP": "************"}, "url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/countries/FR/companies/US001?company-search-by=VAT&serviceType=VIES", "method": "GET"}, "response": {"data": {"isMock": true, "links": {"$key": "POLLING-URL", "$url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/tasks/187bab89-5cdb-496e-a93c-1a92f7a74c87"}, "stage": "process_started"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Api-User-ID": "ff2c0b9a-9b0e-11ec-b909-0242ac120002", "X-Business-ID": "5f022a85-1fea-4c84-b742-75594d5b96b0", "X-Company-ID": "efa51259-254c-49bf-aa03-cecf7d3fc026", "X-Signatory": "sage-intacct", "X-User-IP": "************"}, "url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/tasks/187bab89-5cdb-496e-a93c-1a92f7a74c87", "method": "GET"}, "response": {"data": {"isMock": true, "links": {"$key": "POLLING-URL", "$url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/tasks/187bab89-5cdb-496e-a93c-1a92f7a74c87"}, "stage": "process_started"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Api-User-ID": "ff2c0b9a-9b0e-11ec-b909-0242ac120002", "X-Business-ID": "5f022a85-1fea-4c84-b742-75594d5b96b0", "X-Company-ID": "efa51259-254c-49bf-aa03-cecf7d3fc026", "X-Signatory": "sage-intacct", "X-User-IP": "************"}, "url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/tasks/187bab89-5cdb-496e-a93c-1a92f7a74c87", "method": "GET"}, "response": {"data": {"isMock": true, "links": {"$key": "POLLING-URL", "$url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/tasks/187bab89-5cdb-496e-a93c-1a92f7a74c87"}, "stage": "process_started"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Api-User-ID": "ff2c0b9a-9b0e-11ec-b909-0242ac120002", "X-Business-ID": "5f022a85-1fea-4c84-b742-75594d5b96b0", "X-Company-ID": "efa51259-254c-49bf-aa03-cecf7d3fc026", "X-Signatory": "sage-intacct", "X-User-IP": "************"}, "url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/tasks/187bab89-5cdb-496e-a93c-1a92f7a74c87", "method": "GET"}, "response": {"data": {"isMock": true, "files": [], "stage": "submitted", "statusCode": "200", "status": "success"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Api-User-ID": "ff2c0b9a-9b0e-11ec-b909-0242ac120002", "X-Business-ID": "5f022a85-1fea-4c84-b742-75594d5b96b0", "X-Company-ID": "efa51259-254c-49bf-aa03-cecf7d3fc026", "X-Signatory": "sage-intacct", "X-User-IP": "************"}, "url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/tasks/187bab89-5cdb-496e-a93c-1a92f7a74c87", "method": "GET"}, "response": {"data": {"isMock": true, "files": [{"$fileType": "processing results", "$contentType": "application/json", "$url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/files/28508429", "$fileName": "processing results.json", "$stage": "submitted"}], "stage": "submitted", "statusCode": "200", "status": "success"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Api-User-ID": "ff2c0b9a-9b0e-11ec-b909-0242ac120002", "X-Business-ID": "5f022a85-1fea-4c84-b742-75594d5b96b0", "X-Company-ID": "efa51259-254c-49bf-aa03-cecf7d3fc026", "X-Signatory": "sage-intacct", "X-User-IP": "************"}, "url": "https://gcc-ror-app.eu.uat.sagecompliance.com/api-v5/files/28508429", "method": "GET"}, "response": {"data": {"isMock": true, "vies_response": {"traderStreetMatch": "", "traderCityMatch": "", "traderPostcodeMatch": "", "$statusCode": "200", "traderStreet": null, "traderNameMatch": "", "traderAddress": "---", "valid": false, "traderCompanyType": "---", "countryCode": "FR", "requestDate": "2022-04-05T02:00:00.000+00:00", "traderCity": null, "$status": "success", "traderPostCode": null, "traderCompanyTypeMatch": "", "vatNumber": "US001", "traderName": "---"}}}}]