[{"request": {"method": "GET", "url": "https://qa-api-money.sage.com/auth-v1/organisations", "headers": {"Content-Type": "application/json", "X-Application": "sage.online.product"}}, "response": {"data": {"isMock": true, "organisationId": "2b817f1b-2c25-4c1a-9f03-3490c361af87", "name": "Tenant for tests (automatic creation)", "sageCrmId": "123456789900", "primaryCountry": "FRA", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "sageCRMId": "123456789900"}}}]