[{"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/organisations", "method": "POST", "data": {"name": "Tenant for tests (automatic creation)", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "sageCrmId": "*********900", "primaryCountry": "FRA", "sageCRMId": "*********900"}}, "response": {"data": {"isMock": true, "organisationId": "f01b2cf1-4574-46fd-8f50-37f2c921eb0b", "name": "Tenant for tests (automatic creation)", "sageCrmId": "*********900", "primaryCountry": "FRA", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "primarySigningKey": "ADAED6EF83C07B65FB8F4DABB99C8A0EB0FFE54C20AFF7E26780A75262B6257F"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Organisation-Id": "f01b2cf1-4574-46fd-8f50-37f2c921eb0b"}, "url": "https://qa-api-money.sage.com/auth-v1/accesstoken", "method": "GET"}, "response": {"data": {"isMock": true, "jwt": "xxx"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/companies", "method": "POST", "data": {"name": "US Process Manufacturing 001", "externalId": "*********", "taxNumber": "", "standardIndustrialCode": "", "contactTelNo": "+27821234567", "contactEmail": "", "address": {"addressLine1": "1 Some street", "addressLine2": "Some building", "addressLine3": "", "addressLine4": "", "countrySubdivision": "", "postalCode": "0123", "country": "ZAF"}}}, "response": {"data": {"isMock": true, "errors": [{"code": "FIELD_ERROR", "property": "contactEmail", "description": "Please provide a valid email address"}]}}}]