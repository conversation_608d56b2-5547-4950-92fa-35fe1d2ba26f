[{"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/organisations", "method": "POST", "data": {"name": "Tenant for tests (automatic creation)", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "sageCrmId": "123456789900", "primaryCountry": "FRA", "sageCRMId": "123456789900"}}, "response": {"data": {"isMock": true, "organisationId": "6a3a7a1c-ca39-4bcb-a0f7-82cff8ef8d3a", "name": "Tenant for tests (automatic creation)", "sageCrmId": "123456789900", "primaryCountry": "FRA", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "primarySigningKey": "BAD4CED7A90898B2943C39CBAC7EC45A70FC23B7662298455B22A62BCC2363A4"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Organisation-Id": "6a3a7a1c-ca39-4bcb-a0f7-82cff8ef8d3a"}, "url": "https://qa-api-money.sage.com/auth-v1/accesstoken", "method": "GET"}, "response": {"data": {"isMock": true, "jwt": "xxx"}}}]