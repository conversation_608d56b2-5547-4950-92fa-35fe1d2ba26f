[{"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/organisations", "method": "POST", "data": {"name": "Tenant for tests (automatic creation)", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "sageCrmId": "123456789900", "primaryCountry": "FRA", "sageCRMId": "123456789900"}}, "response": {"data": {"isMock": true, "organisationId": "b1b98038-27d8-47d3-b256-f2914245e2e9", "name": "Tenant for tests (automatic creation)", "sageCrmId": "123456789900", "primaryCountry": "FRA", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "primarySigningKey": "2F111A1BE2EF56ED981397E111635D9517B7E2241F6BD688EF7CE37BB333C4A5"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Organisation-Id": "b1b98038-27d8-47d3-b256-f2914245e2e9"}, "url": "https://qa-api-money.sage.com/auth-v1/accesstoken", "method": "GET"}, "response": {"data": {"isMock": true, "jwt": "xxx"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/organisations", "method": "POST", "data": {"name": "Tenant for tests (automatic creation)", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "sageCrmId": "123456789900", "primaryCountry": "FRA", "sageCRMId": "123456789900"}}, "response": {"data": {"isMock": true, "organisationId": "9f755ed3-9615-45b7-8f87-4445c4b6b9e9", "name": "Tenant for tests (automatic creation)", "sageCrmId": "123456789900", "primaryCountry": "FRA", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "primarySigningKey": "5149BCEF017AB6994961FF42877B6B7AE9880884FDB1434C9AF9A69403910C74"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Organisation-Id": "9f755ed3-9615-45b7-8f87-4445c4b6b9e9"}, "url": "https://qa-api-money.sage.com/auth-v1/accesstoken", "method": "GET"}, "response": {"data": {"isMock": true, "jwt": "xxx"}}}]