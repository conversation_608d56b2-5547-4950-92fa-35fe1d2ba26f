[{"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/organisations", "method": "POST", "data": {"name": "Tenant for tests (automatic creation)", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "sageCrmId": "*********900", "primaryCountry": "FRA", "sageCRMId": "*********900"}}, "response": {"data": {"isMock": true, "organisationId": "ea390d74-923c-4639-b4ff-7b37b3f68b96", "name": "Tenant for tests (automatic creation)", "sageCrmId": "*********900", "primaryCountry": "FRA", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "primarySigningKey": "9405470193BD4EFB4AED42B2D846969ECB3B1379955D63B522F498AA1DE53C3A"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Organisation-Id": "ea390d74-923c-4639-b4ff-7b37b3f68b96"}, "url": "https://qa-api-money.sage.com/auth-v1/accesstoken", "method": "GET"}, "response": {"data": {"isMock": true, "jwt": "xxx"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/companies", "method": "POST", "data": {"name": "US Process Manufacturing 001", "externalId": "*********", "taxNumber": "", "standardIndustrialCode": "", "contactTelNo": "+27821234567", "contactEmail": "<EMAIL>", "address": {"addressLine1": "1 Some street", "addressLine2": "Some building", "addressLine3": "", "addressLine4": "", "countrySubdivision": "", "postalCode": "0123", "country": "ZAF"}}}, "response": {"data": {"isMock": true, "companyId": "59ed293c-8100-4a2b-89ee-dda09fde73df", "organisationId": "ea390d74-923c-4639-b4ff-7b37b3f68b96", "name": "US Process Manufacturing 001", "externalId": "*********", "logoUrl": "", "address": {"addressLine1": "1 Some street", "addressLine2": "Some building", "addressLine3": "", "addressLine4": "", "countrySubdivision": "", "postalCode": "0123", "country": "ZAF"}, "taxNumber": "", "standardIndustrialCode": "", "contactTelNo": "+27821234567", "contactEmail": "<EMAIL>"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/companies", "method": "GET"}, "response": {"data": {"0": {"companyId": "59ed293c-8100-4a2b-89ee-dda09fde73df", "organisationId": "ea390d74-923c-4639-b4ff-7b37b3f68b96", "name": "US Process Manufacturing 001", "externalId": "*********", "logoUrl": "", "address": {"addressLine1": "1 Some street", "addressLine2": "Some building", "addressLine3": "", "addressLine4": "", "countrySubdivision": "", "postalCode": "0123", "country": "ZAF"}, "taxNumber": "", "standardIndustrialCode": "", "contactTelNo": "+27821234567", "contactEmail": "<EMAIL>"}, "isMock": true}}}]