[{"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/organisations", "method": "POST", "data": {"name": "Tenant for tests (automatic creation)", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "sageCrmId": "123456789900", "primaryCountry": "FRA", "sageCRMId": "123456789900"}}, "response": {"data": {"isMock": true, "organisationId": "2cb6b134-d095-4693-b4ca-2a7dcbf9197f", "name": "Tenant for tests (automatic creation)", "sageCrmId": "123456789900", "primaryCountry": "FRA", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "primarySigningKey": "F0C807E933E5D0D47EDAE31C24C980C6131E739B1826B3DF76D36D5B2DF51C0D"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Organisation-Id": "2cb6b134-d095-4693-b4ca-2a7dcbf9197f"}, "url": "https://qa-api-money.sage.com/auth-v1/accesstoken", "method": "GET"}, "response": {"data": {"isMock": true, "jwt": "xxx"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing"}, "url": "https://qa-api-money.sage.com/auth-v1/organisations", "method": "POST", "data": {"name": "Tenant for tests (automatic creation)", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "sageCrmId": "123456789900", "primaryCountry": "FRA"}}, "response": {"data": {"isMock": true, "organisationId": "4e154855-8d38-42ba-8be7-87bd3c123178", "name": "Tenant for tests (automatic creation)", "sageCrmId": "123456789900", "primaryCountry": "FRA", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "primarySigningKey": "293C6405463C9D7169E673753D79140BDB1F01A9E880DBF1BDF0A77D9E292C1B"}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.intacct.manufacturing", "X-Organisation-Id": "4e154855-8d38-42ba-8be7-87bd3c123178"}, "url": "https://qa-api-money.sage.com/auth-v1/accesstoken", "method": "GET"}, "response": {"data": {"isMock": true, "jwt": "xxx"}}}]