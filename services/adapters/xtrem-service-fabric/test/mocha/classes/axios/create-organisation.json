[{"request": {"method": "POST", "url": "https://qa-api-money.sage.com/auth-v1/organisations", "headers": {"Content-Type": "application/json", "X-Application": "sage.online.product"}, "data": {"name": "Tenant for tests (automatic creation)", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "sageCrmId": "123456789900", "primaryCountry": "FRA", "sageCRMId": "123456789900"}}, "response": {"data": {"isMock": true, "organisationId": "2b817f1b-2c25-4c1a-9f03-3490c361af87", "name": "Tenant for tests (automatic creation)", "sageCrmId": "123456789900", "primaryCountry": "FRA", "adminEmail": "<EMAIL>", "defaultLanguage": "EN", "externalId": "8d74d5af-065a-4833-abd3-dddefa317ccb", "primarySigningKey": "ED67EFCED22629CF52E739F6E7FEDEA1BE5F339CDA26CC96A0A6F40BEE2B6C06"}}}]