import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremServiceFabric from '../../../index';

describe('Authentication class ', () => {
    before(() => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-service-fabric': {
                    authURL: 'https://qa-api-money.sage.com/auth-v1',
                    // apiURL: 'https://gcc-ror-app.eu.uat.sagecompliance.com/', qa envurls
                    apiURL: 'https://compliance-eu1.qa-fabric.sage.com/api-v1',
                    application: 'sage.intacct.manufacturing',
                    signingKey: 'EC83F151A5F7F2FA9260DCC755E2AC943D87FDAC433C043CE8EBB7D63A81A6F7',
                    lookupURL: 'https://gcc-ror-app.eu.uat.sagecompliance.com',
                },
            },
        });
    });
    it(' Authentication Class - createCompany fail', () =>
        Test.withContext(
            async context => {
                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);
                // get token
                await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context);

                await assert.isRejected(
                    xtremServiceFabric.classes.Authentication.createCompany(context, {
                        name: 'US Process Manufacturing 001',
                        externalId: '*********',
                        taxNumber: '',
                        standardIndustrialCode: '',
                        contactTelNo: '+27821234567',
                        contactEmail: '',
                        address: {
                            addressLine1: '1 Some street',
                            addressLine2: 'Some building',
                            addressLine3: '',
                            addressLine4: '',
                            countrySubdivision: '',
                            postalCode: '0123',
                            country: 'ZAF',
                        },
                    }),
                    'FIELD_ERROR : Please provide a valid email address',
                );
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'authentication-create-company-fail',
                directory: __dirname,
            },
        ));
    it(' Authentication Class - getCompany', () =>
        Test.withContext(
            async context => {
                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);
                // get token
                await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context);

                await xtremServiceFabric.classes.Authentication.createCompany(context, {
                    name: 'US Process Manufacturing 001',
                    externalId: '*********',
                    taxNumber: '',
                    standardIndustrialCode: '',
                    contactTelNo: '+27821234567',
                    contactEmail: '<EMAIL>',
                    address: {
                        addressLine1: '1 Some street',
                        addressLine2: 'Some building',
                        addressLine3: '',
                        addressLine4: '',
                        countrySubdivision: '',
                        postalCode: '0123',
                        country: 'ZAF',
                    },
                });

                const getCompany = await xtremServiceFabric.classes.Authentication.getCompany(context, 'US001');
                assert.equal(getCompany[0].name, 'US Process Manufacturing 001');
                assert.equal(getCompany[0].externalId, '*********');
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'authentication-create-get-company',
                directory: __dirname,
            },
        ));
    // TODO: return Axios error status : 403 Forbidde can compare the values
    it.skip(' Authentication Class - updateCompany', () =>
        Test.withContext(
            async context => {
                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);
                // get token
                await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context);

                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);
                // get token
                await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context);

                await xtremServiceFabric.classes.Authentication.createCompany(context, {
                    name: 'US Process Manufacturing 001',
                    externalId: '*********',
                    taxNumber: '',
                    standardIndustrialCode: '',
                    contactTelNo: '+27821234567',
                    contactEmail: '<EMAIL>',
                    address: {
                        addressLine1: '1 Some street',
                        addressLine2: 'Some building',
                        addressLine3: '',
                        addressLine4: '',
                        countrySubdivision: '',
                        postalCode: '0123',
                        country: 'ZAF',
                    },
                });
                const updatedCompany = await xtremServiceFabric.classes.Authentication.updateCompany(context, {
                    companyId: 'dc4e28ca-97eb-4bf6-a62f-d13cdc2c0787',
                    name: 'US Process Manufacturing 002',
                    externalId: '*********',
                    taxNumber: '',
                    standardIndustrialCode: '',
                    contactTelNo: '+27821234567',
                    contactEmail: '<EMAIL>',
                    address: {
                        addressLine1: '1 Some street',
                        addressLine2: 'Some building',
                        addressLine3: '',
                        addressLine4: '',
                        countrySubdivision: '',
                        postalCode: '0123',
                        country: 'ZAF',
                    },
                });
                assert.equal(updatedCompany[0].name, 'US Process Manufacturing 002');
            },
            // {
            //     testAttributes: { createMockData: true },
            //     // mocks: ['axios'],
            //     scenario: 'authentication-create-get-organisation',
            //     directory: __dirname,
            // },
        ));
});
