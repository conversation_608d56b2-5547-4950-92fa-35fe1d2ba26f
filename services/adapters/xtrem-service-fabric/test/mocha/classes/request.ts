import { datetime, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremServiceFabric from '../../../index';
import type { OrganisationServiceFabric } from '../../../lib/nodes/organisation-service-fabric';

describe('Request class ', () => {
    before(() => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-service-fabric': {
                    authURL: 'https://qa-api-money.sage.com/auth-v1',
                    apiURL: 'https://compliance-eu1.qa-fabric.sage.com/api-v1',
                    application: 'sage.online.product',
                    signingKey: '42',
                },
            },
        });
    });

    it(' Request Class - constructor  ', () =>
        Test.withContext(async context => {
            const request = new xtremServiceFabric.classes.Request(context);
            assert.equal(await (await request.activeInstance).id, 'DEFAULT');
            assert.isTrue(await (await request.activeInstance).isActive);

            assert.isArray(request.diagnoses);

            request.parameters = {
                method: 'POST',
                additionnalHeaders: { a: 'additionnal header 1', b: 'additionnal header 2' },
                data: { data: 'data to create  ' },
                signingKey: 'SIGNKEY',
                url: 'https://google.fr',
            };
        }));
    it(' Request Class - createOrganisation  ', () =>
        Test.withContext(
            async context => {
                const organisation = await xtremServiceFabric.classes.Authentication.createOrganisation(context);
                assert.deepEqual(organisation.name, Test.defaultTenantName);
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'create-organisation',
                directory: __dirname,
            },
        ));
    it(' Request Class - getToken  ', () =>
        Test.withContext(
            async context => {
                const token = await xtremServiceFabric.classes.Authentication.getOrganisationToken(context);
                assert.isString(token);
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'get-token',
                directory: __dirname,
            },
        ));
    it(' Request Class - getOrganisation  ', () =>
        Test.withContext(
            async context => {
                const orgToken = await context.read(
                    xtremServiceFabric.nodes.OrganisationServiceFabric,
                    { id: 'DEFAULT' },
                    { forUpdate: true },
                );

                await orgToken.$.set({ token: 'toto', tokenCreation: datetime.now() });

                await orgToken.$.save();

                const organisation = await xtremServiceFabric.classes.Authentication.getOrganisation(context);
                assert.isTrue(xtremServiceFabric.functions.instanceOfOrganisation(organisation));
                if (xtremServiceFabric.functions.instanceOfOrganisation(organisation)) {
                    assert.deepEqual(organisation.name, Test.defaultTenantName);
                }
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'get-organisation',
                directory: __dirname,
            },
        ));
    it(' Request Class - No active configuration  ', () =>
        Test.withContext(
            async context => {
                const orgToken = await context.read(
                    xtremServiceFabric.nodes.OrganisationServiceFabric,
                    { id: 'DEFAULT' },
                    { forUpdate: true },
                );

                await orgToken.$.set({ isActive: false });

                await orgToken.$.save();
                const request = new xtremServiceFabric.classes.Request(context);
                await assert.isRejected(
                    request.activeInstance as Promise<OrganisationServiceFabric>,
                    'No active configuration',
                );
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'no-active-configuration',
                directory: __dirname,
            },
        ));
});
describe('Request class ', () => {
    before(() => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-service-fabric': {
                    authURL: 'https://qa-api-money.sage.com/auth-v1',
                    apiURL: 'https://compliance-eu1.qa-fabric.sage.com/api-v1',
                    application: 'sage.online.product',
                    signingKey: '42',
                },
            },
        });
    });
});
