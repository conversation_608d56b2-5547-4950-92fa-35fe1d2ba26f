import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremServiceFabric from '../../../index';
import { systemConfig } from '../../fixtures/system';

describe(' Company Lookups ', () => {
    before(() => {});
    /** Skipped gbecause no moked for now & still not working  */
    it.skip(' Request Class - getOrganisation  ', () =>
        Test.withContext(context => xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context)));
    it(' Throw on wrong contry  ', () =>
        Test.withContext(async context => {
            const node = await xtremServiceFabric.classes.CompanyLookup.create(context);
            await assert.isRejected(node.validation('AU', '1234566'), 'AU not available for ID Validation & Look-up');
        }));
    it('Static FR id usage', () => {
        assert.deepEqual(xtremServiceFabric.classes.CompanyLookup.countryIdUsage('FR', ' FR*********  '), '*********');
    });
});

describe(' Company Lookups without LookupAPI ', () => {
    before(() => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-service-fabric': {
                    authURL: 'https://qa-api-money.sage.com/auth-v1',
                    // apiURL: 'https://gcc-ror-app.eu.uat.sagecompliance.com/', qa envurls
                    apiURL: 'https://compliance-eu1.qa-fabric.sage.com/api-v1',
                    application: 'sage.intacct.manufacturing',
                    signingKey: 'EC83F151A5F7F2FA9260DCC755E2AC943D87FDAC433C043CE8EBB7D63A81A6F7',
                    lookupURL: '',
                },
            },
        });
    });
    /** Constructor doesn't return the error message correctly
     * it returns the error on the line if (!this.activeInstance.lookupURL) instead of

    */
    it(' Company Lookups Class - constructor  ', () =>
        Test.withContext(async context => {
            await assert.isRejected(
                xtremServiceFabric.classes.CompanyLookup.create(context),
                'No URL for the Sage Service Fabric lookup API in the configuration.',
            );
        }));
});

describe(' Company Lookups with LookupAPI ', () => {
    before(() => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-service-fabric': {
                    authURL: 'https://qa-api-money.sage.com/auth-v1',
                    // apiURL: 'https://gcc-ror-app.eu.uat.sagecompliance.com/', qa envurls
                    apiURL: 'https://compliance-eu1.qa-fabric.sage.com/api-v1',
                    application: 'sage.intacct.manufacturing',
                    signingKey: 'EC83F151A5F7F2FA9260DCC755E2AC943D87FDAC433C043CE8EBB7D63A81A6F7',
                    lookupURL: 'https://gcc-ror-app.eu.uat.sagecompliance.com',
                },
            },
        });
        systemConfig.stub();
    });

    after(() => {
        systemConfig.restore();
    });

    /** Constructor doesn't return the error message correctly
     * it returns the error on the line if (!this.activeInstance.lookupURL) instead of

    */
    it(' Company Lookups Class - validation  ', () =>
        Test.withContext(
            async context => {
                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);
                // get token
                await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context);
                const companyLookupInstance = await xtremServiceFabric.classes.CompanyLookup.create(context);

                const companyValidation = await companyLookupInstance.validation('FR', 'US001');

                assert.equal(companyValidation.vies_response.$status, 'success');
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'company-lookups-validation',
                directory: __dirname,
            },
        ));
    it(' Company Lookups Class - validationPooling fail ', () =>
        Test.withContext(
            async context => {
                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);
                // get token
                await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context);
                const companyLookupInstance = await xtremServiceFabric.classes.CompanyLookup.create(context);
                await assert.isRejected(
                    companyLookupInstance.validationPolling({
                        links: {
                            $key: '',
                            $url: '',
                        },
                        stage: '',
                        $diagnoses: '',
                    }),
                    'Error on validationPolling',
                );
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'company-lookups-validation-pooling-fail',
                directory: __dirname,
            },
        ));
    it(' Company Lookups Class - getFilesFromUrl fail ', () =>
        Test.withContext(
            async context => {
                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);
                // get token
                await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context);
                const companyLookupInstance = await xtremServiceFabric.classes.CompanyLookup.create(context);
                await assert.isRejected(
                    companyLookupInstance.getFilesFromUrl({
                        files: [],
                        stage: '',
                        status: '',
                        statusCode: '',
                    }),

                    'Error on requesting files from poll ',
                );
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'get-files-from-url-fail',
                directory: __dirname,
            },
        ));
});
describe(' Company Lookups with QA api url ', () => {
    before(() => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-service-fabric': {
                    authURL: 'https://qa-api-money.sage.com/auth-v1',
                    apiURL: 'https://gcc-ror-app.eu.uat.sagecompliance.com/',
                    // apiURL: 'https://compliance-eu1.qa-fabric.sage.com/api-v1',
                    application: 'sage.intacct.manufacturing',
                    signingKey: 'EC83F151A5F7F2FA9260DCC755E2AC943D87FDAC433C043CE8EBB7D63A81A6F7',
                    lookupURL: 'https://gcc-ror-app.eu.uat.sagecompliance.com',
                },
            },
        });
        systemConfig.stub();
    });

    after(() => {
        systemConfig.restore();
    });

    it.skip(' Company Lookups Class - validationPooling fail ', () =>
        Test.withContext(
            async context => {
                // reset
                await xtremServiceFabric.nodes.OrganisationServiceFabric.resetOrganisation(context);
                // create
                await xtremServiceFabric.nodes.OrganisationServiceFabric.createOrganisation(context);
                // get token
                await xtremServiceFabric.nodes.OrganisationServiceFabric.saveToken(context);
                const companyLookupInstance = await xtremServiceFabric.classes.CompanyLookup.create(context);
                await assert.isRejected(
                    companyLookupInstance.validationPolling({
                        links: {
                            $key: '',
                            $url: '',
                        },
                        stage: '',
                        $diagnoses: '',
                    }),
                    'Error on validationPolling',
                );
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'company-lookups-validation',
                directory: __dirname,
            },
        ));
});
