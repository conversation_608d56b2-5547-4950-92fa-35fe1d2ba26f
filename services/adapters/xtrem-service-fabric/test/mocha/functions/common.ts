import { datetime } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremServiceFabric from '../../../index';

describe('Test common functions  ', () => {
    before(() => {});

    it(' bodyAndParametersEncode  ', () => {
        const param: { [key: string]: string } = {
            firstKey: 'firstValue',
            a: 'a',
            d: 'dValue',
            c: 'cValue',
        };
        const body = {
            object: 'blablablablabla',
        };

        const encodedeString = xtremServiceFabric.functions.bodyAndParametersEncode(param, body);

        assert.deepEqual(
            encodedeString,
            'a=a&body=eyJvYmplY3QiOiJibGFibGFibGFibGFibGEifQ%3D%3D&c=cValue&d=dValue&firstKey=firstValue',
        );
    });
    it(' dateTime Compare the token is only 20min live  ', () => {
        assert.equal(datetime.now().compare(datetime.now().addMinutes(-20)), 1200000);
    });
});
