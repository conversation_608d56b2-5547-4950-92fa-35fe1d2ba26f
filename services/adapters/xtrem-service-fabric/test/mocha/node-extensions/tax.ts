import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremServiceFabric from '../../../index';
import { setDummyToken } from '../../fixtures/organisation';

describe('Tax test ', () => {
    before(() => {});
    it(' Tax getServiceFabricLink  ', () =>
        Test.withContext(
            async context => {
                await setDummyToken(context);
                const taxLinkArray = await xtremServiceFabric.nodeExtensions.TaxExtension.getServiceFabricLink(context);
                assert.isArray(taxLinkArray);
            },
            {
                // testAttributes: { createMockData: true },
                mocks: ['axios'],
                scenario: 'getTax',
                directory: __dirname,
            },
        ));
});
