import type { Context } from '@sage/xtrem-core';
import { datetime } from '@sage/xtrem-core';
import * as xtremServiceFabric from '../../index';

/**
 * Set the token & dateCreation
 * @param context
 */
export async function setDummyToken(context: Context): Promise<void> {
    const organisationServiceFabric = await context.read(
        xtremServiceFabric.nodes.OrganisationServiceFabric,
        { id: 'DEFAULT' },
        { forUpdate: true },
    );
    await organisationServiceFabric.$.set({
        token: 'dummyToken',
        tokenCreation: datetime.now(),
    });

    await organisationServiceFabric.$.save();
}
