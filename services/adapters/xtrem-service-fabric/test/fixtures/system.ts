import { ConfigManager } from '@sage/xtrem-core';
import type { SystemConfig } from '@sage/xtrem-shared';

const serviceFabricUserIp = '************';
let old: SystemConfig | undefined;

export const systemConfig = {
    stub() {
        old = ConfigManager.current.system;
        ConfigManager.current.system = {
            natIpAdresses: serviceFabricUserIp,
        };
    },
    restore() {
        ConfigManager.current.system = old;
    },
};
