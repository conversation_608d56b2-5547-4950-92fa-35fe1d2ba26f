[{"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.online.product", "X-Organisation-Id": "7c964f54-14ba-4c1e-822a-314f322b0b7c"}, "url": "https://sandbox-api-money.sage.com/auth-v1/accesstoken", "method": "GET"}, "response": {"data": {"isMock": true, "errors": [{"code": "UNAUTHORIZED", "description": "Invalid signature"}]}}}, {"request": {"headers": {"Content-Type": "application/json", "X-Application": "sage.online.product"}, "url": "https://sandbox-api-money.sage.com/auth-v1/organisations", "method": "GET"}, "response": {"data": {"isMock": true, "message": "Unauthorized"}}}]