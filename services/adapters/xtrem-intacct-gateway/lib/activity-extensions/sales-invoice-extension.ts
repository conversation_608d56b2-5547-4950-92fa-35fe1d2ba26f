import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremSales from '@sage/xtrem-sales';

export const salesInvoiceExtension = new ActivityExtension({
    extends: xtremSales.activities.salesInvoice,
    __filename,
    permissions: [],
    operationGrants: {
        printSalesInvoice: [
            {
                operations: ['updateOpenItemFromIntacct'],
                on: [() => xtremFinance.nodes.AccountsReceivableInvoice],
            },
        ],
        printSalesInvoiceAndEmail: [
            {
                operations: ['updateOpenItemFromIntacct'],
                on: [() => xtremFinance.nodes.AccountsReceivableInvoice],
            },
        ],
    },
});
