import type { IntegrationState } from '@sage/xtrem-communication-api';
import type { IntacctMatchingStatus } from '@sage/xtrem-intacct-finance-api';

import type { ColoredElement } from '@sage/xtrem-system-api';
import {
    colorfulPillPattern,
    colorfulPillPatternDefaulted,
} from '@sage/xtrem-system/build/lib/client-functions/color-pattern';

function integrationStateColor(status: IntegrationState, coloredElement: ColoredElement) {
    switch (status) {
        case 'not':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'pending':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'success':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        case 'desynchronized':
            return colorfulPillPattern.filledCaution[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function intacctMatchingStatusColor(status: IntacctMatchingStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'cleared':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'draftMatched':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'ignored':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'matched':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'partiallyMatched':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'unmatched':
            return colorfulPillPattern.filledCaution[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

export function getLabelColorByStatus(
    enumEntry: string,
    status?: IntegrationState | IntacctMatchingStatus | null,
): {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
} {
    const getColor = (coloredElement: ColoredElement) => {
        switch (enumEntry) {
            case 'IntegrationState':
                return integrationStateColor(status as IntegrationState, coloredElement);
            case 'IntacctMatchingStatus':
                return intacctMatchingStatusColor(status as IntacctMatchingStatus, coloredElement);
            default:
                return colorfulPillPatternDefaulted(coloredElement);
        }
    };
    return {
        backgroundColor: getColor('backgroundColor'),
        borderColor: getColor('borderColor'),
        textColor: getColor('textColor'),
    };
}

export function setBooleanStatusColors(booleanEntry: string, status: boolean, coloredElement: ColoredElement): string {
    switch (booleanEntry) {
        default:
            return colorfulPillPatternDefaulted(coloredElement);
    }
}
