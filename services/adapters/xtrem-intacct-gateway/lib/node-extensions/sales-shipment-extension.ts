import { decorators, SubNodeExtension1 } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremSales from '@sage/xtrem-sales';

@decorators.subNodeExtension1<SalesShipmentExtension>({
    extends: () => xtremSales.nodes.SalesShipment,
})
export class SalesShipmentExtension extends SubNodeExtension1<xtremSales.nodes.SalesShipment> {
    @decorators.stringProperty<SalesShipmentExtension, 'intacctId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly intacctId: Promise<string>;
}
declare module '@sage/xtrem-sales/lib/nodes/sales-shipment' {
    interface SalesShipment extends SalesShipmentExtension {}
}
