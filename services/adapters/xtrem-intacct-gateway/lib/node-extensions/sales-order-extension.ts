import { decorators, SubNodeExtension1 } from '@sage/xtrem-core';
import * as xtremIntacctFinance from '@sage/xtrem-intacct-finance';
import * as xtremSales from '@sage/xtrem-sales';

@decorators.subNodeExtension1<SalesOrderExtension>({
    extends: () => xtremSales.nodes.SalesOrder,
})
export class SalesOrderExtension extends SubNodeExtension1<xtremSales.nodes.SalesOrder> {
    @decorators.stringProperty<SalesOrderExtension, 'intacctId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacctFinance.dataTypes.financeIntacctPropertyDataType,
    })
    readonly intacctId: Promise<string>;
}
declare module '@sage/xtrem-sales/lib/nodes/sales-order' {
    interface SalesOrder extends SalesOrderExtension {}
}
