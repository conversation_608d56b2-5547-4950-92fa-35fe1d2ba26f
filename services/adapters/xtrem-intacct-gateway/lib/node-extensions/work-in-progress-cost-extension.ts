import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremManufacturing from '@sage/xtrem-manufacturing';

@decorators.nodeExtension<WorkInProgressCostExtension>({
    extends: () => xtremManufacturing.nodes.WorkInProgressCost,
})
export class WorkInProgressCostExtension extends NodeExtension<xtremManufacturing.nodes.WorkInProgressCost> {
    @decorators.stringProperty<WorkInProgressCostExtension, 'journalNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly journalNumber: Promise<string>;
}
declare module '@sage/xtrem-manufacturing/lib/nodes/work-in-progress-cost' {
    export interface WorkInProgressCost extends WorkInProgressCostExtension {}
}
