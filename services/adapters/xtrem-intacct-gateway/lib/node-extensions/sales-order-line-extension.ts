import { decorators, SubNodeExtension2 } from '@sage/xtrem-core';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremSales from '@sage/xtrem-sales';

@decorators.subNodeExtension2<SalesOrderLineExtension>({
    extends: () => xtremSales.nodes.SalesOrderLine,
})
export class SalesOrderLineExtension extends SubNodeExtension2<xtremSales.nodes.SalesOrderLine> {
    @decorators.stringProperty<SalesOrderLineExtension, 'intacctId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacct.dataTypes.intacctPropertyDataType,
    })
    readonly intacctId: Promise<string>;
}
declare module '@sage/xtrem-sales/lib/nodes/sales-order-line' {
    interface SalesOrderLine extends SalesOrderLineExtension {}
}
