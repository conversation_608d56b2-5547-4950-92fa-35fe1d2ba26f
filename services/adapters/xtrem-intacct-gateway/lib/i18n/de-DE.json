{"@sage/xtrem-intacct-gateway/enums__field_type__average": "Durchschnitt", "@sage/xtrem-intacct-gateway/enums__field_type__count": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/enums__field_type__max": "<PERSON>.", "@sage/xtrem-intacct-gateway/enums__field_type__min": "<PERSON>.", "@sage/xtrem-intacct-gateway/enums__field_type__select": "Auswählen", "@sage/xtrem-intacct-gateway/enums__field_type__sum": "Gesamt", "@sage/xtrem-intacct-gateway/enums__intacct_matching_type__contains": "", "@sage/xtrem-intacct-gateway/enums__intacct_matching_type__regularExpression": "", "@sage/xtrem-intacct-gateway/enums__intacct_matching_type__strict": "", "@sage/xtrem-intacct-gateway/enums__property_type__description": "Bezeichnung", "@sage/xtrem-intacct-gateway/enums__property_type__intacctId": "ID Intacct", "@sage/xtrem-intacct-gateway/enums__property_type__name": "Name", "@sage/xtrem-intacct-gateway/enums__property_type__none": "<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/enums__property_type__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/enums__property_type__text": "Text", "@sage/xtrem-intacct-gateway/enums__request_type__create": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/enums__request_type__createUpdate": "Erstellen/Aktualisieren", "@sage/xtrem-intacct-gateway/enums__request_type__delete": "Löschen", "@sage/xtrem-intacct-gateway/enums__request_type__read": "<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/enums__request_type__update": "Aktualisieren", "@sage/xtrem-intacct-gateway/map/no-or-to-many-intacct-data": "Intacct-Daten überschritten oder fehlen: {{length}}  ", "@sage/xtrem-intacct-gateway/map/no-xtrem-data": "Der Suchknoten gibt keine Daten zurück. Erstelle  ", "@sage/xtrem-intacct-gateway/map/to-many-instance": "Der Payload hat zu viele Instanzen zurückgegeben: {{numberOfInstances}}", "@sage/xtrem-intacct-gateway/map/to-many-instance-mass-update": "Der Payload hat zu viele Instanzen zurückgegeben ({{numberOfInstances}}). Massenaktualisierung wird ausgeführt.", "@sage/xtrem-intacct-gateway/map/to-many-xtrem-data": "Der zu aktualisierende Payload hat zu viele Instanzen zurückgegeben: {{numberOfInstances}}.", "@sage/xtrem-intacct-gateway/menu_item__cashbook-manager": "", "@sage/xtrem-intacct-gateway/menu_item__intacct-mapping": "Mapping Intacct", "@sage/xtrem-intacct-gateway/menu_item__intacct-uom-config": "Konfig. ME Intacct", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__discountPaymentAmount": "Zahlungsbetrag Rabatt", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__discountPaymentBeforeDate": "<PERSON><PERSON><PERSON> Zahlung vor Datum", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__discountPaymentType": "Zahlungsart Rabatt", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__penaltyPaymentAmount": "Zahlungsbetrag Mahngebühr", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__penaltyPaymentType": "Zahlungsart Mahngebühr", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__discountPaymentAmount": "Zahlungsbetrag Rabatt", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__discountPaymentBeforeDate": "<PERSON><PERSON><PERSON> Zahlung vor Datum", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__discountPaymentType": "Zahlungsart Rabatt", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__penaltyPaymentAmount": "Zahlungsbetrag Mahngebühr", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__penaltyPaymentType": "Zahlungsart Mahngebühr", "@sage/xtrem-intacct-gateway/node-extensions__sales_order_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-gateway/node-extensions__sales_order_line_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-gateway/node-extensions__sales_shipment_extension__property__intacctId": "ID Sage Intacct", "@sage/xtrem-intacct-gateway/node-extensions__stock_journal_extension__no_financial_site": "Das Unternehmen {{ company }} hat keinen Buchhaltungsstandort.", "@sage/xtrem-intacct-gateway/node-extensions__work_in_progress_cost_extension__property__journalNumber": "Journal-<PERSON><PERSON>mer", "@sage/xtrem-intacct-gateway/package__name": "Zuordnung Sage Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension____title": "Verknüpfungen Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__customPackageFile____title": "Benutzerdefinierte Paketdatei", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__customPackageSection____title": "Benutzerdefiniertes Paket", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateBlock____title": "<PERSON><PERSON> generieren ", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canCreate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canDelete": "Löschen", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canUpdate": "Aktualisieren", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__id": "ID", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__intacctName": "Name Sage Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__synchronisationWay": "Richtung Synchronisierung", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__xtremObject": "Name Sage DMO", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____title": "<PERSON><PERSON> gene<PERSON><PERSON>e Smart Events", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integratedNodes____title": "Integrierte Nodes", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__description": "Bezeichnung", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__intacctMapId": "Mapping-ID Sage Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__integrationState": "Status", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__lastIntegrationDate": "Datum der letzten Integration", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__node": "Knoten", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__sysId": "sys-ID", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____dropdownActions__title": "Synchronisierung prüfen", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____dropdownActions__title__2": "Synchronisierung erzwingen", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____title": "Status Integration", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationStateBlock____title": "Status Integration", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationStateSection____title": "Status Integration", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationStatus____title": "Status Integration", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__isSupplierXtremSide____title": "XTreeM-seitig", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__itemSection____title": "Artikel", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title": "ID XTreeM", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title__2": "XTreeM Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title__3": "Verknüpfung", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title__4": "Entität Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title": "ID XTreeM", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__2": "ID Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__3": "Name Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__4": "Status", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__5": "Entität Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__objectSelect____title": "Objekt Sage Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__refreshIntegrationState____title": "Aktualisieren", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__smartEvent____title": "Intelligente Ereign<PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__supplierSection____title": "Lieferant", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__testSection____title": "Test", "@sage/xtrem-intacct-gateway/page-extensions__item_extension__glGroupName____title": "Name Buchungskreisgruppe", "@sage/xtrem-intacct-gateway/page-extensions__journal_entry_extension__blockIntacct____title": "Intacct", "@sage/xtrem-intacct-gateway/page-extensions__journal_entry_extension__intacctId____title": "ID Intacct", "@sage/xtrem-intacct-gateway/page-extensions__journal_entry_extension__intacctIntegrationState____title": "Status Intacct", "@sage/xtrem-intacct-gateway/page-extensions__journal_entry_extension__intacctUrl____title": "Journal Intacct", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__discountAmount____title": "Prozentsatz", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__discountAmount____title__2": "Prozentsatz", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__discountAmount____title__3": "Betrag", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__penaltyAmount____title": "Prozentsatz", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__penaltyAmount____title__2": "Prozentsatz", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__penaltyAmount____title__3": "Betrag", "@sage/xtrem-intacct-gateway/page-extensions__pre_journal_extension__blockIntacct____title": "Intacct", "@sage/xtrem-intacct-gateway/page-extensions__pre_journal_extension__intacctId____title": "ID Intacct", "@sage/xtrem-intacct-gateway/page-extensions__pre_journal_extension__intacctIntegrationState____title": "Status Intacct", "@sage/xtrem-intacct-gateway/page-extensions__pre_journal_extension__intacctUrl____title": "Journal Intacct", "@sage/xtrem-intacct-gateway/page-extensions__supplier_extension__accountingCodeList____title": "Kontencodeliste", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__base____columns__title__id": "ID", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__base____columns__title__name": "Name", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__base____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__block____title": "Konfiguration", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctFactor____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctGroup____columns__title__intacctrecordNumber": "ID", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctGroup____columns__title__name": "Name", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctGroup____title": "Gruppe", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctName____title": "Name", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctSection____title": "Intacct", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__isBaseIntacct____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure____title": "Konfiguration Intacct Maßeinheiten", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__detailsBlock____title": "Liste der Einheiten", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__inventoryUnitCode____title": "Lager", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__mainBlock____title": "Konfiguration", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__mainSection____title": "Maßeinheit", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__name____title": "Name", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__purchasingUnitCode____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__salesUnitCode____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__type____title": "Einheitstyp", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__columns__intacctGroup__name__title": "Gruppe", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__id": "ID", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__intacctFactor": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__intacctGroup__name": "Typ Intacct", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__intacctName": "Name Intacct", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__isBaseIntacct": "Basis Intacct", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__name": "Name", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__type": "Typ XTreeM", "@sage/xtrem-intacct-gateway/pages__intacct____title": "Konfiguration Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__companyId____title": "ID Unternehmen", "@sage/xtrem-intacct-gateway/pages__intacct__connectionTestAction____title": "Test", "@sage/xtrem-intacct-gateway/pages__intacct__endpointUrl____title": "Endpoint-URL", "@sage/xtrem-intacct-gateway/pages__intacct__entitites____title": "Entitäten", "@sage/xtrem-intacct-gateway/pages__intacct__id____title": "ID", "@sage/xtrem-intacct-gateway/pages__intacct__infoBox____title": "Testbox:", "@sage/xtrem-intacct-gateway/pages__intacct__isActive____title": "Aktiv", "@sage/xtrem-intacct-gateway/pages__intacct__isSupplierXtremSide____title": "XTreeM-seitig", "@sage/xtrem-intacct-gateway/pages__intacct__itemSection____title": "Artikel", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title": "ID XTreeM", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title__2": "XTreeM Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title__3": "Verknüpfung", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title__4": "Entität Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title": "ID XTreeM", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__2": "ID Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__3": "Name Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__4": "Status", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__5": "Entität Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__mainBlock____title": "Konfiguration", "@sage/xtrem-intacct-gateway/pages__intacct__mainSection____title": "Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__objectName____title": "Objektname", "@sage/xtrem-intacct-gateway/pages__intacct__result____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__intacct__saveAction____title": "Speichern", "@sage/xtrem-intacct-gateway/pages__intacct__senderId____title": "ID Absender", "@sage/xtrem-intacct-gateway/pages__intacct__smartEvent____title": "Intelligente Ereign<PERSON>", "@sage/xtrem-intacct-gateway/pages__intacct__supplierSection____title": "Lieferant", "@sage/xtrem-intacct-gateway/pages__intacct__testSection____title": "Test", "@sage/xtrem-intacct-gateway/pages__intacct__userId____title": "<PERSON>", "@sage/xtrem-intacct-gateway/pages__intacct__userPasswordHide____title": "Passwort", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line2__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line4__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line5__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__title__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____objectTypePlural": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____objectTypeSingular": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__additionnalLink____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__additionnalLinkFormated____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__addLineSpecificFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__availableXtremProperties____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__canCreate____title": "kann erstellen", "@sage/xtrem-intacct-gateway/pages__intacct_map__canDelete____title": "kann löschen", "@sage/xtrem-intacct-gateway/pages__intacct_map__canUpdate____title": "kann aktualisieren", "@sage/xtrem-intacct-gateway/pages__intacct_map__createAction____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__intacct_map__createAll____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__createAllIntacct____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__crudBlock____title": "Erstellen/Aktualisieren/Löschen", "@sage/xtrem-intacct-gateway/pages__intacct_map__dataSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__DATATYPE": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__DESCRIPTION": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__ID": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__ISCUSTOM": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__LABEL": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__READONLY": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__REQUIRED": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__xtremProperty": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__xtremPropertyOption": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__id____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__intacctDataBlock____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__intacctName____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__isActive____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__isSmartEvent____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__collectionName": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__line__id": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__line__intacctName": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__propertyHeader": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__propertyLine": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lineSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__integrationStatus": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__isLinked": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__url": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__whenCreated": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__whenModified": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__2": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__3": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__4": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__mainBlock____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__mainSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__refreshData____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__refreshRelationMapping____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__DATATYPE": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__DESCRIPTION": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__ID": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__ISCUSTOM": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__LABEL": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__READONLY": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__REQUIRED": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__xtremProperty": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__xtremPropertyOption": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__save____title": "Speichern", "@sage/xtrem-intacct-gateway/pages__intacct_map__saveAction____title": "Speichern", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__name": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__type": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__whereValue": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____dropdownActions__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__synchronisationWay____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__writeMappingFile____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__xtremObject____title": "", "@sage/xtrem-intacct-gateway/pages__map____subtitle": "Mapping Intacct", "@sage/xtrem-intacct-gateway/pages__map____title": "Mapping Intacct", "@sage/xtrem-intacct-gateway/pages__map__addLineSpecificFields____title": "Komponente hinzufügen", "@sage/xtrem-intacct-gateway/pages__map__availableXtremProperties____title": "Eigenschaften XTreeM", "@sage/xtrem-intacct-gateway/pages__map__canCreate____title": "kann erstellen", "@sage/xtrem-intacct-gateway/pages__map__canDelete____title": "kann löschen", "@sage/xtrem-intacct-gateway/pages__map__canUpdate____title": "kann aktualisieren", "@sage/xtrem-intacct-gateway/pages__map__createAction____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__createAll____title": "Alle in XTreeM erstellen", "@sage/xtrem-intacct-gateway/pages__map__createAllIntacct____title": "Alle in Intacct erstellen", "@sage/xtrem-intacct-gateway/pages__map__crudBlock____title": "Erstellen/Aktualisieren/Löschen", "@sage/xtrem-intacct-gateway/pages__map__dataSection____title": "Daten", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__DATATYPE": "Datentyp", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__DESCRIPTION": "Bezeichnung", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__ID": "ID", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__ISCUSTOM": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__LABEL": "Bezeichnung", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__READONLY": "Schreibgeschützt", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__REQUIRED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__xtremProperty": "Eigenschaft XTreeM", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__xtremPropertyOption": "Option Eigenschaft", "@sage/xtrem-intacct-gateway/pages__map__fieldsBlock____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__id____title": "Objekt Intacct", "@sage/xtrem-intacct-gateway/pages__map__intacctDataBlock____title": "Intacct", "@sage/xtrem-intacct-gateway/pages__map__intacctName____title": "Transaktionen Intacct", "@sage/xtrem-intacct-gateway/pages__map__isActive____title": "Aktiv", "@sage/xtrem-intacct-gateway/pages__map__isSmartEvent____title": "Smart Event", "@sage/xtrem-intacct-gateway/pages__map__lineMappingBlock____title": "Mapping", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__collectionName": "Name Collection", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__line__id": "Objekt", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__line__intacctName": "Name", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__propertyHeader": "Eigenschaft Kopf", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__propertyLine": "Eigenschaft Zeile", "@sage/xtrem-intacct-gateway/pages__map__lineSection____title": "Collections/Zeilen", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__isLinked": "Verknüpft", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__url": "URL", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__whenCreated": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__whenModified": "<PERSON>ktual<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title": "In Intacct erstellen/aktualisieren", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title__2": "In XTreeM erstellen", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title__3": "In XTreeM aktualisieren", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title__4": "In XTreeM löschen", "@sage/xtrem-intacct-gateway/pages__map__mainBlock____title": "Konfiguration", "@sage/xtrem-intacct-gateway/pages__map__mainSection____title": "Mapping", "@sage/xtrem-intacct-gateway/pages__map__refreshData____title": "Daten aktualisieren", "@sage/xtrem-intacct-gateway/pages__map__relationShipBlock____title": "Beziehung", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__DATATYPE": "Datentyp", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__DESCRIPTION": "Bezeichnung", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__ID": "ID", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__ISCUSTOM": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__LABEL": "Bezeichnung", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__READONLY": "Schreibgeschützt", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__REQUIRED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__xtremProperty": "Eigenschaft XTreeM", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__xtremPropertyOption": "Option Eigenschaft", "@sage/xtrem-intacct-gateway/pages__map__saveAction____title": "Speichern", "@sage/xtrem-intacct-gateway/pages__map__specificFields____columns__title__name": "Eigenschaftsname Intacct", "@sage/xtrem-intacct-gateway/pages__map__specificFields____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__specificFields____dropdownActions__title": "Entfernen", "@sage/xtrem-intacct-gateway/pages__map__synchronisationWay____title": "Richtung Synchronisierung", "@sage/xtrem-intacct-gateway/pages__map__xtremObject____title": "Knoten XTreeM", "@sage/xtrem-intacct-gateway/pages__paymentTerm__title": "Zahlungsbedingung"}