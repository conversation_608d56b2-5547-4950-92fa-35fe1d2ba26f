{"@sage/xtrem-intacct-gateway/enums__field_type__average": "", "@sage/xtrem-intacct-gateway/enums__field_type__count": "", "@sage/xtrem-intacct-gateway/enums__field_type__max": "", "@sage/xtrem-intacct-gateway/enums__field_type__min": "", "@sage/xtrem-intacct-gateway/enums__field_type__select": "", "@sage/xtrem-intacct-gateway/enums__field_type__sum": "", "@sage/xtrem-intacct-gateway/enums__intacct_matching_type__contains": "", "@sage/xtrem-intacct-gateway/enums__intacct_matching_type__regularExpression": "", "@sage/xtrem-intacct-gateway/enums__intacct_matching_type__strict": "", "@sage/xtrem-intacct-gateway/enums__property_type__description": "", "@sage/xtrem-intacct-gateway/enums__property_type__intacctId": "", "@sage/xtrem-intacct-gateway/enums__property_type__name": "", "@sage/xtrem-intacct-gateway/enums__property_type__none": "", "@sage/xtrem-intacct-gateway/enums__property_type__number": "", "@sage/xtrem-intacct-gateway/enums__property_type__text": "", "@sage/xtrem-intacct-gateway/enums__request_type__create": "", "@sage/xtrem-intacct-gateway/enums__request_type__createUpdate": "", "@sage/xtrem-intacct-gateway/enums__request_type__delete": "", "@sage/xtrem-intacct-gateway/enums__request_type__read": "", "@sage/xtrem-intacct-gateway/enums__request_type__update": "", "@sage/xtrem-intacct-gateway/map/no-or-to-many-intacct-data": "", "@sage/xtrem-intacct-gateway/map/no-xtrem-data": "", "@sage/xtrem-intacct-gateway/map/to-many-instance": "", "@sage/xtrem-intacct-gateway/map/to-many-instance-mass-update": "", "@sage/xtrem-intacct-gateway/map/to-many-xtrem-data": "", "@sage/xtrem-intacct-gateway/menu_item__cashbook-manager": "", "@sage/xtrem-intacct-gateway/menu_item__intacct-mapping": "", "@sage/xtrem-intacct-gateway/menu_item__intacct-uom-config": "", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__discountPaymentAmount": "Discount payment amount", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__discountPaymentBeforeDate": "Discount payment before date", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__discountPaymentType": "Discount payment type", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__penaltyPaymentAmount": "Penalty payment amount", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__penaltyPaymentType": "Penalty payment type", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__discountPaymentAmount": "Discount payment amount", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__discountPaymentBeforeDate": "Discount payment before date", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__discountPaymentType": "Discount payment type", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__penaltyPaymentAmount": "Penalty payment amount", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__penaltyPaymentType": "Penalty payment type", "@sage/xtrem-intacct-gateway/node-extensions__sales_order_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-gateway/node-extensions__sales_order_line_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-gateway/node-extensions__sales_shipment_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-gateway/node-extensions__stock_journal_extension__no_financial_site": "", "@sage/xtrem-intacct-gateway/node-extensions__work_in_progress_cost_extension__property__journalNumber": "Journal number", "@sage/xtrem-intacct-gateway/package__name": "Sage Intacct mapping", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension____title": "", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__customPackageFile____title": "Custom package file", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__customPackageSection____title": "Custom package", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canCreate": "Create", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canDelete": "Delete", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canUpdate": "Update", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__id": "ID", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__intacctName": "Sage Intacct name", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__synchronisationWay": "Synchronisation way", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__xtremObject": "Sage DMO name", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____title": "Smart Events to generate", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integratedNodes____title": "Integrated nodes", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__description": "Description", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__intacctMapId": "Sage Intacct map ID", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__integrationState": "Status", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__lastIntegrationDate": "Last integration date", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__node": "Node", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__sysId": "Sys ID", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____dropdownActions__title": "Check synchronisation", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____dropdownActions__title__2": "Force synchronisation", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____title": "Integration status", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationStateBlock____title": "Integration status", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationStateSection____title": "Integration status", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationStatus____title": "Integration status", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__isSupplierXtremSide____title": "", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__itemSection____title": "", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title": "", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title__2": "", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title__3": "", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title__4": "", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title": "", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__2": "", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__3": "", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__4": "", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__5": "", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__objectSelect____title": "Sage Intacct object", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__refreshIntegrationState____title": "Refresh", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__smartEvent____title": "", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__supplierSection____title": "", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__testSection____title": "", "@sage/xtrem-intacct-gateway/page-extensions__item_extension__glGroupName____title": "", "@sage/xtrem-intacct-gateway/page-extensions__supplier_extension__accountingCodeList____title": "", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__base____columns__title__id": "", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__base____columns__title__name": "", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__base____title": "", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__block____title": "", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctFactor____title": "", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctGroup____columns__title__intacctrecordNumber": "", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctGroup____columns__title__name": "", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctGroup____title": "", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctName____title": "", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctSection____title": "", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__isBaseIntacct____title": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure____title": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__detailsBlock____title": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__inventoryUnitCode____title": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__mainBlock____title": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__mainSection____title": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__name____title": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__purchasingUnitCode____title": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__salesUnitCode____title": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__type____title": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__columns__intacctGroup__name__title": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__decimalDigits": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__id": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__intacctFactor": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__intacctGroup__name": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__intacctName": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__isBaseIntacct": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__name": "", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__type": "", "@sage/xtrem-intacct-gateway/pages__intacct____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__companyId____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__connectionTestAction____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__endpointUrl____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__entitites____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__id____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__infoBox____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__isActive____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__isSupplierXtremSide____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__itemSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title": "", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title__2": "", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title__3": "", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title__4": "", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title": "", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__2": "", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__3": "", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__4": "", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__5": "", "@sage/xtrem-intacct-gateway/pages__intacct__mainBlock____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__mainSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__objectName____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__result____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__saveAction____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__senderId____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__smartEvent____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__supplierSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__testSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__userId____title": "", "@sage/xtrem-intacct-gateway/pages__intacct__userPasswordHide____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line2__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line4__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line5__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__title__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____objectTypePlural": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____objectTypeSingular": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__additionnalLink____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__additionnalLinkFormated____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__addLineSpecificFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__availableXtremProperties____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__canCreate____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__canDelete____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__canUpdate____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__createAction____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__createAll____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__createAllIntacct____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__crudBlock____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__dataSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__DATATYPE": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__DESCRIPTION": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__ID": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__ISCUSTOM": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__LABEL": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__READONLY": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__REQUIRED": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__xtremProperty": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__xtremPropertyOption": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__id____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__intacctDataBlock____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__intacctName____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__isActive____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__isSmartEvent____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__collectionName": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__line__id": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__line__intacctName": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__propertyHeader": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__propertyLine": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lineSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__integrationStatus": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__isLinked": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__url": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__whenCreated": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__whenModified": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__2": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__3": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__4": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__mainBlock____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__mainSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__refreshData____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__refreshRelationMapping____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__DATATYPE": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__DESCRIPTION": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__ID": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__ISCUSTOM": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__LABEL": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__READONLY": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__REQUIRED": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__xtremProperty": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__xtremPropertyOption": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__saveAction____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__name": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__type": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__whereValue": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____dropdownActions__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__synchronisationWay____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__writeMappingFile____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__xtremObject____title": "", "@sage/xtrem-intacct-gateway/pages__map____subtitle": "", "@sage/xtrem-intacct-gateway/pages__map____title": "", "@sage/xtrem-intacct-gateway/pages__map__addLineSpecificFields____title": "", "@sage/xtrem-intacct-gateway/pages__map__availableXtremProperties____title": "", "@sage/xtrem-intacct-gateway/pages__map__canCreate____title": "", "@sage/xtrem-intacct-gateway/pages__map__canDelete____title": "", "@sage/xtrem-intacct-gateway/pages__map__canUpdate____title": "", "@sage/xtrem-intacct-gateway/pages__map__createAction____title": "", "@sage/xtrem-intacct-gateway/pages__map__createAll____title": "", "@sage/xtrem-intacct-gateway/pages__map__createAllIntacct____title": "", "@sage/xtrem-intacct-gateway/pages__map__crudBlock____title": "", "@sage/xtrem-intacct-gateway/pages__map__dataSection____title": "", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__DATATYPE": "", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__DESCRIPTION": "", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__ID": "", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__ISCUSTOM": "", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__LABEL": "", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__READONLY": "", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__REQUIRED": "", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__xtremProperty": "", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__xtremPropertyOption": "", "@sage/xtrem-intacct-gateway/pages__map__fieldsBlock____title": "", "@sage/xtrem-intacct-gateway/pages__map__id____title": "", "@sage/xtrem-intacct-gateway/pages__map__intacctDataBlock____title": "", "@sage/xtrem-intacct-gateway/pages__map__intacctName____title": "", "@sage/xtrem-intacct-gateway/pages__map__isActive____title": "", "@sage/xtrem-intacct-gateway/pages__map__isSmartEvent____title": "", "@sage/xtrem-intacct-gateway/pages__map__lineMappingBlock____title": "", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__collectionName": "", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__line__id": "", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__line__intacctName": "", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__propertyHeader": "", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__propertyLine": "", "@sage/xtrem-intacct-gateway/pages__map__lineSection____title": "", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__isLinked": "", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__url": "", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__whenCreated": "", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__whenModified": "", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title": "", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title__2": "", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title__3": "", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title__4": "", "@sage/xtrem-intacct-gateway/pages__map__mainBlock____title": "", "@sage/xtrem-intacct-gateway/pages__map__mainSection____title": "", "@sage/xtrem-intacct-gateway/pages__map__refreshData____title": "", "@sage/xtrem-intacct-gateway/pages__map__relationShipBlock____title": "", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__DATATYPE": "", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__DESCRIPTION": "", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__ID": "", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__ISCUSTOM": "", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__LABEL": "", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__READONLY": "", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__REQUIRED": "", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__xtremProperty": "", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__xtremPropertyOption": "", "@sage/xtrem-intacct-gateway/pages__map__saveAction____title": "", "@sage/xtrem-intacct-gateway/pages__map__specificFields____columns__title__name": "", "@sage/xtrem-intacct-gateway/pages__map__specificFields____columns__title__type": "", "@sage/xtrem-intacct-gateway/pages__map__specificFields____dropdownActions__title": "", "@sage/xtrem-intacct-gateway/pages__map__synchronisationWay____title": "", "@sage/xtrem-intacct-gateway/pages__map__xtremObject____title": "", "@sage/xtrem-intacct-gateway/pages__paymentTerm__title": ""}