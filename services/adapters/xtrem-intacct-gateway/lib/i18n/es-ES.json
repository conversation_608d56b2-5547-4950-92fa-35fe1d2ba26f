{"@sage/xtrem-intacct-gateway/enums__field_type__average": "Promedio", "@sage/xtrem-intacct-gateway/enums__field_type__count": "Recuento", "@sage/xtrem-intacct-gateway/enums__field_type__max": "Máx.", "@sage/xtrem-intacct-gateway/enums__field_type__min": "<PERSON><PERSON>.", "@sage/xtrem-intacct-gateway/enums__field_type__select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/enums__field_type__sum": "<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/enums__intacct_matching_type__contains": "", "@sage/xtrem-intacct-gateway/enums__intacct_matching_type__regularExpression": "", "@sage/xtrem-intacct-gateway/enums__intacct_matching_type__strict": "", "@sage/xtrem-intacct-gateway/enums__property_type__description": "Descripción", "@sage/xtrem-intacct-gateway/enums__property_type__intacctId": "Id. en Intacct", "@sage/xtrem-intacct-gateway/enums__property_type__name": "Nombre", "@sage/xtrem-intacct-gateway/enums__property_type__none": "Ninguna", "@sage/xtrem-intacct-gateway/enums__property_type__number": "Número", "@sage/xtrem-intacct-gateway/enums__property_type__text": "Texto", "@sage/xtrem-intacct-gateway/enums__request_type__create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/enums__request_type__createUpdate": "<PERSON><PERSON>r/actualizar", "@sage/xtrem-intacct-gateway/enums__request_type__delete": "Eliminar", "@sage/xtrem-intacct-gateway/enums__request_type__read": "<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/enums__request_type__update": "Actualizar", "@sage/xtrem-intacct-gateway/map/no-or-to-many-intacct-data": "Faltan datos de Intacct o hay demasiados: {{length}}  ", "@sage/xtrem-intacct-gateway/map/no-xtrem-data": "El nodo de búsqueda no devuelve ningún resultado. Crear.  ", "@sage/xtrem-intacct-gateway/map/to-many-instance": "La payload ha devuelto demasiadas instancias:{{numberOfInstances}}.", "@sage/xtrem-intacct-gateway/map/to-many-instance-mass-update": "Payload ha devuelto demasiadas instancias ({{numberOfInstances}}). Actualización masiva en curso.", "@sage/xtrem-intacct-gateway/map/to-many-xtrem-data": "La payload por actualizar ha devuelto demasiadas instancias:{{numberOfInstances}}.", "@sage/xtrem-intacct-gateway/menu_item__cashbook-manager": "", "@sage/xtrem-intacct-gateway/menu_item__intacct-mapping": "Mapeo en Intacct", "@sage/xtrem-intacct-gateway/menu_item__intacct-uom-config": "Config. Intacct de UM", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__discountPaymentBeforeDate": "Fecha límite de pago de descuento", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__discountPaymentBeforeDate": "Fecha límite de pago de descuento", "@sage/xtrem-intacct-gateway/node-extensions__sales_order_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-gateway/node-extensions__sales_order_line_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-gateway/node-extensions__sales_shipment_extension__property__intacctId": "Id. en Sage Intacct", "@sage/xtrem-intacct-gateway/node-extensions__stock_journal_extension__no_financial_site": "No hay ninguna planta financiera en la sociedad {{ company }}.", "@sage/xtrem-intacct-gateway/node-extensions__work_in_progress_cost_extension__property__journalNumber": "Número de diario", "@sage/xtrem-intacct-gateway/package__name": "Mapeo en Sage Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension____title": "Vínculos a Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__customPackageFile____title": "Archivo de paquete personalizado", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__customPackageSection____title": "Paquete personalizado", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canCreate": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canDelete": "Eliminar", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canUpdate": "Actualizar", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__id": "Id.", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__intacctName": "Nombre en Sage Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__synchronisationWay": "Sentido de sincronización", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__xtremObject": "Nombre en Sage DMO", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____title": "Smart Events por generar", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integratedNodes____title": "Nodos integrados", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__description": "Descripción", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__intacctMapId": "Id. de mapeo en Sage Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__integrationState": "Estado", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__lastIntegrationDate": "Última fecha de integración", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__node": "Nodo", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__sysId": "Id. de siste<PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____dropdownActions__title": "Comprobar sincronización", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____dropdownActions__title__2": "<PERSON>zar <PERSON>cron<PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____title": "Estado de integración", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationStateBlock____title": "Estado de integración", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationStateSection____title": "Estado de integración", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationStatus____title": "Estado de integración", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__isSupplierXtremSide____title": "Parte de XTreeM", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__itemSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title": "Id. en XTreeM", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title__2": "XTreeM Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title__4": "Entidad en Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title": "Id. en XTreeM", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__2": "Id. en Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__3": "Nombre en Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__4": "Estado", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__5": "Entidad en Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__objectSelect____title": "Objeto en Sage Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__refreshIntegrationState____title": "Actualizar", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__smartEvent____title": "Smart Events", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__supplierSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__testSection____title": "Prueba", "@sage/xtrem-intacct-gateway/page-extensions__item_extension__glGroupName____title": "Nombre de grupo LM", "@sage/xtrem-intacct-gateway/page-extensions__journal_entry_extension__blockIntacct____title": "Intacct", "@sage/xtrem-intacct-gateway/page-extensions__journal_entry_extension__intacctId____title": "Id. en Intacct", "@sage/xtrem-intacct-gateway/page-extensions__journal_entry_extension__intacctIntegrationState____title": "Estado en Intacct", "@sage/xtrem-intacct-gateway/page-extensions__journal_entry_extension__intacctUrl____title": "Diario en Intacct", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__discountAmount____title": "Po<PERSON>entaj<PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__discountAmount____title__2": "Po<PERSON>entaj<PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__discountAmount____title__3": "Importe", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__penaltyAmount____title": "Po<PERSON>entaj<PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__penaltyAmount____title__2": "Po<PERSON>entaj<PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__penaltyAmount____title__3": "Importe", "@sage/xtrem-intacct-gateway/page-extensions__pre_journal_extension__blockIntacct____title": "Intacct", "@sage/xtrem-intacct-gateway/page-extensions__pre_journal_extension__intacctId____title": "Id. en Intacct", "@sage/xtrem-intacct-gateway/page-extensions__pre_journal_extension__intacctIntegrationState____title": "Estado en Intacct", "@sage/xtrem-intacct-gateway/page-extensions__pre_journal_extension__intacctUrl____title": "Diario en Intacct", "@sage/xtrem-intacct-gateway/page-extensions__supplier_extension__accountingCodeList____title": "Lista de códigos contables", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__base____columns__title__id": "Id.", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__base____columns__title__name": "Nombre", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__base____title": "Base", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__block____title": "Configuración", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctFactor____title": "Coeficiente", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctGroup____columns__title__intacctrecordNumber": "Id.", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctGroup____columns__title__name": "Nombre", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctGroup____title": "Grupo", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctName____title": "Nombre", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctSection____title": "Intacct", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__isBaseIntacct____title": "Base", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure____title": "Configuración de unidades de medida en Intacct", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__detailsBlock____title": "Lista de unidades", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__inventoryUnitCode____title": "Stock", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__mainBlock____title": "Configuración", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__mainSection____title": "Unidad de medida", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__name____title": "Nombre", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__purchasingUnitCode____title": "Compras", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__salesUnitCode____title": "Ventas", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__type____title": "Tipo de unidad", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__columns__intacctGroup__name__title": "Grupo", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__id": "Id.", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__intacctFactor": "Coeficiente", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__intacctGroup__name": "Tipo en Intacct", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__intacctName": "Nombre en Intacct", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__isBaseIntacct": "Base en Intacct", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__name": "Nombre", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__type": "Tipo en XTreeM", "@sage/xtrem-intacct-gateway/pages__intacct____title": "Configuración en Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__companyId____title": "Id. de sociedad", "@sage/xtrem-intacct-gateway/pages__intacct__connectionTestAction____title": "Prueba", "@sage/xtrem-intacct-gateway/pages__intacct__endpointUrl____title": "URL de punto de conexión", "@sage/xtrem-intacct-gateway/pages__intacct__entitites____title": "Entidades", "@sage/xtrem-intacct-gateway/pages__intacct__id____title": "Id.", "@sage/xtrem-intacct-gateway/pages__intacct__infoBox____title": "Cuadro de prueba", "@sage/xtrem-intacct-gateway/pages__intacct__isActive____title": "Activa", "@sage/xtrem-intacct-gateway/pages__intacct__isSupplierXtremSide____title": "Parte de XTreeM", "@sage/xtrem-intacct-gateway/pages__intacct__itemSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title": "Id. en XTreeM", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title__2": "XTreeM Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title__4": "Entidad en Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title": "Id. en XTreeM", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__2": "Id. en Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__3": "Nombre en Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__4": "Estado", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__5": "Entidad en Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__mainBlock____title": "Configuración", "@sage/xtrem-intacct-gateway/pages__intacct__mainSection____title": "Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__objectName____title": "Nombre de objeto", "@sage/xtrem-intacct-gateway/pages__intacct__result____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__intacct__saveAction____title": "Guardar", "@sage/xtrem-intacct-gateway/pages__intacct__senderId____title": "Id. de emisor", "@sage/xtrem-intacct-gateway/pages__intacct__smartEvent____title": "Smart Events", "@sage/xtrem-intacct-gateway/pages__intacct__supplierSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__intacct__testSection____title": "Prueba", "@sage/xtrem-intacct-gateway/pages__intacct__userId____title": "<PERSON>d. de usuario", "@sage/xtrem-intacct-gateway/pages__intacct__userPasswordHide____title": "Contraseña", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line2__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line4__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line5__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__title__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____objectTypePlural": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____objectTypeSingular": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____subtitle": "Mapeo en Sage Intacct", "@sage/xtrem-intacct-gateway/pages__intacct_map____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__additionnalLink____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__additionnalLinkFormated____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__addLineSpecificFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__availableXtremProperties____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__canCreate____title": "puede crear", "@sage/xtrem-intacct-gateway/pages__intacct_map__canDelete____title": "puede eliminar", "@sage/xtrem-intacct-gateway/pages__intacct_map__canUpdate____title": "puede actualizar", "@sage/xtrem-intacct-gateway/pages__intacct_map__createAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__intacct_map__createAll____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__createAllIntacct____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__crudBlock____title": "<PERSON><PERSON><PERSON>/actualizar/eliminar", "@sage/xtrem-intacct-gateway/pages__intacct_map__dataSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__DATATYPE": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__DESCRIPTION": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__ID": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__ISCUSTOM": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__LABEL": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__READONLY": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__REQUIRED": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__xtremProperty": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__xtremPropertyOption": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__id____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__intacctDataBlock____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__intacctName____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__isActive____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__isSmartEvent____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__collectionName": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__line__id": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__line__intacctName": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__propertyHeader": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__propertyLine": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lineSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__integrationStatus": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__isLinked": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__url": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__whenCreated": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__whenModified": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__2": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__3": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__4": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__mainBlock____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__mainSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__refreshData____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__refreshRelationMapping____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__DATATYPE": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__DESCRIPTION": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__ID": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__ISCUSTOM": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__LABEL": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__READONLY": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__REQUIRED": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__xtremProperty": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__xtremPropertyOption": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__save____title": "Guardar", "@sage/xtrem-intacct-gateway/pages__intacct_map__saveAction____title": "Guardar", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__name": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__type": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__whereValue": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____dropdownActions__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__synchronisationWay____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__writeMappingFile____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__xtremObject____title": "", "@sage/xtrem-intacct-gateway/pages__map____subtitle": "Mapeo en Intacct", "@sage/xtrem-intacct-gateway/pages__map____title": "Mapeo en Intacct", "@sage/xtrem-intacct-gateway/pages__map__addLineSpecificFields____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__availableXtremProperties____title": "Propiedades en XTreeM", "@sage/xtrem-intacct-gateway/pages__map__canCreate____title": "puede crear", "@sage/xtrem-intacct-gateway/pages__map__canDelete____title": "puede eliminar", "@sage/xtrem-intacct-gateway/pages__map__canUpdate____title": "puede actualizar", "@sage/xtrem-intacct-gateway/pages__map__createAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__createAll____title": "<PERSON><PERSON>r todo en XTreeM", "@sage/xtrem-intacct-gateway/pages__map__createAllIntacct____title": "<PERSON><PERSON>r todo en Intacct", "@sage/xtrem-intacct-gateway/pages__map__crudBlock____title": "<PERSON><PERSON><PERSON>/actualizar/eliminar", "@sage/xtrem-intacct-gateway/pages__map__dataSection____title": "Datos", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__DATATYPE": "Tipo de <PERSON>", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__DESCRIPTION": "Descripción", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__ID": "Id.", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__ISCUSTOM": "Personalizado", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__LABEL": "Etiqueta", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__READONLY": "Solo lectura", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__REQUIRED": "Obligatorio", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__xtremProperty": "Propiedad en XTreeM", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__xtremPropertyOption": "Opción de propiedad", "@sage/xtrem-intacct-gateway/pages__map__fieldsBlock____title": "Campos", "@sage/xtrem-intacct-gateway/pages__map__id____title": "Objeto en Intacct", "@sage/xtrem-intacct-gateway/pages__map__intacctDataBlock____title": "Intacct", "@sage/xtrem-intacct-gateway/pages__map__intacctName____title": "Transacciones en Intacct", "@sage/xtrem-intacct-gateway/pages__map__isActive____title": "Activo", "@sage/xtrem-intacct-gateway/pages__map__isSmartEvent____title": "Smart Event", "@sage/xtrem-intacct-gateway/pages__map__lineMappingBlock____title": "Mapeo", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__collectionName": "Nombre de colección", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__line__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__line__intacctName": "Nombre", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__propertyHeader": "Propiedad de cabecera", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__propertyLine": "Propiedad de línea", "@sage/xtrem-intacct-gateway/pages__map__lineSection____title": "Colecciones/líneas", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__isLinked": "Vinculado", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__url": "URL", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__whenCreated": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__whenModified": "Actualizado", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title": "Crear/actualizar en Intacct", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title__2": "Crear en XTreeM", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title__3": "Actualizar en XTreeM", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title__4": "Eliminar en XTreeM", "@sage/xtrem-intacct-gateway/pages__map__mainBlock____title": "Configuración", "@sage/xtrem-intacct-gateway/pages__map__mainSection____title": "Mapeo", "@sage/xtrem-intacct-gateway/pages__map__refreshData____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__relationShipBlock____title": "Relación", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__DATATYPE": "Tipo de <PERSON>", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__DESCRIPTION": "Descripción", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__ID": "Id.", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__ISCUSTOM": "Personalizada", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__LABEL": "Etiqueta", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__READONLY": "Solo lectura", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__REQUIRED": "Obligatorio", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__xtremProperty": "Propiedad en XTreeM", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__xtremPropertyOption": "Opción de propiedad", "@sage/xtrem-intacct-gateway/pages__map__saveAction____title": "Guardar", "@sage/xtrem-intacct-gateway/pages__map__specificFields____columns__title__name": "Nombre de propiedad en Intacct", "@sage/xtrem-intacct-gateway/pages__map__specificFields____columns__title__type": "Tipo", "@sage/xtrem-intacct-gateway/pages__map__specificFields____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__synchronisationWay____title": "Sentido de sincronización", "@sage/xtrem-intacct-gateway/pages__map__xtremObject____title": "Nodo en XTreeM", "@sage/xtrem-intacct-gateway/pages__paymentTerm__title": "Condiciones de pago"}