{"@sage/xtrem-intacct-gateway/enums__field_type__average": "Average", "@sage/xtrem-intacct-gateway/enums__field_type__count": "Count", "@sage/xtrem-intacct-gateway/enums__field_type__max": "Max", "@sage/xtrem-intacct-gateway/enums__field_type__min": "<PERSON>.", "@sage/xtrem-intacct-gateway/enums__field_type__select": "Select", "@sage/xtrem-intacct-gateway/enums__field_type__sum": "Sum", "@sage/xtrem-intacct-gateway/enums__intacct_matching_type__contains": "", "@sage/xtrem-intacct-gateway/enums__intacct_matching_type__regularExpression": "", "@sage/xtrem-intacct-gateway/enums__intacct_matching_type__strict": "", "@sage/xtrem-intacct-gateway/enums__property_type__description": "Description", "@sage/xtrem-intacct-gateway/enums__property_type__intacctId": "Intacct ID", "@sage/xtrem-intacct-gateway/enums__property_type__name": "Name", "@sage/xtrem-intacct-gateway/enums__property_type__none": "None", "@sage/xtrem-intacct-gateway/enums__property_type__number": "Number", "@sage/xtrem-intacct-gateway/enums__property_type__text": "Text", "@sage/xtrem-intacct-gateway/enums__request_type__create": "Create", "@sage/xtrem-intacct-gateway/enums__request_type__createUpdate": "Create/Update", "@sage/xtrem-intacct-gateway/enums__request_type__delete": "Delete", "@sage/xtrem-intacct-gateway/enums__request_type__read": "Read", "@sage/xtrem-intacct-gateway/enums__request_type__update": "Update", "@sage/xtrem-intacct-gateway/map/no-xtrem-data": "The search node returns no data. Create  ", "@sage/xtrem-intacct-gateway/map/to-many-instance": "The payload returned too many instances: {{numberOfInstances}}", "@sage/xtrem-intacct-gateway/map/to-many-xtrem-data": "The payload to be updated returned too many instances: {{numberOfInstances}}.", "@sage/xtrem-intacct-gateway/menu_item__cashbook-manager": "", "@sage/xtrem-intacct-gateway/menu_item__intacct-mapping": "Intacct mapping", "@sage/xtrem-intacct-gateway/menu_item__intacct-uom-config": "UOM Intacct config.", "@sage/xtrem-intacct-gateway/node-extensions__accounts-receivable-invoice-extension__accounts_payable_invoice_already_sent": "The accounts receivable invoice has already been sent to Sage Intacct. Status: {{intacctIntegrationState}}", "@sage/xtrem-intacct-gateway/node-extensions__attribute_extension__property__intacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__attribute_extension__property__recordNo": "", "@sage/xtrem-intacct-gateway/node-extensions__attribute_extension__property__statusIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__attribute_extension__property__uIntacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__business_entity_address_contact_extension__property__intacctPrintAs": "", "@sage/xtrem-intacct-gateway/node-extensions__business_entity_address_extension__property__intacctContactName": "", "@sage/xtrem-intacct-gateway/node-extensions__business_entity_address_extension__property__intacctContactPrefix": "", "@sage/xtrem-intacct-gateway/node-extensions__business_entity_address_extension__property__intacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__business_entity_address_extension__property__intacctIntegrationState": "", "@sage/xtrem-intacct-gateway/node-extensions__business_entity_address_extension__property__intacctLastIntegrationDate": "", "@sage/xtrem-intacct-gateway/node-extensions__business_entity_address_extension__property__intacctPrintAs": "", "@sage/xtrem-intacct-gateway/node-extensions__business_entity_address_extension__property__primaryContact": "", "@sage/xtrem-intacct-gateway/node-extensions__business_entity_address_extension__property__recordNo": "", "@sage/xtrem-intacct-gateway/node-extensions__business_entity_address_extension__property__statusIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__business_entity_address_extension__property__uIntacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__customer_extension__property__billToAddressIntacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__customer_extension__property__contactListInfoIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__customer_extension__property__hideDisplayContact": "", "@sage/xtrem-intacct-gateway/node-extensions__customer_extension__property__intacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__customer_extension__property__intacctIntegrationState": "", "@sage/xtrem-intacct-gateway/node-extensions__customer_extension__property__intacctLastIntegrationDate": "", "@sage/xtrem-intacct-gateway/node-extensions__customer_extension__property__intacctUrl": "", "@sage/xtrem-intacct-gateway/node-extensions__customer_extension__property__primaryContactIntacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__customer_extension__property__recordNo": "", "@sage/xtrem-intacct-gateway/node-extensions__customer_extension__property__shipToAddressIntacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__customer_extension__property__status": "", "@sage/xtrem-intacct-gateway/node-extensions__customer_extension__property__uIntacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__dimension_extension__property__intacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__dimension_extension__property__intacctObject": "", "@sage/xtrem-intacct-gateway/node-extensions__dimension_extension__property__recordNo": "", "@sage/xtrem-intacct-gateway/node-extensions__dimension_extension__property__statusIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__dimension_extension__property__uIntacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__dimension_type_extension__property__intacctObject": "", "@sage/xtrem-intacct-gateway/node-extensions__intacct_extension__property__customPackageFile": "", "@sage/xtrem-intacct-gateway/node-extensions__intacct_extension__property__generateSmartEvent": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__intacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__intacctIntegrationState": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__intacctLastIntegrationDate": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__intacctUrl": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__isEnableBinsIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__isEnableExpirationIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__isEnableLotCategoryIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__isEnableSerialNoIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__lotCategoryIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__recordNo": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__shipWeightIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__statusIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__typeIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__uIntacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__unitOfMeasureDefaultIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__item_extension__property__valuationMethodIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_extension__property__intacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_extension__property__intacctIntegrationState": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_extension__property__intacctLastIntegrationDate": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_extension__property__intacctUrl": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_extension__property__isActive": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_extension__property__recordNo": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_line_dimension_extension__property__intacctDimension": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_line_extension__property__absoluteTransactionAmount": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_line_extension__property__allocationSplit": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_line_extension__property__intacctDimensionSplit": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_line_extension__property__intacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_line_extension__property__intacctSign": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_line_extension__property__isActive": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_line_extension__property__locationWhenNoSplit": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_entry_line_extension__property__recordNo": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_extension__property__intacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_extension__property__recordNo": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_extension__property__statusIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__journal_extension__property__uIntacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__discountAmount": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__discountDate": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__discountFrom": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__discountFromIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__discountType": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__discountTypeIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__dueFromIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__intacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__isIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__penaltyAmount": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__penaltyType": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__penaltyTypeIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__recordNo": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__statusIntacct": "", "@sage/xtrem-intacct-gateway/node-extensions__payment_term_extension__property__uIntacctId": "", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__discountPaymentAmount": "Discount payment amount", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__discountPaymentType": "Discount payment type", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__penaltyPaymentAmount": "Penalty payment amount", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__penaltyPaymentType": "Penalty payment type", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__discountPaymentAmount": "Discount payment amount", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__discountPaymentType": "Discount payment type", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__penaltyPaymentAmount": "Penalty payment amount", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__penaltyPaymentType": "Penalty payment type", "@sage/xtrem-intacct-gateway/node-extensions__sales_order_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-gateway/node-extensions__sales_order_line_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-gateway/node-extensions__sales_shipment_extension__property__intacctId": "Sage Intacct ID", "@sage/xtrem-intacct-gateway/node-extensions__stock_journal_extension__no_financial_site": "The {{ company }} company has no financial site.", "@sage/xtrem-intacct-gateway/node-extensions__work_in_progress_cost_extension__property__journalNumber": "Journal number", "@sage/xtrem-intacct-gateway/package__name": "Sage Intacct mapping", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension____title": "Intacct links", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__customPackageFile____title": "Custom package file", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__customPackageSection____title": "Custom package", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canCreate": "Create", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canDelete": "Delete", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canUpdate": "Update", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__id": "ID", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__intacctName": "Sage Intacct name", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__synchronisationWay": "Synchronization way", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__xtremObject": "Sage DMO name", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____title": "Smart Events to generate", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__isSupplierXtremSide____title": "XTreeM side", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__itemSection____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title": "XTreeM ID", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title__2": "XTreeM Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title__3": "Link", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedItem____columns__title__4": "Intacct entity", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title": "XTreeM ID", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__2": "Intacct ID", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__3": "Intacct name", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__4": "Status", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__linkedSupplier____columns__title__5": "Intacct entity", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__objectSelect____title": "Sage Intacct object", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__smartEvent____title": "Smart events", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__supplierSection____title": "Supplier", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__testSection____title": "Test", "@sage/xtrem-intacct-gateway/page-extensions__item_extension__glGroupName____title": "GL group name", "@sage/xtrem-intacct-gateway/page-extensions__sales_order_extension__shippingMethod____title": "Shipping method", "@sage/xtrem-intacct-gateway/page-extensions__supplier_extension__accountingCodeList____title": "Accounting code list", "@sage/xtrem-intacct-gateway/page-extensions__supplier_extension__paymentTermList____title": "Payment term", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__base____columns__title__id": "ID", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__base____columns__title__name": "Name", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__base____title": "Base", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__block____title": "Configuration", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctFactor____title": "Factor", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctGroup____columns__title__intacctrecordNumber": "ID", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctGroup____columns__title__name": "Name", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctGroup____title": "Group", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctName____title": "Name", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__intacctSection____title": "Intacct", "@sage/xtrem-intacct-gateway/page-extensions__unit_of_measure_extension__isBaseIntacct____title": "Base", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure____title": "Unit of measure Intacct configuration", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__detailsBlock____title": "List of units", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__inventoryUnitCode____title": "Stock", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__mainBlock____title": "Configuration", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__mainSection____title": "Unit of measure", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__name____title": "Name", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__purchasingUnitCode____title": "Purchasing", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__salesUnitCode____title": "Sales", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__type____title": "Unit type", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__columns__intacctGroup__name__title": "Group", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__decimalDigits": "Decimal digits", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__id": "ID", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__intacctFactor": "Factor", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__intacctGroup__name": "Intacct type", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__intacctName": "Intacct name", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__isBaseIntacct": "Intacct base", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__name": "Name", "@sage/xtrem-intacct-gateway/pages__group_unit_of_measure__xtremUnits____columns__title__type": "XTreeM type", "@sage/xtrem-intacct-gateway/pages__intacct____title": "Intacct configuration", "@sage/xtrem-intacct-gateway/pages__intacct__companyId____title": "Company ID", "@sage/xtrem-intacct-gateway/pages__intacct__connectionTestAction____title": "Test", "@sage/xtrem-intacct-gateway/pages__intacct__endpointUrl____title": "Endpoint URL", "@sage/xtrem-intacct-gateway/pages__intacct__entitites____title": "Entities", "@sage/xtrem-intacct-gateway/pages__intacct__id____title": "ID", "@sage/xtrem-intacct-gateway/pages__intacct__infoBox____title": "Test box:", "@sage/xtrem-intacct-gateway/pages__intacct__isActive____title": "Active", "@sage/xtrem-intacct-gateway/pages__intacct__isSupplierXtremSide____title": "XTreeM side", "@sage/xtrem-intacct-gateway/pages__intacct__itemSection____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title": "XTreeM ID", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title__2": "XTreeM Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title__3": "Link", "@sage/xtrem-intacct-gateway/pages__intacct__linkedItem____columns__title__4": "Intacct entity", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title": "XTreeM ID", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__2": "Intacct ID", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__3": "Intacct name", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__4": "Status", "@sage/xtrem-intacct-gateway/pages__intacct__linkedSupplier____columns__title__5": "Intacct entity", "@sage/xtrem-intacct-gateway/pages__intacct__mainBlock____title": "Configuration", "@sage/xtrem-intacct-gateway/pages__intacct__mainSection____title": "Intacct", "@sage/xtrem-intacct-gateway/pages__intacct__objectName____title": "Object name", "@sage/xtrem-intacct-gateway/pages__intacct__result____title": "Result", "@sage/xtrem-intacct-gateway/pages__intacct__saveAction____title": "Save", "@sage/xtrem-intacct-gateway/pages__intacct__senderId____title": "Sender ID", "@sage/xtrem-intacct-gateway/pages__intacct__smartEvent____title": "Smart events", "@sage/xtrem-intacct-gateway/pages__intacct__supplierSection____title": "Supplier", "@sage/xtrem-intacct-gateway/pages__intacct__testSection____title": "Test", "@sage/xtrem-intacct-gateway/pages__intacct__userId____title": "User ID", "@sage/xtrem-intacct-gateway/pages__intacct__userPasswordHide____title": "Password", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line2__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line4__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line5__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__title__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____objectTypePlural": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____objectTypeSingular": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__additionnalLink____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__additionnalLinkFormated____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__addLineSpecificFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__availableXtremProperties____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__create____title": "Create", "@sage/xtrem-intacct-gateway/pages__intacct_map__createAll____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__createAllIntacct____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__dataSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__DATATYPE": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__DESCRIPTION": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__ID": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__ISCUSTOM": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__LABEL": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__READONLY": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__REQUIRED": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__xtremProperty": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__xtremPropertyOption": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__id____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__intacctDataBlock____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__intacctName____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__isActive____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__isSmartEvent____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__collectionName": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__line__id": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__line__intacctName": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__propertyHeader": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__propertyLine": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lineSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__integrationStatus": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__isLinked": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__url": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__whenCreated": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__whenModified": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__2": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__3": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__4": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__mainBlock____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__mainSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__refreshData____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__refreshRelationMapping____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__DATATYPE": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__DESCRIPTION": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__ID": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__ISCUSTOM": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__LABEL": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__READONLY": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__REQUIRED": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__xtremProperty": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__xtremPropertyOption": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__name": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__type": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__whereValue": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____dropdownActions__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__synchronisationWay____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__writeMappingFile____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__xtremObject____title": "", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__4": "Create", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__5": "Update", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__6": "XTreeM ID", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__7": "XTreeM name", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__8": "XTreeM description", "@sage/xtrem-intacct-gateway/pages__paymentTerm__title": "Payment term"}