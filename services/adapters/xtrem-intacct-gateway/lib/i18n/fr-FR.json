{"@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__discountPaymentAmount": "Montant de règlement de remise", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__discountPaymentBeforeDate": "Date maximum du règlement de remise", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__discountPaymentType": "Type de règlement de remise", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__penaltyPaymentAmount": "Montant de règlement de pénalité", "@sage/xtrem-intacct-gateway/node-extensions__sales_credit_memo_extension__property__penaltyPaymentType": "Type de règlement de pénalité", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__discountPaymentAmount": "Montant de règlement de remise", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__discountPaymentBeforeDate": "Date maximum du règlement de remise", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__discountPaymentType": "Type de règlement de remise", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__penaltyPaymentAmount": "Montant de règlement de pénalité", "@sage/xtrem-intacct-gateway/node-extensions__sales_invoice_extension__property__penaltyPaymentType": "Type de règlement de pénalité", "@sage/xtrem-intacct-gateway/node-extensions__sales_order_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-gateway/node-extensions__sales_order_line_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-gateway/node-extensions__sales_shipment_extension__property__intacctId": "Code Sage Intacct", "@sage/xtrem-intacct-gateway/node-extensions__stock_journal_extension__no_financial_site": "La société {{legalCompany}} ne possède pas de site financier.", "@sage/xtrem-intacct-gateway/node-extensions__work_in_progress_cost_extension__property__journalNumber": "Numéro de journal", "@sage/xtrem-intacct-gateway/package__name": "Mapping Sage Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__customPackageFile____title": "Fichier de package personnalisé", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__customPackageSection____title": "Package personnalisé", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canCreate": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canDelete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__canUpdate": "Actualiser", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__id": "Code", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__intacctName": "Nom de Sage Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__synchronisationWay": "Sens de synchronisation", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____columns__title__xtremObject": "Nom Sage DMO", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__generateSmartEvent____title": "Smart events à générer", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integratedNodes____title": "Nodes intégrés", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__description": "Description", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__intacctMapId": "Code mapping Sage Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__integrationState": "Statut", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__lastIntegrationDate": "Date dernière integration", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__node": "Node", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____columns__title__sysId": "Code sys.", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____dropdownActions__title": "Vérifiez la synchronisation", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____dropdownActions__title__2": "Forcer la synchronisation", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationState____title": "Statut d'intégration", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationStateBlock____title": "Statut d'intégration", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationStateSection____title": "Statut d'intégration", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__integrationStatus____title": "Statut d'intégration", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__objectSelect____title": "Objet Sage Intacct", "@sage/xtrem-intacct-gateway/page-extensions__intacct_extension__refreshIntegrationState____title": "Actualiser", "@sage/xtrem-intacct-gateway/page-extensions__item_extension__glGroupName____title": "Nom de groupe GL", "@sage/xtrem-intacct-gateway/page-extensions__journal_entry_extension__blockIntacct____title": "Intacct", "@sage/xtrem-intacct-gateway/page-extensions__journal_entry_extension__intacctId____title": "ID Intacct", "@sage/xtrem-intacct-gateway/page-extensions__journal_entry_extension__intacctIntegrationState____title": "Statut Intacct", "@sage/xtrem-intacct-gateway/page-extensions__journal_entry_extension__intacctUrl____title": "Journal Intacct", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__discountAmount____title": "Pourcentage", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__discountAmount____title__2": "Pourcentage", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__discountAmount____title__3": "<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__penaltyAmount____title": "Pourcentage", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__penaltyAmount____title__2": "Pourcentage", "@sage/xtrem-intacct-gateway/page-extensions__payment_term_extension__penaltyAmount____title__3": "<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/page-extensions__pre_journal_extension__blockIntacct____title": "Intacct", "@sage/xtrem-intacct-gateway/page-extensions__pre_journal_extension__intacctId____title": "ID Intacct", "@sage/xtrem-intacct-gateway/page-extensions__pre_journal_extension__intacctIntegrationState____title": "Statut Intacct", "@sage/xtrem-intacct-gateway/page-extensions__pre_journal_extension__intacctUrl____title": "Journal Intacct", "@sage/xtrem-intacct-gateway/page-extensions__supplier_extension__accountingCodeList____title": "Liste de codes comptables", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line2__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line4__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__line5__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__title__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____objectTypePlural": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____objectTypeSingular": "", "@sage/xtrem-intacct-gateway/pages__intacct_map____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__additionnalLink____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__additionnalLinkFormated____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__addLineSpecificFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__availableXtremProperties____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__canCreate____title": "peut créer", "@sage/xtrem-intacct-gateway/pages__intacct_map__canDelete____title": "peut supprimer", "@sage/xtrem-intacct-gateway/pages__intacct_map__canUpdate____title": "peut mettre à jour", "@sage/xtrem-intacct-gateway/pages__intacct_map__createAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__intacct_map__createAll____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__createAllIntacct____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__crudBlock____title": "<PERSON><PERSON><PERSON> / <PERSON><PERSON> à jour / Su<PERSON><PERSON>er", "@sage/xtrem-intacct-gateway/pages__intacct_map__dataSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__DATATYPE": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__DESCRIPTION": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__ID": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__ISCUSTOM": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__LABEL": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__READONLY": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__REQUIRED": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__xtremProperty": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____columns__title__xtremPropertyOption": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__fields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__id____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__intacctDataBlock____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__intacctName____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__isActive____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__isSmartEvent____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__collectionName": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__line__id": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__line__intacctName": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__propertyHeader": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____columns__title__propertyLine": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lines____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__lineSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__integrationStatus": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__isLinked": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__url": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__whenCreated": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____columns__title__whenModified": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__2": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__3": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____dropdownActions__title__4": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__linkedData____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__mainBlock____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__mainSection____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__refreshData____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__refreshRelationMapping____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__DATATYPE": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__DESCRIPTION": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__ID": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__ISCUSTOM": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__LABEL": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__READONLY": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__REQUIRED": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__xtremProperty": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____columns__title__xtremPropertyOption": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__relationshipFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__save____title": "Enregistrer", "@sage/xtrem-intacct-gateway/pages__intacct_map__saveAction____title": "Enregistrer", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__name": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__type": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____columns__title__whereValue": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____dropdownActions__title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__specificFields____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__synchronisationWay____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__writeMappingFile____title": "", "@sage/xtrem-intacct-gateway/pages__intacct_map__xtremObject____title": "", "@sage/xtrem-intacct-gateway/pages__map____subtitle": "Mapping Intacct", "@sage/xtrem-intacct-gateway/pages__map____title": "Mapping Intacct", "@sage/xtrem-intacct-gateway/pages__map__addLineSpecificFields____title": "Ajouter un composant", "@sage/xtrem-intacct-gateway/pages__map__availableXtremProperties____title": "Propriétés XTreeM", "@sage/xtrem-intacct-gateway/pages__map__canCreate____title": "peut créer", "@sage/xtrem-intacct-gateway/pages__map__canDelete____title": "peut supprimer", "@sage/xtrem-intacct-gateway/pages__map__canUpdate____title": "peut modifier", "@sage/xtrem-intacct-gateway/pages__map__createAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__createAll____title": "<PERSON>ut créer dans XTreeM", "@sage/xtrem-intacct-gateway/pages__map__createAllIntacct____title": "<PERSON>ut créer dans Intacct", "@sage/xtrem-intacct-gateway/pages__map__crudBlock____title": "<PERSON><PERSON><PERSON> / Modifier / Supprimer", "@sage/xtrem-intacct-gateway/pages__map__dataSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__DATATYPE": "Type données", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__DESCRIPTION": "Description", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__ID": "ID", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__ISCUSTOM": "Personnalisées", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__LABEL": "Intitulé", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__READONLY": "Lecture seule", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__REQUIRED": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__xtremProperty": "Propriété XTreeM", "@sage/xtrem-intacct-gateway/pages__map__fields____columns__title__xtremPropertyOption": "Option propriété", "@sage/xtrem-intacct-gateway/pages__map__fieldsBlock____title": "<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__id____title": "Objet Intacct", "@sage/xtrem-intacct-gateway/pages__map__intacctDataBlock____title": "Intacct", "@sage/xtrem-intacct-gateway/pages__map__intacctName____title": "Transactions Intacct", "@sage/xtrem-intacct-gateway/pages__map__isActive____title": "Actif", "@sage/xtrem-intacct-gateway/pages__map__isSmartEvent____title": "Smart Events", "@sage/xtrem-intacct-gateway/pages__map__lineMappingBlock____title": "Mapping", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__collectionName": "Nom de collection", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__line__id": "Objet", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__line__intacctName": "Nom", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__propertyHeader": "Propri<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__lines____columns__title__propertyLine": "Propriété ligne", "@sage/xtrem-intacct-gateway/pages__map__lineSection____title": "Collections / Lignes", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__isLinked": "<PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__url": "URL", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__whenCreated": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__linkedData____columns__title__whenModified": "Mise à jour", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title": "Créer / Mettre à jour dans Intacct", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title__2": "<PERSON><PERSON><PERSON> dans XTreeM", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title__3": "Mettre à jour dans XTreeM", "@sage/xtrem-intacct-gateway/pages__map__linkedData____dropdownActions__title__4": "Supprimer dans XTreeM", "@sage/xtrem-intacct-gateway/pages__map__mainBlock____title": "Configuration", "@sage/xtrem-intacct-gateway/pages__map__mainSection____title": "Mapping", "@sage/xtrem-intacct-gateway/pages__map__refreshData____title": "Ra<PERSON><PERSON><PERSON><PERSON> les données", "@sage/xtrem-intacct-gateway/pages__map__relationShipBlock____title": "Relation", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__DATATYPE": "Type données", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__DESCRIPTION": "Description", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__ID": "ID", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__ISCUSTOM": "Personnalisées", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__LABEL": "Intitulé", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__READONLY": "Lecture seule", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__REQUIRED": "<PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__xtremProperty": "Propriété XTreeM", "@sage/xtrem-intacct-gateway/pages__map__relationshipFields____columns__title__xtremPropertyOption": "Option propriété", "@sage/xtrem-intacct-gateway/pages__map__saveAction____title": "Enregistrer", "@sage/xtrem-intacct-gateway/pages__map__specificFields____columns__title__name": "Nom propriété Intacct", "@sage/xtrem-intacct-gateway/pages__map__specificFields____columns__title__type": "Type", "@sage/xtrem-intacct-gateway/pages__map__specificFields____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-intacct-gateway/pages__map__synchronisationWay____title": "Sens de synchronisation", "@sage/xtrem-intacct-gateway/pages__map__xtremObject____title": "Node XTreeM", "@sage/xtrem-intacct-gateway/pages__paymentTerm__title": "Condition de paiement"}