import type { Context } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremIntacctFinance from '@sage/xtrem-intacct-finance';
import * as xtremSales from '@sage/xtrem-sales';

Object.assign(xtremSales.classes.SalesDocumentHooks, {
    async beforePrint(
        writableContext: Context,
        writableSalesDocument: xtremSales.nodes.SalesInvoice | xtremSales.nodes.SalesCreditMemo,
    ) {
        if (await writableSalesDocument.isPrinted) return true;

        const arInvoiceSysId = (
            await xtremFinanceData.nodes.FinanceTransaction.getPostingStatusData(
                writableContext,
                await writableSalesDocument.number,
            )
        ).find(financeTransaction => {
            return (
                financeTransaction.financeIntegrationApp === 'intacct' &&
                financeTransaction.documentType === 'accountsReceivableInvoice' &&
                +(financeTransaction.financeIntegrationAppRecordId || 0) > 0
            );
        })?.documentSysId;

        if (arInvoiceSysId) {
            await xtremIntacctFinance.functions.finance.updateOpenItemFromIntacct(
                writableContext,
                'ARINVOICE',
                await writableContext.read(
                    xtremFinance.nodes.AccountsReceivableInvoice,
                    { _id: arInvoiceSysId },
                    { forUpdate: true },
                ),
            );
        }

        return true;
    },
});
