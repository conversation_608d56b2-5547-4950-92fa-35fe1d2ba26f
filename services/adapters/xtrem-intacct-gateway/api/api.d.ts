declare module '@sage/xtrem-intacct-gateway-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { Package as SageXtremDistribution$Package } from '@sage/xtrem-distribution-api';
    import type { Package as SageXtremFinance$Package } from '@sage/xtrem-finance-api';
    import type {
        AnalyticalData,
        FinanceTransaction,
        Package as SageXtremFinanceData$Package,
    } from '@sage/xtrem-finance-data-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type { Package as SageXtremIntacct$Package } from '@sage/xtrem-intacct-api';
    import type { Package as SageXtremIntacctFinance$Package } from '@sage/xtrem-intacct-finance-api';
    import type { Package as SageXtremLandedCost$Package } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type { Package as SageXtremManufacturing$Package } from '@sage/xtrem-manufacturing-api';
    import type {
        Address,
        BaseDocumentLine,
        BusinessEntityAddress,
        Contact,
        Currency,
        Customer,
        CustomerPriceReason,
        DeliveryMode,
        Incoterm,
        Item,
        ItemSite,
        Package as SageXtremMasterData$Package,
        PaymentTerm,
        UnitOfMeasure,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremPurchasing$Package } from '@sage/xtrem-purchasing-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type {
        Package as SageXtremSales$Package,
        ProformaInvoice,
        SalesOrder,
        SalesOrderLine,
        SalesOrderLineBinding,
        SalesOrderLineDiscountCharge,
        SalesOrderLineDiscountChargeBinding,
        SalesOrderLineDiscountChargeInput,
        SalesOrderLineInput,
        SalesOrderLineTax,
        SalesOrderLineTaxBinding,
        SalesOrderLineTaxInput,
        SalesOrderLineToSalesShipmentLine,
        SalesOrderTax,
        SalesOrderTaxBinding,
        SalesOrderTaxInput,
        SalesShipmentLine,
        SalesShipmentLineBinding,
        SalesShipmentLineInput,
        WorkInProgressSalesOrderLine,
        WorkInProgressSalesOrderLineBinding,
        WorkInProgressSalesOrderLineInput,
    } from '@sage/xtrem-sales-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type {
        OrderAssignment,
        Package as SageXtremStockData$Package,
        StockAllocation,
        StockIssueDetail,
        StockIssueDetailBinding,
        StockIssueDetailInput,
    } from '@sage/xtrem-stock-data-api';
    import type { Package as SageXtremStructure$Package } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type { Package as SageXtremSystem$Package, Site, User } from '@sage/xtrem-system-api';
    import type { Package as SageXtremTax$Package, TaxZone } from '@sage/xtrem-tax-api';
    import type { Package as SageXtremTechnicalData$Package } from '@sage/xtrem-technical-data-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type { ClientCollection, TextStream, decimal, integer } from '@sage/xtrem-client';
    export interface SalesOrderExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        customerNumber: string;
        isQuote: boolean;
        invoiceStatus: SalesDocumentInvoiceStatus;
        orderDate: string;
        taxEngine: TaxEngine;
        soldToCustomer: Customer;
        soldToLinkedAddress: BusinessEntityAddress;
        soldToAddress: Address;
        soldToContact: Contact;
        fxRateDate: string;
        requestedDeliveryDate: string;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        shippingStatus: SalesDocumentShippingStatus;
        salesSite: Site;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        incoterm: Incoterm;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        isOrderAssignmentLinked: boolean;
        taxCalculationStatus: TaxCalculationStatus;
        recordUrl: string;
        proformaInvoices: ClientCollection<ProformaInvoice>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        intacctId: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        paymentTerm: PaymentTerm;
        isOnHold: boolean;
        workDays: integer;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        taxes: ClientCollection<SalesOrderTax>;
        lines: ClientCollection<SalesOrderLine>;
        rateDescription: string;
        shippingDate: string;
        expectedDeliveryDate: string;
        totalAmountExcludingTax: string;
        remainingTotalAmountToShipExcludingTax: string;
        remainingTotalAmountExcludingTaxInCompanyCurrency: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        allocationStatus: StockAllocationStatus;
        allocationRequestStatus: AllocationRequestStatus;
        totalGrossProfit: string;
        isCloseHidden: boolean;
    }
    export interface SalesOrderInputExtension {
        number?: string;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        customerNumber?: string;
        isQuote?: boolean | string;
        invoiceStatus?: SalesDocumentInvoiceStatus;
        soldToCustomer?: integer | string;
        soldToLinkedAddress?: integer | string;
        soldToAddress?: integer | string;
        soldToContact?: integer | string;
        fxRateDate?: string;
        requestedDeliveryDate?: string;
        doNotShipBeforeDate?: string;
        doNotShipAfterDate?: string;
        shippingStatus?: SalesDocumentShippingStatus;
        shipToCustomer?: integer | string;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        billToCustomer?: integer | string;
        billToLinkedAddress?: integer | string;
        billToAddress?: integer | string;
        billToContact?: integer | string;
        incoterm?: integer | string;
        deliveryMode?: integer | string;
        deliveryLeadTime?: integer | string;
        taxCalculationStatus?: TaxCalculationStatus;
        intacctId?: string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        stockSite?: integer | string;
        currency?: integer | string;
        internalNote?: TextStream;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        paymentTerm?: integer | string;
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        taxes?: Partial<SalesOrderTaxInput>[];
        lines?: Partial<SalesOrderLineInput>[];
        shippingDate?: string;
        expectedDeliveryDate?: string;
    }
    export interface SalesOrderBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        customerNumber: string;
        isQuote: boolean;
        invoiceStatus: SalesDocumentInvoiceStatus;
        orderDate: string;
        taxEngine: TaxEngine;
        soldToCustomer: Customer;
        soldToLinkedAddress: BusinessEntityAddress;
        soldToAddress: Address;
        soldToContact: Contact;
        fxRateDate: string;
        requestedDeliveryDate: string;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        shippingStatus: SalesDocumentShippingStatus;
        salesSite: Site;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        incoterm: Incoterm;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        isOrderAssignmentLinked: boolean;
        taxCalculationStatus: TaxCalculationStatus;
        recordUrl: string;
        proformaInvoices: ClientCollection<ProformaInvoice>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        intacctId: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        paymentTerm: PaymentTerm;
        isOnHold: boolean;
        workDays: integer;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        taxes: ClientCollection<SalesOrderTaxBinding>;
        lines: ClientCollection<SalesOrderLineBinding>;
        rateDescription: string;
        shippingDate: string;
        expectedDeliveryDate: string;
        totalAmountExcludingTax: string;
        remainingTotalAmountToShipExcludingTax: string;
        remainingTotalAmountExcludingTaxInCompanyCurrency: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        allocationStatus: StockAllocationStatus;
        allocationRequestStatus: AllocationRequestStatus;
        totalGrossProfit: string;
        isCloseHidden: boolean;
    }
    export interface SalesOrderLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesOrder;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        invoiceStatus: SalesDocumentInvoiceStatus;
        shippingStatus: SalesDocumentShippingStatus;
        salesSite: Site;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        deliveryMode: DeliveryMode;
        requestedDeliveryDate: string;
        deliveryLeadTime: integer;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        shippingDate: string;
        expectedDeliveryDate: string;
        taxZone: TaxZone;
        discountCharges: ClientCollection<SalesOrderLineDiscountCharge>;
        grossPrice: string;
        discountDeterminated: string;
        chargeDeterminated: string;
        grossPriceDeterminated: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        availableQuantityInStockUnit: string;
        stockOnHand: string;
        salesShipmentLines: ClientCollection<SalesOrderLineToSalesShipmentLine>;
        quantityToShipInProgressInSalesUnit: string;
        shippedQuantityInSalesUnit: string;
        remainingQuantityToShipInSalesUnit: string;
        quantityToInvoiceInProgressInSalesUnit: string;
        invoicedQuantityInSalesUnit: string;
        remainingQuantityToInvoiceInSalesUnit: string;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        taxes: ClientCollection<SalesOrderLineTax>;
        uiTaxes: string;
        taxCalculationStatus: TaxCalculationStatus;
        workInProgress: WorkInProgressSalesOrderLine;
        stockAllocations: ClientCollection<StockAllocation>;
        allocationRequestStatus: AllocationRequestStatus;
        stockDetails: ClientCollection<StockIssueDetail>;
        assignments: ClientCollection<OrderAssignment>;
        uWorkOrderLine: BaseDocumentLine;
        uPurchaseOrderLine: BaseDocumentLine;
        assignedQuantity: string;
        uAssignmentOrder: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        intacctId: string;
        unitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        netPrice: string;
        netPriceExcludingTax: string;
        netPriceIncludingTax: string;
        taxAmountAdjusted: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        remainingAmountToShipExcludingTax: string;
        remainingAmountToShipExcludingTaxInCompanyCurrency: string;
        availableQuantityInSalesUnit: string;
        suppliedQuantity: string;
        quantityAllocated: string;
        remainingQuantityToShipInStockUnit: string;
        remainingQuantityToAllocate: string;
        quantityInStockUnit: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockShortageInStockUnit: string;
        stockShortageInSalesUnit: string;
        stockShortageStatus: boolean;
        allocationStatus: StockAllocationStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        stockSiteAddress: Address;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
    }
    export interface SalesOrderLineInputExtension {
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        invoiceStatus?: SalesDocumentInvoiceStatus;
        shippingStatus?: SalesDocumentShippingStatus;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        deliveryMode?: integer | string;
        requestedDeliveryDate?: string;
        deliveryLeadTime?: integer | string;
        doNotShipBeforeDate?: string;
        doNotShipAfterDate?: string;
        shippingDate?: string;
        expectedDeliveryDate?: string;
        discountCharges?: Partial<SalesOrderLineDiscountChargeInput>[];
        grossPrice?: decimal | string;
        discountDeterminated?: decimal | string;
        chargeDeterminated?: decimal | string;
        grossPriceDeterminated?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        priceOrigin?: SalesPriceOrigin;
        priceReason?: integer | string;
        priceOriginDeterminated?: SalesPriceOrigin;
        priceReasonDeterminated?: integer | string;
        isPriceDeterminated?: boolean | string;
        taxes?: Partial<SalesOrderLineTaxInput>[];
        uiTaxes?: string;
        workInProgress?: WorkInProgressSalesOrderLineInput;
        allocationRequestStatus?: AllocationRequestStatus;
        stockDetails?: Partial<StockIssueDetailInput>[];
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        intacctId?: string;
        unitToStockUnitConversionFactor?: decimal | string;
        discount?: decimal | string;
        charge?: decimal | string;
        netPrice?: decimal | string;
        netPriceExcludingTax?: decimal | string;
        netPriceIncludingTax?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        quantityInStockUnit?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockSiteAddress?: integer | string;
    }
    export interface SalesOrderLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesOrder;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        invoiceStatus: SalesDocumentInvoiceStatus;
        shippingStatus: SalesDocumentShippingStatus;
        salesSite: Site;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        deliveryMode: DeliveryMode;
        requestedDeliveryDate: string;
        deliveryLeadTime: integer;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        shippingDate: string;
        expectedDeliveryDate: string;
        taxZone: TaxZone;
        discountCharges: ClientCollection<SalesOrderLineDiscountChargeBinding>;
        grossPrice: string;
        discountDeterminated: string;
        chargeDeterminated: string;
        grossPriceDeterminated: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        availableQuantityInStockUnit: string;
        stockOnHand: string;
        salesShipmentLines: ClientCollection<SalesOrderLineToSalesShipmentLine>;
        quantityToShipInProgressInSalesUnit: string;
        shippedQuantityInSalesUnit: string;
        remainingQuantityToShipInSalesUnit: string;
        quantityToInvoiceInProgressInSalesUnit: string;
        invoicedQuantityInSalesUnit: string;
        remainingQuantityToInvoiceInSalesUnit: string;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        taxes: ClientCollection<SalesOrderLineTaxBinding>;
        uiTaxes: any;
        taxCalculationStatus: TaxCalculationStatus;
        workInProgress: WorkInProgressSalesOrderLineBinding;
        stockAllocations: ClientCollection<StockAllocation>;
        allocationRequestStatus: AllocationRequestStatus;
        stockDetails: ClientCollection<StockIssueDetailBinding>;
        assignments: ClientCollection<OrderAssignment>;
        uWorkOrderLine: BaseDocumentLine;
        uPurchaseOrderLine: BaseDocumentLine;
        assignedQuantity: string;
        uAssignmentOrder: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        intacctId: string;
        unitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        netPrice: string;
        netPriceExcludingTax: string;
        netPriceIncludingTax: string;
        taxAmountAdjusted: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        remainingAmountToShipExcludingTax: string;
        remainingAmountToShipExcludingTaxInCompanyCurrency: string;
        availableQuantityInSalesUnit: string;
        suppliedQuantity: string;
        quantityAllocated: string;
        remainingQuantityToShipInStockUnit: string;
        remainingQuantityToAllocate: string;
        quantityInStockUnit: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockShortageInStockUnit: string;
        stockShortageInSalesUnit: string;
        stockShortageStatus: boolean;
        allocationStatus: StockAllocationStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        stockSiteAddress: Address;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
    }
    export interface SalesShipmentExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        reference: string;
        invoiceStatus: SalesDocumentInvoiceStatus;
        returnRequestStatus: SalesDocumentReturnStatus;
        returnReceiptStatus: SalesDocumentReceiptStatus;
        salesSite: Site;
        recordUrl: string;
        effectiveDate: string;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        trackingNumber: string;
        incoterm: Incoterm;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        workDays: integer;
        paymentTerm: PaymentTerm;
        isOnHold: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        intacctId: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        shippingDate: string;
        deliveryDate: string;
        fxRateDate: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        lines: ClientCollection<SalesShipmentLine>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        rateDescription: string;
        allocationStatus: StockAllocationStatus;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalGrossProfit: string;
        displayStatus: BaseDisplayStatus;
    }
    export interface SalesShipmentInputExtension {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        reference?: string;
        invoiceStatus?: SalesDocumentInvoiceStatus;
        returnRequestStatus?: SalesDocumentReturnStatus;
        returnReceiptStatus?: SalesDocumentReceiptStatus;
        shipToCustomer?: integer | string;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        billToCustomer?: integer | string;
        billToLinkedAddress?: integer | string;
        billToAddress?: integer | string;
        billToContact?: integer | string;
        trackingNumber?: string;
        incoterm?: integer | string;
        deliveryMode?: integer | string;
        deliveryLeadTime?: integer | string;
        paymentTerm?: integer | string;
        intacctId?: string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        date?: string;
        stockSite?: integer | string;
        currency?: integer | string;
        deliveryDate?: string;
        fxRateDate?: string;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        lines?: Partial<SalesShipmentLineInput>[];
        displayStatus?: BaseDisplayStatus;
    }
    export interface SalesShipmentBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        reference: string;
        invoiceStatus: SalesDocumentInvoiceStatus;
        returnRequestStatus: SalesDocumentReturnStatus;
        returnReceiptStatus: SalesDocumentReceiptStatus;
        salesSite: Site;
        recordUrl: string;
        effectiveDate: string;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        trackingNumber: string;
        incoterm: Incoterm;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        workDays: integer;
        paymentTerm: PaymentTerm;
        isOnHold: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        intacctId: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        shippingDate: string;
        deliveryDate: string;
        fxRateDate: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        lines: ClientCollection<SalesShipmentLineBinding>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        rateDescription: string;
        allocationStatus: StockAllocationStatus;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalGrossProfit: string;
        displayStatus: BaseDisplayStatus;
    }
    export interface Package {}
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremDistribution$Package,
            SageXtremFinance$Package,
            SageXtremFinanceData$Package,
            SageXtremImportExport$Package,
            SageXtremIntacct$Package,
            SageXtremIntacctFinance$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremManufacturing$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremPurchasing$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremSales$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremTechnicalData$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-intacct-gateway-api' {
    export type * from '@sage/xtrem-intacct-gateway-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-distribution-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-intacct-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-intacct-finance-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-manufacturing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-purchasing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-sales-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-technical-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-intacct-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-sales-api-partial' {
    import type {
        SalesOrderBindingExtension,
        SalesOrderExtension,
        SalesOrderInputExtension,
        SalesOrderLineBindingExtension,
        SalesOrderLineExtension,
        SalesOrderLineInputExtension,
        SalesShipmentBindingExtension,
        SalesShipmentExtension,
        SalesShipmentInputExtension,
    } from '@sage/xtrem-intacct-gateway-api';
    export interface SalesOrder extends SalesOrderExtension {}
    export interface SalesOrderBinding extends SalesOrderBindingExtension {}
    export interface SalesOrderInput extends SalesOrderInputExtension {}
    export interface SalesOrderLine extends SalesOrderLineExtension {}
    export interface SalesOrderLineBinding extends SalesOrderLineBindingExtension {}
    export interface SalesOrderLineInput extends SalesOrderLineInputExtension {}
    export interface SalesShipment extends SalesShipmentExtension {}
    export interface SalesShipmentBinding extends SalesShipmentBindingExtension {}
    export interface SalesShipmentInput extends SalesShipmentInputExtension {}
}
