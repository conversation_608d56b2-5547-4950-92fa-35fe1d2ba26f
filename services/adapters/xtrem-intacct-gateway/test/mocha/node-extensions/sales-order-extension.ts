// tslint:disable:no-duplicate-string
import { Test } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';
import { assert } from 'chai';

describe('SalesOrderNodeExtension', () => {
    before(() => {});

    it('Update sales shipment 1', () =>
        Test.withContext(
            async context => {
                const salesShipment = await context.read(
                    xtremSales.nodes.SalesShipment,
                    { _id: '#SSH1' },
                    { forUpdate: true },
                );

                await salesShipment.$.set({
                    lines: [
                        {
                            item: '#Chemical D',
                            quantity: 30,
                            unitToStockUnitConversionFactor: 1.1,
                            salesOrderLines: [
                                {
                                    linkedDocument: '#SO5|60400',
                                    quantity: 30,
                                },
                            ],
                            _sortValue: 50,
                            _action: 'create',
                        },
                        {
                            _sortValue: 130000,
                            quantity: 20,
                            _action: 'update',
                        },
                    ],
                });

                await salesShipment.$.save();
                const salesOrderLine = await context.read(xtremSales.nodes.SalesOrderLine, { _id: '#SO5|60400' });

                assert.equal(await salesOrderLine.status, 'closed');
                assert.equal(await salesOrderLine.shippingStatus, 'shipped');

                assert.equal(await (await salesOrderLine.document).status, 'closed');
                assert.equal(await (await salesOrderLine.document).shippingStatus, 'shipped');

                const createShipmentLine = await context.read(xtremSales.nodes.SalesShipmentLine, {
                    _id: '#SSH1|50',
                });

                const salesOrderLineToSalesShipmentLine = await createShipmentLine.salesOrderLines.at(0);

                if (!salesOrderLineToSalesShipmentLine) {
                    assert.fail('The sales order line to sales shipment line is empty');
                }

                assert.equal(await salesOrderLineToSalesShipmentLine.quantity, 30);
            },
            { today: '2020-11-24' },
        ));

    it('Update sales shipment fail 1', () =>
        Test.withContext(async context => {
            const salesShipment = await context.read(
                xtremSales.nodes.SalesShipment,
                { _id: '#SSH3' },
                { forUpdate: true },
            );

            await salesShipment.$.set({
                lines: [
                    {
                        item: '#Chemical D',
                        quantity: 11,
                        unitToStockUnitConversionFactor: 1.1,
                        _action: 'create',
                    },
                ],
            });
            await assert.isRejected(salesShipment.$.save(), 'The record was not updated.');
            assert.deepEqual(salesShipment.$.context.diagnoses, [
                {
                    message:
                        'Line creation is not allowed. The sales shipment is ready to ship or has already been shipped.',
                    path: ['lines', '-1000000001'],
                    severity: 3,
                },
            ]);
        }));
});
