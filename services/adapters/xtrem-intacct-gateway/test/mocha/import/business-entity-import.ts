import '@sage/xtrem-communication';
import type { AnyRecord, Context } from '@sage/xtrem-core';
import { PubSub, Test } from '@sage/xtrem-core';
import * as xtremImportExport from '@sage/xtrem-import-export';
import * as xtremIntacct from '@sage/xtrem-intacct';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremUpload from '@sage/xtrem-upload';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import { Readable } from 'stream';
import { promisify } from 'util';

// TODO: issues in data and templates
describe('Import functional data', () => {
    async function disableIntacct(context: Context): Promise<void> {
        const intacctConfiguration = await context.read(
            xtremIntacct.nodes.Intacct,
            { id: 'XTREM' },
            { forUpdate: true },
        );
        await intacctConfiguration.$.set({ isActive: false });
        await intacctConfiguration.$.save();
    }
    const csvPath = {
        businessEntityCustomData: './test/fixtures/import/import-template-data-be-custom.csv',
        businessEntityData: './test/fixtures/import/import-template-data-be.csv',
    };
    async function parseErrorValue(value: string): Promise<AnyRecord[]> {
        const stream = new Readable();
        stream.push(value);
        stream.push(null);
        return (await xtremImportExport.functions.parseEntireCsvStream(stream, ';')).rows;
    }

    interface TestCasePayload {
        contextValue: { replyTopic: string };
        result: string;
        reason?: string;
    }

    async function mockScanResultListener(uploadedFileId: number, payload: TestCasePayload): Promise<void> {
        await Test.withCommittedContext(
            async context => {
                const uploadedFile = await context.read(
                    xtremUpload.nodes.UploadedFile,
                    { _id: uploadedFileId },
                    { forUpdate: true },
                );

                // See https://confluence.sage.com/pages/viewpage.action?spaceKey=XTREEM&title=Async+Context+Module
                if (payload.result !== 'success') {
                    let rejectReason = payload.reason;
                    if (!rejectReason) rejectReason = `Upload rejected: ${payload.result}`;
                    await uploadedFile.$.set({ status: 'rejected', rejectReason });
                } else {
                    await uploadedFile.$.set({ status: 'verified' });
                }

                await uploadedFile.$.save();
            },
            { source: 'listener' },
        );
    }

    function runBatchImport(templateId: string, uploadedFileId: number): Promise<xtremImportExport.nodes.ImportResult> {
        return Test.withReadonlyContext(
            context =>
                xtremImportExport.nodes.ImportExportTemplate.batchImport(context, templateId, String(uploadedFileId), {
                    doInsert: true,
                }),
            {
                source: 'customMutation',
            },
        );
    }

    async function sleep(ms: number): Promise<void> {
        await promisify(setTimeout)(ms);
    }

    async function executeImport(
        context: Context,
        templateId: string,
        pathToCsv: string,
        sleepMillis = 4000,
    ): Promise<xtremImportExport.nodes.ImportResult> {
        const uploadResult = await xtremImportExport.uploadTestFile(path.resolve(pathToCsv));

        await mockScanResultListener(uploadResult.uploadedFileId, {
            contextValue: { replyTopic: 'UploadedFile/InfrastructureComplete' },
            result: 'success',
        });

        await runBatchImport(templateId, uploadResult.uploadedFileId);

        // Wait for async processing
        await sleep(sleepMillis);

        return (
            await context
                .query(xtremImportExport.nodes.ImportResult, { filter: { uploadedFile: uploadResult.uploadedFileId } })
                .toArray()
        )[0];
    }

    async function cleanUp(): Promise<void> {
        await Test.withCommittedContext(async context => {
            await context
                .query(xtremMasterData.nodes.BusinessEntity, {
                    filter: {
                        id: {
                            _in: [
                                'BE-ADDRESS-PRIMARY',
                                'BE-ADDRESS-NO-PRIMARY',
                                'BE-CUSTOM-ADDRESS-PRIMARY',
                                'BE-CUSTOM-ADDRESS-NO-PRIMARY',
                                'BE-CUSTOM-ADDRESSES-PRIMARY',
                                'BE-CUSTOM-ADDRESSES-NO-PRIMARY',
                            ],
                        },
                    },
                    forUpdate: true,
                })
                .forEach(entity => entity.$.delete());

            const csvDir = `../../../../${context.tenantId}`;
            if (fs.existsSync(csvDir)) fs.rmSync(path.join(__dirname, csvDir), { recursive: true });

            await PubSub.publish(context, 'clear_test_notification_queues', {
                tenantId: null,
                applicationName: Test.application.name,
            });
        });
    }

    // TODO: The import is failing with serialization issues, we need to refactor the business entity code and once optimized
    // we can re-able this test.
    it.skip('Import Business entity using default template', () =>
        Test.withContext(
            async context => {
                const importResult = await executeImport(context, 'BE', csvPath.businessEntityData);
                const rowsInError = await importResult.rowsInError;
                const result = await parseErrorValue(rowsInError.value);
                assert.deepEqual(result, []);
                // Spot check the data
                const entity = await context.tryRead(xtremMasterData.nodes.BusinessEntity, {
                    id: 'BE-ADDRESS-PRIMARY',
                });
                assert.isNotNull(entity);
                assert.equal(await entity?.addresses.length, 1);
            },
            { source: 'customMutation' },
        ));
    // TODO: The import is failing with serialization issues, we need to refactor the business entity code and once optimized
    // we can re-able this test.
    it.skip('Import Business entity using custom template', () =>
        Test.withContext(
            async context => {
                await disableIntacct(context);
                assert.isFalse(await xtremIntacct.functions.isIntacctActive(context));
                const importResult = await executeImport(context, 'BE Custom', csvPath.businessEntityCustomData, 5000);
                const result = await parseErrorValue((await importResult.rowsInError).value);
                assert.deepEqual(result, []);
                // Spot check the data
                const entity = await context.tryRead(xtremMasterData.nodes.BusinessEntity, {
                    id: 'BE-CUSTOM-ADDRESSES-PRIMARY',
                });
                assert.isNotNull(entity);
                assert.equal(await entity?.addresses.length, 2);
            },
            { source: 'customMutation' },
        ));
    after(() => cleanUp());
});
