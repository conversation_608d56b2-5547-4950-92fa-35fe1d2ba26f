"!id";"isActive";"isNaturalPerson";"*name";"country(id)";"currency(id)";"taxIdNumber";"siret";"image";"website";"isSupplier";"isCustomer";"isSite";"#addresses";"isActive#1";"name";"addressLine1";"addressLine2";"city";"region";"postcode";"country(id)#1";"locationPhoneNumber";"isPrimary";"##contacts";"isActive#2";"*title";"*firstName";"*lastName";"preferredName";"role";"position";"locationPhoneNumber#1";"email";"isPrimary#1";
"string";"boolean";"boolean";"string";"reference";"reference";"string";"string";"binaryStream";"string";"boolean";"boolean";"boolean";"collection";"boolean";"string";"string";"string";"string";"string";"string";"reference";"string";"boolean";"collection";"boolean";"enum(ms,mr,dr,mrs)";"string";"string";"string";"enum(mainContact,commercialContact,financialContact)";"string";"string";"string";"boolean";"IGNORE"
"id";"is active(true/false)";"is natural person(false/true)";"name";"country";"currency";"tax id number";"siret";"image";"website";"is supplier(false/true)";"is customer(false/true)";"is site(false/true)";"addresses";"is active(true/false)";"name";"address line 1";"address line 2";"city";"region";"postcode";"country";"location phone number";"is primary(false/true)";"contacts";"is active(false/true)";"title";"first name";"last name";"preferred name";"role";"position";"location phone number";"email";"is primary(false/true)";"IGNORE"
"BE-CUSTOM-ADDRESS-PRIMARY";"true";"false";"Test custom import BE with address";"US";"USD";"653-23-9898";;;;"false";"false";"false";;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;"1";"true";"MAIN-ADDRESS";"Street 1";"Building 1";"Pasadena";"LA";"LA9999";"US";;"true";;;;;;;;;;;;
"BE-CUSTOM-ADDRESS-NO-PRIMARY";"true";"false";"Test custom import BE with address no primary set";"US";"USD";"653-23-9899";;;;"false";"false";"false";;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;"1";"true";"MAIN-ADDRESS";"Street 1";"Building 1";"Pasadena";"LA";"LA9999";"US";;"false";;;;;;;;;;;;
"BE-CUSTOM-ADDRESSES-PRIMARY";"true";"false";"Test custom import BE with 2 addreses";"US";"USD";"653-23-9897";;;;"false";"false";"false";;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;"1";"true";"MAIN-ADDRESS";"Street 1";"Building 1";"Pasadena";"LA";"LA9999";"US";;"false";;;;;;;;;;;;
;;;;;;;;;;;;;"1";"true";"WAREHOUSE-ADDRESS";"Street 1";"Building 1";"Pasadena";"LA";"LA9999";"US";;"true";;;;;;;;;;;;
"BE-CUSTOM-ADDRESSES-NO-PRIMARY";"true";"false";"Test custom import BE with 2 addreses no primary set";"US";"USD";"653-23-9896";;;;"false";"false";"false";;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;"1";"true";"MAIN-ADDRESS";"Street 1";"Building 1";"Pasadena";"LA";"LA9999";"US";;"false";;;;;;;;;;;;
;;;;;;;;;;;;;"1";"true";"WAREHOUSE-ADDRESS";"Street 1";"Building 1";"Pasadena";"LA";"LA9999";"US";;"false";;;;;;;;;;;;
