@xtrem_intacct_gateway
Feature: smoke-test-static

    #Case with navigation panel full width
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                       | NavigationPanelTitle  | Title                |
            | @sage/xtrem-master-data/Item               | Items                 | Item                 |
            | @sage/xtrem-master-data/PaymentTerm        | Payment terms         | Payment term         |
            | @sage/xtrem-finance-data/DimensionType     | Dimension types       | Dimension type       |
            | @sage/xtrem-sales/SalesOrder               | Sales orders          | Sales order          |
            | @sage/xtrem-master-data/Location           | Locations             | Location             |
            | @sage/xtrem-master-data/LicensePlateNumber | License plate numbers | License plate number |

    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                                   | Title                    |
            | @sage/xtrem-master-data/LicensePlateNumberMassCreation | LPN mass creation        |
            | @sage/xtrem-master-data/Location                       | Locations                |
            | @sage/xtrem-master-data/LicensePlateNumber             | License plate numbers    |
            | @sage/xtrem-master-data/LocationType                   | Location types           |
            | @sage/xtrem-master-data/LocationZone                   | Storage zones            |
            | @sage/xtrem-master-data/LocationSequence               | Location sequence number |
            | @sage/xtrem-master-data/LocationMassCreation           | Location mass creation   |
            | @sage/xtrem-master-data/Container                      | Containers               |

    #Case with navigation panel full width with multi action button
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled multi action button on the navigation panel
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                             | NavigationPanelTitle | Title    |
            | @sage/xtrem-master-data/Customer | Customers            | Customer |
            | @sage/xtrem-master-data/Supplier | Suppliers            | Supplier |

    # Positive test to ensure that the error dialog is definitely exist and open
    Scenario: sts \ xtrem-intacct-gateway \ desktop \ check an error dialog appears when the page does not load
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Wazup"
        Then an error dialog appears on the main page
