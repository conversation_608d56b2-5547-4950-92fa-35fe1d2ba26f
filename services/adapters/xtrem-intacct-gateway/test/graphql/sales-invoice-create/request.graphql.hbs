mutation {
    xtremSales {
        salesInvoice {
            create(data: {{inputParameters}}) {
                number
                status
                invoiceDate
                isPrinted
                site {
                    businessEntity {
                        taxIdNumber
                    }
                }
                salesSiteName
                salesSiteTaxIdNumber
                salesSiteLinkedAddress {
                    name
                }
                salesSiteAddress {
                    name
                }
                salesSiteContact {
                    firstName
                }
                billToCustomer {
                    businessEntity{
                        name
                    }
                }
                billToCustomerName
                billToCustomerTaxIdNumber
                billToLinkedAddress {
                    addressLine2
                }
                billToAddress {
                    region
                }
                billToContact {
                    preferredName
                }
                currency {
                    symbol
                }
                paymentTerm {
                    description
                }
                lines {
                    query {
                        edges {
                            node {
                                amountIncludingTax
                            }
                        }
                    }
                }
                totalAmountExcludingTax
                totalAmountIncludingTax
                dueDate
                discountPaymentAmount
                penaltyPaymentAmount
                discountPaymentBeforeDate
                penaltyPaymentType
                discountPaymentType
            }
        }
    }
}
