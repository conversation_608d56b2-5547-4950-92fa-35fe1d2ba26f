{"@sage/xtrem-avalara-gateway": [{"topic": "AvalaraCompany/asyncExport/start", "queue": "import-export", "sourceFileName": "avalara-company.ts"}, {"topic": "AvalaraConfiguration/asyncExport/start", "queue": "import-export", "sourceFileName": "avalara-configuration.ts"}, {"topic": "avalaraCreateTransaction/request", "queue": "sage-network", "sourceFileName": "avatax-listener.ts"}, {"topic": "AvalaraItemTax/asyncExport/start", "queue": "import-export", "sourceFileName": "avalara-item-tax.ts"}, {"topic": "avalaraListEntityUseCodes/request", "queue": "sage-network", "sourceFileName": "avatax-listener.ts"}, {"topic": "avalaraListEntityUseCodes/response", "queue": "sage-network", "sourceFileName": "avatax-response-listener.ts"}, {"topic": "avalaraListTaxCodes/request", "queue": "sage-network", "sourceFileName": "avatax-listener.ts"}, {"topic": "avalaraListTaxCodes/response", "queue": "sage-network", "sourceFileName": "avatax-response-listener.ts"}, {"topic": "AvalaraOptionManagement/asyncExport/start", "queue": "import-export", "sourceFileName": "avalara-option-management.ts"}, {"topic": "avalaraPing/request", "queue": "sage-network", "sourceFileName": "avatax-listener.ts"}, {"topic": "avalaraPing/response", "queue": "sage-network", "sourceFileName": "avatax-response-listener.ts"}, {"topic": "avalaraQueryCompanies/request", "queue": "sage-network", "sourceFileName": "avatax-listener.ts"}, {"topic": "avalaraQueryCompanies/response", "queue": "sage-network", "sourceFileName": "avatax-response-listener.ts"}, {"topic": "avalaraResolveAddress/request", "queue": "sage-network", "sourceFileName": "avatax-listener.ts"}, {"topic": "avalaraResolveAddress/response", "queue": "sage-network", "sourceFileName": "avatax-response-listener.ts"}, {"topic": "AvalaraResponseListener/asyncExport/start", "queue": "import-export", "sourceFileName": "avatax-response-listener.ts"}, {"topic": "AvataxListener/asyncExport/start", "queue": "import-export", "sourceFileName": "avatax-listener.ts"}, {"topic": "EntityUse/asyncExport/start", "queue": "import-export", "sourceFileName": "entity-use.ts"}, {"topic": "MapCompany/asyncExport/start", "queue": "import-export", "sourceFileName": "map-company.ts"}, {"topic": "SalesCreditMemo/avalaraCreateTransaction/response", "queue": "sage-network", "sourceFileName": "avatax-response-listener.ts"}, {"topic": "SalesInvoice/avalaraCreateTransaction/response", "queue": "sage-network", "sourceFileName": "avatax-response-listener.ts"}]}