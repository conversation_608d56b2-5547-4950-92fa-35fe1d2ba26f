import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremAvalaraGateway from '../../../index';
import {
    getTransactionRequestSalesCreditMemo,
    transactionRequestSalesInvoice,
} from '../../fixtures/transaction-request';
import { transactionResponseSalesInvoice } from '../../fixtures/transaction-response';

let lastBaseDocumentLineId = 0; // store the highest _id of BaseDocumentLine before running the tests

describe('TransactionPayloadManager', () => {
    before(async () => {
        await Test.withContext(async context => {
            const maxId = await context
                .queryAggregate(xtremMasterData.nodes.BaseDocumentLine, {
                    group: {},
                    values: { _id: { max: true } },
                })
                .toArray();

            lastBaseDocumentLineId = maxId[0].values._id.max;
        });
    });

    it('sales invoice should be updated with avalara response data', () =>
        Test.withContext(
            async context => {
                await (await xtremAvalaraGateway.classes.TransactionPayloadManager.create(context))
                    .setResponsePayload(transactionResponseSalesInvoice)
                    .processResponsePayload('SalesInvoice');
                let salesInvoice = await context.tryRead(xtremSales.nodes.SalesInvoice, { number: 'SI7' });
                assert.equal(await salesInvoice?.taxCalculationStatus, 'done');

                await (await xtremAvalaraGateway.classes.TransactionPayloadManager.create(context))
                    .setResponsePayload(transactionResponseSalesInvoice)
                    .processResponsePayload('SalesInvoice');
                salesInvoice = await context.tryRead(xtremSales.nodes.SalesInvoice, { number: 'SI7' });
                assert.equal(await salesInvoice?.taxCalculationStatus, 'done');

                const headerTax = await salesInvoice?.taxes.at(2);
                assert.equal(Number(await headerTax?.taxAmount), 50.85);
                assert.equal(Number(await headerTax?.taxRate), 2.3);
                const lineTax = await (await salesInvoice?.lines.at(1))?.taxes.at(2);
                assert.equal(Number(await lineTax?.taxAmount), 49.56);
                assert.equal(Number(await lineTax?.taxRate), 2.3);
            },
            {
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it('sales credit memo should not be updated with avalara response data -> improper data', () =>
        Test.withContext(
            async context => {
                transactionResponseSalesInvoice.code = 'SCE1210006';
                await (await xtremAvalaraGateway.classes.TransactionPayloadManager.create(context))
                    .setResponsePayload(transactionResponseSalesInvoice)
                    .processResponsePayload('SalesCreditMemo');
                const salesCreditMemo = await context.tryRead(xtremSales.nodes.SalesCreditMemo, {
                    number: 'SCE1210006',
                });
                assert.equal(await salesCreditMemo?.taxCalculationStatus, 'notDone');
            },
            {
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it('The Avalara integration is not activated', () =>
        Test.withContext(
            async context => {
                const xtremAvalaraGatewayConfiguration = await context.tryRead(
                    xtremAvalaraGateway.nodes.AvalaraConfiguration,
                    { id: 'AVALARA' },
                    { forUpdate: true },
                );
                await xtremAvalaraGatewayConfiguration!.$.set({ isActive: false });
                await xtremAvalaraGatewayConfiguration!.$.save();

                const site = await context.tryRead(xtremSystem.nodes.Site, { id: 'US001' }, { forUpdate: true });
                const transactionPayloadManager = await xtremAvalaraGateway.classes.TransactionPayloadManager.create(
                    context,
                    site!,
                );
                assert.equal(transactionPayloadManager.companyCode, undefined);
            },
            {
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it('The financial site associated to the sales site or the company associated to the sales site are not defined in the Avalara setup', () =>
        Test.withContext(
            async context => {
                const site = await context.tryRead(xtremSystem.nodes.Site, { id: 'US005' }, { forUpdate: true });
                const transactionPayloadManager = await xtremAvalaraGateway.classes.TransactionPayloadManager.create(
                    context,
                    site!,
                );
                assert.equal(transactionPayloadManager.companyCode, undefined);
            },
            {
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it('The Avalara company is not activated', () =>
        Test.withContext(
            async context => {
                const site = await context.tryRead(xtremSystem.nodes.Site, { id: 'US002' }, { forUpdate: true });
                const avalaraCompany = await context.tryRead(
                    xtremAvalaraGateway.nodes.AvalaraCompany,
                    { _id: '8' },
                    { forUpdate: true },
                );
                await avalaraCompany!.$.set({ isActive: false });
                await avalaraCompany!.$.save();

                const transactionPayloadManager = await xtremAvalaraGateway.classes.TransactionPayloadManager.create(
                    context,
                    site!,
                );
                assert.equal(transactionPayloadManager.companyCode, undefined);
            },
            {
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it('during saving sales invoice proper avalara payload should be prepared', () =>
        Test.withContext(
            async context => {
                const salesInvoice = await context.tryRead(xtremSales.nodes.SalesInvoice, { number: 'SI7' });
                const transactionPayloadManager = await xtremAvalaraGateway.classes.TransactionPayloadManager.create(
                    context,
                    await salesInvoice!.financialSite,
                );
                if (transactionPayloadManager.companyCode) {
                    await transactionPayloadManager.prepareRequestPayload(salesInvoice!);
                }

                assert.deepEqual((transactionPayloadManager as any).requestPayload, transactionRequestSalesInvoice);
            },
            {
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it('during saving sales credit memo proper avalara payload should be prepared', () =>
        Test.withContext(
            async context => {
                // enforce the 'posted' status of the invoice (after post it is 'inProgress'). Normally the 'posted'
                // status is set asynchronously on the reply of the finance integration
                const salesInvoice = await context.read(
                    xtremSales.nodes.SalesInvoice,
                    { number: 'SI7' },
                    { forUpdate: true },
                );
                await salesInvoice.$.set({ forceUpdateForFinance: true, status: 'posted' });
                await salesInvoice.$.save();
                const createdSalesDocuments = await (
                    await new xtremSales.classes.SalesCreditMemosCreator(context).prepareNodeCreateData([
                        { salesDocumentLine: await salesInvoice!.lines.elementAt(0) },
                        { salesDocumentLine: await salesInvoice!.lines.elementAt(1) },
                    ])
                ).createSalesOutputDocuments();
                const salesCreditMemoSysId = createdSalesDocuments.documentsCreated[0]._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                const salesCreditMemo = await context.read(
                    xtremSales.nodes.SalesCreditMemo,
                    { _id: salesCreditMemoSysId },
                    { forUpdate: true },
                );

                const transactionPayloadManager = await xtremAvalaraGateway.classes.TransactionPayloadManager.create(
                    context,
                    await salesCreditMemo.site,
                );
                let transactionRequest: { model: xtremAvalaraGateway.interfaces.TransactionRequest } | undefined;

                if (transactionPayloadManager.companyCode) {
                    transactionRequest = await transactionPayloadManager.prepareRequestPayload(salesCreditMemo);
                }

                assert.deepEqual(transactionRequest, getTransactionRequestSalesCreditMemo(lastBaseDocumentLineId));
            },
            {
                today: '2021-12-14',
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
});
