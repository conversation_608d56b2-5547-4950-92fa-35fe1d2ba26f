import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremAvalaraGateway from '../../../index';
import { AvataxPlugin } from '../../fixtures/avatax-plugin';
import { listResponse, listResponseFilter } from '../../fixtures/companies-list-response';
import { listEntityUseCodesResponse } from '../../fixtures/list-entity-use-codes';
import { listTaxCodesResponse } from '../../fixtures/list-tax-codes-response';
import {
    transactionRequest,
    transactionRequestInventoryTransferInvoice,
    transactionRequestInventoryTransferOrder,
    transactionRequestPurchaseInvoice,
    transactionRequestPurchaseOrder,
    transactionRequestReturnInvoice,
    transactionRequestReturnOrder,
    transactionRequestSalesOrder,
} from '../../fixtures/transaction-request';
import {
    transactionResponse,
    transactionResponseInventoryTransferInvoice,
    transactionResponseInventoryTransferOrder,
    transactionResponsePurchaseInvoice,
    transactionResponsePurchaseOrder,
    transactionResponseReturnInvoice,
    transactionResponseReturnOrder,
    transactionResponseSalesOrder,
} from '../../fixtures/transaction-response';

describe('Avatax Client', () => {
    it('should configure client account and license key', () =>
        Test.withContext(
            context => {
                const accountId = '12345';
                const licenseKey = '54321';
                const appName = 'myapp';
                const appVersion = '1.0';
                const environment = 'sandbox';
                const machineName = 'mbp';
                const config = {
                    appName,
                    appVersion,
                    environment,
                    machineName,
                };
                const client = new xtremAvalaraGateway.classes.XtremAvatax(context, config).withSecurity({
                    accountId,
                    licenseKey,
                });

                assert.isNotNull(client);
            },
            { testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption] },
        ));

    it('should handle various environment settings', () =>
        Test.withContext(
            context => {
                const testCases = [
                    { environment: 'sandbox', expected: 'https://sandbox-rest.avatax.com' },
                    { environment: 'production', expected: 'https://rest.avatax.com' },
                    { environment: '', expected: 'https://rest.avatax.com' },
                    { environment: 'http://specific-url', expected: 'http://specific-url' },
                    { environment: 'https://specific-https-url', expected: 'https://specific-https-url' },
                ];
                testCases.forEach(({ environment, expected }) => {
                    const client = new xtremAvalaraGateway.classes.XtremAvatax(context, {
                        appName: 'myapp',
                        appVersion: '1.0',
                        machineName: 'test-run',
                        environment,
                    });
                    assert.equal(client.baseUrl, expected);
                });
            },
            { testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption] },
        ));

    it(' Ping function must throw systemError without mock  ', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                // TODO: Understand why isRejected is not available in the xtrem-avalara-gateway package
                // await assert.isRejected(avataxClient!.ping(), ' This need to be mocked ');
            },
            { testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption] },
        ));

    it(' Ping function fail - wrong password ', () =>
        Test.withContext(
            async context => {
                let avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                avataxClient = avataxClient!.withSecurity({ accountId: '**********', licenseKey: 'wrongPassword' });
                const result = await avataxClient!.ping();
                delete (result as any).authenticationType;
                delete (result as any).isMock;
                assert.deepEqual(result, { authenticated: false, version: '21.10.0' });
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'ping-fail')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it(' Ping function - mock  ', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                const pingResult = await avataxClient!.ping();

                assert.isTrue((pingResult as any).isMock); // IsMock is hidden ( this test is to be sure that we pass by the mock )
                assert.isTrue(pingResult.authenticated);
                assert.equal(pingResult.version, '21.10.0');
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'ping')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it(' createTransaction function - mock ', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                const transaction: xtremAvalaraGateway.interfaces.CreateTransactionParameters = {
                    model: transactionRequest,
                };

                const createTransactionResult = await avataxClient!
                    .createTransaction(transaction)
                    .then(actualResponse => {
                        return actualResponse;
                    });
                assert.isTrue((createTransactionResult as any).isMock);

                delete (createTransactionResult as any).isMock;
                assert.equal(JSON.stringify(createTransactionResult), JSON.stringify(transactionResponse));
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'create-transaction')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it(' resolveAddress function - mock ', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                const address: xtremAvalaraGateway.interfaces.ResolveAddressRequest = {
                    line1: '1510 Foster Circle',
                    line2: 'Algonquin',
                    line3: '',
                    city: 'Illinois',
                    region: 'IL',
                    postalCode: '60102',
                    country: 'US',
                    textCase: 'mixed',
                };

                const resolveAddressResult: xtremAvalaraGateway.interfaces.AddressResolutionModel = await avataxClient!
                    .resolveAddress(address)
                    .then(actualResponse => {
                        return actualResponse;
                    });
                assert.isTrue((resolveAddressResult as any).isMock);

                assert.isNotEmpty(resolveAddressResult.validatedAddresses);
                if (resolveAddressResult.validatedAddresses?.length) {
                    assert.isNotEmpty(resolveAddressResult.validatedAddresses[0]);
                    assert.isNotNull(resolveAddressResult.validatedAddresses[0].latitude);
                    assert.equal(resolveAddressResult.validatedAddresses[0].latitude, 42.144481999999996);
                }

                assert.isNotEmpty(resolveAddressResult.coordinates);
                assert.isNotNull(resolveAddressResult?.coordinates?.longitude);
                assert.equal(resolveAddressResult?.coordinates?.longitude, -88.320204);

                assert.isNotEmpty(resolveAddressResult.address?.line1);
                assert.equal(resolveAddressResult.address?.line1, '1510 Foster Circle');
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'resolve-address')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it(' resolveAddress function - error when no parameter is filled ', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                const address: xtremAvalaraGateway.interfaces.ResolveAddressRequest = {
                    line1: '',
                    line2: '',
                    line3: '',
                    city: '',
                    region: '',
                    postalCode: '',
                    country: '',
                    textCase: '',
                };
                const resolveAddressResult: xtremAvalaraGateway.interfaces.ResponseErrorMessage = await avataxClient!
                    .resolveAddress(address)
                    .then(actualResponse => {
                        return actualResponse as xtremAvalaraGateway.interfaces.ResponseErrorMessage;
                    });
                assert.isTrue((resolveAddressResult as any).isMock);
                assert.equal(resolveAddressResult.error.message, 'The address value was incomplete.');
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'resolve-address-error')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it(' queryCompanies function - mock ', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                const request: xtremAvalaraGateway.interfaces.QueryCompaniesRequest = {
                    filter: '',
                    include: '',
                    orderBy: '',
                    skip: '',
                    top: '',
                };

                const queryCompaniesResult = await avataxClient!.queryCompanies(request).then(actualResponse => {
                    return actualResponse;
                });
                assert.isTrue((queryCompaniesResult as any).isMock);
                delete (queryCompaniesResult as any).isMock;
                assert.equal(JSON.stringify(queryCompaniesResult), JSON.stringify(listResponse));
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'query-companies')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it(' queryCompanies function - mock - Company code filter ', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                const request: xtremAvalaraGateway.interfaces.QueryCompaniesRequest = {
                    filter: "companyCode eq 'DEFAULT'",
                    include: '',
                    orderBy: '',
                    skip: '',
                    top: '',
                };
                const queryCompaniesResult = await avataxClient!.queryCompanies(request).then(actualResponse => {
                    return actualResponse;
                });
                assert.isTrue((queryCompaniesResult as any).isMock);
                delete (queryCompaniesResult as any).isMock;
                assert.equal(JSON.stringify(queryCompaniesResult), JSON.stringify(listResponseFilter));
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'query-companies-filtered')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it(' queryCompanies function - mock - include error ', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                const request: xtremAvalaraGateway.interfaces.QueryCompaniesRequest = {
                    filter: '',
                    include: '1',
                    orderBy: '',
                    skip: '',
                    top: '',
                };
                const queryCompaniesResult: xtremAvalaraGateway.interfaces.ResponseErrorMessage = await avataxClient!
                    .queryCompanies(request)
                    .then(actualResponse => {
                        return actualResponse as xtremAvalaraGateway.interfaces.ResponseErrorMessage;
                    });
                delete (queryCompaniesResult as any).isMock;
                assert.equal(queryCompaniesResult.error.message, 'An invalid field was provided in the query.');
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'query-companies-error')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it(' listTaxCodes function - mock ', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                const request: xtremAvalaraGateway.interfaces.QueryRequest = {
                    filter: '',
                    orderBy: '',
                    skip: '',
                    top: '10',
                };
                const listTaxCodesResult: xtremAvalaraGateway.interfaces.ListTaxCodesResponse = await avataxClient!
                    .listTaxCodes(request)
                    .then(actualResponse => {
                        return actualResponse;
                    });
                assert.isTrue((listTaxCodesResult as any).isMock);
                delete (listTaxCodesResult as any).isMock;
                assert.equal(JSON.stringify(listTaxCodesResult), JSON.stringify(listTaxCodesResponse));
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'list-tax-codes')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
    it(' listTaxCodes function - mock - include error ', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                const request: xtremAvalaraGateway.interfaces.QueryRequest = {
                    filter: '1',
                    orderBy: '',
                    skip: '',
                    top: '',
                };
                const listTaxCodesResult: xtremAvalaraGateway.interfaces.ResponseErrorMessage = await avataxClient!
                    .listTaxCodes(request)
                    .then(actualResponse => {
                        return actualResponse as xtremAvalaraGateway.interfaces.ResponseErrorMessage;
                    });
                delete (listTaxCodesResult as any).isMock;
                assert.equal(listTaxCodesResult.error.message, 'An invalid field was provided in the query.');
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'list-tax-codes-error')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
    it(' listEntityUseCodes function - mock ', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                const request: xtremAvalaraGateway.interfaces.QueryRequest = {
                    filter: '',
                    orderBy: '',
                    skip: '',
                    top: '10',
                };
                const listEntityUseCodesResult: xtremAvalaraGateway.interfaces.ListEntityUseCodesResponse =
                    await avataxClient!.listEntityUseCodes(request).then(actualResponse => {
                        return actualResponse;
                    });
                assert.isTrue((listEntityUseCodesResult as any).isMock);
                delete (listEntityUseCodesResult as any).isMock;
                assert.equal(JSON.stringify(listEntityUseCodesResult), JSON.stringify(listEntityUseCodesResponse));
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'list-entity-use-codes')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
    it(' listEntityUseCodes function - mock error', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                const request: xtremAvalaraGateway.interfaces.QueryRequest = {
                    filter: '1',
                    orderBy: '',
                    skip: '',
                    top: '',
                };
                const listEntityUseCodesResult: xtremAvalaraGateway.interfaces.ResponseErrorMessage =
                    await avataxClient!.listEntityUseCodes(request).then(actualResponse => {
                        return actualResponse as xtremAvalaraGateway.interfaces.ResponseErrorMessage;
                    });
                assert.equal(listEntityUseCodesResult.error.message, 'An invalid field was provided in the query.');
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'list-entity-use-codes-error')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it(' createTransaction function - SalesOrder mock ', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                const transaction: xtremAvalaraGateway.interfaces.CreateTransactionParameters = {
                    model: transactionRequestSalesOrder,
                };

                const createTransactionResult = await avataxClient!
                    .createTransaction(transaction)
                    .then(actualResponse => {
                        return actualResponse;
                    });
                assert.isTrue((createTransactionResult as any).isMock);

                delete (createTransactionResult as any).isMock;

                assert.equal(createTransactionResult.code, transactionResponseSalesOrder.code);
                assert.equal(createTransactionResult.type, transactionResponseSalesOrder.type);
                assert.equal(
                    createTransactionResult.lines![0].taxCalculated,
                    transactionResponseSalesOrder.lines![0].taxCalculated,
                );
                assert.equal(createTransactionResult.totalAmount, transactionResponseSalesOrder.totalAmount);
                assert.equal(
                    createTransactionResult.totalTaxCalculated,
                    transactionResponseSalesOrder.totalTaxCalculated,
                );
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'create-transaction-sales-order')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it(' createTransaction function - PurchaseOrder mock ', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                const transaction: xtremAvalaraGateway.interfaces.CreateTransactionParameters = {
                    model: transactionRequestPurchaseOrder,
                };

                const createTransactionResult = await avataxClient!
                    .createTransaction(transaction)
                    .then(actualResponse => {
                        return actualResponse;
                    });
                assert.isTrue((createTransactionResult as any).isMock);

                delete (createTransactionResult as any).isMock;
                assert.equal(createTransactionResult.code, transactionResponsePurchaseOrder.code);
                assert.equal(createTransactionResult.type, transactionResponsePurchaseOrder.type);
                assert.equal(
                    createTransactionResult.lines![0].taxCalculated,
                    transactionResponsePurchaseOrder.lines![0].taxCalculated,
                );
                assert.equal(createTransactionResult.totalAmount, transactionResponsePurchaseOrder.totalAmount);
                assert.equal(
                    createTransactionResult.totalTaxCalculated,
                    transactionResponsePurchaseOrder.totalTaxCalculated,
                );
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'create-transaction-purchase-order')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it(' createTransaction function - PurchaseInvoice mock ', () =>
        Test.withContext(
            async context => {
                const avataxClient = await (
                    await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
                )?.getClient();
                assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

                const transaction: xtremAvalaraGateway.interfaces.CreateTransactionParameters = {
                    model: transactionRequestPurchaseInvoice,
                };

                const createTransactionResult = await avataxClient!
                    .createTransaction(transaction)
                    .then(actualResponse => {
                        return actualResponse;
                    });
                assert.isTrue((createTransactionResult as any).isMock);

                delete (createTransactionResult as any).isMock;
                assert.equal(createTransactionResult.code, transactionResponsePurchaseInvoice.code);
                assert.equal(createTransactionResult.type, transactionResponsePurchaseInvoice.type);
                assert.equal(
                    createTransactionResult.lines![0].taxCalculated,
                    transactionResponsePurchaseInvoice.lines![0].taxCalculated,
                );
                assert.equal(createTransactionResult.totalAmount, transactionResponsePurchaseInvoice.totalAmount);
                assert.equal(
                    createTransactionResult.totalTaxCalculated,
                    transactionResponsePurchaseInvoice.totalTaxCalculated,
                );
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'create-transaction-purchase-invoice')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
});

it(' createTransaction function - ReturnOrder mock ', () =>
    Test.withContext(
        async context => {
            const avataxClient = await (
                await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
            )?.getClient();
            assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

            const transaction: xtremAvalaraGateway.interfaces.CreateTransactionParameters = {
                model: transactionRequestReturnOrder,
            };

            const createTransactionResult = await avataxClient!.createTransaction(transaction).then(actualResponse => {
                return actualResponse;
            });
            assert.isTrue((createTransactionResult as any).isMock);

            delete (createTransactionResult as any).isMock;
            assert.equal(createTransactionResult.code, transactionResponseReturnOrder.code);
            assert.equal(createTransactionResult.type, transactionResponseReturnOrder.type);
            assert.equal(
                createTransactionResult.lines![0].taxCalculated,
                transactionResponseReturnOrder.lines![0].taxCalculated,
            );
            assert.equal(createTransactionResult.totalAmount, transactionResponseReturnOrder.totalAmount);
            assert.equal(createTransactionResult.totalTaxCalculated, transactionResponseReturnOrder.totalTaxCalculated);
        },
        {
            plugins: [new AvataxPlugin(__dirname, 'create-transaction-return-order')],
            testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
        },
    ));

it(' createTransaction function - ReturnInvoice mock ', () =>
    Test.withContext(
        async context => {
            const avataxClient = await (
                await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
            )?.getClient();
            assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

            const transaction: xtremAvalaraGateway.interfaces.CreateTransactionParameters = {
                model: transactionRequestReturnInvoice,
            };

            const createTransactionResult = await avataxClient!.createTransaction(transaction).then(actualResponse => {
                return actualResponse;
            });
            assert.isTrue((createTransactionResult as any).isMock);

            delete (createTransactionResult as any).isMock;
            assert.equal(createTransactionResult.code, transactionResponseReturnInvoice.code);
            assert.equal(createTransactionResult.type, transactionResponseReturnInvoice.type);
            assert.equal(
                createTransactionResult.lines![0].taxCalculated,
                transactionResponseReturnInvoice.lines![0].taxCalculated,
            );
            assert.equal(createTransactionResult.totalAmount, transactionResponseReturnInvoice.totalAmount);
            assert.equal(
                createTransactionResult.totalTaxCalculated,
                transactionResponseReturnInvoice.totalTaxCalculated,
            );
        },
        {
            plugins: [new AvataxPlugin(__dirname, 'create-transaction-return-invoice')],
            testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
        },
    ));

it(' createTransaction function - InventoryTransferOrder mock ', () =>
    Test.withContext(
        async context => {
            const avataxClient = await (
                await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
            )?.getClient();
            assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

            const transaction: xtremAvalaraGateway.interfaces.CreateTransactionParameters = {
                model: transactionRequestInventoryTransferOrder,
            };

            const createTransactionResult = await avataxClient!.createTransaction(transaction).then(actualResponse => {
                return actualResponse;
            });
            assert.isTrue((createTransactionResult as any).isMock);

            delete (createTransactionResult as any).isMock;
            assert.equal(createTransactionResult.code, transactionResponseInventoryTransferOrder.code);
            assert.equal(createTransactionResult.type, transactionResponseInventoryTransferOrder.type);
            assert.equal(
                createTransactionResult.lines![0].taxCalculated,
                transactionResponseInventoryTransferOrder.lines![0].taxCalculated,
            );
            assert.equal(createTransactionResult.totalAmount, transactionResponseInventoryTransferOrder.totalAmount);
            assert.equal(
                createTransactionResult.totalTaxCalculated,
                transactionResponseInventoryTransferOrder.totalTaxCalculated,
            );
        },
        {
            plugins: [new AvataxPlugin(__dirname, 'create-transaction-inventory-transfer-order')],
            testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
        },
    ));

it(' createTransaction function - InventoryTransferInvoice mock ', () =>
    Test.withContext(
        async context => {
            const avataxClient = await (
                await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
            )?.getClient();
            assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

            const transaction: xtremAvalaraGateway.interfaces.CreateTransactionParameters = {
                model: transactionRequestInventoryTransferInvoice,
            };

            const createTransactionResult = await avataxClient!.createTransaction(transaction).then(actualResponse => {
                return actualResponse;
            });
            assert.isTrue((createTransactionResult as any).isMock);

            delete (createTransactionResult as any).isMock;
            assert.equal(createTransactionResult.code, transactionResponseInventoryTransferInvoice.code);
            assert.equal(createTransactionResult.type, transactionResponseInventoryTransferInvoice.type);
            assert.equal(
                createTransactionResult.lines![0].taxCalculated,
                transactionResponseInventoryTransferInvoice.lines![0].taxCalculated,
            );
            assert.equal(createTransactionResult.totalAmount, transactionResponseInventoryTransferInvoice.totalAmount);
            assert.equal(
                createTransactionResult.totalTaxCalculated,
                transactionResponseInventoryTransferInvoice.totalTaxCalculated,
            );
        },
        {
            plugins: [new AvataxPlugin(__dirname, 'create-transaction-inventory-transfer-invoice')],
            testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
        },
    ));

it(' createTransaction function - InventoryTransferInvoice mock ', () =>
    Test.withContext(
        async context => {
            const avataxClient = await (
                await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
            )?.getClient();
            assert.instanceOf(avataxClient, xtremAvalaraGateway.classes.XtremAvatax);

            const transaction: xtremAvalaraGateway.interfaces.CreateTransactionParameters = {
                model: transactionRequestInventoryTransferInvoice,
            };

            const createTransactionResult = await avataxClient!.createTransaction(transaction).then(actualResponse => {
                return actualResponse;
            });
            assert.isTrue((createTransactionResult as any).isMock);

            delete (createTransactionResult as any).isMock;
            assert.equal(createTransactionResult.code, transactionResponseInventoryTransferInvoice.code);
            assert.equal(createTransactionResult.type, transactionResponseInventoryTransferInvoice.type);
            assert.equal(
                createTransactionResult.lines![0].taxCalculated,
                transactionResponseInventoryTransferInvoice.lines![0].taxCalculated,
            );
            assert.equal(createTransactionResult.totalAmount, transactionResponseInventoryTransferInvoice.totalAmount);
            assert.equal(
                createTransactionResult.totalTaxCalculated,
                transactionResponseInventoryTransferInvoice.totalTaxCalculated,
            );
        },
        {
            plugins: [new AvataxPlugin(__dirname, 'create-transaction-inventory-transfer-invoice')],
            testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
        },
    ));
