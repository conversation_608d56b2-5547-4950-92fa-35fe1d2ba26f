[{"request": {"url": "https://sandbox-rest.avatax.com/api/v2/companies", "verb": "get", "payload": null}, "response": {"isMock": true, "@recordsetCount": 2, "value": [{"id": 2751999, "accountId": **********, "companyCode": "DEFAULT", "name": "My Company", "isDefault": true, "isActive": true, "taxpayerIdNumber": "12-3456789", "IsFein": false, "hasProfile": true, "isReportingEntity": true, "defaultCountry": "US", "roundingLevelId": "Line", "warningsEnabled": false, "isTest": false, "inProgress": false, "createdDate": "2021-09-23T14:14:49.707", "createdUserId": 1438960, "modifiedDate": "2021-10-19T08:25:10.447", "modifiedUserId": 1438964}, {"id": 2752001, "accountId": **********, "companyCode": "NEWCOMPANY", "name": "New company", "isDefault": false, "isActive": true, "taxpayerIdNumber": "98-7654321", "IsFein": false, "hasProfile": true, "isReportingEntity": true, "defaultCountry": "US", "roundingLevelId": "Line", "warningsEnabled": false, "isTest": false, "inProgress": false, "createdDate": "2021-09-23T14:23:49.187", "createdUserId": 1438960, "modifiedDate": "2021-10-19T08:25:10.45", "modifiedUserId": 1438964}]}}]