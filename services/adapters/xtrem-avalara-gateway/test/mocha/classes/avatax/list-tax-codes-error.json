[{"request": {"url": "https://sandbox-rest.avatax.com/api/v2/definitions/taxcodes?%24filter=1", "verb": "get", "payload": null}, "response": {"isMock": true, "error": {"code": "InvalidQueryField", "target": "IncorrectData", "details": [{"code": "InvalidQueryField", "number": 17, "message": "An invalid field was provided in the query.", "description": "The field named '1' could not be found.", "faultCode": "Client", "helpLink": "http://developer.avalara.com/avatax/errors/InvalidQueryField", "severity": "Error"}], "message": "An invalid field was provided in the query."}}}]