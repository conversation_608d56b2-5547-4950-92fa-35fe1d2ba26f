[{"request": {"url": "https://sandbox-rest.avatax.com/api/v2/transactions/create", "verb": "post", "payload": {"type": "InventoryTransferInvoice", "companyCode": "DEFAULT", "date": "2021-12-16", "customerCode": "ABC", "purchaseOrderNo": "2021-12-13-001", "addresses": {"singleLocation": {"line1": "2000 Main Street", "city": "Irvine", "region": "CA", "country": "US", "postalCode": "92614"}}, "lines": [{"number": "1", "quantity": 1, "amount": 100, "taxCode": "PS081282", "itemCode": "Y0001", "description": "Yarn"}], "commit": true, "currencyCode": "USD", "description": "Yarn"}}, "response": {"isMock": true, "id": **************, "code": "8855ec3a-7f8c-48c5-8b13-7348ec9324b5", "companyId": 2751999, "date": "2021-12-16", "status": "Committed", "type": "InventoryTransferInvoice", "batchCode": "", "currencyCode": "USD", "exchangeRateCurrencyCode": "USD", "customerUsageType": "", "entityUseCode": "", "customerVendorCode": "ABC", "customerCode": "ABC", "exemptNo": "", "reconciled": false, "locationCode": "", "reportingLocationCode": "", "purchaseOrderNo": "2021-12-13-001", "referenceCode": "", "salespersonCode": "", "taxOverrideType": "None", "taxOverrideAmount": 0, "taxOverrideReason": "", "totalAmount": 100, "totalExempt": 0, "totalDiscount": 0, "totalTax": 7.75, "totalTaxable": 100, "totalTaxCalculated": 7.75, "adjustmentReason": "NotAdjusted", "adjustmentDescription": "", "locked": false, "region": "CA", "country": "US", "version": 1, "softwareVersion": "********", "originAddressId": **************, "destinationAddressId": **************, "exchangeRateEffectiveDate": "2021-12-16", "exchangeRate": 1, "description": "", "email": "", "businessIdentificationNo": "", "modifiedDate": "2022-03-22T17:25:06.970494Z", "modifiedUserId": 1444646, "taxDate": "2021-12-16", "lines": [{"id": **************, "transactionId": **************, "lineNumber": "1", "boundaryOverrideId": 0, "customerUsageType": "", "entityUseCode": "", "description": "Yarn", "destinationAddressId": **************, "originAddressId": **************, "discountAmount": 0, "discountTypeId": 0, "exemptAmount": 0, "exemptCertId": 0, "exemptNo": "", "isItemTaxable": true, "isSSTP": false, "itemCode": "Y0001", "lineAmount": 100, "quantity": 1, "ref1": "", "ref2": "", "reportingDate": "2021-12-16", "revAccount": "", "sourcing": "Mixed", "tax": 7.75, "taxableAmount": 100, "taxCalculated": 7.75, "taxCode": "PS081282", "taxCodeId": 38007, "taxDate": "2021-12-16", "taxEngine": "", "taxOverrideType": "None", "businessIdentificationNo": "", "taxOverrideAmount": 0, "taxOverrideReason": "", "taxIncluded": false, "details": [{"id": **************, "transactionLineId": **************, "transactionId": **************, "addressId": **************, "country": "US", "region": "CA", "countyFIPS": "", "stateFIPS": "", "exemptAmount": 0, "exemptReasonId": 4, "inState": true, "jurisCode": "06", "jurisName": "CALIFORNIA", "jurisdictionId": 5000531, "signatureCode": "AGAM", "stateAssignedNo": "", "jurisType": "STA", "jurisdictionType": "State", "nonTaxableAmount": 0, "nonTaxableRuleId": 0, "nonTaxableType": "RateRule", "rate": 0.06, "rateRuleId": 1526144, "rateSourceId": 3, "serCode": "", "sourcing": "Origin", "tax": 6, "taxableAmount": 100, "taxType": "Use", "taxSubTypeId": "U", "taxTypeGroupId": "SalesAndUse", "taxName": "CA STATE TAX", "taxAuthorityTypeId": 45, "taxRegionId": 4017409, "taxCalculated": 6, "taxOverride": 0, "rateType": "General", "rateTypeCode": "G", "taxableUnits": 100, "nonTaxableUnits": 0, "exemptUnits": 0, "unitOfBasis": "PerCurrencyUnit", "isNonPassThru": false, "isFee": false, "reportingTaxableUnits": 100, "reportingNonTaxableUnits": 0, "reportingExemptUnits": 0, "reportingTax": 6, "reportingTaxCalculated": 6, "liabilityType": "<PERSON><PERSON>"}, {"id": 85000338868863, "transactionLineId": **************, "transactionId": **************, "addressId": **************, "country": "US", "region": "CA", "countyFIPS": "", "stateFIPS": "", "exemptAmount": 0, "exemptReasonId": 4, "inState": true, "jurisCode": "059", "jurisName": "ORANGE", "jurisdictionId": 267, "signatureCode": "AHXU", "stateAssignedNo": "", "jurisType": "CTY", "jurisdictionType": "County", "nonTaxableAmount": 0, "nonTaxableRuleId": 0, "nonTaxableType": "RateRule", "rate": 0.0025, "rateRuleId": 1526140, "rateSourceId": 3, "serCode": "", "sourcing": "Origin", "tax": 0.25, "taxableAmount": 100, "taxType": "Use", "taxSubTypeId": "U", "taxTypeGroupId": "SalesAndUse", "taxName": "CA COUNTY TAX", "taxAuthorityTypeId": 45, "taxRegionId": 4017409, "taxCalculated": 0.25, "taxOverride": 0, "rateType": "General", "rateTypeCode": "G", "taxableUnits": 100, "nonTaxableUnits": 0, "exemptUnits": 0, "unitOfBasis": "PerCurrencyUnit", "isNonPassThru": false, "isFee": false, "reportingTaxableUnits": 100, "reportingNonTaxableUnits": 0, "reportingExemptUnits": 0, "reportingTax": 0.25, "reportingTaxCalculated": 0.25, "liabilityType": "<PERSON><PERSON>"}, {"id": 85000338868864, "transactionLineId": **************, "transactionId": **************, "addressId": **************, "country": "US", "region": "CA", "countyFIPS": "", "stateFIPS": "", "exemptAmount": 0, "exemptReasonId": 4, "inState": true, "jurisCode": "EMAZ0", "jurisName": "ORANGE COUNTY DISTRICT TAX SP", "jurisdictionId": 2001061425, "signatureCode": "EMAZ", "stateAssignedNo": "037", "jurisType": "STJ", "jurisdictionType": "Special", "nonTaxableAmount": 0, "nonTaxableRuleId": 0, "nonTaxableType": "RateRule", "rate": 0.005, "rateRuleId": 1526110, "rateSourceId": 3, "serCode": "", "sourcing": "Destination", "tax": 0.5, "taxableAmount": 100, "taxType": "Use", "taxSubTypeId": "U", "taxTypeGroupId": "SalesAndUse", "taxName": "CA SPECIAL TAX", "taxAuthorityTypeId": 45, "taxRegionId": 4017409, "taxCalculated": 0.5, "taxOverride": 0, "rateType": "General", "rateTypeCode": "G", "taxableUnits": 100, "nonTaxableUnits": 0, "exemptUnits": 0, "unitOfBasis": "PerCurrencyUnit", "isNonPassThru": false, "isFee": false, "reportingTaxableUnits": 100, "reportingNonTaxableUnits": 0, "reportingExemptUnits": 0, "reportingTax": 0.5, "reportingTaxCalculated": 0.5, "liabilityType": "<PERSON><PERSON>"}, {"id": 85000338868865, "transactionLineId": **************, "transactionId": **************, "addressId": **************, "country": "US", "region": "CA", "countyFIPS": "", "stateFIPS": "", "exemptAmount": 0, "exemptReasonId": 4, "inState": true, "jurisCode": "EMTN0", "jurisName": "ORANGE CO LOCAL TAX SL", "jurisdictionId": 2001061784, "signatureCode": "EMTN", "stateAssignedNo": "30", "jurisType": "STJ", "jurisdictionType": "Special", "nonTaxableAmount": 0, "nonTaxableRuleId": 0, "nonTaxableType": "RateRule", "rate": 0.01, "rateRuleId": 1526106, "rateSourceId": 3, "serCode": "", "sourcing": "Origin", "tax": 1, "taxableAmount": 100, "taxType": "Use", "taxSubTypeId": "U", "taxTypeGroupId": "SalesAndUse", "taxName": "CA SPECIAL TAX", "taxAuthorityTypeId": 45, "taxRegionId": 4017409, "taxCalculated": 1, "taxOverride": 0, "rateType": "General", "rateTypeCode": "G", "taxableUnits": 100, "nonTaxableUnits": 0, "exemptUnits": 0, "unitOfBasis": "PerCurrencyUnit", "isNonPassThru": false, "isFee": false, "reportingTaxableUnits": 100, "reportingNonTaxableUnits": 0, "reportingExemptUnits": 0, "reportingTax": 1, "reportingTaxCalculated": 1, "liabilityType": "<PERSON><PERSON>"}], "nonPassthroughDetails": [], "lineLocationTypes": [{"documentLineLocationTypeId": 85000338868855, "documentLineId": **************, "documentAddressId": **************, "locationTypeCode": "ShipFrom"}, {"documentLineLocationTypeId": 85000338868856, "documentLineId": **************, "documentAddressId": **************, "locationTypeCode": "ShipTo"}, {"documentLineLocationTypeId": 85000338868857, "documentLineId": **************, "documentAddressId": **************, "locationTypeCode": "PointOfOrderAcceptance"}, {"documentLineLocationTypeId": 85000338868858, "documentLineId": **************, "documentAddressId": **************, "locationTypeCode": "PointOfOrder<PERSON><PERSON>in"}, {"documentLineLocationTypeId": 85000338868859, "documentLineId": **************, "documentAddressId": **************, "locationTypeCode": "GoodsPlaceOrServiceRendered"}, {"documentLineLocationTypeId": 85000338868860, "documentLineId": **************, "documentAddressId": **************, "locationTypeCode": "Import"}, {"documentLineLocationTypeId": 85000338868861, "documentLineId": **************, "documentAddressId": **************, "locationTypeCode": "Bill<PERSON><PERSON>"}], "hsCode": "", "costInsuranceFreight": 0, "vatCode": "", "vatNumberTypeId": 0}], "addresses": [{"id": **************, "transactionId": **************, "boundaryLevel": "Address", "line1": "2000 Main St", "line2": "", "line3": "", "city": "Irvine", "region": "CA", "postalCode": "92614-7202", "country": "US", "taxRegionId": 4017409, "latitude": "33.684716", "longitude": "-117.851489"}], "locationTypes": [{"documentLocationTypeId": 85000338868846, "documentId": **************, "documentAddressId": **************, "locationTypeCode": "ShipFrom"}, {"documentLocationTypeId": 85000338868847, "documentId": **************, "documentAddressId": **************, "locationTypeCode": "ShipTo"}, {"documentLocationTypeId": 85000338868848, "documentId": **************, "documentAddressId": **************, "locationTypeCode": "PointOfOrderAcceptance"}, {"documentLocationTypeId": 85000338868849, "documentId": **************, "documentAddressId": **************, "locationTypeCode": "PointOfOrder<PERSON><PERSON>in"}, {"documentLocationTypeId": 85000338868850, "documentId": **************, "documentAddressId": **************, "locationTypeCode": "GoodsPlaceOrServiceRendered"}, {"documentLocationTypeId": 85000338868851, "documentId": **************, "documentAddressId": **************, "locationTypeCode": "Import"}, {"documentLocationTypeId": 85000338868852, "documentId": **************, "documentAddressId": **************, "locationTypeCode": "Bill<PERSON><PERSON>"}], "summary": [{"country": "US", "region": "CA", "jurisType": "State", "jurisCode": "06", "jurisName": "CALIFORNIA", "taxAuthorityType": 45, "stateAssignedNo": "", "taxType": "Use", "taxSubType": "U", "taxName": "CA STATE TAX", "rateType": "General", "taxable": 100, "rate": 0.06, "tax": 6, "taxCalculated": 6, "nonTaxable": 0, "exemption": 0}, {"country": "US", "region": "CA", "jurisType": "County", "jurisCode": "059", "jurisName": "ORANGE", "taxAuthorityType": 45, "stateAssignedNo": "", "taxType": "Use", "taxSubType": "U", "taxName": "CA COUNTY TAX", "rateType": "General", "taxable": 100, "rate": 0.0025, "tax": 0.25, "taxCalculated": 0.25, "nonTaxable": 0, "exemption": 0}, {"country": "US", "region": "CA", "jurisType": "Special", "jurisCode": "EMTN0", "jurisName": "ORANGE CO LOCAL TAX SL", "taxAuthorityType": 45, "stateAssignedNo": "30", "taxType": "Use", "taxSubType": "U", "taxName": "CA SPECIAL TAX", "rateType": "General", "taxable": 100, "rate": 0.01, "tax": 1, "taxCalculated": 1, "nonTaxable": 0, "exemption": 0}, {"country": "US", "region": "CA", "jurisType": "Special", "jurisCode": "EMAZ0", "jurisName": "ORANGE COUNTY DISTRICT TAX SP", "taxAuthorityType": 45, "stateAssignedNo": "037", "taxType": "Use", "taxSubType": "U", "taxName": "CA SPECIAL TAX", "rateType": "General", "taxable": 100, "rate": 0.005, "tax": 0.5, "taxCalculated": 0.5, "nonTaxable": 0, "exemption": 0}]}}]