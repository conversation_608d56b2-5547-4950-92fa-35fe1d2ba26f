[{"request": {"url": "https://sandbox-rest.avatax.com/api/v2/addresses/resolve", "verb": "get", "payload": null}, "response": {"isMock": true, "error": {"code": "InvalidAddress", "target": "IncorrectData", "details": [{"code": "InvalidAddress", "number": 309, "message": "The address value was incomplete.", "description": "The address value NULL was incomplete. You must provide either a valid postal code, line1 + city + region, or line1 + postal code.", "faultCode": "Client", "helpLink": "http://developer.avalara.com/avatax/errors/InvalidAddress", "severity": "Error"}], "message": "The address value was incomplete."}}}]