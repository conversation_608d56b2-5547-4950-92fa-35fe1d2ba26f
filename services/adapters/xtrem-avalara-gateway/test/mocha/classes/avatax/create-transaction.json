[{"request": {"url": "https://sandbox-rest.avatax.com/api/v2/transactions/create", "verb": "post", "payload": {"type": "SalesInvoice", "companyCode": "DEFAULT", "date": "2017-04-12", "customerCode": "ABC", "purchaseOrderNo": "2017-04-12-001", "addresses": {"singleLocation": {"line1": "123 Main Street", "city": "Irvine", "region": "CA", "country": "US", "postalCode": "92615"}}, "lines": [{"number": "1", "quantity": 1, "amount": 100, "taxCode": "PS081282", "itemCode": "Y0001", "description": "Yarn"}], "commit": true, "currencyCode": "USD", "description": "Yarn"}}, "response": {"isMock": true, "id": **************, "code": "76db2a2b-0cf4-4bfe-815d-bee2bd9fc1a8", "companyId": 2751999, "date": "2017-04-12", "status": "Committed", "type": "SalesInvoice", "batchCode": "", "currencyCode": "USD", "exchangeRateCurrencyCode": "USD", "customerUsageType": "", "entityUseCode": "", "customerVendorCode": "ABC", "customerCode": "ABC", "exemptNo": "", "reconciled": false, "locationCode": "", "reportingLocationCode": "", "purchaseOrderNo": "2017-04-12-001", "referenceCode": "", "salespersonCode": "", "taxOverrideType": "None", "taxOverrideAmount": 0, "taxOverrideReason": "", "totalAmount": 100, "totalExempt": 100, "totalDiscount": 0, "totalTax": 0, "totalTaxable": 0, "totalTaxCalculated": 0, "adjustmentReason": "NotAdjusted", "adjustmentDescription": "", "locked": false, "region": "CA", "country": "US", "version": 1, "softwareVersion": "*********", "originAddressId": **************, "destinationAddressId": **************, "exchangeRateEffectiveDate": "2017-04-12", "exchangeRate": 1, "description": "", "email": "", "businessIdentificationNo": "", "modifiedDate": "2021-11-05T09:25:56.9221129Z", "modifiedUserId": 1444646, "taxDate": "2017-04-12", "lines": [{"id": **************, "transactionId": **************, "lineNumber": "1", "boundaryOverrideId": 0, "customerUsageType": "", "entityUseCode": "", "description": "Yarn", "destinationAddressId": **************, "originAddressId": **************, "discountAmount": 0, "discountTypeId": 0, "exemptAmount": 100, "exemptCertId": 0, "exemptNo": "", "isItemTaxable": false, "isSSTP": false, "itemCode": "Y0001", "lineAmount": 100, "quantity": 1, "ref1": "", "ref2": "", "reportingDate": "2017-04-12", "revAccount": "", "sourcing": "Origin", "tax": 0, "taxableAmount": 0, "taxCalculated": 0, "taxCode": "PS081282", "taxCodeId": 38007, "taxDate": "2017-04-12", "taxEngine": "", "taxOverrideType": "None", "businessIdentificationNo": "", "taxOverrideAmount": 0, "taxOverrideReason": "", "taxIncluded": false, "details": [{"id": **************, "transactionLineId": **************, "transactionId": **************, "addressId": **************, "country": "US", "region": "CA", "countyFIPS": "", "stateFIPS": "", "exemptAmount": 0, "exemptReasonId": 6, "inState": true, "jurisCode": "06", "jurisName": "CALIFORNIA", "jurisdictionId": 5000531, "signatureCode": "AGAM", "stateAssignedNo": "", "jurisType": "STA", "jurisdictionType": "State", "nonTaxableAmount": 100, "nonTaxableRuleId": 0, "nonTaxableType": "NexusRule", "rate": 0, "rateRuleId": 0, "rateSourceId": 0, "serCode": "", "sourcing": "Origin", "tax": 0, "taxableAmount": 0, "taxType": "Sales", "taxSubTypeId": "S", "taxTypeGroupId": "SalesAndUse", "taxName": "CA STATE TAX", "taxAuthorityTypeId": 45, "taxRegionId": 2127863, "taxCalculated": 0, "taxOverride": 0, "rateType": "General", "rateTypeCode": "G", "taxableUnits": 0, "nonTaxableUnits": 100, "exemptUnits": 0, "unitOfBasis": "PerCurrencyUnit", "isNonPassThru": false, "isFee": false, "reportingTaxableUnits": 0, "reportingNonTaxableUnits": 100, "reportingExemptUnits": 0, "reportingTax": 0, "reportingTaxCalculated": 0, "liabilityType": "<PERSON><PERSON>"}], "nonPassthroughDetails": [], "lineLocationTypes": [{"documentLineLocationTypeId": 79000011551993, "documentLineId": **************, "documentAddressId": **************, "locationTypeCode": "ShipFrom"}, {"documentLineLocationTypeId": 80000011551993, "documentLineId": **************, "documentAddressId": **************, "locationTypeCode": "ShipTo"}, {"documentLineLocationTypeId": 81000011551993, "documentLineId": **************, "documentAddressId": **************, "locationTypeCode": "PointOfOrderAcceptance"}, {"documentLineLocationTypeId": 82000011551993, "documentLineId": **************, "documentAddressId": **************, "locationTypeCode": "PointOfOrder<PERSON><PERSON>in"}, {"documentLineLocationTypeId": 83000011551993, "documentLineId": **************, "documentAddressId": **************, "locationTypeCode": "GoodsPlaceOrServiceRendered"}, {"documentLineLocationTypeId": 84000011551993, "documentLineId": **************, "documentAddressId": **************, "locationTypeCode": "Import"}], "hsCode": "", "costInsuranceFreight": 0, "vatCode": "", "vatNumberTypeId": 0}], "addresses": [{"id": **************, "transactionId": **************, "boundaryLevel": "Zip5", "line1": "123 Main Street", "line2": "", "line3": "", "city": "Irvine", "region": "CA", "postalCode": "92615", "country": "US", "taxRegionId": 2127863, "latitude": "33.657808", "longitude": "-117.968427"}], "locationTypes": [{"documentLocationTypeId": 66000003319233, "documentId": **************, "documentAddressId": **************, "locationTypeCode": "ShipFrom"}, {"documentLocationTypeId": 67000003319233, "documentId": **************, "documentAddressId": **************, "locationTypeCode": "ShipTo"}, {"documentLocationTypeId": 68000003319233, "documentId": **************, "documentAddressId": **************, "locationTypeCode": "PointOfOrderAcceptance"}, {"documentLocationTypeId": 69000003319233, "documentId": **************, "documentAddressId": **************, "locationTypeCode": "PointOfOrder<PERSON><PERSON>in"}, {"documentLocationTypeId": 70000003319233, "documentId": **************, "documentAddressId": **************, "locationTypeCode": "GoodsPlaceOrServiceRendered"}, {"documentLocationTypeId": 71000003319233, "documentId": **************, "documentAddressId": **************, "locationTypeCode": "Import"}], "summary": [{"country": "US", "region": "CA", "jurisType": "State", "jurisCode": "06", "jurisName": "CALIFORNIA", "taxAuthorityType": 45, "stateAssignedNo": "", "taxType": "Sales", "taxSubType": "S", "taxName": "CA STATE TAX", "rateType": "General", "taxable": 0, "rate": 0, "tax": 0, "taxCalculated": 0, "nonTaxable": 100, "exemption": 0}]}}]