import { asyncArray, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremAvalaraGateway from '../../../index';

describe('Avatax Client', () => {
    before(() => {});
    it('should configure client account and license key', () =>
        Test.withContext(
            async context => {
                const avalaraConfiguration = await context.read(xtremAvalaraGateway.nodes.AvalaraConfiguration, {
                    id: 'AVALARA',
                });

                const credentials = await avalaraConfiguration.cred;

                assert.equal(credentials.accountId, '**********');

                const client = await avalaraConfiguration.getClient();
                assert.isNotNull(client);
            },
            {
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it('should handle various environment settings', () =>
        Test.withContext(
            async context => {
                let avalaraConfiguration: xtremAvalaraGateway.nodes.AvalaraConfiguration;

                const testCases = [{ id: 'AVALARA', expected: 'https://sandbox-rest.avatax.com' }];
                await asyncArray(testCases).forEach(async ({ id, expected }) => {
                    avalaraConfiguration = await context.read(xtremAvalaraGateway.nodes.AvalaraConfiguration, { id });
                    const client = await avalaraConfiguration.getClient();
                    assert.equal(client.baseUrl, expected);
                });
            },
            {
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
});
