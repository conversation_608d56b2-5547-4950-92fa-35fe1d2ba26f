[{"request": {"url": "https://sandbox-rest.avatax.com/api/v2/transactions/create", "verb": "post", "payload": {"type": "SalesInvoice", "companyCode": "DEFAULT", "date": "2017-04-12"}}, "response": {"isMock": true, "error": {"code": "ValueRequiredError", "message": "Field lines is required.", "target": "IncorrectData", "details": [{"code": "ValueRequiredError", "number": 5, "message": "Field lines is required.", "description": "Please provide a value for field lines.", "faultCode": "Client", "helpLink": "http://developer.avalara.com/avatax/errors/ValueRequiredError", "severity": "Error"}, {"code": "ValueRequiredError", "number": 5, "message": "Field customerCode is required.", "description": "Please provide a value for field customerCode.", "faultCode": "Client", "helpLink": "http://developer.avalara.com/avatax/errors/ValueRequiredError", "severity": "Error"}]}}}]