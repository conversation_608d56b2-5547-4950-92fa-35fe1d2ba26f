[{"request": {"url": "https://sandbox-rest.avatax.com/api/v2/definitions/entityusecodes?%24top=10", "verb": "get", "payload": null}, "response": {"isMock": true, "@recordsetCount": 18, "value": [{"code": "A", "name": "FEDERAL GOV", "description": "", "validCountries": ["US"]}, {"code": "B", "name": "STATE GOV", "description": "", "validCountries": ["US"]}, {"code": "C", "name": "TRIBAL GOVERNMENT", "description": "", "validCountries": ["*"]}, {"code": "D", "name": "FOREIGN DIPLOMAT", "description": "", "validCountries": ["*"]}, {"code": "E", "name": "CHARITABLE/EXEMPT ORG", "description": "", "validCountries": ["*"]}, {"code": "F", "name": "RELIGIOUS ORG", "description": "", "validCountries": ["*"]}, {"code": "G", "name": "RESALE", "description": "", "validCountries": ["*"]}, {"code": "H", "name": "AGRICULTURE", "description": "", "validCountries": ["*"]}, {"code": "I", "name": "INDUSTRIAL PROD/MANUFACTURERS", "description": "", "validCountries": ["*"]}, {"code": "J", "name": "DIRECT PAY", "description": "", "validCountries": ["*"]}], "@nextLink": "/api/v2/definitions/entityusecodes?%24top=10&%24skip=10"}}]