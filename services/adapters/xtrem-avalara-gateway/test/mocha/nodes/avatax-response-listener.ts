import { Test } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';
import { assert } from 'chai';
import * as xtremAvalaraGateway from '../../../index';

describe('Avatax response listener', () => {
    let payload = {};
    before(() => {
        payload = {
            error: {
                code: 'ValueRequiredError',
                message: 'Field lines is required.',
                target: 'IncorrectData',
                details: [
                    {
                        code: 'ValueRequiredError',
                        number: 5,
                        message: 'Field lines is required.',
                        description: 'Please provide a value for field lines.',
                        faultCode: 'Client',
                        helpLink: 'http://developer.avalara.com/avatax/errors/ValueRequiredError',
                        severity: 'Error',
                    },
                    {
                        code: 'ValueRequiredError',
                        number: 5,
                        message: 'Field customerCode is required.',
                        description: 'Please provide a value for field customerCode.',
                        faultCode: 'Client',
                        helpLink: 'http://developer.avalara.com/avatax/errors/ValueRequiredError',
                        severity: 'Error',
                    },
                ],
            },
        };
    });
    it('Topic "SalesInvoice/avalaraCreateTransaction/response"', () =>
        Test.withContext(
            async context => {
                payload = { ...payload, code: 'SI1' };
                let salesInvoice = await context.tryRead(xtremSales.nodes.SalesInvoice, { number: 'SI1' });
                const transactionPayloadManager = await xtremAvalaraGateway.classes.TransactionPayloadManager.create(
                    context,
                    await salesInvoice!.financialSite,
                );
                await transactionPayloadManager.setResponsePayload(payload).processResponsePayload('SalesInvoice');
                xtremAvalaraGateway.nodes.AvalaraResponseListener.createSalesInvoiceTransactionResponse(
                    context,
                    payload,
                );
                salesInvoice = await context.tryRead(xtremSales.nodes.SalesInvoice, { number: 'SI1' });
                assert.equal(await salesInvoice?.taxCalculationStatus, 'failed');
            },
            {
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it('Topic "SalesCreditMemo/avalaraCreateTransaction/response"', () =>
        Test.withContext(
            async context => {
                payload = { ...payload, code: 'SCE1210002' };
                let salesCreditMemo = await context.tryRead(xtremSales.nodes.SalesCreditMemo, { number: 'SCE1210002' });
                const transactionPayloadManager = await xtremAvalaraGateway.classes.TransactionPayloadManager.create(
                    context,
                    await salesCreditMemo!.site,
                );
                await transactionPayloadManager.setResponsePayload(payload).processResponsePayload('SalesCreditMemo');
                xtremAvalaraGateway.nodes.AvalaraResponseListener.createSalesCreditMemoTransactionResponse(
                    context,
                    payload,
                );
                salesCreditMemo = await context.tryRead(xtremSales.nodes.SalesCreditMemo, {
                    number: 'SCE1210002',
                });
                assert.equal(await salesCreditMemo?.taxCalculationStatus, 'failed');
            },
            {
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
});
