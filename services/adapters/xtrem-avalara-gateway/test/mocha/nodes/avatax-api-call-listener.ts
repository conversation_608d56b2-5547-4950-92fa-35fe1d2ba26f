import { Logger, Test } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';
import { assert } from 'chai';
import * as xtremAvalaraGateway from '../../../index';
import { AvataxPlugin } from '../../fixtures/avatax-plugin';
import { listResponse, listResponseFilter } from '../../fixtures/companies-list-response';
import { listEntityUseCodesResponse } from '../../fixtures/list-entity-use-codes';
import { listTaxCodesResponse } from '../../fixtures/list-tax-codes-response';
import { transactionResponseApiCall } from '../../fixtures/transaction-response';

const logger = new Logger(__filename, 'avatax-api-call-listener-unit-test');

describe('Avatax API call listener', () => {
    before(() => {});
    it('Topic "avalaraPing/request', () =>
        Test.withContext(
            async context => {
                const payload = {};
                const notificationId = '1234';
                const topic = 'avalaraPing/request';
                const replyTopic = 'avalaraPing/response';
                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = notificationId;
                (context as any)._contextValues.topic = topic;
                (context as any)._contextValues.replyTopic = replyTopic;
                logger.info(`Topic ${topic} : notificationId=${notificationId} with reply topic ${replyTopic}`);
                const pingResult = await xtremAvalaraGateway.nodes.AvataxListener.ping(context, payload);

                assert.isNotEmpty(pingResult?.replyId);
                assert.isTrue((pingResult?.responsePayload as any).isMock); // IsMock is hidden ( this test is to be sure that we pass by the mock )
                assert.isTrue(pingResult?.responsePayload.authenticated);
                assert.equal(pingResult?.responsePayload.version, '21.10.0');
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'ping-call-listener')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
    it('Topic "avalaraResolveAddress/request', () =>
        Test.withContext(
            async context => {
                const payload = {
                    line1: '1510 Foster Circle',
                    line2: 'Algonquin',
                    line3: '',
                    city: 'Illinois',
                    region: 'IL',
                    postalCode: '60102',
                    country: 'US',
                    textCase: 'mixed',
                };
                const notificationId = '1234';
                const topic = 'avalaraResolveAddress/request';
                const replyTopic = 'avalaraResolveAddress/response';
                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = notificationId;
                (context as any)._contextValues.topic = topic;
                (context as any)._contextValues.replyTopic = replyTopic;
                logger.info(`Topic ${topic} : notificationId=${notificationId} with reply topic ${replyTopic}`);
                const resolveAddressResult = await xtremAvalaraGateway.nodes.AvataxListener.resolveAddress(
                    context,
                    payload,
                );

                assert.isNotEmpty(resolveAddressResult?.replyId);
                assert.isTrue((resolveAddressResult?.responsePayload as any).isMock);

                assert.isNotEmpty(resolveAddressResult?.responsePayload.validatedAddresses);
                if (resolveAddressResult?.responsePayload.validatedAddresses) {
                    assert.isNotEmpty(resolveAddressResult?.responsePayload.validatedAddresses[0]);
                    assert.isNotNull(resolveAddressResult?.responsePayload.validatedAddresses[0].latitude);
                    assert.equal(
                        resolveAddressResult?.responsePayload.validatedAddresses[0].latitude,
                        42.144481999999996,
                    );
                }

                assert.isNotEmpty(resolveAddressResult?.responsePayload.coordinates);
                assert.isNotNull(resolveAddressResult?.responsePayload.coordinates?.longitude);
                assert.equal(resolveAddressResult?.responsePayload.coordinates?.longitude, -88.320204);

                assert.isNotEmpty(resolveAddressResult?.responsePayload.address?.line1);
                assert.equal(resolveAddressResult?.responsePayload.address?.line1, '1510 Foster Circle');
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'resolve-address-call-listener')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
    it('Topic "resolveAddress/request', () =>
        Test.withContext(
            async context => {
                const payload = {
                    line1: '',
                    line2: '',
                    line3: '',
                    city: '',
                    region: '',
                    postalCode: '',
                    country: '',
                    textCase: '',
                };
                const notificationId = '1234';
                const topic = 'avalaraResolveAddress/request';
                const replyTopic = 'avalaraResolveAddress/response';
                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = notificationId;
                (context as any)._contextValues.topic = topic;
                (context as any)._contextValues.replyTopic = replyTopic;
                logger.info(`Topic ${topic} : notificationId=${notificationId} with reply topic ${replyTopic}`);
                const resolveAddressResult = await xtremAvalaraGateway.nodes.AvataxListener.resolveAddress(
                    context,
                    payload,
                );
                assert.isNotEmpty(resolveAddressResult?.replyId);
                assert.isTrue((resolveAddressResult?.responsePayload as any).isMock);
                assert.equal(resolveAddressResult?.responsePayload.error?.message, 'The address value was incomplete.');
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'resolve-address-error-call-listener')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
    it('Topic "avalaraCreateTransaction/request', () =>
        Test.withContext(
            async context => {
                const notificationId = '1234';
                const topic = 'avalaraCreateTransaction/request';
                const replyTopic = 'SalesInvoice/avalaraCreateTransaction/response';
                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = notificationId;
                (context as any)._contextValues.topic = topic;
                (context as any)._contextValues.replyTopic = replyTopic;
                logger.info(`Topic ${topic} : notificationId=${notificationId} with reply topic ${replyTopic}`);
                const salesInvoiceId = context.read(xtremSales.nodes.SalesInvoice, { number: 'SI1' });
                const createTransactionResult = await xtremAvalaraGateway.nodes.AvataxListener.createTransaction(
                    context,
                    {
                        documents: [(await salesInvoiceId)._id],
                        documentsType: 'SalesInvoice',
                        isCommit: false,
                        isPosting: false,
                    },
                );

                assert.isNotEmpty(createTransactionResult?.replyId);
                assert.isTrue((createTransactionResult?.responsePayload as any).isMock);

                delete (createTransactionResult?.responsePayload as any).isMock;
                assert.equal(
                    JSON.stringify(createTransactionResult?.responsePayload),
                    JSON.stringify(transactionResponseApiCall),
                );
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'create-transaction-call-listener')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it('Topic "avalaraCreateTransaction/request" error', () =>
        Test.withContext(
            async context => {
                const notificationId = '1234';
                const topic = 'avalaraCreateTransaction/request';
                const replyTopic = 'SalesInvoice/avalaraCreateTransaction/response';
                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = notificationId;
                (context as any)._contextValues.topic = topic;
                (context as any)._contextValues.replyTopic = replyTopic;
                logger.info(`Topic ${topic} : notificationId=${notificationId} with reply topic ${replyTopic}`);
                const salesInvoiceId = context.read(xtremSales.nodes.SalesInvoice, { number: 'SI1' });
                const createTransactionResult = await xtremAvalaraGateway.nodes.AvataxListener.createTransaction(
                    context,
                    {
                        documents: [(await salesInvoiceId)._id],
                        documentsType: 'SalesInvoice',
                        isCommit: false,
                        isPosting: false,
                    },
                );

                assert.isNotEmpty(createTransactionResult?.replyId);
                assert.isTrue((createTransactionResult?.responsePayload as any).isMock);

                delete (createTransactionResult?.responsePayload as any).isMock;
                assert.equal(
                    (createTransactionResult?.responsePayload as any).error.message,
                    'Field lines is required.',
                );
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'create-transaction-call-listener-error')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it('Topic "avalaraListTaxCodes/request', () =>
        Test.withContext(
            async context => {
                const payload = {
                    filter: '',
                    orderBy: '',
                    skip: '',
                    top: '10',
                };
                const notificationId = '1234';
                const topic = 'avalaraListTaxCodes/request';
                const replyTopic = 'avalaraListTaxCodes/response';
                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = notificationId;
                (context as any)._contextValues.topic = topic;
                (context as any)._contextValues.replyTopic = replyTopic;
                logger.info(`Topic ${topic} : notificationId=${notificationId} with reply topic ${replyTopic}`);
                const listTaxCodesResult = await xtremAvalaraGateway.nodes.AvataxListener.listTaxCodes(
                    context,
                    payload,
                );
                assert.isNotEmpty(listTaxCodesResult?.replyId);
                assert.isTrue((listTaxCodesResult?.responsePayload as any).isMock);

                delete (listTaxCodesResult?.responsePayload as any).isMock;
                assert.equal(JSON.stringify(listTaxCodesResult?.responsePayload), JSON.stringify(listTaxCodesResponse));
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'list-tax-codes-call-listener')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it('Topic "avalaraListTaxCodes/request - error', () =>
        Test.withContext(
            async context => {
                const payload = {
                    filter: '1',
                    orderBy: '',
                    skip: '',
                    top: '',
                };
                const notificationId = '1234';
                const topic = 'avalaraListTaxCodes/request';
                const replyTopic = 'avalaraListTaxCodes/response';
                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = notificationId;
                (context as any)._contextValues.topic = topic;
                (context as any)._contextValues.replyTopic = replyTopic;
                logger.info(`Topic ${topic} : notificationId=${notificationId} with reply topic ${replyTopic}`);
                const listTaxCodesResult = await xtremAvalaraGateway.nodes.AvataxListener.listTaxCodes(
                    context,
                    payload,
                );
                assert.isNotEmpty(listTaxCodesResult?.replyId);
                assert.isTrue((listTaxCodesResult?.responsePayload as any).isMock);

                assert.equal(
                    listTaxCodesResult?.responsePayload.error?.message,
                    'An invalid field was provided in the query.',
                );
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'list-tax-codes-error-call-listener')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));

    it('Topic "avalaraQueryCompanies/request', () =>
        Test.withContext(
            async context => {
                const payload = {
                    filter: '',
                    include: '',
                    orderBy: '',
                    skip: '',
                    top: '',
                };
                const notificationId = '1234';
                const topic = 'avalaraQueryCompanies/request';
                const replyTopic = 'avalaraQueryCompanies/response';
                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = notificationId;
                (context as any)._contextValues.topic = topic;
                (context as any)._contextValues.replyTopic = replyTopic;
                logger.info(`Topic ${topic} : notificationId=${notificationId} with reply topic ${replyTopic}`);
                const queryCompaniesResult = await xtremAvalaraGateway.nodes.AvataxListener.queryCompanies(
                    context,
                    payload,
                );
                assert.isNotEmpty(queryCompaniesResult?.replyId);
                assert.isTrue((queryCompaniesResult?.responsePayload as any).isMock);

                delete (queryCompaniesResult?.responsePayload as any).isMock;
                assert.equal(JSON.stringify(queryCompaniesResult?.responsePayload), JSON.stringify(listResponse));
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'query-companies-call-listener')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
    it('Topic "avalaraQueryCompanies/request- filter', () =>
        Test.withContext(
            async context => {
                const payload = {
                    filter: "companyCode eq 'DEFAULT'",
                    include: '',
                    orderBy: '',
                    skip: '',
                    top: '',
                };
                const notificationId = '1234';
                const topic = 'avalaraQueryCompanies/request';
                const replyTopic = 'avalaraQueryCompanies/response';
                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = notificationId;
                (context as any)._contextValues.topic = topic;
                (context as any)._contextValues.replyTopic = replyTopic;
                logger.info(`Topic ${topic} : notificationId=${notificationId} with reply topic ${replyTopic}`);
                const queryCompaniesResult = await xtremAvalaraGateway.nodes.AvataxListener.queryCompanies(
                    context,
                    payload,
                );
                assert.isNotEmpty(queryCompaniesResult?.replyId);
                assert.isTrue((queryCompaniesResult?.responsePayload as any).isMock);

                delete (queryCompaniesResult?.responsePayload as any).isMock;
                assert.equal(JSON.stringify(queryCompaniesResult?.responsePayload), JSON.stringify(listResponseFilter));
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'query-companies-filter-call-listener')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
    it('Topic "avalaraQueryCompanies/request- error', () =>
        Test.withContext(
            async context => {
                const payload = {
                    filter: '',
                    include: '1',
                    orderBy: '',
                    skip: '',
                    top: '',
                };
                const notificationId = '1234';
                const topic = 'avalaraQueryCompanies/request';
                const replyTopic = 'avalaraQueryCompanies/response';
                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = notificationId;
                (context as any)._contextValues.topic = topic;
                (context as any)._contextValues.replyTopic = replyTopic;
                logger.info(`Topic ${topic} : notificationId=${notificationId} with reply topic ${replyTopic}`);
                const queryCompaniesResult = await xtremAvalaraGateway.nodes.AvataxListener.queryCompanies(
                    context,
                    payload,
                );
                assert.isNotEmpty(queryCompaniesResult?.replyId);

                assert.equal(
                    queryCompaniesResult?.responsePayload.error?.message,
                    'An invalid field was provided in the query.',
                );
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'query-companies-error-call-listener')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
    it('Topic "avalaraListEntityUseCodes/request', () =>
        Test.withContext(
            async context => {
                const payload = {
                    filter: '',
                    orderBy: '',
                    skip: '',
                    top: '10',
                };
                const notificationId = '1234';
                const topic = 'avalaraListEntityUseCodes/request';
                const replyTopic = 'avalaraListEntityUseCodes/response';
                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = notificationId;
                (context as any)._contextValues.topic = topic;
                (context as any)._contextValues.replyTopic = replyTopic;
                logger.info(`Topic ${topic} : notificationId=${notificationId} with reply topic ${replyTopic}`);
                const listEntityUseCodesResult = await xtremAvalaraGateway.nodes.AvataxListener.listEntityUseCodes(
                    context,
                    payload,
                );
                assert.isNotEmpty(listEntityUseCodesResult?.replyId);

                assert.isTrue((listEntityUseCodesResult?.responsePayload as any).isMock);
                delete (listEntityUseCodesResult?.responsePayload as any).isMock;
                assert.equal(
                    JSON.stringify(listEntityUseCodesResult?.responsePayload),
                    JSON.stringify(listEntityUseCodesResponse),
                );
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'list-entity-use-codes-call-listener')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
    it('Topic "avalaraListEntityUseCodes/request', () =>
        Test.withContext(
            async context => {
                const payload = {
                    filter: '1',
                    orderBy: '',
                    skip: '',
                    top: '',
                };
                const notificationId = '1234';
                const topic = 'avalaraListEntityUseCodes/request';
                const replyTopic = 'avalaraListEntityUseCodes/response';
                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = notificationId;
                (context as any)._contextValues.topic = topic;
                (context as any)._contextValues.replyTopic = replyTopic;
                logger.info(`Topic ${topic} : notificationId=${notificationId} with reply topic ${replyTopic}`);
                const listEntityUseCodesResult = await xtremAvalaraGateway.nodes.AvataxListener.listEntityUseCodes(
                    context,
                    payload,
                );
                assert.isNotEmpty(listEntityUseCodesResult?.replyId);
                assert.equal(
                    listEntityUseCodesResult?.responsePayload.error?.message,
                    'An invalid field was provided in the query.',
                );
            },
            {
                plugins: [new AvataxPlugin(__dirname, 'list-entity-use-codes-error-call-listener')],
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
});
it('AvalaraItemTax loadTaxCodesFromAvalara', () =>
    Test.withContext(
        async context => {
            let isActive: boolean | null = null;
            let fromTaxCode: string | null = '';
            let toTaxCode: string | null = '';
            let description: string | null = '';

            let result = await xtremAvalaraGateway.nodes.AvalaraItemTax.loadTaxCodesFromAvalara(
                context,
                isActive,
                fromTaxCode,
                toTaxCode,
                description,
            );

            assert.equal(result, 'The tax codes are being loaded. The process will run in the background.');

            isActive = false;
            fromTaxCode = 'test';
            toTaxCode = 'test';
            description = 'test';
            result = await xtremAvalaraGateway.nodes.AvalaraItemTax.loadTaxCodesFromAvalara(
                context,
                isActive,
                fromTaxCode,
                toTaxCode,
                description,
            );

            assert.equal(result, 'The tax codes are being loaded. The process will run in the background.');
        },
        {
            plugins: [new AvataxPlugin(__dirname, 'create-transaction-call-listener-error')],
            testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
        },
    ));
it('AvalaraCompany loadCompanyFromAvalara', () =>
    Test.withContext(
        async context => {
            const result = await xtremAvalaraGateway.nodes.AvalaraCompany.loadCompanyFromAvalara(context);

            assert.equal(result, 'The avalara company codes are being loaded. The process will run in the background.');
        },
        {
            plugins: [new AvataxPlugin(__dirname, 'create-transaction-call-listener-error')],
            testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
        },
    ));
it('entityUse loadEntityUseCodesFromAvalara', () =>
    Test.withContext(
        async context => {
            const result = await xtremAvalaraGateway.nodes.EntityUse.loadEntityUseCodesFromAvalara(context);

            assert.equal(result, 'The entity use codes are being loaded. The process will run in the background.');
        },
        {
            plugins: [new AvataxPlugin(__dirname, 'create-transaction-call-listener-error')],
            testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
        },
    ));
