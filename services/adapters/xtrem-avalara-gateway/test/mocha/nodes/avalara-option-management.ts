import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremAvalaraGateway from '../../../lib';

describe('Avalara option management', () => {
    it('Update all companies to genericTaxCalculation when Avalara option management turned off', () =>
        Test.withContext(
            async context => {
                let companyTaxEngineAvalara = context.query(xtremSystem.nodes.Company, {
                    forUpdate: true,
                    filter: {
                        taxEngine: { _eq: 'avalaraAvaTax' },
                    },
                });

                assert.isTrue((await companyTaxEngineAvalara.length) > 0);

                const isServiceOptionActive =
                    await xtremAvalaraGateway.nodes.AvalaraOptionManagement.serviceOptionChange(context);

                if (!isServiceOptionActive) {
                    companyTaxEngineAvalara = context.query(xtremSystem.nodes.Company, {
                        forUpdate: true,
                        filter: {
                            taxEngine: { _eq: 'avalaraAvaTax' },
                        },
                    });

                    assert.equal(await companyTaxEngineAvalara.length, 0);
                }
            },
            {
                testActiveServiceOptions: [xtremAvalaraGateway.serviceOptions.avalaraOption],
            },
        ));
});
