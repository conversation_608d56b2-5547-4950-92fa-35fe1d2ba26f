@xtrem_avalara_gateway
Feature: smoke-test-data

    #Case without navigation panel full width
    Scenario Outline: std \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed
        Examples:
            | Page                                                              | Title                                  |
            | @sage/xtrem-avalara-gateway/AvalaraConfiguration/eyJfaWQiOiIxIn0= | Avalara configuration                  |
            | @sage/xtrem-avalara-gateway/AvalaraCompany/eyJfaWQiOiIxIn0=       | Avalara company Sage AME               |
            # Opening pages having extensions in the current package
            | @sage/xtrem-master-data/Customer/eyJfaWQiOiIyMSJ9                 | Customer Siège social S01 PARIS  |
            | @sage/xtrem-master-data/Item/eyJfaWQiOiI0MyJ9                     | Item A bottle of milk                  |
            | @sage/xtrem-sales/SalesCreditMemo/eyJfaWQiOiIzIn0=                | Sales credit memo SCE1210001           |
            | @sage/xtrem-sales/SalesInvoice/eyJfaWQiOiIxIn0=                   | Sales invoice SI1                      |
            | @sage/xtrem-sales/SalesOrder/eyJfaWQiOiIjU08xIn0=                    | Sales order SO1                        |
            | @sage/xtrem-sales/SalesShipment/eyJfaWQiOiIxIn0=                  | Sales shipment SSH1                    |
