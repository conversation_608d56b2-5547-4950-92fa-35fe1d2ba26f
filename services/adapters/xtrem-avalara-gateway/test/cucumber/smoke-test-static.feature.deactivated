@xtrem_avalara_gateway
Feature: smoke-test-static

    #Case without navigation panel full width
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                              | Title       |
            | @sage/xtrem-sales/SalesCreditMemo | Sales credit memos |

    #Case with navigation panel full width without create business action
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                       | Title             |
            | @sage/xtrem-avalara-gateway/AvalaraCompany | Avalara companies |

    #Case with navigation panel full width
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed
        #Click Create business action
        When the user selects the "Create" labelled business action button on the navigation panel
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                              | NavigationPanelTitle | Title          |
            # Opening pages having extensions in the current package
            | @sage/xtrem-master-data/Customer                  | Customers            | Customer       |
            | @sage/xtrem-master-data/Item                      | Items                | Item           |
            | @sage/xtrem-sales/SalesInvoice                    | Sales invoices       | Sales invoice  |
            | @sage/xtrem-sales/SalesOrder                      | Sales orders         | Sales order    |
            | @sage/xtrem-sales/SalesShipment                   | Sales shipments      | Sales shipment |

    # Positive test to ensure that the error dialog is definitely exist and open
    Scenario: sts \ xtrem-avalara-gateway \ desktop \ check an error dialog appears when the page does not load
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-avalara-gateway/Wazup"
        And the user waits 4 seconds
# And no dialogs are displayed
# # Then an error dialog appears on the main page
