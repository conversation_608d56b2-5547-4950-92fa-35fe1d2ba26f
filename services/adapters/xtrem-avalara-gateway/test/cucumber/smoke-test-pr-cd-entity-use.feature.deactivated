@xtrem_avalara_gateway
Feature:  smoke-test-pr-cd-entity-use

    Scenario: Entity use creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-avalara-gateway/EntityUse"
        Then the "Avalara entity use codes" titled page is displayed
        #Click Create business action
        When the user selects the "Create" labelled business action button on the navigation panel
        #Set isActive to "On"
        And the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "ON"
        #Give it the name "TESTADD"
        And the user selects the "Name *" labelled text field on the main page
        And the user writes "TESTADD" in the text field
        #Give it the ID "TESTADD"
        And the user selects the "ID *" labelled text field on the main page
        And the user writes "TESTADD" in the text field
        #Give it a description
        And the user selects the "Description" labelled text field on the main page
        And the user writes "Description test adding" in the text field
        #Save it
        And the user selects the "Save" labelled business action button on the main page
        And the user waits 2 seconds
        #Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: Entity use deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-avalara-gateway/EntityUse"
        Then the "Avalara entity use codes" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the "id" bound nested field of row 1 in the table field
        #When the user opens the navigation panel
        And searches for "TESTADD" in the navigation panel
        And the user clicks on the "first" navigation panel's row
        #Click Delete Crud Button
        And the user selects the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog 
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
