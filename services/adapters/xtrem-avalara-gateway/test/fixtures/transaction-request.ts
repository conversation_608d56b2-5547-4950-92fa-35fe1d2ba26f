import type { integer } from '@sage/xtrem-core';
import type * as xtremAvalaraGateway from '../../index';

export const transactionRequest: xtremAvalaraGateway.interfaces.TransactionRequest = {
    type: 'SalesInvoice',
    companyCode: 'DEFAULT',
    date: '2017-04-12',
    customerCode: 'ABC',
    purchaseOrderNo: '2017-04-12-001',
    addresses: {
        singleLocation: {
            line1: '123 Main Street',
            city: 'Irvine',
            region: 'CA',
            country: 'US',
            postalCode: '92615',
        },
    },
    lines: [
        {
            number: '1',
            quantity: 1,
            amount: 100,
            taxCode: 'PS081282',
            itemCode: 'Y0001',
            description: 'Yarn',
        },
    ],
    commit: true,
    currencyCode: 'USD',
    description: 'Yarn',
};

export const transactionRequestSalesInvoice: { model: xtremAvalaraGateway.interfaces.TransactionRequest } = {
    model: {
        lines: [
            {
                number: '1507',
                quantity: 24,
                amount: 55.92,
                entityUseCode: undefined,
                taxCode: undefined,
                itemCode: 'Consulting01',
                taxIncluded: false,
                description: 'Consulting service',
                addresses: {
                    shipFrom: {
                        line1: 'First address line site',
                        line2: '',
                        city: '',
                        region: 'MD',
                        country: 'US',
                        postalCode: '20746',
                    },
                    shipTo: {
                        line1: '1st Avenue',
                        line2: '',
                        city: 'One',
                        region: 'AZ',
                        country: 'US',
                        postalCode: '85034',
                    },
                },
            },
            {
                number: '1508',
                quantity: 370,
                amount: 2154.88,
                entityUseCode: undefined,
                taxCode: undefined,
                itemCode: 'SalesItem81',
                taxIncluded: false,
                description: 'Sales Item 81',
                addresses: {
                    shipFrom: {
                        line1: 'First address line site',
                        line2: '',
                        city: '',
                        region: 'MD',
                        country: 'US',
                        postalCode: '20746',
                    },
                    shipTo: {
                        line1: '1st Avenue',
                        line2: '',
                        city: 'One',
                        region: 'AZ',
                        country: 'US',
                        postalCode: '85034',
                    },
                },
            },
        ],
        date: '2021-12-13',
        code: 'SI7',
        customerCode: 'US019',
        commit: false,
        currencyCode: 'USD',
        businessIdentificationNo: '***********',
        companyCode: 'DEFAULT',
        type: 'SalesOrder',
    },
};

export function getTransactionRequestSalesCreditMemo(lastId: integer): {
    model: xtremAvalaraGateway.interfaces.TransactionRequest;
} {
    return {
        model: {
            lines: [
                {
                    number: `${lastId + 1}`,
                    quantity: 24,
                    amount: -55.92,
                    itemCode: 'Consulting01',
                    entityUseCode: undefined,
                    taxCode: undefined,
                    taxIncluded: false,
                    description: 'Consulting service',
                    addresses: {
                        shipFrom: {
                            line1: 'First address line site',
                            line2: '',
                            city: '',
                            region: 'MD',
                            country: 'US',
                            postalCode: '20746',
                        },
                        shipTo: {
                            line1: '1st Avenue',
                            line2: '',
                            city: 'One',
                            region: 'AZ',
                            country: 'US',
                            postalCode: '85034',
                        },
                    },
                    ref1: 'SI7',
                    taxOverride: { type: 'taxDate', taxDate: '2021-12-13', reason: 'Return invoice' },
                },
                {
                    number: `${lastId + 2}`,
                    quantity: 370,
                    amount: -21548.8,
                    itemCode: 'SalesItem81',
                    taxIncluded: false,
                    entityUseCode: undefined,
                    taxCode: undefined,
                    description: 'Sales Item 81',
                    addresses: {
                        shipFrom: {
                            line1: 'First address line site',
                            line2: '',
                            city: '',
                            region: 'MD',
                            country: 'US',
                            postalCode: '20746',
                        },
                        shipTo: {
                            line1: '1st Avenue',
                            line2: '',
                            city: 'One',
                            region: 'AZ',
                            country: 'US',
                            postalCode: '85034',
                        },
                    },
                    ref1: 'SI7',
                    taxOverride: { type: 'taxDate', taxDate: '2021-12-13', reason: 'Return invoice' },
                },
            ],
            date: '2021-12-14',
            code: 'SCE1210008',
            customerCode: 'US019',
            commit: false,
            currencyCode: 'USD',
            businessIdentificationNo: '***********',
            companyCode: 'DEFAULT',
            referenceCode: 'SI7',
            type: 'ReturnOrder',
        },
    };
}

export const transactionRequestSalesOrder: xtremAvalaraGateway.interfaces.TransactionRequest = {
    type: 'SalesOrder',
    companyCode: 'DEFAULT',
    date: '2021-12-16',
    customerCode: 'ABC',
    purchaseOrderNo: '2021-12-13-001',
    addresses: {
        singleLocation: {
            line1: '2000 Main Street',
            city: 'Irvine',
            region: 'CA',
            country: 'US',
            postalCode: '92614',
        },
    },
    lines: [
        {
            number: '1',
            quantity: 1,
            amount: 100,
            taxCode: 'PS081282',
            itemCode: 'Y0001',
            description: 'Yarn',
        },
    ],
    commit: true,
    currencyCode: 'USD',
    description: 'Yarn',
};

export const transactionRequestPurchaseOrder: xtremAvalaraGateway.interfaces.TransactionRequest = {
    type: 'PurchaseOrder',
    companyCode: 'DEFAULT',
    date: '2021-12-16',
    customerCode: 'ABC',
    purchaseOrderNo: '2021-12-13-001',
    addresses: {
        singleLocation: {
            line1: '2000 Main Street',
            city: 'Irvine',
            region: 'CA',
            country: 'US',
            postalCode: '92614',
        },
    },
    lines: [
        {
            number: '1',
            quantity: 1,
            amount: 100,
            taxCode: 'PS081282',
            itemCode: 'Y0001',
            description: 'Yarn',
        },
    ],
    commit: true,
    currencyCode: 'USD',
    description: 'Yarn',
};

export const transactionRequestPurchaseInvoice: xtremAvalaraGateway.interfaces.TransactionRequest = {
    type: 'PurchaseInvoice',
    companyCode: 'DEFAULT',
    date: '2021-12-16',
    customerCode: 'ABC',
    purchaseOrderNo: '2021-12-13-001',
    addresses: {
        singleLocation: {
            line1: '2000 Main Street',
            city: 'Irvine',
            region: 'CA',
            country: 'US',
            postalCode: '92614',
        },
    },
    lines: [
        {
            number: '1',
            quantity: 1,
            amount: 100,
            taxCode: 'PS081282',
            itemCode: 'Y0001',
            description: 'Yarn',
        },
    ],
    commit: true,
    currencyCode: 'USD',
    description: 'Yarn',
};

export const transactionRequestReturnOrder: xtremAvalaraGateway.interfaces.TransactionRequest = {
    type: 'ReturnOrder',
    companyCode: 'DEFAULT',
    date: '2021-12-16',
    customerCode: 'ABC',
    purchaseOrderNo: '2021-12-13-001',
    addresses: {
        singleLocation: {
            line1: '2000 Main Street',
            city: 'Irvine',
            region: 'CA',
            country: 'US',
            postalCode: '92614',
        },
    },
    lines: [
        {
            number: '1',
            quantity: 1,
            amount: 100,
            taxCode: 'PS081282',
            itemCode: 'Y0001',
            description: 'Yarn',
        },
    ],
    commit: true,
    currencyCode: 'USD',
    description: 'Yarn',
};

export const transactionRequestReturnInvoice: xtremAvalaraGateway.interfaces.TransactionRequest = {
    type: 'ReturnInvoice',
    companyCode: 'DEFAULT',
    date: '2021-12-16',
    customerCode: 'ABC',
    purchaseOrderNo: '2021-12-13-001',
    addresses: {
        singleLocation: {
            line1: '2000 Main Street',
            city: 'Irvine',
            region: 'CA',
            country: 'US',
            postalCode: '92614',
        },
    },
    lines: [
        {
            number: '1',
            quantity: 1,
            amount: 100,
            taxCode: 'PS081282',
            itemCode: 'Y0001',
            description: 'Yarn',
        },
    ],
    commit: true,
    currencyCode: 'USD',
    description: 'Yarn',
};

export const transactionRequestInventoryTransferOrder: xtremAvalaraGateway.interfaces.TransactionRequest = {
    type: 'InventoryTransferOrder',
    companyCode: 'DEFAULT',
    date: '2021-12-16',
    customerCode: 'ABC',
    purchaseOrderNo: '2021-12-13-001',
    addresses: {
        singleLocation: {
            line1: '2000 Main Street',
            city: 'Irvine',
            region: 'CA',
            country: 'US',
            postalCode: '92614',
        },
    },
    lines: [
        {
            number: '1',
            quantity: 1,
            amount: 100,
            taxCode: 'PS081282',
            itemCode: 'Y0001',
            description: 'Yarn',
        },
    ],
    commit: true,
    currencyCode: 'USD',
    description: 'Yarn',
};

export const transactionRequestInventoryTransferInvoice: xtremAvalaraGateway.interfaces.TransactionRequest = {
    type: 'InventoryTransferInvoice',
    companyCode: 'DEFAULT',
    date: '2021-12-16',
    customerCode: 'ABC',
    purchaseOrderNo: '2021-12-13-001',
    addresses: {
        singleLocation: {
            line1: '2000 Main Street',
            city: 'Irvine',
            region: 'CA',
            country: 'US',
            postalCode: '92614',
        },
    },
    lines: [
        {
            number: '1',
            quantity: 1,
            amount: 100,
            taxCode: 'PS081282',
            itemCode: 'Y0001',
            description: 'Yarn',
        },
    ],
    commit: true,
    currencyCode: 'USD',
    description: 'Yarn',
};
