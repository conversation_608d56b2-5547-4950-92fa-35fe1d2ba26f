import type * as xtremAvalaraGateway from '../../index';

export const transactionResponse: xtremAvalaraGateway.interfaces.TransactionResponse = {
    id: **************,
    code: '76db2a2b-0cf4-4bfe-815d-bee2bd9fc1a8',
    companyId: 2751999,
    date: '2017-04-12',
    status: 'Committed',
    type: 'SalesInvoice',
    batchCode: '',
    currencyCode: 'USD',
    exchangeRateCurrencyCode: 'USD',
    customerUsageType: '',
    entityUseCode: '',
    customerVendorCode: 'ABC',
    customerCode: 'ABC',
    exemptNo: '',
    reconciled: false,
    locationCode: '',
    reportingLocationCode: '',
    purchaseOrderNo: '2017-04-12-001',
    referenceCode: '',
    salespersonCode: '',
    taxOverrideType: 'None',
    taxOverrideAmount: 0,
    taxOverrideReason: '',
    totalAmount: 100,
    totalExempt: 100,
    totalDiscount: 0,
    totalTax: 0,
    totalTaxable: 0,
    totalTaxCalculated: 0,
    adjustmentReason: 'NotAdjusted',
    adjustmentDescription: '',
    locked: false,
    region: 'CA',
    country: 'US',
    version: 1,
    softwareVersion: '*********',
    originAddressId: **************,
    destinationAddressId: **************,
    exchangeRateEffectiveDate: '2017-04-12',
    exchangeRate: 1,
    description: '',
    email: '',
    businessIdentificationNo: '',
    modifiedDate: '2021-11-05T09:25:56.9221129Z',
    modifiedUserId: 1444646,
    taxDate: '2017-04-12',
    lines: [
        {
            id: **************,
            transactionId: **************,
            lineNumber: '1',
            boundaryOverrideId: 0,
            customerUsageType: '',
            entityUseCode: '',
            description: 'Yarn',
            destinationAddressId: **************,
            originAddressId: **************,
            discountAmount: 0,
            discountTypeId: 0,
            exemptAmount: 100,
            exemptCertId: 0,
            exemptNo: '',
            isItemTaxable: false,
            isSSTP: false,
            itemCode: 'Y0001',
            lineAmount: 100,
            quantity: 1,
            ref1: '',
            ref2: '',
            reportingDate: '2017-04-12',
            revAccount: '',
            sourcing: 'Origin',
            tax: 0,
            taxableAmount: 0,
            taxCalculated: 0,
            taxCode: 'PS081282',
            taxCodeId: 38007,
            taxDate: '2017-04-12',
            taxEngine: '',
            taxOverrideType: 'None',
            businessIdentificationNo: '',
            taxOverrideAmount: 0,
            taxOverrideReason: '',
            taxIncluded: false,
            details: [
                {
                    id: **************,
                    transactionLineId: **************,
                    transactionId: **************,
                    addressId: **************,
                    country: 'US',
                    region: 'CA',
                    countyFIPS: '',
                    stateFIPS: '',
                    exemptAmount: 0,
                    exemptReasonId: 6,
                    inState: true,
                    jurisCode: '06',
                    jurisName: 'CALIFORNIA',
                    jurisdictionId: 5000531,
                    signatureCode: 'AGAM',
                    stateAssignedNo: '',
                    jurisType: 'STA',
                    jurisdictionType: 'State',
                    nonTaxableAmount: 100,
                    nonTaxableRuleId: 0,
                    nonTaxableType: 'NexusRule',
                    rate: 0,
                    rateRuleId: 0,
                    rateSourceId: 0,
                    serCode: '',
                    sourcing: 'Origin',
                    tax: 0,
                    taxableAmount: 0,
                    taxType: 'Sales',
                    taxSubTypeId: 'S',
                    taxTypeGroupId: 'SalesAndUse',
                    taxName: 'CA STATE TAX',
                    taxAuthorityTypeId: 45,
                    taxRegionId: 2127863,
                    taxCalculated: 0,
                    taxOverride: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    taxableUnits: 0,
                    nonTaxableUnits: 100,
                    exemptUnits: 0,
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 0,
                    reportingNonTaxableUnits: 100,
                    reportingExemptUnits: 0,
                    reportingTax: 0,
                    reportingTaxCalculated: 0,
                    liabilityType: 'Seller',
                },
            ],
            nonPassthroughDetails: [],
            lineLocationTypes: [
                {
                    documentLineLocationTypeId: 79000011551993,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'ShipFrom',
                },
                {
                    documentLineLocationTypeId: 80000011551993,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'ShipTo',
                },
                {
                    documentLineLocationTypeId: 81000011551993,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'PointOfOrderAcceptance',
                },
                {
                    documentLineLocationTypeId: 82000011551993,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'PointOfOrderOrigin',
                },
                {
                    documentLineLocationTypeId: 83000011551993,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'GoodsPlaceOrServiceRendered',
                },
                {
                    documentLineLocationTypeId: 84000011551993,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'Import',
                },
            ],
            hsCode: '',
            costInsuranceFreight: 0,
            vatCode: '',
            vatNumberTypeId: 0,
        },
    ],
    addresses: [
        {
            id: **************,
            transactionId: **************,
            boundaryLevel: 'Zip5',
            line1: '123 Main Street',
            line2: '',
            line3: '',
            city: 'Irvine',
            region: 'CA',
            postalCode: '92615',
            country: 'US',
            taxRegionId: 2127863,
            latitude: '33.657808',
            longitude: '-117.968427',
        },
    ],
    locationTypes: [
        {
            documentLocationTypeId: 66000003319233,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'ShipFrom',
        },
        {
            documentLocationTypeId: 67000003319233,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'ShipTo',
        },
        {
            documentLocationTypeId: 68000003319233,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'PointOfOrderAcceptance',
        },
        {
            documentLocationTypeId: 69000003319233,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'PointOfOrderOrigin',
        },
        {
            documentLocationTypeId: 70000003319233,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'GoodsPlaceOrServiceRendered',
        },
        {
            documentLocationTypeId: 71000003319233,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'Import',
        },
    ],
    summary: [
        {
            country: 'US',
            region: 'CA',
            jurisType: 'State',
            jurisCode: '06',
            jurisName: 'CALIFORNIA',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Sales',
            taxSubType: 'S',
            taxName: 'CA STATE TAX',
            rateType: 'General',
            taxable: 0,
            rate: 0,
            tax: 0,
            taxCalculated: 0,
            nonTaxable: 100,
            exemption: 0,
        },
    ],
};

export const transactionResponseApiCall: xtremAvalaraGateway.interfaces.TransactionResponse = {
    id: **************,
    code: '7ed7d6e5-5e1b-4919-8566-c49c06d0ead0',
    companyId: 2751999,
    date: '2017-04-12',
    status: 'Committed',
    type: 'SalesInvoice',
    batchCode: '',
    currencyCode: 'USD',
    exchangeRateCurrencyCode: 'USD',
    customerUsageType: '',
    entityUseCode: '',
    customerVendorCode: 'ABC',
    customerCode: 'ABC',
    exemptNo: '',
    reconciled: false,
    locationCode: '',
    reportingLocationCode: '',
    purchaseOrderNo: '2017-04-12-001',
    referenceCode: '',
    salespersonCode: '',
    taxOverrideType: 'None',
    taxOverrideAmount: 0,
    taxOverrideReason: '',
    totalAmount: 100,
    totalExempt: 100,
    totalDiscount: 0,
    totalTax: 0,
    totalTaxable: 0,
    totalTaxCalculated: 0,
    adjustmentReason: 'NotAdjusted',
    adjustmentDescription: '',
    locked: false,
    region: 'CA',
    country: 'US',
    version: 1,
    softwareVersion: '**********',
    originAddressId: **************,
    destinationAddressId: **************,
    exchangeRateEffectiveDate: '2017-04-12',
    exchangeRate: 1,
    description: '',
    email: '',
    businessIdentificationNo: '',
    modifiedDate: '2021-11-19T11:25:33.3549993Z',
    modifiedUserId: 1444646,
    taxDate: '2017-04-12',
    lines: [
        {
            id: **************,
            transactionId: **************,
            lineNumber: '1',
            boundaryOverrideId: 0,
            customerUsageType: '',
            entityUseCode: '',
            description: 'Yarn',
            destinationAddressId: **************,
            originAddressId: **************,
            discountAmount: 0,
            discountTypeId: 0,
            exemptAmount: 100,
            exemptCertId: 0,
            exemptNo: '',
            isItemTaxable: false,
            isSSTP: false,
            itemCode: 'Y0001',
            lineAmount: 100,
            quantity: 1,
            ref1: '',
            ref2: '',
            reportingDate: '2017-04-12',
            revAccount: '',
            sourcing: 'Origin',
            tax: 0,
            taxableAmount: 0,
            taxCalculated: 0,
            taxCode: 'PS081282',
            taxCodeId: 38007,
            taxDate: '2017-04-12',
            taxEngine: '',
            taxOverrideType: 'None',
            businessIdentificationNo: '',
            taxOverrideAmount: 0,
            taxOverrideReason: '',
            taxIncluded: false,
            details: [
                {
                    id: **************,
                    transactionLineId: **************,
                    transactionId: **************,
                    addressId: **************,
                    country: 'US',
                    region: 'CA',
                    countyFIPS: '',
                    stateFIPS: '',
                    exemptAmount: 0,
                    exemptReasonId: 6,
                    inState: true,
                    jurisCode: '06',
                    jurisName: 'CALIFORNIA',
                    jurisdictionId: 5000531,
                    signatureCode: 'AGAM',
                    stateAssignedNo: '',
                    jurisType: 'STA',
                    jurisdictionType: 'State',
                    nonTaxableAmount: 100,
                    nonTaxableRuleId: 0,
                    nonTaxableType: 'NexusRule',
                    rate: 0,
                    rateRuleId: 0,
                    rateSourceId: 0,
                    serCode: '',
                    sourcing: 'Origin',
                    tax: 0,
                    taxableAmount: 0,
                    taxType: 'Sales',
                    taxSubTypeId: 'S',
                    taxTypeGroupId: 'SalesAndUse',
                    taxName: 'CA STATE TAX',
                    taxAuthorityTypeId: 45,
                    taxRegionId: 2127863,
                    taxCalculated: 0,
                    taxOverride: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    taxableUnits: 0,
                    nonTaxableUnits: 100,
                    exemptUnits: 0,
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 0,
                    reportingNonTaxableUnits: 100,
                    reportingExemptUnits: 0,
                    reportingTax: 0,
                    reportingTaxCalculated: 0,
                    liabilityType: 'Seller',
                },
            ],
            nonPassthroughDetails: [],
            lineLocationTypes: [
                {
                    documentLineLocationTypeId: 70000017950519,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'ShipFrom',
                },
                {
                    documentLineLocationTypeId: 71000017950519,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'ShipTo',
                },
                {
                    documentLineLocationTypeId: 72000017950520,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'PointOfOrderAcceptance',
                },
                {
                    documentLineLocationTypeId: 73000017950520,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'PointOfOrderOrigin',
                },
                {
                    documentLineLocationTypeId: 74000017950520,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'GoodsPlaceOrServiceRendered',
                },
                {
                    documentLineLocationTypeId: 75000017950520,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'Import',
                },
            ],
            hsCode: '',
            costInsuranceFreight: 0,
            vatCode: '',
            vatNumberTypeId: 0,
        },
    ],
    addresses: [
        {
            id: **************,
            transactionId: **************,
            boundaryLevel: 'Zip5',
            line1: '123 Main Street',
            line2: '',
            line3: '',
            city: 'Irvine',
            region: 'CA',
            postalCode: '92615',
            country: 'US',
            taxRegionId: 2127863,
            latitude: '33.657808',
            longitude: '-117.968427',
        },
    ],
    locationTypes: [
        {
            documentLocationTypeId: 66000005684021,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'ShipFrom',
        },
        {
            documentLocationTypeId: 67000005684021,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'ShipTo',
        },
        {
            documentLocationTypeId: 68000005684021,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'PointOfOrderAcceptance',
        },
        {
            documentLocationTypeId: 69000005684021,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'PointOfOrderOrigin',
        },
        {
            documentLocationTypeId: 70000005684021,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'GoodsPlaceOrServiceRendered',
        },
        {
            documentLocationTypeId: 71000005684021,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'Import',
        },
    ],
    summary: [
        {
            country: 'US',
            region: 'CA',
            jurisType: 'State',
            jurisCode: '06',
            jurisName: 'CALIFORNIA',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Sales',
            taxSubType: 'S',
            taxName: 'CA STATE TAX',
            rateType: 'General',
            taxable: 0,
            rate: 0,
            tax: 0,
            taxCalculated: 0,
            nonTaxable: 100,
            exemption: 0,
        },
    ],
};

export const transactionResponseSalesInvoice: xtremAvalaraGateway.interfaces.TransactionResponse = {
    id: 0,
    code: 'SI7',
    companyId: 2751999,
    date: '2021-12-13',
    paymentDate: '2021-12-13',
    status: 'Temporary',
    type: 'SalesOrder',
    batchCode: '',
    currencyCode: 'USD',
    exchangeRateCurrencyCode: 'USD',
    customerUsageType: '',
    entityUseCode: '',
    customerVendorCode: 'CUST01',
    customerCode: 'US019',
    exemptNo: '',
    reconciled: false,
    locationCode: '',
    reportingLocationCode: '',
    purchaseOrderNo: '',
    referenceCode: '',
    salespersonCode: '',
    totalAmount: 2210.8,
    totalExempt: 0,
    totalDiscount: 0,
    totalTax: 174.65,
    totalTaxable: 2210.8,
    totalTaxCalculated: 174.65,
    adjustmentReason: 'NotAdjusted',
    locked: false,
    version: 1,
    exchangeRateEffectiveDate: '2021-12-13',
    exchangeRate: 1,
    modifiedDate: '2021-12-13T18:42:12.0007005Z',
    modifiedUserId: 1444646,
    taxDate: '2021-12-13',
    lines: [
        {
            id: 0,
            transactionId: 0,
            lineNumber: '1507',
            customerUsageType: '',
            entityUseCode: '',
            description: 'Consulting service',
            discountAmount: 0,
            exemptAmount: 0,
            exemptCertId: 0,
            exemptNo: '',
            isItemTaxable: true,
            itemCode: 'Consulting01',
            lineAmount: 55.92,
            quantity: 24,
            ref1: '',
            ref2: '',
            reportingDate: '2021-12-13',
            tax: 4.42,
            taxableAmount: 55.92,
            taxCalculated: 4.42,
            taxCode: 'P0000000',
            taxCodeId: 8087,
            taxDate: '2021-12-13',
            taxIncluded: false,
            details: [
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'AZ',
                    exemptAmount: 0,
                    jurisCode: '04',
                    jurisName: 'ARIZONA',
                    stateAssignedNo: '',
                    jurisType: 'STA',
                    jurisdictionType: 'State',
                    nonTaxableAmount: 0,
                    rate: 0.056,
                    tax: 3.13,
                    taxableAmount: 55.92,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxName: 'AZ STATE TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 3.13,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 55.92,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 3.13,
                    reportingTaxCalculated: 3.13,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'AZ',
                    exemptAmount: 0,
                    jurisCode: '013',
                    jurisName: 'MARICOPA',
                    stateAssignedNo: 'MAR',
                    jurisType: 'CTY',
                    jurisdictionType: 'County',
                    nonTaxableAmount: 0,
                    rate: 0,
                    tax: 0,
                    taxableAmount: 55.92,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxName: 'AZ COUNTY TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 55.92,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0,
                    reportingTaxCalculated: 0,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'AZ',
                    exemptAmount: 0,
                    jurisCode: '55000',
                    jurisName: 'PHOENIX',
                    stateAssignedNo: 'PX',
                    jurisType: 'CIT',
                    jurisdictionType: 'City',
                    nonTaxableAmount: 0,
                    rate: 0.023,
                    tax: 1.29,
                    taxableAmount: 55.92,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxName: 'AZ CITY TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 1.29,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 55.92,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 1.29,
                    reportingTaxCalculated: 1.29,
                    liabilityType: 'Seller',
                },
            ],
            nonPassthroughDetails: [],
            hsCode: '',
            costInsuranceFreight: 0,
            vatCode: '',
            vatNumberTypeId: 0,
        },
        {
            id: 0,
            transactionId: 0,
            lineNumber: '1508',
            customerUsageType: '',
            entityUseCode: '',
            description: 'Sales Item 81',
            discountAmount: 0,
            exemptAmount: 0,
            exemptCertId: 0,
            exemptNo: '',
            isItemTaxable: true,
            itemCode: 'SalesItem81',
            lineAmount: 2154.88,
            quantity: 37,
            ref1: '',
            ref2: '',
            reportingDate: '2021-12-13',
            tax: 170.23,
            taxableAmount: 2154.88,
            taxCalculated: 170.23,
            taxCode: 'P0000000',
            taxCodeId: 8087,
            taxDate: '2021-12-13',
            taxIncluded: false,
            details: [
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'AZ',
                    exemptAmount: 0,
                    jurisCode: '04',
                    jurisName: 'ARIZONA',
                    stateAssignedNo: '',
                    jurisType: 'STA',
                    jurisdictionType: 'State',
                    nonTaxableAmount: 0,
                    rate: 0.056,
                    tax: 120.67,
                    taxableAmount: 2154.88,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxName: 'AZ STATE TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 120.67,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 2154.88,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 120.67,
                    reportingTaxCalculated: 120.67,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'AZ',
                    exemptAmount: 0,
                    jurisCode: '013',
                    jurisName: 'MARICOPA',
                    stateAssignedNo: 'MAR',
                    jurisType: 'CTY',
                    jurisdictionType: 'County',
                    nonTaxableAmount: 0,
                    rate: 0,
                    tax: 0,
                    taxableAmount: 2154.88,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxName: 'AZ COUNTY TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 2154.88,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0,
                    reportingTaxCalculated: 0,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'AZ',
                    exemptAmount: 0,
                    jurisCode: '55000',
                    jurisName: 'PHOENIX',
                    stateAssignedNo: 'PX',
                    jurisType: 'CIT',
                    jurisdictionType: 'City',
                    nonTaxableAmount: 0,
                    rate: 0.023,
                    tax: 49.56,
                    taxableAmount: 2154.88,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxName: 'AZ CITY TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 49.56,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 2154.88,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 49.56,
                    reportingTaxCalculated: 49.56,
                    liabilityType: 'Seller',
                },
            ],
            nonPassthroughDetails: [],
            hsCode: '',
            costInsuranceFreight: 0,
            vatCode: '',
            vatNumberTypeId: 0,
        },
    ],
    addresses: [
        {
            id: 0,
            transactionId: 0,
            boundaryLevel: 'Zip5',
            line1: '1st Avenue',
            line2: '',
            line3: '',
            city: 'One',
            region: 'AZ',
            postalCode: '85034',
            country: 'US',
            taxRegionId: 4018107,
            latitude: '33.438877',
            longitude: '-112.029773',
        },
        {
            id: 0,
            transactionId: 0,
            boundaryLevel: 'Zip5',
            line1: 'First address line site',
            line2: '',
            line3: '',
            city: '',
            region: 'MD',
            postalCode: '20746',
            country: 'US',
            taxRegionId: 4003258,
            latitude: '38.837631',
            longitude: '-76.919538',
        },
    ],
    summary: [
        {
            country: 'US',
            region: 'AZ',
            jurisType: 'State',
            jurisCode: '04',
            jurisName: 'ARIZONA',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'AZ STATE TAX',
            rateType: 'General',
            taxable: 2210.8,
            rate: 0.056,
            tax: 123.8,
            taxCalculated: 123.8,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'AZ',
            jurisType: 'County',
            jurisCode: '013',
            jurisName: 'MARICOPA',
            taxAuthorityType: 45,
            stateAssignedNo: 'MAR',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'AZ COUNTY TAX',
            rateType: 'General',
            taxable: 2210.8,
            rate: 0,
            tax: 0,
            taxCalculated: 0,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'AZ',
            jurisType: 'City',
            jurisCode: '55000',
            jurisName: 'PHOENIX',
            taxAuthorityType: 45,
            stateAssignedNo: 'PX',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'AZ CITY TAX',
            rateType: 'General',
            taxable: 2210.8,
            rate: 0.023,
            tax: 50.85,
            taxCalculated: 50.85,
            nonTaxable: 0,
            exemption: 0,
        },
    ],
};

export const transactionResponseSalesOrder: xtremAvalaraGateway.interfaces.TransactionResponse = {
    id: 0,
    code: 'd673cc1d-f93c-4517-9c4e-c6740ea56f6a',
    companyId: 2751999,
    date: '2021-12-16',
    paymentDate: '2021-12-16',
    status: 'Temporary',
    type: 'SalesOrder',
    batchCode: '',
    currencyCode: 'USD',
    exchangeRateCurrencyCode: 'USD',
    customerUsageType: '',
    entityUseCode: '',
    customerVendorCode: 'ABC',
    customerCode: 'ABC',
    exemptNo: '',
    reconciled: false,
    locationCode: '',
    reportingLocationCode: '',
    purchaseOrderNo: '2021-12-13-001',
    referenceCode: '',
    salespersonCode: '',
    totalAmount: 100,
    totalExempt: 0,
    totalDiscount: 0,
    totalTax: 7.75,
    totalTaxable: 100,
    totalTaxCalculated: 7.75,
    adjustmentReason: 'NotAdjusted',
    locked: false,
    version: 1,
    exchangeRateEffectiveDate: '2021-12-16',
    exchangeRate: 1,
    description: 'Yarn',
    modifiedDate: '2022-03-21T18:03:28.972176Z',
    modifiedUserId: 1444646,
    taxDate: '2021-12-16',
    lines: [
        {
            id: 0,
            transactionId: 0,
            lineNumber: '1',
            customerUsageType: '',
            entityUseCode: '',
            description: 'Yarn',
            discountAmount: 0,
            exemptAmount: 0,
            exemptCertId: 0,
            exemptNo: '',
            isItemTaxable: true,
            itemCode: 'Y0001',
            lineAmount: 100,
            quantity: 1,
            ref1: '',
            ref2: '',
            reportingDate: '2021-12-16',
            tax: 7.75,
            taxableAmount: 100,
            taxCalculated: 7.75,
            taxCode: 'PS081282',
            taxCodeId: 38007,
            taxDate: '2021-12-16',
            taxIncluded: false,
            details: [
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: '06',
                    jurisName: 'CALIFORNIA',
                    stateAssignedNo: '',
                    jurisType: 'STA',
                    jurisdictionType: 'State',
                    nonTaxableAmount: 0,
                    rate: 0.06,
                    tax: 6,
                    taxableAmount: 100,
                    taxType: 'Sales',
                    taxSubTypeId: 'S',
                    taxName: 'CA STATE TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 6,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 6,
                    reportingTaxCalculated: 6,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: '059',
                    jurisName: 'ORANGE',
                    stateAssignedNo: '',
                    jurisType: 'CTY',
                    jurisdictionType: 'County',
                    nonTaxableAmount: 0,
                    rate: 0.0025,
                    tax: 0.25,
                    taxableAmount: 100,
                    taxType: 'Sales',
                    taxSubTypeId: 'S',
                    taxName: 'CA COUNTY TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 0.25,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0.25,
                    reportingTaxCalculated: 0.25,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: 'EMAZ0',
                    jurisName: 'ORANGE COUNTY DISTRICT TAX SP',
                    stateAssignedNo: '037',
                    jurisType: 'STJ',
                    jurisdictionType: 'Special',
                    nonTaxableAmount: 0,
                    rate: 0.005,
                    tax: 0.5,
                    taxableAmount: 100,
                    taxType: 'Sales',
                    taxSubTypeId: 'S',
                    taxName: 'CA SPECIAL TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 0.5,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0.5,
                    reportingTaxCalculated: 0.5,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: 'EMTN0',
                    jurisName: 'ORANGE CO LOCAL TAX SL',
                    stateAssignedNo: '30',
                    jurisType: 'STJ',
                    jurisdictionType: 'Special',
                    nonTaxableAmount: 0,
                    rate: 0.01,
                    tax: 1,
                    taxableAmount: 100,
                    taxType: 'Sales',
                    taxSubTypeId: 'S',
                    taxName: 'CA SPECIAL TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 1,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 1,
                    reportingTaxCalculated: 1,
                    liabilityType: 'Seller',
                },
            ],
            nonPassthroughDetails: [],
            hsCode: '',
            costInsuranceFreight: 0,
            vatCode: '',
            vatNumberTypeId: 0,
        },
    ],
    addresses: [
        {
            id: 0,
            transactionId: 0,
            boundaryLevel: 'Address',
            line1: '2000 Main Street',
            line2: '',
            line3: '',
            city: 'Irvine',
            region: 'CA',
            postalCode: '92614',
            country: 'US',
            taxRegionId: 4017409,
            latitude: '33.684716',
            longitude: '-117.851489',
        },
    ],
    summary: [
        {
            country: 'US',
            region: 'CA',
            jurisType: 'State',
            jurisCode: '06',
            jurisName: 'CALIFORNIA',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Sales',
            taxSubType: 'S',
            taxName: 'CA STATE TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.06,
            tax: 6,
            taxCalculated: 6,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'County',
            jurisCode: '059',
            jurisName: 'ORANGE',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Sales',
            taxSubType: 'S',
            taxName: 'CA COUNTY TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.0025,
            tax: 0.25,
            taxCalculated: 0.25,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'Special',
            jurisCode: 'EMTN0',
            jurisName: 'ORANGE CO LOCAL TAX SL',
            taxAuthorityType: 45,
            stateAssignedNo: '30',
            taxType: 'Sales',
            taxSubType: 'S',
            taxName: 'CA SPECIAL TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.01,
            tax: 1,
            taxCalculated: 1,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'Special',
            jurisCode: 'EMAZ0',
            jurisName: 'ORANGE COUNTY DISTRICT TAX SP',
            taxAuthorityType: 45,
            stateAssignedNo: '037',
            taxType: 'Sales',
            taxSubType: 'S',
            taxName: 'CA SPECIAL TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.005,
            tax: 0.5,
            taxCalculated: 0.5,
            nonTaxable: 0,
            exemption: 0,
        },
    ],
};

export const transactionResponsePurchaseOrder: xtremAvalaraGateway.interfaces.TransactionResponse = {
    id: 0,
    code: '51409ac9-67ad-4ca4-82d3-6f2ca675ea1b',
    companyId: 2751999,
    date: '2021-12-16',
    paymentDate: '2021-12-16',
    status: 'Temporary',
    type: 'PurchaseOrder',
    batchCode: '',
    currencyCode: 'USD',
    exchangeRateCurrencyCode: 'USD',
    customerUsageType: '',
    entityUseCode: '',
    customerVendorCode: 'ABC',
    customerCode: 'ABC',
    exemptNo: '',
    reconciled: false,
    locationCode: '',
    reportingLocationCode: '',
    purchaseOrderNo: '2021-12-13-001',
    referenceCode: '',
    salespersonCode: '',
    totalAmount: 100,
    totalExempt: 0,
    totalDiscount: 0,
    totalTax: 7.75,
    totalTaxable: 100,
    totalTaxCalculated: 7.75,
    adjustmentReason: 'NotAdjusted',
    locked: false,
    version: 1,
    exchangeRateEffectiveDate: '2021-12-16',
    exchangeRate: 1,
    description: 'Yarn',
    modifiedDate: '2022-03-22T13:18:48.0467709Z',
    modifiedUserId: 1444646,
    taxDate: '2021-12-16',
    lines: [
        {
            id: 0,
            transactionId: 0,
            lineNumber: '1',
            customerUsageType: '',
            entityUseCode: '',
            description: 'Yarn',
            discountAmount: 0,
            exemptAmount: 0,
            exemptCertId: 0,
            exemptNo: '',
            isItemTaxable: true,
            itemCode: 'Y0001',
            lineAmount: 100,
            quantity: 1,
            ref1: '',
            ref2: '',
            reportingDate: '2021-12-16',
            tax: 7.75,
            taxableAmount: 100,
            taxCalculated: 7.75,
            taxCode: 'PS081282',
            taxCodeId: 38007,
            taxDate: '2021-12-16',
            taxIncluded: false,
            details: [
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: '06',
                    jurisName: 'CALIFORNIA',
                    stateAssignedNo: '',
                    jurisType: 'STA',
                    jurisdictionType: 'State',
                    nonTaxableAmount: 0,
                    rate: 0.06,
                    tax: 6,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxName: 'CA STATE TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 6,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 6,
                    reportingTaxCalculated: 6,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: '059',
                    jurisName: 'ORANGE',
                    stateAssignedNo: '',
                    jurisType: 'CTY',
                    jurisdictionType: 'County',
                    nonTaxableAmount: 0,
                    rate: 0.0025,
                    tax: 0.25,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxName: 'CA COUNTY TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 0.25,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0.25,
                    reportingTaxCalculated: 0.25,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: 'EMAZ0',
                    jurisName: 'ORANGE COUNTY DISTRICT TAX SP',
                    stateAssignedNo: '037',
                    jurisType: 'STJ',
                    jurisdictionType: 'Special',
                    nonTaxableAmount: 0,
                    rate: 0.005,
                    tax: 0.5,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxName: 'CA SPECIAL TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 0.5,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0.5,
                    reportingTaxCalculated: 0.5,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: 'EMTN0',
                    jurisName: 'ORANGE CO LOCAL TAX SL',
                    stateAssignedNo: '30',
                    jurisType: 'STJ',
                    jurisdictionType: 'Special',
                    nonTaxableAmount: 0,
                    rate: 0.01,
                    tax: 1,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxName: 'CA SPECIAL TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 1,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 1,
                    reportingTaxCalculated: 1,
                    liabilityType: 'Seller',
                },
            ],
            nonPassthroughDetails: [],
            hsCode: '',
            costInsuranceFreight: 0,
            vatCode: '',
            vatNumberTypeId: 0,
        },
    ],
    addresses: [
        {
            id: 0,
            transactionId: 0,
            boundaryLevel: 'Address',
            line1: '2000 Main Street',
            line2: '',
            line3: '',
            city: 'Irvine',
            region: 'CA',
            postalCode: '92614',
            country: 'US',
            taxRegionId: 4017409,
            latitude: '33.684716',
            longitude: '-117.851489',
        },
    ],
    summary: [
        {
            country: 'US',
            region: 'CA',
            jurisType: 'State',
            jurisCode: '06',
            jurisName: 'CALIFORNIA',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA STATE TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.06,
            tax: 6,
            taxCalculated: 6,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'County',
            jurisCode: '059',
            jurisName: 'ORANGE',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA COUNTY TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.0025,
            tax: 0.25,
            taxCalculated: 0.25,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'Special',
            jurisCode: 'EMTN0',
            jurisName: 'ORANGE CO LOCAL TAX SL',
            taxAuthorityType: 45,
            stateAssignedNo: '30',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA SPECIAL TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.01,
            tax: 1,
            taxCalculated: 1,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'Special',
            jurisCode: 'EMAZ0',
            jurisName: 'ORANGE COUNTY DISTRICT TAX SP',
            taxAuthorityType: 45,
            stateAssignedNo: '037',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA SPECIAL TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.005,
            tax: 0.5,
            taxCalculated: 0.5,
            nonTaxable: 0,
            exemption: 0,
        },
    ],
};

export const transactionResponsePurchaseInvoice: xtremAvalaraGateway.interfaces.TransactionResponse = {
    id: **************,
    code: '7a7ff82a-41ce-47b7-b995-32ea49de0d9e',
    companyId: 2751999,
    date: '2021-12-16',
    status: 'Committed',
    type: 'PurchaseInvoice',
    batchCode: '',
    currencyCode: 'USD',
    exchangeRateCurrencyCode: 'USD',
    customerUsageType: '',
    entityUseCode: '',
    customerVendorCode: 'ABC',
    customerCode: 'ABC',
    exemptNo: '',
    reconciled: false,
    locationCode: '',
    reportingLocationCode: '',
    purchaseOrderNo: '2021-12-13-001',
    referenceCode: '',
    salespersonCode: '',
    taxOverrideType: 'None',
    taxOverrideAmount: 0,
    taxOverrideReason: '',
    totalAmount: 100,
    totalExempt: 0,
    totalDiscount: 0,
    totalTax: 7.75,
    totalTaxable: 100,
    totalTaxCalculated: 7.75,
    adjustmentReason: 'NotAdjusted',
    adjustmentDescription: '',
    locked: false,
    region: 'CA',
    country: 'US',
    version: 1,
    softwareVersion: '********',
    originAddressId: **************,
    destinationAddressId: **************,
    exchangeRateEffectiveDate: '2021-12-16',
    exchangeRate: 1,
    description: '',
    email: '',
    businessIdentificationNo: '',
    modifiedDate: '2022-03-22T13:37:48.3856581Z',
    modifiedUserId: 1444646,
    taxDate: '2021-12-16',
    lines: [
        {
            id: **************,
            transactionId: **************,
            lineNumber: '1',
            boundaryOverrideId: 0,
            customerUsageType: '',
            entityUseCode: '',
            description: 'Yarn',
            destinationAddressId: **************,
            originAddressId: **************,
            discountAmount: 0,
            discountTypeId: 0,
            exemptAmount: 0,
            exemptCertId: 0,
            exemptNo: '',
            isItemTaxable: true,
            isSSTP: false,
            itemCode: 'Y0001',
            lineAmount: 100,
            quantity: 1,
            ref1: '',
            ref2: '',
            reportingDate: '2021-12-16',
            revAccount: '',
            sourcing: 'Mixed',
            tax: 7.75,
            taxableAmount: 100,
            taxCalculated: 7.75,
            taxCode: 'PS081282',
            taxCodeId: 38007,
            taxDate: '2021-12-16',
            taxEngine: '',
            taxOverrideType: 'None',
            businessIdentificationNo: '',
            taxOverrideAmount: 0,
            taxOverrideReason: '',
            taxIncluded: false,
            details: [
                {
                    id: **************,
                    transactionLineId: **************,
                    transactionId: **************,
                    addressId: **************,
                    country: 'US',
                    region: 'CA',
                    countyFIPS: '',
                    stateFIPS: '',
                    exemptAmount: 0,
                    exemptReasonId: 4,
                    inState: true,
                    jurisCode: '06',
                    jurisName: 'CALIFORNIA',
                    jurisdictionId: 5000531,
                    signatureCode: 'AGAM',
                    stateAssignedNo: '',
                    jurisType: 'STA',
                    jurisdictionType: 'State',
                    nonTaxableAmount: 0,
                    nonTaxableRuleId: 0,
                    nonTaxableType: 'RateRule',
                    rate: 0.06,
                    rateRuleId: 1526144,
                    rateSourceId: 3,
                    serCode: '',
                    sourcing: 'Origin',
                    tax: 6,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxTypeGroupId: 'SalesAndUse',
                    taxName: 'CA STATE TAX',
                    taxAuthorityTypeId: 45,
                    taxRegionId: 4017409,
                    taxCalculated: 6,
                    taxOverride: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    taxableUnits: 100,
                    nonTaxableUnits: 0,
                    exemptUnits: 0,
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 6,
                    reportingTaxCalculated: 6,
                    liabilityType: 'Seller',
                },
                {
                    id: 85000330107782,
                    transactionLineId: **************,
                    transactionId: **************,
                    addressId: **************,
                    country: 'US',
                    region: 'CA',
                    countyFIPS: '',
                    stateFIPS: '',
                    exemptAmount: 0,
                    exemptReasonId: 4,
                    inState: true,
                    jurisCode: '059',
                    jurisName: 'ORANGE',
                    jurisdictionId: 267,
                    signatureCode: 'AHXU',
                    stateAssignedNo: '',
                    jurisType: 'CTY',
                    jurisdictionType: 'County',
                    nonTaxableAmount: 0,
                    nonTaxableRuleId: 0,
                    nonTaxableType: 'RateRule',
                    rate: 0.0025,
                    rateRuleId: 1526140,
                    rateSourceId: 3,
                    serCode: '',
                    sourcing: 'Origin',
                    tax: 0.25,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxTypeGroupId: 'SalesAndUse',
                    taxName: 'CA COUNTY TAX',
                    taxAuthorityTypeId: 45,
                    taxRegionId: 4017409,
                    taxCalculated: 0.25,
                    taxOverride: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    taxableUnits: 100,
                    nonTaxableUnits: 0,
                    exemptUnits: 0,
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0.25,
                    reportingTaxCalculated: 0.25,
                    liabilityType: 'Seller',
                },
                {
                    id: 85000330107783,
                    transactionLineId: **************,
                    transactionId: **************,
                    addressId: **************,
                    country: 'US',
                    region: 'CA',
                    countyFIPS: '',
                    stateFIPS: '',
                    exemptAmount: 0,
                    exemptReasonId: 4,
                    inState: true,
                    jurisCode: 'EMAZ0',
                    jurisName: 'ORANGE COUNTY DISTRICT TAX SP',
                    jurisdictionId: 2001061425,
                    signatureCode: 'EMAZ',
                    stateAssignedNo: '037',
                    jurisType: 'STJ',
                    jurisdictionType: 'Special',
                    nonTaxableAmount: 0,
                    nonTaxableRuleId: 0,
                    nonTaxableType: 'RateRule',
                    rate: 0.005,
                    rateRuleId: 1526110,
                    rateSourceId: 3,
                    serCode: '',
                    sourcing: 'Destination',
                    tax: 0.5,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxTypeGroupId: 'SalesAndUse',
                    taxName: 'CA SPECIAL TAX',
                    taxAuthorityTypeId: 45,
                    taxRegionId: 4017409,
                    taxCalculated: 0.5,
                    taxOverride: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    taxableUnits: 100,
                    nonTaxableUnits: 0,
                    exemptUnits: 0,
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0.5,
                    reportingTaxCalculated: 0.5,
                    liabilityType: 'Seller',
                },
                {
                    id: 85000330107784,
                    transactionLineId: **************,
                    transactionId: **************,
                    addressId: **************,
                    country: 'US',
                    region: 'CA',
                    countyFIPS: '',
                    stateFIPS: '',
                    exemptAmount: 0,
                    exemptReasonId: 4,
                    inState: true,
                    jurisCode: 'EMTN0',
                    jurisName: 'ORANGE CO LOCAL TAX SL',
                    jurisdictionId: 2001061784,
                    signatureCode: 'EMTN',
                    stateAssignedNo: '30',
                    jurisType: 'STJ',
                    jurisdictionType: 'Special',
                    nonTaxableAmount: 0,
                    nonTaxableRuleId: 0,
                    nonTaxableType: 'RateRule',
                    rate: 0.01,
                    rateRuleId: 1526106,
                    rateSourceId: 3,
                    serCode: '',
                    sourcing: 'Origin',
                    tax: 1,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxTypeGroupId: 'SalesAndUse',
                    taxName: 'CA SPECIAL TAX',
                    taxAuthorityTypeId: 45,
                    taxRegionId: 4017409,
                    taxCalculated: 1,
                    taxOverride: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    taxableUnits: 100,
                    nonTaxableUnits: 0,
                    exemptUnits: 0,
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 1,
                    reportingTaxCalculated: 1,
                    liabilityType: 'Seller',
                },
            ],
            nonPassthroughDetails: [],
            lineLocationTypes: [
                {
                    documentLineLocationTypeId: 85000330107774,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'ShipFrom',
                },
                {
                    documentLineLocationTypeId: 85000330107775,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'ShipTo',
                },
                {
                    documentLineLocationTypeId: 85000330107776,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'PointOfOrderAcceptance',
                },
                {
                    documentLineLocationTypeId: 85000330107777,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'PointOfOrderOrigin',
                },
                {
                    documentLineLocationTypeId: 85000330107778,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'GoodsPlaceOrServiceRendered',
                },
                {
                    documentLineLocationTypeId: 85000330107779,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'Import',
                },
                {
                    documentLineLocationTypeId: 85000330107780,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'BillTo',
                },
            ],
            hsCode: '',
            costInsuranceFreight: 0,
            vatCode: '',
            vatNumberTypeId: 0,
        },
    ],
    addresses: [
        {
            id: **************,
            transactionId: **************,
            boundaryLevel: 'Address',
            line1: '2000 Main St',
            line2: '',
            line3: '',
            city: 'Irvine',
            region: 'CA',
            postalCode: '92614-7202',
            country: 'US',
            taxRegionId: 4017409,
            latitude: '33.684716',
            longitude: '-117.851489',
        },
    ],
    locationTypes: [
        {
            documentLocationTypeId: 85000330107765,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'ShipFrom',
        },
        {
            documentLocationTypeId: 85000330107766,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'ShipTo',
        },
        {
            documentLocationTypeId: 85000330107767,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'PointOfOrderAcceptance',
        },
        {
            documentLocationTypeId: 85000330107768,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'PointOfOrderOrigin',
        },
        {
            documentLocationTypeId: 85000330107769,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'GoodsPlaceOrServiceRendered',
        },
        {
            documentLocationTypeId: 85000330107770,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'Import',
        },
        {
            documentLocationTypeId: 85000330107771,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'BillTo',
        },
    ],
    summary: [
        {
            country: 'US',
            region: 'CA',
            jurisType: 'State',
            jurisCode: '06',
            jurisName: 'CALIFORNIA',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA STATE TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.06,
            tax: 6,
            taxCalculated: 6,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'County',
            jurisCode: '059',
            jurisName: 'ORANGE',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA COUNTY TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.0025,
            tax: 0.25,
            taxCalculated: 0.25,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'Special',
            jurisCode: 'EMTN0',
            jurisName: 'ORANGE CO LOCAL TAX SL',
            taxAuthorityType: 45,
            stateAssignedNo: '30',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA SPECIAL TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.01,
            tax: 1,
            taxCalculated: 1,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'Special',
            jurisCode: 'EMAZ0',
            jurisName: 'ORANGE COUNTY DISTRICT TAX SP',
            taxAuthorityType: 45,
            stateAssignedNo: '037',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA SPECIAL TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.005,
            tax: 0.5,
            taxCalculated: 0.5,
            nonTaxable: 0,
            exemption: 0,
        },
    ],
};

export const transactionResponseReturnOrder: xtremAvalaraGateway.interfaces.TransactionResponse = {
    id: 0,
    code: '1ec4bcfb-e162-4923-84d1-0d823ec255c1',
    companyId: 2751999,
    date: '2021-12-16',
    paymentDate: '2021-12-16',
    status: 'Temporary',
    type: 'ReturnOrder',
    batchCode: '',
    currencyCode: 'USD',
    exchangeRateCurrencyCode: 'USD',
    customerUsageType: '',
    entityUseCode: '',
    customerVendorCode: 'ABC',
    customerCode: 'ABC',
    exemptNo: '',
    reconciled: false,
    locationCode: '',
    reportingLocationCode: '',
    purchaseOrderNo: '2021-12-13-001',
    referenceCode: '',
    salespersonCode: '',
    totalAmount: 100,
    totalExempt: 0,
    totalDiscount: 0,
    totalTax: 7.75,
    totalTaxable: 100,
    totalTaxCalculated: 7.75,
    adjustmentReason: 'NotAdjusted',
    locked: false,
    version: 1,
    exchangeRateEffectiveDate: '2021-12-16',
    exchangeRate: 1,
    description: 'Yarn',
    modifiedDate: '2022-03-22T16:11:25.2915252Z',
    modifiedUserId: 1444646,
    taxDate: '2021-12-16',
    lines: [
        {
            id: 0,
            transactionId: 0,
            lineNumber: '1',
            customerUsageType: '',
            entityUseCode: '',
            description: 'Yarn',
            discountAmount: 0,
            exemptAmount: 0,
            exemptCertId: 0,
            exemptNo: '',
            isItemTaxable: true,
            itemCode: 'Y0001',
            lineAmount: 100,
            quantity: 1,
            ref1: '',
            ref2: '',
            reportingDate: '2021-12-16',
            tax: 7.75,
            taxableAmount: 100,
            taxCalculated: 7.75,
            taxCode: 'PS081282',
            taxCodeId: 38007,
            taxDate: '2021-12-16',
            taxIncluded: false,
            details: [
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: '06',
                    jurisName: 'CALIFORNIA',
                    stateAssignedNo: '',
                    jurisType: 'STA',
                    jurisdictionType: 'State',
                    nonTaxableAmount: 0,
                    rate: 0.06,
                    tax: 6,
                    taxableAmount: 100,
                    taxType: 'Sales',
                    taxSubTypeId: 'S',
                    taxName: 'CA STATE TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 6,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 6,
                    reportingTaxCalculated: 6,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: '059',
                    jurisName: 'ORANGE',
                    stateAssignedNo: '',
                    jurisType: 'CTY',
                    jurisdictionType: 'County',
                    nonTaxableAmount: 0,
                    rate: 0.0025,
                    tax: 0.25,
                    taxableAmount: 100,
                    taxType: 'Sales',
                    taxSubTypeId: 'S',
                    taxName: 'CA COUNTY TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 0.25,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0.25,
                    reportingTaxCalculated: 0.25,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: 'EMAZ0',
                    jurisName: 'ORANGE COUNTY DISTRICT TAX SP',
                    stateAssignedNo: '037',
                    jurisType: 'STJ',
                    jurisdictionType: 'Special',
                    nonTaxableAmount: 0,
                    rate: 0.005,
                    tax: 0.5,
                    taxableAmount: 100,
                    taxType: 'Sales',
                    taxSubTypeId: 'S',
                    taxName: 'CA SPECIAL TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 0.5,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0.5,
                    reportingTaxCalculated: 0.5,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: 'EMTN0',
                    jurisName: 'ORANGE CO LOCAL TAX SL',
                    stateAssignedNo: '30',
                    jurisType: 'STJ',
                    jurisdictionType: 'Special',
                    nonTaxableAmount: 0,
                    rate: 0.01,
                    tax: 1,
                    taxableAmount: 100,
                    taxType: 'Sales',
                    taxSubTypeId: 'S',
                    taxName: 'CA SPECIAL TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 1,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 1,
                    reportingTaxCalculated: 1,
                    liabilityType: 'Seller',
                },
            ],
            nonPassthroughDetails: [],
            hsCode: '',
            costInsuranceFreight: 0,
            vatCode: '',
            vatNumberTypeId: 0,
        },
    ],
    addresses: [
        {
            id: 0,
            transactionId: 0,
            boundaryLevel: 'Address',
            line1: '2000 Main Street',
            line2: '',
            line3: '',
            city: 'Irvine',
            region: 'CA',
            postalCode: '92614',
            country: 'US',
            taxRegionId: 4017409,
            latitude: '33.684716',
            longitude: '-117.851489',
        },
    ],
    summary: [
        {
            country: 'US',
            region: 'CA',
            jurisType: 'State',
            jurisCode: '06',
            jurisName: 'CALIFORNIA',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Sales',
            taxSubType: 'S',
            taxName: 'CA STATE TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.06,
            tax: 6,
            taxCalculated: 6,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'County',
            jurisCode: '059',
            jurisName: 'ORANGE',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Sales',
            taxSubType: 'S',
            taxName: 'CA COUNTY TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.0025,
            tax: 0.25,
            taxCalculated: 0.25,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'Special',
            jurisCode: 'EMTN0',
            jurisName: 'ORANGE CO LOCAL TAX SL',
            taxAuthorityType: 45,
            stateAssignedNo: '30',
            taxType: 'Sales',
            taxSubType: 'S',
            taxName: 'CA SPECIAL TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.01,
            tax: 1,
            taxCalculated: 1,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'Special',
            jurisCode: 'EMAZ0',
            jurisName: 'ORANGE COUNTY DISTRICT TAX SP',
            taxAuthorityType: 45,
            stateAssignedNo: '037',
            taxType: 'Sales',
            taxSubType: 'S',
            taxName: 'CA SPECIAL TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.005,
            tax: 0.5,
            taxCalculated: 0.5,
            nonTaxable: 0,
            exemption: 0,
        },
    ],
};

export const transactionResponseReturnInvoice: xtremAvalaraGateway.interfaces.TransactionResponse = {
    id: **************,
    code: '6f4f82a6-4d15-414d-b92f-a9a6a3f1b897',
    companyId: 2751999,
    date: '2021-12-16',
    status: 'Committed',
    type: 'ReturnInvoice',
    batchCode: '',
    currencyCode: 'USD',
    exchangeRateCurrencyCode: 'USD',
    customerUsageType: '',
    entityUseCode: '',
    customerVendorCode: 'ABC',
    customerCode: 'ABC',
    exemptNo: '',
    reconciled: false,
    locationCode: '',
    reportingLocationCode: '',
    purchaseOrderNo: '2021-12-13-001',
    referenceCode: '',
    salespersonCode: '',
    taxOverrideType: 'None',
    taxOverrideAmount: 0,
    taxOverrideReason: '',
    totalAmount: 100,
    totalExempt: 0,
    totalDiscount: 0,
    totalTax: 7.75,
    totalTaxable: 100,
    totalTaxCalculated: 7.75,
    adjustmentReason: 'NotAdjusted',
    adjustmentDescription: '',
    locked: false,
    region: 'CA',
    country: 'US',
    version: 1,
    softwareVersion: '********',
    originAddressId: **************,
    destinationAddressId: **************,
    exchangeRateEffectiveDate: '2021-12-16',
    exchangeRate: 1,
    description: '',
    email: '',
    businessIdentificationNo: '',
    modifiedDate: '2022-03-22T16:35:37.0751357Z',
    modifiedUserId: 1444646,
    taxDate: '2021-12-16',
    lines: [
        {
            id: **************,
            transactionId: **************,
            lineNumber: '1',
            boundaryOverrideId: 0,
            customerUsageType: '',
            entityUseCode: '',
            description: 'Yarn',
            destinationAddressId: **************,
            originAddressId: **************,
            discountAmount: 0,
            discountTypeId: 0,
            exemptAmount: 0,
            exemptCertId: 0,
            exemptNo: '',
            isItemTaxable: true,
            isSSTP: false,
            itemCode: 'Y0001',
            lineAmount: 100,
            quantity: 1,
            ref1: '',
            ref2: '',
            reportingDate: '2021-12-16',
            revAccount: '',
            sourcing: 'Mixed',
            tax: 7.75,
            taxableAmount: 100,
            taxCalculated: 7.75,
            taxCode: 'PS081282',
            taxCodeId: 38007,
            taxDate: '2021-12-16',
            taxEngine: '',
            taxOverrideType: 'None',
            businessIdentificationNo: '',
            taxOverrideAmount: 0,
            taxOverrideReason: '',
            taxIncluded: false,
            details: [
                {
                    id: **************,
                    transactionLineId: **************,
                    transactionId: **************,
                    addressId: **************,
                    country: 'US',
                    region: 'CA',
                    countyFIPS: '',
                    stateFIPS: '',
                    exemptAmount: 0,
                    exemptReasonId: 4,
                    inState: true,
                    jurisCode: '06',
                    jurisName: 'CALIFORNIA',
                    jurisdictionId: 5000531,
                    signatureCode: 'AGAM',
                    stateAssignedNo: '',
                    jurisType: 'STA',
                    jurisdictionType: 'State',
                    nonTaxableAmount: 0,
                    nonTaxableRuleId: 0,
                    nonTaxableType: 'RateRule',
                    rate: 0.06,
                    rateRuleId: 1526142,
                    rateSourceId: 3,
                    serCode: '',
                    sourcing: 'Origin',
                    tax: 6,
                    taxableAmount: 100,
                    taxType: 'Sales',
                    taxSubTypeId: 'S',
                    taxTypeGroupId: 'SalesAndUse',
                    taxName: 'CA STATE TAX',
                    taxAuthorityTypeId: 45,
                    taxRegionId: 4017409,
                    taxCalculated: 6,
                    taxOverride: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    taxableUnits: 100,
                    nonTaxableUnits: 0,
                    exemptUnits: 0,
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 6,
                    reportingTaxCalculated: 6,
                    liabilityType: 'Seller',
                },
                {
                    id: 85000337103338,
                    transactionLineId: **************,
                    transactionId: **************,
                    addressId: **************,
                    country: 'US',
                    region: 'CA',
                    countyFIPS: '',
                    stateFIPS: '',
                    exemptAmount: 0,
                    exemptReasonId: 4,
                    inState: true,
                    jurisCode: '059',
                    jurisName: 'ORANGE',
                    jurisdictionId: 267,
                    signatureCode: 'AHXU',
                    stateAssignedNo: '',
                    jurisType: 'CTY',
                    jurisdictionType: 'County',
                    nonTaxableAmount: 0,
                    nonTaxableRuleId: 0,
                    nonTaxableType: 'RateRule',
                    rate: 0.0025,
                    rateRuleId: 1526120,
                    rateSourceId: 3,
                    serCode: '',
                    sourcing: 'Origin',
                    tax: 0.25,
                    taxableAmount: 100,
                    taxType: 'Sales',
                    taxSubTypeId: 'S',
                    taxTypeGroupId: 'SalesAndUse',
                    taxName: 'CA COUNTY TAX',
                    taxAuthorityTypeId: 45,
                    taxRegionId: 4017409,
                    taxCalculated: 0.25,
                    taxOverride: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    taxableUnits: 100,
                    nonTaxableUnits: 0,
                    exemptUnits: 0,
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0.25,
                    reportingTaxCalculated: 0.25,
                    liabilityType: 'Seller',
                },
                {
                    id: 85000337103339,
                    transactionLineId: **************,
                    transactionId: **************,
                    addressId: **************,
                    country: 'US',
                    region: 'CA',
                    countyFIPS: '',
                    stateFIPS: '',
                    exemptAmount: 0,
                    exemptReasonId: 4,
                    inState: true,
                    jurisCode: 'EMAZ0',
                    jurisName: 'ORANGE COUNTY DISTRICT TAX SP',
                    jurisdictionId: 2001061425,
                    signatureCode: 'EMAZ',
                    stateAssignedNo: '037',
                    jurisType: 'STJ',
                    jurisdictionType: 'Special',
                    nonTaxableAmount: 0,
                    nonTaxableRuleId: 0,
                    nonTaxableType: 'RateRule',
                    rate: 0.005,
                    rateRuleId: 1526112,
                    rateSourceId: 3,
                    serCode: '',
                    sourcing: 'Destination',
                    tax: 0.5,
                    taxableAmount: 100,
                    taxType: 'Sales',
                    taxSubTypeId: 'S',
                    taxTypeGroupId: 'SalesAndUse',
                    taxName: 'CA SPECIAL TAX',
                    taxAuthorityTypeId: 45,
                    taxRegionId: 4017409,
                    taxCalculated: 0.5,
                    taxOverride: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    taxableUnits: 100,
                    nonTaxableUnits: 0,
                    exemptUnits: 0,
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0.5,
                    reportingTaxCalculated: 0.5,
                    liabilityType: 'Seller',
                },
                {
                    id: 85000337103340,
                    transactionLineId: **************,
                    transactionId: **************,
                    addressId: **************,
                    country: 'US',
                    region: 'CA',
                    countyFIPS: '',
                    stateFIPS: '',
                    exemptAmount: 0,
                    exemptReasonId: 4,
                    inState: true,
                    jurisCode: 'EMTN0',
                    jurisName: 'ORANGE CO LOCAL TAX SL',
                    jurisdictionId: 2001061784,
                    signatureCode: 'EMTN',
                    stateAssignedNo: '30',
                    jurisType: 'STJ',
                    jurisdictionType: 'Special',
                    nonTaxableAmount: 0,
                    nonTaxableRuleId: 0,
                    nonTaxableType: 'RateRule',
                    rate: 0.01,
                    rateRuleId: 1526108,
                    rateSourceId: 3,
                    serCode: '',
                    sourcing: 'Origin',
                    tax: 1,
                    taxableAmount: 100,
                    taxType: 'Sales',
                    taxSubTypeId: 'S',
                    taxTypeGroupId: 'SalesAndUse',
                    taxName: 'CA SPECIAL TAX',
                    taxAuthorityTypeId: 45,
                    taxRegionId: 4017409,
                    taxCalculated: 1,
                    taxOverride: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    taxableUnits: 100,
                    nonTaxableUnits: 0,
                    exemptUnits: 0,
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 1,
                    reportingTaxCalculated: 1,
                    liabilityType: 'Seller',
                },
            ],
            nonPassthroughDetails: [],
            lineLocationTypes: [
                {
                    documentLineLocationTypeId: 85000337103330,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'ShipFrom',
                },
                {
                    documentLineLocationTypeId: 85000337103331,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'ShipTo',
                },
                {
                    documentLineLocationTypeId: 85000337103332,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'PointOfOrderAcceptance',
                },
                {
                    documentLineLocationTypeId: 85000337103333,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'PointOfOrderOrigin',
                },
                {
                    documentLineLocationTypeId: 85000337103334,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'GoodsPlaceOrServiceRendered',
                },
                {
                    documentLineLocationTypeId: 85000337103335,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'Import',
                },
                {
                    documentLineLocationTypeId: 85000337103336,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'BillTo',
                },
            ],
            hsCode: '',
            costInsuranceFreight: 0,
            vatCode: '',
            vatNumberTypeId: 0,
        },
    ],
    addresses: [
        {
            id: **************,
            transactionId: **************,
            boundaryLevel: 'Address',
            line1: '2000 Main St',
            line2: '',
            line3: '',
            city: 'Irvine',
            region: 'CA',
            postalCode: '92614-7202',
            country: 'US',
            taxRegionId: 4017409,
            latitude: '33.684716',
            longitude: '-117.851489',
        },
    ],
    locationTypes: [
        {
            documentLocationTypeId: 85000337103321,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'ShipFrom',
        },
        {
            documentLocationTypeId: 85000337103322,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'ShipTo',
        },
        {
            documentLocationTypeId: 85000337103323,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'PointOfOrderAcceptance',
        },
        {
            documentLocationTypeId: 85000337103324,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'PointOfOrderOrigin',
        },
        {
            documentLocationTypeId: 85000337103325,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'GoodsPlaceOrServiceRendered',
        },
        {
            documentLocationTypeId: 85000337103326,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'Import',
        },
        {
            documentLocationTypeId: 85000337103327,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'BillTo',
        },
    ],
    summary: [
        {
            country: 'US',
            region: 'CA',
            jurisType: 'State',
            jurisCode: '06',
            jurisName: 'CALIFORNIA',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Sales',
            taxSubType: 'S',
            taxName: 'CA STATE TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.06,
            tax: 6,
            taxCalculated: 6,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'County',
            jurisCode: '059',
            jurisName: 'ORANGE',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Sales',
            taxSubType: 'S',
            taxName: 'CA COUNTY TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.0025,
            tax: 0.25,
            taxCalculated: 0.25,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'Special',
            jurisCode: 'EMTN0',
            jurisName: 'ORANGE CO LOCAL TAX SL',
            taxAuthorityType: 45,
            stateAssignedNo: '30',
            taxType: 'Sales',
            taxSubType: 'S',
            taxName: 'CA SPECIAL TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.01,
            tax: 1,
            taxCalculated: 1,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'Special',
            jurisCode: 'EMAZ0',
            jurisName: 'ORANGE COUNTY DISTRICT TAX SP',
            taxAuthorityType: 45,
            stateAssignedNo: '037',
            taxType: 'Sales',
            taxSubType: 'S',
            taxName: 'CA SPECIAL TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.005,
            tax: 0.5,
            taxCalculated: 0.5,
            nonTaxable: 0,
            exemption: 0,
        },
    ],
};
export const transactionResponseInventoryTransferOrder: xtremAvalaraGateway.interfaces.TransactionResponse = {
    id: 0,
    code: '79588b6b-89b9-4a89-87a6-8851bc154437',
    companyId: 2751999,
    date: '2021-12-16',
    paymentDate: '2021-12-16',
    status: 'Temporary',
    type: 'InventoryTransferOrder',
    batchCode: '',
    currencyCode: 'USD',
    exchangeRateCurrencyCode: 'USD',
    customerUsageType: '',
    entityUseCode: '',
    customerVendorCode: 'ABC',
    customerCode: 'ABC',
    exemptNo: '',
    reconciled: false,
    locationCode: '',
    reportingLocationCode: '',
    purchaseOrderNo: '2021-12-13-001',
    referenceCode: '',
    salespersonCode: '',
    totalAmount: 100,
    totalExempt: 0,
    totalDiscount: 0,
    totalTax: 7.75,
    totalTaxable: 100,
    totalTaxCalculated: 7.75,
    adjustmentReason: 'NotAdjusted',
    locked: false,
    version: 1,
    exchangeRateEffectiveDate: '2021-12-16',
    exchangeRate: 1,
    description: 'Yarn',
    modifiedDate: '2022-03-22T16:54:13.625337Z',
    modifiedUserId: 1444646,
    taxDate: '2021-12-16',
    lines: [
        {
            id: 0,
            transactionId: 0,
            lineNumber: '1',
            customerUsageType: '',
            entityUseCode: '',
            description: 'Yarn',
            discountAmount: 0,
            exemptAmount: 0,
            exemptCertId: 0,
            exemptNo: '',
            isItemTaxable: true,
            itemCode: 'Y0001',
            lineAmount: 100,
            quantity: 1,
            ref1: '',
            ref2: '',
            reportingDate: '2021-12-16',
            tax: 7.75,
            taxableAmount: 100,
            taxCalculated: 7.75,
            taxCode: 'PS081282',
            taxCodeId: 38007,
            taxDate: '2021-12-16',
            taxIncluded: false,
            details: [
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: '06',
                    jurisName: 'CALIFORNIA',
                    stateAssignedNo: '',
                    jurisType: 'STA',
                    jurisdictionType: 'State',
                    nonTaxableAmount: 0,
                    rate: 0.06,
                    tax: 6,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxName: 'CA STATE TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 6,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 6,
                    reportingTaxCalculated: 6,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: '059',
                    jurisName: 'ORANGE',
                    stateAssignedNo: '',
                    jurisType: 'CTY',
                    jurisdictionType: 'County',
                    nonTaxableAmount: 0,
                    rate: 0.0025,
                    tax: 0.25,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxName: 'CA COUNTY TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 0.25,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0.25,
                    reportingTaxCalculated: 0.25,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: 'EMAZ0',
                    jurisName: 'ORANGE COUNTY DISTRICT TAX SP',
                    stateAssignedNo: '037',
                    jurisType: 'STJ',
                    jurisdictionType: 'Special',
                    nonTaxableAmount: 0,
                    rate: 0.005,
                    tax: 0.5,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxName: 'CA SPECIAL TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 0.5,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0.5,
                    reportingTaxCalculated: 0.5,
                    liabilityType: 'Seller',
                },
                {
                    id: 0,
                    transactionLineId: 0,
                    transactionId: 0,
                    country: 'US',
                    region: 'CA',
                    exemptAmount: 0,
                    jurisCode: 'EMTN0',
                    jurisName: 'ORANGE CO LOCAL TAX SL',
                    stateAssignedNo: '30',
                    jurisType: 'STJ',
                    jurisdictionType: 'Special',
                    nonTaxableAmount: 0,
                    rate: 0.01,
                    tax: 1,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxName: 'CA SPECIAL TAX',
                    taxAuthorityTypeId: 45,
                    taxCalculated: 1,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 1,
                    reportingTaxCalculated: 1,
                    liabilityType: 'Seller',
                },
            ],
            nonPassthroughDetails: [],
            hsCode: '',
            costInsuranceFreight: 0,
            vatCode: '',
            vatNumberTypeId: 0,
        },
    ],
    addresses: [
        {
            id: 0,
            transactionId: 0,
            boundaryLevel: 'Address',
            line1: '2000 Main Street',
            line2: '',
            line3: '',
            city: 'Irvine',
            region: 'CA',
            postalCode: '92614',
            country: 'US',
            taxRegionId: 4017409,
            latitude: '33.684716',
            longitude: '-117.851489',
        },
    ],
    summary: [
        {
            country: 'US',
            region: 'CA',
            jurisType: 'State',
            jurisCode: '06',
            jurisName: 'CALIFORNIA',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA STATE TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.06,
            tax: 6,
            taxCalculated: 6,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'County',
            jurisCode: '059',
            jurisName: 'ORANGE',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA COUNTY TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.0025,
            tax: 0.25,
            taxCalculated: 0.25,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'Special',
            jurisCode: 'EMTN0',
            jurisName: 'ORANGE CO LOCAL TAX SL',
            taxAuthorityType: 45,
            stateAssignedNo: '30',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA SPECIAL TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.01,
            tax: 1,
            taxCalculated: 1,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'Special',
            jurisCode: 'EMAZ0',
            jurisName: 'ORANGE COUNTY DISTRICT TAX SP',
            taxAuthorityType: 45,
            stateAssignedNo: '037',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA SPECIAL TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.005,
            tax: 0.5,
            taxCalculated: 0.5,
            nonTaxable: 0,
            exemption: 0,
        },
    ],
};

export const transactionResponseInventoryTransferInvoice: xtremAvalaraGateway.interfaces.TransactionResponse = {
    id: **************,
    code: '8855ec3a-7f8c-48c5-8b13-7348ec9324b5',
    companyId: 2751999,
    date: '2021-12-16',
    status: 'Committed',
    type: 'InventoryTransferInvoice',
    batchCode: '',
    currencyCode: 'USD',
    exchangeRateCurrencyCode: 'USD',
    customerUsageType: '',
    entityUseCode: '',
    customerVendorCode: 'ABC',
    customerCode: 'ABC',
    exemptNo: '',
    reconciled: false,
    locationCode: '',
    reportingLocationCode: '',
    purchaseOrderNo: '2021-12-13-001',
    referenceCode: '',
    salespersonCode: '',
    taxOverrideType: 'None',
    taxOverrideAmount: 0,
    taxOverrideReason: '',
    totalAmount: 100,
    totalExempt: 0,
    totalDiscount: 0,
    totalTax: 7.75,
    totalTaxable: 100,
    totalTaxCalculated: 7.75,
    adjustmentReason: 'NotAdjusted',
    adjustmentDescription: '',
    locked: false,
    region: 'CA',
    country: 'US',
    version: 1,
    softwareVersion: '********',
    originAddressId: **************,
    destinationAddressId: **************,
    exchangeRateEffectiveDate: '2021-12-16',
    exchangeRate: 1,
    description: '',
    email: '',
    businessIdentificationNo: '',
    modifiedDate: '2022-03-22T17:25:06.970494Z',
    modifiedUserId: 1444646,
    taxDate: '2021-12-16',
    lines: [
        {
            id: **************,
            transactionId: **************,
            lineNumber: '1',
            boundaryOverrideId: 0,
            customerUsageType: '',
            entityUseCode: '',
            description: 'Yarn',
            destinationAddressId: **************,
            originAddressId: **************,
            discountAmount: 0,
            discountTypeId: 0,
            exemptAmount: 0,
            exemptCertId: 0,
            exemptNo: '',
            isItemTaxable: true,
            isSSTP: false,
            itemCode: 'Y0001',
            lineAmount: 100,
            quantity: 1,
            ref1: '',
            ref2: '',
            reportingDate: '2021-12-16',
            revAccount: '',
            sourcing: 'Mixed',
            tax: 7.75,
            taxableAmount: 100,
            taxCalculated: 7.75,
            taxCode: 'PS081282',
            taxCodeId: 38007,
            taxDate: '2021-12-16',
            taxEngine: '',
            taxOverrideType: 'None',
            businessIdentificationNo: '',
            taxOverrideAmount: 0,
            taxOverrideReason: '',
            taxIncluded: false,
            details: [
                {
                    id: **************,
                    transactionLineId: **************,
                    transactionId: **************,
                    addressId: **************,
                    country: 'US',
                    region: 'CA',
                    countyFIPS: '',
                    stateFIPS: '',
                    exemptAmount: 0,
                    exemptReasonId: 4,
                    inState: true,
                    jurisCode: '06',
                    jurisName: 'CALIFORNIA',
                    jurisdictionId: 5000531,
                    signatureCode: 'AGAM',
                    stateAssignedNo: '',
                    jurisType: 'STA',
                    jurisdictionType: 'State',
                    nonTaxableAmount: 0,
                    nonTaxableRuleId: 0,
                    nonTaxableType: 'RateRule',
                    rate: 0.06,
                    rateRuleId: 1526144,
                    rateSourceId: 3,
                    serCode: '',
                    sourcing: 'Origin',
                    tax: 6,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxTypeGroupId: 'SalesAndUse',
                    taxName: 'CA STATE TAX',
                    taxAuthorityTypeId: 45,
                    taxRegionId: 4017409,
                    taxCalculated: 6,
                    taxOverride: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    taxableUnits: 100,
                    nonTaxableUnits: 0,
                    exemptUnits: 0,
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 6,
                    reportingTaxCalculated: 6,
                    liabilityType: 'Seller',
                },
                {
                    id: 85000338868863,
                    transactionLineId: **************,
                    transactionId: **************,
                    addressId: **************,
                    country: 'US',
                    region: 'CA',
                    countyFIPS: '',
                    stateFIPS: '',
                    exemptAmount: 0,
                    exemptReasonId: 4,
                    inState: true,
                    jurisCode: '059',
                    jurisName: 'ORANGE',
                    jurisdictionId: 267,
                    signatureCode: 'AHXU',
                    stateAssignedNo: '',
                    jurisType: 'CTY',
                    jurisdictionType: 'County',
                    nonTaxableAmount: 0,
                    nonTaxableRuleId: 0,
                    nonTaxableType: 'RateRule',
                    rate: 0.0025,
                    rateRuleId: 1526140,
                    rateSourceId: 3,
                    serCode: '',
                    sourcing: 'Origin',
                    tax: 0.25,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxTypeGroupId: 'SalesAndUse',
                    taxName: 'CA COUNTY TAX',
                    taxAuthorityTypeId: 45,
                    taxRegionId: 4017409,
                    taxCalculated: 0.25,
                    taxOverride: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    taxableUnits: 100,
                    nonTaxableUnits: 0,
                    exemptUnits: 0,
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0.25,
                    reportingTaxCalculated: 0.25,
                    liabilityType: 'Seller',
                },
                {
                    id: 85000338868864,
                    transactionLineId: **************,
                    transactionId: **************,
                    addressId: **************,
                    country: 'US',
                    region: 'CA',
                    countyFIPS: '',
                    stateFIPS: '',
                    exemptAmount: 0,
                    exemptReasonId: 4,
                    inState: true,
                    jurisCode: 'EMAZ0',
                    jurisName: 'ORANGE COUNTY DISTRICT TAX SP',
                    jurisdictionId: 2001061425,
                    signatureCode: 'EMAZ',
                    stateAssignedNo: '037',
                    jurisType: 'STJ',
                    jurisdictionType: 'Special',
                    nonTaxableAmount: 0,
                    nonTaxableRuleId: 0,
                    nonTaxableType: 'RateRule',
                    rate: 0.005,
                    rateRuleId: 1526110,
                    rateSourceId: 3,
                    serCode: '',
                    sourcing: 'Destination',
                    tax: 0.5,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxTypeGroupId: 'SalesAndUse',
                    taxName: 'CA SPECIAL TAX',
                    taxAuthorityTypeId: 45,
                    taxRegionId: 4017409,
                    taxCalculated: 0.5,
                    taxOverride: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    taxableUnits: 100,
                    nonTaxableUnits: 0,
                    exemptUnits: 0,
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 0.5,
                    reportingTaxCalculated: 0.5,
                    liabilityType: 'Seller',
                },
                {
                    id: 85000338868865,
                    transactionLineId: **************,
                    transactionId: **************,
                    addressId: **************,
                    country: 'US',
                    region: 'CA',
                    countyFIPS: '',
                    stateFIPS: '',
                    exemptAmount: 0,
                    exemptReasonId: 4,
                    inState: true,
                    jurisCode: 'EMTN0',
                    jurisName: 'ORANGE CO LOCAL TAX SL',
                    jurisdictionId: 2001061784,
                    signatureCode: 'EMTN',
                    stateAssignedNo: '30',
                    jurisType: 'STJ',
                    jurisdictionType: 'Special',
                    nonTaxableAmount: 0,
                    nonTaxableRuleId: 0,
                    nonTaxableType: 'RateRule',
                    rate: 0.01,
                    rateRuleId: 1526106,
                    rateSourceId: 3,
                    serCode: '',
                    sourcing: 'Origin',
                    tax: 1,
                    taxableAmount: 100,
                    taxType: 'Use',
                    taxSubTypeId: 'U',
                    taxTypeGroupId: 'SalesAndUse',
                    taxName: 'CA SPECIAL TAX',
                    taxAuthorityTypeId: 45,
                    taxRegionId: 4017409,
                    taxCalculated: 1,
                    taxOverride: 0,
                    rateType: 'General',
                    rateTypeCode: 'G',
                    taxableUnits: 100,
                    nonTaxableUnits: 0,
                    exemptUnits: 0,
                    unitOfBasis: 'PerCurrencyUnit',
                    isNonPassThru: false,
                    isFee: false,
                    reportingTaxableUnits: 100,
                    reportingNonTaxableUnits: 0,
                    reportingExemptUnits: 0,
                    reportingTax: 1,
                    reportingTaxCalculated: 1,
                    liabilityType: 'Seller',
                },
            ],
            nonPassthroughDetails: [],
            lineLocationTypes: [
                {
                    documentLineLocationTypeId: 85000338868855,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'ShipFrom',
                },
                {
                    documentLineLocationTypeId: 85000338868856,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'ShipTo',
                },
                {
                    documentLineLocationTypeId: 85000338868857,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'PointOfOrderAcceptance',
                },
                {
                    documentLineLocationTypeId: 85000338868858,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'PointOfOrderOrigin',
                },
                {
                    documentLineLocationTypeId: 85000338868859,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'GoodsPlaceOrServiceRendered',
                },
                {
                    documentLineLocationTypeId: 85000338868860,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'Import',
                },
                {
                    documentLineLocationTypeId: 85000338868861,
                    documentLineId: **************,
                    documentAddressId: **************,
                    locationTypeCode: 'BillTo',
                },
            ],
            hsCode: '',
            costInsuranceFreight: 0,
            vatCode: '',
            vatNumberTypeId: 0,
        },
    ],
    addresses: [
        {
            id: **************,
            transactionId: **************,
            boundaryLevel: 'Address',
            line1: '2000 Main St',
            line2: '',
            line3: '',
            city: 'Irvine',
            region: 'CA',
            postalCode: '92614-7202',
            country: 'US',
            taxRegionId: 4017409,
            latitude: '33.684716',
            longitude: '-117.851489',
        },
    ],
    locationTypes: [
        {
            documentLocationTypeId: 85000338868846,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'ShipFrom',
        },
        {
            documentLocationTypeId: 85000338868847,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'ShipTo',
        },
        {
            documentLocationTypeId: 85000338868848,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'PointOfOrderAcceptance',
        },
        {
            documentLocationTypeId: 85000338868849,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'PointOfOrderOrigin',
        },
        {
            documentLocationTypeId: 85000338868850,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'GoodsPlaceOrServiceRendered',
        },
        {
            documentLocationTypeId: 85000338868851,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'Import',
        },
        {
            documentLocationTypeId: 85000338868852,
            documentId: **************,
            documentAddressId: **************,
            locationTypeCode: 'BillTo',
        },
    ],
    summary: [
        {
            country: 'US',
            region: 'CA',
            jurisType: 'State',
            jurisCode: '06',
            jurisName: 'CALIFORNIA',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA STATE TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.06,
            tax: 6,
            taxCalculated: 6,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'County',
            jurisCode: '059',
            jurisName: 'ORANGE',
            taxAuthorityType: 45,
            stateAssignedNo: '',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA COUNTY TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.0025,
            tax: 0.25,
            taxCalculated: 0.25,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'Special',
            jurisCode: 'EMTN0',
            jurisName: 'ORANGE CO LOCAL TAX SL',
            taxAuthorityType: 45,
            stateAssignedNo: '30',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA SPECIAL TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.01,
            tax: 1,
            taxCalculated: 1,
            nonTaxable: 0,
            exemption: 0,
        },
        {
            country: 'US',
            region: 'CA',
            jurisType: 'Special',
            jurisCode: 'EMAZ0',
            jurisName: 'ORANGE COUNTY DISTRICT TAX SP',
            taxAuthorityType: 45,
            stateAssignedNo: '037',
            taxType: 'Use',
            taxSubType: 'U',
            taxName: 'CA SPECIAL TAX',
            rateType: 'General',
            taxable: 100,
            rate: 0.005,
            tax: 0.5,
            taxCalculated: 0.5,
            nonTaxable: 0,
            exemption: 0,
        },
    ],
};
