import { Plugin } from '@sage/xtrem-core';
import { tryJsonParse } from '@sage/xtrem-shared';
import { assert } from 'chai';
import * as fs from 'fs';
import * as fsp from 'path';
import type * as xtremAvalaraGateway from '../../index';

interface MockStructure {
    request: xtremAvalaraGateway.interfaces.RestCallOptions;
    response: any;
}

export class AvataxPlugin extends Plugin {
    public file: MockStructure;

    private fileName: string;

    /**
     * Entries array of request / response
     */
    private entries: MockStructure[];

    constructor(
        public override readonly path: string = '',
        public override readonly scenario: string = '',
    ) {
        super('avatax', path, scenario);
        this.path = this.path.replace(`${fsp.sep}build${fsp.sep}`, fsp.sep);
    }

    /**
     *  Compare the acutal & the expected RestCall Option Request
     * @param actual
     * @param expected
     */
    private static compareRequest(
        actual: xtremAvalaraGateway.interfaces.RestCallOptions,
        expected: xtremAvalaraGateway.interfaces.RestCallOptions,
    ): boolean {
        return (
            actual.url === expected.url &&
            actual.verb === expected.verb &&
            String(actual.payload).replace(/[\s]/g, '') === String(expected.payload).replace(/[\s]/g, '')
        );
    }

    /**
     * populate this.entries if not already done
     */
    private readMockFile(): void {
        if (this.entries == null) {
            this.fileName = `${fsp.join(this.path, this.name, this.scenario)}.json`;
            assert.isTrue(fs.existsSync(this.fileName), `Couldn't find mandatory '${this.fileName}' file.`);
            const file = fs.readFileSync(this.fileName, 'utf8');
            assert.isDefined(file, `Couldn't read '${this.fileName}'.`);
            const data = tryJsonParse(file);
            assert.exists(data, `'${this.fileName}' is not a valid JSON file.`);
            this.entries = !Array.isArray(data) ? [data] : data;
        }
    }

    override mock(): any {
        return {
            isMocked: true,
            restCall: (options: xtremAvalaraGateway.interfaces.RestCallOptions) => {
                this.readMockFile();
                assert.isNotNull(this.entries);

                const result = this.entries.find((entrie: MockStructure) => {
                    assert.exists(entrie.request, `'${this.fileName}' must contain a 'request' key'.`);
                    assert.exists(entrie.response, `'${this.fileName}' must contain a 'response' key'.`);

                    return AvataxPlugin.compareRequest(entrie.request, options);
                })?.response;

                assert.isNotNull(result, `no result for ${options.verb} ${options.url}`);

                return new Promise(resolve => {
                    resolve(result);
                });
            },
        };
    }
}
