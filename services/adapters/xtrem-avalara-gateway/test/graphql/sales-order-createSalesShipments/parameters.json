{"Create sales shipment using mutation": {"input": {"arrayProperties": {"salesDocumentLines": [{"salesDocumentLine": "_id:604", "quantityToProcess": 12}, {"salesDocumentLine": "_id:605", "quantityToProcess": 10}, {"salesDocumentLine": "_id:623", "quantityToProcess": 10}]}}, "output": {"createSalesShipmentsFromOrderLines": {"status": "salesOrderIsShipped", "numberOfShipments": 2, "lineErrors": []}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"], "today": "2021-01-01"}}, "Create sales shipment using mutation empty array": {"input": {"arrayProperties": {"salesDocumentLines": []}}, "output": {"createSalesShipmentsFromOrderLines": {"status": "parametersAreIncorrect", "numberOfShipments": 0, "lineErrors": []}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"], "today": "2020-11-27"}}}