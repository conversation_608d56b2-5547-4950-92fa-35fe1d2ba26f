{"Tax calculation sales invoice fail when status is not pending": {"input": {"invoiceId": "#SI2"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The status is Posted, the tax cannot be calculated for this invoice.", "path": [], "severity": 4}]}, "message": "Calculate tax failed.", "locations": [{"line": 4, "column": 7}], "path": ["xtremSales", "salesInvoice", "calculateTax"]}], "data": {"xtremSales": {"salesInvoice": {"calculateTax": null}}}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"]}}, "Tax calculation sales invoice fail wrong tax calculation status": {"input": {"invoiceId": "#SI8"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The tax calculation status is in progress, the tax cannot be calculated for this invoice.", "path": [], "severity": 4}]}, "message": "Calculate tax failed.", "locations": [{"line": 4, "column": 7}], "path": ["xtremSales", "salesInvoice", "calculateTax"]}], "data": {"xtremSales": {"salesInvoice": {"calculateTax": null}}}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"]}}}