{
    xtremAvalaraGateway {
        mapCompany {
            query(filter: "{_id:1}") {
                edges {
                    node {
                        isActive
                        entityType
                        company {
                            id
                            name
                        }
                        financialSite {
                            id
                            name
                        }
                        avalaraCompany {
                            id
                            name
                        }
                    }
                }
            }
        }
    }
}
