{"Tax calculation sales credit memo fail when status is not pending": {"input": {"creditMemoId": "#SCE1210006"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The status is Posted, the tax cannot be calculated for this credit memo.", "path": [], "severity": 4}]}, "message": "Calculate tax failed.", "locations": [{"line": 4, "column": 7}], "path": ["xtremSales", "salesCreditMemo", "calculateTax"]}], "data": {"xtremSales": {"salesCreditMemo": {"calculateTax": null}}}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"]}}, "Tax calculation sales credit memo fail wrong tax calculation status": {"input": {"creditMemoId": "#SCE1210005"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The tax calculation status is in progress, the tax cannot be calculated for this credit memo.", "path": [], "severity": 4}]}, "message": "Calculate tax failed.", "locations": [{"line": 4, "column": 7}], "path": ["xtremSales", "salesCreditMemo", "calculateTax"]}], "data": {"xtremSales": {"salesCreditMemo": {"calculateTax": null}}}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"]}}}