{"Create sales order with standard discount": {"input": {"properties": {"shippingStatus": "notShipped", "site": "#US001", "shipToCustomer": "#US019", "soldToCustomer": "#US019", "incoterm": "#EXW", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "#US019|1500", "deliveryLeadTime": 1, "currency": "#ZAR", "doNotShipAfterDate": "2020-11-30", "doNotShipBeforeDate": "2020-11-28", "requestedDeliveryDate": "2020-11-28", "date": "2020-11-26", "paymentTerm": "#DUE_UPON_RECEIPT_ALL", "entityUse": 12, "lines": [{"shipToCustomerAddress": "#US019|1500", "item": "#17891", "unit": "#LITER", "quantity": 600, "discount": 3}]}}, "output": {"create": {"date": "2020-11-26", "status": "quote", "entityUse": {"name": "OTHER/CUSTOM"}, "lines": {"query": {"edges": [{"node": {"status": "quote", "entityUse": {"name": "OTHER/CUSTOM"}}}]}}}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"]}}, "Create sales order without entity use": {"input": {"properties": {"shippingStatus": "notShipped", "site": "#US001", "shipToCustomer": "#US019", "soldToCustomer": "#US019", "incoterm": "#EXW", "shipToCustomerAddress": "#US019|1500", "billToLinkedAddress": "#US019|1500", "deliveryLeadTime": 1, "currency": "#ZAR", "doNotShipAfterDate": "2020-11-30", "doNotShipBeforeDate": "2020-11-28", "requestedDeliveryDate": "2020-11-28", "date": "2020-11-26", "paymentTerm": "#DUE_UPON_RECEIPT_ALL", "lines": [{"shipToCustomerAddress": "#US019|1500", "item": "#17891", "unit": "#LITER", "quantity": 600, "discount": 3}]}}, "output": {"create": {"date": "2020-11-26", "status": "quote", "entityUse": {"name": "FEDERAL GOV"}, "lines": {"query": {"edges": [{"node": {"status": "quote", "entityUse": {"name": "FEDERAL GOV"}}}]}}}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"]}}}