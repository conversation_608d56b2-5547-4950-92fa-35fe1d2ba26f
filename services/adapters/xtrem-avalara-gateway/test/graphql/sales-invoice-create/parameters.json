{"Create sales invoice": {"input": {"properties": {"site": "#US001", "incoterm": "#EXW", "billToCustomer": "#US019", "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "country": "US", "exemptAmount": 28, "region": "US", "jurisdictionName": "US", "jurisdictionCode": "US", "jurisdictionType": "US", "taxAmountAdjusted": 33, "taxableAmount": 37}], "lines": [{"item": "#SalesItem81", "quantity": 20, "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "netPriceExcludingTax": 2.4, "netPriceIncludingTax": 2.4, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "country": "US", "exemptAmount": 28, "region": "US", "jurisdictionName": "US", "jurisdictionCode": "US", "jurisdictionType": "US", "taxAmountAdjusted": 33, "numberOfTaxableUnits": "2", "numberOfNonTaxableUnits": "3", "numberOfExemptUnits": "4", "taxableAmount": 37}], "entityUse": 1}, {"item": "#SalesItem81", "quantity": 20, "grossPrice": 2, "discountCharges": [{"sign": "increase", "valueType": "percentage", "calculationBasis": "grossPrice", "basis": 2, "value": 20}], "providerSiteAddress": {"name": "test"}, "consumptionAddress": {"name": "test"}, "taxes": [{"taxCategory": "test", "tax": "test", "nonTaxableAmount": 20, "taxRate": 22.2, "taxAmount": 37, "country": "US", "exemptAmount": 28, "region": "US", "jurisdictionName": "US", "jurisdictionCode": "US", "jurisdictionType": "US", "taxAmountAdjusted": 33, "numberOfTaxableUnits": "2", "numberOfNonTaxableUnits": "3", "numberOfExemptUnits": "4", "taxableAmount": 37}], "entityUse": 1}]}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"], "today": "2021-04-22"}, "output": {"create": {"number": "SITE1210001", "status": "draft", "invoiceDate": "2021-04-22", "isPrinted": false, "site": {"businessEntity": {"taxIdNumber": "***********"}}, "salesSiteName": "Chem. Atlanta", "salesSiteTaxIdNumber": "***********", "salesSiteLinkedAddress": {"name": "US001"}, "salesSiteAddress": {"name": "US001"}, "salesSiteContact": null, "totalTaxAmountAdjusted": "0", "totalDiscountAmount": "0", "totalAmountExcludingTax": "96", "totalAmountIncludingTax": "96", "paymentTerm": {"description": ""}, "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "37", "exemptAmount": "28", "country": "US", "region": "US", "jurisdictionName": "US", "jurisdictionCode": "US", "jurisdictionType": "US", "taxAmountAdjusted": "33", "taxableAmount": "37"}}]}}, "lines": {"query": {"edges": [{"node": {"amountIncludingTax": "81", "taxAmountAdjusted": "33", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "37", "exemptAmount": "28", "country": "US", "region": "US", "jurisdictionName": "US", "jurisdictionCode": "US", "jurisdictionType": "US", "taxAmountAdjusted": "33", "numberOfTaxableUnits": "2", "numberOfNonTaxableUnits": "3", "numberOfExemptUnits": "4", "taxableAmount": "37"}}]}}, "entityUse": {"name": "FEDERAL GOV"}}}, {"node": {"amountIncludingTax": "81", "taxAmountAdjusted": "33", "taxes": {"query": {"edges": [{"node": {"taxCategory": "test", "tax": "test", "nonTaxableAmount": "20", "taxRate": "22.2", "taxAmount": "37", "exemptAmount": "28", "country": "US", "region": "US", "jurisdictionName": "US", "jurisdictionCode": "US", "jurisdictionType": "US", "taxAmountAdjusted": "33", "numberOfTaxableUnits": "2", "numberOfNonTaxableUnits": "3", "numberOfExemptUnits": "4", "taxableAmount": "37"}}]}}, "entityUse": {"name": "FEDERAL GOV"}}}]}}, "billToLinkedAddress": {"addressLine2": ""}, "billToContact": {"preferredName": "<PERSON>"}, "billToAddress": {"region": "AZ"}, "billToCustomer": {"businessEntity": {"name": "Dépot de TOULOUSE - Sud Ouest"}}, "billToCustomerName": "Dépot de TOULOUSE - Sud Ouest", "billToCustomerTaxIdNumber": "***********", "currency": {"symbol": "$"}}}}}