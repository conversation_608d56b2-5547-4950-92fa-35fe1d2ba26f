mutation {
    xtremSales {
        salesInvoice {
            create(data: {{inputParameters}}) {
                number
                status
                invoiceDate
                isPrinted
                site {
                    businessEntity {
                        taxIdNumber
                    }
                }
                salesSiteName
                salesSiteTaxIdNumber
                salesSiteLinkedAddress {
                    name
                }
                salesSiteAddress {
                    name
                }
                salesSiteContact {
                    firstName
                }
                billToCustomer {
                    businessEntity{
                         name
                    }
                }
                billToCustomerName
                billToCustomerTaxIdNumber
                billToLinkedAddress {
                    addressLine2
                }
                billToAddress {
                    region
                }
                billToContact {
                    preferredName
                }
                currency {
                    symbol
                }
                paymentTerm {
                    description
                }
                taxes {
                    query {
                        edges {
                            node {
                                taxCategory
                                tax
                                nonTaxableAmount
                                taxRate
                                taxAmount
                                exemptAmount
                                country
                                region
                                jurisdictionName
                                jurisdictionCode
                                jurisdictionType
                                taxAmountAdjusted
                                taxableAmount
                            }
                        }
                    }
                }
                lines {
                    query {
                        edges {
                            node {
                                amountIncludingTax
                                taxAmountAdjusted
                                taxes {
                                    query {
                                        edges {
                                            node {
                                                taxCategory
                                                tax
                                                nonTaxableAmount
                                                taxRate
                                                taxAmount
                                                exemptAmount
                                                country
                                                region
                                                jurisdictionName
                                                jurisdictionCode
                                                jurisdictionType
                                                taxAmountAdjusted
                                                numberOfTaxableUnits
                                                numberOfNonTaxableUnits
                                                numberOfExemptUnits
                                                taxableAmount
                                            }
                                        }
                                    }
                                }
                                entityUse {
                                    name
                                }
                            }
                        }
                    }
                }
                totalAmountExcludingTax
                totalAmountIncludingTax
                totalTaxAmountAdjusted
                totalDiscountAmount
            }
        }
    }
}
