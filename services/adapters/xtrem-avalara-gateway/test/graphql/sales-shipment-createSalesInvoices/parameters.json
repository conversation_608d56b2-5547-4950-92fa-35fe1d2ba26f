{"Create sales shipment using mutation": {"input": {"arrayProperties": {"salesDocumentLines": [{"salesDocumentLine": "_id:1300", "quantityToProcess": 10}, {"salesDocumentLine": "_id:1302", "quantityToProcess": 10}]}}, "output": {"createSalesInvoicesFromShipmentLines": {"status": "salesShipmentIsInvoiced", "numberOfInvoices": 2, "lineErrors": []}}, "envConfigs": {"today": "2020-11-27", "testActiveServiceOptions": ["avalaraOption"]}}, "Create sales shipment using mutation empty array": {"input": {"arrayProperties": {"salesDocumentLines": []}}, "output": {"createSalesInvoicesFromShipmentLines": {"status": "parametersAreIncorrect", "numberOfInvoices": 0, "lineErrors": []}}, "envConfigs": {"today": "2020-11-27", "testActiveServiceOptions": ["avalaraOption"]}}}