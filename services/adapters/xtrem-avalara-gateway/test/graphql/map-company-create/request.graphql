mutation {
    xtremAvalaraGateway {
        mapCompany {
            create(
                data: {
                    avalaraCompany: "6"
                    financialSite: "#US001"
                    entityType: "financialSite"
                    avalaraConfiguration: "#AVALARA"
                }
            ) {
                isActive
                entityType
                financialSite {
                    id
                    name
                }
                avalaraCompany {
                    id
                    name
                }
                avalaraConfiguration {
                    id
                }
            }
        }
    }
}
