{"Create map company fail financial site mandatory when entity type is financial site": {"input": {"properties": {"entityType": "financialSite", "avalaraCompany": "15", "avalaraConfiguration": "#AVALARA"}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"], "today": "2021-11-23"}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremAvalaraGateway", "mapCompany", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["financialSite"], "message": "The financial site is required."}]}}]}}, "Create map company fail company mandatory when entity type is company": {"input": {"properties": {"avalaraCompany": "14", "avalaraConfiguration": "#AVALARA"}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"], "today": "2021-11-23"}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremAvalaraGateway", "mapCompany", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["company"], "message": "The company is required."}]}}]}}, "Create map company fail financial site should not be passed when entity type is company": {"input": {"properties": {"financialSite": "#US009", "company": "9", "avalaraCompany": "13", "avalaraConfiguration": "#AVALARA"}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"], "today": "2021-11-23"}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremAvalaraGateway", "mapCompany", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["financialSite"], "message": "The entity is a company. Leave the financial site empty."}]}}]}}, "Create map company fail company should not be passed when entity type is financial site": {"input": {"properties": {"financialSite": "#US011", "entityType": "financialSite", "avalaraCompany": "6", "avalaraConfiguration": "#AVALARA", "company": "11"}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"], "today": "2021-11-23"}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremAvalaraGateway", "mapCompany", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["company"], "message": "The entity is a financial site. Leave the company empty."}]}}]}}, "Create map company fail financial site already exists": {"input": {"properties": {"financialSite": "#US002", "entityType": "financialSite", "avalaraCompany": "11", "avalaraConfiguration": "#AVALARA"}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"], "today": "2021-11-23"}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremAvalaraGateway", "mapCompany", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["financialSite"], "message": "The financial site already exists."}]}}]}}, "Create map company fail company already exists": {"input": {"properties": {"company": "2", "avalaraCompany": "10", "avalaraConfiguration": "#AVALARA"}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"], "today": "2021-11-23"}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremAvalaraGateway", "mapCompany", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["company"], "message": "The company already exists."}]}}]}}, "Create map company fail avalara company already exists": {"input": {"properties": {"company": "8", "avalaraCompany": "2", "avalaraConfiguration": "#AVALARA"}}, "envConfigs": {"testActiveServiceOptions": ["avalaraOption"], "today": "2021-11-23"}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremAvalaraGateway", "mapCompany", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["avalaraCompany"], "message": "The avalara company already exists."}]}}]}}}