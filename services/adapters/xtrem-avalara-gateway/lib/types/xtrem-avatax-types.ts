declare module 'avatax' {
    // TODO Migrate interfaces into interface folder
    interface AvataxConfig {
        appName: any;
        appVersion: any;
        environment: any;
        machineName: any;
    }

    interface AvataxCredentials {
        username?: string;
        password?: string;
        accountId?: string;
        licenseKey?: string;
        bearerToken?: string;
    }

    interface AvataxTestConnection {
        version: string;
        authenticated: boolean;
    }

    interface RestCallOptions {
        url: string;
        verb: 'post' | 'get' | 'delete';
        payload: any;
    }
    interface TransactionRequest {
        code?: string;
        lines: LineItemModel[];
        type?: string;
        companyCode?: string;
        date: string;
        salespersonCode?: string;
        customerCode?: string;
        customerUsageType?: string;
        entityUseCode?: string;
        discount?: number;
        purchaseOrderNo?: string;
        exemptionNo?: string;
        addresses?: AddressesModel;
        parameters?: TransactionParameterModel;
        userDefinedFields?: TransactionUserDefinedFieldModel[];
        referenceCode?: string;
        reportingLocationCode?: string;
        commit?: boolean;
        batchCode?: string;
        taxOverride?: TaxOverrideModel;
        currencyCode?: string;
        serviceMode?: string;
        exchangeRate?: number;
        exchangeRateEffectiveDate?: string;
        exchangeRateCurrencyCode?: string;
        posLaneCode?: string;
        businessIdentificationNo?: string;
        isSellerImporterOfRecord?: boolean;
        description?: string;
        email?: string;
        debugLevel?: string;
        customerSupplierName?: string;
        dataSourceId?: number;
        deliveryTerms?: string;
    }

    interface LineItemModel {
        number?: string;
        quantity?: number;
        amount: number;
        addresses?: AddressesModel;
        taxCode?: string;
        customerUsageType?: string;
        entityUseCode?: string;
        itemCode?: string;
        exemptionCode?: string;
        discounted?: boolean;
        taxIncluded?: boolean;
        revenueAccount?: string;
        ref1?: string;
        ref2?: string;
        description?: string;
        businessIdentificationNo?: string;
        taxOverride?: TaxOverrideModel;
        parameters?: TransactionLineParameterModel;
        userDefinedFields?: TransactionLineUserDefinedFieldModel[];
        hsCode?: string;
        merchantSellerId?: string;
        merchantSellerIdentifier?: string;
        marketplaceLiabilityType?: string;
        originationDocumentId?: string;
        originationSite?: string;
        category?: string;
        summary?: string;
    }
    interface AddressesModel {
        singleLocation?: AddressLocationInfo;
        shipFrom?: AddressLocationInfo;
        shipTo?: AddressLocationInfo;
        pointOfOrderOrigin?: AddressLocationInfo;
        pointOfOrderAcceptance?: AddressLocationInfo;
        goodsPlaceOrServiceRendered?: AddressLocationInfo;
        import?: AddressLocationInfo;
    }

    interface AddressLocationInfo extends GeneralAddress, CoordinateInfo {
        locationCode?: string;
    }

    interface TransactionAddressModel extends GeneralAddress {
        id?: number;
        transactionId?: number;
        boundaryLevel?: string;
        taxRegionId?: number;
        latitude?: string;
        longitude?: string;
        jurisdictions?: JurisdictionModel[];
    }

    interface JurisdictionModel {
        code?: string;
        name?: string;
        type?: string;
        rate?: number;
        salesRate?: number;
        signatureCode?: string;
        region?: string;
        useRate?: number;
        city?: string;
        county?: string;
        country?: string;
        shortName?: string;
        stateFips?: string;
        countyFips?: string;
        placeFips?: string;
        id?: number;
        effectiveDate?: string;
        endDate?: string;
    }

    interface TaxOverrideModel {
        type?: string;
        taxAmount?: number;
        taxDate?: string;
        reason?: string;
        taxAmountByTaxTypes?: TransactionLineTaxAmountByTaxTypeModel[];
    }

    interface TransactionLocationTypeModel {
        documentLocationTypeId?: number;
        documentId?: number;
        documentAddressId?: number;
        locationTypeCode?: string;
    }

    interface TransactionResponse {
        id?: number;
        code?: string;
        companyId?: number;
        date?: string;
        type?: string;
        paymentDate?: string;
        customerCode?: string;
        status?: string;
        batchCode?: string;
        currencyCode?: string;
        exchangeRateCurrencyCode?: string;
        customerUsageType?: string;
        entityUseCode?: string;
        customerVendorCode?: string;
        exemptNo?: string;
        reconciled?: boolean;
        locationCode?: string;
        reportingLocationCode?: string;
        purchaseOrderNo?: string;
        referenceCode?: string;
        salespersonCode?: string;
        taxOverrideType?: string;
        taxOverrideAmount?: number;
        taxOverrideReason?: string;
        totalAmount?: number;
        totalExempt?: number;
        totalDiscount?: number;
        totalTax?: number;
        totalTaxable?: number;
        totalTaxCalculated?: number;
        adjustmentReason?: string;
        adjustmentDescription?: string;
        locked?: boolean;
        region?: string;
        country?: string;
        version?: number;
        softwareVersion?: string;
        originAddressId?: number;
        destinationAddressId?: number;
        exchangeRateEffectiveDate?: string;
        exchangeRate?: number;
        isSellerImporterOfRecord?: boolean;
        description?: string;
        email?: string;
        businessIdentificationNo?: string;
        modifiedDate?: string;
        modifiedUserId?: number;
        taxDate?: string;
        lines?: TransactionLineModel[];
        addresses?: TransactionAddressModel[];
        locationTypes?: TransactionLocationTypeModel[];
        summary?: TransactionSummary[];
        taxDetailsByTaxType?: TaxDetailsByTaxType[];
        parameters?: TransactionParameterModel;
        userDefinedFields?: TransactionUserDefinedFieldModel[];
        messages?: AvaTaxMessage[];
        invoiceMessages?: InvoiceMessageModel[];
        customerSupplierName?: string;
        dataSourceId?: number;
        deliveryTerms?: string;
    }

    interface TransactionUserDefinedFieldModel {
        name?: string;
        value?: string;
    }

    interface TransactionParameterModel {
        name?: string;
        value?: string;
        unit?: string;
    }

    interface TaxDetailsByTaxType {
        taxType?: string;
        totalTaxable?: number;
        totalExempt?: number;
        totalNonTaxable?: number;
        totalTax?: number;
        taxSubTypeDetails?: TaxDetailsByTaxSubType[];
    }

    interface TaxDetailsByTaxSubType {
        taxSubType?: string;
        totalTaxable?: number;
        totalExempt?: number;
        totalNonTaxable?: number;
        totalTax?: number;
    }

    interface TransactionLineModel {
        id?: number;
        transactionId?: number;
        lineNumber?: string;
        boundaryOverrideId?: number;
        customerUsageType?: string;
        entityUseCode?: string;
        description?: string;
        destinationAddressId?: number;
        originAddressId?: number;
        discountAmount?: number;
        discountTypeId?: number;
        exemptAmount?: number;
        exemptCertId?: number;
        certificateId?: string;
        exemptNo?: string;
        isItemTaxable?: boolean;
        isSSTP?: boolean;
        itemCode?: string;
        lineAmount?: number;
        quantity?: number;
        ref1?: string;
        ref2?: string;
        reportingDate?: string;
        revAccount?: string;
        sourcing?: string;
        tax?: number;
        taxableAmount?: number;
        taxCalculated?: number;
        taxCode?: string;
        taxCodeId?: number;
        taxDate?: string;
        taxEngine?: string;
        taxOverrideType?: string;
        businessIdentificationNo?: string;
        taxOverrideAmount?: number;
        taxOverrideReason?: string;
        taxIncluded?: boolean;
        merchantSellerId?: number;
        merchantSellerIdentifier?: string;
        marketplaceLiabilityType?: string;
        originationDocumentId?: string;
        originationSite?: string;
        details?: TransactionLineDetailModel[];
        nonPassthroughDetails?: TransactionLineDetailModel[];
        lineLocationTypes?: TransactionLineLocationTypeModel[];
        parameters?: TransactionLineParameterModel;
        userDefinedFields?: TransactionLineUserDefinedFieldModel[];
        hsCode?: string;
        costInsuranceFreight?: number;
        vatCode?: string;
        vatNumberTypeId?: number;
        taxAmountByTaxTypes?: TransactionLineTaxAmountByTaxTypeModel[];
        deemedSupplier?: string;
        category?: string;
        summary?: string;
    }

    interface TransactionLineTaxAmountByTaxTypeModel {
        taxTypeId?: string;
        taxAmount?: number;
    }

    interface TransactionLineUserDefinedFieldModel {
        name?: string;
        value?: string;
    }

    interface TransactionLineLocationTypeModel {
        documentLineLocationTypeId?: number;
        documentLineId?: number;
        documentAddressId?: number;
        locationTypeCode?: string;
    }

    interface TransactionLineParameterModel {
        name?: string;
        value?: string;
        unit?: string;
    }

    interface TransactionLineDetailModel {
        id?: number;
        transactionLineId?: number;
        transactionId?: number;
        addressId?: number;
        country?: string;
        region?: string;
        countyFIPS?: string;
        stateFIPS?: string;
        exemptReasonId?: number;
        inState?: boolean;
        jurisCode?: string;
        jurisName?: string;
        jurisdictionId?: number;
        signatureCode?: string;
        stateAssignedNo?: string;
        jurisType?: string;
        nonTaxableRuleId?: number;
        nonTaxableType?: string;
        rate?: number;
        rateRuleId?: number;
        rateSourceId?: number;
        serCode?: string;
        sourcing?: string;
        tax?: number;
        taxType?: string;
        taxSubTypeId?: string;
        taxTypeGroupId?: string;
        taxAuthorityTypeId?: number;
        taxRegionId?: number;
        taxOverride?: number;
        rateType?: string;
        rateTypeCode?: string;
        unitOfBasis?: string;
        isNonPassThru?: boolean;
        isFee?: boolean;
        reportingTaxableUnits?: number;
        reportingNonTaxableUnits?: number;
        reportingExemptUnits?: number;
        reportingTax?: number;
        reportingTaxCalculated?: number;
        liabilityType?: string;
        taxName?: string;
        taxCalculated?: number;
        exemptAmount?: number;
        jurisdictionType?: string;
        nonTaxableAmount?: number;
        taxableAmount?: number;
        taxableUnits?: number;
        nonTaxableUnits?: number;
        exemptUnits?: number;
    }

    interface TransactionSummary {
        country?: string;
        region?: string;
        jurisCode?: string;
        jurisName?: string;
        taxAuthorityType?: number;
        stateAssignedNo?: string;
        taxType?: string;
        taxSubType?: string;
        taxGroup?: string;
        rateType?: string;
        rateTypeCode?: string;
        rate?: number;
        tax?: number;
        taxName?: string;
        taxCalculated?: number;
        jurisType?: string;
        taxable?: number;
        nonTaxable?: number;
        exemption?: number;
    }

    interface InvoiceMessageModel {
        content?: string;
        lineNumbers?: [];
    }
    interface CreateTransactionParameters {
        include?: string;
        model: TransactionRequest;
    }

    interface ResolveAddressRequest extends GeneralAddress {
        textCase?: string; // Upper/Mixed
    }

    interface AddressResolutionModel {
        address?: AddressInfo;
        validatedAddresses?: ValidatedAddressInfo[];
        coordinates?: CoordinateInfo;
        taxAuthorities?: TaxAuthorityInfo[];
        messages?: AvaTaxMessage[];
    }

    interface TaxAuthorityInfo {
        avalaraId?: string;
        jurisdictionName?: string;
        jurisdictionType?: string;
        signatureCode?: string;
    }

    interface ValidatedAddressInfo extends AddressInfo {
        addressType?: string;
    }

    interface AddressInfo extends GeneralAddress, CoordinateInfo {}

    interface QueryCompaniesResponse {
        '@recordsetCount'?: number;
        value?: CompanyModel[];
        '@nextLink'?: string;
        pageKey?: string;
    }

    interface CompanyModel extends RecordInfo {
        id?: number;
        accountId?: number;
        parentCompanyId?: number;
        sstPid?: string;
        companyCode?: string;
        name?: string;
        isDefault?: boolean;
        defaultLocationId?: number;
        isActive?: boolean;
        taxpayerIdNumber?: string;
        IsFein?: boolean;
        hasProfile?: boolean;
        isReportingEntity?: boolean;
        sstEffectiveDate?: string;
        defaultCountry?: string;
        baseCurrencyCode?: string;
        roundingLevelId?: string;
        warningsEnabled?: boolean;
        isTest?: boolean;
        taxDependencyLevelId?: string;
        inProgress?: boolean;
        businessIdentificationNo?: string;
        contacts?: ContactModel[];
        items?: ItemModel[];
        locations?: LocationModel[];
        nexus?: NexusModel[];
        settings?: SettingModel[];
        taxCodes?: TaxCodeModel[];
        taxRules?: TaxRuleModel[];
        upcs?: UpcModel[];
        nonReportingChildCompanies?: CompanyModel[];
        exemptCerts?: EcmsModel[];
        mossId?: string;
        mossCountry?: string;
        parameters?: CompanyParameterDetailModel[];
        supplierandcustomers?: CustomerSupplierModel[];
    }

    interface CustomerSupplierModel {
        id?: number;
        companyId?: number;
        customerCode?: string;
    }

    interface CompanyParameterDetailModel {
        id?: number;
        companyId?: number;
        name?: string;
        value?: string;
    }

    interface EcmsModel extends RecordInfo {
        exemptCertId?: number;
        companyId?: number;
        customerCode?: string;
        customerName?: string;
        address1?: string;
        address2?: string;
        address3?: string;
        city?: string;
        region?: string;
        postalCode?: string;
        country?: string;
        exemptCertTypeId?: string;
        documentRefNo?: string;
        businessTypeId?: number;
        businessTypeOtherDescription?: string;
        exemptReasonId?: string;
        exemptReasonOtherDescription?: string;
        effectiveDate?: string;
        regionsApplicable?: string;
        exemptCertStatusId?: string;
        createdDate?: string;
        lastTransactionDate?: string;
        expiryDate?: string;
        countryIssued?: string;
        avaCertId?: string;
        exemptCertReviewStatusId?: string;
        details?: EcmsDetailModel[];
    }

    interface EcmsDetailModel {
        exemptCertDetailId?: number;
        exemptCertId?: number;
        stateFips?: string;
        region?: string;
        idNo?: string;
        country?: string;
        endDate?: string;
        idType?: string;
        isTaxCodeListExclusionList?: number;
        taxCodes?: EcmsDetailTaxCodeModel[];
    }

    interface EcmsDetailTaxCodeModel {
        exemptCertDetailTaxCodeId?: number;
        exemptCertDetailId?: number;
        taxCodeId?: number;
    }

    interface UpcModel extends RecordInfo {
        id?: number;
        companyId?: number;
        upc?: string;
        legacyTaxCode?: string;
        description?: string;
        effectiveDate?: string;
        endDate?: string;
        usage?: number;
        isSystem?: number;
    }

    interface TaxRuleModel extends RecordInfo {
        id?: number;
        companyId?: number;
        taxCodeId?: number;
        taxCode?: string;
        stateFIPS?: string;
        jurisName?: string;
        jurisCode?: string;
        jurisTypeId?: string;
        jurisdictionTypeId?: string;
        customerUsageType?: string;
        entityUseCode?: string;
        taxTypeId?: string;
        taxTypeCode?: string;
        taxRuleProductDetail?: TaxRuleProductDetailModel[];
        rateTypeId?: string;
        rateTypeCode?: string;
        taxRuleTypeId?: string;
        isAllJuris?: boolean;
        value?: number;
        cap?: number;
        threshold?: number;
        options?: string;
        effectiveDate?: string;
        endDate?: string;
        description?: string;
        countyFIPS?: string;
        isSTPro?: boolean;
        country?: string;
        region?: string;
        sourcing?: string;
        taxTypeGroup?: string;
        taxSubType?: string;
        nonPassthroughExpression?: string;
        currencyCode?: string;
        preferredProgramId?: number;
        uomId?: number;
        unitOfBasis?: string;
    }

    interface TaxRuleProductDetailModel {
        taxRuleProductDetailId?: number;
        taxRuleId?: number;
        productCode?: string;
        effectiveDate?: string;
        endDate?: string;
        systemId?: number;
    }

    interface NexusModel extends RecordInfo {
        id?: number;
        companyId?: number;
        country?: string;
        region?: string;
        jurisTypeId?: string;
        jurisdictionTypeId?: string;
        jurisCode?: string;
        jurisName?: string;
        effectiveDate?: string;
        endDate?: string;
        shortName?: string;
        signatureCode?: string;
        stateAssignedNo?: string;
        nexusTypeId?: string;
        sourcing?: string;
        hasLocalNexus?: boolean;
        localNexusTypeId?: string;
        hasPermanentEstablishment?: boolean;
        taxId?: string;
        streamlinedSalesTax?: boolean;
        isSSTActive?: boolean;
        taxTypeGroup?: string;
        nexusTaxTypeGroup?: string;
        taxAuthorityId?: number;
        isSellerImporterOfRecord?: boolean;
        taxName?: string;
        parameters?: NexusParameterDetailModel[];
        taxableNexus?: boolean;
    }

    interface SettingModel {
        id?: number;
        companyId?: number;
        set?: string;
        name?: string;
        value?: string;
        modifiedDate?: string;
        modifiedUserId?: number;
    }

    interface NexusParameterDetailModel {
        id?: number;
        name?: string;
        value?: string;
        unit?: string;
        nexusId?: number;
    }

    interface LocationModel extends GeneralAddress, RecordInfo {
        id?: number;
        companyId?: number;
        locationCode?: string;
        description?: string;
        addressTypeId?: string;
        addressCategoryId?: string;
        isMarketplaceOutsideUsa?: boolean;
        county?: string;
        isDefault?: boolean;
        isRegistered?: boolean;
        dbaName?: string;
        outletName?: string;
        effectiveDate?: string;
        endDate?: string;
        lastTransactionDate?: string;
        registeredDate?: string;
        settings?: LocationSettingModel[];
        parameters?: LocationParameterModel[];
    }

    interface LocationParameterModel {
        id?: number;
        name?: string;
        unit?: string;
        value?: string;
        locationId?: number;
    }

    interface LocationSettingModel {
        questionId?: number;
        questionName?: string;
        value?: string;
    }

    interface ItemModel extends RecordInfo {
        id?: number;
        companyId?: number;
        itemCode?: string;
        taxCodeId?: number;
        taxCode?: string;
        description?: string;
        itemGroup?: string;
        category?: string;
        classifications?: ClassificationModel[];
        parameters?: ItemParameterModel[];
        tags?: ItemTagDetailModel[];
    }

    interface ItemTagDetailModel {
        itemTagDetailId?: number;
        tagId?: number;
        tagName?: string;
        itemId?: number;
        companyId?: number;
        createdDate?: string;
    }

    interface ItemParameterModel {
        id?: number;
        name?: string;
        value?: string;
        unit?: string;
        itemId?: number;
        isNeededForCalculation?: boolean;
        isNeededForReturns?: boolean;
        isNeededForClassification?: boolean;
    }

    interface ClassificationModel {
        productCode?: string;
        systemCode?: string;
    }

    interface ContactModel extends GeneralAddress, RecordInfo {
        id?: number;
        companyId?: number;
        contactCode?: string;
        firstName?: string;
        middleName?: string;
        lastName?: string;
        title?: string;
        email?: string;
        phone?: string;
        mobile?: string;
        fax?: string;
    }

    interface AvaTaxMessage {
        summary?: string;
        details?: string;
        refersTo?: string;
        severity?: string;
        source?: string;
    }

    interface TaxCodeModel extends RecordInfo {
        id?: number;
        companyId?: number;
        taxCode?: string;
        taxCodeTypeId?: string;
        description?: string;
        parentTaxCode?: string;
        isPhysical?: boolean;
        goodsServiceCode?: number;
        entityUseCode?: string;
        isActive?: boolean;
        isSSTCertified?: boolean;
    }

    interface GeneralAddress {
        line1?: string;
        line2?: string;
        line3?: string;
        city?: string;
        region?: string;
        country?: string;
        postalCode?: string;
    }
    interface ResolveAddressRequest {
        line1?: string;
        line2?: string;
        line3?: string;
        city?: string;
        region?: string; // State/Province/Region
        postalCode?: string; // Postal Code / Zip code
        country?: string;
        textCase?: string; // Upper/Mixed
    }

    interface AddressResolutionModel {
        address?: AddressInfo;
        validatedAddresses?: ValidatedAddressInfo[];
        coordinates?: CoordinateInfo;
        taxAuthorities?: TaxAuthorityInfo[];
        messages?: AvaTaxMessage[];
    }

    interface TaxAuthorityInfo {
        avalaraId?: string;
        jurisdictionName?: string;
        jurisdictionType?: string;
        signatureCode?: string;
    }

    interface CoordinateInfo {
        latitude?: number;
        longitude?: number;
    }

    interface ValidatedAddressInfo {
        addressType?: string;
        line1?: string;
        line2?: string;
        line3?: string;
        city?: string;
        region?: string;
        country?: string;
        postalCode?: string;
        latitude?: number;
        longitude?: number;
    }

    interface AddressInfo {
        line1?: string;
        line2?: string;
        line3?: string;
        city?: string;
        region?: string;
        country?: string;
        postalCode?: string;
        latitude?: number;
        longitude?: number;
    }
    interface QueryCompaniesResponse extends QueryResponse {
        value?: CompanyModel[];
    }

    interface CompanyModel {
        id?: number;
        accountId?: number;
        parentCompanyId?: number;
        sstPid?: string;
        companyCode?: string;
        name?: string;
        isDefault?: boolean;
        defaultLocationId?: number;
        isActive?: boolean;
        taxpayerIdNumber?: string;
        IsFein?: boolean;
        hasProfile?: boolean;
        isReportingEntity?: boolean;
        sstEffectiveDate?: string;
        defaultCountry?: string;
        baseCurrencyCode?: string;
        roundingLevelId?: string;
        warningsEnabled?: boolean;
        isTest?: boolean;
        taxDependencyLevelId?: string;
        inProgress?: boolean;
        businessIdentificationNo?: string;
        createdDate?: string;
        createdUserId?: number;
        modifiedDate?: string;
        modifiedUserId?: number;
        contacts?: ContactModel[];
        items?: ItemModel[];
        locations?: LocationModel[];
        nexus?: NexusModel[];
        settings?: SettingModel[];
        taxCodes?: TaxCodeModel[];
        taxRules?: TaxRuleModel[];
        upcs?: UpcModel[];
        nonReportingChildCompanies?: CompanyModel[];
        exemptCerts?: EcmsModel[];
        mossId?: string;
        mossCountry?: string;
        parameters?: CompanyParameterDetailModel[];
        supplierandcustomers?: CustomerSupplierModel[];
    }

    interface CustomerSupplierModel {
        id?: number;
        companyId?: number;
        customerCode?: string;
    }

    interface CompanyParameterDetailModel {
        id?: number;
        companyId?: number;
        name?: string;
        value?: string;
    }

    interface CoordinateInfo {
        latitude?: number;
        longitude?: number;
    }

    interface RecordInfo {
        createdDate?: string;
        createdUserId?: number;
        modifiedDate?: string;
        modifiedUserId?: number;
    }

    interface ResponseErrorMessage {
        error: {
            code: string;
            target: string;
            message: string;
            details: {
                code: string;
                number: number;
                message: string;
                description: string;
                faultCode: string;
                helpLink: string;
                severity: string;
            }[];
        };
    }

    interface ListTaxCodesResponse extends QueryResponse {
        value?: TaxCodeModel[];
    }

    interface QueryRequest {
        filter?: string;
        top?: string;
        skip?: string;
        orderBy?: string;
    }

    interface QueryResponse {
        '@recordsetCount'?: number;
        '@nextLink'?: string;
        pageKey?: string;
    }
    interface QueryCompaniesRequest extends QueryRequest {
        include?: string;
    }

    interface EntityUseCodeModel {
        code?: string;
        name?: string;
        description?: string;
        validCountries?: [];
    }
    interface ListEntityUseCodesResponse extends QueryResponse {
        value?: EntityUseCodeModel[];
    }
    class Avatax {
        baseUrl: string;
        constructor(config: AvataxConfig);
        /**
         * Configuration to use the specified username/password security settings
         *
         * @param  string          creds        Object with credentials
         */
        withSecurity(creds: AvataxCredentials): Avatax;
        ping(): Promise<AvataxTestConnection>;
        restCall<T>(options: RestCallOptions): Promise<T | ResponseErrorMessage>;

        createTransaction(parameters: CreateTransactionParameters): Promise<TransactionResponse>;
        resolveAddress(address: ResolveAddressRequest): Promise<AddressResolutionModel>;
        queryCompanies(request: QueryCompaniesRequest): Promise<QueryCompaniesResponse>;
        listTaxCodes(request: QueryRequest): Promise<ListTaxCodesResponse>;
        listEntityUseCodes(request: QueryRequest): Promise<ListEntityUseCodesResponse>;
    }
    export = Avatax;
}
