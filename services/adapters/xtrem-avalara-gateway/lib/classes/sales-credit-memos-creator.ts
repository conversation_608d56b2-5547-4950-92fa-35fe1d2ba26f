import type { decimal, NodeCreateData } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';

export class SalesCreditMemosCreator extends xtremSales.classes.SalesCreditMemosCreator {
    override async prepareNewLineToDocumentNode(
        line: xtremSales.nodes.SalesInvoiceLine,
        outputQuantity: decimal,
    ): Promise<number> {
        const salesCreditMemoLinesLength = await super.prepareNewLineToDocumentNode(line, outputQuantity);
        const lineData: NodeCreateData<xtremSales.nodes.SalesCreditMemoLine> = {
            entityUse: (await line.entityUse)?._id,
        };

        this.salesOutputDocuments[this.dictKey].lines![salesCreditMemoLinesLength - 1] = {
            ...this.salesOutputDocuments[this.dictKey].lines![salesCreditMemoLinesLength - 1],
            ...lineData,
        };
        return salesCreditMemoLinesLength;
    }
}
