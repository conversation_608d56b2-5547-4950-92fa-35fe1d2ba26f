import type { decimal, NodeCreateData } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremAvalaraGateway from '../index';

export class SalesInvoicesCreator extends xtremSales.classes.SalesInvoicesCreator {
    override async prepareNewLineToDocumentNode(
        line: xtremSales.nodes.SalesShipmentLine,
        outputQuantity: decimal,
    ): Promise<number> {
        const salesInvoiceLinesLength = await super.prepareNewLineToDocumentNode(line, outputQuantity);
        const lineData: NodeCreateData<xtremSales.nodes.SalesInvoiceLine> = {
            entityUse: (await (await line.document).entityUse)?._id,
        };

        this.salesOutputDocuments[this.dictKey].skipSendNotification = true;

        this.salesOutputDocuments[this.dictKey].lines![salesInvoiceLinesLength - 1] = {
            ...this.salesOutputDocuments[this.dictKey].lines![salesInvoiceLinesLength - 1],
            ...lineData,
        };
        return salesInvoiceLinesLength;
    }

    override async createSalesOutputDocuments(): Promise<xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues> {
        const response = await super.createSalesOutputDocuments();
        await this.classContext.runInWritableContext(async writableContext => {
            if (response.documentsCreated && response.documentsCreated.length !== 0) {
                const representativeDocument = await writableContext.read(xtremSales.nodes.SalesInvoice, {
                    _id: response.documentsCreated[0]._id,
                });
                const transactionPayloadManager = await xtremAvalaraGateway.classes.TransactionPayloadManager.create(
                    writableContext,
                    await representativeDocument.financialSite,
                );
                if (transactionPayloadManager.companyCode) {
                    response.documentsCreated.forEach(_id => {
                        transactionPayloadManager.addDocumentToCheck(Number(_id), 'SalesInvoice');
                    });

                    await transactionPayloadManager.sendNotification('SalesInvoice');
                }
            }
        });

        return response;
    }
}
