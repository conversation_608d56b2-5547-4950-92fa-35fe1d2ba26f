import Avatax = require('avatax');
import type { Context } from '@sage/xtrem-core';
import { <PERSON><PERSON>, SystemError, ThirdPartyRequestAuditor } from '@sage/xtrem-core';
import * as fs from 'fs';
import * as fsp from 'path';
import type * as xtremAvalaraGateway from '../../index';

const thirdPartyId = 'Avalara';

const newAvalaraSingleRecordRead = (context: Context): ThirdPartyRequestAuditor =>
    ThirdPartyRequestAuditor.newSingleRecordRead(context, thirdPartyId);

export class XtremAvatax extends Avatax {
    public readonly name = 'avatax';

    constructor(
        public context: Context,
        public config: xtremAvalaraGateway.interfaces.AvataxConfig,
    ) {
        super(config);
    }

    override withSecurity(credential: xtremAvalaraGateway.interfaces.AvataxCredentials): XtremAvatax {
        super.withSecurity(credential);
        return this;
    }

    /** Return the error formated  */
    static formatError(
        errorPayload: xtremAvalaraGateway.interfaces.AvataxErrorMessage,
    ): xtremAvalaraGateway.interfaces.ResponseErrorMessage {
        return {
            error: {
                code: errorPayload.code,
                target: errorPayload.target,
                details: errorPayload.details,
                message: errorPayload.message,
            },
        } as xtremAvalaraGateway.interfaces.ResponseErrorMessage;
    }

    /**
     * Overriding of restCall method : if mocked we return a result , if not we call the original Method
     ** To create mockadata : add testAttributes:{createMockData:true} directory & scenario in the unit test
     ** it will create the request/response file automatically
     ** after file is created delete this 3 properties
     *
     * @param options : url , method & payload to call the avalara api
     * @returns typed Promise
     */
    override async restCall<T>(options: xtremAvalaraGateway.interfaces.RestCallOptions): Promise<any> {
        const mocker = Mocker.get('avatax', require);
        if (mocker.isMocked) {
            return mocker.restCall(options);
        }

        if (this.context.testMode) {
            if (this.context?.testConfig?.testAttributes?.createMockData) {
                this.context.logger.warn(() => ` create mock data `);
            } else {
                throw new SystemError(` This need to be mocked `);
            }
        }

        this.context.logger.debug(() => `${options.payload}`);

        const newrelicAuditor = newAvalaraSingleRecordRead(this.context);
        const result = super
            .restCall<T>(options)
            .catch(error => {
                newrelicAuditor.recordFailure();
                return XtremAvatax.formatError(error);
            })
            .then(restCallResult => {
                newrelicAuditor.recordSuccess(); // here it would be better if we could know how many lines have been impacted.
                return restCallResult;
            });

        if (
            this.context?.testConfig?.testAttributes?.createMockData &&
            this.context?.testConfig?.directory &&
            this.context?.testConfig?.scenario
        ) {
            this.writeMockData(
                options,
                (await result) as any,
                this.context?.testConfig?.directory,
                this.context?.testConfig?.scenario,
            );
        }

        return result as any;
    }

    /**
     *
     * @param request
     * @param response
     */
    writeMockData(
        request: xtremAvalaraGateway.interfaces.RestCallOptions,
        response: any,
        directory: string,
        filename: string,
    ): void {
        let mockData;
        if (!fs.existsSync(fsp.join(directory, this.name))) fs.mkdirSync(fsp.join(directory, this.name));
        if (!fs.existsSync(fsp.join(directory, this.name, `${filename}.json`))) {
            mockData = [];
        } else {
            this.context.logger.debug(() => `Try to open file : ${directory}/${this.name}/${filename}.json`);
            mockData = JSON.parse(
                fs.readFileSync(fsp.join(directory, this.name, `${filename}.json`), 'utf8').toString(),
            );
        }
        const { url, verb, payload } = request;
        mockData.push({
            request: { url, verb, payload },
            response: { isMock: true, ...response },
        });
        fs.writeFileSync(fsp.join(directory, this.name, `${filename}.json`), JSON.stringify(mockData, null, 4));
    }
}
