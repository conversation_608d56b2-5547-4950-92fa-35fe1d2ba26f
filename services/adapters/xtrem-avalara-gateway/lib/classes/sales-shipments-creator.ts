import * as xtremSales from '@sage/xtrem-sales';

export class SalesShipmentsCreator extends xtremSales.classes.SalesShipmentsCreator {
    override async createDictKey(
        line: xtremSales.nodes.SalesOrderLine,
        header: xtremSales.nodes.SalesOrder,
    ): Promise<void> {
        await super.createDictKey(line, header);
        if ((await header.taxEngine) === 'avalaraAvaTax') {
            this.dictKey += String((await line.entityUse)?._id ?? null);
        }
    }

    override async addHeaderDataToDocumentNode(line: xtremSales.nodes.SalesOrderLine): Promise<void> {
        await super.addHeaderDataToDocumentNode(line);

        if ((await (await line.document).taxEngine) === 'avalaraAvaTax') {
            this.salesOutputDocuments[this.dictKey] = {
                ...this.salesOutputDocuments[this.dictKey],
                entityUse: (await line.entityUse)?._id === undefined ? null : (await line.entityUse)?._id,
            };
        }
    }
}
