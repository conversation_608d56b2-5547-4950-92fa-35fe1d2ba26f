import type { Context, StaticThis } from '@sage/xtrem-core';
import { asyncArray } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremAvalaraGateway from '../../../index';
import { BasePayloadManager } from './base-payload-manger';

export class TransactionPayloadManager extends BasePayloadManager {
    protected requestPayload: { model: xtremAvalaraGateway.interfaces.TransactionRequest };

    protected responsePayload: xtremAvalaraGateway.interfaces.TransactionResponse;

    protected documentsToCheck: xtremAvalaraGateway.interfaces.DocumentsToCheck;

    companyCode?: string;

    constructor(public classContext: Context) {
        super();
    }

    protected async init(site?: xtremSystem.nodes.Site): Promise<this> {
        if (
            !(await (
                await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(this.classContext)
            )?.isActive)
        ) {
            this.classContext.logger.warn(`The Avalara integration is not activated`);
        } else if (site) {
            this.companyCode = await this.getCompanyCode(site);
        }
        return this;
    }

    static create(classContext: Context, site?: xtremSystem.nodes.Site): Promise<TransactionPayloadManager> {
        return new TransactionPayloadManager(classContext).init(site);
    }

    addDocumentToCheck(
        _id: number,
        documentsType: xtremAvalaraGateway.enums.TransactionPayloadManagerType,
        isCommit = false,
        isPosting = false,
    ): void {
        if (!this.documentsToCheck) {
            this.documentsToCheck = { documentsType, isCommit, isPosting, documents: [] };
        }
        this.documentsToCheck.documents.push(_id);
    }

    async sendNotification(documentType: xtremAvalaraGateway.enums.TransactionPayloadManagerType): Promise<void> {
        if (this.documentsToCheck && this.documentsToCheck.documents.length > 0) {
            const topic = 'avalaraCreateTransaction/request';
            const replyTopic = `${documentType}/avalaraCreateTransaction/response`;
            await this.classContext.notify(topic, this.documentsToCheck, { replyTopic });
        }
    }

    setResponsePayload(payload: xtremAvalaraGateway.interfaces.TransactionResponse): TransactionPayloadManager {
        this.responsePayload = payload;
        return this;
    }

    /**
     * In this method, we are checking if the company has mapped avalara company and in case it has
     * we fill companyCode property with this value
     *
     * @param site : the financial site
     * @returns string | undefined
     */
    private async getCompanyCode(site: xtremSystem.nodes.Site): Promise<string | undefined> {
        if ((await (await site.legalCompany).taxEngine) !== 'avalaraAvaTax') {
            return undefined;
        }

        let mapCompany: xtremAvalaraGateway.nodes.MapCompany[] = await this.classContext
            .query(xtremAvalaraGateway.nodes.MapCompany, {
                filter: { financialSite: site, isActive: true },
            })
            .toArray();
        if (!mapCompany.length) {
            mapCompany = await this.classContext
                .query(xtremAvalaraGateway.nodes.MapCompany, {
                    filter: {
                        company: await site.legalCompany,
                        isActive: true,
                    },
                })
                .toArray();
        }
        if (mapCompany.length && (await (await mapCompany[0].avalaraCompany).isActive)) {
            return (await mapCompany[0].avalaraCompany).id;
        }
        if (!mapCompany.length) {
            this.classContext.logger.warn(
                `The financial site associated to the sales site or the company associated to the sales site are not defined in the Avalara setup`,
            );
        } else {
            this.classContext.logger.warn(`The Avalara company is not activated`);
        }
        return undefined;
    }

    private async checkDocumentHasNoErrors(
        document: xtremAvalaraGateway.interfaces.TransactionDocumentType | null,
    ): Promise<boolean> {
        if (document && ((this.responsePayload as any).errors || (this.responsePayload as any).error)) {
            await document.$.set({ taxCalculationStatus: 'failed', skipCallAvalaraApi: true });
            if (this.requestPayload?.model?.commit === true) {
                await document.$.set({ forceUpdateForFinance: true });
            }
            await document.$.save();
            return false;
        }
        return true;
    }

    private async processDocument(
        document: xtremAvalaraGateway.interfaces.TransactionDocumentType | null,
    ): Promise<void> {
        if (
            document &&
            (await (await (await document.billToCustomer).businessEntity).id) === this.responsePayload.customerCode &&
            this.responsePayload.date ===
                String(
                    document instanceof xtremSales.nodes.SalesInvoice
                        ? await document.invoiceDate
                        : await document.date,
                )
        ) {
            await document.$.set({
                lines: await this.processResponsePayloadLines(document),
                ...this.processResponsePayloadHeader(),
                taxCalculationStatus: 'done',
                skipCallAvalaraApi: true,
            });
            await TransactionPayloadManager.processResponsePayloadTaxDetails(document, this.responsePayload.summary);

            await document.$.save();

            if (this.responsePayload.status === 'Committed' || this.responsePayload.status === 'Saved') {
                await document.$.set({ skipCallAvalaraApi: true, avalaraId: String(this.responsePayload.id) });
                if (document instanceof xtremSales.nodes.SalesCreditMemo) {
                    await xtremSales.nodes.SalesCreditMemo.postMainLogic(this.classContext, document);
                }

                if (document instanceof xtremSales.nodes.SalesInvoice) {
                    const { context } = document.$;
                    // Set new sequence number for FR/ZA
                    const finalInvoiceNumber = await xtremSales.functions.SalesInvoiceLib.allocateNewSequenceNumber(
                        context,
                        {
                            invoice: document,
                            status: await document.status,
                            taxCalculationStatus: await document.taxCalculationStatus,
                        },
                    );
                    if (finalInvoiceNumber) {
                        await document.$.set({ number: finalInvoiceNumber });
                    }

                    // send notification in order to create a staging table entry for the accounting engine
                    const postingResult = await xtremSales.functions.FinanceIntegration.salesInvoiceNotification(
                        context,
                        document,
                    );

                    // if no notification was sent (all amounts=0), the document is considered as posted
                    await document.$.set({
                        status: postingResult.notificationsSent ? 'inProgress' : 'posted',
                        forceUpdateForFinance: true,
                    });
                    document.__skipPrepare = true;
                    await document.$.save();
                }
            }
        }
    }

    async processResponsePayload(documentType: xtremAvalaraGateway.enums.TransactionPayloadManagerType): Promise<void> {
        let document: xtremAvalaraGateway.interfaces.TransactionDocumentType | null = null;
        document = await this.readDocument(xtremSales.nodes[documentType]);

        if (await this.checkDocumentHasNoErrors(document)) {
            await this.processDocument(document);
        }
    }

    private readDocument(
        node: StaticThis<xtremAvalaraGateway.interfaces.TransactionDocumentType>,
    ): Promise<xtremAvalaraGateway.interfaces.TransactionDocumentType | null> {
        return this.classContext.tryRead(
            node,
            {
                number: this.responsePayload.code,
            },
            { forUpdate: true },
        );
    }

    protected processResponsePayloadLines(
        document: xtremAvalaraGateway.interfaces.TransactionDocumentType,
    ): Promise<xtremAvalaraGateway.interfaces.NodeCreateDataLines[]> | undefined {
        if (this.responsePayload.lines) {
            return asyncArray(this.responsePayload.lines)
                ?.map(async (responsePayloadLine: xtremAvalaraGateway.interfaces.TransactionLineModel) => {
                    const foundLine = await document.lines.find(
                        (line: xtremAvalaraGateway.interfaces.TransactionDocumentLineType) =>
                            line._id === Number(responsePayloadLine.lineNumber),
                    );

                    const documentLine: xtremAvalaraGateway.interfaces.NodeCreateDataLines = {
                        _action: 'update',
                        _id: foundLine!._id,
                        exemptAmount: Math.abs(Number(responsePayloadLine.exemptAmount)),
                        quantity: Number(responsePayloadLine.quantity),
                        taxAmountAdjusted: Math.abs(Number(responsePayloadLine.tax)),
                        taxableAmount: Math.abs(Number(responsePayloadLine.taxableAmount)),
                        taxAmount: Math.abs(Number(responsePayloadLine.taxCalculated)),
                        // taxDate: date.parse(String(responsePayloadLine.taxDate)),
                    };

                    await TransactionPayloadManager.processResponsePayloadTaxDetails(
                        foundLine!,
                        responsePayloadLine.details,
                    );

                    return documentLine;
                })
                .toArray();
        }
        return undefined;
    }

    protected processResponsePayloadHeader(): xtremAvalaraGateway.interfaces.ResponsePayloadHeader {
        return {
            totalTaxAmountAdjusted: Math.abs(Number(this.responsePayload.totalTax)),
            totalExemptAmount: Math.abs(Number(this.responsePayload.totalExempt)),
            totalDiscountAmount: Math.abs(Number(this.responsePayload.totalDiscount)),
            totalTaxableAmount: Math.abs(Number(this.responsePayload.totalTaxable)),
            totalTaxAmount: Math.abs(Number(this.responsePayload.totalTaxCalculated)),
        };
    }

    protected static instanceOfTransactionSummary(
        object: any,
    ): object is xtremAvalaraGateway.interfaces.TransactionSummary {
        return 'taxable' in object;
    }

    protected static unpackArray<T>(array: T): Array<xtremAvalaraGateway.interfaces.UnpackedArray<T>> {
        return array as any;
    }

    protected static async processResponsePayloadTaxDetails(
        document:
            | xtremAvalaraGateway.interfaces.TransactionDocumentType
            | xtremAvalaraGateway.interfaces.TransactionDocumentLineType,
        summary?:
            | xtremAvalaraGateway.interfaces.TransactionSummary[]
            | xtremAvalaraGateway.interfaces.TransactionLineDetailModel[],
    ): Promise<void> {
        await (
            await document.taxes.slice()
        ).forEach((taxLine: xtremAvalaraGateway.interfaces.Taxes) =>
            document.$.set({
                taxes: [{ _id: taxLine._id, _action: 'delete' }],
            }),
        );

        let counter: number = 10;

        await document.$.set({
            taxes: TransactionPayloadManager.unpackArray(summary).map(summaryLine => {
                const taxLine: xtremAvalaraGateway.interfaces.NodeCreateDataTaxes = {
                    country: summaryLine.country,
                    region: summaryLine.region,
                    jurisdictionType: TransactionPayloadManager.instanceOfTransactionSummary(summaryLine)
                        ? summaryLine.jurisType
                        : summaryLine.jurisdictionType,
                    jurisdictionCode: summaryLine.jurisCode,
                    jurisdictionName: summaryLine.jurisName,
                    tax: summaryLine.taxName,
                    taxableAmount: TransactionPayloadManager.instanceOfTransactionSummary(summaryLine)
                        ? Math.abs(Number(summaryLine.taxable))
                        : Math.abs(Number(summaryLine.taxableAmount)),
                    taxRate: Number(summaryLine.rate) * 100,
                    taxAmountAdjusted: Math.abs(Number(summaryLine.tax)),
                    taxAmount: Math.abs(Number(summaryLine.taxCalculated)),
                    nonTaxableAmount: TransactionPayloadManager.instanceOfTransactionSummary(summaryLine)
                        ? Math.abs(Number(summaryLine.nonTaxable))
                        : Math.abs(Number(summaryLine.nonTaxableAmount)),
                    exemptAmount: TransactionPayloadManager.instanceOfTransactionSummary(summaryLine)
                        ? Math.abs(Number(summaryLine.exemption))
                        : Math.abs(Number(summaryLine.exemptAmount)),
                    taxCategory: TransactionPayloadManager.instanceOfTransactionSummary(summaryLine)
                        ? summaryLine.jurisType
                        : summaryLine.jurisdictionType,
                    deductibleTaxRate: 100,
                    deductibleTaxAmount: (Math.abs(Number(summaryLine.tax)) * 100) / 100,
                    _action: 'create',
                    _sortValue: counter,
                };
                if (!TransactionPayloadManager.instanceOfTransactionSummary(summaryLine)) {
                    (taxLine as any).numberOfTaxableUnits = summaryLine.taxableUnits
                        ? Math.abs(summaryLine.taxableUnits)
                        : undefined;
                    (taxLine as any).numberOfNonTaxableUnits = summaryLine.nonTaxableUnits
                        ? Math.abs(summaryLine.nonTaxableUnits)
                        : undefined;
                    (taxLine as any).numberOfExemptUnits = summaryLine.exemptUnits
                        ? Math.abs(summaryLine.exemptUnits)
                        : undefined;
                }
                counter += 10;
                return taxLine;
            }),
        });
    }

    async prepareRequestPayload(
        document: xtremAvalaraGateway.interfaces.TransactionDocumentType,
        isCommit = false,
        isPosting = false,
    ): Promise<{ model: xtremAvalaraGateway.interfaces.TransactionRequest }> {
        this.requestPayload = { model: { lines: [], date: '' } };
        await this.prepareRequestPayloadHeader(document, isCommit, isPosting);
        await this.prepareRequestPayloadLines(document.lines);
        return this.requestPayload;
    }

    protected async prepareRequestPayloadHeader(
        document: xtremAvalaraGateway.interfaces.TransactionDocumentType,
        isCommit = false,
        isPosting = false,
    ): Promise<void> {
        this.requestPayload.model = {
            ...this.requestPayload.model,
            code: await document.number,
            customerCode: await (await (await document.billToCustomer).businessEntity).id,
            commit: isCommit || false,
            currencyCode: await (await document.currency).id,
            businessIdentificationNo: await (await (await document.billToCustomer).businessEntity).taxIdNumber,
            companyCode: this.companyCode,
        };

        const header = this.requestPayload.model;

        if ((await document.currency) !== (await (await (await document.site).legalCompany).currency)) {
            header.exchangeRate = await document.companyFxRate;
            header.exchangeRateEffectiveDate = String(await document.fxRateDate);
        }

        if (document instanceof xtremSales.nodes.SalesCreditMemo) {
            header.referenceCode = await (
                await (await (
                    await (await document.lines.elementAt(0)).toInvoiceLines.at(0)
                )?.linkedDocument)!.document
            ).number;
            if (isPosting) {
                header.type = 'ReturnInvoice';
            } else if (isCommit) {
                header.type = 'ReturnInvoice';
            } else {
                header.type = 'ReturnOrder';
            }
            header.date = String(await document.date);
        } else {
            if (isPosting) {
                header.type = 'SalesInvoice';
            } else if (isCommit) {
                header.type = 'SalesInvoice';
            } else {
                header.type = 'SalesOrder';
            }
            header.date = String(await document.invoiceDate);
        }
    }

    protected async prepareRequestPayloadLines(
        lines: xtremAvalaraGateway.interfaces.TransactionDocumentType['lines'],
    ): Promise<void> {
        const header = this.requestPayload.model;
        await lines.forEach(async (line: xtremAvalaraGateway.interfaces.TransactionDocumentLineType) => {
            const isSalesInvoiceLine = line instanceof xtremSales.nodes.SalesInvoiceLine;
            let lineDict: xtremAvalaraGateway.interfaces.LineItemModel = {
                number: String(line._id),
                quantity: Number(await line.quantity),
                amount: (await line.amountExcludingTax) * (isSalesInvoiceLine ? 1 : -1),
                itemCode: await (await line.item).id,
                entityUseCode: await (await line.entityUse)?.id,
                taxIncluded: false,
                taxCode: await (await (await line.item).avalaraItemTax)?.id,
                description: await line.itemDescription,
                addresses: await TransactionPayloadManager.prepareRequestPayloadAddressesModel(line),
            };
            if (line instanceof xtremSales.nodes.SalesCreditMemoLine) {
                lineDict = {
                    ...lineDict,
                    ref1: String(
                        await (
                            await (await (
                                await line.toInvoiceLines.at(0)
                            )?.linkedDocument)!.document
                        ).number,
                    ),
                    taxOverride: await TransactionPayloadManager.prepareRequestPayloadTaxOverride(line),
                };
            }
            header.lines.push(lineDict);
        });
    }

    static async prepareRequestPayloadTaxOverride(
        line: xtremSales.nodes.SalesCreditMemoLine,
    ): Promise<xtremAvalaraGateway.interfaces.TaxOverrideModel> {
        return {
            type: 'taxDate',
            taxDate: String(
                await (
                    await (await (
                        await line.toInvoiceLines.at(0)
                    )?.linkedDocument)!.document
                ).invoiceDate,
            ),
            reason: (await (await (await line.document).reason)?.name) ?? 'Return invoice',
        };
    }

    static async prepareRequestPayloadAddressesModel(
        line: xtremSales.nodes.SalesCreditMemoLine | xtremSales.nodes.SalesInvoiceLine,
    ): Promise<xtremAvalaraGateway.interfaces.AddressesModel> {
        return {
            shipFrom: await TransactionPayloadManager.prepareRequestPayloadShipFrom(line),
            shipTo: await TransactionPayloadManager.prepareRequestPayloadShipTo(line),
        };
    }

    static async prepareRequestPayloadShipFrom(
        line: xtremSales.nodes.SalesCreditMemoLine | xtremSales.nodes.SalesInvoiceLine,
    ): Promise<xtremAvalaraGateway.interfaces.AddressLocationInfo> {
        return {
            line1: await (await line.providerSiteAddress)?.addressLine1,
            line2: await (await line.providerSiteAddress)?.addressLine2,
            city: await (await line.providerSiteAddress)?.city,
            region: await (await line.providerSiteAddress)?.region,
            country: await (await (await line.providerSiteAddress)?.country)?.id,
            postalCode: await (await line.providerSiteAddress)?.postcode,
        };
    }

    static async prepareRequestPayloadShipTo(
        line: xtremSales.nodes.SalesCreditMemoLine | xtremSales.nodes.SalesInvoiceLine,
    ): Promise<xtremAvalaraGateway.interfaces.AddressLocationInfo> {
        return {
            line1: await (await line.consumptionAddress)?.addressLine1,
            line2: await (await line.consumptionAddress)?.addressLine2,
            city: await (await line.consumptionAddress)?.city,
            region: await (await line.consumptionAddress)?.region,
            country: await (await (await line.consumptionAddress)?.country)?.id,
            postalCode: await (await line.consumptionAddress)?.postcode,
        };
    }
}
