import type { Dict, Node } from '@sage/xtrem-core';
import type * as xtremAvalaraGateway from '../../index';

export abstract class BasePayloadManager {
    protected abstract requestPayload: Dict<any>;

    protected abstract responsePayload: Dict<any>;

    abstract prepareRequestPayload(
        document: Node,
    ): Promise<{ model: xtremAvalaraGateway.interfaces.TransactionRequest }>;

    abstract processResponsePayload(documentType: any): Promise<void>;

    abstract sendNotification(documentType: any): Promise<void>;
}
