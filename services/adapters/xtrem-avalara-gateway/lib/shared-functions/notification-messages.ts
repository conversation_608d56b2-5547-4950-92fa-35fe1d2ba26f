import type { Dict } from '@sage/xtrem-shared';

function getAvalaraPingSucceeded(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-succeeded',
        'Ping notification succeeded.',
    );
}
function getAvalaraResolveAddressSucceeded(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-succeeded',
        'Resolve address notification succeeded.',
    );
}
function getAvalaraListEntityUseCodesSucceeded(context: Dict<any>, recordCount?: number): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-succeeded',
        'List entity code notification succeeded. (Received: {{recordCount}})',
        { recordCount },
    );
}
function getAvalaraListTaxCodesSucceeded(context: Dict<any>, recordCount?: number): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-succeeded',
        'List tax code notification succeeded. (Received: {{recordCount}})',
        { recordCount },
    );
}
function getAvalaraQueryCompaniesSucceeded(context: Dict<any>, recordCount?: number): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-succeeded',
        'Query companies notification succeeded. (Received: {{recordCount}})',
        { recordCount },
    );
}
function getAvalaraCreateTransactionOnSalesInvoiceForPostingWithCommitSucceeded(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-post-and-commit',
        'Sales invoice notification for posting with commit option succeeded.',
    );
}
function getAvalaraCreateTransactionOnSalesInvoiceForPostingWithUncommitSucceeded(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-post-only',
        'Sales invoice notification for posting with uncommit option succeeded.',
    );
}
function getAvalaraCreateTransactionOnSalesInvoiceForTaxCalculationSucceeded(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-taxes-and-values-only',
        'Sales invoice notification for tax calculation succeeded.',
    );
}
function getAvalaraCreateTransactionOnSalesCreditMemoForPostingWithCommitSucceeded(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-post-and-commit',
        'Sales credit memo notification for posting with commit option succeeded.',
    );
}
function getAvalaraCreateTransactionOnSalesCreditMemoForPostingWithUncommitSucceeded(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-post-only',
        'Sales credit memo notification for posting with uncommit option succeeded.',
    );
}
function getAvalaraCreateTransactionOnSalesCreditMemoForTaxCalculationSucceeded(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-taxes-and-values-only',
        'Sales credit memo notification for tax calculation succeeded.',
    );
}
function getAvalaraPingError(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-failed',
        'Ping notification failed.',
    );
}
function getAvalaraResolveAddressError(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-failed',
        'Resolve address notification failed.',
    );
}
function getAvalaraListEntityUseCodesError(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-failed',
        'List entity code notification failed.',
    );
}
function getAvalaraListTaxCodesError(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-failed',
        'List tax code notification failed.',
    );
}
function getAvalaraQueryCompaniesError(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-failed',
        'Query companies notification failed.',
    );
}
function getAvalaraCreateTransactionOnSalesInvoiceForPostingWithCommitError(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-post-and-commit',
        'Sales invoice for posting with commit option failed.',
    );
}
function getAvalaraCreateTransactionOnSalesInvoiceForPostingWithUncommitError(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-post-only',
        'Sales invoice notification for posting with uncommit option failed.',
    );
}
function getAvalaraCreateTransactionOnSalesInvoiceForTaxCalculationError(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-taxes-and-values-only',
        'Sales invoice notification for tax calculation failed.',
    );
}
function getAvalaraCreateTransactionOnSalesCreditMemoForPostingWithCommitError(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-post-and-commit',
        'Sales credit memo notification for posting with commit options failed.',
    );
}
function getAvalaraCreateTransactionOnSalesCreditMemoForPostingWithUncommitError(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-post-only',
        'Sales credit memo notification for posting with uncommit option failed.',
    );
}
function getAvalaraCreateTransactionOnSalesCreditMemoForTaxCalculationError(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-taxes-and-values-only',
        'Sales credit memo notification for tax calculation failed.',
    );
}
function getAvalaraPingSent(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-sent',
        'Ping notification sent.',
    );
}
function getAvalaraResolveAddressSent(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-sent',
        'Resolve address notification sent.',
    );
}
function getAvalaraListEntityUseCodesSent(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-sent',
        'List entity code notification sent.',
    );
}
function getAvalaraListTaxCodesSent(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-sent',
        'List tax code notification sent.',
    );
}
function getAvalaraQueryCompaniesSent(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-sent',
        'Query companies notification sent.',
    );
}
function getAvalaraCreateTransactionOnSalesInvoiceForPostingWithCommitSent(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-post-and-commit',
        'Sales invoice notification for posting with commit options sent.',
    );
}
function getAvalaraCreateTransactionOnSalesInvoiceForPostingWithUncommitSent(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-post-only',
        'Sales invoice notification for posting with uncommit option sent.',
    );
}
function getAvalaraCreateTransactionOnSalesInvoiceForTaxCalculationSent(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-taxes-and-values-only',
        'Sales invoice notification for tax calculation sent.',
    );
}
function getAvalaraCreateTransactionOnSalesCreditMemoForPostingWithCommitSent(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-post-and-commit',
        'Sales credit memo notification for posting with commit option sent.',
    );
}
function getAvalaraCreateTransactionOnSalesCreditMemoForPostingWithUncommitSent(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-post-only',
        'Sales credit memo notification for posting with uncommit option sent.',
    );
}
function getAvalaraCreateTransactionOnSalesCreditMemoForTaxCalculationSent(context: Dict<any>): string {
    return context.localize(
        '@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-taxes-and-values-only',
        'Sales credit memo notification for tax calculation sent.',
    );
}

export function getAvataxNotificationMessageLocalized(context: Dict<any>, code: string, recordCount?: number): string {
    if (code === 'AvalaraPingSucceeded') {
        return getAvalaraPingSucceeded(context);
    }
    if (code === 'AvalaraResolveAddressSucceeded') {
        return getAvalaraResolveAddressSucceeded(context);
    }
    if (code === 'AvalaraListEntityUseCodesSucceeded') {
        return getAvalaraListEntityUseCodesSucceeded(context, recordCount);
    }
    if (code === 'AvalaraListTaxCodesSucceeded') {
        return getAvalaraListTaxCodesSucceeded(context, recordCount);
    }
    if (code === 'AvalaraQueryCompaniesSucceeded') {
        return getAvalaraQueryCompaniesSucceeded(context, recordCount);
    }
    if (code === 'AvalaraCreateTransactionOnSalesInvoiceForPostingWithCommitSucceeded') {
        return getAvalaraCreateTransactionOnSalesInvoiceForPostingWithCommitSucceeded(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesInvoiceForPostingWithUncommitSucceeded') {
        return getAvalaraCreateTransactionOnSalesInvoiceForPostingWithUncommitSucceeded(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesInvoiceForTaxCalculationSucceeded') {
        return getAvalaraCreateTransactionOnSalesInvoiceForTaxCalculationSucceeded(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesCreditMemoForPostingWithCommitSucceeded') {
        return getAvalaraCreateTransactionOnSalesCreditMemoForPostingWithCommitSucceeded(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesCreditMemoForPostingWithUncommitSucceeded') {
        return getAvalaraCreateTransactionOnSalesCreditMemoForPostingWithUncommitSucceeded(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesCreditMemoForTaxCalculationSucceeded') {
        return getAvalaraCreateTransactionOnSalesCreditMemoForTaxCalculationSucceeded(context);
    }
    if (code === 'AvalaraPingError') {
        return getAvalaraPingError(context);
    }
    if (code === 'AvalaraResolveAddressError') {
        return getAvalaraResolveAddressError(context);
    }
    if (code === 'AvalaraListEntityUseCodesError') {
        return getAvalaraListEntityUseCodesError(context);
    }
    if (code === 'AvalaraListTaxCodesError') {
        return getAvalaraListTaxCodesError(context);
    }
    if (code === 'AvalaraQueryCompaniesError') {
        return getAvalaraQueryCompaniesError(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesInvoiceForPostingWithCommitError') {
        return getAvalaraCreateTransactionOnSalesInvoiceForPostingWithCommitError(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesInvoiceForPostingWithUncommitError') {
        return getAvalaraCreateTransactionOnSalesInvoiceForPostingWithUncommitError(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesInvoiceForTaxCalculationError') {
        return getAvalaraCreateTransactionOnSalesInvoiceForTaxCalculationError(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesCreditMemoForPostingWithCommitError') {
        return getAvalaraCreateTransactionOnSalesCreditMemoForPostingWithCommitError(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesCreditMemoForPostingWithUncommitError') {
        return getAvalaraCreateTransactionOnSalesCreditMemoForPostingWithUncommitError(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesCreditMemoForTaxCalculationError') {
        return getAvalaraCreateTransactionOnSalesCreditMemoForTaxCalculationError(context);
    }
    if (code === 'AvalaraPingSent') {
        return getAvalaraPingSent(context);
    }
    if (code === 'AvalaraResolveAddressSent') {
        return getAvalaraResolveAddressSent(context);
    }
    if (code === 'AvalaraListEntityUseCodesSent') {
        return getAvalaraListEntityUseCodesSent(context);
    }
    if (code === 'AvalaraListTaxCodesSent') {
        return getAvalaraListTaxCodesSent(context);
    }
    if (code === 'AvalaraQueryCompaniesSent') {
        return getAvalaraQueryCompaniesSent(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesInvoiceForPostingWithCommitSent') {
        return getAvalaraCreateTransactionOnSalesInvoiceForPostingWithCommitSent(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesInvoiceForPostingWithUncommitSent') {
        return getAvalaraCreateTransactionOnSalesInvoiceForPostingWithUncommitSent(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesInvoiceForTaxCalculationSent') {
        return getAvalaraCreateTransactionOnSalesInvoiceForTaxCalculationSent(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesCreditMemoForPostingWithCommitSent') {
        return getAvalaraCreateTransactionOnSalesCreditMemoForPostingWithCommitSent(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesCreditMemoForPostingWithUncommitSent') {
        return getAvalaraCreateTransactionOnSalesCreditMemoForPostingWithUncommitSent(context);
    }
    if (code === 'AvalaraCreateTransactionOnSalesCreditMemoForTaxCalculationSent') {
        return getAvalaraCreateTransactionOnSalesCreditMemoForTaxCalculationSent(context);
    }
    return '';
}

// all these are intentionally left not localized

export const topicPingRequest = 'avalaraPing/request';
export const topicResolveAddressRequest = 'avalaraResolveAddress/request';
export const topicListEntityUseCodesRequest = 'avalaraListEntityUseCodes/request';
export const topicListTaxCodesRequest = 'avalaraListTaxCodes/request';
export const topicQueryCompaniesRequest = 'avalaraQueryCompanies/request';
export const topicCreateTransactionRequest = 'avalaraCreateTransaction/request';

export const listenerPing = 'AvataxListener.ping';
export const listenerResolveAddress = 'AvataxListener.resolveAddress';
export const listenerListEntityUseCodes = 'AvataxListener.listEntityUseCodes';
export const listenerListTaxCodes = 'AvataxListener.listTaxCodes';
export const listenerQueryCompanies = 'AvataxListener.queryCompanies';
export const listenerCreateTransaction = 'AvataxListener.createTransaction';

export const listenerPingExplicit = 'Ping';
export const listenerResolveAddressExplicit = 'Resolve address';
export const listenerListEntityUseCodesExplicit = 'List entity use codes';
export const listenerListTaxCodesExplicit = 'List tax codes';
export const listenerQueryCompaniesExplicit = 'Query companies';
export const listenerCreateTransactionExplicit = 'Create transaction';

export const avalaraHistoryStatusValues = ['sent', 'success', 'error'];

export const avalaraTopicValues = [
    topicListEntityUseCodesRequest,
    topicListTaxCodesRequest,
    topicQueryCompaniesRequest,
    topicCreateTransactionRequest,
];

export const avalaraListenerValues = [
    listenerListEntityUseCodes,
    listenerListTaxCodes,
    listenerQueryCompanies,
    listenerCreateTransaction,
];

export const avalaraMainTopicValues = [
    listenerListEntityUseCodesExplicit,
    listenerListTaxCodesExplicit,
    listenerQueryCompaniesExplicit,
    listenerCreateTransactionExplicit,
];

export const avalaraDocumentTypeSalesInvoice = 'SalesInvoice';
export const avalaraDocumentTypeSalesCreditMemo = 'SalesCreditMemo';
export const avalaraDocumentTypeSalesInvoiceExplicit = 'Sales invoice';
export const avalaraDocumentTypeSalesCreditMemoExplicit = 'Sales credit memo';

export const avalaraDocumentTypeValues = [
    avalaraDocumentTypeSalesInvoiceExplicit,
    avalaraDocumentTypeSalesCreditMemoExplicit,
];
