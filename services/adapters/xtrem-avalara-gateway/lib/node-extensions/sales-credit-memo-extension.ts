import type { Context, decimal } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, SubNodeExtension1 } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAvalaraGateway from '../index';

@decorators.subNodeExtension1<SalesCreditMemoExtension>({
    extends: () => xtremSales.nodes.SalesCreditMemo,
    async saveBegin() {
        if (
            !(await this.skipCallAvalaraApi) &&
            (await this.status) === 'draft' &&
            !(await this.forceUpdateForFinance)
        ) {
            this.__transactionPayloadManager = await xtremAvalaraGateway.classes.TransactionPayloadManager.create(
                this.$.context,
                await this.site,
            );
            if (this.__transactionPayloadManager.companyCode) {
                await this.$.set({ taxCalculationStatus: 'inProgress' });
            }
        }
    },
    async saveEnd() {
        if (this.__transactionPayloadManager?.companyCode && !(await this.forceUpdateForFinance)) {
            this.__transactionPayloadManager.addDocumentToCheck(this._id, 'SalesCreditMemo');
            await this.__transactionPayloadManager.sendNotification('SalesCreditMemo');
        }
    },
})
export class SalesCreditMemoExtension extends SubNodeExtension1<xtremSales.nodes.SalesCreditMemo> {
    @decorators.booleanProperty<SalesCreditMemoExtension, 'skipCallAvalaraApi'>({
        defaultValue: false,
    })
    readonly skipCallAvalaraApi: Promise<boolean>;

    @decorators.stringProperty<SalesCreditMemoExtension, 'avalaraId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.id,
    })
    readonly avalaraId: Promise<string>;

    __transactionPayloadManager: xtremAvalaraGateway.classes.TransactionPayloadManager;

    @decorators.decimalProperty<SalesCreditMemoExtension, 'totalDiscountAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalDiscountAmount: Promise<decimal>;

    @decorators.mutation<typeof SalesCreditMemoExtension, 'calculateTax'>({
        isPublished: true,
        parameters: [
            {
                name: 'creditMemo',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => xtremSales.nodes.SalesCreditMemo,
            },
            {
                name: 'isCommit',
                type: 'boolean',
                isMandatory: false,
            },
            {
                name: 'isPosting',
                type: 'boolean',
                isMandatory: false,
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async calculateTax(
        context: Context,
        creditMemo: xtremSales.nodes.SalesCreditMemo,
        isCommit = false,
        isPosting = false,
    ): Promise<string> {
        await creditMemo.$.set({
            taxCalculationStatus: await this.commonCalculateTaxCreditMemo(context, creditMemo, isCommit, isPosting),
        });
        await creditMemo.$.save();

        return context.localize(
            '@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__tax_calculation_asked',
            'The tax calculation has been asked for this credit memo.',
        );
    }

    static async commonCalculateTaxCreditMemo(
        context: Context,
        document: xtremSales.nodes.SalesCreditMemo,
        isCommit: boolean,
        isPosting: boolean,
    ): Promise<xtremMasterData.enums.TaxCalculationStatus> {
        if ((await document.taxCalculationStatus) === 'inProgress') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__cant_calculate_tax_wrong_tax_calculation_status',
                    'The tax calculation status is in progress, the tax cannot be calculated for this credit memo.',
                ),
            );
        }

        if (['posted', 'inProgress', 'error'].includes(await document.status)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__cant_calculate_tax_wrong_invoice_status',
                    'The status is {{posted}}, the tax cannot be calculated for this credit memo.',
                    {
                        posted: context.localizeEnumMember(
                            '@sage/xtrem-sales/SalesCreditMemoStatus',
                            await document.status,
                        ),
                    },
                ),
            );
        }

        const transactionPayloadManager = await xtremAvalaraGateway.classes.TransactionPayloadManager.create(
            context,
            await document.financialSite,
        );
        if (transactionPayloadManager.companyCode) {
            transactionPayloadManager.addDocumentToCheck(document._id, 'SalesCreditMemo', isCommit, isPosting);
            await transactionPayloadManager.sendNotification('SalesCreditMemo');
            return 'inProgress';
        }

        if (!transactionPayloadManager.companyCode && (await document.taxCalculationStatus) === 'notDone') {
            return 'notDone';
        }
        return 'done';
    }

    @decorators.mutation<typeof SalesCreditMemoExtension, 'postAvalara'>({
        isPublished: true,
        parameters: [
            {
                name: 'creditMemo',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => xtremSales.nodes.SalesCreditMemo,
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async postAvalara(
        context: Context,
        creditMemo: xtremSales.nodes.SalesCreditMemo,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        if ((await creditMemo.taxEngine) === 'avalaraAvaTax') {
            const { postResult, document } = await xtremSales.nodes.SalesCreditMemo.beforePost(context, creditMemo);

            if (!document) {
                return postResult;
            }

            const avataxClient = await (
                await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
            )?.isActive;

            if (!avataxClient) {
                postResult.wasSuccessful = false;
                postResult.message = context.localize(
                    '@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__avalara_integration_inactive',
                    'The Avalara integration is not activated.',
                );

                return postResult;
            }

            // reset to initial state to run avalara
            await creditMemo.$.set({ forceUpdateForFinance: true, status: 'draft', taxCalculationStatus: 'done' });
            creditMemo.__skipPrepare = true;
            await creditMemo.$.save();

            await this.calculateTax(
                context,
                creditMemo,
                await xtremAvalaraGateway.nodes.AvalaraConfiguration.getIsCommit(context),
                true,
            );

            postResult.wasSuccessful = true;
            postResult.message = context.localize(
                '@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__posted',
                'The sales credit memo has been posted.',
            );
            return postResult;
        }
        return xtremSales.nodes.SalesCreditMemo.post(context, creditMemo);
    }
}

declare module '@sage/xtrem-sales/lib/nodes/sales-credit-memo' {
    interface SalesCreditMemo extends SalesCreditMemoExtension {}
}
