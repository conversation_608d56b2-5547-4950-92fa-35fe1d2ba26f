import { decorators, SubNodeExtension1 } from '@sage/xtrem-core';
import * as xtremFinance from '@sage/xtrem-finance';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremAvalaraGateway from '../index';

@decorators.subNodeExtension1<AccountsReceivableInvoiceLineExtension>({
    extends: () => xtremFinance.nodes.AccountsReceivableInvoiceLine,
})
export class AccountsReceivableInvoiceLineExtension extends SubNodeExtension1<xtremFinance.nodes.AccountsReceivableInvoiceLine> {
    @decorators.jsonPropertyOverride<AccountsReceivableInvoiceLineExtension, 'uiTaxes'>({
        dependsOn: ['taxes'],
        async computeValue() {
            return xtremTax.functions.prepareTaxesToUiTaxes<xtremFinance.nodes.AccountsReceivableInvoiceLine>(
                this as xtremFinance.nodes.AccountsReceivableInvoiceLine,
                (await (await this.document).taxEngine) ?? '',
                xtremAvalaraGateway.functions.additionalUiTaxesLineProperties,
            );
        },
    })
    readonly uiTaxes: Promise<xtremTax.interfaces.UiTaxes | null>;
}

declare module '@sage/xtrem-finance/lib/nodes/accounts-receivable-invoice-line' {
    interface AccountsReceivableInvoiceLine extends AccountsReceivableInvoiceLineExtension {}
}
