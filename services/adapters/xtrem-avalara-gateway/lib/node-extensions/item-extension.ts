import type { Reference } from '@sage/xtrem-core';
import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremAvalaraGateway from '../index';

@decorators.nodeExtension<ItemExtension>({
    extends: () => xtremMasterData.nodes.Item,
})
export class ItemExtension extends NodeExtension<xtremMasterData.nodes.Item> {
    @decorators.referenceProperty<ItemExtension, 'avalaraItemTax'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremAvalaraGateway.nodes.AvalaraItemTax,
        serviceOptions: () => [xtremAvalaraGateway.serviceOptions.avalaraOption],
    })
    readonly avalaraItemTax: Reference<xtremAvalaraGateway.nodes.AvalaraItemTax | null>;
}

declare module '@sage/xtrem-master-data/lib/nodes/item' {
    interface Item extends ItemExtension {}
}
