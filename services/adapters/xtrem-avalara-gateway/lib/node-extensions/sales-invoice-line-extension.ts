import type { Reference } from '@sage/xtrem-core';
import { decorators, SubNodeExtension2 } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremAvalaraGateway from '../index';

@decorators.subNodeExtension2<SalesInvoiceLineExtension>({
    extends: () => xtremSales.nodes.SalesInvoiceLine,
})
export class SalesInvoiceLineExtension extends SubNodeExtension2<xtremSales.nodes.SalesInvoiceLine> {
    @decorators.referenceProperty<SalesInvoiceLineExtension, 'entityUse'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremAvalaraGateway.nodes.EntityUse,
        serviceOptions: () => [xtremAvalaraGateway.serviceOptions.avalaraOption],
    })
    readonly entityUse: Reference<xtremAvalaraGateway.nodes.EntityUse | null>;

    @decorators.jsonPropertyOverride<SalesInvoiceLineExtension, 'uiTaxes'>({
        dependsOn: ['taxes'],
        async computeValue() {
            return xtremTax.functions.prepareTaxesToUiTaxes<xtremSales.nodes.SalesInvoiceLine>(
                this as any,
                (await (
                    await this.document
                ).taxEngine)!,
                xtremAvalaraGateway.functions.additionalUiTaxesLineProperties,
            );
        },
    })
    readonly uiTaxes: Promise<xtremTax.interfaces.UiTaxes | null>;
}

declare module '@sage/xtrem-sales/lib/nodes/sales-invoice-line' {
    interface SalesInvoiceLine extends SalesInvoiceLineExtension {}
}
