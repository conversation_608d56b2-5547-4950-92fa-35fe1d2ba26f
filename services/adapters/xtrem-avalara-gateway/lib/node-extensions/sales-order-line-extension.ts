import type { Reference } from '@sage/xtrem-core';
import { decorators, SubNodeExtension2, useDefaultValue } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremAvalaraGateway from '../index';

@decorators.subNodeExtension2<SalesOrderLineExtension>({
    extends: () => xtremSales.nodes.SalesOrderLine,
})
export class SalesOrderLineExtension extends SubNodeExtension2<xtremSales.nodes.SalesOrderLine> {
    @decorators.referenceProperty<SalesOrderLineExtension, 'entityUse'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremAvalaraGateway.nodes.EntityUse,
        serviceOptions: () => [xtremAvalaraGateway.serviceOptions.avalaraOption],
        dependsOn: [{ document: ['entityUse'] }],
        async defaultValue() {
            return (await this.document)?.entityUse;
        },
        updatedValue: useDefaultValue,
    })
    readonly entityUse: Reference<xtremAvalaraGateway.nodes.EntityUse | null>;
}

declare module '@sage/xtrem-sales/lib/nodes/sales-order-line' {
    interface SalesOrderLine extends SalesOrderLineExtension {}
}
