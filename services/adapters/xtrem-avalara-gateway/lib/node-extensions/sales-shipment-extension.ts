import type { Context, Reference } from '@sage/xtrem-core';
import { date, decorators, SubNodeExtension1 } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremAvalaraGateway from '..';

@decorators.subNodeExtension1<SalesShipmentExtension>({
    extends: () => xtremSales.nodes.SalesShipment,
})
export class SalesShipmentExtension extends SubNodeExtension1<xtremSales.nodes.SalesShipment> {
    @decorators.referenceProperty<SalesShipmentExtension, 'entityUse'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremAvalaraGateway.nodes.EntityUse,
        serviceOptions: () => [xtremAvalaraGateway.serviceOptions.avalaraOption],
        async defaultValue() {
            return (await (await this.shipToCustomerAddress)?.deliveryDetail)?.entityUse || null;
        },
    })
    readonly entityUse: Reference<xtremAvalaraGateway.nodes.EntityUse | null>;

    static instantiateSalesInvoicesCreator(
        context: Context,
        invoiceDate: date = date.today(),
    ): xtremAvalaraGateway.classes.SalesInvoicesCreator {
        return new xtremAvalaraGateway.classes.SalesInvoicesCreator(context, invoiceDate);
    }
}

declare module '@sage/xtrem-sales/lib/nodes/sales-shipment' {
    interface SalesShipment extends SalesShipmentExtension {}
}
