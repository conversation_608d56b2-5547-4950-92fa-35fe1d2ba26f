import { decorators, NodeExtension } from '@sage/xtrem-core';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';

@decorators.nodeExtension<BaseTaxExtension>({
    extends: () => xtremTax.nodes.BaseTax,
})
export class BaseTaxExtension extends NodeExtension<xtremTax.nodes.BaseTax> {
    /**
     * Corresponding to avalara country
     */
    @decorators.stringProperty<BaseTaxExtension, 'country'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly country: Promise<string>;

    /**
     * Corresponding to avalara region
     */
    @decorators.stringProperty<BaseTaxExtension, 'region'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly region: Promise<string>;

    /**
     * Corresponding to avalara jurisCode
     */
    @decorators.stringProperty<BaseTaxExtension, 'jurisdictionCode'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly jurisdictionCode: Promise<string>;

    /**
     * Corresponding to avalara jurisdictionType
     */
    @decorators.stringProperty<BaseTaxExtension, 'jurisdictionType'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly jurisdictionType: Promise<string>;
}

declare module '@sage/xtrem-tax/lib/nodes/base-tax' {
    interface BaseTax extends BaseTaxExtension {}
}
