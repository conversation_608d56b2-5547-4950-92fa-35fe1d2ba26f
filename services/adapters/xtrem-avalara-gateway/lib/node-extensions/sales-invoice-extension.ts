import type { Context, decimal } from '@sage/xtrem-core';
import { BusinessRuleError, date, decorators, SubNodeExtension1 } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAvalaraGateway from '../index';

@decorators.subNodeExtension1<SalesInvoiceExtension>({
    extends: () => xtremSales.nodes.SalesInvoice,
    async saveBegin() {
        if (
            !(await this.skipCallAvalaraApi) &&
            (await this.status) === 'draft' &&
            !(await this.forceUpdateForFinance)
        ) {
            this.__transactionPayloadManager = await xtremAvalaraGateway.classes.TransactionPayloadManager.create(
                this.$.context,
                await this.financialSite,
            );
            if (this.__transactionPayloadManager.companyCode) {
                await this.$.set({ taxCalculationStatus: 'inProgress' });
            }
        }
    },
    async saveEnd() {
        if (
            this.__transactionPayloadManager?.companyCode &&
            !(await this.skipSendNotification) &&
            !(await this.forceUpdateForFinance)
        ) {
            this.__transactionPayloadManager.addDocumentToCheck(this._id, 'SalesInvoice');
            await this.__transactionPayloadManager.sendNotification('SalesInvoice');
        }
    },
})
export class SalesInvoiceExtension extends SubNodeExtension1<xtremSales.nodes.SalesInvoice> {
    @decorators.booleanProperty<SalesInvoiceExtension, 'skipCallAvalaraApi'>({
        defaultValue: false,
    })
    readonly skipCallAvalaraApi: Promise<boolean>;

    @decorators.booleanProperty<SalesInvoiceExtension, 'skipSendNotification'>({
        defaultValue: false,
    })
    readonly skipSendNotification: Promise<boolean>;

    @decorators.stringProperty<SalesInvoiceExtension, 'avalaraId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.id,
    })
    readonly avalaraId: Promise<string>;

    __transactionPayloadManager: xtremAvalaraGateway.classes.TransactionPayloadManager;

    @decorators.decimalProperty<SalesInvoiceExtension, 'totalDiscountAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalDiscountAmount: Promise<decimal>;

    static instantiateSalesCreditMemosCreator(
        context: Context,
        creditMemoDate: date = date.today(),
    ): xtremAvalaraGateway.classes.SalesCreditMemosCreator {
        return new xtremAvalaraGateway.classes.SalesCreditMemosCreator(context, creditMemoDate);
    }

    @decorators.mutation<typeof SalesInvoiceExtension, 'calculateTax'>({
        isPublished: true,
        parameters: [
            {
                name: 'invoice',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => xtremSales.nodes.SalesInvoice,
            },
            {
                name: 'isCommit',
                type: 'boolean',
                isMandatory: false,
            },
            {
                name: 'isPosting',
                type: 'boolean',
                isMandatory: false,
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async calculateTax(
        context: Context,
        invoice: xtremSales.nodes.SalesInvoice,
        isCommit = false,
        isPosting = false,
    ): Promise<string> {
        await invoice.$.set({
            taxCalculationStatus: await this.commonCalculateTaxSalesInvoice(context, invoice, isCommit, isPosting),
        });
        await invoice.$.save();

        return context.localize(
            '@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__tax_calculation_asked',
            'The tax calculation has been asked for this invoice.',
        );
    }

    static async commonCalculateTaxSalesInvoice(
        context: Context,
        document: xtremSales.nodes.SalesInvoice,
        isCommit: boolean,
        isPosting: boolean,
    ): Promise<xtremMasterData.enums.TaxCalculationStatus> {
        if (['posted', 'inProgress', 'error'].includes(await document.status)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__cant_calculate_tax_wrong_invoice_status',
                    'The status is {{posted}}, the tax cannot be calculated for this invoice.',
                    {
                        posted: context.localizeEnumMember(
                            '@sage/xtrem-sales/SalesInvoiceStatus',
                            await document.status,
                        ),
                    },
                ),
            );
        }

        if ((await document.taxCalculationStatus) === 'inProgress') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__cant_calculate_tax_wrong_tax_calculation_status',
                    'The tax calculation status is in progress, the tax cannot be calculated for this invoice.',
                ),
            );
        }

        const transactionPayloadManager = await xtremAvalaraGateway.classes.TransactionPayloadManager.create(
            context,
            await document.financialSite,
        );
        if (transactionPayloadManager.companyCode) {
            transactionPayloadManager.addDocumentToCheck(document._id, 'SalesInvoice', isCommit, isPosting);
            await transactionPayloadManager.sendNotification('SalesInvoice');
            return 'inProgress';
        }

        if (!transactionPayloadManager.companyCode && (await document.taxCalculationStatus) === 'notDone') {
            return 'notDone';
        }
        return 'done';
    }

    @decorators.mutation<typeof SalesInvoiceExtension, 'postAvalara'>({
        isPublished: true,
        parameters: [
            {
                name: 'invoice',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => xtremSales.nodes.SalesInvoice,
            },
        ],
        return: {
            type: 'reference',
            node: () => xtremSales.nodes.SalesInvoice,
        },
    })
    static async postAvalara(
        context: Context,
        invoice: xtremSales.nodes.SalesInvoice,
    ): Promise<xtremSales.nodes.SalesInvoice> {
        if ((await invoice.taxEngine) === 'avalaraAvaTax') {
            // Sends the invoice to posting on salesInvoiceControlAndCreateMutationResult and controls the result
            xtremSales.functions.SalesInvoiceLib.controlPost(context, {
                postResult: await xtremSales.functions.FinanceIntegration.salesInvoiceControlAndCreateMutationResult(
                    context,
                    invoice,
                ),
                isSafeToRetry: false,
                status: await invoice.status,
                taxCalculationStatus: await invoice.taxCalculationStatus,
                discountPaymentBeforeDate: await invoice.discountPaymentBeforeDate,
                dueDate: await invoice.dueDate,
            });

            const isActiveAvatax = await (
                await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context)
            )?.isActive;

            if (!isActiveAvatax) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__avalara_integration_inactive',
                        'The Avalara integration is not activated.',
                    ),
                );
            }
            // reset to initial state to run avalara
            await invoice.$.set({ forceUpdateForFinance: true, status: 'draft', taxCalculationStatus: 'done' });
            invoice.__skipPrepare = true;
            await invoice.$.save();

            await this.calculateTax(
                context,
                invoice,
                await xtremAvalaraGateway.nodes.AvalaraConfiguration.getIsCommit(context),
                true,
            );
            return invoice;
        }
        return xtremSales.nodes.SalesInvoice.post(context, invoice);
    }
}

declare module '@sage/xtrem-sales/lib/nodes/sales-invoice' {
    interface SalesInvoice extends SalesInvoiceExtension {}
}
