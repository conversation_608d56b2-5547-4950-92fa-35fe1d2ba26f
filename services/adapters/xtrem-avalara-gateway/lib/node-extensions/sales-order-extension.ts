import type { Collection, Context, Reference } from '@sage/xtrem-core';
import { decorators, SubNodeExtension1 } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremAvalaraGateway from '../index';

@decorators.subNodeExtension1<SalesOrderExtension>({
    extends: () => xtremSales.nodes.SalesOrder,
})
export class SalesOrderExtension extends SubNodeExtension1<xtremSales.nodes.SalesOrder> {
    @decorators.referenceProperty<SalesOrderExtension, 'entityUse'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremAvalaraGateway.nodes.EntityUse,
        serviceOptions: () => [xtremAvalaraGateway.serviceOptions.avalaraOption],
        dependsOn: ['shipToCustomerAddress'],
        async defaultValue() {
            return (await (await this.shipToCustomerAddress)?.deliveryDetail)?.entityUse || null;
        },
    })
    readonly entityUse: Reference<xtremAvalaraGateway.nodes.EntityUse | null>;

    @decorators.collectionPropertyOverride<SalesOrderExtension, 'lines'>({
        dependsOn: ['entityUse'],
    })
    readonly lines: Collection<xtremSales.nodes.SalesOrderLine>;

    static instantiateSalesShipmentsCreator(context: Context): xtremAvalaraGateway.classes.SalesShipmentsCreator {
        return new xtremAvalaraGateway.classes.SalesShipmentsCreator(context);
    }
}

declare module '@sage/xtrem-sales/lib/nodes/sales-order' {
    interface SalesOrder extends SalesOrderExtension {}
}
