import type { Reference } from '@sage/xtrem-core';
import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremAvalaraGateway from '../index';

@decorators.nodeExtension<DeliveryDetailExtension>({
    extends: () => xtremMasterData.nodes.DeliveryDetail,
})
export class DeliveryDetailExtension extends NodeExtension<xtremMasterData.nodes.DeliveryDetail> {
    @decorators.referenceProperty<DeliveryDetailExtension, 'entityUse'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremAvalaraGateway.nodes.EntityUse,
        serviceOptions: () => [xtremAvalaraGateway.serviceOptions.avalaraOption],
    })
    readonly entityUse: Reference<xtremAvalaraGateway.nodes.EntityUse | null>;
}

declare module '@sage/xtrem-master-data/lib/nodes/delivery-detail' {
    interface DeliveryDetail extends DeliveryDetailExtension {}
}
