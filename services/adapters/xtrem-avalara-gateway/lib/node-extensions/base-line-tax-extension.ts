import type { decimal } from '@sage/xtrem-core';
import { decorators, SubNodeExtension1 } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';

@decorators.subNodeExtension1<BaseLineTaxExtension>({
    extends: () => xtremTax.nodes.BaseLineTax,
})
export class BaseLineTaxExtension extends SubNodeExtension1<xtremTax.nodes.BaseLineTax> {
    /**
     * Corresponding to avalara taxableUnits
     */
    @decorators.decimalProperty<BaseLineTaxExtension, 'numberOfTaxableUnits'>({
        isStored: true,
        isPublished: true,
        defaultValue: 0,
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly numberOfTaxableUnits: Promise<decimal>;

    /**
     * Corresponding to avalara nonTaxableUnits
     */
    @decorators.decimalProperty<BaseLineTaxExtension, 'numberOfNonTaxableUnits'>({
        isStored: true,
        isPublished: true,
        defaultValue: 0,
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly numberOfNonTaxableUnits: Promise<decimal>;

    /**
     * Corresponding to avalara exemptUnits
     */
    @decorators.decimalProperty<BaseLineTaxExtension, 'numberOfExemptUnits'>({
        isStored: true,
        isPublished: true,
        defaultValue: 0,
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly numberOfExemptUnits: Promise<decimal>;
}

declare module '@sage/xtrem-tax/lib/nodes/base-line-tax' {
    interface BaseLineTax extends BaseLineTaxExtension {}
}
