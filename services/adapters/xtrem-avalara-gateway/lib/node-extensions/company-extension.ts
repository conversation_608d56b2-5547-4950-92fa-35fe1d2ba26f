import { decorators, NodeExtension, ServiceOption } from '@sage/xtrem-core';

import * as xtremSystem from '@sage/xtrem-system';

@decorators.nodeExtension<CompanyExtension>({
    extends: () => xtremSystem.nodes.Company,
    async controlBegin(cx) {
        const avalaraOption = new ServiceOption({
            __filename,
            name: 'avalaraOption',
            status: 'released',
            isHidden: false,
        });

        if (
            (await this.taxEngine) === 'avalaraAvaTax' &&
            !(await this.$.context.isServiceOptionEnabled(avalaraOption))
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-avalara-gateway/nodes__company_extension_avalara_service_option_off',
                'You need to activate Avalara in Option management before you can select it as a tax calculation package.',
            );
        }
    },
})
export class CompanyExtension extends NodeExtension<xtremSystem.nodes.Company> {}
declare module '@sage/xtrem-system/lib/nodes/company' {
    export interface Company extends CompanyExtension {}
}
