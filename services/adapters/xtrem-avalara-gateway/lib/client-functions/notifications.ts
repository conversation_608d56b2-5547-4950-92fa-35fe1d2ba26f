import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import {
    avalaraDocumentTypeSalesCreditMemo,
    avalaraDocumentTypeSalesCreditMemoExplicit,
    avalaraDocumentTypeSalesInvoice,
    avalaraDocumentTypeSalesInvoiceExplicit,
    listenerCreateTransactionExplicit,
    listenerListEntityUseCodesExplicit,
    listenerListTaxCodesExplicit,
    listenerQueryCompaniesExplicit,
    topicCreateTransactionRequest,
    topicListEntityUseCodesRequest,
    topicListTaxCodesRequest,
    topicQueryCompaniesRequest,
} from '../shared-functions/notification-messages';

import type { AvalaraNotificationHistory } from './interfaces/interfaces';

export function getMainTopic(topic: string): string {
    switch (topic) {
        case topicListEntityUseCodesRequest:
            return listenerListEntityUseCodesExplicit;
        case topicListTaxCodesRequest:
            return listenerListTaxCodesExplicit;
        case topicQueryCompaniesRequest:
            return listenerQueryCompaniesExplicit;
        case topicCreateTransactionRequest:
            return listenerCreateTransactionExplicit;
        default:
            return '';
    }
}

export function getDocumentType(type: string): string {
    switch (type) {
        case avalaraDocumentTypeSalesInvoice:
            return avalaraDocumentTypeSalesInvoiceExplicit;
        case avalaraDocumentTypeSalesCreditMemo:
            return avalaraDocumentTypeSalesCreditMemoExplicit;
        default:
            return '';
    }
}

export function getDocumentPageNameSales(constructor: string): string {
    switch (constructor) {
        case avalaraDocumentTypeSalesInvoiceExplicit: {
            return '@sage/xtrem-sales/SalesInvoice';
        }
        case avalaraDocumentTypeSalesCreditMemoExplicit: {
            return '@sage/xtrem-sales/SalesCreditMemo';
        }
        default:
            break;
    }
    return '';
}

export function getLineCurKey(line: ExtractEdgesPartial<AvalaraNotificationHistory>) {
    if (line.documentType) {
        return line.topic + line.documentType + line.documentNumber;
    }
    return line.topic || '';
}
