import type { Graph<PERSON><PERSON> } from '@sage/xtrem-avalara-gateway-api';
import type { Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { SysNotificationHistory } from '@sage/xtrem-communication-api';
import * as ui from '@sage/xtrem-ui';
import {
    getAvataxNotificationMessageLocalized,
    listenerCreateTransaction,
    topicCreateTransactionRequest,
} from '../shared-functions';
import type { AvalaraNotificationHistoryContext } from './interfaces/interfaces';

export function confirmDialog(page: ui.Page<GraphApi>, dialog: { title: string; message: string }) {
    const options = {
        acceptButton: {
            text: ui.localize('@sage/xtrem-avalara-gateway/pages-confirm-yes', 'Yes'),
        },
        cancelButton: {
            text: ui.localize('@sage/xtrem-avalara-gateway/pages-confirm-no', 'No'),
        },
    };
    return page.$.dialog
        .confirmation('warn', dialog.title, dialog.message, options)
        .then(() => true)
        .catch(() => false);
}

function getAvataxNotificationMessageFilter(): Filter<SysNotificationHistory> {
    const filter = { _and: [{}] };
    filter._and.push({
        listener: {
            _eq: listenerCreateTransaction,
        },
        topic: { _eq: topicCreateTransactionRequest },
        status: { _eq: 'error' },
    });
    return filter._and.length ? filter : {};
}

export async function getAvataxNotificationMessageOnDocument(page: ui.Page, documentType: string, documentId: string) {
    const avalaraNotifications = extractEdges<SysNotificationHistory>(
        await page.$.graph
            .node('@sage/xtrem-communication/SysNotificationHistory')
            .query(
                ui.queryUtils.edgesSelector<SysNotificationHistory>(
                    {
                        _id: true,
                        status: true,
                        errorMessage: true,
                        dateLogged: true,
                        notificationContext: true,
                    },
                    { filter: getAvataxNotificationMessageFilter(), orderBy: { _id: -1 } }, // first: 1,
                ),
            )
            .execute(),
    );

    let avalaraNotificationMessage = '';
    if (avalaraNotifications.length > 0) {
        const avalaraNotification = avalaraNotifications.find(sysNotificationHistoryLine => {
            const notificationContext: AvalaraNotificationHistoryContext = JSON.parse(
                sysNotificationHistoryLine.notificationContext,
            );

            return (
                notificationContext.documentId.toString() === documentId &&
                notificationContext.documentType.toString() === documentType
            );
        });
        if (avalaraNotification && avalaraNotification.notificationContext) {
            const externalMessage = JSON.parse(avalaraNotification.notificationContext).message;
            const internalMessage = getAvataxNotificationMessageLocalized(
                ui,
                JSON.parse(avalaraNotification.notificationContext).code,
            );
            if (externalMessage !== '') {
                avalaraNotificationMessage = `${internalMessage} [${externalMessage}]`;
            } else {
                avalaraNotificationMessage = `${internalMessage}`;
            }
        }
    }
    return avalaraNotificationMessage;
}
