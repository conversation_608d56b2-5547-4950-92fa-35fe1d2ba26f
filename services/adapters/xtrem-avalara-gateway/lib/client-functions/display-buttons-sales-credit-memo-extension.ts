import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-sales-credit-memo-extension';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-sales-credit-memo-extension';

export function isHiddenButtonAvalaraAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.AvalaraParameters>,
) {
    if (
        !data.recordId ||
        (data.parameters.status && ['posted', 'inProgress', 'error'].includes(data.parameters.status)) ||
        data.parameters.taxCalculationStatus === 'inProgress' ||
        data.parameters.taxCalculationStatus === 'done' ||
        data.parameters.taxEngine === 'genericTaxCalculation'
    ) {
        return true;
    }
    return data.isDirty;
}

export function isDisabledButtonAvalaraAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.AvalaraParameters>,
) {
    return isHiddenButtonAvalaraAction(data);
}
