import type { SysNotificationHistory as SysNotificationHistoryNode } from '@sage/xtrem-communication-api';

export interface AvalaraNotificationHistory extends SysNotificationHistoryNode {
    topic: string;
    documentType: string;
    documentId: number;
    documentNumber: string;
    isCommit: boolean;
    isPosting: boolean;
    recordCount: number;
    message: string;
    internalMessage: string;
    code: string;
}

export interface AvalaraNotificationHistoryContext {
    topic: string;
    documentType: string;
    documentId: number;
    documentNumber: string;
    isCommit: boolean;
    isPosting: boolean;
    recordCount: number;
    message: string;
    code: string;
}
