{"@sage/xtrem-avalara-gateway/activity__avalara_company__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/activity__avalara_configuration__name": "Konfiguration <PERSON><PERSON>a", "@sage/xtrem-avalara-gateway/activity__avalara_item_tax__name": "Artikelsteuer Avalara", "@sage/xtrem-avalara-gateway/activity__avalara_option_management__name": "Optionsverwaltung Avalara", "@sage/xtrem-avalara-gateway/activity__entity_use__name": "Verwendung Entität", "@sage/xtrem-avalara-gateway/avalara_notification_dialog_title": "Benachrichtigung Avalara", "@sage/xtrem-avalara-gateway/configuration-page": "Konfigurationsseite", "@sage/xtrem-avalara-gateway/data_types__address_text_case_enum__name": "Enum Groß-/Kleinschreibung Adresse", "@sage/xtrem-avalara-gateway/data_types__document_type_enum__name": "Enum Dokumenttyp", "@sage/xtrem-avalara-gateway/data_types__entity_type_enum__name": "Enum Entitätstyp", "@sage/xtrem-avalara-gateway/data_types__transaction_payload_manager_enum__name": "Enum Payload-Manager Transaktion", "@sage/xtrem-avalara-gateway/enums__address_text_case__mixed": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/enums__address_text_case__upper": "Großschreibung", "@sage/xtrem-avalara-gateway/enums__document_type__salesCreditMemo": "Verkaufsgutschrift", "@sage/xtrem-avalara-gateway/enums__document_type__salesInvoice": "Verkaufsrechnung", "@sage/xtrem-avalara-gateway/enums__entity_type__company": "Unternehmen", "@sage/xtrem-avalara-gateway/enums__entity_type__financialSite": "Buchhaltungsstandort", "@sage/xtrem-avalara-gateway/enums__transaction_payload_manager_type__SalesCreditMemo": "Verkaufsgutschrift", "@sage/xtrem-avalara-gateway/enums__transaction_payload_manager_type__SalesInvoice": "Verkaufsrechnung", "@sage/xtrem-avalara-gateway/enums__transaction_payload_manger__SalesCreditMemo": "Verkaufsgutschrift", "@sage/xtrem-avalara-gateway/enums__transaction_payload_manger__SalesInvoice": "Verkaufsrechnung", "@sage/xtrem-avalara-gateway/load-companies-button": "Unternehmen", "@sage/xtrem-avalara-gateway/load-entity-use-button": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/load-tax-codes-button": "Steuercodes", "@sage/xtrem-avalara-gateway/menu_item__avalara": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/node__sales_credit_memo__cant_calculate_tax_wrong_invoice_status": "Der Status ist {{posted}}. Die Steuer kann für diese Gutschrift nicht berechnet werden.", "@sage/xtrem-avalara-gateway/node__sales_credit_memo__cant_calculate_tax_wrong_tax_calculation_status": "Der Status der Steuerberechnung ist {{inProgress}} oder {{done}}. Die Steuer kann für diese Gutschrift nicht berechnet werden.", "@sage/xtrem-avalara-gateway/node__sales_invoice__cant_calculate_tax_wrong_invoice_status": "Die Steuer kann für diese Rechnung nicht berechnet werden. Die Rechnung ist {{posted}}.", "@sage/xtrem-avalara-gateway/node__sales_invoice__cant_calculate_tax_wrong_tax_calculation_status": "Die Steuer kann für diese Rechnung nicht berechnet werden. Die Berechnung ist {{inProgress}} oder {{done}}.", "@sage/xtrem-avalara-gateway/node-extensions__accounts_receivable_invoice_line_extension__property__uiTaxes": "Steuern Arbeitslosenversicherung", "@sage/xtrem-avalara-gateway/node-extensions__base_line_tax_extension__property__numberOfExemptUnits": "<PERSON><PERSON><PERSON> befreiter Einheiten", "@sage/xtrem-avalara-gateway/node-extensions__base_line_tax_extension__property__numberOfNonTaxableUnits": "<PERSON><PERSON><PERSON> nicht steuerpflichtiger Einheiten", "@sage/xtrem-avalara-gateway/node-extensions__base_line_tax_extension__property__numberOfTaxableUnits": "<PERSON><PERSON><PERSON> steuerpflichtiger Einheiten", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__country": "Land", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__jurisdictionCode": "Code Zuständigkeit", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__jurisdictionType": "Typ Zuständigkeit", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__region": "Region", "@sage/xtrem-avalara-gateway/node-extensions__delivery_detail_extension__property__entityUse": "Verwendung Entität", "@sage/xtrem-avalara-gateway/node-extensions__item_extension__property__avalaraItemTax": "Artikelsteuer Avalara", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax": "<PERSON><PERSON><PERSON> berechnen", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__failed": "Steuer berechnen fehlgeschlagen.", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__parameter__creditMemo": "Gutschrift", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__parameter__isCommit": "Zusagen", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__parameter__isPosting": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__postAvalara": "<PERSON><PERSON><PERSON> buchen", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__postAvalara__failed": "Avalara buchen fehlgeschlagen.", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__postAvalara__parameter__creditMemo": "Gutschrift", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__property__avalaraId": "Avalara-ID", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__property__skipCallAvalaraApi": "Aufruf <PERSON>a-API überspringen", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__property__totalDiscountAmount": "Gesamtrabattbetrag", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_line_extension__property__entityUse": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_line_extension__property__uiTaxes": "Steuern Arbeitslosenversicherung", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax": "<PERSON><PERSON><PERSON> berechnen", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__failed": "Steuer berechnen fehlgeschlagen.", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__parameter__invoice": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__parameter__isCommit": "Zusagen", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__parameter__isPosting": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__postAvalara": "<PERSON><PERSON><PERSON> buchen", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__postAvalara__failed": "Avalara buchen fehlgeschlagen.", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__postAvalara__parameter__invoice": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__avalaraId": "Avalara-ID", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__skipCallAvalaraApi": "Aufruf <PERSON>a-API überspringen", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__skipSendNotification": "Benachrichtigung senden überspringen", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__totalDiscountAmount": "Gesamtrabattbetrag", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_line_extension__property__entityUse": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_line_extension__property__uiTaxes": "Steuern Arbeitslosenversicherung", "@sage/xtrem-avalara-gateway/node-extensions__sales_order_extension__property__entityUse": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/node-extensions__sales_order_extension__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/node-extensions__sales_order_line_extension__property__entityUse": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/node-extensions__sales_shipment_extension__property__entityUse": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/nodes__avalara_company__asyncMutation__asyncExport": "Export", "@sage/xtrem-avalara-gateway/nodes__avalara_company__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-avalara-gateway/nodes__avalara_company__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_company__load_company_codes_from_avalara_asked": "Die Avalara-Unternehmenscodes werden geladen. Der Vorgang wird im Hintergrund ausgeführt.", "@sage/xtrem-avalara-gateway/nodes__avalara_company__mutation__loadCompanyFromAvalara": "Unternehmen aus Avalara laden", "@sage/xtrem-avalara-gateway/nodes__avalara_company__mutation__loadCompanyFromAvalara__failed": "Unternehmen aus Avalara laden fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara_company__node_name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/nodes__avalara_company__property__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_company__property__isActive": "Aktiv", "@sage/xtrem-avalara-gateway/nodes__avalara_company__property__name": "Name", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__asyncMutation__asyncExport": "Export", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__mutation__ping": "<PERSON>", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__mutation__ping__failed": "Ping fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__mutation__ping__parameter__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__node_name": "Konfiguration <PERSON><PERSON>a", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__accountId": "Konto-ID", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__addressTextCase": "Groß-/Kleinschreibung Adresse", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__endPointUrl": "Endpoint-URL", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isActive": "Aktiv", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isAddressValidationActive": "Adressfreigabe aktiv", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isCommittedTransaction": "Zugesagte Transaktion", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isSandboxMode": "Sandbox-Modus", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__licenseKey": "Lizenzschlüssel", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__mapCompanyLines": "Zuordnung Unternehmenszeilen", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__setupId": "ID Einstellungen", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__defaultInstance": "Standardinstanz", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__defaultInstance__failed": "Standardinstanz fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getIsCommit": "Ist Zusage abrufen", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getIsCommit__failed": "Ist Zusage abrufen fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getUrl": "URL abrufen", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getUrl__failed": "URL abrufen fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getUrl__parameter__isSandBox": "Sandbox", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__load_tax_codes_from_avalara_asked": "Die Steuercodes werden geladen. Der Vorgang wird im Hintergrund ausgeführt.", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara": "Steuercodes aus Avalara laden", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__failed": "Steuercodes aus Avalara laden fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__description": "Bezeichnung", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__fromTaxCode": "Steuer<PERSON> von", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__isActive": "Aktiv", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__toTaxCode": "Steuercode bis", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__node_name": "Artikelsteuer Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__property__description": "Bezeichnung", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__property__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__property__isActive": "Aktiv", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__asyncMutation__asyncExport": "Export", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__mutation__serviceOptionChange": "Änderung Dienstoption", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__mutation__serviceOptionChange__failed": "Änderung Dienstoption fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__node_name": "Optionsverwaltung Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__query__isServiceOptionActiveFunction": "Funktion Dienstoption aktiv", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__query__isServiceOptionActiveFunction__failed": "Ist Funktion Dienstoption aktiv fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara_response_listener__node_name": "Response-Listener <PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-failed": "Liste Benachrichtigung Entitätscode fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-sent": "Liste Benachrichtigung Entitätscode gesendet.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-succeeded": "Liste Benachrichtigung Entitätscode erfolgreich. (Erhalten: {{recordCount}})", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-failed": "Liste Benachrichtigung Steuercode fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-sent": "Liste Benachrichtigung Steuercode gesendet.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-succeeded": "Liste Benachrichtigung Steuercode erfolgreich. (Erhalten: {{recordCount}})", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-failed": "Benachrichtigung Ping fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-sent": "Benachrichtigung Ping gesendet.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-succeeded": "Benachrichtigung Ping erfolgreich.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-failed": "Abfrage Benachrichtigung Unternehmen fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-sent": "Abfrage Benachrichtigung Unternehmen gesendet.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-succeeded": "Abfrage Benachrichtigung Unternehmen erfolgreich. (Erhalten: {{recordCount}})", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-failed": "Auflösen der Adressbenachrichtigung fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-sent": "Auflösen der Adressbenachrichtigung gesendet.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-succeeded": "Auflösen der Adressbenachrichtigung erfolgreich.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-post-and-commit": "Benachrichtigung Verkaufsgutschrift für Buchung mit Option zur Zusage fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-post-only": "Benachrichtigung Verkaufsgutschrift für Buchung mit Option zur Aufhebung der Zusage fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-taxes-and-values-only": "Benachrichtigung Verkaufsgutschrift für Steuerberechnung fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-post-and-commit": "Benachrichtigung Verkaufsgutschrift für Buchung mit Option zur Zusage gesendet.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-post-only": "Benachrichtigung Verkaufsgutschrift für Buchung mit Option zur Aufhebung der Zusage gesendet.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-taxes-and-values-only": "Benachrichtigung Verkaufsgutschrift für Steuerberechnung gesendet.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-post-and-commit": "Benachrichtigung Verkaufsgutschrift für Buchung mit Option zur Zusage erfolgreich.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-post-only": "Benachrichtigung Verkaufsgutschrift für Buchung mit Option zur Aufhebung der Zusage erfolgreich.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-taxes-and-values-only": "Benachrichtigung Verkaufsgutschrift für Steuerberechnung erfolgreich.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-post-and-commit": "Benachrichtigung Verkaufsrechnung für Buchung mit Option zur Zusage fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-post-only": "Benachrichtigung Verkaufsrechnung für Buchung mit Option zur Aufhebung der Zusage fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-taxes-and-values-only": "Benachrichtigung Verkaufsrechnung für Steuerberechnung fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-post-and-commit": "Benachrichtigung Verkaufsrechnung für Buchung mit Option zur Zusage gesendet.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-post-only": "Benachrichtigung Verkaufsrechnung für Buchung mit Option zur Aufhebung der Zusage gesendet.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-taxes-and-values-only": "Benachrichtigung Verkaufsrechnung für Steuerberechnung gesendet.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-post-and-commit": "Benachrichtigung Verkaufsrechnung für Buchung mit Option zur Zusage erfolgreich.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-post-only": "Benachrichtigung Verkaufsrechnung für Buchung mit Option zur Aufhebung der Zusage erfolgreich.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-taxes-and-values-only": "Benachrichtigung Verkaufsrechnung für Steuerberechnung erfolgreich.", "@sage/xtrem-avalara-gateway/nodes__avatax_listener__node_name": "Listener <PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/nodes__company_extension_avalara_service_option_off": "<PERSON>e müssen Avalara in der Optionsverwaltung aktivieren, bevor <PERSON> als Steuerberechnungspaket auswählen können.", "@sage/xtrem-avalara-gateway/nodes__entity_use__asyncMutation__asyncExport": "Export", "@sage/xtrem-avalara-gateway/nodes__entity_use__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-avalara-gateway/nodes__entity_use__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-avalara-gateway/nodes__entity_use__load_entity_use_codes_from_avalara_asked": "Die Codes für die Verwendung der Entität werden geladen. Der Vorgang wird im Hintergrund ausgeführt.", "@sage/xtrem-avalara-gateway/nodes__entity_use__mutation__loadEntityUseCodesFromAvalara": "Codes für die Verwendung der Entität aus Avalara laden", "@sage/xtrem-avalara-gateway/nodes__entity_use__mutation__loadEntityUseCodesFromAvalara__failed": "Codes für die Verwendung der Entität aus Avalara laden fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__entity_use__node_name": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__description": "Bezeichnung", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__id": "ID", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__isActive": "Aktiv", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__name": "Name", "@sage/xtrem-avalara-gateway/nodes__map_company__asyncMutation__asyncExport": "Export", "@sage/xtrem-avalara-gateway/nodes__map_company__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-avalara-gateway/nodes__map_company__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-avalara-gateway/nodes__map_company__avalara_company_check_duplicate": "Das Avalara-Unternehmen ist bereits vorhanden.", "@sage/xtrem-avalara-gateway/nodes__map_company__company_check_duplicate": "Das Unternehmen ist bereits vorhanden.", "@sage/xtrem-avalara-gateway/nodes__map_company__company_mandatory": "Erfassen Si<PERSON> ein Unternehmen.", "@sage/xtrem-avalara-gateway/nodes__map_company__company_must_be_empty": "Die Entität ist ein Buchhaltungsstandort. Lassen Sie das Unternehmen leer.", "@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_check_duplicate": "Der Buchhaltungsstandort ist bereits vorhanden.", "@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_mandatory": "Erfassen Sie einen Buchhaltungsstandort.", "@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_must_be_empty": "Die Entität ist ein Unternehmen. Lassen Sie den Buchhaltungsstandort leer.", "@sage/xtrem-avalara-gateway/nodes__map_company__node_name": "<PERSON><PERSON>rd<PERSON>ng Unternehmen", "@sage/xtrem-avalara-gateway/nodes__map_company__property__avalaraCompany": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/nodes__map_company__property__avalaraConfiguration": "Konfiguration <PERSON><PERSON>a", "@sage/xtrem-avalara-gateway/nodes__map_company__property__company": "Unternehmen", "@sage/xtrem-avalara-gateway/nodes__map_company__property__entityType": "Entitätstyp", "@sage/xtrem-avalara-gateway/nodes__map_company__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-avalara-gateway/nodes__map_company__property__isActive": "Aktiv", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany": "Avalara-Unternehmen freigeben", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany__failed": "Avalara-Unternehmen freigeben fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany__parameter__avalaraCompany": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany__parameter__cachedAvalaraCompanies": "Zwischengespeicherte Avalara-Unternehmen", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany": "Unternehmen freigeben", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__failed": "Unternehmen freigeben fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__parameter__cachedCompanies": "Zwischengespeicherte Unternehmen", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__parameter__company": "Unternehmen", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__parameter__entityType": "Entitätstyp", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite": "Buchhaltungsstandort freigeben", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__failed": "Buchhaltungsstandort freigeben fehlgeschlagen.", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__parameter__cachedFinancialSites": "Zwischengespeicherte Buchhaltungsstandorte", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__parameter__entityType": "Entitätstyp", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__parameter__financialSite": "Buchhaltungsstandort", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo__tax_calculation_asked": "<PERSON><PERSON><PERSON> diese Gutschrift wurde die Steuerberechnung angefordert.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__avalara_integration_inactive": "Die Avalara-Integration ist nicht aktiviert.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__cant_calculate_tax_wrong_invoice_status": "Der Status ist {{posted}}. Die Steuer kann für diese Gutschrift nicht berechnet werden.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__cant_calculate_tax_wrong_tax_calculation_status": "Der Status der Steuerberechnung ist {{inProgress}} oder {{done}}. Die Steuer kann für diese Gutschrift nicht berechnet werden.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__posted": "Die Verkaufsgutschrift wurde gebucht.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__tax_calculation_asked": "<PERSON><PERSON><PERSON> diese Gutschrift wurde die Steuerberechnung angefordert.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice__tax_calculation_asked": "<PERSON><PERSON><PERSON> diese Rechnung wurde eine Steuerberechnung angefordert.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__avalara_integration_inactive": "Die Avalara-Integration ist nicht aktiviert.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__cant_calculate_tax_wrong_invoice_status": "Der Status ist {{posted}}. Die Steuer kann für diese Rechnung nicht berechnet werden.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__cant_calculate_tax_wrong_tax_calculation_status": "Der Status der Steuerberechnung ist {{inProgress}} oder {{done}}. Die Steuer kann für diese Rechnung nicht berechnet werden.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__tax_calculation_asked": "<PERSON><PERSON><PERSON> diese Rechnung wurde die Steuerberechnung angefordert.", "@sage/xtrem-avalara-gateway/package__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page__title__avalara_configuration": "Konfiguration <PERSON><PERSON>a", "@sage/xtrem-avalara-gateway/page-extensions__accounts_receivable_invoice_extension__taxDetails____columns__title__jurisdictionName": "Zuständigkeit", "@sage/xtrem-avalara-gateway/page-extensions__business_entity_address_panel_extension__entityUse____title": "Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__addresses____columnOverrides__title": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__deliveryAddresses____columns__columns__entityUse__title": "Name", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__deliveryAddresses____columns__columns__entityUse__title__2": "ID", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__deliveryAddresses____columns__columns__entityUse__title__3": "Bezeichnung", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__deliveryAddresses____columns__title__entityUse": "Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__item_extension____navigationPanel__listItem__avalaraItemTax__title": "Artikelsteuer Avalara", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__avalaraItemTax____lookupDialogTitle": "Artikelsteuer Avalara au<PERSON>wählen", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__avalaraItemTax____title": "Artikelsteuer Avalara", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__itemTaxability____columns__title__description": "Bezeichnung", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__itemTaxability____columns__title__id": "ID", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__itemTaxability____title": "Steuercode Artikel", "@sage/xtrem-avalara-gateway/page-extensions__option_management_base_extension__avalaraBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__option_management_base_extension__avalaraConfigurationPage____title": "Konfigurationsseite <PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__option_management_base_extension__isAvalaraServiceOptionActive____title": "Aktiv", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalara____title": "<PERSON><PERSON><PERSON> berechnen", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalaraMessage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalaraNotificationBlock____title": "Informationen", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalaraNotificationSection____title": "Benachrichtigung Avalara", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__columns__entityUse__title": "Name", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__columns__entityUse__title__2": "ID", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__columns__entityUse__title__3": "Bezeichnung", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__title__entityUse": "Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__title__entityUse__name": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__taxDetails____columns__title__jurisdictionName": "Zuständigkeit", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalara____title": "<PERSON><PERSON><PERSON> berechnen", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalaraMessage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalaraNotificationBlock____title": "Informationen", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalaraNotificationSection____title": "Benachrichtigung Avalara", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__columns__entityUse__title": "Name", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__columns__entityUse__title__2": "ID", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__columns__entityUse__title__3": "Bezeichnung", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__title__entityUse": "Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__title__entityUse__name": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__taxDetails____columns__title__jurisdictionName": "Zuständigkeit", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension____navigationPanel__listItem__line30__title": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__entityUse____title": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__columns__entityUse__title": "Name", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__columns__entityUse__title__2": "ID", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__columns__entityUse__title__3": "Bezeichnung", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__title__entityUse": "Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__title__entityUse__name": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_card_view_extension__salesOrderLines____columns__columns__entityUse__title": "Name", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_card_view_extension__salesOrderLines____columns__columns__entityUse__title__2": "ID", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_card_view_extension__salesOrderLines____columns__columns__entityUse__title__3": "Bezeichnung", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_card_view_extension__salesOrderLines____columns__title__entityUse": "Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_panel_extension__salesOrderLines____columns__columns__entityUse__name__title": "Name", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_panel_extension__salesOrderLines____columns__columns__entityUse__name__title__2": "ID", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_panel_extension__salesOrderLines____columns__columns__entityUse__name__title__3": "Bezeichnung", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_panel_extension__salesOrderLines____columns__title__entityUse__name": "Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__sales_shipment_extension____navigationPanel__listItem__line11__title": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__sales_shipment_extension__entityUse____title": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraDocumentNumber____title": "Dokumentnummer", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraDocumentType____title": "Dokumenttyp", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__code": "Code", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__dateLogged": "Datum", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__documentId": "Dokument-ID", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__documentNumber": "Dokumentnummer", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__documentType": "Dokumenttyp", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__errorMessage": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__internalMessage": "<PERSON>ne <PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__isCommit": "Zusagen", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__isPosting": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__message": "Fehlermeldung", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__recordCount": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__status": "Status", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__topic": "<PERSON>a", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____title": "Ergebnisse", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistoryBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistoryCriteriaBlock____title": "Kriterien", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistoryStatus____title": "Status", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraLatestNotification____title": "Letzte Benachrichtigungen", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraMainTopic____title": "Themen", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraTopic____title": "Themen", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__fromDate____title": "<PERSON><PERSON> von", "@sage/xtrem-avalara-gateway/page-extensions__tax_panel_extension__taxDetails____columns__title__jurisdictionName": "Zuständigkeit", "@sage/xtrem-avalara-gateway/pages__avalara_company____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-avalara-gateway/pages__avalara_company____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-avalara-gateway/pages__avalara_company____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-avalara-gateway/pages__avalara_company____objectTypePlural": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_company____objectTypeSingular": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_company____title": "Unternehmen", "@sage/xtrem-avalara-gateway/pages__avalara_company__id____title": "ID", "@sage/xtrem-avalara-gateway/pages__avalara_company__isActive____title": "Aktiv", "@sage/xtrem-avalara-gateway/pages__avalara_company__mainSection____title": "Allgemein", "@sage/xtrem-avalara-gateway/pages__avalara_company__name____title": "Name", "@sage/xtrem-avalara-gateway/pages__avalara_configuration____subtitle": "Konfiguration <PERSON><PERSON>a", "@sage/xtrem-avalara-gateway/pages__avalara_configuration____title": "Konfiguration", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__accountId____title": "Konto-ID", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__addMapCompany____title": "Hinzufügen", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__addressTextCase____title": "Groß-/Kleinschreibung Adresse", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__authenticated_sucessfull": "Angemeldet in {{version}} mit Konto {{authenticatedUserName}}.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__authentication_not_sucessfull": "Angemeldet in {{version}} ohne Authentifizierung.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__avalara_item_tax_dialog_content": "<PERSON><PERSON> sind da<PERSON>, Steuercodes aus Avalara zu laden. Bestätigen?", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__avalara_item_tax_dialog_title": "Laden bestätigen", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__companies_cannot_be_loaded": "Avalara-Unternehmenscodes können nicht geladen werden.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__confirm_delete": "Löschen", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__delete_dialog_content": "<PERSON><PERSON> sind dabei, diesen Datensatz zu löschen.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__delete_dialog_title": "Löschen bestätigen", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__descriptionFilter____title": "Bezeichnung", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__endPointUrl____title": "Endpoint", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__entity_use_cannot_be_loaded": "Die Codes für die Verwendung der Entität können nicht geladen werden.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__fromTaxCode____title": "Steuer<PERSON> von", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__id____title": "ID", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isActive____title": "Aktiv", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isActiveFilter____title": "Aktiv", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isAddressValidationActive____title": "Adressfreigabe", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isCommittedTransaction____title": "Zugesagte Transaktion", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isSandboxMode____title": "Sandbox-Modus", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__licenseKey____title": "Lizenzschlüssel", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__load_button_text": "Laden", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__load_tax_exception": "Steuercodes aus Avalara können nicht geladen werden. ({{exception}})", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__loadBlock____title": "Daten aus Avalara laden", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__loadTaxCodesBlock____title": "Filterkriterien", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__loadTaxCodesSection____title": "Steuercodes aus Avalara laden", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mainSection____title": "Allgemein", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyBlock____title": "Mapping Unternehmen", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__lookupDialogTitle__avalaraCompany__id": "Avalara-Unternehmen auswählen", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__lookupDialogTitle__company__id": "Unternehmen auswählen", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__lookupDialogTitle__financialSite__id": "Buchhaltungsstandort auswählen", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__avalaraCompany__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__company__id": "Unternehmen", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__entityType": "Entitätstyp", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__financialSite__id": "Buchhaltungsstandort", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__isActive": "Aktiv", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____dropdownActions__title__2": "Löschen", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____title": "Unternehmen", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanySection____title": "<PERSON><PERSON>rd<PERSON>ng Unternehmen", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__taxes_cannot_be_loaded": "Steuercodes aus Avalara können nicht geladen werden.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__testConnection____title": "Testverbindung", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__toTaxCode____title": "Steuercode bis", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____objectTypePlural": "Artikelsteuern <PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____objectTypeSingular": "Artikelsteuer Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__description____title": "Bezeichnung", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__id____title": "ID", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__isActive____title": "Aktiv", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__mainSection____title": "Allgemein", "@sage/xtrem-avalara-gateway/pages__delete_avalara_link_company_dialog_content": "Mit dem Steuerberechnungspaket Avalara verknüpfte Unternehmen: {{noCompanies}}. <PERSON><PERSON> <PERSON><PERSON> fort<PERSON>hren, werden diese auf die generische Steuerberechnung umgestellt.", "@sage/xtrem-avalara-gateway/pages__delete_avalara_link_company_dialog_title": "Steuerverwaltung bestätigen", "@sage/xtrem-avalara-gateway/pages__entity_use____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-avalara-gateway/pages__entity_use____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-avalara-gateway/pages__entity_use____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-avalara-gateway/pages__entity_use____objectTypePlural": "Codes Verwendung Entität Avalara", "@sage/xtrem-avalara-gateway/pages__entity_use____objectTypeSingular": "Code Verwendung Entität Avalara", "@sage/xtrem-avalara-gateway/pages__entity_use____title": "Code Verwendung Entität", "@sage/xtrem-avalara-gateway/pages__entity_use__description____title": "Bezeichnung", "@sage/xtrem-avalara-gateway/pages__entity_use__id____title": "ID", "@sage/xtrem-avalara-gateway/pages__entity_use__isActive____title": "Aktiv", "@sage/xtrem-avalara-gateway/pages__entity_use__mainSection____title": "Allgemein", "@sage/xtrem-avalara-gateway/pages__entity_use__name____title": "Name", "@sage/xtrem-avalara-gateway/pages__item_taxability____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-avalara-gateway/pages__item_taxability____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-avalara-gateway/pages__item_taxability____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-avalara-gateway/pages__item_taxability____subtitle": "Steuercode Artikel", "@sage/xtrem-avalara-gateway/pages__item_taxability____title": "Steuercode Artikel", "@sage/xtrem-avalara-gateway/pages__item_taxability__description____title": "Bezeichnung", "@sage/xtrem-avalara-gateway/pages__item_taxability__id____title": "ID", "@sage/xtrem-avalara-gateway/pages__item_taxability__isActive____title": "Aktiv", "@sage/xtrem-avalara-gateway/pages__map_company__edit____title": "Mapping-Unterneh<PERSON> bearbeiten", "@sage/xtrem-avalara-gateway/pages__map_company_panel____title": "<PERSON><PERSON>rd<PERSON>ng Unternehmen", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraCompany____lookupDialogTitle": "Avalara-Unternehmen auswählen", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraCompany____placeholder": "Avalara-Unternehmen auswählen", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraCompany____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraConfiguration____title": "Konfiguration <PERSON><PERSON>a", "@sage/xtrem-avalara-gateway/pages__map_company_panel__cancel____title": "Abbrechen", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____columns__title__description": "Bezeichnung", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____columns__title__id": "ID", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____columns__title__name": "Name", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____lookupDialogTitle": "Unternehmen auswählen", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____placeholder": "Unternehmen auswählen", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____title": "Unternehmen", "@sage/xtrem-avalara-gateway/pages__map_company_panel__confirm____title": "OK", "@sage/xtrem-avalara-gateway/pages__map_company_panel__entityType____title": "Entitätstyp", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____lookupDialogTitle": "Buchhaltungsstandort auswählen", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____placeholder": "Auswählen ...", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____title": "Buchhaltungsstandort", "@sage/xtrem-avalara-gateway/pages__map_company_panel__isActive____title": "Aktiv", "@sage/xtrem-avalara-gateway/pages__map_company_panel__mainSection____title": "Allgemein", "@sage/xtrem-avalara-gateway/pages__map_company_panel__save____title": "OK", "@sage/xtrem-avalara-gateway/pages__map-company-panel__edit____title": "Unternehmenszuordnung bearbeiten", "@sage/xtrem-avalara-gateway/pages__map-company-panel__new____title": "Unternehmenszuordnung hinzufügen", "@sage/xtrem-avalara-gateway/pages__test_conntection": "Testverbindung", "@sage/xtrem-avalara-gateway/pages-confirm-no": "<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages-confirm-yes": "<PERSON>a", "@sage/xtrem-avalara-gateway/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/service_options__avalara_option__name": "Avalara-Option", "@sage/xtrem-avalara-gateway/sys__notification_history_extension__revert_criteria": "Kriterien rückgängig machen", "@sage/xtrem-avalara-gateway/sys__notification_history_extension__search": "<PERSON><PERSON>"}