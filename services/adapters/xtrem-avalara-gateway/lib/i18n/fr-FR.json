{"@sage/xtrem-avalara-gateway/activity__avalara_company__name": "Société Avalara", "@sage/xtrem-avalara-gateway/activity__avalara_configuration__name": "Configuration <PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/activity__avalara_item_tax__name": "Taxe article Avalara", "@sage/xtrem-avalara-gateway/activity__avalara_option_management__name": "Gestion des options Avalara", "@sage/xtrem-avalara-gateway/activity__entity_use__name": "Exonération entité/usage", "@sage/xtrem-avalara-gateway/avalara_notification_dialog_title": "Notification Avalara", "@sage/xtrem-avalara-gateway/configuration-page": "Page de configuration", "@sage/xtrem-avalara-gateway/data_types__address_text_case_enum__name": "Enum police texte adresse", "@sage/xtrem-avalara-gateway/data_types__document_type_enum__name": "Enum type document", "@sage/xtrem-avalara-gateway/data_types__entity_type_enum__name": "Enum type entité", "@sage/xtrem-avalara-gateway/data_types__transaction_payload_manager_enum__name": "Enum gestionnaire de charge de transaction", "@sage/xtrem-avalara-gateway/enums__address_text_case__mixed": "M<PERSON>lang<PERSON>", "@sage/xtrem-avalara-gateway/enums__address_text_case__upper": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/enums__document_type__salesCreditMemo": "Avoir de vente", "@sage/xtrem-avalara-gateway/enums__document_type__salesInvoice": "Facture de vente", "@sage/xtrem-avalara-gateway/enums__entity_type__company": "Société", "@sage/xtrem-avalara-gateway/enums__entity_type__financialSite": "Site financier", "@sage/xtrem-avalara-gateway/enums__transaction_payload_manager_type__SalesCreditMemo": "Avoir de vente", "@sage/xtrem-avalara-gateway/enums__transaction_payload_manager_type__SalesInvoice": "Facture de vente", "@sage/xtrem-avalara-gateway/enums__transaction_payload_manger__SalesCreditMemo": "Avoir de vente", "@sage/xtrem-avalara-gateway/enums__transaction_payload_manger__SalesInvoice": "Facture de vente", "@sage/xtrem-avalara-gateway/load-companies-button": "Sociétés", "@sage/xtrem-avalara-gateway/load-entity-use-button": "Code exonération entité/usage", "@sage/xtrem-avalara-gateway/load-tax-codes-button": "Codes taxes", "@sage/xtrem-avalara-gateway/menu_item__avalara": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/node__sales_credit_memo__cant_calculate_tax_wrong_invoice_status": "Le statut est {{posted}}. La taxe ne peut pas être calculée pour cet avoir.", "@sage/xtrem-avalara-gateway/node__sales_credit_memo__cant_calculate_tax_wrong_tax_calculation_status": "Le statut du calcul de taxe est {{inProgress}} ou {{done}}. La taxe ne peut pas être calculée pour cet avoir.", "@sage/xtrem-avalara-gateway/node__sales_invoice__cant_calculate_tax_wrong_invoice_status": "La taxe ne peut pas être calculée pour cette facture. La facture est {{posted}}.", "@sage/xtrem-avalara-gateway/node__sales_invoice__cant_calculate_tax_wrong_tax_calculation_status": "La taxe ne peut pas être calculée pour cette facture. Le calcul est {{inProgress}} ou {{done}}.", "@sage/xtrem-avalara-gateway/node-extensions__accounts_receivable_invoice_line_extension__property__uiTaxes": "Taxes UI", "@sage/xtrem-avalara-gateway/node-extensions__base_line_tax_extension__property__numberOfExemptUnits": "Nombre d'unités exonérées", "@sage/xtrem-avalara-gateway/node-extensions__base_line_tax_extension__property__numberOfNonTaxableUnits": "Nombre d'unités non imposables", "@sage/xtrem-avalara-gateway/node-extensions__base_line_tax_extension__property__numberOfTaxableUnits": "Nombre d'unités imposables", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__country": "Pays", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__jurisdictionCode": "Code de juridiction", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__jurisdictionType": "Type de juridiction", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__region": "Région", "@sage/xtrem-avalara-gateway/node-extensions__delivery_detail_extension__property__entityUse": "Exonération entité/usage", "@sage/xtrem-avalara-gateway/node-extensions__item_extension__property__avalaraItemTax": "Taxe article Avalara", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax": "Calculer la taxe", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__failed": "Échec de calcul des taxes de ligne.", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__parameter__creditMemo": "Avoir", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__parameter__isCommit": "Validation", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__parameter__isPosting": "Comptabilisation", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__postAvalara": "Comptabilis<PERSON>", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__postAvalara__failed": "Échec de la comptabilisation Avalara.", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__postAvalara__parameter__creditMemo": "Avoir", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__property__avalaraId": "Code Avalara", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__property__skipCallAvalaraApi": "Omettre l'appel à l'API Avalara", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__property__totalDiscountAmount": "Montant total escompte", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_line_extension__property__entityUse": "Code d'exonération entité/usage", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_line_extension__property__uiTaxes": "Taxes UI", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax": "Calculer la taxe", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__failed": "Échec de calcul des taxes de ligne.", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__parameter__invoice": "Facture", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__parameter__isCommit": "Validation", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__parameter__isPosting": "Comptabilisation", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__postAvalara": "Comptabilis<PERSON>", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__postAvalara__failed": "Échec de la comptabilisation Avalara.", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__postAvalara__parameter__invoice": "Facture", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__avalaraId": "Code Avalara", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__skipCallAvalaraApi": "Omettre l'appel à l'API Avalara", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__skipSendNotification": "Omettre l'envoi de la notification", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__totalDiscountAmount": "Montant total escompte", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_line_extension__property__entityUse": "Code d'exonération entité/usage", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_line_extension__property__uiTaxes": "Taxes UI", "@sage/xtrem-avalara-gateway/node-extensions__sales_order_extension__property__entityUse": "Code d'exonération entité/usage", "@sage/xtrem-avalara-gateway/node-extensions__sales_order_extension__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/node-extensions__sales_order_line_extension__property__entityUse": "Code d'exonération entité/usage", "@sage/xtrem-avalara-gateway/node-extensions__sales_shipment_extension__property__entityUse": "Code d'exonération entité/usage", "@sage/xtrem-avalara-gateway/nodes__avalara_company__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-avalara-gateway/nodes__avalara_company__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-avalara-gateway/nodes__avalara_company__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-avalara-gateway/nodes__avalara_company__load_company_codes_from_avalara_asked": "Les codes société d'Avalara sont en cours de chargement. Le processus se déroulera en arrière-plan.", "@sage/xtrem-avalara-gateway/nodes__avalara_company__mutation__loadCompanyFromAvalara": "Charger la société depuis Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_company__mutation__loadCompanyFromAvalara__failed": "Échec de chargement de la société depuis Avalara.", "@sage/xtrem-avalara-gateway/nodes__avalara_company__node_name": "Société Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_company__property__id": "Code", "@sage/xtrem-avalara-gateway/nodes__avalara_company__property__isActive": "Active", "@sage/xtrem-avalara-gateway/nodes__avalara_company__property__name": "Nom", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__mutation__ping": "<PERSON>", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__mutation__ping__failed": "Échec du ping.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__mutation__ping__parameter__id": "Code", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__node_name": "Configuration <PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__accountId": "Code de compte", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__addressTextCase": "Police texte adresse", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__endPointUrl": "URL de point de connexion", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__id": "Code", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isActive": "Active", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isAddressValidationActive": "Adresse de validation active", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isCommittedTransaction": "Transaction engagée", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isSandboxMode": "Mode Sandbox", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__licenseKey": "Clé de licence", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__mapCompanyLines": "Mapper les lignes société", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__setupId": "Code paramétrage", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__defaultInstance": "Instance par défaut", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__defaultInstance__failed": "Échec de l'instance par défaut.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getIsCommit": "Commit", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getIsCommit__failed": "Échec get commit.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getUrl": "Obtenir URL", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getUrl__failed": "Échec d'obtention de l'URL.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getUrl__parameter__isSandBox": "Sandbox", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__load_tax_codes_from_avalara_asked": "Les codes taxe sont en cours de chargement. Le processus se déroulera en arrière-plan.", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara": "Charger les codes taxes depuis Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__failed": "Échec de chargement des codes taxes depuis Avalara.", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__description": "Description", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__fromTaxCode": "Code taxe début", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__isActive": "Active", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__toTaxCode": "Code taxe fin", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__node_name": "Taxe article Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__property__description": "Description", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__property__id": "Code", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__property__isActive": "Active", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__mutation__serviceOptionChange": "Modification option de service", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__mutation__serviceOptionChange__failed": "Échec de modification des options de service.", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__node_name": "Gestion des options Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__query__isServiceOptionActiveFunction": "Fonction options de service active", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__query__isServiceOptionActiveFunction__failed": "Échec de la fonction des options de service active.", "@sage/xtrem-avalara-gateway/nodes__avalara_response_listener__node_name": "Listener de réponse <PERSON>", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-failed": "Échec de notification des codes d'exonération entité/usage", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-sent": "La notification des codes d'exonération entité/usage a été envoyée.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-succeeded": "La notification des codes d'exonération entité/usage a abouti. (Reçue : {{recordCount}})", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-failed": "Échec de notification des codes taxe de la liste", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-sent": "La notification des codes taxe de la liste a été envoyée.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-succeeded": "La notification des codes taxe de la liste a abouti. (Reçue : {{recordCount}})", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-failed": "Échec de notification Ping", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-sent": "Notification Ping envoyée", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-succeeded": "La notification Ping a réussi.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-failed": "Échec de notification d’interrogation des sociétés", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-sent": "La notification d’interrogation des sociétés a été envoyée.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-succeeded": "La notification d’interrogation des sociétés a abouti. (Reçue : {{recordCount}})", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-failed": "Échec de notification de vérification d’adresses", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-sent": "La notification de vérification d’adresses a été envoyée.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-succeeded": "La notification de vérification d’adresses a abouti.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-post-and-commit": "La notification d'avoir de vente pour comptabilisation avec option de validation a échoué.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-post-only": "La notification d'avoir de vente pour comptabilisation avec option de non-validation a échoué.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-taxes-and-values-only": "La notification d'avoir de vente pour le calcul de taxe a échoué.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-post-and-commit": "La notification d'avoir de vente pour comptabilisation avec option de validation a été envoyée.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-post-only": "La notification d'avoir de vente pour comptabilisation avec option de non-validation a été envoyée.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-taxes-and-values-only": "La notification d'avoir de vente pour le calcul de taxe a été envoyée.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-post-and-commit": "La notification d'avoir de vente pour comptabilisation avec option de validation a réussi.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-post-only": "La notification d'avoir de vente pour comptabilisation avec option de non-validation a réussi.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-taxes-and-values-only": "La notification d'avoir de vente pour le calcul de taxe a abouti.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-post-and-commit": "La notification de facture de vente pour comptabilisation avec option de validation a échoué.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-post-only": "La notification de facture de vente pour comptabilisation avec option de non-validation a échoué.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-taxes-and-values-only": "La notification de facture de vente pour le calcul de taxe a échoué.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-post-and-commit": "La notification de facture de vente pour comptabilisation avec option de validation a été envoyée.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-post-only": "La notification de facture de vente pour comptabilisation avec option de non-validation a été envoyée.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-taxes-and-values-only": "La notification de facture de vente pour le calcul de taxe a été envoyée.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-post-and-commit": "La notification de facture de vente pour comptabilisation avec option de validation a abouti.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-post-only": "La notification de facture de vente pour comptabilisation avec option de non-validation a abouti.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-taxes-and-values-only": "La notification de facture de vente pour le calcul de taxe a abouti.", "@sage/xtrem-avalara-gateway/nodes__avatax_listener__node_name": "Listener <PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/nodes__company_extension_avalara_service_option_off": "<PERSON><PERSON> devez activer <PERSON><PERSON><PERSON> dans la 'Gestion des options' avant de pouvoir la sélectionner comme package de calcul de taxe.", "@sage/xtrem-avalara-gateway/nodes__entity_use__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-avalara-gateway/nodes__entity_use__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-avalara-gateway/nodes__entity_use__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-avalara-gateway/nodes__entity_use__load_entity_use_codes_from_avalara_asked": "Les codes d'exonération entité/usage sont en cours de chargement. Le processus se déroulera en arrière-plan.", "@sage/xtrem-avalara-gateway/nodes__entity_use__mutation__loadEntityUseCodesFromAvalara": "Charger les codes d'exonération entité/usage à partir d'Avalara", "@sage/xtrem-avalara-gateway/nodes__entity_use__mutation__loadEntityUseCodesFromAvalara__failed": "Échec de chargement des codes d'exonération entité/usage à partir d'Avalara.", "@sage/xtrem-avalara-gateway/nodes__entity_use__node_name": "Code exonération entité/usage", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__description": "Description", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__id": "Code", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__isActive": "Actif", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__name": "Nom", "@sage/xtrem-avalara-gateway/nodes__map_company__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-avalara-gateway/nodes__map_company__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-avalara-gateway/nodes__map_company__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-avalara-gateway/nodes__map_company__avalara_company_check_duplicate": "La société Avalara existe déjà.", "@sage/xtrem-avalara-gateway/nodes__map_company__company_check_duplicate": "La société existe déjà.", "@sage/xtrem-avalara-gateway/nodes__map_company__company_mandatory": "Renseignez une société.", "@sage/xtrem-avalara-gateway/nodes__map_company__company_must_be_empty": "L'entité est un site financier. Ne renseignez pas de société.", "@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_check_duplicate": "Le site financier existe déjà.", "@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_mandatory": "Renseignez un site financier.", "@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_must_be_empty": "L'entité est une société. Ne renseignez pas de site financier.", "@sage/xtrem-avalara-gateway/nodes__map_company__node_name": "Mapper la société", "@sage/xtrem-avalara-gateway/nodes__map_company__property__avalaraCompany": "Société Avalara", "@sage/xtrem-avalara-gateway/nodes__map_company__property__avalaraConfiguration": "Configuration <PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/nodes__map_company__property__company": "Société", "@sage/xtrem-avalara-gateway/nodes__map_company__property__entityType": "Type d'entité", "@sage/xtrem-avalara-gateway/nodes__map_company__property__financialSite": "Site financier", "@sage/xtrem-avalara-gateway/nodes__map_company__property__isActive": "Active", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany": "Valider la société Avalara", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany__failed": "Échec de validation des sociétés Avalara.", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany__parameter__avalaraCompany": "Société Avalara", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany__parameter__cachedAvalaraCompanies": "Sociétés Avalara en cache", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany": "Valider la société", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__failed": "Échec de création d'organisation.", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__parameter__cachedCompanies": "Sociétés en cache", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__parameter__company": "Société", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__parameter__entityType": "Type d'entité", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite": "Valider le site financier", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__failed": "Échec de validation de site financier.", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__parameter__cachedFinancialSites": "Sites financiers en cache", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__parameter__entityType": "Type d'entité", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__parameter__financialSite": "Site financier", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo__tax_calculation_asked": "Le calcul de taxe a été demandé pour cet avoir.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__avalara_integration_inactive": "L'intégration Avalara n'est pas activée.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__cant_calculate_tax_wrong_invoice_status": "Le statut est {{posted}}. La taxe ne peut pas être calculée pour cet avoir.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__cant_calculate_tax_wrong_tax_calculation_status": "Le statut du calcul de taxe est {{inProgress}} ou {{done}}. La taxe ne peut pas être calculée pour cet avoir.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__posted": "L'avoir de vente a été comptabilisé.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__tax_calculation_asked": "Le calcul de taxe a été demandé pour cet avoir.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice__tax_calculation_asked": "Un calcul de taxe a été demandé pour cette facture.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__avalara_integration_inactive": "L'intégration Avalara n'est pas activée.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__cant_calculate_tax_wrong_invoice_status": "Le statut est {{posted}}. La taxe ne peut pas être calculée pour cette facture.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__cant_calculate_tax_wrong_tax_calculation_status": "Le statut du calcul de taxe est {{inProgress}} ou {{done}}. La taxe ne peut pas être calculée pour cette facture.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__tax_calculation_asked": "Le calcul de taxe a été demandé pour cette facture.", "@sage/xtrem-avalara-gateway/package__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page__title__avalara_configuration": "Configuration <PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__accounts_receivable_invoice_extension__taxDetails____columns__title__jurisdictionName": "Juridiction", "@sage/xtrem-avalara-gateway/page-extensions__business_entity_address_panel_extension__entityUse____title": "Exonération entité/usage", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__addresses____columnOverrides__title": "Code exonération entité/usage", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__deliveryAddresses____columns__columns__entityUse__title": "Nom", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__deliveryAddresses____columns__columns__entityUse__title__2": "ID", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__deliveryAddresses____columns__columns__entityUse__title__3": "Description", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__deliveryAddresses____columns__title__entityUse": "Utilisation entité", "@sage/xtrem-avalara-gateway/page-extensions__item_extension____navigationPanel__listItem__avalaraItemTax__title": "Taxe article Avalara", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__avalaraItemTax____lookupDialogTitle": "Sélectionner la taxe article Avalara", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__avalaraItemTax____title": "Taxe article Avalara", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__itemTaxability____columns__title__description": "Description", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__itemTaxability____columns__title__id": "ID", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__itemTaxability____title": "Code tax Avalara", "@sage/xtrem-avalara-gateway/page-extensions__option_management_base_extension__avalaraBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__option_management_base_extension__avalaraConfigurationPage____title": "Page de configuration Avalara", "@sage/xtrem-avalara-gateway/page-extensions__option_management_base_extension__isAvalaraServiceOptionActive____title": "Active", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalara____title": "Calculer taxe", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalaraMessage____title": "Message", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalaraNotificationBlock____title": "Informations", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalaraNotificationSection____title": "Notification Avalara", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__columns__entityUse__title": "Nom", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__columns__entityUse__title__2": "Code", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__columns__entityUse__title__3": "Description", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__title__entityUse": "Utilisation entité", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__title__entityUse__name": "Code d'exonération entité/usage", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__taxDetails____columns__title__jurisdictionName": "Juridiction", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalara____title": "Calculer taxe", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalaraMessage____title": "Message", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalaraNotificationBlock____title": "Informations", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalaraNotificationSection____title": "Notification Avalara", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__columns__entityUse__title": "Nom", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__columns__entityUse__title__2": "Code", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__columns__entityUse__title__3": "Description", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__title__entityUse": "Utilisation entité", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__title__entityUse__name": "Code d'exonération entité/usage", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__taxDetails____columns__title__jurisdictionName": "Juridiction", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension____navigationPanel__listItem__line30__title": "Code d'exonération entité/usage", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__entityUse____title": "Code d'exonération entité/usage", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__columns__entityUse__title": "Nom", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__columns__entityUse__title__2": "Code", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__columns__entityUse__title__3": "Description", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__title__entityUse": "Utilisation entité", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__title__entityUse__name": "Code d'exonération entité/usage", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_card_view_extension__salesOrderLines____columns__columns__entityUse__title": "Nom", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_card_view_extension__salesOrderLines____columns__columns__entityUse__title__2": "Code", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_card_view_extension__salesOrderLines____columns__columns__entityUse__title__3": "Description", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_card_view_extension__salesOrderLines____columns__title__entityUse": "Utilisation entité", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_panel_extension__salesOrderLines____columns__columns__entityUse__name__title": "Nom", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_panel_extension__salesOrderLines____columns__columns__entityUse__name__title__2": "Code", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_panel_extension__salesOrderLines____columns__columns__entityUse__name__title__3": "Description", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_panel_extension__salesOrderLines____columns__title__entityUse__name": "Exonération entité/usage", "@sage/xtrem-avalara-gateway/page-extensions__sales_shipment_extension____navigationPanel__listItem__line11__title": "Code d'exonération entité/usage", "@sage/xtrem-avalara-gateway/page-extensions__sales_shipment_extension__entityUse____title": "Code d'exonération entité/usage", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraDocumentNumber____title": "Numéro de document", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraDocumentType____title": "Type de document", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__code": "Code", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__dateLogged": "Date", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__documentId": "Code document", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__documentNumber": "Numéro de document", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__documentType": "Type document", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__errorMessage": "Message", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__internalMessage": "Message interne", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__isCommit": "Validation", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__isPosting": "Comptabilisation", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__message": "Message d'erreur", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__recordCount": "Nombre d'enregistrements", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__status": "Statut", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__topic": "Sujet", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____title": "Résultats", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistoryBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistoryCriteriaBlock____title": "Critères", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistoryStatus____title": "Statut", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraLatestNotification____title": "Dernières notifications", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraMainTopic____title": "Sujets", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraTopic____title": "Sujets", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__fromDate____title": "Date début", "@sage/xtrem-avalara-gateway/page-extensions__tax_panel_extension__taxDetails____columns__title__jurisdictionName": "Juridiction", "@sage/xtrem-avalara-gateway/pages__avalara_company____navigationPanel__optionsMenu__title": "Toutes", "@sage/xtrem-avalara-gateway/pages__avalara_company____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-avalara-gateway/pages__avalara_company____navigationPanel__optionsMenu__title__3": "Inactif", "@sage/xtrem-avalara-gateway/pages__avalara_company____objectTypePlural": "Sociétés Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_company____objectTypeSingular": "Société Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_company____title": "Société", "@sage/xtrem-avalara-gateway/pages__avalara_company__id____title": "Code", "@sage/xtrem-avalara-gateway/pages__avalara_company__isActive____title": "Actif", "@sage/xtrem-avalara-gateway/pages__avalara_company__mainSection____title": "Général", "@sage/xtrem-avalara-gateway/pages__avalara_company__name____title": "Nom", "@sage/xtrem-avalara-gateway/pages__avalara_configuration____subtitle": "Configuration <PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_configuration____title": "Configuration", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__accountId____title": "Code compte", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__addMapCompany____title": "Ajouter", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__addressTextCase____title": "Police texte adresse", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__authenticated_sucessfull": "Connexion à {{version}} avec le compte {{authenticatedUserName}}.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__authentication_not_sucessfull": "Connexion à {{version}} sans authentification.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__avalara_item_tax_dialog_content": "Vous êtes sur le point de charger des codes taxes à partir d'Avalara.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__avalara_item_tax_dialog_title": "Confirmer le chargement", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__companies_cannot_be_loaded": "Les codes société Avalara ne peuvent pas être chargés.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__confirm_delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__delete_dialog_content": "Vous êtes sur le point de supprimer cet enregistrement.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__delete_dialog_title": "Confirmer la <PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__descriptionFilter____title": "Description", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__endPointUrl____title": "Point de connexion", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__entity_use_cannot_be_loaded": "Les codes d'exonération entité/usage ne peuvent pas être chargés.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__fromTaxCode____title": "Code taxe début", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__id____title": "Code", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isActive____title": "Active", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isActiveFilter____title": "Active", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isAddressValidationActive____title": "Validation d’adresse", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isCommittedTransaction____title": "Transaction engagée", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isSandboxMode____title": "Mode Sandbox", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__licenseKey____title": "Clé de licence", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__load_button_text": "Charge", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__load_tax_exception": "Les codes taxes d'Avalara ne peuvent pas être chargés. ({{exception}})", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__loadBlock____title": "Charger les données depuis <PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__loadTaxCodesBlock____title": "Critères de filtres", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__loadTaxCodesSection____title": "Charger les codes taxes depuis Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mainSection____title": "Général", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyBlock____title": "Mapping de sociétés", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__lookupDialogTitle__avalaraCompany__id": "Sélectionner la société Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__lookupDialogTitle__company__id": "Sélectionner la société", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__lookupDialogTitle__financialSite__id": "Sélectionner le site financier", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__avalaraCompany__id": "Société Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__company__id": "Société", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__entityType": "Type d'entité", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__financialSite__id": "Site financier", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__isActive": "Active", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____dropdownActions__title": "Modifier", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____title": "Sociétés", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanySection____title": "Mapping de sociétés", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__taxes_cannot_be_loaded": "Les codes taxes d'Avalara ne peuvent pas être chargés.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__testConnection____title": "Tester la connexion", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__toTaxCode____title": "Code taxe fin", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____objectTypePlural": "Taxes articles Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____objectTypeSingular": "Taxe article Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____title": "Taxe article", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__description____title": "Description", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__id____title": "Code", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__isActive____title": "Active", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__mainSection____title": "Général", "@sage/xtrem-avalara-gateway/pages__delete_avalara_link_company_dialog_content": "Sociétés associées au package de calcul Avalara : {{noCompanies}}. <PERSON> vous <PERSON>, le calcul de taxe générique sera utilisé.", "@sage/xtrem-avalara-gateway/pages__delete_avalara_link_company_dialog_title": "Confirmer la gestion des taxes", "@sage/xtrem-avalara-gateway/pages__entity_use____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-avalara-gateway/pages__entity_use____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-avalara-gateway/pages__entity_use____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-avalara-gateway/pages__entity_use____objectTypePlural": "Codes d'exonération entité/usage Avalara", "@sage/xtrem-avalara-gateway/pages__entity_use____objectTypeSingular": "Code d'exonération entité/usage Avalara", "@sage/xtrem-avalara-gateway/pages__entity_use____title": "Code d'exonération entité/usage", "@sage/xtrem-avalara-gateway/pages__entity_use__description____title": "Description", "@sage/xtrem-avalara-gateway/pages__entity_use__id____title": "Code", "@sage/xtrem-avalara-gateway/pages__entity_use__isActive____title": "Active", "@sage/xtrem-avalara-gateway/pages__entity_use__mainSection____title": "Général", "@sage/xtrem-avalara-gateway/pages__entity_use__name____title": "Nom", "@sage/xtrem-avalara-gateway/pages__item_taxability____navigationPanel__optionsMenu__title": "Toutes", "@sage/xtrem-avalara-gateway/pages__item_taxability____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-avalara-gateway/pages__item_taxability____navigationPanel__optionsMenu__title__3": "Inactif", "@sage/xtrem-avalara-gateway/pages__item_taxability____subtitle": "Code taxe article", "@sage/xtrem-avalara-gateway/pages__item_taxability____title": "Code taxe article", "@sage/xtrem-avalara-gateway/pages__item_taxability__description____title": "Description", "@sage/xtrem-avalara-gateway/pages__item_taxability__id____title": "ID", "@sage/xtrem-avalara-gateway/pages__item_taxability__isActive____title": "Actif", "@sage/xtrem-avalara-gateway/pages__map_company__edit____title": "Modifier la société de mapping", "@sage/xtrem-avalara-gateway/pages__map_company_panel____title": "Mapping de sociétés", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraCompany____lookupDialogTitle": "Sélectionner la société Avalara", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraCompany____placeholder": "Sélectionner la société Avalara", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraCompany____title": "Société Avalara", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraConfiguration____title": "Configuration <PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__map_company_panel__cancel____title": "Annuler", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____columns__title__description": "Description", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____columns__title__id": "Code", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____columns__title__name": "Nom", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____lookupDialogTitle": "Sélectionner la société", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____placeholder": "Sélectionner la société", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____title": "Société", "@sage/xtrem-avalara-gateway/pages__map_company_panel__confirm____title": "OK", "@sage/xtrem-avalara-gateway/pages__map_company_panel__entityType____title": "Type d'entité", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____columns__title__legalCompany__name": "Société", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____lookupDialogTitle": "Sélectionner le site financier", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____placeholder": "Sélectionner...", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____title": "Site financier", "@sage/xtrem-avalara-gateway/pages__map_company_panel__isActive____title": "Actif", "@sage/xtrem-avalara-gateway/pages__map_company_panel__mainSection____title": "Général", "@sage/xtrem-avalara-gateway/pages__map_company_panel__save____title": "OK", "@sage/xtrem-avalara-gateway/pages__map-company-panel__edit____title": "Modifier le mapping de sociétés", "@sage/xtrem-avalara-gateway/pages__map-company-panel__new____title": "Ajouter mapping de sociétés", "@sage/xtrem-avalara-gateway/pages__test_conntection": "Tester la connexion", "@sage/xtrem-avalara-gateway/pages-confirm-no": "Non", "@sage/xtrem-avalara-gateway/pages-confirm-yes": "O<PERSON>", "@sage/xtrem-avalara-gateway/permission__manage__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/service_options__avalara_option__name": "Option Avalara", "@sage/xtrem-avalara-gateway/sys__notification_history_extension__revert_criteria": "Rétablir les critères", "@sage/xtrem-avalara-gateway/sys__notification_history_extension__search": "Recherche"}