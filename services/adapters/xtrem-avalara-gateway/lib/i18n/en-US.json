{"@sage/xtrem-avalara-gateway/activity__avalara_company__name": "Avalara company", "@sage/xtrem-avalara-gateway/activity__avalara_configuration__name": "Avalara configuration", "@sage/xtrem-avalara-gateway/activity__avalara_item_tax__name": "Avalara item tax", "@sage/xtrem-avalara-gateway/activity__entity_use__name": "Entity use", "@sage/xtrem-avalara-gateway/avalara_notification_dialog_title": "Avalara notification", "@sage/xtrem-avalara-gateway/configuration-page": "Configuration page", "@sage/xtrem-avalara-gateway/data_types__address_text_case_enum__name": "Address text case enum", "@sage/xtrem-avalara-gateway/data_types__document_type_enum__name": "Document type enum", "@sage/xtrem-avalara-gateway/data_types__entity_type_enum__name": "Entity type enum", "@sage/xtrem-avalara-gateway/data_types__transaction_payload_manager_enum__name": "Transaction payload manager enum", "@sage/xtrem-avalara-gateway/enums__address_text_case__mixed": "Mixed", "@sage/xtrem-avalara-gateway/enums__address_text_case__upper": "Upper", "@sage/xtrem-avalara-gateway/enums__document_type__salesCreditMemo": "Sales credit memo", "@sage/xtrem-avalara-gateway/enums__document_type__salesInvoice": "Sales invoice", "@sage/xtrem-avalara-gateway/enums__entity_type__company": "Company", "@sage/xtrem-avalara-gateway/enums__entity_type__financialSite": "Financial site", "@sage/xtrem-avalara-gateway/enums__transaction_payload_manager_type__SalesCreditMemo": "Sales credit memo", "@sage/xtrem-avalara-gateway/enums__transaction_payload_manager_type__SalesInvoice": "Sales invoice", "@sage/xtrem-avalara-gateway/load-companies-button": "Companies", "@sage/xtrem-avalara-gateway/load-entity-use-button": "Entity use code", "@sage/xtrem-avalara-gateway/load-tax-codes-button": "Tax codes", "@sage/xtrem-avalara-gateway/menu_item__avalara": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/node-extensions__accounts_receivable_invoice_line_extension__property__uiTaxes": "UI taxes", "@sage/xtrem-avalara-gateway/node-extensions__base_line_tax_extension__property__numberOfExemptUnits": "Number of exempt units", "@sage/xtrem-avalara-gateway/node-extensions__base_line_tax_extension__property__numberOfNonTaxableUnits": "Number of non-taxable units", "@sage/xtrem-avalara-gateway/node-extensions__base_line_tax_extension__property__numberOfTaxableUnits": "Number of taxable units", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__country": "Country", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__jurisdictionCode": "Jurisdiction code", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__jurisdictionType": "Jurisdiction type", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__region": "Region", "@sage/xtrem-avalara-gateway/node-extensions__delivery_detail_extension__property__entityUse": "Entity use", "@sage/xtrem-avalara-gateway/node-extensions__item_extension__property__avalaraItemTax": "Avalara item tax", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax": "Calculate tax", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__failed": "Calculate tax failed.", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__parameter__creditMemo": "Credit memo", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__parameter__isCommit": "Commit", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__parameter__isPosting": "Posting", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__postAvalara": "Post Avalara", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__postAvalara__failed": "Post Avalara failed.", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__postAvalara__parameter__creditMemo": "Credit memo", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__property__avalaraId": "Avalara ID", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__property__skipCallAvalaraApi": "Skip call Avalara API", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__property__totalDiscountAmount": "Total discount amount", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_line_extension__property__entityUse": "Entity use code", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_line_extension__property__uiTaxes": "UI taxes", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax": "Calculate tax", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__failed": "Calculate tax failed.", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__parameter__invoice": "Invoice", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__parameter__isCommit": "Commit", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__parameter__isPosting": "Posting", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__postAvalara": "Post Avalara", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__postAvalara__failed": "Post Avalara failed.", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__postAvalara__parameter__invoice": "Invoice", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__avalaraId": "Avalara ID", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__skipCallAvalaraApi": "Skip call Avalara API", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__skipSendNotification": "Skip send notification", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__totalDiscountAmount": "Total discount amount", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_line_extension__property__entityUse": "Entity use code", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_line_extension__property__uiTaxes": "UI taxes", "@sage/xtrem-avalara-gateway/node-extensions__sales_order_extension__property__entityUse": "Entity use code", "@sage/xtrem-avalara-gateway/node-extensions__sales_order_extension__property__lines": "Lines", "@sage/xtrem-avalara-gateway/node-extensions__sales_order_line_extension__property__entityUse": "Entity use code", "@sage/xtrem-avalara-gateway/node-extensions__sales_shipment_extension__property__entityUse": "Entity use code", "@sage/xtrem-avalara-gateway/nodes__avalara_company__asyncMutation__asyncExport": "Export", "@sage/xtrem-avalara-gateway/nodes__avalara_company__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-avalara-gateway/nodes__avalara_company__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_company__load_company_codes_from_avalara_asked": "The avalara company codes are being loaded. The process will run in the background.", "@sage/xtrem-avalara-gateway/nodes__avalara_company__mutation__loadCompanyFromAvalara": "Load company from Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_company__mutation__loadCompanyFromAvalara__failed": "Load company from Avalara failed.", "@sage/xtrem-avalara-gateway/nodes__avalara_company__node_name": "Avalara company", "@sage/xtrem-avalara-gateway/nodes__avalara_company__property__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_company__property__isActive": "Active", "@sage/xtrem-avalara-gateway/nodes__avalara_company__property__name": "Name", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__asyncMutation__asyncExport": "Export", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__mutation__ping": "<PERSON>", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__mutation__ping__failed": "<PERSON> failed.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__mutation__ping__parameter__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__node_name": "Avalara configuration", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__accountId": "Account ID", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__addressTextCase": "Address text case", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__endPointUrl": "Endpoint URL", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isActive": "Active", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isAddressValidationActive": "Address validation active", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isCommittedTransaction": "Committed transaction", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isSandboxMode": "Sandbox mode", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__licenseKey": "License key", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__mapCompanyLines": "Map company lines", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__setupId": "Setup ID", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__defaultInstance": "Default instance", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__defaultInstance__failed": "Default instance failed.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getIsCommit": "Get is commit", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getIsCommit__failed": "Get is commit failed.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getUrl": "Get URL", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getUrl__failed": "Get URL failed.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getUrl__parameter__isSandBox": "Sandbox", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__load_tax_codes_from_avalara_asked": "The tax codes are being loaded. The process will run in the background.", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara": "Load tax codes from Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__failed": "Load tax codes from Avalara failed.", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__description": "Description", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__fromTaxCode": "From tax code", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__isActive": "Active", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__toTaxCode": "To tax code", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__node_name": "Avalara item tax", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__property__description": "Description", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__property__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__property__isActive": "Active", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__asyncMutation__asyncExport": "Export", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__mutation__serviceOptionChange": "Service option change", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__mutation__serviceOptionChange__failed": "Service option change failed.", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__node_name": "Avalara option management", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__query__isServiceOptionActiveFunction": "Service option active function", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__query__isServiceOptionActiveFunction__failed": "Is service option active function failed.", "@sage/xtrem-avalara-gateway/nodes__avalara_response_listener__node_name": "Avalara response listener", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-failed": "List entity code notification failed.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-sent": "List entity code notification sent.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-succeeded": "List entity code notification succeeded. (Received: {{recordCount}})", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-failed": "List tax code notification failed.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-sent": "List tax code notification sent.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-succeeded": "List tax code notification succeeded. (Received: {{recordCount}})", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-failed": "Ping notification failed.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-sent": "Ping notification sent.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-succeeded": "Ping notification succeeded.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-failed": "Query companies notification failed.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-sent": "Query companies notification sent.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-succeeded": "Query companies notification succeeded. (Received: {{recordCount}})", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-failed": "Resolve address notification failed.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-sent": "Resolve address notification sent.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-succeeded": "Resolve address notification succeeded.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-post-and-commit": "Sales credit memo notification for posting with commit options failed.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-post-only": "Sales credit memo notification for posting with uncommit option failed.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-taxes-and-values-only": "Sales credit memo notification for tax calculation failed.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-post-and-commit": "Sales credit memo notification for posting with commit option sent.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-post-only": "Sales credit memo notification for posting with uncommit option sent.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-taxes-and-values-only": "Sales credit memo notification for tax calculation sent.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-post-and-commit": "Sales credit memo notification for posting with commit option succeeded.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-post-only": "Sales credit memo notification for posting with uncommit option succeeded.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-taxes-and-values-only": "Sales credit memo notification for tax calculation succeeded.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-post-and-commit": "Sales invoice notification for posting with commit option failed.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-post-only": "Sales invoice notification for posting with uncommit option failed.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-taxes-and-values-only": "Sales invoice notification for tax calculation failed.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-post-and-commit": "Sales invoice notification for posting with commit option sent.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-post-only": "Sales invoice notification for posting with uncommit option sent.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-taxes-and-values-only": "Sales invoice notification for tax calculation sent.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-post-and-commit": "Sales invoice notification for posting with commit option succeeded.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-post-only": "Sales invoice notification for posting with uncommit option succeeded.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-taxes-and-values-only": "Sales invoice notification for tax calculation succeeded.", "@sage/xtrem-avalara-gateway/nodes__avatax_listener__node_name": "Avatax listener", "@sage/xtrem-avalara-gateway/nodes__avatax_response_listener__node_name": "Avatax response listener", "@sage/xtrem-avalara-gateway/nodes__company_extension_avalara_service_option_off": "You need to activate <PERSON><PERSON><PERSON> in 'Option management' before you can select it as a tax calculation package.", "@sage/xtrem-avalara-gateway/nodes__entity_use__asyncMutation__asyncExport": "Export", "@sage/xtrem-avalara-gateway/nodes__entity_use__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-avalara-gateway/nodes__entity_use__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-avalara-gateway/nodes__entity_use__load_entity_use_codes_from_avalara_asked": "The entity use codes are being loaded. The process will run in the background.", "@sage/xtrem-avalara-gateway/nodes__entity_use__mutation__loadEntityUseCodesFromAvalara": "Load entity use codes from Avalara", "@sage/xtrem-avalara-gateway/nodes__entity_use__mutation__loadEntityUseCodesFromAvalara__failed": "Load entity use codes from Avalara failed.", "@sage/xtrem-avalara-gateway/nodes__entity_use__node_name": "Entity use code", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__description": "Description", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__id": "ID", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__isActive": "Active", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__name": "Name", "@sage/xtrem-avalara-gateway/nodes__map_company__asyncMutation__asyncExport": "Export", "@sage/xtrem-avalara-gateway/nodes__map_company__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-avalara-gateway/nodes__map_company__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-avalara-gateway/nodes__map_company__avalara_company_check_duplicate": "The Avalara company already exists.", "@sage/xtrem-avalara-gateway/nodes__map_company__company_check_duplicate": "The company already exists.", "@sage/xtrem-avalara-gateway/nodes__map_company__company_mandatory": "Enter a company.", "@sage/xtrem-avalara-gateway/nodes__map_company__company_must_be_empty": "The entity is a financial site. Leave the company empty.", "@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_check_duplicate": "The financial site already exists.", "@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_mandatory": "Enter a financial site.", "@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_must_be_empty": "The entity is a company. Leave the financial site empty.", "@sage/xtrem-avalara-gateway/nodes__map_company__node_name": "Map company", "@sage/xtrem-avalara-gateway/nodes__map_company__property__avalaraCompany": "Avalara company", "@sage/xtrem-avalara-gateway/nodes__map_company__property__avalaraConfiguration": "Avalara configuration", "@sage/xtrem-avalara-gateway/nodes__map_company__property__company": "Company", "@sage/xtrem-avalara-gateway/nodes__map_company__property__entityType": "Entity type", "@sage/xtrem-avalara-gateway/nodes__map_company__property__financialSite": "Financial site", "@sage/xtrem-avalara-gateway/nodes__map_company__property__isActive": "Active", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany": "Validate Avalara company", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany__failed": "Validate Avalara company failed.", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany__parameter__avalaraCompany": "Avalara company", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany__parameter__cachedAvalaraCompanies": "Cached Avalara companies", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany": "Validate company", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__failed": "Validate company failed.", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__parameter__cachedCompanies": "Cached companies", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__parameter__company": "Company", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__parameter__entityType": "Entity type", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite": "Validate financial site", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__failed": "Validate financial site failed.", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__parameter__cachedFinancialSites": "Cached financial sites", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__parameter__entityType": "Entity type", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__parameter__financialSite": "Financial site", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__avalara_integration_inactive": "The Avalara integration is not activated.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__cant_calculate_tax_wrong_invoice_status": "The status is {{posted}}. The tax cannot be calculated for this credit memo.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__cant_calculate_tax_wrong_tax_calculation_status": "The tax calculation status is {{inProgress}} or {{done}}. The tax cannot be calculated for this credit memo.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__posted": "The sales credit memo was posted.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__tax_calculation_asked": "The tax calculation has been requested for this credit memo.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__avalara_integration_inactive": "The Avalara integration is not activated.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__cant_calculate_tax_wrong_invoice_status": "The status is {{posted}}. The tax cannot be calculated for this invoice.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__cant_calculate_tax_wrong_tax_calculation_status": "The tax calculation status is {{inProgress}} or {{done}}. The tax cannot be calculated for this invoice.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__tax_calculation_asked": "The tax calculation has been requested for this invoice.", "@sage/xtrem-avalara-gateway/package__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page__title__avalara_configuration": "Avalara configuration", "@sage/xtrem-avalara-gateway/page-extensions__accounts_receivable_invoice_extension__taxDetails____columns__title__jurisdictionName": "Juris<PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__business_entity_address_panel_extension__entityUse____title": "Entity use", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__addresses____columnOverrides__title": "Entity use code", "@sage/xtrem-avalara-gateway/page-extensions__item_extension____navigationPanel__listItem__avalaraItemTax__title": "Avalara item tax", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__avalaraItemTax____lookupDialogTitle": "Select Avalara item tax", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__avalaraItemTax____title": "Avalara item tax", "@sage/xtrem-avalara-gateway/page-extensions__option_management_base_extension__avalaraBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__option_management_base_extension__avalaraConfigurationPage____title": "Avalara configuration page", "@sage/xtrem-avalara-gateway/page-extensions__option_management_base_extension__isAvalaraServiceOptionActive____title": "Active", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalara____title": "Calculate tax", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalaraMessage____title": "Message", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalaraNotificationBlock____title": "Information", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalaraNotificationSection____title": "Avalara notification", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__title__entityUse__name": "Entity use code", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__taxDetails____columns__title__jurisdictionName": "Juris<PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalara____title": "Calculate tax", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalaraMessage____title": "Message", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalaraNotificationBlock____title": "Information", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalaraNotificationSection____title": "Avalara notification", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__title__entityUse__name": "Entity use code", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__taxDetails____columns__title__jurisdictionName": "Juris<PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension____navigationPanel__listItem__line30__title": "Entity use code", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__entityUse____title": "Entity use code", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__title__entityUse__name": "Entity use code", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_panel_extension__salesOrderLines____columns__title__entityUse__name": "Entity use", "@sage/xtrem-avalara-gateway/page-extensions__sales_shipment_extension____navigationPanel__listItem__line11__title": "Entity use code", "@sage/xtrem-avalara-gateway/page-extensions__sales_shipment_extension__entityUse____title": "Entity use code", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraDocumentNumber____title": "Document number", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraDocumentType____title": "Document type", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__dateLogged": "Date", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__documentNumber": "Document number", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__documentType": "Document type", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__internalMessage": "Internal message", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__message": "Error message", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__status": "Status", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__topic": "Topic", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____title": "Results", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistoryBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistoryCriteriaBlock____title": "Criteria", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistoryStatus____title": "Status", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraLatestNotification____title": "Latest notifications", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraMainTopic____title": "Topics", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraTopic____title": "Topics", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__fromDate____title": "From date", "@sage/xtrem-avalara-gateway/page-extensions__tax_panel_extension__taxDetails____columns__title__jurisdictionName": "Juris<PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_company____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-avalara-gateway/pages__avalara_company____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-avalara-gateway/pages__avalara_company____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-avalara-gateway/pages__avalara_company____objectTypePlural": "Avalara companies", "@sage/xtrem-avalara-gateway/pages__avalara_company____objectTypeSingular": "Avalara company", "@sage/xtrem-avalara-gateway/pages__avalara_company____title": "Company", "@sage/xtrem-avalara-gateway/pages__avalara_company__id____title": "ID", "@sage/xtrem-avalara-gateway/pages__avalara_company__isActive____title": "Active", "@sage/xtrem-avalara-gateway/pages__avalara_company__mainSection____title": "General", "@sage/xtrem-avalara-gateway/pages__avalara_company__name____title": "Name", "@sage/xtrem-avalara-gateway/pages__avalara_configuration____title": "Configuration", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__accountId____title": "Account ID", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__addMapCompany____title": "Add", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__addressTextCase____title": "Address text case", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__authenticated_sucessfull": "Logged in to {{version}} with account {{authenticatedUserName}}.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__authentication_not_sucessfull": "Logged in to {{version}} without authentication.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__avalara_item_tax_dialog_content": "You are about to load tax codes from Avalara. Confirm?", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__avalara_item_tax_dialog_title": "Loading confirmation", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__companies_cannot_be_loaded": "Avalara company codes cannot be loaded.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__confirm_delete": "Delete", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__delete_dialog_content": "You are about to delete this record.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__delete_dialog_title": "Confirm deletion", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__descriptionFilter____title": "Description", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__endPointUrl____title": "Endpoint", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__entity_use_cannot_be_loaded": "Entity use codes cannot be loaded.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__fromTaxCode____title": "From tax code", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__id____title": "ID", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isActive____title": "Active", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isActiveFilter____title": "Active", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isAddressValidationActive____title": "Address validation", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isCommittedTransaction____title": "Committed transaction", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isSandboxMode____title": "Sandbox mode", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__licenseKey____title": "License key", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__load_button_text": "Load", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__load_tax_exception": "Tax codes from Avalara cannot be loaded. ({{exception}})", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__loadBlock____title": "Load data from Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__loadTaxCodesBlock____title": "Filter criteria", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__loadTaxCodesSection____title": "Load tax codes from Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mainSection____title": "General", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyBlock____title": "Company mapping", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__lookupDialogTitle__avalaraCompany__id": "Select Avalara company", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__lookupDialogTitle__company__id": "Select company", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__lookupDialogTitle__financialSite__id": "Select financial site", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__avalaraCompany__id": "Avalara company", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__company__id": "Company", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__entityType": "Entity type", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__financialSite__id": "Financial site", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__isActive": "Active", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____dropdownActions__title": "Edit", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____dropdownActions__title__2": "Delete", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____title": "Companies", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanySection____title": "Company mapping", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__taxes_cannot_be_loaded": "Tax codes from Avalara cannot be loaded.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__testConnection____title": "Test connection", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__toTaxCode____title": "To tax code", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____objectTypePlural": "Avalara item taxes", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____objectTypeSingular": "Avalara item tax", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____title": "Item tax", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__description____title": "Description", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__id____title": "ID", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__isActive____title": "Active", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__mainSection____title": "General", "@sage/xtrem-avalara-gateway/pages__delete_avalara_link_company_dialog_content": "Companies linked to Avalara tax calculation package: {{noCompanies}}. If you continue, they will be changed to the generic tax calculation.", "@sage/xtrem-avalara-gateway/pages__delete_avalara_link_company_dialog_title": "Confirm tax management", "@sage/xtrem-avalara-gateway/pages__entity_use____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-avalara-gateway/pages__entity_use____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-avalara-gateway/pages__entity_use____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-avalara-gateway/pages__entity_use____objectTypePlural": "Avalara entity use codes", "@sage/xtrem-avalara-gateway/pages__entity_use____objectTypeSingular": "Avalara entity use code", "@sage/xtrem-avalara-gateway/pages__entity_use____title": "Entity use code", "@sage/xtrem-avalara-gateway/pages__entity_use__description____title": "Description", "@sage/xtrem-avalara-gateway/pages__entity_use__id____title": "ID", "@sage/xtrem-avalara-gateway/pages__entity_use__isActive____title": "Active", "@sage/xtrem-avalara-gateway/pages__entity_use__mainSection____title": "General", "@sage/xtrem-avalara-gateway/pages__entity_use__name____title": "Name", "@sage/xtrem-avalara-gateway/pages__map_company_panel____title": "Company mapping", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraCompany____lookupDialogTitle": "Select Avalara company", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraCompany____placeholder": "Select avalara company", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraCompany____title": "Avalara company", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraConfiguration____title": "Avalara configuration", "@sage/xtrem-avalara-gateway/pages__map_company_panel__cancel____title": "Cancel", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____columns__title__description": "Description", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____columns__title__id": "ID", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____columns__title__name": "Name", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____lookupDialogTitle": "Select company", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____placeholder": "Select a company", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____title": "Company", "@sage/xtrem-avalara-gateway/pages__map_company_panel__confirm____title": "OK", "@sage/xtrem-avalara-gateway/pages__map_company_panel__entityType____title": "Entity type", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____lookupDialogTitle": "Select financial site", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____placeholder": "Select ...", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____title": "Financial site", "@sage/xtrem-avalara-gateway/pages__map_company_panel__isActive____title": "Active", "@sage/xtrem-avalara-gateway/pages__map_company_panel__mainSection____title": "General", "@sage/xtrem-avalara-gateway/pages__map-company-panel__edit____title": "Edit company mapping", "@sage/xtrem-avalara-gateway/pages__map-company-panel__new____title": "Add company mapping", "@sage/xtrem-avalara-gateway/pages__test_conntection": "Test connection", "@sage/xtrem-avalara-gateway/pages-confirm-no": "No", "@sage/xtrem-avalara-gateway/pages-confirm-yes": "Yes", "@sage/xtrem-avalara-gateway/permission__manage__name": "Manage", "@sage/xtrem-avalara-gateway/permission__read__name": "Read", "@sage/xtrem-avalara-gateway/service_options__avalara_option__name": "Avalara option", "@sage/xtrem-avalara-gateway/sys__notification_history_extension__revert_criteria": "Revert criteria", "@sage/xtrem-avalara-gateway/sys__notification_history_extension__search": "Search"}