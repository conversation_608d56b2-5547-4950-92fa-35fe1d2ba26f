{"@sage/xtrem-avalara-gateway/activity__avalara_company__name": "Sociedad en Avalara", "@sage/xtrem-avalara-gateway/activity__avalara_configuration__name": "Configuración de Avalara", "@sage/xtrem-avalara-gateway/activity__avalara_item_tax__name": "Impuesto de artículo en Avalara", "@sage/xtrem-avalara-gateway/activity__avalara_option_management__name": "Gestión de opciones de Avalara", "@sage/xtrem-avalara-gateway/activity__entity_use__name": "Código de exención", "@sage/xtrem-avalara-gateway/avalara_notification_dialog_title": "Notificación de Avalara", "@sage/xtrem-avalara-gateway/configuration-page": "Página de configuración", "@sage/xtrem-avalara-gateway/data_types__address_text_case_enum__name": "Mayúsculas y minúsculas del texto de dirección", "@sage/xtrem-avalara-gateway/data_types__document_type_enum__name": "Document type enum", "@sage/xtrem-avalara-gateway/data_types__entity_type_enum__name": "Unit type enum", "@sage/xtrem-avalara-gateway/data_types__transaction_payload_manager_enum__name": "Transaction payload manager enum", "@sage/xtrem-avalara-gateway/enums__address_text_case__mixed": "Mayúsculas y minúsculas", "@sage/xtrem-avalara-gateway/enums__address_text_case__upper": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/enums__document_type__salesCreditMemo": "Factura rectificativa de venta", "@sage/xtrem-avalara-gateway/enums__document_type__salesInvoice": "Factura de venta", "@sage/xtrem-avalara-gateway/enums__entity_type__company": "Sociedad", "@sage/xtrem-avalara-gateway/enums__entity_type__financialSite": "Planta financiera", "@sage/xtrem-avalara-gateway/enums__transaction_payload_manager_type__SalesCreditMemo": "Factura rectificativa de venta", "@sage/xtrem-avalara-gateway/enums__transaction_payload_manager_type__SalesInvoice": "Factura de venta", "@sage/xtrem-avalara-gateway/enums__transaction_payload_manger__SalesCreditMemo": "<PERSON><PERSON><PERSON> de <PERSON>a", "@sage/xtrem-avalara-gateway/enums__transaction_payload_manger__SalesInvoice": "Factura de venta", "@sage/xtrem-avalara-gateway/load-companies-button": "Sociedades", "@sage/xtrem-avalara-gateway/load-entity-use-button": "Códigos de exención", "@sage/xtrem-avalara-gateway/load-tax-codes-button": "Códigos de impuesto", "@sage/xtrem-avalara-gateway/menu_item__avalara": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/node__sales_credit_memo__cant_calculate_tax_wrong_invoice_status": "El impuesto no se puede calcular para este abono porque el estado es {{posted}}.", "@sage/xtrem-avalara-gateway/node__sales_credit_memo__cant_calculate_tax_wrong_tax_calculation_status": "El impuesto no se puede calcular para este abono porque el estado del cálculo de impuesto es {{inProgress}} o {{done}}.", "@sage/xtrem-avalara-gateway/node__sales_invoice__cant_calculate_tax_wrong_invoice_status": "El impuesto no se puede calcular para esta factura. El estado de la factura es {{posted}}.", "@sage/xtrem-avalara-gateway/node__sales_invoice__cant_calculate_tax_wrong_tax_calculation_status": "El impuesto no se puede calcular para esta factura. El estado del cálculo es {{inProgress}} o {{done}}.", "@sage/xtrem-avalara-gateway/node-extensions__accounts_receivable_invoice_line_extension__property__uiTaxes": "Impuestos de seguro de desempleo", "@sage/xtrem-avalara-gateway/node-extensions__base_line_tax_extension__property__numberOfExemptUnits": "Número de unidades exentas", "@sage/xtrem-avalara-gateway/node-extensions__base_line_tax_extension__property__numberOfNonTaxableUnits": "Número de unidades no imponibles", "@sage/xtrem-avalara-gateway/node-extensions__base_line_tax_extension__property__numberOfTaxableUnits": "Número de unidades imponibles", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__country": "<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__jurisdictionCode": "Código de jurisdicción", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__jurisdictionType": "Tipo de jurisdicción", "@sage/xtrem-avalara-gateway/node-extensions__base_tax_extension__property__region": "Región", "@sage/xtrem-avalara-gateway/node-extensions__delivery_detail_extension__property__entityUse": "Código de exención", "@sage/xtrem-avalara-gateway/node-extensions__item_extension__property__avalaraItemTax": "Impuesto de artículo en Avalara", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax": "Calcular impuesto", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__failed": "Error al calcular el impuesto", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__parameter__creditMemo": "Factura rectificativa", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__parameter__isCommit": "Confirmación", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__calculateTax__parameter__isPosting": "Contabilización", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__postAvalara": "Con<PERSON>bil<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__postAvalara__failed": "Error al contabilizar Avalara", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__mutation__postAvalara__parameter__creditMemo": "Factura rectificativa", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__property__avalaraId": "Id. en <PERSON>lara", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__property__skipCallAvalaraApi": "Omitir llamada a API de Avalara", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_extension__property__totalDiscountAmount": "Importe de descuento total", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_line_extension__property__entityUse": "Código de exención", "@sage/xtrem-avalara-gateway/node-extensions__sales_credit_memo_line_extension__property__uiTaxes": "Impuestos de seguro de desempleo", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax": "Calcular impuesto", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__failed": "Error al calcular el impuesto", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__parameter__invoice": "Factura", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__parameter__isCommit": "Confirmación", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__calculateTax__parameter__isPosting": "Contabilización", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__postAvalara": "Con<PERSON>bil<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__postAvalara__failed": "Error al contabilizar Avalara", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__mutation__postAvalara__parameter__invoice": "Factura", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__avalaraId": "Id. en <PERSON>lara", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__skipCallAvalaraApi": "Omitir llamada a API de Avalara", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__skipSendNotification": "Omitir envío de notificaciónt", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_extension__property__totalDiscountAmount": "Importe de descuento total", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_line_extension__property__entityUse": "Código de exención", "@sage/xtrem-avalara-gateway/node-extensions__sales_invoice_line_extension__property__uiTaxes": "Impuestos de seguro de desempleo", "@sage/xtrem-avalara-gateway/node-extensions__sales_order_extension__property__entityUse": "Código de exención", "@sage/xtrem-avalara-gateway/node-extensions__sales_order_extension__property__lines": "Líneas", "@sage/xtrem-avalara-gateway/node-extensions__sales_order_line_extension__property__entityUse": "Código de exención", "@sage/xtrem-avalara-gateway/node-extensions__sales_shipment_extension__property__entityUse": "Código de exención", "@sage/xtrem-avalara-gateway/nodes__avalara_company__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-avalara-gateway/nodes__avalara_company__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-avalara-gateway/nodes__avalara_company__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-avalara-gateway/nodes__avalara_company__load_company_codes_from_avalara_asked": "Los códigos de sociedad se están cargando. El proceso se ejecutará en segundo plano.", "@sage/xtrem-avalara-gateway/nodes__avalara_company__mutation__loadCompanyFromAvalara": "Cargar sociedad de Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_company__mutation__loadCompanyFromAvalara__failed": "Error al cargar la sociedad de Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_company__node_name": "Sociedad en Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_company__property__id": "Id.", "@sage/xtrem-avalara-gateway/nodes__avalara_company__property__isActive": "Activa", "@sage/xtrem-avalara-gateway/nodes__avalara_company__property__name": "Nombre", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__mutation__ping": "<PERSON>cer ping", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__mutation__ping__failed": "Error al hacer ping", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__mutation__ping__parameter__id": "Id.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__node_name": "Configuración de Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__accountId": "<PERSON><PERSON><PERSON> de cuenta", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__addressTextCase": "Mayúsculas y minúsculas del texto de dirección", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__endPointUrl": "URL de punto de conexión", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__id": "Id.", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isActive": "Activa", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isAddressValidationActive": "Validación de dirección activa", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isCommittedTransaction": "Transacción confirmada", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__isSandboxMode": "Modo de espacio aislado", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__licenseKey": "Clave de licencia", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__mapCompanyLines": "Mapeo de líneas de sociedad", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__property__setupId": "Id. de parametrización", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__defaultInstance": "Instancia por defecto", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__defaultInstance__failed": "Error de instancia por defecto", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getIsCommit": "Obtener confirmación", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getIsCommit__failed": "Error al obtener la confirmación", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getUrl": "Obtener URL", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getUrl__failed": "Error al obtener la URL", "@sage/xtrem-avalara-gateway/nodes__avalara_configuration__query__getUrl__parameter__isSandBox": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__load_tax_codes_from_avalara_asked": "Los códigos de impuesto se están cargando. El proceso se ejecutará en segundo plano.", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara": "Cargar códigos de impuesto de Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__failed": "Error al cargar los códigos de impuesto de Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__description": "Descripción", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__fromTaxCode": "Desde código de impuesto", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__isActive": "Activo", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__mutation__loadTaxCodesFromAvalara__parameter__toTaxCode": "Hasta código de impuesto", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__node_name": "Impuesto de artículo en Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__property__description": "Descripción", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__property__id": "Id.", "@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__property__isActive": "Activo", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__mutation__serviceOptionChange": "Cambiar opción de servicio", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__mutation__serviceOptionChange__failed": "Error al cambiar la opción de servicio", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__node_name": "Gestión de opciones de Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__query__isServiceOptionActiveFunction": "Service option active function", "@sage/xtrem-avalara-gateway/nodes__avalara_option_management__query__isServiceOptionActiveFunction__failed": "Is service option active function failed.", "@sage/xtrem-avalara-gateway/nodes__avalara_response_listener__node_name": "Proceso de escucha de respuesta en Avalara", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-failed": "Ha habido un error al notificar la lista de códigos de exención.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-sent": "La notificación de la lista de códigos de exención se ha enviado.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-entity-use-notification-succeeded": "La notificación de la lista de códigos de exención ha recibido una respuesta. Registros recibidos: {{recordCount}}", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-failed": "Ha habido un error al notificar la lista de códigos de impuesto.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-sent": "La notificación de la lista de códigos de impuesto se ha enviado.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/list-tax-code-notification-succeeded": "La notificación de la lista de códigos de impuesto ha recibido una respuesta. Registros recibidos: {{recordCount}}", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-failed": "Ha habido un error al notificar el ping.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-sent": "La notificación del ping se ha enviado.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/ping-notification-succeeded": "La notificación del ping ha recibido una respuesta.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-failed": "Ha habido un error al notificar las consultas de sociedad.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-sent": "La notificación de las consultas de sociedad se ha enviado.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/query-companies-notification-succeeded": "La notificación de las consultas de sociedad ha recibido una respuesta. Registros recibidos: {{recordCount}}", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-failed": "Ha habido un error al notificar la resolución de dirección.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-sent": "La notificación de la resolución de dirección se ha enviado.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/resolve-address-notification-succeeded": "La notificación de la resolución de dirección ha recibido una respuesta.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-post-and-commit": "Ha habido un error al notificar la contabilización con confirmación de la factura rectificativa de venta.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-post-only": "Ha habido un error al notificar la contabilización sin confirmación de la factura rectificativa de venta.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-failed-taxes-and-values-only": "Ha habido un error al notificar el cálculo de impuestos de la factura rectificativa de venta.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-post-and-commit": "La notificación de la factura rectificativa de venta para contabilizar con confirmación se ha enviado.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-post-only": "La notificación de la factura rectificativa de venta para contabilizar sin confirmación se ha enviado.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-sent-taxes-and-values-only": "La notificación del cálculo de impuestos de la factura rectificativa de venta se ha enviado.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-post-and-commit": "La notificación de la factura rectificativa de venta para contabilizar con confirmación ha recibido una respuesta.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-post-only": "La notificación de la factura rectificativa de venta para contabilizar sin confirmación ha recibido una respuesta.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-credit-memo-notification-succeeded-taxes-and-values-only": "La notificación del cálculo de impuestos de la factura rectificativa de venta ha recibido una respuesta.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-post-and-commit": "Ha habido un error al notificar la contabilización con confirmación de la factura de venta.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-post-only": "Ha habido un error al notificar la contabilización sin confirmación de la factura de venta.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-failed-taxes-and-values-only": "Ha habido un error al notificar el cálculo de impuestos de la factura de venta.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-post-and-commit": "La notificación de la factura de venta para contabilizar con confirmación se ha enviado.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-post-only": "La notificación de la factura de venta para contabilizar sin confirmación se ha enviado.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-sent-taxes-and-values-only": "La notificación del cálculo de impuestos de la factura de venta se ha enviado.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-post-and-commit": "La notificación de la factura de venta para contabilizar con confirmación ha recibido una respuesta.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-post-only": "La notificación de la factura de venta para contabilizar sin confirmación ha recibido una respuesta.", "@sage/xtrem-avalara-gateway/nodes__avalara-listener/sales-invoice-notification-succeeded-taxes-and-values-only": "La notificación del cálculo de impuestos de la factura de venta ha recibido una respuesta.", "@sage/xtrem-avalara-gateway/nodes__avatax_listener__node_name": "Proceso de escucha en Avatax", "@sage/xtrem-avalara-gateway/nodes__company_extension_avalara_service_option_off": "Activa Avalara en \"Gestión de opciones\" para poder seleccionarlo como paquete de cálculo de impuestos.", "@sage/xtrem-avalara-gateway/nodes__entity_use__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-avalara-gateway/nodes__entity_use__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-avalara-gateway/nodes__entity_use__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-avalara-gateway/nodes__entity_use__load_entity_use_codes_from_avalara_asked": "Los códigos de exención se están cargando. El proceso se ejecutará en segundo plano.", "@sage/xtrem-avalara-gateway/nodes__entity_use__mutation__loadEntityUseCodesFromAvalara": "Cargar códigos de exención de Avalara", "@sage/xtrem-avalara-gateway/nodes__entity_use__mutation__loadEntityUseCodesFromAvalara__failed": "Error al cargar los códigos de exención de Avalara", "@sage/xtrem-avalara-gateway/nodes__entity_use__node_name": "Código de exención", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__description": "Descripción", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__id": "Id.", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__isActive": "Activo", "@sage/xtrem-avalara-gateway/nodes__entity_use__property__name": "Nombre", "@sage/xtrem-avalara-gateway/nodes__map_company__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-avalara-gateway/nodes__map_company__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-avalara-gateway/nodes__map_company__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-avalara-gateway/nodes__map_company__avalara_company_check_duplicate": "La sociedad de Avalara ya existe.", "@sage/xtrem-avalara-gateway/nodes__map_company__company_check_duplicate": "La sociedad ya existe.", "@sage/xtrem-avalara-gateway/nodes__map_company__company_mandatory": "Introduce una sociedad.", "@sage/xtrem-avalara-gateway/nodes__map_company__company_must_be_empty": "La entidad es una planta financiera. Deja la sociedad en blanco.", "@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_check_duplicate": "La planta financiera ya existe.", "@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_mandatory": "Introduce una planta financiera.", "@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_must_be_empty": "La entidad es una sociedad. Deja la planta financiera en blanco.", "@sage/xtrem-avalara-gateway/nodes__map_company__node_name": "Mapeo de sociedad", "@sage/xtrem-avalara-gateway/nodes__map_company__property__avalaraCompany": "Sociedad en Avalara", "@sage/xtrem-avalara-gateway/nodes__map_company__property__avalaraConfiguration": "Configuración de Avalara", "@sage/xtrem-avalara-gateway/nodes__map_company__property__company": "Sociedad", "@sage/xtrem-avalara-gateway/nodes__map_company__property__entityType": "Tipo de entidad", "@sage/xtrem-avalara-gateway/nodes__map_company__property__financialSite": "Planta financiera", "@sage/xtrem-avalara-gateway/nodes__map_company__property__isActive": "Activa", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany": "Validar sociedad de Avalara", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany__failed": "Error al validar la sociedad de Avalara", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany__parameter__avalaraCompany": "Sociedad en Avalara", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateAvalaraCompany__parameter__cachedAvalaraCompanies": "Sociedades en Avalara en caché", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany": "Validar sociedad", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__failed": "Error al validar la sociedad", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__parameter__cachedCompanies": "Sociedades en caché", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__parameter__company": "Sociedad", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateCompany__parameter__entityType": "Tipo de entidad", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite": "Validar planta financiera", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__failed": "Error al validar la planta financiera", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__parameter__cachedFinancialSites": "Plantas financieras en caché", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__parameter__entityType": "Tipo de entidad", "@sage/xtrem-avalara-gateway/nodes__map_company__query__validateFinancialSite__parameter__financialSite": "Planta financiera", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo__tax_calculation_asked": "Se ha solicitado el cálculo del impuesto para este abono.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__avalara_integration_inactive": "La integración con Avalara no está activada.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__cant_calculate_tax_wrong_invoice_status": "El impuesto no se puede calcular para esta factura rectificativa porque el estado es \"{{posted}}\".", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__cant_calculate_tax_wrong_tax_calculation_status": "El impuesto no se puede calcular para esta factura rectificativa porque el estado del cálculo de impuestos es \"{{inProgress}}\" o \"{{done}}\".", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__posted": "La factura rectificativa de venta se ha contabilizado.", "@sage/xtrem-avalara-gateway/nodes__sales_credit_memo_extension__tax_calculation_asked": "Se ha solicitado el cálculo del impuesto para esta factura rectificativa.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice__tax_calculation_asked": "Se ha solicitado el cálculo del impuesto para esta factura.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__avalara_integration_inactive": "La integración con Avalara no está activada.", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__cant_calculate_tax_wrong_invoice_status": "El impuesto no se puede calcular para esta factura porque el estado es \"{{posted}}\".", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__cant_calculate_tax_wrong_tax_calculation_status": "El impuesto no se puede calcular para esta factura porque el estado del cálculo de impuestos es \"{{inProgress}}\" o \"{{done}}\".", "@sage/xtrem-avalara-gateway/nodes__sales_invoice_extension__tax_calculation_asked": "Se ha solicitado el cálculo de impuestos para esta factura.", "@sage/xtrem-avalara-gateway/package__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page__title__avalara_configuration": "Configuración de Avalara", "@sage/xtrem-avalara-gateway/page-extensions__accounts_receivable_invoice_extension__taxDetails____columns__title__jurisdictionName": "Jurisdicción", "@sage/xtrem-avalara-gateway/page-extensions__business_entity_address_panel_extension__entityUse____title": "Código de exención", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__addresses____columnOverrides__title": "Código de exención", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__deliveryAddresses____columns__columns__entityUse__title": "Nombre", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__deliveryAddresses____columns__columns__entityUse__title__2": "Id.", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__deliveryAddresses____columns__columns__entityUse__title__3": "Descripción", "@sage/xtrem-avalara-gateway/page-extensions__customer_extension__deliveryAddresses____columns__title__entityUse": "Uso de entidad", "@sage/xtrem-avalara-gateway/page-extensions__item_extension____navigationPanel__listItem__avalaraItemTax__title": "Impuesto de artículo en Avalara", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__avalaraItemTax____lookupDialogTitle": "Seleccionar impuesto de artículo en Avalara", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__avalaraItemTax____title": "Impuesto de artículo en Avalara", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__itemTaxability____columns__title__description": "Descripción", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__itemTaxability____columns__title__id": "Id.", "@sage/xtrem-avalara-gateway/page-extensions__item_extension__itemTaxability____title": "Código de impuesto Avalara", "@sage/xtrem-avalara-gateway/page-extensions__option_management_base_extension__avalaraBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__option_management_base_extension__avalaraConfigurationPage____title": "Página de configuración de Avalara", "@sage/xtrem-avalara-gateway/page-extensions__option_management_base_extension__isAvalaraServiceOptionActive____title": "Activa", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalara____title": "Calcular impuesto", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalaraMessage____title": "Men<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalaraNotificationBlock____title": "Información", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__avalaraNotificationSection____title": "Notificación de Avalara", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__columns__entityUse__title": "Nombre", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__columns__entityUse__title__2": "Id.", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__columns__entityUse__title__3": "Descripción", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__title__entityUse": "Código de exención", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__lines____columns__title__entityUse__name": "Código de exención", "@sage/xtrem-avalara-gateway/page-extensions__sales_credit_memo_extension__taxDetails____columns__title__jurisdictionName": "Jurisdicción", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalara____title": "Calcular impuesto", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalaraMessage____title": "Men<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalaraNotificationBlock____title": "Información", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__avalaraNotificationSection____title": "Notificación de Avalara", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__columns__entityUse__title": "Nombre", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__columns__entityUse__title__2": "Id.", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__columns__entityUse__title__3": "Descripción", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__title__entityUse": "Código de exención", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__lines____columns__title__entityUse__name": "Código de exención", "@sage/xtrem-avalara-gateway/page-extensions__sales_invoice_extension__taxDetails____columns__title__jurisdictionName": "Jurisdicción", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension____navigationPanel__listItem__line30__title": "Código de exención", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__entityUse____title": "Código de exención", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__columns__entityUse__title": "Nombre", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__columns__entityUse__title__2": "Id.", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__columns__entityUse__title__3": "Descripción", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__title__entityUse": "Código de exención", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_extension__lines____columns__title__entityUse__name": "Código de exención", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_card_view_extension__salesOrderLines____columns__columns__entityUse__title": "Nombre", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_card_view_extension__salesOrderLines____columns__columns__entityUse__title__2": "Id.", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_card_view_extension__salesOrderLines____columns__columns__entityUse__title__3": "Descripción", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_card_view_extension__salesOrderLines____columns__title__entityUse": "Código de exención", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_panel_extension__salesOrderLines____columns__columns__entityUse__name__title": "Nombre", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_panel_extension__salesOrderLines____columns__columns__entityUse__name__title__2": "Id.", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_panel_extension__salesOrderLines____columns__columns__entityUse__name__title__3": "Descripción", "@sage/xtrem-avalara-gateway/page-extensions__sales_order_table_panel_extension__salesOrderLines____columns__title__entityUse__name": "Código de exención", "@sage/xtrem-avalara-gateway/page-extensions__sales_shipment_extension____navigationPanel__listItem__line11__title": "Código de exención", "@sage/xtrem-avalara-gateway/page-extensions__sales_shipment_extension__entityUse____title": "Código de exención", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraDocumentNumber____title": "Número de documento", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraDocumentType____title": "Tipo de documento", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__code": "Código", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__dateLogged": "<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__documentId": "Id. de documento", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__documentNumber": "Número de documento", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__documentType": "Tipo de documento", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__errorMessage": "Men<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__internalMessage": "Mensaje interno", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__isCommit": "Confirmación", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__isPosting": "Contabilización", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__message": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__recordCount": "Número de registros", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__status": "Estado", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____columns__title__topic": "<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistory____title": "Resul<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistoryBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistoryCriteriaBlock____title": "Criterios", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraHistoryStatus____title": "Estado", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraLatestNotification____title": "Últimas notificaciones", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraMainTopic____title": "<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__avalaraTopic____title": "<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/page-extensions__sys_notification_history_extension__fromDate____title": "Fecha de inicio", "@sage/xtrem-avalara-gateway/page-extensions__tax_panel_extension__taxDetails____columns__title__jurisdictionName": "Jurisdicción", "@sage/xtrem-avalara-gateway/pages__avalara_company____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_company____navigationPanel__optionsMenu__title__2": "Activas", "@sage/xtrem-avalara-gateway/pages__avalara_company____navigationPanel__optionsMenu__title__3": "Inactivas", "@sage/xtrem-avalara-gateway/pages__avalara_company____objectTypePlural": "Sociedades en Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_company____objectTypeSingular": "Sociedad en Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_company____title": "Sociedad", "@sage/xtrem-avalara-gateway/pages__avalara_company__id____title": "Id.", "@sage/xtrem-avalara-gateway/pages__avalara_company__isActive____title": "Activa", "@sage/xtrem-avalara-gateway/pages__avalara_company__mainSection____title": "General", "@sage/xtrem-avalara-gateway/pages__avalara_company__name____title": "Nombre", "@sage/xtrem-avalara-gateway/pages__avalara_configuration____subtitle": "Configuración Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_configuration____title": "Configuración", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__accountId____title": "<PERSON><PERSON><PERSON> de cuenta", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__addMapCompany____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__addressTextCase____title": "Mayúsculas y minúsculas del texto de dirección", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__authenticated_sucessfull": "Has iniciado sesión en la versión {{version}} con la cuenta {{authenticatedUserName}}.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__authentication_not_sucessfull": "Has iniciado sesión en la versión {{version}} sin autenticación.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__avalara_item_tax_dialog_content": "¿Quieres cargar los códigos de impuesto de Avalara?", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__avalara_item_tax_dialog_title": "Confirmación de carga", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__companies_cannot_be_loaded": "Los códigos de sociedad no se pueden cargar.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__confirm_delete": "", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__delete_dialog_content": "", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__delete_dialog_title": "", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__descriptionFilter____title": "Descripción", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__endPointUrl____title": "Punto de conexión", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__entity_use_cannot_be_loaded": "Los códigos de exención no se pueden cargar.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__fromTaxCode____title": "Desde código de impuesto", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__id____title": "Id.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isActive____title": "Activa", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isActiveFilter____title": "Activos", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isAddressValidationActive____title": "Validación de dirección", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isCommittedTransaction____title": "Transacción confirmada", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__isSandboxMode____title": "Modo de espacio aislado", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__licenseKey____title": "Clave de licencia", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__load_button_text": "<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__load_tax_exception": "Los códigos de impuesto no se pueden cargar: {{exception}}", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__loadBlock____title": "<PERSON><PERSON> da<PERSON> de Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__loadTaxCodesBlock____title": "Criterios", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__loadTaxCodesSection____title": "Cargar códigos de impuesto de Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mainSection____title": "General", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyBlock____title": "Mapeo de sociedades", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__lookupDialogTitle__avalaraCompany__id": "Seleccionar sociedad en Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__lookupDialogTitle__company__id": "Seleccionar sociedad", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__lookupDialogTitle__financialSite__id": "Seleccionar planta financiera", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__avalaraCompany__id": "Sociedad en Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__company__id": "Sociedad", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__entityType": "Tipo de entidad", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__financialSite__id": "Planta financiera", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____columns__title__isActive": "Activa", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____dropdownActions__title__2": "Eliminar", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanyLines____title": "Sociedades", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__mapCompanySection____title": "Mapeo de sociedades", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__taxes_cannot_be_loaded": "Los códigos de impuesto no se pueden cargar.", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__testConnection____title": "Prueba de conexión", "@sage/xtrem-avalara-gateway/pages__avalara_configuration__toTaxCode____title": "Hasta código de impuesto", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____navigationPanel__optionsMenu__title__3": "Inactivos", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____objectTypePlural": "Impuestos de artículo en Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____objectTypeSingular": "Impuesto de artículo en Avalara", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax____title": "Impuesto de artículo", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__description____title": "Descripción", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__id____title": "Id.", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__isActive____title": "Activo", "@sage/xtrem-avalara-gateway/pages__avalara_item_tax__mainSection____title": "General", "@sage/xtrem-avalara-gateway/pages__delete_avalara_link_company_dialog_content": "Sociedades vinculadas al paquete de cálculo de impuestos de Avalara: {{noCompanies}}. <PERSON> continúas, se cambiarán al cálculo de impuestos genérico.", "@sage/xtrem-avalara-gateway/pages__delete_avalara_link_company_dialog_title": "Confirmar gestión de impuestos", "@sage/xtrem-avalara-gateway/pages__entity_use____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-avalara-gateway/pages__entity_use____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-avalara-gateway/pages__entity_use____navigationPanel__optionsMenu__title__3": "Inactivos", "@sage/xtrem-avalara-gateway/pages__entity_use____objectTypePlural": "Códigos de exención en Avalara", "@sage/xtrem-avalara-gateway/pages__entity_use____objectTypeSingular": "Código de exención en Avalara", "@sage/xtrem-avalara-gateway/pages__entity_use____title": "Código de exención", "@sage/xtrem-avalara-gateway/pages__entity_use__description____title": "Descripción", "@sage/xtrem-avalara-gateway/pages__entity_use__id____title": "Id.", "@sage/xtrem-avalara-gateway/pages__entity_use__isActive____title": "Activo", "@sage/xtrem-avalara-gateway/pages__entity_use__mainSection____title": "General", "@sage/xtrem-avalara-gateway/pages__entity_use__name____title": "Nombre", "@sage/xtrem-avalara-gateway/pages__item_taxability____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__item_taxability____navigationPanel__optionsMenu__title__2": "Activas", "@sage/xtrem-avalara-gateway/pages__item_taxability____navigationPanel__optionsMenu__title__3": "Inactivas", "@sage/xtrem-avalara-gateway/pages__item_taxability____subtitle": "Código de impuesto de artículo", "@sage/xtrem-avalara-gateway/pages__item_taxability____title": "Código de impuesto de artículo", "@sage/xtrem-avalara-gateway/pages__item_taxability__description____title": "Descripción", "@sage/xtrem-avalara-gateway/pages__item_taxability__id____title": "Id.", "@sage/xtrem-avalara-gateway/pages__item_taxability__isActive____title": "Activa", "@sage/xtrem-avalara-gateway/pages__map_company__edit____title": "Editar sociedad de mapeo", "@sage/xtrem-avalara-gateway/pages__map_company_panel____title": "Mapeo de sociedades", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraCompany____lookupDialogTitle": "Seleccionar sociedad en Avalara", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraCompany____placeholder": "Seleccionar sociedad en Avalara", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraCompany____title": "Sociedad en Avalara", "@sage/xtrem-avalara-gateway/pages__map_company_panel__avalaraConfiguration____title": "Configuración de Avalara", "@sage/xtrem-avalara-gateway/pages__map_company_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____columns__title__description": "Descripción", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____columns__title__id": "Id.", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____columns__title__name": "Nombre", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____lookupDialogTitle": "Seleccionar sociedad", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____placeholder": "Seleccionar sociedad", "@sage/xtrem-avalara-gateway/pages__map_company_panel__company____title": "Sociedad", "@sage/xtrem-avalara-gateway/pages__map_company_panel__confirm____title": "Aceptar", "@sage/xtrem-avalara-gateway/pages__map_company_panel__entityType____title": "Tipo de entidad", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____lookupDialogTitle": "Seleccionar planta financiera", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____placeholder": "Seleccionar...", "@sage/xtrem-avalara-gateway/pages__map_company_panel__financialSite____title": "Planta financiera", "@sage/xtrem-avalara-gateway/pages__map_company_panel__isActive____title": "Activa", "@sage/xtrem-avalara-gateway/pages__map_company_panel__mainSection____title": "General", "@sage/xtrem-avalara-gateway/pages__map_company_panel__save____title": "Aceptar", "@sage/xtrem-avalara-gateway/pages__map-company-panel__edit____title": "Editar mapeo de sociedades", "@sage/xtrem-avalara-gateway/pages__map-company-panel__new____title": "Añadir mapeo de sociedades", "@sage/xtrem-avalara-gateway/pages__test_conntection": "Prueba de conexión", "@sage/xtrem-avalara-gateway/pages-confirm-no": "No", "@sage/xtrem-avalara-gateway/pages-confirm-yes": "Sí", "@sage/xtrem-avalara-gateway/permission__manage__name": "Gestionar", "@sage/xtrem-avalara-gateway/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-avalara-gateway/service_options__avalara_option__name": "Opción de Avalara", "@sage/xtrem-avalara-gateway/sys__notification_history_extension__revert_criteria": "Revertir criterios", "@sage/xtrem-avalara-gateway/sys__notification_history_extension__search": "Buscar"}