import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';

export const salesInvoiceExtension = new ActivityExtension({
    extends: xtremSales.activities.salesInvoice,
    __filename,
    permissions: [],
    operationGrants: {
        manage: [
            {
                operations: ['calculateTax'],
                on: [() => xtremSales.nodes.SalesInvoice],
            },
        ],

        post: [
            {
                operations: ['postAvalara'],
                on: [() => xtremSales.nodes.SalesInvoice],
            },
        ],
    },
});
