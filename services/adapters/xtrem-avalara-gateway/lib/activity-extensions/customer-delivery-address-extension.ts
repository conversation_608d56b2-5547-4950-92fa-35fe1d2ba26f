import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { EntityUse } from '../nodes/entity-use';

export const customerDeliveryAddress = new ActivityExtension({
    extends: xtremMasterData.activities.businessEntity,
    __filename,
    permissions: [],
    operationGrants: {
        read: [{ operations: ['read'], on: [() => EntityUse] }],
        create: [{ operations: ['read'], on: [() => EntityUse] }],
        update: [{ operations: ['read'], on: [() => EntityUse] }],
    },
});
