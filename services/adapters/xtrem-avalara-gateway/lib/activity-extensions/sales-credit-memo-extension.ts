import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';

export const salesCreditMemoExtension = new ActivityExtension({
    extends: xtremSales.activities.salesCreditMemo,
    __filename,
    permissions: [],
    operationGrants: {
        manage: [
            {
                operations: ['calculateTax'],
                on: [() => xtremSales.nodes.SalesCreditMemo],
            },
        ],

        post: [
            {
                operations: ['postAvalara'],
                on: [() => xtremSales.nodes.SalesCreditMemo],
            },
        ],
    },
});
