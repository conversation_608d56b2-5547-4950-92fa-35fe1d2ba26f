import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import { AvalaraOptionManagement } from '../nodes/avalara-option-management';

export const avalaraOptionManagementExtension = new ActivityExtension({
    extends: xtremStructure.activities.baseOptionManagement,
    __filename,
    permissions: [],
    operationGrants: {
        read: [
            { operations: ['isServiceOptionActiveFunction'], on: [() => AvalaraOptionManagement] },
            { operations: ['lookup'], on: [() => xtremSystem.nodes.Company] },
        ],
        update: [
            { operations: ['serviceOptionChange'], on: [() => AvalaraOptionManagement] },
            { operations: ['lookup'], on: [() => xtremSystem.nodes.Company] },
        ],
    },
});
