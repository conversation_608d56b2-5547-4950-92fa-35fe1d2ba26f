import '@sage/xtrem-communication';
import * as activities from './activities/_index';
import * as activityExtensions from './activity-extensions/_index';
import * as classes from './classes/_index';
import * as enums from './enums/_index';
import * as functions from './functions/_index';
import * as interfaces from './interfaces/_index';
import * as menuItems from './menu-items/_index';
import * as nodeExtensions from './node-extensions/_index';
import * as nodes from './nodes/_index';
import * as serviceOptions from './service-options/_index';

export * as sharedFunctions from './shared-functions';
export {
    classes,
    nodes,
    enums,
    interfaces,
    menuItems,
    nodeExtensions,
    serviceOptions,
    functions,
    activities,
    activityExtensions,
};
