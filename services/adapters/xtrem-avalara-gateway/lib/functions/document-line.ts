import type { NodeCreateData } from '@sage/xtrem-core';
import type * as xtremTax from '@sage/xtrem-tax';

export const additionalUiTaxesLineProperties = async (
    uiTax: NodeCreateData<xtremTax.nodes.BaseLineTax>,
    tax: xtremTax.nodes.BaseLineTax,
): Promise<NodeCreateData<xtremTax.nodes.BaseLineTax>> => {
    uiTax.country = await tax.country;
    uiTax.region = await tax.region;
    uiTax.jurisdictionCode = await tax.jurisdictionCode;
    uiTax.jurisdictionType = await tax.jurisdictionType;
    uiTax.numberOfTaxableUnits = await tax.numberOfTaxableUnits;
    uiTax.numberOfNonTaxableUnits = await tax.numberOfNonTaxableUnits;
    uiTax.numberOfExemptUnits = await tax.numberOfExemptUnits;
    return uiTax;
};
