import * as xtremCommunication from '@sage/xtrem-communication';
import type { Context } from '@sage/xtrem-core';
import { Logger } from '@sage/xtrem-core';
import * as xtremAvalaraGateway from '../index';
import type {
    AnyAvataxQueryType,
    AnyAvataxResponseType,
    AvataxNotificationHistoryContext,
    AvataxNotificationMessage,
    AvataxNotificationParameters,
} from '../interfaces/notification';
import {
    topicCreateTransactionRequest,
    topicListEntityUseCodesRequest,
    topicListTaxCodesRequest,
    topicPingRequest,
    topicQueryCompaniesRequest,
    topicResolveAddressRequest,
} from '../shared-functions/notification-messages';

const logger = Logger.getLogger(__filename, 'avatax-api-call-listener');

export function getListenerInfoFromContext(
    context: Context,
    requestPayload: AnyAvataxQueryType | AnyAvataxResponseType,
): {
    notificationId: string;
    topic: string;
    replyTopic: string;
} {
    const notificationId = context.getContextValue('notificationId') || '';
    const topic = context.getContextValue('topic') || '';
    const replyTopic = context.getContextValue('replyTopic') || '';

    logger.info(() =>
        xtremAvalaraGateway.functions.getLoggerOnSentMessage(topic, requestPayload, notificationId, replyTopic),
    );

    return {
        notificationId,
        topic,
        replyTopic,
    };
}

export async function getListenerReplyInfoFromContext(
    context: Context,
    topic: string,
    replyTopic: string,
    requestPayload: AnyAvataxQueryType,
    responsePayload: AnyAvataxResponseType,
): Promise<string> {
    const replyId = await context.reply<AnyAvataxResponseType>(replyTopic, responsePayload);

    logger.info(() =>
        xtremAvalaraGateway.functions.getLoggerOnReplyMessage(topic, requestPayload, responsePayload, replyId),
    );

    return replyId;
}

export async function createNotificationHistoryOnSentEvent(
    context: Context,
    attributes: {
        notificationId: string;
        topic: string;
        replyTopic: string;
        listener: string;
    },
    requestPayload: AnyAvataxQueryType,
    parameters?: AvataxNotificationParameters,
) {
    const loggerMessage = xtremAvalaraGateway.functions.getLoggerOnSentMessage(
        attributes.topic,
        requestPayload,
        attributes.notificationId,
        attributes.replyTopic,
    );
    const status: xtremCommunication.enums.CommunicationState | undefined = 'sent';
    const errorMessage: { error: Error; notificationContext: AvataxNotificationHistoryContext } =
        xtremAvalaraGateway.functions.setSentMessage(context, attributes.topic, parameters);
    logger.info(() => loggerMessage);

    await xtremCommunication.nodes.SysNotificationHistory.createNotificationHistory(
        context,
        xtremAvalaraGateway.functions.getEnvelope(requestPayload, attributes, context.originId),
        status,
        errorMessage.error,
        attributes.listener,
        errorMessage.notificationContext,
    );
}

export async function createNotificationHistoryOnErrorOrSuccessEvent(
    context: Context,
    attributes: {
        notificationId: string;
        topic: string;
        replyId: string;
        replyTopic: string;
        listener: string;
    },
    requestPayload: AnyAvataxQueryType,
    responsePayload: AnyAvataxResponseType,
    parameters?: AvataxNotificationParameters,
) {
    const loggerMessage = xtremAvalaraGateway.functions.getLoggerOnErrorMessage(
        attributes.topic,
        requestPayload,
        responsePayload,
        attributes.replyId,
    );

    let status: xtremCommunication.enums.CommunicationState | undefined;
    let errorMessage: { error: Error; notificationContext: AvataxNotificationHistoryContext };

    if (responsePayload.error) {
        status = 'error';
        errorMessage = xtremAvalaraGateway.functions.setErrorMessage(
            context,
            attributes.topic,
            responsePayload,
            parameters,
        );
        logger.error(() => loggerMessage);
    } else {
        status = 'success';
        errorMessage = xtremAvalaraGateway.functions.setSuccessMessage(context, attributes.topic, parameters);
        logger.info(() => loggerMessage);
    }

    await xtremCommunication.nodes.SysNotificationHistory.createNotificationHistory(
        context,
        xtremAvalaraGateway.functions.getEnvelope(responsePayload, attributes, context.originId),
        status,
        errorMessage.error,
        attributes.listener,
        errorMessage.notificationContext,
    );
}

export function getEnvelope(
    payload: AnyAvataxQueryType | AnyAvataxResponseType,
    attributes: {
        notificationId: string;
        topic: string;
        replyId?: string;
        replyTopic?: string;
    },
    originId?: string,
) {
    return {
        payload,
        attributes: {
            notificationId: attributes.notificationId,
            topic: attributes.topic,
            replyId: attributes.replyId,
            replyTopic: attributes.replyTopic,
            originId,
            locale: 'en-US',
        },
    } as unknown as xtremCommunication.NotificationEnvelope<any>;
}

export function getNotificationContext(
    topic: string,
    avataxNotificationMessage: AvataxNotificationMessage,
    parameters?: AvataxNotificationParameters,
    stackErrorMessage?: string,
): AvataxNotificationHistoryContext {
    return {
        topic,
        documentType: parameters?.documentType,
        documentId: parameters?.documentId,
        documentNumber: parameters?.documentNumber,
        isCommit: parameters?.isCommit,
        isPosting: parameters?.isPosting,
        recordCount: parameters?.recordCount,
        localizedMessage: avataxNotificationMessage.localizedMessage,
        message: stackErrorMessage,
        code: avataxNotificationMessage.code,
    };
}

export function setErrorMessage(
    context: Context,
    topic: string,
    responsePayload: AnyAvataxResponseType,
    parameters?: AvataxNotificationParameters,
): { error: Error; notificationContext: AvataxNotificationHistoryContext } {
    const documentType = parameters?.documentType !== undefined ? parameters?.documentType : '';
    const isPosting = parameters?.isPosting !== undefined ? parameters?.isPosting : false;
    const isCommit = parameters?.isCommit !== undefined ? parameters?.isCommit : false;
    let stackErrorMessage = '';
    if (responsePayload.error?.message) {
        stackErrorMessage = responsePayload.error?.message;
    }

    const avataxNotificationMessage = xtremAvalaraGateway.functions.getAvataxMessage(
        context,
        topic,
        'error',
        documentType,
        isPosting,
        isCommit,
    );

    const notificationContext: AvataxNotificationHistoryContext = xtremAvalaraGateway.functions.getNotificationContext(
        topic,
        avataxNotificationMessage,
        parameters,
        stackErrorMessage,
    );

    const error: Error = {
        name: avataxNotificationMessage.code,
        message: stackErrorMessage,
        stack: JSON.stringify(responsePayload.error),
    };
    return {
        error,
        notificationContext,
    };
}

export function setSuccessMessage(
    context: Context,
    topic: string,
    parameters?: AvataxNotificationParameters,
): { error: Error; notificationContext: AvataxNotificationHistoryContext } {
    const recordCount = parameters?.recordCount !== undefined ? parameters?.recordCount : 1;
    const documentType = parameters?.documentType !== undefined ? parameters?.documentType : '';
    const isPosting = parameters?.isPosting !== undefined ? parameters?.isPosting : false;
    const isCommit = parameters?.isCommit !== undefined ? parameters?.isCommit : false;

    const avataxNotificationMessage = xtremAvalaraGateway.functions.getAvataxMessage(
        context,
        topic,
        'success',
        documentType,
        isPosting,
        isCommit,
        recordCount,
    );

    const notificationContext: AvataxNotificationHistoryContext = xtremAvalaraGateway.functions.getNotificationContext(
        topic,
        avataxNotificationMessage,
        parameters,
    );

    return {
        error: { name: '', message: '' },
        notificationContext,
    };
}

export function setSentMessage(
    context: Context,
    topic: string,
    parameters?: AvataxNotificationParameters,
): { error: Error; notificationContext: AvataxNotificationHistoryContext } {
    const documentType = parameters?.documentType !== undefined ? parameters?.documentType : '';
    const isPosting = parameters?.isPosting !== undefined ? parameters?.isPosting : false;
    const isCommit = parameters?.isCommit !== undefined ? parameters?.isCommit : false;

    const avataxNotificationMessage = xtremAvalaraGateway.functions.getAvataxMessage(
        context,
        topic,
        'sent',
        documentType,
        isPosting,
        isCommit,
    );

    const notificationContext: AvataxNotificationHistoryContext = xtremAvalaraGateway.functions.getNotificationContext(
        topic,
        avataxNotificationMessage,
        parameters,
    );

    return {
        error: { name: '', message: '' },
        notificationContext,
    };
}

function getAvataxCode(status: string, partialCode: string) {
    switch (status) {
        case 'sent': {
            return `${partialCode}Sent`;
        }
        case 'error': {
            return `${partialCode}Error`;
        }
        case 'success': {
            return `${partialCode}Succeeded`;
        }
        default:
            return '';
    }
}

function getAvataxMessageCode(
    topic: string,
    status: string,
    documentType?: string,
    isPosting = false,
    isCommit = false,
): string {
    switch (topic) {
        case topicPingRequest: {
            return getAvataxCode(status, 'AvalaraPing');
        }
        case topicResolveAddressRequest: {
            return getAvataxCode(status, 'AvalaraResolveAddress');
        }
        case topicListEntityUseCodesRequest: {
            return getAvataxCode(status, 'AvalaraListEntityUseCodes');
        }
        case topicListTaxCodesRequest: {
            return getAvataxCode(status, 'AvalaraListTaxCodes');
        }
        case topicQueryCompaniesRequest: {
            return getAvataxCode(status, 'AvalaraQueryCompanies');
        }
        case topicCreateTransactionRequest: {
            if (documentType === 'SalesInvoice') {
                if (isCommit && isPosting) {
                    return getAvataxCode(status, 'AvalaraCreateTransactionOnSalesInvoiceForPostingWithCommit');
                }
                if (isPosting && !isCommit) {
                    return getAvataxCode(status, 'AvalaraCreateTransactionOnSalesInvoiceForPostingWithUncommit');
                }
                if (!isPosting && !isCommit) {
                    return getAvataxCode(status, 'AvalaraCreateTransactionOnSalesInvoiceForTaxCalculation');
                }
            }
            if (documentType === 'SalesCreditMemo') {
                if (isCommit && isPosting) {
                    return getAvataxCode(status, 'AvalaraCreateTransactionOnSalesCreditMemoForPostingWithCommit');
                }
                if (isPosting && !isCommit) {
                    return getAvataxCode(status, 'AvalaraCreateTransactionOnSalesCreditMemoForPostingWithUncommit');
                }
                if (!isPosting && !isCommit) {
                    return getAvataxCode(status, 'AvalaraCreateTransactionOnSalesCreditMemoForTaxCalculation');
                }
            }
            break;
        }
        default:
            return '';
    }
    return '';
}

export function getAvataxMessage(
    context: Context,
    topic: string,
    status: string,
    documentType?: string,
    isPosting = false,
    isCommit = false,
    recordCount?: number,
): AvataxNotificationMessage {
    const code = getAvataxMessageCode(topic, status, documentType, isPosting, isCommit);

    const localizedMessage = xtremAvalaraGateway.sharedFunctions.getAvataxNotificationMessageLocalized(
        context,
        code,
        recordCount,
    );
    const avataxNotificationMessage: AvataxNotificationMessage = {
        code,
        localizedMessage,
    };
    return avataxNotificationMessage;
}

export function getLoggerOnSentMessage(
    topic: string,
    requestPayload: AnyAvataxQueryType | AnyAvataxResponseType,
    notificationId: string,
    replyTopic: string,
) {
    return `AvataxListener sent for "${JSON.stringify(
        topic,
    )}" topic. NotificationId ${notificationId} for topic ${topic} with requestPayload ${JSON.stringify(
        requestPayload,
    )} / Reply topic ${replyTopic}`;
}

export function getLoggerOnReplyMessage(
    topic: string,
    requestPayload: AnyAvataxQueryType,
    responsePayload: AnyAvataxResponseType,
    replyId: string,
) {
    return `AvataxListener succeeded for "${JSON.stringify(topic)}" topic. Request payload: ${JSON.stringify(
        requestPayload,
    )}. Reply notificationID ${replyId}, response payload: ${JSON.stringify(responsePayload)}`;
}

export function getLoggerOnErrorMessage(
    topic: string,
    requestPayload: AnyAvataxQueryType,
    responsePayload: AnyAvataxResponseType,
    replyId: string,
) {
    return `AvataxListener failed for "${JSON.stringify(topic)}" topic. Request payload: ${JSON.stringify(
        requestPayload,
    )}. Reply notificationID ${replyId}, response payload: ${JSON.stringify(responsePayload)}`;
}
