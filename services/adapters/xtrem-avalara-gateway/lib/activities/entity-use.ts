import { Activity } from '@sage/xtrem-core';
import { EntityUse } from '../nodes/entity-use';

export const entityUse = new Activity({
    description: 'Entity use',
    node: () => EntityUse,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['read', 'create', 'update', 'delete', 'loadEntityUseCodesFromAvalara'],
                on: [() => EntityUse],
            },
        ],
    },
});
