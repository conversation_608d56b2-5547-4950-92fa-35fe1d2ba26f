import { Activity } from '@sage/xtrem-core';
import { AvalaraItemTax } from '../nodes/avalara-item-tax';

export const avalaraItemTax = new Activity({
    description: 'Item tax',
    node: () => AvalaraItemTax,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['create', 'update', 'delete'],
                on: [() => AvalaraItemTax],
            },
        ],
    },
});
