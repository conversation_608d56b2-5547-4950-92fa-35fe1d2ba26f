import { Activity } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { AvalaraCompany } from '../nodes/avalara-company';
import { AvalaraConfiguration } from '../nodes/avalara-configuration';
import { AvalaraItemTax } from '../nodes/avalara-item-tax';
import { EntityUse } from '../nodes/entity-use';

export const avalaraConfiguration = new Activity({
    description: 'Configuration',
    node: () => AvalaraConfiguration,
    __filename,
    permissions: ['manage'],
    operationGrants: {
        manage: [
            {
                operations: ['read', 'update', 'getUrl', 'defaultInstance', 'ping', 'getIsCommit'],
                on: [() => AvalaraConfiguration],
            },
            { operations: ['lookup', 'loadCompanyFromAvalara'], on: [() => AvalaraCompany] },
            { operations: ['loadTaxCodesFromAvalara'], on: [() => AvalaraItemTax] },
            { operations: ['loadEntityUseCodesFromAvalara'], on: [() => EntityUse] },
            { operations: ['lookup', 'loadCompanyFromAvalara'], on: [() => AvalaraCompany] },
            { operations: ['lookup'], on: [() => xtremSystem.nodes.Company] },
        ],
    },
});
