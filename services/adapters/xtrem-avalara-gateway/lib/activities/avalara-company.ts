import { Activity } from '@sage/xtrem-core';
import { AvalaraCompany } from '../nodes/avalara-company';

export const avalaraCompany = new Activity({
    description: 'Company',
    node: () => AvalaraCompany,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['create', 'update', 'delete', 'loadCompanyFromAvalara'],
                on: [() => AvalaraCompany],
            },
        ],
    },
});
