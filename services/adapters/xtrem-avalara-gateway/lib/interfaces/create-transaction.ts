import type { AvaTaxMessage, CoordinateInfo, GeneralAddress } from './common';
import type { ResponseErrorMessage } from './request';

export interface TransactionRequest {
    code?: string;
    lines: LineItemModel[];
    type?: string;
    companyCode?: string;
    date: string;
    salespersonCode?: string;
    customerCode?: string;
    customerUsageType?: string;
    entityUseCode?: string;
    discount?: number;
    purchaseOrderNo?: string;
    exemptionNo?: string;
    addresses?: AddressesModel;
    parameters?: TransactionParameterModel;
    userDefinedFields?: TransactionUserDefinedFieldModel[];
    referenceCode?: string;
    reportingLocationCode?: string;
    commit?: boolean;
    batchCode?: string;
    taxOverride?: TaxOverrideModel;
    currencyCode?: string;
    serviceMode?: string;
    exchangeRate?: number;
    exchangeRateEffectiveDate?: string;
    exchangeRateCurrencyCode?: string;
    posLaneCode?: string;
    businessIdentificationNo?: string;
    isSellerImporterOfRecord?: boolean;
    description?: string;
    email?: string;
    debugLevel?: string;
    customerSupplierName?: string;
    dataSourceId?: number;
    deliveryTerms?: string;
}

export interface LineItemModel {
    number?: string;
    quantity?: number;
    amount: number;
    addresses?: AddressesModel;
    taxCode?: string;
    customerUsageType?: string;
    entityUseCode?: string;
    itemCode?: string;
    exemptionCode?: string;
    discounted?: boolean;
    taxIncluded?: boolean;
    revenueAccount?: string;
    ref1?: string;
    ref2?: string;
    description?: string;
    businessIdentificationNo?: string;
    taxOverride?: TaxOverrideModel;
    parameters?: TransactionLineParameterModel;
    userDefinedFields?: TransactionLineUserDefinedFieldModel[];
    hsCode?: string;
    merchantSellerId?: string;
    merchantSellerIdentifier?: string;
    marketplaceLiabilityType?: string;
    originationDocumentId?: string;
    originationSite?: string;
    category?: string;
    summary?: string;
}
export interface AddressesModel {
    singleLocation?: AddressLocationInfo;
    shipFrom?: AddressLocationInfo;
    shipTo?: AddressLocationInfo;
    pointOfOrderOrigin?: AddressLocationInfo;
    pointOfOrderAcceptance?: AddressLocationInfo;
    goodsPlaceOrServiceRendered?: AddressLocationInfo;
    import?: AddressLocationInfo;
}

export interface AddressLocationInfo extends GeneralAddress, CoordinateInfo {
    locationCode?: string;
}

interface TransactionAddressModel extends GeneralAddress {
    id?: number;
    transactionId?: number;
    boundaryLevel?: string;
    taxRegionId?: number;
    latitude?: string;
    longitude?: string;
    jurisdictions?: JurisdictionModel[];
}

interface JurisdictionModel {
    code?: string;
    name?: string;
    type?: string;
    rate?: number;
    salesRate?: number;
    signatureCode?: string;
    region?: string;
    useRate?: number;
    city?: string;
    county?: string;
    country?: string;
    shortName?: string;
    stateFips?: string;
    countyFips?: string;
    placeFips?: string;
    id?: number;
    effectiveDate?: string;
    endDate?: string;
}

export interface TaxOverrideModel {
    type?: string;
    taxAmount?: number;
    taxDate?: string;
    reason?: string;
    taxAmountByTaxTypes?: TransactionLineTaxAmountByTaxTypeModel[];
}

interface TransactionLocationTypeModel {
    documentLocationTypeId?: number;
    documentId?: number;
    documentAddressId?: number;
    locationTypeCode?: string;
}

export interface TransactionResponse {
    id?: number;
    code?: string;
    companyId?: number;
    date?: string;
    type?: string;
    paymentDate?: string;
    customerCode?: string;
    status?: string;
    batchCode?: string;
    currencyCode?: string;
    exchangeRateCurrencyCode?: string;
    customerUsageType?: string;
    entityUseCode?: string;
    customerVendorCode?: string;
    exemptNo?: string;
    reconciled?: boolean;
    locationCode?: string;
    reportingLocationCode?: string;
    purchaseOrderNo?: string;
    referenceCode?: string;
    salespersonCode?: string;
    taxOverrideType?: string;
    taxOverrideAmount?: number;
    taxOverrideReason?: string;
    totalAmount?: number;
    totalExempt?: number;
    totalDiscount?: number;
    totalTax?: number;
    totalTaxable?: number;
    totalTaxCalculated?: number;
    adjustmentReason?: string;
    adjustmentDescription?: string;
    locked?: boolean;
    region?: string;
    country?: string;
    version?: number;
    softwareVersion?: string;
    originAddressId?: number;
    destinationAddressId?: number;
    exchangeRateEffectiveDate?: string;
    exchangeRate?: number;
    isSellerImporterOfRecord?: boolean;
    description?: string;
    email?: string;
    businessIdentificationNo?: string;
    modifiedDate?: string;
    modifiedUserId?: number;
    taxDate?: string;
    lines?: TransactionLineModel[];
    addresses?: TransactionAddressModel[];
    locationTypes?: TransactionLocationTypeModel[];
    summary?: TransactionSummary[];
    taxDetailsByTaxType?: TaxDetailsByTaxType[];
    parameters?: TransactionParameterModel;
    userDefinedFields?: TransactionUserDefinedFieldModel[];
    messages?: AvaTaxMessage[];
    invoiceMessages?: InvoiceMessageModel[];
    customerSupplierName?: string;
    dataSourceId?: number;
    deliveryTerms?: string;
}

export type TransactionResponseType = TransactionResponse & Partial<ResponseErrorMessage>;

interface TransactionUserDefinedFieldModel {
    name?: string;
    value?: string;
}

interface TransactionParameterModel {
    name?: string;
    value?: string;
    unit?: string;
}

interface TaxDetailsByTaxType {
    taxType?: string;
    totalTaxable?: number;
    totalExempt?: number;
    totalNonTaxable?: number;
    totalTax?: number;
    taxSubTypeDetails?: TaxDetailsByTaxSubType[];
}

interface TaxDetailsByTaxSubType {
    taxSubType?: string;
    totalTaxable?: number;
    totalExempt?: number;
    totalNonTaxable?: number;
    totalTax?: number;
}

export interface TransactionLineModel {
    id?: number;
    transactionId?: number;
    lineNumber?: string;
    boundaryOverrideId?: number;
    customerUsageType?: string;
    entityUseCode?: string;
    description?: string;
    destinationAddressId?: number;
    originAddressId?: number;
    discountAmount?: number;
    discountTypeId?: number;
    exemptAmount?: number;
    exemptCertId?: number;
    certificateId?: string;
    exemptNo?: string;
    isItemTaxable?: boolean;
    isSSTP?: boolean;
    itemCode?: string;
    lineAmount?: number;
    quantity?: number;
    ref1?: string;
    ref2?: string;
    reportingDate?: string;
    revAccount?: string;
    sourcing?: string;
    tax?: number;
    taxableAmount?: number;
    taxCalculated?: number;
    taxCode?: string;
    taxCodeId?: number;
    taxDate?: string;
    taxEngine?: string;
    taxOverrideType?: string;
    businessIdentificationNo?: string;
    taxOverrideAmount?: number;
    taxOverrideReason?: string;
    taxIncluded?: boolean;
    merchantSellerId?: number;
    merchantSellerIdentifier?: string;
    marketplaceLiabilityType?: string;
    originationDocumentId?: string;
    originationSite?: string;
    details?: TransactionLineDetailModel[];
    nonPassthroughDetails?: TransactionLineDetailModel[];
    lineLocationTypes?: TransactionLineLocationTypeModel[];
    parameters?: TransactionLineParameterModel;
    userDefinedFields?: TransactionLineUserDefinedFieldModel[];
    hsCode?: string;
    costInsuranceFreight?: number;
    vatCode?: string;
    vatNumberTypeId?: number;
    taxAmountByTaxTypes?: TransactionLineTaxAmountByTaxTypeModel[];
    deemedSupplier?: string;
    category?: string;
    summary?: string;
}

interface TransactionLineTaxAmountByTaxTypeModel {
    taxTypeId?: string;
    taxAmount?: number;
}

interface TransactionLineUserDefinedFieldModel {
    name?: string;
    value?: string;
}

interface TransactionLineLocationTypeModel {
    documentLineLocationTypeId?: number;
    documentLineId?: number;
    documentAddressId?: number;
    locationTypeCode?: string;
}

interface TransactionLineParameterModel {
    name?: string;
    value?: string;
    unit?: string;
}

export interface TransactionLineDetailModel {
    id?: number;
    transactionLineId?: number;
    transactionId?: number;
    addressId?: number;
    country?: string;
    region?: string;
    countyFIPS?: string;
    stateFIPS?: string;
    exemptReasonId?: number;
    inState?: boolean;
    jurisCode?: string;
    jurisName?: string;
    jurisdictionId?: number;
    signatureCode?: string;
    stateAssignedNo?: string;
    jurisType?: string;
    nonTaxableRuleId?: number;
    nonTaxableType?: string;
    rate?: number;
    rateRuleId?: number;
    rateSourceId?: number;
    serCode?: string;
    sourcing?: string;
    tax?: number;
    taxType?: string;
    taxSubTypeId?: string;
    taxTypeGroupId?: string;
    taxAuthorityTypeId?: number;
    taxRegionId?: number;
    taxOverride?: number;
    rateType?: string;
    rateTypeCode?: string;
    unitOfBasis?: string;
    isNonPassThru?: boolean;
    isFee?: boolean;
    reportingTaxableUnits?: number;
    reportingNonTaxableUnits?: number;
    reportingExemptUnits?: number;
    reportingTax?: number;
    reportingTaxCalculated?: number;
    liabilityType?: string;
    taxName?: string;
    taxCalculated?: number;
    exemptAmount?: number;
    jurisdictionType?: string;
    nonTaxableAmount?: number;
    taxableAmount?: number;
    taxableUnits?: number;
    nonTaxableUnits?: number;
    exemptUnits?: number;
}

export interface TransactionSummary {
    country?: string;
    region?: string;
    jurisCode?: string;
    jurisName?: string;
    taxAuthorityType?: number;
    stateAssignedNo?: string;
    taxType?: string;
    taxSubType?: string;
    taxGroup?: string;
    rateType?: string;
    rateTypeCode?: string;
    rate?: number;
    tax?: number;
    taxName?: string;
    taxCalculated?: number;
    jurisType?: string;
    taxable?: number;
    nonTaxable?: number;
    exemption?: number;
}

interface InvoiceMessageModel {
    content?: string;
    lineNumbers?: [];
}
export interface CreateTransactionParameters {
    include?: string;
    model: TransactionRequest;
}
