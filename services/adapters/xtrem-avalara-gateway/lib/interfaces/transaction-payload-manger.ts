import type { CollectionElement, NodeCreateData, UpdateAction } from '@sage/xtrem-core';
import type * as xtremSales from '@sage/xtrem-sales';
import type * as xtremAvalaraGateway from '../../index';

export type TransactionDocumentType = xtremSales.nodes.SalesCreditMemo | xtremSales.nodes.SalesInvoice;

export type UnpackedArray<T> = T extends Array<infer U> ? U : never;

export type TransactionDocumentLineType = CollectionElement<TransactionDocumentType['lines']>;

export type Taxes = CollectionElement<TransactionDocumentType['taxes'] | TransactionDocumentLineType['taxes']>;

export type NodeCreateDataTaxes = Omit<
    NodeCreateData<
        Taxes & {
            _sortValue?: number;
            _action: UpdateAction;
        }
    >,
    'document' | '$'
>;

export type NodeCreateDataLines = Omit<
    NodeCreateData<
        TransactionDocumentLineType & {
            _sortValue?: number;
            _action: UpdateAction;
        }
    >,
    'document' | '$' | 'discountCharges' | 'consumptionAddress' | 'providerSiteAddress' | 'taxes'
>;

export interface DocumentsToCheck {
    documents: number[];
    documentsType: xtremAvalaraGateway.enums.TransactionPayloadManagerType;
    isCommit: boolean;
    isPosting: boolean;
}
