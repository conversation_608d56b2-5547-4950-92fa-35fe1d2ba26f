import type { QueryResponse } from './common';
import type { ResponseErrorMessage } from './request';

interface EntityUseCodeModel {
    code?: string;
    name?: string;
    description?: string;
    validCountries?: string[];
}

export interface ListEntityUseCodesResponse extends QueryResponse {
    value?: EntityUseCodeModel[];
}

export type ListEntityUseCodesResponseType = ListEntityUseCodesResponse & Partial<ResponseErrorMessage>;
