import type { ResponseErrorMessage } from './request';

export interface AvataxConfig {
    appName: any;
    appVersion: any;
    environment: any;
    machineName: any;
}
export interface AvataxCredentials {
    username?: string;
    password?: string;
    accountId?: string;
    licenseKey?: string;
    bearerToken?: string;
}

export interface AvataxTestConnection {
    version: string;
    authenticated: boolean;
}

export type AvataxTestResponseType = AvataxTestConnection & Partial<ResponseErrorMessage>;
