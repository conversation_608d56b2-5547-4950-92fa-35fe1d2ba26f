import type * as xtremAvalaraGateway from '../index';

export type AnyAvataxQueryType =
    | xtremAvalaraGateway.interfaces.QueryCompaniesRequest
    | xtremAvalaraGateway.interfaces.QueryRequest
    | xtremAvalaraGateway.interfaces.TransactionRequest
    | xtremAvalaraGateway.interfaces.ResolveAddressRequest
    | xtremAvalaraGateway.interfaces.DocumentsToCheck
    | {};

export type AnyAvataxResponseType =
    | xtremAvalaraGateway.interfaces.ResolveAddressResponseType
    | xtremAvalaraGateway.interfaces.TransactionResponseType
    | xtremAvalaraGateway.interfaces.ListTaxCodesResponseType
    | xtremAvalaraGateway.interfaces.QueryCompaniesResponseType
    | xtremAvalaraGateway.interfaces.ListEntityUseCodesResponseType
    | xtremAvalaraGateway.interfaces.AvataxTestResponseType;

export interface AvataxNotificationMessage {
    code: string;
    localizedMessage?: string;
}

export interface AvataxNotificationParameters {
    documentType?: string;
    documentId?: number;
    documentNumber?: string;
    recordCount?: number;
    isCommit?: boolean;
    isPosting?: boolean;
}

export interface AvataxNotificationHistoryContext {
    topic: string;
    documentType?: string;
    documentId?: number;
    documentNumber?: string;
    isCommit?: boolean;
    isPosting?: boolean;
    recordCount?: number;
    localizedMessage?: string;
    message?: string;
    code?: string;
}
