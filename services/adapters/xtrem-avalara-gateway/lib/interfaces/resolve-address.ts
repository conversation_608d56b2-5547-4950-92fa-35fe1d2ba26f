import type { AvaTaxMessage, CoordinateInfo, GeneralAddress } from './common';
import type { ResponseErrorMessage } from './request';

export interface ResolveAddressRequest extends GeneralAddress {
    textCase?: string; // Upper/Mixed
}

export interface AddressResolutionModel {
    address?: AddressInfo;
    validatedAddresses?: ValidatedAddressInfo[];
    coordinates?: CoordinateInfo;
    taxAuthorities?: TaxAuthorityInfo[];
    messages?: AvaTaxMessage[];
}

export type ResolveAddressResponseType = AddressResolutionModel & Partial<ResponseErrorMessage>;

interface TaxAuthorityInfo {
    avalaraId?: string;
    jurisdictionName?: string;
    jurisdictionType?: string;
    signatureCode?: string;
}

interface ValidatedAddressInfo extends AddressInfo {
    addressType?: string;
}

interface AddressInfo extends GeneralAddress, CoordinateInfo {}
