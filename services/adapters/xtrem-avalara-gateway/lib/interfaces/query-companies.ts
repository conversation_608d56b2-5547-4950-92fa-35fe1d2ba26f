import type { GeneralAddress, QueryRequest, QueryResponse, RecordInfo, TaxCodeModel } from './common';
import type { ResponseErrorMessage } from './request';

export interface QueryCompaniesRequest extends QueryRequest {
    include?: string;
}
export interface QueryCompaniesResponse extends QueryResponse {
    value?: CompanyModel[];
}

export type QueryCompaniesResponseType = QueryCompaniesResponse & Partial<ResponseErrorMessage>;

interface CompanyModel extends RecordInfo {
    id?: number;
    accountId?: number;
    parentCompanyId?: number;
    sstPid?: string;
    companyCode?: string;
    name?: string;
    isDefault?: boolean;
    defaultLocationId?: number;
    isActive?: boolean;
    taxpayerIdNumber?: string;
    IsFein?: boolean;
    hasProfile?: boolean;
    isReportingEntity?: boolean;
    sstEffectiveDate?: string;
    defaultCountry?: string;
    baseCurrencyCode?: string;
    roundingLevelId?: string;
    warningsEnabled?: boolean;
    isTest?: boolean;
    taxDependencyLevelId?: string;
    inProgress?: boolean;
    businessIdentificationNo?: string;
    contacts?: ContactModel[];
    items?: ItemModel[];
    locations?: LocationModel[];
    nexus?: NexusModel[];
    settings?: SettingModel[];
    taxCodes?: TaxCodeModel[];
    taxRules?: TaxRuleModel[];
    upcs?: UpcModel[];
    nonReportingChildCompanies?: CompanyModel[];
    exemptCerts?: EcmsModel[];
    mossId?: string;
    mossCountry?: string;
    parameters?: CompanyParameterDetailModel[];
    supplierandcustomers?: CustomerSupplierModel[];
}

interface CustomerSupplierModel {
    id?: number;
    companyId?: number;
    customerCode?: string;
}

interface CompanyParameterDetailModel {
    id?: number;
    companyId?: number;
    name?: string;
    value?: string;
}

interface EcmsModel extends RecordInfo {
    exemptCertId?: number;
    companyId?: number;
    customerCode?: string;
    customerName?: string;
    address1?: string;
    address2?: string;
    address3?: string;
    city?: string;
    region?: string;
    postalCode?: string;
    country?: string;
    exemptCertTypeId?: string;
    documentRefNo?: string;
    businessTypeId?: number;
    businessTypeOtherDescription?: string;
    exemptReasonId?: string;
    exemptReasonOtherDescription?: string;
    effectiveDate?: string;
    regionsApplicable?: string;
    exemptCertStatusId?: string;
    createdDate?: string;
    lastTransactionDate?: string;
    expiryDate?: string;
    countryIssued?: string;
    avaCertId?: string;
    exemptCertReviewStatusId?: string;
    details?: EcmsDetailModel[];
}

interface EcmsDetailModel {
    exemptCertDetailId?: number;
    exemptCertId?: number;
    stateFips?: string;
    region?: string;
    idNo?: string;
    country?: string;
    endDate?: string;
    idType?: string;
    isTaxCodeListExclusionList?: number;
    taxCodes?: EcmsDetailTaxCodeModel[];
}

interface EcmsDetailTaxCodeModel {
    exemptCertDetailTaxCodeId?: number;
    exemptCertDetailId?: number;
    taxCodeId?: number;
}

interface UpcModel extends RecordInfo {
    id?: number;
    companyId?: number;
    upc?: string;
    legacyTaxCode?: string;
    description?: string;
    effectiveDate?: string;
    endDate?: string;
    usage?: number;
    isSystem?: number;
}

interface TaxRuleModel extends RecordInfo {
    id?: number;
    companyId?: number;
    taxCodeId?: number;
    taxCode?: string;
    stateFIPS?: string;
    jurisName?: string;
    jurisCode?: string;
    jurisTypeId?: string;
    jurisdictionTypeId?: string;
    customerUsageType?: string;
    entityUseCode?: string;
    taxTypeId?: string;
    taxTypeCode?: string;
    taxRuleProductDetail?: TaxRuleProductDetailModel[];
    rateTypeId?: string;
    rateTypeCode?: string;
    taxRuleTypeId?: string;
    isAllJuris?: boolean;
    value?: number;
    cap?: number;
    threshold?: number;
    options?: string;
    effectiveDate?: string;
    endDate?: string;
    description?: string;
    countyFIPS?: string;
    isSTPro?: boolean;
    country?: string;
    region?: string;
    sourcing?: string;
    taxTypeGroup?: string;
    taxSubType?: string;
    nonPassthroughExpression?: string;
    currencyCode?: string;
    preferredProgramId?: number;
    uomId?: number;
    unitOfBasis?: string;
}

interface TaxRuleProductDetailModel {
    taxRuleProductDetailId?: number;
    taxRuleId?: number;
    productCode?: string;
    effectiveDate?: string;
    endDate?: string;
    systemId?: number;
}

interface NexusModel extends RecordInfo {
    id?: number;
    companyId?: number;
    country?: string;
    region?: string;
    jurisTypeId?: string;
    jurisdictionTypeId?: string;
    jurisCode?: string;
    jurisName?: string;
    effectiveDate?: string;
    endDate?: string;
    shortName?: string;
    signatureCode?: string;
    stateAssignedNo?: string;
    nexusTypeId?: string;
    sourcing?: string;
    hasLocalNexus?: boolean;
    localNexusTypeId?: string;
    hasPermanentEstablishment?: boolean;
    taxId?: string;
    streamlinedSalesTax?: boolean;
    isSSTActive?: boolean;
    taxTypeGroup?: string;
    nexusTaxTypeGroup?: string;
    taxAuthorityId?: number;
    isSellerImporterOfRecord?: boolean;
    taxName?: string;
    parameters?: NexusParameterDetailModel[];
    taxableNexus?: boolean;
}

interface SettingModel {
    id?: number;
    companyId?: number;
    set?: string;
    name?: string;
    value?: string;
    modifiedDate?: string;
    modifiedUserId?: number;
}

interface NexusParameterDetailModel {
    id?: number;
    name?: string;
    value?: string;
    unit?: string;
    nexusId?: number;
}

interface LocationModel extends GeneralAddress, RecordInfo {
    id?: number;
    companyId?: number;
    locationCode?: string;
    description?: string;
    addressTypeId?: string;
    addressCategoryId?: string;
    isMarketplaceOutsideUsa?: boolean;
    county?: string;
    isDefault?: boolean;
    isRegistered?: boolean;
    dbaName?: string;
    outletName?: string;
    effectiveDate?: string;
    endDate?: string;
    lastTransactionDate?: string;
    registeredDate?: string;
    settings?: LocationSettingModel[];
    parameters?: LocationParameterModel[];
}

interface LocationParameterModel {
    id?: number;
    name?: string;
    unit?: string;
    value?: string;
    locationId?: number;
}

interface LocationSettingModel {
    questionId?: number;
    questionName?: string;
    value?: string;
}

interface ItemModel extends RecordInfo {
    id?: number;
    companyId?: number;
    itemCode?: string;
    taxCodeId?: number;
    taxCode?: string;
    description?: string;
    itemGroup?: string;
    category?: string;
    classifications?: ClassificationModel[];
    parameters?: ItemParameterModel[];
    tags?: ItemTagDetailModel[];
}

interface ItemTagDetailModel {
    itemTagDetailId?: number;
    tagId?: number;
    tagName?: string;
    itemId?: number;
    companyId?: number;
    createdDate?: string;
}

interface ItemParameterModel {
    id?: number;
    name?: string;
    value?: string;
    unit?: string;
    itemId?: number;
    isNeededForCalculation?: boolean;
    isNeededForReturns?: boolean;
    isNeededForClassification?: boolean;
}

interface ClassificationModel {
    productCode?: string;
    systemCode?: string;
}

interface ContactModel extends GeneralAddress, RecordInfo {
    id?: number;
    companyId?: number;
    contactCode?: string;
    firstName?: string;
    middleName?: string;
    lastName?: string;
    title?: string;
    email?: string;
    phone?: string;
    mobile?: string;
    fax?: string;
}
