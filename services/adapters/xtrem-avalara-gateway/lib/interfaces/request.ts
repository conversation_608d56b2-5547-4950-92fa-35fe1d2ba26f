/**
 *  the standard call of the api - used by the restCall function
 */
export interface RestCallOptions {
    url: string;
    verb: 'post' | 'get' | 'delete';
    payload: any;
}

export interface AvataxErrorMessage {
    code: string;
    target: string;
    message: string;
    details: {
        code: string;
        number: number;
        message: string;
        description: string;
        faultCode: string;
        helpLink: string;
        severity: string;
    }[];
}

/**
 * This is the standard error message returned by the avatax class
 */
export interface ResponseErrorMessage {
    error: AvataxErrorMessage;
}
