import type { decimal } from '@sage/xtrem-shared';

export interface AvaTaxMessage {
    summary?: string;
    details?: string;
    refersTo?: string;
    severity?: string;
    source?: string;
}

export interface TaxCodeModel extends RecordInfo {
    id?: number;
    companyId?: number;
    taxCode?: string;
    taxCodeTypeId?: string;
    description?: string;
    parentTaxCode?: string;
    isPhysical?: boolean;
    goodsServiceCode?: number;
    entityUseCode?: string;
    isActive?: boolean;
    isSSTCertified?: boolean;
}

export interface GeneralAddress {
    line1?: string;
    line2?: string;
    line3?: string;
    city?: string;
    region?: string;
    country?: string;
    postalCode?: string;
}

export interface CoordinateInfo {
    latitude?: number;
    longitude?: number;
}

export interface RecordInfo {
    createdDate?: string;
    createdUserId?: number;
    modifiedDate?: string;
    modifiedUserId?: number;
}

export interface QueryRequest {
    filter?: string;
    top?: string;
    skip?: string;
    orderBy?: string;
}

export interface QueryResponse {
    '@recordsetCount'?: number;
    '@nextLink'?: string;
    pageKey?: string;
}

export interface ResponsePayloadHeader {
    totalTaxAmountAdjusted: decimal;
    totalExemptAmount: decimal;
    totalDiscountAmount: decimal;
    totalTaxableAmount: decimal;
    totalTaxAmount: decimal;
}
