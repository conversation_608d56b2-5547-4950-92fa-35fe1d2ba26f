import type { Reference, Context } from '@sage/xtrem-core';
import { decorators, Node, NodeStatus, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAvalaraGateway from '../../index';
import * as xtremAvalara from '../index';

@decorators.node<MapCompany>({
    package: 'xtrem-avalara-gateway',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    serviceOptions: () => [xtremAvalara.serviceOptions.avalaraOption],
    indexes: [
        {
            orderBy: {
                avalaraCompany: 1,
            },
            isUnique: true,
        },
    ],
})
export class MapCompany extends Node {
    @decorators.booleanProperty<MapCompany, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.enumProperty<MapCompany, 'entityType'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremAvalara.enums.entityTypeDataType,
        defaultValue: 'company',
    })
    readonly entityType: Promise<xtremAvalara.enums.EntityType>;

    @decorators.referenceProperty<MapCompany, 'company'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Company,
        async control(cx, val) {
            if (this.$.status === NodeStatus.added || (await (await this.$.old).company) !== val) {
                const result = await xtremAvalaraGateway.nodes.MapCompany.validateCompany(
                    this.$.context,
                    await this.entityType,
                    (await this.avalaraConfiguration).currentSessionIds.companies,
                    val,
                );

                if (result.hasErrors) {
                    cx.addDiagnose(ValidationSeverity.error, result.errorMessage!);
                }
                if (val) (await this.avalaraConfiguration).currentSessionIds.companies.push(val._id);
            }
        },
    })
    readonly company: Reference<xtremSystem.nodes.Company | null>;

    @decorators.referenceProperty<MapCompany, 'financialSite'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        filters: {
            control: {
                isFinance: true,
            },
        },
        async control(cx, val) {
            if (this.$.status === NodeStatus.added || (await (await this.$.old).financialSite) !== val) {
                const result = await xtremAvalaraGateway.nodes.MapCompany.validateFinancialSite(
                    this.$.context,
                    await this.entityType,
                    (await this.avalaraConfiguration).currentSessionIds.financialSites,
                    val,
                );
                if (result.hasErrors) {
                    cx.addDiagnose(ValidationSeverity.error, result.errorMessage!);
                }
                if (val) (await this.avalaraConfiguration).currentSessionIds.financialSites.push(val._id);
            }
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<MapCompany, 'avalaraCompany'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremAvalara.nodes.AvalaraCompany,
        async control(cx, val) {
            if (this.$.status === NodeStatus.added || (await (await this.$.old).avalaraCompany) !== val) {
                const result = await xtremAvalaraGateway.nodes.MapCompany.validateAvalaraCompany(
                    this.$.context,
                    val,
                    (await this.avalaraConfiguration).currentSessionIds.avalaraCompanies,
                );

                if (result.hasErrors) {
                    cx.addDiagnose(ValidationSeverity.error, result.errorMessage!);
                }
                if (val) (await this.avalaraConfiguration).currentSessionIds.avalaraCompanies.push(val._id);
            }
        },
    })
    readonly avalaraCompany: Reference<xtremAvalara.nodes.AvalaraCompany>;

    @decorators.referenceProperty<MapCompany, 'avalaraConfiguration'>({
        isStored: true,
        isPublished: true,
        node: () => xtremAvalara.nodes.AvalaraConfiguration,
    })
    readonly avalaraConfiguration: Reference<xtremAvalara.nodes.AvalaraConfiguration>;

    /**
     * This query validate company
     * @param context
     * @param company
     */
    @decorators.query<typeof MapCompany, 'validateCompany'>({
        isPublished: true,
        parameters: [
            { name: 'entityType', type: 'enum', dataType: () => xtremAvalaraGateway.enums.entityTypeDataType },
            { name: 'cachedCompanies', type: 'array', item: 'integer' },
            { name: 'company', type: 'reference', node: () => xtremSystem.nodes.Company },
        ],
        return: {
            type: 'object',
            properties: {
                hasErrors: 'boolean',
                errorMessage: 'string',
                fieldName: 'string',
            },
        },
    })
    static async validateCompany(
        context: Context,
        entityType: xtremAvalaraGateway.enums.EntityType,
        cachedCompanies: number[],
        company?: xtremSystem.nodes.Company | null,
    ): Promise<{ errorMessage?: string; hasErrors: boolean }> {
        if (entityType === 'company') {
            if (!company) {
                return {
                    hasErrors: true,
                    errorMessage: context.localize(
                        '@sage/xtrem-avalara-gateway/nodes__map_company__company_mandatory',
                        'The company is required.',
                    ),
                };
            }

            const companyResult = await context
                .query(xtremAvalaraGateway.nodes.MapCompany, {
                    filter: {
                        company: { _id: company?._id },
                    },
                })
                .toArray();

            if (companyResult.length || cachedCompanies.find(_id => _id === company?._id)) {
                return {
                    hasErrors: true,
                    errorMessage: context.localize(
                        '@sage/xtrem-avalara-gateway/nodes__map_company__company_check_duplicate',
                        'The company already exists.',
                    ),
                };
            }
        }

        if (entityType === 'financialSite' && company) {
            return {
                hasErrors: true,
                errorMessage: context.localize(
                    '@sage/xtrem-avalara-gateway/nodes__map_company__company_must_be_empty',
                    'The entity is a financial site. Leave the company empty.',
                ),
            };
        }

        return { hasErrors: false };
    }

    /**
     * This query validate financialSite
     * @param context
     * @param entityType
     * @param financialSite
     */
    @decorators.query<typeof MapCompany, 'validateFinancialSite'>({
        isPublished: true,
        parameters: [
            { name: 'entityType', type: 'enum', dataType: () => xtremAvalaraGateway.enums.entityTypeDataType },
            { name: 'cachedFinancialSites', type: 'array', item: 'integer' },
            { name: 'financialSite', type: 'reference', node: () => xtremSystem.nodes.Site },
        ],
        return: {
            type: 'object',
            properties: {
                hasErrors: 'boolean',
                errorMessage: 'string',
                fieldName: 'string',
            },
        },
    })
    static async validateFinancialSite(
        context: Context,
        entityType: xtremAvalaraGateway.enums.EntityType,
        cachedFinancialSites: number[],
        financialSite?: xtremSystem.nodes.Site | null,
    ): Promise<{ errorMessage?: string; hasErrors: boolean }> {
        if (entityType === 'company') {
            if (financialSite) {
                return {
                    hasErrors: true,
                    errorMessage: context.localize(
                        '@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_must_be_empty',
                        'The entity is a company. Leave the financial site empty.',
                    ),
                };
            }
        }

        if (entityType === 'financialSite') {
            if (!financialSite) {
                return {
                    hasErrors: true,
                    errorMessage: context.localize(
                        '@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_mandatory',
                        'The financial site is required.',
                    ),
                };
            }

            const financialSiteResult = await context
                .query(xtremAvalaraGateway.nodes.MapCompany, {
                    filter: {
                        financialSite: { _id: financialSite?._id },
                    },
                })
                .toArray();

            if (financialSiteResult.length || cachedFinancialSites.find(_id => _id === financialSite?._id)) {
                return {
                    hasErrors: true,
                    errorMessage: context.localize(
                        '@sage/xtrem-avalara-gateway/nodes__map_company__financial_site_check_duplicate',
                        'The financial site already exists.',
                    ),
                };
            }
        }

        return { hasErrors: false };
    }

    /**
     * This query validate avalaraCompany
     * @param context
     * @param avalaraCompany
     */
    @decorators.query<typeof MapCompany, 'validateAvalaraCompany'>({
        isPublished: true,
        parameters: [
            { name: 'avalaraCompany', type: 'reference', node: () => xtremAvalaraGateway.nodes.AvalaraCompany },
            { name: 'cachedAvalaraCompanies', type: 'array', item: 'integer' },
        ],
        return: {
            type: 'object',
            properties: {
                hasErrors: 'boolean',
                errorMessage: 'string',
                fieldName: 'string',
            },
        },
    })
    static async validateAvalaraCompany(
        context: Context,
        avalaraCompany: xtremAvalaraGateway.nodes.AvalaraCompany,
        cachedAvalaraCompanies: number[],
    ): Promise<{ errorMessage?: string; hasErrors: boolean; fieldName?: string }> {
        const avalaraCompanyResult = await context
            .query(xtremAvalaraGateway.nodes.MapCompany, {
                filter: {
                    avalaraCompany: { _id: avalaraCompany._id },
                },
            })
            .toArray();

        if (avalaraCompanyResult.length || cachedAvalaraCompanies.find(_id => _id === avalaraCompany._id)) {
            return {
                hasErrors: true,
                errorMessage: context.localize(
                    '@sage/xtrem-avalara-gateway/nodes__map_company__avalara_company_check_duplicate',
                    'The avalara company already exists.',
                ),
            };
        }
        return { hasErrors: false };
    }
}
