import type { Context } from '@sage/xtrem-core';
import { asyncArray, decorators, Node, NodeStatus } from '@sage/xtrem-core';
import * as xtremAvalaraGateway from '../index';

@decorators.node<AvalaraResponseListener>({
    package: 'xtrem-avalara-gateway',
})
export class AvalaraResponseListener extends Node {
    @decorators.notificationListener<typeof AvalaraResponseListener>({
        topic: 'avalaraPing/response',
    })
    static avalaraPingResponse(context: Context, payload: xtremAvalaraGateway.interfaces.AvataxTestConnection): void {
        // Let's get the notificationId and the replyTopic
        xtremAvalaraGateway.functions.getListenerInfoFromContext(context, payload);
    }

    @decorators.notificationListener<typeof AvalaraResponseListener>({
        topic: 'avalaraResolveAddress/response',
    })
    static resolveAddressResponse(
        context: Context,
        payload: xtremAvalaraGateway.interfaces.ResolveAddressResponseType,
    ): void {
        // Let's get the notificationId and the replyTopic
        xtremAvalaraGateway.functions.getListenerInfoFromContext(context, payload);
    }

    @decorators.notificationListener<typeof AvalaraResponseListener>({
        topic: 'SalesInvoice/avalaraCreateTransaction/response',
    })
    static createSalesInvoiceTransactionResponse(
        context: Context,
        payload: xtremAvalaraGateway.interfaces.TransactionResponseType,
    ): void {
        // Let's get the notificationId and the replyTopic
        xtremAvalaraGateway.functions.getListenerInfoFromContext(context, payload);
    }

    @decorators.notificationListener<typeof AvalaraResponseListener>({
        topic: 'SalesCreditMemo/avalaraCreateTransaction/response',
    })
    static createSalesCreditMemoTransactionResponse(
        context: Context,
        payload: xtremAvalaraGateway.interfaces.TransactionResponseType,
    ): void {
        // Let's get the notificationId and the replyTopic
        xtremAvalaraGateway.functions.getListenerInfoFromContext(context, payload);
    }

    @decorators.notificationListener<typeof AvalaraResponseListener>({
        topic: 'avalaraListTaxCodes/response',
    })
    static listTaxCodesResponse(
        context: Context,
        payload: xtremAvalaraGateway.interfaces.ListTaxCodesResponseType,
    ): void {
        // Let's get the notificationId and the replyTopic
        xtremAvalaraGateway.functions.getListenerInfoFromContext(context, payload);
    }

    @decorators.notificationListener<typeof AvalaraResponseListener>({
        topic: 'avalaraQueryCompanies/response',
    })
    static async queryCompaniesResponse(
        context: Context,
        payload: xtremAvalaraGateway.interfaces.QueryCompaniesResponseType,
    ): Promise<void> {
        if (payload && payload.value) {
            // 1st pass : update or create from Avalara
            await asyncArray(payload.value)?.forEach(async avalaraCompanyRow => {
                const avalaraCompany =
                    (await context.tryRead(
                        xtremAvalaraGateway.nodes.AvalaraCompany,
                        { id: avalaraCompanyRow.companyCode },
                        { forUpdate: true },
                    )) ||
                    (await context.create(xtremAvalaraGateway.nodes.AvalaraCompany, {
                        id: avalaraCompanyRow.companyCode,
                        name: avalaraCompanyRow.name,
                        isActive: avalaraCompanyRow.isActive,
                    }));
                if (avalaraCompany.$.status === NodeStatus.unchanged) {
                    await avalaraCompany.$.set({
                        name: avalaraCompanyRow.name,
                        isActive: avalaraCompanyRow.isActive,
                    });
                }
                await avalaraCompany.$.save();
            });

            // 2nd pass : deactivate row from SDMO that doesn't exists on Avalara
            const xtremCompaniesToDeactivate = context.query(xtremAvalaraGateway.nodes.AvalaraCompany, {
                forUpdate: true,
                filter: {
                    id: { _nin: payload.value.map(company => company.companyCode || '') },
                },
            });
            await xtremCompaniesToDeactivate.forEach(async xtremCompany => {
                await xtremCompany.$.set({
                    isActive: false,
                });
                await xtremCompany.$.save();
            });
        }

        // Let's get the notificationId and the replyTopic
        xtremAvalaraGateway.functions.getListenerInfoFromContext(context, payload);
    }

    @decorators.notificationListener<typeof AvalaraResponseListener>({
        topic: 'avalaraListEntityUseCodes/response',
    })
    static async listEntityUseCodesResponse(
        context: Context,
        payload: xtremAvalaraGateway.interfaces.ListEntityUseCodesResponseType,
    ): Promise<void> {
        if (payload && payload.value) {
            // 1st pass : update or create from Avalara
            await asyncArray(payload.value).forEach(async entityUseCodeRow => {
                const entityUse =
                    (await context.tryRead(
                        xtremAvalaraGateway.nodes.EntityUse,
                        { id: entityUseCodeRow.code },
                        { forUpdate: true },
                    )) ||
                    (await context.create(xtremAvalaraGateway.nodes.EntityUse, {
                        id: entityUseCodeRow.code,
                        name: entityUseCodeRow.name,
                        description: entityUseCodeRow.description,
                    }));
                if (entityUse.$.status === NodeStatus.unchanged) {
                    await entityUse.$.set({
                        name: entityUseCodeRow.name,
                        description: entityUseCodeRow.description,
                    });
                }
                await entityUse.$.save();
            });

            // 2nd pass : deactivate row from SDMO that doesn't exists on Avalara
            const xtremEntityUseToDeactivate = context.query(xtremAvalaraGateway.nodes.EntityUse, {
                forUpdate: true,
                filter: {
                    id: { _nin: payload.value.map(entityUse => entityUse.code || '') },
                },
            });
            await xtremEntityUseToDeactivate.forEach(async xtremEntityUse => {
                await xtremEntityUse.$.set({
                    isActive: false,
                });
                await xtremEntityUse.$.save();
            });
        }

        // Let's get the notificationId and the replyTopic
        xtremAvalaraGateway.functions.getListenerInfoFromContext(context, payload);
    }
}
