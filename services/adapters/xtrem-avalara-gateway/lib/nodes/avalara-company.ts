import type { Context } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAvalaraGateway from '../../index';

@decorators.node<AvalaraCompany>({
    package: 'xtrem-avalara-gateway',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    serviceOptions: () => [xtremAvalaraGateway.serviceOptions.avalaraOption],
    indexes: [
        {
            orderBy: {
                id: 1,
            },
            isUnique: true,
        },
    ],
})
export class AvalaraCompany extends Node {
    @decorators.booleanProperty<AvalaraCompany, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
        lookupAccess: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<AvalaraCompany, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isRequired: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'hashLimit', // Part of a unique index, hash to avoid collisions
        anonymizeValue: 16,
        dataType: () => xtremSystem.dataTypes.id,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<AvalaraCompany, 'name'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    /**
     * Upload records from Avalara using queryCompanies API
     * @param context
     * @returns string
     */
    @decorators.mutation<typeof AvalaraCompany, 'loadCompanyFromAvalara'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'string',
        },
    })
    static async loadCompanyFromAvalara(context: Context): Promise<string> {
        await context.notify('avalaraQueryCompanies/request', {}, { replyTopic: 'avalaraQueryCompanies/response' });

        return context.localize(
            '@sage/xtrem-avalara-gateway/nodes__avalara_company__load_company_codes_from_avalara_asked',
            'The avalara company codes are being loaded. The process will run in the background.',
        );
    }
}
