import type { Context } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAvalaraGateway from '..';

@decorators.node<EntityUse>({
    package: 'xtrem-avalara-gateway',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    serviceOptions: () => [xtremAvalaraGateway.serviceOptions.avalaraOption],
    indexes: [
        {
            orderBy: {
                id: 1,
            },
            isUnique: true,
        },
    ],
})
export class EntityUse extends Node {
    @decorators.booleanProperty<EntityUse, 'isActive'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: true,
        provides: ['isActive'],
        lookupAccess: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<EntityUse, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.id,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<EntityUse, 'name'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<EntityUse, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
        lookupAccess: true,
    })
    readonly description: Promise<string>;

    /**
     * Send a notification to ask for the avalara list of entity use codes
     * @param context
     * @returns string
     */
    @decorators.mutation<typeof EntityUse, 'loadEntityUseCodesFromAvalara'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'string',
        },
    })
    static async loadEntityUseCodesFromAvalara(context: Context): Promise<string> {
        await context.notify(
            'avalaraListEntityUseCodes/request',
            {},
            { replyTopic: 'avalaraListEntityUseCodes/response' },
        );

        return context.localize(
            '@sage/xtrem-avalara-gateway/nodes__entity_use__load_entity_use_codes_from_avalara_asked',
            'The entity use codes are being loaded. The process will run in the background.',
        );
    }
}
