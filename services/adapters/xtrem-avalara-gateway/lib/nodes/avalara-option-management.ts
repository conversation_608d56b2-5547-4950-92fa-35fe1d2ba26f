import type { Context } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import { avalaraOption } from '../service-options/avalara-option';

@decorators.subNode<AvalaraOptionManagement>({
    extends: () => xtremStructure.nodes.BaseOptionManagement,
    isPublished: true,
})
export class AvalaraOptionManagement extends xtremStructure.nodes.BaseOptionManagement {
    /**
     * TODO : see enhancement request https://jira.sage.com/browse/XT-31348
     * this two query decorator must be in the baseOptionMangement node
     * Find a way to have a static that can be override by the extended node
     */

    @decorators.query<typeof AvalaraOptionManagement, 'isServiceOptionActiveFunction'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'boolean',
        },
    })
    static isServiceOptionActiveFunction(context: Context) {
        return AvalaraOptionManagement.baseIsServiceOptionActiveFunction(context, avalaraOption);
    }

    @decorators.mutation<typeof AvalaraOptionManagement, 'serviceOptionChange'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'boolean',
        },
    })
    static async serviceOptionChange(context: Context): Promise<boolean> {
        const isServiceOptionActive = await AvalaraOptionManagement.baseServiceOptionChange(context, avalaraOption);

        if (!isServiceOptionActive) {
            const companyTaxEngineAvalara = context.query(xtremSystem.nodes.Company, {
                forUpdate: true,
                filter: {
                    taxEngine: { _eq: 'avalaraAvaTax' },
                },
            });

            await companyTaxEngineAvalara.forEach(async xtremCompany => {
                await xtremCompany.$.set({
                    taxEngine: 'genericTaxCalculation',
                });
                await xtremCompany.$.save();
            });
        }

        return isServiceOptionActive;
    }
}
