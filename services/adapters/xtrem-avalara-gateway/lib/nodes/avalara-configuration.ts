import { ConfigManager } from '@sage/xtrem-config';
import type { Collection, Context } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, Node, withRethrow } from '@sage/xtrem-core';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremAvalaraGateway from '../../index';
import * as xtremAvalara from '../index';

@decorators.node<AvalaraConfiguration>({
    package: 'xtrem-avalara-gateway',
    storage: 'sql',
    isPublished: true,
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDelete: false,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
    serviceOptions: () => [xtremAvalaraGateway.serviceOptions.avalaraOption],
})
export class AvalaraConfiguration extends Node {
    currentSessionIds: { companies: number[]; financialSites: number[]; avalaraCompanies: number[] } = {
        companies: [],
        financialSites: [],
        avalaraCompanies: [],
    };

    @decorators.stringProperty<AvalaraConfiguration, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.booleanProperty<AvalaraConfiguration, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
        exportValue: false,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<AvalaraConfiguration, 'endPointUrl'>({
        isPublished: true,
        async computeValue() {
            return (await this.getClient()).baseUrl;
        },
    })
    readonly endPointUrl: Promise<string>;

    @decorators.booleanProperty<AvalaraConfiguration, 'isSandboxMode'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
    })
    readonly isSandboxMode: Promise<boolean>;

    @decorators.booleanProperty<AvalaraConfiguration, 'isCommittedTransaction'>({
        isStored: true,
        isPublished: true,
        async defaultValue() {
            if ((await this.isSandboxMode) === true) {
                return false;
            }
            return true;
        },
    })
    readonly isCommittedTransaction: Promise<boolean>;

    @decorators.stringProperty<AvalaraConfiguration, 'accountId'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
        exportValue: '',
    })
    readonly accountId: Promise<string>;

    @decorators.stringProperty<AvalaraConfiguration, 'licenseKey'>({
        isStored: true,
        isPublished: true,
        isStoredEncrypted: true,
        dataType: () => dataTypes.password,
        exportValue: '',
    })
    readonly licenseKey: Promise<string>;

    @decorators.booleanProperty<AvalaraConfiguration, 'isAddressValidationActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
    })
    readonly isAddressValidationActive: Promise<boolean>;

    @decorators.enumProperty<AvalaraConfiguration, 'addressTextCase'>({
        isStored: true,
        isPublished: true,
        isNullable: false,
        dataType: () => xtremAvalara.enums.addressTextCaseDataType,
        defaultValue: 'upper',
    })
    readonly addressTextCase: Promise<xtremAvalara.enums.AddressTextCase>;

    private _Config: xtremAvalaraGateway.interfaces.AvataxConfig;

    get config(): Promise<xtremAvalaraGateway.interfaces.AvataxConfig> {
        return (async () => {
            if (this._Config) {
                return this._Config;
            }

            this._Config = {
                appName: this.$.context.application.name,
                appVersion: this.$.context.application.version,
                environment: (await this.isSandboxMode) ? 'sandbox' : 'production',
                machineName: ConfigManager.current.clusterId,
            };
            return this._Config;
        })();
    }

    private _Cred: xtremAvalaraGateway.interfaces.AvataxCredentials;

    get cred(): Promise<xtremAvalaraGateway.interfaces.AvataxCredentials> {
        return (async () => {
            if (this._Cred) {
                return this._Cred;
            }

            this._Cred = {
                accountId: await this.accountId,
                licenseKey: await this.$.decryptValue('licenseKey'),
            };
            return this._Cred;
        })();
    }

    /**
     * get the client object by the default values
     *
     */
    async getClient(): Promise<xtremAvalaraGateway.classes.XtremAvatax> {
        return new xtremAvalaraGateway.classes.XtremAvatax(this.$.context, await this.config).withSecurity(
            await this.cred,
        );
    }

    @decorators.mutation<typeof AvalaraConfiguration, 'ping'>({
        isPublished: true,
        parameters: [
            {
                name: 'id',
                type: 'string',
                isMandatory: true,
            },
        ],
        return: {
            type: 'object',
            properties: {
                version: 'string',
                authenticated: 'boolean',
            },
        },
    })
    static async ping(context: Context, id: string): Promise<xtremAvalaraGateway.interfaces.AvataxTestConnection> {
        const avalaraConfiguration = await context.read(AvalaraConfiguration, { id });
        const client = await avalaraConfiguration.getClient();
        return withRethrow(
            async () => {
                const pingResult = await client.ping();
                return {
                    authenticated: pingResult.authenticated,
                    version: pingResult.version,
                };
            },
            error => {
                // TODO: provide a higher level error message
                return new BusinessRuleError(error.message, error);
            },
        );
    }

    /**
     * get the default record
     *
     */
    @decorators.query<typeof AvalaraConfiguration, 'defaultInstance'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'instance',
            node: () => AvalaraConfiguration,
        },
    })
    static async defaultInstance(context: Context): Promise<AvalaraConfiguration | null> {
        const avalaraInstance = await context
            .query(xtremAvalaraGateway.nodes.AvalaraConfiguration, {
                filter: { id: 'AVALARA' },
            })
            .toArray();
        return avalaraInstance.length ? avalaraInstance[0] : null;
    }

    @decorators.query<typeof AvalaraConfiguration, 'getIsCommit'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'boolean',
        },
    })
    static async getIsCommit(context: Context): Promise<boolean> {
        const avalaraInstance = await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context);
        let isCommit = true;
        if (
            avalaraInstance &&
            (await avalaraInstance.isCommittedTransaction) === false &&
            (await avalaraInstance.isSandboxMode) === true
        ) {
            isCommit = false;
        }
        return isCommit;
    }

    /**
     * get the baseUrl depending on the value selected in isSandBoxMode by requesting the client object
     *
     */
    @decorators.query<typeof AvalaraConfiguration, 'getUrl'>({
        isPublished: true,
        parameters: [
            {
                name: 'isSandBox',
                type: 'boolean',
            },
        ],
        return: {
            type: 'string',
        },
    })
    static getUrl(context: Context, isSandBox: boolean): string {
        return new xtremAvalaraGateway.classes.XtremAvatax(context, {
            appName: '',
            appVersion: '',
            machineName: '',
            environment: isSandBox ? 'sandbox' : 'production',
        }).baseUrl;
    }

    @decorators.collectionProperty<AvalaraConfiguration, 'mapCompanyLines'>({
        isPublished: true,
        // isVital: true,
        reverseReference: 'avalaraConfiguration',
        node: () => xtremAvalaraGateway.nodes.MapCompany,
    })
    readonly mapCompanyLines: Collection<xtremAvalaraGateway.nodes.MapCompany>;
}
