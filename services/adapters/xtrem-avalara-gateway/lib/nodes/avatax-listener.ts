import type { Context, StaticThis } from '@sage/xtrem-core';
import { asyncArray, decorators, Node, NodeStatus, SystemError } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';
import * as _ from 'lodash';
import * as xtremAvalaraGateway from '../index';

@decorators.node<AvataxListener>({
    package: 'xtrem-avalara-gateway',
})
export class AvataxListener extends Node {
    @decorators.notificationListener<typeof AvataxListener>({
        topic: 'avalaraPing/request',
    })
    // eslint-disable-next-line consistent-return
    static async ping(
        context: Context,
        requestPayload: {},
    ): Promise<{ replyId: string; responsePayload: xtremAvalaraGateway.interfaces.AvataxTestConnection } | undefined> {
        const listener = 'AvataxListener.ping';

        // Let's get the notificationId and the replyTopic
        const { topic, replyTopic, notificationId } = xtremAvalaraGateway.functions.getListenerInfoFromContext(
            context,
            requestPayload,
        );

        await xtremAvalaraGateway.functions.createNotificationHistoryOnSentEvent(
            context,
            { notificationId, topic, replyTopic, listener },
            requestPayload,
        );

        // First, we acknowledge receiving the notification
        const responsePayload: xtremAvalaraGateway.interfaces.AvataxTestConnection = await (await this.getClient(
            context,
            topic,
        ))!.ping();

        const replyId = await xtremAvalaraGateway.functions.getListenerReplyInfoFromContext(
            context,
            topic,
            replyTopic,
            requestPayload,
            responsePayload,
        );

        await xtremAvalaraGateway.functions.createNotificationHistoryOnErrorOrSuccessEvent(
            context,
            { notificationId, topic, replyId, replyTopic, listener },
            requestPayload,
            responsePayload,
        );

        if (context.testMode) {
            return { replyId, responsePayload };
        }
    }

    @decorators.notificationListener<typeof AvataxListener>({
        topic: 'avalaraResolveAddress/request',
    })
    // eslint-disable-next-line consistent-return
    static async resolveAddress(
        context: Context,
        requestPayload: xtremAvalaraGateway.interfaces.ResolveAddressRequest,
    ): Promise<
        { replyId: string; responsePayload: xtremAvalaraGateway.interfaces.ResolveAddressResponseType } | undefined
    > {
        const listener = 'AvataxListener.resolveAddress';

        // Let's get the notificationId and the replyTopic
        const { notificationId, topic, replyTopic } = xtremAvalaraGateway.functions.getListenerInfoFromContext(
            context,
            requestPayload,
        );

        await xtremAvalaraGateway.functions.createNotificationHistoryOnSentEvent(
            context,
            { notificationId, topic, replyTopic, listener },
            requestPayload,
        );

        // First, we acknowledge receiving the notification
        const responsePayload: xtremAvalaraGateway.interfaces.ResolveAddressResponseType = await (await this.getClient(
            context,
            topic,
        ))!.resolveAddress(requestPayload);

        const replyId = await xtremAvalaraGateway.functions.getListenerReplyInfoFromContext(
            context,
            topic,
            replyTopic,
            requestPayload,
            responsePayload,
        );

        await xtremAvalaraGateway.functions.createNotificationHistoryOnErrorOrSuccessEvent(
            context,
            { notificationId, topic, replyId, replyTopic, listener },
            requestPayload,
            responsePayload,
        );

        if (context.testMode) {
            return { replyId, responsePayload };
        }
    }

    @decorators.notificationListener<typeof AvataxListener>({
        topic: 'avalaraCreateTransaction/request',
    })
    static async createTransaction(
        context: Context,
        payload: xtremAvalaraGateway.interfaces.DocumentsToCheck,
    ): Promise<{ replyId: string; responsePayload: xtremAvalaraGateway.interfaces.TransactionResponseType } | null> {
        const listener = 'AvataxListener.createTransaction';

        // Let's get the notificationId and the replyTopic
        const { notificationId, topic, replyTopic } = xtremAvalaraGateway.functions.getListenerInfoFromContext(
            context,
            payload,
        );

        let node: StaticThis<xtremAvalaraGateway.interfaces.TransactionDocumentType>;
        if (payload.documentsType === 'SalesCreditMemo') {
            node = xtremSales.nodes.SalesCreditMemo;
        } else {
            node = xtremSales.nodes.SalesInvoice;
        }
        const representativeDocument = await context.read(node, { _id: payload.documents[0] });

        const transactionPayloadManager = await xtremAvalaraGateway.classes.TransactionPayloadManager.create(
            representativeDocument.$.context,
            payload.documentsType === 'SalesCreditMemo'
                ? await representativeDocument.site
                : await representativeDocument.financialSite,
        );

        let responsePayload: xtremAvalaraGateway.interfaces.TransactionResponseType = {};

        await asyncArray(payload.documents).forEach(async _id => {
            const document = await context.read(node, { _id });
            const requestPayload = await transactionPayloadManager.prepareRequestPayload(
                document,
                payload.isCommit,
                payload.isPosting,
            );

            const documentNumber = requestPayload.model.code;

            await xtremAvalaraGateway.functions.createNotificationHistoryOnSentEvent(
                context,
                { notificationId, topic, replyTopic, listener },
                requestPayload.model,
                {
                    documentId: _id,
                    documentNumber,
                    isPosting: payload.isPosting,
                    isCommit: payload.isCommit,
                    documentType: payload.documentsType,
                },
            );

            // First, we acknowledge receiving the notification
            const client = await this.getClient(context, topic);
            if (client) {
                responsePayload = await client.createTransaction(requestPayload);
            }
            // if ((responsePayload as any).errors || (responsePayload as any).error) {
            //     responsePayload = { ...responsePayload, code: requestPayload.model.code };
            // }

            if (responsePayload.error) {
                responsePayload = { ...responsePayload, code: requestPayload.model.code };
            }

            await transactionPayloadManager
                .setResponsePayload(responsePayload)
                .processResponsePayload(payload.documentsType);

            const replyId = await xtremAvalaraGateway.functions.getListenerReplyInfoFromContext(
                context,
                topic,
                replyTopic,
                requestPayload.model,
                responsePayload,
            );

            await xtremAvalaraGateway.functions.createNotificationHistoryOnErrorOrSuccessEvent(
                context,
                { notificationId, topic, replyId, replyTopic, listener },
                requestPayload,
                responsePayload,
                {
                    documentId: _id,
                    documentNumber,
                    isPosting: payload.isPosting,
                    isCommit: payload.isCommit,
                    documentType: payload.documentsType,
                },
            );
        });

        if (context.testMode) {
            const replyId = await context.reply<xtremAvalaraGateway.interfaces.TransactionResponseType>(
                replyTopic,
                responsePayload,
            );
            return { replyId, responsePayload };
        }
        return null;
    }

    @decorators.notificationListener<typeof AvataxListener>({
        topic: 'avalaraListTaxCodes/request',
    })
    // eslint-disable-next-line consistent-return
    static async listTaxCodes(
        context: Context,
        requestPayload: xtremAvalaraGateway.interfaces.QueryRequest,
    ): Promise<
        { replyId: string; responsePayload: xtremAvalaraGateway.interfaces.ListTaxCodesResponseType } | undefined
    > {
        const listener = 'AvataxListener.listTaxCodes';

        // Let's get the notificationId and the replyTopic
        const { notificationId, topic, replyTopic } = xtremAvalaraGateway.functions.getListenerInfoFromContext(
            context,
            requestPayload,
        );

        await xtremAvalaraGateway.functions.createNotificationHistoryOnSentEvent(
            context,
            { notificationId, topic, replyTopic, listener },
            requestPayload,
        );

        // First, we acknowledge receiving the notification
        const responsePayload: xtremAvalaraGateway.interfaces.ListTaxCodesResponseType = await (await this.getClient(
            context,
            topic,
        ))!.listTaxCodes(requestPayload);

        // TODO: This block should be on the avatax-response-listener node
        // To process the response from avalara
        // there is actually a bug with the large response from avalara that exceed a limit
        // when all the taxes are requested
        let recordCount = 0;
        if (responsePayload && responsePayload.value) {
            await asyncArray(responsePayload.value).forEach(async taxCodeRow => {
                recordCount += 1;
                const itemTax =
                    (await context.tryRead(
                        xtremAvalaraGateway.nodes.AvalaraItemTax,
                        { id: taxCodeRow.taxCode },
                        { forUpdate: true },
                    )) ||
                    (await context.create(xtremAvalaraGateway.nodes.AvalaraItemTax, {
                        id: taxCodeRow.taxCode,
                        description: taxCodeRow.description,
                        isActive: taxCodeRow.isActive,
                    }));
                if (itemTax.$.status === NodeStatus.unchanged) {
                    await itemTax.$.set({ description: taxCodeRow.description, isActive: taxCodeRow.isActive });
                }
                await itemTax.$.save();
            });
        }

        const responsePayloadCopy = _.cloneDeep(responsePayload);
        delete responsePayload.value; // without this payload exceeds allowed size
        // End of previous TODO

        const replyId = await xtremAvalaraGateway.functions.getListenerReplyInfoFromContext(
            context,
            topic,
            replyTopic,
            requestPayload,
            responsePayload,
        );

        await xtremAvalaraGateway.functions.createNotificationHistoryOnErrorOrSuccessEvent(
            context,
            { notificationId, topic, replyId, replyTopic, listener },
            requestPayload,
            responsePayload,
            { recordCount },
        );

        if (context.testMode) {
            return { replyId, responsePayload: responsePayloadCopy };
        }
    }

    @decorators.notificationListener<typeof AvataxListener>({
        topic: 'avalaraQueryCompanies/request',
    })
    // eslint-disable-next-line consistent-return
    static async queryCompanies(
        context: Context,
        requestPayload: xtremAvalaraGateway.interfaces.QueryCompaniesRequest,
    ): Promise<
        { replyId: string; responsePayload: xtremAvalaraGateway.interfaces.QueryCompaniesResponseType } | undefined
    > {
        const listener = 'AvataxListener.queryCompanies';

        // Let's get the notificationId and the replyTopic
        const { notificationId, topic, replyTopic } = xtremAvalaraGateway.functions.getListenerInfoFromContext(
            context,
            requestPayload,
        );

        await xtremAvalaraGateway.functions.createNotificationHistoryOnSentEvent(
            context,
            { notificationId, topic, replyTopic, listener },
            requestPayload,
        );

        // First, we acknowledge receiving the notification
        const responsePayload: xtremAvalaraGateway.interfaces.QueryCompaniesResponseType = await (await this.getClient(
            context,
            topic,
        ))!.queryCompanies(requestPayload);

        let recordCount = 0;
        if (responsePayload !== undefined && responsePayload.value) {
            recordCount = responsePayload.value.length || 0;
        }

        const replyId = await xtremAvalaraGateway.functions.getListenerReplyInfoFromContext(
            context,
            topic,
            replyTopic,
            requestPayload,
            responsePayload,
        );

        await xtremAvalaraGateway.functions.createNotificationHistoryOnErrorOrSuccessEvent(
            context,
            { notificationId, topic, replyId, replyTopic, listener },
            requestPayload,
            responsePayload,
            { recordCount },
        );

        if (context.testMode) {
            return { replyId, responsePayload };
        }
    }

    @decorators.notificationListener<typeof AvataxListener>({
        topic: 'avalaraListEntityUseCodes/request',
    })
    // eslint-disable-next-line consistent-return
    static async listEntityUseCodes(
        context: Context,
        requestPayload: xtremAvalaraGateway.interfaces.QueryRequest,
    ): Promise<
        { replyId: string; responsePayload: xtremAvalaraGateway.interfaces.ListEntityUseCodesResponseType } | undefined
    > {
        const listener = 'AvataxListener.listEntityUseCodes';

        // Let's get the notificationId and the replyTopic
        const { notificationId, topic, replyTopic } = xtremAvalaraGateway.functions.getListenerInfoFromContext(
            context,
            requestPayload,
        );

        await xtremAvalaraGateway.functions.createNotificationHistoryOnSentEvent(
            context,
            { notificationId, topic, replyTopic, listener },
            requestPayload,
        );

        // First, we acknowledge receiving the notification
        let responsePayload: xtremAvalaraGateway.interfaces.ListEntityUseCodesResponseType = {};
        const client = await this.getClient(context, topic);
        if (client) {
            responsePayload = await client.listEntityUseCodes(requestPayload);
        }
        let recordCount = 0;
        if (responsePayload !== undefined && responsePayload.value) {
            recordCount = responsePayload.value.length || 0;
        }

        const replyId = await xtremAvalaraGateway.functions.getListenerReplyInfoFromContext(
            context,
            topic,
            replyTopic,
            requestPayload,
            responsePayload,
        );

        await xtremAvalaraGateway.functions.createNotificationHistoryOnErrorOrSuccessEvent(
            context,
            { notificationId, topic, replyId, replyTopic, listener },
            requestPayload,
            responsePayload,
            { recordCount },
        );

        if (context.testMode) {
            return { replyId, responsePayload };
        }
    }

    static async getClient(context: Context, topic: string): Promise<xtremAvalaraGateway.classes.XtremAvatax> {
        const defaultAvalaraInstance = await xtremAvalaraGateway.nodes.AvalaraConfiguration.defaultInstance(context);
        if (defaultAvalaraInstance) {
            return defaultAvalaraInstance.getClient();
        }
        await context.reply<xtremAvalaraGateway.interfaces.ResponseErrorMessage>(topic, {
            error: {
                code: '',
                message: 'Could not create instance get the default instance for avalara configuration.',
                details: [],
                target: '',
            },
        });
        throw new SystemError('Could not create instance get the default instance for avalara configuration.');
    }
}
