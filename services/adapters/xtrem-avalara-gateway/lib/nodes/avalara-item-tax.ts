import type { Context } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAvalaraGateway from '../../index';

@decorators.node<AvalaraItemTax>({
    package: 'xtrem-avalara-gateway',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    serviceOptions: () => [xtremAvalaraGateway.serviceOptions.avalaraOption],
    indexes: [
        {
            orderBy: {
                id: 1,
            },
            isUnique: true,
        },
    ],
})
export class AvalaraItemTax extends Node {
    @decorators.booleanProperty<AvalaraItemTax, 'isActive'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<AvalaraItemTax, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.id,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<AvalaraItemTax, 'description'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.mutation<typeof AvalaraItemTax, 'loadTaxCodesFromAvalara'>({
        isPublished: true,
        parameters: [
            {
                name: 'isActive',
                type: 'boolean',
                isMandatory: false,
                isNullable: true,
            },
            {
                name: 'fromTaxCode',
                type: 'string',
                isMandatory: false,
                isNullable: true,
            },
            {
                name: 'toTaxCode',
                type: 'string',
                isMandatory: false,
                isNullable: true,
            },
            {
                name: 'description',
                type: 'string',
                isMandatory: false,
                isNullable: true,
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async loadTaxCodesFromAvalara(
        context: Context,
        isActive: boolean | null,
        fromTaxCode: string | null,
        toTaxCode: string | null,
        description: string | null,
    ): Promise<string> {
        /** Filter for list tax code avalara request  */
        let filter = isActive ? 'isActive eq true' : `isActive eq false`;

        if (fromTaxCode && fromTaxCode !== '') {
            filter = `${filter} and taxCode ge "${fromTaxCode}"`;
        }
        if (toTaxCode && toTaxCode !== '') {
            filter = `${filter} and taxCode le "${[toTaxCode, 'ZZZZ'].join('')}"`;
        }
        if (description && description !== '') {
            filter = `${filter} and description contains "${description}"`;
        }
        const topic = 'avalaraListTaxCodes/request';
        const replyTopic = 'avalaraListTaxCodes/response';
        await context.notify(topic, { filter }, { replyTopic });

        return context.localize(
            '@sage/xtrem-avalara-gateway/nodes__avalara_item_tax__load_tax_codes_from_avalara_asked',
            'The tax codes are being loaded. The process will run in the background.',
        );
    }
}
