import type { EntityUse, GraphApi } from '@sage/xtrem-avalara-gateway-api';
import type { SalesOrderLine } from '@sage/xtrem-sales-api-partial';
import type { SalesOrder } from '@sage/xtrem-sales/build/lib/pages/sales-order';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<SalesOrderExtension>({
    extends: '@sage/xtrem-sales/SalesOrder',
    navigationPanel: {
        listItem: {
            line30: ui.nestedFieldExtensions.reference({
                title: 'Entity use code',
                bind: 'entityUse',
                node: '@sage/xtrem-avalara-gateway/EntityUse',
                valueField: 'name',
                insertBefore: 'deliveryMode',
                isHiddenOnMainField: true,
            }),
        },
    },
    onLoad() {
        if (this.$.recordId && this.status.value === 'closed') {
            this.entityUse.isDisabled = true;
        } else {
            this.entityUse.isDisabled = false;
        }
        this.entityUse.isHidden = !this.$.isServiceOptionEnabled('avalaraOption');
    },
})
export class SalesOrderExtension extends ui.PageExtension<SalesOrder, GraphApi> {
    @ui.decorators.referenceField<SalesOrderExtension, EntityUse>({
        title: 'Entity use code',
        parent() {
            return this.shippingSectionShippingBlock;
        },
        insertBefore() {
            return this.deliveryMode;
        },
        node: '@sage/xtrem-avalara-gateway/EntityUse',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
        ],
    })
    entityUse: ui.fields.Reference;

    @ui.decorators.tableFieldOverride<SalesOrderExtension, SalesOrderLine>({
        columns: [
            ui.nestedFieldExtensions.reference({
                title: 'Entity use code',
                bind: 'entityUse',
                node: '@sage/xtrem-avalara-gateway/EntityUse',
                insertBefore: 'deliveryMode',
                isReadOnly: true,
                isHiddenOnMainField: true,
                valueField: 'name',
                helperTextField: 'id',
                width: 'large',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                ],
            }),
        ],
    })
    lines: ui.fields.Table;

    async fetchDefaultsFromShipToAddressExtension() {
        await this.$.fetchDefaults(['entityUse']);
    }

    addValuesFromAvalaraExtension(values: any) {
        values.entityUse = this.entityUse.value;
    }
}
declare module '@sage/xtrem-sales/build/lib/pages/sales-order' {
    export interface SalesOrder extends SalesOrderExtension {}
}
