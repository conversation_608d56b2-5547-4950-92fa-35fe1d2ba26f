import type { Graph<PERSON><PERSON> } from '@sage/xtrem-avalara-gateway-api';
import type { TaxPanel } from '@sage/xtrem-tax/build/lib/pages/tax-panel';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<TaxPanelExtension>({
    extends: '@sage/xtrem-tax/TaxPanel',
})
export class TaxPanelExtension extends ui.PageExtension<TaxPanel, GraphApi> {
    isGenericTaxCalculationEnabled: boolean;

    @ui.decorators.tableFieldOverride<TaxPanelExtension>({
        columns: [
            ui.nestedFieldExtensions.text({
                bind: 'jurisdictionName',
                title: 'Jurisdiction',
                insertBefore: 'taxableAmount',
                isReadOnly: true,
                isHidden() {
                    return this.isGenericTaxCalculationEnabled;
                },
            }),
        ],
    })
    taxDetails: ui.fields.Table;
}

declare module '@sage/xtrem-tax/build/lib/pages/tax-panel' {
    export interface TaxPanel extends TaxPanelExtension {}
}
