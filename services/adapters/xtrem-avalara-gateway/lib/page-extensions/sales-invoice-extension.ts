import type { Graph<PERSON><PERSON> } from '@sage/xtrem-avalara-gateway-api';
import type { SalesInvoiceLine } from '@sage/xtrem-sales-api';
import type { SalesInvoice } from '@sage/xtrem-sales/build/lib/pages/sales-invoice';
import * as ui from '@sage/xtrem-ui';
import * as displayButtons from '../client-functions/display-buttons-sales-invoice-extension';
import { getAvataxNotificationMessageOnDocument } from '../client-functions/page-functions';

@ui.decorators.pageExtension<SalesInvoiceExtension>({
    extends: '@sage/xtrem-sales/SalesInvoice',
})
export class SalesInvoiceExtension extends ui.PageExtension<SalesInvoice, GraphApi> {
    taxEngine: any;

    @ui.decorators.section<SalesInvoiceExtension>({
        title: 'Avalara notification',
        isHidden: true,
    })
    avalaraNotificationSection: ui.containers.Section;

    @ui.decorators.block<SalesInvoiceExtension>({
        parent() {
            return this.avalaraNotificationSection;
        },
        width: 'large',
        title: 'Information',
    })
    avalaraNotificationBlock: ui.containers.Block;

    @ui.decorators.textField<SalesInvoiceExtension>({
        parent() {
            return this.avalaraNotificationBlock;
        },
        isTransient: true,
        title: 'Message',
        bind: 'avalaraMessage',
    })
    avalaraMessage: ui.fields.Text;

    @ui.decorators.labelFieldOverride<SalesInvoiceExtension>({
        async onClick() {
            await this.getAvalaraNotification('SalesInvoice', this._id.value);
        },
    })
    taxCalculationStatus: ui.fields.Label;

    @ui.decorators.tableFieldOverride<SalesInvoiceExtension>({
        columns: [
            ui.nestedFieldExtensions.text({
                bind: 'jurisdictionName',
                title: 'Jurisdiction',
                insertBefore: 'taxableAmount',
                isReadOnly: true,
                isHidden() {
                    return this.taxEngine.value !== 'avalaraAvaTax';
                },
            }),
        ],
    })
    taxDetails: ui.fields.Table;

    @ui.decorators.tableFieldOverride<SalesInvoiceExtension, SalesInvoiceLine>({
        columns: [
            ui.nestedFieldExtensions.reference({
                title: 'Entity use code',
                bind: 'entityUse',
                node: '@sage/xtrem-avalara-gateway/EntityUse',
                isHiddenOnMainField: true,
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                ],
            }),
        ],
    })
    lines: ui.fields.Table;

    @ui.decorators.pageActionOverride<SalesInvoiceExtension>({
        title: 'Calculate tax',
        async onClick() {
            this.$.loader.isHidden = false;
            const result = await this.$.graph
                .node('@sage/xtrem-sales/SalesInvoice')
                .mutations.calculateTax(true, { invoice: this.$.recordId ?? '' })
                .execute();
            this.$.showToast(result, { type: 'success' });
            await this.$.router.refresh(true);
            this.$.loader.isHidden = true;
        },
    })
    avalara: ui.PageAction;

    callPostMutation() {
        return this.$.graph
            .node('@sage/xtrem-sales/SalesInvoice')
            .mutations.postAvalara({ status: true }, { invoice: this.$.recordId ?? '' })
            .execute();
    }

    async getAvalaraNotification(documentType: string, documentId: string | null) {
        if (documentId && this.taxCalculationStatus.value === 'failed' && this.taxEngine.value === 'avalaraAvaTax') {
            await this.$.dialog.message(
                'info',
                ui.localize('@sage/xtrem-avalara-gateway/avalara_notification_dialog_title', 'Avalara notification'),
                await getAvataxNotificationMessageOnDocument(this, documentType, documentId),
                {},
            );
        }
    }

    manageDisplayButtonAvalaraAction(isDirty: boolean, status: string) {
        this.avalara.isHidden = displayButtons.isHiddenButtonAvalaraAction({
            parameters: {
                status,
                taxCalculationStatus: this.taxCalculationStatus.value,
                taxEngine: this.taxEngine.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }
}

declare module '@sage/xtrem-sales/build/lib/pages/sales-invoice' {
    export interface SalesInvoice extends SalesInvoiceExtension {}
}
