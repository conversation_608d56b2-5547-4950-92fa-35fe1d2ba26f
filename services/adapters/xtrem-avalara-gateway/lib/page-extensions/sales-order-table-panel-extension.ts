import type { Graph<PERSON><PERSON> } from '@sage/xtrem-avalara-gateway-api';
import type { Dict, Filter, integer } from '@sage/xtrem-client';
import type { SalesOrderLine } from '@sage/xtrem-sales-api';
import type { SalesOrderTablePanel } from '@sage/xtrem-sales/build/lib/pages/sales-order-table-panel';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<SalesOrderTablePanelExtension>({
    extends: '@sage/xtrem-sales/SalesOrderTablePanel',
})
export class SalesOrderTablePanelExtension extends ui.PageExtension<SalesOrderTablePanel, GraphApi> {
    entityUse: any;

    salesOrderLineQueryFields: Dict<any> = {};

    @ui.decorators.tableFieldOverride<SalesOrderTablePanelExtension>({
        columns: [
            ui.nestedFieldExtensions.reference({
                title: 'Entity use',
                bind: 'entityUse',
                node: '@sage/xtrem-avalara-gateway/EntityUse',
                isReadOnly: true,
                valueField: 'name',
                helperTextField: 'id',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                ],
            }),
        ],
    })
    salesOrderLines: ui.fields.Table;

    getLoadFilterAvalaraExtension(
        reqFilter: Filter<SalesOrderLine>,
        shipmentLinesLength: integer,
    ): Filter<SalesOrderLine> {
        if (shipmentLinesLength > 0 || this.salesOrderLines.selectedRecords.length > 0) {
            if (this.entityUse) {
                reqFilter.entityUse = { _id: this.entityUse._id };
            } else {
                reqFilter.entityUse = undefined;
            }
        }
        return reqFilter;
    }

    updateFieldValuesAvalaraExtension(rowItem: SalesOrderLine) {
        this.entityUse = rowItem.entityUse;
    }

    onLoadAvalaraExtension() {
        this.salesOrderLineQueryFields.entityUse = { name: true, id: true, description: true, _id: true };
        this.entityUse = this.$.queryParameters.entityUse;
    }
}

declare module '@sage/xtrem-sales/build/lib/pages/sales-order-table-panel' {
    export interface SalesOrderTablePanel extends SalesOrderTablePanelExtension {}
}
