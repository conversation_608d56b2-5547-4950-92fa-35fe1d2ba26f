import type { EntityUse, GraphApi } from '@sage/xtrem-avalara-gateway-api';
import type { Dict } from '@sage/xtrem-client';
import type { SalesShipment } from '@sage/xtrem-sales/build/lib/pages/sales-shipment';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<SalesShipmentExtension>({
    extends: '@sage/xtrem-sales/SalesShipment',
    navigationPanel: {
        listItem: {
            line11: ui.nestedFieldExtensions.reference({
                title: 'Entity use code',
                bind: 'entityUse',
                node: '@sage/xtrem-avalara-gateway/EntityUse',
                valueField: 'name',
                insertBefore: 'deliveryMode',
                isHiddenOnMainField: true,
            }),
        },
    },
    onLoad() {
        this.entityUse.isReadOnly = (this.$.recordId && this.status.value === 'shipped') || false;
        this.salesOrderLineQueryFields = {
            ...this.salesOrderLineQueryFields,
            entityUse: { name: true, id: true, description: true, _id: true },
        };
        this.entityUse.isHidden = !this.$.isServiceOptionEnabled('avalaraOption');
    },
})
export class SalesShipmentExtension extends ui.PageExtension<SalesShipment, GraphApi> {
    @ui.decorators.referenceField<SalesShipmentExtension, EntityUse>({
        title: 'Entity use code',
        parent() {
            return this.shippingBlock;
        },
        insertBefore() {
            return this.deliveryMode;
        },
        node: '@sage/xtrem-avalara-gateway/EntityUse',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
        ],
    })
    entityUse: ui.fields.Reference;

    async fetchDefaultsFromShipToAddressExtension() {
        await this.$.fetchDefaults(['entityUse']);
    }

    addFieldsFromAvalaraExtension(): Dict<any> {
        return { entityUse: this.entityUse.value };
    }

    assignValuesToHeaderPropertiesAvalaraExtension(value: any) {
        this.entityUse.value = value.entityUse;
    }
}
declare module '@sage/xtrem-sales/build/lib/pages/sales-shipment' {
    export interface SalesShipment extends SalesShipmentExtension {}
}
