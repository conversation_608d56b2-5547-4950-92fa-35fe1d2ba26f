import type { Graph<PERSON><PERSON> } from '@sage/xtrem-avalara-gateway-api';
import type { BusinessEntityAddress } from '@sage/xtrem-master-data-api';
import type { Customer as CustomerPage } from '@sage/xtrem-master-data/build/lib/pages/customer';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<CustomerExtension>({
    extends: '@sage/xtrem-master-data/Customer',
})
export class CustomerExtension extends ui.PageExtension<CustomerPage, GraphApi> {
    @ui.decorators.podCollectionFieldOverride<CustomerExtension, BusinessEntityAddress>({
        columnOverrides: [
            ui.nestedFieldExtensions.reference<CustomerExtension>({
                title: 'Entity use code',
                bind: { deliveryDetail: { entityUse: true } },
                node: '@sage/xtrem-avalara-gateway/EntityUse',
                isReadOnly: true,
                valueField: 'name',
                helperTextField: 'id',
                isHidden: (_val, rowValue) => !rowValue?.isShippingAddress,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                ],
            }),
        ],
    })
    addresses: ui.fields.PodCollection<BusinessEntityAddress>;
}

declare module '@sage/xtrem-master-data/build/lib/pages/customer' {
    interface Customer extends CustomerExtension {}
}
