import type { Graph<PERSON><PERSON> } from '@sage/xtrem-avalara-gateway-api';
import type { Filter } from '@sage/xtrem-client';
import { aggregateEdgesSelector, withoutEdges } from '@sage/xtrem-client';
import type { OptionManagementBase as OptionManagementBaseNode } from '@sage/xtrem-structure/build/lib/pages/option-management-base';
import type { Company } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { confirmDialog } from '../client-functions/page-functions';

@ui.decorators.pageExtension<OptionManagementBaseExtension>({
    extends: '@sage/xtrem-structure/OptionManagementBase',
    async onLoad() {
        await this.checkAvalara();
        this.numberOfSaveAfter += 1;
    },
})
export class OptionManagementBaseExtension extends ui.PageExtension<OptionManagementBaseNode, GraphApi> {
    isAvalaraServiceOptionActiveBoolean: boolean;

    @ui.decorators.block<OptionManagementBaseExtension>({
        parent() {
            return this.taxSection;
        },
        title: 'Avalara',
        width: 'small',
    })
    avalaraBlock: ui.containers.Block;

    @ui.decorators.switchField<OptionManagementBaseExtension>({
        isTransient: true,
        parent() {
            return this.avalaraBlock;
        },
        title: 'Active',
        async onChange() {
            await this.checkLinkedCompanies();
        },
    })
    isAvalaraServiceOptionActive: ui.fields.Switch;

    @ui.decorators.linkField<OptionManagementBaseExtension>({
        isTransient: true,
        parent() {
            return this.avalaraBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-avalara-gateway/configuration-page', 'Configuration page');
        },
        page: '@sage/xtrem-avalara-gateway/AvalaraConfiguration',
        title: 'Avalara configuration page',
        isTitleHidden: true,
    })
    avalaraConfigurationPage: ui.fields.Link;

    async checkAvalara() {
        this.isAvalaraServiceOptionActiveBoolean =
            (await this.$.graph
                .node('@sage/xtrem-avalara-gateway/AvalaraOptionManagement')
                .queries.isServiceOptionActiveFunction(true, false)
                .execute()) || false;
        this.isAvalaraServiceOptionActive.value = this.isAvalaraServiceOptionActiveBoolean;
        this.avalaraConfigurationPage.isHidden = !this.isAvalaraServiceOptionActiveBoolean;
    }

    async avalaraSaveOverload() {
        if (this.isAvalaraServiceOptionActive.value !== this.isAvalaraServiceOptionActiveBoolean) {
            await this.$.graph
                .node('@sage/xtrem-avalara-gateway/AvalaraOptionManagement')
                .mutations.serviceOptionChange(true, true)
                .execute();
        }
    }

    @ui.decorators.pageActionOverride<OptionManagementBaseExtension>({
        async onClickAfter() {
            await this.avalaraSaveOverload();
            await this.checkAvalara();
            await this.hardRefresh();
        },
    })
    save: ui.PageAction;

    async checkLinkedCompanies(): Promise<void> {
        if (!this.isAvalaraServiceOptionActive.value) {
            const filter: Filter<Company> = { taxEngine: { _eq: 'avalaraAvaTax' } };
            const companies = withoutEdges<{
                group: { taxEngine: 'string' };
                values: { id: { distinctCount: number } };
            }>(
                await this.$.graph
                    .node('@sage/xtrem-system/Company')
                    .aggregate.query(
                        aggregateEdgesSelector<Company>(
                            {
                                group: { taxEngine: { _by: 'value' } },
                                values: { id: { distinctCount: true } },
                            },
                            { filter },
                        ),
                    )
                    .execute(),
            );
            const countAvataxCompany = (companies.length && companies[0].values.id.distinctCount) || 0;
            if (
                countAvataxCompany &&
                !(await confirmDialog(this, {
                    title: ui.localize(
                        '@sage/xtrem-avalara-gateway/pages__delete_avalara_link_company_dialog_title',
                        'Confirm tax management',
                    ),
                    message: ui.localize(
                        '@sage/xtrem-avalara-gateway/pages__delete_avalara_link_company_dialog_content',
                        'Companies linked to Avalara tax calculation package: {{noCompanies}}. If you continue, they will be changed to the generic tax calculation."',
                        { noCompanies: countAvataxCompany },
                    ),
                }))
            ) {
                this.isAvalaraServiceOptionActive.value = true;
            }
        }
    }
}
declare module '@sage/xtrem-structure/build/lib/pages/option-management-base' {
    interface OptionManagementBase extends OptionManagementBaseExtension {}
}
