import type { GraphApi } from '@sage/xtrem-avalara-gateway-api';
import type { AccountsReceivableInvoice } from '@sage/xtrem-finance/build/lib/pages/accounts-receivable-invoice';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<AccountsReceivableInvoiceExtension>({
    extends: '@sage/xtrem-finance/AccountsReceivableInvoice',
})
export class AccountsReceivableInvoiceExtension extends ui.PageExtension<AccountsReceivableInvoice, GraphApi> {
    @ui.decorators.tableFieldOverride<AccountsReceivableInvoiceExtension>({
        columns: [
            ui.nestedFieldExtensions.text({
                bind: 'jurisdictionName',
                title: 'Jurisdiction',
                insertBefore: 'taxableAmount',
                isReadOnly: true,
                isHidden() {
                    return (this as any).financialSite.value.legalCompany?.taxEngine !== 'avalaraAvaTax';
                },
            }),
        ],
    })
    taxDetails: ui.fields.Table;
}

declare module '@sage/xtrem-finance/build/lib/pages/accounts-receivable-invoice' {
    export interface AccountsReceivableInvoice extends AccountsReceivableInvoiceExtension {}
}
