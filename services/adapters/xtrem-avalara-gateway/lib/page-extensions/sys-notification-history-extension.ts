import type { <PERSON><PERSON>h<PERSON><PERSON> } from '@sage/xtrem-avalara-gateway-api';
import type { ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { SysNotificationHistory as SysNotificationHistoryNode } from '@sage/xtrem-communication-api';
import * as PillColorCommunicationState from '@sage/xtrem-communication/build/lib/client-functions/pill-color-communication';
import type { SysNotificationHistory as SysNotificationHistoryComm } from '@sage/xtrem-communication/build/lib/pages/sys-notification-history';
import { DateValue } from '@sage/xtrem-date-time';
import * as ui from '@sage/xtrem-ui';
import type {
    AvalaraNotificationHistory,
    AvalaraNotificationHistoryContext,
} from '../client-functions/interfaces/interfaces';
import {
    getDocumentPageNameSales,
    getDocumentType,
    getLineCurKey,
    getMainTopic,
} from '../client-functions/notifications';
import {
    avalaraDocumentTypeValues,
    avalaraHistoryStatusValues,
    avalaraListenerValues,
    avalaraMainTopicValues,
    avalaraTopicValues,
    getAvataxNotificationMessageLocalized,
} from '../shared-functions';

@ui.decorators.pageExtension<SysNotificationHistoryExtension>({
    extends: '@sage/xtrem-communication/SysNotificationHistory',

    async onLoad() {
        await this.defaultLoadingPage();
        this.avalaraSection.isHidden = !this.$.isServiceOptionEnabled('avalaraOption');
    },
})
export class SysNotificationHistoryExtension extends ui.PageExtension<SysNotificationHistoryComm, GraphApi> {
    @ui.decorators.section<SysNotificationHistoryExtension>({
        isTitleHidden: true,
        title: 'Avalara',
    })
    avalaraSection: ui.containers.Section;

    @ui.decorators.block<SysNotificationHistoryExtension>({
        parent() {
            return this.avalaraSection;
        },
        title: 'Criteria',
        // isTitleHidden: true,
    })
    avalaraHistoryCriteriaBlock: ui.containers.Block;

    @ui.decorators.block<SysNotificationHistoryExtension>({
        parent() {
            return this.avalaraSection;
        },
        title: 'Avalara',
        isTitleHidden: true,
    })
    avalaraHistoryBlock: ui.containers.Block;

    @ui.decorators.multiDropdownField<SysNotificationHistoryExtension>({
        parent() {
            return this.avalaraHistoryCriteriaBlock;
        },
        title: 'Status',
        bind: 'status',
        optionType: '@sage/xtrem-communication/CommunicationState',
        // options: ['sent', 'success', 'error'],
    })
    avalaraHistoryStatus: ui.fields.MultiDropdown;

    @ui.decorators.multiDropdownField<SysNotificationHistoryExtension>({
        isTransient: true,
        parent() {
            return this.avalaraHistoryCriteriaBlock;
        },
        title: 'Topics',
        options: avalaraMainTopicValues,
        onChange() {
            this.refreshAvalaraTopicSelected();
            this.refreshDocumentCriteria();
        },
    })
    avalaraMainTopic: ui.fields.MultiDropdown;

    @ui.decorators.multiDropdownField<SysNotificationHistoryExtension>({
        isTransient: true,
        parent() {
            return this.avalaraHistoryCriteriaBlock;
        },
        title: 'Topics',
        isHidden: true,
        bind: 'avalaraTopic',
        options: avalaraTopicValues,
    })
    avalaraTopic: ui.fields.MultiDropdown;

    @ui.decorators.dateField<SysNotificationHistoryExtension>({
        isTransient: true,
        parent() {
            return this.avalaraHistoryCriteriaBlock;
        },
        title: 'From date',
        maxDate: DateValue.today().toString(),
        bind: 'fromDate',
    })
    fromDate: ui.fields.Date;

    @ui.decorators.multiDropdownField<SysNotificationHistoryExtension>({
        isTransient: true,
        parent() {
            return this.avalaraHistoryCriteriaBlock;
        },
        title: 'Document type',
        bind: 'avalaraDocumentType',
        options: avalaraDocumentTypeValues,
    })
    avalaraDocumentType: ui.fields.MultiDropdown;

    @ui.decorators.textField<SysNotificationHistoryExtension>({
        isTransient: true,
        parent() {
            return this.avalaraHistoryCriteriaBlock;
        },
        title: 'Document number',
        bind: 'avalaraDocumentNumber',
    })
    avalaraDocumentNumber: ui.fields.Text;

    @ui.decorators.checkboxField<SysNotificationHistoryExtension>({
        isTransient: true,
        parent() {
            return this.avalaraHistoryCriteriaBlock;
        },
        title: 'Latest notifications',
        bind: 'avalaraLatestNotification',
    })
    avalaraLatestNotification: ui.fields.Checkbox;

    @ui.decorators.buttonField<SysNotificationHistoryExtension>({
        parent() {
            return this.avalaraHistoryCriteriaBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-avalara-gateway/sys__notification_history_extension__search', 'Search');
        },
        width: 'small',
        async onClick() {
            this.$.loader.isHidden = false;
            await this.fillAvalaraHistoryTable();
            this.$.setPageClean();
            this.$.loader.isHidden = true;
        },
    })
    avalaraSearchHistoryButton: ui.fields.Button;

    @ui.decorators.buttonField<SysNotificationHistoryExtension>({
        parent() {
            return this.avalaraHistoryCriteriaBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-avalara-gateway/sys__notification_history_extension__revert_criteria',
                'Revert criteria',
            );
        },
        width: 'small',
        async onClick() {
            this.$.loader.isHidden = false;
            await this.defaultLoadingPage();
            this.$.setPageClean();
            this.$.loader.isHidden = true;
        },
    })
    avalaraRevertCriteria: ui.fields.Button;

    @ui.decorators.tableField<SysNotificationHistoryExtension, AvalaraNotificationHistory>({
        pageSize: 100,
        canFilter: false,
        isChangeIndicatorDisabled: true,
        title: 'Results',
        canResizeColumns: true,
        canSelect: false,
        isReadOnly: true,
        canExport: true,
        parent() {
            return this.avalaraHistoryBlock;
        },
        columns: [
            ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-communication/CommunicationState',
                size: 'small',
                style(_id, rowValue) {
                    return PillColorCommunicationState.getLabelColorByStatus('CommunicationState', rowValue.status);
                },
            }),

            ui.nestedFields.text({
                bind: 'errorMessage',
                isHidden: true,
            }),

            ui.nestedFields.text({
                isTransient: true,
                bind: 'topic',
                title: 'Topic',
                isReadOnly: true,
            }),

            ui.nestedFields.numeric({
                isTransient: true,
                bind: 'documentId',
                isHidden: true,
            }),

            ui.nestedFields.text({
                isTransient: true,
                bind: 'documentType',
                title: 'Document type',
                isReadOnly: true,
                size: 'small',
            }),

            ui.nestedFields.link({
                bind: 'documentNumber',
                title: 'Document number',
                size: 'small',
                async onClick(_id, rowData: ui.PartialNode<AvalaraNotificationHistory>) {
                    if (rowData.documentType !== undefined && rowData.documentId !== undefined) {
                        const page = getDocumentPageNameSales(rowData.documentType);
                        await this.$.dialog.page(
                            page,
                            { _id: rowData.documentId },
                            {
                                size: 'extra-large',
                                resolveOnCancel: true,
                            },
                        );
                    }
                },
            }),

            ui.nestedFields.numeric({
                isTransient: true,
                bind: 'recordCount',
                isHidden: true,
            }),

            ui.nestedFields.checkbox({
                isTransient: true,
                bind: 'isCommit',
                isHidden: true,
            }),

            ui.nestedFields.checkbox({
                isTransient: true,
                bind: 'isPosting',
                isHidden: true,
            }),

            ui.nestedFields.text({
                isTransient: true,
                bind: 'internalMessage',
                title: 'Internal message',
                isReadOnly: true,
                isFullWidth: true,
            }),

            ui.nestedFields.text({
                isTransient: true,
                bind: 'message',
                title: 'Error message',
                isReadOnly: true,
                isFullWidth: true,
            }),

            ui.nestedFields.date({
                title: 'Date',
                bind: 'dateLogged',
                isReadOnly: true,
                size: 'small',
            }),

            ui.nestedFields.text({
                isTransient: true,
                bind: 'code',
                isHidden: true,
            }),
        ],
    })
    avalaraHistory: ui.fields.Table<AvalaraNotificationHistory>;

    async fillAvalaraHistoryTable() {
        this.avalaraHistory.value = [];
        let avalaraHistoryValues: ExtractEdgesPartial<AvalaraNotificationHistory>[] =
            await this.getAvalaraHistoryValues();
        avalaraHistoryValues = this.manageGetOnlyLatestNotifications(avalaraHistoryValues);
        avalaraHistoryValues
            .filter(
                (lineFilter: ExtractEdgesPartial<AvalaraNotificationHistory>) =>
                    this.fromDate.value && lineFilter.dateLogged && lineFilter.dateLogged >= this.fromDate.value,
            )
            .forEach(line => this.avalaraHistory.addOrUpdateRecordValue(line));
    }

    private async getAvalaraHistoryValues() {
        const sysNotificationHistory = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-communication/SysNotificationHistory')
                .query(
                    ui.queryUtils.edgesSelector<SysNotificationHistoryNode>(
                        {
                            _id: true,
                            status: true,
                            errorMessage: true,
                            dateLogged: true,
                            notificationContext: true,
                        },
                        { filter: this.getFilter(), orderBy: { _id: -1 }, first: 2000 },
                    ),
                )
                .execute(),
        ) as ExtractEdgesPartial<AvalaraNotificationHistory>[];

        const avalaraHistoryValues: ExtractEdgesPartial<AvalaraNotificationHistory>[] = [];
        sysNotificationHistory.forEach((sysNotificationHistoryLine: Partial<ui.plugin.Dict<any>>) => {
            const notificationContext: AvalaraNotificationHistoryContext = JSON.parse(
                sysNotificationHistoryLine.notificationContext,
            );
            const line: ExtractEdgesPartial<AvalaraNotificationHistory> = {
                _id: sysNotificationHistoryLine._id,
                status: sysNotificationHistoryLine.status,
                errorMessage: sysNotificationHistoryLine.errorMessage,
                dateLogged: sysNotificationHistoryLine.dateLogged,
                notificationContext: sysNotificationHistoryLine.notificationContext,
                topic: getMainTopic(notificationContext.topic),
                documentType: getDocumentType(notificationContext.documentType),

                documentId: notificationContext.documentId,
                documentNumber: notificationContext.documentNumber,
                isCommit: notificationContext.isCommit,
                isPosting: notificationContext.isPosting,
                recordCount: notificationContext.recordCount,
                message: notificationContext.message,
                internalMessage: getAvataxNotificationMessageLocalized(
                    ui,
                    notificationContext.code,
                    notificationContext.recordCount,
                ),
                code: notificationContext.code,
            };
            // filters
            let isLineDisplayed = true;
            isLineDisplayed = this.lineFilterWithAvalaraDocumentNumberCriteria(isLineDisplayed, line);
            isLineDisplayed = this.lineFilterWithAvalaraDocumentTypeCriteria(isLineDisplayed, line);
            isLineDisplayed = this.lineFilterWithAvalaraHistoryStatusCriteria(isLineDisplayed, line);
            if (isLineDisplayed) {
                avalaraHistoryValues.push(line);
            }
        });
        return avalaraHistoryValues;
    }

    private manageGetOnlyLatestNotifications(avalaraHistory: ExtractEdgesPartial<AvalaraNotificationHistory>[]) {
        let finalAvalaraHistory: ExtractEdgesPartial<AvalaraNotificationHistory>[] = avalaraHistory;
        finalAvalaraHistory = this.sortHistoryByDocumentNumberDescIfRequired(finalAvalaraHistory);
        finalAvalaraHistory = this.keepLatestNotificationIfRequired(finalAvalaraHistory);
        finalAvalaraHistory = this.sortHistoryByIdDescIfRequired(finalAvalaraHistory);
        return finalAvalaraHistory;
    }

    private sortHistoryByDocumentNumberDescIfRequired(
        avalaraHistory: ExtractEdgesPartial<AvalaraNotificationHistory>[],
    ) {
        if (this.avalaraLatestNotification.value && avalaraHistory.length > 0) {
            avalaraHistory.sort((a, b) => {
                const curA = getLineCurKey(JSON.parse(a.notificationContext || ''));
                const curB = getLineCurKey(JSON.parse(b.notificationContext || ''));
                if (curA && curB) {
                    if (curA < curB) return -1;
                    if (curA > curB) return 1;
                }
                return 0;
            });
        }
        return avalaraHistory;
    }

    private keepLatestNotificationIfRequired(avalaraHistory: ExtractEdgesPartial<AvalaraNotificationHistory>[]) {
        const finalAvalaraHistory: ExtractEdgesPartial<AvalaraNotificationHistory>[] = [];
        let prevKey = '';
        let curKey = '';
        avalaraHistory.forEach(line => {
            if (this.avalaraLatestNotification.value) {
                curKey = getLineCurKey(line);
                if (curKey !== prevKey) {
                    finalAvalaraHistory.push(line);
                }
                prevKey = curKey;
            } else {
                finalAvalaraHistory.push(line);
            }
        });
        return finalAvalaraHistory;
    }

    private sortHistoryByIdDescIfRequired(avalaraHistory: ExtractEdgesPartial<AvalaraNotificationHistory>[]) {
        if (this.avalaraLatestNotification.value && avalaraHistory.length > 0) {
            avalaraHistory.sort((a, b) => {
                if (a._id && b._id) {
                    if (a._id < b._id) return -1;
                    if (a._id > b._id) return 1;
                }
                return 0;
            });
        }
        return avalaraHistory;
    }

    private lineFilterWithAvalaraDocumentNumberCriteria(
        isLineDisplayed: boolean,
        line: ExtractEdgesPartial<AvalaraNotificationHistory>,
    ) {
        let isLineDisplayedAfterCriteria = isLineDisplayed;
        if (isLineDisplayedAfterCriteria) {
            if (this.avalaraDocumentNumber.value && this.avalaraDocumentNumber.value !== '') {
                if (line.documentType === null || line.documentType === '') {
                    isLineDisplayedAfterCriteria = false;
                } else if (line.documentNumber === null || line.documentNumber === '') {
                    isLineDisplayedAfterCriteria = false;
                } else if (
                    line.documentNumber !== '' &&
                    !(line.documentNumber || '').includes(this.avalaraDocumentNumber.value)
                ) {
                    isLineDisplayedAfterCriteria = false;
                }
            }
        }
        return isLineDisplayedAfterCriteria;
    }

    private lineFilterWithAvalaraDocumentTypeCriteria(
        isLineDisplayed: boolean,
        line: ExtractEdgesPartial<AvalaraNotificationHistory>,
    ) {
        let isLineDisplayedAfterCriteria = isLineDisplayed;
        if (isLineDisplayedAfterCriteria) {
            if (this.avalaraDocumentType.value && this.avalaraDocumentType.value?.length > 0) {
                if (
                    line.documentType === null ||
                    (line.documentType && !this.avalaraDocumentType.value.includes(line.documentType))
                ) {
                    isLineDisplayedAfterCriteria = false;
                }
            }
        }
        return isLineDisplayedAfterCriteria;
    }

    private lineFilterWithAvalaraHistoryStatusCriteria(
        isLineDisplayed: boolean,
        line: ExtractEdgesPartial<AvalaraNotificationHistory>,
    ) {
        let isLineDisplayedAfterCriteria = isLineDisplayed;
        if (isLineDisplayedAfterCriteria) {
            if (this.avalaraHistoryStatus.value && this.avalaraHistoryStatus.value?.length > 0) {
                if (line.status && !this.avalaraHistoryStatus.value.includes(line.status)) {
                    isLineDisplayedAfterCriteria = false;
                }
            } else {
                isLineDisplayedAfterCriteria = false;
            }
        }
        return isLineDisplayedAfterCriteria;
    }

    getFilter(): Filter<SysNotificationHistoryNode> | {} {
        const filter = { _and: [{}] };
        filter._and.push({
            listener: {
                _in: avalaraListenerValues,
            },
        });

        if (this.avalaraTopic.value) {
            filter._and.push({
                topic: { _in: this.avalaraTopic.value },
            });
        }
        return filter._and.length ? filter : {};
    }

    async defaultLoadingPage() {
        this.fromDate.value = new Date(Date.now() - 2592000000).toISOString().substring(0, 10);
        this.avalaraHistoryStatus.value = avalaraHistoryStatusValues;
        this.avalaraTopic.value = avalaraTopicValues;
        this.avalaraMainTopic.value = avalaraMainTopicValues;
        this.avalaraLatestNotification.value = true;
        this.avalaraDocumentType.value = null;
        this.avalaraDocumentNumber.value = null;
        await this.fillAvalaraHistoryTable();
        this.refreshDocumentCriteria();
    }

    refreshAvalaraTopicSelected() {
        this.avalaraTopic.value = [];
        if (this.avalaraMainTopic.value) {
            if (this.avalaraMainTopic.value.length > 0) {
                this.avalaraTopic.value = avalaraTopicValues
                    .filter(
                        (_value, index) =>
                            this.avalaraMainTopic.value.filter(list => list === avalaraMainTopicValues[index]).length >
                            0,
                    )
                    .map(value => value);
            }
        }
    }

    refreshDocumentCriteria() {
        if (
            this.avalaraMainTopic.value &&
            this.avalaraMainTopic.value?.length > 0 &&
            this.avalaraMainTopic.value.find((value: string) => value === avalaraMainTopicValues[3])
        ) {
            this.avalaraDocumentType.isDisabled = false;
            this.avalaraDocumentNumber.isDisabled = false;
        } else {
            this.avalaraDocumentType.isDisabled = true;
            this.avalaraDocumentType.value = null;
            this.avalaraDocumentNumber.isDisabled = true;
            this.avalaraDocumentNumber.value = null;
        }
    }
}

declare module '@sage/xtrem-communication/build/lib/pages/sys-notification-history' {
    interface SysNotificationHistory extends SysNotificationHistoryExtension {}
}
