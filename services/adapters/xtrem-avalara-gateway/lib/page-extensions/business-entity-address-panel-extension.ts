import type { EntityUse, GraphApi } from '@sage/xtrem-avalara-gateway-api';
import type { BusinessEntityAddressPanel } from '@sage/xtrem-master-data/build/lib/pages/business-entity-address-panel';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<BusinessEntityAddressPanelExtension>({
    extends: '@sage/xtrem-master-data/BusinessEntityAddressPanel',
    onLoad() {
        this.entityUse.isHidden = !this.$.isServiceOptionEnabled('avalaraOption');
    },
})
export class BusinessEntityAddressPanelExtension extends ui.PageExtension<BusinessEntityAddressPanel, GraphApi> {
    @ui.decorators.referenceField<BusinessEntityAddressPanelExtension, EntityUse>({
        title: 'Entity use',
        parent() {
            return this.shippingBlock;
        },
        bind: { deliveryDetail: { entityUse: true } },
        node: '@sage/xtrem-avalara-gateway/EntityUse',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
        ],
    })
    entityUse: ui.fields.Reference<EntityUse>;
}
declare module '@sage/xtrem-master-data/build/lib/pages/business-entity-address-panel' {
    export interface BusinessEntityAddressPanel extends BusinessEntityAddressPanelExtension {}
}
