import type { <PERSON>raph<PERSON><PERSON> } from '@sage/xtrem-avalara-gateway-api';
import type { SalesCreditMemoLine } from '@sage/xtrem-sales-api';
import type { SalesCreditMemo } from '@sage/xtrem-sales/build/lib/pages/sales-credit-memo';
import * as ui from '@sage/xtrem-ui';
import * as displayButtons from '../client-functions/display-buttons-sales-credit-memo-extension';
import { getAvataxNotificationMessageOnDocument } from '../client-functions/page-functions';

@ui.decorators.pageExtension<SalesCreditMemoExtension>({
    extends: '@sage/xtrem-sales/SalesCreditMemo',
})
export class SalesCreditMemoExtension extends ui.PageExtension<SalesCreditMemo, GraphApi> {
    taxEngine: any;

    @ui.decorators.section<SalesCreditMemoExtension>({
        title: 'Avalara notification',
        isHidden: true,
    })
    avalaraNotificationSection: ui.containers.Section;

    @ui.decorators.block<SalesCreditMemoExtension>({
        parent() {
            return this.avalaraNotificationSection;
        },
        width: 'large',
        title: 'Information',
    })
    avalaraNotificationBlock: ui.containers.Block;

    @ui.decorators.textField<SalesCreditMemoExtension>({
        parent() {
            return this.avalaraNotificationBlock;
        },
        isTransient: true,
        title: 'Message',
        bind: 'avalaraMessage',
    })
    avalaraMessage: ui.fields.Text;

    @ui.decorators.labelFieldOverride<SalesCreditMemoExtension>({
        async onClick() {
            await this.getAvalaraNotification('SalesCreditMemo', this._id.value);
        },
    })
    taxCalculationStatus: ui.fields.Label;

    @ui.decorators.tableFieldOverride<SalesCreditMemoExtension>({
        columns: [
            ui.nestedFieldExtensions.text({
                bind: 'jurisdictionName',
                title: 'Jurisdiction',
                insertBefore: 'taxableAmount',
                isReadOnly: true,
                isHidden() {
                    return this.taxEngine.value !== 'avalaraAvaTax';
                },
            }),
        ],
    })
    taxDetails: ui.fields.Table;

    @ui.decorators.tableFieldOverride<SalesCreditMemoExtension, SalesCreditMemoLine>({
        columns: [
            ui.nestedFieldExtensions.reference({
                title: 'Entity use code',
                bind: 'entityUse',
                node: '@sage/xtrem-avalara-gateway/EntityUse',
                isHiddenOnMainField: true,
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                ],
            }),
        ],
    })
    lines: ui.fields.Table;

    @ui.decorators.pageActionOverride<SalesCreditMemoExtension>({
        title: 'Calculate tax',
        async onClick() {
            this.$.loader.isHidden = false;
            const result = await this.$.graph
                .node('@sage/xtrem-sales/SalesCreditMemo')
                .mutations.calculateTax(true, { creditMemo: this.$.recordId ?? '' })
                .execute();
            this.$.showToast(result, { type: 'success' });
            await this.$.router.refresh(true);
            this.$.loader.isHidden = true;
        },
    })
    avalara: ui.PageAction;

    callPostMutation() {
        return this.$.graph
            .node('@sage/xtrem-sales/SalesCreditMemo')
            .mutations.postAvalara(
                {
                    wasSuccessful: true,
                    message: true,
                },
                { creditMemo: this.$.recordId ?? '' },
            )
            .execute();
    }

    async getAvalaraNotification(documentType: string, documentId: string | null) {
        if (documentId && this.taxCalculationStatus.value === 'failed' && this.taxEngine.value === 'avalaraAvaTax') {
            await this.$.dialog.message(
                'info',
                ui.localize('@sage/xtrem-avalara-gateway/avalara_notification_dialog_title', 'Avalara notification'),
                await getAvataxNotificationMessageOnDocument(this, documentType, documentId),
                {},
            );
        }
    }

    manageDisplayButtonAvalaraAction(isDirty: boolean, status: string) {
        this.avalara.isHidden = displayButtons.isHiddenButtonAvalaraAction({
            parameters: {
                status,
                taxCalculationStatus: this.taxCalculationStatus.value,
                taxEngine: this.taxEngine.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }
}

declare module '@sage/xtrem-sales/build/lib/pages/sales-credit-memo' {
    export interface SalesCreditMemo extends SalesCreditMemoExtension {}
}
