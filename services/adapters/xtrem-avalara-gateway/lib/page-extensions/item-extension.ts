import type { AvalaraItemTax, GraphApi } from '@sage/xtrem-avalara-gateway-api';
import type { Item } from '@sage/xtrem-master-data/build/lib/pages/item';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<ItemExtension>({
    extends: '@sage/xtrem-master-data/Item',
    extensionAccessBinding: { node: '@sage/xtrem-avalara-gateway/AvalaraItemTax', bind: '$read' },
    navigationPanel: {
        listItem: {
            avalaraItemTax: ui.nestedFieldExtensions.reference({
                bind: 'avalaraItemTax',
                valueField: 'description',
                insertBefore: 'lotManagement',
                node: '@sage/xtrem-avalara-gateway/AvalaraItemTax',
                title: 'Avalara item tax',
                isHiddenOnMainField: true,
            }),
        },
    },
    onLoad() {
        this.avalaraItemTax.isHidden = !this.$.isServiceOptionEnabled('avalaraOption');
    },
})
export class ItemExtension extends ui.PageExtension<Item, GraphApi> {
    @ui.decorators.referenceField<ItemExtension, AvalaraItemTax>({
        title: 'Avalara item tax',
        lookupDialogTitle: 'Select Avalara item tax',
        parent() {
            return this.financialBlock;
        },
        insertBefore() {
            return this.positionField3;
        },
        node: '@sage/xtrem-avalara-gateway/AvalaraItemTax',
        valueField: 'description',
        helperTextField: 'id',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        columns: [ui.nestedFields.text({ bind: 'id' }), ui.nestedFields.text({ bind: 'description' })],
    })
    avalaraItemTax: ui.fields.Reference;
}
declare module '@sage/xtrem-master-data/build/lib/pages/item' {
    export interface Item extends ItemExtension {}
}
