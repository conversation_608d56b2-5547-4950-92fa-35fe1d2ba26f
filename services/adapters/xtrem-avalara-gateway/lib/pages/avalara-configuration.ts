import type { AvalaraCompany, GraphApi, MapCompany } from '@sage/xtrem-avalara-gateway-api';
import type { Dict } from '@sage/xtrem-client';
import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-master-data/build/lib/client-functions/page-functions';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { Company, Site } from '@sage/xtrem-system-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { confirmDialog } from '../client-functions/page-functions';
import { avalara } from '../menu-items/avalara';

@ui.decorators.page<AvalaraConfiguration>({
    module: 'xtrem-avalara-gateway',
    node: '@sage/xtrem-avalara-gateway/AvalaraConfiguration',
    title: 'Configuration',
    menuItem: avalara,
    mode: 'tabs',

    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
        });
    },
    businessActions() {
        return [this.testConnection, this.$standardSaveAction];
    },
    async defaultEntry() {
        return (
            (
                await this.$.graph
                    .node('@sage/xtrem-avalara-gateway/AvalaraConfiguration')
                    .queries.defaultInstance({ _id: true }, false)
                    .execute()
            )?._id || null
        );
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
        });
        this.$.page.title = ui.localize(
            '@sage/xtrem-avalara-gateway/page__title__avalara_configuration',
            'Avalara configuration',
        );
        this.isActiveFilter.value = true;
        await this.mapCompanyLines.refresh();
    },
})
export class AvalaraConfiguration extends ui.Page<GraphApi> {
    currentSessionIds: {
        companies: Dict<boolean>;
        financialSites: Dict<boolean>;
        avalaraCompanies: Dict<boolean>;
    } = {
        companies: {},
        financialSites: {},
        avalaraCompanies: {},
    };

    @ui.decorators.section<AvalaraConfiguration>({
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<AvalaraConfiguration>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<AvalaraConfiguration>({
        isHidden: true,
        title: 'ID',
    })
    id: ui.fields.Text;

    @ui.decorators.switchField<AvalaraConfiguration>({
        parent() {
            return this.mainBlock;
        },
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<AvalaraConfiguration>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorIsActive: ui.fields.Separator;

    @ui.decorators.checkboxField<AvalaraConfiguration>({
        parent() {
            return this.mainBlock;
        },
        title: 'Sandbox mode',
        async onChange() {
            if (this.isSandboxMode.value === false) {
                this.isCommittedTransaction.isHidden = true;
                this.isCommittedTransaction.value = false;
            } else {
                this.isCommittedTransaction.isHidden = false;
            }
            this.endPointUrl.value = await this.$.graph
                .node('@sage/xtrem-avalara-gateway/AvalaraConfiguration')
                .queries.getUrl(true, { isSandBox: this.isSandboxMode.value || false })
                .execute();
        },
    })
    isSandboxMode: ui.fields.Checkbox;

    @ui.decorators.checkboxField<AvalaraConfiguration>({
        parent() {
            return this.mainBlock;
        },
        title: 'Committed transaction',
        isHidden() {
            return !this.isSandboxMode;
        },
    })
    isCommittedTransaction: ui.fields.Checkbox;

    @ui.decorators.textField<AvalaraConfiguration>({
        parent() {
            return this.mainBlock;
        },
        title: 'Endpoint',
        isDisabled: true,
    })
    endPointUrl: ui.fields.Text;

    @ui.decorators.textField<AvalaraConfiguration>({
        parent() {
            return this.mainBlock;
        },
        title: 'Account ID',
        isMandatory: true,
    })
    accountId: ui.fields.Text;

    @ui.decorators.textField<AvalaraConfiguration>({
        parent() {
            return this.mainBlock;
        },
        title: 'License key',
        isMandatory: true,
        isPassword: true,
    })
    licenseKey: ui.fields.Text;

    @ui.decorators.block<AvalaraConfiguration>({
        parent() {
            return this.mainSection;
        },
    })
    addressBlock: ui.containers.Block;

    @ui.decorators.checkboxField<AvalaraConfiguration>({
        parent() {
            return this.addressBlock;
        },
        title: 'Address validation',
    })
    isAddressValidationActive: ui.fields.Checkbox;

    @ui.decorators.dropdownListField<AvalaraConfiguration>({
        parent() {
            return this.addressBlock;
        },
        title: 'Address text case',
        optionType: '@sage/xtrem-avalara-gateway/AddressTextCase',
        isMandatory: true,
    })
    addressTextCase: ui.fields.DropdownList;

    @ui.decorators.block<AvalaraConfiguration>({
        parent() {
            return this.mainSection;
        },
        title: 'Load data from Avalara',
    })
    loadBlock: ui.containers.Block;

    @ui.decorators.buttonField<AvalaraConfiguration>({
        isTransient: true,
        width: 'medium',
        parent() {
            return this.loadBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-avalara-gateway/load-companies-button', 'Companies');
        },
        isDisabled() {
            return this.$.isDirty;
        },
        onError(error) {
            return error.message;
        },
        async onClick() {
            this.$.loader.isHidden = false;

            const loadCompanyCodesFromAvalaraResult = await this.$.graph
                .node('@sage/xtrem-avalara-gateway/AvalaraCompany')
                .mutations.loadCompanyFromAvalara(true, true)
                .execute();

            if (!loadCompanyCodesFromAvalaraResult) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-avalara-gateway/pages__avalara_configuration__companies_cannot_be_loaded',
                        'Avalara company codes cannot be loaded.',
                    ),
                );
            }
            this.loadCompanyFromAvalara.isDisabled = true;
            this.$.loader.isHidden = true;
            this.$.setPageClean();
            this.$.showToast(loadCompanyCodesFromAvalaraResult, { type: 'success' });
        },
    })
    loadCompanyFromAvalara: ui.fields.Button;

    @ui.decorators.buttonField<AvalaraConfiguration>({
        isTransient: true,
        width: 'medium',
        parent() {
            return this.loadBlock;
        },
        isDisabled() {
            return this.$.isDirty;
        },
        map() {
            return ui.localize('@sage/xtrem-avalara-gateway/load-entity-use-button', 'Entity use codes');
        },
        onError(error) {
            return error.message;
        },
        async onClick() {
            this.$.loader.isHidden = false;

            const loadEntityUseCodesFromAvalaraResult = await this.$.graph
                .node('@sage/xtrem-avalara-gateway/EntityUse')
                .mutations.loadEntityUseCodesFromAvalara(true, {})
                .execute();

            if (!loadEntityUseCodesFromAvalaraResult) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-avalara-gateway/pages__avalara_configuration__entity_use_cannot_be_loaded',
                        'Entity use codes cannot be loaded.',
                    ),
                );
            }
            this.loadEntityUseFromAvalara.isDisabled = true;
            this.$.loader.isHidden = true;
            this.$.setPageClean();
            this.$.showToast(loadEntityUseCodesFromAvalaraResult, { type: 'success' });
        },
    })
    loadEntityUseFromAvalara: ui.fields.Button;

    // Load tax code starts from here
    @ui.decorators.buttonField<AvalaraConfiguration>({
        isTransient: true,
        width: 'medium',
        parent() {
            return this.loadBlock;
        },
        isDisabled() {
            return this.$.isDirty;
        },
        map() {
            return ui.localize('@sage/xtrem-avalara-gateway/load-tax-codes-button', 'Tax codes');
        },
        onError(error) {
            return error.message;
        },
        async onClick() {
            this.loadTaxCodesSection.isHidden = false;
            await this.$.dialog.custom('info', this.loadTaxCodesSection, {
                cancelButton: { isHidden: true },
                acceptButton: { isHidden: true },
                resolveOnCancel: true,
            });
            this.loadTaxCodesSection.isHidden = true;
            this.$.setPageClean();
        },
    })
    loadTaxCodesFromAvalara: ui.fields.Button;

    @ui.decorators.section<AvalaraConfiguration>({
        isHidden: true,
        title: 'Load tax codes from Avalara',
    })
    loadTaxCodesSection: ui.containers.Section;

    @ui.decorators.block<AvalaraConfiguration>({
        parent() {
            return this.loadTaxCodesSection;
        },
        title: 'Filter criteria',
    })
    loadTaxCodesBlock: ui.containers.Block;

    @ui.decorators.switchField<AvalaraConfiguration>({
        isTransient: true,
        parent() {
            return this.loadTaxCodesBlock;
        },
        title: 'Active',
    })
    isActiveFilter: ui.fields.Switch;

    @ui.decorators.separatorField<AvalaraConfiguration>({
        parent() {
            return this.loadTaxCodesBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveFilterSeparator: ui.fields.Separator;

    @ui.decorators.textField<AvalaraConfiguration>({
        isTransient: true,
        parent() {
            return this.loadTaxCodesBlock;
        },
        title: 'From tax code',
    })
    fromTaxCode: ui.fields.Text;

    @ui.decorators.textField<AvalaraConfiguration>({
        isTransient: true,
        parent() {
            return this.loadTaxCodesBlock;
        },
        title: 'To tax code',
    })
    toTaxCode: ui.fields.Text;

    @ui.decorators.separatorField<AvalaraConfiguration>({
        parent() {
            return this.loadTaxCodesBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    toTaxCodeSeparator: ui.fields.Separator;

    @ui.decorators.textField<AvalaraConfiguration>({
        isTransient: true,
        parent() {
            return this.loadTaxCodesBlock;
        },
        title: 'Description',
    })
    descriptionFilter: ui.fields.Text;

    @ui.decorators.separatorField<AvalaraConfiguration>({
        parent() {
            return this.loadTaxCodesBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    descriptionFilterSeparator: ui.fields.Separator;

    @ui.decorators.buttonField<AvalaraConfiguration>({
        isTransient: true,
        parent() {
            return this.loadTaxCodesBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-avalara-gateway/pages__avalara_configuration__load_button_text', 'Load');
        },
        onError(error) {
            this.$.loader.isHidden = true;
            const message = error.message.substring(error.message.lastIndexOf(':') + 2);
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-avalara-gateway/pages__avalara_configuration__load_tax_exception',
                    'Tax codes from Avalara cannot be loaded. ({{exception}})',
                    { exception: message },
                ),
                { type: 'error' },
            );
        },
        async onClick() {
            if (
                await confirmDialog(this, {
                    title: ui.localize(
                        '@sage/xtrem-avalara-gateway/pages__avalara_configuration__avalara_item_tax_dialog_title',
                        'Loading confirmation',
                    ),
                    message: ui.localize(
                        '@sage/xtrem-avalara-gateway/pages__avalara_configuration__avalara_item_tax_dialog_content',
                        'You are about to load tax codes from Avalara. Confirm?',
                    ),
                })
            ) {
                this.$.loader.isHidden = false;

                const loadTaxCodesFromAvalaraMessage = await this.$.graph
                    .node('@sage/xtrem-avalara-gateway/AvalaraItemTax')
                    .mutations.loadTaxCodesFromAvalara(true, {
                        isActive: this.isActiveFilter.value ?? null,
                        fromTaxCode: this.fromTaxCode.value ?? null,
                        toTaxCode: this.toTaxCode.value ?? null,
                        description: this.descriptionFilter.value ?? null,
                    })
                    .execute();

                if (!loadTaxCodesFromAvalaraMessage) {
                    throw new Error(
                        ui.localize(
                            '@sage/xtrem-avalara-gateway/pages__avalara_configuration__taxes_cannot_be_loaded',
                            'Tax codes from Avalara cannot be loaded.',
                        ),
                    );
                }

                this.loadTaxCodesFromAvalara.isDisabled = true;
                this.$.loader.isHidden = true;
                this.$.setPageClean();
                this.$.showToast(loadTaxCodesFromAvalaraMessage, { type: 'success' });
                this.$.finish();
            }
        },
    })
    loadTaxCodesButton: ui.fields.Button;

    @ui.decorators.pageAction<AvalaraConfiguration>({
        title: 'Test connection',
        isDisabled() {
            return this.$.isDirty;
        },
        async onClick() {
            const response = await this.$.graph
                .node('@sage/xtrem-avalara-gateway/AvalaraConfiguration')
                .mutations.ping(
                    {
                        version: true,
                        authenticated: true,
                    },
                    { id: this.id.value ?? '' },
                )
                .execute();

            if (response) {
                if (response.authenticated) {
                    await this.$.dialog.message(
                        'info',
                        ui.localize('@sage/xtrem-avalara-gateway/pages__test_conntection', 'Test connection'),
                        ui.localize(
                            '@sage/xtrem-avalara-gateway/pages__avalara_configuration__authenticated_sucessfull',
                            'Logged in to {{version}} with {{authenticatedUserName}}',
                            {
                                authenticatedUserName: this.accountId.value,
                                version: response.version,
                            },
                        ),
                    );
                } else {
                    await this.$.dialog.message(
                        'info',
                        ui.localize('@sage/xtrem-avalara-gateway/pages__test_conntection', 'Test connection'),
                        ui.localize(
                            '@sage/xtrem-avalara-gateway/pages__avalara_configuration__authentication_not_sucessfull',
                            'Logged in to {{version}} without authentication',
                            {
                                version: response.version,
                            },
                        ),
                    );
                }
            }
        },
    })
    testConnection: ui.PageAction;

    @ui.decorators.section<AvalaraConfiguration>({
        title: 'Company mapping',
    })
    mapCompanySection: ui.containers.Section;

    @ui.decorators.tableField<AvalaraConfiguration>({
        bind: 'mapCompanyLines',
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-avalara-gateway/MapCompany',
        orderBy: {
            entityType: +1,
            company: +1,
            financialSite: +1,
        },
        parent() {
            return this.mapCompanySection;
        },
        title: 'Companies',
        columns: [
            ui.nestedFields.checkbox({
                title: 'Active',
                bind: 'isActive',
                size: 'small',
                isReadOnly: true,
            }),
            ui.nestedFields.dropdownList({
                title: 'Entity type',
                bind: 'entityType',
                optionType: '@sage/xtrem-avalara-gateway/entityType',
                isReadOnly: true,
            }),
            ui.nestedFields.reference<AvalaraConfiguration, MapCompany, Company>({
                title: 'Company',
                lookupDialogTitle: 'Select company',
                bind: 'company',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                valueField: 'id',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.reference<AvalaraConfiguration, MapCompany, Site>({
                title: 'Financial site',
                lookupDialogTitle: 'Select financial site',
                bind: 'financialSite',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'id',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.reference<AvalaraConfiguration, MapCompany, AvalaraCompany>({
                title: 'Avalara company',
                lookupDialogTitle: 'Select avalara company',
                bind: 'avalaraCompany',
                node: '@sage/xtrem-avalara-gateway/AvalaraCompany',
                valueField: 'id',
                isReadOnly: true,
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: `Edit`,
                async onClick(rowId: any, rowItem: any) {
                    await this.addOrEditMapCompanyLine(rowId, rowItem, rowItem._id, 'U');
                },
            },
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                async onClick(rowId: any) {
                    if (
                        await confirmDialogWithAcceptButtonText(
                            this,
                            ui.localize(
                                '@sage/xtrem-avalara-gateway/pages__avalara_configuration__delete_dialog_title',
                                'Confirm deletion',
                            ),
                            ui.localize(
                                '@sage/xtrem-avalara-gateway/pages__avalara_configuration__delete_dialog_content',
                                'You are about to delete this record.',
                            ),
                            ui.localize(
                                '@sage/xtrem-avalara-gateway/pages__avalara_configuration__confirm_delete',
                                'Delete',
                            ),
                        )
                    ) {
                        this.mapCompanyLines.removeRecord(rowId);
                        await this.$.graph.delete({ _id: rowId, nodeName: '@sage/xtrem-avalara-gateway/MapCompany' });
                        await this.mapCompanyLines.refresh();
                        this.$.setPageClean();
                    }
                },
            },
        ],
        fieldActions() {
            return [this.addMapCompany];
        },
    })
    mapCompanyLines: ui.fields.Table;

    @ui.decorators.pageAction<AvalaraConfiguration>({
        icon: 'add',
        title: 'Add',
        async onClick() {
            await this.addOrEditMapCompanyLine(null, null, null, 'C');
        },
    })
    addMapCompany: ui.PageAction;

    @ui.decorators.textField<AvalaraConfiguration>({
        isHidden: true,
    })
    _id: ui.fields.Text;

    async addOrEditMapCompanyLine(
        rowId: string | null,
        line: Dict<string> | null,
        mapCompanyLineId: string | null,
        action: string,
    ) {
        let _id: string | null = mapCompanyLineId;
        if (Number(rowId) < 0) _id = null;
        await utils.catchPanelCrossQuitButtonAsNoop(
            this.$.dialog.page(
                '@sage/xtrem-avalara-gateway/MapCompanyPanel',
                {
                    _id: _id ?? '',
                    rowId: rowId ?? '',
                    line: line ? JSON.stringify(line) : '',
                    currentSessionIds: JSON.stringify(this.currentSessionIds),
                    action,
                    avalaraConfiguration: this._id.value ?? '',
                },
                {
                    rightAligned: true,
                    size: 'large',
                },
            ),
        );

        await this.mapCompanyLines.refresh();
    }
}
