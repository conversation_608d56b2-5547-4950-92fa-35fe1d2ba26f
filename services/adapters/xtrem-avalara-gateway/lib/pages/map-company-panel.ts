import type { <PERSON>laraCompany, AvalaraConfiguration, GraphApi, MapCompany } from '@sage/xtrem-avalara-gateway-api';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { Dict } from '@sage/xtrem-shared';
import type { Company, Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<MapCompanyPanel>({
    module: 'xtrem-avalara-gateway',
    title: 'Company mapping',
    node: '@sage/xtrem-avalara-gateway/MapCompany',

    businessActions() {
        return [this.confirm, this.cancel];
    },
    onLoad() {
        if (this.$.queryParameters.action === 'U') {
            this.$.page.title = `${ui.localize(
                '@sage/xtrem-avalara-gateway/pages__map-company-panel__edit____title',
                'Edit company mapping',
            )}`;
            this.confirm.title = 'Update';
        } else {
            this.$.page.title = `${ui.localize(
                '@sage/xtrem-avalara-gateway/pages__map-company-panel__new____title',
                'Add company mapping',
            )}`;
            this.confirm.title = 'Save';
        }
        this.rowId.value = this.$.queryParameters.rowId as string;
        this.lineData = this.$.queryParameters.line ? JSON.parse(this.$.queryParameters.line as string) : null!;
        this.currentSessionIds = JSON.parse(this.$.queryParameters.currentSessionIds as string);
        this.avalaraConfiguration.value = { _id: this.$.queryParameters.avalaraConfiguration as number };
        this.setPageValues(this.lineData);
        this.enableDisableCompanyAndFinancialSite();
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return utils.formatError(this, error);
    },
})
export class MapCompanyPanel extends ui.Page<GraphApi> {
    private lineData: MapCompany;

    currentSessionIds: {
        companies: Dict<boolean>;
        financialSites: Dict<boolean>;
        avalaraCompanies: Dict<boolean>;
    };

    @ui.decorators.section<MapCompanyPanel>({
        title: 'General',
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<MapCompanyPanel>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<MapCompanyPanel>({})
    _id: ui.fields.Text;

    @ui.decorators.textField<MapCompanyPanel>({
        isTransient: true,
    })
    rowId: ui.fields.Text;

    @ui.decorators.switchField<MapCompanyPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<MapCompanyPanel>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.dropdownListField<MapCompanyPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Entity type',
        optionType: '@sage/xtrem-avalara-gateway/EntityType',
        onChange() {
            this.enableDisableCompanyAndFinancialSite();
            this.$.setPageClean();
        },
    })
    entityType: ui.fields.DropdownList;

    @ui.decorators.referenceField<MapCompanyPanel, Company>({
        parent() {
            return this.mainBlock;
        },
        title: 'Company',
        lookupDialogTitle: 'Select company',
        node: '@sage/xtrem-system/Company',
        tunnelPage: '@sage/xtrem-master-data/Company',
        width: 'small',
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
            }),
            ui.nestedFields.text({
                bind: 'id',
                title: 'ID',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            ui.nestedFields.checkbox({
                bind: 'isActive',
                isHidden: true,
            }),
        ],
        async validation(val) {
            if (
                this.entityType.value === 'company' &&
                this.lineData?.company?._id !== val?._id &&
                (!Object.prototype.hasOwnProperty.call(this.currentSessionIds.companies, val._id) ||
                    this.currentSessionIds.companies[val._id])
            ) {
                const result = await this.$.graph
                    .node('@sage/xtrem-avalara-gateway/MapCompany')
                    .queries.validateCompany(
                        {
                            hasErrors: true,
                            errorMessage: true,
                        },
                        {
                            entityType: this.entityType.value,
                            cachedCompanies: Object.keys(this.currentSessionIds.companies),
                            company: val?._id ?? null,
                        },
                    )
                    .execute();
                if (result.hasErrors) {
                    return result.errorMessage;
                }
            }
            return undefined;
        },
        filter() {
            return {
                isActive: { _eq: true },
            };
        },
        isAutoSelectEnabled: true,
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 1,
        placeholder: 'Select a company',
    })
    company: ui.fields.Reference;

    @ui.decorators.referenceField<MapCompanyPanel, Site>({
        parent() {
            return this.mainBlock;
        },
        title: 'Financial site',
        lookupDialogTitle: 'Select financial site',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isFinance', isHidden: true }),
            ui.nestedFields.reference<MapCompanyPanel, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
        ],
        minLookupCharacters: 1,
        placeholder: 'Select site',
        width: 'small',
        filter() {
            return {
                isActive: { _eq: true },
                isFinance: { _eq: true },
            };
        },
        async validation(val) {
            if (
                this.entityType.value !== 'company' &&
                this.lineData?.financialSite?._id !== val?._id &&
                (!Object.prototype.hasOwnProperty.call(this.currentSessionIds.financialSites, val._id) ||
                    this.currentSessionIds.financialSites[val._id])
            ) {
                const result = await this.$.graph
                    .node('@sage/xtrem-avalara-gateway/MapCompany')
                    .queries.validateFinancialSite(
                        {
                            hasErrors: true,
                            errorMessage: true,
                        },
                        {
                            entityType: 'financialSite',
                            cachedFinancialSites: Object.keys(this.currentSessionIds.financialSites),
                            financialSite: val?._id ?? null,
                        },
                    )
                    .execute();

                if (result.hasErrors) {
                    return result.errorMessage;
                }
            }
            return undefined;
        },
    })
    financialSite: ui.fields.Reference;

    @ui.decorators.referenceField<MapCompanyPanel, AvalaraCompany>({
        parent() {
            return this.mainBlock;
        },
        title: 'Avalara company',
        lookupDialogTitle: 'Select avalara company',
        node: '@sage/xtrem-avalara-gateway/AvalaraCompany',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
        ],
        minLookupCharacters: 1,
        isMandatory: true,
        placeholder: 'Select avalara company',
        width: 'small',
        filter() {
            return {
                isActive: { _eq: true },
            };
        },
        async validation(val) {
            if (
                val &&
                this.lineData?.avalaraCompany?._id !== val?._id &&
                (!Object.prototype.hasOwnProperty.call(this.currentSessionIds.avalaraCompanies, val._id) ||
                    this.currentSessionIds.avalaraCompanies[val._id])
            ) {
                const result = await this.$.graph
                    .node('@sage/xtrem-avalara-gateway/MapCompany')
                    .queries.validateAvalaraCompany(
                        {
                            hasErrors: true,
                            errorMessage: true,
                        },
                        {
                            avalaraCompany: val._id,
                            cachedAvalaraCompanies: Object.keys(this.currentSessionIds.avalaraCompanies),
                        },
                    )
                    .execute();

                if (result.hasErrors) {
                    return result.errorMessage;
                }
            }
            return undefined;
        },
    })
    avalaraCompany: ui.fields.Reference;

    @ui.decorators.referenceField<MapCompanyPanel, AvalaraConfiguration>({
        parent() {
            return this.mainBlock;
        },
        title: 'Avalara configuration',
        node: '@sage/xtrem-avalara-gateway/AvalaraConfiguration',
        valueField: '_id',
        columns: [ui.nestedFields.text({ bind: 'id' })],
        isHidden: true,
    })
    avalaraConfiguration: ui.fields.Reference;

    @ui.decorators.pageAction<MapCompanyPanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.setPageClean();
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.pageAction<MapCompanyPanel>({
        title: 'OK',
        onError() {},
        async onClick() {
            this.$.loader.isHidden = false;
            await this.$standardSaveAction.execute(true);
            this.$.finish({ toRefresh: true });
        },
    })
    confirm: ui.PageAction;

    getSerializedValues() {
        const { values } = this.$;
        return values;
    }

    enableDisableCompanyAndFinancialSite() {
        if (this.entityType.value === 'company') {
            this.financialSite.isDisabled = true;
            if (this.financialSite.value) {
                this.financialSite.value = null;
            }
            this.company.isDisabled = false;
        } else {
            this.financialSite.isDisabled = false;
            this.company.isDisabled = true;
            if (this.company.value) {
                this.company.value = null;
            }
        }
    }

    private setPageValues(line: any) {
        if (line) {
            this.company.value = this.lineData.company;
            this.financialSite.value = this.lineData.financialSite;
            this.isActive.value = this.lineData.isActive;
            this.avalaraCompany.value = this.lineData.avalaraCompany;
            this.entityType.value = this.lineData.entityType;
        }
    }
}
