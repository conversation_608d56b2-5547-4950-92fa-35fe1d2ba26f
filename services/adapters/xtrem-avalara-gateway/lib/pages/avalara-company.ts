import type { Graph<PERSON>pi } from '@sage/xtrem-avalara-gateway-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { avalara } from '../menu-items/avalara';

@ui.decorators.page<AvalaraCompany>({
    module: 'xtrem-avalara-gateway',
    menuItem: avalara,
    title: 'Company',
    objectTypeSingular: 'Avalara company',
    objectTypePlural: 'Avalara companies',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-avalara-gateway/AvalaraCompany',
    isTransient: false,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'id' }),
            line2: ui.nestedFields.text({ bind: 'name' }),
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'Active',
                graphQLFilter: { isActive: { _eq: true } },
            },
            {
                title: 'Inactive',
                graphQLFilter: { isActive: { _eq: false } },
            },
        ],
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class AvalaraCompany extends ui.Page<GraphApi> {
    @ui.decorators.section<AvalaraCompany>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<AvalaraCompany>({
        parent() {
            return this.mainSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.numericField<AvalaraCompany>({
        isHidden: true,
    })
    _id: ui.fields.Numeric;

    @ui.decorators.switchField<AvalaraCompany>({
        parent() {
            return this.generalBlock;
        },
        size: 'small',
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<AvalaraCompany>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.textField<AvalaraCompany>({
        parent() {
            return this.generalBlock;
        },
        title: 'Name',
        isMandatory: true,
        width: 'large',
    })
    name: ui.fields.Text;

    @ui.decorators.textField<AvalaraCompany>({
        parent() {
            return this.generalBlock;
        },
        title: 'ID',
        isMandatory: true,
    })
    id: ui.fields.Text;
}
