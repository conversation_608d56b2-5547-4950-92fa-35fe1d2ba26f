import type { Graph<PERSON>pi } from '@sage/xtrem-avalara-gateway-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { avalara } from '../menu-items/avalara';

@ui.decorators.page<EntityUse>({
    module: 'xtrem-avalara-gateway',
    menuItem: avalara,
    title: 'Entity use code',
    objectTypeSingular: 'Entity use',
    objectTypePlural: 'Entity uses',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-avalara-gateway/EntityUse',
    isTransient: false,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'id' }),
            line2: ui.nestedFields.text({ bind: 'name' }),
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'Active',
                graphQLFilter: { isActive: { _eq: true } },
            },
            {
                title: 'Inactive',
                graphQLFilter: { isActive: { _eq: false } },
            },
        ],
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class EntityUse extends ui.Page<GraphApi> {
    @ui.decorators.section<EntityUse>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<EntityUse>({
        parent() {
            return this.mainSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.numericField<EntityUse>({
        isHidden: true,
    })
    _id: ui.fields.Numeric;

    @ui.decorators.switchField<EntityUse>({
        parent() {
            return this.generalBlock;
        },
        size: 'small',
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<EntityUse>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.textField<EntityUse>({
        parent() {
            return this.generalBlock;
        },
        title: 'Name',
        isMandatory: true,
        width: 'large',
    })
    name: ui.fields.Text;

    @ui.decorators.textField<EntityUse>({
        parent() {
            return this.generalBlock;
        },
        title: 'ID',
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<EntityUse>({
        parent() {
            return this.generalBlock;
        },
        title: 'Description',
        width: 'large',
    })
    description: ui.fields.Text;
}
