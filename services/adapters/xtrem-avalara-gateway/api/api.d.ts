declare module '@sage/xtrem-avalara-gateway-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { Package as SageXtremDistribution$Package } from '@sage/xtrem-distribution-api';
    import type {
        AccountsReceivableInvoice,
        AccountsReceivableInvoiceLineDimension,
        AccountsReceivableInvoiceLineDimensionBinding,
        AccountsReceivableInvoiceLineDimensionInput,
        AccountsReceivableInvoiceLineTax,
        AccountsReceivableInvoiceLineTaxBinding,
        AccountsReceivableInvoiceLineTaxInput,
        Package as SageXtremFinance$Package,
    } from '@sage/xtrem-finance-api';
    import type {
        Account,
        AccountsReceivableInvoiceLineStaging,
        AccountsReceivableInvoiceLineStagingBinding,
        AccountsReceivableInvoiceLineStagingInput,
        AnalyticalData,
        BankAccount,
        BaseOpenItem,
        CompanyAttributeType,
        CompanyAttributeTypeBinding,
        CompanyAttributeTypeInput,
        CompanyDefaultAttribute,
        CompanyDefaultAttributeBinding,
        CompanyDefaultAttributeInput,
        CompanyDefaultDimension,
        CompanyDefaultDimensionBinding,
        CompanyDefaultDimensionInput,
        CompanyDimensionType,
        CompanyDimensionTypeBinding,
        CompanyDimensionTypeInput,
        FinanceTransaction,
        Package as SageXtremFinanceData$Package,
        PaymentDocumentLine,
        PostingClass,
    } from '@sage/xtrem-finance-data-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type {
        LandedCostItem,
        LandedCostItemBinding,
        LandedCostItemInput,
        Package as SageXtremLandedCost$Package,
    } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        Address,
        BaseDocumentLine,
        BomRevisionSequence,
        BusinessEntityAddress,
        CompanyAddress,
        CompanyAddressBinding,
        CompanyAddressInput,
        CompanyContact,
        CompanyContactBinding,
        CompanyContactInput,
        Contact,
        Currency,
        Customer,
        CustomerPriceReason,
        DeliveryDetailInput,
        DeliveryMode,
        Incoterm,
        Item,
        ItemAllergen,
        ItemAllergenBinding,
        ItemAllergenInput,
        ItemCategory,
        ItemClassifications,
        ItemClassificationsBinding,
        ItemClassificationsInput,
        ItemCustomer,
        ItemCustomerInput,
        ItemCustomerPrice,
        ItemCustomerPriceBinding,
        ItemCustomerPriceInput,
        ItemInput,
        ItemSite,
        ItemSiteBinding,
        ItemSiteInput,
        ItemSupplier,
        ItemSupplierInput,
        ItemSupplierPrice,
        ItemSupplierPriceBinding,
        ItemSupplierPriceInput,
        Package as SageXtremMasterData$Package,
        PaymentTerm,
        SequenceNumber,
        UnitOfMeasure,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremPurchasing$Package, PurchaseOrderLine } from '@sage/xtrem-purchasing-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type {
        Package as SageXtremSales$Package,
        ProformaInvoice,
        SalesCreditMemo,
        SalesCreditMemoLine,
        SalesCreditMemoLineBinding,
        SalesCreditMemoLineDiscountCharge,
        SalesCreditMemoLineDiscountChargeBinding,
        SalesCreditMemoLineDiscountChargeInput,
        SalesCreditMemoLineInput,
        SalesCreditMemoLineTax,
        SalesCreditMemoLineTaxBinding,
        SalesCreditMemoLineTaxInput,
        SalesCreditMemoReason,
        SalesCreditMemoTax,
        SalesCreditMemoTaxBinding,
        SalesCreditMemoTaxInput,
        SalesInvoice,
        SalesInvoiceLine,
        SalesInvoiceLineBinding,
        SalesInvoiceLineDiscountCharge,
        SalesInvoiceLineDiscountChargeBinding,
        SalesInvoiceLineDiscountChargeInput,
        SalesInvoiceLineInput,
        SalesInvoiceLineTax,
        SalesInvoiceLineTaxBinding,
        SalesInvoiceLineTaxInput,
        SalesInvoiceLineToSalesCreditMemoLine,
        SalesInvoiceLineToSalesCreditMemoLineBinding,
        SalesInvoiceLineToSalesCreditMemoLineInput,
        SalesInvoiceTax,
        SalesInvoiceTaxBinding,
        SalesInvoiceTaxInput,
        SalesOrder,
        SalesOrderInput,
        SalesOrderLine,
        SalesOrderLineBinding,
        SalesOrderLineDiscountCharge,
        SalesOrderLineDiscountChargeBinding,
        SalesOrderLineDiscountChargeInput,
        SalesOrderLineInput,
        SalesOrderLineTax,
        SalesOrderLineTaxBinding,
        SalesOrderLineTaxInput,
        SalesOrderLineToSalesInvoiceLine,
        SalesOrderLineToSalesInvoiceLineBinding,
        SalesOrderLineToSalesInvoiceLineInput,
        SalesOrderLineToSalesShipmentLine,
        SalesOrderTax,
        SalesOrderTaxBinding,
        SalesOrderTaxInput,
        SalesReturnRequestLineSalesCreditMemoLine,
        SalesReturnRequestLineSalesCreditMemoLineBinding,
        SalesReturnRequestLineSalesCreditMemoLineInput,
        SalesShipmentInput,
        SalesShipmentLine,
        SalesShipmentLineBinding,
        SalesShipmentLineInput,
        SalesShipmentLineToSalesInvoiceLine,
        SalesShipmentLineToSalesInvoiceLineBinding,
        SalesShipmentLineToSalesInvoiceLineInput,
        WorkInProgressSalesOrderLine,
        WorkInProgressSalesOrderLineBinding,
        WorkInProgressSalesOrderLineInput,
    } from '@sage/xtrem-sales-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type {
        OrderAssignment,
        Package as SageXtremStockData$Package,
        StockAllocation,
        StockIssueDetail,
        StockIssueDetailBinding,
        StockIssueDetailInput,
    } from '@sage/xtrem-stock-data-api';
    import type {
        ChartOfAccount,
        Country,
        Legislation,
        Package as SageXtremStructure$Package,
    } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type { Company, Package as SageXtremSystem$Package, Site, SysVendor, User } from '@sage/xtrem-system-api';
    import type { ItemTaxGroup, Package as SageXtremTax$Package, Tax, TaxCategory, TaxZone } from '@sage/xtrem-tax-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface AddressTextCase$Enum {
        upper: 1;
        mixed: 2;
    }
    export type AddressTextCase = keyof AddressTextCase$Enum;
    export interface DocumentType$Enum {
        salesInvoice: 1;
        salesCreditMemo: 2;
    }
    export type DocumentType = keyof DocumentType$Enum;
    export interface EntityType$Enum {
        company: 1;
        financialSite: 2;
    }
    export type EntityType = keyof EntityType$Enum;
    export interface TransactionPayloadManagerType$Enum {
        SalesInvoice: 1;
        SalesCreditMemo: 2;
    }
    export type TransactionPayloadManagerType = keyof TransactionPayloadManagerType$Enum;
    export interface AvalaraCompany extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
    }
    export interface AvalaraCompanyInput extends ClientNodeInput {
        isActive?: boolean | string;
        id?: string;
        name?: string;
    }
    export interface AvalaraCompanyBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
    }
    export interface AvalaraCompany$Mutations {
        loadCompanyFromAvalara: Node$Operation<{}, string>;
    }
    export interface AvalaraCompany$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AvalaraCompany$Operations {
        query: QueryOperation<AvalaraCompany>;
        read: ReadOperation<AvalaraCompany>;
        aggregate: {
            read: AggregateReadOperation<AvalaraCompany>;
            query: AggregateQueryOperation<AvalaraCompany>;
        };
        create: CreateOperation<AvalaraCompanyInput, AvalaraCompany>;
        getDuplicate: GetDuplicateOperation<AvalaraCompany>;
        update: UpdateOperation<AvalaraCompanyInput, AvalaraCompany>;
        updateById: UpdateByIdOperation<AvalaraCompanyInput, AvalaraCompany>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: AvalaraCompany$Mutations;
        asyncOperations: AvalaraCompany$AsyncOperations;
        getDefaults: GetDefaultsOperation<AvalaraCompany>;
    }
    export interface AvalaraConfiguration extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        endPointUrl: string;
        isSandboxMode: boolean;
        isCommittedTransaction: boolean;
        accountId: string;
        licenseKey: string;
        isAddressValidationActive: boolean;
        addressTextCase: AddressTextCase;
        mapCompanyLines: ClientCollection<MapCompany>;
    }
    export interface AvalaraConfigurationInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        isActive?: boolean | string;
        isSandboxMode?: boolean | string;
        isCommittedTransaction?: boolean | string;
        accountId?: string;
        licenseKey?: string;
        isAddressValidationActive?: boolean | string;
        addressTextCase?: AddressTextCase;
    }
    export interface AvalaraConfigurationBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        endPointUrl: string;
        isSandboxMode: boolean;
        isCommittedTransaction: boolean;
        accountId: string;
        licenseKey: string;
        isAddressValidationActive: boolean;
        addressTextCase: AddressTextCase;
        mapCompanyLines: ClientCollection<MapCompany>;
    }
    export interface AvalaraConfiguration$Queries {
        defaultInstance: Node$Operation<{}, AvalaraConfiguration>;
        getIsCommit: Node$Operation<{}, boolean>;
        getUrl: Node$Operation<
            {
                isSandBox?: boolean | string;
            },
            string
        >;
    }
    export interface AvalaraConfiguration$Mutations {
        ping: Node$Operation<
            {
                id: string;
            },
            {
                version: string;
                authenticated: boolean;
            }
        >;
    }
    export interface AvalaraConfiguration$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AvalaraConfiguration$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface AvalaraConfiguration$Operations {
        query: QueryOperation<AvalaraConfiguration>;
        read: ReadOperation<AvalaraConfiguration>;
        aggregate: {
            read: AggregateReadOperation<AvalaraConfiguration>;
            query: AggregateQueryOperation<AvalaraConfiguration>;
        };
        queries: AvalaraConfiguration$Queries;
        update: UpdateOperation<AvalaraConfigurationInput, AvalaraConfiguration>;
        updateById: UpdateByIdOperation<AvalaraConfigurationInput, AvalaraConfiguration>;
        mutations: AvalaraConfiguration$Mutations;
        asyncOperations: AvalaraConfiguration$AsyncOperations;
        lookups(dataOrId: string | { data: AvalaraConfigurationInput }): AvalaraConfiguration$Lookups;
        getDefaults: GetDefaultsOperation<AvalaraConfiguration>;
    }
    export interface AvalaraItemTax extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        description: string;
    }
    export interface AvalaraItemTaxInput extends ClientNodeInput {
        isActive?: boolean | string;
        id?: string;
        description?: string;
    }
    export interface AvalaraItemTaxBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        description: string;
    }
    export interface AvalaraItemTax$Mutations {
        loadTaxCodesFromAvalara: Node$Operation<
            {
                isActive?: (boolean | string) | null;
                fromTaxCode?: string | null;
                toTaxCode?: string | null;
                description?: string | null;
            },
            string
        >;
    }
    export interface AvalaraItemTax$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AvalaraItemTax$Operations {
        query: QueryOperation<AvalaraItemTax>;
        read: ReadOperation<AvalaraItemTax>;
        aggregate: {
            read: AggregateReadOperation<AvalaraItemTax>;
            query: AggregateQueryOperation<AvalaraItemTax>;
        };
        create: CreateOperation<AvalaraItemTaxInput, AvalaraItemTax>;
        getDuplicate: GetDuplicateOperation<AvalaraItemTax>;
        update: UpdateOperation<AvalaraItemTaxInput, AvalaraItemTax>;
        updateById: UpdateByIdOperation<AvalaraItemTaxInput, AvalaraItemTax>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: AvalaraItemTax$Mutations;
        asyncOperations: AvalaraItemTax$AsyncOperations;
        getDefaults: GetDefaultsOperation<AvalaraItemTax>;
    }
    export interface EntityUse extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        description: string;
    }
    export interface EntityUseInput extends ClientNodeInput {
        isActive?: boolean | string;
        id?: string;
        name?: string;
        description?: string;
    }
    export interface EntityUseBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        description: string;
    }
    export interface EntityUse$Mutations {
        loadEntityUseCodesFromAvalara: Node$Operation<{}, string>;
    }
    export interface EntityUse$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface EntityUse$Operations {
        query: QueryOperation<EntityUse>;
        read: ReadOperation<EntityUse>;
        aggregate: {
            read: AggregateReadOperation<EntityUse>;
            query: AggregateQueryOperation<EntityUse>;
        };
        create: CreateOperation<EntityUseInput, EntityUse>;
        getDuplicate: GetDuplicateOperation<EntityUse>;
        update: UpdateOperation<EntityUseInput, EntityUse>;
        updateById: UpdateByIdOperation<EntityUseInput, EntityUse>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: EntityUse$Mutations;
        asyncOperations: EntityUse$AsyncOperations;
        getDefaults: GetDefaultsOperation<EntityUse>;
    }
    export interface MapCompany extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        entityType: EntityType;
        company: Company;
        financialSite: Site;
        avalaraCompany: AvalaraCompany;
        avalaraConfiguration: AvalaraConfiguration;
    }
    export interface MapCompanyInput extends ClientNodeInput {
        isActive?: boolean | string;
        entityType?: EntityType;
        company?: integer | string;
        financialSite?: integer | string;
        avalaraCompany?: integer | string;
        avalaraConfiguration?: integer | string;
    }
    export interface MapCompanyBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        entityType: EntityType;
        company: Company;
        financialSite: Site;
        avalaraCompany: AvalaraCompany;
        avalaraConfiguration: AvalaraConfiguration;
    }
    export interface MapCompany$Queries {
        validateCompany: Node$Operation<
            {
                entityType?: EntityType;
                cachedCompanies?: (integer | string)[];
                company?: string;
            },
            {
                hasErrors: boolean;
                errorMessage: string;
                fieldName: string;
            }
        >;
        validateFinancialSite: Node$Operation<
            {
                entityType?: EntityType;
                cachedFinancialSites?: (integer | string)[];
                financialSite?: string;
            },
            {
                hasErrors: boolean;
                errorMessage: string;
                fieldName: string;
            }
        >;
        validateAvalaraCompany: Node$Operation<
            {
                avalaraCompany?: string;
                cachedAvalaraCompanies?: (integer | string)[];
            },
            {
                hasErrors: boolean;
                errorMessage: string;
                fieldName: string;
            }
        >;
    }
    export interface MapCompany$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MapCompany$Lookups {
        company: QueryOperation<Company>;
        financialSite: QueryOperation<Site>;
        avalaraCompany: QueryOperation<AvalaraCompany>;
        avalaraConfiguration: QueryOperation<AvalaraConfiguration>;
    }
    export interface MapCompany$Operations {
        query: QueryOperation<MapCompany>;
        read: ReadOperation<MapCompany>;
        aggregate: {
            read: AggregateReadOperation<MapCompany>;
            query: AggregateQueryOperation<MapCompany>;
        };
        queries: MapCompany$Queries;
        create: CreateOperation<MapCompanyInput, MapCompany>;
        getDuplicate: GetDuplicateOperation<MapCompany>;
        update: UpdateOperation<MapCompanyInput, MapCompany>;
        updateById: UpdateByIdOperation<MapCompanyInput, MapCompany>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: MapCompany$AsyncOperations;
        lookups(dataOrId: string | { data: MapCompanyInput }): MapCompany$Lookups;
        getDefaults: GetDefaultsOperation<MapCompany>;
    }
    export interface AvalaraOptionManagement extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
    }
    export interface AvalaraOptionManagementInput extends ClientNodeInput {}
    export interface AvalaraOptionManagementBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
    }
    export interface AvalaraOptionManagement$Queries {
        isServiceOptionActiveFunction: Node$Operation<{}, boolean>;
    }
    export interface AvalaraOptionManagement$Mutations {
        serviceOptionChange: Node$Operation<{}, boolean>;
    }
    export interface AvalaraOptionManagement$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AvalaraOptionManagement$Operations {
        queries: AvalaraOptionManagement$Queries;
        update: UpdateOperation<AvalaraOptionManagementInput, AvalaraOptionManagement>;
        updateById: UpdateByIdOperation<AvalaraOptionManagementInput, AvalaraOptionManagement>;
        mutations: AvalaraOptionManagement$Mutations;
        asyncOperations: AvalaraOptionManagement$AsyncOperations;
        getDefaults: GetDefaultsOperation<AvalaraOptionManagement>;
    }
    export interface AccountsReceivableInvoiceLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsReceivableInvoice;
        financialSite: Site;
        taxDate: string;
        providerSite: Site;
        account: Account;
        documentLineType: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType: AccountsPayableReceivableInvoiceLineType;
        currency: Currency;
        amountExcludingTax: string;
        taxLineTaxAmount: string;
        taxDetail: string;
        quantity: string;
        quantityInSalesUnit: string;
        description: string;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        attributesAndDimensions: ClientCollection<AccountsReceivableInvoiceLineDimension>;
        accountingStagingLines: ClientCollection<AccountsReceivableInvoiceLineStaging>;
        taxes: ClientCollection<AccountsReceivableInvoiceLineTax>;
        uiTaxes: string;
        storedDimensions: string;
        storedAttributes: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxAmountAdjusted: string;
        amountIncludingTax: string;
        netPriceIncludingTax: string;
    }
    export interface AccountsReceivableInvoiceLineInputExtension {
        financialSite?: integer | string;
        taxDate?: string;
        providerSite?: integer | string;
        account?: integer | string;
        documentLineType?: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType?: AccountsPayableReceivableInvoiceLineType;
        currency?: integer | string;
        amountExcludingTax?: decimal | string;
        taxLineTaxAmount?: decimal | string;
        taxDetail?: string;
        quantity?: decimal | string;
        description?: string;
        sourceDocumentNumber?: string;
        attributesAndDimensions?: Partial<AccountsReceivableInvoiceLineDimensionInput>[];
        accountingStagingLines?: Partial<AccountsReceivableInvoiceLineStagingInput>[];
        taxes?: Partial<AccountsReceivableInvoiceLineTaxInput>[];
        uiTaxes?: string;
        netPriceIncludingTax?: decimal | string;
    }
    export interface AccountsReceivableInvoiceLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsReceivableInvoice;
        financialSite: Site;
        taxDate: string;
        providerSite: Site;
        account: Account;
        documentLineType: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType: AccountsPayableReceivableInvoiceLineType;
        currency: Currency;
        amountExcludingTax: string;
        taxLineTaxAmount: string;
        taxDetail: string;
        quantity: string;
        quantityInSalesUnit: string;
        description: string;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        attributesAndDimensions: ClientCollection<AccountsReceivableInvoiceLineDimensionBinding>;
        accountingStagingLines: ClientCollection<AccountsReceivableInvoiceLineStagingBinding>;
        taxes: ClientCollection<AccountsReceivableInvoiceLineTaxBinding>;
        uiTaxes: any;
        storedDimensions: any;
        storedAttributes: any;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxAmountAdjusted: string;
        amountIncludingTax: string;
        netPriceIncludingTax: string;
    }
    export interface BaseLineTaxExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        taxRate: string;
        currency: Currency;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        deductibleTaxAmount: string;
        isReverseCharge: boolean;
        taxAmountAdjusted: string;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        numberOfTaxableUnits: string;
        numberOfNonTaxableUnits: string;
        numberOfExemptUnits: string;
        country: string;
        region: string;
        jurisdictionCode: string;
        jurisdictionType: string;
        taxCategory: string;
        tax: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface BaseLineTaxInputExtension {
        _constructor?: string;
        taxRate?: decimal | string;
        taxCategoryReference?: integer | string;
        taxReference?: integer | string;
        deductibleTaxRate?: decimal | string;
        deductibleTaxAmount?: decimal | string;
        isReverseCharge?: boolean | string;
        taxAmountAdjusted?: decimal | string;
        isTaxMandatory?: boolean | string;
        isSubjectToGlTaxExcludedAmount?: boolean | string;
        jurisdictionName?: string;
        numberOfTaxableUnits?: decimal | string;
        numberOfNonTaxableUnits?: decimal | string;
        numberOfExemptUnits?: decimal | string;
        country?: string;
        region?: string;
        jurisdictionCode?: string;
        jurisdictionType?: string;
        taxCategory?: string;
        tax?: string;
        nonTaxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxableAmount?: decimal | string;
    }
    export interface BaseLineTaxBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        taxRate: string;
        currency: Currency;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        deductibleTaxAmount: string;
        isReverseCharge: boolean;
        taxAmountAdjusted: string;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        numberOfTaxableUnits: string;
        numberOfNonTaxableUnits: string;
        numberOfExemptUnits: string;
        country: string;
        region: string;
        jurisdictionCode: string;
        jurisdictionType: string;
        taxCategory: string;
        tax: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface BaseTaxExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        taxRate: string;
        currency: Currency;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        deductibleTaxAmount: string;
        isReverseCharge: boolean;
        taxAmountAdjusted: string;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        country: string;
        region: string;
        jurisdictionCode: string;
        jurisdictionType: string;
        taxCategory: string;
        tax: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface BaseTaxInputExtension {
        _constructor?: string;
        taxRate?: decimal | string;
        taxCategoryReference?: integer | string;
        taxReference?: integer | string;
        deductibleTaxRate?: decimal | string;
        deductibleTaxAmount?: decimal | string;
        isReverseCharge?: boolean | string;
        taxAmountAdjusted?: decimal | string;
        isTaxMandatory?: boolean | string;
        isSubjectToGlTaxExcludedAmount?: boolean | string;
        jurisdictionName?: string;
        country?: string;
        region?: string;
        jurisdictionCode?: string;
        jurisdictionType?: string;
        taxCategory?: string;
        tax?: string;
        nonTaxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxableAmount?: decimal | string;
    }
    export interface BaseTaxBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        taxRate: string;
        currency: Currency;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        deductibleTaxAmount: string;
        isReverseCharge: boolean;
        taxAmountAdjusted: string;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        country: string;
        region: string;
        jurisdictionCode: string;
        jurisdictionType: string;
        taxCategory: string;
        tax: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface CompanyExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        name: string;
        description: string;
        legislation: Legislation;
        chartOfAccount: ChartOfAccount;
        hierarchyChartContent: TextStream;
        siren: string;
        naf: string;
        rcs: string;
        legalForm: LegalForm;
        country: Country;
        currency: Currency;
        priceScale: integer;
        addresses: ClientCollection<CompanyAddress>;
        primaryAddress: CompanyAddress;
        primaryContact: CompanyContact;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        customerOnHoldCheck: CustomerOnHoldType;
        contacts: ClientCollection<CompanyContact>;
        postingClass: PostingClass;
        taxEngine: TaxEngine;
        doStockPosting: boolean;
        doWipPosting: boolean;
        doArPosting: boolean;
        doApPosting: boolean;
        doNonAbsorbedPosting: boolean;
        attributeTypes: ClientCollection<CompanyAttributeType>;
        dimensionTypes: ClientCollection<CompanyDimensionType>;
        defaultAttributes: ClientCollection<CompanyDefaultAttribute>;
        defaultDimensions: ClientCollection<CompanyDefaultDimension>;
        datevConsultantNumber: integer;
        datevCustomerNumber: integer;
        bankAccount: BankAccount;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface CompanyInputExtension {
        id?: string;
        isActive?: boolean | string;
        name?: string;
        description?: string;
        legislation?: integer | string;
        chartOfAccount?: integer | string;
        siren?: string;
        naf?: string;
        rcs?: string;
        legalForm?: LegalForm;
        country?: integer | string;
        currency?: integer | string;
        addresses?: Partial<CompanyAddressInput>[];
        sequenceNumberId?: string;
        customerOnHoldCheck?: CustomerOnHoldType;
        contacts?: Partial<CompanyContactInput>[];
        postingClass?: integer | string;
        taxEngine?: TaxEngine;
        doStockPosting?: boolean | string;
        doWipPosting?: boolean | string;
        doNonAbsorbedPosting?: boolean | string;
        attributeTypes?: Partial<CompanyAttributeTypeInput>[];
        dimensionTypes?: Partial<CompanyDimensionTypeInput>[];
        defaultAttributes?: Partial<CompanyDefaultAttributeInput>[];
        defaultDimensions?: Partial<CompanyDefaultDimensionInput>[];
        datevConsultantNumber?: integer | string;
        datevCustomerNumber?: integer | string;
        bankAccount?: integer | string;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface CompanyBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        name: string;
        description: string;
        legislation: Legislation;
        chartOfAccount: ChartOfAccount;
        hierarchyChartContent: TextStream;
        siren: string;
        naf: string;
        rcs: string;
        legalForm: LegalForm;
        country: Country;
        currency: Currency;
        priceScale: integer;
        addresses: ClientCollection<CompanyAddressBinding>;
        primaryAddress: CompanyAddress;
        primaryContact: CompanyContact;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        customerOnHoldCheck: CustomerOnHoldType;
        contacts: ClientCollection<CompanyContactBinding>;
        postingClass: PostingClass;
        taxEngine: TaxEngine;
        doStockPosting: boolean;
        doWipPosting: boolean;
        doArPosting: boolean;
        doApPosting: boolean;
        doNonAbsorbedPosting: boolean;
        attributeTypes: ClientCollection<CompanyAttributeTypeBinding>;
        dimensionTypes: ClientCollection<CompanyDimensionTypeBinding>;
        defaultAttributes: ClientCollection<CompanyDefaultAttributeBinding>;
        defaultDimensions: ClientCollection<CompanyDefaultDimensionBinding>;
        datevConsultantNumber: integer;
        datevCustomerNumber: integer;
        bankAccount: BankAccount;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface DeliveryDetailExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        address: BusinessEntityAddress;
        isActive: boolean;
        isPrimary: boolean;
        shipmentSite: Site;
        mode: DeliveryMode;
        leadTime: integer;
        incoterm: Incoterm;
        isMondayWorkDay: boolean;
        isTuesdayWorkDay: boolean;
        isWednesdayWorkDay: boolean;
        isThursdayWorkDay: boolean;
        isFridayWorkDay: boolean;
        isSaturdayWorkDay: boolean;
        isSundayWorkDay: boolean;
        workDaysSelection: WeekDays[];
        taxZone: TaxZone;
        entityUse: EntityUse;
    }
    export interface DeliveryDetailInputExtension {
        isActive?: boolean | string;
        isPrimary?: boolean | string;
        shipmentSite?: integer | string;
        mode?: integer | string;
        leadTime?: integer | string;
        incoterm?: integer | string;
        isMondayWorkDay?: boolean | string;
        isTuesdayWorkDay?: boolean | string;
        isWednesdayWorkDay?: boolean | string;
        isThursdayWorkDay?: boolean | string;
        isFridayWorkDay?: boolean | string;
        isSaturdayWorkDay?: boolean | string;
        isSundayWorkDay?: boolean | string;
        taxZone?: integer | string;
        entityUse?: integer | string;
    }
    export interface DeliveryDetailBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        address: BusinessEntityAddress;
        isActive: boolean;
        isPrimary: boolean;
        shipmentSite: Site;
        mode: DeliveryMode;
        leadTime: integer;
        incoterm: Incoterm;
        isMondayWorkDay: boolean;
        isTuesdayWorkDay: boolean;
        isWednesdayWorkDay: boolean;
        isThursdayWorkDay: boolean;
        isFridayWorkDay: boolean;
        isSaturdayWorkDay: boolean;
        isSundayWorkDay: boolean;
        workDaysSelection: WeekDays[];
        taxZone: TaxZone;
        entityUse: EntityUse;
    }
    export interface DeliveryDetailExtension$Lookups {
        entityUse: QueryOperation<EntityUse>;
    }
    export interface DeliveryDetailExtension$Operations {
        lookups(dataOrId: string | { data: DeliveryDetailInput }): DeliveryDetailExtension$Lookups;
    }
    export interface ItemExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergen>;
        classifications: ClientCollection<ItemClassifications>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPrice>;
        itemSites: ClientCollection<ItemSite>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPrice>;
        capacity: integer;
        landedCostItem: LandedCostItem;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        inStockQuantity: string;
        inTransitStockQuantity: string;
        itemTaxGroup: ItemTaxGroup;
        postingClass: PostingClass;
        storedDimensions: string;
        storedAttributes: string;
        composedDescription: string;
        purchaseDocuments: ClientCollection<PurchaseOrderLine>;
        avalaraItemTax: AvalaraItemTax;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
        analyticalData: AnalyticalData;
    }
    export interface ItemInputExtension {
        name?: string;
        description?: string;
        status?: ItemStatus;
        type?: ItemType;
        isBought?: boolean | string;
        isManufactured?: boolean | string;
        isSold?: boolean | string;
        eanNumber?: string;
        commodityCode?: string;
        stockUnit?: integer | string;
        volumeUnit?: integer | string;
        volume?: decimal | string;
        weightUnit?: integer | string;
        weight?: decimal | string;
        density?: decimal | string;
        image?: BinaryStream;
        allergens?: Partial<ItemAllergenInput>[];
        classifications?: Partial<ItemClassificationsInput>[];
        category?: integer | string;
        isStockManaged?: boolean | string;
        isPhantom?: boolean | string;
        isBomRevisionManaged?: boolean | string;
        bomRevisionSequenceNumber?: integer | string;
        isPotencyManagement?: boolean | string;
        isTraceabilityManagement?: boolean | string;
        supplierPrices?: Partial<ItemSupplierPriceInput>[];
        itemSites?: Partial<ItemSiteInput>[];
        suppliers?: Partial<ItemSupplierInput>[];
        customers?: Partial<ItemCustomerInput>[];
        purchaseUnit?: integer | string;
        purchaseUnitToStockUnitConversion?: decimal | string;
        serialNumberManagement?: SerialNumberManagement;
        serialNumberSequenceNumber?: integer | string;
        serialNumberUsage?: SerialNumberUsage;
        salesUnit?: integer | string;
        salesUnitToStockUnitConversion?: decimal | string;
        minimumSalesQuantity?: decimal | string;
        maximumSalesQuantity?: decimal | string;
        currency?: integer | string;
        basePrice?: decimal | string;
        minimumPrice?: decimal | string;
        customerPrices?: Partial<ItemCustomerPriceInput>[];
        capacity?: integer | string;
        landedCostItem?: LandedCostItemInput;
        itemTaxGroup?: integer | string;
        postingClass?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        avalaraItemTax?: integer | string;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        id?: string;
        lotManagement?: LotManagement;
        lotSequenceNumber?: integer | string;
        isExpiryManaged?: boolean | string;
        analyticalData?: integer | string;
    }
    export interface ItemBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergenBinding>;
        classifications: ClientCollection<ItemClassificationsBinding>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPriceBinding>;
        itemSites: ClientCollection<ItemSiteBinding>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversion: string;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversion: string;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPriceBinding>;
        capacity: integer;
        landedCostItem: LandedCostItemBinding;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        inStockQuantity: string;
        inTransitStockQuantity: string;
        itemTaxGroup: ItemTaxGroup;
        postingClass: PostingClass;
        storedDimensions: any;
        storedAttributes: any;
        composedDescription: string;
        purchaseDocuments: ClientCollection<PurchaseOrderLine>;
        avalaraItemTax: AvalaraItemTax;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
        analyticalData: AnalyticalData;
    }
    export interface ItemExtension$Lookups {
        avalaraItemTax: QueryOperation<AvalaraItemTax>;
    }
    export interface ItemExtension$Operations {
        lookups(dataOrId: string | { data: ItemInput }): ItemExtension$Lookups;
    }
    export interface SalesCreditMemoExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        paymentStatus: OpenItemStatus;
        taxEngine: TaxEngine;
        salesSite: Site;
        salesSiteName: string;
        salesSiteTaxIdNumber: string;
        salesSiteLinkedAddress: BusinessEntityAddress;
        salesSiteAddress: Address;
        salesSiteContact: Contact;
        reason: SalesCreditMemoReason;
        billToCustomer: Customer;
        billToCustomerName: string;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        billToCustomerTaxIdNumber: string;
        paymentTerm: PaymentTerm;
        dueDate: string;
        incoterm: Incoterm;
        taxCalculationStatus: TaxCalculationStatus;
        penaltyPaymentType: DiscountOrPenaltyType;
        discountPaymentType: DiscountOrPenaltyType;
        fxRateDate: string;
        financeIntegrationApp: FinanceIntegrationApp;
        creationNumber: string;
        arOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        receipts: ClientCollection<PaymentDocumentLine>;
        discountPaymentBeforeDate: string;
        financeTransactions: ClientCollection<FinanceTransaction>;
        postingDetails: ClientCollection<FinanceTransaction>;
        isOpenItemPageOptionActive: boolean;
        financeIntegrationStatus: FinanceIntegrationStatus;
        avalaraId: string;
        totalDiscountAmount: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        displayStatus: BaseDisplayStatus;
        currency: Currency;
        transactionCurrency: Currency;
        totalTaxAmount: string;
        totalTaxAmountAdjusted: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        discountPaymentAmount: string;
        penaltyPaymentAmount: string;
        taxes: ClientCollection<SalesCreditMemoTax>;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountPaid: string;
        forcedAmountPaid: string;
        transactionAmountPaid: string;
        companyAmountPaid: string;
        financialSiteAmountPaid: string;
        netBalance: string;
        lines: ClientCollection<SalesCreditMemoLine>;
        totalAmountExcludingTax: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        rateDescription: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalGrossProfit: string;
    }
    export interface SalesCreditMemoInputExtension {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        paymentStatus?: OpenItemStatus;
        salesSiteName?: string;
        salesSiteTaxIdNumber?: string;
        salesSiteLinkedAddress?: integer | string;
        salesSiteAddress?: integer | string;
        salesSiteContact?: integer | string;
        reason?: integer | string;
        billToCustomer?: integer | string;
        billToCustomerName?: string;
        billToLinkedAddress?: integer | string;
        billToAddress?: integer | string;
        billToContact?: integer | string;
        billToCustomerTaxIdNumber?: string;
        paymentTerm?: integer | string;
        dueDate?: string;
        incoterm?: integer | string;
        taxCalculationStatus?: TaxCalculationStatus;
        penaltyPaymentType?: DiscountOrPenaltyType;
        discountPaymentType?: DiscountOrPenaltyType;
        fxRateDate?: string;
        creationNumber?: string;
        discountPaymentBeforeDate?: string;
        avalaraId?: string;
        totalDiscountAmount?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        displayStatus?: BaseDisplayStatus;
        currency?: integer | string;
        totalTaxAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        discountPaymentAmount?: decimal | string;
        penaltyPaymentAmount?: decimal | string;
        taxes?: Partial<SalesCreditMemoTaxInput>[];
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        transactionAmountPaid?: decimal | string;
        companyAmountPaid?: decimal | string;
        financialSiteAmountPaid?: decimal | string;
        lines?: Partial<SalesCreditMemoLineInput>[];
    }
    export interface SalesCreditMemoBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        paymentStatus: OpenItemStatus;
        taxEngine: TaxEngine;
        salesSite: Site;
        salesSiteName: string;
        salesSiteTaxIdNumber: string;
        salesSiteLinkedAddress: BusinessEntityAddress;
        salesSiteAddress: Address;
        salesSiteContact: Contact;
        reason: SalesCreditMemoReason;
        billToCustomer: Customer;
        billToCustomerName: string;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        billToCustomerTaxIdNumber: string;
        paymentTerm: PaymentTerm;
        dueDate: string;
        incoterm: Incoterm;
        taxCalculationStatus: TaxCalculationStatus;
        penaltyPaymentType: DiscountOrPenaltyType;
        discountPaymentType: DiscountOrPenaltyType;
        fxRateDate: string;
        financeIntegrationApp: FinanceIntegrationApp;
        creationNumber: string;
        arOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        receipts: ClientCollection<PaymentDocumentLine>;
        discountPaymentBeforeDate: string;
        financeTransactions: ClientCollection<FinanceTransaction>;
        postingDetails: ClientCollection<FinanceTransaction>;
        isOpenItemPageOptionActive: boolean;
        financeIntegrationStatus: FinanceIntegrationStatus;
        avalaraId: string;
        totalDiscountAmount: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        displayStatus: BaseDisplayStatus;
        currency: Currency;
        transactionCurrency: Currency;
        totalTaxAmount: string;
        totalTaxAmountAdjusted: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        discountPaymentAmount: string;
        penaltyPaymentAmount: string;
        taxes: ClientCollection<SalesCreditMemoTaxBinding>;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountPaid: string;
        forcedAmountPaid: string;
        transactionAmountPaid: string;
        companyAmountPaid: string;
        financialSiteAmountPaid: string;
        netBalance: string;
        lines: ClientCollection<SalesCreditMemoLineBinding>;
        totalAmountExcludingTax: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        rateDescription: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalGrossProfit: string;
    }
    export interface SalesCreditMemoExtension$Mutations {
        calculateTax: Node$Operation<
            {
                creditMemo: string;
                isCommit?: boolean | string;
                isPosting?: boolean | string;
            },
            string
        >;
        postAvalara: Node$Operation<
            {
                creditMemo: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
    }
    export interface SalesCreditMemoExtension$Operations {
        mutations: SalesCreditMemoExtension$Mutations;
        getDefaults: GetDefaultsOperation<SalesCreditMemo>;
    }
    export interface SalesCreditMemoLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesCreditMemo;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        salesSite: Site;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        grossPrice: string;
        grossPriceDeterminated: string;
        discountCharges: ClientCollection<SalesCreditMemoLineDiscountCharge>;
        discount: string;
        charge: string;
        netPrice: string;
        taxDate: string;
        netPriceExcludingTax: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        toInvoiceLines: ClientCollection<SalesInvoiceLineToSalesCreditMemoLine>;
        toReturnRequestLines: ClientCollection<SalesReturnRequestLineSalesCreditMemoLine>;
        sourceDocumentNumber: string;
        sourceDocumentSysId: integer;
        sourceDocumentType: SourceDocumentType;
        consumptionLinkedAddress: BusinessEntityAddress;
        consumptionAddress: Address;
        providerSite: Site;
        providerSiteLinkedAddress: BusinessEntityAddress;
        providerSiteAddress: Address;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        taxes: ClientCollection<SalesCreditMemoLineTax>;
        uiTaxes: string;
        taxCalculationStatus: TaxCalculationStatus;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        entityUse: EntityUse;
        unitToStockUnitConversionFactor: string;
        discountDeterminated: string;
        chargeDeterminated: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        netPriceIncludingTax: string;
        taxAmountAdjusted: string;
        quantityInStockUnit: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
    }
    export interface SalesCreditMemoLineInputExtension {
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        grossPrice?: decimal | string;
        grossPriceDeterminated?: decimal | string;
        discountCharges?: Partial<SalesCreditMemoLineDiscountChargeInput>[];
        discount?: decimal | string;
        charge?: decimal | string;
        netPrice?: decimal | string;
        netPriceExcludingTax?: decimal | string;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        toInvoiceLines?: Partial<SalesInvoiceLineToSalesCreditMemoLineInput>[];
        toReturnRequestLines?: Partial<SalesReturnRequestLineSalesCreditMemoLineInput>[];
        consumptionLinkedAddress?: integer | string;
        consumptionAddress?: integer | string;
        providerSite?: integer | string;
        providerSiteLinkedAddress?: integer | string;
        providerSiteAddress?: integer | string;
        priceOrigin?: SalesPriceOrigin;
        priceReason?: integer | string;
        priceOriginDeterminated?: SalesPriceOrigin;
        priceReasonDeterminated?: integer | string;
        isPriceDeterminated?: boolean | string;
        taxes?: Partial<SalesCreditMemoLineTaxInput>[];
        uiTaxes?: string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        entityUse?: integer | string;
        unitToStockUnitConversionFactor?: decimal | string;
        discountDeterminated?: decimal | string;
        chargeDeterminated?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        netPriceIncludingTax?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        quantityInStockUnit?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
    }
    export interface SalesCreditMemoLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesCreditMemo;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        salesSite: Site;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        grossPrice: string;
        grossPriceDeterminated: string;
        discountCharges: ClientCollection<SalesCreditMemoLineDiscountChargeBinding>;
        discount: string;
        charge: string;
        netPrice: string;
        taxDate: string;
        netPriceExcludingTax: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        toInvoiceLines: ClientCollection<SalesInvoiceLineToSalesCreditMemoLineBinding>;
        toReturnRequestLines: ClientCollection<SalesReturnRequestLineSalesCreditMemoLineBinding>;
        sourceDocumentNumber: string;
        sourceDocumentSysId: integer;
        sourceDocumentType: SourceDocumentType;
        consumptionLinkedAddress: BusinessEntityAddress;
        consumptionAddress: Address;
        providerSite: Site;
        providerSiteLinkedAddress: BusinessEntityAddress;
        providerSiteAddress: Address;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        taxes: ClientCollection<SalesCreditMemoLineTaxBinding>;
        uiTaxes: any;
        taxCalculationStatus: TaxCalculationStatus;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        entityUse: EntityUse;
        unitToStockUnitConversionFactor: string;
        discountDeterminated: string;
        chargeDeterminated: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        netPriceIncludingTax: string;
        taxAmountAdjusted: string;
        quantityInStockUnit: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
    }
    export interface SalesCreditMemoLineExtension$Lookups {
        entityUse: QueryOperation<EntityUse>;
    }
    export interface SalesCreditMemoLineExtension$Operations {
        lookups(dataOrId: string | { data: SalesCreditMemoLineInput }): SalesCreditMemoLineExtension$Lookups;
    }
    export interface SalesInvoiceExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        paymentStatus: OpenItemStatus;
        invoiceDate: string;
        enablePrintButton: boolean;
        taxEngine: TaxEngine;
        salesSite: Site;
        salesSiteName: string;
        salesSiteTaxIdNumber: string;
        salesSiteLinkedAddress: BusinessEntityAddress;
        salesSiteAddress: Address;
        salesSiteContact: Contact;
        billToCustomer: Customer;
        billToCustomerName: string;
        billToCustomerTaxIdNumber: string;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        paymentTerm: PaymentTerm;
        incoterm: Incoterm;
        taxCalculationStatus: TaxCalculationStatus;
        dueDate: string;
        penaltyPaymentType: DiscountOrPenaltyType;
        discountPaymentType: DiscountOrPenaltyType;
        creditStatus: SalesDocumentCreditStatus;
        rateDescription: string;
        fxRateDate: string;
        financeIntegrationApp: FinanceIntegrationApp;
        creationNumber: string;
        arOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        receipts: ClientCollection<PaymentDocumentLine>;
        discountPaymentBeforeDate: string;
        financeTransactions: ClientCollection<FinanceTransaction>;
        isOpenItemPageOptionActive: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        avalaraId: string;
        totalDiscountAmount: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        currency: Currency;
        transactionCurrency: Currency;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        discountPaymentAmount: string;
        penaltyPaymentAmount: string;
        taxes: ClientCollection<SalesInvoiceTax>;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountPaid: string;
        forcedAmountPaid: string;
        transactionAmountPaid: string;
        companyAmountPaid: string;
        financialSiteAmountPaid: string;
        netBalance: string;
        lines: ClientCollection<SalesInvoiceLine>;
        totalAmountExcludingTax: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalGrossProfit: string;
        displayStatus: BaseDisplayStatus;
    }
    export interface SalesInvoiceInputExtension {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        stockSite?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        paymentStatus?: OpenItemStatus;
        salesSiteName?: string;
        salesSiteTaxIdNumber?: string;
        salesSiteLinkedAddress?: integer | string;
        salesSiteAddress?: integer | string;
        salesSiteContact?: integer | string;
        billToCustomer?: integer | string;
        billToCustomerName?: string;
        billToCustomerTaxIdNumber?: string;
        billToLinkedAddress?: integer | string;
        billToAddress?: integer | string;
        billToContact?: integer | string;
        paymentTerm?: integer | string;
        incoterm?: integer | string;
        taxCalculationStatus?: TaxCalculationStatus;
        dueDate?: string;
        penaltyPaymentType?: DiscountOrPenaltyType;
        discountPaymentType?: DiscountOrPenaltyType;
        creditStatus?: SalesDocumentCreditStatus;
        fxRateDate?: string;
        creationNumber?: string;
        discountPaymentBeforeDate?: string;
        avalaraId?: string;
        totalDiscountAmount?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        currency?: integer | string;
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        discountPaymentAmount?: decimal | string;
        penaltyPaymentAmount?: decimal | string;
        taxes?: Partial<SalesInvoiceTaxInput>[];
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        transactionAmountPaid?: decimal | string;
        companyAmountPaid?: decimal | string;
        financialSiteAmountPaid?: decimal | string;
        lines?: Partial<SalesInvoiceLineInput>[];
        displayStatus?: BaseDisplayStatus;
    }
    export interface SalesInvoiceBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        stockSite: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        paymentStatus: OpenItemStatus;
        invoiceDate: string;
        enablePrintButton: boolean;
        taxEngine: TaxEngine;
        salesSite: Site;
        salesSiteName: string;
        salesSiteTaxIdNumber: string;
        salesSiteLinkedAddress: BusinessEntityAddress;
        salesSiteAddress: Address;
        salesSiteContact: Contact;
        billToCustomer: Customer;
        billToCustomerName: string;
        billToCustomerTaxIdNumber: string;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        paymentTerm: PaymentTerm;
        incoterm: Incoterm;
        taxCalculationStatus: TaxCalculationStatus;
        dueDate: string;
        penaltyPaymentType: DiscountOrPenaltyType;
        discountPaymentType: DiscountOrPenaltyType;
        creditStatus: SalesDocumentCreditStatus;
        rateDescription: string;
        fxRateDate: string;
        financeIntegrationApp: FinanceIntegrationApp;
        creationNumber: string;
        arOpenItems: ClientCollection<BaseOpenItem>;
        openItemSysId: integer;
        receipts: ClientCollection<PaymentDocumentLine>;
        discountPaymentBeforeDate: string;
        financeTransactions: ClientCollection<FinanceTransaction>;
        isOpenItemPageOptionActive: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        avalaraId: string;
        totalDiscountAmount: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        currency: Currency;
        transactionCurrency: Currency;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        discountPaymentAmount: string;
        penaltyPaymentAmount: string;
        taxes: ClientCollection<SalesInvoiceTaxBinding>;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountPaid: string;
        forcedAmountPaid: string;
        transactionAmountPaid: string;
        companyAmountPaid: string;
        financialSiteAmountPaid: string;
        netBalance: string;
        lines: ClientCollection<SalesInvoiceLineBinding>;
        totalAmountExcludingTax: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalGrossProfit: string;
        displayStatus: BaseDisplayStatus;
    }
    export interface SalesInvoiceExtension$Mutations {
        calculateTax: Node$Operation<
            {
                invoice: string;
                isCommit?: boolean | string;
                isPosting?: boolean | string;
            },
            string
        >;
        postAvalara: Node$Operation<
            {
                invoice: string;
            },
            SalesInvoice
        >;
    }
    export interface SalesInvoiceExtension$Operations {
        mutations: SalesInvoiceExtension$Mutations;
        getDefaults: GetDefaultsOperation<SalesInvoice>;
    }
    export interface SalesInvoiceLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesInvoice;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        salesSite: Site;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        discountCharges: ClientCollection<SalesInvoiceLineDiscountCharge>;
        grossPrice: string;
        discountDeterminated: string;
        chargeDeterminated: string;
        grossPriceDeterminated: string;
        netPrice: string;
        taxDate: string;
        netPriceExcludingTax: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        consumptionLinkedAddress: BusinessEntityAddress;
        consumptionAddress: Address;
        providerSite: Site;
        providerSiteLinkedAddress: BusinessEntityAddress;
        providerSiteAddress: Address;
        salesOrderLines: ClientCollection<SalesOrderLineToSalesInvoiceLine>;
        salesShipmentLines: ClientCollection<SalesShipmentLineToSalesInvoiceLine>;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        sourceDocumentSysId: integer;
        creditStatus: SalesDocumentCreditStatus;
        quantityCreditedInProgressInSalesUnit: string;
        quantityCreditedPostedInSalesUnit: string;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        taxes: ClientCollection<SalesInvoiceLineTax>;
        uiTaxes: string;
        taxCalculationStatus: TaxCalculationStatus;
        customerNumber: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        entityUse: EntityUse;
        unitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        netPriceIncludingTax: string;
        taxAmountAdjusted: string;
        quantityInStockUnit: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
    }
    export interface SalesInvoiceLineInputExtension {
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        discountCharges?: Partial<SalesInvoiceLineDiscountChargeInput>[];
        grossPrice?: decimal | string;
        discountDeterminated?: decimal | string;
        chargeDeterminated?: decimal | string;
        grossPriceDeterminated?: decimal | string;
        netPrice?: decimal | string;
        netPriceExcludingTax?: decimal | string;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        consumptionLinkedAddress?: integer | string;
        consumptionAddress?: integer | string;
        providerSite?: integer | string;
        providerSiteLinkedAddress?: integer | string;
        providerSiteAddress?: integer | string;
        salesOrderLines?: Partial<SalesOrderLineToSalesInvoiceLineInput>[];
        salesShipmentLines?: Partial<SalesShipmentLineToSalesInvoiceLineInput>[];
        creditStatus?: SalesDocumentCreditStatus;
        priceOrigin?: SalesPriceOrigin;
        priceReason?: integer | string;
        priceOriginDeterminated?: SalesPriceOrigin;
        priceReasonDeterminated?: integer | string;
        isPriceDeterminated?: boolean | string;
        taxes?: Partial<SalesInvoiceLineTaxInput>[];
        uiTaxes?: string;
        customerNumber?: string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        entityUse?: integer | string;
        unitToStockUnitConversionFactor?: decimal | string;
        discount?: decimal | string;
        charge?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        netPriceIncludingTax?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        quantityInStockUnit?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
    }
    export interface SalesInvoiceLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesInvoice;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        salesSite: Site;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        discountCharges: ClientCollection<SalesInvoiceLineDiscountChargeBinding>;
        grossPrice: string;
        discountDeterminated: string;
        chargeDeterminated: string;
        grossPriceDeterminated: string;
        netPrice: string;
        taxDate: string;
        netPriceExcludingTax: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        consumptionLinkedAddress: BusinessEntityAddress;
        consumptionAddress: Address;
        providerSite: Site;
        providerSiteLinkedAddress: BusinessEntityAddress;
        providerSiteAddress: Address;
        salesOrderLines: ClientCollection<SalesOrderLineToSalesInvoiceLineBinding>;
        salesShipmentLines: ClientCollection<SalesShipmentLineToSalesInvoiceLineBinding>;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        sourceDocumentSysId: integer;
        creditStatus: SalesDocumentCreditStatus;
        quantityCreditedInProgressInSalesUnit: string;
        quantityCreditedPostedInSalesUnit: string;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        taxes: ClientCollection<SalesInvoiceLineTaxBinding>;
        uiTaxes: any;
        taxCalculationStatus: TaxCalculationStatus;
        customerNumber: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        entityUse: EntityUse;
        unitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        netPriceIncludingTax: string;
        taxAmountAdjusted: string;
        quantityInStockUnit: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
    }
    export interface SalesInvoiceLineExtension$Lookups {
        entityUse: QueryOperation<EntityUse>;
    }
    export interface SalesInvoiceLineExtension$Operations {
        lookups(dataOrId: string | { data: SalesInvoiceLineInput }): SalesInvoiceLineExtension$Lookups;
    }
    export interface SalesOrderExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        customerNumber: string;
        isQuote: boolean;
        invoiceStatus: SalesDocumentInvoiceStatus;
        orderDate: string;
        taxEngine: TaxEngine;
        soldToCustomer: Customer;
        soldToLinkedAddress: BusinessEntityAddress;
        soldToAddress: Address;
        soldToContact: Contact;
        fxRateDate: string;
        requestedDeliveryDate: string;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        shippingStatus: SalesDocumentShippingStatus;
        salesSite: Site;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        incoterm: Incoterm;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        isOrderAssignmentLinked: boolean;
        taxCalculationStatus: TaxCalculationStatus;
        recordUrl: string;
        proformaInvoices: ClientCollection<ProformaInvoice>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        entityUse: EntityUse;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        paymentTerm: PaymentTerm;
        isOnHold: boolean;
        workDays: integer;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        taxes: ClientCollection<SalesOrderTax>;
        lines: ClientCollection<SalesOrderLine>;
        rateDescription: string;
        shippingDate: string;
        expectedDeliveryDate: string;
        totalAmountExcludingTax: string;
        remainingTotalAmountToShipExcludingTax: string;
        remainingTotalAmountExcludingTaxInCompanyCurrency: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        allocationStatus: StockAllocationStatus;
        allocationRequestStatus: AllocationRequestStatus;
        totalGrossProfit: string;
        isCloseHidden: boolean;
    }
    export interface SalesOrderInputExtension {
        number?: string;
        status?: BaseStatus;
        displayStatus?: BaseDisplayStatus;
        approvalStatus?: ApprovalStatus;
        date?: string;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        customerNumber?: string;
        isQuote?: boolean | string;
        invoiceStatus?: SalesDocumentInvoiceStatus;
        soldToCustomer?: integer | string;
        soldToLinkedAddress?: integer | string;
        soldToAddress?: integer | string;
        soldToContact?: integer | string;
        fxRateDate?: string;
        requestedDeliveryDate?: string;
        doNotShipBeforeDate?: string;
        doNotShipAfterDate?: string;
        shippingStatus?: SalesDocumentShippingStatus;
        shipToCustomer?: integer | string;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        billToCustomer?: integer | string;
        billToLinkedAddress?: integer | string;
        billToAddress?: integer | string;
        billToContact?: integer | string;
        incoterm?: integer | string;
        deliveryMode?: integer | string;
        deliveryLeadTime?: integer | string;
        taxCalculationStatus?: TaxCalculationStatus;
        entityUse?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        stockSite?: integer | string;
        currency?: integer | string;
        internalNote?: TextStream;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        paymentTerm?: integer | string;
        totalTaxAmount?: decimal | string;
        totalTaxableAmount?: decimal | string;
        totalExemptAmount?: decimal | string;
        totalTaxAmountAdjusted?: decimal | string;
        taxes?: Partial<SalesOrderTaxInput>[];
        lines?: Partial<SalesOrderLineInput>[];
        shippingDate?: string;
        expectedDeliveryDate?: string;
    }
    export interface SalesOrderBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        displayStatus: BaseDisplayStatus;
        approvalStatus: ApprovalStatus;
        date: string;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        customerNumber: string;
        isQuote: boolean;
        invoiceStatus: SalesDocumentInvoiceStatus;
        orderDate: string;
        taxEngine: TaxEngine;
        soldToCustomer: Customer;
        soldToLinkedAddress: BusinessEntityAddress;
        soldToAddress: Address;
        soldToContact: Contact;
        fxRateDate: string;
        requestedDeliveryDate: string;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        shippingStatus: SalesDocumentShippingStatus;
        salesSite: Site;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        incoterm: Incoterm;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        isOrderAssignmentLinked: boolean;
        taxCalculationStatus: TaxCalculationStatus;
        recordUrl: string;
        proformaInvoices: ClientCollection<ProformaInvoice>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        entityUse: EntityUse;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        internalNote: TextStream;
        companyFxRate: string;
        companyFxRateDivisor: string;
        paymentTerm: PaymentTerm;
        isOnHold: boolean;
        workDays: integer;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        totalTaxAmountAdjusted: string;
        taxes: ClientCollection<SalesOrderTaxBinding>;
        lines: ClientCollection<SalesOrderLineBinding>;
        rateDescription: string;
        shippingDate: string;
        expectedDeliveryDate: string;
        totalAmountExcludingTax: string;
        remainingTotalAmountToShipExcludingTax: string;
        remainingTotalAmountExcludingTaxInCompanyCurrency: string;
        totalAmountIncludingTax: string;
        totalAmountIncludingTaxInCompanyCurrency: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        allocationStatus: StockAllocationStatus;
        allocationRequestStatus: AllocationRequestStatus;
        totalGrossProfit: string;
        isCloseHidden: boolean;
    }
    export interface SalesOrderExtension$Lookups {
        entityUse: QueryOperation<EntityUse>;
    }
    export interface SalesOrderExtension$Operations {
        lookups(dataOrId: string | { data: SalesOrderInput }): SalesOrderExtension$Lookups;
    }
    export interface SalesOrderLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesOrder;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        invoiceStatus: SalesDocumentInvoiceStatus;
        shippingStatus: SalesDocumentShippingStatus;
        salesSite: Site;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        deliveryMode: DeliveryMode;
        requestedDeliveryDate: string;
        deliveryLeadTime: integer;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        shippingDate: string;
        expectedDeliveryDate: string;
        taxZone: TaxZone;
        discountCharges: ClientCollection<SalesOrderLineDiscountCharge>;
        grossPrice: string;
        discountDeterminated: string;
        chargeDeterminated: string;
        grossPriceDeterminated: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        availableQuantityInStockUnit: string;
        stockOnHand: string;
        salesShipmentLines: ClientCollection<SalesOrderLineToSalesShipmentLine>;
        quantityToShipInProgressInSalesUnit: string;
        shippedQuantityInSalesUnit: string;
        remainingQuantityToShipInSalesUnit: string;
        quantityToInvoiceInProgressInSalesUnit: string;
        invoicedQuantityInSalesUnit: string;
        remainingQuantityToInvoiceInSalesUnit: string;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        taxes: ClientCollection<SalesOrderLineTax>;
        uiTaxes: string;
        taxCalculationStatus: TaxCalculationStatus;
        workInProgress: WorkInProgressSalesOrderLine;
        stockAllocations: ClientCollection<StockAllocation>;
        allocationRequestStatus: AllocationRequestStatus;
        stockDetails: ClientCollection<StockIssueDetail>;
        assignments: ClientCollection<OrderAssignment>;
        uWorkOrderLine: BaseDocumentLine;
        uPurchaseOrderLine: BaseDocumentLine;
        assignedQuantity: string;
        uAssignmentOrder: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        entityUse: EntityUse;
        unitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        netPrice: string;
        netPriceExcludingTax: string;
        netPriceIncludingTax: string;
        taxAmountAdjusted: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        remainingAmountToShipExcludingTax: string;
        remainingAmountToShipExcludingTaxInCompanyCurrency: string;
        availableQuantityInSalesUnit: string;
        suppliedQuantity: string;
        quantityAllocated: string;
        remainingQuantityToShipInStockUnit: string;
        remainingQuantityToAllocate: string;
        quantityInStockUnit: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockShortageInStockUnit: string;
        stockShortageInSalesUnit: string;
        stockShortageStatus: boolean;
        allocationStatus: StockAllocationStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        stockSiteAddress: Address;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
    }
    export interface SalesOrderLineInputExtension {
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        invoiceStatus?: SalesDocumentInvoiceStatus;
        shippingStatus?: SalesDocumentShippingStatus;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        deliveryMode?: integer | string;
        requestedDeliveryDate?: string;
        deliveryLeadTime?: integer | string;
        doNotShipBeforeDate?: string;
        doNotShipAfterDate?: string;
        shippingDate?: string;
        expectedDeliveryDate?: string;
        discountCharges?: Partial<SalesOrderLineDiscountChargeInput>[];
        grossPrice?: decimal | string;
        discountDeterminated?: decimal | string;
        chargeDeterminated?: decimal | string;
        grossPriceDeterminated?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        priceOrigin?: SalesPriceOrigin;
        priceReason?: integer | string;
        priceOriginDeterminated?: SalesPriceOrigin;
        priceReasonDeterminated?: integer | string;
        isPriceDeterminated?: boolean | string;
        taxes?: Partial<SalesOrderLineTaxInput>[];
        uiTaxes?: string;
        workInProgress?: WorkInProgressSalesOrderLineInput;
        allocationRequestStatus?: AllocationRequestStatus;
        stockDetails?: Partial<StockIssueDetailInput>[];
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        entityUse?: integer | string;
        unitToStockUnitConversionFactor?: decimal | string;
        discount?: decimal | string;
        charge?: decimal | string;
        netPrice?: decimal | string;
        netPriceExcludingTax?: decimal | string;
        netPriceIncludingTax?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        quantityInStockUnit?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockSiteAddress?: integer | string;
    }
    export interface SalesOrderLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesOrder;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        invoiceStatus: SalesDocumentInvoiceStatus;
        shippingStatus: SalesDocumentShippingStatus;
        salesSite: Site;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        deliveryMode: DeliveryMode;
        requestedDeliveryDate: string;
        deliveryLeadTime: integer;
        doNotShipBeforeDate: string;
        doNotShipAfterDate: string;
        shippingDate: string;
        expectedDeliveryDate: string;
        taxZone: TaxZone;
        discountCharges: ClientCollection<SalesOrderLineDiscountChargeBinding>;
        grossPrice: string;
        discountDeterminated: string;
        chargeDeterminated: string;
        grossPriceDeterminated: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        availableQuantityInStockUnit: string;
        stockOnHand: string;
        salesShipmentLines: ClientCollection<SalesOrderLineToSalesShipmentLine>;
        quantityToShipInProgressInSalesUnit: string;
        shippedQuantityInSalesUnit: string;
        remainingQuantityToShipInSalesUnit: string;
        quantityToInvoiceInProgressInSalesUnit: string;
        invoicedQuantityInSalesUnit: string;
        remainingQuantityToInvoiceInSalesUnit: string;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        taxes: ClientCollection<SalesOrderLineTaxBinding>;
        uiTaxes: any;
        taxCalculationStatus: TaxCalculationStatus;
        workInProgress: WorkInProgressSalesOrderLineBinding;
        stockAllocations: ClientCollection<StockAllocation>;
        allocationRequestStatus: AllocationRequestStatus;
        stockDetails: ClientCollection<StockIssueDetailBinding>;
        assignments: ClientCollection<OrderAssignment>;
        uWorkOrderLine: BaseDocumentLine;
        uPurchaseOrderLine: BaseDocumentLine;
        assignedQuantity: string;
        uAssignmentOrder: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        entityUse: EntityUse;
        unitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        netPrice: string;
        netPriceExcludingTax: string;
        netPriceIncludingTax: string;
        taxAmountAdjusted: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        remainingAmountToShipExcludingTax: string;
        remainingAmountToShipExcludingTaxInCompanyCurrency: string;
        availableQuantityInSalesUnit: string;
        suppliedQuantity: string;
        quantityAllocated: string;
        remainingQuantityToShipInStockUnit: string;
        remainingQuantityToAllocate: string;
        quantityInStockUnit: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        stockShortageInStockUnit: string;
        stockShortageInSalesUnit: string;
        stockShortageStatus: boolean;
        allocationStatus: StockAllocationStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        stockSiteAddress: Address;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
    }
    export interface SalesOrderLineExtension$Lookups {
        entityUse: QueryOperation<EntityUse>;
    }
    export interface SalesOrderLineExtension$Operations {
        lookups(dataOrId: string | { data: SalesOrderLineInput }): SalesOrderLineExtension$Lookups;
    }
    export interface SalesShipmentExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        reference: string;
        invoiceStatus: SalesDocumentInvoiceStatus;
        returnRequestStatus: SalesDocumentReturnStatus;
        returnReceiptStatus: SalesDocumentReceiptStatus;
        salesSite: Site;
        recordUrl: string;
        effectiveDate: string;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        trackingNumber: string;
        incoterm: Incoterm;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        workDays: integer;
        paymentTerm: PaymentTerm;
        isOnHold: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        entityUse: EntityUse;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        shippingDate: string;
        deliveryDate: string;
        fxRateDate: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        lines: ClientCollection<SalesShipmentLine>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        rateDescription: string;
        allocationStatus: StockAllocationStatus;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalGrossProfit: string;
        displayStatus: BaseDisplayStatus;
    }
    export interface SalesShipmentInputExtension {
        number?: string;
        status?: BaseStatus;
        approvalStatus?: ApprovalStatus;
        isPrinted?: boolean | string;
        isSent?: boolean | string;
        site?: integer | string;
        financialSite?: integer | string;
        text?: TextStream;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        isOverwriteNote?: boolean | string;
        isTransferHeaderNote?: boolean | string;
        isTransferLineNote?: boolean | string;
        siteAddress?: integer | string;
        businessEntityAddress?: integer | string;
        reference?: string;
        invoiceStatus?: SalesDocumentInvoiceStatus;
        returnRequestStatus?: SalesDocumentReturnStatus;
        returnReceiptStatus?: SalesDocumentReceiptStatus;
        shipToCustomer?: integer | string;
        shipToCustomerAddress?: integer | string;
        shipToAddress?: integer | string;
        shipToContact?: integer | string;
        billToCustomer?: integer | string;
        billToLinkedAddress?: integer | string;
        billToAddress?: integer | string;
        billToContact?: integer | string;
        trackingNumber?: string;
        incoterm?: integer | string;
        deliveryMode?: integer | string;
        deliveryLeadTime?: integer | string;
        paymentTerm?: integer | string;
        entityUse?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        date?: string;
        stockSite?: integer | string;
        currency?: integer | string;
        deliveryDate?: string;
        fxRateDate?: string;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        lines?: Partial<SalesShipmentLineInput>[];
        displayStatus?: BaseDisplayStatus;
    }
    export interface SalesShipmentBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        status: BaseStatus;
        approvalStatus: ApprovalStatus;
        canPrint: boolean;
        isPrinted: boolean;
        isSent: boolean;
        site: Site;
        companyCurrency: Currency;
        financialSite: Site;
        text: TextStream;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        isOverwriteNote: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        siteAddress: Address;
        businessEntityAddress: BusinessEntityAddress;
        reference: string;
        invoiceStatus: SalesDocumentInvoiceStatus;
        returnRequestStatus: SalesDocumentReturnStatus;
        returnReceiptStatus: SalesDocumentReceiptStatus;
        salesSite: Site;
        recordUrl: string;
        effectiveDate: string;
        shipToCustomer: Customer;
        shipToCustomerAddress: BusinessEntityAddress;
        shipToAddress: Address;
        shipToContact: Contact;
        billToCustomer: Customer;
        billToLinkedAddress: BusinessEntityAddress;
        billToAddress: Address;
        billToContact: Contact;
        trackingNumber: string;
        incoterm: Incoterm;
        deliveryMode: DeliveryMode;
        deliveryLeadTime: integer;
        workDays: integer;
        paymentTerm: PaymentTerm;
        isOnHold: boolean;
        postingDetails: ClientCollection<FinanceTransaction>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        entityUse: EntityUse;
        _attachments: ClientCollection<AttachmentAssociation>;
        date: string;
        stockSite: Site;
        currency: Currency;
        transactionCurrency: Currency;
        shippingDate: string;
        deliveryDate: string;
        fxRateDate: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        lines: ClientCollection<SalesShipmentLineBinding>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        rateDescription: string;
        allocationStatus: StockAllocationStatus;
        totalAmountExcludingTax: string;
        totalAmountExcludingTaxInCompanyCurrency: string;
        totalGrossProfit: string;
        displayStatus: BaseDisplayStatus;
    }
    export interface SalesShipmentExtension$Lookups {
        entityUse: QueryOperation<EntityUse>;
    }
    export interface SalesShipmentExtension$Operations {
        lookups(dataOrId: string | { data: SalesShipmentInput }): SalesShipmentExtension$Lookups;
    }
    export interface Package {
        '@sage/xtrem-avalara-gateway/AvalaraCompany': AvalaraCompany$Operations;
        '@sage/xtrem-avalara-gateway/AvalaraConfiguration': AvalaraConfiguration$Operations;
        '@sage/xtrem-avalara-gateway/AvalaraItemTax': AvalaraItemTax$Operations;
        '@sage/xtrem-avalara-gateway/EntityUse': EntityUse$Operations;
        '@sage/xtrem-avalara-gateway/MapCompany': MapCompany$Operations;
        '@sage/xtrem-avalara-gateway/AvalaraOptionManagement': AvalaraOptionManagement$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremDistribution$Package,
            SageXtremFinance$Package,
            SageXtremFinanceData$Package,
            SageXtremImportExport$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremPurchasing$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremSales$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-avalara-gateway-api' {
    export type * from '@sage/xtrem-avalara-gateway-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-distribution-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-purchasing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-sales-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-avalara-gateway-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-api-partial' {
    import type {
        AccountsReceivableInvoiceLineBindingExtension,
        AccountsReceivableInvoiceLineExtension,
        AccountsReceivableInvoiceLineInputExtension,
    } from '@sage/xtrem-avalara-gateway-api';
    export interface AccountsReceivableInvoiceLine extends AccountsReceivableInvoiceLineExtension {}
    export interface AccountsReceivableInvoiceLineBinding extends AccountsReceivableInvoiceLineBindingExtension {}
    export interface AccountsReceivableInvoiceLineInput extends AccountsReceivableInvoiceLineInputExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type {
        BaseLineTaxBindingExtension,
        BaseLineTaxExtension,
        BaseLineTaxInputExtension,
        BaseTaxBindingExtension,
        BaseTaxExtension,
        BaseTaxInputExtension,
    } from '@sage/xtrem-avalara-gateway-api';
    export interface BaseLineTax extends BaseLineTaxExtension {}
    export interface BaseLineTaxBinding extends BaseLineTaxBindingExtension {}
    export interface BaseLineTaxInput extends BaseLineTaxInputExtension {}
    export interface BaseTax extends BaseTaxExtension {}
    export interface BaseTaxBinding extends BaseTaxBindingExtension {}
    export interface BaseTaxInput extends BaseTaxInputExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type {
        CompanyBindingExtension,
        CompanyExtension,
        CompanyInputExtension,
    } from '@sage/xtrem-avalara-gateway-api';
    export interface Company extends CompanyExtension {}
    export interface CompanyBinding extends CompanyBindingExtension {}
    export interface CompanyInput extends CompanyInputExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type {
        DeliveryDetailBindingExtension,
        DeliveryDetailExtension,
        DeliveryDetailExtension$Lookups,
        DeliveryDetailExtension$Operations,
        DeliveryDetailInputExtension,
        ItemBindingExtension,
        ItemExtension,
        ItemExtension$Lookups,
        ItemExtension$Operations,
        ItemInputExtension,
    } from '@sage/xtrem-avalara-gateway-api';
    export interface DeliveryDetail extends DeliveryDetailExtension {}
    export interface DeliveryDetailBinding extends DeliveryDetailBindingExtension {}
    export interface DeliveryDetailInput extends DeliveryDetailInputExtension {}
    export interface DeliveryDetail$Lookups extends DeliveryDetailExtension$Lookups {}
    export interface DeliveryDetail$Operations extends DeliveryDetailExtension$Operations {}
    export interface Item extends ItemExtension {}
    export interface ItemBinding extends ItemBindingExtension {}
    export interface ItemInput extends ItemInputExtension {}
    export interface Item$Lookups extends ItemExtension$Lookups {}
    export interface Item$Operations extends ItemExtension$Operations {}
}
declare module '@sage/xtrem-sales-api-partial' {
    import type {
        SalesCreditMemoBindingExtension,
        SalesCreditMemoExtension,
        SalesCreditMemoExtension$Mutations,
        SalesCreditMemoExtension$Operations,
        SalesCreditMemoInputExtension,
        SalesCreditMemoLineBindingExtension,
        SalesCreditMemoLineExtension,
        SalesCreditMemoLineExtension$Lookups,
        SalesCreditMemoLineExtension$Operations,
        SalesCreditMemoLineInputExtension,
        SalesInvoiceBindingExtension,
        SalesInvoiceExtension,
        SalesInvoiceExtension$Mutations,
        SalesInvoiceExtension$Operations,
        SalesInvoiceInputExtension,
        SalesInvoiceLineBindingExtension,
        SalesInvoiceLineExtension,
        SalesInvoiceLineExtension$Lookups,
        SalesInvoiceLineExtension$Operations,
        SalesInvoiceLineInputExtension,
        SalesOrderBindingExtension,
        SalesOrderExtension,
        SalesOrderExtension$Lookups,
        SalesOrderExtension$Operations,
        SalesOrderInputExtension,
        SalesOrderLineBindingExtension,
        SalesOrderLineExtension,
        SalesOrderLineExtension$Lookups,
        SalesOrderLineExtension$Operations,
        SalesOrderLineInputExtension,
        SalesShipmentBindingExtension,
        SalesShipmentExtension,
        SalesShipmentExtension$Lookups,
        SalesShipmentExtension$Operations,
        SalesShipmentInputExtension,
    } from '@sage/xtrem-avalara-gateway-api';
    export interface SalesCreditMemo extends SalesCreditMemoExtension {}
    export interface SalesCreditMemoBinding extends SalesCreditMemoBindingExtension {}
    export interface SalesCreditMemoInput extends SalesCreditMemoInputExtension {}
    export interface SalesCreditMemo$Mutations extends SalesCreditMemoExtension$Mutations {}
    export interface SalesCreditMemo$Operations extends SalesCreditMemoExtension$Operations {}
    export interface SalesCreditMemoLine extends SalesCreditMemoLineExtension {}
    export interface SalesCreditMemoLineBinding extends SalesCreditMemoLineBindingExtension {}
    export interface SalesCreditMemoLineInput extends SalesCreditMemoLineInputExtension {}
    export interface SalesCreditMemoLine$Lookups extends SalesCreditMemoLineExtension$Lookups {}
    export interface SalesCreditMemoLine$Operations extends SalesCreditMemoLineExtension$Operations {}
    export interface SalesInvoice extends SalesInvoiceExtension {}
    export interface SalesInvoiceBinding extends SalesInvoiceBindingExtension {}
    export interface SalesInvoiceInput extends SalesInvoiceInputExtension {}
    export interface SalesInvoice$Mutations extends SalesInvoiceExtension$Mutations {}
    export interface SalesInvoice$Operations extends SalesInvoiceExtension$Operations {}
    export interface SalesInvoiceLine extends SalesInvoiceLineExtension {}
    export interface SalesInvoiceLineBinding extends SalesInvoiceLineBindingExtension {}
    export interface SalesInvoiceLineInput extends SalesInvoiceLineInputExtension {}
    export interface SalesInvoiceLine$Lookups extends SalesInvoiceLineExtension$Lookups {}
    export interface SalesInvoiceLine$Operations extends SalesInvoiceLineExtension$Operations {}
    export interface SalesOrder extends SalesOrderExtension {}
    export interface SalesOrderBinding extends SalesOrderBindingExtension {}
    export interface SalesOrderInput extends SalesOrderInputExtension {}
    export interface SalesOrder$Lookups extends SalesOrderExtension$Lookups {}
    export interface SalesOrder$Operations extends SalesOrderExtension$Operations {}
    export interface SalesOrderLine extends SalesOrderLineExtension {}
    export interface SalesOrderLineBinding extends SalesOrderLineBindingExtension {}
    export interface SalesOrderLineInput extends SalesOrderLineInputExtension {}
    export interface SalesOrderLine$Lookups extends SalesOrderLineExtension$Lookups {}
    export interface SalesOrderLine$Operations extends SalesOrderLineExtension$Operations {}
    export interface SalesShipment extends SalesShipmentExtension {}
    export interface SalesShipmentBinding extends SalesShipmentBindingExtension {}
    export interface SalesShipmentInput extends SalesShipmentInputExtension {}
    export interface SalesShipment$Lookups extends SalesShipmentExtension$Lookups {}
    export interface SalesShipment$Operations extends SalesShipmentExtension$Operations {}
}
